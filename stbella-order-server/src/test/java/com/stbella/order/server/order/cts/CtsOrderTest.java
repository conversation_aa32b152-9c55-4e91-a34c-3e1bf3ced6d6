package com.stbella.order.server.order.cts;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.stbella.core.base.UserTokenInfoDTO;
import com.stbella.order.server.contract.biz.config.ContractSignConfig;
import com.stbella.order.server.order.cts.biz.config.DingTalkRobotConfig;
import com.stbella.order.server.order.cts.biz.config.StoreToSiteConfig;
import com.stbella.order.server.order.cts.dto.RefundNotifyMqDTO;
import com.stbella.order.server.order.cts.enums.OrderStatusStateMachineStatusEnum;
import com.stbella.order.server.order.cts.request.order.*;
import com.stbella.order.server.order.cts.response.order.OrderCtsResponse;
import com.stbella.order.server.order.cts.response.order.OrderPayRecordVO;
import com.stbella.order.server.order.cts.response.order.StoreToSiteVO;
import com.stbella.order.server.order.cts.service.CtsPerformanceService;
import com.stbella.order.server.order.cts.service.OrderCtsApplyRefundService;
import com.stbella.order.server.order.cts.service.OrderCtsService;
import com.stbella.order.server.order.cts.service.OrderFacade;
import com.stbella.order.server.order.cts.statemachine.OrderStateEvent;
import com.stbella.order.server.order.cts.statemachine.OrderStateMachineService;
import com.stbella.order.server.order.month.enums.OrderTypeEnum;
import com.stbella.order.server.order.month.enums.RecordTypeEnum;
import com.stbella.order.server.order.month.service.OrderPayRecordService;
import com.stbella.order.server.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.statemachine.StateMachine;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 这里写类的注释
 *
 * <AUTHOR>
 * @date 2022-03-17 19:42
 * @sine 1.0.0
 */
@RunWith(SpringRunner.class)
@Slf4j
public class CtsOrderTest {

    @Resource
    private OrderStateMachineService orderStateMachineService;
    @Resource
    private OrderFacade orderFacade;
    @Resource
    private OrderCtsService orderCtsService;
    @Resource
    private ContractSignConfig contractSignConfig;
    @Resource
    private OrderCtsApplyRefundService orderCtsApplyRefundService;

    private final static List<Integer> orderTypeList = Arrays.asList(OrderTypeEnum.SITTER_GENERAL.getCode(), OrderTypeEnum.CUSTOMER_GENERAL.getCode(),
            OrderTypeEnum.AUNT_SITTER.getCode(), OrderTypeEnum.BABY_SITTER.getCode(), OrderTypeEnum.COURSE.getCode());

    @Test
    public void testCreateOrder() {
        final Long userId = 123L;
        final String orderSign = orderFacade.orderSign(userId);
        BabySitterOrderCreateRequest request = new BabySitterOrderCreateRequest();
        request.setOrderSign(orderSign);
        UserTokenInfoDTO dto = new UserTokenInfoDTO();
        dto.setUserId(userId);
        request.setUserTokenInfoDTO(dto);
        final String orderNo = orderFacade.createOrderBabySitter(request);
        System.out.println("订单创建成功 orderNo:" + orderNo);
    }

    @Test
    public void testCancelOrder() {
        OrderCancelBaseRequest request = new OrderCancelBaseRequest();
        UserTokenInfoDTO dto = new UserTokenInfoDTO();
        dto.setUserId(123L);
        request.setUserTokenInfoDTO(dto);
        request.setOrderNo("DJ202203241715042026");
        orderFacade.cancelOrder(request);
        System.out.println("订单取消成功 orderNo:" + request.getOrderNo());
    }

    @Test
    public void orderList() {
    }

    @Test
    public void orderDetail() {
        OrderCtsResponse orderCtsResponse = orderCtsService.getCtsByOrderNo("10202203231150507328");
        log.info(JsonUtil.write(orderCtsResponse));
    }

    @Test
    public void testRefundApplyOrder() {
        UserTokenInfoDTO dto = new UserTokenInfoDTO();
        dto.setUserId(123L);
        OrderApplyRefundRequest request = new OrderApplyRefundRequest();
        request.setLocalTransactionalNo("CTS20220325172854813522");
        request.setRefundAmount(new BigDecimal("0.01"));
        request.setOrderNo("********************");
        request.setOrderType(13);
        request.setRefundRemark("1111");
        request.setUserTokenInfoDTO(dto);
        orderFacade.applyRefund(request);
    }

    @Test
    public void testRefundEnd() {
        RefundNotifyMqDTO dto = new RefundNotifyMqDTO();
        dto.setAccountType(2);
        dto.setOutTradeNo("CTS20220329103439786899");
        dto.setTransactionalNo("2022032922001425321418794817");
        dto.setOutRefundNo("1508633708837023745");
        dto.setRefundType(1);
        dto.setRefundStatus(1);
        dto.setRefundAmount(new BigDecimal("1.5"));
        dto.setSuccessTime(new Date());
        dto.setRequestBody("");
        orderFacade.refundNotify(dto);
    }

    @Test
    public void testConfig() {
        Map<Integer, Boolean> config =
                contractSignConfig.getMapper();
        Boolean aBoolean = config.get(11);
        System.out.println(aBoolean);
        System.out.println(JSONObject.toJSONString(config));
    }

    @Test
    public void testApplyRefund() {
        CustomerPayApplyRefundAdminSearchRequest refundAdminSearchRequest = new CustomerPayApplyRefundAdminSearchRequest();
        refundAdminSearchRequest.setPageNum(2);
        refundAdminSearchRequest.setPageSize(10);
        orderCtsApplyRefundService.pageAdminApplyRefundList(refundAdminSearchRequest);
    }

    @Resource
    private DingTalkRobotConfig dingTalkRobotConfig;
    @Resource
    private StoreToSiteConfig storeToSiteConfig;

    @Test
    public void testStateMachine() {
        final StateMachine<OrderStatusStateMachineStatusEnum, OrderStateEvent> stateMachine = orderStateMachineService
                .sendEvent(OrderStateEvent.AUNT_SITTER_EVENT_CREATE, null, null, null);
    }

    @Resource
    private CtsPerformanceService ctsPerformanceService;

    @Test
    public void ad1() {
        String secret = dingTalkRobotConfig.getSecret();
        System.out.println(secret);
        List<StoreToSiteVO> a = storeToSiteConfig.mappers;
        System.out.println(JSONArray.toJSONString(a));
    }

    @Resource
    private OrderPayRecordService orderPayRecordService;

    @Test
    public void ads() {
        ctsPerformanceService.pushMsg("捷报啊啊啊啊啊");
    }

    @Test
    public void ad() {
        PayRecordPerformanceQuery payRecordPerformanceQuery = new PayRecordPerformanceQuery();
        payRecordPerformanceQuery.setStartTime(DateUtil.beginOfMonth(new Date()));
        payRecordPerformanceQuery.setEndTime(new Date());
        payRecordPerformanceQuery.setRecordType(RecordTypeEnum.PAY.getCode());
        payRecordPerformanceQuery.setPerformance(1);
        payRecordPerformanceQuery.setOrderTypes(orderTypeList);
        List<OrderPayRecordVO> orderPayRecordPOS = orderPayRecordService.queryPerformanceOfPayRecordList(payRecordPerformanceQuery);

        System.out.println(JSONObject.toJSONString(orderPayRecordPOS.get(0)));
    }
}

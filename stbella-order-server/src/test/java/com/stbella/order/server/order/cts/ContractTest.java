package com.stbella.order.server.order.cts;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.server.contract.dto.CompanyAccountRequestDTO;
import com.stbella.order.server.contract.dto.PersonAccountDTO;
import com.stbella.order.server.contract.dto.SignAddressResponseDTO;
import com.stbella.order.server.contract.dto.UpdateAccountResquestDTO;
import com.stbella.order.server.contract.entity.OrderContractConfigPO;
import com.stbella.order.server.contract.enums.IdTypeEnum;
import com.stbella.order.server.contract.service.impl.ESignServiceImpl;
import com.stbella.order.server.utils.HTTPHelper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.LinkedHashMap;
import java.util.Objects;

@Slf4j
public class ContractTest {


    /**
     * 1、e签宝环境域名换为线上的
     * 2、线上对应的api标准版与非标版接口访问权限需与e签宝方确认开通，微信小程序个人认证刷脸是否开启
     * 3、查看e签宝合同地址有两个接口，其中一个需在网关配置白名单
     * 5、签合同总部章需再e签宝官网后台配置
     * 6、实名认证默认刷脸需通知e签宝工作人员线上配置
     * 7、e签宝公司后台配置应用id并生效订单
     * 8、小程序当面签合同刷脸需提供appid和中间页地址给到e签宝相关人员配置
     * 中间页地址：pages/task/dotask/esignFaceScan/esignFaceScan
     * 9、网关需配置白名单发布、helper发布审批和一些相关审批配置
     */

    @Test
    public void companyAuth() {
        CompanyAccountRequestDTO companyAccountRequestDTO = new CompanyAccountRequestDTO();
        companyAccountRequestDTO.setThirdPartyUserId("91330109MA2H33DH25");
        companyAccountRequestDTO.setCreator("5cd1e734e179422d9eafa2b392a63ab0");
        companyAccountRequestDTO.setName("杭州贝康恩护家政服务有限公司");
        companyAccountRequestDTO.setIdNumber("91330109MA2H33DH25");

        ESignServiceImpl eSignService = new ESignServiceImpl();
        String companyAccount = eSignService.createCompanyAccount(companyAccountRequestDTO);

        System.out.println(companyAccount);
    }

    @Test
    public void companyAuthTest() {
        CompanyAccountRequestDTO companyAccountRequestDTO = new CompanyAccountRequestDTO();
        companyAccountRequestDTO.setThirdPartyUserId("91440400MA4W4AYY7X");
        companyAccountRequestDTO.setCreator("jj");
        companyAccountRequestDTO.setName("杭州贝康健康科技集团有限公司");
        companyAccountRequestDTO.setIdNumber("91440400MA4W4AYY7X");

        ESignServiceImpl eSignService = new ESignServiceImpl();
        String companyAccount = eSignService.createCompanyAccount(companyAccountRequestDTO);

        System.out.println(companyAccount);
    }



    @Test
    public void personAuth() {
        ESignServiceImpl eSignService = new ESignServiceImpl();
        PersonAccountDTO personAccountDTO = new PersonAccountDTO();
        personAccountDTO.setIdNumber("510131199101084620");
        personAccountDTO.setMobile("***********");
        personAccountDTO.setName("柴进");
        personAccountDTO.setThirdPartyUserId("510131199101084620");
        personAccountDTO.setIdType(IdTypeEnum.getIdType(0));
        String personSignAccount = eSignService.createPersonSignAccount(personAccountDTO);
        //{"code":********,"message":"账号已存在","data":{"accountId":"899c321ec0f64e8cbf4134951fbc1c53"}}
        System.out.println(personSignAccount);
    }

    @Test
    public void updatePersonAuth() {
        ESignServiceImpl eSignService = new ESignServiceImpl();
        UpdateAccountResquestDTO personAccountDTO = new UpdateAccountResquestDTO();
        personAccountDTO.setAccountId("899c321ec0f64e8cbf4134951fbc1c53");
        personAccountDTO.setName("柴进");
        personAccountDTO.setPhone("***********");
        String personSignAccount = eSignService.updatePersonSignAccount(personAccountDTO);
        //{"code":********,"message":"账号已存在","data":{"accountId":"899c321ec0f64e8cbf4134951fbc1c53"}}
        System.out.println(personSignAccount);

        Boolean gift = Boolean.TRUE;




    }

    @SneakyThrows
    @Test
    public void apply() {
        ESignServiceImpl eSignService = new ESignServiceImpl();


        // http请求访问url
        OrderContractConfigPO orderContractConfigPO = new OrderContractConfigPO();
        orderContractConfigPO.setAppId("**********");
        orderContractConfigPO.setAppSecret("");
        orderContractConfigPO.setESignHost("https://openapi.esign.cn");
        orderContractConfigPO.setCompanyAccountId("9b2ccc0b084b44f7a4b1d05b171b0adb");
        orderContractConfigPO.setPrimaryCallbackUrl("https://pre.primecare.top/order-center/order/callback/contract/esign/notify");

        String apiUrl = "/v1/signflows/" + "5e9d7cc79e804068b256468832ce6f23" + "/executeUrl" + "?accountId=" + "4a6c226577b74e93baeee937fb256c21";

        String url = orderContractConfigPO.getESignHost() + apiUrl;

        LinkedHashMap<String, String> header = eSignService.findHeader(apiUrl, orderContractConfigPO);

        String s = HTTPHelper.sendGet(url, header, "UTF-8");
        JSONObject jsonObject = JSON.parseObject(s);
        if (0 == jsonObject.getIntValue("code")) {
            JSONObject data = jsonObject.getJSONObject("data");
            SignAddressResponseDTO signAddressResponse = new SignAddressResponseDTO();
            signAddressResponse.setLongUrl(data.getString("url"));
            signAddressResponse.setShortUrl(data.getString("shortUrl"));
            System.out.println(JSONObject.toJSONString(signAddressResponse));
        } else {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, jsonObject.getString("message"));
        }

    }


    @Test
    public void lajiyiqianbao() {
        ESignServiceImpl eSignService = new ESignServiceImpl();

        UpdateAccountResquestDTO updateAccountResquestDTO = new UpdateAccountResquestDTO();
        updateAccountResquestDTO.setAccountId("e9c65ac39a28406e9b7ebf586dfa2567");
        updateAccountResquestDTO.setName("杨黎明");
        updateAccountResquestDTO.setPhone("***********");
        String s = eSignService.updatePersonSignAccount(updateAccountResquestDTO);
        System.out.println(s);
    }

    @SneakyThrows
    @Test
    public void geturl() {
        String str = "589af121ce2b4a6b97745a3c6ae1a0c8,45ce406879bf40c9939aac830225a9b1,https://hzbeikang.h5.esign.cn/guide?context=xUc8Aj8SLgs8&flowId=ca2a2208f0814f60b668734533656d24&organ=false&appId=**********&linkSource=1&bizType=1&tsign_source_type=SIGN_LINK_WUKONG&tsign_source_detail=1vGqUyZBm8k4aIosrZzTBwXK73Gx3LmrAjydboGdLZBSxmqpfdEjEH56%2FaP%2FeAu4TVudGNUJ9%2FWzcwwCtaYs0Lv0V3af3YFTeBKk0BtsZa2j8Bd9F5wZMgaWgeY8bqGAnSX%2F7foOZrRQPdPbACnMLmbr9dmL6V420tJHOpgIXi%2Bx7VCTrPuIQOV9vfvP2aYGoBJAeQQrGIU18gnFBDeCfNguZY4C5wFjnfmZz%2BRDiSxgXfFeJ8a2auqhm0auLaN%2BI\n" +
                "919d092bf446469cbbe8044b445d1aa8,45ce406879bf40c9939aac830225a9b1,https://hzbeikang.h5.esign.cn/guide?context=xUc8Aj8SLgs8&flowId=ca2a2208f0814f60b668734533656d24&organ=false&appId=**********&linkSource=1&bizType=1&tsign_source_type=SIGN_LINK_WUKONG&tsign_source_detail=1vGqUyZBm8k4aIosrZzTBwXK73Gx3LmrAjydboGdLZBSxmqpfdEjEH56%2FaP%2FeAu4TVudGNUJ9%2FWzcwwCtaYs0Lv0V3af3YFTeBKk0BtsZa2j8Bd9F5wZMgaWgeY8bqGAnSX%2F7foOZrRQPdPbACnMLmbr9dmL6V420tJHOpgIXi%2Bx7VCTrPuIQOV9vfvP2aYGoBJAeQQrGIU18gnFBDeCfNguZY4C5wFjnfmZz%2BRDiSxgXfFeJ8a2auqhm0auLaN%2BI\n" +
                "ca2a2208f0814f60b668734533656d24,45ce406879bf40c9939aac830225a9b1,https://hzbeikang.h5.esign.cn/guide?context=xUc8Aj8SLgs8&flowId=ca2a2208f0814f60b668734533656d24&organ=false&appId=**********&linkSource=1&bizType=1&tsign_source_type=SIGN_LINK_WUKONG&tsign_source_detail=1vGqUyZBm8k4aIosrZzTBwXK73Gx3LmrAjydboGdLZBSxmqpfdEjEH56%2FaP%2FeAu4TVudGNUJ9%2FWzcwwCtaYs0Lv0V3af3YFTeBKk0BtsZa2j8Bd9F5wZMgaWgeY8bqGAnSX%2F7foOZrRQPdPbACnMLmbr9dmL6V420tJHOpgIXi%2Bx7VCTrPuIQOV9vfvP2aYGoBJAeQQrGIU18gnFBDeCfNguZY4C5wFjnfmZz%2BRDiSxgXfFeJ8a2auqhm0auLaN%2BI\n" +
                "022bced634e544558c12a6f9ca0b46ee,45ce406879bf40c9939aac830225a9b1,https://hzbeikang.h5.esign.cn/guide?context=e9uqV1b3jXwx&flowId=022bced634e544558c12a6f9ca0b46ee&organ=false&appId=**********&linkSource=1&bizType=1&tsign_source_type=SIGN_LINK_WUKONG&tsign_source_detail=1vGqUyZBm8k4aIosrZzTBwXK73Gx3LmrAjydboGdLZBSxmqpfdEjEH56%2FaP%2FeAu4TVudGNUJ9%2FWzcwwCtaYs0Lv0V3af3YFTeBKk0BtsZa2j8Bd9F5wZMgaWgeY8bqGAnSX%2F7foOZrRQPdPbACnMLmbr9dmL6V420tJHOpgIXi%2Bx7VCTrPuIQOV9vfvP2aYGoqLsgNtt6ykj4GGQpvynNBeish2YSvPvQXJxS9Fuj7%2Bp5Cx7ZjmsHH%2B8LObUm4RLA\n" +
                "0e4eb78acb37446fab4bceac2ca8aa0b,25e032d67590405491ee5786eb15d02b,https://hzbeikang.h5.esign.cn/guide?context=5MqdhIabKGUl&flowId=0e4eb78acb37446fab4bceac2ca8aa0b&organ=false&appId=**********&linkSource=1&bizType=1&tsign_source_type=SIGN_LINK_WUKONG&tsign_source_detail=1vGqUyZBm8k4aIosrZzTBwXK73Gx3LmrAjydboGdLZBSxmqpfdEjEH56%2FaP%2FeAu4TVudGNUJ9%2FWzcwwCtaYs0Lv0V3af3YFTeBKk0BtsZa2j8Bd9F5wZMgaWgeY8bqGAnSX%2F7foOZrRQPdPbACnMLmbr9dmL6V420tJHOpgIXi%2Bx7VCTrPuIQOV9vfvP2aYGo5e6%2B87X%2BSJ2SOT%2F1E2rQRSEl5cgQl9Hxe3ZzSndZrRNlyQPHdmmY3Bti0FB3tZuC\n";

        // http请求访问url


        String[] split = str.split("\n");
        for (String s : split) {
            String[] split1 = s.split(",");
            SignAddressResponseDTO excute = excute( split1[0], split1[1]);
            log.info("===res:{}",JSONObject.toJSONString(excute));
            if (!Objects.equals(split1[2], excute.getLongUrl())) {
                log.info("flowId:{}" , split1[0]);
                break;
            }
        }
    }
    @Test
    public void assd(){
        SignAddressResponseDTO excute = excute("022bced634e544558c12a6f9ca0b46ee" , "45ce406879bf40c9939aac830225a9b1");

        System.out.println(JSONObject.toJSONString(excute));
    }

    static OrderContractConfigPO orderContractConfigPO = new OrderContractConfigPO();

    static {

        orderContractConfigPO.setAppId("**********");
        orderContractConfigPO.setAppSecret("4d88a6ea7a093dc0141ff01f368728bd");
        orderContractConfigPO.setESignHost("https://openapi.esign.cn");
        orderContractConfigPO.setCompanyAccountId("9b2ccc0b084b44f7a4b1d05b171b0adb");
        orderContractConfigPO.setPrimaryCallbackUrl("https://pre.primecare.top/order-center/order/callback/contract/esign/notify");
    }


    @SneakyThrows
    private static SignAddressResponseDTO excute( String flowId, String accountId) {

        // 接口请求地址
        String apiUrl = "/v1/signflows/" + flowId + "/executeUrl" + "?accountId=" + accountId;

        String url = orderContractConfigPO.getESignHost() + apiUrl;
        ESignServiceImpl eSignService = new ESignServiceImpl();

        LinkedHashMap<String, String> header = eSignService.findHeader(apiUrl, orderContractConfigPO);

        String s = HTTPHelper.sendGet(url, header, "UTF-8");
        JSONObject jsonObject = JSON.parseObject(s);
        if (0 == jsonObject.getIntValue("code")) {
            JSONObject data = jsonObject.getJSONObject("data");
            SignAddressResponseDTO signAddressResponse = new SignAddressResponseDTO();
            signAddressResponse.setLongUrl(data.getString("url"));
            signAddressResponse.setShortUrl(data.getString("shortUrl"));
            return signAddressResponse;
        } else {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, jsonObject.getString("message"));
        }
    }


    @SneakyThrows
    public static void main(String[] args) {
        Runtime.getRuntime().exec("rundll32 url.dll,FileProtocolHandler http://www.jb51.net");

    }




}

package com.stbella.order.server.context.component.checker;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.core.OrderNoticeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.context.component.spi.OrderBroadcastSpi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.SnowballExtensionInvoker;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

/**
 * @Author: wangchuang
 * @CreateTime: 2024-05-28  13:55
 * @Description: 报单中断检查器
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "报单中断检查器")
public class BroadcastInterruptChecker implements IExecutableAtom<FlowContext> {

    @Override
    public boolean condition(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        return orderEntity.isJustEffect();
    }

    @Override
    public void run(FlowContext bizContext) {

        Result invokeResult = bizContext.getAttribute(Result.class);
        if (ObjectUtil.isNull(invokeResult)){
            return;
        }
        if (!invokeResult.getSuccess()) {
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code()+"", invokeResult.getMsg());
        }

    }
}

package com.stbella.order.server.convert;

import com.stbella.order.domain.order.production.OrderProductionAppointmentOptLogEntity;
import com.stbella.order.server.order.production.res.ProductionAppointmentOptVo;
import org.mapstruct.Mapper;

/**
 * @description:
 * @author: Seven
 * @time: 2022/10/11 14:23
 */
@Mapper(componentModel = "spring")
public interface AppProductionAppointmentOptLogConverter {

    /**
     * entity -> vo
     *
     * @param entity 实体
     * @return ProductionAppointmentOptVo
     */
    ProductionAppointmentOptVo entity2VoProductionAppointmentOptLog(OrderProductionAppointmentOptLogEntity entity);
}

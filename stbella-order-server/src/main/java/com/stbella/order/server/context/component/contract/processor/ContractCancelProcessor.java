package com.stbella.order.server.context.component.contract.processor;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.server.manager.ContractManager;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

/**
 * 合同撤销 条件
 * 1. 合同ID不为空
 * 2. 合同短链接不为空
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "合同撤销")
public class ContractCancelProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private ContractManager contractManager;


    @Override
    public void run(FlowContext bizContext) {
        OrderContractSignRecordVO orderContractSignRecordVO = bizContext.getAttribute(OrderContractSignRecordVO.class);
        log.info("合同撤销请求参数,orderId={},contractId", orderContractSignRecordVO.getGuideId(), orderContractSignRecordVO.getId());
        if (TemplateContractTypeEnum.SUPPLIMENT.code().equals(orderContractSignRecordVO.getTemplateContractType())) {
            contractManager.cancelSupplement(orderContractSignRecordVO.getId());
        } else {
            contractManager.cancelContract(orderContractSignRecordVO.getId());
        }

    }

    @Override
    public boolean condition(FlowContext bizContext) {
        OrderContractSignRecordVO orderContractSignRecordVO = bizContext.getAttribute(OrderContractSignRecordVO.class);
        if (ObjectUtil.isNotEmpty(orderContractSignRecordVO.getId())
                && StringUtils.isNotEmpty(orderContractSignRecordVO.getContractShortUrl())) {
            return true;
        }
        return false;
    }


}

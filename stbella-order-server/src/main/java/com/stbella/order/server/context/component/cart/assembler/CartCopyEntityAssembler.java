package com.stbella.order.server.context.component.cart.assembler;

import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.entity.HeCartEntity;
import com.stbella.order.domain.repository.HeCartRepository;
import com.stbella.platform.order.api.req.QueryCartReq;
import com.stbella.platform.order.api.req.RestoreCartReq;
import com.stbella.platform.order.api.res.CartRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

@Component
@Slf4j
@SnowballComponent(name = "购物车实体拷贝组装器")
public class CartCopyEntityAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private HeCartRepository heCartRepository;

    @Override
    public void run(FlowContext bizContext) {

        RestoreCartReq param = bizContext.getAttribute(RestoreCartReq.class);
        QueryCartReq queryCartReq = QueryCartReq.builder().cartId(param.getBusinessId()).build();
        HeCartEntity cartEntity = heCartRepository.queryOne(queryCartReq);
        CartRes cartRes = new CartRes();
        cartRes.setTotalAmount(AmountChangeUtil.changeF2Y(cartEntity.getOrderAmount()));
        cartRes.setPayAmount(AmountChangeUtil.changeF2Y(cartEntity.getPayAmount()));
        bizContext.setAttribute(CartRes.class, cartRes);
    }
}

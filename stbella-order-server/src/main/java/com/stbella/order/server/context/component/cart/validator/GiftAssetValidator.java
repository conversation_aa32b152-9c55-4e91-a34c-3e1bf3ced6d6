package com.stbella.order.server.context.component.cart.validator;

import com.stbella.asset.api.res.AccountDto;
import com.stbella.core.base.Operator;
import com.stbella.core.exception.BusinessException;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.core.CartSceneEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.UserEntity;
import com.stbella.order.domain.repository.UserRepository;
import com.stbella.order.server.manager.AssetManager;
import com.stbella.order.server.order.OrderPrice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

import static com.stbella.order.common.enums.ErrorCodeEnum.GIFT_ASSET_NOT_ENOUGH;
import static com.stbella.order.common.enums.ErrorCodeEnum.GIFT_ASSET_NULL;


/**
 * 礼赠金校验
 */
@Component
@Slf4j
@SnowballComponent(name = "礼赠金校验", desc = "")
public class GiftAssetValidator implements IExecutableAtom<FlowContext> {

    @Resource
    private AssetManager assetManager;
    @Resource
    private UserRepository userRepository;

    @Override
    public void run(FlowContext bizContext) {

        OrderPrice orderPrice = bizContext.getAttribute(OrderPrice.class);

        BigDecimal giftAmount = orderPrice.getGiftAmount();
        if (Objects.isNull(giftAmount) || giftAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("礼赠金校验：礼赠金金额为0，不需要校验");
            return;
        }
        Operator operator = bizContext.getAttribute(Operator.class);
        int userId = Integer.parseInt(operator.getOperatorGuid());
        UserEntity userEntity = userRepository.queryById(userId);
        if (Objects.isNull(userEntity)) {
            log.warn("礼赠金校验：用户不存在");
            throw new BusinessException(GIFT_ASSET_NULL.code() + "", GIFT_ASSET_NULL.desc());
        }
        AccountDto accountDto = assetManager.queryGiftAccount(userEntity.getBasicUid());
        if (Objects.isNull(accountDto)) {
            log.warn("礼赠金校验：礼赠金账户不存在");
            throw new BusinessException(GIFT_ASSET_NULL.code() + "", GIFT_ASSET_NULL.desc());
        }
        BigDecimal balance = AmountChangeUtil.changeF2Y(accountDto.getBalance());
        if (balance.compareTo(giftAmount) < 0) {
            log.warn("礼赠金校验：礼赠金余额不足");
            throw new BusinessException(GIFT_ASSET_NOT_ENOUGH.code() + "", GIFT_ASSET_NOT_ENOUGH.desc());
        }

    }

//    @Override
//    public boolean condition(FlowContext bizContext) {
//        Integer scene  = (Integer)bizContext.getAttribute(BizConstant.ExtraKey.scene);
//        return !CartSceneEnum.CUSTOMIZE_AS_YOU_WISH.code().equals(scene);
//    }
}

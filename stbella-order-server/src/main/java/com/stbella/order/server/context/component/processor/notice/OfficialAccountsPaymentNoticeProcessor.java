package com.stbella.order.server.context.component.processor.notice;

import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.domain.client.MessageClient;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.StoreRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

/**
 * @Author: JJ
 * @CreateTime: 2024-05-28  13:55
 * @Description: 支付成功公众号通知组件
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "支付成功公众号通知组件")
public class OfficialAccountsPaymentNoticeProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private MessageClient messageClient;
    @Resource
    StoreRepository storeRepository;

    @Override
    public boolean condition(FlowContext bizContext) {
        HeIncomeRecordEntity incomeRecord = bizContext.getAttribute(HeIncomeRecordEntity.class);
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        return !OmniPayTypeEnum.PRODUCTION_COIN.getCode().equals(incomeRecord.getPayType()) && !OmniPayTypeEnum.REDUCTION.getCode().equals(incomeRecord.getPayType()) && !OmniOrderTypeEnum.getHomeOrderTypeCodeList().contains(orderEntity.getOrderType());
    }

    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);
        HeIncomeRecordEntity incomeRecord = bizContext.getAttribute(HeIncomeRecordEntity.class);
        CfgStoreEntity store = storeRepository.queryCfgStoreById(order.getStoreId());
        String orderSn = order.getOrderSn();
        incomeRecord.setOrderSn(orderSn);
        incomeRecord.setStoreType(store.getType());
        log.info("发送支付成功消息: {}", orderSn);
        messageClient.paymentMessage(incomeRecord);
    }
}

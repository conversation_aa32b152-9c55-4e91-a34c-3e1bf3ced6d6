package com.stbella.order.server.context.component.validator;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.server.utils.wangdian.StringUtils;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@RefreshScope
@SnowballComponent(name = "订单退款校验")
public class OrderRefundValidator implements IExecutableAtom<FlowContext> {


    @Override
    public void run(FlowContext bizContext) {

        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);

        if (!orderEntity.isNewOrder()) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "不支持旧订单的退款！");
        }

        List<HeOrderRefundEntity> refundEntityList = bizContext.getListAttribute(HeOrderRefundEntity.class);

        //判断主退款
        List<HeOrderRefundEntity> collect = refundEntityList.stream().filter(r -> StringUtils.isEmpty(r.getParentRefundOrderSn())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(collect)) {
            List<HeOrderRefundEntity> notFinished = collect.stream().filter(c -> HeOrderRefundEntity.NOT_FINISH_STATUS.contains(c.getStatus())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(notFinished)) {
                throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "当前订单有未完成的退款，请先完成退款后再发起！");
            }
        }

    }


    @Override
    public boolean condition(FlowContext context) {
        CreateRefundReq createRefundReq = context.getAttribute(CreateRefundReq.class);
        HeOrderEntity orderEntity = context.getAttribute(HeOrderEntity.class);
        return ObjectUtil.isNotEmpty(createRefundReq) && ObjectUtil.isNotEmpty(createRefundReq.getOrderId()) && orderEntity.isNewOrder();
    }
}

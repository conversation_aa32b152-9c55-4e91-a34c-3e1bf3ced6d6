package com.stbella.order.server.context.component.ui;

import com.stbella.order.common.enums.core.CartSceneEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.month.OrderButtonEnum;
import com.stbella.order.common.enums.month.OrderStatusV2Enum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.service.ContractOpponentAuthDomainService;
import com.stbella.order.server.order.month.res.OrderOperateButton;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.stbella.order.common.enums.ErrorCodeEnum.CONTRACT_SUBJECT_NOT_AUTH;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-08-12  16:28
 * @Description: 复制订单
 */
@Component
@Slf4j
public class CopyOrderOperate implements OrderOperate{



    @Override
    public void setPriority(int priority) {

    }

    @Override
    public int getPriority() {
        return 3;
    }

    /**
     * 生成订单操作按钮
     * @return
     */
    @Override
    public OrderOperateButton generateButton(OrderOperateContext context) {

        HeOrderEntity order = context.getOrder();
        //新订单及特定订单类型允许复制
        if (order.isNewOrder() && OmniOrderTypeEnum.enableCopy(order.getOrderType())) {
            if (CartSceneEnum.GIFT_ORDER.getCode().equals(order.getScene()) || CartSceneEnum.UPGRADE_ORDER.getCode().equals(order.getScene())){
                return null;
            }
            OrderOperateButton button = OrderOperateButton.builder().code(getButtonEnum().getCode()).value(getButtonEnum().getValue()).build();
            return button;
        }
        return null;
    }

    /**
     * 获取按钮枚举
     *
     * @return
     */
    @Override
    public OrderButtonEnum getButtonEnum() {
        return OrderButtonEnum.COPY_ORDER;
    }

    @Override
    public int compareTo(@NotNull IPriority o) {
        return this.getPriority() - o.getPriority();
    }
}

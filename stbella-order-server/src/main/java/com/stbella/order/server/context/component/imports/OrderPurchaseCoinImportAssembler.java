package com.stbella.order.server.context.component.imports;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.core.IsNanEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.exception.ApplicationException;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.production.OrderProductionExtendEntity;
import com.stbella.order.domain.repository.OrderProductionExtendRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.domain.repository.StoreRepository;
import com.stbella.order.infrastructure.repository.mapper.saas.OrderPurchaseGrantRecordMapper;
import com.stbella.order.infrastructure.repository.po.OrderPurchaseGrantRecordPo;
import com.stbella.order.server.order.cts.entity.OrderCtsPhpRefundPO;
import com.stbella.order.server.utils.IdGenUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: jijunjian
 * @CreateTime: 2023-08-24  16:12
 * @Description: 三方产康订单导入组装
 */
@Slf4j
@Component
public class OrderPurchaseCoinImportAssembler {

    @Resource
    private OrderPurchaseGrantRecordMapper recordMapper;


    /**
     * 产康通卡初始化
     * @param cardDataList
     * @return
     */
    public Integer run(List<OrderPurchaseCoinImportDTO> orderList){

        int insertCount = 0;
        List<OrderPurchaseGrantRecordPo> recordPos = new ArrayList<>();
        for (OrderPurchaseCoinImportDTO importDTO:orderList){
            OrderPurchaseGrantRecordPo recordPo = new OrderPurchaseGrantRecordPo();
            recordPo.setOrderSn(importDTO.getOrderSn());
            recordPo.setTopup(1);
            recordPo.setAmount(new BigDecimal(importDTO.getRechargeAmount()));
            recordPo.setTopupDate(DateUtil.parse(importDTO.getRechargeDate()));
            recordPo.setDeleted(0);
            recordPo.setGmtCreate(new Date());
            recordPo.setGmtUpdate(new Date());
            recordPos.add(recordPo);

            LambdaQueryWrapper<OrderPurchaseGrantRecordPo> wrapper = new LambdaQueryWrapper<OrderPurchaseGrantRecordPo>()
                    .eq(OrderPurchaseGrantRecordPo::getOrderSn, importDTO.getOrderSn());
            Integer count = recordMapper.selectCount(wrapper);
            if (count <= 0){
                recordMapper.insert(recordPo);
                insertCount++;
            }
        }

        return  insertCount;
    }


}

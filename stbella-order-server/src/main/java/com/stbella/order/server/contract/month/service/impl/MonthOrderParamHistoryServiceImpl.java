package com.stbella.order.server.contract.month.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.month.AheadOutRoomEnum;
import com.stbella.order.common.enums.month.ContractStatusEnum;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.common.enums.month.TemplateTypeEnum;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.config.StoreConfig;
import com.stbella.order.server.contract.enums.ContractExceptionEnum;
import com.stbella.order.server.contract.req.OrderParamHistoryPushDTO;
import com.stbella.order.server.contract.req.OrderParamHistoryValuePushDTO;
import com.stbella.order.server.contract.service.month.*;
import com.stbella.order.server.convert.AppMonthContractSignRecordConverter;
import com.stbella.order.server.order.month.enums.OrderHistoryTypeEnum;
import com.stbella.order.server.order.month.enums.StoreChildTypeEnum;
import com.stbella.order.server.order.month.req.AheadOutRoomQuery;
import com.stbella.order.server.order.month.req.MonthContractSignRecordReq;
import com.stbella.order.server.order.month.res.*;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.order.server.utils.BigDecimalUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-02 15:42
 */
@Service
@Slf4j
@RefreshScope
public class MonthOrderParamHistoryServiceImpl implements MonthOrderParamHistoryService {

    @Resource
    private AppMonthContractSignRecordConverter appMonthContractSignRecordConverter;
    @Resource
    private MonthEsignTemplateParamService esignTemplateParamService;
    @Resource
    private MonthEsignTemplateService monthEsignTemplateService;
    @Resource
    MonthContractSignRecordService contractSignRecordService;
    @Resource
    private MonthContractSignRecordRepository monthContractSignRecordRepository;
    @Resource
    private MonthContractSignAgreementService monthContractSignAgreementService;
    @Resource
    private StoreRepository storeRepository;
    @Resource
    private MonthOrderParamHistoryRepository monthOrderParamHistoryRepository;
    @Resource
    private AheadOutRoomRepository aheadOutRoomRepository;
    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;
    @Resource
    private ContractSignRecordPaperRepository contractSignRecordPaperRepository;
    @Value("${month-contract-h5.seal-url}")
    private String sealUrl;

    /**
     * 保存历史参数
     * 历史参数起效关联合同
     * 搜索有效的历史参数
     *
     * @param dto
     */
    @Override
    public Boolean pushOrderParamHistory(OrderParamHistoryPushDTO dto) {
        log.info("订单创建or修改参数推送记录{}", JSONUtil.toJsonStr(dto));
        //根据订单查到合同模版表实体 查询参数,  订单类型,门店类型
        //查询门店信息,找到门店类型
        CfgStoreEntity cfgStore = storeRepository.queryCfgStoreById(dto.getStoreId());
        Optional.ofNullable(cfgStore).orElseThrow(() -> new BusinessException(ResultEnum.PARAM_ERROR, "门店不存在"));
        Integer storeType = cfgStore.getType();
        if (StoreConfig.SPECIAL_TYPE_STORE_MAP.containsKey(cfgStore.getStoreId())) {
            storeType = StoreConfig.SPECIAL_TYPE_STORE_MAP.get(cfgStore.getStoreId());
        }
        // todo 先按最小维度查询具体门店
        //      其次按门店类型 订单类型 维度查询
        MonthEsignTemplateVO monthEsignTemplatePO = monthEsignTemplateService.specialGetByStoreId(dto.getStoreId(), dto.getOrderType(), storeType, TemplateTypeEnum.MAIN_TYPE.code(), dto.getTemplateContractType(), 0);
        if (Objects.isNull(monthEsignTemplatePO)) {
            monthEsignTemplatePO = monthEsignTemplateService.getByTypes(dto.getOrderType(), storeType, TemplateTypeEnum.MAIN_TYPE.code(), dto.getTemplateContractType());
        }
        Optional.ofNullable(monthEsignTemplatePO).orElseThrow(() -> new BusinessException(ResultEnum.PARAM_ERROR, "合同模版不存在"));
        // 组装本次的参数记录
        List<MonthOrderParamHistoryVO> newOrderParamHistoryList = new ArrayList<>(dto.getParamList().size());
        //获取模版参数列表
        List<MonthEsignTemplateParamVO> esignTemplateParamList = esignTemplateParamService.list(dto.getOrderType(), monthEsignTemplatePO.getId());
        //转换为map
        Map<String, MonthEsignTemplateParamVO> templateParamMap = esignTemplateParamList.stream().collect(Collectors.toMap(MonthEsignTemplateParamVO::getMark, a -> a, (k1, k2) -> k1));
        //匹配模版,填充订单信息和所属条款,标识符信息 保留二位小数
        String mark = "";
        String extraGift = "";
        for (OrderParamHistoryValuePushDTO param : dto.getParamList()) {
            String name = param.getName();
            String value = param.getValue();
            MonthOrderParamHistoryVO orderParamHistory = new MonthOrderParamHistoryVO();
            MonthEsignTemplateParamVO esignTemplateParam = templateParamMap.get(name);
            if (esignTemplateParam == null) {
                continue;
            }
            // 截取备注与额外礼赠内容
            mark = "remark".equals(name) ? value : mark;
            extraGift = "extra_gift".equals(name) ? value : extraGift;
            orderParamHistory.setName(esignTemplateParam.getName());
            orderParamHistory.setParamValue(value);
            orderParamHistory.setItem(esignTemplateParam.getItem());
            orderParamHistory.setMark(esignTemplateParam.getMark());
            orderParamHistory.setTerms(esignTemplateParam.getTerms());
            orderParamHistory.setOrderId(Long.valueOf(dto.getOrderId()));
            orderParamHistory.setValid(1);
            newOrderParamHistoryList.add(orderParamHistory);
        }
        // 拼接额外礼赠与备注 并替换
        String value = StringUtils.isNotBlank(extraGift) ? BizConstant.GIFT + "：\n\n" + extraGift + "\n\n" + BizConstant.REMARK + "：\n\n" + mark : BizConstant.REMARK + "：\n\n" + mark;
        newOrderParamHistoryList.forEach(orderParamHistory -> {
            if ("remark".equals(orderParamHistory.getMark())) {
                orderParamHistory.setParamValue(value);
            }
        });
        // 查询订单合同状态 决定这个参数落地状态
        MonthContractSignRecordReq monthContractSignRecordReq = new MonthContractSignRecordReq();
        monthContractSignRecordReq.setGuideId(Long.valueOf(dto.getOrderId()));
        monthContractSignRecordReq.setTemplateType(TemplateTypeEnum.MAIN_TYPE.code());
        monthContractSignRecordReq.setTemplateContractTypes(Collections.singletonList(dto.getTemplateContractType()));
        MonthContractSignRecordVO contractSignRecord = contractSignRecordService.queryByCondition(monthContractSignRecordReq);
        //合同状态分为三种  合同为null,说明还没发起合同  合同有数据,但未签署 则更新参数历史记录即可  合同有数据,已签署 则进入补充协议逻辑
        //合同标志 0: 未生成主合同 1:已生成主合同,暂未签署  2:已合成主合同,且已签署
        int contractFlag;
        if (contractSignRecord == null) {
            contractFlag = 0;
        } else if (contractSignRecord.getContractStatus() == 0) {
            contractFlag = 1;
        } else {
            contractFlag = 2;
        }

        //如果合同未生成或未签署,则直接保存即可
        if (contractFlag == 0 || contractFlag == 1) {
            //先查前面有没有记录
            List<MonthOrderParamHistoryVO> paramHistories = list(Long.valueOf(dto.getOrderId()), 0L, 1,Arrays.asList(OrderHistoryTypeEnum.MAIN.getCode(),OrderHistoryTypeEnum.AGREEMENT.getCode()));
            //如果前面有记录,则作废这些记录
            if (paramHistories != null || paramHistories.size() > 0) {
                for (MonthOrderParamHistoryVO paramHistory : paramHistories) {
                    paramHistory.setHistory(paramHistory.getGmtCreate().getTime());
                    paramHistory.setValid(0);
                    paramHistory.setContractId(0L);
                }
                monthOrderParamHistoryRepository.updateBatchByParamHistoryId(paramHistories);
            }
            //插入新记录,设置为类型合同
            for (MonthOrderParamHistoryVO orderParamHistory : newOrderParamHistoryList) {
                if (contractSignRecord != null) {
                    orderParamHistory.setContractId(contractSignRecord.getId());
                }
                orderParamHistory.setType(1);
                orderParamHistory.setValid(1);
                orderParamHistory.setHistory(0L);
            }
            monthOrderParamHistoryRepository.saveBatchByParamHistoryId(newOrderParamHistoryList);
            //如果有合同,根据新参数重新生成合同
            if (contractSignRecord != null) {
                contractSignRecordService.reloadContract(contractSignRecord.getId(), storeType, dto.getTemplateContractType());
            }
            return true;
        }

        //如果合同已签署,则进入补充协议逻辑
        if (contractFlag == 2) {
            //先查前面有没有记录
            List<MonthOrderParamHistoryVO> paramHistories = list(Long.valueOf(dto.getOrderId()), 0L, 1,Arrays.asList(OrderHistoryTypeEnum.MAIN.getCode(),OrderHistoryTypeEnum.AGREEMENT.getCode()));
            //如果有记录,则查看记录对应的协议是否已签署,如果已经签署则直接压栈  没有的话需要先删除补充协议
            if (paramHistories != null && paramHistories.size() > 0) {
                Long contractId = paramHistories.get(0).getContractId();
                //查询补充协议
                MonthContractSignAgreementVO agreement = monthContractSignAgreementService.getByContractAgreementId(contractId);

                //协议是空,说明合同签署后的第一个补充协议
                if (agreement == null) {
                    for (MonthOrderParamHistoryVO paramHistory : paramHistories) {
                        //历史版本记为当前时间
                        paramHistory.setHistory(System.currentTimeMillis());
                    }
                    //变更旧版本
                    monthOrderParamHistoryRepository.updateBatchByParamHistoryId(paramHistories);
                    //补充协议未签订,则压栈参数并取消
                } else if (agreement.getState() == 0) {
                    for (MonthOrderParamHistoryVO paramHistory : paramHistories) {
                        //置为失效
                        paramHistory.setValid(0);
                        //历史版本记为当前时间
                        paramHistory.setHistory(System.currentTimeMillis());
                    }
                    monthOrderParamHistoryRepository.updateBatchByParamHistoryId(paramHistories);
                    //调用失效补充协议功能
                    monthContractSignAgreementService.cancal(agreement.getId());
                    //已签订,只需要对旧参数进行压栈即可
                } else if (agreement.getState() == 2) {
                    for (MonthOrderParamHistoryVO paramHistory : paramHistories) {
                        //历史版本记为当前时间
                        paramHistory.setHistory(System.currentTimeMillis());
                    }
                    //变更旧版本
                    monthOrderParamHistoryRepository.updateBatchByParamHistoryId(paramHistories);
                }
            }
            //生成一条补充协议和补充协议文本
            Long agreementId = monthContractSignAgreementService.create(dto.getOrderId(), contractSignRecord, newOrderParamHistoryList, TemplateContractTypeEnum.SUPPLIMENT.code());
            //插入新记录,设置为补充协议
            for (MonthOrderParamHistoryVO orderParamHistory : newOrderParamHistoryList) {
                orderParamHistory.setType(2);
                orderParamHistory.setValid(1);
                orderParamHistory.setHistory(0L);
                orderParamHistory.setContractId(agreementId);
            }
            monthOrderParamHistoryRepository.saveBatchByParamHistoryId(newOrderParamHistoryList);
            return true;
        }
        //走到这里说明状态与预期不符 直接报错
        throw new BusinessException(ContractExceptionEnum.CONTRACT_ERROR_NULL.getCode(), ContractExceptionEnum.CONTRACT_ERROR_NULL.getValue());
    }

    /**
     * 获取模版所需要的数据
     *
     * @param orderId 订单id
     * @param type    参数类型 1:合同 2:补充协议
     * @return 合同模版所需参数
     */
    @Override
    public List<MonthOrderParamHistoryVO> getContractTemplateData(Long orderId, Integer type) {
        return appMonthContractSignRecordConverter.entity2MonthOrderParamHistoryVoList(monthOrderParamHistoryRepository.getContractTemplateData(orderId, type));
    }

    /**
     * 订单所有的有效参数列表
     *
     * @param orderId 订单id
     * @param valid   生效状态,为null则忽略该条件
     * @return 参数列表
     */
    @Override
    public List<MonthOrderParamHistoryVO> list(Long orderId, Integer valid) {
        return appMonthContractSignRecordConverter.entity2MonthOrderParamHistoryVoList(monthOrderParamHistoryRepository.list(orderId, valid));
    }

    /**
     * 获取合同对应的参数列表
     *
     * @param contractId 合同id
     * @return 参数list
     */
    @Override
    public List<MonthOrderParamHistoryVO> listByContractId(Long contractId) {
        return appMonthContractSignRecordConverter.entity2MonthOrderParamHistoryVoList(monthOrderParamHistoryRepository.listByContractId(contractId));
    }

    /**
     * 生效或失效协议对应的参数
     *
     * @param orderId    订单id
     * @param contractId 协议或合同id
     * @param type       类型 1:合同2:补充协议
     * @param valid      1:生效 2:失效
     * @return 操作结果
     */
    @Override
    public Boolean valid(Long orderId, Long contractId, Integer type, Integer valid) {
        return monthOrderParamHistoryRepository.valid(orderId, contractId, type, valid);
    }

    /**
     * 根据合同ID获取合同填充参数
     *
     * @param contractId
     */
    @Override
    public HashMap<String, String> getContractParamByContractId(Integer contractId) {
        HashMap<String, String> contentData = new HashMap<>();
        //获取当前合同信息
        MonthContractSignRecordVO byContractSignId = contractSignRecordService.getByContractSignId(Long.valueOf(contractId));
        //获取主合同信息
        MonthContractSignRecordEntity yzManContract = monthContractSignRecordRepository.getByOrderIdAndContractStatus(Math.toIntExact(byContractSignId.getGuideId()), TemplateContractTypeEnum.YZ_SAINTBELLA.code(), ContractStatusEnum.SIGNED.code());
        List<ContractSignRecordPaperEntity> paperEntityList = contractSignRecordPaperRepository.getByOrderId(Math.toIntExact(byContractSignId.getGuideId()), ContractStatusEnum.SIGNED.code(), TemplateContractTypeEnum.YZ_SAINTBELLA.code());

        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(Math.toIntExact(byContractSignId.getGuideId()));
        //提前离管合同解除协议填充
        if (TemplateContractTypeEnum.RELEASE.code().equals(byContractSignId.getTemplateContractType())) {
            //查询主合同签订信息
            //contract_no 协议编号
            //client_name 甲方客户
            //company_name 乙方公司
            //main_contract_time_str 主合同签订时间
            //main_contract_no 主合同编号
            //amount 未消费金额
            //amount_words 未消费金额大写
            //sign_time_str2 签署日期
            //in_room_time_str 入住房态时间
            //取提前离管表
            AheadOutRoomQuery roomQuery = new AheadOutRoomQuery();
            roomQuery.setOrderId(Math.toIntExact(byContractSignId.getGuideId()));
            AheadOutRoomEntity aheadOutRoomEntity = aheadOutRoomRepository.queryByOrderId(roomQuery);
            if (Objects.isNull(aheadOutRoomEntity)) {
                throw new BusinessException(ResultEnum.PARAM_ERROR, "提前离管未申请,不能签署合同");
            }
            if (AheadOutRoomEnum.STATE_DISENABLE.code().equals(aheadOutRoomEntity.getState())) {
                throw new BusinessException(ResultEnum.PARAM_ERROR, "合同已经签署无需在创建");
            }
            String main_contract_no = "";
            String main_contract_time_str = "";
            if (ObjectUtil.isNotEmpty(paperEntityList) && ObjectUtil.isNotEmpty(paperEntityList.get(0))) {
                main_contract_no = paperEntityList.get(0).getCode();
                main_contract_time_str = DateUtil.date(paperEntityList.get(0).getUpdatedAt() * 1000).toDateStr();
            }
            if (ObjectUtil.isNotEmpty(yzManContract)) {
                main_contract_no = yzManContract.getContractNo();
                main_contract_time_str = DateUtil.date(yzManContract.getUpdatedAt() * 1000).toDateStr();
            }
            contentData.put("client_name", heOrderUserSnapshotEntity.getName());
            contentData.put("company_name", BizConstant.COMPANY_NAME);
            contentData.put("in_room_time_str", DateUtil.formatDate(aheadOutRoomEntity.getCheckInRoomDate()));
            //未消费金额
            contentData.put("amount", RMBUtils.formatToseparaDecimals(BigDecimalUtil.divide(new BigDecimal(aheadOutRoomEntity.getRefundedAmount()), new BigDecimal(100))));
            //未消费金额大写
            contentData.put("amount_words", RMBUtils.numToRMBStr(BigDecimalUtil.divide(new BigDecimal(aheadOutRoomEntity.getRefundedAmount()), new BigDecimal(100)).doubleValue()));
            //主合同编号
            contentData.put("main_contract_no", main_contract_no);
            //主合同签订时间
            contentData.put("main_contract_time_str", main_contract_time_str);
        } else {
            List<MonthOrderParamHistoryVO> contractTemplateData = listByContractId(Long.valueOf(contractId));
            for (MonthOrderParamHistoryVO orderParamHistory : contractTemplateData) {
                contentData.put(orderParamHistory.getMark(), orderParamHistory.getParamValue());
            }

        }
        //添加合同编号,合同名称
        contentData.put("contract_no", byContractSignId.getContractNo());
        contentData.put("contract_name", byContractSignId.getContractName());
        //设置门店品牌
        String signTemplate;
        Long storeId = byContractSignId.getStoreId();
        CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(Math.toIntExact(storeId));
        if (ObjectUtil.isNotEmpty(cfgStoreEntity.getChildType()) && ObjectUtil.equals(cfgStoreEntity.getChildType(), StoreChildTypeEnum.BELLA_VILLA.getCode())) {
            //默认老合同签署BELLA  VILLA 为10
            signTemplate = "10";
        } else {
            signTemplate = String.valueOf(cfgStoreEntity.getType());
        }
        contentData.put("sign_template", signTemplate);
        //签字时间
        if (ObjectUtil.isNotEmpty(byContractSignId.getContractImageSignature()) && ObjectUtil.isNotEmpty(byContractSignId.getUpdatedAt()) && byContractSignId.getUpdatedAt() != 0) {
            contentData.put("sign_time_str2", DateUtil.format(new Date(byContractSignId.getUpdatedAt() * 1000), "yyyy-MM-dd"));
        }
        //签字图片地址
        if (ObjectUtil.isNotEmpty(byContractSignId.getContractImageSignature())) {
            contentData.put("contract_image_signature", byContractSignId.getContractImageSignature());
        }
        //合同PDF地址
        if (ObjectUtil.isNotEmpty(byContractSignId.getContractLongUrl())) {
            contentData.put("contract_long_url", byContractSignId.getContractLongUrl());
        }

        //公章url
        contentData.put("seal_url", sealUrl);
        return contentData;
    }

    /**
     * 添加合同ID到合同参数历史表(老订单迁移主合同id不存在的情况)
     *
     * @param orderId
     * @param type
     * @param contractId
     */
    @Override
    public Boolean setContractId(Long orderId, Integer type, Integer contractId) {
        return monthOrderParamHistoryRepository.setContractId(orderId, type, contractId);
    }

    List<MonthOrderParamHistoryVO> list(Long orderId, Long history, Integer valid) {
        return appMonthContractSignRecordConverter.entity2MonthOrderParamHistoryVoList(monthOrderParamHistoryRepository.list(orderId, history, valid));
    }
    /**
     * 只查询1，2
     *
     * @param orderId
     * @param history
     * @param valid
     * @param type
     * @return
     */
    List<MonthOrderParamHistoryVO> list(Long orderId, Long history, Integer valid,List<Integer> type) {
        return appMonthContractSignRecordConverter.entity2MonthOrderParamHistoryVoList(monthOrderParamHistoryRepository.list(orderId, history, valid,type));
    }

    /**
     * 订单所有的有效参数列表
     *
     * @param orderId 订单id
     * @param valid   生效状态,为null则忽略该条件
     * @param type    1,2
     * @return 参数列表
     */
    @Override
    public List<MonthOrderParamHistoryVO> list(Long orderId, Integer valid, List<Integer> type) {
        return appMonthContractSignRecordConverter.entity2MonthOrderParamHistoryVoList(monthOrderParamHistoryRepository.list(orderId, valid,type));
    }

}

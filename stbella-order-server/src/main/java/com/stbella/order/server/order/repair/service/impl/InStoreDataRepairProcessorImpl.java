package com.stbella.order.server.order.repair.service.impl;

import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.repository.OrderGoodsRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.context.component.imports.InStoreDataRepairImportDTO;
import com.stbella.order.server.order.repair.service.InStoreDataRepairProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 馆内数据修复处理器实现
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Slf4j
@Service
public class InStoreDataRepairProcessorImpl implements InStoreDataRepairProcessor {

    @Autowired
    private OrderRepository orderRepository;
    
    @Autowired
    private OrderGoodsRepository orderGoodsRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processDataRepair(List<InStoreDataRepairImportDTO> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            log.warn("数据列表为空，无需处理");
            return false;
        }

        log.info("开始处理馆内数据修复，共{}条数据", dataList.size());
        
        int successCount = 0;
        int failCount = 0;
        
        for (InStoreDataRepairImportDTO dto : dataList) {
            try {
                boolean result = processSingleRecord(dto);
                if (result) {
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (Exception e) {
                log.error("处理单条记录失败，订单号: {}, 错误: {}", dto.getOrderSn(), e.getMessage(), e);
                failCount++;
            }
        }
        
        log.info("馆内数据修复处理完成，成功: {}条，失败: {}条", successCount, failCount);
        return failCount == 0;
    }

    /**
     * 处理单条记录
     */
    private boolean processSingleRecord(InStoreDataRepairImportDTO dto) {
        log.info("开始处理订单: {}, 客户: {}", dto.getOrderSn(), dto.getCustomerName());
        
        // 1. 根据订单号查询订单
        HeOrderEntity orderEntity = orderRepository.getByOrderSn(dto.getOrderSn());
        if (orderEntity == null) {
            log.error("未找到订单: {}", dto.getOrderSn());
            return false;
        }
        
        // 2. 根据订单ID查询商品列表
        List<HeOrderGoodsEntity> goodsList = orderGoodsRepository.getAllItermByOrderId(orderEntity.getOrderId());
        if (CollectionUtils.isEmpty(goodsList)) {
            log.error("订单商品列表为空: {}", dto.getOrderSn());
            return false;
        }
        
        // 3. 匹配商品名称相同的商品
        HeOrderGoodsEntity matchedGoods = findMatchingGoods(goodsList, dto.getGoodsName());
        if (matchedGoods == null) {
            log.error("未找到匹配的商品: {}, 订单: {}", dto.getGoodsName(), dto.getOrderSn());
            return false;
        }
        
        // 4. 处理商品拆分
        return processGoodsSplit(matchedGoods, dto);
    }

    /**
     * 查找匹配的商品
     */
    private HeOrderGoodsEntity findMatchingGoods(List<HeOrderGoodsEntity> goodsList, String goodsName) {
        if (!StringUtils.hasText(goodsName)) {
            return null;
        }
        
        for (HeOrderGoodsEntity goods : goodsList) {
            if (goodsName.equals(goods.getGoodsName())) {
                return goods;
            }
        }
        
        // 如果完全匹配不到，尝试模糊匹配
        for (HeOrderGoodsEntity goods : goodsList) {
            if (goods.getGoodsName() != null && goods.getGoodsName().contains(goodsName)) {
                log.warn("使用模糊匹配找到商品: {} -> {}", goodsName, goods.getGoodsName());
                return goods;
            }
        }
        
        return null;
    }

    /**
     * 处理商品拆分
     */
    private boolean processGoodsSplit(HeOrderGoodsEntity originalGoods, InStoreDataRepairImportDTO dto) {
        List<HeOrderGoodsEntity> newGoodsList = new ArrayList<>();
        boolean hasSubGoods = false;
        
        try {
            // 创建在馆商品
            if (dto.getInStoreDays() != null && dto.getInStoreDays() > 0) {
                HeOrderGoodsEntity inStoreGoods = createInStoreGoods(originalGoods, dto);
                newGoodsList.add(inStoreGoods);
                hasSubGoods = true;
                log.info("创建在馆商品: {} - {}天", inStoreGoods.getGoodsName(), dto.getInStoreDays());
            }
            
            // 创建医院外派商品
            if (dto.getHospitalOutsourcingDays() != null && dto.getHospitalOutsourcingDays() > 0) {
                HeOrderGoodsEntity hospitalGoods = createHospitalOutsourcingGoods(originalGoods, dto);
                newGoodsList.add(hospitalGoods);
                hasSubGoods = true;
                log.info("创建医院外派商品: {} - {}天", hospitalGoods.getGoodsName(), dto.getHospitalOutsourcingDays());
            }
            
            // 创建到家外派商品
            if (dto.getHomeOutsourcingDays() != null && dto.getHomeOutsourcingDays() > 0) {
                HeOrderGoodsEntity homeGoods = createHomeOutsourcingGoods(originalGoods, dto);
                newGoodsList.add(homeGoods);
                hasSubGoods = true;
                log.info("创建到家外派商品: {} - {}天", homeGoods.getGoodsName(), dto.getHomeOutsourcingDays());
            }
            
            // 创建产康金商品
            if (dto.getProductionAmount() != null && dto.getProductionAmount().compareTo(BigDecimal.ZERO) > 0) {
                HeOrderGoodsEntity productionGoods = createProductionGoods(originalGoods, dto);
                newGoodsList.add(productionGoods);
                log.info("创建产康金商品: {} - {}", productionGoods.getGoodsName(), dto.getProductionAmount());
            }

            if (dto.getRepairAmount() != null && dto.getRepairAmount().compareTo(BigDecimal.ZERO) > 0) {
                // 创建服务类产后康复服务商品
                HeOrderGoodsEntity serviceGoods = createServiceGoods(originalGoods, dto);
                newGoodsList.add(serviceGoods);
                log.info("创建服务类商品: {}", serviceGoods.getGoodsName());
            }

            // 如果有子商品，更新原商品为组合商品
            if (hasSubGoods) {
                updateOriginalGoodsToCombo(originalGoods);
                log.info("原商品已更新为组合商品: {}", originalGoods.getGoodsName());
            }
            
            // 批量保存新商品
            if (!CollectionUtils.isEmpty(newGoodsList)) {
                boolean result = orderGoodsRepository.batchInsert(newGoodsList);
                if (result) {
                    log.info("成功保存{}个拆分商品，订单: {}", newGoodsList.size(), dto.getOrderSn());
                    return true;
                } else {
                    log.error("保存拆分商品失败，订单: {}", dto.getOrderSn());
                    return false;
                }
            } else {
                log.warn("没有需要创建的拆分商品，订单: {}", dto.getOrderSn());
                return true;
            }
            
        } catch (Exception e) {
            log.error("处理商品拆分失败，订单: {}, 错误: {}", dto.getOrderSn(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 创建在馆商品
     */
    private HeOrderGoodsEntity createInStoreGoods(HeOrderGoodsEntity originalGoods, InStoreDataRepairImportDTO dto) {
        // 深拷贝原商品
        HeOrderGoodsEntity newGoods = new HeOrderGoodsEntity();
        BeanUtils.copyProperties(originalGoods, newGoods);
        
        // 重置ID，让数据库自动生成
        newGoods.setId(null);
        
        // 设置新的商品信息
        newGoods.setGoodsName(dto.getGoodsName() + "在馆" + dto.getInStoreDays() + "天");
        newGoods.setGoodsNum(dto.getInStoreDays());  // 数量 = 天数
        newGoods.setNum(dto.getInStoreDays());       // 份数 = 天数
        
        // 计算单价（分摊金额 / 天数）
        BigDecimal inStoreAllocationAmount = dto.getInStoreAllocationAmount();
        if (Objects.isNull(inStoreAllocationAmount)) {
            inStoreAllocationAmount = BigDecimal.ZERO;
        }
        BigDecimal unitPrice = inStoreAllocationAmount.divide(new BigDecimal(dto.getInStoreDays()), 2, RoundingMode.HALF_UP);
        newGoods.setGoodsPricePay(unitPrice.multiply(new BigDecimal(100)).intValue()); // 转换为分
        newGoods.setPayAmount(inStoreAllocationAmount.multiply(new BigDecimal(100)).intValue()); // 总额，转换为分
        
        // 设置原价相关字段
        newGoods.setGoodsPriceOrgin(unitPrice.multiply(new BigDecimal(100)).intValue()); // 原价单价，转换为分
        newGoods.setAllocationOriginPrice(unitPrice.multiply(new BigDecimal(100)).intValue()); // 分摊原价，转换为分
        newGoods.setTotalAllocationOriginPrice(inStoreAllocationAmount.multiply(new BigDecimal(100)).intValue()); // 总分摊原价，转换为分
        
        // 设置商品类型和资产类型
        newGoods.setGoodsType(0);  // 在馆商品类型
        newGoods.setAssetType("0"); // 在馆资产类型
        newGoods.setParentCombineSn(originalGoods.getOrderGoodsSn()); // 设置父级组合sn
        
        // 设置创建时间
        newGoods.setCreatedAt(System.currentTimeMillis() / 1000);
        newGoods.setUpdatedAt(System.currentTimeMillis() / 1000);
        
        return newGoods;
    }

    /**
     * 创建医院外派商品
     */
    private HeOrderGoodsEntity createHospitalOutsourcingGoods(HeOrderGoodsEntity originalGoods, InStoreDataRepairImportDTO dto) {
        // 深拷贝原商品
        HeOrderGoodsEntity newGoods = new HeOrderGoodsEntity();
        BeanUtils.copyProperties(originalGoods, newGoods);
        
        // 重置ID，让数据库自动生成
        newGoods.setId(null);
        
        // 设置新的商品信息
        newGoods.setGoodsName(dto.getGoodsName() + "医院外派" + dto.getHospitalOutsourcingDays() + "天");
        newGoods.setGoodsNum(dto.getHospitalOutsourcingDays());  // 数量 = 天数
        newGoods.setNum(dto.getHospitalOutsourcingDays());       // 份数 = 天数
        
        // 计算单价（分摊金额 / 天数）
        BigDecimal hospitalOutsourcingAllocationAmount = dto.getHospitalOutsourcingAllocationAmount();
        if (Objects.isNull(hospitalOutsourcingAllocationAmount)) {
            hospitalOutsourcingAllocationAmount = BigDecimal.ZERO;
        }
        BigDecimal unitPrice = hospitalOutsourcingAllocationAmount.divide(new BigDecimal(dto.getHospitalOutsourcingDays()), 2, RoundingMode.HALF_UP);
        newGoods.setGoodsPricePay(unitPrice.multiply(new BigDecimal(100)).intValue()); // 转换为分
        newGoods.setPayAmount(hospitalOutsourcingAllocationAmount.multiply(new BigDecimal(100)).intValue()); // 总额，转换为分
        
        // 设置原价相关字段
        newGoods.setGoodsPriceOrgin(unitPrice.multiply(new BigDecimal(100)).intValue()); // 原价单价，转换为分
        newGoods.setAllocationOriginPrice(unitPrice.multiply(new BigDecimal(100)).intValue()); // 分摊原价，转换为分  
        newGoods.setTotalAllocationOriginPrice(hospitalOutsourcingAllocationAmount.multiply(new BigDecimal(100)).intValue()); // 总分摊原价，转换为分
        
        // 设置商品类型和资产类型
        newGoods.setGoodsType(7);  // 外派商品类型
        newGoods.setAssetType("7"); // 外派资产类型
        newGoods.setParentCombineSn(originalGoods.getOrderGoodsSn()); // 设置父级组合sn
        
        // 设置创建时间
        newGoods.setCreatedAt(System.currentTimeMillis() / 1000);
        newGoods.setUpdatedAt(System.currentTimeMillis() / 1000);
        
        return newGoods;
    }

    /**
     * 创建到家外派商品
     */
    private HeOrderGoodsEntity createHomeOutsourcingGoods(HeOrderGoodsEntity originalGoods, InStoreDataRepairImportDTO dto) {
        // 深拷贝原商品
        HeOrderGoodsEntity newGoods = new HeOrderGoodsEntity();
        BeanUtils.copyProperties(originalGoods, newGoods);
        
        // 重置ID，让数据库自动生成
        newGoods.setId(null);
        
        // 设置新的商品信息
        newGoods.setGoodsName(dto.getGoodsName() + "到家外派" + dto.getHomeOutsourcingDays() + "天");
        newGoods.setGoodsNum(dto.getHomeOutsourcingDays());  // 数量 = 天数
        newGoods.setNum(dto.getHomeOutsourcingDays());       // 份数 = 天数
        
        // 计算单价（分摊金额 / 天数）
        BigDecimal homeOutsourcingAllocationAmount = dto.getHomeOutsourcingAllocationAmount();
        if (Objects.isNull(homeOutsourcingAllocationAmount)) {
            homeOutsourcingAllocationAmount = BigDecimal.ZERO;
        }
        BigDecimal unitPrice = homeOutsourcingAllocationAmount.divide(new BigDecimal(dto.getHomeOutsourcingDays()), 2, RoundingMode.HALF_UP);
        newGoods.setGoodsPricePay(unitPrice.multiply(new BigDecimal(100)).intValue()); // 转换为分
        newGoods.setPayAmount(homeOutsourcingAllocationAmount.multiply(new BigDecimal(100)).intValue()); // 总额，转换为分
        
        // 设置原价相关字段
        newGoods.setGoodsPriceOrgin(unitPrice.multiply(new BigDecimal(100)).intValue()); // 原价单价，转换为分
        newGoods.setAllocationOriginPrice(unitPrice.multiply(new BigDecimal(100)).intValue()); // 分摊原价，转换为分
        newGoods.setTotalAllocationOriginPrice(homeOutsourcingAllocationAmount.multiply(new BigDecimal(100)).intValue()); // 总分摊原价，转换为分
        
        // 设置商品类型和资产类型
        newGoods.setGoodsType(7);  // 外派商品类型
        newGoods.setAssetType("7"); // 外派资产类型
        newGoods.setParentCombineSn(originalGoods.getOrderGoodsSn()); // 设置父级组合sn
        
        // 设置创建时间
        newGoods.setCreatedAt(System.currentTimeMillis() / 1000);
        newGoods.setUpdatedAt(System.currentTimeMillis() / 1000);
        
        return newGoods;
    }

    /**
     * 创建产康金商品
     */
    private HeOrderGoodsEntity createProductionGoods(HeOrderGoodsEntity originalGoods, InStoreDataRepairImportDTO dto) {
        // 深拷贝原商品
        HeOrderGoodsEntity newGoods = new HeOrderGoodsEntity();
        BeanUtils.copyProperties(originalGoods, newGoods);
        
        // 重置ID，让数据库自动生成
        newGoods.setId(null);
        
        // 设置产康金商品信息
        newGoods.setGoodsName(dto.getGoodsName() + "产康金");
        newGoods.setGoodsNum(1);  // 产康金数量为1
        newGoods.setNum(1);       // 份数为1
        
        // 设置产康金金额
        BigDecimal productionAmount = dto.getProductionAmount();
        newGoods.setGoodsPricePay(productionAmount.multiply(new BigDecimal(100)).intValue());
        newGoods.setPayAmount(productionAmount.multiply(new BigDecimal(100)).intValue());
        
        // 设置原价相关字段
        newGoods.setGoodsPriceOrgin(productionAmount.multiply(new BigDecimal(100)).intValue()); // 原价，转换为分
        newGoods.setAllocationOriginPrice(productionAmount.multiply(new BigDecimal(100)).intValue()); // 分摊原价，转换为分
        newGoods.setTotalAllocationOriginPrice(productionAmount.multiply(new BigDecimal(100)).intValue()); // 总分摊原价，转换为分
        
        // 设置商品类型和资产类型
        newGoods.setGoodsType(22); // 产康金商品类型
        newGoods.setAssetType("22"); // 产康金资产类型
        newGoods.setParentCombineSn(originalGoods.getOrderGoodsSn()); // 设置父级组合sn
        
        // 设置创建时间
        newGoods.setCreatedAt(System.currentTimeMillis() / 1000);
        newGoods.setUpdatedAt(System.currentTimeMillis() / 1000);
        
        return newGoods;
    }

    /**
     * 创建服务类产后康复服务商品
     */
    private HeOrderGoodsEntity createServiceGoods(HeOrderGoodsEntity originalGoods, InStoreDataRepairImportDTO dto) {
        // 深拷贝原商品
        HeOrderGoodsEntity newGoods = new HeOrderGoodsEntity();
        BeanUtils.copyProperties(originalGoods, newGoods);
        
        // 重置ID，让数据库自动生成
        newGoods.setId(null);
        
        // 设置服务类商品信息
        newGoods.setGoodsName("服务类/产后康复服务/" + dto.getGoodsName());
        newGoods.setGoodsNum(1);  // 服务类数量为1
        newGoods.setNum(1);       // 份数为1
        
        // 设置金额为0或默认值
        newGoods.setGoodsPricePay(0);
        newGoods.setPayAmount(0);
        
        // 设置原价相关字段为0
        newGoods.setGoodsPriceOrgin(0); // 原价为0
        newGoods.setAllocationOriginPrice(0); // 分摊原价为0
        newGoods.setTotalAllocationOriginPrice(0); // 总分摊原价为0
        
        // 设置商品类型和资产类型
        newGoods.setGoodsType(12); // 服务类商品类型
        newGoods.setAssetType("12"); // 服务类资产类型
        newGoods.setParentCombineSn(originalGoods.getOrderGoodsSn()); // 设置父级组合sn
        
        // 设置创建时间
        newGoods.setCreatedAt(System.currentTimeMillis() / 1000);
        newGoods.setUpdatedAt(System.currentTimeMillis() / 1000);
        
        return newGoods;
    }

    /**
     * 更新原商品为组合商品
     */
    private void updateOriginalGoodsToCombo(HeOrderGoodsEntity originalGoods) {
        // 更新原商品的商品类型和资产类型为组合商品
        originalGoods.setGoodsType(17);  // 组合商品类型
        originalGoods.setAssetType("17"); // 组合商品资产类型
        originalGoods.setUpdatedAt(System.currentTimeMillis() / 1000);
        
        // 更新到数据库
        orderGoodsRepository.update(originalGoods);
        log.info("原商品已更新为组合商品，ID: {}", originalGoods.getId());
    }
}
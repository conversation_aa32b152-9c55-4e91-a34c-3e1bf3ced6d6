package com.stbella.order.server.context.component.complaints.processor;

import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsEntity;
import com.stbella.order.server.order.month.enums.CustomerComplaintsStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

@Component
@Slf4j
@SnowballComponent(name = "客诉闭环审批发起失败后续", desc = "客诉闭环审批发起失败后续")
public class CustomerComplaintApproveFailProcessor implements IExecutableAtom<FlowContext> {


    @Override
    public void run(FlowContext bizContext) {

    }


    @Override
    public boolean condition(FlowContext context) {
        HeCustomerComplaintsEntity attribute = context.getAttribute(HeCustomerComplaintsEntity.class);
        //审批发起失败才走
        return CustomerComplaintsStatusEnum.APPROVAL_FAILED.getCode().equals(attribute.getComplaintStatus());
    }
}

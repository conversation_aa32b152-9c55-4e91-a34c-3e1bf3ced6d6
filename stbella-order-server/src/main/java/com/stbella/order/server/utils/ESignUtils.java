package com.stbella.order.server.utils;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.MessageFormat;

public class ESignUtils {

    /***
     * 计算请求签名值
     * @param message 待签名字符串
     * @param secret  密钥APP KEY
     * @return HmacSHA256计算后摘要值的Base64编码
     * @throws Exception 加密过程中的异常信息
     */
    public static String doSignatureBase64(String message, String secret) {
        String algorithm = "HmacSHA256";
        Mac hmacSha256;
        String digestBase64 = null;
        try {
            hmacSha256 = Mac.getInstance(algorithm);
            byte[] keyBytes = secret.getBytes(StandardCharsets.UTF_8);
            byte[] messageBytes = message.getBytes(StandardCharsets.UTF_8);
            hmacSha256.init(new SecretKeySpec(keyBytes, 0, keyBytes.length, algorithm));
            // 使用HmacSHA256对二进制数据消息Bytes计算摘要
            byte[] digestBytes = hmacSha256.doFinal(messageBytes);
            // 把摘要后的结果digestBytes使用Base64进行编码
            digestBase64 = Base64.encodeBase64String(digestBytes);
        } catch (NoSuchAlgorithmException e) {
            String msg = MessageFormat.format("不支持此算法: {0}", e.getMessage());
            Exception ex = new Exception(msg);
            ex.initCause(e);
            try {
                throw ex;
            } catch (Exception exc) {
                exc.printStackTrace();
            }
        } catch (InvalidKeyException e) {
            String msg = MessageFormat.format("无效的密钥规范: {0}", e.getMessage());
            Exception ex = new Exception(msg);
            ex.initCause(e);
            try {
                throw ex;
            } catch (Exception exc) {
                exc.printStackTrace();
            }
        }
        return digestBase64;
    }


    /**
     * 构建post请求待签名字符串
     *
     * @param requestUrl 请求url，不包含域名
     * @param contentMD5 Body的MD5值，只有当 Body非Form表单时才计算 MD5
     * @return 字符串
     */
    public static String createPostSignedString(String requestUrl, String contentMD5) {
        // 构建待签名字符串
        String method = "POST";
        String accept = "*/*";
        String contentType = "application/json;charset=UTF-8";
        // 可选，表示消息发送时间
        String date = "";
        // 根据e签宝开发文档，若无需对Headers进行签名，可以设为空
        String headers = "";

        StringBuffer sb = new StringBuffer();
        sb.append(method).append("\n").append(accept).append("\n").append(contentMD5).append("\n")
                .append(contentType).append("\n").append(date).append("\n");
        sb.append(headers).append(requestUrl);
        return String.valueOf(sb);

    }

    /**
     * 构建delete请求待签名字符串
     *
     * @param url 请求url
     * @return 字符串
     */
    public static String createDeleteSignedString(String url) {
        // 构建待签名字符串
        String method = "DELETE";
        String accept = "*/*";
        String contentType = "application/json; charset=UTF-8";
        // 可选，表示消息发送时间
        String date = "";
        // 根据e签宝开发文档，若无需对Headers进行签名，可以设为空
        String headers = "";
        // GET请求时ContentMD5为""
        String contentMD5 = "{}";

        StringBuffer sb = new StringBuffer();
        sb.append(method).append("\n").append(accept).append("\n").append(contentMD5).append("\n")
                .append(contentType).append("\n").append(date).append("\n");
        sb.append(headers).append(url);
        return String.valueOf(sb);
    }

    /**
     * 构建PUT请求待签名字符串
     *
     * @param requestUrl 请求url
     * @param contentMD5 请求body
     * @return 字符串
     */
    public static String createPutSignedString(String requestUrl, String contentMD5) {
        // 构建待签名字符串
        String method = "PUT";
        String accept = "*/*";
        String contentType = "application/json;charset=UTF-8";
        // 可选，表示消息发送时间
        String date = "";
        // 根据e签宝开发文档，若无需对Headers进行签名，可以设为空
        String headers = "";

        StringBuffer sb = new StringBuffer();
        sb.append(method).append("\n").append(accept).append("\n").append(contentMD5).append("\n")
                .append(contentType).append("\n").append(date).append("\n");
        sb.append(headers).append(requestUrl);
        return String.valueOf(sb);

    }

    /**
     * 构建delete请求待签名字符串
     *
     * @param url 请求url
     * @return 字符串
     */
    public static String createNewPutSignedString(String url) {
        // 构建待签名字符串
        String method = "PUT";
        String accept = "*/*";
        String contentType = "application/json; charset=UTF-8";
        // 可选，表示消息发送时间
        String date = "";
        // 根据e签宝开发文档，若无需对Headers进行签名，可以设为空
        String headers = "";
        // GET请求时ContentMD5为""
        String contentMD5 = "{}";

        StringBuffer sb = new StringBuffer();
        sb.append(method).append("\n").append(accept).append("\n").append(contentMD5).append("\n")
                .append(contentType).append("\n").append(date).append("\n");
        sb.append(headers).append(url);
        return String.valueOf(sb);
    }


    /***
     * 计算Content-MD5
     * @param str 待计算的消息（body中的信息）
     * @return MD5计算后摘要值的Base64编码(ContentMD5)
     * @throws Exception 加密过程中的异常信息
     */
    public static String doContentMD5(String str) {
        byte[] md5Bytes = null;
        MessageDigest md5 = null;
        String contentMD5 = null;
        try {
            md5 = MessageDigest.getInstance("MD5");
            // 计算md5函数
            md5.update(str.getBytes(StandardCharsets.UTF_8));
            // 获取文件MD5的二进制数组（128位）
            md5Bytes = md5.digest();
            // 把MD5摘要后的二进制数组md5Bytes使用Base64进行编码（而不是对32位的16进制字符串进行编码）
            contentMD5 = Base64.encodeBase64String(md5Bytes);
        } catch (NoSuchAlgorithmException e) {
            String msg = MessageFormat.format("不支持此算法: {0}", e.getMessage());
            Exception ex = new Exception(msg);
            ex.initCause(e);
            try {
                throw ex;
            } catch (Exception exc) {
                exc.printStackTrace();
            }
        }
        return contentMD5;
    }

    /**
     * 构建get请求待签名字符串
     *
     * @param url 请求url
     * @return 字符串
     */
    public static String createGetSignedString(String url) {
        // 构建待签名字符串
        String method = "GET";
        String accept = "*/*";
        String contentType = "application/json;charset=UTF-8";
        // 可选，表示消息发送时间
        String date = "";
        // 根据e签宝开发文档，若无需对Headers进行签名，可以设为空
        String headers = "";
        // GET请求时ContentMD5为""
        String contentMD5 = "{}";

        StringBuffer sb = new StringBuffer();
        sb.append(method).append("\n").append(accept).append("\n").append("\n")
                .append(contentType).append("\n").append(date).append("\n");
        sb.append(headers).append(url);
        return String.valueOf(sb);
    }

    /***
     * 计算字符串的Content-MD5
     * @param str 文件路径
     * @return
     */
    public static String getStringContentMD5(String str) {
        // 获取文件MD5的二进制数组（128位）
        byte[] bytes = getFileMD5Bytes1282(str);
        // 对文件MD5的二进制数组进行base64编码
        return new String(Base64.encodeBase64(bytes));
    }

    /***
     * 获取文件MD5-二进制数组（128位）
     *
     * @param filePath 文件路径
     * @return byte[]
     * @throws IOException
     */
    public static byte[] getFileMD5Bytes1282(String filePath) {
        FileInputStream fis = null;
        byte[] md5Bytes = null;
        try {
            File file = new File(filePath);
            fis = new FileInputStream(file);
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] buffer = new byte[1024];
            int length = -1;
            while ((length = fis.read(buffer, 0, 1024)) != -1) {
                md5.update(buffer, 0, length);
            }
            md5Bytes = md5.digest();
            fis.close();
        } catch (FileNotFoundException e) {
            System.out.println(e.getMessage());
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            System.out.println(e.getMessage());
            e.printStackTrace();
        } catch (IOException e) {
            System.out.println(e.getMessage());
            e.printStackTrace();
        }
        return md5Bytes;
    }

    /**
     * 获取文件字节流
     *
     * @param filePath {@link String} 文件地址
     * @return byte[]
     * @date 2019年7月10日 上午9:17:00
     * <AUTHOR>
     */
    public static byte[] getBytes(String filePath) {
        File file = new File(filePath);
        FileInputStream fis = null;
        byte[] buffer = null;
        try {
            fis = new FileInputStream(file);
            buffer = new byte[(int) file.length()];
            fis.read(buffer);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return buffer;
    }

    /***
     * 获取请求签名值
     *
     * @param data
     *            加密前数据
     * @param key
     *            密钥
     * @param algorithm
     *            HmacMD5 HmacSHA1 HmacSHA256 HmacSHA384 HmacSHA512
     * @param encoding
     *            编码格式
     * @return HMAC加密后16进制字符串
     * @throws
     */
    public static String getSignature(String data, String key, String algorithm, String encoding) {
        Mac mac = null;
        try {
            mac = Mac.getInstance(algorithm);
            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(encoding), algorithm);
            mac.init(secretKey);
            mac.update(data.getBytes(encoding));
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            System.out.println("获取Signature签名信息异常：" + e.getMessage());
            return null;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            System.out.println("获取Signature签名信息异常：" + e.getMessage());
            return null;
        } catch (InvalidKeyException e) {
            e.printStackTrace();
            System.out.println("获取Signature签名信息异常：" + e.getMessage());
            return null;
        }
        return byte2hex(mac.doFinal());
    }

    /***
     * 将byte[]转成16进制字符串
     *
     * @param data
     *
     * @return 16进制字符串
     */
    public static String byte2hex(byte[] data) {
        StringBuilder hash = new StringBuilder();
        String stmp;
        for (int n = 0; data != null && n < data.length; n++) {
            stmp = Integer.toHexString(data[n] & 0XFF);
            if (stmp.length() == 1)
                hash.append('0');
            hash.append(stmp);
        }
        return hash.toString();
    }

}

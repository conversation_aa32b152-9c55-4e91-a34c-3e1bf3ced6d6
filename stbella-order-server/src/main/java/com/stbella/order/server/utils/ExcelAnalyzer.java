package com.stbella.order.server.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.stbella.order.server.context.component.imports.InStoreDataRepairImportDTO;
import com.stbella.order.server.order.repair.service.InStoreDataRepairProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Excel文件分析工具
 */
@Component
public class ExcelAnalyzer {

    @Autowired
    private InStoreDataRepairProcessor repairProcessor;

    public static void main(String[] args) {
        List<InStoreDataRepairImportDTO> resultList = parseExcelToDTO();

        System.out.println("\n=== 解析结果 ===");
        System.out.println("共解析到 " + resultList.size() + " 条数据");

        // 输出前5条数据作为示例
        System.out.println("\n前5条数据示例:");
        for (int i = 0; i < Math.min(5, resultList.size()); i++) {
            InStoreDataRepairImportDTO dto = resultList.get(i);
            System.out.println("第" + (i + 1) + "条: " + "门店=" + dto.getStoreName() + ", 客户=" + dto.getCustomerName() + ", 订单号=" + dto.getOrderSn() + ", 总金额=" + dto.getTotalAmount());
        }
    }

    /**
     * 解析并处理Excel数据
     */
    public boolean parseAndProcessExcel() {
        // 1. 解析Excel文件
        List<InStoreDataRepairImportDTO> dataList = parseExcelToDTO();
        
        if (dataList.isEmpty()) {
            System.err.println("没有解析到任何数据");
            return false;
        }
        
        System.out.println("成功解析到 " + dataList.size() + " 条数据，开始处理...");
        
        // 2. 处理数据
        try {
            boolean result = repairProcessor.processDataRepair(dataList);
            if (result) {
                System.out.println("所有数据处理成功！");
            } else {
                System.err.println("部分数据处理失败，请查看日志");
            }
            return result;
        } catch (Exception e) {
            System.err.println("数据处理失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 解析Excel文件为DTO列表
     */
    public static List<InStoreDataRepairImportDTO> parseExcelToDTO() {
        // 尝试不同的路径
        String[] possiblePaths = {"../order-deploy/src/main/resources/馆内数据修复.xlsx", "../../order-deploy/src/main/resources/馆内数据修复.xlsx", "./order-deploy/src/main/resources/馆内数据修复.xlsx", "order-deploy/src/main/resources/馆内数据修复.xlsx", "/Users/<USER>/IdeaProjects/bk/stbella-order-bak/order-deploy/src/main/resources/馆内数据修复.xlsx"};

        String fileName = null;
        for (String path : possiblePaths) {
            java.io.File file = new java.io.File(path);
            if (file.exists()) {
                fileName = path;
                System.out.println("找到Excel文件: " + fileName);
                break;
            }
        }

        if (fileName == null) {
            System.err.println("找不到Excel文件，尝试的路径:");
            for (String path : possiblePaths) {
                System.err.println("  " + path);
            }
            return new ArrayList<>();
        }

        // 使用EasyExcel直接读取为DTO对象
        List<InStoreDataRepairImportDTO> dataList = new ArrayList<>();

        try {
            EasyExcel.read(fileName, InStoreDataRepairImportDTO.class, new ReadListener<InStoreDataRepairImportDTO>() {
                private int rowCount = 0;

                @Override
                public void invoke(InStoreDataRepairImportDTO data, AnalysisContext context) {
                    rowCount++;
                    System.out.println("解析第" + rowCount + "行: " + data.getCustomerName() + " - " + data.getOrderSn());
                    dataList.add(data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    System.out.println("Excel解析完成，共" + rowCount + "行数据");
                }
            }).sheet().doRead();

        } catch (Exception e) {
            System.err.println("解析Excel文件失败: " + e.getMessage());
            e.printStackTrace();
        }

        return dataList;
    }

    /**
     * 生成完整的Java实体类
     */
    private static void generateJavaClass(Map<Integer, String> headerRow) {
        String className = "InStoreDataRepairExcel";

        System.out.println("package com.stbella.order.server.order.repair.excel;");
        System.out.println();
        System.out.println("import com.alibaba.excel.annotation.ExcelProperty;");
        System.out.println("import io.swagger.annotations.ApiModelProperty;");
        System.out.println("import lombok.Data;");
        System.out.println();
        System.out.println("import java.io.Serializable;");
        System.out.println("import java.math.BigDecimal;");
        System.out.println();
        System.out.println("/**");
        System.out.println(" * 馆内数据修复Excel实体类");
        System.out.println(" * 自动生成于: " + java.time.LocalDateTime.now());
        System.out.println(" */");
        System.out.println("@Data");
        System.out.println("public class " + className + " implements Serializable {");
        System.out.println("    private static final long serialVersionUID = 1L;");
        System.out.println();

        // 生成字段
        headerRow.forEach((columnIndex, columnValue) -> {
            String fieldName = generateFieldName(columnValue);
            System.out.println("    @ExcelProperty(value = \"" + columnValue + "\")");
            System.out.println("    @ApiModelProperty(\"" + columnValue + "\")");
            System.out.println("    private String " + fieldName + ";");
            System.out.println();
        });

        System.out.println("}");

        // 额外生成DTO类
        System.out.println("\n=== 对应的DTO类 ===");
        generateDTOClass(headerRow);
    }

    /**
     * 生成对应的DTO类
     */
    private static void generateDTOClass(Map<Integer, String> headerRow) {
        String className = "InStoreDataRepairImportDTO";

        System.out.println("package com.stbella.order.server.context.component.imports;");
        System.out.println();
        System.out.println("import com.alibaba.excel.annotation.ExcelProperty;");
        System.out.println("import io.swagger.annotations.ApiModelProperty;");
        System.out.println("import lombok.Data;");
        System.out.println();
        System.out.println("import java.io.Serializable;");
        System.out.println("import java.math.BigDecimal;");
        System.out.println();
        System.out.println("/**");
        System.out.println(" * 馆内数据修复导入DTO");
        System.out.println(" * 自动生成于: " + java.time.LocalDateTime.now());
        System.out.println(" */");
        System.out.println("@Data");
        System.out.println("public class " + className + " implements Serializable {");
        System.out.println("    private static final long serialVersionUID = 1L;");
        System.out.println();

        // 生成字段，DTO使用更合适的数据类型
        headerRow.forEach((columnIndex, columnValue) -> {
            String fieldName = generateFieldName(columnValue);
            String fieldType = determineFieldType(columnValue);
            System.out.println("    @ExcelProperty(value = \"" + columnValue + "\")");
            System.out.println("    @ApiModelProperty(\"" + columnValue + "\")");
            System.out.println("    private " + fieldType + " " + fieldName + ";");
            System.out.println();
        });

        System.out.println("}");
    }

    /**
     * 根据列名确定字段类型
     */
    private static String determineFieldType(String columnName) {
        if (columnName == null) return "String";

        String lowerName = columnName.toLowerCase();
        if (lowerName.contains("金额") || lowerName.contains("价格") || lowerName.contains("费用")) {
            return "BigDecimal";
        }
        if (lowerName.contains("数量") || lowerName.contains("次数") || lowerName.contains("count")) {
            return "Integer";
        }
        if (lowerName.contains("时间") || lowerName.contains("日期") || lowerName.contains("time") || lowerName.contains("date")) {
            return "String"; // 可以考虑使用LocalDateTime，但String更通用
        }
        return "String";
    }

    /**
     * 根据Excel列名生成Java字段名
     */
    private static String generateFieldName(String columnName) {
        if (columnName == null || columnName.trim().isEmpty()) {
            return "unknownField";
        }

        String trimmed = columnName.trim();

        // 中文到英文的映射
        switch (trimmed) {
            case "订单ID":
            case "订单id":
                return "orderId";
            case "订单编号":
                return "orderSn";
            case "用户ID":
            case "用户id":
                return "userId";
            case "门店ID":
            case "门店id":
                return "storeId";
            case "门店名称":
                return "storeName";
            case "产品ID":
            case "产品id":
                return "productId";
            case "产品名称":
                return "productName";
            case "商品名称":
                return "goodsName";
            case "原始金额":
                return "originalAmount";
            case "修复后金额":
                return "repairedAmount";
            case "数量":
                return "quantity";
            case "订单状态":
                return "orderStatus";
            case "创建时间":
                return "createTime";
            case "更新时间":
                return "updateTime";
            case "修复时间":
                return "repairTime";
            case "修复原因":
                return "repairReason";
            case "操作人":
                return "operator";
            case "备注":
                return "remark";
            case "手机号":
            case "手机号码":
                return "mobile";
            case "客户姓名":
            case "姓名":
                return "customerName";
            case "价格":
                return "price";
            case "金额":
                return "amount";
            case "支付状态":
                return "payStatus";
            case "支付时间":
                return "payTime";
            case "退款金额":
                return "refundAmount";
            case "类型":
                return "type";
            case "分类":
                return "category";
            case "来源":
                return "source";
            case "渠道":
                return "channel";
            case "地址":
                return "address";
            case "省份":
                return "province";
            case "城市":
                return "city";
            case "区县":
                return "district";
            default:
                // 如果没有匹配的映射，尝试自动转换
                return autoGenerateFieldName(trimmed);
        }
    }

    /**
     * 自动生成字段名（当没有预定义映射时使用）
     */
    private static String autoGenerateFieldName(String columnName) {
        // 简单的字段名生成逻辑
        String fieldName = columnName.replace("ID", "Id").replace("编号", "Sn").replace("名称", "Name").replace("金额", "Amount").replace("时间", "Time").replace("状态", "Status").replace("数量", "Quantity").replace("原因", "Reason").replace("备注", "Remark");

        // 转换为驼峰命名
        StringBuilder result = new StringBuilder();
        boolean nextUpper = false;

        for (char c : fieldName.toCharArray()) {
            if (Character.isLetter(c) || Character.isDigit(c)) {
                if (nextUpper) {
                    result.append(Character.toUpperCase(c));
                    nextUpper = false;
                } else {
                    result.append(Character.toLowerCase(c));
                }
            } else {
                nextUpper = true;
            }
        }

        return result.length() > 0 ? result.toString() : "field" + System.currentTimeMillis();
    }
}
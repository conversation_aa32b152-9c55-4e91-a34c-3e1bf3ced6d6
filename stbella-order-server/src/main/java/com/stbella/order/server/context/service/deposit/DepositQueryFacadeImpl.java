package com.stbella.order.server.context.service.deposit;

import cn.hutool.core.util.NumberUtil;
import com.stbella.core.result.Result;
import com.stbella.order.domain.order.factory.OrderFactory;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.server.context.component.settlement.OrderSettlementAssembler;
import com.stbella.order.server.convert.ApporderSettlementConverter;
import com.stbella.order.server.order.month.component.OrderIncomeAssembler;
import com.stbella.order.server.order.month.req.OrderMonthIncomeQuery;
import com.stbella.order.server.order.month.request.standard.OrderSettlementQuery;
import com.stbella.order.server.order.month.res.DepositSettlementVO;
import com.stbella.order.server.order.month.res.OrderSettlementVO;
import com.stbella.order.server.utils.BigDecimalUtil;
import com.stbella.platform.order.api.deposit.DepositQueryFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.core.context.FlowContext;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-08-07  11:43
 * @Description: 押金支付查询
 * @Version: 1.0
 */
@Service
@Slf4j
public class DepositQueryFacadeImpl implements DepositQueryFacade {

    @Resource
    private OrderIncomeAssembler orderIncomeAssembler;
    @Resource
    OrderSettlementAssembler orderSettlementAssembler;
    @Resource
    OrderFactory orderFactory;
    @Resource
    ApporderSettlementConverter apporderSettlementConverter;

    /**
     * 押金支付查询
     *
     * @param query
     * @return
     */
    @Override
    public Result<DepositSettlementVO> queryDeposit(OrderMonthIncomeQuery query) {

        DepositSettlementVO deposit = null;
        if (Objects.isNull(query.getOrderId()) || query.getOrderId().intValue() == 0){
            //老的押金计算
            deposit = orderIncomeAssembler.queryDepositAssembler(query);
        }else {
            // 新押金
            HeOrderEntity order = orderFactory.restore(query.getOrderId());
            OrderSettlementQuery orderSettlementQuery = new OrderSettlementQuery()
                    .setOrderId(query.getOrderId())
                    .setType(0)
                    .setClientUid(order.getClientUid())
                    .setOrderSn(order.getOrderSn());

            FlowContext context = new FlowContext();
            context.setAttribute(OrderSettlementQuery.class, orderSettlementQuery);
            SnowballFlowLauncher.fire(context, orderSettlementAssembler);
            OrderSettlementVO settlementVO = context.getAttribute(OrderSettlementVO.class);
            //转化
            deposit = apporderSettlementConverter.order2Deposite(settlementVO);
            BigDecimal paidAmount = settlementVO.getPaidAmount().add(settlementVO.getUnConfirmAmount());

            deposit.setReceivableAmount(deposit.getDeposit().subtract(paidAmount));
            if (deposit.getDeposit().equals(paidAmount)){
                deposit.setIsFullAmount(true);
                deposit.setPercentagePayment(100);
            }else {
                deposit.setPercentagePayment(BigDecimalUtil.divideRoundingModeAndScale(paidAmount, deposit.getDeposit(), BigDecimal.ROUND_DOWN, 2).multiply(new BigDecimal(100)).intValue());
            }
        }


        return Result.success(deposit);
    }
}

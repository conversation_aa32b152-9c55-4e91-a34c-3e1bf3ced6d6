package com.stbella.order.server.context.component.ui;

import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.month.OrderButtonEnum;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.repository.OrderGoodsRepository;
import com.stbella.order.server.order.month.res.OrderOperateButton;
import com.stbella.order.server.utils.OrderProcessCheckUtil;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: ji<PERSON><PERSON><PERSON>
 * @CreateTime: 2024-08-12  16:28
 * @Description: 退款
 */
@Component
@Slf4j
public class RefundOperate implements OrderOperate {

    @Resource
    private RuleLinkClient ruleLinkClient;

    @Resource
    private OrderGoodsRepository orderGoodsRepository;

    @Resource
    private OrderProcessCheckUtil orderProcessCheckUtil;

    @Override
    public void setPriority(int priority) {

    }

    @Override
    public int getPriority() {
        return 4;
    }

    /**
     * 生成订单操作按钮
     *
     * @param context
     * @return
     */
    @Override
    public OrderOperateButton generateButton(OrderOperateContext context) {


        /**
         【订单列表/我的订单(新)】，满足以下任一条件隐藏“发起退款”、显示“客诉工单”按钮：
         A、该笔订单在核销表中有已完成的核销记录
         B、该笔订单有入住记录：根据入住日期是否有值判断，有值时满足条件
         */
        HeOrderEntity order = context.getOrder();

        OrderOperateButton button = OrderOperateButton.builder().code(getButtonEnum().getCode()).value(getButtonEnum().getValue()).build();

        boolean realAmount = order.getRealAmount() + order.getProductionAmountPay() > 0;
        if ((!realAmount)) {
            log.info("订单未付款，不显示按钮");
            return null;
        }

        Integer orderType = order.getOrderType();
        if (OmniOrderTypeEnum.MONTH_ORDER.code().equals(orderType) || OmniOrderTypeEnum.SMALL_MONTH_ORDER.code().equals(orderType)) {
            if (order.checkIn()) {
                log.info("订单已入住，不显示按钮");
                return null;
            }
            return hasRefundRecord(button, context);
        } else if (OmniOrderTypeEnum.PRODUCTION_ORDER.code().equals(orderType)) {
            Map<String, String> factMap = new HashMap<>();
            factMap.put("goodsAssetType", "goods_type_manage");
            ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req("assetType", factMap);
            HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
            Integer verificationStatus = order.getVerificationStatus();
            if (Objects.nonNull(hitRuleVo) && StringUtils.isNotEmpty(hitRuleVo.getSimpleRuleValue())) {
                String[] goodsAssetArray = hitRuleVo.getSimpleRuleValue().split(",");
                if (goodsAssetArray.length > 0) {

                    List<String> list = Arrays.asList(goodsAssetArray);
                    List<HeOrderGoodsEntity> orderGoodsEntityList = orderGoodsRepository.getByOrderIdList(Collections.singletonList(order.getOrderId()));
                    HeOrderGoodsEntity heOrderGoodsEntity = orderGoodsEntityList.stream().filter(orderGoods -> list.contains(orderGoods.getAssetType())).findFirst().orElse(null);
                    if (Objects.nonNull(heOrderGoodsEntity) && Integer.valueOf(1).equals(verificationStatus)) {
                        return null;
                    }
                }
            }

            if (Integer.valueOf(0).equals(verificationStatus)) {
                return hasRefundRecord(button, context);
            }
        } else if (OmniOrderTypeEnum.DEPOSIT_ORDER.code().equals(orderType)) {
            return hasRefundRecord(button, context);
        } else  if (OmniOrderTypeEnum.DJ_MOTHER_CARE_ORDER.code().equals(orderType) || OmniOrderTypeEnum.DJ_INFANT_CARE_ORDER.code().equals(orderType)) {
            return hasRefundRecord(button, context);
        }
        return null;
    }

    private OrderOperateButton hasRefundRecord(OrderOperateButton button, OrderOperateContext context) {
        //如果订单有付钱，但是没有核销或者入住，就显示按钮
        if (orderProcessCheckUtil.hasUnfinishedRefund(context.getOrder().getOrderId())) {
            button.setText("当前订单存在退款审批中流程，请等待审批完成后再次尝试！");
        }
        return button;
    }

    /**
     * 获取按钮枚举
     *
     * @return
     */
    @Override
    public OrderButtonEnum getButtonEnum() {
        return OrderButtonEnum.INITIATING_REFUND;
    }

    @Override
    public int compareTo(@NotNull IPriority o) {
        return this.getPriority() - o.getPriority();
    }
}

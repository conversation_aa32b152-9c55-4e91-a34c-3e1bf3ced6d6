package com.stbella.order.server.order.month.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.stbella.core.base.Operator;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.core.utils.BigDecimalUtil;
import com.stbella.customer.server.ecp.request.OrderMonthClientBailorCacheRequest;
import com.stbella.customer.server.ecp.request.OrderMonthClientCacheRequest;
import com.stbella.customer.server.ecp.service.TabClientBailorService;
import com.stbella.customer.server.ecp.service.TabClientService;
import com.stbella.customer.server.ecp.vo.OrderMonthClientBailorCacheVO;
import com.stbella.customer.server.ecp.vo.OrderMonthClientCacheVO;
import com.stbella.notice.server.OaProcessIdRelationService;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.core.*;
import com.stbella.order.common.enums.month.*;
import com.stbella.order.common.enums.production.OrderGiftExtendTypeEnum;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.common.utils.JsonUtil;
import com.stbella.order.domain.client.MessageClient;
import com.stbella.order.domain.order.month.dto.OrderTaskJsonDTO;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.month.service.HeOrderAttachmentDomainService;
import com.stbella.order.domain.order.month.service.OrderMonthWxDomainService;
import com.stbella.order.domain.order.production.*;
import com.stbella.order.domain.order.production.dto.OrderGiftExtendRoomChangeDTO;
import com.stbella.order.domain.order.production.dto.OrderGiftExtendRoomDTO;
import com.stbella.order.domain.order.production.dto.OrderGiftExtendStayOverDTO;
import com.stbella.order.domain.order.service.OrderIncomeDomainService;
import com.stbella.order.domain.repository.*;
import com.stbella.order.domain.utils.AsyncService;
import com.stbella.order.domain.utils.dto.AsyncResultDTO;
import com.stbella.order.server.async.AsyncOrder;
import com.stbella.order.server.context.component.StoreCurrencyContainer;
import com.stbella.order.server.context.component.processor.FixGoodsAllocateProcessor;
import com.stbella.order.server.context.component.processor.pay.AllocationPaymentFixProcessor;
import com.stbella.order.server.context.component.processor.pay.AllocationPaymentProcessor;
import com.stbella.order.server.contract.dto.Old2NewOrderDTO;
import com.stbella.order.server.contract.dto.OrderV1V2RelatedDTO;
import com.stbella.order.server.contract.month.component.MonthContractAssembler;
import com.stbella.order.server.contract.req.MonthContractQuery;
import com.stbella.order.server.contract.req.OrderParamHistoryPushDTO;
import com.stbella.order.server.contract.req.OrderParamHistoryValuePushDTO;
import com.stbella.order.server.contract.res.ContractSignDetailVO;
import com.stbella.order.server.contract.service.month.MonthContractAuthenticationService;
import com.stbella.order.server.contract.service.month.MonthOrderParamHistoryService;
import com.stbella.order.server.convert.OrderConvert;
import com.stbella.order.server.manager.*;
import com.stbella.order.server.order.month.component.EcpOrderSyncAssembler;
import com.stbella.order.server.order.month.component.OrderAttachmentAssembler;
import com.stbella.order.server.order.month.enums.*;
import com.stbella.order.server.order.month.req.*;
import com.stbella.order.server.order.month.request.mail.SendMailToFinanceRequest;
import com.stbella.order.server.order.month.request.pay.IncomeRecordTransferRequest;
import com.stbella.order.server.order.month.res.*;
import com.stbella.order.server.order.month.service.AddressQueryService;
import com.stbella.order.server.order.month.service.MonthOrderWxCommandService;
import com.stbella.order.server.order.month.service.MonthOrderWxQueryService;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.order.server.producer.OrderEventProducer;
import com.stbella.order.server.producer.OrderTransferProducer;
import com.stbella.order.server.utils.IdGenUtils;
import com.stbella.store.goodz.res.RoomByGoodsIdVo;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import top.primecare.snowball.flow.core.context.FlowContext;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 月子餐订单服务 服务实现类
 */
@Service
@DubboService
@Slf4j
public class MonthOrderWxCommandServiceImpl implements MonthOrderWxCommandService {

    @Resource
    private OrderMonthWxDomainService orderMonthWxDomainService;
    @Resource
    private GoodsManager goodsManager;
    @Resource
    private TabClientManager tabClientManager;
    @Resource
    private AsyncService asyncService;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderGiftExtendRepository orderGiftExtendRepository;
    @Resource
    private OrderAdditionalRevenueRepository orderAdditionalRevenueRepository;
    @Resource
    private OrderVoucherRepository orderVoucherRepository;
    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;
    @Resource
    private OrderGoodsRepository orderGoodsRepository;
    @Resource
    private MonthOrderWxQueryService monthOrderWxQueryService;
    @Resource
    private MonthOrderParamHistoryService monthOrderParamHistoryService;

    @Resource
    private OrderBailorSnapshotRepository orderBailorSnapshotRepository;
    @DubboReference
    private TabClientService tabClientService;
    @Resource
    private StoreRepository cfgStoreRepository;
    @Resource
    private AddressQueryService addressQueryService;
    @Resource
    private OrderConvert orderConvert;
    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @DubboReference
    private TabClientBailorService tabClientBailorService;
    @Resource
    private MonthContractAuthenticationService contractAuthenticationService;
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private MonthContractSignRecordRepository monthContractSignRecordRepository;
    @Resource
    private MonthContractSignAgreementRepository monthContractSignAgreementRepository;
    @Resource
    private ContractSignRecordPaperRepository contractSignRecordPaperRepository;
    @Resource
    private ScrmManager scrmManager;
    @Resource
    private OrderAttachmentAssembler orderAttachmentAssembler;
    @Resource
    private HeOrderAttachmentDomainService heOrderAttachmentDomainService;
    @Resource
    private OrderRoomTypeChangeRecordRepository orderRoomTypeChangeRecordRepository;
    @Resource
    private MailManager mailManager;
    @Resource
    private StoreRepository storeRepository;
    @Resource
    private GoodsRepository goodsRepository;
    @Resource
    private HeUserProductionAmountListRepository heUserProductionAmountListRepository;
    @Resource
    private HeUserProductionAmountPiLogRepository heUserProductionAmountPiLogRepository;
    @Resource
    private DistrictRepository districtRepository;
    @Resource
    private MessageClient messageClient;
    @Resource
    private GoodsSkuRepository goodsSkuRepository;
    @Resource
    private HeProjectRepository heProjectRepository;
    @Resource
    private UserRepository userRepository;
    @Resource
    private HeOrderSyncLogRepository heOrderSyncLogRepository;

    @Resource
    private IncomeProofRecordRepository incomeProofRecordRepository;
    @Resource
    private CareManager careManager;
    @Resource
    private EcpOrderSyncAssembler ecpOrderSyncAssembler;
    @Resource
    private HeOrderIncomeRecordTransferLogRepository heOrderIncomeRecordTransferLogRepository;
    @DubboReference
    private OaProcessIdRelationService oaProcessIdRelationService;
    @Resource
    private HeTaskRepository heTaskRepository;
    @Resource
    private OrderAssetTradeService orderAssetTradeService;
    @Resource
    @Lazy
    private AsyncOrder asyncOrder;

    @Resource
    private ContractManager contractManager;
    @Resource
    private HeOrderGoodsSbarRepository heOrderGoodsSbarRepository;
    @Resource
    private OrderEventProducer orderEventProducer;

    @Resource
    private MonthContractAssembler monthContractAssembler;


    private BaseManager baseManager;
    @Resource
    private AllocationPaymentProcessor allocationPaymentProcessor;

    @Resource
    private AllocationPaymentFixProcessor allocationPaymentFixProcessor;
    @Resource
    private OrderTransferProducer orderTransferProducer;
    @Resource
    IncomePaidAllocationRepository allocationRepository;
    @Resource
    OrderIncomeDomainService incomeDomainService;

    @Resource
    FixGoodsAllocateProcessor fixGoodsAllocateProcessor;


    @Override
    public Result orderGoodsCache(OrderGoodsCacheReq req) {
        OrderGoodsCacheReq orderGoodsCache = orderRepository.getOrderGoodsCache(req);
        //套餐不一致
        if (ObjectUtils.isNotEmpty(orderGoodsCache) && ObjectUtil.notEqual(orderGoodsCache.getGoodsId(), req.getGoodsId())) {
            //删除所有的加收项
            orderRepository.delOrderMonthAdditionalRevenueCache(req);
        }
        orderMonthWxDomainService.orderGoodsCache(req);
        return Result.success();
    }

    @Override
    public Result orderMonthGiftExtendCache(OrderMonthGiftExtendCacheReq req) {
        orderMonthWxDomainService.orderMonthGiftExtendCache(req);
        return Result.success();
    }

    /**
     * 创建订单
     *
     * @param req
     */
    @Override
//    @GlobalTransactional(rollbackFor = Exception.class)
    public Result<AddOrderVO> addOrderMonth(OrderMonthReq req) {
        log.info("创建订单参数={}", JSONUtil.toJsonStr(req));
        //统一设置操作人和操作
        req.setSallerName();

        //生成订单SN
        String orderSn = IdGenUtils.createOrderNo(new Date(), OrderTypeEnum.STANDARD.getCode());
        //多线程查询
        AsyncResultDTO<OrderMonthGoodsCacheVO, List<OrderGiftCacheByUserVO>, List<OrderAdditionalRevenueCacheVO>, OrderDiscountsCacheVO> resultDTO = multiThreadGetOrderInfo(req);


        log.info("创建订单获取套餐缓存数据：{}", JsonUtil.write(resultDTO.getResult1()));

        //保存订单
        HeOrderEntity heOrderEntity = orderRepository.convertOrderMonthReq2HeOrderEntity(true, req, resultDTO);
        heOrderEntity.setOrderSn(orderSn);
        heOrderEntity.setCreatedAt(System.currentTimeMillis() / 1000);
        heOrderEntity.setUpdatedAt(heOrderEntity.getCreatedAt());
        heOrderEntity.setCreateBy(heOrderEntity.getStaffId().longValue());
        String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(heOrderEntity.getStoreId());
        storeCurrencyCode = storeCurrencyCode.trim();
        heOrderEntity.setFxRate(StoreCurrencyContainer.Default_Currency.equals(storeCurrencyCode) ? BigDecimal.ONE : StoreCurrencyContainer.getStoreCurrencyFxRate(storeCurrencyCode, heOrderEntity.getCreatedAt()));
        Integer orderId = heOrderEntity.add(Operator.of("系统"));
        try {
            //保存客户信息
            tabClientManager.updateClientInfo(req.getOrderMonthClientReq());
            //保存委托人信息
            if (ObjectUtil.isNotEmpty(req.getOrderMonthClientBailorReq()) && req.getOrderMonthClientReq().getSignType().equals(1)) {
                //委托人主表
                Integer bailorId = tabClientManager.createBailor(req.getOrderMonthClientBailorReq());
                //委托人快照
                orderBailorSnapshotRepository.insertOrUpdateOne(orderBailorSnapshotRepository.orderMonthClientBailorReq2Entity(req.getOrderMonthClientBailorReq()), bailorId, orderId);
            }
        } catch (Exception e) {
            log.error("保存客户信息失败", e);
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), e.getMessage());
        }

        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.orderMonthClientReq2Entity(req.getOrderMonthClientReq(), orderId);
        //处理胎数字段
        dealFetusNum(req, heOrderUserSnapshotEntity);
        //保存订单客户快照
        heOrderUserSnapshotEntity.add();

        //保存订单商品
        HeOrderGoodsEntity heOrderGoodsEntity = orderGoodsRepository.orderMonthGoodsCacheVO2Entity(resultDTO.getResult1(), req.getOrderMonthClientReq(), orderId, req.getOrderMonthGoodsReq().getGoodsImage());
        Integer orderGoodsId = orderGoodsRepository.insertOne(heOrderGoodsEntity);
        List<OrderGiftExtendEntity> orderGiftExtendEntities = orderGiftExtendRepository.orderGiftCacheByUserVOS2Entity(resultDTO.getResult2(), orderId, req.getOrderMonthClientReq().getBasicUid());
        //保存额外礼赠
        orderGiftExtendRepository.batchInsert(orderGiftExtendEntities);
        //检测商品是否有sbar或者产康的数据
        heOrderEntity.setOrderId(orderId);
        heOrderGoodsSbarRepository.checkSbraAndInsert(orderGiftExtendEntities, heOrderUserSnapshotEntity, heOrderEntity, req.getOperator());
        //保存加收项(要去除开启状态为0的加收项)
        List<HeOrderAdditionalRevenueEntity> orderAdditionalRevenueEntities = orderAdditionalRevenueRepository.orderAdditionalRevenueCacheVOS2EntityList(resultDTO.getResult3().stream().filter(x -> ObjectUtil.isNull(x.getIsFestival()) || x.getIsFestival() != 0).collect(Collectors.toList()), orderId);
        orderAdditionalRevenueEntities = orderAdditionalRevenueRepository.batchInsert(orderAdditionalRevenueEntities);
        //保存续住变更历史
        orderContinueLiveRecordRepository.saveContinueLive(orderAdditionalRevenueEntities, orderId, req);
        RoomByGoodsIdVo roomInfoByGoodsId = goodsManager.getRoomInfoByGoodsId(req.getOrderMonthGoodsReq().getGoodsId());
        //保存房型变更
        orderRoomTypeChangeRecordRepository.saveRoomType(orderAdditionalRevenueEntities, orderId, req, roomInfoByGoodsId);
        //保存定订单其他
        orderVoucherRepository.batchInsert(orderVoucherRepository.listConverterEntity(req.getOrderMonthOtherReq(), orderId));
        //推送订单参数变更历史
        //Fixme 改成调用合同服务rpc
        contractManager.pushOrderParamHistory(convertOrderId2OrderParamHistoryPushDTO(true, req, orderId, resultDTO));
//        monthOrderParamHistoryService.pushOrderParamHistory(convertOrderId2OrderParamHistoryPushDTO(true, req, orderId, resultDTO));

        //创建保存订单套餐附件
        HeOrderAttachmentEntity heOrderAttachmentEntity = orderAttachmentAssembler.queryOrderAttachment(orderId, orderGoodsId, resultDTO.getResult1().getGoodsId());
        heOrderAttachmentDomainService.saveOrderAttachment(heOrderAttachmentEntity);
        //保存或修改产康金记录
        saveOrUpdateProductionAmountList(req.getOrderMonthClientReq().getSellerId(), req.getOrderMonthClientReq().getSellerName(), orderId, heOrderUserSnapshotEntity.getBasicUid(), heOrderGoodsEntity.getGoodsId(), orderGiftExtendEntities, true);
        //异步推送SCRM
        scrmManager.phpPushScrmCustomer(req.getOrderMonthClientReq().getClientUid());
        //异步发送创建订单公众号消息
        messageClient.sendTemplateOrderInfo(orderId);
        //创建完订单删除缓存
        removeCache(getOrderCacheBaseReq(req));

        AddOrderVO result = new AddOrderVO();
        result.setOrderId(orderId);
        return Result.success(result);
    }

    private void saveOrUpdateProductionAmountList(Integer sellerId, String sellerName, Integer orderId, Integer basicUid, Integer goodsId, List<OrderGiftExtendEntity> orderGiftExtendEntities, boolean isCreate) {
        GoodsEntity goodsEntity = goodsRepository.selectById(goodsId);
        log.info("goodsEntity123:{}", JsonUtil.write(goodsEntity));
        //保存或修改套餐产康金
        saveOrUpdateProduction(sellerId, sellerName, orderId, basicUid, 1, goodsEntity.getProductionAmount(), isCreate);
        //保存或修改额外礼赠产康金
        int sum = 0;
        sum = orderGiftExtendEntities.stream().filter(x -> x.getType().equals(1)).mapToInt(OrderGiftExtendEntity::getPrice).sum();
        saveOrUpdateProduction(sellerId, sellerName, orderId, basicUid, 2, sum, isCreate);
    }

    private void saveOrUpdateProduction(Integer sellerId, String sellerName, Integer orderId, Integer basicUid, Integer source, Integer productionAmount, boolean isCreate) {
        log.info("sellerId:{},sellerName:{},orderId:{},basicUid:{},source:{},productionAmount:{},isCreate:{}", sellerId, sellerName, orderId, basicUid, source, productionAmount, isCreate);
        if (isCreate) {
            //新增逻辑
            if (productionAmount > 0) {

                HeUserProductionAmountListEntity dbHeUserProductionAmountListEntity = heUserProductionAmountListRepository.queryByOrderIdAndSource(orderId, source);

                if (ObjectUtil.isEmpty(dbHeUserProductionAmountListEntity)) {
                    HeUserProductionAmountListEntity heUserProductionAmountListEntity = new HeUserProductionAmountListEntity();
                    heUserProductionAmountListEntity.setBasicId(basicUid);
                    heUserProductionAmountListEntity.setTotalAmount(Long.valueOf(productionAmount));
                    heUserProductionAmountListEntity.setSource(source);
                    heUserProductionAmountListEntity.setClientType(7);
                    heUserProductionAmountListEntity.setOperatorId(sellerId);
                    heUserProductionAmountListEntity.setOperator(sellerName);
                    heUserProductionAmountListEntity.setOrderId(Long.valueOf(orderId));
                    heUserProductionAmountListEntity.setCreatedAt(Math.toIntExact(System.currentTimeMillis() / 1000));
                    log.info("新增产康金参数={}", JSONUtil.toJsonStr(heUserProductionAmountListEntity));
                    heUserProductionAmountListRepository.saveOne(heUserProductionAmountListEntity);
                } else {
                    heUserProductionAmountListRepository.updateByOrderAndSource(dbHeUserProductionAmountListEntity.getId(), productionAmount, Math.toIntExact(System.currentTimeMillis() / 1000));
                }

                orderAssetTradeService.saveCkj(orderId + "" + source + "-save", basicUid + "", orderId, productionAmount.longValue(), source, orderAssetTradeService.getSellOperator(sellerId));
            }
        } else {
            //修改逻辑
            HeUserProductionAmountListEntity byOrderIdAndSource = heUserProductionAmountListRepository.getByOrderIdAndSource(orderId, source);
            if (productionAmount > 0) {
                if (ObjectUtil.isNotEmpty(byOrderIdAndSource)) {
                    //走修改
                    byOrderIdAndSource.setTotalAmount(Long.valueOf(productionAmount));
                    byOrderIdAndSource.setOperatorId(sellerId);
                    byOrderIdAndSource.setOperator(sellerName);
                    byOrderIdAndSource.setUpdatedAt(Math.toIntExact(System.currentTimeMillis() / 1000));
                    log.info("修改产康金参数={}", JSONUtil.toJsonStr(byOrderIdAndSource));
                    heUserProductionAmountListRepository.updateOne(byOrderIdAndSource);
                    orderAssetTradeService.saveCkj(orderId + "" + source + IdWorker.getId(), basicUid + "", orderId, productionAmount.longValue(), source, orderAssetTradeService.getSellOperator(sellerId));
                } else {
                    //走新增
                    this.saveOrUpdateProduction(sellerId, sellerName, orderId, basicUid, source, productionAmount, true);
                }

            } else {
                if (ObjectUtil.isNotEmpty(byOrderIdAndSource)) {
                    //走删除
                    log.info("删除产康金参数={}", JSONUtil.toJsonStr(byOrderIdAndSource));
                    heUserProductionAmountListRepository.removeOne(byOrderIdAndSource);
                }
                orderAssetTradeService.saveCkj(orderId + "" + source + IdWorker.getId(), basicUid + "", orderId, 0L, source, orderAssetTradeService.getSellOperator(sellerId));
            }
        }
    }


    /**
     * 修改订单
     *
     * @param req
     */
    @Override
//    @GlobalTransactional(rollbackFor = Exception.class)
    @SneakyThrows
    public Result<EditOrderVO> editOrderMonth(OrderMonthReq req) {
        EditOrderVO result = new EditOrderVO();
        result.setOrderId(req.getOrderId());
        HeOrderEntity oldOrderEntity = orderRepository.getByOrderId(req.getOrderId());
        //2024-01-15折扣审批前置逻辑,只有无需审批或审批通过可以修改订单
        if (ObjectUtil.isNotEmpty(oldOrderEntity)) {
            Integer approvalDiscountStatus = oldOrderEntity.getApprovalDiscountStatus();
            // NO_APPROVAL_NEEDED(0, "无需审批"),
            //    APPROVING(1, "审批中"),
            //    APPROVED(2, "审批通过"),
            //    APPROVAL_FAILED(3, "审批失败"),
            //    INITIATION_FAILED(4, "发起失败");
            //审批中
            if (approvalDiscountStatus.equals(ApprovalDiscountStatusEnum.APPROVING.getCode())) {
                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "订单优惠审批过程中不可修改合同");
            }

        }
        //默认不需要签订
        result.setCreateContractModificationApproval(0);

        log.info("修改订单参数={}", JSONUtil.toJsonStr(req));
        //多线程查询
        //如果没有缓存则直接成功
        RemoveAllOrderCacheReq removeAllOrderCacheReq = new RemoveAllOrderCacheReq();
        removeAllOrderCacheReq.setOrderId(req.getOrderId());
        Result<RemoveAllOrderCacheVO> removeAllOrderCacheVOResult = checkCacheByOrderId(removeAllOrderCacheReq);
        if (!removeAllOrderCacheVOResult.getData().getExist()) {
            //标识没有修改,不让前端调用合同修改审批与折扣审批
            result.setCreateContractModificationApproval(0);
            return Result.success(result);
        }

        AsyncResultDTO<OrderMonthGoodsCacheVO, List<OrderGiftCacheByUserVO>, List<OrderAdditionalRevenueCacheVO>, OrderDiscountsCacheVO> resultDTO = multiThreadGetOrderInfo(req);
        log.info("修改订单获取套餐缓存数据：{}", JsonUtil.write(resultDTO.getResult1()));
        HeOrderEntity heOrderEntity = orderRepository.convertOrderMonthReq2HeOrderEntity(false, req, resultDTO);
        heOrderEntity.setUpdatedAt(System.currentTimeMillis() / 1000);
        Integer orderId = orderRepository.updateOrderMonthByOrderId(heOrderEntity);
        // 查询订单类合同是否已签署,未签署则所有信息可以修改,已签署则客户认证模块和委托人不允许修改
        MonthContractQuery monthContractQuery = new MonthContractQuery();
        monthContractQuery.setOrderId(req.getOrderId());
        monthContractQuery.setTemplateContractType(TemplateContractTypeV4Enum.YZ_SAINTBELLA.code());
        monthContractQuery.setContractStatus(ContractStatusEnum.SIGNED.code());
        Result<ContractSignDetailVO> contractSignDetailVOResult = contractAuthenticationService.querySignDetailWithContractStatus(monthContractQuery);
        log.info("合同签署结果={}", contractSignDetailVOResult.getData());
        if (ObjectUtil.isEmpty(contractSignDetailVOResult.getData())) {
            try {
                if (ObjectUtil.isNotEmpty(req.getOrderMonthClientBailorReq()) && req.getOrderMonthClientReq().getSignType().equals(1)) {
                    //委托人主表
                    Integer bailorId = tabClientManager.createBailor(req.getOrderMonthClientBailorReq());
                    //委托人快照
                    orderBailorSnapshotRepository.insertOrUpdateOne(orderBailorSnapshotRepository.orderMonthClientBailorReq2Entity(req.getOrderMonthClientBailorReq()), bailorId, orderId);
                }
            } catch (Exception e) {
                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), e.getMessage());
            }
        }
        //修改客户信息
        tabClientManager.updateClientInfo(req.getOrderMonthClientReq());
        //修改订单客户快照
        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.orderMonthClientReq2Entity(req.getOrderMonthClientReq(), orderId);
        //处理胎数字段
        dealFetusNum(req, heOrderUserSnapshotEntity);
        orderUserSnapshotRepository.updateOne(heOrderUserSnapshotEntity);
        //保存订单商品
        HeOrderGoodsEntity heOrderGoodsEntity = orderGoodsRepository.orderMonthGoodsCacheVO2Entity(resultDTO.getResult1(), req.getOrderMonthClientReq(), orderId, req.getOrderMonthGoodsReq().getGoodsImage());
        Integer orderGoodsId = orderGoodsRepository.updateOne(heOrderGoodsEntity);
        //只有未生效与待入住状态的订单可以修改额外礼赠
        List<OrderGiftExtendEntity> orderGiftExtendEntities = orderGiftExtendRepository.orderGiftCacheByUserVOS2Entity(resultDTO.getResult2(), orderId, req.getOrderMonthClientReq().getBasicUid());
        if (heOrderEntity.getOrderStatus().equals(OrderStatusV2Enum.NONE.getCode()) || heOrderEntity.getOrderStatus().equals(OrderStatusV2Enum.TO_STAY_IN.getCode())) {
            //保存额外礼赠(全删全增)
            orderGiftExtendRepository.batchUpdate(orderGiftExtendEntities, orderId);
        }
        //检测商品是否有sbar或者产康的数据
        heOrderGoodsSbarRepository.checkSbraAndInsert(orderGiftExtendEntities, heOrderUserSnapshotEntity, heOrderEntity, req.getOperator());
        //保存加收项(与数据库比对,①ID数据库有,缓存有,则修改,②ID数据库有,缓存没有,则删除,③ID数据库没有,缓存有则新增)
        List<HeOrderAdditionalRevenueEntity> orderAdditionalRevenueEntities = orderAdditionalRevenueRepository.orderAdditionalRevenueCacheVOS2EntityList(resultDTO.getResult3(), orderId);
        orderAdditionalRevenueRepository.batchUpdate(orderAdditionalRevenueEntities, orderId);
        List<HeOrderAdditionalRevenueEntity> newHeOrderAdditionalRevenueEntityList = orderAdditionalRevenueRepository.getByOrderId(orderId);
        //保存续住变更历史
        orderContinueLiveRecordRepository.updateContinueLive(newHeOrderAdditionalRevenueEntityList, orderId, req);
        //保存房型变更历史
        RoomByGoodsIdVo roomInfoByGoodsId = goodsManager.getRoomInfoByGoodsId(req.getOrderMonthGoodsReq().getGoodsId());
        orderRoomTypeChangeRecordRepository.updateRoomTypeChange(newHeOrderAdditionalRevenueEntityList, orderId, req, roomInfoByGoodsId);

        //保存定订单其他(全删全增)
        orderVoucherRepository.batchUpdate(orderVoucherRepository.listConverterEntity(req.getOrderMonthOtherReq(), orderId), orderId);
        //推送订单参数变更历史
        //Fixme 改成调用合同服务rpc
        contractManager.pushOrderParamHistory(convertOrderId2OrderParamHistoryPushDTO(false, req, orderId, resultDTO));
        //创建保存订单套餐附件
        HeOrderAttachmentEntity heOrderAttachmentEntity = orderAttachmentAssembler.queryOrderAttachment(orderId, orderGoodsId, resultDTO.getResult1().getGoodsId());
        heOrderAttachmentDomainService.saveOrderAttachment(heOrderAttachmentEntity);
        //保存或修改产康金记录
        saveOrUpdateProductionAmountList(req.getOrderMonthClientReq().getSellerId(), req.getOrderMonthClientReq().getSellerName(), orderId, heOrderUserSnapshotEntity.getBasicUid(), heOrderGoodsEntity.getGoodsId(), orderGiftExtendRepository.getByOrderId(orderId), false);
        //修改完订单删除缓存
        removeCache(getOrderCacheBaseReq(req));
        //异步推送SCRM
        scrmManager.phpPushScrmCustomer(req.getOrderMonthClientReq().getClientUid());
        //异步推送订单;只有订单修改时,并且是业绩生效时才推送 订单信息推送SCRM
        HeOrderEntity byOrderId = orderRepository.getByOrderId(orderId);
        if (ObjectUtil.isNotEmpty(byOrderId.getPercentFirstTime()) && byOrderId.getPercentFirstTime() > 0) {
            scrmManager.pushScrmCustomerOrder(req.getOrderId());
        }
        //异步发送邮件通知到财务和同步资产中心,只有订单修改时,并且是累计收款金额>0,并且订单金额发生了变动
        log.info("heOrderEntity New Log:{}", JSONUtil.toJsonStr(heOrderEntity));
        log.info("heOrderEntity:{}", JsonUtil.write(heOrderEntity));
        if (!oldOrderEntity.getPayAmount().equals(heOrderEntity.getPayAmount()) && ObjectUtil.isNotEmpty(oldOrderEntity.getPaidAmount()) && oldOrderEntity.getPaidAmount() > 0) {
            //2024-01-15折扣审批前置逻辑,不发财务邮件
            SendMailToFinanceRequest request = getSendMailToFinanceRequest(oldOrderEntity, heOrderEntity, heOrderUserSnapshotEntity);
            //发邮件
            mailManager.sendMailToFinance(request);
            //同步资产中心
            asyncOrder.orderUpdateAsset(heOrderEntity.getOrderId());
        }
        //2024-01-15折扣审批前置逻辑,不发起合同修改审批
        //2024-05-31 产品逻辑需要发起合同修改审批
        Result<Boolean> booleanResult = contractAuthenticationService.signMasterContract(req.getOrderId().longValue());
        if (booleanResult.getSuccess()) {
            result.setCreateContractModificationApproval(1);
        }
        //修改产康金日志表
        updateProductionAmountPiLog(req.getOrderId().longValue(), 1);
        //订单变更通知房态
        orderEventProducer.sendMq(OrderEventEnum.ORDER_CHANGE.getCode(), oldOrderEntity.getOrderSn(), null);
        return Result.success(result);
    }

    private void dealFetusNum(OrderMonthReq req, HeOrderUserSnapshotEntity heOrderUserSnapshotEntity) {
        List<OrderMonthAdditionalRevenueReq> orderMonthAdditionalRevenueReqList = req.getOrderMonthAdditionalRevenueReqList();
        if (CollectionUtils.isNotEmpty(orderMonthAdditionalRevenueReqList)) {
            for (OrderMonthAdditionalRevenueReq orderMonthAdditionalRevenueReq : orderMonthAdditionalRevenueReqList) {
                if (MonthAdditionalRevenueEnum.COST_MULTIPLE_BIRTHS.getCode().equals(orderMonthAdditionalRevenueReq.getType())) {
                    heOrderUserSnapshotEntity.setFetusNum(orderMonthAdditionalRevenueReq.getEmbryoNumber());
                }
            }
        }
    }


    private void updateProductionAmountPiLog(Long orderId, Integer type) {
        List<HeUserProductionAmountListEntity> byOrderIdList = heUserProductionAmountListRepository.getByOrderId(orderId.intValue(), Arrays.asList(0, 1));
        if (ObjectUtil.isNotEmpty(byOrderIdList)) {
            long all = 0;
            for (HeUserProductionAmountListEntity heUserProductionAmountListEntity : byOrderIdList) {
                all = all + (heUserProductionAmountListEntity.getTotalAmount() - heUserProductionAmountListEntity.getUsedAmount());
            }
            UserProductionAmountPiLogEntity userProductionAmountPiLogEntity = heUserProductionAmountPiLogRepository.queryByOrderIdAndType(orderId, type);
            if (ObjectUtil.isNotEmpty(userProductionAmountPiLogEntity)) {
                userProductionAmountPiLogEntity.setOperateAmount(all);
                userProductionAmountPiLogEntity.setUpdatedAt((int) (System.currentTimeMillis() / 1000));
                heUserProductionAmountPiLogRepository.update(userProductionAmountPiLogEntity);
            }
        }
    }


    /**
     * 组装发送财务邮件参数
     */
    private SendMailToFinanceRequest getSendMailToFinanceRequest(HeOrderEntity oldOrderEntity, HeOrderEntity heOrderEntity, HeOrderUserSnapshotEntity heOrderUserSnapshotEntity) throws Exception {
        SendMailToFinanceRequest request = new SendMailToFinanceRequest();
        request.setClientName(heOrderUserSnapshotEntity.getName());
        request.setPhone(heOrderUserSnapshotEntity.getPhone());
        CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(heOrderUserSnapshotEntity.getStoreId());
        request.setStoreName(ObjectUtil.isNotEmpty(cfgStoreEntity) ? cfgStoreEntity.getStoreName() : "");
        request.setOldPayAmount(RMBUtils.changeF2Y(String.valueOf(oldOrderEntity.getPayAmount())));
        request.setNewPayAmount(RMBUtils.changeF2Y(String.valueOf(heOrderEntity.getPayAmount())));
        request.setOrderSn(oldOrderEntity.getOrderSn());
        return request;
    }

    private AsyncResultDTO<OrderMonthGoodsCacheVO, List<OrderGiftCacheByUserVO>, List<OrderAdditionalRevenueCacheVO>, OrderDiscountsCacheVO> multiThreadGetOrderInfo(OrderMonthReq req) {
        return asyncService.asyncCallResult(
                //套餐
                () -> monthOrderWxQueryService.getOrderGoodsCache(getOrderGoodsCacheReq(req)).getData(),
                //额外礼赠
                () -> monthOrderWxQueryService.getOrderGiftExtentCache(getOrderAdvanceOrderGiftReq(req)),
                //加收项
                () -> monthOrderWxQueryService.getOrderAdditionalRevenueCache(getOrderAddtionalRevenueCacheReq(req)),
                //获取套餐折扣明细
                () -> monthOrderWxQueryService.getOrderDiscounts(getOrderDiscountsCacheReq(req)).getData());
    }


    @Override
    public Result orderMonthAdditionalRevenueCache(List<OrderMonthAdditionalRevenueCacheReq> req) {


        if (CollectionUtils.isNotEmpty(req)) {
            for (OrderMonthAdditionalRevenueCacheReq orderMonthAdditionalRevenueCacheReq : req) {
                Integer type = orderMonthAdditionalRevenueCacheReq.getType();
                orderMonthAdditionalRevenueCacheReq.setIntegrality(1);

                if (ObjectUtil.isEmpty(orderMonthAdditionalRevenueCacheReq.getPrice())) {
                    //未填写应收都是没填完整
                    orderMonthAdditionalRevenueCacheReq.setIntegrality(0);
                }

                switch (MonthAdditionalRevenueEnum.getEnumByCode(type)) {
                    case COST_MULTIPLE_BIRTHS:
                        if (ObjectUtil.isEmpty(orderMonthAdditionalRevenueCacheReq.getEmbryoNumber())) {
                            orderMonthAdditionalRevenueCacheReq.setIntegrality(0);
                        }
                        break;
                    case STAY_COST:
                    case ROOM_CHANGES:
                        if (ObjectUtil.isEmpty(orderMonthAdditionalRevenueCacheReq.getRoomId())) {
                            orderMonthAdditionalRevenueCacheReq.setIntegrality(0);
                        }
                        if (MonthAdditionalRevenueEnum.STAY_COST.equals(MonthAdditionalRevenueEnum.getEnumByCode(type)) && ObjectUtil.isEmpty(orderMonthAdditionalRevenueCacheReq.getDays())) {
                            orderMonthAdditionalRevenueCacheReq.setIntegrality(0);
                        }
                        if (MonthAdditionalRevenueEnum.ROOM_CHANGES.equals(MonthAdditionalRevenueEnum.getEnumByCode(type))) {
                            if (CollectionUtils.isEmpty(orderMonthAdditionalRevenueCacheReq.getDaysList())) {
                                orderMonthAdditionalRevenueCacheReq.setIntegrality(0);
                            }
                            List<String> daysList = orderMonthAdditionalRevenueCacheReq.getDaysList();
                            if (CollectionUtils.isNotEmpty(daysList)) {
                                orderMonthAdditionalRevenueCacheReq.setDays(orderMonthAdditionalRevenueCacheReq.getDaysList().size());
                            }
                        }
                        break;
                    case HOLIDAY:
                        if (ObjectUtil.isEmpty(orderMonthAdditionalRevenueCacheReq.getDays())) {
                            orderMonthAdditionalRevenueCacheReq.setIntegrality(0);
                        }
                    default:
                }
            }


            List<OrderMonthAdditionalRevenueCacheReq> collect = req.stream().filter(r -> r.getType().equals(MonthAdditionalRevenueEnum.ROOM_CHANGES.getCode())).collect(Collectors.toList());
            //房型变更某一天只能变更一次
            Set<String> repetition = new HashSet<>();
            if (CollectionUtils.isNotEmpty(collect)) {
                for (OrderMonthAdditionalRevenueCacheReq out : collect) {
                    List<String> outList = out.getDaysList();
                    outList = CollectionUtils.isEmpty(outList) ? new ArrayList<>() : outList;
                    for (OrderMonthAdditionalRevenueCacheReq in : collect) {
                        if (out.equals(in)) {
                            continue;
                        }
                        List<String> inList = in.getDaysList();
                        inList = CollectionUtils.isEmpty(inList) ? new ArrayList<>() : inList;
                        for (String i : inList) {
                            if (outList.contains(i)) {
                                repetition.add(i);
                            }
                        }
                    }
                }
                if (repetition.size() > 0) {
                    throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, "房型变更重复天数：" + repetition + "，请修改后重新提交");
                }
            }

        }

        orderMonthWxDomainService.orderMonthAddtionalRevenueCache(req);
        return Result.success();
    }

    @Override
    public Result updateOrderMonthGiftExtendCache(OrderMonthGiftExtendCacheReq req) {
        orderMonthWxDomainService.updateOrderMonthGiftExtendCache(req);
        return Result.success();
    }

    /**
     * 创建订单后删除缓存
     *
     * @param req
     * @return
     */
    @Override
    public Boolean removeCache(OrderCacheBaseReq req) {
        orderMonthWxDomainService.removeCache(req);
        tabClientManager.removeClientInfoCache(req);
        tabClientManager.removeBailorInfoCache(req);
        return true;
    }

    @Resource
    private OrderContinueLiveRecordRepository orderContinueLiveRecordRepository;

    @Override
    public Result orderOtherInfoCache(OrderOtherInfoCacheReq req) {
        if (CollectionUtils.isNotEmpty(req.getVoucherUrlList()) && req.getVoucherUrlList().size() > 6) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "上传的图片数量最多6张");
        }

        orderMonthWxDomainService.orderOtherInfoCache(req);
        return Result.success();
    }

    @Override
    public Result getOrderCacheByOrderId(OrderCacheByOrderIdReq orderCacheByOrderIdReq) {
        Integer orderId = orderCacheByOrderIdReq.getOrderId();

        log.info("OrderInfoByOrderVO orderInfoByOrderId = monthOrderWxQueryService.getOrderInfoByOrderId(orderId);");
        OrderInfoByOrderVO orderInfoByOrderId = monthOrderWxQueryService.getOrderInfoByOrderId(orderId);
        log.info("END___--------OrderInfoByOrderVO orderInfoByOrderId = monthOrderWxQueryService.getOrderInfoByOrderId(orderId);");
        //生成客户缓存


        log.info("生成套餐");

        //生成套餐
        OrderInfoByGoodsVO orderInfoByGoodsVO = orderInfoByOrderId.getOrderInfoByGoodsVO();
        OrderGoodsCacheReq orderGoodsCacheReq = null;
        if (ObjectUtil.isNotEmpty(orderInfoByGoodsVO)) {
            orderGoodsCacheReq = new OrderGoodsCacheReq();
            orderGoodsCacheReq.setId(orderInfoByGoodsVO.getId());
            orderGoodsCacheReq.setOrderId(orderId);
            orderGoodsCacheReq.setGoodsId(orderInfoByGoodsVO.getGoodsId());
            orderGoodsCacheReq.setSkuId(orderInfoByGoodsVO.getSkuId());
            orderGoodsCacheReq.setGoodsReceivableAmount(orderInfoByGoodsVO.getGoodsReceivableAmount());
        }
        log.info("END-_生成套餐");

        log.info("生成套餐额外礼赠");

        //生成套餐额外礼赠
        //实物虚拟商品需要合并
        List<OrderInfoByGiftExtendSkuVO> orderInfoByGiftExtendSkuVOList = orderInfoByOrderId.getOrderInfoByGiftExtendVO().getOrderInfoByGiftExtendSkuVOList();
        List<OrderInfoByGiftExtendSkuVO> newOrderInfoByGiftExtendSkuVOList = new ArrayList<>();
        newOrderInfoByGiftExtendSkuVOList.addAll(orderInfoByGiftExtendSkuVOList.stream().filter(o -> o.getType() != 7).collect(Collectors.toList()));
        List<OrderInfoByGiftExtendSkuVO> collect = orderInfoByGiftExtendSkuVOList.stream().filter(o -> o.getType() == 7).collect(Collectors.toList());
        Map<String, OrderInfoByGiftExtendSkuVO> orderInfoByGiftExtendSkuVOMap = new HashMap<>();
        for (OrderInfoByGiftExtendSkuVO orderInfoByGiftExtendSkuV : collect) {
            String key = orderInfoByGiftExtendSkuV.getGoodsId() + "-" + orderInfoByGiftExtendSkuV.getSkuId();
            if (null != orderInfoByGiftExtendSkuVOMap.get(key)) {
                OrderInfoByGiftExtendSkuVO orderInfoByGiftExtendSkuVO = orderInfoByGiftExtendSkuVOMap.get(key);
                orderInfoByGiftExtendSkuVO.setQuantity(orderInfoByGiftExtendSkuVO.getQuantity() + orderInfoByGiftExtendSkuV.getQuantity());
            } else {
                orderInfoByGiftExtendSkuVOMap.put(key, orderInfoByGiftExtendSkuV);
            }
        }
        for (String key : orderInfoByGiftExtendSkuVOMap.keySet()) {
            newOrderInfoByGiftExtendSkuVOList.add(orderInfoByGiftExtendSkuVOMap.get(key));
        }
        List<OrderMonthGiftExtendCacheReq> orderMonthGiftExtendCacheReqList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(newOrderInfoByGiftExtendSkuVOList)) {
            newOrderInfoByGiftExtendSkuVOList.stream().forEach(o -> {
                OrderMonthGiftExtendCacheReq orderMonthGiftExtendCacheReq = new OrderMonthGiftExtendCacheReq().setQuantity(o.getQuantity()).setGoodsId(o.getGoodsId()).setSkuId(o.getSkuId()).setCategoryFront(o.getCategoryFront());
                orderMonthGiftExtendCacheReq.setOrderId(orderId);
                orderMonthGiftExtendCacheReqList.add(orderMonthGiftExtendCacheReq);
            });
        }

        log.info("END---生成套餐额外礼赠");

        log.info("生成加收项");
        //生成加收项
        List<OrderMonthAdditionalRevenueCacheReq> orderMonthAdditionalRevenueCacheReqArrayList = new ArrayList<>();
        List<OrderInfoByAdditionalRevenueVO> orderInfoByAdditionalRevenueVOList = orderInfoByOrderId.getOrderInfoByAdditionalRevenueVOList();

        if (CollectionUtils.isNotEmpty(orderInfoByAdditionalRevenueVOList)) {
            orderInfoByAdditionalRevenueVOList.stream().forEach(o -> {
                OrderMonthAdditionalRevenueCacheReq orderMonthAdditionalRevenueCacheReq = new OrderMonthAdditionalRevenueCacheReq();
                orderMonthAdditionalRevenueCacheReq.setId(o.getId());
                orderMonthAdditionalRevenueCacheReq.setGoodsId(orderInfoByGoodsVO.getGoodsId());
                orderMonthAdditionalRevenueCacheReq.setType(o.getType());
                orderMonthAdditionalRevenueCacheReq.setPrice(o.getPrice());
                orderMonthAdditionalRevenueCacheReq.setOrderId(orderId);
                orderMonthAdditionalRevenueCacheReq.setIsFestival(o.getIsFestival());

                switch (MonthAdditionalRevenueEnum.getEnumByCode(o.getType())) {
                    case COST_MULTIPLE_BIRTHS://多胞胎费用
                        orderMonthAdditionalRevenueCacheReq.setEmbryoNumber(o.getEmbryoNumber());
                        break;
                    case STAY_COST://续住费用
                    case ROOM_CHANGES://房型变更
                        orderMonthAdditionalRevenueCacheReq.setRoomId(o.getRoomId());
                        orderMonthAdditionalRevenueCacheReq.setDays(o.getDays());
                        orderMonthAdditionalRevenueCacheReq.setDaysList(o.getDaysList());
                        break;
                    case HOLIDAY://节假日
                        orderMonthAdditionalRevenueCacheReq.setDays(o.getDays());
                        break;
                    default:
                }
                orderMonthAdditionalRevenueCacheReqArrayList.add(orderMonthAdditionalRevenueCacheReq);
            });
        }

        log.info("END---生成加收项");


        log.info("生成订单其他信息");
        //生成订单其他信息
        OrderInfoByOtherInfoVO orderInfoByOtherInfoVO = orderInfoByOrderId.getOrderInfoByOtherInfoVO();
        OrderOtherInfoCacheReq orderOtherInfoCacheReq = new OrderOtherInfoCacheReq();
        if (ObjectUtil.isNotEmpty(orderInfoByOtherInfoVO)) {
            orderOtherInfoCacheReq.setOrderTag(orderInfoByOtherInfoVO.getOrderTag());
            orderOtherInfoCacheReq.setOrderTagName(orderInfoByOtherInfoVO.getOrderTagName());
            orderOtherInfoCacheReq.setRemark(orderInfoByOtherInfoVO.getRemark());
            orderOtherInfoCacheReq.setVoucherUrlList(orderInfoByOtherInfoVO.getVoucherUrlList());
            orderOtherInfoCacheReq.setOrderId(orderId);
        }

        log.info("END生成订单其他信息");


        log.info("创建套餐缓存");

        //创建套餐缓存
        if (ObjectUtil.isNotEmpty(orderGoodsCacheReq)) {
            orderGoodsCache(orderGoodsCacheReq);
        }

        log.info("END创建套餐缓存");


        log.info("创建套餐额外礼赠缓存");

        if (CollectionUtils.isNotEmpty(orderMonthGiftExtendCacheReqList)) {
            //创建套餐额外礼赠缓存
            for (OrderMonthGiftExtendCacheReq orderMonthGiftExtendCacheReq : orderMonthGiftExtendCacheReqList) {
                updateOrderMonthGiftExtendCache(orderMonthGiftExtendCacheReq);
            }
        }

        log.info("END创建套餐额外礼赠缓存");


        log.info("创建套餐加收项缓存");
        if (CollectionUtils.isNotEmpty(orderMonthAdditionalRevenueCacheReqArrayList)) {
            //创建套餐加收项缓存
            orderMonthAdditionalRevenueCache(orderMonthAdditionalRevenueCacheReqArrayList);
        }
        log.info("END创建套餐加收项缓存");

        log.info("创建套餐额外信息");
        if (ObjectUtil.isNotEmpty(orderOtherInfoCacheReq)) {
            //创建套餐额外信息
            orderOtherInfoCache(orderOtherInfoCacheReq);
        }
        log.info("END创建套餐额外信息");


        log.info("生成客户缓存");
        //生成客户缓存
        tabClientManager.clientInfoCache(orderInfoByOrderId.getOrderMonthClientVO(), orderCacheByOrderIdReq.getOperator(), orderId);
        if (ObjectUtil.isNotEmpty(orderInfoByOrderId.getOrderMonthClientBailorVO())) {
            tabClientManager.bailorInfoCache(orderInfoByOrderId.getOrderMonthClientBailorVO(), orderCacheByOrderIdReq.getOperator(), orderId);
        }


        log.info("END生成客户缓存");
        return Result.success();
    }

    @Override
    public Result getOrderCacheByOrderIdForCustomer(OrderCacheByOrderIdReq orderCacheByOrderIdReq) {
        Integer orderId = orderCacheByOrderIdReq.getOrderId();

        log.info("OrderInfoByOrderVO orderInfoByOrderId = monthOrderWxQueryService.getOrderInfoByOrderId(orderId);");
        OrderInfoByOrderVO orderInfoByOrderId = monthOrderWxQueryService.getOrderInfoByOrderId(orderId);
        log.info("END___--------OrderInfoByOrderVO orderInfoByOrderId = monthOrderWxQueryService.getOrderInfoByOrderId(orderId);");
        //生成客户缓存


        log.info("生成套餐");

        //生成套餐
        OrderInfoByGoodsVO orderInfoByGoodsVO = orderInfoByOrderId.getOrderInfoByGoodsVO();
        OrderGoodsCacheReq orderGoodsCacheReq = null;
        if (ObjectUtil.isNotEmpty(orderInfoByGoodsVO)) {
            orderGoodsCacheReq = new OrderGoodsCacheReq();
            orderGoodsCacheReq.setId(orderInfoByGoodsVO.getId());
            orderGoodsCacheReq.setOrderId(orderId);
            orderGoodsCacheReq.setGoodsId(orderInfoByGoodsVO.getGoodsId());
            orderGoodsCacheReq.setSkuId(orderInfoByGoodsVO.getSkuId());
            orderGoodsCacheReq.setGoodsReceivableAmount(orderInfoByGoodsVO.getGoodsReceivableAmount());
        }
        log.info("END-_生成套餐");

        log.info("生成套餐额外礼赠");

        //生成套餐额外礼赠
        //实物虚拟商品需要合并
        List<OrderInfoByGiftExtendSkuVO> orderInfoByGiftExtendSkuVOList = orderInfoByOrderId.getOrderInfoByGiftExtendVO().getOrderInfoByGiftExtendSkuVOList();
        List<OrderInfoByGiftExtendSkuVO> newOrderInfoByGiftExtendSkuVOList = new ArrayList<>();
        newOrderInfoByGiftExtendSkuVOList.addAll(orderInfoByGiftExtendSkuVOList.stream().filter(o -> o.getType() != 7).collect(Collectors.toList()));
        List<OrderInfoByGiftExtendSkuVO> collect = orderInfoByGiftExtendSkuVOList.stream().filter(o -> o.getType() == 7).collect(Collectors.toList());
        Map<String, OrderInfoByGiftExtendSkuVO> orderInfoByGiftExtendSkuVOMap = new HashMap<>();
        for (OrderInfoByGiftExtendSkuVO orderInfoByGiftExtendSkuV : collect) {
            String key = orderInfoByGiftExtendSkuV.getGoodsId() + "-" + orderInfoByGiftExtendSkuV.getSkuId();
            if (null != orderInfoByGiftExtendSkuVOMap.get(key)) {
                OrderInfoByGiftExtendSkuVO orderInfoByGiftExtendSkuVO = orderInfoByGiftExtendSkuVOMap.get(key);
                orderInfoByGiftExtendSkuVO.setQuantity(orderInfoByGiftExtendSkuVO.getQuantity() + orderInfoByGiftExtendSkuV.getQuantity());
            } else {
                orderInfoByGiftExtendSkuVOMap.put(key, orderInfoByGiftExtendSkuV);
            }
        }
        for (String key : orderInfoByGiftExtendSkuVOMap.keySet()) {
            newOrderInfoByGiftExtendSkuVOList.add(orderInfoByGiftExtendSkuVOMap.get(key));
        }
        List<OrderMonthGiftExtendCacheReq> orderMonthGiftExtendCacheReqList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(newOrderInfoByGiftExtendSkuVOList)) {
            newOrderInfoByGiftExtendSkuVOList.stream().forEach(o -> {
                OrderMonthGiftExtendCacheReq orderMonthGiftExtendCacheReq = new OrderMonthGiftExtendCacheReq().setQuantity(o.getQuantity()).setGoodsId(o.getGoodsId()).setSkuId(o.getSkuId()).setCategoryFront(o.getCategoryFront());
                orderMonthGiftExtendCacheReq.setOrderId(orderId);
                orderMonthGiftExtendCacheReqList.add(orderMonthGiftExtendCacheReq);
            });
        }

        log.info("END---生成套餐额外礼赠");

        log.info("生成加收项");
        //生成加收项
        List<OrderMonthAdditionalRevenueCacheReq> orderMonthAdditionalRevenueCacheReqArrayList = new ArrayList<>();
        List<OrderInfoByAdditionalRevenueVO> orderInfoByAdditionalRevenueVOList = orderInfoByOrderId.getOrderInfoByAdditionalRevenueVOList();

        if (CollectionUtils.isNotEmpty(orderInfoByAdditionalRevenueVOList)) {
            orderInfoByAdditionalRevenueVOList.stream().forEach(o -> {
                OrderMonthAdditionalRevenueCacheReq orderMonthAdditionalRevenueCacheReq = new OrderMonthAdditionalRevenueCacheReq();
                orderMonthAdditionalRevenueCacheReq.setId(o.getId());
                orderMonthAdditionalRevenueCacheReq.setGoodsId(orderInfoByGoodsVO.getGoodsId());
                orderMonthAdditionalRevenueCacheReq.setType(o.getType());
                orderMonthAdditionalRevenueCacheReq.setPrice(o.getPrice());
                orderMonthAdditionalRevenueCacheReq.setOrderId(orderId);
                orderMonthAdditionalRevenueCacheReq.setIsFestival(o.getIsFestival());

                switch (MonthAdditionalRevenueEnum.getEnumByCode(o.getType())) {
                    case COST_MULTIPLE_BIRTHS://多胞胎费用
                        orderMonthAdditionalRevenueCacheReq.setEmbryoNumber(o.getEmbryoNumber());
                        break;
                    case STAY_COST://续住费用
                    case ROOM_CHANGES://房型变更
                        orderMonthAdditionalRevenueCacheReq.setRoomId(o.getRoomId());
                        orderMonthAdditionalRevenueCacheReq.setDays(o.getDays());
                        orderMonthAdditionalRevenueCacheReq.setDaysList(o.getDaysList());
                        break;
                    case HOLIDAY://节假日
                        orderMonthAdditionalRevenueCacheReq.setDays(o.getDays());
                        break;
                    default:
                }
                orderMonthAdditionalRevenueCacheReqArrayList.add(orderMonthAdditionalRevenueCacheReq);
            });
        }

        log.info("END---生成加收项");


        log.info("生成订单其他信息");
        //生成订单其他信息
        OrderInfoByOtherInfoVO orderInfoByOtherInfoVO = orderInfoByOrderId.getOrderInfoByOtherInfoVO();
        OrderOtherInfoCacheReq orderOtherInfoCacheReq = new OrderOtherInfoCacheReq();
        if (ObjectUtil.isNotEmpty(orderInfoByOtherInfoVO)) {
            orderOtherInfoCacheReq.setOrderTag(orderInfoByOtherInfoVO.getOrderTag());
            orderOtherInfoCacheReq.setOrderTagName(orderInfoByOtherInfoVO.getOrderTagName());
            orderOtherInfoCacheReq.setRemark(orderInfoByOtherInfoVO.getRemark());
            orderOtherInfoCacheReq.setVoucherUrlList(orderInfoByOtherInfoVO.getVoucherUrlList());
            orderOtherInfoCacheReq.setOrderId(orderId);
        }

        log.info("END生成订单其他信息");


        log.info("创建套餐缓存");

        //创建套餐缓存
        if (ObjectUtil.isNotEmpty(orderGoodsCacheReq)) {
            orderGoodsCache(orderGoodsCacheReq);
        }

        log.info("END创建套餐缓存");


        log.info("创建套餐额外礼赠缓存");

        if (CollectionUtils.isNotEmpty(orderMonthGiftExtendCacheReqList)) {
            //创建套餐额外礼赠缓存
            for (OrderMonthGiftExtendCacheReq orderMonthGiftExtendCacheReq : orderMonthGiftExtendCacheReqList) {
                updateOrderMonthGiftExtendCache(orderMonthGiftExtendCacheReq);
            }
        }

        log.info("END创建套餐额外礼赠缓存");


        log.info("创建套餐加收项缓存");
        if (CollectionUtils.isNotEmpty(orderMonthAdditionalRevenueCacheReqArrayList)) {
            //创建套餐加收项缓存
            orderMonthAdditionalRevenueCache(orderMonthAdditionalRevenueCacheReqArrayList);
        }
        log.info("END创建套餐加收项缓存");

        log.info("创建套餐额外信息");
        if (ObjectUtil.isNotEmpty(orderOtherInfoCacheReq)) {
            //创建套餐额外信息
            orderOtherInfoCache(orderOtherInfoCacheReq);
        }
        log.info("END创建套餐额外信息");

        return Result.success();
    }


    /**
     * 创建订单的额外礼赠缓存
     *
     * @param orderId
     */
    @Override
    public void createGiftExtendSkuCache(Integer orderId) {
        OrderInfoByOrderVO orderInfoByOrderId = monthOrderWxQueryService.getOrderInfoByOrderId(orderId);
        List<OrderInfoByGiftExtendSkuVO> orderInfoByGiftExtendSkuVOList = orderInfoByOrderId.getOrderInfoByGiftExtendVO().getOrderInfoByGiftExtendSkuVOList();
        List<OrderMonthGiftExtendCacheReq> orderMonthGiftExtendCacheReqList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(orderInfoByGiftExtendSkuVOList)) {


            //实物虚拟商品需要合并
            List<OrderInfoByGiftExtendSkuVO> newOrderInfoByGiftExtendSkuVOList = new ArrayList<>();
            newOrderInfoByGiftExtendSkuVOList.addAll(orderInfoByGiftExtendSkuVOList.stream().filter(o -> o.getType() != 7).collect(Collectors.toList()));

            List<OrderInfoByGiftExtendSkuVO> collect = orderInfoByGiftExtendSkuVOList.stream().filter(o -> o.getType() == 7).collect(Collectors.toList());


            Map<String, OrderInfoByGiftExtendSkuVO> orderInfoByGiftExtendSkuVOMap = new HashMap<>();
            for (OrderInfoByGiftExtendSkuVO orderInfoByGiftExtendSkuV : collect) {
                String key = orderInfoByGiftExtendSkuV.getGoodsId() + "-" + orderInfoByGiftExtendSkuV.getSkuId();
                if (null != orderInfoByGiftExtendSkuVOMap.get(key)) {
                    OrderInfoByGiftExtendSkuVO orderInfoByGiftExtendSkuVO = orderInfoByGiftExtendSkuVOMap.get(key);
                    orderInfoByGiftExtendSkuVO.setQuantity(orderInfoByGiftExtendSkuVO.getQuantity() + orderInfoByGiftExtendSkuV.getQuantity());
                } else {
                    orderInfoByGiftExtendSkuVOMap.put(key, orderInfoByGiftExtendSkuV);
                }
            }
            for (String key : orderInfoByGiftExtendSkuVOMap.keySet()) {
                newOrderInfoByGiftExtendSkuVOList.add(orderInfoByGiftExtendSkuVOMap.get(key));
            }


            newOrderInfoByGiftExtendSkuVOList.stream().forEach(o -> {
                OrderMonthGiftExtendCacheReq orderMonthGiftExtendCacheReq = new OrderMonthGiftExtendCacheReq().setQuantity(o.getQuantity()).setGoodsId(o.getGoodsId()).setSkuId(o.getSkuId()).setCategoryFront(o.getCategoryFront());
                orderMonthGiftExtendCacheReq.setOrderId(orderId);
                orderMonthGiftExtendCacheReqList.add(orderMonthGiftExtendCacheReq);
            });
        }
        if (CollectionUtils.isNotEmpty(orderMonthGiftExtendCacheReqList)) {
            //创建套餐额外礼赠缓存
            for (OrderMonthGiftExtendCacheReq orderMonthGiftExtendCacheReq : orderMonthGiftExtendCacheReqList) {
                updateOrderMonthGiftExtendCache(orderMonthGiftExtendCacheReq);
            }
        }
    }

    @Override
    public Result getOrderCacheByOrderIdAndCheck(OrderCacheByOrderIdReq orderCacheByOrderIdReq) {
        //判断有缓存
        RemoveAllOrderCacheReq removeAllOrderCacheReq = new RemoveAllOrderCacheReq();
        removeAllOrderCacheReq.setOrderId(orderCacheByOrderIdReq.getOrderId());
        Result<RemoveAllOrderCacheVO> removeAllOrderCacheVOResult = checkCacheByOrderId(removeAllOrderCacheReq);
        log.info("是否生成了全部缓存：" + (!removeAllOrderCacheVOResult.getData().getExist()));
        if (!removeAllOrderCacheVOResult.getData().getExist()) {
            //生成缓存
            getOrderCacheByOrderId(orderCacheByOrderIdReq);
        }
        return Result.success();
    }

    @Override
    public Result getOrderCacheByOrderIdAndCheckForCustomer(OrderCacheByOrderIdReq orderCacheByOrderIdReq) {
        //判断有缓存
        RemoveAllOrderCacheReq removeAllOrderCacheReq = new RemoveAllOrderCacheReq();
        removeAllOrderCacheReq.setOrderId(orderCacheByOrderIdReq.getOrderId());
        Result<RemoveAllOrderCacheVO> removeAllOrderCacheVOResult = checkCacheByOrderId(removeAllOrderCacheReq);
        log.info("是否生成了全部缓存：" + (!removeAllOrderCacheVOResult.getData().getExist()));
        if (!removeAllOrderCacheVOResult.getData().getExist()) {
            //生成缓存
            getOrderCacheByOrderIdForCustomer(orderCacheByOrderIdReq);
        }
        return Result.success();
    }


    @Override
    public Result removeAllBeforeOrderCache() {
        orderRepository.removeAllBeforeOrderCache();
        return Result.success();
    }

    @Override
    public Result closeOrder(Integer orderId) {
        //订单关闭	所有没有收款、退款流水的订单，且没有签订过任何合同的，能关闭


        HeOrderEntity byOrderId = orderRepository.getByOrderId(orderId);
        if (ObjectUtil.isEmpty(byOrderId)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "订单不存在");
        }

        // 收款记录
        List<HeIncomeRecordEntity> incomeList = incomeRecordRepository.getSuccessfulRecordListByOrderId(orderId);

        // 退款记录
        List<HeOrderRefundEntity> refundList = orderRefundRepository.getRefundByOrderId(orderId);

        // 签约记录表
        List<MonthContractSignRecordEntity> mainContractList = monthContractSignRecordRepository.getListByOrderId(orderId.longValue());

        // 合同补充协议
        List<MonthContractSignAgreementEntity> agreementContractList = monthContractSignAgreementRepository.list(orderId.longValue(), 2);

        // 纸质合同
        List<ContractSignRecordPaperEntity> paperList = contractSignRecordPaperRepository.getByOrderId(orderId);


        //查询已签订的
        mainContractList = CollectionUtils.isEmpty(mainContractList) ? new ArrayList<>() : mainContractList.stream().filter(c -> ContractStatusEnum.SIGNED.code().equals(c.getContractStatus())).collect(Collectors.toList());
        agreementContractList = CollectionUtils.isEmpty(agreementContractList) ? new ArrayList<>() : agreementContractList.stream().filter(c -> c.getState() == 2).collect(Collectors.toList());
        paperList = CollectionUtils.isEmpty(paperList) ? new ArrayList<>() : paperList.stream().filter(c -> ContractStatusEnum.SIGNED.code().equals(c.getContractStatus())).collect(Collectors.toList());

        boolean flag = CollectionUtils.isEmpty(incomeList) && CollectionUtils.isEmpty(refundList);

        boolean contractFlag = CollectionUtils.isEmpty(mainContractList) && CollectionUtils.isEmpty(agreementContractList) && CollectionUtils.isEmpty(paperList);

        if (flag && contractFlag) {
            //才可以关闭订单
            byOrderId.setOrderStatus(OrderStatusV2Enum.CLOSE.getCode());
            orderRepository.closeOrder(byOrderId);
            //关闭订单产康金失效
            orderAssetTradeService.failureCkj(byOrderId.getBasicUid() + "", byOrderId.getOrderSn(), new Operator());
        } else if (!flag) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "订单有收款记录，无法关闭");
        } else if (!contractFlag) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "订单签署了合同，无法关闭");
        }
        return Result.success();
    }

    @Override
    public List<RefundReasonVO> getRefundReasonByType(Integer type) {
        return RefundReasonEnum.getByType(type).stream().map(r -> {
            RefundReasonVO refundReasonVO = new RefundReasonVO();
            refundReasonVO.setValue(r.getCode());
            refundReasonVO.setLabel(r.getValue());
            return refundReasonVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<RefundReasonVO> getOmniRefundReasonByType(Integer type) {
        return RefundReasonEnum.getOmniByType(type).stream().map(r -> {
            RefundReasonVO refundReasonVO = new RefundReasonVO();
            refundReasonVO.setValue(r.getCode());
            refundReasonVO.setLabel(r.getValue());
            return refundReasonVO;
        }).collect(Collectors.toList());
    }

    /**
     * 同步订单退款状态
     *
     * @param orderId
     */
    @Override
    public Result<Integer> syncOrderRefundStatus(Integer orderId) throws BusinessException {
        //订单退款状态按退款流水判断
        Integer refundStatus = getRefundStatusByOrderId(orderId);
        return Result.success(orderRepository.syncOrderRefundStatus(orderId, refundStatus));
    }

    @Resource
    private IncomePaidAllocationRepository incomePaidAllocationRepository;

    /**
     * 获取订单退款状态
     *
     * @param orderId
     */
    public Integer getRefundStatusByOrderId(Integer orderId) {
        //获取已支付的流水
        List<HeIncomeRecordEntity> recordListByOrderId = incomeRecordRepository.getSuccessfulRecordListByOrderId(orderId);
        //获取退款流水
        List<HeOrderRefundEntity> refundByOrderId = orderRefundRepository.getRefundByOrderId(orderId);
        //累计支付
        AtomicReference<Integer> totalIncome = new AtomicReference<>(0);
        //累计冻结
        AtomicReference<Integer> totalFreeze = new AtomicReference<>(0);
        //累计已退
        AtomicReference<Integer> totalAlreadyRefund = new AtomicReference<>(0);
        recordListByOrderId.forEach(x -> {
            totalIncome.updateAndGet(j -> j + x.getIncome());
            totalFreeze.updateAndGet(k -> k + (ObjectUtil.isEmpty(x.getFreezeAmount()) ? 0 : x.getFreezeAmount()));
            totalAlreadyRefund.updateAndGet(l -> l + (ObjectUtil.isEmpty(x.getAlreadyRefundAmount()) ? 0 : x.getAlreadyRefundAmount()));
        });
        //"退款状态：0-无退款 1-部分退款中 2-部分退款 3-全部退款中 4-全部退款")
        //冻结金额不为0则为部分退款中,或全部退款中
        //冻结金额为0则为部分退款或全部退款
        //支付金额>已退 则为部分退款或部分退款中,支付金额=已退则为全部退款中或为全部退款
        Integer refundStatus = null;
        if (ObjectUtil.isEmpty(refundByOrderId)) {
            refundStatus = OrderRefundStatusEnum.NONE.getCode();
        } else {
            if (ObjectUtil.equals(totalFreeze.get(), 0)) {
                if (ObjectUtil.equals(totalIncome.get(), totalAlreadyRefund.get())) {
                    refundStatus = OrderRefundStatusEnum.FULL_REFUND.getCode();
                } else {
                    refundStatus = OrderRefundStatusEnum.PARTIAL_REFUND.getCode();
                }
            } else {
                if (ObjectUtil.equals(totalIncome.get(), totalAlreadyRefund.get() + totalFreeze.get())) {
                    refundStatus = OrderRefundStatusEnum.FULL_REFUND_IN_PROGRESS.getCode();
                } else {
                    refundStatus = OrderRefundStatusEnum.PART_OF_THE_REFUND.getCode();
                }
            }
        }
        log.info("同步订单退款状态,orderId={},totalIncome={},totalFreeze={},totalAlreadyRefund={},refundStatus={}", orderId, totalIncome.get(), totalFreeze.get(), totalAlreadyRefund.get(), refundStatus);
        return refundStatus;
    }

    @Resource
    private HeOrderRefundGoodsRepository orderRefundGoodsRepository;

    @Override
    public Result<Integer> syncOrderRefundStatusForNew(Integer orderId) throws BusinessException {
        //订单退款状态按退款流水判断
        getRefundStatusByOrderIdForNewOrder(orderId);
        return Result.success(orderId);
    }

    private Integer getRefundStatusByOrderIdForNewOrder(Integer orderId) {
        HeOrderEntity orderEntity = orderRepository.queryOrderById(orderId);

        //获取已支付的流水
        List<IncomePaidAllocationEntity> incomePaidAllocationEntities = incomePaidAllocationRepository.queryListByOrderId(orderId.longValue());
        //获取已支付的流水
        List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList = orderRefundGoodsRepository.queryByOrderId(orderId);
        //累计支付
        BigDecimal totalIncome = BigDecimal.ZERO;
        //累计冻结
        BigDecimal totalFreeze = BigDecimal.ZERO;
        //累计已退
        BigDecimal totalAlreadyRefund = BigDecimal.ZERO;
        for (IncomePaidAllocationEntity incomePaidAllocationEntity : incomePaidAllocationEntities) {
            totalIncome = totalIncome.add(RMBUtils.bigDecimalF2Y(incomePaidAllocationEntity.getPaidAmount()));
        }
        for (HeOrderRefundGoodsEntity heOrderRefundGoodsEntity : orderRefundGoodsEntityList) {
            if (heOrderRefundGoodsEntity.getStatus().equals(OrderRefundGoodsStatusEnum.REFUNDING.code())) {
                totalFreeze = totalFreeze.add(RMBUtils.bigDecimalF2Y(heOrderRefundGoodsEntity.getRefundAmount()));
            }
            if (heOrderRefundGoodsEntity.getStatus().equals(OrderRefundGoodsStatusEnum.SUCCESS.code())) {
                totalAlreadyRefund = totalAlreadyRefund.add(RMBUtils.bigDecimalF2Y(heOrderRefundGoodsEntity.getRefundAmount()));
            }
        }

        //"退款状态：0-无退款 1-部分退款中 2-部分退款 3-全部退款中 4-全部退款")
        //冻结金额不为0则为部分退款中,或全部退款中
        //冻结金额为0则为部分退款或全部退款
        //支付金额>已退 则为部分退款或部分退款中,支付金额=已退则为全部退款中或为全部退款
        Integer refundStatus = null;
        if (CollectionUtils.isEmpty(orderRefundGoodsEntityList)) {
            refundStatus = OrderRefundStatusEnum.NONE.getCode();
        } else {
            if (totalFreeze.compareTo(BigDecimal.ZERO) == 0) {
                if (totalAlreadyRefund.compareTo(totalIncome) == 0) {
                    refundStatus = OrderRefundStatusEnum.FULL_REFUND.getCode();
                } else {
                    refundStatus = OrderRefundStatusEnum.PARTIAL_REFUND.getCode();
                }
            } else {
                if (totalIncome.compareTo(totalAlreadyRefund.add(totalFreeze)) == 0) {

                    refundStatus = OrderRefundStatusEnum.FULL_REFUND_IN_PROGRESS.getCode();
                } else {
                    refundStatus = OrderRefundStatusEnum.PART_OF_THE_REFUND.getCode();
                }
            }
        }
        log.info("同步订单退款状态,orderId={},totalIncome={},totalFreeze={},totalAlreadyRefund={},refundStatus={}", orderId, totalIncome, totalFreeze, totalAlreadyRefund, refundStatus);

        orderEntity.setRefundStatus(refundStatus);
        //全部退款且全部退款的时间为空
        if (refundStatus.equals(OrderRefundStatusEnum.FULL_REFUND.getCode()) && ObjectUtil.isEmpty(orderEntity.getFullRefundDate())) {
            //TODO 订单第一次全部退款
            orderEntity.setFullRefundDate(new Date());
        }
        orderRepository.updateOne(orderEntity);
        return refundStatus;
    }

    /**
     * 老订单数据清洗新订单-订单ID
     *
     * @param orderId
     * @return
     */
    @Override
    public String oldOrderSyncByOrderId(Integer orderId) {
        String msg;
        //首先判断该订单是否是老订单
        HeOrderEntity oldOrderEntity = orderRepository.getByOrderId(orderId);
        final Integer basicUid = oldOrderEntity.getBasicUid();
        final Integer clientUid = oldOrderEntity.getClientUid();
        final Integer storeId = oldOrderEntity.getStoreId();
        if (ObjectUtil.isEmpty(oldOrderEntity)) {
            msg = "老订单清洗失败-订单不存在,订单ID=" + orderId;
            log.info(msg);
            return msg;
        }
        if (ObjectUtil.isNotEmpty(oldOrderEntity.getOldOrNew()) && oldOrderEntity.getOldOrNew().equals(1)) {
            msg = "老订单清洗失败-该笔是新订单,订单ID=" + orderId;
            log.info(msg);
            return msg;
        }
        if (ObjectUtil.notEqual(oldOrderEntity.getOrderType(), OmniOrderTypeEnum.MONTH_ORDER.code())) {
            msg = "老订单清洗失败-该笔不是月子标准订单,订单ID=" + orderId;
            log.info(msg);
            return msg;
        }
        HeTaskEntity byTaskId = heTaskRepository.getByTaskId(oldOrderEntity.getTaskId());
        List<OrderTaskJsonDTO> orderTaskJsonDTOList = JSONUtil.toList(byTaskId.getFormtemplate(), OrderTaskJsonDTO.class);
        OrderTaskJsonDTO orderTaskJsonDTO = new OrderTaskJsonDTO();
        if (ObjectUtil.isNotEmpty(orderTaskJsonDTOList)) {
            orderTaskJsonDTO = orderTaskJsonDTOList.get(0);
            log.info("task订单快照信息={}", JSONUtil.toJsonStr(orderTaskJsonDTO));
        }
        List<OrderTaskJsonDTO.Content> contentList = orderTaskJsonDTO.getContent();
        //老订单V1或者V2(task表formtemplateType为5的为V1订单,42为V2订单)
        //需要特殊处理产康金和赠送服务
        // (order_gift表中的goods_type=2产康金)(order_gift表中的goods_type=3赠送服务)
        //产康金转移到额外礼赠,赠送服务通过逗号分割将goods_id与he_derive_services中id关联取出名称追加到he_order的remark字段
        OrderV1V2RelatedDTO orderV1V2RelatedDTOByOrderId = new OrderV1V2RelatedDTO();
        //默认44 V3版本
        Integer formtemplateType = 44;
        if (ObjectUtil.isNotEmpty(byTaskId.getFormtemplateType()) && (byTaskId.getFormtemplateType().equals(5) || byTaskId.getFormtemplateType().equals(42))) {
            formtemplateType = byTaskId.getFormtemplateType();
            orderV1V2RelatedDTOByOrderId = heTaskRepository.getOrderV1V2RelatedDTOByOrderId(orderId);
            log.info("老订单V1或者V2信息={}", JSONUtil.toJsonStr(orderV1V2RelatedDTOByOrderId));
        }

        //清洗订单商品
        HeOrderGoodsEntity heOrderGoodsEntity = getHeOrderGoodsEntity(orderId, basicUid);
        orderGoodsRepository.updateOne(heOrderGoodsEntity);
        //清洗客户快照
        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = getHeOrderUserSnapshotEntity(orderId, oldOrderEntity, contentList, formtemplateType);
        log.info("老订单清洗-客户快照数据={}", JSONUtil.parse(heOrderUserSnapshotEntity));
        orderUserSnapshotRepository.insertOrUpdateOne(heOrderUserSnapshotEntity);
        //清洗加收项(2023-12-8产品逻辑 节日费用要考虑额外礼赠里的折扣)
        List<OrderGiftExtendEntity> giftExtendEntityList = orderGiftExtendRepository.getByOrderId(orderId);
        //加收项清洗要先删除原先的,在新增
        orderAdditionalRevenueRepository.delByOrderId(orderId);
        List<HeOrderAdditionalRevenueEntity> additionalRevenueEntityList = giftExtendEntity2AdditionalRevenueEntityList(giftExtendEntityList, oldOrderEntity);
        log.info("老订单清洗-加收项数据={}", JSONUtil.parse(additionalRevenueEntityList));
        orderAdditionalRevenueRepository.batchInsert(additionalRevenueEntityList);
        //清洗额外礼赠(需要逻辑删除该订单下的节假日费用和续住信息)
        boolean isNeedDeletedGift = false;
        boolean isNeedUpdateProduct = false;
        for (OrderGiftExtendEntity orderGiftExtendEntity : giftExtendEntityList) {
            if (orderGiftExtendEntity.getType().equals(OrderGiftExtendTypeEnum.CONTINUE_TO_LIVE.code()) || orderGiftExtendEntity.getType().equals(OrderGiftExtendTypeEnum.ROOM_TYPE_UPGRADE.code()) || orderGiftExtendEntity.getType().equals(OrderGiftExtendTypeEnum.FESTIVAL_EXPENSES.code())) {
                isNeedDeletedGift = true;
                continue;
            }
            if (orderGiftExtendEntity.getType().equals(OrderGiftExtendTypeEnum.SANKANGJIN.code())) {
                isNeedUpdateProduct = true;
                continue;
            }
        }
        if (isNeedDeletedGift) {
            orderGiftExtendRepository.deleteGiftExtendByOrderIdForOld2New(orderId);
        }
        //添加V1V2产康金
        if (ObjectUtil.isNotEmpty(orderV1V2RelatedDTOByOrderId) && ObjectUtil.isNotEmpty(orderV1V2RelatedDTOByOrderId.getRelatedCouponAmount()) && orderV1V2RelatedDTOByOrderId.getRelatedCouponAmount() > 0) {
            orderGiftExtendRepository.deleteAndAddProductOrderGiftExtend(orderV1V2RelatedDTOByOrderId, heOrderUserSnapshotEntity.getBasicUid(), heOrderUserSnapshotEntity.getStoreId());
        }
        //修改V3产康金
        if (isNeedUpdateProduct) {
            Optional<OrderGiftExtendEntity> first = giftExtendEntityList.stream().filter(x -> x.getType().equals(OrderGiftExtendTypeEnum.SANKANGJIN.code())).findFirst();
            if (first.isPresent()) {
                orderGiftExtendRepository.updateProductOrderGiftExtend(first.get());
            }
        }
        //额外礼赠要拆开重新存(除产康金)
        updateGiftExtend(orderId, basicUid);
        //清洗未签署与签署中的合同(产品与运营缺的逻辑,2023-7-25)
        monthContractSignRecordRepository.delContractByOrderIdAndTypeAndStatus(orderId, TemplateContractTypeEnum.YZ_SAINTBELLA.code(), ContractStatusEnum.NOT_SIGNED.code());
        monthContractSignRecordRepository.delContractByOrderIdAndTypeAndStatus(orderId, TemplateContractTypeEnum.YZ_SAINTBELLA.code(), ContractStatusEnum.WAITING.code());
        //如果主合同已签署,需要手动推送history合同ID
        MonthContractSignRecordEntity byOrderIdAndContractStatus = monthContractSignRecordRepository.getByOrderIdAndContractStatus(orderId, TemplateContractTypeEnum.YZ_SAINTBELLA.code(), ContractStatusEnum.SIGNED.code());
        if (ObjectUtil.isNotEmpty(byOrderIdAndContractStatus)) {
            monthOrderParamHistoryService.setContractId(orderId.longValue(), 1, Math.toIntExact(byOrderIdAndContractStatus.getId()));
        }
        //清洗支付记录
        List<HeIncomeRecordEntity> allRecordEntityList = incomeRecordRepository.getSuccessfulRecordListByOrderId(orderId);
        //所有的线下支付需要手动设置income_sn,并且同步到线下支付凭证表中
        allRecordEntityList.forEach(x -> {
            if (ObjectUtil.equals(x.getPayType(), OmniPayTypeEnum.OFFLINE.getCode())) {
                //生成本地交易流水号
                String localTransactionalNo = IdGenUtils.createLocalTransactionalNo(null, new Date());
                x.setIncomeSn(localTransactionalNo);
                incomeRecordRepository.updateRecord(x);
                HeIncomeProofRecordEntity incomeProofRecordEntity = incomeProofRecordRepository.getLastOneByIncomeId(x.getId());
                incomeProofRecordEntity.setIncomeSn(localTransactionalNo);
                incomeProofRecordRepository.updateOne(incomeProofRecordEntity);
            }
        });

        //正常支付记录
        List<HeIncomeRecordEntity> recordEntityList = allRecordEntityList.stream().filter(x -> ObjectUtil.notEqual(x.getReceiptType(), IncomeReceiptTypeEnum.RECORD_RECEIPT_TYPE_DEPOSIT.code())).collect(Collectors.toList());
        List<Integer> recordIdList = recordEntityList.stream().map(HeIncomeRecordEntity::getId).collect(Collectors.toList());
        //押金支付记录
        List<HeIncomeRecordEntity> depositRecordEntityList = allRecordEntityList.stream().filter(x -> ObjectUtil.equals(x.getReceiptType(), IncomeReceiptTypeEnum.RECORD_RECEIPT_TYPE_DEPOSIT.code())).collect(Collectors.toList());
        //所有退款记录
        List<HeOrderRefundEntity> allOrderRefundEntityList = orderRefundRepository.getRefundByOrderId(orderId);
        //正常退款记录
        List<HeOrderRefundEntity> orderRefundEntityList = allOrderRefundEntityList.stream().filter(x -> recordIdList.contains(x.getOrderGoodId())).collect(Collectors.toList());
        List<Integer> depositRecordIdList = depositRecordEntityList.stream().map(HeIncomeRecordEntity::getId).collect(Collectors.toList());

        //清洗押金支付/退款记录
        syncDepositRecord(depositRecordEntityList, depositRecordIdList, oldOrderEntity);

        //清洗审批记录
        processOldToNew(orderId, oldOrderEntity, allOrderRefundEntityList);

        AtomicReference<Integer> payAmount = new AtomicReference<>(0);
        AtomicReference<Integer> refundAmount = new AtomicReference<>(0);
        //清洗支付记录数据
        getIncomeEntityList(clientUid, recordEntityList, orderRefundEntityList, payAmount);
        Integer realAmount;
        log.info("老订单清洗-支付记录数据={}", JSONUtil.parse(recordEntityList));

        incomeRecordRepository.batchUpdateRecordList(recordEntityList);
        //清洗退款记录
        getRefundEntityList(recordEntityList, orderRefundEntityList, refundAmount);
        realAmount = payAmount.get() - refundAmount.get();
        log.info("老订单清洗-退款记录数据={}", JSONUtil.parse(orderRefundEntityList));
        orderRefundRepository.batchUpdateById(orderRefundEntityList);
        //获取订单退款状态
        Integer refundStatus = getRefundStatusByOrderId(orderId);
        oldOrderEntity.setRealAmount(realAmount);
        oldOrderEntity.setOldOrNew(1);
        oldOrderEntity.setRefundStatus(refundStatus);
        //获取订单折扣(2023-12-8产品逻辑 取老订单折扣直接赋值)
        OrderDiscountsCacheVO orderDiscountsCacheVO = monthOrderWxQueryService.getOrderDiscountsByOrderId(orderId);
        if (ObjectUtil.isNotEmpty(orderDiscountsCacheVO)) {
            //订单折扣率
            orderDiscountsCacheVO.setOrderDiscount(oldOrderEntity.getDiscountMargin().multiply(new BigDecimal(100)));
            //净折扣率
            orderDiscountsCacheVO.setNetDiscountRate(oldOrderEntity.getNetMargin().multiply(new BigDecimal(100)));
            //毛利率
            orderDiscountsCacheVO.setGrossProfitMargin(oldOrderEntity.getGrossMargin().multiply(new BigDecimal(100)));
        }
        oldOrderEntity.setDiscountDetails(ObjectUtil.isEmpty(orderDiscountsCacheVO) ? "" : JsonUtil.write(orderDiscountsCacheVO));
        //设置省市区
        CfgStoreEntity cfgStoreEntity = cfgStoreRepository.queryCfgStoreById(storeId);
        if (ObjectUtil.isNotEmpty(cfgStoreEntity)) {
            Integer province = cfgStoreEntity.getProvince();
            CfgDistrictEntity provinceById = districtRepository.getOneById(province);
            Integer city = cfgStoreEntity.getCity();
            CfgDistrictEntity cityById = districtRepository.getOneById(city);
            Integer region = cfgStoreEntity.getRegion();
            CfgDistrictEntity regionById = districtRepository.getOneById(region);
            if (ObjectUtil.isNotEmpty(provinceById)) {
                oldOrderEntity.setProvince(provinceById.getCode());
            }
            if (ObjectUtil.isNotEmpty(cityById)) {
                oldOrderEntity.setCity(cityById.getCode());
            }
            if (ObjectUtil.isNotEmpty(regionById)) {
                oldOrderEntity.setArea(regionById.getCode());
            }
        }
        //V1V2订单添加订单remark
        if (ObjectUtil.isNotEmpty(orderV1V2RelatedDTOByOrderId) && ObjectUtil.isNotEmpty(orderV1V2RelatedDTOByOrderId.getRelatedServiceNames())) {
            if (!oldOrderEntity.getRemark().contains(orderV1V2RelatedDTOByOrderId.getRelatedServiceNames())) {
                String remark = oldOrderEntity.getRemark() + "," + orderV1V2RelatedDTOByOrderId.getRelatedServiceNames();
                if (remark.startsWith(",")) {
                    remark = remark.substring(1);
                }
                oldOrderEntity.setRemark(remark);
            }
        }
        //清洗订单主体
        log.info("老订单清洗-订单主表记录数据={}", JSONUtil.parse(oldOrderEntity));
        orderRepository.updateOrderMonthByOrderId(oldOrderEntity);
        //添加老订单清洗记录log
        HeOrderSyncLogEntity heOrderSyncLogEntity = getHeOrderSyncLogEntity(orderId, oldOrderEntity, heOrderGoodsEntity, heOrderUserSnapshotEntity, additionalRevenueEntityList, recordEntityList, orderRefundEntityList);
        heOrderSyncLogRepository.saveOrUpdateOne(heOrderSyncLogEntity);
        msg = "老订单清洗-成功,订单ID=" + orderId;
        return msg;
    }

    private void updateGiftExtend(Integer orderId, Integer basicUid) {
        List<OrderGiftExtendEntity> byOrderId = orderGiftExtendRepository.getByOrderId(orderId);
        //去除产康金
        List<OrderGiftExtendEntity> orderGiftExtendEntities = byOrderId.stream().filter(x -> !x.getType().equals(OrderGiftExtendTypeEnum.SANKANGJIN.code())).collect(Collectors.toList());
        List<OrderGiftExtendEntity> newOrderGiftExtendEntities = new ArrayList<>();
        for (OrderGiftExtendEntity orderGiftExtendEntity : orderGiftExtendEntities) {
            //1=产康金 2=房型升级 3=续住 4=节日费用 5=家属房 6=产康服务 7=实物商品
            if (orderGiftExtendEntity.getType().equals(OrderGiftExtendTypeEnum.FAMILY_ROOM.code())) {
                List<OrderGiftExtendRoomDTO> orderGiftExtendRoomDTOS = JSONUtil.toList(orderGiftExtendEntity.getContent(), OrderGiftExtendRoomDTO.class);
                if (ObjectUtil.isNotEmpty(orderGiftExtendRoomDTOS)) {
                    for (OrderGiftExtendRoomDTO orderGiftExtendRoomDTO : orderGiftExtendRoomDTOS) {
                        OrderGiftExtendEntity newOrderGiftExtendEntity = new OrderGiftExtendEntity();
                        BeanUtil.copyProperties(orderGiftExtendEntity, newOrderGiftExtendEntity, "id");

                        newOrderGiftExtendEntity.setGoodsNum(orderGiftExtendRoomDTO.getDays());
                        newOrderGiftExtendEntity.setGoodsId(orderGiftExtendRoomDTO.getRoom_id());
                        newOrderGiftExtendEntity.setSkuId(orderGiftExtendRoomDTO.getRoom_id());
                        newOrderGiftExtendEntity.setPrice(orderGiftExtendRoomDTO.getRoom_price());
                        newOrderGiftExtendEntity.setCost(orderGiftExtendRoomDTO.getRoom_cost());
                        newOrderGiftExtendEntity.setGoodsName(orderGiftExtendRoomDTO.getRoom_name());
                        newOrderGiftExtendEntity.setCategoryId(-1);
                        newOrderGiftExtendEntity.setCategoryName("家属房");
                        newOrderGiftExtendEntity.setSkuName(orderGiftExtendRoomDTO.getRoom_name());
                        newOrderGiftExtendEntity.setSkuNum(1);
                        newOrderGiftExtendEntity.setContent("");
                        newOrderGiftExtendEntity.setOrderId(orderId);
                        newOrderGiftExtendEntity.setBasicId(basicUid);
                        newOrderGiftExtendEntities.add(newOrderGiftExtendEntity);
                    }
                }
                //原家属房礼赠要删除
                orderGiftExtendRepository.deleteById(orderGiftExtendEntity.getId());
            }
        }
        if (ObjectUtil.isNotEmpty(newOrderGiftExtendEntities)) {
            orderGiftExtendRepository.batchInsertOrUpdate(newOrderGiftExtendEntities);
        }
    }

    private void processOldToNew(Integer orderId, HeOrderEntity oldOrderEntity, List<HeOrderRefundEntity> allOrderRefundEntityList) {
        //订单所有审批记录;
        List<Old2NewOrderDTO> processIdList = getOld2NewOrderDTOS(allOrderRefundEntityList);
        List<Old2NewOrderDTO> taskHisProcess = heTaskRepository.getTaskHisProcess(oldOrderEntity.getTaskId());
        processIdList.addAll(taskHisProcess);
        if (ObjectUtil.isNotEmpty(processIdList)) {
            log.info("需要修改的审批记录={},订单ID={}", JSONUtil.toJsonStr(processIdList), orderId);
            baseManager.old2New(processIdList, orderId);
        }
    }

    private void getRefundEntityList(List<HeIncomeRecordEntity> recordEntityList, List<HeOrderRefundEntity> orderRefundEntityList, AtomicReference<Integer> refundAmount) {
        orderRefundEntityList.forEach(x -> {
            x.setIncomeSn(recordEntityList.stream().filter(y -> y.getId().equals(x.getOrderGoodId())).findFirst().get().getIncomeSn());
            HeProjectEntity oneByProjectId = heProjectRepository.getOneByProjectId(x.getProjectId());
            if (ObjectUtil.isNotEmpty(oneByProjectId)) {
                if (ObjectUtil.isNotEmpty(oneByProjectId.getRealTime())) {
                    //审批同意时间
                    x.setAgreeAt(oneByProjectId.getRealTime().getTime() / 1000);
                }
                if (ObjectUtil.isNotEmpty(oneByProjectId.getStaffId()) && oneByProjectId.getStaffId() != 0) {
                    //申请人ID
                    x.setApplyId(Math.toIntExact(oneByProjectId.getStaffId()));
                    //申请人名称
                    x.setApplyName(oneByProjectId.getStaffName());
                    UserEntity userEntity = userRepository.queryById(Math.toIntExact(oneByProjectId.getStaffId()));
                    if (ObjectUtil.isNotEmpty(userEntity)) {
                        //申请人手机号
                        x.setApplyPhone(userEntity.getPhone());
                    }
                }
            }
            //退款金额
            if (x.getStatus().equals(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode())) {
                refundAmount.updateAndGet(v -> v + x.getApplyAmount());
            }
        });
    }

    public String getBackCategoryName(Map<Integer, GoodsCategoryBackEntity> categoryBackEntityMap, Integer categoryId) {
        GoodsCategoryBackEntity entity = categoryBackEntityMap.get(categoryId);
        if (Objects.isNull(entity)) {
            return "";
        }
        return entity.getName();
    }

    public String getFrontCategoryName(Map<Integer, GoodsCategoryFrontEntity> backCateFrontMap, Integer categoryId) {
        GoodsCategoryFrontEntity entity = backCateFrontMap.get(categoryId);
        if (Objects.isNull(entity)) {
            return "";
        }
        return entity.getName();
    }

    private void getIncomeEntityList(Integer clientUid, List<HeIncomeRecordEntity> recordEntityList, List<HeOrderRefundEntity> orderRefundEntityList, AtomicReference<Integer> payAmount) {
        recordEntityList.forEach(x -> {
            x.setClientUid(clientUid);
            List<HeOrderRefundEntity> refundEntityByIncomeIdList = orderRefundEntityList.stream().filter(y -> y.getOrderGoodId().equals(x.getId())).collect(Collectors.toList());
            //设置冻结金额和已退金额
            setFreezeAmountAndAlreadyAmount(recordEntityList, refundEntityByIncomeIdList, x);
            if (x.getStatus().equals(PayStatusEnum.PAY_STATUS_SUCCESS.getCode())) {
                payAmount.updateAndGet(v -> v + x.getIncome());
            }
        });
    }

    /**
     * 这个方法会查找并返回字符串中第一个数字序列，该序列后面没有跟随任何数字或中文字符。如果找不到这样的数字序列，则返回空字符串
     */
    public static String extractNumberBeforeNonDigit(String input) {
        Matcher matcher = Pattern.compile("(\\d+)(?![\\d\\u4e00-\\u9fa5])").matcher(input);
        if (matcher.find()) {
            return matcher.group(1);
        } else {
            return "";
        }
    }

    @SneakyThrows
    private HeOrderUserSnapshotEntity getHeOrderUserSnapshotEntity(Integer orderId, HeOrderEntity oldOrderEntity, List<OrderTaskJsonDTO.Content> contentList, Integer formtemplateType) {
        OrderMonthClientReq clientInfoById = tabClientManager.getClientInfoById(oldOrderEntity.getClientUid());
        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.orderMonthClientReq2Entity(clientInfoById, orderId);
        heOrderUserSnapshotEntity.setSellerId(oldOrderEntity.getStaffId());
        UserEntity userEntity = userRepository.queryById(oldOrderEntity.getStaffId());
        if (ObjectUtil.isNotEmpty(userEntity)) {
            heOrderUserSnapshotEntity.setSellerName(userEntity.getName());
        }
        //老订单快照信息
        for (OrderTaskJsonDTO.Content content : contentList) {
            //V2V3版本
            if (formtemplateType.equals(44) || formtemplateType.equals(42)) {
                //基本信息
                if (content.getKey_name().equals("order_client")) {
                    List<OrderTaskJsonDTO.Components> components = content.getComponents();
                    if (ObjectUtil.isNotEmpty(components)) {
                        for (OrderTaskJsonDTO.Components component : components) {
                            if (component.getKey_name().equals("name")) {
                                heOrderUserSnapshotEntity.setName(component.getValue());
                                continue;
                            }
                            if (component.getKey_name().equals("phone")) {
                                heOrderUserSnapshotEntity.setPhone(component.getValue());
                                heOrderUserSnapshotEntity.setIsPhoneVerify(component.getValid_status());
                                continue;
                            }
                            if (component.getKey_name().equals("cert_type")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    heOrderUserSnapshotEntity.setCertType(Integer.valueOf(component.getValue()));
                                    continue;
                                }
                            }
                            if (component.getKey_name().equals("id_card_no")) {
                                heOrderUserSnapshotEntity.setIdCard(component.getValue());
                                continue;
                            }
                            if (component.getKey_name().equals("id_card_front")) {
                                heOrderUserSnapshotEntity.setIdCardFront(component.getValue());
                                continue;
                            }
                            if (component.getKey_name().equals("id_card_back")) {
                                heOrderUserSnapshotEntity.setIdCardBack(component.getValue());
                                continue;
                            }
                            if (component.getKey_name().equals("cert_status_name")) {
                                heOrderUserSnapshotEntity.setIsCardVerify(component.getValid_status());
                                continue;
                            }
                        }
                    }
                    continue;
                }
                //合同信息
                if (content.getKey_name().equals("order_contract")) {
                    List<OrderTaskJsonDTO.Components> components = content.getComponents();
                    if (ObjectUtil.isNotEmpty(components)) {
                        for (OrderTaskJsonDTO.Components component : components) {
                            if (component.getKey_name().equals("predict_born_date")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                                    heOrderUserSnapshotEntity.setPredictBornDate(sdf.parse(component.getValue()));
                                }
                                continue;
                            }
                            if (component.getKey_name().equals("want_in")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                                    heOrderUserSnapshotEntity.setWantIn(sdf.parse(component.getValue()));
                                }
                                continue;
                            }
                            if (component.getKey_name().equals("home_address")) {
                                heOrderUserSnapshotEntity.setAddress(component.getValue());
                                continue;
                            }
                            if (component.getKey_name().equals("emergency_contact")) {
                                heOrderUserSnapshotEntity.setUrgentName(component.getValue());
                                continue;
                            }
                            if (component.getKey_name().equals("emergency_contact_phone")) {
                                heOrderUserSnapshotEntity.setUrgentPhone(component.getValue());
                                continue;
                            }
                            if (component.getKey_name().equals("relation_with_client")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    heOrderUserSnapshotEntity.setRelationWithClient(Integer.valueOf(component.getValue()));
                                }
                                continue;
                            }
                        }
                    }
                    continue;
                }

                //订单信息
                if (content.getKey_name().equals("goods_childbirth_care")) {
                    continue;
                }

                //其他信息
                if (content.getKey_name().equals("client_other")) {
                    List<OrderTaskJsonDTO.Components> components = content.getComponents();
                    if (ObjectUtil.isNotEmpty(components)) {
                        for (OrderTaskJsonDTO.Components component : components) {
                            if (component.getKey_name().equals("age")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    try {
                                        heOrderUserSnapshotEntity.setAge(Integer.valueOf(component.getValue()));
                                    } catch (Exception e) {
                                        heOrderUserSnapshotEntity.setAge(0);
                                    }

                                    continue;
                                }
                            }
                            if (component.getKey_name().equals("blood_type")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    heOrderUserSnapshotEntity.setBloodType(Integer.valueOf(component.getValue()));
                                    continue;
                                }
                            }
                            if (component.getKey_name().equals("profession")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    heOrderUserSnapshotEntity.setProfession(component.getValue());
                                    continue;
                                }
                            }
                            if (component.getKey_name().equals("constellation_type")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    if (Integer.valueOf(component.getValue()) == -1) {
                                        component.setValue(String.valueOf(0));
                                    }
                                    heOrderUserSnapshotEntity.setConstellationType(Integer.valueOf(component.getValue()));
                                    heOrderUserSnapshotEntity.setConstellation(ConstellationTypeEnum.getValueByCode(Integer.valueOf(component.getValue())));
                                    continue;
                                }
                            }
                            if (component.getKey_name().equals("from_type")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    heOrderUserSnapshotEntity.setFromType(Integer.valueOf(component.getValue()));
                                    continue;
                                }
                            }
                            if (component.getKey_name().equals("hospital")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    heOrderUserSnapshotEntity.setHospital(component.getValue());
                                    continue;
                                }
                            }
                            if (component.getKey_name().equals("gestation_week_now")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    String gestationWeekNow = extractNumberBeforeNonDigit(component.getValue());
                                    if (ObjectUtil.isEmpty(gestationWeekNow)) {
                                        heOrderUserSnapshotEntity.setGestationWeekNow(0);
                                    } else {
                                        heOrderUserSnapshotEntity.setGestationWeekNow(Integer.valueOf(gestationWeekNow));
                                    }
                                    //v1v2v3迁移特殊逻辑, 根据孕周判断是否备孕中
                                    if (heOrderUserSnapshotEntity.getGestationWeekNow().equals(0)) {
                                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                                        heOrderUserSnapshotEntity.setPredictBornDate(sdf.parse("2099-12-01"));
                                    }
                                    continue;
                                }
                            }

                        }
                    }
                    continue;
                }

                //推荐码
                if (content.getKey_name().equals("invite_info")) {
                    Map<String, Object> data = content.getData();
                    if (ObjectUtil.isNotEmpty(data.get("invite_qrcode"))) {
                        heOrderUserSnapshotEntity.setQrCode(String.valueOf(data.get("invite_qrcode")));
                    }
                    continue;
                }


            } else if (formtemplateType.equals(5)) {
                //V1版本
                //基本信息
                if (content.getKey_name().equals("order_client")) {
                    List<OrderTaskJsonDTO.Components> components = content.getComponents();
                    if (ObjectUtil.isNotEmpty(components)) {
                        for (OrderTaskJsonDTO.Components component : components) {
                            if (component.getKey_name().equals("name")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    heOrderUserSnapshotEntity.setName(component.getValue());
                                    continue;
                                }
                            }
                            if (component.getKey_name().equals("age")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    try {
                                        heOrderUserSnapshotEntity.setAge(Integer.valueOf(component.getValue()));
                                    } catch (Exception e) {
                                        heOrderUserSnapshotEntity.setAge(0);
                                    }
                                    continue;
                                }
                            }
                            if (component.getKey_name().equals("phone")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    heOrderUserSnapshotEntity.setPhone(component.getValue());
                                    continue;
                                }
                            }
                            if (component.getKey_name().equals("blood_type")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    heOrderUserSnapshotEntity.setBloodType(Integer.valueOf(component.getValue()));
                                    continue;
                                }
                            }
                            if (component.getKey_name().equals("profession")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    heOrderUserSnapshotEntity.setProfession(component.getValue());
                                    continue;
                                }
                            }
                            if (component.getKey_name().equals("constellation_type")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    if (Integer.valueOf(component.getValue()) == -1) {
                                        component.setValue(String.valueOf(0));
                                    }
                                    heOrderUserSnapshotEntity.setConstellationType(Integer.valueOf(component.getValue()));
                                    heOrderUserSnapshotEntity.setConstellation(ConstellationTypeEnum.getValueByCode(Integer.valueOf(component.getValue())));
                                    continue;
                                }
                            }
                            if (component.getKey_name().equals("from_type")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    heOrderUserSnapshotEntity.setFromType(Integer.valueOf(component.getValue()));
                                    continue;
                                }
                            }
                            if (component.getKey_name().equals("emergency_contact")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    heOrderUserSnapshotEntity.setUrgentName(component.getValue());
                                    continue;
                                }
                            }
                            if (component.getKey_name().equals("emergency_contact_phone")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    heOrderUserSnapshotEntity.setUrgentPhone(component.getValue());
                                    continue;
                                }
                            }
                            if (component.getKey_name().equals("relation_with_client")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    heOrderUserSnapshotEntity.setRelationWithClient(Integer.valueOf(component.getValue()));
                                }
                                continue;
                            }
                            if (component.getKey_name().equals("predict_born_date")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                                    heOrderUserSnapshotEntity.setPredictBornDate(sdf.parse(component.getValue()));
                                }
                                continue;
                            }
                            if (component.getKey_name().equals("hospital")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    heOrderUserSnapshotEntity.setHospital(component.getValue());
                                    continue;
                                }
                            }
                            if (component.getKey_name().equals("home_address")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    heOrderUserSnapshotEntity.setAddress(component.getValue());
                                    continue;
                                }
                            }
                            if (component.getKey_name().equals("gestation_week_now")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    String gestationWeekNow = extractNumberBeforeNonDigit(component.getValue());
                                    if (ObjectUtil.isEmpty(gestationWeekNow)) {
                                        heOrderUserSnapshotEntity.setGestationWeekNow(0);
                                    } else {
                                        heOrderUserSnapshotEntity.setGestationWeekNow(Integer.valueOf(gestationWeekNow));
                                    }
                                    //v1v2v3迁移特殊逻辑, 根据孕周判断是否备孕中
                                    if (heOrderUserSnapshotEntity.getGestationWeekNow().equals(0)) {
                                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                                        heOrderUserSnapshotEntity.setPredictBornDate(sdf.parse("2099-12-01"));
                                    }
                                    continue;
                                }
                            }
                            if (component.getKey_name().equals("want_in")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                                    heOrderUserSnapshotEntity.setWantIn(sdf.parse(component.getValue()));
                                }
                                continue;
                            }


                            if (component.getKey_name().equals("id_card_no")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    heOrderUserSnapshotEntity.setIdCard(component.getValue());
                                    continue;
                                }
                            }
                            if (component.getKey_name().equals("id_card_front")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    heOrderUserSnapshotEntity.setIdCardFront(component.getValue());
                                    continue;
                                }
                            }
                            if (component.getKey_name().equals("id_card_back")) {
                                if (ObjectUtil.isNotEmpty(component.getValue())) {
                                    heOrderUserSnapshotEntity.setIdCardBack(component.getValue());
                                    continue;
                                }
                            }
                        }
                    }
                    continue;
                }
                //推荐码
                if (content.getKey_name().equals("invite_info")) {
                    Map<String, Object> data = content.getData();
                    if (ObjectUtil.isNotEmpty(data.get("invite_qrcode"))) {
                        heOrderUserSnapshotEntity.setQrCode(String.valueOf(data.get("invite_qrcode")));
                    }
                    continue;
                }
            }

        }
        return heOrderUserSnapshotEntity;
    }

    private HeOrderGoodsEntity getHeOrderGoodsEntity(Integer orderId, Integer basicUid) {
        HeOrderGoodsEntity heOrderGoodsEntity = orderGoodsRepository.getByOrderId(orderId);
        Integer goodsId = heOrderGoodsEntity.getGoodsId();
        GoodsEntity goodsEntity = goodsRepository.selectById(goodsId);
        GoodsSkuEntity goodsSkuEntity = goodsSkuRepository.selectByGoodsId(goodsId);
        //获取房型数据
        RoomByGoodsIdVo oldRoomInfoById = goodsManager.getRoomInfoByGoodsId(goodsId);
        if (ObjectUtil.isNotEmpty(goodsEntity)) {
            //父级商品ID
            heOrderGoodsEntity.setParentId(goodsEntity.getParentId());
            //服务天数
            heOrderGoodsEntity.setServiceDays(goodsEntity.getServiceDays());
            //ecp房型配置
            heOrderGoodsEntity.setEcpRoomType(goodsEntity.getEcpRoomType());
        }
        if (ObjectUtil.isNotEmpty(goodsSkuEntity)) {
            //skuId
            heOrderGoodsEntity.setSkuId(goodsSkuEntity.getId());
            heOrderGoodsEntity.setSkuName(goodsSkuEntity.getSkuName());
        }
        if (ObjectUtil.isNotEmpty(oldRoomInfoById)) {
            //房型Id
            heOrderGoodsEntity.setRoomId(oldRoomInfoById.getId());
            //房型名称
            heOrderGoodsEntity.setRoomName(oldRoomInfoById.getRoomName());
        }
        //客户basicUid
        heOrderGoodsEntity.setBasicUid(basicUid);
        log.info("老订单清洗-订单商品数据={}", JSONUtil.parse(heOrderGoodsEntity));
        return heOrderGoodsEntity;
    }

    private HeOrderSyncLogEntity getHeOrderSyncLogEntity(Integer orderId, HeOrderEntity oldOrderEntity, HeOrderGoodsEntity heOrderGoodsEntity, HeOrderUserSnapshotEntity heOrderUserSnapshotEntity, List<HeOrderAdditionalRevenueEntity> additionalRevenueEntityList, List<HeIncomeRecordEntity> recordEntityList, List<HeOrderRefundEntity> orderRefundEntityList) {
        HeOrderSyncLogEntity heOrderSyncLogEntity;
        HeOrderSyncLogEntity heOrderSyncLog = heOrderSyncLogRepository.getOneByOrderId(orderId);
        HeOrderSyncLogBodyEntity heOrderSyncLogBodyEntity = new HeOrderSyncLogBodyEntity();
        if (ObjectUtil.isEmpty(heOrderSyncLog)) {
            heOrderSyncLogEntity = new HeOrderSyncLogEntity();
        } else {
            heOrderSyncLogEntity = heOrderSyncLog;
        }
        heOrderSyncLogBodyEntity.setHeOrderEntity(oldOrderEntity);
        heOrderSyncLogBodyEntity.setHeOrderGoodsEntity(heOrderGoodsEntity);
        heOrderSyncLogBodyEntity.setHeOrderUserSnapshotEntity(heOrderUserSnapshotEntity);
        heOrderSyncLogBodyEntity.setAdditionalRevenueEntityList(additionalRevenueEntityList);
        heOrderSyncLogBodyEntity.setRecordEntityList(recordEntityList);
        heOrderSyncLogBodyEntity.setOrderRefundEntityList(orderRefundEntityList);
        heOrderSyncLogEntity.setStoreId(oldOrderEntity.getStoreId());
        heOrderSyncLogEntity.setOrderId(orderId);
        heOrderSyncLogEntity.setSuccess(0);
        heOrderSyncLogEntity.setBodyLog(JSONUtil.toJsonStr(heOrderSyncLogBodyEntity));
        return heOrderSyncLogEntity;
    }

    /**
     * 老订单数据清洗新订单-门店ID
     *
     * @param storeId
     * @return
     */
    @Override
    public String oldOrderSyncByStoreId(Integer storeId) {
        //获取门店的老订单
        List<HeOrderEntity> heOrderEntityList = orderRepository.getOldOrderByStoreIdAndType(storeId, OmniOrderTypeEnum.MONTH_ORDER.code());
        if (ObjectUtil.isEmpty(heOrderEntityList)) {
            return "该门店无老月子订单,storeId=" + storeId;
        }
        for (HeOrderEntity heOrderEntity : heOrderEntityList) {
            try {
                oldOrderSyncByOrderId(heOrderEntity.getOrderId());
            } catch (Exception e) {
                saveOrUpdateErrorOrderSyncLog(heOrderEntity, e);
            }
        }
        return "门店老月子订单清洗完成,门店ID" + storeId;
    }

    private void saveOrUpdateErrorOrderSyncLog(HeOrderEntity heOrderEntity, Exception e) {
        //清洗失败要记录日志,方便查找问题
        HeOrderSyncLogEntity heOrderSyncLogEntity;
        HeOrderSyncLogEntity heOrderSyncLog = heOrderSyncLogRepository.getOneByOrderId(heOrderEntity.getOrderId());
        if (ObjectUtil.isEmpty(heOrderSyncLog)) {
            heOrderSyncLogEntity = new HeOrderSyncLogEntity();
        } else {
            heOrderSyncLogEntity = heOrderSyncLog;
        }
        heOrderSyncLogEntity.setStoreId(heOrderEntity.getStoreId());
        heOrderSyncLogEntity.setOrderId(heOrderEntity.getOrderId());
        heOrderSyncLogEntity.setSuccess(1);
        heOrderSyncLogEntity.setBodyLog(JSONUtil.toJsonStr(e));
        heOrderSyncLogRepository.saveOrUpdateOne(heOrderSyncLogEntity);
    }

    /**
     * 老订单数据清洗新订单-定时任务
     *
     * @return
     */
    @Override
    public String oldOrderSyncByScheduler() {
        HeOrderEntity heOrderEntity = orderRepository.getOldOrderByType(OmniOrderTypeEnum.MONTH_ORDER.code());
        if (ObjectUtil.isEmpty(heOrderEntity)) {
            return "没有未清洗的老订单";
        }
        String s;
        try {
            s = oldOrderSyncByOrderId(heOrderEntity.getOrderId());
        } catch (Exception e) {
            saveOrUpdateErrorOrderSyncLog(heOrderEntity, e);
            return "定时任务清洗失败,orderId=" + heOrderEntity.getOrderId();
        }
        return s;
    }

    /**
     * 订单支付记录转移
     * 支付记录
     * 退款记录(有退款审批记录也要转移)
     * 线下支付凭证记录
     * 订单主体-支付状态,退款状态,累计金额,已付金额等
     * 押金转移(兼容)
     * 新订单首次达到50%推送ecp订单和房态
     * 保存log
     *
     * @param request
     */
    @Override
    public Result<Integer> incomeRecordTransfer(IncomeRecordTransferRequest request) {
        log.info("订单支付转移参数={}", JSONUtil.toJsonStr(request));
        //按业务流程这里只考虑支付记录是同一个订单的情况
        List<Integer> allIncomeIdList = request.getIncomeIdList();
        String orderSnNew = request.getOrderSnNew();
        Integer isNotice = request.getIsNotice();
        List<HeIncomeRecordEntity> heIncomeRecordEntityList = incomeRecordRepository.getRecordListById(allIncomeIdList);
        //正常支付记录
        List<HeIncomeRecordEntity> recordListById = heIncomeRecordEntityList.stream().filter(x -> ObjectUtil.notEqual(x.getReceiptType(), IncomeReceiptTypeEnum.RECORD_RECEIPT_TYPE_DEPOSIT.code())).collect(Collectors.toList());
        List<Integer> incomeIdList = recordListById.stream().map(HeIncomeRecordEntity::getId).distinct().collect(Collectors.toList());
        //押金支付记录
        List<HeIncomeRecordEntity> depositRecordListById = heIncomeRecordEntityList.stream().filter(x -> ObjectUtil.equals(x.getReceiptType(), IncomeReceiptTypeEnum.RECORD_RECEIPT_TYPE_DEPOSIT.code())).collect(Collectors.toList());
        List<Integer> depositRecordIdList = depositRecordListById.stream().map(HeIncomeRecordEntity::getId).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(heIncomeRecordEntityList)) {
            log.info("支付记录不存在,支付记录ID={}", JSONUtil.parse(allIncomeIdList));
            return Result.failed("支付记录不存在,支付记录ID=" + JSONUtil.parse(allIncomeIdList));
        }
        List<HeOrderEntity> byOrderSnList = orderRepository.getByOrderSnList(Collections.singletonList(orderSnNew));
        if (ObjectUtil.isEmpty(byOrderSnList)) {
            log.info("新订单不存在,订单号={}", orderSnNew);
            return Result.failed("新订单不存在,订单号=" + orderSnNew);
        }

        // 支付记录按 payTime排序，取最小的，做来最早支付时间
        recordListById.sort(Comparator.comparing(HeIncomeRecordEntity::getPayTime));

        final Integer oldOrderId = recordListById.get(0).getOrderId();
        HeOrderEntity oldOrder = orderRepository.getByOrderId(oldOrderId);
        final Integer oldOrderPercentFirstTime1 = oldOrder.getPercentFirstTime();
        final Integer oldOrderPayFirstTime1 = recordListById.get(0).getPayTime();
        Integer oldOldOrNew = oldOrder.getOldOrNew();
        HeOrderEntity newOrder = byOrderSnList.get(0);
        Integer newOrderPercentFirstTime1 = newOrder.getPercentFirstTime();
        Integer newOrderOldOrNew = newOrder.getOldOrNew();
        final Integer newOrderId = newOrder.getOrderId();

        // 如果老订单有业绩，使用老订单的，如果没有，使用当前时间。

        if (oldOrderId.equals(newOrderId)) {
            log.info("新老订单相同");
            return Result.failed("新老订单相同");
        }
        if (ObjectUtil.equals(oldOrder.getOldOrNew(), 1) && (ObjectUtil.isEmpty(newOrder.getOldOrNew()) || ObjectUtil.equals(newOrder.getOldOrNew(), 0))) {
            log.info("新订单不能转移到老订单");
            return Result.failed("新订单不能转移到老订单");
        }
        if (ObjectUtil.isNotEmpty(recordListById)) {
            //支付记录转移
            transferIncomeRecord(recordListById, newOrder, newOrderId);

            //线下支付凭证转移
            transferIncomeProof(incomeIdList, newOrderId);
            //退款记录转移
            int refundAmount = 0;
            refundAmount = transferRefundRecord(incomeIdList, oldOrderId, newOrderId, refundAmount);
            Integer sumIncome = recordListById.stream().filter(x -> ObjectUtil.equals(x.getStatus(), PayStatusEnum.PAY_STATUS_SUCCESS.getCode())).mapToInt(HeIncomeRecordEntity::getIncome).sum();
            //老订单处理
            transferOldOrder(oldOrder, refundAmount, sumIncome);
            final Integer oldOrderPercentFirstTime2 = oldOrder.getPercentFirstTime();
            //新订单处理
            boolean needSyncCare = transferNewOrder(isNotice, newOrder, refundAmount, sumIncome, oldOrderPercentFirstTime1, oldOrderPayFirstTime1);
            Integer newOrderPercentFirstTime2 = newOrder.getPercentFirstTime();
            //同步退款状态
            syncOrderRefundStatus(oldOrderId);
            syncOrderRefundStatus(newOrderId);
            if (needSyncCare && (newOrder.getOrderType().equals(OmniOrderTypeEnum.MONTH_ORDER.code()) || newOrder.getOrderType().equals(OmniOrderTypeEnum.SMALL_MONTH_ORDER.code()))) {
                //同步ecp订单
                ecpOrderSyncAssembler.syncEcpOrder(newOrderId);
                // 创建房态
                careManager.autoCreateFangTai(newOrderId);
            }
            //推送SCRM(无论修改前后只要存在业绩时间,就需要推送scrm)
            if ((ObjectUtil.isNotEmpty(oldOrderPercentFirstTime1) && oldOrderPercentFirstTime1 != 0) || (ObjectUtil.isNotEmpty(oldOrderPercentFirstTime2) && oldOrderPercentFirstTime2 != 0)) {
                scrmManager.pushScrmCustomerOrder(oldOrderId);
            }
            if ((ObjectUtil.isNotEmpty(newOrderPercentFirstTime1) && newOrderPercentFirstTime1 != 0) || (ObjectUtil.isNotEmpty(newOrderPercentFirstTime2) && newOrderPercentFirstTime2 != 0)) {
                scrmManager.pushScrmCustomerOrder(newOrderId);
            }
        }
        if (ObjectUtil.equals(oldOldOrNew, 1) && ObjectUtil.equals(newOrderOldOrNew, 0)) {
            log.info("新转老押金不支持转移,原订单号={},新订单号={}", oldOrderId, newOrderId);
        } else {
            //转移押金
            syncDepositRecord(depositRecordListById, depositRecordIdList, newOrder);
        }

        //保存log
        orderTransferProducer.sendMq(allIncomeIdList.get(0));
        saveTransferLog(request, oldOrderId, newOrderId);
        log.info("订单支付转移成功={}", JSONUtil.toJsonStr(request));
        return Result.success(allIncomeIdList.size());
    }

    @Override
    public Result<Integer> allocationPayment(String incomeRecordId) {
        HeIncomeRecordEntity incomeRecordEntity = incomeRecordRepository.getRecordByIncomeSn(incomeRecordId);
        HeOrderEntity orderEntity = orderRepository.getByOrderId(incomeRecordEntity.getOrderId());
        List<IncomePaidAllocationEntity> incomePaidAllocationEntities = allocationRepository.queryListByIncomeIds(Collections.singletonList(incomeRecordEntity.getId()));
        //先把旧的分摊记录失效掉
        if (CollectionUtils.isNotEmpty(incomePaidAllocationEntities)) {
            allocationRepository.deleteByIncomeIds(Collections.singletonList(incomeRecordEntity.getId()));
        }

        FlowContext flowContext = new FlowContext();
        flowContext.setAttribute(HeIncomeRecordEntity.class, incomeRecordEntity);
        flowContext.setAttribute(HeOrderEntity.class, orderEntity);
        allocationPaymentProcessor.run(flowContext);

        return null;
    }


    private Result<Integer> fixAllocationPayment(String incomeRecordId) {
        HeIncomeRecordEntity incomeRecordEntity = incomeRecordRepository.getRecordByIncomeSn(incomeRecordId);
        HeOrderEntity orderEntity = orderRepository.getByOrderId(incomeRecordEntity.getOrderId());
        List<IncomePaidAllocationEntity> incomePaidAllocationEntities = allocationRepository.queryListByIncomeIds(Collections.singletonList(incomeRecordEntity.getId()));
        //先把旧的分摊记录失效掉
        if (CollectionUtils.isNotEmpty(incomePaidAllocationEntities)) {
            allocationRepository.deleteByIncomeIds(Collections.singletonList(incomeRecordEntity.getId()));
        }

        FlowContext flowContext = new FlowContext();
        flowContext.setAttribute(HeIncomeRecordEntity.class, incomeRecordEntity);
        flowContext.setAttribute(HeOrderEntity.class, orderEntity);
        allocationPaymentFixProcessor.run(flowContext);

        return null;
    }

    @Override
    public Result<Integer> sendAllocationMq(Integer incomeId) {
        //保存log
        orderTransferProducer.sendMq(incomeId);
        return Result.success();
    }

    @Override
    public Result<?> fixAllocationPayment(List<Integer> orderIdList, Boolean fixAll) {
        List<HeOrderEntity> byOrderIdList = orderRepository.getByOrderIdList(orderIdList);
        fixOrderEntitylist(fixAll, byOrderIdList);


        return null;
    }

    private void fixOrderEntitylist(Boolean fixAll, List<HeOrderEntity> byOrderIdList) {
        for (HeOrderEntity orderEntity : byOrderIdList) {
            log.info("修复商品分摊，订单号={}", orderEntity.getOrderSn());
            FlowContext flowContext = new FlowContext();
            flowContext.setAttribute(HeOrderEntity.class, orderEntity);
            fixGoodsAllocateProcessor.run(flowContext);
            if (Boolean.TRUE.equals(fixAll)) {
                List<HeIncomeRecordEntity> heIncomeRecordEntities = incomeDomainService.queryEffectiveRecord(orderEntity);
                log.info("订单号={},分摊记录={}", orderEntity.getOrderId(), JSONUtil.toJsonStr(heIncomeRecordEntities));
                List<HeIncomeRecordEntity> reFixIncomeRecordEntities = new ArrayList<>();
                for (HeIncomeRecordEntity heIncomeRecordEntity : heIncomeRecordEntities) {
                    boolean equals = heIncomeRecordEntity.getIncome().equals(heIncomeRecordEntity.getAlreadyRefundAmount());
                    if (equals) {
                        continue;
                    }
                    if (heIncomeRecordEntity.getStatus() == 1 && heIncomeRecordEntity.isActualSuccess()) {
                        reFixIncomeRecordEntities.add(heIncomeRecordEntity);
                    }
                }
                if (CollectionUtils.isNotEmpty(reFixIncomeRecordEntities)) {
                    List<Integer> incomeRecordIdlist = reFixIncomeRecordEntities.stream().map(HeIncomeRecordEntity::getId).collect(Collectors.toList());
                    //删除旧的分摊记录
                    List<IncomePaidAllocationEntity> incomePaidAllocationEntities = allocationRepository.queryListByIncomeIds(incomeRecordIdlist);
                    //先把旧的分摊记录失效掉
                    if (CollectionUtils.isNotEmpty(incomePaidAllocationEntities)) {
                        allocationRepository.deleteByIncomeIds(incomeRecordIdlist);
                    }

                }
                for (HeIncomeRecordEntity reFixIncomeRecordEntity : reFixIncomeRecordEntities) {
                    fixAllocationPayment(reFixIncomeRecordEntity.getIncomeSn());
                }
            }
        }
    }

    @Override
    public Result<?> fixGoodsAllocation() {
//        List<HeOrderEntity> fixOrderIdList = orderRepository.getFixOrderIdList();
//        fixOrderIdList = fixOrderIdList.stream().filter(o -> o.isNewOrder()).collect(Collectors.toList());
//        log.info("fixOrderIdList size ={}", fixOrderIdList.size());
//        if (CollectionUtils.isNotEmpty(fixOrderIdList)) {
//            for (HeOrderEntity heOrderEntity : fixOrderIdList) {
//                fixAllocationPayment(heOrderEntity.getOrderId(), false);
//            }
//        }
        return null;
    }

    @Override
    public Result<?> fixAllocationPayment2(List<String> orderSnList, Boolean fixAll) {
        List<HeOrderEntity> byOrderSnList = orderRepository.getByOrderSnList(orderSnList);
        fixOrderEntitylist(fixAll, byOrderSnList);
        return null;
    }

    @Override
    public Result<?> fixAllocationPayment3(List<String> orderSnList, Boolean fixAll) {
        List<HeOrderEntity> byOrderSnList = orderRepository.getByOrderSnList(orderSnList);

        List<Integer> orderIdList = byOrderSnList.stream().map(HeOrderEntity::getOrderId).collect(Collectors.toList());
        //先把单价处理掉
        List<HeOrderGoodsEntity> byOrderIdList = orderGoodsRepository.getByOrderIdList(orderIdList);
        byOrderIdList.forEach(x -> {
            if (ObjectUtil.isNotEmpty(x.getGoodsPriceOrgin()) && x.getGoodsNum() > 0) {
                x.setGoodsPriceOrgin(x.getGoodsPriceOrgin() / x.getGoodsNum());
            }
        });
        orderGoodsRepository.batchUpdate(byOrderIdList);
        fixOrderEntitylist(fixAll, byOrderSnList);
        return null;
    }


    private void saveTransferLog(IncomeRecordTransferRequest request, Integer oldOrderId, Integer newOrderId) {
        HeOrderIncomeRecordTransferLogEntity entity = new HeOrderIncomeRecordTransferLogEntity();
        entity.setOldOrderId(oldOrderId);
        entity.setNewOrderId(newOrderId);
        entity.setBodyLog(JSONUtil.toJsonStr(request));
        heOrderIncomeRecordTransferLogRepository.saveOne(entity);
    }

    private boolean transferNewOrder(Integer isNotice, HeOrderEntity newOrder, int refundAmount, Integer sumIncome, Integer oldOrderPercentFirstTime, Integer oldOrderPayFirstTime) {
        Integer newPaidAmount = newOrder.getPaidAmount() + sumIncome;
        Integer newRealAmount = newOrder.getRealAmount() + sumIncome - refundAmount;
        newOrder.setPaidAmount(newPaidAmount);
        newOrder.setRealAmount(newRealAmount);
        //支付状态
        if (newPaidAmount <= 0) {
            newOrder.setPayStatus(PayStatusV2Enum.WAIT_PAY.getCode());
        } else {
            if (newPaidAmount < newOrder.getPayAmount()) {
                newOrder.setPayStatus(PayStatusV2Enum.NO_PAY_OFF.getCode());
            } else if (newPaidAmount.equals(newOrder.getPayAmount())) {
                newOrder.setPayStatus(PayStatusV2Enum.PAY_OFF.getCode());
            } else {
                newOrder.setPayStatus(PayStatusV2Enum.EXCESS_PAY.getCode());
            }
        }
        //是否需要报单
        if (isNotice == 1) {
            newOrder.setIsNotice(OrderNoticeEnum.NOTICE_NO.code());
        } else if (isNotice == 0) {
            newOrder.setIsNotice(OrderNoticeEnum.NOTICE_YES.code());
        }
        boolean needSyncCare = false;
        //业绩生效时间
        if ((Objects.isNull(newOrder.getPercentFirstTime()) || newOrder.getPercentFirstTime() == 0) && newOrder.getPaidAmount() * 10 >= (newOrder.getPayAmount() * 5)) {
            Integer newPercentFirstTime = oldOrderPercentFirstTime;
            if (ObjectUtil.isNull(oldOrderPercentFirstTime) || oldOrderPercentFirstTime == 0) {
                newPercentFirstTime = Math.toIntExact(System.currentTimeMillis() / 1000);
            }
            newOrder.setPercentFirstTime(newPercentFirstTime);
            needSyncCare = true;
        }

        int payFirstTime = 0;
        //设置首次支付时间如果不存在,设为当前操作时间,存在则不动
        if (newOrder.getPayFirstTime() == 0 || oldOrderPayFirstTime == 0) {
            //有一个为0，选项有值的
            payFirstTime = Math.max(newOrder.getPayFirstTime(), oldOrderPayFirstTime);
        } else {
            // 都不为0，取最小值
            payFirstTime = Math.min(newOrder.getPayFirstTime(), oldOrderPayFirstTime);
        }
        if (payFirstTime == 0) {
            payFirstTime = Math.toIntExact(System.currentTimeMillis() / 1000);
        }
        newOrder.setPayFirstTime(payFirstTime);

        orderRepository.updateOrderMonthByOrderId(newOrder);
        return needSyncCare;
    }

    private void transferOldOrder(HeOrderEntity oldOrder, int refundAmount, Integer sumIncome) {
        Integer oldPaidAmount = oldOrder.getPaidAmount() - sumIncome;
        Integer oldRealAmount = 0;
        //如果是老订单则不操作realAmount
        Integer oldOrNew = oldOrder.getOldOrNew();
        boolean isNotOldOrder = ObjectUtil.isNotEmpty(oldOrNew) && (oldOrNew.equals(1) || oldOrNew.equals(3));
        if (isNotOldOrder) {
            oldRealAmount = oldOrder.getRealAmount() - sumIncome + refundAmount;
            oldOrder.setRealAmount(oldRealAmount);
        }
        oldOrder.setPaidAmount(oldPaidAmount);
        //支付状态
        if (oldPaidAmount <= 0) {
            oldOrder.setPayStatus(PayStatusV2Enum.WAIT_PAY.getCode());
        } else {
            if (oldPaidAmount < oldOrder.getPayAmount()) {
                oldOrder.setPayStatus(PayStatusV2Enum.NO_PAY_OFF.getCode());
            } else if (oldPaidAmount.equals(oldOrder.getPayAmount())) {
                oldOrder.setPayStatus(PayStatusV2Enum.PAY_OFF.getCode());
            } else {
                oldOrder.setPayStatus(PayStatusV2Enum.EXCESS_PAY.getCode());
            }
        }
        //业绩生效时间
        if (Objects.isNull(oldOrder.getPaidAmount()) || oldOrder.getPaidAmount() * 10 < (oldOrder.getPayAmount() * 5)) {
            oldOrder.setPercentFirstTime(0);
        }
        //老订单如果oldPaidAmount<=0需要将首次支付时间改为0,其余不管
        if (oldPaidAmount <= 0) {
            oldOrder.setPayFirstTime(0);
        }
        //如果原订单实际已付金额为0,则发送订单变更事件
        //原订单是老订单,取oldPaidAmount,原订单是新订单取oldRealAmount
        if (isNotOldOrder) {
            if (oldRealAmount <= 0) {
                orderEventProducer.sendMq(OrderEventEnum.TRANSFER_PAYMENT.getCode(), oldOrder.getOrderSn(), null);
            }
        } else {
            if (oldPaidAmount <= 0) {
                orderEventProducer.sendMq(OrderEventEnum.TRANSFER_PAYMENT.getCode(), oldOrder.getOrderSn(), null);
            }
        }
        orderRepository.updateOrderMonthByOrderId(oldOrder);
    }

    private int transferRefundRecord(List<Integer> incomeIdList, Integer oldOrderId, Integer newOrderId, int refundAmount) {
        List<HeOrderRefundEntity> refundByOrderIncomeIdList = orderRefundRepository.getRefundByOrderIncomeIdList(incomeIdList);
        List<Old2NewOrderDTO> processIdList = getOld2NewOrderDTOS(refundByOrderIncomeIdList);
        if (ObjectUtil.isNotEmpty(processIdList)) {
            log.info("需要修改的审批记录={},订单ID={}", JSONUtil.toJsonStr(processIdList), oldOrderId);
            baseManager.old2New(processIdList, oldOrderId);
        }
        //因为新订单,没有存流程id,所以需要转移
        if (ObjectUtil.isNotEmpty(refundByOrderIncomeIdList)) {
            refundByOrderIncomeIdList.forEach(x -> {
                //如果没有支付流水ID需要设置下
                if (ObjectUtil.isEmpty(x.getIncomeSn())) {
                    HeIncomeRecordEntity oneById = incomeRecordRepository.getOneById(x.getOrderGoodId());
                    if (ObjectUtil.isNotEmpty(oneById)) {
                        x.setIncomeSn(oneById.getIncomeSn());
                    }
                }
                x.setOrderId(newOrderId);
                orderRefundRepository.updateOneById(x);
                //修转移退款审批
                oaProcessIdRelationService.transferOrder(oldOrderId, newOrderId, x.getId());

            });
            //退款成功的金额
            refundAmount = refundByOrderIncomeIdList.stream().filter(x -> x.getStatus().equals(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode())).mapToInt(HeOrderRefundEntity::getActualAmount).sum();
        }
        return refundAmount;
    }


    private List<Old2NewOrderDTO> getOld2NewOrderDTOS(List<HeOrderRefundEntity> refundByOrderIncomeIdList) {
        List<Old2NewOrderDTO> processIdList = new ArrayList<>();
        if (ObjectUtil.isEmpty(refundByOrderIncomeIdList)) {
            return processIdList;
        }
        List<Long> refundProject = refundByOrderIncomeIdList.stream().map(x -> x.getProjectId()).distinct().collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(refundProject)) {
            List<Old2NewOrderDTO> taskProcessByProjectId = heTaskRepository.getTaskProcessByProjectId(refundProject);
            taskProcessByProjectId.forEach(x -> {
                List<HeOrderRefundEntity> heOrderRefundEntities = refundByOrderIncomeIdList.stream().filter(y -> y.getProjectId().equals(x.getProjectId())).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(heOrderRefundEntities)) {
                    x.setOrderRefundId(heOrderRefundEntities.get(0).getId());
                    x.setId(heOrderRefundEntities.get(0).getId());
                }
            });
            processIdList.addAll(taskProcessByProjectId);
        }
        return processIdList;
    }

    private void transferIncomeProof(List<Integer> incomeIdList, Integer newOrderId) {
        List<HeIncomeProofRecordEntity> incomeProofRecordByIncomeIdList = incomeProofRecordRepository.getIncomeProofRecordByIncomeIdList(incomeIdList);
        if (ObjectUtil.isNotEmpty(incomeProofRecordByIncomeIdList)) {
            incomeProofRecordByIncomeIdList.forEach(x -> {
                //如果之前没有流水号,需要设置下
                if (ObjectUtil.isEmpty(x.getIncomeSn())) {
                    HeIncomeRecordEntity oneById = incomeRecordRepository.getOneById(x.getIncomeId());
                    if (ObjectUtil.isNotEmpty(oneById)) {
                        x.setIncomeSn(oneById.getIncomeSn());
                    }
                }
                x.setOrderId(newOrderId);
                incomeProofRecordRepository.updateOne(x);
            });
        }
    }

    private void transferIncomeRecord(List<HeIncomeRecordEntity> recordListById, HeOrderEntity newOrder, Integer newOrderId) {
        recordListById.forEach(x -> {
            //如果没有支付流水号,需要设置
            if (ObjectUtil.isEmpty(x.getIncomeSn())) {
                //生成本地交易流水号
                String localTransactionalNo = IdGenUtils.createLocalTransactionalNo(null, new Date());
                x.setIncomeSn(localTransactionalNo);
            }
            x.setOrderId(newOrderId);
            x.setStoreId(newOrder.getStoreId());
            x.setClientUid(newOrder.getClientUid());
            x.setUpdatedAt(System.currentTimeMillis() / 1000);
            incomeRecordRepository.updateRecord(x);

            if (newOrder.isNewOrder()) {
                allocationPayment(x.getIncomeSn());
            }

        });
    }

    private void syncDepositRecord(List<HeIncomeRecordEntity> depositRecordListById, List<Integer> depositRecordIdList, HeOrderEntity newOrder) {
        List<HeOrderRefundEntity> depositRefundByOrderIncomeIdList = orderRefundRepository.getRefundByOrderIncomeIdList(depositRecordIdList);
        //押金支付记录转移
        if (ObjectUtil.isNotEmpty(depositRecordListById)) {
            depositRecordListById.forEach(x -> {
                //如果没有支付流水号,需要设置
                if (ObjectUtil.isEmpty(x.getIncomeSn())) {
                    //生成本地交易流水号
                    String localTransactionalNo = IdGenUtils.createLocalTransactionalNo(null, new Date());
                    x.setIncomeSn(localTransactionalNo);
                }
                x.setOrderIdBak(x.getOrderId());
                x.setOrderId(null);
                x.setStoreId(newOrder.getStoreId());
                x.setClientUid(newOrder.getClientUid());

                //设置冻结金额和已退金额
                setFreezeAmountAndAlreadyAmount(depositRecordListById, depositRefundByOrderIncomeIdList, x);

                //押金要设置orderId为 NULL
                incomeRecordRepository.updateSetOrderIdNullById(x.getId());
                incomeRecordRepository.updateRecord(x);
            });
        }
        //押金线下支付凭证转移
        List<HeIncomeProofRecordEntity> depositIncomeProofRecordByIncomeIdList = incomeProofRecordRepository.getIncomeProofRecordByIncomeIdList(depositRecordIdList);
        if (ObjectUtil.isNotEmpty(depositIncomeProofRecordByIncomeIdList)) {
            depositIncomeProofRecordByIncomeIdList.forEach(x -> {
                x.setOrderId(null);
                //如果没有支付流水ID需要设置下
                if (ObjectUtil.isEmpty(x.getIncomeSn())) {
                    HeIncomeRecordEntity oneById = incomeRecordRepository.getOneById(x.getIncomeId());
                    if (ObjectUtil.isNotEmpty(oneById)) {
                        x.setIncomeSn(oneById.getIncomeSn());
                    }
                }
                incomeProofRecordRepository.updateSetOrderIdNullById(x.getId());
                incomeProofRecordRepository.updateOne(x);
            });
        }
        //押金退款记录转移
        if (ObjectUtil.isNotEmpty(depositRefundByOrderIncomeIdList)) {
            depositRefundByOrderIncomeIdList.forEach(x -> {
                //如果没有支付流水ID需要设置下
                if (ObjectUtil.isEmpty(x.getIncomeSn())) {
                    HeIncomeRecordEntity oneById = incomeRecordRepository.getOneById(x.getOrderGoodId());
                    if (ObjectUtil.isNotEmpty(oneById)) {
                        x.setIncomeSn(oneById.getIncomeSn());
                    }
                }
                x.setOrderId(null);
                orderRefundRepository.updateSetOrderIdNullById(x.getId());
                orderRefundRepository.updateOneById(x);
            });
        }
    }

    private void setFreezeAmountAndAlreadyAmount(List<HeIncomeRecordEntity> depositRecordListById, List<HeOrderRefundEntity> depositRefundByOrderIncomeIdList, HeIncomeRecordEntity x) {
        if (ObjectUtil.isNotEmpty(depositRefundByOrderIncomeIdList)) {
            List<HeOrderRefundEntity> refundEntityByIncomeIdList = depositRefundByOrderIncomeIdList.stream().filter(y -> y.getOrderGoodId().equals(x.getId())).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(refundEntityByIncomeIdList)) {
                // 的需要设置冻结金额审核中或审批成功等待打款
                List<HeOrderRefundEntity> freezeAmountEntityList = refundEntityByIncomeIdList.stream().filter(z -> z.getStatus().equals(RefundRecordPayStatusEnum.REFUND_RECORD_1.getCode()) || z.getStatus().equals(RefundRecordPayStatusEnum.REFUND_RECORD_3.getCode())).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(freezeAmountEntityList)) {
                    int freezeAmount = freezeAmountEntityList.stream().mapToInt(s -> ObjectUtil.isNotEmpty(s.getApplyAmount()) ? s.getApplyAmount() : 0).sum();
                    x.setFreezeAmount(freezeAmount);
                } else {
                    x.setFreezeAmount(0);
                }
                //退款成功需要设置已退金额
                List<HeOrderRefundEntity> alreadyRefundAmountEntityList = refundEntityByIncomeIdList.stream().filter(z -> z.getStatus().equals(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode())).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(alreadyRefundAmountEntityList)) {
                    int alreadyRefundAmount = alreadyRefundAmountEntityList.stream().mapToInt(k -> ObjectUtil.isNotEmpty(k.getApplyAmount()) ? k.getApplyAmount() : 0).sum();
                    x.setAlreadyRefundAmount(alreadyRefundAmount);
                } else {
                    x.setAlreadyRefundAmount(0);
                }
            }
        } else {
            x.setFreezeAmount(0);
            x.setAlreadyRefundAmount(0);
        }
    }


    @Override
    public Result delAllOrderMonthGiftExtendCache(OrderGiftByStoreReq req) {
        orderMonthWxDomainService.delAllOrderMonthGiftExtendCache(req);
        return Result.success();
    }

    private OrderDiscountsCacheReq getOrderDiscountsCacheReq(OrderMonthReq req) {
        OrderDiscountsCacheReq orderDiscountsCacheReq = new OrderDiscountsCacheReq();
        orderDiscountsCacheReq.setOrderId(req.getOrderId());
        orderDiscountsCacheReq.setGoodsId(req.getOrderMonthGoodsReq().getGoodsId());
        orderDiscountsCacheReq.setOperator(req.getOperator());
        orderDiscountsCacheReq.setClientUid(req.getOrderMonthClientReq().getClientUid());
        orderDiscountsCacheReq.setStoreId(req.getOrderMonthClientReq().getStoreId());
        orderDiscountsCacheReq.setOrderType(OmniOrderTypeEnum.MONTH_ORDER.getCode());
        return orderDiscountsCacheReq;
    }

    private OrderCacheBaseReq getOrderCacheBaseReq(OrderMonthReq req) {
        OrderCacheBaseReq OrderCacheBaseReq = new OrderCacheBaseReq();
        OrderCacheBaseReq.setOrderId(req.getOrderId());
        OrderCacheBaseReq.setOperator(req.getOperator());
        OrderCacheBaseReq.setClientUid(req.getOrderMonthClientReq().getClientUid());
        OrderCacheBaseReq.setStoreId(req.getOrderMonthClientReq().getStoreId());
        OrderCacheBaseReq.setOrderType(OmniOrderTypeEnum.MONTH_ORDER.getCode());
        return OrderCacheBaseReq;
    }


    private OrderAddtionalRevenueCacheReq getOrderAddtionalRevenueCacheReq(OrderMonthReq req) {
        //月子订单客户对象")
        OrderMonthClientReq orderMonthClientReq = req.getOrderMonthClientReq();
        //套餐信息")
        OrderMonthGoodsReq orderMonthGoodsReq = req.getOrderMonthGoodsReq();
        OrderAddtionalRevenueCacheReq orderAddtionalRevenueCacheReq = new OrderAddtionalRevenueCacheReq();
        orderAddtionalRevenueCacheReq.setOrderId(req.getOrderId());
        orderAddtionalRevenueCacheReq.setGoodsId(orderMonthGoodsReq.getGoodsId());
        orderAddtionalRevenueCacheReq.setOperator(req.getOperator());
        orderAddtionalRevenueCacheReq.setClientUid(orderMonthClientReq.getClientUid());
        orderAddtionalRevenueCacheReq.setStoreId(orderMonthClientReq.getStoreId());
        orderAddtionalRevenueCacheReq.setOrderType(OmniOrderTypeEnum.MONTH_ORDER.getCode());
        return orderAddtionalRevenueCacheReq;
    }

    private OrderAdvanceOrderGiftReq getOrderAdvanceOrderGiftReq(OrderMonthReq req) {
        //月子订单客户对象")
        OrderMonthClientReq orderMonthClientReq = req.getOrderMonthClientReq();
        //套餐信息")
        OrderMonthGoodsReq orderMonthGoodsReq = req.getOrderMonthGoodsReq();
        OrderAdvanceOrderGiftReq orderMonthGiftExtendCacheReq = new OrderAdvanceOrderGiftReq();
        orderMonthGiftExtendCacheReq.setOrderId(req.getOrderId());
        orderMonthGiftExtendCacheReq.setGoodsId(orderMonthGoodsReq.getGoodsId());
        orderMonthGiftExtendCacheReq.setOperator(req.getOperator());
        orderMonthGiftExtendCacheReq.setClientUid(orderMonthClientReq.getClientUid());
        orderMonthGiftExtendCacheReq.setStoreId(orderMonthClientReq.getStoreId());
        orderMonthGiftExtendCacheReq.setOrderType(OmniOrderTypeEnum.MONTH_ORDER.getCode());
        return orderMonthGiftExtendCacheReq;
    }

    private OrderGoodsCacheReq getOrderGoodsCacheReq(OrderMonthReq req) {
        //月子订单客户对象")
        OrderMonthClientReq orderMonthClientReq = req.getOrderMonthClientReq();
        OrderMonthGoodsReq orderMonthGoodsReq = req.getOrderMonthGoodsReq();
        OrderGoodsCacheReq orderGoodsCacheReq = new OrderGoodsCacheReq();
        orderGoodsCacheReq.setOrderId(req.getOrderId());
        orderGoodsCacheReq.setGoodsId(orderMonthGoodsReq.getGoodsId());
        orderGoodsCacheReq.setSkuId(orderMonthGoodsReq.getSkuId());
        orderGoodsCacheReq.setOperator(req.getOperator());
        orderGoodsCacheReq.setClientUid(orderMonthClientReq.getClientUid());
        orderGoodsCacheReq.setStoreId(orderMonthClientReq.getStoreId());
        orderGoodsCacheReq.setOrderType(OmniOrderTypeEnum.MONTH_ORDER.getCode());
        return orderGoodsCacheReq;
    }

    /**
     * 订单转换为合同
     */
    private OrderParamHistoryPushDTO convertOrderId2OrderParamHistoryPushDTO(Boolean isCreate, OrderMonthReq req, Integer orderId, AsyncResultDTO<OrderMonthGoodsCacheVO, List<OrderGiftCacheByUserVO>, List<OrderAdditionalRevenueCacheVO>, OrderDiscountsCacheVO> resultDTO) {

        log.info("订单转换为合同resultDTO:{}", JSONUtil.toJsonStr(resultDTO));
        OrderInfoByOrderVO orderInfoByOrderVO;
        // "月子订单客户对象")
        OrderMonthClientVO orderMonthClientVO;
        // "订单加收项信息")
        List<OrderInfoByAdditionalRevenueVO> orderInfoByAdditionalRevenueVOList;
        // "套餐信息")
        OrderInfoByGoodsVO orderInfoByGoodsVO;
        //服务期限
        Integer serviceDays;
        //"套餐额外礼赠信息")
        List<OrderInfoByGiftExtendSkuVO> orderInfoByGiftExtendSkuVOList;
        // "订单其他信息信息")
        OrderInfoByOtherInfoVO orderInfoByOtherInfoVO;
        // "费用明细")
        OrderDiscountsCacheVO orderDiscountsCacheVO;
        //意向金
//        AtomicReference<BigDecimal> earnestMoney = new AtomicReference<>(BigDecimal.ZERO);
        //预付款
//        AtomicReference<BigDecimal> bargainMoney = new AtomicReference<>(BigDecimal.ZERO);
        //尾款
//        AtomicReference<BigDecimal> finalPayment = new AtomicReference<>(BigDecimal.ZERO);
        if (!isCreate) {
            orderInfoByOrderVO = monthOrderWxQueryService.getOrderInfoByOrderId(req.getOrderId());
            orderMonthClientVO = orderInfoByOrderVO.getOrderMonthClientVO();
            orderInfoByAdditionalRevenueVOList = orderInfoByOrderVO.getOrderInfoByAdditionalRevenueVOList();
            orderInfoByGoodsVO = orderInfoByOrderVO.getOrderInfoByGoodsVO();
            orderInfoByGiftExtendSkuVOList = orderInfoByOrderVO.getOrderInfoByGiftExtendVO().getOrderInfoByGiftExtendSkuVOList();
            orderInfoByOtherInfoVO = orderInfoByOrderVO.getOrderInfoByOtherInfoVO();
            orderDiscountsCacheVO = orderInfoByOrderVO.getOrderDiscountsCacheVO();


        } else {
            orderInfoByGoodsVO = orderConvert.orderMonthGoodsCacheVO2OrderInfoByGoodsVO(resultDTO.getResult1());
            orderMonthClientVO = orderConvert.orderMonthClientReq2OrderMonthClientVO(req.getOrderMonthClientReq());
            orderInfoByAdditionalRevenueVOList = orderConvert.cacheOrderInfoByAdditionalRevenueVOList(resultDTO.getResult3());
            orderInfoByGiftExtendSkuVOList = orderConvert.orderInfoByGiftExtendSkuVOList2OrderInfoByGiftExtendSkuVOList(resultDTO.getResult2());
            orderInfoByOtherInfoVO = orderConvert.orderMonthOtherReq2OrderInfoByOtherInfoVO(req.getOrderMonthOtherReq());
            OrderDiscountsCacheReq orderDiscountsCacheReq = new OrderDiscountsCacheReq();
            orderDiscountsCacheReq.setOrderType(OmniOrderTypeEnum.MONTH_ORDER.getCode());
            orderDiscountsCacheReq.setClientUid(orderMonthClientVO.getClientUid());
            orderDiscountsCacheReq.setStoreId(orderMonthClientVO.getStoreId());
            orderDiscountsCacheReq.setGoodsId(orderInfoByGoodsVO.getGoodsId());
            orderDiscountsCacheReq.setOperator(req.getOperator());
            orderDiscountsCacheVO = monthOrderWxQueryService.getOrderDiscounts(orderDiscountsCacheReq).getData();
        }
        //服务天数
        AtomicReference<Integer> atomicServiceDays = new AtomicReference<>(0);
        //节假日天数
        AtomicReference<Integer> atomicHolidayDays = new AtomicReference<>(0);
        // 服务对象人数(默认2个,宝妈+宝宝)
        AtomicReference<Integer> servicePeopleNum = new AtomicReference<>(2);
        orderInfoByAdditionalRevenueVOList.forEach(x -> {
            if (x.getType().equals(MonthAdditionalRevenueEnum.STAY_COST.getCode())) {
                atomicServiceDays.updateAndGet(v -> v + x.getDays());

            }
            //服务对象人数
            if (x.getType().equals(MonthAdditionalRevenueEnum.COST_MULTIPLE_BIRTHS.getCode())) {
                servicePeopleNum.set(x.getEmbryoNumber() + 1);
            }
            if (ObjectUtil.isNotNull(x.getIsFestival())) {
                if (x.getIsFestival().equals(1) && x.getType().equals(MonthAdditionalRevenueEnum.HOLIDAY.getCode())) {
                    atomicHolidayDays.set(x.getDays());
                }
            }
        });

//        Integer atomicDays = atomicServiceDays.get();
        serviceDays = atomicServiceDays.get() + orderInfoByGoodsVO.getServiceDays();
//        serviceDays = ObjectUtil.isNotEmpty(atomicDays) ? atomicDays : 0 + (ObjectUtil.isNotEmpty(serviceDays1) ? serviceDays1 : 0);

        // todo 主合同模板 意向金 预定金 押金 特殊费用 空着 不填充
        List<OrderParamHistoryValuePushDTO> paramList = new ArrayList<>();

        //甲方
        OrderParamHistoryValuePushDTO param1 = new OrderParamHistoryValuePushDTO();
        param1.setName("client_name");
        param1.setValue(orderMonthClientVO.getName());
        //地址
        OrderParamHistoryValuePushDTO param2 = new OrderParamHistoryValuePushDTO();
        param2.setName("address");
        param2.setValue(addressQueryService.getProvincesRegions(orderMonthClientVO.getProvince(), orderMonthClientVO.getCity(), orderMonthClientVO.getRegion()) + orderMonthClientVO.getAddress());
        //手机
        OrderParamHistoryValuePushDTO param3 = new OrderParamHistoryValuePushDTO();
        param3.setName("phone");
        param3.setValue(orderMonthClientVO.getPhone());
        //身份证号
        OrderParamHistoryValuePushDTO param4 = new OrderParamHistoryValuePushDTO();
        param4.setName("id_card");
        param4.setValue(orderMonthClientVO.getIdCard());
        //甲方紧急联络人姓名
        OrderParamHistoryValuePushDTO param5 = new OrderParamHistoryValuePushDTO();
        param5.setName("urgent_name");
        param5.setValue(orderMonthClientVO.getUrgentName());
        //与甲方关系情况
        OrderParamHistoryValuePushDTO param6 = new OrderParamHistoryValuePushDTO();
        param6.setName("relation_with_client_detail");
        param6.setValue(RelationshipWithEnum.getValueByCode(orderMonthClientVO.getRelationWithClient()));
        //紧急联络人电话
        OrderParamHistoryValuePushDTO param7 = new OrderParamHistoryValuePushDTO();
        param7.setName("urgent_phone");
        param7.setValue(orderMonthClientVO.getUrgentPhone());
        //乙方
        OrderParamHistoryValuePushDTO param8 = new OrderParamHistoryValuePushDTO();
        param8.setName("company_name");
        param8.setValue(BizConstant.COMPANY_NAME);
        //预计护理期限共计
        OrderParamHistoryValuePushDTO param9 = new OrderParamHistoryValuePushDTO();
        param9.setName("service_days");
        param9.setValue(serviceDays.toString());
        //甲方预产期  xxxx年xx月xx日
        OrderParamHistoryValuePushDTO param10 = new OrderParamHistoryValuePushDTO();
        param10.setName("predict_born_time");
        param10.setValue(DateUtil.year(orderMonthClientVO.getPredictBornDate()) == 2099 ? "备孕中" : DateUtil.formatChineseDate(orderMonthClientVO.getPredictBornDate(), false, false));
        //甲方入住乙方会所时间 xxxx年xx月xx日
        OrderParamHistoryValuePushDTO param11 = new OrderParamHistoryValuePushDTO();
        param11.setName("want_in");
        param11.setValue(DateUtil.year(orderMonthClientVO.getPredictBornDate()) == 2099 ? BizConstant.WAIT_IN_STR2 : String.format(BizConstant.WAIT_IN_STR1, DateUtil.formatChineseDate(orderMonthClientVO.getWantIn(), false, false)));
        //甲方选择
        OrderParamHistoryValuePushDTO param12 = new OrderParamHistoryValuePushDTO();
        param12.setName("store_name");
        param12.setValue(cfgStoreRepository.queryCfgStoreById(orderMonthClientVO.getStoreId()).getStoreName());
        //多胎费用
        OrderParamHistoryValuePushDTO param13 = new OrderParamHistoryValuePushDTO();
        param13.setName("multiple_birth_amount");
        param13.setValue(RMBUtils.formatToseparaDecimals(orderDiscountsCacheVO.getAmountReceivableMultipleBirthsCurrentPrice()));
        //多胎费用大写
        OrderParamHistoryValuePushDTO param43 = new OrderParamHistoryValuePushDTO();
        param43.setName("multiple_birth_amount_words");
        param43.setValue(RMBUtils.numToRMBStr(orderDiscountsCacheVO.getAmountReceivableMultipleBirthsCurrentPrice().doubleValue()));
        //套餐
        OrderParamHistoryValuePushDTO param14 = new OrderParamHistoryValuePushDTO();
        param14.setName("goods_name");
        param14.setValue(orderInfoByGoodsVO.getGoodsName());
        //todo 产品需求改动 套餐费用(套餐原价格)
        OrderParamHistoryValuePushDTO param15 = new OrderParamHistoryValuePushDTO();
        param15.setName("order_amount");
        param15.setValue(RMBUtils.formatToseparaDecimals(orderInfoByGoodsVO.getGoodsPriceOrgin()));
        //套餐费用(套餐原价格)大写
        OrderParamHistoryValuePushDTO param16 = new OrderParamHistoryValuePushDTO();
        param16.setName("order_amount_words");
        param16.setValue(RMBUtils.numToRMBStr(orderInfoByGoodsVO.getGoodsPriceOrgin().doubleValue()));
        //订单总额(订单应收)
        OrderParamHistoryValuePushDTO param17 = new OrderParamHistoryValuePushDTO();
        param17.setName("pay_amount");
        param17.setValue(RMBUtils.formatToseparaDecimals(orderDiscountsCacheVO.getOrderReceivableAmountCurrentPrice()));
        //订单总额(订单应收)大写
        OrderParamHistoryValuePushDTO param18 = new OrderParamHistoryValuePushDTO();
        param18.setName("pay_amount_words");
        param18.setValue(RMBUtils.numToRMBStr(orderDiscountsCacheVO.getOrderReceivableAmountCurrentPrice().doubleValue()));
        //账号
        OrderParamHistoryValuePushDTO param19 = new OrderParamHistoryValuePushDTO();
        param19.setName("bank_card");
        param19.setValue(BizConstant.BANK_CARD);
        //银行
        OrderParamHistoryValuePushDTO param20 = new OrderParamHistoryValuePushDTO();
        param20.setName("bank");
        param20.setValue(BizConstant.BANK);
        //户名
        OrderParamHistoryValuePushDTO param21 = new OrderParamHistoryValuePushDTO();
        param21.setName("bank_name");
        param21.setValue(BizConstant.BANK_NAME);
        //合同负责人
        OrderParamHistoryValuePushDTO param22 = new OrderParamHistoryValuePushDTO();
        param22.setName("sale_name");
        param22.setValue(orderMonthClientVO.getSellerName());
        //备注
        OrderParamHistoryValuePushDTO param23 = new OrderParamHistoryValuePushDTO();
        param23.setName("remark");
        param23.setValue(orderInfoByOtherInfoVO.getRemark());
        //节日费用
        OrderParamHistoryValuePushDTO param25 = new OrderParamHistoryValuePushDTO();
        param25.setName("holiday_amount");
        param25.setValue(RMBUtils.formatToseparaDecimals(orderDiscountsCacheVO.getAmountReceivableHolidaysCurrentPrice()));
        //节日费用大写
        OrderParamHistoryValuePushDTO param26 = new OrderParamHistoryValuePushDTO();
        param26.setName("holiday_amount_words");
        param26.setValue(RMBUtils.numToRMBStr(orderDiscountsCacheVO.getAmountReceivableHolidaysCurrentPrice().doubleValue()));
        //入住押金
//        OrderParamHistoryValuePushDTO param27 = new OrderParamHistoryValuePushDTO();
//        param27.setName("deposit");
//        param27.setValue(RMBUtils.formatToseparaDecimals(orderDiscountsCacheVO.getDeposit()));

        Boolean checkHolidayDays = null == atomicHolidayDays.get() || atomicHolidayDays.get() == 0;
        //每日节日费用
        OrderParamHistoryValuePushDTO param28 = new OrderParamHistoryValuePushDTO();
        param28.setName("holiday_amount_day");
        param28.setValue(checkHolidayDays ? RMBUtils.formatToseparaDecimals(BigDecimal.ZERO) : RMBUtils.formatToseparaDecimals(BigDecimalUtil.divide(orderDiscountsCacheVO.getAmountReceivableHolidaysCurrentPrice(), BigDecimal.valueOf(atomicHolidayDays.get()))));
        //每日节日费用
        OrderParamHistoryValuePushDTO param29 = new OrderParamHistoryValuePushDTO();
        param29.setName("holiday_amount_special");
        param29.setValue(checkHolidayDays ? RMBUtils.formatToseparaDecimals(BigDecimal.ZERO) : RMBUtils.formatToseparaDecimals(BigDecimalUtil.divide(orderDiscountsCacheVO.getAmountReceivableHolidaysCurrentPrice(), BigDecimal.valueOf(atomicHolidayDays.get()))));

        //乙方应于甲方退房后的
        OrderParamHistoryValuePushDTO param30 = new OrderParamHistoryValuePushDTO();
        param30.setName("deposit_day");
        param30.setValue(BizConstant.DEPOSIT_DAY);
        //额外礼赠
        OrderParamHistoryValuePushDTO param31 = new OrderParamHistoryValuePushDTO();
        param31.setName("extra_gift");
        param31.setValue(convertOrderGift2String(orderInfoByGiftExtendSkuVOList));
        //续住费用
        OrderParamHistoryValuePushDTO param32 = new OrderParamHistoryValuePushDTO();
        param32.setName("stay_cost");
        param32.setValue(RMBUtils.formatToseparaDecimals(orderDiscountsCacheVO.getAmountReceivableContinuedResidenceCurrentPrice()));
        //续住费用大写
        OrderParamHistoryValuePushDTO param33 = new OrderParamHistoryValuePushDTO();
        param33.setName("stay_cost_words");
        param33.setValue(RMBUtils.numToRMBStr(orderDiscountsCacheVO.getAmountReceivableContinuedResidenceCurrentPrice().doubleValue()));
        //房型变更费用
        OrderParamHistoryValuePushDTO param34 = new OrderParamHistoryValuePushDTO();
        param34.setName("room_changes");
        param34.setValue(RMBUtils.formatToseparaDecimals(orderDiscountsCacheVO.getChangeRoomTypeReceivableAmountCurrentPrice()));
        //房型变更费用大写
        OrderParamHistoryValuePushDTO param35 = new OrderParamHistoryValuePushDTO();
        param35.setName("room_changes_words");
        param35.setValue(RMBUtils.numToRMBStr(orderDiscountsCacheVO.getChangeRoomTypeReceivableAmountCurrentPrice().doubleValue()));
        //意向金
//        OrderParamHistoryValuePushDTO param36 = new OrderParamHistoryValuePushDTO();
//        param36.setName("pay_amount_1");
//        param36.setValue(RMBUtils.formatToseparaDecimals(BigDecimalUtil.divide(earnestMoney.get(), new BigDecimal(100))));
        //意向金大写
//        OrderParamHistoryValuePushDTO param37 = new OrderParamHistoryValuePushDTO();
//        param37.setName("pay_amount_1_words");
//        param37.setValue(RMBUtils.numToRMBStr(BigDecimalUtil.divide(earnestMoney.get(), new BigDecimal(100)).doubleValue()));
        //预定金 签单金额的50%
        OrderParamHistoryValuePushDTO param38 = new OrderParamHistoryValuePushDTO();
        param38.setName("pay_amount_2");
        param38.setValue(RMBUtils.formatToseparaDecimals(BigDecimalUtil.divide(orderDiscountsCacheVO.getOrderReceivableAmountCurrentPrice(), new BigDecimal(2))));
        //预定金大写
        OrderParamHistoryValuePushDTO param39 = new OrderParamHistoryValuePushDTO();
        param39.setName("pay_amount_2_words");
        param39.setValue(RMBUtils.numToRMBStr(BigDecimalUtil.divide(orderDiscountsCacheVO.getOrderReceivableAmountCurrentPrice(), new BigDecimal(2)).doubleValue()));
        //尾款
        OrderParamHistoryValuePushDTO param40 = new OrderParamHistoryValuePushDTO();
        param40.setName("pay_amount_3");
        param40.setValue(RMBUtils.formatToseparaDecimals(BigDecimalUtil.divide(orderDiscountsCacheVO.getOrderReceivableAmountCurrentPrice(), new BigDecimal(2))));
        //尾款大写
        OrderParamHistoryValuePushDTO param41 = new OrderParamHistoryValuePushDTO();
        param41.setName("pay_amount_3_words");
        param41.setValue(RMBUtils.numToRMBStr(BigDecimalUtil.divide(orderDiscountsCacheVO.getOrderReceivableAmountCurrentPrice(), new BigDecimal(2)).doubleValue()));
        //退房时间
        OrderParamHistoryValuePushDTO param42 = new OrderParamHistoryValuePushDTO();
        param42.setName("service_end_date");
        param42.setValue(DateUtil.year(orderMonthClientVO.getPredictBornDate()) == 2099 ? String.format(BizConstant.SERVICE_END_DATE2, serviceDays) : String.format(BizConstant.SERVICE_END_DATE1, DateUtil.formatChineseDate(DateUtils.addTime(orderMonthClientVO.getWantIn(), serviceDays, Calendar.DAY_OF_YEAR), false, false)));
        CfgStoreEntity cfgStore = storeRepository.queryCfgStoreById(orderMonthClientVO.getStoreId());
        //门店地址
        OrderParamHistoryValuePushDTO param44 = new OrderParamHistoryValuePushDTO();
        param44.setName("storeAddress");
        param44.setValue(contractStoreAddress(cfgStore));
        //服务对象人数
        OrderParamHistoryValuePushDTO param45 = new OrderParamHistoryValuePushDTO();
        param45.setName("service_people_num");
        param45.setValue(String.valueOf(servicePeopleNum.get()));

        OrderParamHistoryValuePushDTO param46 = new OrderParamHistoryValuePushDTO();
        param46.setName("contract_sign_date");
        param46.setValue(DateTime.now().toDateStr());
        /**
         * 获取签署人信息
         * 暂时注释
         */
//        MonthUserEsignDTO userEsign = monthContractAssembler.userEsignAssembler(orderId, TemplateContractTypeEnum.YZ_SAINTBELLA.code());
//        OrderParamHistoryValuePushDTO param46 = new OrderParamHistoryValuePushDTO();
//        param46.setName("signPhone");
//        param46.setValue(userEsign.getPhone());
//        OrderParamHistoryValuePushDTO param47 = new OrderParamHistoryValuePushDTO();
//        param47.setName("companyName");
//        param47.setValue(BizConstant.COMPANY_NAME);

        //组装
        paramList.add(param1);
        paramList.add(param2);
        paramList.add(param3);
        paramList.add(param4);
        paramList.add(param5);
        paramList.add(param6);
        paramList.add(param7);
        paramList.add(param8);
        paramList.add(param9);
        paramList.add(param10);
        paramList.add(param11);
        paramList.add(param12);
        paramList.add(param13);
        paramList.add(param14);
        paramList.add(param15);
        paramList.add(param16);
        paramList.add(param17);
        paramList.add(param18);
        paramList.add(param19);
        paramList.add(param20);
        paramList.add(param21);
        paramList.add(param22);
        paramList.add(param23);
        paramList.add(param25);
        paramList.add(param26);
//        paramList.add(param27);
        paramList.add(param28);
        paramList.add(param29);
        paramList.add(param30);
        paramList.add(param31);
        paramList.add(param32);
        paramList.add(param33);
        paramList.add(param34);
        paramList.add(param35);
//        paramList.add(param36);
//        paramList.add(param37);
        paramList.add(param38);
        paramList.add(param39);
        paramList.add(param40);
        paramList.add(param41);
        paramList.add(param42);
        paramList.add(param43);
        paramList.add(param44);
        paramList.add(param45);
        paramList.add(param46);
//        paramList.add(param46);
//        paramList.add(param47);
        OrderParamHistoryPushDTO dto = new OrderParamHistoryPushDTO();
        dto.setOrderId(orderId);
        dto.setOrderType(OmniOrderTypeEnum.MONTH_ORDER.getCode());
        dto.setStoreId(orderMonthClientVO.getStoreId());
        dto.setTemplateContractType(TemplateContractTypeEnum.YZ_SAINTBELLA.code());
        dto.setParamList(paramList);
        log.info("订单推送合同参数={}", JSONUtil.parse(dto));
        return dto;
    }

    /**
     * 组合门店地址 加上省市区
     *
     * @param store
     * @return
     */
    private String contractStoreAddress(CfgStoreEntity store) {
        String address = "";
        List<String> removeDuplicatesNameList = districtRepository.getRemoveDuplicatesNameList(Arrays.asList(store.getProvince(), store.getCity(), store.getRegion()));
        StringBuilder stringBuilder = new StringBuilder();
        for (String s : removeDuplicatesNameList) {
            stringBuilder.append(s);
        }
        address = stringBuilder + store.getAddress();
        return address;
    }

    //转换额外礼赠  产康金1000元，升级为厦门2000 *22，续住 *2，节日费用抵扣 *3，家属房 *2，产康-古法洗头 *2，Bosch/博世电动智能婴儿床_800x800 *2
    @Override
    public String convertOrderGift2String(List<OrderInfoByGiftExtendSkuVO> orderInfoByGiftExtendSkuVOList) {
        Map<String, Integer> giftMap = new HashMap();

        log.info("转换额外礼赠 req:{}", JSONUtil.toJsonStr(orderInfoByGiftExtendSkuVOList));
        orderInfoByGiftExtendSkuVOList.forEach(x -> {
            if (x.getType().equals(OrderGiftExtendTypeEnum.SANKANGJIN.code())) {
                String key = OrderGiftExtendTypeEnum.SANKANGJIN.desc();
                Integer num = giftMap.get(key);
                if (ObjectUtil.isEmpty(num)) {
                    giftMap.put(key, x.getQuantity());
                } else {
                    giftMap.put(key, x.getQuantity() + num);
                }
            } else if (x.getType().equals(OrderGiftExtendTypeEnum.FAMILY_ROOM.code())) {
                String key = x.getGoodsName();
                Integer num = giftMap.get(key);
                if (ObjectUtil.isEmpty(num)) {
                    giftMap.put(key, x.getQuantity());
                } else {
                    giftMap.put(key, x.getQuantity() + num);
                }
            } else {
                if (ObjectUtil.isNotEmpty(x.getSkuName())) {
                    String key = "";
                    if (x.getSkuName().contains(x.getGoodsName())) {
                        key = x.getSkuName();
                    } else {
                        key = x.getGoodsName() + "-" + x.getSkuName();
                    }
                    Integer num = giftMap.get(key);
                    if (ObjectUtil.isEmpty(num)) {
                        giftMap.put(key, x.getQuantity());
                    } else {
                        giftMap.put(key, x.getQuantity() + num);
                    }
                } else {
                    String key = x.getGoodsName();
                    Integer num = giftMap.get(key);
                    if (ObjectUtil.isEmpty(num)) {
                        giftMap.put(key, x.getQuantity());
                    } else {
                        giftMap.put(key, x.getQuantity() + num);
                    }

                }
            }
        });
        return giftMap.keySet().stream().map(x -> x + " *" + giftMap.get(x)).collect(Collectors.joining(","));
    }

    @Override
    public Result removeAllOrderCache(RemoveAllOrderCacheReq removeAllOrderCacheReq) {
        OrderCacheBaseReq OrderCacheBaseReq = new OrderCacheBaseReq();
        OrderCacheBaseReq.setOrderId(removeAllOrderCacheReq.getOrderId());
        OrderCacheBaseReq.setOperator(removeAllOrderCacheReq.getOperator());
        removeCache(OrderCacheBaseReq);
        return Result.success();
    }

    @Override
    public Result<RemoveAllOrderCacheVO> checkCacheByOrderId(RemoveAllOrderCacheReq removeAllOrderCacheReq) {
        OrderGoodsCacheReq orderGoodsCacheReq = new OrderGoodsCacheReq();
        orderGoodsCacheReq.setOrderId(removeAllOrderCacheReq.getOrderId());
        Result<OrderMonthGoodsCacheVO> orderGoodsCache = monthOrderWxQueryService.getOrderGoodsCache(orderGoodsCacheReq);
        RemoveAllOrderCacheVO removeAllOrderCacheVO = new RemoveAllOrderCacheVO();

        OrderMonthClientCacheRequest orderMonthClientCacheRequest = new OrderMonthClientCacheRequest();
        orderMonthClientCacheRequest.setOrderId(removeAllOrderCacheReq.getOrderId());
        Result<OrderMonthClientCacheVO> clientInfoCache = tabClientService.getClientInfoCache(orderMonthClientCacheRequest);
        OrderMonthClientCacheVO orderMonthClientCacheVO = clientInfoCache.getData();

        OrderMonthClientBailorCacheRequest orderMonthClientBailorCacheRequest = new OrderMonthClientBailorCacheRequest();
        orderMonthClientBailorCacheRequest.setOrderId(removeAllOrderCacheReq.getOrderId());
        Result<OrderMonthClientBailorCacheVO> bailorInfoCache = tabClientBailorService.getBailorInfoCache(orderMonthClientBailorCacheRequest);
        OrderMonthClientBailorCacheVO orderMonthClientBailorCacheVO = bailorInfoCache.getData();


        log.info("orderGoodsCache:" + orderGoodsCache);
        log.info("clientInfoCache:" + orderMonthClientCacheVO);
        log.info("bailorInfoCache:" + orderMonthClientBailorCacheVO);

        log.info("orderGoodsCache:" + (ObjectUtil.isEmpty(orderGoodsCache.getData()) || ObjectUtil.isEmpty(orderGoodsCache.getData().getGoodsId())));
        log.info("clientInfoCache:" + ObjectUtil.isEmpty(orderMonthClientCacheVO));
        log.info("bailorInfoCache:" + ObjectUtil.isEmpty(orderMonthClientBailorCacheVO));

        if ((ObjectUtil.isEmpty(orderGoodsCache.getData()) || ObjectUtil.isEmpty(orderGoodsCache.getData().getGoodsId())) && ObjectUtil.isEmpty(orderMonthClientCacheVO) && ObjectUtil.isEmpty(orderMonthClientBailorCacheVO)) {
            removeAllOrderCacheVO.setExist(false);
        } else {
            removeAllOrderCacheVO.setExist(true);
        }
        return Result.success(removeAllOrderCacheVO);
    }

    /**
     * 清洗数据不改,因为数据已经清洗完毕
     * <p>
     * 将订单礼赠扩展信息列表转化为额外收益信息列表
     *
     * @param orderGiftExtendEntityList 订单礼赠扩展信息列表
     * @param heOrderEntity             订单实体
     * @return 额外收益信息列表
     */
    private List<HeOrderAdditionalRevenueEntity> giftExtendEntity2AdditionalRevenueEntityList(List<OrderGiftExtendEntity> orderGiftExtendEntityList, HeOrderEntity heOrderEntity) {
        List<HeOrderAdditionalRevenueEntity> revenueEntityList = new ArrayList<>();
        //节假日费用,取值为订单主表的节假日信息,计算应付=订单主表的节日费用-额为额外礼赠的节日抵扣
        OrderMonthStoreTypeCostEnum orderMonthStoreTypeCostEnum = getStoreType(heOrderEntity.getStoreId());
        HeOrderAdditionalRevenueEntity holidayAdditionalRevenueEntity = new HeOrderAdditionalRevenueEntity();
        if (ObjectUtil.isNotEmpty(heOrderEntity.getHolidayItems()) || heOrderEntity.getHolidayAmount() > 0 || heOrderEntity.getHolidayNum() > 0) {
            //获取门店类型
            holidayAdditionalRevenueEntity.setType(MonthAdditionalRevenueEnum.HOLIDAY.getCode());
            holidayAdditionalRevenueEntity.setGoodsName("节假日费用");
            holidayAdditionalRevenueEntity.setStoreId(heOrderEntity.getStoreId());
            holidayAdditionalRevenueEntity.setOrderId(heOrderEntity.getOrderId());
            holidayAdditionalRevenueEntity.setSource(1);
            if (ObjectUtil.isNotEmpty(heOrderEntity.getCreatedAt()) && heOrderEntity.getCreatedAt() != 0) {
                holidayAdditionalRevenueEntity.setGmtCreate(new Date(heOrderEntity.getCreatedAt()));
            }
            if (ObjectUtil.isNotEmpty(heOrderEntity.getUpdatedAt()) && heOrderEntity.getUpdatedAt() != 0) {
                holidayAdditionalRevenueEntity.setGmtModified(new Date(heOrderEntity.getUpdatedAt()));
            }
            //应收
            holidayAdditionalRevenueEntity.setPrice(heOrderEntity.getHolidayAmount());
            //成本
            if (heOrderEntity.getHolidayNum() > 0) {
                //todo 清洗数据完成这里不改动
                holidayAdditionalRevenueEntity.setCostPrice(BigDecimalUtil.multiplication(orderMonthStoreTypeCostEnum.getHolidayCost().multiply(new BigDecimal(100)), new BigDecimal(heOrderEntity.getHolidayNum())).intValue());
                //原价
                holidayAdditionalRevenueEntity.setCost(holidayAdditionalRevenueEntity.getCostPrice());
            }
            holidayAdditionalRevenueEntity.setIsFestival(1);
            //节假日天数
            holidayAdditionalRevenueEntity.setDays(heOrderEntity.getHolidayNum());
            revenueEntityList.add(holidayAdditionalRevenueEntity);
        }


        for (OrderGiftExtendEntity orderGiftExtendEntity : orderGiftExtendEntityList) {

            //1=产康金 2=房型升级 3=续住 4=节日费用 5=家属房 6=产康服务 7=实物商品礼赠
            //1=多胞胎费用 2=续住费用 3=变更房型 加收项
            if (orderGiftExtendEntity.getType().equals(2)) {
                HeOrderAdditionalRevenueEntity heOrderAdditionalRevenueEntity = new HeOrderAdditionalRevenueEntity();

                heOrderAdditionalRevenueEntity.setGoodsName(orderGiftExtendEntity.getGoodsName());
                heOrderAdditionalRevenueEntity.setStoreId(orderGiftExtendEntity.getStoreId());
                heOrderAdditionalRevenueEntity.setOrderId(orderGiftExtendEntity.getOrderId());
                heOrderAdditionalRevenueEntity.setContent(orderGiftExtendEntity.getContent());
                heOrderAdditionalRevenueEntity.setSource(orderGiftExtendEntity.getSource());
                heOrderAdditionalRevenueEntity.setGmtCreate(orderGiftExtendEntity.getGmtCreate());
                heOrderAdditionalRevenueEntity.setGmtModified(orderGiftExtendEntity.getGmtModified());
                String content = orderGiftExtendEntity.getContent();
                heOrderAdditionalRevenueEntity.setContent(content);
                OrderGiftExtendRoomChangeDTO orderGiftExtendRoomChangeDTO = JSONUtil.toBean(content, OrderGiftExtendRoomChangeDTO.class);
                log.info("老订单清洗房型变更数据源={}", JSONUtil.toJsonStr(orderGiftExtendRoomChangeDTO));
                //房型变更天数(特殊逻辑取数量)
                heOrderAdditionalRevenueEntity.setType(MonthAdditionalRevenueEnum.ROOM_CHANGES.getCode());
                Integer days = orderGiftExtendEntity.getGoodsNum();
                heOrderAdditionalRevenueEntity.setDays(days);
                heOrderAdditionalRevenueEntity.setRoomId(orderGiftExtendRoomChangeDTO.getRoom_id());
                heOrderAdditionalRevenueEntity.setRoomName(orderGiftExtendRoomChangeDTO.getRoom_name());

                //设置房型变更具体天数
                List<String> dayList = new ArrayList<>();
                for (int i = 1; i <= days; i++) {
                    dayList.add(String.valueOf(i));
                }
                //具体时间
                //原价
                heOrderAdditionalRevenueEntity.setCost(orderGiftExtendEntity.getPrice());
                heOrderAdditionalRevenueEntity.setPrice(0);
                heOrderAdditionalRevenueEntity.setDaysList(dayList);
                revenueEntityList.add(heOrderAdditionalRevenueEntity);
            } else if (orderGiftExtendEntity.getType().equals(3) && orderGiftExtendEntity.getGoodsNum() > 0) {

                String content = orderGiftExtendEntity.getContent();
                //只同步续住为正的数据数据
                List<OrderGiftExtendStayOverDTO> stayOverDTOList = JSONUtil.toList(content, OrderGiftExtendStayOverDTO.class);
                log.info("老订单清洗续住数据={}", JSONUtil.toJsonStr(stayOverDTOList));
                for (OrderGiftExtendStayOverDTO stayOverDTO : stayOverDTOList) {
                    HeOrderAdditionalRevenueEntity heOrderAdditionalRevenueEntity = new HeOrderAdditionalRevenueEntity();

                    heOrderAdditionalRevenueEntity.setStoreId(orderGiftExtendEntity.getStoreId());
                    heOrderAdditionalRevenueEntity.setOrderId(orderGiftExtendEntity.getOrderId());
                    heOrderAdditionalRevenueEntity.setSource(orderGiftExtendEntity.getSource());
                    heOrderAdditionalRevenueEntity.setGmtCreate(orderGiftExtendEntity.getGmtCreate());
                    heOrderAdditionalRevenueEntity.setGmtModified(orderGiftExtendEntity.getGmtModified());
                    heOrderAdditionalRevenueEntity.setContent(JSONUtil.toJsonStr(stayOverDTO));
                    //续住天数
                    Integer days = stayOverDTO.getStay_days();
                    heOrderAdditionalRevenueEntity.setGoodsName(stayOverDTO.getRoom_name());

                    heOrderAdditionalRevenueEntity.setType(MonthAdditionalRevenueEnum.STAY_COST.getCode());
                    heOrderAdditionalRevenueEntity.setDays(days);
                    heOrderAdditionalRevenueEntity.setRoomId(stayOverDTO.getRoom_id());
                    heOrderAdditionalRevenueEntity.setRoomName(stayOverDTO.getRoom_name());
                    //计算得出
                    heOrderAdditionalRevenueEntity.setCost(stayOverDTO.getRoom_price_stay() * days);
                    heOrderAdditionalRevenueEntity.setPrice(0);
                    revenueEntityList.add(heOrderAdditionalRevenueEntity);
                }
            }

        }
        return revenueEntityList;
    }

    /**
     * 清洗数据不改
     * <p>
     * 获取门店类型
     *
     * @param storeId
     * @return
     */
    private OrderMonthStoreTypeCostEnum getStoreType(Integer storeId) {
        //获取门店类型
        CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(storeId);

        //判断门店品牌
        Integer storeType = cfgStoreEntity.getType();

        OrderMonthStoreTypeCostEnum orderMonthStoreTypeCostEnum = null;

        if (storeType == 0) {
            orderMonthStoreTypeCostEnum = OrderMonthStoreTypeCostEnum.SAINT_BELLA;
        } else if (storeType == 1) {
            Integer childType = cfgStoreEntity.getChildType();
            if (ObjectUtil.isNotEmpty(childType) && childType == 0) {
                orderMonthStoreTypeCostEnum = OrderMonthStoreTypeCostEnum.DELUXE;
            } else {
                orderMonthStoreTypeCostEnum = OrderMonthStoreTypeCostEnum.BABY_BELLA;
            }
        }
        return orderMonthStoreTypeCostEnum;
    }


}

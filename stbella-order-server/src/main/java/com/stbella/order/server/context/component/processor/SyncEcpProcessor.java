package com.stbella.order.server.context.component.processor;

import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundGoodsEntity;
import com.stbella.order.domain.order.service.OrderGoodsDomainService;
import com.stbella.order.server.order.month.component.EcpOrderSyncAssembler;
import com.stbella.order.server.order.month.enums.OrderEventEnum;
import com.stbella.order.server.producer.OrderEventProducer;
import com.stbella.order.server.utils.RefundGoodsNumUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: wangchuang
 * @CreateTime: 2024-05-28  13:55
 * @Description: 同步ecp组件
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "同步ecp组件")
public class SyncEcpProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private EcpOrderSyncAssembler ecpOrderSyncAssembler;
    @Resource
    OrderGoodsDomainService orderGoodsDomainService;
    @Resource
    private OrderEventProducer orderEventProducer;

    @Override
    public boolean condition(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        return orderEntity.isJustEffect();
    }

    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);
        List<HeOrderGoodsEntity> goodsEntityList = orderGoodsDomainService.queryOrderGoodsWithExpand(order.getOrderId());
        //heOrderGoodsEntityList的goodsNum 需要减去退款成功的
        List<HeOrderRefundGoodsEntity> refundGoodsEntities = bizContext.getListAttribute(HeOrderRefundGoodsEntity.class);
        RefundGoodsNumUtil.dealGoodsNum(goodsEntityList, refundGoodsEntities);
        List<HeOrderGoodsEntity> goodsOfRoom = goodsEntityList.stream().filter(a -> BizConstant.OrderAppKey.ASSET_LIST_ROOM_SERVICE.contains(a.getGoodsType()) && a.getGoodsNum() > 0).collect(Collectors.toList());
        // [资产类型]=[馆内护理服务]/[客房服务] 需要同步 ecp
        log.info("{} 业绩是否生效 {} 月子类商品数量: {}", order.getOrderSn(), order.isJustEffect(), goodsOfRoom.size());

        if (!goodsOfRoom.isEmpty()) {
            log.info("订单支付开始推送房态和ECP{} 业绩是否生效 {} 月子类商品数量: {}", order.getOrderSn(), order.isJustEffect(), goodsOfRoom.size());

            ecpOrderSyncAssembler.syncEcpOrder(order.getOrderId());
            orderEventProducer.sendMq(OrderEventEnum.PAY_FIFTY_PERCENT.getCode(), order.getOrderSn(), null);
        }
    }


}

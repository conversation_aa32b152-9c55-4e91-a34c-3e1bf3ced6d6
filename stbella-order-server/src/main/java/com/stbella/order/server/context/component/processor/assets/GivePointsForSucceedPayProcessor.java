package com.stbella.order.server.context.component.processor.assets;

import cn.hutool.json.JSONUtil;
import com.stbella.customer.server.ecp.request.OrderAddIntegralRequest;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.domain.client.PointsClient;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.server.convert.OrderConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * @Author: JJ
 * @Description: 全部支付成功赠送客户积分
 * 支付成功
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "支付成功赠送客户积分")
public class GivePointsForSucceedPayProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private PointsClient pointsClient;
    @Resource
    private OrderConvert orderConvert;
    @Resource
    private IncomeRecordRepository incomeRecordRepository;

    @Override
    public boolean condition(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        return orderEntity.isJustFullPayment() && !OmniOrderTypeEnum.getHomeOrderTypeCodeList().contains(orderEntity.getOrderType());
    }

    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);
        log.info("订单V3支付赠送客户积分信息orderEntity:{}", JSONUtil.toJsonStr(order));
        OrderAddIntegralRequest request = orderConvert.heOrderEntity2OrderAddIntegralRequest(order);
        request.setOfflineIncomeWaitHandleRecordNum(0);
//        Integer calPayable = order.calPayable();
        if (Objects.isNull(order.getFxRate())) {
            order.setFxRate(BigDecimal.ONE);
        }
        //产康金不用发放积分
        List<HeIncomeRecordEntity> allRecordListByOrderId =
                incomeRecordRepository.getAllRecordListByOrderId(order.getOrderId());
        int sum = allRecordListByOrderId.stream().filter(o -> !OmniPayTypeEnum.PRODUCTION_COIN.getCode().equals(o.getPayType())
                && !OmniPayTypeEnum.REDUCTION.getCode().equals(o.getPayType())
        ).mapToInt(HeIncomeRecordEntity::getIncome).sum();
        BigDecimal givePoints = order.getFxRate().multiply(new BigDecimal(sum));
        request.setPaidAmount(givePoints.intValue());
        request.setPayAmount(givePoints.intValue());
        request.setAmount(givePoints.intValue());
        request.setPayType(OmniPayTypeEnum.OFFLINE.getCode());
        request.setIncomeId(order.getOrderId());
        request.setIncomeSn(order.getOrderSn());
        request.setPayTime(DateUtils.getTenBitTimestamp().intValue());
        request.setOrderType(order.getOrderType());
        pointsClient.addPoints(request);
    }
}


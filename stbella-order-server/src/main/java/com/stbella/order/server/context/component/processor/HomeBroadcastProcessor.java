package com.stbella.order.server.context.component.processor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.core.result.Result;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.core.OrderNoticeEnum;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.server.context.component.spi.OrderBroadcastSpi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.SnowballExtensionInvoker;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "予家订单报单组件")
public class HomeBroadcastProcessor implements IExecutableAtom<FlowContext> {

    @Override
    public boolean condition(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        return OmniOrderTypeEnum.getHomeOrderTypeCodeList().contains(orderEntity.getOrderType());
    }

    @Override
    public void run(FlowContext bizContext) {

        try {
            HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);
            log.info("予家订单-开始报单，order：{}", JSONUtil.toJsonStr(order));
            log.info("已付金额,sumPaidAmount:{}", order.sumPaidAmount());


            HeIncomeRecordEntity incomeRecordEntity = bizContext.getAttribute(HeIncomeRecordEntity.class);
            log.info("予家订单-收入记录incomeRecordEntity：{}", JSONUtil.toJsonStr(incomeRecordEntity));
            if (OmniPayTypeEnum.REDUCTION.getCode().equals(incomeRecordEntity.getPayType())) {
                log.info("予家订单-收入记录为减免收入，不进行报单");
                return;
            }

            Result invokeResult = SnowballExtensionInvoker.invoke(OrderBroadcastSpi.class, order);
            if (ObjectUtil.isNotNull(invokeResult)) {
                bizContext.setAttribute(Result.class, invokeResult);
            }
        } catch (Exception e) {
            log.error("予家订单-报单失败", e);
            Result invokeResult = new Result();
            invokeResult.setSuccess(false);
            invokeResult.setMsg(e.getMessage());
            bizContext.setAttribute(Result.class, invokeResult);
        }
    }
}

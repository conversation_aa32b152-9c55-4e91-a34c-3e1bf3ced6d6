package com.stbella.order.server.order.nutrition.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.order.server.order.nutrition.entity.OrderNutritionAuditPO;
import com.stbella.order.server.order.nutrition.mapper.OrderNutritionAuditMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 营养订单审批表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2022-03-17
 */
@Service
public class OrderNutritionAuditServiceImpl extends ServiceImpl<OrderNutritionAuditMapper, OrderNutritionAuditPO> implements OrderNutritionAuditService {

    @Override
    public List<OrderNutritionAuditPO> getApproveInfoByOrderNo(List<String> orderNos) {
        LambdaQueryWrapper<OrderNutritionAuditPO> queryWrapper = new LambdaQueryWrapper<OrderNutritionAuditPO>()
                .in(OrderNutritionAuditPO::getOrderNo, orderNos);
        return baseMapper.selectList(queryWrapper);
    }
}

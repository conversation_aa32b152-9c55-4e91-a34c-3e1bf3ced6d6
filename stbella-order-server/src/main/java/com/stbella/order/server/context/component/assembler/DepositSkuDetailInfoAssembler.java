package com.stbella.order.server.context.component.assembler;

import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.platform.order.api.req.CreateOrderReq;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import com.stbella.store.common.enums.core.GoodsTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 押金商品组装器
 *     依赖如下对象：
 *     CustomAttribute.class
 *     SkuDetailInfo.class
 *     CfgStoreEntity.class
 */
@Slf4j
@Component
@SnowballComponent(name = "sku详情组装器", desc = "组装sku详情信息")
public class DepositSkuDetailInfoAssembler implements IExecutableAtom<FlowContext> {

    @Override
    public void run(FlowContext bizContext) {

        CfgStoreEntity cfgStore = bizContext.getAttribute(CfgStoreEntity.class);
        CreateOrderReq createOrderReq = bizContext.getAttribute(CreateOrderReq.class);
        createOrderReq.setPayAmount(new BigDecimal(cfgStore.getDeposit() * 1.0/100).setScale(2, RoundingMode.HALF_UP));
        createOrderReq.setOriginalPrice(createOrderReq.getPayAmount());

        SkuDetailInfo skuDetailInfo = initDepositSku();
        skuDetailInfo.setNum(1);
        skuDetailInfo.setSkuNum(1);
        skuDetailInfo.setPrice(AmountChangeUtil.changeF2Y(cfgStore.getDeposit()));
        List<SkuDetailInfo> skuList = new ArrayList<>();
        skuList.add(skuDetailInfo);
        bizContext.setListAttribute(SkuDetailInfo.class, skuList);
        bizContext.setAttribute(CreateOrderReq.class, createOrderReq);
    }


    /**
     * 初始化押金sku
     */
    protected SkuDetailInfo initDepositSku(){
        SkuDetailInfo cartSku = new SkuDetailInfo();
        cartSku.setSkuSaleState(1);
        cartSku.setSkuName("押金");
        cartSku.setPrice(new BigDecimal(1));
        cartSku.setCostPrice(new BigDecimal(0));
        cartSku.setBackCategoryId(0);
        cartSku.setAssetType(GoodsTypeEnum.DEPOSIT.code());
        cartSku.setCostPricingMethod(0);
        cartSku.setCostRatio(0);
        cartSku.setType(CombineTypeEnum.SIMPLE.code());
        cartSku.setRoomType(0);
        cartSku.setEcpRoomType(0);
        cartSku.setUnitStr("");
        cartSku.setCategoryBackTitle("");
        cartSku.setGift(false);
        return cartSku;
    }



}

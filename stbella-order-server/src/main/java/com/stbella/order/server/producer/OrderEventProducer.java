package com.stbella.order.server.producer;

import com.alibaba.fastjson.JSON;
import com.stbella.order.server.order.month.dto.OrderEventBody;
import com.stbella.pulsar.template.PulsarTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 订单变更通知房态
 * @date 2023/1/10 19:56
 */
@Slf4j
@Service
public class OrderEventProducer {
    @Resource
    private PulsarTemplate pulsarTemplate;

    private String ORDER_EVENT_TOPIC = "";

    @Value("${pulsar.nameSpace}")
    private void setNameSpace(String nameSpace) {
        ORDER_EVENT_TOPIC = "persistent://pulsar-44k4paxzz5vg/" + nameSpace + "/order_event_notify";
    }

    public void sendMq(Integer code, String orderNo, Integer refundNature) {

        OrderEventBody orderEventBody = new OrderEventBody();
        orderEventBody.setOrderNo(orderNo);
        orderEventBody.setEventType(code);
        orderEventBody.setRefundNature(refundNature);
        log.info("发送 订单变更事件mq topic:{} 消息: {}", ORDER_EVENT_TOPIC, JSON.toJSONString(orderEventBody));
        try {
            pulsarTemplate.send(ORDER_EVENT_TOPIC, JSON.toJSONString(orderEventBody));
        } catch (Exception e) {
            log.error("发送 mq 消息失败: {}", JSON.toJSONString(orderEventBody));
            log.error("发送 mq 消息失败: 异常：", e);
        }
    }
}

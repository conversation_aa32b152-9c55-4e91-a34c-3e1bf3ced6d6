package com.stbella.order.server.context.component.processor;

import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.service.OrderCreateDomainService;
import com.stbella.platform.order.api.req.CreateOrderReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

/**
 * @Author: jijun<PERSON>an
 * @CreateTime: 2024-03-26  13:55
 * @Description: 创建通用订单
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "创建通用订单")
public class CreateOrderProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    OrderCreateDomainService orderCreateDomainService;


    @Override
    public void run(FlowContext bizContext) {
        CreateOrderReq req = bizContext.getAttribute(CreateOrderReq.class);
        HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);
        orderCreateDomainService.create(order, req.getOperator());
        bizContext.setAttribute(HeOrderEntity.class, order);
        log.info("保存通用订单成功，订单号：{}", order.getOrderSn());
    }
}

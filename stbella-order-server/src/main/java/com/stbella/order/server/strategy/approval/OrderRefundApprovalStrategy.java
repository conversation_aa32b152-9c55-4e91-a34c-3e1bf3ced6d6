package com.stbella.order.server.strategy.approval;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.month.server.request.RefundApprovalRequest;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.order.month.request.approval.ApprovalStatusInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.FlowIdentity;
import top.primecare.snowball.flow.core.definition.FlowIdentityBuilder;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * 线下退款
 * @date 2024/3/18 17:45
 */
@Component
@Slf4j
public class OrderRefundApprovalStrategy implements ApprovalStrategy {

    @Resource
    private OrderRepository orderRepository;

    @Override
    public void handleApproval(OrderApproveRecordTypeEnum type, String params, String messageId, ApprovalStatusInfo approvalStatusInfo, String processId) {
        RefundApprovalRequest refundApprovalRequest = JSONObject.parseObject(params, RefundApprovalRequest.class);
        HeOrderEntity orderEntity = orderRepository.getByOrderId(refundApprovalRequest.getOrderId());
        if (ObjectUtil.isNotEmpty(orderEntity) && orderEntity.isNewOrder()) {
            //走新逻辑
            // 业务线：场景：订单类型
            FlowIdentityBuilder flowIdentityBuilder = FlowIdentity.builder()
                    .bizActivity(BizActivityEnum.ORDER_REFUND_AFTER_APPROVE.code())
                    .idSlice("Order")
                    .idSlice("Refund")
                    .idSlice("After")
                    .idSlice("Approve");
            FlowContext context = new FlowContext();
            context.setAttribute(RefundApprovalRequest.class, refundApprovalRequest);
            context.setAttribute(ApprovalStatusInfo.class, approvalStatusInfo);
            context.setAttribute(HeOrderEntity.class, orderEntity);

            boolean deposit = orderEntity.isDepositOrder();
            context.setAttribute(OtherConstant.DEPOSIT, deposit);
            context.setAttribute(OtherConstant.CONTINUE, true);
            SnowballFlowLauncher.fire(flowIdentityBuilder.build(), context);
        }
    }

    @Override
    public OrderApproveRecordTypeEnum getOrderApproveRecordTypeEnum() {
        return OrderApproveRecordTypeEnum.ORDER_REFUND;
    }
}

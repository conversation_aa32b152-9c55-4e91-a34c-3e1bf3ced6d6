package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.common.enums.order.OrderAdditionalKeyEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.entity.HeCartEntity;
import com.stbella.order.domain.repository.HeCartRepository;
import com.stbella.platform.order.api.req.QueryCartReq;
import com.stbella.order.server.context.component.StoreCurrencyContainer;
import com.stbella.order.server.context.component.processor.GoodsAllocateUtil;
import com.stbella.order.server.convert.OrderGoodsConverter;
import com.stbella.order.server.order.OrderIndex;
import com.stbella.order.server.order.OrderPrice;
import com.stbella.order.server.order.ProductionCoinRule;
import com.stbella.order.server.order.month.enums.ApprovalDiscountStatusEnum;
import com.stbella.order.server.order.month.enums.DiscountRuleResultEnum;
import com.stbella.order.server.order.month.res.OrderDiscountsCacheVO;
import com.stbella.platform.order.api.req.CreateOrderReq;
import com.stbella.platform.order.api.req.CustomAttribute;
import com.stbella.platform.order.api.req.ExtraInfo;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import com.stbella.platform.order.api.res.DiscountRes;
import com.stbella.platform.order.api.res.SkuAdditionalInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-03-26  13:55
 * @Description: 添加通用订单
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "通用订单信息组装", desc = "订单相关信息组合到entity中")
public class OrderEntityForCartAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    com.stbella.order.server.utils.IdGenUtils IdGenUtils;
    @Resource
    OrderGoodsConverter orderGoodsConverter;
    @Resource
    HeCartRepository heCartRepository;

    @Override
    public void run(FlowContext bizContext) {

        CreateOrderReq req = bizContext.getAttribute(CreateOrderReq.class);
        OrderIndex orderIndex = bizContext.getAttribute(OrderIndex.class);

        CfgStoreEntity store = bizContext.getAttribute(CfgStoreEntity.class);
        DiscountRes discountRes = bizContext.getAttribute(DiscountRes.class);

        HeOrderEntity heOrder = orderAssembler(req);
        heOrder.setBu(req.getBu());
        heOrder.setStore(store);
        setOrderIndex(heOrder, orderIndex);
        initApprovalDiscountStatus(heOrder, discountRes);

        heOrder.setStore(store);
        BigDecimal giftAmount = BigDecimal.ZERO;
        BigDecimal selectedAmount = BigDecimal.ZERO;
        OrderPrice orderPrice = bizContext.getAttribute(OrderPrice.class);
        if (Objects.nonNull(orderPrice)) {
            if (Objects.nonNull(orderPrice.getGiftAmount())) {
                giftAmount = orderPrice.getGiftAmount();
            }
            if (Objects.nonNull(orderPrice.getSelectedAmount())) {
                selectedAmount = orderPrice.getSelectedAmount();
            }
        }
        heOrder.setGiftAmount(AmountChangeUtil.changeY2FFoInt(giftAmount));

        //客户
        HeOrderUserSnapshotEntity customer = bizContext.getAttribute(HeOrderUserSnapshotEntity.class);
        //扩展信息 定制处理。
        ExtraInfo extraInfo = req.getExtraInfo();
        if (ObjectUtil.isNotEmpty(extraInfo)) {
            orderFulfillExtraAssembler(heOrder, customer, extraInfo, req.getSkuList());
        }
        //商品
        List<SkuDetailInfo> skuList = bizContext.getListAttribute(SkuDetailInfo.class);

        List<HeOrderGoodsEntity> heOrderGoodsEntities = orderGoodsAssembler(skuList, heOrder);
        heOrder.setGoodsList(heOrderGoodsEntities);
        heOrder.setNeedSign();
        heOrder.setCustomer(customer);
        setOrderCustomerInfo(heOrder, customer);

        //附件
        List<HeOrderAttachmentEntity> attachmentEntityList = bizContext.getListAttribute(HeOrderAttachmentEntity.class);
        heOrder.setAttachmentList(attachmentEntityList);

        //特殊订单凭证
        List<HeOrderVoucherEntity> heOrderVoucherEntities = orderVoucherAssembler(req);
        heOrder.setVoucherEntityList(heOrderVoucherEntities);

        bizContext.setAttribute(HeOrderEntity.class, heOrder);
    }

    protected void initApprovalDiscountStatus(HeOrderEntity heOrder, DiscountRes discountRes) {
        Integer discountRuleResult = discountRes.getDiscountRuleResult();
        if (Objects.isNull(discountRuleResult)) {
            return;
        }
        if (discountRuleResult.equals(DiscountRuleResultEnum.APPROVAL_REQUIRED.getCode())) {
            heOrder.setApprovalDiscountStatus(ApprovalDiscountStatusEnum.APPROVING.getCode());
        } else {
            heOrder.setApprovalDiscountStatus(ApprovalDiscountStatusEnum.NO_APPROVAL_NEEDED.getCode());
        }
    }

    /**
     * 订单组装
     *
     * @param req
     * @return
     */
    protected HeOrderEntity orderAssembler(CreateOrderReq req) {
        HeOrderEntity heOrder = new HeOrderEntity();
        heOrder.setOrderType(req.getOrderType());
        heOrder.setOrderAmount(AmountChangeUtil.changeY2FFoInt(req.getOriginalPrice()));
        heOrder.setPayAmount(AmountChangeUtil.changeY2FFoInt(req.getPayAmount()));
        heOrder.setPaidAmount(0);
        heOrder.setRealAmount(0);
        heOrder.setProductionAmountPay(0);
        heOrder.setIsDelete(0);
        String orderSn = IdGenUtils.genOrderSn(heOrder.getOrderType(), new Date());
        heOrder.setOrderSn(orderSn);
        heOrder.setCreatedAt(System.currentTimeMillis() / 1000);
        heOrder.setUpdatedAt(System.currentTimeMillis() / 1000);
        heOrder.setStaffId(req.getStaffId());
        heOrder.setCreateBy(Long.valueOf(req.getOperator().getOperatorGuid()));
        heOrder.setStaffPhone(req.getStaffPhone());
        heOrder.setBasicUid(req.getBasicUid());
        heOrder.setClientUid(req.getClientUid());
        heOrder.setClientType(1);
        heOrder.setPayStatus(0);
        heOrder.setExtendsContent("");
        heOrder.setSignType(0);
        heOrder.setTaskId(0L);
        heOrder.setIsClose(0);
        heOrder.setPayStatus(0);
        heOrder.setStoreId(req.getStoreId());
        heOrder.setPayFirstTime(0);
        heOrder.setPercentFirstTime(0);
        heOrder.setUpdatedAt(DateUtils.getTenBitTimestamp());
        heOrder.setCreatedAt(DateUtils.getTenBitTimestamp());
        //通用订单
        String remark = "";
        Integer orderTag = 0;
        String orderTagName = "";
        String address = "";
        if (ObjectUtil.isNotNull(req.getExtraInfo())) {
            remark = req.getExtraInfo().getRemark();
            orderTag = req.getExtraInfo().getOrderTag();
            orderTagName = req.getExtraInfo().getOrderTagName();
            address = req.getExtraInfo().getAddress();
        }
        heOrder.setRemark(remark);
        heOrder.setOrderTag(orderTag);
        heOrder.setOrderTagName(orderTagName);
        heOrder.setAddress(address);
        heOrder.setIsNotice(1);
        heOrder.setProductionType(0);
        heOrder.setOldOrNew(3);
        heOrder.setVersion(new BigDecimal("3.00"));
        heOrder.setUpdateStaffId(heOrder.getStaffId());
        heOrder.setHolidayNum(0);

        heOrder.setOrderStatus(0);
        heOrder.setRefundStatus(0);
        heOrder.setScene(req.getScene());


        heOrder.setDiscountMargin(new BigDecimal(0));
        heOrder.setGrossMargin(new BigDecimal(0));
        heOrder.setNetMargin(new BigDecimal(0));
        //订单折扣详情
        heOrder.setDiscountDetails("");
        heOrder.setPromotionInfos(req.getPromotionInfos());

        String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(req.getStoreId());
        if (Strings.isNotBlank(storeCurrencyCode)) {
            storeCurrencyCode = storeCurrencyCode.trim();
        }
        heOrder.setCurrency(storeCurrencyCode);
        heOrder.setFxRate(StoreCurrencyContainer.Default_Currency.equals(storeCurrencyCode) ? BigDecimal.ONE : StoreCurrencyContainer.getStoreCurrencyFxRate(storeCurrencyCode, heOrder.getCreatedAt()));

        // 设置订单升级相关字段
        setUpgradeOrderFields(heOrder, req);

        return heOrder;
    }

    /**
     * 设置订单升级相关字段
     *
     * @param heOrder 订单实体
     * @param req     创建订单请求
     */
    protected void setUpgradeOrderFields(HeOrderEntity heOrder, CreateOrderReq req) {
        // 如果有购物车ID，尝试获取购物车信息并设置升级订单相关字段
        if (req.getCartId() != null) {
            try {
                QueryCartReq queryReq = new QueryCartReq();
                queryReq.setCartId(req.getCartId());
                queryReq.setScene(req.getScene());
                HeCartEntity cartEntity = heCartRepository.queryOne(queryReq);
                
                if (cartEntity != null) {
                    // 设置原订单ID和原商品总价
                    heOrder.setOriginalOrderId(cartEntity.getOriginalOrderId());
                    heOrder.setOriginalGoodsTotalPrice(cartEntity.getOriginalGoodsTotalPrice());
                    
                    log.info("OrderEntityForCartAssembler: 设置升级订单字段, cartId={}, originalOrderId={}, originalGoodsTotalPrice={}", 
                             req.getCartId(), cartEntity.getOriginalOrderId(), cartEntity.getOriginalGoodsTotalPrice());
                }
            } catch (Exception e) {
                log.error("OrderEntityForCartAssembler: 获取购物车信息失败, cartId={}", req.getCartId(), e);
            }
        }
    }

    /**
     * 设置省市区
     *
     * @param order
     * @param customer
     */
    protected void setOrderCustomerInfo(HeOrderEntity order, HeOrderUserSnapshotEntity customer) {
        // 注，原标准月子订单，id 没设置了，设置到了name
        order.setProvinceId(0);
        order.setCityId(0);
        order.setAreaId(0);

        order.setProvince(customer.getProvince() + "");
        order.setCity(customer.getCity() + "");
        order.setArea(customer.getRegion() + "");
    }


    /**
     * 设置折扣等指标
     *
     * @param order
     * @param index
     */
    protected void setOrderIndex(HeOrderEntity order, OrderIndex index) {

        OrderDiscountsCacheVO init = OrderDiscountsCacheVO.init();
        init.setCurrency(order.getCurrency());
        init
                .setOrderDiscount(index.getDiscountMargin().multiply(new BigDecimal(100)))
                .setNetDiscountRate(index.getNetMargin().multiply(new BigDecimal(100)))
                .setGrossProfitMargin(index.getGrossMargin().multiply(new BigDecimal(100)))
                .setAmountReceivablePackageOriginalPrice(new BigDecimal(0))
                .setAmountReceivablePackageCurrentPrice(new BigDecimal(0))
                .setAmountReceivableHolidaysOriginalPrice(new BigDecimal(0))
                .setAmountReceivableHolidaysCurrentPrice(new BigDecimal(0))
                .setAmountReceivableMultipleBirthsOriginalPrice(new BigDecimal(0))
                .setAmountReceivableMultipleBirthsCurrentPrice(new BigDecimal(0))
                .setAmountReceivableContinuedResidenceOriginalPrice(new BigDecimal(0))
                .setAmountReceivableContinuedResidenceCurrentPrice(new BigDecimal(0))
                .setChangeRoomTypeReceivableAmountOriginalPrice(new BigDecimal(0))
                .setChangeRoomTypeReceivableAmountCurrentPrice(new BigDecimal(0))

                .setOrderReceivableAmountOriginalPrice(new BigDecimal(0))
                .setOrderReceivableAmountCurrentPrice(new BigDecimal(0))
                .setTaxRate(order.getStore().getTaxRate())
                .setDeposit(new BigDecimal(0));

        order.setDiscountMargin(index.getDiscountMargin());
        order.setGrossMargin(index.getGrossMargin());
        order.setNetMargin(index.getNetMargin());
        order.setSignOrderDiscountMargin(index.getSignOrderDiscountMargin());
        //订单折扣详情
        order.setDiscountDetails("");
    }

    /**
     * 订单商品组装
     * 组合商品，节假日等附件项
     *
     * @param skuList
     * @param orderEntity
     * @return
     */
    protected List<HeOrderGoodsEntity> orderGoodsAssembler(List<SkuDetailInfo> skuList, HeOrderEntity orderEntity) {

        List<HeOrderGoodsEntity> goodsEntityList = new ArrayList<>();
        skuList.forEach(sku -> {
            HeOrderGoodsEntity goodsEntity = orderGoodsConverter.skuDetail2OrderGoods(sku);
            goodsEntity.setOrderGoodsSn(IdGenUtils.genOmniSn(BizConstant.OrderAppKey.OMNI_SN_PRE_KEY, null));
            //goodsEntity 查询goods表添加到商品里去
            setOrderSkuBasicsInfo(orderEntity, goodsEntity);
            initCoinRule(sku, goodsEntity);

            goodsEntityList.add(goodsEntity);
            //处理组合商品
            if (CompareUtil.integerEqual(CombineTypeEnum.COMBINE.code(), sku.getType())) {
                if (CollectionUtil.isNotEmpty(sku.getSubList())) {
                    //升序，这样最后一个商品做尾差处理。不会分到0的情况
                    sku.getSubList().sort(Comparator.comparing(SkuDetailInfo::getPrice));

                    for (SkuDetailInfo childSku : sku.getSubList()) {
                        HeOrderGoodsEntity childGoodsEntity = orderGoodsConverter.skuDetail2OrderGoods(childSku);
                        childGoodsEntity.setOrderGoodsSn(IdGenUtils.genOmniSn(BizConstant.OrderAppKey.OMNI_SN_PRE_KEY, null));
                        childGoodsEntity.setParentCombineSn(goodsEntity.getOrderGoodsSn());
                        childGoodsEntity.setType(CombineTypeEnum.COMBINE_SUB.code());
                        goodsEntityList.add(childGoodsEntity);
                        // additional 商品
                        List<HeOrderGoodsEntity> additionalEntityList = additionalGoods2OrderGoods(childGoodsEntity, childSku.getAdditionalList());
                        goodsEntityList.addAll(additionalEntityList);
                    }

                }
            } else {
                // 普通商品的 additional 商品
                List<HeOrderGoodsEntity> additionalEntityList = additionalGoods2OrderGoods(goodsEntity, sku.getAdditionalList());
                goodsEntityList.addAll(additionalEntityList);
            }

        });
        batchSetOrderSkuBasicsInfo(orderEntity, goodsEntityList);

        //处理商品扩展信息
        goodsEntityList.forEach(goodsEntity -> {
            Map<String, String> extra = new HashMap<>();
            if (ObjectUtil.isNotNull(goodsEntity.getExtraPrice())) {
                extra.put(BizConstant.ExtraKey.extraPriceKey, goodsEntity.getExtraPrice().toString());
            }
            if (Strings.isBlank(goodsEntity.getContent())) {
                goodsEntity.setContent(JSONUtil.toJsonStr(extra));
            }

        });

        //处理商品分摊
        GoodsAllocateUtil.processGoodsAllocation(orderEntity, goodsEntityList);

        return goodsEntityList;
    }

    /**
     * 节假日附加费 转 订单商品
     *
     * @param orderGoodsEntity
     * @param orderGoodsEntity
     */
    protected List<HeOrderGoodsEntity> additionalGoods2OrderGoods(HeOrderGoodsEntity orderGoodsEntity, List<SkuAdditionalInfo> additionalLite) {

        if (CollectionUtil.isEmpty(additionalLite)) {
            return new ArrayList<>();
        }
        List<HeOrderGoodsEntity> additionalEntityList = new ArrayList<>();
        additionalLite.forEach(additional -> {
            HeOrderGoodsEntity additionalGoodsEntity = orderGoodsConverter.additional2OrderGoods(additional);
            additionalGoodsEntity.setOrderGoodsSn(IdGenUtils.genOmniSn(BizConstant.OrderAppKey.OMNI_SN_PRE_KEY, null));
            additionalGoodsEntity.setParentCombineSn(orderGoodsEntity.getOrderGoodsSn());
            additionalGoodsEntity.setType(CombineTypeEnum.SIMPLE.code());
            additionalGoodsEntity.setGoodsId(0);
            additionalEntityList.add(additionalGoodsEntity);
        });
        return additionalEntityList;

    }

    /**
     * 订单商品基础信息 比如用户id,门店id 之类
     *
     * @param order
     * @param goodsEntityList
     */
    protected void batchSetOrderSkuBasicsInfo(HeOrderEntity order, List<HeOrderGoodsEntity> goodsEntityList) {
        if (CollectionUtil.isEmpty(goodsEntityList)) {
            return;
        }
        goodsEntityList.forEach(goodsEntity -> {
            setOrderSkuBasicsInfo(order, goodsEntity);
        });

    }


    /**
     * 订单商品基础信息 比如用户id,门店id 之类
     *
     * @param order
     * @param goodsEntity
     */
    protected void setOrderSkuBasicsInfo(HeOrderEntity order, HeOrderGoodsEntity goodsEntity) {
        goodsEntity.setBasicUid(order.getBasicUid());
        goodsEntity.setStoreId(order.getStoreId());
        goodsEntity.setClientUid(order.getClientUid());
        goodsEntity.setStaffId(order.getStaffId());
    }


    /**
     * 订单额外凭证表
     *
     * @param req
     * @return
     */
    protected List<HeOrderVoucherEntity> orderVoucherAssembler(CreateOrderReq req) {
        if (ObjectUtil.isNotNull(req.getExtraInfo()) && ObjectUtil.isNotEmpty(req.getExtraInfo().getVoucherUrlList())) {
            List<HeOrderVoucherEntity> heOrderVoucherEntityList = new ArrayList<>();
            req.getExtraInfo().getVoucherUrlList().forEach(url -> {
                HeOrderVoucherEntity heOrderVoucherEntity = new HeOrderVoucherEntity();
                heOrderVoucherEntity.setUrl(url);
                heOrderVoucherEntityList.add(heOrderVoucherEntity);
            });
            return heOrderVoucherEntityList;
        }

        return new ArrayList<>();
    }

    /**
     * 扩展信息组装 这里是硬写的，不好扩展
     * 部分要设置到订单里，部分设置到客户信息里
     *
     * @param order
     * @return
     */
    protected void orderFulfillExtraAssembler(HeOrderEntity order, HeOrderUserSnapshotEntity customer, ExtraInfo extraInfo, List<SkuDetailInfo> skuList) {
        List<CustomAttribute> fulfillExtraList = extraInfo.getFulfillExtraList();
        if (ObjectUtil.isEmpty(fulfillExtraList)) {
            return;
        }

        Map<String, CustomAttribute> fulfillExtraMap = fulfillExtraList.stream().collect(Collectors.toMap(CustomAttribute::getCode, Function.identity(), (item1, item2) -> item1));

        //预产期处理
        if (fulfillExtraMap.containsKey(OrderAdditionalKeyEnum.PREDICT_BORN_DATE.code())) {
            // 预产期
            CustomAttribute predictAttr = fulfillExtraMap.get(OrderAdditionalKeyEnum.PREDICT_BORN_DATE.code());
            Date predictDate = new Date(Long.parseLong(predictAttr.getValue()) * 1000);
            customer.setPredictBornDate(predictDate);
            order.setIsPreparePregnancy(0);
            if (DateUtil.year(predictDate) >= BizConstant.OrderAppKey.PreparationForPregnancyYearTag) {
                //备孕中
                order.setIsPreparePregnancy(1);
            } else if (!fulfillExtraMap.containsKey(OrderAdditionalKeyEnum.ESTIMATED_ASSIGNMENT.code())){

                int simpleSum = skuList.stream().filter(sku -> !CombineTypeEnum.COMBINE.code().equals(sku.getType()) && Objects.nonNull(sku.getGoodsType()) && (sku.getGoodsType() == 1 || sku.getGoodsType() == 0)).mapToInt(sub -> sub.getNum() * sub.getSkuNum()).sum();
                AtomicInteger combineSum = new AtomicInteger();
                skuList.stream().filter(sku -> CombineTypeEnum.COMBINE.code().equals(sku.getType())).forEach(sku -> {
                    for (SkuDetailInfo sub : sku.getSubList()) {
                        if (Objects.isNull(sub.getGoodsType())){
                            continue;
                        }
                        if (sub.getGoodsType() == 1 || sub.getGoodsType() == 0){
                            combineSum.addAndGet(sub.getNum() * sub.getSkuNum());
                        }
                    }
                });
                Integer addMount = simpleSum + combineSum.get() + 3;
                CustomAttribute customAttribute = new CustomAttribute();
                customAttribute.setPropertyId(1902622715459895298L);
                customAttribute.setPropertyName(OrderAdditionalKeyEnum.ESTIMATED_ASSIGNMENT.desc());
                customAttribute.setCode(OrderAdditionalKeyEnum.ESTIMATED_ASSIGNMENT.code());
                Date date = DateUtils.addTime(predictDate, addMount, Calendar.DAY_OF_YEAR);
                customAttribute.setValue(String.valueOf(date.getTime() / 1000));
                customAttribute.setShowStr(DateUtils.format(date, DateUtils.YYYY_MM_DD));
                fulfillExtraList.add(customAttribute);
                fulfillExtraMap.put(OrderAdditionalKeyEnum.ESTIMATED_ASSIGNMENT.code(), customAttribute);
            }
        }

        //入住日期
        Date wantInDate;
        boolean wantInDateNotnull = fulfillExtraMap.containsKey(OrderAdditionalKeyEnum.WANT_IN_DATE.code());
        if (wantInDateNotnull) {
            CustomAttribute wantInAttr = fulfillExtraMap.get(OrderAdditionalKeyEnum.WANT_IN_DATE.code());
            wantInDate = new Date(Long.parseLong(wantInAttr.getValue()) * 1000);
            if (DateUtil.year(wantInDate) >= BizConstant.OrderAppKey.PreparationForPregnancyYearTag) {
                //备孕中
                order.setIsPreparePregnancy(1);
                customer.setWantIn(null);
            } else {
                customer.setWantIn(wantInDate);
                if (!fulfillExtraMap.containsKey(OrderAdditionalKeyEnum.ESTIMATED_ASSIGNMENT.code())){
                    int simpleSum = skuList.stream().filter(sku -> !CombineTypeEnum.COMBINE.code().equals(sku.getType()) && Objects.nonNull(sku.getGoodsType()) && (sku.getGoodsType() == 1)).mapToInt(sub -> sub.getNum() * sub.getSkuNum()).sum();
                    AtomicInteger combineSum = new AtomicInteger();
                    skuList.stream().filter(sku -> CombineTypeEnum.COMBINE.code().equals(sku.getType())).forEach(sku -> {
                        for (SkuDetailInfo sub : sku.getSubList()) {
                            if (Objects.nonNull(sub.getGoodsType()) && sub.getGoodsType() == 1){
                                combineSum.addAndGet(sub.getNum() * sub.getSkuNum());
                            }
                        }
                    });
                    Integer addMount = simpleSum + combineSum.get();
                    CustomAttribute customAttribute = new CustomAttribute();
                    customAttribute.setPropertyId(1902622715459895298L);
                    customAttribute.setPropertyName(OrderAdditionalKeyEnum.ESTIMATED_ASSIGNMENT.desc());
                    customAttribute.setCode(OrderAdditionalKeyEnum.ESTIMATED_ASSIGNMENT.code());
                    Date date = DateUtils.addTime(wantInDate, addMount, Calendar.DAY_OF_YEAR);
                    customAttribute.setValue(String.valueOf(date.getTime() / 1000));
                    customAttribute.setShowStr(DateUtils.format(date, DateUtils.YYYY_MM_DD));
                    fulfillExtraList.add(customAttribute);
                    fulfillExtraMap.put(OrderAdditionalKeyEnum.ESTIMATED_ASSIGNMENT.code(), customAttribute);
                }
            }
            order.setWantIn(DateUtils.getTenBitTimestamp(wantInDate).longValue());
        }

        //胎数
        if (fulfillExtraMap.containsKey(OrderAdditionalKeyEnum.FETUS_NUM.code())) {
            CustomAttribute attr = fulfillExtraMap.get(OrderAdditionalKeyEnum.FETUS_NUM.code());
            customer.setFetusNum(Integer.valueOf(attr.getValue()));
        }

        // 自定义值的显示处理
        order.setExtraInfo(extraInfo);
    }

    /**
     * 产康金抵扣规则
     **/
    protected void initCoinRule(SkuDetailInfo sku, HeOrderGoodsEntity goodsEntity) {
        ProductionCoinRule coinRule = new ProductionCoinRule();
        if (CombineTypeEnum.SIMPLE.code().intValue() != goodsEntity.getType().intValue()) {
            coinRule.setType(-1);
            goodsEntity.setProductionDiscountRule(JSONUtil.toJsonStr(coinRule));
            return;
        }
        if (sku.getProduceAmountDeduction() == null || sku.getProduceAmountDeduction() == 0) {
            coinRule.setType(-1);
            goodsEntity.setProductionDiscountRule(JSONUtil.toJsonStr(coinRule));
            return;
        }
        switch (sku.getProductionDiscountRuleType()) {
            case 0:
                coinRule.setType(0);
                coinRule.setLimit(0);
                coinRule.setReduction(-1);
                break;
            case 1:
                // 目前这里固定，后需要调整从商品开始
                coinRule.setType(1);
                coinRule.setLimit(10000);
                coinRule.setReduction(2000);
                break;
            default:
                coinRule.setType(-1);
        }
        goodsEntity.setProductionDiscountRule(JSONUtil.toJsonStr(coinRule));
    }
}


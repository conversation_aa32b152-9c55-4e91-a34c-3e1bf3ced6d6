package com.stbella.order.server.context.component.processor.pay;

import cn.hutool.json.JSONUtil;
import com.stbella.core.enums.BusinessEnum;
import com.stbella.order.common.constant.RedisConstant;
import com.stbella.order.common.enums.core.IncomeRecordPayStatusEnum;
import com.stbella.order.common.enums.core.OfflineAuditStatusV2Enum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.month.PayChannelTypeEnum;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.service.OrderIncomeDomainService;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.order.month.request.pay.PayNotityRequest;
import com.stbella.order.server.order.month.service.OrderPayV2Service;
import com.stbella.order.server.order.pay.OfflinePayAuditV2Request;
import com.stbella.redisson.DistributedLocker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Sets;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.FlowIdentity;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-05-29  15:20
 * @Description: 线下支付回调
 */
@Slf4j
@Component
public class OfflineCallbackProcessor {

    @Resource
    OrderRepository orderRepository;
    @Resource
    private OrderPayV2Service orderPayV2Service;
    @Resource
    private OrderIncomeDomainService orderIncomeDomainService;
    @Resource
    private DistributedLocker redisson;
    @Resource
    IncomeRecordRepository incomeRecordRepository;



    public void run(OfflinePayAuditV2Request request) {


        RLock lock = redisson.lock(MessageFormat.format(RedisConstant.OFFLINE_PAY_CONFIRM_KEY, request.getIncomeId()));
        try {
            HeIncomeRecordEntity incomeRecord = orderIncomeDomainService.getWithAuthState(request.getIncomeId());
            if (OmniPayTypeEnum.OFFLINE.getCode().intValue() != incomeRecord.getPayType() ){
                log.info("未线下支付不处理 orderId={}, incomeId={}，参数：{}", incomeRecord.getOrderId(), incomeRecord.getId(), JSONUtil.toJsonStr(request));
                return;
            }
            Set<Integer> confirmStateSet = Sets.newHashSet(OfflineAuditStatusV2Enum.REVIEW_REJECTED.getCode(), OfflineAuditStatusV2Enum.EXAMINATION_PASSED.getCode());
            if (incomeRecord.isActualSuccess() || confirmStateSet.contains(incomeRecord.getApproveStatus()) ){
                log.info("线下支付审核通过 orderSn={}，参数：{}", incomeRecord.getOrderId());

                if (OfflineAuditStatusV2Enum.EXAMINATION_PASSED.getCode().intValue() == incomeRecord.getApproveStatus()){
                    log.info("线下支付审核通过，更新支付时间 orderSn={}, incomeSn={}", incomeRecord.getOrderId(), incomeRecord.getIncomeSn());
                    HeIncomeRecordEntity incomeUpdateEntity = new HeIncomeRecordEntity();
                    incomeUpdateEntity.setId(incomeRecord.getId());
                    incomeUpdateEntity.setPayTime(request.getIncomeFinanceTime());
                    incomeUpdateEntity.setUpdatedAt(DateUtils.getTenBitTimestamp());
                    incomeRecordRepository.updateRecord(incomeUpdateEntity);
                }

                return;
            }
            HeOrderEntity order = orderRepository.getByOrderId(incomeRecord.getOrderId());
            if (Objects.isNull(order.getBu())){
                order.setBu(BusinessEnum.CARE_CENTER.getCode());
            }
            BusinessEnum bu = BusinessEnum.getEnumByCode(order.getBu());
            OmniPayTypeEnum payTypeEnum = OmniPayTypeEnum.OFFLINE;
            if (order.isNewOrder()) {
                if (request.isAgree()){

                    FlowIdentity identity = FlowIdentity.builder().bizActivity(BizActivityEnum.SUCCEED_PAY.code())
                            .idSlice(bu.name())
                            .idSlice(payTypeEnum.name())
                            .build();

                    PayNotityRequest payNotityRequest =buildPayNotityRequest(request, order, incomeRecord);

                    log.info("线下支付审核通过 orderSn={}，参数：{}", order.getOrderSn(), JSONUtil.toJsonStr(payNotityRequest));

                    FlowContext context = new FlowContext();
                    context.setAttribute(PayNotityRequest.class, payNotityRequest);
                    context.setAttribute(OfflinePayAuditV2Request.class, request);
                    context.setAttribute(HeOrderEntity.class, order);
                    SnowballFlowLauncher.fire(identity, context);

                }else {
                    log.info("线下支付审核驳回 orderSn={}", order.getOrderSn());
                    FlowIdentity identity = FlowIdentity.builder().bizActivity(BizActivityEnum.FAILED_OFFLINE_PAY.code())
                            .idSlice(bu.name())
                            .idSlice(payTypeEnum.name())
                            .build();

                    FlowContext context = new FlowContext();
                    context.setAttribute(OfflinePayAuditV2Request.class, request);
                    context.setAttribute(HeOrderEntity.class, order);
                    SnowballFlowLauncher.fire(identity, context);

                }
            }else {
                log.info("老订单，走老流程 orderSn={}", order.getOrderSn());
                orderPayV2Service.offlinePayAuditFix(request);
            }
        } finally {
            if (lock != null && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    protected PayNotityRequest buildPayNotityRequest(OfflinePayAuditV2Request request, HeOrderEntity order, HeIncomeRecordEntity heIncomeRecordEntity) {
        PayNotityRequest payNotityRequest = new PayNotityRequest();
        payNotityRequest.setPayChannel(PayChannelTypeEnum.NATIVE.getCode());
        payNotityRequest.setOrderNo(order.getOrderSn());
        payNotityRequest.setOutTradeNo(heIncomeRecordEntity.getIncomeSn());
        payNotityRequest.setTransactionalNo(request.getTransactionalNo());
        payNotityRequest.setPayType(OmniPayTypeEnum.OFFLINE.getCode());
        payNotityRequest.setPayStatus(IncomeRecordPayStatusEnum.COMPLETE.getCode());
        payNotityRequest.setPayTime(new Date(request.getIncomeFinanceTime().longValue() * 1000));
        payNotityRequest.setPayAmount(AmountChangeUtil.changeF2Y(request.getIncomeFinance()));

        return payNotityRequest;
    }
}
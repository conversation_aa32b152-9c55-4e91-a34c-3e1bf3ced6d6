package com.stbella.order.server.context.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.core.result.Result;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.core.OmniRefundTypeEnum;
import com.stbella.order.common.enums.core.OrderRefundNatureEnum;
import com.stbella.order.common.enums.month.RefundRecordPayStatusEnum;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.service.OrderRefundDomainService;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.order.month.enums.RefundTypeEnum;
import com.stbella.order.server.order.month.req.SubmitRefundApplyV2Request;
import com.stbella.order.server.utils.OrderProcessCheckUtil;
import com.stbella.platform.order.api.refund.api.OrderRefundService;
import com.stbella.platform.order.api.refund.req.*;
import com.stbella.platform.order.api.refund.res.*;
import com.stbella.platform.order.api.req.CustomerComplaintsCreateReq;
import com.stbella.platform.order.api.res.CreateApproveRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.FlowIdentity;
import top.primecare.snowball.flow.core.definition.FlowIdentityBuilder;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@DubboService
public class OrderRefundServiceImpl implements OrderRefundService {

    @Resource
    private OrderRefundDomainService orderRefundDomainService;

    @Resource
    private OrderProcessCheckUtil orderProcessCheckUtil;

    @Resource
    private IncomeRecordRepository incomeRecordRepository;

    @Resource
    private OrderRefundRepository orderRefundRepository;

    @Resource
    private HeCustomerComplaintsRepository customerComplaintsRepository;

    @Resource
    private HeOrderRefundGoodsRepository orderRefundGoodsRepository;

    @Resource
    private OrderGoodsRepository orderGoodsRepository;

    @Override
    public Result<String> createSimpleReverseOrder(CreateSimpleReverseOrderReq req) {
        return null;
    }


    @Override
    public Result<QueryOrderRefundInfoRes> queryGoods(QueryRefundGoodsReq req) {
        // 业务线：场景：订单类型
        FlowIdentity identity = FlowIdentity.builder()
                .bizActivity(BizActivityEnum.ORDER_REFUND_GOODS_QUERY.code())
                .idSlice("Order")
                .idSlice("Refund")
                .idSlice("Goods")
                .build();

        FlowContext context = new FlowContext();
        context.setAttribute(req.getClass(), req);
        //默认流程一直走
        context.setAttribute(OtherConstant.CONTINUE, true);
        context.setAttribute(OtherConstant.ORDER_ID, req.getOrderId());
        SnowballFlowLauncher.fire(identity, context);
        QueryOrderRefundInfoRes orderRefundInfoRes = context.getAttribute(QueryOrderRefundInfoRes.class);
        return Result.success(orderRefundInfoRes);
    }

    @Override
    public Result<CreateApproveRes> create(CreateRefundReq req) {
        log.info("创建订单退款请求req:{}", JSONUtil.toJsonStr(req));

        // 检查是否存在未完成的退款流程
        if (orderProcessCheckUtil.hasUnfinishedRefund(req.getOrderId())) {
            String description = orderProcessCheckUtil.getUnfinishedProcessDescription(req.getOrderId());
            log.warn("发起退单失败，订单{}存在未完成的流程：{}", req.getOrderId(), description);
            return Result.failed("当前订单" + description + "，请等待处理完成后再次尝试！");
        }

        // 业务线：场景：订单类型
        FlowIdentityBuilder flowIdentityBuilder = FlowIdentity.builder()
                .bizActivity(BizActivityEnum.ORDER_REFUND_CREATE.code())
                .idSlice("Order")
                .idSlice("Refund")
                .idSlice("Create");
        FlowContext context = new FlowContext();
        context.setAttribute(req.getClass(), req);
        //默认流程一直走
        context.setAttribute(OtherConstant.CONTINUE, true);
        context.setAttribute(OtherConstant.ORDER_ID, req.getOrderId());
        CreateApproveRes createApproveRes = new CreateApproveRes();
        context.setAttribute(CreateApproveRes.class, createApproveRes);
        SnowballFlowLauncher.fire(flowIdentityBuilder.build(), context);
        createApproveRes = context.getAttribute(CreateApproveRes.class);
        return Result.success(createApproveRes);
    }

    @Override
    public Result<List<QueryOrderRefundGoodsInfoRes>> queryOrderGoodsWithRefundStatics(QueryOrderRefundGoodsInfoReq req) {
        List<QueryOrderRefundGoodsInfoRes> result = orderRefundDomainService.queryOrderGoodsWithRefundStatics(req.getOrderId());
        return Result.success(result);
    }

    @Override
    public Result<List<OrderRefundListRes>> refundListByOrderId(OrderRefundListReq req) {
        List<OrderRefundListRes> result = orderRefundDomainService.refundListByOrderId(req.getOrderId());
        return Result.success(result);
    }

    @Override
    public Result<OrderRefundDetailsRes> refundDetails(OrderRefundDetailsReq req) {
        OrderRefundDetailsRes orderRefundDetailsRes = orderRefundDomainService.refundDetails(req.getRefundId());
        return Result.success(orderRefundDetailsRes);
    }

    @Override
    public Result<List<OrderRefundProgressRes>> refundProgress(OrderRefundDetailsReq req) {
        List<OrderRefundProgressRes> result = orderRefundDomainService.refundProgress(req.getRefundId());
        return Result.success(result);
    }

    @Override
    public Result<OrderRefundProgressRes> refreshStatus(OrderRefundDetailsReq req) {
        OrderRefundProgressRes result = orderRefundDomainService.refreshStatus(req.getRefundId());
        return Result.success(result);
    }

    @Override
    public Result<CreateApproveRes> againRefund(AgainCreateRefundReq req) {
        CreateRefundReq createRefundReq = orderRefundDomainService.againRefund(req.getRefundId());
        createRefundReq.setOperator(req.getOperator());
        return create(createRefundReq);
    }

    @Override
    public Result<QueryEquallyGoodsRes> queryEquallyGoods(QueryQueryEquallyGoodsReq req) {
        QueryEquallyGoodsRes queryEquallyGoodsRes = orderRefundDomainService.queryEquallyGoods(req);
        return Result.success(queryEquallyGoodsRes);
    }

    @Override
    public Result<List<OrderRefundProgressRes>> complaintRefundProgress(ComplaintRefundDetailsReq req) {
        List<OrderRefundProgressRes> result = orderRefundDomainService.complaintRefundProgress(req.getComplaintId());
        return Result.success(result);
    }

    @Override
    public Result verify(CustomerComplaintsCreateReq customerComplaintsCreateReq) {
        log.info("订单退款校验请求req:{}", JSONUtil.toJsonStr(customerComplaintsCreateReq));
        // 业务线：场景：订单类型
        FlowIdentityBuilder flowIdentityBuilder = FlowIdentity.builder()
                .bizActivity(BizActivityEnum.ORDER_REFUND_VERIFY.code())
                .idSlice("Order")
                .idSlice("Refund")
                .idSlice("Verify");
        FlowContext context = new FlowContext();
        context.setAttribute(customerComplaintsCreateReq.getClass(), customerComplaintsCreateReq);

        SubmitRefundApplyV2Request oldCreateRefundReq = customerComplaintsCreateReq.getCreateRefundReq().getOldCreateRefundReq();
        if (ObjectUtil.isNotEmpty(oldCreateRefundReq)) {
            context.setAttribute(SubmitRefundApplyV2Request.class, oldCreateRefundReq);
        }

        CreateRefundReq newCreateRefundReq = customerComplaintsCreateReq.getCreateRefundReq().getNewCreateRefundReq();
        if (ObjectUtil.isNotEmpty(newCreateRefundReq)) {
            context.setAttribute(CreateRefundReq.class, newCreateRefundReq);
        }

        //默认流程一直走
        context.setAttribute(OtherConstant.CONTINUE, true);
        context.setAttribute(OtherConstant.ORDER_ID, customerComplaintsCreateReq.getOrderId());
        return Result.success();
    }


    @Override
    public Result<Integer> verifyOrderBalance(Integer orderId) {
        List<HeIncomeRecordEntity> allRecordListByOrderId = incomeRecordRepository.getAllRecordListByOrderId(orderId);
        //意向金支付的记录
        List<HeIncomeRecordEntity> balanceIncome = allRecordListByOrderId.stream().filter(a -> a.getPayType().equals(OmniPayTypeEnum.BALANCE.getCode()) && a.getStatus().equals(1)).collect(Collectors.toList());

        //全部意向金支付
        Integer allBalance = balanceIncome.stream().mapToInt(HeIncomeRecordEntity::getIncome).sum();

        List<Integer> refundIdList = balanceIncome.stream().map(HeIncomeRecordEntity::getId).collect(Collectors.toList());

        List<HeOrderRefundEntity> refundByOrderId = orderRefundRepository.getRefundByOrderId(orderId);
        refundByOrderId = refundByOrderId.stream().filter(r ->
                r.getStatus().equals(RefundRecordPayStatusEnum.REFUND_RECORD_1.getCode()) ||
                        r.getStatus().equals(RefundRecordPayStatusEnum.REFUND_RECORD_3.getCode()) ||
                        r.getStatus().equals(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode())
        ).collect(Collectors.toList());
        List<HeOrderRefundEntity> refundList = refundByOrderId.stream().filter(r -> refundIdList.contains(r.getOrderGoodId())).collect(Collectors.toList());

        int refundBalance = refundList.stream().mapToInt(HeOrderRefundEntity::getApplyAmount).sum();

        return Result.success(BooleanUtil.toInt(allBalance - refundBalance > 0));
    }

    @Override
    public Result<Integer> calAchievement(Integer parentRefundId) {
        HeCustomerComplaintsEntity customerComplaintsEntity = customerComplaintsRepository.selectByOrderRefundId(parentRefundId);
        if (ObjectUtil.isNotNull(customerComplaintsEntity)) {
            log.info("客诉退款，不在这里算业绩");
            //跟客诉无关联才扣业绩
            return Result.success(0);
        }
        HeOrderRefundEntity orderRefundEntity = orderRefundRepository.getOneById(parentRefundId);
        Integer refundAchievement = getRefundAchievement(orderRefundEntity);
        return Result.success(refundAchievement);
    }


    @Override
    public Result<Integer> calRefundAchievement(Integer parentRefundId) {
        HeOrderRefundEntity orderRefundEntity = orderRefundRepository.getOneById(parentRefundId);

        Integer refundAchievement = getRefundAchievement(orderRefundEntity);
        return Result.success(refundAchievement);
    }


    private Integer getRefundAchievement(HeOrderRefundEntity orderRefundEntity) {
        log.info("开始计算退款业绩，订单退款ID：{}", orderRefundEntity.getId());
        if (ObjectUtil.isNotNull(orderRefundEntity) && RefundTypeEnum.BACK_ACCOUNT.getCode().equals(orderRefundEntity.getRefundMethod())) {
            log.info("退回至意向金，退单业绩为0");
            return 0;
        }
        if (OrderRefundNatureEnum.TEMP_REFUND.code().equals(orderRefundEntity.getRefundNature())) {
            log.info("退回重付，退单业绩为0");
            return 0;
        }
        // 获取本次退款的商品列表
        List<HeOrderRefundGoodsEntity> currentRefundGoods = orderRefundGoodsRepository.queryByRefundOrderSn(orderRefundEntity.getRefundOrderSn());

        // 获取子项退款列表
        List<HeOrderRefundEntity> refundByParentSn = orderRefundRepository.getRefundByParentSn(orderRefundEntity.getRefundOrderSn());

        // 使用新的退款业绩计算方法
        Integer newAchievement = calculateRefundAchievementNew(orderRefundEntity, currentRefundGoods, refundByParentSn);


        log.info("退款业绩计算完成，新增业绩：{}", newAchievement);

        // 更新退款业绩
        Integer originalAchievement = orderRefundEntity.getRefundAchievement() != null ? orderRefundEntity.getRefundAchievement() : 0;

        log.info("退款业绩计算完成，订单退款ID：{}，原业绩：{}，新增业绩：{}，总业绩：{}",
                orderRefundEntity.getId(), originalAchievement, newAchievement, orderRefundEntity.getRefundAchievement());
        return newAchievement;
    }

    /**
     * 新的退款业绩计算方法
     * 计算公式：本次退款的所有商品的本次数量*商品分摊单价-本次所有商品的产康金退款金额
     *
     * @param orderRefundEntity          退款实体
     * @param orderRefundGoodsEntityList 本次退款的商品列表
     * @param refundByParentSn           子项退款列表
     * @return 退款业绩金额（分）
     */
    public Integer calculateRefundAchievementNew(HeOrderRefundEntity orderRefundEntity,
                                                 List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList,
                                                 List<HeOrderRefundEntity> refundByParentSn) {
        try {
            // 1. 计算本次退款的所有商品的数量*商品分摊单价
            BigDecimal totalGoodsAmount = calculateTotalGoodsRefundAmount(orderRefundGoodsEntityList);

            // 2. 计算本次所有商品的产康金退款金额
            BigDecimal totalProductionCoinAmount = calculateTotalProductionCoinRefundAmount(refundByParentSn);

            // 3. 计算退款业绩 = 商品退款总额 - 产康金退款金额
            BigDecimal refundAchievement = totalGoodsAmount.subtract(totalProductionCoinAmount);

            // 4. 确保结果不为负数
            if (refundAchievement.compareTo(BigDecimal.ZERO) < 0) {
                refundAchievement = BigDecimal.ZERO;
                log.warn("退款业绩计算结果为负数，已调整为0。订单退款ID：{}，商品退款总额：{}，产康金退款：{}",
                        orderRefundEntity.getId(), totalGoodsAmount, totalProductionCoinAmount);
            }

            // 5. 转换为分并返回
            Integer result = refundAchievement.setScale(0, RoundingMode.HALF_UP).intValue();

            log.info("新退款业绩计算完成，订单退款ID：{}，商品退款总额：{}，产康金退款：{}，最终业绩：{}",
                    orderRefundEntity.getId(), totalGoodsAmount, totalProductionCoinAmount, result);

            return result;

        } catch (Exception e) {
            log.error("计算新退款业绩失败，订单退款ID：{}", orderRefundEntity.getId(), e);
            return 0;
        }
    }

    /**
     * 计算本次退款的所有商品的数量*商品分摊单价总额
     *
     * @param orderRefundGoodsEntityList 本次退款的商品列表
     * @return 商品退款总额（元）
     */
    private BigDecimal calculateTotalGoodsRefundAmount(List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList) {
        if (CollectionUtil.isEmpty(orderRefundGoodsEntityList)) {
            return BigDecimal.ZERO;
        }

        // 获取订单ID（从第一个退款商品中获取）
        Integer orderId = orderRefundGoodsEntityList.get(0).getOrderId().intValue();

        // 查询该订单的所有商品信息
        List<HeOrderGoodsEntity> allOrderGoods = orderGoodsRepository.getAllItermByOrderId(orderId);
        if (CollectionUtil.isEmpty(allOrderGoods)) {
            log.warn("未找到订单商品信息，orderId={}", orderId);
            return BigDecimal.ZERO;
        }

        // 按 orderGoodsSn 分组，相同商品只取第一条记录的退款数量，避免重复计算
        Map<String, Integer> goodsRefundQuantityMap = orderRefundGoodsEntityList.stream()
                .filter(refundGoods -> refundGoods.getRefundNum() != null && refundGoods.getRefundNum() > 0)
                .collect(Collectors.groupingBy(
                        HeOrderRefundGoodsEntity::getOrderGoodsSn,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.get(0).getRefundNum() // 只取第一条记录的退款数量
                        )
                ));

        if (goodsRefundQuantityMap.isEmpty()) {
            log.info("没有有效的退款商品数量");
            return BigDecimal.ZERO;
        }

        BigDecimal totalAmount = BigDecimal.ZERO;

        for (Map.Entry<String, Integer> entry : goodsRefundQuantityMap.entrySet()) {
            String orderGoodsSn = entry.getKey();
            Integer totalRefundNum = entry.getValue();

            // 根据 orderGoodsSn 查找对应的订单商品信息
            HeOrderGoodsEntity orderGoods = allOrderGoods.stream()
                    .filter(goods -> goods.getOrderGoodsSn().equals(orderGoodsSn))
                    .findFirst()
                    .orElse(null);

            if (orderGoods == null) {
                log.warn("未找到订单商品信息，orderGoodsSn={}", orderGoodsSn);
                continue;
            }

            // 获取商品应付分摊单价（分）
            Integer allocationUnitPrice = orderGoods.getAllocationOriginPrice();
            if (allocationUnitPrice == null) {
                allocationUnitPrice = 0;
                log.warn("商品应付分摊单价为空，orderGoodsSn={}", orderGoodsSn);
            }

            // 计算该商品的退款金额：合并后的退款数量 * 应付分摊单价（保持分为单位）
            BigDecimal goodsRefundAmount = new BigDecimal(totalRefundNum)
                    .multiply(new BigDecimal(allocationUnitPrice));

            totalAmount = totalAmount.add(goodsRefundAmount);

            log.info("商品退款金额计算：orderGoodsSn={}, 退款数量={}, 应付分摊单价={}分, 退款金额={}分",
                    orderGoodsSn, totalRefundNum, allocationUnitPrice, goodsRefundAmount);
        }

        log.info("商品退款总额计算完成：{}分，处理商品种类数：{}", totalAmount, goodsRefundQuantityMap.size());
        return totalAmount;
    }

    /**
     * 计算本次所有商品的产康金退款金额
     *
     * @param refundByParentSn 子项退款列表
     * @return 产康金退款总额（元）
     */
    private BigDecimal calculateTotalProductionCoinRefundAmount(List<HeOrderRefundEntity> refundByParentSn) {
        if (CollectionUtil.isEmpty(refundByParentSn)) {
            return BigDecimal.ZERO;
        }

        // 筛选出产康金类型的退款记录
        List<HeOrderRefundEntity> productionCoinRefunds = refundByParentSn.stream()
                .filter(refund -> refund.getRefundType() != null)
                .filter(refund -> refund.getRefundType().equals(OmniPayTypeEnum.PRODUCTION_COIN.getCode()))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(productionCoinRefunds)) {
            log.info("本次退款无产康金退款记录");
            return BigDecimal.ZERO;
        }

        // 计算产康金退款总额
        BigDecimal totalProductionCoinAmount = productionCoinRefunds.stream()
                .filter(refund -> refund.getApplyAmount() != null)
                .map(refund -> new BigDecimal(refund.getApplyAmount())) // 分转元
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        log.info("产康金退款总额计算完成：{}元，退款记录数：{}", totalProductionCoinAmount, productionCoinRefunds.size());
        return totalProductionCoinAmount;
    }


}

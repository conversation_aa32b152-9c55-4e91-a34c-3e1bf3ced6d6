package com.stbella.order.server.order.nutrition.controller;

import cn.hutool.core.date.DateUtil;
import com.stbella.core.base.PageVO;
import com.stbella.core.result.Result;
import com.stbella.order.server.order.nutrition.request.*;
import com.stbella.order.server.order.nutrition.response.CommodityAnalysisVO;
import com.stbella.order.server.order.nutrition.response.NutritionOnlineRetailersVO;
import com.stbella.order.server.order.nutrition.response.NutritionSuiteListVO;
import com.stbella.order.server.order.nutrition.response.OnlineRetailergetSkuByGoodsIdListVO;
import com.stbella.order.server.order.nutrition.service.OrderNutritionGoodsService;
import com.stbella.order.server.order.nutrition.service.WdtSkuService;
import com.stbella.order.server.order.nutrition.service.WdtSpuService;
import com.stbella.order.server.order.nutrition.service.WdtSuiteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

@Validated
@Api(tags = "电商商品中心")
@RestController
@RequestMapping("/order/nutrition/goods")
public class NutritionGoodsController {

    @Resource
    private WdtSkuService wdtSkuService;
    @Resource
    private WdtSpuService wdtSpuService;
    @Resource
    private WdtSuiteService wdtSuiteService;
    @Resource
    private OrderNutritionGoodsService orderNutritionGoodsService;

    @ApiOperation(value = "获取商品列表")
    @GetMapping("/onlineRetailers/get/spu")
    public Result<PageVO<NutritionOnlineRetailersVO>> onlineRetailersGetSpu(@Validated NutritionOnlineRetailersRequest nutritionOnlineRetailersRequest) {
        return Result.success(wdtSpuService.onlineRetailersGetSpu(nutritionOnlineRetailersRequest));
    }

    @ApiOperation(value = "获取商品详情（SKU列表）")
    @PostMapping("/onlineRetailers/get/skuByGoodsId")
    public Result<List<OnlineRetailergetSkuByGoodsIdListVO>> onlineRetailersGetSkuByGoodsId(@RequestBody @Validated GetSkuByGoodsIdsRequest getSkuByGoodsIdsRequest) {
        return Result.success(wdtSkuService.onlineRetailersGetSkuByGoodsId(getSkuByGoodsIdsRequest));
    }

    @ApiOperation(value = "修改商品sku数据（成本价、税率）")
    @PostMapping("/update/sku")
    public Result updateSku(@RequestBody @Validated OrderNutritionUpdateSkuRequest orderNutritionUpdateSkuRequest) {
        wdtSkuService.updateSku(orderNutritionUpdateSkuRequest.getUpdateList());
        return Result.success();
    }

    @ApiOperation(value = "获取组合装商品列表")
    @GetMapping("/suite/list")
    public Result<PageVO<NutritionSuiteListVO>> suiteList(@Validated NutritionSuiteRequest nutritionSuiteRequest) {
        return Result.success(wdtSuiteService.suiteList(nutritionSuiteRequest));
    }


    @ApiOperation(value = "商品分析")
    @GetMapping("/goods/commodityAnalysis")
    public Result<CommodityAnalysisVO> commodityAnalysis(@Valid TransactionAnalysisRequest transactionAnalysisRequest) {
        return Result.success(wdtSuiteService.commodityAnalysis(transactionAnalysisRequest));
    }


    @ApiOperation(value = "手动同步旺店通商品的数据")
    @GetMapping("/getSpuList")
    public Result getSpuList(String startStr, Integer i) {
        Date start = DateUtil.parse(startStr);
        Date end = DateUtil.offsetDay(start, i);
        while (end.before(new Date())) {
            wdtSpuService.getSpuList(start, end);
            start = end;
            end = DateUtil.offsetDay(start, i);
        }
        return Result.success();
    }

    @ApiOperation(value = "手动同步旺店通组合装的数据")
    @GetMapping("/getSuiteList")
    public Result getSuiteList(String startStr, Integer i) {
        Date start = DateUtil.parse(startStr);
        Date end = DateUtil.offsetDay(start, i);
        while (end.before(new Date())) {
            wdtSuiteService.getSuiteList(start, end);
            start = end;
            end = DateUtil.offsetDay(start, i);
        }
        return Result.success();
    }
}

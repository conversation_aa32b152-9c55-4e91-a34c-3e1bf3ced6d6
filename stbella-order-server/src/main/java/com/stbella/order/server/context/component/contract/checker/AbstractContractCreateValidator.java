package com.stbella.order.server.context.component.contract.checker;

import com.stbella.contract.model.enums.ContractTypeEnum;
import com.stbella.contract.model.enums.OrderSignTypeEnum;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.enums.month.ContractStatusEnum;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.HeOrderBailorSnapshotEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.server.order.month.enums.ApprovalDiscountStatusEnum;
import com.stbella.order.server.order.month.enums.CardVerifyEnum;
import com.stbella.platform.order.api.contract.req.ContractBaseReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

@Slf4j
public abstract class AbstractContractCreateValidator {

    /**
     * 合同创建校验
     *
     * @param context
     * @return
     */
    public boolean validate(ContractValidationContext context) {

        HeOrderEntity orderEntity = context.getOrderEntity();
        Optional.ofNullable(orderEntity).orElseThrow(() -> new BusinessException(ResultEnum.PARAM_ERROR, "订单不存在"));
        //TODO 暂时解决手签合同问题
        if (orderEntity.isNewOrder() && !checkContractMain(context.getOrderEntity(), context.getHeOrderBailorSnapshotEntity(), context.getHeOrderUserSnapshotEntity())) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "请先完成合同主体认证");
        }

        checkApprovalDiscountStatus(orderEntity, context.getRequest());

        CfgStoreEntity cfgStore = context.getCfgStore();
        Optional.ofNullable(cfgStore).orElseThrow(() -> new BusinessException(ResultEnum.PARAM_ERROR, "门店不存在"));

        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = context.getHeOrderUserSnapshotEntity();
        Optional.ofNullable(heOrderUserSnapshotEntity).orElseThrow(() -> new BusinessException(ResultEnum.PARAM_ERROR, "订单用户信息不存在"));

        if (checkCurrentContractTemplateTypeSigned()) {
            currentContractTemplateTypeSigned(context);
        }

        boolean validate = doValidate(context);
        log.info("订单id {},合同创建校验结果:{}", context.getRequest().getOrderId(), validate);
        return validate;
    }

    protected boolean checkCurrentContractTemplateTypeSigned() {
        return true;
    }

    public abstract boolean doValidate(ContractValidationContext context);


    public abstract TemplateContractTypeEnum templateContractType();

    private boolean checkContractMain(HeOrderEntity orderEntity, HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity, HeOrderUserSnapshotEntity heOrderUserSnapshotEntity) {

        if (Objects.isNull(orderEntity.getSignType())) {
            return false;
        }
        if (!Arrays.asList(OrderSignTypeEnum.SIGN_TYPE_CLIENT.code(), OrderSignTypeEnum.SIGN_TYPE_BAILOR.code()).contains(orderEntity.getSignType())) {
            return false;
        }
        if (OrderSignTypeEnum.SIGN_TYPE_CLIENT.code().equals(orderEntity.getSignType())) {
            if (Objects.isNull(heOrderUserSnapshotEntity)) {
                return false;
            }

            if (CardVerifyEnum.successVerify(heOrderUserSnapshotEntity.getIsCardVerify())) {
                return Boolean.TRUE;
            }

            if (Objects.isNull(heOrderUserSnapshotEntity.getAuthType())) {
                return false;
            }

            if (heOrderUserSnapshotEntity.getAuthType() == 0 && heOrderUserSnapshotEntity.getIsCardVerify() == 2) {
                return true;
            }

            if (heOrderUserSnapshotEntity.getAuthType() == 1 && StringUtils.isNotEmpty(heOrderUserSnapshotEntity.getEmail())) {
                return true;
            }
        } else {
            if (Objects.isNull(heOrderBailorSnapshotEntity)) {
                return false;
            }

            if (CardVerifyEnum.successVerify(heOrderBailorSnapshotEntity.getIsCardVerify())) {
                return Boolean.TRUE;
            }

            if (Objects.isNull(heOrderBailorSnapshotEntity.getAuthType())) {
                return false;
            }

            if (heOrderBailorSnapshotEntity.getAuthType() == 0 && heOrderBailorSnapshotEntity.getIsCardVerify() == 2) {
                return true;
            }
            if (heOrderBailorSnapshotEntity.getAuthType() == 1 && StringUtils.isNotEmpty(heOrderBailorSnapshotEntity.getEmail())) {
                return true;
            }
        }
        return false;
    }


    /**
     * 验证主合同是否已签署
     *
     * @param context
     */
    public void validateMainContractSigned(ContractValidationContext context) {
        boolean mainContractSigned = context.getSignRecords().stream().anyMatch(contract -> Objects.equals(contract.getTemplateContractType(), TemplateContractTypeEnum.YZ_SAINTBELLA.code()) && Objects.equals(contract.getContractStatus(), ContractStatusEnum.SIGNED.code()));

        if (!mainContractSigned) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "请先签署订单主合同");
        }
    }

    /**
     * 验证当前合同模板类型是否已签署
     *
     * @param context
     */
    public void currentContractTemplateTypeSigned(ContractValidationContext context) {
        boolean mainContractSigned = context.getSignRecords().stream().anyMatch(contract -> Objects.equals(contract.getTemplateContractType(), context.getRequest().getTemplateContractType()) && Objects.equals(contract.getContractStatus(), ContractStatusEnum.SIGNED.code()));

        if (mainContractSigned) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "当前合同已签署");
        }
    }

    public void checkPaperContract(ContractValidationContext context) {
        boolean isPaperExists = context.getSignRecords().stream().anyMatch(contract -> Objects.equals(contract.getTemplateContractType(), context.getRequest().getTemplateContractType()) && ContractTypeEnum.PAPER_TYPE.code().equals(contract.getContractType()));
        if (isPaperExists) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "您已提交/已签订纸质合同，无需再次签订");
        }
    }

    private void checkApprovalDiscountStatus(HeOrderEntity orderEntity, ContractBaseReq req) {
        ApprovalDiscountStatusEnum approvalDiscountStatus = ApprovalDiscountStatusEnum.getEnumByCode(orderEntity.getApprovalDiscountStatus());
        Integer templateContractType = req.getTemplateContractType();
        Integer contractType = req.getContractType();
        switch (approvalDiscountStatus) {
            case APPROVING:
                handleApprovingStatus(templateContractType, contractType);
                break;
            case APPROVAL_FAILED:
                handleApprovalFailedStatus(templateContractType);
                break;
            case INITIATION_FAILED:
                handleInitiationFailedStatus(templateContractType);
                break;
        }
    }

    private void handleApprovingStatus(Integer templateContractType, Integer contractType) {
        if (isYzSaintBellaOrNurse(templateContractType)) {
            if (isOldContract(contractType)) {
                throw new BusinessException(ResultEnum.ERROR_AND_SHOW.getCode(), "重要提醒", "订单优惠审批当前仍在审批中，请留意审批结果，审批通过后请签订《母婴护理服务合同书》否则将无法报单和计入门店业绩。");
            } else {
                throw new BusinessException(ResultEnum.ERROR_AND_SHOW.getCode(), "警示提示", "订单优惠审批当前仍在审批中，可先签订预约协议收取意向金，审批通过方可签署主合同并收取全款");
            }
        } else if (isYzSmall(templateContractType)) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "订单优惠审批中，审批通过方可签署合同");
        }
    }

    private void handleApprovalFailedStatus(Integer templateContractType) {
        if (isYzSaintBellaOrNurseOrAppointment(templateContractType)) {
            throw new BusinessException(ResultEnum.ERROR_AND_SHOW.getCode(), "警示提示", "您发起的订单优惠审批未通过，请前往修改合同！");
        } else if (isYzSmall(templateContractType)) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "订单优惠审批未通过，请重新创建订单！");
        }
    }

    private void handleInitiationFailedStatus(Integer templateContractType) {
        if (isYzSaintBellaOrNurseOrAppointment(templateContractType)) {
            throw new BusinessException(ResultEnum.ERROR_AND_SHOW.getCode(), "警示提示", "您发起的订单优惠审批未通过/发起失败，请前往修改合同后再次尝试！");
        } else if (isYzSmall(templateContractType)) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "订单优惠审批未通过/发起失败，请重新创建订单！");
        }
    }

    private boolean isYzSaintBellaOrNurse(Integer templateContractType) {
        return Arrays.asList(com.stbella.contract.model.enums.TemplateContractTypeEnum.YZ_SAINTBELLA.code(), com.stbella.contract.model.enums.TemplateContractTypeEnum.YZ_NURSE.code()).contains(templateContractType);
    }

    private boolean isYzSaintBellaOrNurseOrAppointment(Integer templateContractType) {
        return Arrays.asList(com.stbella.contract.model.enums.TemplateContractTypeEnum.YZ_SAINTBELLA.code(), com.stbella.contract.model.enums.TemplateContractTypeEnum.YZ_NURSE.code(), com.stbella.contract.model.enums.TemplateContractTypeEnum.APPOINTMENT.code()).contains(templateContractType);
    }

    private boolean isYzSmall(Integer templateContractType) {
        return com.stbella.contract.model.enums.TemplateContractTypeEnum.YZ_SMALL.code().equals(templateContractType);
    }

    private boolean isOldContract(Integer contractType) {
        return contractType.equals(2);
    }


    protected boolean matchValidator(TemplateContractTypeEnum templateContractTypeEnum) {
        return templateContractType().equals(templateContractTypeEnum);
    }
}

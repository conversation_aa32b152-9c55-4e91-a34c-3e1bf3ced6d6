package com.stbella.order.server.context.component.contract.stage;

import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ProjectName stbella-contract
 * @PackageName com.stbella.contract.application.context.component.strategy
 * @ClassName ContractFillContext
 * @Description 合同填充上下文 用于路由
 * <AUTHOR>
 * @CreateDate 2024-03-26 13:10
 * @Version 1.0
 */
@Component
public class ContractFillContext {

    /**
     * 接口注入器
     */
    @Autowired
    List<ContractFillStrategy> contractFillStrategyList;


    /**
     * @param contractParamFillContext
     * @return {@link ContractFillStrategy}
     */
    public ContractFillStrategy getFillStrategy(ContractParamFillContext contractParamFillContext) {
        for (ContractFillStrategy fillStrategy : contractFillStrategyList) {
            if (fillStrategy.filter(contractParamFillContext.getRequest().getTemplateContractType())) {
                return fillStrategy;
            }
        }
        throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, "没有找到可用的模板templateContractType");
    }

}
package com.stbella.order.server.context.component.processor.pay;

import com.stbella.order.common.enums.core.IncomeRecordPayStatusEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.domain.utils.PayTypeConvertUtil;
import com.stbella.order.server.order.pay.OfflinePayAuditV2Request;
import com.stbella.pay.server.enums.PayTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-05-29  15:20
 * @Description: 驳回线下支付
 */
@Slf4j
@Component
@SnowballComponent(name = "驳回线下支付", desc = "驳回线下支付")
public class FailedOrderTransactionProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private IncomeRecordRepository incomeRecordRepository;

    @Override
    public void run(FlowContext bizContext) {

        OfflinePayAuditV2Request offlinePayAuditV2Request = bizContext.getAttribute(OfflinePayAuditV2Request.class);
        HeIncomeRecordEntity heIncomeRecordEntity = incomeRecordRepository.getOneById(offlinePayAuditV2Request.getIncomeId());
        heIncomeRecordEntity.setParams("");
        heIncomeRecordEntity.setStatus(IncomeRecordPayStatusEnum.Failed.getCode());
        heIncomeRecordEntity.setPayTime(0);
        //新payType转为旧payType
        heIncomeRecordEntity.setPayType(OmniPayTypeEnum.OFFLINE.getCode());
        heIncomeRecordEntity.setTransactionId("");
        heIncomeRecordEntity.setUpdatedAt(DateUtils.getTenBitTimestamp(new Date()).longValue());
        incomeRecordRepository.updateRecord(heIncomeRecordEntity);

        bizContext.setAttribute(HeIncomeRecordEntity.class, heIncomeRecordEntity);
    }


}

package com.stbella.order.server.order.job;

import com.stbella.core.utils.TraceIdUtils;
import com.stbella.order.server.listener.ExportMonthOrderItermListener;
import com.stbella.order.server.listener.event.ExportAllCtsOrderIdsEvent;
import com.stbella.order.server.listener.event.ExportMonthOrderItermEvent;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Calendar;

/**
 * @Author: jijunjian
 * @CreateTime: 2023-04-21  16:49
 * @Description: 导出母婴月订单明细
 */
@Component
@Slf4j
public class ExportMonthOrderItermHandler {

    @Resource
    private ApplicationContext applicationContext;

    /**
     * 导出所有予家订单id
     */
    @XxlJob("exportMonthOrderItermHandler")
    public void exportMonthOrderIterm() {
        TraceIdUtils.resetTraceId();
        log.info("开始导出月子订单明细");
        ExportMonthOrderItermEvent event = new ExportMonthOrderItermEvent();
        event.setEventTime(System.currentTimeMillis()/1000);
        event.setSource("job");
        // 开始时间上个月第一天
        Calendar lastMonthFirstDateCal = Calendar.getInstance();
        lastMonthFirstDateCal.add(Calendar.MONTH, -1);
        lastMonthFirstDateCal.set(Calendar.DAY_OF_MONTH, 1);
        event.setStartTime(lastMonthFirstDateCal.getTime().getTime()/1000);
        // 结束时间当月第一天
        Calendar thisMonthFirstDateCal = Calendar.getInstance();
        thisMonthFirstDateCal.set(Calendar.DAY_OF_MONTH, 1);
        event.setEndTime(thisMonthFirstDateCal.getTime().getTime()/1000);


        applicationContext.publishEvent(event);

    }
}

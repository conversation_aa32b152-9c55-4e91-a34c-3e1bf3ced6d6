package com.stbella.order.server.context.component.spi;

import cn.hutool.json.JSONObject;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.server.context.component.refund.processor.OrderRefundProcessor;
import top.primecare.snowball.extpoint.annotation.FuncPointSpi;
import top.primecare.snowball.extpoint.definition.IFuncSpiSupport;

@FuncPointSpi(name = "退款扩展点", attachToComponent = OrderRefundProcessor.class)
public interface RefundSpi extends IFuncSpiSupport<HeOrderRefundEntity, JSONObject> {

}

package com.stbella.order.server.context.component.processor;

import com.stbella.mail.MailUtils;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.platform.order.api.refund.req.CreateSimpleReverseOrderReq;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-03-26  13:55
 * @Description: 简单反向订单邮件通知
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "简单反向订单邮件通知")
public class SimpleReverseOrderEmailNotifyProcessor implements IExecutableAtom<FlowContext> {

    @Autowired
    OrderRepository orderRepository;
    @Resource
    RuleLinkClient ruleLinkClient;
    @Resource
    private MailUtils mailUtils;

    @Override
    public void run(FlowContext bizContext) {

        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        CreateSimpleReverseOrderReq req = bizContext.getAttribute(CreateSimpleReverseOrderReq.class);

        Map<String, String> factMap = new HashMap<>();
        factMap.put("configCode","simple_reverse_order_email");
        ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req("systemConfig", factMap);

        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
        // simpleRuleValue 按逗号分成数组
        String[] to = hitRuleVo.getSimpleRuleValue().split(",");
        String subject = "月子逆向订单创建通知";
        String content = req.getOperator().getOperatorName() + "创建了负向订单，原单号["+req.getParentOrderNo()+"]，子单号为["+orderEntity.getOrderSn()+"], 金额为["+req.getOrderAmount()+"]元";

        mailUtils.sendSimpleMail(to, subject, content);

    }
}

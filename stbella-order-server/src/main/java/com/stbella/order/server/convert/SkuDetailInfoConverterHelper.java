package com.stbella.order.server.convert;

import cn.hutool.json.JSONUtil;
import com.stbella.store.goodz.res.GoodsPropertyVO;
import com.stbella.store.goodz.res.PropertyValueListVO;
import com.stbella.store.goodz.res.SkuVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * SKU详情信息转换器辅助类
 * 处理复杂的转换逻辑
 */
@Slf4j
@Component
public class SkuDetailInfoConverterHelper {

    /**
     * 将SKU的属性值转换为JSON字符串
     *
     * @param sku SKU信息
     * @return JSON字符串
     */
    public static String convertPropertyValueToJson(SkuVo sku) {
        if (sku == null) {
            return "[]";
        }

        try {
            List<GoodsPropertyVO> goodsPropertyVOList = convertPropertyValueListVOS(sku);
            return JSONUtil.toJsonStr(goodsPropertyVOList);
        } catch (Exception e) {
            log.error("转换SKU属性值为JSON失败, skuId: {}", sku.getId(), e);
            return "[]";
        }
    }

    /**
     * 转换SKU属性值列表为商品属性VO列表
     *
     * @param sku SKU信息
     * @return 商品属性VO列表
     */
    private static List<GoodsPropertyVO> convertPropertyValueListVOS(SkuVo sku) {
        if (sku == null) {
            return Collections.emptyList();
        }

        List<PropertyValueListVO> propertyValueListVOList = sku.getPropertyValueListVOList();
        if (CollectionUtils.isEmpty(propertyValueListVOList)) {
            return Collections.emptyList();
        }

        return propertyValueListVOList.stream().filter(Objects::nonNull).map(SkuDetailInfoConverterHelper::convertToGoodsPropertyVO).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 转换单个属性值为商品属性VO
     *
     * @param propertyValueListVO 属性值列表VO
     * @return 商品属性VO
     */
    private static GoodsPropertyVO convertToGoodsPropertyVO(PropertyValueListVO propertyValueListVO) {
        if (propertyValueListVO == null) {
            return null;
        }

        try {
            GoodsPropertyVO goodsPropertyVO = new GoodsPropertyVO();
            goodsPropertyVO.setPropertyId(propertyValueListVO.getPropertyId());
            goodsPropertyVO.setPropertyName(propertyValueListVO.getPropertyName());
            goodsPropertyVO.setPropertyCode(propertyValueListVO.getCode());

            // 转换属性详情
            List<GoodsPropertyVO.GoodsPropertyDetailsVO> detailsList = convertPropertyDetails(propertyValueListVO.getPropertyValueId(), propertyValueListVO.getPropertyValue());
            goodsPropertyVO.setGoodsPropertyDetailsVOS(detailsList);

            return goodsPropertyVO;
        } catch (Exception e) {
            log.error("转换属性值VO失败: {}", propertyValueListVO, e);
            return null;
        }
    }

    /**
     * 转换属性详情列表
     *
     * @param propertyValueIds 属性值ID列表
     * @param propertyValues   属性值列表
     * @return 属性详情VO列表
     */
    private static List<GoodsPropertyVO.GoodsPropertyDetailsVO> convertPropertyDetails(List<String> propertyValueIds, List<String> propertyValues) {

        if (CollectionUtils.isEmpty(propertyValueIds) || CollectionUtils.isEmpty(propertyValues)) {
            return new ArrayList<>();
        }

        // 确保两个列表大小相等
        if (propertyValueIds.size() != propertyValues.size()) {
            log.warn("属性值ID列表和属性值列表大小不匹配: {} vs {}", propertyValueIds.size(), propertyValues.size());
            return new ArrayList<>();
        }

        List<GoodsPropertyVO.GoodsPropertyDetailsVO> detailsList = new ArrayList<>();
        for (int i = 0; i < propertyValueIds.size(); i++) {
            String valueId = propertyValueIds.get(i);
            String value = propertyValues.get(i);

            if (valueId != null && value != null) {
                try {
                    GoodsPropertyVO.GoodsPropertyDetailsVO detailsVO = new GoodsPropertyVO.GoodsPropertyDetailsVO(valueId, value);
                    detailsList.add(detailsVO);
                } catch (Exception e) {
                    log.error("创建属性详情VO失败, valueId: {}, value: {}", valueId, value, e);
                }
            }
        }

        return detailsList;
    }

    /**
     * 验证转换结果
     *
     * @param target 转换后的目标对象
     * @param source 源对象
     * @return 是否转换成功
     */
    public static boolean validateConversion(Object target, Object source) {
        if (target == null) {
            log.warn("转换结果为空");
            return false;
        }

        if (source == null) {
            log.warn("源对象为空");
            return false;
        }

        return true;
    }

    /**
     * 记录转换日志
     *
     * @param sourceName 源对象名称
     * @param targetName 目标对象名称
     * @param success    是否成功
     */
    public static void logConversion(String sourceName, String targetName, boolean success) {
        if (success) {
            log.debug("转换成功: {} -> {}", sourceName, targetName);
        } else {
            log.warn("转换失败: {} -> {}", sourceName, targetName);
        }
    }
}

package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.collection.CollectionUtil;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.core.OrderRefundGoodsStatusEnum;
import com.stbella.order.domain.client.StoreGoodsClient;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundGoodsEntity;
import com.stbella.order.domain.order.month.entity.IncomePaidAllocationEntity;
import com.stbella.order.domain.order.production.GoodsEntity;
import com.stbella.order.domain.repository.GoodsRepository;
import com.stbella.order.domain.repository.HeOrderRefundGoodsRepository;
import com.stbella.order.server.order.month.enums.PayMethodEnum;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.order.server.utils.wangdian.StringUtils;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import com.stbella.store.goodz.res.PropertyDetailVO;
import com.stbella.store.goodz.res.PropertyValueVO;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


@Component
@Slf4j
@SnowballComponent(name = "发起退款，冻结商品", desc = "发起退款，冻结商品")
public class OrderRefundCreateOperationGoodsAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private HeOrderRefundGoodsRepository orderRefundGoodsRepository;
    @Resource
    private GoodsRepository goodsRepository;
    @Resource
    private StoreGoodsClient storeGoodsClient;

    private @NotNull HeOrderRefundGoodsEntity getHeOrderRefundGoodsEntity(
            CreateRefundReq.GoodsInfo goodsInfo,
            CreateRefundReq attribute,
            String parentRefundOrderSn,
            HeOrderGoodsEntity heOrderGoodsEntity,
            List<GoodsEntity> refundGoodsInfo,
            PropertyDetailVO unitProperty
    ) {
        HeOrderRefundGoodsEntity heOrderRefundGoodsEntity = new HeOrderRefundGoodsEntity();
        heOrderRefundGoodsEntity.setOrderId(attribute.getOrderId().longValue());
        heOrderRefundGoodsEntity.setRefundOrderSn(parentRefundOrderSn);
        heOrderRefundGoodsEntity.setOrderGoodsSn(goodsInfo.getOrderGoodsSn());
        heOrderRefundGoodsEntity.setSkuId(goodsInfo.getSkuId());
        heOrderRefundGoodsEntity.setSkuName(goodsInfo.getSkuName());
        heOrderRefundGoodsEntity.setGoodsId(goodsInfo.getGoodsId());
        heOrderRefundGoodsEntity.setGoodsName(goodsInfo.getGoodsName());
        heOrderRefundGoodsEntity.setGoodsImage(goodsInfo.getGoodsImage());
        heOrderRefundGoodsEntity.setRefundNum(goodsInfo.getRefundNum());
//      这里的数量不改了
//        goodsInfo.setRefundNum(goodsInfo.getRefundNum() - heOrderRefundGoodsEntity.getRefundNum());

        heOrderRefundGoodsEntity.setRefundAmount(RMBUtils.YTFInt(goodsInfo.getRefundAmount()));
        heOrderRefundGoodsEntity.setRefundType(attribute.getRefundType());
        heOrderRefundGoodsEntity.setRefundNature(attribute.getGoodsRefundType());
        heOrderRefundGoodsEntity.setGoodsType(goodsInfo.getGoodsType());
        heOrderRefundGoodsEntity.setGoodsPriceOrgin(heOrderGoodsEntity.getGoodsPriceOrgin());
        heOrderRefundGoodsEntity.setGoodsPricePay(heOrderGoodsEntity.getGoodsPricePay());
        heOrderRefundGoodsEntity.setGift(heOrderGoodsEntity.getGift());
        heOrderRefundGoodsEntity.setParentCombineSn(goodsInfo.getParentCombineSn());
        heOrderRefundGoodsEntity.setType(goodsInfo.getType());
        heOrderRefundGoodsEntity.setOrderGoodsId(heOrderGoodsEntity.getId());

        Optional<GoodsEntity> first = refundGoodsInfo.stream().filter(r -> r.getId().equals(goodsInfo.getGoodsId())).findFirst();
        if (first.isPresent()) {
            heOrderRefundGoodsEntity.setParentGoodsName(first.get().getParentName());
            Optional<PropertyValueVO> unitFirst = unitProperty.getValueList().stream().filter(u -> u.getCode().equals(first.get().getGoodsUnit() + "")).findFirst();
            if (unitFirst.isPresent()) {
                String unitValue = unitFirst.get().getValue();
                unitValue = org.apache.commons.lang3.StringUtils.isNotEmpty(unitValue) && "无".equals(unitValue) ? "" : unitValue;
                heOrderRefundGoodsEntity.setGoodsUnit(unitValue);
            }
        } else {
            heOrderRefundGoodsEntity.setParentGoodsName(goodsInfo.getGoodsName());
            heOrderRefundGoodsEntity.setGoodsUnit("");
        }
        return heOrderRefundGoodsEntity;
    }

    @Override
    public void run(FlowContext bizContext) {
        //请求参数
        CreateRefundReq attribute = bizContext.getAttribute(CreateRefundReq.class);
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        //押金
        boolean deposit = orderEntity.isDepositOrder();
        //本次退款的sn
        String parentRefundOrderSn = (String) bizContext.getAttribute(OtherConstant.PARENT_REFUND_ORDER_SN);
        //下单的商品列表
        List<HeOrderGoodsEntity> orderGoodsEntityList = bizContext.getListAttribute(HeOrderGoodsEntity.class);
        //退款商品信息
        List<CreateRefundReq.GoodsInfo> goodsInfoList = attribute.getGoodsInfoList();

        List<HeOrderRefundGoodsEntity> goodsRefundList = new ArrayList<>();

        //退款的商品id
        List<Integer> refundGoodsId = goodsInfoList.stream().map(CreateRefundReq.GoodsInfo::getGoodsId).collect(Collectors.toList());

        List<GoodsEntity> refundGoodsInfo = goodsRepository.queryByIdForDelete(refundGoodsId);

        //获取商品单位
        PropertyDetailVO unitProperty = storeGoodsClient.getGoodsUnit();

        for (CreateRefundReq.GoodsInfo goodsInfo : goodsInfoList) {

            Integer refundNum = goodsInfo.getRefundNum();

            HeOrderGoodsEntity orderGoodsEntity = orderGoodsEntityList.stream().filter(o -> o.getOrderGoodsSn().equals(goodsInfo.getOrderGoodsSn())).findFirst().get();
            if (goodsInfo.getCombinationAddition()) {
                //如果是组合的附加项，需要分摊数量和金额
                List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList = setCombinationAddition(goodsInfo, bizContext, attribute, parentRefundOrderSn, orderGoodsEntity);
                if (CollectionUtil.isNotEmpty(orderRefundGoodsEntityList)) {
                    goodsRefundList.addAll(orderRefundGoodsEntityList);
                }
            } else {
                if (deposit) {
                    //押金写死退款数量1
                    goodsInfo.setRefundNum(0);
                }

                List<CreateRefundReq.GoodsRefundAmountInfo> refundGoodsAmountInfoList = goodsInfo.getRefundGoodsAmountInfoList();

                if (CollectionUtil.isEmpty(refundGoodsAmountInfoList.stream().filter(r -> r.getAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList()))) {
                    //纯退数量，不退金额
                    HeOrderRefundGoodsEntity heOrderRefundGoodsEntity = getHeOrderRefundGoodsEntity(goodsInfo, attribute, parentRefundOrderSn, orderGoodsEntity, refundGoodsInfo, unitProperty);
                    heOrderRefundGoodsEntity.setRefundAmount(0);
                    heOrderRefundGoodsEntity.setPaymentMethod(OmniPayTypeEnum.WECHAT.getCode());
                    heOrderRefundGoodsEntity.setPayType(OmniPayTypeEnum.WECHAT.getCode());
                    goodsRefundList.add(heOrderRefundGoodsEntity);
                } else {
                    // 处理尾差情况：确保退款金额总和等于前端提交的总金额
                    List<CreateRefundReq.GoodsRefundAmountInfo> validRefundAmounts = goodsInfo.getRefundGoodsAmountInfoList()
                            .stream()
                            .filter(info -> info.getAmount().compareTo(BigDecimal.ZERO) > 0)
                            .collect(Collectors.toList());

                    handleTailDifference(validRefundAmounts, goodsInfo.getRefundNum());

                    for (CreateRefundReq.GoodsRefundAmountInfo goodsRefundAmountInfo : validRefundAmounts) {
                        HeOrderRefundGoodsEntity heOrderRefundGoodsEntity = getHeOrderRefundGoodsEntity(goodsInfo, attribute, parentRefundOrderSn, orderGoodsEntity, refundGoodsInfo, unitProperty);
                        heOrderRefundGoodsEntity.setRefundAmount(RMBUtils.YTFInt(goodsRefundAmountInfo.getAmount()));
                        Integer amountType = goodsRefundAmountInfo.getAmountType();
                        if (deposit || amountType.equals(new Integer(PayMethodEnum.CASH.getCode()))) {
                            //默认写死，这边关联的是主退款的sn，主退款不关注子退款的退款方式，默认写微信，押金百分百是现金
                            heOrderRefundGoodsEntity.setPaymentMethod(OmniPayTypeEnum.WECHAT.getCode());
                            heOrderRefundGoodsEntity.setPayType(OmniPayTypeEnum.WECHAT.getCode());
                        } else if (new Integer(PayMethodEnum.REDUCTION.getCode()).equals(amountType)) {
                            heOrderRefundGoodsEntity.setPaymentMethod(OmniPayTypeEnum.REDUCTION.getCode());
                            heOrderRefundGoodsEntity.setPayType(OmniPayTypeEnum.REDUCTION.getCode());
                        } else {
                            heOrderRefundGoodsEntity.setPaymentMethod(OmniPayTypeEnum.PRODUCTION_COIN.getCode());
                            heOrderRefundGoodsEntity.setPayType(OmniPayTypeEnum.PRODUCTION_COIN.getCode());
                        }
                        goodsRefundList.add(heOrderRefundGoodsEntity);
                    }
                }
            }
            goodsInfo.setRefundNum(refundNum);
        }
        orderRefundGoodsRepository.saveList(goodsRefundList);
    }

    /**
     * 组合商品均摊节假日和多胞胎
     *
     * @param goodsInfo
     */
    private List<HeOrderRefundGoodsEntity> setCombinationAddition(
            CreateRefundReq.GoodsInfo goodsInfo,
            FlowContext bizContext,
            CreateRefundReq attribute,
            String parentRefundOrderSn,
            HeOrderGoodsEntity heOrderGoodsEntity
    ) {

        List<HeOrderRefundGoodsEntity> result = new ArrayList<>();

        //退款记录
        List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList = bizContext.getListAttribute(HeOrderRefundGoodsEntity.class);
        //支付记录
        List<IncomePaidAllocationEntity> incomePaidAllocationEntities = bizContext.getListAttribute(IncomePaidAllocationEntity.class);
        //订单商品表
        List<HeOrderGoodsEntity> orderGoodsEntityList = bizContext.getListAttribute(HeOrderGoodsEntity.class);
        ;

        //初始退款数量
        Integer refundNum = goodsInfo.getRefundNum();
        //组合的sn
        String combinationSn = goodsInfo.getCombinationSn();

        //获取这个组合下所有的节假日或者多胞胎
        List<String> orderGoodsSnList = new ArrayList<>();
        orderGoodsEntityList.stream().filter(g -> StringUtils.areNotEmpty(g.getParentCombineSn()) && g.getParentCombineSn().equals(combinationSn)).forEach(child -> {
            //获取所有符合的子项
            orderGoodsEntityList.stream().filter(g -> StringUtils.areNotEmpty(g.getParentCombineSn()) && g.getParentCombineSn().equals(child.getOrderGoodsSn()) && g.getGoodsType().equals(goodsInfo.getGoodsType())).forEach(add -> {
                orderGoodsSnList.add(add.getOrderGoodsSn());
            });
        });

        Integer finalRefundNum = refundNum;


        //获取商品单位
        PropertyDetailVO unitProperty = storeGoodsClient.getGoodsUnit();

        List<HeOrderGoodsEntity> heOrderGoodsEntityList = orderGoodsEntityList.stream().filter(o -> orderGoodsSnList.contains(o.getOrderGoodsSn())).collect(Collectors.toList());

        List<GoodsEntity> refundGoodsInfo = goodsRepository.queryByIdForDelete(heOrderGoodsEntityList.stream().map(HeOrderGoodsEntity::getGoodsId).collect(Collectors.toList()));


        for (PayMethodEnum value : PayMethodEnum.values()) {

            List<HeOrderRefundGoodsEntity> refundGoodsType = orderRefundGoodsEntityList.stream().filter(o -> o.getPaymentMethod().equals(value.getCode())).collect(Collectors.toList());

            List<IncomePaidAllocationEntity> paidListType = incomePaidAllocationEntities.stream().filter(o -> PayMethodEnum.PayType2Currency(Integer.parseInt(o.getPaymentMethod())).getCode().equals(value.getCode())).collect(Collectors.toList());

            //初始退款金额
            BigDecimal refundAmount = BigDecimal.ZERO;
            Optional<CreateRefundReq.GoodsRefundAmountInfo> first = goodsInfo.getRefundGoodsAmountInfoList().stream().filter(g -> g.getAmountType().equals(new Integer(value.getCode()))).findFirst();
            if (first.isPresent()) {
                refundAmount = first.get().getAmount();
            }

            BigDecimal finalRefundAmount = refundAmount;

            for (HeOrderGoodsEntity orderGoodsEntity : heOrderGoodsEntityList) {
                if (finalRefundNum > 0 || finalRefundAmount.compareTo(BigDecimal.ZERO) > 0) {
                    //总数量
                    Integer goodsNum = orderGoodsEntity.getGoodsNum();
                    //获取支付记录
                    List<IncomePaidAllocationEntity> incomeList = paidListType.stream().filter(i -> i.getOrderGoodsSn().equals(orderGoodsEntity.getOrderGoodsSn())).collect(Collectors.toList());
                    //付款总金额
                    BigDecimal allPaidAmount = RMBUtils.bigDecimalF2Y(incomeList.stream().mapToInt(IncomePaidAllocationEntity::getPaidAmount).sum());
                    //退款记录
                    List<HeOrderRefundGoodsEntity> refundList = refundGoodsType.stream().filter(i -> i.getOrderGoodsSn().equals(orderGoodsEntity.getOrderGoodsSn()) && (i.getStatus().equals(OrderRefundGoodsStatusEnum.REFUNDING.code()) || i.getStatus().equals(OrderRefundGoodsStatusEnum.SUCCESS.code()))).collect(Collectors.toList());
                    //退款总金额
                    BigDecimal allRefundAmount = RMBUtils.bigDecimalF2Y(refundList.stream().mapToInt(HeOrderRefundGoodsEntity::getRefundAmount).sum());
                    //退款总数量
                    Integer allRefundNum = refundList.stream().mapToInt(HeOrderRefundGoodsEntity::getRefundNum).sum();

                    //当前商品退款数量
                    int thisRefundNum = 0;
                    //当前商品退款金额
                    BigDecimal thisRefundAmount = BigDecimal.ZERO;

                    if (refundNum > 0) {
                        //如果退款的数量小于这个商品目前可退
                        if (finalRefundNum > 0 && finalRefundNum < (goodsNum - allRefundNum - finalRefundNum)) {
                            finalRefundNum = 0;
                        } else {
                            thisRefundNum = goodsNum - allRefundNum;
                            finalRefundNum -= (goodsNum - allRefundNum);
                        }
                    }

                    if (refundAmount.compareTo(BigDecimal.ZERO) > 0) {
                        if (finalRefundAmount.compareTo(BigDecimal.ZERO) > 0 && finalRefundAmount.compareTo(allPaidAmount.subtract(allRefundAmount).subtract(finalRefundAmount)) < 0) {
                            thisRefundAmount = finalRefundAmount;
                            finalRefundAmount = BigDecimal.ZERO;
                        } else {
                            thisRefundAmount = allPaidAmount.subtract(allRefundAmount);
                            finalRefundAmount = finalRefundAmount.subtract(allPaidAmount.subtract(allRefundAmount));
                        }
                    }

                    CreateRefundReq.GoodsInfo tempGoodsInfo = new CreateRefundReq.GoodsInfo();
                    tempGoodsInfo.setOrderGoodsSn(orderGoodsEntity.getOrderGoodsSn());
                    tempGoodsInfo.setSkuId(orderGoodsEntity.getSkuId());
                    tempGoodsInfo.setSkuName(orderGoodsEntity.getSkuName());
                    tempGoodsInfo.setGoodsId(orderGoodsEntity.getGoodsId());
                    tempGoodsInfo.setGoodsName(orderGoodsEntity.getGoodsName());
                    tempGoodsInfo.setGoodsImage(orderGoodsEntity.getGoodsImage());
                    tempGoodsInfo.setRefundNum(thisRefundNum);
                    tempGoodsInfo.setRefundAmount(thisRefundAmount);
                    tempGoodsInfo.setGoodsType(orderGoodsEntity.getGoodsType());
                    HeOrderRefundGoodsEntity heOrderRefundGoodsEntity = getHeOrderRefundGoodsEntity(tempGoodsInfo, attribute, parentRefundOrderSn, heOrderGoodsEntity, refundGoodsInfo, unitProperty);
                    if (value.equals(PayMethodEnum.CASH)) {
                        //默认写死，这边关联的是主退款的sn，主退款不关注子退款的退款方式，默认写微信
                        heOrderRefundGoodsEntity.setPaymentMethod(OmniPayTypeEnum.WECHAT.getCode());
                        heOrderRefundGoodsEntity.setPayType(OmniPayTypeEnum.WECHAT.getCode());
                    } else if (value.equals(PayMethodEnum.CJK)) {
                        heOrderRefundGoodsEntity.setPaymentMethod(OmniPayTypeEnum.PRODUCTION_COIN.getCode());
                        heOrderRefundGoodsEntity.setPayType(OmniPayTypeEnum.PRODUCTION_COIN.getCode());
                    } else {
                        heOrderRefundGoodsEntity.setPaymentMethod(OmniPayTypeEnum.REDUCTION.getCode());
                        heOrderRefundGoodsEntity.setPayType(OmniPayTypeEnum.REDUCTION.getCode());
                    }
                    result.add(heOrderRefundGoodsEntity);
                }
            }

        }
        return result;
    }

    /**
     * 处理尾差情况
     * 当前端提交的退款金额与按单价计算的金额存在尾差时，将尾差分摊到第一个支付方式上
     *
     * @param refundAmountInfoList 退款金额信息列表
     * @param refundNum            退款数量
     */
    private void handleTailDifference(List<CreateRefundReq.GoodsRefundAmountInfo> refundAmountInfoList, Integer refundNum) {
        if (CollectionUtil.isEmpty(refundAmountInfoList) || refundNum == null || refundNum <= 0) {
            return;
        }

        // 计算总的退款金额
        BigDecimal totalRefundAmount = refundAmountInfoList.stream()
                .map(CreateRefundReq.GoodsRefundAmountInfo::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (totalRefundAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        // 计算单价（保留4位小数以提高精度）
        BigDecimal unitPrice = totalRefundAmount.divide(new BigDecimal(refundNum), 4, BigDecimal.ROUND_HALF_UP);

        // 计算按单价应该的总金额
        BigDecimal expectedTotalAmount = unitPrice.multiply(new BigDecimal(refundNum)).setScale(2, BigDecimal.ROUND_HALF_UP);

        // 计算尾差
        BigDecimal tailDifference = totalRefundAmount.subtract(expectedTotalAmount);

        // 如果存在尾差，将尾差加到第一个非零金额的支付方式上
        if (tailDifference.compareTo(BigDecimal.ZERO) != 0) {
            log.info("检测到退款尾差：{}，将调整到第一个支付方式", tailDifference);

            for (CreateRefundReq.GoodsRefundAmountInfo refundAmountInfo : refundAmountInfoList) {
                if (refundAmountInfo.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal adjustedAmount = refundAmountInfo.getAmount().add(tailDifference);
                    // 确保调整后的金额不为负数
                    if (adjustedAmount.compareTo(BigDecimal.ZERO) >= 0) {
                        refundAmountInfo.setAmount(adjustedAmount);
                        log.info("已将尾差{}调整到支付方式{}，调整后金额：{}",
                                tailDifference, refundAmountInfo.getAmountType(), adjustedAmount);
                        break;
                    }
                }
            }
        }
    }

    @Override
    public boolean condition(FlowContext context) {
        return (boolean) context.getAttribute(OtherConstant.CONTINUE);
    }

}

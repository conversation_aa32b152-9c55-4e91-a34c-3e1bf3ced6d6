package com.stbella.order.server.strategy.pay;

import com.stbella.order.server.convert.OrderNutritionConvert;
import com.stbella.order.server.order.month.enums.PayRecordEnum;
import com.stbella.order.server.order.month.request.pay.PayNotityRequest;
import com.stbella.order.server.order.nutrition.service.OrderNutritionMealService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 支付策略-营养订单
 * @date 2024/3/15 16:01
 */
@Component
@Slf4j
public class NutritionOrderPayStrategy implements PayNotificationStrategy {
    @Resource
    private OrderNutritionMealService orderNutritionMealService;
    @Resource
    private OrderNutritionConvert orderNutritionConvert;

    @Override
    public boolean handlePayNotification(PayNotityRequest payNotityRequest) {
        //营养订单处理
        boolean editOrderInfo = orderNutritionMealService.payNotify(orderNutritionConvert.PayNotityRequest2PayNotifyMqDTO(payNotityRequest), payNotityRequest.getOrderNo());
        return editOrderInfo;
    }

    @Override
    public PayRecordEnum getPayRecordEnum() {
        return PayRecordEnum.GH;
    }
}

package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.month.RefundRecordPayStatusEnum;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.repository.IncomePaidAllocationRepository;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.domain.repository.OrderRefundRepository;
import com.stbella.order.server.order.month.enums.RefundTypeEnum;
import com.stbella.order.server.utils.IdGenUtils;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Component
@Slf4j
@SnowballComponent(name = "发起退款，冻结商品，支付记录金额", desc = "发起退款，冻结商品，支付记录金额")
public class OrderRefundCreateCapitationFeeAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @Resource
    private IncomePaidAllocationRepository paidAllocationRepository;
    @Resource
    private OrderRefundRepository orderRefundRepository;

    @Override
    public void run(FlowContext bizContext) {
        CreateRefundReq attribute = bizContext.getAttribute(CreateRefundReq.class);
        Integer orderId = attribute.getOrderId();

        BigDecimal refundAmount = attribute.getRefundAmount();
        //退款金额（分）
        Integer refundAmountFen = refundAmount.multiply(new BigDecimal(100)).intValue();

        //支付记录
        List<HeIncomeRecordEntity> allRecordListByOrderId = incomeRecordRepository.getAllRecordListByOrderId(orderId);


        RefundTypeEnum refundType = RefundTypeEnum.getEnumByCode(attribute.getRefundType());

        String parentRefundOrderSn = IdGenUtils.createRefundTransactionalNo(null, new Date());

        List<HeOrderRefundEntity> refundEntityList = new ArrayList<>();

        switch (refundType) {
            case BACK_TRACK:
                refundAmountFen = backTrack(allRecordListByOrderId, refundAmountFen, refundEntityList, parentRefundOrderSn, attribute);
                break;
            case OFFLINE_REMITTANCE:
                //线下汇款
                break;
            case BACK_ACCOUNT:
                //退款至余额
                break;
            default:
        }

        //更新商品分摊表
        if (CollectionUtil.isNotEmpty(refundEntityList)) {
            //退款主项
            HeOrderRefundEntity heOrderRefundEntity = setRefundInfo(null, parentRefundOrderSn, refundAmountFen, null, attribute);
            //主项存
            Integer refundId = orderRefundRepository.saveOne(heOrderRefundEntity);
            //退款子项
            orderRefundRepository.saveOneList(refundEntityList);
            bizContext.setAttribute(OtherConstant.PARENT_REFUND_ORDER_ID, refundId);
        }
        bizContext.setAttribute(OtherConstant.PARENT_REFUND_ORDER_SN, parentRefundOrderSn);
    }

    /**
     * 原路退回
     *
     * @param allRecordListByOrderId
     * @param refundAmountFen
     * @param refundEntityList
     * @param parentRefundOrderSn
     * @param attribute
     * @return
     */
    private Integer backTrack(List<HeIncomeRecordEntity> allRecordListByOrderId, Integer refundAmountFen, List<HeOrderRefundEntity> refundEntityList, String parentRefundOrderSn, CreateRefundReq attribute) {
        /**
         * 原路退回，根据支付记录的可退+支付时间倒叙进行扣除，
         * 当前订单3笔支付记录，分别是支付宝、微信、pos，金额都为100，支付宝支付支付时间1月1号，微信支付时间1月2号，pos支付时间1月1号
         * 先判断微信和支付宝是否还有可退金额，如果都有，优先扣除支付宝再微信，最后pos
         */
        //获取可退的线上支付,除去pos，对支付宝微信根据时间进行排序
        List<HeIncomeRecordEntity> wechatAndAliPay = allRecordListByOrderId.stream().filter(a -> (
                a.getPayType().equals(OmniPayTypeEnum.WECHAT.getCode()) ||
                        a.getPayType().equals(OmniPayTypeEnum.ALIPAY.getCode())) && a.getRefundIncome() > 0).sorted((o1, o2) -> o2.getPayTime().compareTo(o1.getPayTime())).collect(Collectors.toList());
        //先通过支付宝或者微信扣款
        if (CollectionUtil.isNotEmpty(wechatAndAliPay)) {
            for (HeIncomeRecordEntity income : wechatAndAliPay) {
                if (refundAmountFen > 0) {
                    //冻结金额
                    Integer freezeAmount = income.getFreezeAmount();
                    //当前可退金额
                    Integer refundIncome = income.getRefundIncome();
                    refundAmountFen = refundAmountFen - refundIncome;
                    Integer applyAmount;
                    //如果这笔支付记录可退金额>申请的金额
                    if (refundAmountFen > refundIncome) {
                        //冻结金额 = 当前+当前可退
                        freezeAmount = freezeAmount + refundIncome;
                        applyAmount = refundIncome;
                    } else {
                        //冻结金额 = 当前+申请退款金额
                        freezeAmount = freezeAmount + refundAmountFen;
                        applyAmount = refundAmountFen;
                    }
                    income.setFreezeAmount(freezeAmount);
                    //创建退款表记录
                    refundEntityList.add(setRefundInfo(parentRefundOrderSn, IdGenUtils.createRefundTransactionalNo(null, new Date()), applyAmount, income, attribute));
                }
            }
            //支付宝+微信分摊后还是有剩余的，用pos退款
            if (refundAmountFen > 0) {
                //pos机
                List<HeIncomeRecordEntity> posIncomeList = allRecordListByOrderId.stream().filter(a -> a.getPayType().equals(OmniPayTypeEnum.ONLINE_POS.getCode()) && a.getRefundIncome() > 0).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(posIncomeList)) {
                    for (HeIncomeRecordEntity income : posIncomeList) {
                        if (refundAmountFen > 0) {
                            //冻结金额
                            Integer freezeAmount = income.getFreezeAmount();
                            //当前可退金额
                            Integer refundIncome = income.getRefundIncome();
                            refundAmountFen = refundAmountFen - refundIncome;
                            Integer applyAmount;
                            //如果这笔支付记录可退金额>申请的金额
                            if (refundAmountFen > refundIncome) {
                                //冻结金额 = 当前+当前可退
                                freezeAmount = freezeAmount + refundIncome;
                                applyAmount = refundIncome;
                            } else {
                                //冻结金额 = 当前+申请退款金额
                                freezeAmount = freezeAmount + refundAmountFen;
                                applyAmount = refundAmountFen;
                            }
                            income.setFreezeAmount(freezeAmount);
                            //创建退款表记录
                            refundEntityList.add(setRefundInfo(parentRefundOrderSn, IdGenUtils.createRefundTransactionalNo(null, new Date()), applyAmount, income, attribute));
                        }
                    }
                }
            }
            //如果支付宝+微信+pos分摊后还是有剩（一般不可能出现，之前有加判断）
            if (refundAmountFen > 0) {
                throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "线上支付金额小于申请退款金额，无法原路返回，建议修改退款方式为线下打款。");
            }
        }
        return refundAmountFen;
    }


    public HeOrderRefundEntity setRefundInfo(
            String parentRefundOrderSn,
            String refundOrderSn,
            Integer applyAmount,
            HeIncomeRecordEntity incomeRecord,
            CreateRefundReq attribute
    ) {
        HeOrderRefundEntity heOrderRefundEntity = new HeOrderRefundEntity();
        heOrderRefundEntity.setParentRefundOrderSn(parentRefundOrderSn);
        heOrderRefundEntity.setOrderId(attribute.getOrderId());

        if (ObjectUtil.isNotEmpty(incomeRecord)) {
            heOrderRefundEntity.setOrderGoodId(incomeRecord.getId());
            heOrderRefundEntity.setIncomeSn(incomeRecord.getIncomeSn());
            heOrderRefundEntity.setRefundType(incomeRecord.getPayType());
        } else {
            heOrderRefundEntity.setOrderGoodId(0);
            heOrderRefundEntity.setIncomeSn("");
            heOrderRefundEntity.setRefundType(OmniPayTypeEnum.OTHER.getCode());
        }


        heOrderRefundEntity.setType(0);
        heOrderRefundEntity.setRefundOrderSn(refundOrderSn);
        heOrderRefundEntity.setApplyAmount(applyAmount);

        heOrderRefundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_1.getCode());
        heOrderRefundEntity.setCreatedAt(System.currentTimeMillis() / 1000);
        heOrderRefundEntity.setApplyId(Integer.valueOf(attribute.getOperator().getOperatorGuid()));
        heOrderRefundEntity.setApplyName(attribute.getOperator().getOperatorName());
        heOrderRefundEntity.setApplyPhone(attribute.getOperator().getOperatorPhone());
        Map<String, Object> map = new HashMap<>();
        map.put("images", attribute.getAuditRefundProof());
        //线上
        if (attribute.getRefundType().equals(RefundTypeEnum.OFFLINE_REMITTANCE.getCode())) {
            map.put("name", attribute.getAccountName());
            map.put("bank_number", attribute.getBankNo());
            map.put("bank_name", attribute.getAccountBank());
        }
        heOrderRefundEntity.setRefundInfo(JSONUtil.toJsonStr(map));
        heOrderRefundEntity.setRemark(attribute.getRemark());
        heOrderRefundEntity.setRefundReasonType(attribute.getRefundReasonType());
        heOrderRefundEntity.setRefundReason(attribute.getRefundReason());
        heOrderRefundEntity.setHasLiquidatedDamages(0);
        heOrderRefundEntity.setGiftExtendDisabled(0);
        return heOrderRefundEntity;
    }

}

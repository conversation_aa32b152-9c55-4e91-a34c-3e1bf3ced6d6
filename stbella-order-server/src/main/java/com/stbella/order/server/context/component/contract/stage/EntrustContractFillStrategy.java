package com.stbella.order.server.context.component.contract.stage;

import cn.hutool.core.date.DateTime;
import com.stbella.contract.common.utils.GeneratorUtil;
import com.stbella.contract.model.enums.TemplateContractTypeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderBailorSnapshotEntity;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.domain.repository.OrderBailorSnapshotRepository;
import com.stbella.order.domain.repository.OrderUserSnapshotRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-23 15:33
 */
@Component
@Slf4j
public class EntrustContractFillStrategy extends ContractFillStrategy {

    @Resource
    private OrderBailorSnapshotRepository orderBailorSnapshotRepository;
    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;


    @Override
    protected Map<String, String> buildParam(ContractParamFillContext contractParamFillContext) {
        Integer orderId = contractParamFillContext.getOrderId();
        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(orderId);
        HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity = orderBailorSnapshotRepository.queryByOrderId(orderId);
        String contractNo = GeneratorUtil.contractNo();
        //获取信息,填充部分合同信息
        Map<String, String> templateData = new HashMap<>();

        //签署人手机号(个人签署需要)
        templateData.put("signPhone", heOrderUserSnapshotEntity.getPhone());

        //合同参数需要的开始
        templateData.put("contract_no", contractNo);
        templateData.put("bailor_name", heOrderBailorSnapshotEntity.getName());
        templateData.put("bailor_id_card", heOrderBailorSnapshotEntity.getIdCard());
        templateData.put("client_name", heOrderUserSnapshotEntity.getName());
        templateData.put("sign_time_str2", DateTime.now().toDateStr());

        return templateData;
    }

    @Override
    protected String getContractName(ContractParamFillContext contractParamFillContext) {
        return TemplateContractTypeEnum.ENTRUST.value();
    }



    @Override
    public boolean filter(Integer templateContractType) {
        return TemplateContractTypeEnum.ENTRUST.getCode().equals(templateContractType);
    }
}
package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.collection.CollectionUtil;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.production.OrderGiftExtendTypeEnum;
import com.stbella.order.common.enums.production.OrderProductionVerificationStateEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundDisabledProductEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.order.production.OrderGiftExtendEntity;
import com.stbella.order.domain.order.production.OrderProductionExtendEntity;
import com.stbella.order.domain.repository.HeOrderRefundDisabledProductRepository;
import com.stbella.order.domain.repository.OrderGiftExtendRepository;
import com.stbella.order.domain.repository.OrderProductionExtendRepository;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 审批通过后失效产康商品组件
 * 该组件负责在退款审批通过后，将相关的产康商品标记为失效
 */
@Component
@Slf4j
@SnowballComponent(name = "审批通过后失效产康商品", desc = "审批通过后失效产康商品")
public class OrderRefundAfterDisabledCKGoodsAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderGiftExtendRepository orderGiftExtendRepository;

    @Resource
    private OrderProductionExtendRepository orderProductionExtendRepository;

    @Resource
    private HeOrderRefundDisabledProductRepository orderRefundDisabledProductRepository;

    @Override
    public boolean condition(FlowContext context) {
        return (boolean) context.getAttribute(OtherConstant.CONTINUE);
    }

    @Override
    public void run(FlowContext bizContext) {
        try {
            // 获取订单和退款信息
            HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
            HeOrderRefundEntity refundEntity = bizContext.getAttribute(HeOrderRefundEntity.class);

            // 获取退款商品信息
            CreateRefundReq refundRequest = bizContext.getAttribute(CreateRefundReq.class);
            List<CreateRefundReq.GoodsInfo> goodsInfoList = refundRequest.getGoodsInfoList();

            if (CollectionUtil.isEmpty(goodsInfoList)) {
                log.info("退款商品列表为空，无需处理产康商品失效，订单ID: {}, 退款ID: {}",
                        orderEntity.getOrderId(), refundEntity.getId());
                return;
            }

            // 查询订单相关的产康商品
            List<OrderGiftExtendEntity> giftExtendList = orderGiftExtendRepository.getByOrderId(orderEntity.getOrderId());
            List<OrderProductionExtendEntity> productionExtendList = orderProductionExtendRepository.queryByOrderId(orderEntity.getOrderId());

            // 记录需要失效的产品
            List<HeOrderRefundDisabledProductEntity> disabledProductList = new ArrayList<>();

            // 按商品ID分组处理
            Map<Integer, List<CreateRefundReq.GoodsInfo>> goodsInfoByGoodsId =
                    goodsInfoList.stream().collect(Collectors.groupingBy(CreateRefundReq.GoodsInfo::getGoodsId));

            // 处理每个商品
            for (Map.Entry<Integer, List<CreateRefundReq.GoodsInfo>> entry : goodsInfoByGoodsId.entrySet()) {
                processGoodsGroup(entry.getKey(), entry.getValue(), orderEntity, refundEntity,
                        giftExtendList, productionExtendList, disabledProductList);
            }

            // 批量更新数据库
            if (!giftExtendList.isEmpty()) {
                log.info("更新礼赠产康商品状态，数量: {}", giftExtendList.size());
                orderGiftExtendRepository.batchUpdateById(giftExtendList);
            }

            if (!productionExtendList.isEmpty()) {
                log.info("更新购买产康商品状态，数量: {}", productionExtendList.size());
                orderProductionExtendRepository.saveOrUpdateBatch(productionExtendList);
            }

            if (!disabledProductList.isEmpty()) {
                log.info("保存失效产品记录，数量: {}", disabledProductList.size());
                orderRefundDisabledProductRepository.saveList(disabledProductList);
            }

        } catch (Exception e) {
            log.error("处理产康商品失效时发生异常", e);
            // 异常不影响主流程，记录日志后继续
        }
    }

    /**
     * 处理单个商品组的失效逻辑
     */
    private void processGoodsGroup(Integer goodsId, List<CreateRefundReq.GoodsInfo> goodsInfos,
                                   HeOrderEntity orderEntity, HeOrderRefundEntity refundEntity,
                                   List<OrderGiftExtendEntity> giftExtendList,
                                   List<OrderProductionExtendEntity> productionExtendList,
                                   List<HeOrderRefundDisabledProductEntity> disabledProductList) {

        if (CollectionUtil.isEmpty(goodsInfos)) {
            return;
        }

        // 计算总退款数量
        int totalRefundNum = calculateTotalRefundNum(goodsInfos);
        if (totalRefundNum <= 0) {
            return;
        }

        // 获取第一个商品信息作为代表
        CreateRefundReq.GoodsInfo goodsInfo = goodsInfos.get(0);

        // 筛选出符合条件的礼赠产康
        List<OrderGiftExtendEntity> matchedGiftExtends = filterMatchedGiftExtends(
                giftExtendList, goodsInfo.getGoodsId(), goodsInfo.getSkuId());

        // 筛选出符合条件的购买产康
        List<OrderProductionExtendEntity> matchedProductionExtends = filterMatchedProductionExtends(
                productionExtendList, goodsInfo.getGoodsId(), goodsInfo.getSkuId());

        // 处理失效逻辑
        processDisableLogic(matchedGiftExtends, matchedProductionExtends, totalRefundNum,
                orderEntity, refundEntity, disabledProductList);
    }

    /**
     * 计算商品组的总退款数量
     */
    private int calculateTotalRefundNum(List<CreateRefundReq.GoodsInfo> goodsInfos) {
        return goodsInfos.stream()
                .mapToInt(CreateRefundReq.GoodsInfo::getRefundNum)
                .sum();
    }

    /**
     * 筛选匹配的礼赠产康
     */
    private List<OrderGiftExtendEntity> filterMatchedGiftExtends(
            List<OrderGiftExtendEntity> giftExtendList, Integer goodsId, Integer skuId) {

        return giftExtendList.stream()
                .filter(gift ->
                        gift.getType().equals(OrderGiftExtendTypeEnum.INDUSTRIAL_HEALTH_SERVICE.code()) &&
                                !gift.getVerificationStatus().equals(OrderProductionVerificationStateEnum.WRITTEN_OFF.code()) &&
                                gift.getGoodsId().equals(goodsId) &&
                                gift.getSkuId().equals(skuId))
                .collect(Collectors.toList());
    }

    /**
     * 筛选匹配的购买产康
     */
    private List<OrderProductionExtendEntity> filterMatchedProductionExtends(
            List<OrderProductionExtendEntity> productionExtendList, Integer goodsId, Integer skuId) {

        return productionExtendList.stream()
                .filter(prod ->
                        !prod.getVerificationStatus().equals(OrderProductionVerificationStateEnum.WRITTEN_OFF.code()) &&
                                prod.getGoodsId().equals(goodsId) &&
                                prod.getSkuId().equals(skuId))
                .collect(Collectors.toList());
    }

    /**
     * 处理产品失效逻辑
     */
    private void processDisableLogic(List<OrderGiftExtendEntity> matchedGiftExtends,
                                     List<OrderProductionExtendEntity> matchedProductionExtends,
                                     int refundNum, HeOrderEntity orderEntity, HeOrderRefundEntity refundEntity,
                                     List<HeOrderRefundDisabledProductEntity> disabledProductList) {

        // 优先处理礼赠和购买都存在的情况
        if (CollectionUtil.isNotEmpty(matchedGiftExtends) && CollectionUtil.isNotEmpty(matchedProductionExtends)) {
            processGiftAndPurchasedProducts(matchedGiftExtends, matchedProductionExtends, refundNum,
                    orderEntity, refundEntity, disabledProductList);
        } else {
            // 单独处理礼赠产康
            if (CollectionUtil.isNotEmpty(matchedGiftExtends)) {
                processGiftProducts(matchedGiftExtends, refundNum, orderEntity, refundEntity, disabledProductList);
            }

            // 单独处理购买产康
            if (CollectionUtil.isNotEmpty(matchedProductionExtends)) {
                processPurchasedProducts(matchedProductionExtends, refundNum, orderEntity, refundEntity, disabledProductList);
            }
        }
    }

    /**
     * 处理同时存在礼赠和购买产康的情况
     */
    private void processGiftAndPurchasedProducts(List<OrderGiftExtendEntity> giftExtends,
                                                 List<OrderProductionExtendEntity> productionExtends,
                                                 int refundNum, HeOrderEntity orderEntity, HeOrderRefundEntity refundEntity,
                                                 List<HeOrderRefundDisabledProductEntity> disabledProductList) {

        // 优先失效礼赠产康
        int remainingRefundNum = refundNum;

        if (giftExtends.size() <= remainingRefundNum) {
            // 礼赠产康数量不足，全部失效
            for (OrderGiftExtendEntity gift : giftExtends) {
                disableGiftProduct(gift, orderEntity.getOrderId(), refundEntity.getId(), disabledProductList);
            }

            remainingRefundNum -= giftExtends.size();

            // 如果还有剩余退款数量，继续失效购买产康
            if (remainingRefundNum > 0 && !productionExtends.isEmpty()) {
                int disableCount = Math.min(remainingRefundNum, productionExtends.size());
                for (int i = 0; i < disableCount; i++) {
                    disablePurchasedProduct(productionExtends.get(i), orderEntity.getOrderId(),
                            refundEntity.getId(), disabledProductList);
                }
            }
        } else {
            // 礼赠产康数量足够，只失效部分
            for (int i = 0; i < remainingRefundNum; i++) {
                disableGiftProduct(giftExtends.get(i), orderEntity.getOrderId(),
                        refundEntity.getId(), disabledProductList);
            }
        }
    }

    /**
     * 处理只有礼赠产康的情况
     */
    private void processGiftProducts(List<OrderGiftExtendEntity> giftExtends, int refundNum,
                                     HeOrderEntity orderEntity, HeOrderRefundEntity refundEntity,
                                     List<HeOrderRefundDisabledProductEntity> disabledProductList) {

        int disableCount = Math.min(refundNum, giftExtends.size());
        for (int i = 0; i < disableCount; i++) {
            disableGiftProduct(giftExtends.get(i), orderEntity.getOrderId(),
                    refundEntity.getId(), disabledProductList);
        }
    }

    /**
     * 处理只有购买产康的情况
     */
    private void processPurchasedProducts(List<OrderProductionExtendEntity> productionExtends, int refundNum,
                                          HeOrderEntity orderEntity, HeOrderRefundEntity refundEntity,
                                          List<HeOrderRefundDisabledProductEntity> disabledProductList) {

        int disableCount = Math.min(refundNum, productionExtends.size());
        for (int i = 0; i < disableCount; i++) {
            disablePurchasedProduct(productionExtends.get(i), orderEntity.getOrderId(),
                    refundEntity.getId(), disabledProductList);
        }
    }

    /**
     * 失效礼赠产康并记录
     */
    private void disableGiftProduct(OrderGiftExtendEntity gift, Integer orderId, Integer refundId,
                                    List<HeOrderRefundDisabledProductEntity> disabledProductList) {

        // 记录原始状态
        createDisabledProductRecord(orderId, refundId, 1, gift.getId(), gift.getStatus(), disabledProductList);

        // 更新状态为失效(2)
        gift.setStatus(2);
    }

    /**
     * 失效购买产康并记录
     */
    private void disablePurchasedProduct(OrderProductionExtendEntity production, Integer orderId, Integer refundId,
                                         List<HeOrderRefundDisabledProductEntity> disabledProductList) {

        // 记录原始状态
        createDisabledProductRecord(orderId, refundId, 0, production.getId(), production.getStatus(), disabledProductList);

        // 更新状态为失效(2)
        production.setStatus(2);
    }

    /**
     * 创建失效产品记录
     *
     * @param orderId             订单ID
     * @param refundId            退款ID
     * @param productType         产品类型：0-购买产康，1-礼赠产康
     * @param productId           产品ID
     * @param sourceStatus        原始状态
     * @param disabledProductList 失效产品记录列表
     */
    private void createDisabledProductRecord(Integer orderId, Integer refundId, Integer productType,
                                             Integer productId, Integer sourceStatus,
                                             List<HeOrderRefundDisabledProductEntity> disabledProductList) {

        HeOrderRefundDisabledProductEntity productEntity = new HeOrderRefundDisabledProductEntity();
        productEntity.setOrderId(orderId);
        productEntity.setRefundId(refundId);
        productEntity.setProductType(productType);
        productEntity.setProductId(productId);
        productEntity.setSourceStatus(sourceStatus);
        disabledProductList.add(productEntity);
    }
}

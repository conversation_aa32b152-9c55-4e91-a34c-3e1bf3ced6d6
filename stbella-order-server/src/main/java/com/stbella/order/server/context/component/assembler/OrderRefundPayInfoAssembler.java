package com.stbella.order.server.context.component.assembler;

import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.order.service.OrderRefundDomainService;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.platform.order.api.refund.req.QueryRefundGoodsReq;
import com.stbella.platform.order.api.refund.res.QueryOrderRefundInfoRes;
import com.stbella.platform.order.api.refund.res.QueryRefundInfoRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.List;


@Component
@Slf4j
@SnowballComponent(name = "订单支付、退款情况", desc = "获取当前订单的退款信息")
public class OrderRefundPayInfoAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderRefundDomainService orderRefundDomainService;


    @Override
    public void run(FlowContext bizContext) {
        QueryRefundGoodsReq queryRefundGoodsReq = bizContext.getAttribute(QueryRefundGoodsReq.class);
        QueryOrderRefundInfoRes result = bizContext.getAttribute(QueryOrderRefundInfoRes.class);
        List<HeOrderRefundEntity> refundEntityList = bizContext.getListAttribute(HeOrderRefundEntity.class);
        //支付记录
        List<HeIncomeRecordEntity> orderIncomeList =  bizContext.getListAttribute(HeIncomeRecordEntity.class);

        Integer orderId = queryRefundGoodsReq.getOrderId();

        QueryRefundInfoRes queryRefundInfoRes = orderRefundDomainService.refundInfoRes(orderId, refundEntityList, orderIncomeList);
        result.setQueryRefundInfoRes(queryRefundInfoRes);

        bizContext.setAttribute(QueryOrderRefundInfoRes.class, result);
    }


    @Override
    public boolean condition(FlowContext context) {
        return (boolean) context.getAttribute(OtherConstant.CONTINUE);
    }
}

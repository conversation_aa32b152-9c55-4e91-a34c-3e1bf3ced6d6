package com.stbella.order.server.context.component.settlement;

import com.alibaba.fastjson.JSON;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OrderRefundNatureEnum;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.service.OrderIncomeDomainService;
import com.stbella.order.domain.order.service.OrderRefundDomainService;
import com.stbella.order.domain.repository.IncomePaidAllocationRepository;
import com.stbella.order.domain.repository.OrderGoodsRepository;
import com.stbella.order.server.order.ProductionCoinRule;
import com.stbella.order.server.order.month.enums.ProductCoinDeductionEnum;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.stbella.order.common.enums.core.OmniPayTypeEnum.PRODUCTION_COIN;

/**
 * 产康金支付相关
 */
@Component
@Slf4j
public class ProductCoinPayAssembler {


    @Resource
    OrderIncomeDomainService incomeDomainService;
    @Resource
    OrderGoodsRepository orderGoodsRepository;
    @Resource
    IncomePaidAllocationRepository incomePaidAllocationRepository;
    @Resource
    OrderRefundDomainService refundDomainService;


    /**
     * 计算产康金可抵扣金额
     *
     * @param order
     * @return
     */
    public Integer fetchOrderAmount(HeOrderEntity order) {

        //设置可以抵扣的产康金金额，产康金金额=商品的可抵扣产康金总额-已支付产康金总额-商品已经分摊的非产康金金额
        if (!OmniOrderTypeEnum.PRODUCTION_ORDER.getCode().equals(order.getOrderType())) {
            return 0;
        }
        //非礼赠商品才可以用产康金抵扣
        List<HeOrderGoodsEntity> orderGoodsEntityList = orderGoodsRepository.getAllItermByOrderId(order.getOrderId()).stream().filter(o -> !o.isGift()).collect(Collectors.toList());
        //产康金可抵扣总额
        Map<Integer, Integer> productCoinPayAmount= fetchOrderGoodsProductCoinAmount(orderGoodsEntityList, order);
        int productCoinAmount = productCoinPayAmount.values().stream().filter(Objects::nonNull).mapToInt(Integer::intValue).sum();
        log.info("订单结算信息组装，订单号：{}，产康金可抵扣总额：{}", order.getOrderSn(), productCoinAmount);
        return Math.max(productCoinAmount, 0);
    }


    private Integer getProductCoinAmount(List<HeOrderGoodsEntity> orderGoodsEntityList, HeOrderEntity order) {

        //商品的折扣比例就是分摊的比例

        //全额抵扣商品
        List<HeOrderGoodsEntity> fullAmountGoodsList = orderGoodsEntityList.stream().filter(o -> {
            if (StringUtil.isBlank(o.getProductionDiscountRule())) {
                return false;
            }
            ProductionCoinRule productionCoinRule = JSON.parseObject(o.getProductionDiscountRule(), ProductionCoinRule.class);
            return ProductCoinDeductionEnum.FULL_AMOUNT.getCode().equals(productionCoinRule.getType());
        }).collect(Collectors.toList());

        //每满减商品
        List<HeOrderGoodsEntity> preFullDeductionGoodsList = orderGoodsEntityList.stream().filter(o -> {
            if (StringUtil.isBlank(o.getProductionDiscountRule())) {
                return false;
            }
            ProductionCoinRule productionCoinRule = JSON.parseObject(o.getProductionDiscountRule(), ProductionCoinRule.class);
            return ProductCoinDeductionEnum.PER_FULL_DEDUCTION.getCode().equals(productionCoinRule.getType());
        }).collect(Collectors.toList());
        //全额可抵扣商品金额等于商品应付单价*数量
        int fullAmountProductCoinAmount = fullAmountGoodsList.stream().map(o -> BigDecimal.valueOf(o.getTotalAllocationOriginPrice())).reduce(BigDecimal.ZERO, BigDecimal::add).intValue();

        //暂时只考虑满减是一样的情况
        if (CollectionUtils.isEmpty(preFullDeductionGoodsList)) {
            return fullAmountProductCoinAmount;
        }

        HeOrderGoodsEntity heOrderGoodsEntity = preFullDeductionGoodsList.get(0);
        String productionDiscountRule = heOrderGoodsEntity.getProductionDiscountRule();
        ProductionCoinRule productionCoinRule = JSON.parseObject(productionDiscountRule, ProductionCoinRule.class);

        Integer limit = productionCoinRule.getLimit();

        Integer reduction = productionCoinRule.getReduction();

        int preFullDeductionProductCoinAmount = preFullDeductionGoodsList.stream().mapToInt(HeOrderGoodsEntity::getTotalAllocationOriginPrice).sum();

        preFullDeductionProductCoinAmount = preFullDeductionProductCoinAmount / (limit * 100) * (reduction * 100);

        log.info("全额抵扣商品金额：{}，满减抵扣商品金额：{}", fullAmountProductCoinAmount, preFullDeductionProductCoinAmount);

        return fullAmountProductCoinAmount + preFullDeductionProductCoinAmount;
    }


    public Map<Integer, Integer> fetchOrderGoodsProductCoinAmount(List<HeOrderGoodsEntity> orderGoodsEntityList, HeOrderEntity order) {


        //全额抵扣商品
        List<HeOrderGoodsEntity> fullAmountGoodsList = orderGoodsEntityList.stream().filter(o -> {
            if (StringUtil.isBlank(o.getProductionDiscountRule())) {
                return false;
            }
            ProductionCoinRule productionCoinRule = JSON.parseObject(o.getProductionDiscountRule(), ProductionCoinRule.class);
            return ProductCoinDeductionEnum.FULL_AMOUNT.getCode().equals(productionCoinRule.getType());
        }).collect(Collectors.toList());

        //每满减商品
        List<HeOrderGoodsEntity> preFullDeductionGoodsList = orderGoodsEntityList.stream().filter(o -> {
            if (StringUtil.isBlank(o.getProductionDiscountRule())) {
                return false;
            }
            ProductionCoinRule productionCoinRule = JSON.parseObject(o.getProductionDiscountRule(), ProductionCoinRule.class);
            return ProductCoinDeductionEnum.PER_FULL_DEDUCTION.getCode().equals(productionCoinRule.getType());
        }).collect(Collectors.toList());

        Map<Integer, Integer> resultMap = new HashMap<>();

        Integer orderId = orderGoodsEntityList.get(0).getOrderId();

        Map<Long, List<IncomePaidAllocationEntity>> incomePaidAllocationEntityMap = getGoodsPaidAllocation(orderId);
        Map<Integer, List<HeOrderRefundGoodsEntity>> refundGoodsMap = getRefundTempMap(orderId);

        //满减的商品产康金分摊计算
        dealPreFull(preFullDeductionGoodsList, incomePaidAllocationEntityMap, refundGoodsMap, resultMap);

        dealFull(fullAmountGoodsList, incomePaidAllocationEntityMap, refundGoodsMap, resultMap);


        return resultMap;
    }

    private void dealFull(List<HeOrderGoodsEntity> fullAmountGoodsList, Map<Long, List<IncomePaidAllocationEntity>> incomePaidAllocationEntityMap, Map<Integer, List<HeOrderRefundGoodsEntity>> refundGoodsMap, Map<Integer, Integer> resultMap) {
        if (CollectionUtils.isEmpty(fullAmountGoodsList)) {
            return;
        }
        Map<Integer, GoodsProductCoinAmount> integerGoodsProductCoinAmountMap = goodsPayMap(fullAmountGoodsList, incomePaidAllocationEntityMap, refundGoodsMap);
        for (HeOrderGoodsEntity heOrderGoodsEntity : fullAmountGoodsList) {
            GoodsProductCoinAmount goodsProductCoinAmount = integerGoodsProductCoinAmountMap.get(heOrderGoodsEntity.getId());
            resultMap.put(heOrderGoodsEntity.getId(), goodsProductCoinAmount.getWaitPayAmount());
        }
    }

    private @NotNull Map<Integer, List<HeOrderRefundGoodsEntity>> getRefundTempMap(Integer orderId) {
        //每个商品退回重付
        List<HeOrderRefundGoodsEntity> orderGoodsRefundList = refundDomainService.querySuccessfulOrderGoodsAllocationRefund(orderId).stream().filter(a -> Objects.nonNull(a.getRefundNature())).filter(refund -> OrderRefundNatureEnum.TEMP_REFUND.code().intValue() == refund.getRefundNature()).collect(Collectors.toList());
        Map<Integer, List<HeOrderRefundGoodsEntity>> refundGoodsMap = orderGoodsRefundList.stream().collect(Collectors.groupingBy(HeOrderRefundGoodsEntity::getOrderGoodsId));
        return refundGoodsMap;
    }

    private @NotNull Map<Long, List<IncomePaidAllocationEntity>> getGoodsPaidAllocation(Integer orderId) {
        //查询已分摊和已经退回重付的数量
        HeOrderEntity heOrderEntity = new HeOrderEntity();

        heOrderEntity.setOrderId(orderId);
        List<HeIncomeRecordEntity> incomeRecordEntities = incomeDomainService.queryEffectiveRecord(heOrderEntity);

        List<Integer> incomeIdList = incomeRecordEntities.stream().map(HeIncomeRecordEntity::getId).collect(Collectors.toList());
        List<IncomePaidAllocationEntity> incomePaidAllocationEntities = incomePaidAllocationRepository.queryListByIncomeIds(incomeIdList);
        //根据商品id分组
        Map<Long, List<IncomePaidAllocationEntity>> incomePaidAllocationEntityMap = incomePaidAllocationEntities.stream().collect(Collectors.groupingBy(IncomePaidAllocationEntity::getOrderGoodsId));
        return incomePaidAllocationEntityMap;
    }

    private void dealPreFull(List<HeOrderGoodsEntity> preFullDeductionGoodsList, Map<Long, List<IncomePaidAllocationEntity>> incomePaidAllocationEntityMap, Map<Integer, List<HeOrderRefundGoodsEntity>> refundGoodsMap, Map<Integer, Integer> resultMap) {
        if (CollectionUtils.isEmpty(preFullDeductionGoodsList)) {
            return;
        }
        //得到每个商品产康金已支付金额、非产康金支付金额，每个商品的退款金额
        ProductionCoinRule productionCoinRule = JSON.parseObject(preFullDeductionGoodsList.get(0).getProductionDiscountRule(), ProductionCoinRule.class);
        Map<Integer, GoodsProductCoinAmount> goodsWaitPayAmountMap = goodsPayMap(preFullDeductionGoodsList, incomePaidAllocationEntityMap, refundGoodsMap);
        // 商品应付总金额
        int preFullTotalAmount = goodsWaitPayAmountMap.values().stream().mapToInt(GoodsProductCoinAmount::getGoodsTotalAmount).sum();
        // 待付总金额
        int waitPayTotalAmount = goodsWaitPayAmountMap.values().stream().mapToInt(GoodsProductCoinAmount::getWaitPayAmount).sum();
        log.info("满减商品总金额：{},待付总金额: {}", preFullTotalAmount, waitPayTotalAmount);
        if (preFullTotalAmount < productionCoinRule.getLimit()) {
            log.info("满减商品总金额小于满减金额，不满足满减条件");
            return;
        }

        //满减抵扣商品产康金分摊计算
        int preFullProductCoin = preFullTotalAmount / (productionCoinRule.getLimit() * 100) * (productionCoinRule.getReduction() * 100);
        //已支付产康金金额
        int sumPreFullProductPaidCoin = goodsWaitPayAmountMap.values().stream().mapToInt(GoodsProductCoinAmount::getProductCoinPaidAmount).sum();
        //剩余待支付金额
        int leftPayCoinAmount = preFullProductCoin - sumPreFullProductPaidCoin;
        if (leftPayCoinAmount <= 0) {
            log.info("满减商品产康金抵扣金额小于等于0，不满足满减条件");
            return;
        }

        int actuallyAllocated = 0;

        // 按未付金额降序排序，便于处理尾差
        List<HeOrderGoodsEntity> sortedGoods = new ArrayList<>(preFullDeductionGoodsList);
        sortedGoods.sort((a, b) -> {
            Integer unPayA = goodsWaitPayAmountMap.get(a.getId()).getWaitPayAmount();
            Integer unPayB = goodsWaitPayAmountMap.get(b.getId()).getWaitPayAmount();
            return unPayB.compareTo(unPayA);
        });

        // 处理除最后一个商品外的所有商品
        for (int i = 0; i < sortedGoods.size() - 1; i++) {
            HeOrderGoodsEntity goods = sortedGoods.get(i);
            GoodsProductCoinAmount goodsProductCoinAmount = goodsWaitPayAmountMap.get(goods.getId());
            // 产康应抵扣金额
            int allocation = (int) Math.round((double) goods.getTotalAllocationOriginPrice() / preFullTotalAmount * preFullProductCoin);

            int leftProductCoinPayAmount = allocation - goodsProductCoinAmount.getProductCoinPaidAmount();
            if (leftProductCoinPayAmount <= 0) {
                log.info("商品id：{}，产康金抵扣金额小于等于0，不满足满减条件", goods.getId());
                continue;
            }
            resultMap.put(goods.getId(), leftProductCoinPayAmount);
            actuallyAllocated += leftProductCoinPayAmount;
        }

        // 最后一个商品处理尾差
        HeOrderGoodsEntity lastGoods = sortedGoods.get(sortedGoods.size() - 1);
        int leftAllocate = leftPayCoinAmount - actuallyAllocated;
        if (leftAllocate > 0) {
            resultMap.put(lastGoods.getId(), leftAllocate);
        }
    }

    private Map<Integer, GoodsProductCoinAmount> goodsPayMap(List<HeOrderGoodsEntity> preFullDeductionGoodsList, Map<Long, List<IncomePaidAllocationEntity>> incomePaidAllocationEntityMap, Map<Integer, List<HeOrderRefundGoodsEntity>> refundGoodsMap) {
        Map<Integer, GoodsProductCoinAmount> goodsWaitPayAmountMap = new HashMap<>();
        for (HeOrderGoodsEntity heOrderGoodsEntity : preFullDeductionGoodsList) {
            Integer id = heOrderGoodsEntity.getId();
            List<IncomePaidAllocationEntity> incomePaidAllocationEntities1 = incomePaidAllocationEntityMap.getOrDefault(id.longValue(), new ArrayList<>());

            int cashPay = incomePaidAllocationEntities1.stream().filter(o -> !PRODUCTION_COIN.getCode().toString().equals(o.getPaymentMethod())).mapToInt(IncomePaidAllocationEntity::getPaidAmount).sum();
            int productCoinPay = incomePaidAllocationEntities1.stream().filter(o -> PRODUCTION_COIN.getCode().toString().equals(o.getPaymentMethod())).mapToInt(IncomePaidAllocationEntity::getPaidAmount).sum();

            List<HeOrderRefundGoodsEntity> heOrderRefundGoodsEntities = refundGoodsMap.getOrDefault(id, new ArrayList<>());

            //产康金退回重付金额
            int productCoinTempRefundAmount = heOrderRefundGoodsEntities.stream().filter(o -> PRODUCTION_COIN.getCode().toString().equals(o.getPaymentMethod())).mapToInt(HeOrderRefundGoodsEntity::getRefundAmount).sum();
            int cashTempRefundAmount = heOrderRefundGoodsEntities.stream().filter(o -> !PRODUCTION_COIN.getCode().toString().equals(o.getPaymentMethod())).mapToInt(HeOrderRefundGoodsEntity::getRefundAmount).sum();

            int totalAmount = heOrderGoodsEntity.getTotalAllocationOriginPrice();

            int waitPayAmount = totalAmount - cashPay - productCoinPay + cashTempRefundAmount + productCoinTempRefundAmount;

            log.info("商品id：{}，剩余待支付金额：{}，商品总金额: {}", id, waitPayAmount, totalAmount);
            GoodsProductCoinAmount goodsProductCoinAmount = new GoodsProductCoinAmount(id, cashPay, productCoinTempRefundAmount, productCoinPay - productCoinTempRefundAmount, waitPayAmount, totalAmount);
            goodsWaitPayAmountMap.put(id, goodsProductCoinAmount);
        }
        return goodsWaitPayAmountMap;
    }


    @Data
    public class GoodsProductCoinAmount {

        private Integer goodsId;


        /**
         * 商品现金金额
         */
        private Integer cashAmount;

        /**
         * 商品产康金退回重付金额
         */
        private Integer productCoinTempRefundAmount;

        /**
         * 商品产康金已支付金额
         */
        private Integer productCoinPaidAmount;

        /**
         * 商品待支付金额
         */
        private Integer waitPayAmount;

        /**
         * 商品总金额
         */
        private Integer goodsTotalAmount;

        public GoodsProductCoinAmount(Integer goodsId, int cashAmount, int productCoinTempRefundAmount, int productCoinPaidAmount, int waitPayAmount, int goodsTotalAmount) {
            this.goodsId = goodsId;
            this.cashAmount = cashAmount;
            this.productCoinTempRefundAmount = productCoinTempRefundAmount;
            this.productCoinPaidAmount = productCoinPaidAmount;
            this.waitPayAmount = waitPayAmount;
            this.goodsTotalAmount = goodsTotalAmount;
        }
    }

}

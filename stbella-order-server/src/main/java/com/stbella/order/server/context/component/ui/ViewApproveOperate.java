package com.stbella.order.server.context.component.ui;

import com.stbella.notice.entity.OaProcessIdRelationPO;
import com.stbella.notice.server.OaProcessIdRelationService;
import com.stbella.order.common.enums.month.OrderButtonEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.server.order.month.res.OrderOperateButton;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-08-12  16:28
 * @Description: 查看审批进度
 */
@Component
@Slf4j
public class ViewApproveOperate implements OrderOperate{


    @Override
    public void setPriority(int priority) {

    }

    @Override
    public int getPriority() {
        return 7;
    }

    /**
     * 生成订单操作按钮
     *
     * @param order
     * @return
     */
    @Override
    public OrderOperateButton generateButton(OrderOperateContext context) {
        // 钉钉审批记录
        List<OaProcessIdRelationPO> approveList = context.getApproveList();
        if (CollectionUtils.isNotEmpty(approveList)) {
            return OrderOperateButton.builder().code(getButtonEnum().getCode()).value(getButtonEnum().getValue()).build();
        }
        return null;
    }

    /**
     * 获取按钮枚举
     *
     * @return
     */
    @Override
    public OrderButtonEnum getButtonEnum() {
        return OrderButtonEnum.APPROVAL_PROGRESS;
    }

    @Override
    public int compareTo(@NotNull IPriority o) {
        return this.getPriority() - o.getPriority();
    }
}

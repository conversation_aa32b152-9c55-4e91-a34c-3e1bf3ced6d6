package com.stbella.order.server.context.component.validator;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.stbella.asset.api.enums.AccountStatus;
import com.stbella.asset.api.enums.AccountType;
import com.stbella.asset.api.res.AccountDto;
import com.stbella.asset.api.res.AccountStreamDto;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.repository.OrderGoodsRepository;
import com.stbella.order.server.config.DynamicConfig;
import com.stbella.order.server.manager.AssetManager;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import com.stbella.store.common.enums.core.GoodsTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@RefreshScope
@SnowballComponent(name = "客户订单对应的资产账户-状态校验器")
public class AssetAccountStatusValidator implements IExecutableAtom<FlowContext> {

    @Resource
    private DynamicConfig dynamicConfig;

    @Resource
    private AssetManager assetManager;

    @Resource
    private OrderGoodsRepository orderGoodsRepository;

    @Override
    public void run(FlowContext bizContext) {

        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);

        Assert.notNull(dynamicConfig.getTimeNodes(), "timeNode must not be null");
        Date date = new Date(dynamicConfig.getTimeNodes() * 1000);
        if(date.after(new Date(orderEntity.getCreatedAt() * 1000))){
            log.info("订单创建时间在当前时间节点之前，不进行资产账户状态校验");
            return;
        }

        CreateRefundReq createRefundReq = bizContext.getAttribute(CreateRefundReq.class);
        AccountStreamDto assetByOrderSnAndUserId = assetManager.getAssetByOrderSnAndUserId(orderEntity.getOrderSn(), orderEntity.getBasicUid().toString());
        if (Objects.nonNull(assetByOrderSnAndUserId)){
            log.info("验证当前用户积分账户状态");
            AccountDto accountDto = assetManager.queryAccount(orderEntity.getBasicUid(), AccountType.INTEGRAL.getCode());
            if (accountDto == null){
                log.warn("积分账户不存在，不进行资产账户状态校验");
                throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code()+"", "账户不存在！");
            }
            if (AccountStatus.DISABLE.getCode().equals(accountDto.getStatus())){
                log.warn("积分账户已冻结，无法退款，请联系客服处理");
                throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "部分资产已冻结，无法退款，请联系客服处理");
            }
        }

        List<CreateRefundReq.GoodsInfo> productionCoinList = createRefundReq.getGoodsInfoList().stream().filter(g -> g.getGoodsType().equals(GoodsTypeEnum.Production_Coin.code())).filter(goodsInfo -> goodsInfo.getRefundNum() > 0).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(productionCoinList)){
            log.info("验证当前用户产康金账户状态");
            AccountDto accountDto = assetManager.queryAccount(orderEntity.getBasicUid(), AccountType.CKJ.getCode());
            if (accountDto == null){
                log.warn("产康金账户不存在，不进行资产账户状态校验");
                throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code()+"", "账户不存在！");
            }
            if (AccountStatus.DISABLE.getCode().equals(accountDto.getStatus())){
                log.warn("产康金账户已冻结，无法退款，请联系客服处理");
                throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "部分资产已冻结，无法退款，请联系客服处理");
            }
        }


        List<HeOrderGoodsEntity> orderGoodsEntityList = orderGoodsRepository.getByOrderIdList(Collections.singletonList(orderEntity.getOrderId()));
        log.info("订单商品列表：{}", JSON.toJSONString(orderGoodsEntityList));
        Map<Integer, HeOrderGoodsEntity> orderGoodsEntityMap = orderGoodsEntityList.stream().collect(Collectors.toMap(HeOrderGoodsEntity::getId, o -> o, (k, v) -> k));

        CreateRefundReq attribute = bizContext.getAttribute(CreateRefundReq.class);
        List<CreateRefundReq.GoodsInfo> goodsInfoList = attribute.getGoodsInfoList();

        List<String> bizCodeList = Lists.newArrayList();
        for (CreateRefundReq.GoodsInfo goodsInfo : goodsInfoList) {
            HeOrderGoodsEntity heOrderGoodsEntity = orderGoodsEntityMap.get(goodsInfo.getOrderGoodsId().intValue());
            if (Objects.isNull(heOrderGoodsEntity) || goodsInfo.getRefundNum() <= 0){
                continue;
            }
            bizCodeList.add(heOrderGoodsEntity.getAssetType());
        }
        assetManager.queryAssetAccountStatus(bizCodeList, orderEntity.getBasicUid().toString());
    }
}

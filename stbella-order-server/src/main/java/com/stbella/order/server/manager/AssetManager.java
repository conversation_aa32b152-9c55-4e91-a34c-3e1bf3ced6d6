package com.stbella.order.server.manager;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.stbella.asset.api.enums.AccountType;
import com.stbella.asset.api.enums.AssetType;
import com.stbella.asset.api.enums.TradeEventEnum;
import com.stbella.asset.api.enums.TradeType;
import com.stbella.asset.api.facade.AccountQueryService;
import com.stbella.asset.api.facade.AccountStreamQueryService;
import com.stbella.asset.api.facade.AssetTradeService;
import com.stbella.asset.api.facade.TradeCmdService;
import com.stbella.asset.api.req.StreamQueryReq;
import com.stbella.asset.api.req.trade.*;
import com.stbella.asset.api.res.AccountDto;
import com.stbella.asset.api.res.AssetAccountDto;
import com.stbella.asset.api.res.ManyTradeRes;
import com.stbella.asset.api.res.SingleTradeRes;
import com.stbella.asset.api.res.*;
import com.stbella.core.base.Operator;
import com.stbella.core.base.PageVO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.customer.server.customer.enums.AssetTypeEnum;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.month.entity.UserEntity;
import com.stbella.order.domain.repository.UserRepository;
import com.stbella.order.server.config.DynamicConfig;
import com.stbella.order.server.order.month.service.impl.OrderAssetTradeService;
import com.stbella.platform.order.api.res.PromotionInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.stbella.asset.api.enums.TradeType.GIFT_COIN_ORDER_CLOSE;
import static com.stbella.asset.api.enums.TradeType.GIFT_COIN_ORDER_PAY;
import static com.stbella.order.common.enums.ErrorCodeEnum.GIFT_ASSET_NOT_ENOUGH_TO_PAY;
import static com.stbella.order.common.enums.ErrorCodeEnum.USER_NOT_EXIST;


/**
 * 资产中心调用
 */
@Component
@Slf4j
public class AssetManager {

    private static final List<Integer> assetTypeList = Lists.newArrayList(AssetType.POSTPARTUM_CARE_ASSET.getCode(), AssetType.SMALL_POSTPARTUM_CARE_ASSET.getCode(), AssetType.YOUNGER_AGE_CARE_ASSET.getCode(), AssetType.EXPATRIATE_NURSING_ASSET.getCode(), AssetType.HOME_CARE_ASSET.getCode());

    @Resource
    private DynamicConfig dynamicConfig;

    @DubboReference
    private AssetTradeService assetTradeService;

    @DubboReference
    private AccountQueryService accountQueryService;

    @Resource
    private UserRepository userRepository;

    @DubboReference
    private AccountStreamQueryService accountStreamQueryService;

    @DubboReference
    private TradeCmdService tradeCmdService;

    @Resource
    private OrderAssetTradeService orderAssetTradeService;



    /**
     * 占用礼赠金账户
     *
     * @param giftAmount
     * @param heOrderEntity
     */
    public void occupyGiftAccount(BigDecimal giftAmount, HeOrderEntity heOrderEntity) {
        SingleTradeReq req = new SingleTradeReq();
        List<SingleTradeReq.AccountInfo> accountList = new ArrayList<>();
        SingleTradeReq.AccountInfo accountInfo = new SingleTradeReq.AccountInfo();
        accountInfo.setTradeType(GIFT_COIN_ORDER_PAY.getCode());
        UserEntity userEntity = userRepository.queryById(heOrderEntity.getCreateBy().intValue());
        if (userEntity == null) {
            log.error("占用礼赠金账户失败,订单号:{},错误信息:{}", heOrderEntity.getOrderSn(), "用户不存在");
            throw new BusinessException(USER_NOT_EXIST.code() + "", USER_NOT_EXIST.desc());
        }
        accountInfo.setUserId(userEntity.getBasicUid() + "");
        accountInfo.setAmount(AmountChangeUtil.convertY2F(giftAmount.multiply(Objects.isNull(heOrderEntity.getFxRate()) ? BigDecimal.ONE : heOrderEntity.getFxRate())));
        accountInfo.setTitle(GIFT_COIN_ORDER_PAY.getName());
        SingleTradeReq.ExtInfo extInfo = new SingleTradeReq.ExtInfo();
        extInfo.setOriginalAmount(AmountChangeUtil.convertY2F(giftAmount));
        extInfo.setOriginalCurrency(heOrderEntity.getCurrency());
        extInfo.setFxRate(heOrderEntity.getFxRate());
        accountInfo.setExt(extInfo);
        accountList.add(accountInfo);
        req.setOrderNo(heOrderEntity.getOrderSn());
        req.setTradeType(GIFT_COIN_ORDER_PAY.getCode());
        req.setUniqueId(heOrderEntity.getOrderSn() + "_" + GIFT_COIN_ORDER_PAY.getName());
        req.setAccountList(accountList);
        req.setOperator(Operator.system());
        log.info("请求资产中心进行交易: req:{}", JSONObject.toJSONString(req));
        Result<SingleTradeRes> singleTradeResResult = assetTradeService.execSingleTrade(req);
        log.info("请求资产中心进行交易: result:{}", JSONObject.toJSONString(singleTradeResResult));
        Boolean success = singleTradeResResult.getSuccess();
        if (!success) {
            log.error("占用礼赠金账户失败,订单号:{},错误信息:{}", heOrderEntity.getOrderSn(), singleTradeResResult.getMsg());
            throw new BusinessException(GIFT_ASSET_NOT_ENOUGH_TO_PAY.code() + "", GIFT_ASSET_NOT_ENOUGH_TO_PAY.desc());
        }
    }

    public void warAreaDistributionGiftGold(String uniqueId, String userId, Long amount, String title) {

        log.info("请求资产中心进行交易: uniqueId:{}, userId:{}, amount:{}, title:{}", uniqueId, userId, amount, title);
        SingleTradeReq.AccountInfo accountInfo = new SingleTradeReq.AccountInfo();
        accountInfo.setAmount(amount);
        accountInfo.setUserId(userId);
        accountInfo.setTradeType(TradeType.AWARD_GIFT_COIN.getCode());
        accountInfo.setTitle(title);

        SingleTradeReq req = new SingleTradeReq();
        req.setTradeType(TradeType.AWARD_GIFT_COIN.getCode());
        req.setAccountList(Collections.singletonList(accountInfo));
        req.setUniqueId(uniqueId);
        Result<SingleTradeRes> singleTradeResResult = assetTradeService.execSingleTrade(req);
        log.info("请求资产中心进行交易: result:{}", JSONObject.toJSONString(singleTradeResResult));
        Assert.isTrue(singleTradeResResult.getSuccess(), "礼品分发失败");
    }

    public AccountDto queryGiftAccount(Integer basicUid) {
        Result<AccountDto> account = accountQueryService.getAccountByUserAndAccountType(basicUid + "", AccountType.GIFT_COIN.getCode().longValue());
        return account.getData();
    }

    public AccountDto queryAccount(Integer basicUid, Integer accountType) {
        log.info("请求资产中心查询账户信息: basicUid:{}, accountType:{}", basicUid, accountType);
        Result<AccountDto> account = accountQueryService.queryAccountByUserAndAccountType(basicUid + "", accountType.longValue());
        log.info("请求资产中心查询账户信息: result:{}", JSONObject.toJSONString(account));
        if (!account.getSuccess() || Objects.isNull(account.getData())){
            return null;
        }
        return account.getData();
    }

    public void giveBackGiftAmount(HeOrderEntity heOrderEntity) {
        SingleTradeReq req = new SingleTradeReq();
        List<SingleTradeReq.AccountInfo> accountList = new ArrayList<>();
        SingleTradeReq.AccountInfo accountInfo = new SingleTradeReq.AccountInfo();
        accountInfo.setTradeType(GIFT_COIN_ORDER_CLOSE.getCode());
        UserEntity userEntity = userRepository.queryById(heOrderEntity.getCreateBy().intValue());
        if (userEntity == null) {
            log.error("归还礼赠金账户失败,订单号:{},错误信息:{}", heOrderEntity.getOrderSn(), "用户不存在");
            throw new BusinessException(USER_NOT_EXIST.code() + "", USER_NOT_EXIST.desc());
        }
        accountInfo.setUserId(userEntity.getBasicUid() + "");
        accountInfo.setAmount(getGiftAmount(heOrderEntity.getGiftAmount(), heOrderEntity.getFxRate()));
        accountInfo.setTitle(GIFT_COIN_ORDER_CLOSE.getName());
        SingleTradeReq.ExtInfo extInfo = new SingleTradeReq.ExtInfo();
        extInfo.setOriginalAmount(heOrderEntity.getGiftAmount().longValue());
        extInfo.setOriginalCurrency(heOrderEntity.getCurrency());
        extInfo.setFxRate(heOrderEntity.getFxRate());
        accountInfo.setExt(extInfo);
        accountList.add(accountInfo);
        req.setOrderNo(heOrderEntity.getOrderSn());
        req.setTradeType(GIFT_COIN_ORDER_CLOSE.getCode());
        req.setUniqueId(heOrderEntity.getOrderSn() + "_" + GIFT_COIN_ORDER_CLOSE.getName());
        req.setAccountList(accountList);
        req.setOperator(Operator.system());
        log.info("请求资产中心进行交易: req:{}", JSONObject.toJSONString(req));
        Result<SingleTradeRes> singleTradeResResult = assetTradeService.execSingleTrade(req);
        log.info("请求资产中心进行交易: result:{}", JSONObject.toJSONString(singleTradeResResult));
        Boolean success = singleTradeResResult.getSuccess();
    }

    /**
     * 查询用户产康金余额
     *
     * @param basicUid
     * @return
     */
    public Integer queryProductCoinBalance(Integer basicUid) {
        Result<AssetAccountDto> accountByUserAndAssetType = accountQueryService.getAccountByUserAndAssetType(basicUid + "", AssetTypeEnum.AVAILABLE_CKJ.getCode().longValue());
        log.info("请求资产中心进行交易: result:{}", JSONObject.toJSONString(accountByUserAndAssetType));
        AssetAccountDto data = accountByUserAndAssetType.getData();
        if (Objects.isNull(data)) {
            return 0;
        }
        return data.getBalance().intValue();
    }

    public Long getGiftAmount(Integer giftAmount, BigDecimal fxRate){

        Assert.notNull(giftAmount, "礼赠金-金额不能为空");
        if(Objects.isNull(fxRate)){
            return giftAmount.longValue();
        }
        return fxRate.multiply(BigDecimal.valueOf(giftAmount)).divide(BigDecimal.ONE, 2, RoundingMode.DOWN).longValue();
    }

    public Boolean execMultiUserTrade(List<HeOrderGoodsEntity> orderGoodsEntityList, HeOrderEntity orderEntity, TradeEventEnum tradeEventEnum, Integer expiredMonth, String flag){

        try {
            log.info("请求资产中心进行批量交易: orderGoodsEntityList:{}, orderEntity:{}", orderGoodsEntityList, orderEntity);
            MultiUserTradeReq req = new MultiUserTradeReq();
            req.setUniqueId(orderEntity.getOrderSn() + tradeEventEnum.getCode().toString() + flag);
            req.setOperator(Operator.system());
            List<UserTradeReq>  tradeReqList = Lists.newArrayList();
            for (HeOrderGoodsEntity orderGoodsEntity : orderGoodsEntityList) {
                if (StringUtils.isEmpty(orderGoodsEntity.getAssetType())) {
                    log.info("订单对应的商品id:{}没有资产类型", orderGoodsEntity.getId());
                    continue;
                }
                Integer assetType = AssetType.getCodeByBizCode(Integer.valueOf(orderGoodsEntity.getAssetType()));
                if (!AssetType.ORDER_ASSET_TYPE.contains(assetType)){
                    log.info("订单对应的商品id:{}配置的资产类型暂不支持接入资产中心", orderGoodsEntity.getId());
                    continue;
                }
                TradeType tradeEvent = TradeEventEnum.getTradeEventEnum(tradeEventEnum, assetType.longValue());
                if (Objects.isNull(tradeEvent)){
                    log.info("订单对应的商品id:{}配置的资产类型未定义交易类型", orderGoodsEntity.getId());
                    continue;
                }
                tradeReqList.add(buildUserTradeReq(orderGoodsEntity, orderEntity, tradeEventEnum, assetType, tradeEvent, expiredMonth));
            }
            req.setTradeList(tradeReqList);
            Result<List<Long>> listResult = tradeCmdService.execMultiUserTrade(req);
            log.info("请求资产中心批量处理订单交易: result:{}", JSONObject.toJSONString(listResult));
            return listResult.getSuccess();
        } catch (Exception e) {
            log.error(String.format("请求资产中心进行批量交易失败, orderSn:%s", orderEntity.getOrderSn()), e);
            return false;
        }
    }

    private UserTradeReq buildUserTradeReq(HeOrderGoodsEntity orderGoodsEntity, HeOrderEntity orderEntity, TradeEventEnum tradeEventEnum, Integer assetType, TradeType tradeEvent, Integer expiredMonth) {

        UserTradeReq req = new UserTradeReq();
        req.setUserId(orderEntity.getBasicUid().toString());
        req.setAssetType(assetType);
        req.setTradeType(tradeEvent.getCode());
        long amount = Objects.nonNull(orderGoodsEntity.getRevocationNum()) && orderGoodsEntity.getRevocationNum() > 0 ? orderGoodsEntity.getRevocationNum() : orderGoodsEntity.getGoodsNum().longValue();
        req.setAmount(amount);
        if (AssetType.EXPATRIATE_NURSING_ASSET.getCode().equals(assetType)){
            req.setAmount(req.getAmount() * dynamicConfig.getExpatriateNursingAsset());
        }
        if (AssetType.HOME_CARE_ASSET.getCode().equals(assetType)){
            req.setAmount(req.getAmount() * dynamicConfig.getHomeCareAsset());
        }
        if(Objects.nonNull(expiredMonth)){
            req.setExpiredMonth(expiredMonth);
            req.setExpiredTime(getEndDateOfDay(expiredMonth));
        }
        req.setOrderNo(orderEntity.getOrderSn());
        req.setAssetId(orderGoodsEntity.getOrderGoodsSn());
        req.setAssetName(orderGoodsEntity.getGoodsName());
        req.setTitle(tradeEventEnum.getName());
        req.setRemark(tradeEvent.getName());
        req.setSourceBizId(orderGoodsEntity.getId().toString());
        req.setUnitValue(Objects.isNull(orderGoodsEntity.getAllocationOriginPrice()) ? 0 :orderGoodsEntity.getAllocationOriginPrice().longValue());
        Map<String, Object> extMap = new HashMap<>();
        extMap.put("bizCode", orderGoodsEntity.getAssetType());
        extMap.put("fxRate", orderEntity.getFxRate());
        extMap.put("currency", orderEntity.getCurrency());
        extMap.put("goodsName", orderGoodsEntity.getGoodsName());
        extMap.put("allocationPrice", AmountChangeUtil.f2YScale2(orderGoodsEntity.getAllocationOriginPrice()));
        if (StringUtils.isNotEmpty(orderGoodsEntity.getParentCombineSn())){
            extMap.put("parentCombineSn", orderGoodsEntity.getParentCombineSn());
        }
        req.setExt(JSONUtil.toJsonStr(extMap));
        return req;
    }


    public void manyTradeExec(List<HeOrderGoodsEntity> orderGoodsEntityList, HeOrderEntity orderEntity, TradeType tradeType, Integer expiredMonth){

        Assert.notNull(dynamicConfig.getTimeNodes(), "timeNode must not be null");
        Date date = new Date(dynamicConfig.getTimeNodes() * 1000);
        if(date.after(new Date(orderEntity.getCreatedAt() * 1000))){
            log.info("该订单创建时间:{}, 超过当前时间节点:{}",orderEntity.getCreatedAt(), dynamicConfig.getTimeNodes());
            return;
        }
        log.info("请求资产中心进行批量交易: orderGoodsEntityList:{}, orderEntity:{}, tradeName:{}", orderGoodsEntityList, orderEntity, tradeType.getName());
        ManyBatchTradeReq req = new ManyBatchTradeReq();
        req.setTradeReqList(Lists.newArrayList());
        req.setUniqueId(String.valueOf(System.currentTimeMillis()));
        req.setOperator(Operator.system());
        List<ManyTradeReq> tradeReqList = Lists.newArrayList();
        orderGoodsEntityList.forEach(orderGoodsEntity -> {
            tradeReqList.add(buildTradeReq(orderGoodsEntity, orderEntity, tradeType, expiredMonth));
        });
        req.setTradeReqList(tradeReqList);
        Result<ManyTradeRes> manyTradeResResult = assetTradeService.manyTradeExec(req);
        log.info("请求资产中心进行批量交易: result:{}", JSONObject.toJSONString(manyTradeResResult));
    }

    public void queryAssetAccountStatus(List<String> bizCodeList, String userId){

        log.info("请求资产中心查询账户状态: bizCodeList:{}, userId:{}", bizCodeList, userId);
        Result<String> result = assetTradeService.queryAssetAccountStatus(bizCodeList, userId);
        if (!result.getSuccess()){
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "部分资产已冻结，无法退款，请联系客服处理");
        }
    }

    public ManyTradeReq buildTradeReq(HeOrderGoodsEntity orderGoodsEntity, HeOrderEntity orderEntity, TradeType tradeType, Integer expiredMonth){

        ManyTradeReq result = new ManyTradeReq();
        if (Objects.nonNull(expiredMonth)) {
            log.info("设置过期时间:{}", expiredMonth);
            result.setExpiredMonth(expiredMonth);
        }
        result.setUserId(String.valueOf(orderGoodsEntity.getBasicUid()));
        result.setTradeType(tradeType.getCode());
        result.setOrderNo(orderEntity.getOrderSn());

        result.setTitle(getTitle(orderGoodsEntity, orderEntity.getOrderType(), tradeType));
        result.setRemark(tradeType.getName());
        result.setAmount(orderGoodsEntity.getGoodsNum().longValue());
        result.setSourceBizId(orderGoodsEntity.getId().toString());

        Map<String, Object> extMap = new HashMap<>();
        extMap.put("bizCode", orderGoodsEntity.getAssetType());
        extMap.put("fxRate", orderEntity.getFxRate());
        extMap.put("currency", orderEntity.getCurrency());
        extMap.put("goodsName", orderGoodsEntity.getGoodsName());
        if (StringUtils.isNotEmpty(orderGoodsEntity.getParentCombineSn())){
            extMap.put("parentCombineSn", orderGoodsEntity.getParentCombineSn());
        }
        result.setExtMap(extMap);
        return result;
    }

    public String getTitle(HeOrderGoodsEntity orderGoodsEntity, Integer orderType, TradeType tradeType){

        String orderName = OmniOrderTypeEnum.getOrderName(orderType);
        if (TradeType.ORDER_RETURN_FAIL.equals(tradeType)){
            return String.format("%s-退款审批拒绝", orderName);
        }
        if (TradeType.ORDER_RETURN.equals(tradeType)){
            return String.format("%s-发起退款审批", orderName);
        }
        if (orderGoodsEntity.getGift() == 1){
            PromotionInfo promotionInfo = orderGoodsEntity.getPromotionInfo();
            if (Objects.isNull(promotionInfo)){
                return String.format("%s-定制礼赠", orderName);
            }
            return String.format("%s-活动礼赠(%s)", orderName, promotionInfo.getPromotionName());
        } else {
            return String.format("%s-购买", orderName);
        }

    }

    public AccountStreamDto getAssetByOrderSnAndUserId(String orderNo, String userId) {

        log.info("查询资产中心结果,orderNo={}, userId={}", orderNo, userId);
        StreamQueryReq req = new StreamQueryReq();
        req.setOrderNo(orderNo);
        req.setTradeTypes(new LinkedHashSet<>(Collections.singleton(TradeType.AVAILABLE_ORDER_PAYED.getCode())));
        req.setUserId(userId);
        Result<PageVO<AccountStreamDto>> result = accountStreamQueryService.listBy(req);
        log.info("查询资产中心结果={}", JSONUtil.parse(result));
        if (result.getSuccess() && ObjectUtil.isNotEmpty(result.getData().getList()) && ObjectUtil.isNotEmpty(result.getData().getList().get(0))) {
            return result.getData().getList().get(0);
        }
        return null;
    }

    public static Date getEndDateOfDay(Integer month) {

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, month);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    public Map<String, Integer> getCountByGoodsSn(String orderSn, List<HeOrderGoodsEntity> allOrderGoods){

        List<AccountDetailDto> accountDetailList = orderAssetTradeService.queryListByOrderNo(orderSn);
        Map<String, Integer> countByGoodsAndSkuId = new HashMap<>();

        if (CollectionUtil.isEmpty(allOrderGoods)) {
            return countByGoodsAndSkuId;
        }

        Map<String, AccountDetailDto> accountDetailMap = accountDetailList.stream().filter(item -> Objects.nonNull(item.getAssetType()) && assetTypeList.contains(item.getAssetType().intValue())).collect(Collectors.toMap(AccountDetailDto::getAssetId, Function.identity(), (v1, v2) -> v2));
        for (HeOrderGoodsEntity orderGoods : allOrderGoods) {
            if (org.apache.commons.lang3.StringUtils.isEmpty(orderGoods.getOrderGoodsSn())){
                log.warn("订单商品的商品货号为空，订单商品id：{}", orderGoods.getId());
                continue;
            }
            AccountDetailDto accountDetailDto = accountDetailMap.get(orderGoods.getOrderGoodsSn());
            if (Objects.isNull(accountDetailDto)){
                log.warn("订单商品的资产信息为空，订单商品id：{}", orderGoods.getId());
                continue;
            }
            int availableAmount = this.getAvailableNum(accountDetailDto);
            countByGoodsAndSkuId.put(orderGoods.getOrderGoodsSn(), availableAmount);
        }
        return countByGoodsAndSkuId;
    }

    private Integer getAvailableNum(AccountDetailDto accountDetailDto) {

        int availableAmount = Objects.isNull(accountDetailDto.getAvailableAmount()) ? 0 : accountDetailDto.getAvailableAmount().intValue();
        if (AssetType.EXPATRIATE_NURSING_ASSET.getCode().equals(accountDetailDto.getAssetType().intValue())){
            availableAmount = availableAmount / dynamicConfig.getExpatriateNursingAsset();
        }
        if (AssetType.HOME_CARE_ASSET.getCode().equals(accountDetailDto.getAssetType().intValue())){
            availableAmount = availableAmount / dynamicConfig.getHomeCareAsset();
        }
        return availableAmount;
    }
}

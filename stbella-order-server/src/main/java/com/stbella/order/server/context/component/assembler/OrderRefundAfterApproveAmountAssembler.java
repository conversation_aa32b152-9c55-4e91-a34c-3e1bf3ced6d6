package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.stbella.month.server.request.RefundApprovalRequest;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.core.*;
import com.stbella.order.common.enums.month.RefundRecordPayStatusEnum;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundGoodsEntity;
import com.stbella.order.domain.order.service.OrderRefundDomainService;
import com.stbella.order.domain.repository.HeOrderRefundGoodsRepository;
import com.stbella.order.domain.repository.OrderRefundRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.context.component.refund.processor.ComplaintApproveAssetBecomesEffectiveProcessor;
import com.stbella.order.server.order.month.enums.RefundTypeEnum;
import com.stbella.order.server.order.month.service.MonthOrderWxCommandService;
import com.stbella.order.server.order.month.service.OrderPayV2Service;
import com.stbella.order.server.utils.service.FlowService;
import com.stbella.pay.server.alipay.enums.AccountTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 退款审批通过
 * 1、发送短信
 * 2、减去冻结金额
 * 3、发起真正退款
 */
@Component
@Slf4j
@SnowballComponent(name = "审批结束解冻支付记录等", desc = "审批结束解冻支付记录等")
public class OrderRefundAfterApproveAmountAssembler implements IExecutableAtom<FlowContext> {


    @Resource
    private OrderPayV2Service orderPayV2Service;
    @Resource
    @Lazy
    private MonthOrderWxCommandService monthOrderWxCommandService;
    @Resource
    private OrderRefundDomainService orderRefundDomainService;
    @Resource
    private FlowService flowService;
    @Resource
    private OrderRefundRepository orderRefundRepository;

    @Resource
    private HeOrderRefundGoodsRepository orderRefundGoodsRepository;

    @Resource
    private ComplaintApproveAssetBecomesEffectiveProcessor complaintApproveAssetBecomesEffectiveProcessor;
    @Resource
    private OrderRepository orderRepository;

    @Override
    public boolean condition(FlowContext context) {
        return (boolean) context.getAttribute(OtherConstant.CONTINUE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void run(FlowContext bizContext) {
        Boolean isRefund = (Boolean) bizContext.getAttribute(OtherConstant.IS_REFUND);
        log.info("isRefund is {}", isRefund);
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        //审批
        RefundApprovalRequest refundApprovalRequest = bizContext.getAttribute(RefundApprovalRequest.class);
        //主退款
        HeOrderRefundEntity refundEntity = bizContext.getAttribute(HeOrderRefundEntity.class);
        log.info("主退款refundSn为 {} 处理金额相关", refundEntity.getRefundOrderSn());
        if (!isRefund) {
            refundFailure(refundEntity);
        } else {
            refundSuccess(bizContext, refundEntity, refundApprovalRequest, orderEntity);
        }
        //同步退款状态
        Integer finalOrderId = refundEntity.getOrderId();
        monthOrderWxCommandService.syncOrderRefundStatusForNew(finalOrderId);
    }

    private void refundSuccess(FlowContext bizContext, HeOrderRefundEntity refundEntity, RefundApprovalRequest refundApprovalRequest, HeOrderEntity orderEntity) {
        RefundTypeEnum enumByCode;
        enumByCode = RefundTypeEnum.getEnumByCode(refundEntity.getRefundMethod());
        switch (enumByCode) {
            case BACK_TRACK:
                //如果是原路退回需要走这个逻辑(走支付宝、微信、pos)
                orderPayV2Service.refundForNew(refundApprovalRequest.getOrderRefundId(), false, AccountTypeEnum.BEI_KANG.getCode());
                break;
            case BACK_ACCOUNT:
                //增加余额
                orderPayV2Service.refundBalance(refundEntity.getId());
                break;
            case OFFLINE_REMITTANCE:
                break;
        }

        List<HeOrderRefundEntity> childRefundList = bizContext.getListAttribute(HeOrderRefundEntity.class);
        //处理退款产康金
        Integer refundNature = refundEntity.getRefundNature();
        if (ObjectUtil.isNotEmpty(refundNature) && !OrderRefundNatureEnum.TEMP_REFUND.code().equals(refundNature)) {
            //将所有的产康金对应的refundGoods表改成退款成功
            ckjRefundGoodsUpdate(orderEntity.getOrderId(), refundEntity.getRefundOrderSn());
            updateCkjChild(childRefundList, refundNature, orderEntity.getOrderId());
            //将所有的减免支付对应的退款商品改成成功
            reductionRefundGoodsUpdate(orderEntity.getOrderId(), refundEntity.getRefundOrderSn());
            updateReductionChild(childRefundList, refundNature);
        }

        //更新主状态
        orderRefundDomainService.updateParentStatus(refundEntity.getId());
        //退款至余额可以直接走流程
        if (enumByCode.equals(RefundTypeEnum.BACK_ACCOUNT) && refundEntity.getApplyAmount() > 0) {
            flowService.fire(BizActivityEnum.ORDER_REFUND_SUCCESS.code(), refundEntity, refundEntity.getOrderId(), "Order", "Refund", "After");
        }
    }

    private void updateCkjChild(List<HeOrderRefundEntity> childRefundList, Integer refundNature, Integer orderId) {
        if (ObjectUtil.isNotEmpty(refundNature) && OrderRefundNatureEnum.TEMP_REFUND.code().equals(refundNature)) {
            log.info("退回重付不处理产康金支付相关");
            return;
        }
        HeOrderEntity orderEntity = orderRepository.queryOrderById(orderId);
        List<HeOrderRefundEntity> cjkRefund = childRefundList.stream().filter(r -> r.getRefundType().equals(OmniRefundTypeEnum.PRODUCTION_COIN.getCode())).collect(Collectors.toList());
        log.info("cjkRefund size:{}", cjkRefund.size());
        if (CollectionUtil.isEmpty(cjkRefund)) {
            return;
        }
        for (HeOrderRefundEntity heOrderRefundEntity : cjkRefund) {
            heOrderRefundEntity.setActualAmount(heOrderRefundEntity.getApplyAmount());
            heOrderRefundEntity.setStatus(OmniRefundApproveEnum.REFUND_RECORD_4.getCode());
            heOrderRefundEntity.setFinishAt(System.currentTimeMillis() / 1000);
            orderEntity.setProductionAmountPay(orderEntity.getProductionAmountPay() - heOrderRefundEntity.getApplyAmount());
            orderEntity.setFreezeAmount(orderEntity.getFreezeAmount() - heOrderRefundEntity.getApplyAmount());
        }
        orderRefundRepository.batchUpdateById(cjkRefund);
        orderRepository.updateOne(orderEntity);
    }

    private void updateReductionChild(List<HeOrderRefundEntity> childRefundList, Integer refundNature) {
        if (ObjectUtil.isNotEmpty(refundNature) && OrderRefundNatureEnum.TEMP_REFUND.code().equals(refundNature)) {
            log.info("退回重付不处理退款减免支付相关");
            return;
        }
        List<HeOrderRefundEntity> reduceList = childRefundList.stream().filter(r -> r.getRefundType().equals(OmniPayTypeEnum.REDUCTION.getCode())).collect(Collectors.toList());
        log.info("reduce size:{}", reduceList.size());
        if (CollectionUtil.isEmpty(reduceList)) {
            return;
        }
        for (HeOrderRefundEntity heOrderRefundEntity : reduceList) {
            heOrderRefundEntity.setActualAmount(heOrderRefundEntity.getApplyAmount());
            heOrderRefundEntity.setStatus(OmniRefundApproveEnum.REFUND_RECORD_4.getCode());
            heOrderRefundEntity.setFinishAt(System.currentTimeMillis() / 1000);
        }
        orderRefundRepository.batchUpdateById(reduceList);
    }

    private void refundFailure(HeOrderRefundEntity refundEntity) {

        //回滚
        orderRefundDomainService.approveFailOrRefuse(refundEntity.getId(), RefundRecordPayStatusEnum.REFUND_RECORD_2);
        //回滚资产
        complaintApproveAssetBecomesEffectiveProcessor.rollBackAssets(refundEntity.getId().longValue());

    }


    private void ckjRefundGoodsUpdate(Integer orderId, String parentRefundSn) {
        List<HeOrderRefundGoodsEntity> heOrderRefundGoodsEntities = orderRefundGoodsRepository.queryByOrderId(orderId);
        List<HeOrderRefundGoodsEntity> ckjRefundGoods = heOrderRefundGoodsEntities.stream()
                .filter(r -> r.getRefundOrderSn().equals(parentRefundSn)
                        && ObjectUtil.isNotEmpty(r.getPayType())
                        && r.getPayType().equals(OmniPayTypeEnum.PRODUCTION_COIN.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(ckjRefundGoods)) {
            for (HeOrderRefundGoodsEntity ckjRefundGood : ckjRefundGoods) {
                ckjRefundGood.setStatus(OrderRefundGoodsStatusEnum.SUCCESS.code());
            }
            orderRefundGoodsRepository.updateList(ckjRefundGoods);
        }
    }


    private void reductionRefundGoodsUpdate(Integer orderId, String parentRefundSn) {
        List<HeOrderRefundGoodsEntity> heOrderRefundGoodsEntities = orderRefundGoodsRepository.queryByOrderId(orderId);
        List<HeOrderRefundGoodsEntity> refundGoodsEntities = heOrderRefundGoodsEntities.stream()
                .filter(r -> r.getRefundOrderSn().equals(parentRefundSn)
                        && ObjectUtil.isNotEmpty(r.getPayType())
                        && r.getPayType().equals(OmniPayTypeEnum.REDUCTION.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(refundGoodsEntities)) {
            for (HeOrderRefundGoodsEntity ckjRefundGood : refundGoodsEntities) {
                ckjRefundGood.setStatus(OrderRefundGoodsStatusEnum.SUCCESS.code());
            }
            orderRefundGoodsRepository.updateList(refundGoodsEntities);
        }
    }


}

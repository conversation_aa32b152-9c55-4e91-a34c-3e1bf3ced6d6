package com.stbella.order.server.context.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.stbella.base.server.ding.response.CreateOrderApproveRecordVO;
import com.stbella.core.base.Operator;
import com.stbella.core.enums.BusinessEnum;
import com.stbella.core.result.Result;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OrderRefundStatusEnum;
import com.stbella.order.common.enums.month.OrderStatusV2Enum;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.common.enums.order.OrderSceneEnum;
import com.stbella.order.common.utils.BPCheckUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.convert.CreateOrderReqConverter;
import com.stbella.order.server.order.month.req.OrderQuery;
import com.stbella.platform.order.api.OrderCreateService;
import com.stbella.platform.order.api.refund.req.CreateSimpleReverseOrderReq;
import com.stbella.platform.order.api.req.CreateOrderReq;
import com.stbella.platform.order.api.req.CustomAttribute;
import com.stbella.platform.order.api.req.DiscountReq;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import com.stbella.platform.order.api.res.CreateOrderRes;
import com.stbella.platform.order.api.res.PromotionInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.annotation.SnowballPlatformAbility;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.FlowIdentity;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-03-26  11:35
 * @Description: 创建订单服务
 */
@Service
@DubboService
@Slf4j
public class OrderCreateServiceImpl implements OrderCreateService {

    @Resource
    CreateOrderReqConverter createOrderReqConverter;
    @Resource
    private OrderRepository orderRepository;


    /**
     * 创建简单反向订单
     *
     * @param req
     * @return
     */
    @Override
    @SnowballPlatformAbility(name = "创建简单反向订单", bizActivity = "CreateOrder", desc = "创建简单反向订单")
    public Result<String> createSimpleReverseOrder(CreateSimpleReverseOrderReq req) {

        FlowIdentity identity = FlowIdentity.builder().bizActivity(BizActivityEnum.CREATE_ORDER.code()).idSlice(BusinessEnum.CARE_CENTER.name()).idSlice("simple_reverse_order").delimiter(":").build();

        FlowContext context = new FlowContext();
        context.setAttribute(CreateSimpleReverseOrderReq.class, req);
        SnowballFlowLauncher.fire(identity, context);
        HeOrderEntity heOrder = context.getAttribute(HeOrderEntity.class);
        return Result.success(heOrder.getOrderSn());
    }

    /**
     * 创建订单
     *
     * @param req
     * @return
     */
    @Override
    public Result<CreateOrderRes> createOrder(CreateOrderReq req) {
        log.info("创建订单请求参数：{}", JSONObject.toJSONString(req));
        // 业务线：场景：订单类型
        FlowIdentity identity = FlowIdentity.builder()
                .bizActivity(BizActivityEnum.CREATE_ORDER.code())
                .idSlice(BusinessEnum.getEnumByCode(req.getBu()).name())
                .idSlice(OrderSceneEnum.from(req.getSceneCode()).name())
                .idSlice(OmniOrderTypeEnum.getByCode(req.getOrderType()).name())
                .build();

        FlowContext context = new FlowContext();
        context.setAttribute(CreateOrderReq.class, req);
        context.setAttribute(BizConstant.ExtraKey.storeId, req.getStoreId());
        context.setAttribute(BizConstant.ExtraKey.scene, req.getScene());
        context.setAttribute(BizConstant.ExtraKey.cartId, req.getCartId());
        context.setAttribute(BizConstant.ExtraKey.clientUid, req.getClientUid());
        List<PromotionInfo> promotionInfos = req.getPromotionInfos();
        if (CollectionUtils.isEmpty(promotionInfos)) {
            promotionInfos = Lists.newArrayList();
        }
        context.setAttribute(BizConstant.ExtraKey.promotionInfos, promotionInfos);
        context.setAttribute(Operator.class, req.getOperator());
        context.setListAttribute(SkuDetailInfo.class, req.getSkuList());
        DiscountReq discountReq = createOrderReqConverter.toDiscountReq(req);
        context.setAttribute(DiscountReq.class, discountReq);
        context.setListAttribute(CustomAttribute.class, req.getExtraInfo().getFulfillExtraList());

        SnowballFlowLauncher.fire(identity, context);

        HeOrderEntity heOrder = context.getAttribute(HeOrderEntity.class);
        CreateOrderRes res = CreateOrderRes.builder().orderId(heOrder.getOrderId()).orderSn(heOrder.getOrderSn()).approveList(new ArrayList<>()).build();

        CreateOrderApproveRecordVO approveRecordVO = context.getAttribute(CreateOrderApproveRecordVO.class);
        if (approveRecordVO != null) {
            List<CreateOrderApproveRecordVO> approveList = Lists.newArrayList();
            approveList.add(approveRecordVO);
            res.setApproveList(approveList);
        }

        return Result.success(res);

    }

    /**
     * 创建押金订单
     *
     * @param req
     * @return
     * @link deposit_order_create.xml
     */
    @Override
    public Result<CreateOrderRes> createDepositOrder(CreateOrderReq req) {

        BPCheckUtil.checkEmptyInBean(new String[]{"bu", "storeId", "sceneCode", "basicUid", "operator"}, req, false);

        //判断是否有押金订单，有直接返回。
        OrderQuery orderQuery = new OrderQuery().setOrderType(OmniOrderTypeEnum.DEPOSIT_ORDER.code()).setBasicUid(req.getBasicUid()).setStoreId(req.getStoreId());
        List<HeOrderEntity> heOrderEntities = orderRepository.queryByCondition(orderQuery);

        //全额退款，或者关闭走新订单
        Optional<HeOrderEntity> ableOrderOpt = heOrderEntities.stream().filter(order -> OrderStatusV2Enum.CLOSE.getCode().intValue() != order.getOrderStatus() && OrderRefundStatusEnum.FULL_REFUND.getCode().intValue() != order.getRefundStatus()).findFirst();
        if (ableOrderOpt.isPresent()) {
            log.info("该用户已存在押金订单，订单号：{}", ableOrderOpt.get().getOrderSn());
            CreateOrderRes res = CreateOrderRes.builder().orderId(ableOrderOpt.get().getOrderId()).orderSn(ableOrderOpt.get().getOrderSn()).build();
            return Result.success(res);
        }

        req.setStaffId(Integer.valueOf(req.getOperator().getOperatorGuid()));


        FlowIdentity identity = FlowIdentity.builder().bizActivity(BizActivityEnum.CREATE_ORDER.code()).idSlice(BusinessEnum.getEnumByCode(req.getBu()).name()).idSlice(OrderSceneEnum.from(req.getSceneCode()).name()).idSlice(OmniOrderTypeEnum.getByCode(req.getOrderType()).name()).build();

        FlowContext context = new FlowContext();
        context.setAttribute(CreateOrderReq.class, req);
        context.setAttribute(BizConstant.ExtraKey.storeId, req.getStoreId());

        SnowballFlowLauncher.fire(identity, context);

        HeOrderEntity heOrder = context.getAttribute(HeOrderEntity.class);
        CreateOrderRes res = CreateOrderRes.builder().orderId(heOrder.getOrderId()).orderSn(heOrder.getOrderSn()).build();

        return Result.success(res);
    }
}

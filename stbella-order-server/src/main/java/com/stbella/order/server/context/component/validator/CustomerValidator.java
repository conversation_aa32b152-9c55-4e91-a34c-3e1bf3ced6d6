package com.stbella.order.server.context.component.validator;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-05-29  17:10
 * @Description: 客户信息验证
 */
@Component
@Slf4j
@SnowballComponent(name = "客户信息验证", desc = "验证客户是否存在，是否是黑名单等")
public class CustomerValidator implements IExecutableAtom<FlowContext> {
    @Override
    public void run(FlowContext bizContext) {

    }
}

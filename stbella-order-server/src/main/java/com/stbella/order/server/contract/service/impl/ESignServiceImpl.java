package com.stbella.order.server.contract.service.impl;

import cn.hutool.core.io.IoUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.codec.Base64;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.stbella.core.base.UserTokenInfoDTO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.core.utils.JwtUtil;
import com.stbella.order.server.config.ContractContextConfig;
import com.stbella.order.server.contract.dto.*;
import com.stbella.order.server.contract.entity.OrderContractConfigPO;
import com.stbella.order.server.contract.service.ESignService;
import com.stbella.order.server.contract.service.OrderContractConfigService;
import com.stbella.order.server.order.cts.enums.YesOrNoEnum;
import com.stbella.order.server.utils.ESignUtils;
import com.stbella.order.server.utils.HTTPHelper;
import com.stbella.order.server.utils.HttpUtils;
import com.stbella.store.server.cts.entity.CtsSitePO;
import com.stbella.store.server.cts.service.CtsSiteService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ESignServiceImpl implements ESignService {
    @Resource
    private OrderContractConfigService orderContractConfigService;

    @DubboReference
    private CtsSiteService ctsSiteService;

    @Override
    public OrderContractConfigPO getConfig() {
        CtsSitePO ctsSitePO = null;
        Boolean value = ContractContextConfig.getValue();
        if (!value){
            UserTokenInfoDTO userTokenInfoDTO = JwtUtil.getJwtTokenUserInfo();
            ctsSitePO = ctsSiteService.getSiteByUserId(userTokenInfoDTO.getUserId());
            if (ctsSitePO == null) {
                throw new BusinessException(ResultEnum.NOT_EXIST, "分站异常");
            }
        }else {
            // 表示培训订单的配置
            return orderContractConfigService.getOne(new LambdaQueryWrapper<OrderContractConfigPO>()
                    .eq(OrderContractConfigPO::getStatus, YesOrNoEnum.YES.getCode())
                    .eq(OrderContractConfigPO::getCtsSiteId, 0));
        }

        return orderContractConfigService.getOne(new LambdaQueryWrapper<OrderContractConfigPO>()
                .eq(OrderContractConfigPO::getStatus, YesOrNoEnum.YES.getCode())
                .eq(OrderContractConfigPO::getCtsSiteId, value ? -1 : ctsSitePO.getId()));
    }

    @Override
    public boolean telecom3Factors(String name, String idNo, String mobileNo) {
        // 接口请求url
        String apiUrl = "/v2/identity/auth/api/individual/telecom3Factors";
        // 调用url

        JSONObject bodys = new JSONObject();
        bodys.put("idNo", idNo);
        bodys.put("name", name);
        bodys.put("mobileNo", mobileNo);
        String json = bodys.toString();

        List<OrderContractConfigPO> list = orderContractConfigService.list(new LambdaQueryWrapper<OrderContractConfigPO>()
                .eq(OrderContractConfigPO::getStatus, YesOrNoEnum.YES.getCode()));
        if (list.isEmpty()) {
            throw new BusinessException(ResultEnum.NOT_EXIST);
        }
        OrderContractConfigPO orderContractConfigPO = list.get(0);
        String url = orderContractConfigPO.getESignHost() + apiUrl;

        LinkedHashMap<String, String> header = getHeader(apiUrl, json, orderContractConfigPO);
        String response = null;
        try {
            response = HTTPHelper.sendPOST(url, json, header, "UTF-8");
        } catch (Exception e) {
            log.warn("调用esign 失败 e{},", json, JSONUtil.toJsonStr(e));
        }
        log.info("=====response:{}", response);
        JSONObject jsonObject = JSON.parseObject(response);
        if (0 == jsonObject.getIntValue("code")) {
            return true;
        } else {
            log.error("调用esign 失败 telecom3Factors req {}, error:{}", json, jsonObject.getString("message"));
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR, jsonObject.getString("message"));
        }
    }

    @Override
    public boolean doPersonIdentityComparison(String name, String idNo) {
        // 接口请求url
        String apiUrl = "/v2/identity/verify/individual/base";
        // 调用url

        JSONObject bodys = new JSONObject();
        bodys.put("idNo", idNo);
        bodys.put("name", name);
        String json = bodys.toString();

        List<OrderContractConfigPO> list = orderContractConfigService.list(new LambdaQueryWrapper<OrderContractConfigPO>()
                .eq(OrderContractConfigPO::getStatus, YesOrNoEnum.YES.getCode()));
        if (list.isEmpty()) {
            throw new BusinessException(ResultEnum.NOT_EXIST);
        }
        OrderContractConfigPO orderContractConfigPO = list.get(0);
        String url = orderContractConfigPO.getESignHost() + apiUrl;

        LinkedHashMap<String, String> header = getHeader(apiUrl, json, orderContractConfigPO);

        String response = null;
        try {
            log.info("开始请求");
            response = HTTPHelper.sendPOST(url, json, header, "UTF-8");
            log.info("请求成功");
        } catch (Exception e) {
            log.warn("调用esign 失败 e{},", json, JSONUtil.toJsonStr(e));
        }
        log.info("=====response:{}", response);
        JSONObject jsonObject = JSON.parseObject(response);
        if (0 == jsonObject.getIntValue("code")) {
            return true;
        } else {
            log.warn("调用esign 失败 doPersonIdentityComparison req {}, error:{}", json, jsonObject.getString("message"));
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR, jsonObject.getString("message"));
        }
    }

    @Override
    public String createPersonSignAccount(PersonAccountDTO personAccountDTO) {
        // 接口请求地址
        String apiUrl = "/v1/accounts/createByThirdPartyUserId";
        // http请求访问url
        OrderContractConfigPO orderContractConfigPO = getConfig();
        String url = orderContractConfigPO.getESignHost() + apiUrl;

        JSONObject bodys = new JSONObject();
        bodys.put("thirdPartyUserId", personAccountDTO.getThirdPartyUserId());
        bodys.put("name", personAccountDTO.getName());
        bodys.put("idType", personAccountDTO.getIdType());
        bodys.put("idNumber", personAccountDTO.getIdNumber());
        bodys.put("mobile", personAccountDTO.getMobile());
        String json = bodys.toString();
        LinkedHashMap<String, String> header = getHeader(apiUrl, json, orderContractConfigPO);

        try {
            String response = HTTPHelper.sendPOST(url, json, header, "UTF-8");
            log.info("eSign=====res:{}", response);
            JSONObject jsonObject = JSON.parseObject(response);
            int code = jsonObject.getIntValue("code");
            if (0 == code) {
                JSONObject data = jsonObject.getJSONObject("data");
                log.info("eSign======accountId:{}", data.toJSONString());
                String accountId = data.getString("accountId");
                return accountId;
            } else {
                if (******** == code) {
                    JSONObject data = jsonObject.getJSONObject("data");
                    log.info("eSign======该账户:{}已经在e签宝注册过", personAccountDTO.getThirdPartyUserId());
                    return data.getString("accountId");
                }
            }
        } catch (Exception e) {
            log.error("调用esign 失败 createPersonSignAccount req {}, error:{}",json,  e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String updatePersonSignAccount(UpdateAccountResquestDTO updateAccountResquest) {
        // 接口请求地址
        String apiUrl = "/v1/accounts/" + updateAccountResquest.getAccountId();
        // http请求访问url
        OrderContractConfigPO orderContractConfigPO = getConfig();
        String url = orderContractConfigPO.getESignHost() + apiUrl;

        JSONObject bodys = new JSONObject();
        bodys.put("name", updateAccountResquest.getName());
        bodys.put("mobile", updateAccountResquest.getPhone());
        String json = bodys.toString();

        // 获取待签名字符串
        String getSignedString = ESignUtils.createPutSignedString(apiUrl, ESignUtils.doContentMD5(json));
        // 获取签名值
        String s = ESignUtils.doSignatureBase64(getSignedString, orderContractConfigPO.getAppSecret());
        // 构建请求头
        LinkedHashMap<String, String> header = new LinkedHashMap<String, String>();
        header.put("X-Tsign-Open-App-Id", orderContractConfigPO.getAppId());
        header.put("X-Tsign-Open-Auth-Mode", "Signature");
        header.put("X-Tsign-Open-Ca-Timestamp", String.valueOf(System.currentTimeMillis()));
        header.put("Accept", "*/*");
        header.put("Content-Type", "application/json;charset=UTF-8");
        header.put("X-Tsign-Open-Ca-Signature", s);
        header.put("Content-MD5", ESignUtils.doContentMD5(json));

        String response = HttpUtils.doNewPut(url, json, header);
        log.info("======update person auth response:{}", response);
        JSONObject jsonObject = JSON.parseObject(response);
        if (0 == jsonObject.getIntValue("code")) {
            JSONObject data = jsonObject.getJSONObject("data");
            return data.getString("accountId");
        }
        log.error("调用esign 失败 updatePersonSignAccount req {}, error:{}",json,  jsonObject.getString("message"));
        return null;
    }

    @Override
    public TemplateResponseDTO doUploadFileUrl(byte[] fileByte, String fileName, OrderContractConfigPO orderContractConfigPO) throws NoSuchAlgorithmException {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        byte[] digest = md5.digest(fileByte);

        // 接口请求地址
        String apiUrl = "/v1/docTemplates/createByUploadUrl";
        // http调用地址
        String url = orderContractConfigPO.getESignHost() + apiUrl;

        JSONObject bodys = new JSONObject();
        bodys.put("contentMd5", new String(Base64.encodeBase64(digest)));
        bodys.put("contentType", "application/pdf");
        bodys.put("fileName", fileName);
        String json = bodys.toString();

        LinkedHashMap<String, String> header = getHeader(apiUrl, json, orderContractConfigPO);

        try {
            String s = HTTPHelper.sendPOST(url, json, header, "UTF-8");
            log.info("doUploadFileUrl================s:{}", s);
            JSONObject jsonObject = JSON.parseObject(s);
            if (0 == jsonObject.getIntValue("code")) {
                JSONObject data = jsonObject.getJSONObject("data");
                TemplateResponseDTO templateResponse = new TemplateResponseDTO();
                templateResponse.setTemplateId(data.getString("templateId"));
                templateResponse.setUploadUrl(data.getString("uploadUrl"));
                return templateResponse;
            }
            log.error("调用esign 失败 doUploadFileUrl req {}, header {}, error:{}",json, JSONUtil.toJsonStr(header), jsonObject.getString("message"));
        } catch (Exception e) {
            log.error("调用esign 失败 doUploadFileUrl req {}, error:{}",json,  e.getMessage());
        }
        return null;
    }

    @Override
    public String uploadFileByUrlAddress(String urlAddress, String fileName, OrderContractConfigPO orderContractConfigPO) {
        try {
            URL url = new URL(urlAddress);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(50000);
            conn.setReadTimeout(50000);
            InputStream inputStream = conn.getInputStream();
            byte[] bytes = IoUtil.readBytes(inputStream);
            return uploadFileStream(bytes, fileName, orderContractConfigPO);
        } catch (Exception e) {
            log.error("调用esign 失败 uploadFileByUrlAddress req {} fileName {}, error:{}",urlAddress, fileName, e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String uploadFileStream(byte[] fileByte, String fileName, OrderContractConfigPO orderContractConfigPO) throws NoSuchAlgorithmException {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        byte[] digest = md5.digest(fileByte);


        TemplateResponseDTO templateResponse = doUploadFileUrl(fileByte, fileName,orderContractConfigPO);
        // 调用url
        String url = templateResponse.getUploadUrl();

        Map<String, String> headers = Maps.newHashMap();
        headers.put("Content-MD5", new String(Base64.encodeBase64(digest)));
        headers.put("Content-Type", "application/pdf");
        String s = HttpUtils.doPut(url, fileByte, headers);
        log.info("fileName {} =============================res:{}", fileName, s);
        return templateResponse.getTemplateId();

    }

    @SneakyThrows
    @Override
    public ContractParamFillResponseDTO doFillContentTemplate(ContractParamFillRequestDTO contractParamFillRequest) {
        // 接口请求地址
        String apiUrl = "/v1/files/createByTemplate";
        // http调用地址
        OrderContractConfigPO orderContractConfigPO = getConfig();
        String url = orderContractConfigPO.getESignHost() + apiUrl;

        JSONObject bodys = new JSONObject();
        ContractParamFillRequestDTO.InnerSimpleFormFieIds simpleFormFieIds =
                contractParamFillRequest.getSimpleFormFieIds();
        Map<String, String> content = simpleFormFieIds.getContent();
        String s1 = JSON.toJSONString(content);
        JSONObject object = JSON.parseObject(s1);
        bodys.put("name", contractParamFillRequest.getName());
        bodys.put("templateId", contractParamFillRequest.getTemplateId());
        bodys.put("simpleFormFields", object);
        String json = bodys.toString();

        // 获取待签名字符串
        String postSignedString = ESignUtils.createPostSignedString(apiUrl, ESignUtils.doContentMD5(json));
        // 获取签名值
        String s = ESignUtils.doSignatureBase64(postSignedString, orderContractConfigPO.getAppSecret());
        // 构建请求头
        LinkedHashMap<String, String> header = new LinkedHashMap<String, String>();
        header.put("X-Tsign-Open-App-Id", orderContractConfigPO.getAppId());
        header.put("X-Tsign-Open-Auth-Mode", "Signature");
        header.put("X-Tsign-Open-Ca-Timestamp", String.valueOf(System.currentTimeMillis()));
        header.put("Accept", "*/*");
        header.put("Content-Type", "application/json;charset=UTF-8");
        header.put("X-Tsign-Open-Ca-Signature", s);
        header.put("Content-MD5", ESignUtils.doContentMD5(json));

        String response = HTTPHelper.sendPOST(url, json, header, "UTF-8");
        log.info("doFillContentTemplate================================res:{}", response);
        JSONObject jsonObject = JSON.parseObject(response);
        if (0 == jsonObject.getIntValue("code")) {
            JSONObject data = jsonObject.getJSONObject("data");
            ContractParamFillResponseDTO contractParamFillResponse = new ContractParamFillResponseDTO();
            contractParamFillResponse.setFileId(data.getString("fileId"));
            contractParamFillResponse.setFileName(data.getString("fileName"));
            return contractParamFillResponse;
        } else {
            log.error("调用esign 失败 doFillContentTemplate req {}, error:{}, header={}",json,  jsonObject.getString("message"),JSONUtil.toJsonStr(header));
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, jsonObject.getString("message"));
        }
    }

    @Override
    public String createCompanyAccount(CompanyAccountRequestDTO companyAccountRequestDTO) {
        // 接口请求地址
        String apiUrl = "/v1/organizations/createByThirdPartyUserId";
        // http请求访问url
        OrderContractConfigPO orderContractConfigPO = getConfig();
        String url = orderContractConfigPO.getESignHost() + apiUrl;

        JSONObject bodys = new JSONObject();
        bodys.put("thirdPartyUserId", companyAccountRequestDTO.getThirdPartyUserId());
        bodys.put("name", companyAccountRequestDTO.getName());
        bodys.put("idType", companyAccountRequestDTO.getIdType());
        bodys.put("idNumber", companyAccountRequestDTO.getIdNumber());
        //bodys.put("creator", companyAccountRequestDTO.getCreator());
        String json = bodys.toString();

        LinkedHashMap<String, String> header = getHeader(apiUrl, json, orderContractConfigPO);

        try {
            String s = HTTPHelper.sendPOST(url, json, header, "UTF-8");
            log.info("==============s:{}", s);
            JSONObject jsonObject = JSON.parseObject(s);
            if (0 == jsonObject.getIntValue("code")) {
                JSONObject data = jsonObject.getJSONObject("data");
                return data.getString("orgId");
            }
            if (******** == jsonObject.getIntValue("code")) {
                JSONObject data = jsonObject.getJSONObject("data");
                return data.getString("orgId");
            }
            log.error("调用esign 失败 createCompanyAccount req {}, error:{}",json,  jsonObject.getString("message"));
        } catch (Exception e) {
            log.error("调用esign 失败 createCompanyAccount req {}, error:{}",json,  e.getMessage());
        }
        return null;
    }

    @Override
    public CompanyAuthenticationDTO doCompanyAuthentication(CompanyAuthenticationRequestDTO companyAuthenticationRequest,
                                                            String orgId) {
        // 接口请求地址
        String apiUrl = "/v2/identity/auth/web/" + orgId + "/orgIdentityUrl";
        // http请求访问url
        OrderContractConfigPO orderContractConfigPO = getConfig();
        String url = orderContractConfigPO.getESignHost() + apiUrl;

        JSONObject bodys = new JSONObject();
        bodys.put("agentAccountId", companyAuthenticationRequest.getAgentAccountId());
        bodys.put("authType", companyAuthenticationRequest.getAuthType());
        String json = bodys.toString();

        LinkedHashMap<String, String> header = getHeader(apiUrl, json, orderContractConfigPO);

        try {
            String s = HTTPHelper.sendPOST(url, json, header, "UTF-8");
            log.info("=========cas:{}", s);
            JSONObject jsonObject = JSON.parseObject(s);
            if (0 == jsonObject.getIntValue("code")) {
                JSONObject data = jsonObject.getJSONObject("data");
                CompanyAuthenticationDTO companyAuthentication = new CompanyAuthenticationDTO();
                companyAuthentication.setFlowId(data.getString("flowId"));
                companyAuthentication.setShortLink(data.getString("shortLink"));
                companyAuthentication.setUrl(data.getString("url"));
                return companyAuthentication;
            }
            log.error("调用esign 失败 createCompanyAccount req {}, error:{}",json,  jsonObject.getString("message"));
        } catch (Exception e) {
            log.error("调用esign 失败 createCompanyAccount req {}, error:{}",json,  e.getMessage());
        }
        return null;
    }

    @Override
    public Boolean doSetCompanyAutoGrantAuthentication(String orgId) {
        // 接口请求地址
        String apiUrl = "/v1/signAuth/" + orgId;
        // http请求访问url
        OrderContractConfigPO orderContractConfigPO = getConfig();
        String url = orderContractConfigPO.getESignHost() + apiUrl;

        LinkedHashMap<String, String> header = findHeader(apiUrl, orderContractConfigPO);

        try {
            String s = HttpUtils.doPost(url, null, header);
            log.info(" doSetCompanyAutoGrantAuthentication ============res:{}", s);
            JSONObject jsonObject = JSON.parseObject(s);
            if (0 == jsonObject.getIntValue("code")) {
                return true;
            }
            log.error("调用esign 失败 doSetCompanyAutoGrantAuthentication req {}, error:{}",apiUrl,  s);
        } catch (Exception e) {
            log.error("调用esign 失败 doSetCompanyAutoGrantAuthentication req {}, error:{}",apiUrl,  e.getMessage());
        }

        return false;
    }

    @Override
    public CompanyAuthenticationResponseDTO getCompanyAuthenticationState(String flowId) {
        // 接口请求地址
        String apiUrl = "/v2/identity/auth/api/common/" + flowId + "/detail";
        // http请求访问url
        OrderContractConfigPO orderContractConfigPO = getConfig();
        String url = orderContractConfigPO.getESignHost() + apiUrl;

        LinkedHashMap<String, String> header = findHeader(apiUrl, orderContractConfigPO);

        try {
            String s = HTTPHelper.sendGet(url, header, "UTF-8");
            log.info("==========================res:{}", s);
            JSONObject jsonObject = JSON.parseObject(s);
            if (0 == jsonObject.getIntValue("code")) {
                JSONObject data = jsonObject.getJSONObject("data");
                CompanyAuthenticationResponseDTO companyAuthenticationResponse = new CompanyAuthenticationResponseDTO();
                companyAuthenticationResponse.setFlowId(data.getString("flowId"));
                companyAuthenticationResponse.setStatus(data.getString("status"));
                return companyAuthenticationResponse;
            }
            log.error("调用esign 失败 searchWordsPosition req {}, error:{}",apiUrl,  jsonObject.getString("message"));
        } catch (Exception e) {
            log.error("调用esign 失败 searchWordsPosition req {}, error:{}",apiUrl,  e.getMessage());
        }
        return null;
    }

    @SneakyThrows
    @Override
    public String doInitiateSigningProcess(ContractSignStartRequestDTO contractSignStartRequestDTO) {
        // 接口请求地址
        String apiUrl = "/api/v2/signflows/createFlowOneStep";
        // http请求访问url
        OrderContractConfigPO orderContractConfigPO = getConfig();
        String url = orderContractConfigPO.getESignHost() + apiUrl;

        ContractSignStartRequestDTO.InnerFlowInfo flowInfo = contractSignStartRequestDTO.getFlowInfo();
        ContractSignStartRequestDTO.InnerFlowInfo.InnerFlowConfigInfo flowConfigInfo = flowInfo.getFlowConfigInfo();

        JSONObject bodys = new JSONObject();
        JSONObject flowConfigInfos = new JSONObject();
        flowConfigInfos.put("noticeDeveloperUrl", flowConfigInfo.getNoticeDeveloperUrl());
        flowConfigInfos.put("noticeType", flowConfigInfo.getNoticeType());
        flowConfigInfos.put("signPlatform", flowConfigInfo.getSignPlatform());
        flowConfigInfos.put("personAvailableAuthTypes", flowConfigInfo.getPersonAvailableAuthTypes());
        flowConfigInfos.put("willTypes", flowConfigInfo.getWillTypes());
        flowConfigInfos.put("batchDropSeal", flowConfigInfo.getBatchDropSeal());

        JSONObject flowInfos = new JSONObject();
        flowInfos.put("autoArchive", flowInfo.getAutoArchive());
        flowInfos.put("autoInitiate", flowInfo.getAutoInitiate());
        flowInfos.put("businessScene", flowInfo.getBusinessScene());
        flowInfos.put("flowConfigInfo", flowConfigInfos);
        bodys.put("flowInfo", flowInfos);

        List<ContractSignStartRequestDTO.InnerSigners> signer = contractSignStartRequestDTO.getSigners();
        JSONArray signerList = new JSONArray();
        signer.forEach(instance -> {
            JSONObject signers = new JSONObject();
            signers.put("platformSign", instance.getPlatformSign());
            JSONObject signAccounts = new JSONObject();
            signAccounts.put("signerAccountId", instance.getSignerAccount().getSignerAccount());
            signAccounts.put("authorizedAccountId", instance.getSignerAccount().getAuthorizedAccountId());
            signers.put("signerAccount", signAccounts);
            List<ContractSignStartRequestDTO.InnerSigners.InnerSignfields> innerSignfields = instance.getSignfields();
            innerSignfields.forEach(item -> {
                JSONArray signfieldList = new JSONArray();
                JSONObject signfield = new JSONObject();
                signfield.put("autoExecute", item.getAutoExecute());
                signfield.put("actorIndentityType", item.getActorIndentityType());
                signfield.put("fileId", item.getFileId());
                signfield.put("sealType", item.getSealType());
                JSONObject innerPosBean = new JSONObject();
                innerPosBean.put("posPage", item.getPosBean().getPosPage());
                innerPosBean.put("posX", item.getPosBean().getPosX());
                innerPosBean.put("posY", item.getPosBean().getPosY());
                signfield.put("posBean", innerPosBean);
                signfieldList.add(signfield);
                signers.put("signfields", signfieldList);
            });
            signerList.add(signers);
            bodys.put("signers", signerList);
        });

        List<ContractSignStartRequestDTO.InnerDocs> docs = contractSignStartRequestDTO.getDocs();
        List<String> fileIds = docs.stream().map(ContractSignStartRequestDTO.InnerDocs::getFileId).collect(Collectors.toList());
        List<String> fileNames = docs.stream().map(ContractSignStartRequestDTO.InnerDocs::getFileName).collect(Collectors.toList());
        JSONArray docList = new JSONArray();
        JSONObject doc = new JSONObject();
        doc.put("fileId", doListToString(fileIds));
        doc.put("fileName", doListToString(fileNames));
        docList.add(doc);

        bodys.put("docs", docList);
        String json = bodys.toString();

        LinkedHashMap<String, String> header = getHeader(apiUrl, json, orderContractConfigPO);
        String s = HTTPHelper.sendPOST(url, json, header, "UTF-8");
        log.info("=================================res:{}", s);
        JSONObject jsonObject = JSON.parseObject(s);
        if (0 == jsonObject.getIntValue("code")) {
            JSONObject data = jsonObject.getJSONObject("data");
            return data.getString("flowId");
        } else {
            log.error("【销售签订合同】e签宝发起合同签约异常:{}", jsonObject.get("message"));
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, jsonObject.getString("message"));
        }
    }

    @SneakyThrows
    @Override
    public SignAddressResponseDTO getSignAddress(String accountId, String flowId) {
        // 接口请求地址
        String apiUrl = "/v1/signflows/" + flowId + "/executeUrl" + "?accountId=" + accountId;
        // http请求访问url
        OrderContractConfigPO orderContractConfigPO = getConfig();
        String url = orderContractConfigPO.getESignHost() + apiUrl;

        LinkedHashMap<String, String> header = findHeader(apiUrl, orderContractConfigPO);

        String s = HTTPHelper.sendGet(url, header, "UTF-8");
        log.info("getSignAddress ============================res:{}", s);
        JSONObject jsonObject = JSON.parseObject(s);
        if (0 == jsonObject.getIntValue("code")) {
            JSONObject data = jsonObject.getJSONObject("data");
            SignAddressResponseDTO signAddressResponse = new SignAddressResponseDTO();
            signAddressResponse.setLongUrl(data.getString("url"));
            signAddressResponse.setShortUrl(data.getString("shortUrl"));
            return signAddressResponse;
        } else {
            log.error("调用esign 失败 getSignAddress req {}, error:{}",apiUrl,  jsonObject.getString("message"));
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, jsonObject.getString("message"));
        }
    }

    @Override
    public SignPositionResponseDTO searchWordsPosition(String keywords, String fileId) {
        // 接口请求地址
        String apiUrl = "/v1/documents/" + fileId + "/searchWordsPosition";
        // http请求访问url
        OrderContractConfigPO orderContractConfigPO = getConfig();
        String url = orderContractConfigPO.getESignHost() + apiUrl + "?keywords=" + keywords;

        LinkedHashMap<String, String> header = findHeader(apiUrl, orderContractConfigPO);

        try {
            String s = HTTPHelper.sendGet(url, header, "UTF-8");
            log.info("searchWordsPosition =============================res:{}", s);
            JSONObject jsonObject = JSON.parseObject(s);
            if (0 == jsonObject.getIntValue("code")) {
                JSONObject data = jsonObject.getJSONObject("data");
                SignPositionResponseDTO signPositionResponse = new SignPositionResponseDTO();
                signPositionResponse.setFileId(data.getString("fileId"));
                signPositionResponse.setKeyword(data.getString("keyword"));
                JSONArray positionList = data.getJSONArray("positionList");
                List<SignPositionResponseDTO.InnerPositionList> innerPositionLists =
                        JSONObject.parseArray(positionList.toJSONString(), SignPositionResponseDTO.InnerPositionList.class);
                signPositionResponse.setPositionList(innerPositionLists);
                return signPositionResponse;
            }
            log.error("调用esign 失败 searchWordsPosition req {}, error:{}",apiUrl,  jsonObject.getString("message"));
        } catch (Exception e) {
            log.error("调用esign 失败 searchWordsPosition req {}, error:{}",apiUrl,  e.getMessage());
        }
        return null;
    }

    @Override
    public SignCompleteResponseDTO getSignStatus(String flowId) {
        // 接口请求地址
        String apiUrl = "/v1/signflows/" + flowId;
        // http请求访问url
        OrderContractConfigPO orderContractConfigPO = getConfig();
        String url = orderContractConfigPO.getESignHost() + apiUrl;

        LinkedHashMap<String, String> header = findHeader(apiUrl, orderContractConfigPO);

        try {
            String s = HTTPHelper.sendGet(url, header, "UTF-8");
            log.info("getSignStatus =========================res:{}", s);
            JSONObject jsonObject = JSONObject.parseObject(s);
            if (0 == jsonObject.getIntValue("code")) {
                JSONObject data = jsonObject.getJSONObject("data");
                SignCompleteResponseDTO signCompleteResponse = new SignCompleteResponseDTO();
                signCompleteResponse.setAppId(data.getString("appId"));
                signCompleteResponse.setFlowId(data.getString("flowId"));
                signCompleteResponse.setFlowStatus(data.getIntValue("flowStatus"));
                signCompleteResponse.setFlowDesc(data.getString("flowDesc"));
                return signCompleteResponse;
            }
            log.error("调用esign 失败 getSignStatus req {}, error:{}",apiUrl,  jsonObject.getString("message"));
        } catch (Exception e) {
            log.error("调用esign 失败 getSignStatus req {}, error:{}",apiUrl,  e.getMessage());
        }
        return null;
    }

    @Override
    public DownloadUrlResponseDTO downloadContractFile(String flowId) {
        // 接口请求地址
        String apiUrl = "/v1/signflows/" + flowId + "/documents";
        // http请求访问url
        OrderContractConfigPO orderContractConfigPO = getConfig();
        String url = orderContractConfigPO.getESignHost() + apiUrl;

        LinkedHashMap<String, String> header = findHeader(apiUrl, orderContractConfigPO);

        try {
            String s = HTTPHelper.sendGet(url, header, "UTF-8");
            log.info("=========================res:{}", s);
            JSONObject jsonObject = JSONObject.parseObject(s);
            if (0 == jsonObject.getIntValue("code")) {
                JSONObject data = jsonObject.getJSONObject("data");
                JSONArray docs = data.getJSONArray("docs");
                DownloadUrlResponseDTO downloadUrlResponse = new DownloadUrlResponseDTO();
                List<DownloadUrlResponseDTO> downloadUrlResponseDTO =
                        JSONObject.parseArray(docs.toJSONString(), DownloadUrlResponseDTO.class);
                // TODO e签宝文档支持一个签署流程签多份文件，目前业务及当前版本只有一个签署文件，故未做list接收
                downloadUrlResponse.setFileId(downloadUrlResponseDTO.get(0).getFileId());
                downloadUrlResponse.setFileName(downloadUrlResponseDTO.get(0).getFileName());
                downloadUrlResponse.setFileUrl(downloadUrlResponseDTO.get(0).getFileUrl());
                return downloadUrlResponse;
            }
            log.error("调用esign 失败 downloadContractFile req {}, error:{}",apiUrl,  jsonObject.getString("message"));
        } catch (Exception e) {
            log.error("调用esign 失败 downloadContractFile req {}, error:{}",apiUrl,  e.getMessage());
        }
        return null;
    }

    @Override
    public Boolean cancellationAccount(String accountId) {
        // 接口请求地址
        String apiUrl = "/v1/accounts/" + accountId;
        // http请求访问url
        OrderContractConfigPO orderContractConfigPO = getConfig();
        String url = orderContractConfigPO.getESignHost() + apiUrl;

        // get请求或者post请求参数在路径上body无参数时contentMD5为''
        String contentMd5 = "{}";

        // 获取待签名字符串
        String getSignedString = ESignUtils.createDeleteSignedString(apiUrl);
        // 获取签名值
        String s = ESignUtils.doSignatureBase64(getSignedString, orderContractConfigPO.getAppSecret());
        // 构建请求头
        LinkedHashMap<String, String> header = new LinkedHashMap<String, String>();
        header.put("X-Tsign-Open-App-Id", orderContractConfigPO.getAppId());
        header.put("X-Tsign-Open-Auth-Mode", "Signature");
        header.put("X-Tsign-Open-Ca-Timestamp", String.valueOf(System.currentTimeMillis()));
        header.put("Accept", "*/*");
        header.put("Content-Type", "application/json;charset=UTF-8");
        header.put("X-Tsign-Open-Ca-Signature", s);
        header.put("Content-MD5", contentMd5);

        String response = HttpUtils.doDelete(url, header);
        log.info("cancellationAccount ===============================res:{}", response);
        JSONObject jsonObject = JSON.parseObject(response);
        if (0 == jsonObject.getIntValue("code")) {
            return true;
        } else {
            log.error("调用esign 失败 cancellationAccount req {}, error:{}",apiUrl,  jsonObject.getString("message"));
            return false;
        }
    }

    @Override
    public Boolean cancelSignFlow(String flowId) {
        // 接口请求地址
        String apiUrl = "/v1/signflows/" + flowId + "/revoke";
        // http请求访问url
        OrderContractConfigPO orderContractConfigPO = getConfig();
        String url = orderContractConfigPO.getESignHost() + apiUrl;

        // get请求或者post请求参数在路径上body无参数时contentMD5为''
        String contentMd5 = "{}";

        // 获取待签名字符串
        String getSignedString = ESignUtils.createNewPutSignedString(apiUrl);
        // 获取签名值
        String s = ESignUtils.doSignatureBase64(getSignedString, orderContractConfigPO.getAppSecret());
        // 构建请求头
        LinkedHashMap<String, String> header = new LinkedHashMap<String, String>();
        header.put("X-Tsign-Open-App-Id", orderContractConfigPO.getAppId());
        header.put("X-Tsign-Open-Auth-Mode", "Signature");
        header.put("X-Tsign-Open-Ca-Timestamp", String.valueOf(System.currentTimeMillis()));
        header.put("Accept", "*/*");
        header.put("Content-Type", "application/json;charset=UTF-8");
        header.put("X-Tsign-Open-Ca-Signature", s);
        header.put("Content-MD5", contentMd5);

        String response = HttpUtils.doNewCancelPut(url, header);
        log.info("cancelSignFlow===============================res:{}", response);
        JSONObject jsonObject = JSON.parseObject(response);
        if (0 == jsonObject.getIntValue("code")) {
            return true;
        }
        log.error("调用esign 失败 cancelSignFlow req {}, error:{}",apiUrl,  jsonObject.getString("message"));
        return false;
    }

    /**
     * 获取post请求签名鉴权方式的请求头，此种方式无需携带token
     *
     * @param apiUrl 接口请求地址
     * @param bodys  请求参数
     */
    private LinkedHashMap<String, String> getHeader(String apiUrl, String bodys, OrderContractConfigPO orderContractConfigPO) {
        // 获取待签名字符串
        String postSignedString = ESignUtils.createPostSignedString(apiUrl, ESignUtils.doContentMD5(bodys));
        // 获取签名值
        String s = ESignUtils.doSignatureBase64(postSignedString, orderContractConfigPO.getAppSecret());
        // 构建请求头
        LinkedHashMap<String, String> header = new LinkedHashMap<String, String>();
        header.put("X-Tsign-Open-App-Id", orderContractConfigPO.getAppId());
        header.put("X-Tsign-Open-Auth-Mode", "Signature");
        header.put("X-Tsign-Open-Ca-Timestamp", String.valueOf(System.currentTimeMillis()));
        header.put("Accept", "*/*");
        header.put("Content-Type", "application/json;charset=UTF-8");
        header.put("X-Tsign-Open-Ca-Signature", s);
        header.put("Content-MD5", ESignUtils.doContentMD5(bodys));

        return header;
    }

    /**
     * 获取get请求或者post请求参数在路径上body无参数时请求签名鉴权方式的请求头，此种方式无需携带token
     *
     * @param apiUrl 接口请求地址
     * @return map
     */
    public LinkedHashMap<String, String> findHeader(String apiUrl, OrderContractConfigPO orderContractConfigPO) {
        // get请求或者post请求参数在路径上body无参数时contentMD5为''
        String contentMd5 = "";

        // 获取待签名字符串
        String getSignedString = ESignUtils.createGetSignedString(apiUrl);
        // 获取签名值
        String s = ESignUtils.doSignatureBase64(getSignedString, orderContractConfigPO.getAppSecret());
        // 构建请求头
        LinkedHashMap<String, String> header = new LinkedHashMap<String, String>();
        header.put("X-Tsign-Open-App-Id", orderContractConfigPO.getAppId());
        header.put("X-Tsign-Open-Auth-Mode", "Signature");
        header.put("X-Tsign-Open-Ca-Timestamp", String.valueOf(System.currentTimeMillis()));
        header.put("Accept", "*/*");
        header.put("Content-Type", "application/json;charset=UTF-8");
        header.put("X-Tsign-Open-Ca-Signature", s);
        header.put("Content-MD5", contentMd5);

        return header;
    }

    /**
     * 将List<String>转为String
     *
     * @param list
     * @return
     */
    private String doListToString(List<String> list) {
        StringBuilder result = new StringBuilder();
        boolean flag = true;
        for (String string : list) {
            if (flag) {
                flag = false;
            } else {
                result.append(",");
            }
            result.append(string);
        }
        return result.toString();
    }

    @Override
    public boolean doPersonIdentityComparison(String name, String idNo, String mobileNo) {
        // 接口请求url
        String apiUrl = "/v2/identity/verify/individual/telecom3Factors";
        // 调用url

        JSONObject bodys = new JSONObject();
        bodys.put("idNo", idNo);
        bodys.put("name", name);
        bodys.put("mobileNo", mobileNo);
        String json = bodys.toString();

        List<OrderContractConfigPO> list = orderContractConfigService.list(new LambdaQueryWrapper<OrderContractConfigPO>()
                .eq(OrderContractConfigPO::getStatus, YesOrNoEnum.YES.getCode()));
        if (list.isEmpty()) {
            throw new BusinessException(ResultEnum.NOT_EXIST);
        }
        OrderContractConfigPO orderContractConfigPO = list.get(0);
        String url = orderContractConfigPO.getESignHost() + apiUrl;

        LinkedHashMap<String, String> header = getHeader(apiUrl, json, orderContractConfigPO);

        String response = null;
        try {
            log.info("开始请求");
            response = HTTPHelper.sendPOST(url, json, header, "UTF-8");
            log.info("请求成功");
        } catch (Exception e) {
            log.warn("调用esign 失败 e{},", json, JSONUtil.toJsonStr(e));
        }
        log.info("=====response:{}", response);
        JSONObject jsonObject = JSON.parseObject(response);
        if (0 == jsonObject.getIntValue("code")) {
            return true;
        } else {
            log.warn("调用esign 失败 doPersonIdentityComparison req {}, error:{}", json, jsonObject.getString("message"));
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR, "信息比对不通过，请保证与客户本人(或委托人)姓名、手机号以及身份证信息一致");
        }
    }

}

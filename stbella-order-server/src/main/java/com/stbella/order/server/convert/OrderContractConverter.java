package com.stbella.order.server.convert;

import cn.hutool.core.date.DateUtil;
import com.stbella.contract.model.req.v3.ContractCreateV3Req;
import com.stbella.order.common.enums.month.ContractTypeEnum;
import com.stbella.order.common.enums.month.TemplateTypeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.platform.order.api.contract.req.ContractBaseReq;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;


@Mapper(componentModel = "spring", uses = AppImageFormater.class, imports = {DateUtil.class, ContractTypeEnum.class, TemplateTypeEnum.class})
public interface OrderContractConverter {

    @Mappings({
            @Mapping(source = "contractBaseReq.contractType", target = "contractType"),
            @Mapping(source = "order.orderId", target = "guideId"),
            @Mapping(target = "guideType", constant = "1"),
            @Mapping(source = "order.clientUid", target = "clientUid"),
            @Mapping(source = "order.staffId", target = "staffId"),
            @Mapping(source = "order.storeId", target = "storeId"),
            @Mapping(source = "contractBaseReq.templateType", target = "templateType"),
            @Mapping(target = "version",ignore = true),
            @Mapping(target = "source",ignore = true),
    })
    ContractCreateV3Req toContractCreateV3Req(ContractBaseReq contractBaseReq, HeOrderEntity order);
}

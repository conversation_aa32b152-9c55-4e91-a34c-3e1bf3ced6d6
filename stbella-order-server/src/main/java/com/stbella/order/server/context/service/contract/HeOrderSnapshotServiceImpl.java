package com.stbella.order.server.context.service.contract;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.stbella.contract.common.utils.BeanMapper;
import com.stbella.contract.model.enums.AuthTypeEnum;
import com.stbella.contract.model.enums.OrderSignTypeEnum;
import com.stbella.contract.model.enums.TemplateContractTypeEnum;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.customer.server.ecp.entity.TabClientBailorPO;
import com.stbella.customer.server.ecp.request.ClientCardTypeRequest;
import com.stbella.customer.server.ecp.request.ClientSearchByIdRequest;
import com.stbella.customer.server.ecp.service.TabClientBailorService;
import com.stbella.customer.server.ecp.service.TabClientService;
import com.stbella.customer.server.ecp.vo.ClientCardTypeVO;
import com.stbella.customer.server.ecp.vo.ClientInfoVO;
import com.stbella.customer.server.ecp.vo.HeUserCardVO;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.HeOrderBailorSnapshotEntity;
import com.stbella.order.domain.order.month.entity.HeOrderBailorSnapshotExtEntity;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.domain.repository.OrderBailorSnapshotRepository;
import com.stbella.order.domain.repository.OrderUserSnapshotRepository;
import com.stbella.order.server.manager.ContractManager;
import com.stbella.order.server.manager.StoreManager;
import com.stbella.order.server.manager.TabClientManager;
import com.stbella.order.server.order.month.req.OrderMonthClientBailorReq;
import com.stbella.order.server.order.month.req.OrderMonthClientReq;
import com.stbella.order.server.order.month.response.ClientBooleanVO;
import com.stbella.order.server.order.month.response.OrderUserSnapshotVO;
import com.stbella.order.server.order.month.response.TabClientVO;
import com.stbella.order.server.utils.SensitiveInformationUtil;
import com.stbella.platform.order.api.contract.api.HeOrderSnapshotService;
import com.stbella.platform.order.api.contract.req.ButtonUpdateReq;
import com.stbella.platform.order.api.contract.req.ClientCardTypeReq;
import com.stbella.platform.order.api.contract.req.ContractAuthTypeUpdateReq;
import com.stbella.platform.order.api.contract.req.OrderUserSnapshotReq;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import com.stbella.platform.order.api.contract.res.OrderContractSnapshotVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单记录表（草稿表） 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2021-12-10
 */
@Service
@Slf4j
public class HeOrderSnapshotServiceImpl implements HeOrderSnapshotService {

    @Resource
    private TabClientManager tabClientManager;
    @Resource
    private OrderBailorSnapshotRepository orderBailorSnapshotRepository;
    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;
    @Resource
    private StoreManager storeManager;

    @DubboReference
    private TabClientService tabClientService;

    @DubboReference
    private TabClientBailorService tabClientBailorService;

    @Resource
    private ContractManager contractManager;


    @Override
    public OrderContractSnapshotVO updateSnapshot(OrderUserSnapshotReq req) {

        OrderContractSnapshotVO result = new OrderContractSnapshotVO();

        try {

            List<Integer> orderSignList = Arrays.asList(OrderSignTypeEnum.SIGN_TYPE_CLIENT.code(), OrderSignTypeEnum.SIGN_TYPE_BAILOR.code());
            Integer signType = req.getSignType();
            Assert.isTrue(Objects.nonNull(signType) && orderSignList.contains(signType), "签署类型不合法");
            Integer authType = req.getAuthType();
            Assert.isTrue(Objects.nonNull(authType) && Arrays.asList(AuthTypeEnum.AUTH_EMAIL.code(), AuthTypeEnum.AUTH_CH_IDCARD.code()).contains(authType), "认证类型不能为空");
            if (OrderSignTypeEnum.SIGN_TYPE_CLIENT.code().equals(signType)) {
                OrderMonthClientReq clientInfoById = tabClientManager.getClientInfoById(req.getClientUid());
                if (Objects.equals(authType, AuthTypeEnum.AUTH_CH_IDCARD.code())) {
                    if (Objects.equals(clientInfoById.getIsCardVerify(), 1)) {
                        Assert.isTrue(clientInfoById.getIdCard().equals(req.getIdCard()), "实名认证成功后，证件号不能修改");
                    }
                } else {
                    if (StringUtils.isNotEmpty(clientInfoById.getEmail()) && Objects.nonNull(clientInfoById.getAuthType()) && Objects.equals(clientInfoById.getAuthType(), AuthTypeEnum.AUTH_EMAIL.code()) && Objects.equals(clientInfoById.getEmailVerify(), 1)) {
                        Assert.isTrue(clientInfoById.getEmail().equals(req.getEmail()), "邮箱认证成功，邮箱地址不可修改");
                    }
                }
                Boolean updateClientInfoResult = tabClientManager.updateClientInfoForOmni(req);
                if (!updateClientInfoResult) {
                    throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), "网络连接不稳定，请及时联系相关管理员处理");
                }
                HeOrderUserSnapshotEntity map = BeanMapper.map(req, HeOrderUserSnapshotEntity.class);
                map.setAuthType(null);
                HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(map.getOrderId());
                if (Objects.nonNull(heOrderUserSnapshotEntity) && Objects.isNull(heOrderUserSnapshotEntity.getAuthType())) {
                    map.setAuthType(req.getAuthType());
                }
                orderUserSnapshotRepository.updateOne(map);
            } else {
                OrderMonthClientBailorReq bailorReq = BeanMapper.map(req, OrderMonthClientBailorReq.class);
                bailorReq.setVerifyState(req.getEmailVerify());
                Integer bailor = tabClientManager.createBailor(bailorReq);
                orderBailorSnapshotRepository.insertOrUpdateOne(buildHeOrderBailorSnapshotEntity(req), bailor, req.getOrderId());
            }
            result.setResult(Boolean.TRUE);
        } catch (Exception e) {
            result.setResult(Boolean.FALSE);
            log.error("提交认证订单合同主体信息请求处理异常", e);
            result.setFailMsg(e.getMessage());
        }

        return result;
    }

    private HeOrderBailorSnapshotEntity buildHeOrderBailorSnapshotEntity(OrderUserSnapshotReq clientBailor) {

        HeOrderBailorSnapshotEntity entity = new HeOrderBailorSnapshotEntity();
        entity.setClientUid(clientBailor.getClientUid());
        entity.setName(clientBailor.getName());
        entity.setPhoneType(clientBailor.getPhoneType());
        entity.setPhone(clientBailor.getPhone());
        entity.setCertType(clientBailor.getCertType());
        entity.setIdCard(clientBailor.getIdCard());
        entity.setIdCardFront(clientBailor.getIdCardFront());
        entity.setIdCardBack(clientBailor.getIdCardBack());
        entity.setIsCardVerify(clientBailor.getIsCardVerify());
        entity.setIsPhoneVerify(clientBailor.getIsPhoneVerify());
        entity.setAuthType(clientBailor.getAuthType());
        entity.setEmail(clientBailor.getEmail());
        return entity;
    }

    @Override
    public OrderUserSnapshotVO queryBailorInfo(OrderUserSnapshotReq req) {
        HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity = orderBailorSnapshotRepository.queryByOrderId(req.getOrderId());
        if (Objects.isNull(heOrderBailorSnapshotEntity)) {
            return null;
        }
        ClientSearchByIdRequest clientSearchByIdRequest = new ClientSearchByIdRequest();
        clientSearchByIdRequest.setId(getBailorId(heOrderBailorSnapshotEntity, req.getAuthType()));
        Result<TabClientBailorPO> tabClientBailorPOResult = tabClientBailorService.queryBailorInfo(clientSearchByIdRequest);
        Assert.isTrue(tabClientBailorPOResult.getSuccess(), "获取委托人信息失败");
        TabClientBailorPO tabClientBailorPO = tabClientBailorPOResult.getData();
        if (tabClientBailorPO.getAuthType().equals(req.getAuthType()) || Objects.isNull(req.getAuthType())){
            heOrderBailorSnapshotEntity.setBailorId(tabClientBailorPO.getId().intValue());
            heOrderBailorSnapshotEntity.setCertType(tabClientBailorPO.getCertType());
            heOrderBailorSnapshotEntity.setIdCard(tabClientBailorPO.getIdCard());
            heOrderBailorSnapshotEntity.setIdCardFront(tabClientBailorPO.getIdCardFront());
            heOrderBailorSnapshotEntity.setIdCardBack(tabClientBailorPO.getIdCardBack());
            heOrderBailorSnapshotEntity.setAuthType(tabClientBailorPO.getAuthType());
            heOrderBailorSnapshotEntity.setEmail(tabClientBailorPO.getEmail());
            heOrderBailorSnapshotEntity.setPhone(tabClientBailorPO.getPhone());
        } else {
            heOrderBailorSnapshotEntity.setBailorId(null);
            heOrderBailorSnapshotEntity.setCertType(null);
            heOrderBailorSnapshotEntity.setIdCard(null);
            heOrderBailorSnapshotEntity.setIdCardFront(null);
            heOrderBailorSnapshotEntity.setIdCardBack(null);
            heOrderBailorSnapshotEntity.setAuthType(req.getAuthType());
            heOrderBailorSnapshotEntity.setIsCardVerify(null);
            heOrderBailorSnapshotEntity.setEmail(null);
            heOrderBailorSnapshotEntity.setPhone(null);
        }
        OrderUserSnapshotVO bailorVO = BeanMapper.map(heOrderBailorSnapshotEntity, OrderUserSnapshotVO.class);
        bailorVO.setPhoneStr(SensitiveInformationUtil.conductPhoneOrIdCardNo(bailorVO.getPhone()));
        return bailorVO;
    }

    private Integer getBailorId(HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity, Integer authType){

        Integer bailorId = Objects.isNull(heOrderBailorSnapshotEntity.getOldBailorId()) ? heOrderBailorSnapshotEntity.getBailorId() : heOrderBailorSnapshotEntity.getOldBailorId();
        try {
            if (StringUtils.isEmpty(heOrderBailorSnapshotEntity.getExt()) || Objects.isNull(authType)) {
                return bailorId;
            }
            List<HeOrderBailorSnapshotExtEntity> heOrderBailorSnapshotExtEntities = JSON.parseArray(heOrderBailorSnapshotEntity.getExt(), HeOrderBailorSnapshotExtEntity.class);

            if (CollectionUtils.isEmpty(heOrderBailorSnapshotExtEntities)) {
                return bailorId;
            }
            HeOrderBailorSnapshotExtEntity heOrderBailorSnapshotExtEntity = heOrderBailorSnapshotExtEntities.stream().filter(ext -> ext.getAuthType().equals(authType)).findFirst().orElse(null);
            return Objects.isNull(heOrderBailorSnapshotExtEntity) ? bailorId : heOrderBailorSnapshotExtEntity.getBailorId();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return bailorId;
        }
    }

    @Override
    public TabClientVO queryClientInfo(OrderUserSnapshotReq req) {
        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(req.getOrderId());
        if (Objects.isNull(heOrderUserSnapshotEntity)) {
            return null;
        }
        Assert.isTrue(Objects.nonNull(heOrderUserSnapshotEntity.getClientUid()), "客户ID不能为空");
        ClientInfoVO clientInfoVO = tabClientManager.getClientInfoVOByIdForAuth(heOrderUserSnapshotEntity.getClientUid(), req.getAuthType());
        Assert.isTrue(Objects.nonNull(clientInfoVO), "客户信息不能为空");
        heOrderUserSnapshotEntity.setName(clientInfoVO.getName());
        heOrderUserSnapshotEntity.setEmailVerify(Objects.isNull(clientInfoVO.getEmailVerify()) ? 0 : clientInfoVO.getEmailVerify());
        heOrderUserSnapshotEntity.setIsCardVerify(clientInfoVO.getIsCardVerify());
        buildHeOrderUserSnapshotEntity(heOrderUserSnapshotEntity, clientInfoVO);
        TabClientVO tabClientVO = BeanMapper.map(heOrderUserSnapshotEntity, TabClientVO.class);
        tabClientVO.setPhoneStr(SensitiveInformationUtil.conductPhoneOrIdCardNo(tabClientVO.getPhone()));

        CfgStoreEntity cfgStore = storeManager.queryByStoreId(heOrderUserSnapshotEntity.getStoreId());
        if (Objects.nonNull(cfgStore)) {
            tabClientVO.setStoreName(cfgStore.getStoreNameToC());
        }
        if (Objects.nonNull(req.getAuthType())) {
            tabClientVO.setAuthType(req.getAuthType());
        }
        return tabClientVO;
    }

    @Override
    public void authTypeUpdate(ContractAuthTypeUpdateReq req) {

        if (OrderSignTypeEnum.SIGN_TYPE_CLIENT.code().equals(req.getSignType()) || (OrderSignTypeEnum.SIGN_TYPE_BAILOR.code().equals(req.getSignType()) && TemplateContractTypeEnum.ENTRUST.code().equals(req.getTemplateContractType()))){

            HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(req.getOrderId());
            Assert.isTrue(Objects.nonNull(heOrderUserSnapshotEntity), "订单快照信息不存在");
            ClientCardTypeRequest clientCardTypeRequest = new ClientCardTypeRequest();
            clientCardTypeRequest.setBasicUid(heOrderUserSnapshotEntity.getBasicUid());
            Result<List<HeUserCardVO>> cardList = tabClientService.getCardList(clientCardTypeRequest);
            Assert.isTrue(cardList.getSuccess(), "获取客户证件列表失败");
            HeUserCardVO heUserCardVO = cardList.getData().stream().filter(card -> req.getAuthType().equals(card.getAuthType())).findFirst().orElse(null);
            Assert.isTrue(Objects.nonNull(heUserCardVO), "客户证件信息不存在");
            heOrderUserSnapshotEntity.setCertType(heUserCardVO.getCertType());
            heOrderUserSnapshotEntity.setPhone(heUserCardVO.getPhone());
            heOrderUserSnapshotEntity.setIdCard(heUserCardVO.getIdCard());
            heOrderUserSnapshotEntity.setIdCardFront(heUserCardVO.getIdCardFront());
            heOrderUserSnapshotEntity.setIdCardBack(heUserCardVO.getIdCardBack());
            heOrderUserSnapshotEntity.setAuthType(heUserCardVO.getAuthType());
            heOrderUserSnapshotEntity.setEmail(heUserCardVO.getEmail());
            orderUserSnapshotRepository.updateOne(heOrderUserSnapshotEntity);
        }

        if (OrderSignTypeEnum.SIGN_TYPE_CLIENT.code().equals(req.getSignType()) || TemplateContractTypeEnum.ENTRUST.code().equals(req.getTemplateContractType())){
            return;
        }
        HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity = orderBailorSnapshotRepository.queryByOrderId(req.getOrderId());
        if (Objects.nonNull(heOrderBailorSnapshotEntity)) {

            Assert.isTrue(StringUtils.isNotEmpty(heOrderBailorSnapshotEntity.getExt()), "当前委托人认证方式为空");
            List<HeOrderBailorSnapshotExtEntity> heOrderBailorSnapshotExtEntities = JSON.parseArray(heOrderBailorSnapshotEntity.getExt(), HeOrderBailorSnapshotExtEntity.class);
            HeOrderBailorSnapshotExtEntity heOrderBailorSnapshotExtEntity = heOrderBailorSnapshotExtEntities.stream().filter(ext -> ext.getAuthType().equals(req.getAuthType())).findFirst().orElse(null);
            if (Objects.isNull(heOrderBailorSnapshotExtEntity)){
                return;
            }

            ClientSearchByIdRequest clientSearchByIdRequest = new ClientSearchByIdRequest();
            clientSearchByIdRequest.setId(heOrderBailorSnapshotExtEntity.getBailorId());
            Result<TabClientBailorPO> tabClientBailorPOResult = tabClientBailorService.queryBailorInfo(clientSearchByIdRequest);
            Assert.isTrue(tabClientBailorPOResult.getSuccess(), "获取委托人信息失败");
            TabClientBailorPO tabClientBailorPO = tabClientBailorPOResult.getData();
            heOrderBailorSnapshotEntity.setBailorId(tabClientBailorPO.getId().intValue());
            heOrderBailorSnapshotEntity.setCertType(tabClientBailorPO.getCertType());
            heOrderBailorSnapshotEntity.setIdCard(tabClientBailorPO.getIdCard());
            heOrderBailorSnapshotEntity.setIdCardFront(tabClientBailorPO.getIdCardFront());
            heOrderBailorSnapshotEntity.setIdCardBack(tabClientBailorPO.getIdCardBack());
            heOrderBailorSnapshotEntity.setAuthType(tabClientBailorPO.getAuthType());
            heOrderBailorSnapshotEntity.setEmail(tabClientBailorPO.getEmail());
            orderBailorSnapshotRepository.updateOne(heOrderBailorSnapshotEntity);
        }

    }

    @Override
    public Result<ClientBooleanVO> queryCardList(ClientCardTypeReq request) {

        ClientBooleanVO clientBooleanVO = new ClientBooleanVO();
        clientBooleanVO.setResultInfo(Boolean.FALSE);
        if (OrderSignTypeEnum.SIGN_TYPE_BAILOR.code().equals(request.getSignType()) && !TemplateContractTypeEnum.ENTRUST.code().equals(request.getTemplateContractType())) {

            HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity = orderBailorSnapshotRepository.queryByOrderId(request.getOrderId());
            if (Objects.isNull(heOrderBailorSnapshotEntity) || StringUtils.isEmpty(heOrderBailorSnapshotEntity.getExt())){
                return Result.success(clientBooleanVO);
            }
            List<HeOrderBailorSnapshotExtEntity> list = JSONUtil.toList(heOrderBailorSnapshotEntity.getExt(), HeOrderBailorSnapshotExtEntity.class);
            clientBooleanVO.setResultInfo(list.size() > 1 ? Boolean.TRUE : Boolean.FALSE);
            return Result.success(clientBooleanVO);
        }
        ClientCardTypeRequest req = new ClientCardTypeRequest();
        req.setBasicUid(request.getBasicUid());
        Result<ClientCardTypeVO> clientCardTypeVOResult = tabClientService.queryCardList(req);
        clientBooleanVO.setResultInfo(!clientCardTypeVOResult.getSuccess() ? Boolean.FALSE : clientCardTypeVOResult.getData().getSupportOption());
        return Result.success(clientBooleanVO);
    }

    @Override
    public Result<ClientBooleanVO> queryUpdateButton(ButtonUpdateReq request) {


        ClientBooleanVO clientBooleanVO = new ClientBooleanVO();
        clientBooleanVO.setResultInfo(Boolean.FALSE);
        List<OrderContractSignRecordVO> signList = contractManager.getContractSignRecordAndAgreementListByOrderIdList(ListUtil.toList(request.getOrderId().longValue()));

        if (OrderSignTypeEnum.SIGN_TYPE_CLIENT.code().equals(request.getSignType())){

            ClientCardTypeRequest clientCardTypeRequest = new ClientCardTypeRequest();
            clientCardTypeRequest.setBasicUid(request.getBasicUid());
            Result<List<HeUserCardVO>> cardList = tabClientService.getCardList(clientCardTypeRequest);
            Assert.isTrue(cardList.getSuccess(), "获取客户证件列表失败");
            Set<Integer> authTypeSet = cardList.getData().stream().filter(card -> Objects.nonNull(card.getAuthType())).map(HeUserCardVO::getAuthType).collect(Collectors.toSet());
            if (authTypeSet.size() != 1){
                return Result.success(clientBooleanVO);
            }
            if (CollectionUtils.isNotEmpty(signList)){
                OrderContractSignRecordVO orderContractSignRecordVO = signList.stream().filter(item -> Objects.nonNull(item.getContractStatus()) && Objects.nonNull(item.getContractType())).filter(tr -> Arrays.asList(TemplateContractTypeEnum.YZ_SAINTBELLA.getCode(), TemplateContractTypeEnum.ENTRUST.getCode()).contains(tr.getTemplateContractType()) && tr.getContractStatus() == 2 && tr.getContractType() == 1).findFirst().orElse(null);
                if (Objects.nonNull(orderContractSignRecordVO)){
                    return Result.success(clientBooleanVO);
                }
            }
            clientBooleanVO.setResultInfo(Boolean.TRUE);
            return Result.success(clientBooleanVO);
        } else {
            HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity = orderBailorSnapshotRepository.queryByOrderId(request.getOrderId());
            if (Objects.isNull(heOrderBailorSnapshotEntity)){
                return Result.success(clientBooleanVO);
            }

            if (StringUtils.isNotEmpty(heOrderBailorSnapshotEntity.getExt())){
                List<HeOrderBailorSnapshotExtEntity> heOrderBailorSnapshotExtEntities = JSON.parseArray(heOrderBailorSnapshotEntity.getExt(), HeOrderBailorSnapshotExtEntity.class);
                if (heOrderBailorSnapshotExtEntities.size() > 1){
                    return Result.success(clientBooleanVO);
                }
            }
            OrderContractSignRecordVO orderContractSignRecordVO = signList.stream().filter(item -> Objects.nonNull(item.getContractStatus()) && Objects.nonNull(item.getContractType())).filter(tr -> TemplateContractTypeEnum.ENTRUST.getCode().equals(tr.getTemplateContractType()) && tr.getContractStatus() == 2 && tr.getContractType() == 1).findFirst().orElse(null);
            if (Objects.nonNull(orderContractSignRecordVO)){
                return Result.success(clientBooleanVO);
            }
            clientBooleanVO.setResultInfo(Boolean.TRUE);
            return Result.success(clientBooleanVO);
        }
    }

    private HeOrderUserSnapshotEntity buildHeOrderUserSnapshotEntity(HeOrderUserSnapshotEntity heOrderUserSnapshotEntity, ClientInfoVO clientInfoVO) {
        setOrderUserSnapshotByClientInfo(clientInfoVO, heOrderUserSnapshotEntity);
        return heOrderUserSnapshotEntity;
    }

    private static void setOrderUserSnapshotByClientInfo(ClientInfoVO clientInfoVO, HeOrderUserSnapshotEntity entity) {
        entity.setName(clientInfoVO.getName());
        entity.setPhone(clientInfoVO.getPhone());
        entity.setPhoneType(clientInfoVO.getPhoneType());
        entity.setCertType(clientInfoVO.getCertType());
        entity.setIdCard(clientInfoVO.getIdCard());
        entity.setIdCardFront(clientInfoVO.getIdCardFront());
        entity.setIdCardBack(clientInfoVO.getIdCardBack());
        entity.setBornNum(clientInfoVO.getBornNum());
        entity.setHospital(clientInfoVO.getHospital());
        entity.setFromType(clientInfoVO.getFromType());
        entity.setProvince(clientInfoVO.getProvince());
        entity.setCity(clientInfoVO.getCity());
        entity.setRegion(clientInfoVO.getRegion());
        entity.setAddress(clientInfoVO.getAddress());
        entity.setUrgentName(clientInfoVO.getUrgentName());
        entity.setUrgentPhone(clientInfoVO.getUrgentPhone());
        entity.setRelationWithClient(clientInfoVO.getRelationWithClient());
        entity.setConstellationType(clientInfoVO.getConstellationType());
        entity.setConstellation(clientInfoVO.getConstellation());
        entity.setProfession(clientInfoVO.getProfession());
        entity.setAge(clientInfoVO.getAge());
        entity.setBloodType(clientInfoVO.getBloodType());
        entity.setGestationWeekNow(clientInfoVO.getGestationWeekNow());
        entity.setQrCode(clientInfoVO.getQrCode());
        entity.setIsCardVerify(clientInfoVO.getIsCardVerify());
        entity.setIsPhoneVerify(clientInfoVO.getIsPhoneVerify());
        entity.setPhoneCiphertext(clientInfoVO.getPhone());
        entity.setUrgentPhoneCiphertext(clientInfoVO.getUrgentPhone());
        entity.setIdCardFrontCiphertext(clientInfoVO.getIdCardFront());
        entity.setIdCardBackCiphertext(clientInfoVO.getIdCardBack());
        entity.setIdCardCiphertext(clientInfoVO.getIdCard());
        entity.setAuthType(clientInfoVO.getAuthType());
        entity.setEmail(clientInfoVO.getEmail());
        entity.setEmailVerify(clientInfoVO.getEmailVerify());
    }


}

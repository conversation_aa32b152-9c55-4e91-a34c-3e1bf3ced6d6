package com.stbella.order.server.context.component.contract.checker;

import cn.hutool.core.collection.ListUtil;
import com.stbella.contract.model.enums.ContractStatusEnum;
import com.stbella.contract.model.enums.ContractTypeEnum;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.server.order.month.enums.ApprovalDiscountStatusEnum;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * 主合同创建校验器
 */
@Component
public class MainContractCreateValidator extends AbstractContractCreateValidator {


    @Override
    public boolean doValidate(ContractValidationContext context) {


        checkEntrustSignStatus(context);

        checkPaperContract(context);

        return false;
    }

    private void checkEntrustSignStatus(ContractValidationContext context) {
        HeOrderEntity orderEntity = context.getOrderEntity();
        Optional<OrderContractSignRecordVO> first = context.getSignRecords().stream().filter(i -> ListUtil.toList(ContractTypeEnum.ESIGN_TYPE.code(), ContractTypeEnum.OLD_TYPE.code(), ContractTypeEnum.PAPER_TYPE.code()).contains(i.getContractType())).filter(item -> Objects.equals(com.stbella.contract.model.enums.TemplateContractTypeEnum.ENTRUST.code(), item.getTemplateContractType()) && Objects.equals(ContractStatusEnum.SIGNED.code(), item.getContractStatus())).findFirst();
        if (!first.isPresent() && Objects.nonNull(orderEntity.getSignType()) && orderEntity.getSignType() == 1) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "请先签署授权委托书");
        }
    }



    @Override
    public TemplateContractTypeEnum templateContractType() {
        return TemplateContractTypeEnum.YZ_SAINTBELLA;
    }
}

package com.stbella.order.server.context.component.spi.impl.cart;

import com.stbella.order.common.enums.core.CartSceneEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.HeQuotationEntity;
import com.stbella.order.domain.repository.HeQuotationRepository;
import com.stbella.order.server.context.component.spi.CartRelatedBusinessEntityQuerySpi;
import com.stbella.platform.order.api.req.RestoreCartReq;
import com.stbella.platform.order.api.res.CartRes;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.annotation.FuncPointSpiImpl;
import top.primecare.snowball.extpoint.definition.FuncSpiConfig;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * @Author: ji<PERSON><PERSON>an
 * @CreateTime: 2024-05-30  14:01
 * @Description: 从订单获取价格信息
 */
@Component
@FuncPointSpiImpl(name = "获取订单价格信息扩展点实现")
public class CartForQuotationQuerySpiImpl implements CartRelatedBusinessEntityQuerySpi {

    @Resource
    private HeQuotationRepository quotationRepository;

    @Override
    public boolean condition(RestoreCartReq param) {
        if (CartSceneEnum.QUOTATION.getCode().equals(param.getScene())){
            return true;
        }
        return false;
    }

    @Override
    public CartRes invoke(RestoreCartReq param) {

        HeQuotationEntity heQuotationEntity = quotationRepository.queryById(param.getBusinessId());
        CartRes cartRes = new CartRes();
        // 设置价格
        cartRes.setTotalAmount(AmountChangeUtil.changeF2Y(heQuotationEntity.getOrderAmount()));
        cartRes.setPayAmount(AmountChangeUtil.changeF2Y(heQuotationEntity.getPayAmount()));
        return cartRes;
    }

    @Override
    public FuncSpiConfig config() {
        //默认优先级高
        return new FuncSpiConfig(true, Integer.MIN_VALUE);
    }
}

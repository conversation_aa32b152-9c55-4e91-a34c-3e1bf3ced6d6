package com.stbella.order.server.strategy.approval;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.month.server.request.DepositRefundApprovalRequest;
import com.stbella.order.common.enums.month.RefundRecordPayStatusEnum;
import com.stbella.order.common.utils.JsonUtil;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.domain.repository.OrderRefundRepository;
import com.stbella.order.server.order.month.request.approval.ApprovalStatusInfo;
import com.stbella.order.server.order.month.service.OrderPayDepositService;
import com.stbella.pay.server.alipay.enums.AccountTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 押金退款
 * @date 2024/3/18 17:45
 */
@Component
@Slf4j
public class DepositRefundApprovalStrategy implements ApprovalStrategy {
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @Resource
    private OrderPayDepositService orderPayDepositService;

    @Override
    public void handleApproval(OrderApproveRecordTypeEnum type, String params, String messageId, ApprovalStatusInfo approvalStatusInfo, String processId) {
        DepositRefundApprovalRequest depositRefundApprovalRequest = JSONObject.parseObject(params, DepositRefundApprovalRequest.class);
        HeOrderRefundEntity orderRefundEntity3 = orderRefundRepository.getOneById(depositRefundApprovalRequest.getOrderRefundId());
        Integer applyRefundAmount = orderRefundEntity3.getApplyAmount();
        HeIncomeRecordEntity heIncomeRecordEntity = incomeRecordRepository.getRecordByIncomeSn(orderRefundEntity3.getIncomeSn());
        if (ObjectUtil.isEmpty(orderRefundEntity3)) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "钉钉审批完成退款失败,找不到对应的退款记录,退款ID=" + depositRefundApprovalRequest.getOrderRefundId());
        }
        boolean agree = approvalStatusInfo.isAgree();
        boolean finish = approvalStatusInfo.isFinish();
        boolean refuse = approvalStatusInfo.isRefuse();
        boolean terminate = approvalStatusInfo.isTerminate();
        boolean isSendRefund = false;
        if (finish && agree) {
            orderRefundEntity3.setAgreeAt(System.currentTimeMillis() / 1000);
            orderRefundEntity3.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_3.getCode());
            isSendRefund = true;
        }
        if (terminate) {
            orderRefundEntity3.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_2.getCode());
        }
        if (finish && refuse) {
            orderRefundEntity3.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_2.getCode());
        }
        if (!isSendRefund) {
            heIncomeRecordEntity.setFreezeAmount(heIncomeRecordEntity.getFreezeAmount() - applyRefundAmount);
            //释放支付记录冻结金额
            incomeRecordRepository.updateRecord(heIncomeRecordEntity);
        }
        orderRefundRepository.updateOneById(orderRefundEntity3);
        //发起退款接口
        if (isSendRefund) {
            orderPayDepositService.refund(depositRefundApprovalRequest.getOrderRefundId(), false, AccountTypeEnum.BEI_KANG.getCode());
        }
    }

    @Override
    public OrderApproveRecordTypeEnum getOrderApproveRecordTypeEnum() {
        return OrderApproveRecordTypeEnum.APPROVAL_OF_DEPOSIT_REFUND;
    }
}

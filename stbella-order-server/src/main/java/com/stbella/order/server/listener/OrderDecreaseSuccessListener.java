package com.stbella.order.server.listener;

import cn.hutool.json.JSONUtil;
import com.stbella.core.enums.BusinessEnum;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.domain.repository.OrderGoodsRepository;
import com.stbella.order.domain.repository.OrderReductionRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.listener.event.OrderDecreaseSuccessEvent;
import com.stbella.order.server.order.cts.enums.DecreaseTypeEnum;
import com.stbella.platform.order.api.reduction.req.DecreaseReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.FlowIdentity;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 订单减免成功监听
 */
@Slf4j
@Component
public class OrderDecreaseSuccessListener {

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private OrderReductionRepository orderReductionRepository;

    @Resource
    private OrderGoodsRepository orderGoodsRepository;

    /**
     * 订单减免成功
     *
     * @param event
     */
    @Async
    @EventListener
    public void decreaseSuccess(OrderDecreaseSuccessEvent event) {
        log.info("订单减免成功 事件 {}", JSONUtil.toJsonStr(event));

        Integer orderId = event.getOrderId();
        HeOrderEntity order = orderRepository.getByOrderId(orderId);
        List<HeOrderGoodsEntity> goodsEntities = orderGoodsRepository.getByOrderIdList(Arrays.asList(orderId));
        order.setGoodsList(goodsEntities);
        OrderReductionEntity orderReductionEntity = orderReductionRepository.getById(event.getReductionId());
        DecreaseReq req = new DecreaseReq();
        req.setDecreaseAmount(BigDecimal.ZERO);
        req.setOrderId(order.getOrderId().longValue());

        FlowContext context = new FlowContext();
        context.setAttribute(HeOrderEntity.class, order);
        context.setAttribute(OrderReductionEntity.class, orderReductionEntity);
        context.setAttribute(DecreaseReq.class, req);
        context.setAttribute(DecreaseTypeEnum.class, DecreaseTypeEnum.getValueByCode(event.getSuccessType()));
        context.setAttribute(BizConstant.ExtraKey.storeId, order.getStoreId());

        FlowIdentity identity = FlowIdentity.builder().bizActivity(BizActivityEnum.DECREASES.code())
                .idSlice(BusinessEnum.CARE_CENTER.name())
                .idSlice("order_decrease_success")
                .delimiter(":").build();

        SnowballFlowLauncher.fire(identity, context);
    }

}

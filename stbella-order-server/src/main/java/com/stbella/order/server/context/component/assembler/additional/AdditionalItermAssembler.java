package com.stbella.order.server.context.component.assembler.additional;

import com.stbella.platform.order.api.res.SkuAdditionalInfo;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-06-19  11:16
 * @Description: 额外收费信息组装器
 */
public interface AdditionalItermAssembler {

    /**
     * 过滤器
     * @param context
     * @return
     */
    boolean filter(AdditionalContext context);

    /**
     *  生成额外收费信息
     */
    SkuAdditionalInfo run(AdditionalContext bizContext);
}

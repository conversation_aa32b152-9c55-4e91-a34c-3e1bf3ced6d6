package com.stbella.order.server.context.component.processor.performance;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.stbella.order.common.enums.core.OfflineAuditStatusV2Enum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.core.OrderRefundNatureEnum;
import com.stbella.order.common.enums.month.OrderDecreaseStatusEnum;
import com.stbella.order.common.enums.month.RefundRecordPayStatusEnum;
import com.stbella.order.common.utils.SpringContextHolder;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.service.OrderIncomeDomainService;
import com.stbella.order.domain.order.service.OrderRefundDomainService;
import com.stbella.order.domain.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单金额处理器
 */
@Component
@Slf4j
@SnowballComponent(name = "业绩生效及全额支付", desc = "根据累计累计支付记录总额，订单业绩生效及订单全额支付")
public class OrderAmountProcessor implements IExecutableAtom<FlowContext> {


    @Resource
    OrderReductionRepository orderReductionRepository;

    @Resource
    OrderRefundRepository orderRefundRepository;

    @Resource
    IncomeProofRecordRepository incomeProofRecordRepository;

    @Resource
    OrderRepository orderRepository;

    @Resource
    private OrderIncomeDomainService orderIncomeDomainService;


    @Override
    public void run(FlowContext bizContext) {

        HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);

        //支付记录列表
        Integer orderId = order.getOrderId();
        List<HeIncomeRecordEntity> allRecordListByOrderId = orderIncomeDomainService.queryEffectiveRecord(order);

        log.info("income result is {}", JSONObject.toJSONString(allRecordListByOrderId));

        //减免成功记录列表
        List<OrderReductionEntity> orderReductionEntities = orderReductionRepository.listByOrderIdList(Collections.singletonList(orderId), Lists.newArrayList(OrderDecreaseStatusEnum.APPROVED.getCode(), OrderDecreaseStatusEnum.NO_APPROVAL_NEEDED.getCode()));

        log.info("orderReductionEntities result is {}", JSONObject.toJSONString(orderReductionEntities));
        //退款记录列表
        List<HeOrderRefundEntity> refundList = orderRefundRepository.getRefundByOrderId(orderId);
        //把主退款过滤掉
        refundList = refundList.stream().filter(o -> o.getParentRefundOrderSn() != null).collect(Collectors.toList());

        log.info("refundList result is {}", JSONObject.toJSONString(refundList));

        // 计算现金相关金额
        PaymentAmounts cashAmounts = calculateCashAmounts(allRecordListByOrderId, refundList);
        Integer cashTotalPayAmount = cashAmounts.getTotalPayAmount();
        Integer cashRefundAmount = cashAmounts.getRefundAmount();

        // 计算产康金相关金额
        PaymentAmounts productionCoinAmounts = calculateProductionCoinAmounts(allRecordListByOrderId, refundList);
        Integer productionCoinTotalPayAmount = productionCoinAmounts.getTotalPayAmount();
        Integer productionCoinRefundAmount = productionCoinAmounts.getRefundAmount();

        // 计算减免相关金额
        ReductionAmounts reductionAmounts = calculateReductionAmounts(orderReductionEntities, refundList);
        Integer reductionSuccessAmount = reductionAmounts.getSuccessAmount();
        Integer reductionRefundAmount = reductionAmounts.getRefundAmount();

        // 计算现金审批中金额
        Integer cashReviewingAmount = calculateCashReviewingAmount(allRecordListByOrderId);

        //退款中的金额
        Integer goodsRefundAllocationAmount = calculateGoodsRefundAllocation(orderId);
        List<Integer> notCashPayTypes = Lists.newArrayList(
                OmniPayTypeEnum.PRODUCTION_COIN.getCode(),
                OmniPayTypeEnum.REDUCTION.getCode()
        );

        log.info("refundList is {}",JSONObject.toJSONString(refundList));

        //退回重付金额
        Integer tempRefundAmount = refundList.stream()
                .filter(refund -> RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode().equals(refund.getStatus()))
                .filter(refund -> OrderRefundNatureEnum.TEMP_REFUND.code().equals(refund.getRefundNature()))
                .filter(refund -> !notCashPayTypes.contains(refund.getRefundType()))
                .mapToInt(refund -> ObjectUtil.isEmpty(refund.getActualAmount()) ? 0 : refund.getActualAmount())
                .sum();

        log.info("tempRefundAmount is {}", tempRefundAmount);

        // 持久化到数据库
        persistAmountsToDatabase(order, cashTotalPayAmount, cashRefundAmount, productionCoinTotalPayAmount,
                productionCoinRefundAmount, reductionSuccessAmount, reductionRefundAmount, cashReviewingAmount, goodsRefundAllocationAmount, tempRefundAmount);

        log.info("订单金额计算完成 orderSn={}, 现金支付={}, 现金退款={}, 产康金支付={}, 产康金退款={}, 减免成功={}, 减免退款={}, 现金审批中={}",
                order.getOrderSn(), cashTotalPayAmount, cashRefundAmount,
                productionCoinTotalPayAmount, productionCoinRefundAmount,
                reductionSuccessAmount, reductionRefundAmount, cashReviewingAmount);

        bizContext.setAttribute(HeOrderEntity.class, order);

    }

    /**
     * 计算现金相关金额
     */
    private PaymentAmounts calculateCashAmounts(List<HeIncomeRecordEntity> incomeRecords, List<HeOrderRefundEntity> refundList) {
        // 现金支付类型：微信、支付宝、线下支付等（排除产康金）
        List<Integer> notCashPayTypes = Lists.newArrayList(
                OmniPayTypeEnum.PRODUCTION_COIN.getCode(),
                OmniPayTypeEnum.REDUCTION.getCode()
        );

        // 计算现金总支付金额 - 只统计已支付成功的记录
        Integer totalPayAmount = incomeRecords.stream()
                .filter(record -> Integer.valueOf(1).equals(record.getStatus())) // 1=已支付
                .filter(record -> !notCashPayTypes.contains(record.getPayType()))
                .filter(HeIncomeRecordEntity::isActualSuccess)
                .mapToInt(record -> ObjectUtil.isEmpty(record.getIncome()) ? 0 : record.getIncome())
                .sum();

        // 计算现金退款金额
        Integer refundAmount = refundList.stream()
                .filter(refund -> RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode().equals(refund.getStatus()))
                .filter(refund -> !OrderRefundNatureEnum.TEMP_REFUND.code().equals(refund.getRefundNature()))
                .filter(refund -> !notCashPayTypes.contains(refund.getRefundType()))
                .mapToInt(refund -> ObjectUtil.isEmpty(refund.getActualAmount()) ? 0 : refund.getActualAmount())
                .sum();

        return new PaymentAmounts(totalPayAmount, refundAmount);
    }

    /**
     * 计算产康金相关金额
     */
    private PaymentAmounts calculateProductionCoinAmounts(List<HeIncomeRecordEntity> incomeRecords, List<HeOrderRefundEntity> refundList) {
        // 计算产康金总支付金额 - 只统计已支付成功的记录
        Integer totalPayAmount = incomeRecords.stream()
                .filter(record -> Integer.valueOf(1).equals(record.getStatus())) // 1=已支付
                .filter(record -> OmniPayTypeEnum.PRODUCTION_COIN.getCode().equals(record.getPayType()))
                .mapToInt(record -> ObjectUtil.isEmpty(record.getIncome()) ? 0 : record.getIncome())
                .sum();

        // 计算产康金退款金额
        Integer refundAmount = refundList.stream()
                .filter(refund -> RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode().equals(refund.getStatus()))
                .filter(refund -> OmniPayTypeEnum.PRODUCTION_COIN.getCode().equals(refund.getRefundType()))
                .mapToInt(refund -> ObjectUtil.isEmpty(refund.getActualAmount()) ? 0 : refund.getActualAmount())
                .sum();

        return new PaymentAmounts(totalPayAmount, refundAmount);
    }

    /**
     * 计算减免相关金额
     */
    private ReductionAmounts calculateReductionAmounts(List<OrderReductionEntity> reductionEntities, List<HeOrderRefundEntity> refundList) {
        // 计算减免成功金额
        Integer successAmount = reductionEntities.stream()
                .filter(OrderReductionEntity::isSuccessAuthState)
                .mapToInt(reduction -> ObjectUtil.isEmpty(reduction.getDecreaseAmount()) ? 0 : reduction.getDecreaseAmount().intValue())
                .sum();

        // 计算减免退款金额 - 退款类型为减免的退款记录
        Integer refundAmount = refundList.stream()
                .filter(refund -> RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode().equals(refund.getStatus()))
                .filter(refund -> OmniPayTypeEnum.REDUCTION.getCode().equals(refund.getRefundType()))
                .mapToInt(refund -> ObjectUtil.isEmpty(refund.getActualAmount()) ? 0 : refund.getActualAmount())
                .sum();

        return new ReductionAmounts(successAmount, refundAmount);
    }

    /**
     * 计算现金审批中金额 - 参考 queryEffectiveRecord 方法的逻辑
     */
    private Integer calculateCashReviewingAmount(List<HeIncomeRecordEntity> incomeRecords) {
        // 获取线下支付凭证记录
        Integer orderId = incomeRecords.isEmpty() ? null : incomeRecords.get(0).getOrderId();
        List<HeIncomeProofRecordEntity> proofRecords = Lists.newArrayList();
        if (orderId != null) {
            proofRecords = incomeProofRecordRepository.getIncomeProofRecordByOrderId(orderId);
        }

        // 现金支付类型
        List<Integer> cashPayTypes = Lists.newArrayList(
                OmniPayTypeEnum.OFFLINE.getCode());
        // 构建审批状态映射
        Map<Integer, Integer> approveStatusMap = proofRecords.stream()
                .collect(Collectors.toMap(HeIncomeProofRecordEntity::getIncomeId, HeIncomeProofRecordEntity::getStatus));

        // 计算现金审批中金额
        return incomeRecords.stream()
                .filter(record -> OmniPayTypeEnum.OFFLINE.getCode().equals(record.getPayType())) // 只有线下支付需要审批
                .filter(record -> !Integer.valueOf(2).equals(record.getStatus())) // 排除支付失败的记录
                .filter(record -> cashPayTypes.contains(record.getPayType()))
                .filter(record -> {
                    Integer approveStatus = approveStatusMap.get(record.getId());
                    return OfflineAuditStatusV2Enum.UNDER_REVIEW.getCode().equals(approveStatus); // 0=审批中
                })
                .mapToInt(record -> ObjectUtil.isEmpty(record.getIncome()) ? 0 : record.getIncome())
                .sum();
    }

    /**
     * 持久化金额到数据库
     */
    private void persistAmountsToDatabase(HeOrderEntity order, Integer cashTotalPayAmount, Integer cashRefundAmount,
                                          Integer productionCoinTotalPayAmount, Integer productionCoinRefundAmount,
                                          Integer reductionSuccessAmount, Integer reductionRefundAmount,
                                          Integer cashReviewingAmount, Integer goodsRefundAllocationAmount, Integer tempRefundAmount) {
        try {
            // 更新订单金额字段
            HeOrderEntity updateModel = new HeOrderEntity();
            updateModel.setOrderId(order.getOrderId());
            updateModel.setCashPaidAmount(cashTotalPayAmount);
            updateModel.setCashRefundAmount(cashRefundAmount);
            updateModel.setProductionCoinPaidAmount(productionCoinTotalPayAmount);
            updateModel.setProductionCoinRefundAmount(productionCoinRefundAmount);
            updateModel.setTotalReductionAmount(reductionSuccessAmount);
            updateModel.setReductionRefundAmount(reductionRefundAmount);
            updateModel.setCashReviewingAmount(cashReviewingAmount);
            updateModel.setTempRefundAmount(tempRefundAmount);
            updateModel.setGoodsRefundAllocationAmount(goodsRefundAllocationAmount);


            order.setCashPaidAmount(cashTotalPayAmount);
            order.setCashRefundAmount(cashRefundAmount);
            order.setProductionCoinPaidAmount(productionCoinTotalPayAmount);
            order.setProductionCoinRefundAmount(productionCoinRefundAmount);
            order.setTotalReductionAmount(reductionSuccessAmount);
            order.setReductionRefundAmount(reductionRefundAmount);
            order.setCashReviewingAmount(cashReviewingAmount);
            order.setGoodsRefundAllocationAmount(goodsRefundAllocationAmount);
            order.setTempRefundAmount(tempRefundAmount);

            orderRepository.updateOrderMonthByOrderId(updateModel);

            log.info("订单金额持久化成功 orderId={}", order.getOrderId());
        } catch (Exception e) {
            log.error("订单金额持久化失败 orderId={}", order.getOrderId(), e);
        }
    }

    /**
     * 支付金额数据类
     */
    private static class PaymentAmounts {
        private final Integer totalPayAmount;
        private final Integer refundAmount;

        public PaymentAmounts(Integer totalPayAmount, Integer refundAmount) {
            this.totalPayAmount = totalPayAmount;
            this.refundAmount = refundAmount;
        }

        public Integer getTotalPayAmount() {
            return totalPayAmount;
        }

        public Integer getRefundAmount() {
            return refundAmount;
        }
    }

    /**
     * 减免金额数据类
     */
    private static class ReductionAmounts {
        private final Integer successAmount;
        private final Integer refundAmount;

        public ReductionAmounts(Integer successAmount, Integer refundAmount) {
            this.successAmount = successAmount;
            this.refundAmount = refundAmount;
        }

        public Integer getSuccessAmount() {
            return successAmount;
        }

        public Integer getRefundAmount() {
            return refundAmount;
        }
    }


    /**
     * 获取商品退货数量映射
     *
     * @return Map<orderGoodsSn, refundQuantity>
     */
    private Map<String, Integer> getGoodsRefundQuantityMap(Integer orderId) {
        try {
            OrderRefundDomainService refundDomainService = SpringContextHolder.getBean(OrderRefundDomainService.class);

            // 获取所有成功的退款商品记录
            List<HeOrderRefundGoodsEntity> successfulRefunds = refundDomainService.querySuccessfulOrderGoodsAllocationRefund(orderId);

            if (CollectionUtils.isEmpty(successfulRefunds)) {
                return new HashMap<>();
            }

            successfulRefunds = successfulRefunds.stream().filter(o-> !Objects.equals(OmniPayTypeEnum.REDUCTION.getCode(), o.getPayType())).collect(Collectors.toList());
            // 按商品SN分组并统计退货数量
            Map<String, Integer> refundQuantityMap = new HashMap<>();
            for (HeOrderRefundGoodsEntity refund : successfulRefunds) {
                String orderGoodsSn = refund.getOrderGoodsSn();
                Integer refundNum = refund.getRefundNum() != null ? refund.getRefundNum() : 0;

                refundQuantityMap.merge(orderGoodsSn, refundNum, Integer::sum);
            }

            return refundQuantityMap;

        } catch (Exception e) {
            log.error("获取商品退货数量失败, orderId={}", orderId, e);
            return new HashMap<>();
        }
    }

    /**
     * 计算商品退货数量 × 商品应付分摊
     */
    private Integer calculateGoodsRefundAllocation(Integer orderId) {
        try {
            // 获取商品退货数量映射
            Map<String, Integer> goodsRefundQuantityMap = getGoodsRefundQuantityMap(orderId);
            if (goodsRefundQuantityMap.isEmpty()) {
                return 0;
            }

            // 获取订单商品列表
            OrderGoodsRepository orderGoodsRepository = SpringContextHolder.getBean(OrderGoodsRepository.class);
            List<HeOrderGoodsEntity> orderGoodsList = orderGoodsRepository.getAllItermByOrderId(orderId);
            if (CollectionUtils.isEmpty(orderGoodsList)) {
                return 0;
            }

            int totalRefundAllocation = 0;

            for (HeOrderGoodsEntity goods : orderGoodsList) {
                String orderGoodsSn = goods.getOrderGoodsSn();
                Integer refundQuantity = goodsRefundQuantityMap.get(orderGoodsSn);

                if (refundQuantity == null || refundQuantity <= 0) {
                    continue;
                }

                // 获取商品数量和分摊价格
                Integer goodsNum = goods.getGoodsNum() != null ? goods.getGoodsNum() : 0;
                Integer allocationUnitPrice = goods.getAllocationOriginPrice() != null ? goods.getAllocationOriginPrice() : 0;
                int totalAllocationAmount = goods.getTotalAllocationOriginPrice() != null ? goods.getTotalAllocationOriginPrice() : 0;

                int goodsRefundAllocation;
                
                // 如果退货数量等于购买数量，直接使用总分摊金额，避免除法误差
                if (refundQuantity.equals(goodsNum)) {
                    goodsRefundAllocation = totalAllocationAmount;
                    log.info("商品[{}]全量退货，直接使用总分摊金额: 退货数量={}, 购买数量={}, 退货分摊={}",
                            orderGoodsSn, refundQuantity, goodsNum, goodsRefundAllocation);
                } else {
                    // 部分退货，使用long避免Integer溢出
                    long refundAllocation = (long) allocationUnitPrice * refundQuantity;
                    goodsRefundAllocation = (int) Math.min(refundAllocation, totalAllocationAmount);
                    log.info("商品[{}]部分退货分摊计算: 退货数量={}, 购买数量={}, 分摊单价={}, 退货分摊={}, 分摊总额={}",
                            orderGoodsSn, refundQuantity, goodsNum, allocationUnitPrice, goodsRefundAllocation, totalAllocationAmount);
                }

                totalRefundAllocation += goodsRefundAllocation;
            }

            return totalRefundAllocation;

        } catch (Exception e) {
            log.error("计算商品退货分摊失败, orderId={}", orderId, e);
            return 0;
        }
    }

}

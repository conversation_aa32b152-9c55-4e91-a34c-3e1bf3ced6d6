package com.stbella.order.server.listener;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.stbella.order.server.order.cts.dto.RefundNotifyMqDTO;
import com.stbella.order.server.order.cts.service.OrderFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 招行退款成功，开始处理业务逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CmbRefundSuccessListener {

    @Resource
    private OrderFacade orderFacade;

    /**
     * 招行退款成功，开始处理业务逻辑
     * @param event
     */
    @Async
    @EventListener
    public void cmbRefundSuccess(RefundNotifyMqDTO event) {
        // 退款成功，执行后续逻辑（支付宝和微信是发送的mq  refund_notify）
        try {
            // 兼容原来的处理，等待1秒。 微信支付走的是异步消息 ， 原来是调用后修改状态，现在是修改状态后调用
            // 见 CtsPayServiceImpl if ((PayStatusEnum.PAY_STATUS_SUCCESS.getCode()).equals(payRefundResult.getPayStatus()))
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        log.info("招行退款调用成功，开始处理业务逻辑");
        Boolean refundNotify = orderFacade.refundNotify(event);
        if (!refundNotify){
            log.error("招行退款成功，处理业务逻辑失败，refundNotifyMqDTO={}", JSONUtil.toJsonStr(event));
        }
    }

}

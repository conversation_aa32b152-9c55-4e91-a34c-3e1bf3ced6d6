package com.stbella.order.server.order.production.component;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.common.enums.production.ProductionServeTypeEnum;
import com.stbella.order.server.order.OrderGoodsForSplitValueModel;
import com.stbella.order.server.order.ProductionGoodsSkuModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: Seven
 * @time: 2022/10/14 15:30
 */
@Component
@Slf4j
public class OrderAmountSplitComponent {

    public static void main(String[] args) {

        String json = "{\"skuList\":[{\"serviceType\":0,\"isFullMoonType\":0,\"combineType\":2,\"orderSellType\":\"购买\",\"sellingPrice\":1281,\"payAmount\":1281,\"goodsPrice\":39800,\"orderProductionId\":30650,\"productionAmount\":0,\"produceAmountDeduction\":0,\"skuId\":4708},{\"serviceType\":0,\"isFullMoonType\":0,\"combineType\":2,\"orderSellType\":\"购买\",\"sellingPrice\":1281,\"payAmount\":1281,\"goodsPrice\":39800,\"orderProductionId\":30651,\"productionAmount\":0,\"produceAmountDeduction\":0,\"skuId\":4711},{\"serviceType\":0,\"isFullMoonType\":0,\"combineType\":2,\"orderSellType\":\"购买\",\"sellingPrice\":598000,\"payAmount\":598000,\"goodsPrice\":598000,\"orderProductionId\":30652,\"productionAmount\":0,\"produceAmountDeduction\":0,\"skuId\":4713},{\"serviceType\":0,\"isFullMoonType\":0,\"combineType\":2,\"orderSellType\":\"购买\",\"sellingPrice\":598000,\"payAmount\":598000,\"goodsPrice\":598000,\"orderProductionId\":30653,\"productionAmount\":0,\"produceAmountDeduction\":0,\"skuId\":4713},{\"serviceType\":0,\"isFullMoonType\":0,\"combineType\":2,\"orderSellType\":\"购买\",\"sellingPrice\":598000,\"payAmount\":598000,\"goodsPrice\":598000,\"orderProductionId\":30654,\"productionAmount\":0,\"produceAmountDeduction\":0,\"skuId\":4713},{\"serviceType\":0,\"isFullMoonType\":0,\"combineType\":2,\"orderSellType\":\"购买\",\"sellingPrice\":598000,\"payAmount\":598000,\"goodsPrice\":598000,\"orderProductionId\":30655,\"productionAmount\":0,\"produceAmountDeduction\":0,\"skuId\":4713}],\"orderId\":54963,\"payAmount\":2471600,\"productionAmountPay\":0} ";
        OrderGoodsForSplitValueModel orderGoods = JSONUtil.toBean(json, OrderGoodsForSplitValueModel.class);

        OrderAmountSplitComponent split = new OrderAmountSplitComponent();
        split.split(orderGoods);

        System.out.println(JSONObject.toJSONString(orderGoods));

    }

    private static Map<Integer, Integer> fixedPriceProduct = new HashMap<>();

    static {
        //银盒
        fixedPriceProduct.put(4712, 398000);
        fixedPriceProduct.put(5864, 398000);
        //金盒
        fixedPriceProduct.put(4713, 598000);
        fixedPriceProduct.put(5865, 598000);
    }


    public void split(OrderGoodsForSplitValueModel orderGoodsForValueModel) {

        boolean containGroup = splitForGroup(orderGoodsForValueModel);
        if (containGroup) {
            log.info("包含通卡，走特殊逻辑");
            return;
        }


        // 如果只单独购买了一个三方商品，那么这个商品的支付金额直接按售价除以次数
        if (orderGoodsForValueModel.getSkuList().size() == 1 &&
                CollectionUtil.isNotEmpty(orderGoodsForValueModel.getSkuList().get(0).getChildSkuList())
                && orderGoodsForValueModel.getSkuList().get(0).getChildSkuList().size() == 1) {
            orderGoodsForValueModel.getSkuList().forEach(sku -> {
                sku.getChildSkuList().forEach(child -> {
                    log.info("只有一种商品的套餐直接按支付金额分摊 {}", JSONUtil.toJsonStr(orderGoodsForValueModel));
                    if (child.getGroupShareCount() == 0){
                        Integer payAmount = 0;
                        child.setProductionAmount(0);
                        child.setPayAmount(payAmount);
                        child.setSellingPrice(0);
                    }else {
                        Integer payAmount = (orderGoodsForValueModel.getPayAmount() - orderGoodsForValueModel.getProductionAmountPay()) / child.getGroupShareCount();
                        child.setProductionAmount(orderGoodsForValueModel.getProductionAmountPay() / child.getGroupShareCount());
                        child.setPayAmount(payAmount);
                        child.setSellingPrice(orderGoodsForValueModel.getPayAmount());
                    }

                });
            });
            return;
        }

        //参与折扣的商品价格总和
        BigDecimal goodsDiscountSum = BigDecimal.ZERO;
        //订单产康金抵扣金额
        BigDecimal productionAmountPay = new BigDecimal(orderGoodsForValueModel.getProductionAmountPay());
        //自营商品原价
        Integer selfOriginalTotal = 0;
        Integer originalTotal = 0;
        /**
         * 三方原价
         */
        Integer thirdOriginalTotal = 0;

        //找出三方的所有商品，先分金额。剩下的再分给自营
        boolean allThirdParty = true;
        //所有支付的金额
        Integer totalPayAmount = orderGoodsForValueModel.getPayAmount() - orderGoodsForValueModel.getProductionAmountPay();
        List<ProductionGoodsSkuModel> thirdSkuList = new ArrayList<>();
        for (ProductionGoodsSkuModel sku : orderGoodsForValueModel.getSkuList()) {
            if (Objects.equals(CombineTypeEnum.SIMPLE.code(), sku.getCombineType())) {
                if (Objects.equals(ProductionServeTypeEnum.APPOINTMENT_TYPE_TRIPARTITE.code(), sku.getServiceType())) {
                    thirdSkuList.add(sku);
                    originalTotal += sku.getGoodsPrice();
                    thirdOriginalTotal +=sku.getGoodsPrice();
                } else {
                    selfOriginalTotal += sku.getGoodsPrice();
                    allThirdParty = false;
                }
            } else {
                if (CollectionUtil.isNotEmpty(sku.getChildSkuList())) {
                    for (ProductionGoodsSkuModel child : sku.getChildSkuList()) {
                        if (Objects.equals(ProductionServeTypeEnum.APPOINTMENT_TYPE_TRIPARTITE.code(), child.getServiceType())) {
                            thirdSkuList.add(child);
                            originalTotal += child.getGoodsPrice() * child.getGroupShareCount();
                            thirdOriginalTotal +=child.getGoodsPrice() * child.getGroupShareCount();
                        } else {
                            selfOriginalTotal += child.getGoodsPrice() * child.getGroupShareCount();
                            allThirdParty = false;
                        }
                    }
                }
            }
        }

        //如果全是三方，那么直接按照售价分摊 - 存在买的价格比较单价高的情况。
        if (allThirdParty) {
            allThirdPartySpit(orderGoodsForValueModel,  originalTotal);
            return;
        }

        //部分商品要优先分走支付金额。再是三方，最后才是自营
        Integer[] fixedResult = splitFixedProductList(orderGoodsForValueModel);
        totalPayAmount -= fixedResult[0];
        if (totalPayAmount <= 0) {
            totalPayAmount = 0;
        }
        if (log.isDebugEnabled()) {
            log.debug("order:{}, 固定商品的分摊: {}", JSONUtil.toJsonStr(orderGoodsForValueModel),  JSONUtil.toJsonStr(fixedResult));
        }

        //固定商品，价格不计算在自营原价，这里剔除
        selfOriginalTotal -= fixedResult[0];


        // 三方商品分摊已经支付金额。 这里要按比例分摊。
        totalPayAmount = thirdSplit(thirdSkuList, totalPayAmount, thirdOriginalTotal);
        if (totalPayAmount <= 0){
            log.info("订单号-{}三方分摊了所有钱", orderGoodsForValueModel.getOrderId());
        }

        if (log.isDebugEnabled()) {
            log.debug("order:{}, 自营商品的的总分摊: {}", JSONUtil.toJsonStr(orderGoodsForValueModel), JSONUtil.toJsonStr(totalPayAmount));
        }
        //自营实付金额
        BigDecimal selfPayAmount = new BigDecimal(totalPayAmount);
        //自营商品按公允价格分摊实付与产康金

        selfSupportSpit(orderGoodsForValueModel, selfOriginalTotal, selfPayAmount, productionAmountPay);

    }

    /**
     * 三方金额如果小于总金额按公允价格分摊，否者按原价
     * @param thirdSkuList
     * @param totalPayAmount
     * @return 三方分摊后剩余的钱
     */
    protected Integer thirdSplit(List<ProductionGoodsSkuModel> thirdSkuList, Integer totalPayAmount, Integer thirdOriginalTotal){
        // 三方商品分摊已经支付金额。 这里要按比例分摊。

        Integer spitAmount = thirdOriginalTotal;
        if (thirdOriginalTotal >= totalPayAmount){
            spitAmount = totalPayAmount;
        }
        for (ProductionGoodsSkuModel thirdSku : thirdSkuList) {
            if (Objects.isNull(thirdSku.getGroupShareCount()) || thirdSku.getGroupShareCount() == 0) {
                log.info("skuId {} 的分摊次数为0", thirdSku.getSkuId());
                thirdSku.setGroupShareCount(1);
            }

            if (thirdOriginalTotal <=0){
                thirdSku.setSellingPrice(0);
                thirdSku.setProductionAmount(0);
                thirdSku.setPayAmount(0);
                continue;
            }
            BigDecimal subRate = new BigDecimal(thirdSku.getGoodsPrice() * thirdSku.getGroupShareCount()).divide(new BigDecimal(thirdOriginalTotal), 10, RoundingMode.HALF_UP);
            thirdSku.setSellingPrice(new BigDecimal(spitAmount).multiply(subRate).intValue());
            thirdSku.setProductionAmount(0);
            thirdSku.setPayAmount(thirdSku.getSellingPrice() / thirdSku.getGroupShareCount());
        }

        return  totalPayAmount - spitAmount;
    }

    /**
     * 自营分摊 剩余的实付金额和产康金
     * @param orderGoodsForValueModel
     * @param selfOriginalTotal
     * @param selfPayAmount
     * @param productionAmountPay
     */
    protected void selfSupportSpit(OrderGoodsForSplitValueModel orderGoodsForValueModel, Integer selfOriginalTotal, BigDecimal selfPayAmount, BigDecimal productionAmountPay) {
        log.info("订单: {}, 可分摊的total:{}, payAmount:{}, productionAmount:{}", orderGoodsForValueModel.getOrderId(), selfOriginalTotal, selfPayAmount, productionAmountPay);
        for (ProductionGoodsSkuModel sku : orderGoodsForValueModel.getSkuList()) {
            if (Objects.equals(ProductionServeTypeEnum.APPOINTMENT_TYPE_TRIPARTITE.code(), sku.getServiceType())) {
                continue;
            }
            if (fixedPriceProduct.containsKey(sku.getSkuId())){
                continue;
            }

            if (Objects.equals(CombineTypeEnum.SIMPLE.code(), sku.getCombineType())) {
                if (!Objects.equals(ProductionServeTypeEnum.APPOINTMENT_TYPE_TRIPARTITE.code(), sku.getServiceType())) {
                    //单项都是一个
                    BigDecimal subRate = new BigDecimal(sku.getGoodsPrice()).divide(new BigDecimal(selfOriginalTotal), 10, RoundingMode.HALF_UP);
                    sku.setSellingPrice(selfPayAmount.multiply(subRate).intValue());
                    sku.setProductionAmount(productionAmountPay.multiply(subRate).intValue());
                    sku.setPayAmount(selfPayAmount.multiply(subRate).intValue());
                }
            } else {
                if (CollectionUtil.isNotEmpty(sku.getChildSkuList())) {
                    for (ProductionGoodsSkuModel child : sku.getChildSkuList()) {

                        if (fixedPriceProduct.containsKey(child.getSkuId())){
                            continue;
                        }

                        if (!Objects.equals(ProductionServeTypeEnum.APPOINTMENT_TYPE_TRIPARTITE.code(), child.getServiceType())) {
                            BigDecimal subRate = new BigDecimal(0);
                            if (selfOriginalTotal != 0){
                                subRate = new BigDecimal(child.getGoodsPrice() * child.getGroupShareCount()).divide(new BigDecimal(selfOriginalTotal), 10, RoundingMode.HALF_UP);
                                child.setSellingPrice(selfPayAmount.multiply(subRate).intValue());
                                if (child.getGroupShareCount() == 0) {
                                    child.setGroupShareCount(1);
                                }
                                child.setProductionAmount(productionAmountPay.multiply(subRate).intValue() / child.getGroupShareCount());
                                child.setPayAmount(child.getSellingPrice() / child.getGroupShareCount());
                            }else {
                                child.setProductionAmount(0);
                                child.setPayAmount(0);
                            }



                        }
                    }
                }
            }

        }

        if (log.isDebugEnabled()) {
            log.debug("处理分摊之后的内容: {}", JSONUtil.toJsonStr(orderGoodsForValueModel.getSkuList()));
        }
    }

    /**
     * 所有都是3方的情况，按其公允价值分摊
     *
     * @param order
     * @param originalTotal
     */
    public void allThirdPartySpit(OrderGoodsForSplitValueModel order, Integer originalTotal) {

        BigDecimal productionAmountPay = new BigDecimal(order.getProductionAmountPay());
        BigDecimal payAmount = new BigDecimal(order.getPayAmount() - order.getProductionAmountPay());
        if  (originalTotal == 0){
            log.info("分摊金额异常, originalTotal=0, orderid={}", order.getOrderId());
            return;
        }

        for (ProductionGoodsSkuModel sku : order.getSkuList()) {
            if (Objects.equals(CombineTypeEnum.SIMPLE.code(), sku.getCombineType())) {
                BigDecimal subRate = new BigDecimal(sku.getGoodsPrice()).divide(new BigDecimal(originalTotal), 10, RoundingMode.HALF_UP);
                sku.setSellingPrice(payAmount.multiply(subRate).intValue());
                sku.setProductionAmount(productionAmountPay.multiply(subRate).intValue());
                sku.setPayAmount(payAmount.multiply(subRate).intValue());
            } else {
                if (CollectionUtil.isNotEmpty(sku.getChildSkuList())) {
                    for (ProductionGoodsSkuModel child : sku.getChildSkuList()) {
                        BigDecimal subRate = new BigDecimal(child.getGoodsPrice() * child.getGroupShareCount()).divide(new BigDecimal(originalTotal), 10, RoundingMode.HALF_UP);
                        child.setSellingPrice(payAmount.multiply(subRate).intValue());
                        if (child.getGroupShareCount() == 0) {
                            child.setGroupShareCount(1);
                        }
                        child.setProductionAmount(productionAmountPay.multiply(subRate).intValue() / child.getGroupShareCount());
                        child.setPayAmount(child.getSellingPrice() / child.getGroupShareCount());
                    }
                }
            }

        }
    }

    /**
     * 固定价格的商品先分摊实付金额
     * 现在的写法，如果未来只买这样的商品，又打折之类的会有问题。
     * @param orderGoodsForValueModel
     * @return
     */
    protected Integer[] splitFixedProductList(OrderGoodsForSplitValueModel orderGoodsForValueModel){

        Integer fixedAmount = 0;
        Integer fixedCount = 0;
        //实际支付
        Integer realPay = orderGoodsForValueModel.getPayAmount() - orderGoodsForValueModel.getProductionAmountPay();
        for (ProductionGoodsSkuModel sku : orderGoodsForValueModel.getSkuList()) {
            if (Objects.equals(CombineTypeEnum.SIMPLE.code(), sku.getCombineType())) {
                if (fixedPriceProduct.containsKey(sku.getSkuId())){
                    Integer sellingPrice = Math.min(realPay, sku.getGoodsPrice());
                    sku.setSellingPrice(sellingPrice);
                    sku.setProductionAmount(0);
                    sku.setPayAmount(sellingPrice);
                    fixedAmount += sku.getSellingPrice();
                    fixedCount ++;

                    realPay -= sellingPrice;
                    if (realPay <= 0){
                        realPay = 0;
                    }
                }
            } else {
                if (CollectionUtil.isNotEmpty(sku.getChildSkuList())) {
                    for (ProductionGoodsSkuModel child : sku.getChildSkuList()) {
                        if (fixedPriceProduct.containsKey(child.getSkuId())){
                            Integer sellingPrice = Math.min(realPay, child.getGoodsPrice()* child.getGroupShareCount());
                            child.setSellingPrice(sellingPrice);
                            child.setProductionAmount(0);
                            child.setPayAmount(child.getSellingPrice() / child.getGroupShareCount());
                            fixedAmount += child.getSellingPrice();
                            realPay -= sellingPrice;
                            if (realPay <= 0){
                                realPay = 0;
                            }
                            fixedCount += child.getGroupShareCount();
                        }
                    }
                }
            }

        }


        return new Integer[]{fixedAmount, fixedCount};
    }

    /**
     * 如果有通卡，逻辑如下
     * 1，找出三方商品，按原价格。
     * 2，其他所有商品，按次数平均。
     *
     * @param orderGoodsForValueModel
     * @return 是否有通卡，如果是更新价格
     */
    public boolean splitForGroup(OrderGoodsForSplitValueModel orderGoodsForValueModel) {

        Integer thirdPartyGoodsTotalPrice = 0;
        //三方商品总次数
        Integer thirdPartyGoodsTotalCount = 0;
        List<ProductionGoodsSkuModel> groupCardList = orderGoodsForValueModel.getSkuList().stream().filter(a -> a.getCombineType().equals(CombineTypeEnum.GROUP_CARD.code())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(groupCardList)) {
            return false;
        }

        log.info("订单有通卡，走特殊逻辑:{}", orderGoodsForValueModel.getOrderId());

        //所有支付的金额
        Integer totalPayAmount = orderGoodsForValueModel.getPayAmount() - orderGoodsForValueModel.getProductionAmountPay();

        // 先分固定价格商品。再分三方，再自营
        Integer[] fixedResult = splitFixedProductList(orderGoodsForValueModel);
        totalPayAmount -= fixedResult[0];
        if (totalPayAmount <= 0) {
            totalPayAmount = 0;
        }


        // 统计三方商品总价，自营总次数
        for (ProductionGoodsSkuModel productionGoodsSkuModel : orderGoodsForValueModel.getSkuList()) {
            if (Objects.equals(CombineTypeEnum.SIMPLE.code(), productionGoodsSkuModel.getCombineType())) {
                if (Objects.equals(ProductionServeTypeEnum.APPOINTMENT_TYPE_TRIPARTITE.code(), productionGoodsSkuModel.getServiceType())) {
                    productionGoodsSkuModel.setSellingPrice(productionGoodsSkuModel.getGoodsPrice());
                    productionGoodsSkuModel.setProductionAmount(0);
                    productionGoodsSkuModel.setPayAmount(productionGoodsSkuModel.getGoodsPrice());

                    int Num = 1;
                    Integer price = Math.min(totalPayAmount, productionGoodsSkuModel.getGoodsPrice() * Num);
                    productionGoodsSkuModel.setSellingPrice(price);
                    productionGoodsSkuModel.setProductionAmount(0);
                    totalPayAmount -= price;
                    if (totalPayAmount <= 0) {
                        totalPayAmount = 0;
                    }
                    productionGoodsSkuModel.setPayAmount(price / Num);
                    thirdPartyGoodsTotalPrice += price;
                    thirdPartyGoodsTotalCount += Num;
                }
            } else {
                for (ProductionGoodsSkuModel childSku : productionGoodsSkuModel.getChildSkuList()) {
                    if (Objects.equals(ProductionServeTypeEnum.APPOINTMENT_TYPE_TRIPARTITE.code(), childSku.getServiceType())) {

                        int num = childSku.getGroupShareCount();
                        Integer price = Math.min(totalPayAmount, childSku.getGoodsPrice() * num);
                        childSku.setSellingPrice(price);
                        childSku.setProductionAmount(0);
                        totalPayAmount -= price;
                        if (totalPayAmount <= 0) {
                            totalPayAmount = 0;
                        }
                        childSku.setPayAmount(price / num);
                        thirdPartyGoodsTotalPrice += price;
                        thirdPartyGoodsTotalCount += num;

                    }
                }
            }
        }
        log.info("三方商品次数{}，金额{}", thirdPartyGoodsTotalCount, thirdPartyGoodsTotalPrice);

        // 计算总的次数，单项商品的次数，次卡每个商品的次数，能卡每个组合的次数
        Integer totalTimes = 0;
        for (ProductionGoodsSkuModel productionGoodsSkuModel : orderGoodsForValueModel.getSkuList()) {

            CombineTypeEnum compineTypeEnum = CombineTypeEnum.from(productionGoodsSkuModel.getCombineType());
            switch (compineTypeEnum) {
                case COUNT_CARD:
                    //次卡 直接加次数
                    for (ProductionGoodsSkuModel childSku : productionGoodsSkuModel.getChildSkuList()) {
                        totalTimes += childSku.getGroupShareCount();
                    }
                    break;
                case GROUP_CARD:
                    //按group id 分组
                    Map<Integer, List<ProductionGoodsSkuModel>> groupMap = productionGoodsSkuModel.getChildSkuList().stream().collect(Collectors.groupingBy(ProductionGoodsSkuModel::getGroupId));
                    //for 循环 遍历groupMap， 获取每个 ProductionGoodsSkuModel的次数
                    for (Map.Entry<Integer, List<ProductionGoodsSkuModel>> entry : groupMap.entrySet()) {
                        totalTimes += entry.getValue().get(0).getGroupShareCount();
                    }

                    break;
                default:
                    totalTimes += 1;
                    break;
            }
        }


        // 计算每次的价格
        Integer selfGoodsTotalPrice = orderGoodsForValueModel.getPayAmount() - thirdPartyGoodsTotalPrice - fixedResult[0];
        if (selfGoodsTotalPrice < 0) {
            selfGoodsTotalPrice = 0;
        }

        Integer setlGoodsTimes = totalTimes - thirdPartyGoodsTotalCount - fixedResult[1];
        Integer oncePrice = 0;
        // 单次产康金支付
        Integer oneProduceAmountDeduction = 0;
        if (setlGoodsTimes > 0) {
            oncePrice = selfGoodsTotalPrice / setlGoodsTimes;
            oneProduceAmountDeduction = orderGoodsForValueModel.getProductionAmountPay() / setlGoodsTimes;
        }

        log.info("总数次{}", totalTimes);

        // 设置价格
        for (ProductionGoodsSkuModel productionGoodsSkuModel : orderGoodsForValueModel.getSkuList()) {

            CombineTypeEnum compineTypeEnum = CombineTypeEnum.from(productionGoodsSkuModel.getCombineType());
            switch (compineTypeEnum) {
                case COUNT_CARD:
                case GROUP_CARD:
                    //次卡 直接加次数
                    for (ProductionGoodsSkuModel childSku : productionGoodsSkuModel.getChildSkuList()) {

                        if (fixedPriceProduct.containsKey(childSku.getSkuId())){
                            continue;
                        }

                        if (!Objects.equals(ProductionServeTypeEnum.APPOINTMENT_TYPE_TRIPARTITE.code(), childSku.getServiceType())) {
                            childSku.setSellingPrice(oncePrice * childSku.getGroupShareCount());
                            childSku.setPayAmount(oncePrice);
                            childSku.setProductionAmount(oneProduceAmountDeduction);

                        }

                    }
                    break;
                default:
                    if (fixedPriceProduct.containsKey(productionGoodsSkuModel.getSkuId())){
                        continue;
                    }
                    totalTimes += 1;
                    if (!Objects.equals(ProductionServeTypeEnum.APPOINTMENT_TYPE_TRIPARTITE.code(), productionGoodsSkuModel.getServiceType())) {
                        productionGoodsSkuModel.setSellingPrice(oncePrice);
                        productionGoodsSkuModel.setPayAmount(oncePrice);
                        //有通卡的，产康金不分摊了
                        productionGoodsSkuModel.setProductionAmount(oneProduceAmountDeduction);
                    }
                    break;
            }
        }
        return true;
    }
}

package com.stbella.order.server.context.component.giftOrder.validator;

import com.stbella.core.exception.BusinessException;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.order.OrderGiftReasonTypeEnum;
import com.stbella.order.server.order.month.req.HeOrderGiftInfoGoodsAddReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
@RefreshScope
@SnowballComponent(name = "赠送订单商品校验")
public class GiftOrderGoodsValidator implements IExecutableAtom<FlowContext> {

    private static final String URL_REGEX = "^(http|https)://[a-zA-Z0-9.-]+(\\.[a-zA-Z]{2,})?(:\\d+)?(/[A\\s]*)?$";

    @Override
    public void run(FlowContext bizContext) {

        HeOrderGiftInfoGoodsAddReq heOrderGiftInfoGoodsAddReq = bizContext.getAttribute(HeOrderGiftInfoGoodsAddReq.class);
        if (StringUtils.isEmpty(OrderGiftReasonTypeEnum.getDesc(heOrderGiftInfoGoodsAddReq.getGiftReasonType()))) {
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_ENUM_VALUE.code() + "", ErrorCodeEnum.ILLEGAL_ENUM_VALUE.desc());
        }
        if (OrderGiftReasonTypeEnum.OTHER_GIFT.getCode().equals(heOrderGiftInfoGoodsAddReq.getGiftReasonType()) && StringUtils.isEmpty(heOrderGiftInfoGoodsAddReq.getGiftSpecificReason())){
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code() + "", "请填写具体原因");
        }
        if (OrderGiftReasonTypeEnum.UGC_GIFT.getCode().equals(heOrderGiftInfoGoodsAddReq.getGiftReasonType()) && StringUtils.isEmpty(heOrderGiftInfoGoodsAddReq.getLinkUrl())){
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code() + "", "需填写发布链接");
        }
        if (!OrderGiftReasonTypeEnum.getNoUploadFileCodeList().contains(heOrderGiftInfoGoodsAddReq.getGiftReasonType()) && CollectionUtils.isEmpty(heOrderGiftInfoGoodsAddReq.getEvidence())){
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code() + "", "需上传证明截图");
        }
//        if (StringUtils.isNotEmpty(heOrderGiftInfoGoodsAddReq.getLinkUrl()) && !checkLinkUrl(heOrderGiftInfoGoodsAddReq.getLinkUrl().trim())){
//            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code() + "", "发布链接格式存在问题");
//        }
    }

    private static Boolean checkLinkUrl(String url) {
        Pattern pattern = Pattern.compile(URL_REGEX);
        Matcher matcher = pattern.matcher(url);
        return matcher.matches();
    }
}

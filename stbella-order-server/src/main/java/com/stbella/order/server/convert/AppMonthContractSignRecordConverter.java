package com.stbella.order.server.convert;

import cn.hutool.core.date.DateUtil;
import com.stbella.contract.model.req.ContractSignRecordReq;
import com.stbella.contract.model.res.ContractSignRecordVO2;
import com.stbella.customer.server.ecp.dto.HeUserCardDTO;
import com.stbella.order.common.enums.month.ContractTypeEnum;
import com.stbella.order.common.enums.month.TemplateTypeEnum;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.server.contract.res.ContractAttachmentVO;
import com.stbella.order.server.contract.res.ContractSignDetailVO;
import com.stbella.order.server.contract.res.ContractSignPaperVO;
import com.stbella.order.server.contract.res.ContractSignVO;
import com.stbella.order.server.order.month.req.MonthContractSignRecordReq;
import com.stbella.order.server.order.month.res.*;
import com.stbella.platform.order.api.contract.req.ContractBaseReq;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

@Mapper(componentModel = "spring", uses = AppImageFormater.class, imports = {DateUtil.class, ContractTypeEnum.class, TemplateTypeEnum.class})
public interface AppMonthContractSignRecordConverter {


    /**
     * entity -> vo
     *
     * @param entity
     * @return {@link MonthContractSignRecordVO}
     */
    MonthContractSignRecordVO entity2MonthContractSignRecordVo(MonthContractSignRecordEntity entity);

    /**
     * vo -> entity
     *
     * @param vo
     * @return {@link MonthContractSignRecordEntity}
     */
    MonthContractSignRecordEntity vo2MonthContractSignRecordEntity(MonthContractSignRecordVO vo);

    MonthContractSignRecordReq vo2MonthContractSignRecordReq(MonthContractSignRecordVO vo);

    /**
     * req -> vo
     *
     * @param req
     * @return {@link MonthContractSignRecordVO}
     */
    MonthContractSignRecordVO req2MonthContractSignRecordVo(MonthContractSignRecordReq req);

    /**
     * entity -> vo 多条
     *
     * @param monthContractSignRecordPOS
     * @return {@link List}<{@link MonthContractSignRecordEntity}>
     */
    List<MonthContractSignRecordVO> entity2MonthContractSignRecordVoList(List<MonthContractSignRecordEntity> monthContractSignRecordPOS);


    /**
     * @param entity
     * @return {@link MonthEsignTemplateParamVO}
     */
    MonthEsignTemplateParamVO entity2MonthEsignTemplateParamVoList(MonthEsignTemplateParamEntity entity);

    /**
     * @param entityList
     * @return {@link List}<{@link MonthEsignTemplateParamVO}>
     */
    List<MonthEsignTemplateParamVO> entity2MonthEsignTemplateParamVoList(List<MonthEsignTemplateParamEntity> entityList);


    /**
     * @param entity
     * @return {@link MonthEsignTemplateVO}
     */
    MonthEsignTemplateVO entity2MonthEsignTemplateVo(MonthEsignTemplateEntity entity);

    /**
     * @param entityList
     * @return {@link List}<{@link MonthEsignTemplateVO}>
     */
    List<MonthEsignTemplateVO> entity2MonthEsignTemplateVoList(List<MonthEsignTemplateEntity> entityList);


    /**
     * @param entity
     * @return {@link MonthOrderParamHistoryVO}
     */
    MonthOrderParamHistoryVO entity2MonthOrderParamHistoryVo(MonthOrderParamHistoryEntity entity);


    /**
     * @param entityList
     * @return {@link List}<{@link MonthOrderParamHistoryVO}>
     */
    List<MonthOrderParamHistoryVO> entity2MonthOrderParamHistoryVoList(List<MonthOrderParamHistoryEntity> entityList);


    /**
     * @param entity
     * @return {@link MonthHeUserEsignVO}
     */
    MonthHeUserEsignVO entity2MonthHeUserEsignVo(MonthHeUserEsignEntity entity);


    /**
     * @param entityList
     * @return {@link List}<{@link MonthHeUserEsignVO}>
     */
    List<MonthHeUserEsignVO> entity2MonthHeUserEsignVoList(List<MonthHeUserEsignEntity> entityList);


    /**
     * @param entity
     * @return {@link MonthContractSignAgreementVO}
     */
    MonthContractSignAgreementVO entity2MonthContractSignAgreementVo(MonthContractSignAgreementEntity entity);


    /**
     * @param entityList
     * @return {@link List}<{@link MonthContractSignAgreementVO}>
     */
    List<MonthContractSignAgreementVO> entity2MonthContractSignAgreementVoList(List<MonthContractSignAgreementEntity> entityList);


    /**
     * @param entity
     * @return {@link MonthEsignTemplateParamConfigVO}
     */
    MonthEsignTemplateParamConfigVO entity2MonthEsignTemplateParamConfigVo(MonthEsignTemplateParamConfigEntity entity);


    /**
     * @param entityList
     * @return {@link List}<{@link MonthEsignTemplateParamConfigVO}>
     */
    List<MonthEsignTemplateParamConfigVO> entity2MonthEsignTemplateParamConfigVoList(List<MonthEsignTemplateParamConfigEntity> entityList);

    ContractSignDetailVO entity2ContractSignDetailVo(MonthContractSignRecordEntity entity);

    HeUserCardDTO entity2UserCardDTO(HeOrderUserSnapshotEntity entity);

    @Mappings({
            @Mapping(target = "orderId", source = "guideId"),
            @Mapping(target = "gmtCreate", expression = "java(DateUtil.date(entity.getCreatedAt()*1000L))"),
            @Mapping(target = "signTime", expression = "java(DateUtil.date(entity.getUpdatedAt()*1000L))"),
            @Mapping(target = "url", source = "contractLongUrl"),
            @Mapping(target = "signFrom", source = "signFrom"),
            @Mapping(target = "img", qualifiedByName = "str2List"),
    })
    ContractSignVO entity2ContractSignVo(MonthContractSignRecordEntity entity);

    List<ContractSignVO> entity2ContractSignVoList(List<MonthContractSignRecordEntity> entity);


    @Mappings({
            @Mapping(target = "contractName", source = "name"),
            @Mapping(target = "contractNo", source = "code"),
            @Mapping(target = "contractStatus", source = "state"),
            @Mapping(target = "contractType", expression = "java(ContractTypeEnum.ESIGN_TYPE.code())"),
            @Mapping(target = "templateType", expression = "java(TemplateTypeEnum.AGREEMENT_TYPE.code())"),
            @Mapping(target = "signTime", expression = "java(DateUtil.date(entity.getSignTime().toLocalTime()))"),
            @Mapping(target = "url", source = "contractLongUrl"),
            @Mapping(target = "signFrom", source = "signFrom"),
    })
    ContractSignVO entitySignAgreement2ContractSignVo(MonthContractSignAgreementEntity entity);

    List<ContractSignVO> entitySignAgreement2ContractSignVoList(List<MonthContractSignAgreementEntity> entity);

    @Mappings({
            @Mapping(target = "contractNo", source = "code"),
            @Mapping(target = "templateContractType", source = "contractType"),
            @Mapping(target = "contractType", expression = "java(ContractTypeEnum.PAPER_TYPE.code())"),
            @Mapping(target = "gmtCreate", expression = "java(DateUtil.date(entity.getCreatedAt()*1000L))"),
            @Mapping(target = "img", qualifiedByName = "str2List"),
            @Mapping(target = "signTime", expression = "java(DateUtil.date(entity.getUpdatedAt()*1000L))"),
    })
    ContractSignVO entitySignPaper2ContractSignVo(ContractSignRecordPaperEntity entity);

    List<ContractSignVO> entitySignPaper2ContractSignVoList(List<ContractSignRecordPaperEntity> entity);


    @Mappings({
            @Mapping(target = "contractNo", source = "code"),
            @Mapping(target = "templateContractType", source = "contractType"),
            @Mapping(target = "contractType", expression = "java(ContractTypeEnum.PAPER_TYPE.code())"),
            @Mapping(target = "gmtCreate", expression = "java(DateUtil.date(entity.getCreatedAt()*1000L))"),
            @Mapping(target = "img", qualifiedByName = "str2List"),
    })
    ContractSignPaperVO entitySignPaper2ContractSignPaperVo(ContractSignRecordPaperEntity entity);

    List<ContractSignPaperVO> entitySignPaper2ContractSignPaperVoList(List<ContractSignRecordPaperEntity> entity);


    @Mappings({
            @Mapping(target = "contractName", source = "name"),
            @Mapping(target = "contractType", expression = "java(ContractTypeEnum.ATTACHMENT_TYPE.code())"),
            @Mapping(target = "templateType", expression = "java(TemplateTypeEnum.ATTACHMENT_TYPE.code())"),
            @Mapping(target = "gmtCreate", expression = "java(DateUtil.date(entity.getCreatedAt()*1000L))"),
    })
    ContractAttachmentVO entityOrderAttachment2ContractSignVo(HeOrderAttachmentEntity entity);

    List<ContractAttachmentVO> entityOrderAttachment2ContractSignVoList(List<HeOrderAttachmentEntity> entity);

    ContractAttachmentVO voContractSignVO2ContractAttachmentVo(ContractSignVO contractSignVO);

    List<ContractAttachmentVO> voContractSignVO2ContractAttachmentVoList(List<ContractSignVO> contractSignVOS);


    List<OrderContractSignRecordVO> signRecordList2OrderSign(List<ContractSignRecordVO2> contractSignRecordAndAgreementListByOrderIdList);

    OrderContractSignRecordVO signRecord2OrderSign(ContractSignRecordVO2 contractSignRecordVO2);

    List<OrderContractSignRecordVO> orderAttachment2ContractRecord(List<HeOrderAttachmentEntity> heOrderAttachmentEntities);


    @Mappings({
            @Mapping(target = "contractName", source = "name"),
            @Mapping(target = "contractType", expression = "java(ContractTypeEnum.ATTACHMENT_TYPE.code())"),
            @Mapping(target = "templateType", expression = "java(TemplateTypeEnum.ATTACHMENT_TYPE.code())"),
            @Mapping(target = "gmtCreate", expression = "java(DateUtil.date(entity.getCreatedAt()*1000L))"),
            @Mapping(target = "url", source = "url"),
    })
    OrderContractSignRecordVO entityOrderAttachment2ContractSign(HeOrderAttachmentEntity entity);


    @Mappings({
            @Mapping(target = "guideId", source = "orderId"),
    })
    ContractSignRecordReq baseReq2SignRecordReq(ContractBaseReq req);
}

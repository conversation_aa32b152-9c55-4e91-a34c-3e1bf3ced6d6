package com.stbella.order.server.context.component.giftOrder.checker;

import com.stbella.core.exception.BusinessException;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.order.OrderGiftReasonTypeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.order.month.req.HeOrderGiftInfoGoodsAddReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@Component
@RefreshScope
@SnowballComponent(name = "赠送订单校验")
public class GiftOrderGoodsChecker implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderRepository orderRepository;

    @Override
    public void run(FlowContext bizContext) {

        HeOrderGiftInfoGoodsAddReq heOrderGiftInfoGoodsAddReq = bizContext.getAttribute(HeOrderGiftInfoGoodsAddReq.class);

        if (OrderGiftReasonTypeEnum.ORDER_REMARK_GIFT.getCode().equals(heOrderGiftInfoGoodsAddReq.getGiftReasonType())){
            if (StringUtils.isEmpty(heOrderGiftInfoGoodsAddReq.getOrderSn()) || Objects.isNull(heOrderGiftInfoGoodsAddReq.getOrderId())){
                throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code() + "", "需填写关联订单ID或关联订单号为必填项");
            }
        }
        if (!OrderGiftReasonTypeEnum.ORDER_REMARK_GIFT.getCode().equals(heOrderGiftInfoGoodsAddReq.getGiftReasonType()) && Objects.nonNull(heOrderGiftInfoGoodsAddReq.getOrderId())){
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code() + "", "仅订单备注赠送需要填写关联订单");
        }
        if (Objects.isNull(heOrderGiftInfoGoodsAddReq.getOrderId())){
            return;
        }
        HeOrderEntity heOrderEntity = orderRepository.getByOrderId(heOrderGiftInfoGoodsAddReq.getOrderId());
        if (Objects.isNull(heOrderEntity)){
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code() + "", "订单不存在");
        }
        if (!heOrderEntity.getOrderSn().equals(heOrderGiftInfoGoodsAddReq.getOrderSn())){
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code() + "", "关联订单号不合法");
        }
        heOrderGiftInfoGoodsAddReq.setRemark(heOrderEntity.getRemark());
    }
}

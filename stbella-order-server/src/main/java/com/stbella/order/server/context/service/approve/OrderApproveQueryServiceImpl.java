package com.stbella.order.server.context.service.approve;

import com.stbella.core.result.Result;
import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.notice.entity.OaProcessIdRelationPO;
import com.stbella.order.domain.client.OaApproveGateway;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.platform.order.api.OrderApproveQueryService;
import com.stbella.platform.order.api.req.OrderDetailQuery;
import com.stbella.platform.order.api.res.ApproveRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author: ji<PERSON><PERSON><PERSON>
 * @CreateTime: 2024-08-03  20:44
 * @Description: 审批查询服务实现
 */
@Service
@DubboService
@Slf4j
public class OrderApproveQueryServiceImpl implements OrderApproveQueryService {

    @Resource
    OaApproveGateway oaApproveGateway;
    @Resource
    OrderRepository orderRepository;

    /**
     * 根据订单查询审批信息
     *
     * @param query
     * @return
     */
    @Override
    public Result<ApproveRes> getApprove(OrderDetailQuery query) {

        OaProcessIdRelationPO approveRecord = oaApproveGateway.getLastRecordByBizIdAndType(query.getOrderId().longValue(), OrderApproveRecordTypeEnum.NEW_DISCOUNT_APPROVAL);
        HeOrderEntity order = orderRepository.getByOrderId(query.getOrderId());



        if (approveRecord == null){
            approveRecord = new OaProcessIdRelationPO();
        }
        ApproveRes approveRes = new ApproveRes().setId(approveRecord.getLocalProcessId()).setTitle(approveRecord.getTitle()).setStatus(order.getApprovalDiscountStatus());


        return Result.success(approveRes);
    }
}

package com.stbella.order.server.manager;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alipay.api.response.AlipayTradeAppPayResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.core.utils.RMBUtils;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.server.convert.PayRecordConvert;
import com.stbella.order.server.order.month.entity.OrderPayRecordPO;
import com.stbella.pay.server.cmbpay.request.*;
import com.stbella.pay.server.cmbpay.request.allinpay.sybpay.PosRefundRequest;
import com.stbella.pay.server.cmbpay.vo.AddOrderRespVO;
import com.stbella.pay.server.cmbpay.vo.DeleteBillRespVO;
import com.stbella.pay.server.cmbpay.vo.RefundRespVO;
import com.stbella.pay.server.cmbpay.vo.allinpay.sybpay.SybRefundRespVO;
import com.stbella.pay.server.core.enums.CmbRefundStatusEnum;
import com.stbella.pay.server.entity.*;
import com.stbella.pay.server.pay.service.CmbPayPosService;
import com.stbella.pay.server.pay.service.CmbPayService;
import com.stbella.pay.server.pay.service.SendCmbRefundMqService;
import com.stbella.pay.server.paymq.service.AliPayService;
import com.stbella.pay.server.paymq.service.PosPayService;
import com.stbella.pay.server.paymq.service.WxPayService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: PayManager
 * @date 2021/12/15 2:49 下午
 */
@Component
@Slf4j
public class PayManager {

    @DubboReference(timeout = 60000)
    private AliPayService aliPayService;
    @DubboReference(timeout = 60000)
    private WxPayService wxPayService;
    @DubboReference(timeout = 60000)
    private CmbPayService cmbPayService;
    @DubboReference(timeout = 180000)
    private PosPayService posPayService;
    @DubboReference(timeout = 180000)
    private CmbPayPosService cmbPayPosService;
    @DubboReference(timeout = 180000)
    private SendCmbRefundMqService sendCmbRefundMqService;
    @Resource
    private PayRecordConvert payRecordConvert;


    /**
     * 聚合支付
     */
    public AddOrderRespVO cmbPay(AddOrderRequest request) {

        Result<AddOrderRespVO> addOrderRespVOResult = cmbPayService.addPayOrder(request);
        if (!addOrderRespVOResult.getSuccess()) {
            log.error("聚合支付异常：{}，参数：{}", addOrderRespVOResult.getMsg(), JSONUtil.toJsonStr(request));
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), "聚合支付发起支付异常");
        }
        return addOrderRespVOResult.getData();
    }

    /**
     * 聚合支付退款
     */
    public RefundRespVO cmbRefund(RefundRequest request) {

        Result<RefundRespVO> addOrderRespVOResult = cmbPayService.refund(request);
        if (!addOrderRespVOResult.getSuccess()) {
            log.error("聚合支付退款异常：{}，参数：{}", addOrderRespVOResult.getMsg(), JSONUtil.toJsonStr(request));
            RefundRespVO refundRespVO = new RefundRespVO();
            refundRespVO.setRespCode(CmbRefundStatusEnum.FAIL.getCode());
            refundRespVO.setRespDesc(addOrderRespVOResult.getMsg());
            return refundRespVO;
            //throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), "聚合支付退款异常"+ addOrderRespVOResult.getMsg());
        }
        return addOrderRespVOResult.getData();
    }

    /**
     * 查询退款情况
     */
    public RefundRespVO queryCmbRefund(OrderPayRecordPO orderPayRecord) {

        RefundSearchRequest refundSearchRequest = new RefundSearchRequest();
        refundSearchRequest.setRfdSerial(orderPayRecord.getId() + "");
        refundSearchRequest.setPayAccountId(orderPayRecord.getPayAccountId());

        Result<RefundRespVO> refundRespVOResult = cmbPayService.refundQuery(refundSearchRequest);
        if (!refundRespVOResult.getSuccess()) {
            log.error("聚合支付请求退款查询异常：{}，参数：{}", refundRespVOResult.getMsg(), JSONUtil.toJsonStr(orderPayRecord));
//            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), "聚合支付退款查询异常");
        }
        return refundRespVOResult.getData();
    }

    public RefundRespVO queryCmbRefund(String refundOrderSn, Long payAccountId) {
        RefundSearchRequest refundSearchRequest = new RefundSearchRequest();
        refundSearchRequest.setRfdSerial(refundOrderSn);
        refundSearchRequest.setPayAccountId(payAccountId);
        Result<RefundRespVO> refundRespVOResult = cmbPayService.refundQuery(refundSearchRequest);
        if (!refundRespVOResult.getSuccess()) {
            log.error("聚合支付请求退款查询异常：{}，refundOrderSn参数：{}", refundRespVOResult.getMsg(), refundOrderSn);
            RefundRespVO refundRespVO = new RefundRespVO();
            refundRespVO.setRespCode(CmbRefundStatusEnum.IN_PROCESS.getCode());
            refundRespVO.setRespDesc("聚合支付退款查询异常");
            return refundRespVO;
        }
        log.info("聚合支付退款查询结果：{}", JSONUtil.toJsonStr(refundRespVOResult.getData()));
        return refundRespVOResult.getData();
    }

    /**
     * 支付宝APP发起支付
     */
    public String aliAppPay(AliPayRequest request) {
        AlipayTradeAppPayResponse response = aliPayService.aliAppPay(request);
        if (!response.isSuccess()) {
            log.info("支付宝发起支付异常,={}", response.getMsg());
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), "支付宝发起支付异常");
        }
        return response.getBody();
    }

    /**
     * 支付宝wap发起支付
     */
    public String aliWapPay(AliPayRequest request) {
        String url = aliPayService.aliWapPay(request);
        if (ObjectUtil.isEmpty(url)) {
            log.info("支付宝wap发起支付异常");
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), "支付宝发起支付异常");
        }
        return url;
    }

    /**
     * 支付宝wap发起支付
     */
    public String aliWapPayStr(AliPayRequest request) {
        String form = aliPayService.aliWapPayStr(request);
        if (ObjectUtil.isEmpty(form)) {
            log.info("支付宝wap发起支付异常");
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), "支付宝发起支付异常");
        }
        return form;
    }


    /**
     * 支付宝交易查询
     */
    public AlipayTradeQueryResponse aliTradeQuery(AlipayTradeQueryRequest request) {
        AlipayTradeQueryResponse response = aliPayService.tradeQuery(request);
        if (!response.isSuccess()) {
            log.info("支付宝查询异常,={}", response.getMsg());
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), "支付宝查询异常");
        }
        return response;
    }

    /**
     * 支付宝退款
     */
    public AlipayTradeRefundResponse aliTradeRefund(AlipayTradeRefundRequest request) {
        AlipayTradeRefundResponse response = aliPayService.tradeRefund(request);
        if (!response.isSuccess()) {
            log.info("支付宝退款异常,={}", response.getMsg());
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), response.getMsg());
        }
        return response;
    }

    /**
     * 微信JS发起支付
     */
    public JSONObject wxJsPay(WxPayRequest request) {
        try {
            return wxPayService.wxJsPay(request);
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 微信APP发起支付
     */
    public JSONObject wxAppPay(WxPayRequest request) {
        try {
            return wxPayService.wxAppPay(request);
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), e.getMessage());
        }
    }


    /**
     * 微信交易查询
     */
    public String wxTradeQuery(WxpayTradeQueryRequest request) {
        try {
            return wxPayService.tradeQuery(request);
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), e.getMessage());
        }
    }


    /**
     * 微信退款
     */
    public String wxTradeRefund(WxpayTradeRefundRequest request) {
        try {
            return wxPayService.tradeRefund(request);
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * pos支付
     */
    public JSONObject posPay(PosPayRequest request) {
        try {
            return posPayService.posWapPayStr(request);
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * pos查询
     */
    public JSONObject posTradeQuery(PosPayQueryRequest request) {
        try {
            return posPayService.tradeQuery(request);
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * pos退款
     */
    public JSONObject posTradeRefund(PosPayRefundRequest request) {
        try {
            return posPayService.tradeRefund(request);
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 招行pos退款
     */
    public SybRefundRespVO cmbPosRefund(PosRefundRequest request) {
        try {
            return cmbPayPosService.refundPosV2(request);
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), e.getMessage());
        }
    }

    public Boolean deleteBill(String incomeSn) {
        DeleteBillRequest request = new DeleteBillRequest();
        request.setOrderId(incomeSn);
        Result<DeleteBillRespVO> deleteBillRespVOResult = cmbPayService.deleteBill(request);
        Boolean result = true;
        if (!deleteBillRespVOResult.getSuccess()) {
            result = false;
        }
        return result;
    }

    /**
     * 删除未支付的账单，避免多支付
     *
     * @param accountId
     * @param incomeSn
     * @return
     */
    public Boolean deleteBill(Long accountId, String incomeSn) {
        DeleteBillRequest request = new DeleteBillRequest();
        request.setOrderId(incomeSn);
        request.setPayAccountId(accountId);
        Result<DeleteBillRespVO> deleteBillRespVOResult = cmbPayService.deleteBill(request);
        Boolean result = true;
        if (!deleteBillRespVOResult.getSuccess()) {
            result = false;
        }
        return result;
    }


    /**
     * 发送招商退款MQ
     */
    @SneakyThrows
    public void sendCmbRefundMq(HeOrderRefundEntity heOrderRefundEntity, RefundRespVO refundRespVO, Integer accountType) {
        SendCmbMqRequest sendCmbMqRequest = payRecordConvert.refundRespVO2sendCmbMqRequest(refundRespVO);
        sendCmbMqRequest.setAccountType(accountType);
        sendCmbMqRequest.setOutTradeNo(heOrderRefundEntity.getIncomeSn());
        sendCmbMqRequest.setOutRefundNo(String.valueOf(heOrderRefundEntity.getId()));
        sendCmbMqRequest.setRefundType(heOrderRefundEntity.getRefundType());
        sendCmbMqRequest.setRefundAmount(new BigDecimal(RMBUtils.changeF2Y(String.valueOf(heOrderRefundEntity.getApplyAmount()))));
        sendCmbMqRequest.setSuccessTime(new Date());
        sendCmbRefundMqService.sendCmb(sendCmbMqRequest);
    }
}

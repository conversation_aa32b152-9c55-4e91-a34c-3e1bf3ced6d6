package com.stbella.order.server.order.cts.controller;

import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.stbella.core.result.Result;
import com.stbella.core.utils.JwtUtil;
import com.stbella.order.server.order.cts.entity.OrderCtsPO;
import com.stbella.order.server.order.cts.entity.OrderCtsPaySignPO;
import com.stbella.order.server.order.cts.request.order.OrderApplyRefundRequest;
import com.stbella.order.server.order.cts.request.order.OrderPaySignRequest;
import com.stbella.order.server.order.cts.response.order.CtsPayDetailsBaseResponse;
import com.stbella.order.server.order.cts.response.order.CtsPayDetailsResponse;
import com.stbella.order.server.order.cts.response.order.OrderApplyRefundDetailResponse;
import com.stbella.order.server.order.cts.service.CtsPayService;
import com.stbella.order.server.order.cts.service.OrderCtsPaySignService;
import com.stbella.order.server.order.cts.service.OrderCtsService;
import com.stbella.order.server.order.cts.service.OrderFacade;
import com.stbella.order.server.utils.ThrowableUtil;
import com.stbella.store.server.cts.entity.CtsSitePO;
import com.stbella.store.server.cts.service.CtsSiteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;

@Validated
@Api(tags = "到家支付")
@RestController
@RequestMapping("/cts/pay")
@Slf4j
@RefreshScope
public class CtsPayController {

    @Resource
    private CtsPayService ctsPayService;
    @Resource
    private OrderFacade orderFacade;
    @Resource
    private OrderCtsPaySignService orderCtsPaySignService;
    @Resource
    private OrderCtsService orderCtsService;
    @DubboReference(timeout = 60000)
    private CtsSiteService ctsSiteService;

    @Value("${qrCode.cts-url}")
    private String ctsUrl;
    @Value("${qrCode.width}")
    private String width;
    @Value("${qrCode.height}")
    private String height;
    @Value("${qrCode.margin}")
    private String margin;

    @ApiOperation(value = "获取二维码")
    @GetMapping("/createQrCodeN")
    public void createQrCodeN(HttpServletResponse response, String paySign) {
        String content = ctsUrl + "?" + "paySign=" + paySign;
        OrderCtsPaySignPO byPaySign = orderCtsPaySignService.getByPaySign(paySign);
        OrderCtsPO orderCtsPO = orderCtsService.getByOrderNo(byPaySign.getOrderNo());
        if (orderCtsPO != null) {
            CtsSitePO ctsSitePo = ctsSiteService.getSiteByUserId(orderCtsPO.getSellId());
            if (ctsSitePo != null) {
                content = content + "&bizId=" + ctsSitePo.getId() + "&orderType=" + orderCtsPO.getOrderType();
            }
        }

        int widthInt = Integer.parseInt(width);
        int heightInt = Integer.parseInt(height);
        QrConfig config = new QrConfig(widthInt, heightInt);
        //        File file = null;
        //        //附带logo
        //        config.setImg(file);
        // 设置边距，既二维码和背景之间的边距
        config.setMargin(Integer.valueOf(margin));
        // 高纠错级别
        config.setErrorCorrection(ErrorCorrectionLevel.H);
        // 设置前景色，既二维码颜色（青色）
        Color foreColor = new Color(11, 12, 14);
        config.setForeColor(foreColor);
        // 设置背景色（灰色）
        Color backColor = new Color(255, 255, 255);
        config.setBackColor(backColor);

        BufferedImage bufferedImage = QrCodeUtil.generate(//
                content, //二维码内容
                config
        );
        try {
            //以JPEG格式向客户端发送
            ServletOutputStream os = response.getOutputStream();
            ImageIO.write(bufferedImage, "PNG", os);
            os.flush();
            os.close();
        } catch (IOException e) {
            log.error("二维码生成异常:{}", ThrowableUtil.getStackTrace(e));
        }
    }

    @ApiOperation(value = "申请退款详情")
    @GetMapping("/refund/apply/detail")
    public Result<OrderApplyRefundDetailResponse> applyRefundDetail(@NotBlank String orderNo) {
        return Result.success(ctsPayService.applyRefundDetail(orderNo));
    }

    @ApiOperation(value = "申请退款")
    @PostMapping("/refund/apply")
    public Result<Long> applyRefund(@Valid @RequestBody OrderApplyRefundRequest request) {
        request.setUserTokenInfoDTO(JwtUtil.getJwtTokenUserInfo());
        return Result.success(ctsPayService.applyRefund(request));
    }

    @ApiOperation(value = "通过订单编号获取订单金额信息")
    @GetMapping("/detail/by-order-no")
    public Result<CtsPayDetailsBaseResponse> queryPayDetailByOrderNo(@NotBlank String orderNo) {
        return Result.success(orderFacade.queryPayDetailByOrderNo(orderNo));
    }

    @ApiOperation(value = "获取订单支付标识")
    @GetMapping("/get-pay-sign")
    public Result<String> getOrderPaySign(@Valid OrderPaySignRequest request) {
        return Result.success(ctsPayService.getOrderPaySign(request));
    }

    @ApiOperation(value = "通过订单支付标识获取支付状态", notes = "1-支付成功;2-支付失败;3-处理中")
    @GetMapping("/pay-status")
    public Result<Integer> getOrderPayStatusByPaySign(@NotBlank String paySign) {
        return Result.success(ctsPayService.getOrderPayStatusByPaySign(paySign));
    }

    @ApiOperation(value = "通过支付标识获取支付页详情")
    @GetMapping("/detail/by-pay-sign")
    public Result<CtsPayDetailsResponse> queryPayDetailByPaySign(@NotBlank String paySign) {
        return Result.success(orderFacade.queryPayDetailByPaySign(paySign));
    }
}

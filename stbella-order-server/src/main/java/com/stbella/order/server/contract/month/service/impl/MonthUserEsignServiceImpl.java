package com.stbella.order.server.contract.month.service.impl;

import com.stbella.order.domain.repository.UserEsignRepository;
import com.stbella.order.server.contract.service.month.MonthUserEsignService;
import com.stbella.order.server.convert.AppMonthContractSignRecordConverter;
import com.stbella.order.server.order.month.res.MonthHeUserEsignVO;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class MonthUserEsignServiceImpl  implements MonthUserEsignService {

    @Resource
    private UserEsignRepository userEsignRepository;
    @Resource
    private AppMonthContractSignRecordConverter appMonthContractSignRecordConverter;
    @Override
    public MonthHeUserEsignVO get(String name, String idCardNo) {
        return appMonthContractSignRecordConverter.entity2MonthHeUserEsignVo(userEsignRepository.queryByConditionOne(name, idCardNo));
    }

    @Override
    public MonthHeUserEsignVO queryByConditionOne(String name, String phone, Integer idCardType, String idCardNo){
        return appMonthContractSignRecordConverter.entity2MonthHeUserEsignVo(userEsignRepository.queryByConditionOne(name, phone, idCardType, idCardNo));
    }

}

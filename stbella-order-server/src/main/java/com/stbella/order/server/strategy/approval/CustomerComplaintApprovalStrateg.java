package com.stbella.order.server.strategy.approval;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.stbella.asset.api.enums.TradeEventEnum;
import com.stbella.customer.server.ecp.entity.UserPO;
import com.stbella.customer.server.ecp.service.UserService;
import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.month.server.request.RefundApprovalRequest;
import com.stbella.notice.entity.OaProcessIdRelationPO;
import com.stbella.notice.server.OaProcessIdRelationService;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.common.enums.order.OrderGiftStatusEnum;
import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGiftExtendEntity;
import com.stbella.order.domain.order.month.entity.TabClientEntity;
import com.stbella.order.domain.repository.ClientRepository;
import com.stbella.order.domain.repository.HeCustomerComplaintsRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.order.month.enums.CustomerComplaintsStatusEnum;
import com.stbella.order.server.order.month.request.approval.ApprovalStatusInfo;
import com.stbella.order.server.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.FlowIdentity;
import top.primecare.snowball.flow.core.definition.FlowIdentityBuilder;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Component
public class CustomerComplaintApprovalStrateg implements ApprovalStrategy {

    @Resource
    private HeCustomerComplaintsRepository customerComplaintsRepository;
    @DubboReference
    private OaProcessIdRelationService oaProcessIdRelationService;
    @DubboReference
    private UserService userService;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private ClientRepository clientRepository;


    @Override
    public void handleApproval(OrderApproveRecordTypeEnum type, String params, String messageId, ApprovalStatusInfo approvalStatusInfo, String processId) {

        log.info("开始处理客诉回调：{}，approvalStatusInfo：{}，processId：{}", params, JsonUtil.write(approvalStatusInfo), processId);


        OaProcessIdRelationPO oneByLocalProcessId = oaProcessIdRelationService.getOneByLocalProcessId(processId);

        String qwProcessId = oneByLocalProcessId.getQwProcessId();

        HeCustomerComplaintsEntity customerComplaintsEntity = customerComplaintsRepository.selectByApproveId(processId);

        if (ObjectUtil.isEmpty(customerComplaintsEntity)) {
            log.error("客诉审批回调不存在：{}", qwProcessId);
        }
        if (CustomerComplaintsStatusEnum.CANCEL.getCode().equals(customerComplaintsEntity.getComplaintStatus())){
            log.info("客诉工单申请已取消,processId: {},当前消息不做处理", oneByLocalProcessId.getQwProcessId());
            return;
        }

        boolean finish = approvalStatusInfo.isFinish();
        boolean agree = approvalStatusInfo.isAgree();

        if (finish) {

            FlowIdentityBuilder flowIdentityBuilder = null;

            Long orderId = customerComplaintsEntity.getOrderId();
            HeOrderEntity orderEntity = orderRepository.getByOrderId(orderId.intValue());

            FlowContext context = new FlowContext();

            customerComplaintsEntity.setApproveFinish(new Date());

            if (agree) {
                //审批结束且成功
                flowIdentityBuilder = FlowIdentity.builder()
                        .bizActivity(BizActivityEnum.CUSTOMER_COMPLAINTS_AGREE.code())
                        .idSlice("Customer")
                        .idSlice("Complaints")
                        .idSlice("Agree");
                customerComplaintsEntity.setComplaintStatus(CustomerComplaintsStatusEnum.PROCESS.getCode());

                context.setAttribute(TradeEventEnum.class, TradeEventEnum.COMPLAINT_GIFT);
                context.setAttribute(ApprovalStatusInfo.class, approvalStatusInfo);
                context.setAttribute(OtherConstant.CONTINUE, true);
            } else {
                //拒绝
                flowIdentityBuilder = FlowIdentity.builder()
                        .bizActivity(BizActivityEnum.CUSTOMER_COMPLAINTS_REFUSE.code())
                        .idSlice("Customer")
                        .idSlice("Complaints")
                        .idSlice("Refuse");
                customerComplaintsEntity.setComplaintStatus(CustomerComplaintsStatusEnum.APPROVAL_REFUSE.getCode());
                customerComplaintsEntity.setResolveFinish(new Date());
            }

            customerComplaintsRepository.update(customerComplaintsEntity);

            context.setAttribute(HeCustomerComplaintsEntity.class, customerComplaintsEntity);
            context.setAttribute(HeOrderEntity.class, orderEntity);
            context.setAttribute(ApprovalStatusInfo.class, approvalStatusInfo);

            if (ObjectUtil.isNotEmpty(type)) {
                context.setAttribute("type", type);
            }

            OaProcessIdRelationPO oneByLocalProcessId1 = oaProcessIdRelationService.getOneByLocalProcessId(processId);
            if (ObjectUtil.isNotEmpty(oneByLocalProcessId1.getParam())) {
                context.setAttribute("params", oneByLocalProcessId1.getParam());
            }
            if (ObjectUtil.isNotEmpty(messageId)) {
                context.setAttribute("messageId", messageId);
            }
            if (ObjectUtil.isNotEmpty(processId)) {
                context.setAttribute("processId", processId);
            }

            RefundApprovalRequest refundApprovalRequest = new RefundApprovalRequest();

            Integer creator = customerComplaintsEntity.getCreator();

            UserPO creatorInfo = userService.queryUserById(creator.longValue());
            if (ObjectUtil.isNotEmpty(creatorInfo)) {
                refundApprovalRequest.setPhone(creatorInfo.getPhone());
            }

            TabClientEntity tabClientEntity = clientRepository.getTabClientById(orderEntity.getClientUid());
            if (ObjectUtil.isNotEmpty(tabClientEntity)) {
                refundApprovalRequest.setCustomerName(tabClientEntity.getName());
            }

            refundApprovalRequest.setOrderId(customerComplaintsEntity.getOrderId().intValue());
            if (ObjectUtil.isNotEmpty(customerComplaintsEntity.getRefundOrderId())) {
                refundApprovalRequest.setOrderRefundId(customerComplaintsEntity.getRefundOrderId().intValue());
            }
            context.setAttribute(RefundApprovalRequest.class, refundApprovalRequest);

            SnowballFlowLauncher.fire(flowIdentityBuilder.build(), context);
        }

        if (approvalStatusInfo.isTerminate()) {

            log.info("客诉工单申请终止,客户手动取消工单申请 processId: {}", oneByLocalProcessId.getQwProcessId());
            customerComplaintsEntity.setComplaintStatus(CustomerComplaintsStatusEnum.CANCEL.getCode());
            customerComplaintsRepository.update(customerComplaintsEntity);

            FlowContext context = new FlowContext();
            context.setAttribute(HeCustomerComplaintsEntity.class, customerComplaintsEntity);
            context.setAttribute(OaProcessIdRelationPO.class, oneByLocalProcessId);
            FlowIdentity identity = FlowIdentity.builder()
                    .bizActivity(BizActivityEnum.CUSTOMER_COMPLAINTS_CANCEL.code())
                    .idSlice("Complaints")
                    .idSlice("Cancel")
                    .build();
            SnowballFlowLauncher.fire(identity, context);
        }
    }


    @Override
    public OrderApproveRecordTypeEnum getOrderApproveRecordTypeEnum() {
        return OrderApproveRecordTypeEnum.CUSTOMER_COMPLAINT_CLOSED_LOOP;
    }
}

package com.stbella.order.server.context.component.assembler;

import cn.hutool.json.JSONUtil;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.server.order.TaxRateFact;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 获取币种税率
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TaxRateAssembler {

    @Resource
    RuleLinkClient ruleLinkClient;

    /**
     * 获取币种税率
     * 格式：0.09 表示9%的税率
     * 0表示没有税
     *
     * @param taxRateFact
     * @return
     */
    public BigDecimal run(TaxRateFact taxRateFact) {

        log.info("开始获取taxRate={}", JSONUtil.toJsonStr(taxRateFact));
        //获取税率
        //删除可能的空格
        String pureCurrency = taxRateFact.getCurrency().trim();
        Map<String, Object> factMap = new HashMap<>(1);
        factMap.put("currency", pureCurrency);
        factMap.put("startTime", taxRateFact.getEffectDate());
        ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req("currency_text_rate", factMap);

        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
        log.info("获取结果hitRuleVo={}", JSONUtil.toJsonStr(hitRuleVo));
        if (hitRuleVo != null) {
            return new BigDecimal(hitRuleVo.getSimpleRuleValue());
        }
        //如果没有默认0
        return new BigDecimal(0);
    }

}

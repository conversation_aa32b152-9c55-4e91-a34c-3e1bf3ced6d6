package com.stbella.order.server.context.component.spi.impl;

import cn.hutool.json.JSONObject;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.server.context.component.spi.RefundSpi;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.annotation.FuncPointSpiImpl;
import top.primecare.snowball.extpoint.definition.FuncSpiConfig;

@Component
@FuncPointSpiImpl(name = "余额")
public class BalanceRefundSpiImpl implements RefundSpi {

    @Override
    public boolean condition(HeOrderRefundEntity param) {
        return false;
    }

    @Override
    public JSONObject invoke(HeOrderRefundEntity param) {
        return null;
    }

    @Override
    public FuncSpiConfig config() {
        return null;
    }
}

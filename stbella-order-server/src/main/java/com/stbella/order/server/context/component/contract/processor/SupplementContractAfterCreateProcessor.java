package com.stbella.order.server.context.component.contract.processor;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.contract.model.res.ContractSignRecordVO2;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.domain.repository.OrderReductionRepository;
import com.stbella.platform.order.api.contract.req.ContractBaseReq;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 合同创建
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "合同创建后处理")
public class SupplementContractAfterCreateProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    OrderReductionRepository orderReductionRepository;

    @Override
    public void run(FlowContext bizContext) {

        ContractBaseReq contractBaseReq = bizContext.getAttribute(ContractBaseReq.class);
        OrderReductionEntity orderReductionEntity;
        if (Objects.nonNull(contractBaseReq.getReductionId())) {
            orderReductionEntity = orderReductionRepository.getById(contractBaseReq.getReductionId());
        } else {
            Long contractId = contractBaseReq.getContractId().longValue();
            orderReductionEntity = orderReductionRepository.selectByContractId(contractId);
            log.info("补充协议创建后处理，查询减免记录，contractId={},orderReductionEntity={}", contractId, orderReductionEntity);
        }
        if (Objects.isNull(orderReductionEntity)) {
            log.error("补充协议创建后处理失败，未找到对应的减免记录");
            return;
        }
        ContractSignRecordVO2 contractSignRecordVO2 = bizContext.getAttribute(ContractSignRecordVO2.class);
        orderReductionEntity.setContractId(contractSignRecordVO2.getId());
        orderReductionRepository.updateOne(orderReductionEntity);
    }

    @Override
    public boolean condition(FlowContext bizContext) {
        ContractSignRecordVO2 contractSignRecordVO2 = bizContext.getAttribute(ContractSignRecordVO2.class);
        if (ObjectUtil.isNotEmpty(contractSignRecordVO2) && TemplateContractTypeEnum.SUPPLIMENT.code().equals(contractSignRecordVO2.getTemplateContractType())) {
            return true;
        }
        return false;
    }


}

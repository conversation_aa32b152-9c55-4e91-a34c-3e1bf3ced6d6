package com.stbella.order.server.context.component.validator;

import com.stbella.core.exception.BusinessException;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.core.OrderNoticeEnum;
import com.stbella.order.common.enums.core.OrderProcessTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.service.OrderIncomeDomainService;
import com.stbella.order.server.context.component.checker.OrderProcessCheckerContext;
import com.stbella.order.server.order.month.req.PayReqV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 订单报单校验器
 * <AUTHOR>
 */
@Slf4j
@Component
@RefreshScope
@SnowballComponent(name = "订单报单校验器")
public class OrderBroadcastValidator implements IExecutableAtom<FlowContext> {

    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);
        if (order.getIsNotice().equals(OrderNoticeEnum.NOTICE_YES.code())) {
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code()+"", "订单已报过单，无需重复报单！");
        }
    }

}

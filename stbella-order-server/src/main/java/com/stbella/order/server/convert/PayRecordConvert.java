package com.stbella.order.server.convert;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.HeIncomeProofRecordEntity;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.server.config.DateMapper;
import com.stbella.order.server.config.MappingConfig;
import com.stbella.order.server.order.month.entity.OrderPayRecordPO;
import com.stbella.order.server.order.month.request.pay.PayNotityRequest;
import com.stbella.order.server.order.month.res.IncomeRecordVO;
import com.stbella.order.server.order.month.res.RefundRecordDetailVO;
import com.stbella.order.server.order.month.res.RefundRecordListVO;
import com.stbella.order.server.order.month.response.*;
import com.stbella.order.server.order.nutrition.entity.OrderNutritionApplyRefundPO;
import com.stbella.order.server.order.nutrition.entity.OrderNutritionAuditPO;
import com.stbella.order.server.order.nutrition.response.SimpleApplyRefundVO;
import com.stbella.order.server.order.nutrition.response.SimpleAuditVO;
import com.stbella.order.server.utils.BigDecimalUtil;
import com.stbella.pay.server.cmbpay.request.SendCmbMqRequest;
import com.stbella.pay.server.cmbpay.vo.RefundRespVO;
import com.stbella.pay.server.entity.mq.PayNotifyMq;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: StoreConvert 门店转换类
 * @date 2021/5/17 4:26 下午
 */
@Mapper(config = MappingConfig.class, uses = DateMapper.class, imports = {AmountChangeUtil.class, Date.class, ObjectUtil.class,BigDecimal.class,BigDecimalUtil.class})
public interface PayRecordConvert {

    Page<PayRecordVO> orderPayRecordPOPage2PayRecordVOPage(Page<OrderPayRecordPO> orderPayRecordPOS);

    List<PayRecordDetailVO> orderPayRecordPO2PayRecordDetailVO(List<OrderPayRecordPO> orderPayRecordPO);

    OrderPayRecordPO orderPayRecordPO2OrderPayRecordPO(OrderPayRecordPO orderPayRecordPO);

    Page<RefundRecordVO> orderPayRecordPOPage2RefundRecordVOPage(Page<OrderPayRecordPO> orderPayRecordPOPage);

    PayNotityRequest payRecordConvert2PayNotityRequest(PayNotifyMq payNotifyMq);


    @Mappings({
            @Mapping(target = "refundDate", source = "payTime"),
            @Mapping(target = "refundApplyDate", source = "gmtCreate"),
            @Mapping(target = "refundAmount", source = "payAmount")
    })
    RefundStatusVO orderPayRecordPO2RefundStatusVO(OrderPayRecordPO orderPayRecordPO);


    SimpleAuditVO orderNutritionAuditPO2SimpleAuditVO(OrderNutritionAuditPO orderNutritionAuditPO);

    SimpleApplyRefundVO orderNutritionApplyRefundPO2SimpleApplyRefundVO(OrderNutritionApplyRefundPO orderNutritionApplyRefundPO);

    @Mappings({
            @Mapping(target = "canRefundAmount", expression = "java( (ObjectUtil.isEmpty(entity.getIncome())?0:entity.getIncome())-(ObjectUtil.isEmpty(entity.getFreezeAmount())?0:entity.getFreezeAmount())-(ObjectUtil.isEmpty(entity.getAlreadyRefundAmount())?0:entity.getAlreadyRefundAmount()))"),
            @Mapping(target = "income", expression = "java( ObjectUtil.isEmpty(entity.getIncome())?BigDecimal.ZERO:BigDecimalUtil.divide(new BigDecimal(entity.getIncome().toString()),new BigDecimal(100)))"),
    })
    IncomeRecordVO entity2VO(HeIncomeRecordEntity entity);

    Page<IncomeRecordVO> pageIncomeEntity2VO(Page<HeIncomeRecordEntity> page);

    RefundRecordDetailVO refundEntity2VO(HeOrderRefundEntity entity);


    @Mappings({
            @Mapping(target = "receiptType", source = "type"),
            @Mapping(target = "incomeId", source = "orderGoodId")
    })
    RefundRecordListVO refundEntity2VOList(HeOrderRefundEntity entity);

    List<RefundRecordListVO> listRefundEntity2VO(List<HeOrderRefundEntity> entityList);

    Page<RefundRecordListVO> pageRefundEntity2VO(Page<HeOrderRefundEntity> page);

    List<QueryOrderByCustomerIdQueryVO> incomeRecordEntity2QueryOrderByCustomerIdQueryVO(List<HeIncomeRecordEntity> recordListByOrderId);

    @Mappings({
            @Mapping(target = "incomeProof", expression = "java(new BigDecimal(oneByIncomeSn.getIncomeProof()).divide(new BigDecimal(100)).setScale(2))"),
    })
    QueryIncomeProofRecordByIncomeSnVO heIncomeProofRecordEntity2QueryIncomeProofRecordByIncomeSnVO(HeIncomeProofRecordEntity oneByIncomeSn);

    List<QueryIncomeProofRecordByIncomeSnVO> heIncomeProofRecordEntity2QueryIncomeProofRecordByIncomeSnVOList(List<HeIncomeProofRecordEntity> entityList);


    List<IncomeRecordVO> listIncomeEntity2VO(List<HeIncomeRecordEntity> recordListByOrderId);

    SendCmbMqRequest refundRespVO2sendCmbMqRequest(RefundRespVO refundRespVO);
}

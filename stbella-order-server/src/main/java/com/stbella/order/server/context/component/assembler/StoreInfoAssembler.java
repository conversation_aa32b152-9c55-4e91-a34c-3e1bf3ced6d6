package com.stbella.order.server.context.component.assembler;

import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.repository.StoreRepository;
import com.stbella.order.server.context.component.StoreCurrencyContainer;
import com.stbella.order.server.order.TaxRateFact;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-05-29  15:20
 * @Description: 门店信息组装
 */
@Component
@Slf4j
@SnowballComponent(name = "门店信息组装", desc = "通过门店id组装相关信息")
public class StoreInfoAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    StoreRepository storeRepository;
    @Resource
    TaxRateAssembler taxRateAssembler;

    @Override
    public void run(FlowContext bizContext) {
        Integer storeId = (Integer) bizContext.getAttribute(BizConstant.ExtraKey.storeId);
        CfgStoreEntity storeEntity = storeRepository.queryCfgStoreById(storeId);

        String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(storeEntity.getStoreId());
        String pureCurrency = storeCurrencyCode.trim();
        TaxRateFact taxRateFact = TaxRateFact.builder().currency(pureCurrency).effectDate(DateUtils.getTenBitTimestamp()).build();
        BigDecimal taxRate = taxRateAssembler.run(taxRateFact);

        storeEntity.setTaxRate(taxRate);

        log.info("store：{}-{}, taxRate:{}", storeEntity.getStoreId(), storeEntity.getStoreName(), taxRate);

        bizContext.addAttribute(CfgStoreEntity.class, storeEntity);
    }
}

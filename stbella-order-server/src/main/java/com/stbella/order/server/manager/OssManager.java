package com.stbella.order.server.manager;

import com.stbella.sso.server.dingding.entity.DdEmployeePO;
import com.stbella.sso.server.dingding.service.DdEmployeeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * OSS服务调用
 */
@Slf4j
@Component
public class OssManager {

    @DubboReference(timeout = 30000)
    private DdEmployeeService ddEmployeeService;

    /**
     * 根据工号查询员工信息
     */
    public List<DdEmployeePO> queryUserByJobNumberList(List<String> jobNumberList){
        log.info("queryUserByJobNumberList start, jobNumberList={}", jobNumberList);
        return ddEmployeeService.queryUserByJobNumberList(jobNumberList);
    }
}

package com.stbella.order.server.context.component.imports;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2023-07-25  14:43
 * @Description: 订单采购金导入
 */
@Data
public class OrderPurchaseCoinImportDTO implements Serializable {

    @ApiModelProperty(value = "门店")
    @ExcelProperty(value = "门店")
    private String storeName;

    @ExcelProperty(value = "区域")
    private String regionName;

    @ExcelProperty(value = "品牌")
    private String brandName;

    @ExcelProperty(value = "房态状态")
    private String roomStatusName;

    @ExcelProperty(value = "护理模式")
    private String careModeName;

    @ExcelProperty(value = "订单号")
    private String orderSn;


    @ExcelProperty(value = "客户名")
    private String customerName;

    @ExcelProperty(value = "basicId")
    private String basicId;

    @ExcelProperty(value = "入住时间")
    private String checkinDate;

    @ExcelProperty(value = "离店时间")
    private String checkoutDate;

    @ExcelProperty(value = "间夜数")
    private String livingDays;

    @ExcelProperty(value = "房间号")
    private String roomNo;

    @ExcelProperty(value = "套餐名")
    private String goodsName;

    @ExcelProperty(value = "套餐简称")
    private String nickGoodsName;

    @ExcelProperty(value = "天数")
    private String Days;

    @ExcelProperty(value = "是否充值")
    private String recharge;

    @ExcelProperty(value = "充值金额")
    private String rechargeAmount;

    @ExcelProperty(value = "充值日期")
    private String rechargeDate;

}

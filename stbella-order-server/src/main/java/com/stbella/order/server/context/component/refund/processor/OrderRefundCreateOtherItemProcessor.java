package com.stbella.order.server.context.component.refund.processor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.customer.server.ecp.request.OrderRefundMinusAssetRequest;
import com.stbella.customer.server.ecp.service.HeUserIntegralRecordV2Service;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.month.AheadOutRoomEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.AheadOutRoomEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.repository.AheadOutRoomRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.async.AsyncOrder;
import com.stbella.order.server.order.month.req.AheadOutRoomQuery;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;


@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "创建退款申请后的其他项处理")
public class OrderRefundCreateOtherItemProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private AheadOutRoomRepository aheadOutRoomRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private AsyncOrder asyncOrder;
    @DubboReference
    private HeUserIntegralRecordV2Service heUserIntegralRecordV2Service;

    @Override
    public boolean condition(FlowContext context) {
        return (boolean) context.getAttribute(OtherConstant.CONTINUE);
    }

    @Override
    public void run(FlowContext bizContext) {
        //本次请求参数
        CreateRefundReq attribute = bizContext.getAttribute(CreateRefundReq.class);
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        Boolean deposit = orderEntity.isDepositOrder();

        log.info("创建退款申请后的其他项处理attribute:{}, deposit:{}", JSONUtil.toJsonStr(attribute), deposit);
        //非押金
        if (!deposit) {
            //修改提前离馆可退金额
            AheadOutRoomQuery query = new AheadOutRoomQuery();
            query.setOrderId(attribute.getOrderId());
            query.setState(AheadOutRoomEnum.STATE_DISENABLE.code());
            AheadOutRoomEntity aheadOutRoomEntity = aheadOutRoomRepository.queryByOrderId(query);
            if (ObjectUtil.isNotEmpty(aheadOutRoomEntity)) {
                aheadOutRoomEntity.setRemainingRefundableAmount(aheadOutRoomEntity.getRemainingRefundableAmount() - attribute.getRefundAmount().multiply(new BigDecimal(100)).intValue());
                aheadOutRoomRepository.updateOutRoom(aheadOutRoomEntity);
            }


            //主退款
            HeOrderRefundEntity orderRefundEntity = bizContext.getAttribute(HeOrderRefundEntity.class);
            //订单那
            OrderRefundMinusAssetRequest orderRefundMinusAssetRequest = new OrderRefundMinusAssetRequest();
            orderRefundMinusAssetRequest.setRefundId(orderRefundEntity.getId());
            orderRefundMinusAssetRequest.setRefundSn(orderRefundEntity.getRefundOrderSn());
            orderRefundMinusAssetRequest.setOrderId(orderEntity.getOrderId());
            orderRefundMinusAssetRequest.setOrderSn(orderEntity.getOrderSn());
            orderRefundMinusAssetRequest.setBasicUid(orderEntity.getBasicUid());
            orderRefundMinusAssetRequest.setClientUid(orderEntity.getClientUid());
            if (Objects.isNull(orderEntity.getFxRate())){
                orderEntity.setFxRate(BigDecimal.ONE);
            }
            BigDecimal applyAmount = orderEntity.getFxRate().multiply(new BigDecimal(orderRefundEntity.getApplyAmount()));
            orderRefundMinusAssetRequest.setAmount(applyAmount.longValue());
            orderRefundMinusAssetRequest.setOrderRefundNature(orderRefundEntity.getRefundNature());
            orderRefundMinusAssetRequest.setOrderType(orderEntity.getOrderType());

            heUserIntegralRecordV2Service.orderRefundMinusAsset(orderRefundMinusAssetRequest);
        }
    }

}

package com.stbella.order.server.context.component.cancel.exec;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.stbella.asset.api.enums.TradeType;
import com.stbella.asset.api.req.trade.UserTradeReq;
import com.stbella.core.base.Operator;
import com.stbella.order.common.enums.core.OrderRefundGoodsStatusEnum;
import com.stbella.order.common.enums.month.AheadOutRoomEnum;
import com.stbella.order.common.enums.month.RefundRecordPayStatusEnum;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.repository.AheadOutRoomRepository;
import com.stbella.order.domain.repository.HeOrderRefundGoodsRepository;
import com.stbella.order.domain.repository.OrderRefundRepository;
import com.stbella.order.infrastructure.gateway.asset.AssetRemoteService;
import com.stbella.order.server.order.month.req.AheadOutRoomQuery;
import com.stbella.store.common.enums.core.GoodsTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
@SnowballComponent(name = "客诉工单取消审批-退款信息处理")
public class ComplaintCancelRefundExecProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderRefundRepository orderRefundRepository;

    @Resource
    private AheadOutRoomRepository aheadOutRoomRepository;

    @Resource
    private AssetRemoteService assetRemoteService;

    @Resource
    private HeOrderRefundGoodsRepository orderRefundGoodsRepository;

    private static void accept(HeOrderRefundEntity heOrderRefundEntity) {
        heOrderRefundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_CANCEL_20.getCode());
    }

    private static void accept(HeOrderRefundGoodsEntity heOrderRefundGoodsEntity) {
        heOrderRefundGoodsEntity.setStatus(OrderRefundGoodsStatusEnum.FAIL.code());
    }

    @Override
    public void run(FlowContext bizContext) {

        log.info("客诉工单取消审批-退款信息处理");

        HeCustomerComplaintsEntity heCustomerComplaintsEntity = bizContext.getAttribute(HeCustomerComplaintsEntity.class);
        HeOrderEntity heOrderEntity = bizContext.getAttribute(HeOrderEntity.class);
        List<HeOrderRefundEntity> heOrderRefundList = bizContext.getListAttribute(HeOrderRefundEntity.class);

        heOrderRefundList.forEach(ComplaintCancelRefundExecProcessor::accept);
        orderRefundRepository.batchUpdateById(heOrderRefundList);

        boolean newOrder = heOrderEntity.isNewOrder();
        if (Objects.nonNull(heCustomerComplaintsEntity.getRefundOrderId())){
            heOrderRefundList.stream().filter(item -> heCustomerComplaintsEntity.getRefundOrderId().equals(item.getId().longValue())).findFirst().ifPresent(heOrderRefundEntity -> {
                this.updateAheadOutRoomAmount(heOrderRefundEntity, newOrder);
                BigDecimal fxRate = Optional.ofNullable(heOrderEntity.getFxRate()).orElse(BigDecimal.ONE);
                Long amountJf =fxRate.multiply(new BigDecimal(heOrderRefundEntity.getApplyAmount())).divide(new BigDecimal(1000), 0, RoundingMode.HALF_UP).longValue();
                List<Long> execTrade = assetRemoteService.execTrade(createTradeRequest(TradeType.AVAILABLE_ORDER_PAYED, heOrderEntity, heOrderRefundEntity, amountJf,
                        "JF" + heOrderEntity.getOrderSn() + heOrderRefundEntity.getId()));
                log.info("积分退回执行结果: {}", execTrade);
                if (newOrder){
                    List<HeOrderRefundGoodsEntity> heOrderRefundGoodsEntityList = bizContext.getListAttribute(HeOrderRefundGoodsEntity.class);
                    if (CollectionUtils.isNotEmpty(heOrderRefundGoodsEntityList)){

                        heOrderRefundGoodsEntityList.forEach(ComplaintCancelRefundExecProcessor::accept);
                        orderRefundGoodsRepository.updateList(heOrderRefundGoodsEntityList);
                        List<HeOrderRefundGoodsEntity> heOrderRefundGoodsCkjList = heOrderRefundGoodsEntityList.stream().filter(item -> GoodsTypeEnum.Production_Coin.code().equals(item.getGoodsType())).collect(Collectors.toList());
                        for (HeOrderRefundGoodsEntity heOrderRefundGoodsEntity : heOrderRefundGoodsCkjList){
                            if (heOrderRefundGoodsEntity.getRefundNum()<=0){
                                continue;
                            }

                            Long amountCkj =fxRate.multiply(new BigDecimal(heOrderRefundGoodsEntity.getRefundAmount())).divide(BigDecimal.ONE, 0, RoundingMode.HALF_UP).longValue();
                            List<Long> execTradeCkj = assetRemoteService.execTrade(createTradeRequest(TradeType.AVAILABLE_CKJ_ORDER_UPDATE_ADD, heOrderEntity, heOrderRefundEntity, amountCkj,
                                    "CKJ" + heOrderEntity.getOrderSn() + heOrderRefundGoodsEntity.getId()));
                            log.info("产康金退回执行结果: {}", execTradeCkj);
                        }
                    }
                }
            });
        }
    }

    private UserTradeReq createTradeRequest(TradeType tradeType, HeOrderEntity orderEntity, HeOrderRefundEntity heOrderRefundEntity, Long amount, String uniqueId) {

        UserTradeReq tradeReq = new UserTradeReq();
        tradeReq.setUniqueId(uniqueId);
        Operator operator = new Operator();
        operator.setOperatorName("system");
        tradeReq.setOperator(operator);
        tradeReq.setAssetType(tradeType.getAssetType());
        tradeReq.setTradeType(tradeType.getCode());
        tradeReq.setTitle("客户取消订单退款申请");
        tradeReq.setRemark("支付订单 " + orderEntity.getOrderSn());
        tradeReq.setUserId(orderEntity.getBasicUid().toString());
        tradeReq.setAmount(amount);
        tradeReq.setOrderNo(orderEntity.getOrderSn());
        tradeReq.setSourceBizId(heOrderRefundEntity.getType() == 2 ? String.valueOf(heOrderRefundEntity.getProjectId()) : heOrderRefundEntity.getIncomeSn());
        return tradeReq;
    }

    private void updateAheadOutRoomAmount(HeOrderRefundEntity heOrderRefundEntity, boolean newOrder) {

        if (newOrder){
            log.info("新订单,不更新预退房记录");
            return;
        }
        log.info("更新预退房记录,订单ID={},退款ID={}", heOrderRefundEntity.getOrderId(), heOrderRefundEntity.getId());
        AheadOutRoomQuery query = new AheadOutRoomQuery();
        query.setOrderId(heOrderRefundEntity.getOrderId());
        query.setState(AheadOutRoomEnum.STATE_DISENABLE.code());
        AheadOutRoomEntity aheadOutRoomEntity = aheadOutRoomRepository.queryByOrderId(query);
        if (ObjectUtil.isNotEmpty(aheadOutRoomEntity) && aheadOutRoomEntity.getAgreeAt().before(DateUtil.date(heOrderRefundEntity.getCreatedAt() * 1000))) {
            aheadOutRoomEntity.setRemainingRefundableAmount(aheadOutRoomEntity.getRemainingRefundableAmount() + heOrderRefundEntity.getApplyAmount());
            aheadOutRoomRepository.updateOutRoom(aheadOutRoomEntity);
        }
    }
}

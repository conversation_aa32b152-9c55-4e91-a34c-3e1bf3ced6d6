package com.stbella.order.server.order.month.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stbella.care.server.care.vo.room.RoomStateQueryDetailInfoVO;
import com.stbella.core.result.Result;
import com.stbella.order.server.manager.TabClientManager;
import com.stbella.order.server.order.month.req.QueryOrderPageReq;
import com.stbella.order.server.order.month.req.platform.PlatformOrderPageRequest;
import com.stbella.order.server.order.month.req.platform.PlatformOrderRequest;
import com.stbella.order.server.order.month.res.*;
import com.stbella.order.server.order.month.service.MonthOrderAdminQueryService;
import com.stbella.order.server.order.month.service.PlatformOrderQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * 月子订单服务 后台查询服务实现类
 */
@Service
@Slf4j
@DubboService
public class PlatformOrderQueryServiceImpl implements PlatformOrderQueryService {

    @Resource
    private MonthOrderAdminQueryService monthOrderAdminQueryService;
    @Resource
    private TabClientManager tabClientManager;
    @Override
    public Result<Page<QueryOrderPageVO>> queryPageForArea(PlatformOrderPageRequest platformOrderPageRequest) {

        //解析appid和appsecret
        // TODO: 2024/2/28  等平台化在具体的处理,本期只注重逻辑
        Integer appid = platformOrderPageRequest.getAppid();
        String appsecret = platformOrderPageRequest.getAppsecret();
        //模拟验证是香港的
        if (appid.equals(100002) && appsecret.equals("100002-hk-frct-1452")) {
            QueryOrderPageReq queryOrderPageReq = platformOrderPageRequest.getQueryOrderPageReq();
            if (ObjectUtil.isEmpty(queryOrderPageReq)) {
                queryOrderPageReq = new QueryOrderPageReq();
            }
//            queryOrderPageReq.setStoreIds(Arrays.asList(1044, 1066));
            queryOrderPageReq.setAllOrderType(true);
            Page<QueryOrderPageVO> queryOrderPageVOPage = monthOrderAdminQueryService.queryPage(queryOrderPageReq);
            return Result.success(queryOrderPageVOPage);
        }

        return Result.failed("appid和appsecret不存在或无权限");
    }

    @Override
    public Result<PlatformOrdeInfoVO> queryOrderBasicInfoByOrderIdForArea(PlatformOrderRequest platformOrderRequest) {
        //解析appid和appsecret
        // TODO: 2024/2/28  等平台化在具体的处理,本期只注重逻辑
        Integer appid = platformOrderRequest.getAppid();
        String appsecret = platformOrderRequest.getAppsecret();
        //模拟验证是香港的
        if (appid.equals(100002) && appsecret.equals("100002-hk-frct-1452")) {
            Integer orderId = platformOrderRequest.getOrderId();

            //订单信息
            Result<STMOrderInfoVO> stmOrderInfoVOResult = monthOrderAdminQueryService.queryByOrderIdOrderInfo(orderId);
            //基本信息
            Result<STMOOrderBasicInfoVO> stmoOrderBasicInfoVOResult = monthOrderAdminQueryService.queryOrderBasicInfoByOrderNo(orderId);
            //财务信息
            Result<STMOrderInfoFinancialVO> stmOrderInfoFinancialVOResult = monthOrderAdminQueryService.queryByOrderIdInancialInformation(orderId);
            //额外礼赠
            Result<STMOrderInfoGiftVO> stmOrderInfoGiftVOResult = monthOrderAdminQueryService.queryByOrderIdGift(orderId);
            //分娩喜报
            Result<STMOrderInfoLaborVO> stmOrderInfoLaborVOResult = monthOrderAdminQueryService.queryByOrderIdGoodNewsChildbirth(orderId);
            PlatformOrdeInfoVO platformOrdeInfoVO = new PlatformOrdeInfoVO();
            //特殊需求,对方需要客户实际入住时间
            STMOOrderBasicInfoVO standardOrdeInfoVO = stmoOrderBasicInfoVOResult.getData();
            if (ObjectUtil.isNotEmpty(standardOrdeInfoVO)) {
                STMOOrderBasicCustomerInfoVO orderBasicCustomerInfoVO = standardOrdeInfoVO.getOrderBasicCustomerInfoVO();
                if (ObjectUtil.isNotEmpty(orderBasicCustomerInfoVO)) {
                    STMOOrderBasicGeneraInfoVO orderBasicGeneraInfoVO = standardOrdeInfoVO.getOrderBasicGeneraInfoVO();
                    if (ObjectUtil.isNotEmpty(orderBasicGeneraInfoVO)) {
                        RoomStateQueryDetailInfoVO roomStateQueryDetailInfoVO = tabClientManager.queryClientRoomDetail(orderBasicGeneraInfoVO.getOrderNo());
                        if (ObjectUtil.isNotEmpty(roomStateQueryDetailInfoVO)) {
                            String roomCheckInDate = roomStateQueryDetailInfoVO.getCheckInDate();
                            if (ObjectUtil.isNotEmpty(roomCheckInDate)) {
                                orderBasicCustomerInfoVO.setRoomCheckInDate(DateUtil.parseDate(roomCheckInDate));
                            }
                        }
                    }

                }

            }
            platformOrdeInfoVO.setStmoOrderBasicInfoVO(standardOrdeInfoVO);
            platformOrdeInfoVO.setStmOrderInfoVO(stmOrderInfoVOResult.getData());
            platformOrdeInfoVO.setStmOrderInfoFinancialVO(stmOrderInfoFinancialVOResult.getData());
            platformOrdeInfoVO.setStmOrderInfoGiftVO(stmOrderInfoGiftVOResult.getData());
            platformOrdeInfoVO.setStmOrderInfoLaborVO(stmOrderInfoLaborVOResult.getData());
            return Result.success(platformOrdeInfoVO);
        }
        return Result.failed("appid和appsecret不存在或无权限");
    }

}

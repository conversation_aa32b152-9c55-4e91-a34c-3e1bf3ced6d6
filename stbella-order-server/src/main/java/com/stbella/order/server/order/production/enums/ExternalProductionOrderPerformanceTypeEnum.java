package com.stbella.order.server.order.production.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 产康馆外订单业绩操作类型枚举
 * </p>
 *
 * <AUTHOR> @since 2024-08-15
 */
@Getter
@AllArgsConstructor
public enum ExternalProductionOrderPerformanceTypeEnum {

    /**
     * 计入业绩
     */
    INCLUDE(1, "计入业绩"),

    /**
     * 剔除业绩
     */
    EXCLUDE(2, "剔除业绩");

    /**
     * 类型码
     */
    private final Integer code;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 根据类型码获取类型名称
     *
     * @param code 类型码
     * @return 类型名称
     */
    public static String getNameByCode(Integer code) {
        for (ExternalProductionOrderPerformanceTypeEnum type : ExternalProductionOrderPerformanceTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type.getName();
            }
        }
        return "";
    }
}

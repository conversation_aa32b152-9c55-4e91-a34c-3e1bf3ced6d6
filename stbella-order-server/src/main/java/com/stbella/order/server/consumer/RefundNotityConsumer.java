package com.stbella.order.server.consumer;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.server.order.month.enums.CtsPayTypeEnum;
import com.stbella.order.server.order.month.enums.PayRecordEnum;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.order.server.strategy.refund.RefundStrategyFactory;
import com.stbella.order.server.utils.AccountTypeUtil;
import com.stbella.pay.server.entity.mq.RefundNotifyMq;
import com.stbella.pay.server.paymq.service.PayMqService;
import com.stbella.pulsar.consumer.PulsarConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.pulsar.client.api.Message;
import org.apache.pulsar.client.api.SubscriptionType;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;

@Component
@Slf4j
public class RefundNotityConsumer {

    @DubboReference
    private PayMqService payMqService;
    @Resource
    private RefundStrategyFactory refundStrategyFactory;

    /**
     * 退款成功mq消费
     */
    @PulsarConsumerListener(destination = "persistent://pulsar-44k4paxzz5vg/nameSpace/refund_notify", subscriptionName = "refund_notify", subscriptionType = SubscriptionType.Shared)
    @Transactional(rollbackFor = Exception.class)
    public void getMsg(Message message) {
        String messageData = new String(message.getData());
        String messageId = String.valueOf(message.getMessageId());
        try {
            log.info("退款回调消费者消费：messageId={},messageData={}", messageId, messageData);

            RefundNotifyMq refundNotifyMq = JSONUtil.toBean(messageData, RefundNotifyMq.class);
            if (refundNotifyMq.getRefundType().equals(CtsPayTypeEnum.WX.getCode())) {
                refundNotifyMq.setRefundAmount(new BigDecimal(RMBUtils.changeF2Y(refundNotifyMq.getRefundAmount().toString())));
            }
            //如果是POS机 因为和支付宝微信返回实例不一样需要特殊处理
            PayRecordEnum accountTypeByLocalTransactionalNo = AccountTypeUtil.getAccountTypeByLocalTransactionalNo(ObjectUtil.isEmpty(refundNotifyMq.getOutTradeNo()) ? refundNotifyMq.getOutRefundNo() : refundNotifyMq.getOutTradeNo());
            //根据枚举选择各自的退款策略
            refundStrategyFactory.executeRefundStrategy(accountTypeByLocalTransactionalNo, refundNotifyMq);

            //更新
            payMqService.updateStatusByMessageId(messageId);
            log.info("退款回调通知客户成功：messageId={},refundNotifyMq={}", messageId, refundNotifyMq);

        } catch (Exception e) {
            log.error("退款回调通知客户异常：messageId={},messageData={}", messageId, messageData);
            log.error("退款回调通知客户异常：messageId=" + messageId, e);
            throw new BusinessException(ResultEnum.MESSAGE_CONSUMPTION_ERROR, e.getMessage());
        }
    }

}

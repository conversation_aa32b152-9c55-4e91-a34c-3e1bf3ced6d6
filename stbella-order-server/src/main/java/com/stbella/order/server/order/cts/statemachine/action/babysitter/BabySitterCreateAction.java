package com.stbella.order.server.order.cts.statemachine.action.babysitter;

import com.stbella.order.server.order.cts.constant.CtsOrderConstant;
import com.stbella.order.server.order.cts.enums.OrderStatusStateMachineStatusEnum;
import com.stbella.order.server.order.cts.request.order.CustomerOrderCreateBaseRequest;
import com.stbella.order.server.order.cts.statemachine.OrderContext;
import com.stbella.order.server.order.cts.statemachine.OrderStateEvent;
import com.stbella.order.server.order.cts.statemachine.action.common.CommonAction;
import com.stbella.order.server.order.month.enums.OrderTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.action.Action;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 育婴师订单创建
 *
 * <AUTHOR>
 * @date 2022-03-10 11:41
 * @sine 1.0.0
 */
@Slf4j
@Component
public class BabySitterCreateAction implements Action<OrderStatusStateMachineStatusEnum, OrderStateEvent> {

    @Resource
    private CommonAction commonAction;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void execute(StateContext<OrderStatusStateMachineStatusEnum, OrderStateEvent> stateContext) {
        log.info("执行雇主-育婴师订单创建" + stateContext.toString());
        OrderContext context = (OrderContext) stateContext.getMessageHeader(OrderContext.HEADER_KEY);

        // 订单创建
        final String orderNo = commonAction.createCustomerOrder((CustomerOrderCreateBaseRequest) context.getModel(), OrderTypeEnum.BABY_SITTER.getCode());

        // 填充orderNo
        stateContext.getStateMachine().getExtendedState().getVariables().put(CtsOrderConstant.MACHINE_VARIABLES_ORDER_NO, orderNo);
        log.info("执行雇主-育婴师订单创建成功 orderNo:{}", orderNo);
    }
}

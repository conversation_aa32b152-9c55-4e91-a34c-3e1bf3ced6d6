package com.stbella.order.server.strategy.approval;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.stbella.asset.api.enums.AssetType;
import com.stbella.asset.api.enums.TradeType;
import com.stbella.asset.api.req.trade.UserTradeReq;
import com.stbella.base.server.sms.enums.SmsTemplateV2Enum;
import com.stbella.core.base.Operator;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.month.server.request.RefundApprovalRequest;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.month.AheadOutRoomEnum;
import com.stbella.order.common.enums.month.RefundRecordPayStatusEnum;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.repository.*;
import com.stbella.order.infrastructure.gateway.asset.AssetRemoteService;
import com.stbella.order.server.async.AsyncOrder;
import com.stbella.order.server.manager.SmsManager;
import com.stbella.order.server.order.month.enums.CustomerComplaintsStatusEnum;
import com.stbella.order.server.order.month.req.AheadOutRoomQuery;
import com.stbella.order.server.order.month.request.approval.ApprovalStatusInfo;
import com.stbella.order.server.order.month.service.HeOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 产康金tuo退款审批策略
 * Created by jaydeep.gc on 06/07/21.
 */
@Slf4j
@Component
public class ProductionRefundApprovalStrategy implements ApprovalStrategy {

    @Resource
    private SmsManager smsManager;

    @Resource
    private AsyncOrder asyncOrder;

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private OrderRefundRepository orderRefundRepository;

    @Resource
    private AheadOutRoomRepository aheadOutRoomRepository;

    @Resource
    private AssetRemoteService assetRemoteService;

    @Resource
    private ProductionAmountPayRepository productionAmountPayRepository;

    @Resource
    private IncomeRecordRepository incomeRecordRepository;

    @Resource
    private ProductionAmountRefundRepository productionAmountRefundRepository;

    @Resource
    private HeOrderService heOrderService;

    @Resource
    private HeCustomerComplaintsRepository customerComplaintsRepository;

    @Override
    public void handleApproval(OrderApproveRecordTypeEnum type, String params, String messageId, ApprovalStatusInfo approvalStatusInfo, String processId) {

        log.info("开始处理退款审批,params={}, messageId:{}, processId:{}", params, messageId, processId);
        RefundApprovalRequest refundApprovalRequest = parseRefundApprovalRequest(params);
        HeOrderRefundEntity heOrderRefundEntity = validateRefundEntity(refundApprovalRequest.getOrderRefundId());
        HeOrderEntity heOrderEntity = validateOrderEntity(heOrderRefundEntity.getOrderId());
        HeUserProductionAmountPayLogEntity payLogEntity = validatePayLog(refundApprovalRequest.getProductionPayId());
        HeUserProductionAmountRefundLogEntity productionRefundEntity = validateRefundLog(refundApprovalRequest.getProductionRefundId());

        processApproval(approvalStatusInfo, refundApprovalRequest, heOrderRefundEntity, heOrderEntity, payLogEntity, productionRefundEntity);
    }

    private RefundApprovalRequest parseRefundApprovalRequest(String params) {
        RefundApprovalRequest request = JSONObject.parseObject(params, RefundApprovalRequest.class);
        if (Objects.isNull(request)) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "退款审批完成,找不到对应的参数,params=" + params);
        }
        return request;
    }

    private HeOrderRefundEntity validateRefundEntity(Integer orderRefundId) {
        HeOrderRefundEntity entity = orderRefundRepository.getOneById(orderRefundId);
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "找不到对应的退款记录,退款ID=" + orderRefundId);
        }
        return entity;
    }

    private HeOrderEntity validateOrderEntity(Integer orderId) {
        HeOrderEntity entity = orderRepository.getByOrderId(orderId);
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "找不到对应的订单记录,orderId=" + orderId);
        }
        return entity;
    }

    private HeUserProductionAmountPayLogEntity validatePayLog(Integer productionPayId) {
        HeUserProductionAmountPayLogEntity entity = productionAmountPayRepository.getOneById(productionPayId);
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "找不到对应的产康金支付记录,productionPayId=" + productionPayId);
        }
        return entity;
    }

    private HeUserProductionAmountRefundLogEntity validateRefundLog(Integer productionRefundId) {
        HeUserProductionAmountRefundLogEntity entity = productionAmountRefundRepository.queryById(productionRefundId);
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "找不到对应的产康金退款记录,productionRefundId=" + productionRefundId);
        }
        return entity;
    }

    private void processApproval(ApprovalStatusInfo approvalStatusInfo, RefundApprovalRequest request,
                                 HeOrderRefundEntity refundEntity, HeOrderEntity orderEntity,
                                 HeUserProductionAmountPayLogEntity payLog,
                                 HeUserProductionAmountRefundLogEntity productionRefundEntity) {

        log.info("开始处理退款审批,退款ID={},订单ID={},支付记录ID={},退款记录ID={}", refundEntity.getId(), orderEntity.getOrderId(), payLog.getId(), productionRefundEntity.getId());
        String originatorPhone = request.getPhone();
        Integer refundAmount = refundEntity.getApplyAmount();
        payLog.setFreezeAmount(payLog.getFreezeAmount() - refundAmount);

        if (approvalStatusInfo.isTerminate()) {
            handleTermination(refundEntity, productionRefundEntity);
        }

        if (approvalStatusInfo.isFinish() && approvalStatusInfo.isRefuse()) {
            handleRefusal(refundEntity, productionRefundEntity, originatorPhone, request);
        }

        boolean isRefund = processRefund(approvalStatusInfo, refundEntity, productionRefundEntity, refundAmount, originatorPhone, request);

        if (isRefund) {
            this.handleRefund(request, refundEntity, orderEntity, payLog, refundAmount, originatorPhone);
            this.opComplaint(refundEntity.getId());
        } else {
            this.updateAheadOutRoomAmount(refundEntity);
        }
        updateRefundEntities(orderEntity, refundEntity, refundAmount, payLog, productionRefundEntity, isRefund);

    }

    private void opComplaint(Integer id) {
        //处理客诉
        HeCustomerComplaintsEntity customerComplaintsEntity = customerComplaintsRepository.selectByOrderRefundId(id);
        if (ObjectUtil.isNotEmpty(customerComplaintsEntity)) {
            customerComplaintsEntity.setComplaintStatus(CustomerComplaintsStatusEnum.SUCCESS.getCode());
            customerComplaintsEntity.setResolveFinish(new Date());
            customerComplaintsRepository.update(customerComplaintsEntity);
        }
    }

    private void handleRefund(RefundApprovalRequest request, HeOrderRefundEntity refundEntity, HeOrderEntity orderEntity,
                              HeUserProductionAmountPayLogEntity payLog, Integer refundAmount, String originatorPhone) {
        log.info("开始处理产康金退款,退款ID={},订单ID={},产康金支付记录ID={},产康金退款记录ID={}", refundEntity.getId(), orderEntity.getOrderId(), payLog.getId(), request.getProductionRefundId());
        UserTradeReq tradeReq = createTradeRequest(request, orderEntity, payLog, refundAmount);
        List<Long> refundResult = assetRemoteService.execTrade(tradeReq);

        refundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode());
        refundEntity.setPaymentResult(JSONUtil.toJsonStr(refundResult));
        payLog.setRefundAmount(Objects.isNull(payLog.getRefundAmount()) ? 0 : payLog.getRefundAmount() + refundAmount);

//        if (shouldCalculateAchievement(orderEntity)) {
//            this.calculateRefundAchievement(refundEntity, orderEntity);
//        }

        if (isMonthOrder(orderEntity)) {
            orderEntity.setRealAmount(orderEntity.getRealAmount() - refundAmount);
        }
        try {
            log.info("产康金退款完成发送短信通知");
            smsManager.sendMessage(originatorPhone, SmsTemplateV2Enum.HELPER_REFUND_ORIGINAL_BACK_USER, new String[]{request.getCustomerName(), request.getRefundAmount().toString()});
            smsManager.sendMessage(request.getCustomerPhone(), SmsTemplateV2Enum.HELPER_REFUND_ORIGINAL_BACK_CLIENT, new String[]{orderEntity.getOrderSn(), request.getRefundAmount().toString()});
        } catch (Exception e) {
            log.error("退款成功发送给客户异常：{}，手机号：{}，内容：{}", e.getMessage(), request.getCustomerName(), new String[]{orderEntity.getOrderId().toString(), request.getRefundAmount().toString()});
        }
    }

    private UserTradeReq createTradeRequest(RefundApprovalRequest request, HeOrderEntity orderEntity,
                                            HeUserProductionAmountPayLogEntity payLog, Integer refundAmount) {
        UserTradeReq tradeReq = new UserTradeReq();
        tradeReq.setUniqueId(request.getProductionRefundId().toString() + IdWorker.getId());

        Operator operator = new Operator();
        operator.setOperatorGuid(request.getOperatorGuid());
        operator.setOperatorName(request.getOperatorName());
        operator.setOperatorPhone(request.getOperatorPhone());

        tradeReq.setOperator(operator);
        tradeReq.setAssetType(AssetType.AVAILABLE_BALANCE.getCode());
        tradeReq.setTradeType(TradeType.AVAILABLE_CKJ_ORDER_RETURN_ADD.getCode());
        tradeReq.setTitle("订单退款失效归还积分");
        tradeReq.setRemark("支付订单 " + orderEntity.getOrderSn());
        tradeReq.setUserId(orderEntity.getBasicUid().toString());
        tradeReq.setAmount(refundAmount.longValue());
        tradeReq.setOrderNo(orderEntity.getOrderSn());
        tradeReq.setSourceBizId(payLog.getId().toString());

        return tradeReq;
    }

    private boolean shouldCalculateAchievement(HeOrderEntity orderEntity) {
        return ObjectUtil.isNotEmpty(orderEntity.getPercentFirstTime()) && !orderEntity.getPercentFirstTime().equals(0);
    }


    private boolean isMonthOrder(HeOrderEntity orderEntity) {
        return OmniOrderTypeEnum.MONTH_ORDER.getCode().equals(orderEntity.getOrderType());
    }

    private boolean processRefund(ApprovalStatusInfo approvalStatusInfo, HeOrderRefundEntity refundEntity, HeUserProductionAmountRefundLogEntity productionRefundEntity,
                                  Integer refundAmount, String originatorPhone, RefundApprovalRequest request) {
        boolean isRefund = false;

        if (approvalStatusInfo.isFinish() && approvalStatusInfo.isAgree()) {
            isRefund = true;
            this.finalizeRefund(refundEntity, productionRefundEntity, refundAmount);
            this.sendRefundNotification(originatorPhone, request);
        }

        return isRefund;
    }

    private void finalizeRefund(HeOrderRefundEntity refundEntity, HeUserProductionAmountRefundLogEntity productionRefundEntity, Integer refundAmount) {
        long currentTime = System.currentTimeMillis() / 1000;
        refundEntity.setAgreeAt(currentTime);
        refundEntity.setFinishAt(currentTime);
        refundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_3.getCode());

        productionRefundEntity.setAgreeAt(currentTime);
        productionRefundEntity.setFinishAt(currentTime);
        productionRefundEntity.setActualAmount(refundAmount);
        productionRefundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode());
    }

    private void sendRefundNotification(String originatorPhone, RefundApprovalRequest request) {
        try {
            log.info("产康金审批通过发送短信通知（原路退回）:{},refundType:{}", originatorPhone, request.getRefundType());
            smsManager.sendMessage(originatorPhone, null, null, SmsTemplateV2Enum.HELPER_REFUND_ORIGINAL_BACK_USER,
                    new String[]{request.getCustomerName(), request.getRefundAmount().toString()});
        } catch (Exception e) {
            log.error("发送短信通知异常：{}，手机号：{}，内容：{}", e.getMessage(), originatorPhone,
                    new String[]{request.getCustomerName(), request.getRefundAmount().toString()});
        }
    }

    private void handleTermination(HeOrderRefundEntity refundEntity, HeUserProductionAmountRefundLogEntity productionRefundEntity) {
        log.info("产康金退款审批终止,订单ID={}, 产康金支付记录productionPayId={}", refundEntity.getOrderId(), refundEntity.getId());
        refundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_2.getCode());
        productionRefundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_2.getCode());
    }

    private void handleRefusal(HeOrderRefundEntity refundEntity, HeUserProductionAmountRefundLogEntity productionRefundEntity, String originatorPhone, RefundApprovalRequest request) {

        refundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_2.getCode());
        productionRefundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_2.getCode());

        try {
            log.info("审批拒绝发送短信通知（原路退回）:{},refundType:{}", originatorPhone, refundEntity.getRefundType());
            SmsTemplateV2Enum smsTemplate = OmniPayTypeEnum.ONLINE_POS.getCode().equals(refundEntity.getRefundType())
                    ? SmsTemplateV2Enum.HELPER_REFUND_POS_APPLY_REFUSED_USER
                    : SmsTemplateV2Enum.HELPER_REFUND_APPLY_REFUSE_USER;
            smsManager.sendMessage(originatorPhone, null, null, smsTemplate,
                    new String[]{request.getCustomerName()});
        } catch (Exception e) {
            log.error("审批拒绝发送短信通知（原路退回）异常：{}，手机号：{}，内容：{}", e.getMessage(), originatorPhone,
                    new String[]{request.getCustomerName()});
        }
    }

    private void updateAheadOutRoomAmount(HeOrderRefundEntity heOrderRefundEntity) {

        log.info("更新预退房记录,订单ID={},退款ID={}", heOrderRefundEntity.getOrderId(), heOrderRefundEntity.getId());
        AheadOutRoomQuery query = new AheadOutRoomQuery();
        query.setOrderId(heOrderRefundEntity.getOrderId());
        query.setState(AheadOutRoomEnum.STATE_DISENABLE.code());
        AheadOutRoomEntity aheadOutRoomEntity = aheadOutRoomRepository.queryByOrderId(query);
        if (ObjectUtil.isNotEmpty(aheadOutRoomEntity) && aheadOutRoomEntity.getAgreeAt().before(DateUtil.date(heOrderRefundEntity.getCreatedAt() * 1000))) {
            aheadOutRoomEntity.setRemainingRefundableAmount(aheadOutRoomEntity.getRemainingRefundableAmount() + heOrderRefundEntity.getApplyAmount());
            aheadOutRoomRepository.updateOutRoom(aheadOutRoomEntity);
        }
    }

    private void updateRefundEntities(HeOrderEntity orderEntity, HeOrderRefundEntity refundEntity, Integer refundAmount, HeUserProductionAmountPayLogEntity payLog, HeUserProductionAmountRefundLogEntity productionRefundEntity, boolean isRefund) {

        fillRefundStatus(orderEntity, refundAmount, isRefund);
        orderRepository.updateOrderMonthByOrderId(orderEntity);
        orderRefundRepository.updateOneById(refundEntity);
        productionAmountPayRepository.updateRecord(payLog);
        productionAmountRefundRepository.updateRecord(productionRefundEntity);
        log.info("退款审批（原路退回）-积分处理,订单ID={},退款ID={}", refundEntity.getOrderId(), refundEntity.getId());
        asyncOrder.refundDeductIntegral(orderEntity, refundEntity);
    }


    @Override
    public OrderApproveRecordTypeEnum getOrderApproveRecordTypeEnum() {
        return OrderApproveRecordTypeEnum.ORDER_PRODUCTION_REFUND_APPROVE;
    }

    private void fillRefundStatus(HeOrderEntity heOrderEntity, Integer refundAmount, boolean isRefund) {

        heOrderEntity.setRefundStatus(heOrderService.getRefundStatusByActualAmount(heOrderEntity.getOrderId(), refundAmount, isRefund, true));
    }

    private Integer refundAmount(Integer orderId) {

        Integer refundAmount = 0;
        List<HeIncomeRecordEntity> successfulRecordListByOrderId = incomeRecordRepository.getSuccessfulRecordListByOrderId(orderId);
        if (CollectionUtils.isNotEmpty(successfulRecordListByOrderId)) {
            int alreadyRefundAmount = successfulRecordListByOrderId.stream().mapToInt(HeIncomeRecordEntity::getAlreadyRefundAmount).sum();
            refundAmount = refundAmount + alreadyRefundAmount;
        }
        List<HeUserProductionAmountPayLogEntity> heUserProductionAmountPayLogEntities = productionAmountPayRepository.querySuccessRecordListByOrderId(orderId);
        if (CollectionUtils.isNotEmpty(heUserProductionAmountPayLogEntities)) {
            int freezeAmount = heUserProductionAmountPayLogEntities.stream().mapToInt(HeUserProductionAmountPayLogEntity::getRefundAmount).sum();
            refundAmount = refundAmount + freezeAmount;
        }
        return refundAmount;
    }
}

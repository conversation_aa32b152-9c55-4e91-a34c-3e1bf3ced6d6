package com.stbella.order.server.context.component.spi.impl.pay;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.month.PayChannelTypeEnum;
import com.stbella.order.common.enums.month.PayStatusV2Enum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.server.context.component.spi.PaySpi;
import com.stbella.order.server.manager.PayManager;
import com.stbella.order.server.order.month.enums.PayAmountEnum;
import com.stbella.pay.server.cmbpay.request.AddOrderRequest;
import com.stbella.pay.server.cmbpay.vo.AddOrderRespVO;
import com.stbella.pay.server.enums.CmbPayTypeEnum;
import com.stbella.pay.server.enums.CmbPosBizTypeEnum;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.annotation.FuncPointSpiImpl;
import top.primecare.snowball.extpoint.definition.FuncSpiConfig;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-05-30  14:01
 * @Description: 通联pos支付扩展点
 */
@Component
@FuncPointSpiImpl(name = "通联pos支付扩展点")
public class CmbPosSpiImpl implements PaySpi {

    @Override
    public boolean condition(HeIncomeRecordEntity param) {

        List<Integer> goodPayTypeList = Lists.newArrayList(OmniPayTypeEnum.ONLINE_POS.getCode());

        if (CompareUtil.integerEqual(param.getChannelType() , PayChannelTypeEnum.CMB.getCode()) && goodPayTypeList.contains(param.getPayType())){
            return true;
        }
        return false;
    }

    @Override
    public JSONObject invoke(HeIncomeRecordEntity param) {

        Map<String, Object> map = new HashMap<>();
        map.put("incomeSn", param.getIncomeSn());
        //业务类型;0-母婴(PHP),1-母婴(JAVA),2-到家
        map.put("bizType", CmbPosBizTypeEnum.JAVA_MONTH.getCode());

        JSONObject result = JSONUtil.parseObj(map);
        return result;
    }


    @Override
    public FuncSpiConfig config() {
        //默认优先级高
        return new FuncSpiConfig(true, Integer.MIN_VALUE);
    }
}

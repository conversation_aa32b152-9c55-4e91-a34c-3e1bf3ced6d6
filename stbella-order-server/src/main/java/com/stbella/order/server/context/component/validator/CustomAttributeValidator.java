package com.stbella.order.server.context.component.validator;

import cn.hutool.core.collection.CollectionUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.order.OrderAdditionalKeyEnum;
import com.stbella.platform.order.api.req.CreateOrderReq;
import com.stbella.platform.order.api.req.CustomAttribute;
import com.stbella.platform.order.api.req.ExtraInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
@SnowballComponent(name = "服务信息验证", desc = "验证客户预产期，预计入住日期是否过了未知")
public class CustomAttributeValidator implements IExecutableAtom<FlowContext> {

    @Override
    public void run(FlowContext bizContext) {

        CreateOrderReq req = bizContext.getAttribute(CreateOrderReq.class);
        ExtraInfo extraInfo = req.getExtraInfo();
        if (Objects.isNull(extraInfo)){
            return;
        }
        List<CustomAttribute> fulfillExtraList = extraInfo.getFulfillExtraList();
        if (CollectionUtil.isEmpty(fulfillExtraList)){
            return;
        }
        List<String> additionalKeyList = Arrays.asList(OrderAdditionalKeyEnum.PREDICT_BORN_DATE.code(), OrderAdditionalKeyEnum.WANT_IN_DATE.code());
        CustomAttribute customAttribute = fulfillExtraList.stream().filter(item -> additionalKeyList.contains(item.getCode()) && "未知".equals(item.getShowStr())).findFirst().orElse(null);
        if (Objects.nonNull(customAttribute)){
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code()+"", String.format("请返回选择%s", customAttribute.getPropertyName()));
        }
    }
}

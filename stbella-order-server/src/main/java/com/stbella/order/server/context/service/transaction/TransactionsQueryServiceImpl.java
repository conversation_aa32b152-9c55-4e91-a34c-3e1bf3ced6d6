package com.stbella.order.server.context.service.transaction;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.customer.server.ecp.vo.ClientInfoVO;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OrderRefundStatusEnum;
import com.stbella.order.common.utils.BPCheckUtil;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.context.component.StoreCurrencyContainer;
import com.stbella.order.server.convert.PayRecordConvert;
import com.stbella.order.server.manager.TabClientManager;
import com.stbella.order.server.order.month.response.QueryIncomeProofRecordByIncomeSnVO;
import com.stbella.platform.order.api.TransactionsQueryService;
import com.stbella.platform.order.api.req.TransactionDetailQuery;
import com.stbella.platform.order.api.res.TransactionPaidAllocationDto;
import com.stbella.platform.order.api.res.TransactionWithAllocationDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-07-30  07:29
 * @Description: 交易查询服务
 */
@Slf4j
@Service
@DubboService
public class TransactionsQueryServiceImpl implements TransactionsQueryService {

    @Resource
    IncomePaidAllocationRepository allocationRepository;
    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @Resource
    private IncomeProofRecordRepository incomeProofRecordRepository;
    @Resource
    private PayRecordConvert payRecordConvert;
    @Resource
    private UserRepository userRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    TabClientManager tabClientManager;

    /**
     * 查询单个支付记录的分摊
     *
     * @param query
     * @return
     */
    @Override
    public Result<TransactionWithAllocationDto> getTransactionAllocation(TransactionDetailQuery query) {
        BPCheckUtil.checkEmptyInBean(new String[]{"transactionId"}, query, false);

        HeIncomeRecordEntity incomeRecord = incomeRecordRepository.getOneById(query.getTransactionId());
        if (Objects.isNull(incomeRecord)) {
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code()+"","支付记录不存在");
        }

        HeOrderEntity heOrderEntity = new HeOrderEntity();
        if (incomeRecord.getOrderId() > 0){
            heOrderEntity =orderRepository.getByOrderId(incomeRecord.getOrderId());
        }else {
            //老的押金的情况
            heOrderEntity.setOrderType(OmniOrderTypeEnum.DEPOSIT_ORDER.getCode());
            ClientInfoVO clientInfo = tabClientManager.getClientInfoByClientId(incomeRecord.getClientUid());
            heOrderEntity.setBasicUid(clientInfo.getBasicUid());
        }

        TransactionWithAllocationDto transaction = BeanMapper.map(incomeRecord, TransactionWithAllocationDto.class);
        transaction.setOrderType(heOrderEntity.getOrderType());
        transaction.setBasicUid(heOrderEntity.getBasicUid());
        transaction.setNewOrder(heOrderEntity.isNewOrder());

        List<IncomePaidAllocationEntity> allocationEntities = allocationRepository.queryListByIncomeIds(Lists.newArrayList(incomeRecord.getId()));
        List<TransactionPaidAllocationDto> transactionPaidAllocationDtos = BeanMapper.mapList(allocationEntities, TransactionPaidAllocationDto.class);

        transaction.setAllocationList(transactionPaidAllocationDtos);

        return Result.success(transaction);
    }

    /**
     * 查询线下支付凭证
     *
     * @param query
     * @return
     */
    @Override
    public Result<QueryIncomeProofRecordByIncomeSnVO> getOfflineProofByTransSn(TransactionDetailQuery query) {

        HeIncomeRecordEntity incomeRecord = incomeRecordRepository.getOneById(query.getTransactionId());
        HeIncomeProofRecordEntity proofRecord = incomeProofRecordRepository.getLastOneByIncomeId(incomeRecord.getId());

        String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(incomeRecord.getStoreId());
        QueryIncomeProofRecordByIncomeSnVO queryIncomeProofRecordByIncomeSnVO = payRecordConvert.heIncomeProofRecordEntity2QueryIncomeProofRecordByIncomeSnVO(proofRecord);
        queryIncomeProofRecordByIncomeSnVO.setCurrency(storeCurrencyCode);
        if (ObjectUtil.isNotEmpty(proofRecord.getAuditBasicId()) && proofRecord.getAuditBasicId() != 0) {
            UserEntity userEntity = userRepository.queryByBasicUid(proofRecord.getAuditBasicId());
            if (ObjectUtil.isNotEmpty(userEntity)) {
                queryIncomeProofRecordByIncomeSnVO.setAuditName(userEntity.getName());
            }
            queryIncomeProofRecordByIncomeSnVO.setAuditDate(new Date(proofRecord.getAuditTime().longValue() * 1000));
        }
        return Result.success(queryIncomeProofRecordByIncomeSnVO);
    }
}

package com.stbella.order.server.strategy.approval;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.month.server.request.ContractModificationApprovalRequest;
import com.stbella.order.common.enums.month.OrderDecreaseStatusEnum;
import com.stbella.order.common.utils.JsonUtil;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.order.cts.enums.DecreaseTypeEnum;
import com.stbella.order.server.order.month.enums.ApprovalDiscountStatusEnum;
import com.stbella.order.server.order.month.request.approval.ApprovalStatusInfo;
import com.stbella.platform.order.api.reduction.api.OrderReductionService;
import com.stbella.platform.order.api.reduction.req.OrderReductionApprovalDealReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * 合同修改审批
 * @date 2024/3/18 17:45
 */
@Component
@Slf4j
public class ContractModificationApprovalStrategy implements ApprovalStrategy {

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderReductionService orderReductionService;

    @Override
    public void handleApproval(OrderApproveRecordTypeEnum type, String params, String messageId, ApprovalStatusInfo approvalStatusInfo, String processId) {


        //修改订单折扣审批状态
        boolean agree = approvalStatusInfo.isAgree();
        boolean finish = approvalStatusInfo.isFinish();
        boolean refuse = approvalStatusInfo.isRefuse();
        boolean terminate = approvalStatusInfo.isTerminate();
        Integer approvalDiscountStatus = null;
        if (finish && agree) {
            approvalDiscountStatus = ApprovalDiscountStatusEnum.APPROVED.getCode();
        } else if (finish && refuse) {
            approvalDiscountStatus = ApprovalDiscountStatusEnum.APPROVAL_FAILED.ordinal();
        } else if (terminate) {
            approvalDiscountStatus = ApprovalDiscountStatusEnum.APPROVAL_FAILED.ordinal();
        }


        if (ObjectUtil.isNotNull(approvalDiscountStatus)) {
            //减免审批处理
            OrderReductionApprovalDealReq orderReductionApprovalDealReq = new OrderReductionApprovalDealReq();
            orderReductionApprovalDealReq.setLocalProcessId(processId);
            orderReductionApprovalDealReq.setType(DecreaseTypeEnum.MODIFY_APPROVAL);
            if (Objects.equals(approvalDiscountStatus, ApprovalDiscountStatusEnum.APPROVED.getCode())) {
                orderReductionApprovalDealReq.setAuthState(OrderDecreaseStatusEnum.APPROVED.getCode());
            } else {
                orderReductionApprovalDealReq.setAuthState(OrderDecreaseStatusEnum.APPROVAL_FAILED.getCode());
            }
            orderReductionService.reductionApprovalCallback(orderReductionApprovalDealReq);
        }
    }

    @Override
    public OrderApproveRecordTypeEnum getOrderApproveRecordTypeEnum() {
        return OrderApproveRecordTypeEnum.MONTHLY_STANDARD_ORDER_CONTRACT_MODIFICATION_APPLICATION;
    }
}

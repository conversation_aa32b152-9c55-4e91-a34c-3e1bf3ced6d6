package com.stbella.order.server.order.ding;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.stbella.base.server.constant.DingConstant;
import com.stbella.base.server.ding.enums.DingEventTypeEnum;
import com.stbella.base.server.ding.enums.DingResultEnum;
import com.stbella.base.server.ding.enums.DingTypeEnum;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.server.ding.DingService;
import com.stbella.order.server.order.cts.service.CtsPayService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@DubboService
@Slf4j
public class DingServiceImpl implements DingService {

    @Resource
    private CtsPayService ctsPayService;


    @Override
    public boolean callBackForCts(JSONObject bodyJson) throws BusinessException {
        if (ObjectUtil.isNull(bodyJson)) {
            log.error("到家钉钉审批回调异常,参数为空");
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, "到家钉钉审批回调异常,参数为空");
        }
        String processInstanceId = bodyJson.getStr(DingConstant.PROCESS_INSTANCE_ID);
        String eventType = bodyJson.getStr(DingConstant.EVENT_TYPE);
        String type = bodyJson.getStr(DingConstant.TYPE);
        String result = bodyJson.getStr(DingConstant.RESULT);
        log.info("钉钉审批：" + processInstanceId + "——" + eventType + "——" + type + "——" + result);
        if (StringUtils.isBlank(processInstanceId)) {
            log.error("到家钉钉审批回调异常,PROCESS_INSTANCE_ID为空");
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, "到家钉钉审批回调异常,PROCESS_INSTANCE_ID为空");
        }
        switch (DingEventTypeEnum.getEnumByCode(eventType)) {
            //只针对审批最终阶段做处理，中间阶段不做处理
            case BPMS_INSTANCE_CHANGE:
                if (DingTypeEnum.FINISH.getCode().equalsIgnoreCase(type)) {
                    if (DingResultEnum.AGREE.getCode().equalsIgnoreCase(result)) {
                        ctsPayService.executeRefundForDing(processInstanceId, true);
                        return true;
                    } else if (DingResultEnum.REFUSE.getCode().equalsIgnoreCase(result)) {
                        ctsPayService.executeRefundForDing(processInstanceId, false);
                        return true;
                    }
                } else if (DingTypeEnum.TERMINATE.getCode().equalsIgnoreCase(type)) {
                    ctsPayService.executeRefundForDing(processInstanceId, false);
                    return true;
                }
                break;
            default:
                break;
        }
        return true;
    }

}

package com.stbella.order.server.context.component.assembler;

import cn.hutool.json.JSONUtil;
import com.stbella.core.base.Operator;
import com.stbella.core.result.Result;
import com.stbella.order.common.enums.core.CartSceneEnum;
import com.stbella.order.common.enums.order.OrderSceneEnum;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.server.order.month.req.quotation.CreateQuotationReq;
import com.stbella.platform.order.api.cart.CartQueryService;
import com.stbella.platform.order.api.req.CreateOrderReq;
import com.stbella.platform.order.api.req.QueryCartReq;
import com.stbella.platform.order.api.res.CartRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import top.primecare.snowball.flow.annotation.SnowballComponent;

import javax.annotation.Resource;

@Slf4j
@Component
@SnowballComponent(name = "创建报价单", desc = "获取购物车信息")
public class CreateQuotationAssembler {

    @Resource
    private CartQueryService cartQueryService;

    public CreateOrderReq run(QueryCartReq req, Operator operator) {

        log.info("获取购物车信息开始处理req:{}", JSONUtil.toJsonStr(req));
        Result<CartRes> cartResResult = cartQueryService.queryCart(req);
        CartRes cartData = cartResResult.getData();
        Assert.isTrue(!cartData.isSubmitted(), "当前订单不可重复提交");
        return buildCreateOrderReq(cartData, operator);
    }

    private CreateOrderReq buildCreateOrderReq(CartRes cartData, Operator operator) {

        CreateOrderReq res = new CreateOrderReq();
        res.setBu(cartData.getBu());
        res.setOrderType(cartData.getOrderType());
        res.setSceneCode(OrderSceneEnum.QUOTATION.code());
        res.setStoreId(cartData.getStoreId());
        res.setBasicUid(cartData.getBasicUid());
        res.setClientUid(cartData.getClientUid());
        res.setSkuList(cartData.getSkuList());
        res.setOriginalPrice(cartData.getOriginalPrice());
        res.setPayAmount(cartData.getPayAmount());
        res.setExtraInfo(cartData.getExtraInfo());
        res.setCartId(cartData.getCartId());
        res.setOriginalPrice(cartData.getTotalAmount());
        res.setScene(CartSceneEnum.QUOTATION.code());
        res.setOperator(operator);
        return res;
    }


}

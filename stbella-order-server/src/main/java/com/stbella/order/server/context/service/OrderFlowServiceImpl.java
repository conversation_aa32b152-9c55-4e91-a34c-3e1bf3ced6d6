package com.stbella.order.server.context.service;

import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.core.OrderProcessTypeEnum;
import com.stbella.order.common.utils.BPCheckUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.context.component.checker.OrderProcessCheckerContext;
import com.stbella.platform.order.api.OrderFlowService;
import com.stbella.platform.order.api.req.CheckOrderProcessReq;
import com.stbella.platform.order.api.res.CheckOrderProcessRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: ji<PERSON><PERSON><PERSON>
 * @CreateTime: 2024-07-04  14:34
 * @Description:  订单流程节点服务
 */
@Service
@DubboService
@Slf4j
public class OrderFlowServiceImpl implements OrderFlowService {

    @Resource
    OrderProcessCheckerContext orderProcessCheckerContext;
    @Resource
    OrderRepository orderRepository;

    /**
     * 过程节点校验
     * @param req
     * @return
     */
    @Override
    public Result<CheckOrderProcessRes> check(CheckOrderProcessReq req) {
        BPCheckUtil.checkEmptyInBean(new String[]{"processType", "orderId", "operator"}, req, false);
        HeOrderEntity order = orderRepository.getByOrderId(req.getOrderId());
        OrderProcessTypeEnum processTypeEnum = OrderProcessTypeEnum.from(req.getProcessType());
        if (processTypeEnum == null){
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code()+"", "流程代码错误，请重新输入");
        }
        try {
            orderProcessCheckerContext.check(order, processTypeEnum);
        }catch (BusinessException e){
             // 特定
            CheckOrderProcessRes checkOrderProcessRes = new CheckOrderProcessRes().setCode(e.getCode()).setMessage(e.getMessage());
            if (Objects.nonNull(e.getData())){
                checkOrderProcessRes.setApproveInstanceId(e.getData().getStr(BizConstant.ExtraKey.approveIdKey));
            }
            return Result.failed(BizConstant.OrderAppKey.SPECIAL_ERROR_CODE ,e.getMessage(),  checkOrderProcessRes);
        }


        return Result.success();
    }
}

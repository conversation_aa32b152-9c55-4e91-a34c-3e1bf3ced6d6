package com.stbella.order.server.context.component.ui;

import com.stbella.notice.entity.OaProcessIdRelationPO;
import com.stbella.order.domain.order.month.entity.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-08-13  10:23
 * @Description: 订单操作上下文
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderOperateContext {

    public HeOrderEntity order;

    List<HeIncomeRecordEntity> incomeList;

    public List<HeOrderRefundEntity> refundList;

    List<OaProcessIdRelationPO> approveList;

    /**
     * 签约记录表
     */
    List<MonthContractSignRecordEntity> mainContractList;
    /**
     * 合同补充协议
     */
    List<MonthContractSignAgreementEntity> agreementContractList;
    /**
     * 纸质合同
     */
    List<ContractSignRecordPaperEntity> paperList;


}

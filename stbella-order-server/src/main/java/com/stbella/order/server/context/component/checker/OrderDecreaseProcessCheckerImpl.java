package com.stbella.order.server.context.component.checker;

import com.stbella.contract.model.res.ContractSignRecordVO2;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.core.OrderProcessTypeEnum;
import com.stbella.order.common.enums.month.ContractStatusEnum;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.domain.order.service.OrderIncomeDomainService;
import com.stbella.order.infrastructure.gateway.ContractGateway;
import com.stbella.order.server.order.month.enums.ApprovalDiscountStatusEnum;
import com.stbella.platform.order.api.req.CheckOrderProcessReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 订单减免流程校验
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class OrderDecreaseProcessCheckerImpl implements OrderProcessChecker {

    @Resource
    OrderIncomeDomainService orderIncomeDomainService;
    @Resource
    ContractGateway contractGateway;


    @Override
    public boolean filter(CheckOrderProcessReq req) {
        return OrderProcessTypeEnum.DERATE.code().equalsIgnoreCase(req.getProcessType());
    }

    /**
     * 订单减免校验
     * - 订单存在审批中的合同
     * - 订单存在审批中的折扣申请
     * - 减免金额不能大于剩余待支付金额
     * - 订单是否存在审批状态等于审批中的退款审批流程
     *
     * @param req
     */
    @Override
    public void run(HeOrderEntity req) {

        //判断查询合同签署状态
        if (req.isNeedSign()) {
            ContractSignRecordVO2 contract = contractGateway.queryOrderMainContract(req.getOrderId());
            if (contract == null || !CompareUtil.integerEqual(ContractStatusEnum.SIGNED.code(), contract.getContractStatus())) {
                throw new BusinessException(ErrorCodeEnum.CONTRACT_NOT_SIGN.code() + "", ErrorCodeEnum.CONTRACT_NOT_SIGN.desc());
            }
        }
        //判断订单是否存在审批状态=审批中
        OrderReductionEntity orderReduction = req.fetchLastOrderReduction();
        //判断是否存在减免审批
        if (Objects.nonNull(orderReduction) && orderReduction.isApproving()) {
            throw new BusinessException(ErrorCodeEnum.DECREASE_APPROVAL_ERROR.code() + "", ErrorCodeEnum.DECREASE_APPROVAL_ERROR.desc());
        }

        //判断是否存在审阅中的收款
        List<HeIncomeRecordEntity> effectiveRecord = orderIncomeDomainService.queryEffectiveRecord(req);
        boolean hasUnsuccessfulRecord = effectiveRecord.stream()
                .anyMatch(record -> !record.isActualSuccess());
        if (hasUnsuccessfulRecord) {
            throw new BusinessException(
                    ResultEnum.ERROR_AND_SHOW.getCode(),
                    ErrorCodeEnum.DECREASE_INCOME_APPROVING_ERROR.desc()
            );
        }

        // 退款记录
        HeOrderRefundEntity lastApprovingOrderRefund = req.fetchLastApprovingOrderRefund();
        if (Objects.nonNull(lastApprovingOrderRefund)) {
            throw new BusinessException(ErrorCodeEnum.DECREASE_IN_REFUND_ERROR.code() + "", ErrorCodeEnum.DECREASE_IN_REFUND_ERROR.desc());
        }

        if (CompareUtil.integerEqual(ApprovalDiscountStatusEnum.APPROVING.getCode(), req.getApprovalDiscountStatus())) {
            throw new BusinessException(ErrorCodeEnum.APPROVE_PROCESSING.code() + "", ErrorCodeEnum.APPROVE_PROCESSING.desc());
        }


    }
}

package com.stbella.order.server.context.component.assembler;

import com.stbella.core.exception.BusinessException;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.server.context.component.DiscountCalculator;
import com.stbella.order.server.order.OrderIndex;
import com.stbella.order.server.order.OrderIndexFact;
import com.stbella.order.server.order.OrderPrice;
import com.stbella.platform.order.api.req.DiscountReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-05-29  15:20
 * @Description: 费用计算组装
 */
@Slf4j
@Component
@SnowballComponent(name = "指标计算组装", desc = "指标计算组装")
public class OrderIndexCalcuAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private DiscountCalculator discountCalculator;

    @Override
    public void run(FlowContext bizContext) {

        CfgStoreEntity cfgStore = bizContext.getAttribute(CfgStoreEntity.class);
        DiscountReq discountReq = bizContext.getAttribute(DiscountReq.class);
        OrderPrice price = bizContext.getAttribute(OrderPrice.class);

        if (Objects.isNull(discountReq)) {
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code() + "", "折扣信息不能为空");
        }
        if (Objects.isNull(price)) {
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code() + "", "计价信息不能为空");
        }

        // 开始配置事实
        OrderIndexFact indexFact = OrderIndexFact.builder()
                .bu(cfgStore.getBu())
                .orderAmount(price.getTotalAmount())
                .payAmount(discountReq.getPayAmount())
                .selectedAmount(price.getSelectedAmount())
                .goodsOriginalPrice(new BigDecimal(0))
                .goodsCostPrice(price.getTotalCost())
                .giftOriginalPrice(new BigDecimal(0))
                .giftCostPrice(new BigDecimal(0))
                .additionalOriginalPrice(new BigDecimal(0))
                .additionalCostPrice(new BigDecimal(0))
                .effectDate(System.currentTimeMillis() / 1000)
                .storeId(cfgStore.getStoreId())
                .orderType(String.valueOf(discountReq.getOrderType()))
                .storeType(cfgStore.getType())
                .isNewOrder(Boolean.TRUE)
                .build();


        FlowContext discountContext = new FlowContext();
        discountContext.setAttribute(OrderIndexFact.class, indexFact);
        OrderIndex orderIndex = discountCalculator.run(discountContext);

        bizContext.setAttribute(OrderIndex.class, orderIndex);

    }


}

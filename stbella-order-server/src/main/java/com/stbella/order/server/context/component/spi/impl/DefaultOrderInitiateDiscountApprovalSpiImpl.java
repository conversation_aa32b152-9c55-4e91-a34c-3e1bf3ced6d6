package com.stbella.order.server.context.component.spi.impl;

import cn.hutool.json.JSONUtil;
import com.stbella.base.server.ding.MonthDingService;
import com.stbella.base.server.ding.response.CreateOrderApproveRecordVO;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.month.server.request.NewOrderDiscountApprovalRequest;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderVoucherEntity;
import com.stbella.order.domain.repository.OrderVoucherRepository;
import com.stbella.order.server.context.component.spi.InitiateDiscountApprovalSpi;
import com.stbella.order.server.convert.OrderConvert;
import com.stbella.order.server.convert.OrderGoodsConverter;
import com.stbella.order.server.order.month.enums.ApprovalDiscountStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.annotation.FuncPointSpiImpl;
import top.primecare.snowball.extpoint.definition.FuncSpiConfig;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-05-30  14:01
 * @Description: 普通订单折扣审批发起实现 - 月子类
 */
@Component
@Slf4j
@FuncPointSpiImpl(name = "默认订单折折扣审批发起扩展点实现", desc = "")
public class DefaultOrderInitiateDiscountApprovalSpiImpl implements InitiateDiscountApprovalSpi {

    @DubboReference(timeout = 60000)
    private MonthDingService monthDingService;
    @Resource
    OrderConvert orderConvert;
    @Resource
    private OrderVoucherRepository orderVoucherRepository;

    @Override
    public boolean condition(HeOrderEntity param) {
        // 这里只根据BU判断
        if (CompareUtil.integerEqual(param.getOrderType(), OmniOrderTypeEnum.MONTH_ORDER.getCode())
                || CompareUtil.integerEqual(param.getOrderType(),OmniOrderTypeEnum.SMALL_MONTH_ORDER.code())
                || CompareUtil.integerEqual(param.getOrderType(),OmniOrderTypeEnum.PRODUCTION_ORDER.code())
                || CompareUtil.integerEqual(param.getOrderType(),OmniOrderTypeEnum.OTHER_MONTH_ORDER.code())

        ) {
            return true;
        }
        return false;
    }

    @Override
    public CreateOrderApproveRecordVO invoke(HeOrderEntity order) {

        List<HeOrderVoucherEntity> orderVoucher = orderVoucherRepository.getByOrderId(order.getOrderId());
        order.setVoucherEntityList(orderVoucher);
        NewOrderDiscountApprovalRequest orderDiscountApprovalRequest = orderConvert.order2NewOrderDiscountApprovalRequestList(order);

        try {
            log.info("发起审批 orderSn{}, 参数：{}" + order.getOrderSn(), JSONUtil.toJsonStr(orderDiscountApprovalRequest));

            Result<CreateOrderApproveRecordVO> orderDiscountApproval = monthDingService.createNewOrderDiscountApproval(orderDiscountApprovalRequest);
            if (!orderDiscountApproval.getCode().equals(ResultEnum.SUCCESS.getCode())) {
                //发起审批失败
                order.setApprovalDiscountStatus(ApprovalDiscountStatusEnum.INITIATION_FAILED.getCode());
                log.error("发起审批异常 orderSn= " + order.getOrderSn(), orderDiscountApproval.getMsg());
            }
            if (orderDiscountApproval.getData().getStatus().equals(1)) {
                //发起审批后改为审批中
                order.setApprovalDiscountStatus(ApprovalDiscountStatusEnum.APPROVING.getCode());
            } else {
                order.setApprovalDiscountStatus(ApprovalDiscountStatusEnum.INITIATION_FAILED.getCode());
            }
            return orderDiscountApproval.getData();
        } catch (Exception e) {
            //发起审批失败
            order.setApprovalDiscountStatus(ApprovalDiscountStatusEnum.INITIATION_FAILED.getCode());
            log.error("发起审批异常 orderSn= " + order.getOrderSn(), e.getMessage());
            log.warn("发起审批异常 orderSn= " + order.getOrderSn(), e);
        }
        order.saveApprovalState();
        return null;
    }

    @Override
    public FuncSpiConfig config() {
        return new FuncSpiConfig(true, 100);
    }
}

package com.stbella.order.server.context.component.contract.checker;

import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderBailorSnapshotEntity;
import com.stbella.order.domain.repository.OrderBailorSnapshotRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;


@Component
@Slf4j
public class EntrustContractValidator extends AbstractContractCreateValidator {

    @Resource
    private OrderBailorSnapshotRepository orderBailorSnapshotRepository;


    @Override
    public boolean doValidate(ContractValidationContext context) {

        //判断委托人信息是否存在
        HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity = orderBailorSnapshotRepository.queryByOrderId(context.getRequest().getOrderId());
        Optional.ofNullable(heOrderBailorSnapshotEntity).orElseThrow(() -> new BusinessException(ResultEnum.NOT_EXIST.getCode(), "委托人快照不存在"));

        return true;
    }

    @Override
    public TemplateContractTypeEnum templateContractType() {
        return TemplateContractTypeEnum.ENTRUST;
    }
}
//package com.stbella.order.server.strategy.approval;
//
//import com.alibaba.fastjson.JSONObject;
//import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
//import com.stbella.order.domain.order.entity.ExternalProductionOrderEntity;
//import com.stbella.order.domain.repository.ExternalProductionOrderRepository;
//import com.stbella.order.server.order.month.request.approval.ApprovalStatusInfo;
//import com.stbella.order.server.order.order.service.ExternalProductionOrderStatusEnum;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.Objects;
//
///**
// * <p>
// * 产康馆外订单审批策略
// * </p>
// *
// * <AUTHOR> * @since 2024-08-15
// */
//@Slf4j
//@Component
//public class ExternalProductionOrderApprovalStrategy implements ApprovalStrategy {
//
//    @Resource
//    private ExternalProductionOrderRepository externalProductionOrderRepository;
//
//    @Override
//    public void handleApproval(OrderApproveRecordTypeEnum type, String params, String messageId, ApprovalStatusInfo approvalStatusInfo, String processId) {
//        log.info("处理产康馆外订单审批回调：{}, approvalStatusInfo: {}, processId: {}", params, JSONObject.toJSONString(approvalStatusInfo), processId);
//
//        // 根据审批流程ID查询订单
//        ExternalProductionOrderEntity entity = externalProductionOrderRepository.getByApprovalProcessId(processId);
//        if (Objects.isNull(entity)) {
//            log.error("产康馆外订单审批回调找不到对应订单：{}", processId);
//            return;
//        }
//
//        // 判断审批结果
//        boolean finish = approvalStatusInfo.isFinish();
//        boolean agree = approvalStatusInfo.isAgree();
//
//        if (finish) {
//            // 审批完成
//            if (agree) {
//                // 审批通过
//                externalProductionOrderRepository.updateStatus(entity.getId(), ExternalProductionOrderStatusEnum.APPROVED.getCode());
//                log.info("产康馆外订单审批通过：{}", entity.getOrderSn());
//            } else {
//                // 审批拒绝
//                externalProductionOrderRepository.updateStatus(entity.getId(), ExternalProductionOrderStatusEnum.REJECTED.getCode());
//                log.info("产康馆外订单审批拒绝：{}", entity.getOrderSn());
//            }
//        }
//    }
//
//    @Override
//    public OrderApproveRecordTypeEnum getOrderApproveRecordTypeEnum() {
//        return OrderApproveRecordTypeEnum.EXTERNAL_PRODUCTION_ORDER;
//    }
//}

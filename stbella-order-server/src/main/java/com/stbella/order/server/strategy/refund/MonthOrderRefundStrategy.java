package com.stbella.order.server.strategy.refund;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.stbella.base.server.sms.enums.SmsTemplateV2Enum;
import com.stbella.core.base.Operator;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.notice.entity.OaProcessRecordPO;
import com.stbella.notice.server.OaProcessRecordService;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OrderRefundGoodsStatusEnum;
import com.stbella.order.common.enums.core.OrderRefundNatureEnum;
import com.stbella.order.common.enums.core.OrderRefundStatusEnum;
import com.stbella.order.common.enums.month.IncomeReceiptTypeEnum;
import com.stbella.order.common.enums.month.PayStatusV2Enum;
import com.stbella.order.common.enums.month.RefundRecordPayStatusEnum;
import com.stbella.order.common.enums.month.RefundRecordPayStatusSimpleEnum;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.service.OrderRefundDomainService;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.context.component.processor.performance.OrderAmountProcessor;
import com.stbella.order.server.manager.SmsManager;
import com.stbella.order.server.order.month.enums.CustomerComplaintsStatusEnum;
import com.stbella.order.server.order.month.enums.OrderEventEnum;
import com.stbella.order.server.order.month.enums.PayRecordEnum;
import com.stbella.order.server.order.month.enums.PayStatusEnum;
import com.stbella.order.server.order.month.service.HeOrderService;
import com.stbella.order.server.order.month.service.MonthOrderWxCommandService;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.order.server.producer.OrderEventProducer;
import com.stbella.order.server.utils.JsonUtil;
import com.stbella.order.server.utils.RefundAchievementCalculateUtil;
import com.stbella.pay.server.entity.mq.RefundNotifyMq;
import com.stbella.platform.order.api.OrderAggregateRoot;
import com.stbella.platform.order.api.refund.api.OrderRefundService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import top.primecare.snowball.flow.core.context.FlowContext;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 退款策略-母婴订单
 * @date 2024/3/15 10:56
 */
@Slf4j
@Component
public class MonthOrderRefundStrategy implements RefundStrategy {
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private SmsManager smsManager;
    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;
    @Resource
    private MonthOrderWxCommandService monthOrderWxCommandService;
    @DubboReference
    private OaProcessRecordService oaProcessRecordService;
    @Resource
    private OrderEventProducer orderEventProducer;
    @Resource
    private HeOrderRefundGoodsRepository orderRefundGoodsRepository;
    @Resource
    private OrderRefundDomainService orderRefundDomainService;
    @Resource
    private OrderAggregateRoot orderAggregateRoot;

    @Resource
    private HeOrderService heOrderService;
    @Resource
    private HeCustomerComplaintsRepository customerComplaintsRepository;
    @Resource
    private OrderAmountProcessor orderAmountProcessor;
    @Resource
    private OrderRefundService orderRefundService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processRefund(RefundNotifyMq refundNotifyMq) {
        Integer orderId = null;
        //申请记录表id
        String outRefundNo = refundNotifyMq.getOutRefundNo();
        Integer refundStatus = refundNotifyMq.getRefundStatus();
        BigDecimal refundAmount = refundNotifyMq.getRefundAmount();
        //CSP订单
        HeOrderRefundEntity oneByRefundOrderSn = orderRefundRepository.getOneById(Integer.valueOf(outRefundNo));
        if (ObjectUtil.isEmpty(oneByRefundOrderSn)) {
            log.error("退款回调找不到退款记录：{}", outRefundNo);
            throw new BusinessException(ResultEnum.NOT_EXIST, "退款回调找不到退款记录,退款编号=" + outRefundNo);
        }
        HeIncomeRecordEntity recordByIncomeSn = incomeRecordRepository.getRecordByIncomeSn(oneByRefundOrderSn.getIncomeSn());
        if (ObjectUtil.isEmpty(recordByIncomeSn)) {
            log.error("退款回调找不到支付记录：{}", outRefundNo);
            throw new BusinessException(ResultEnum.NOT_EXIST, "退款回调找不到支付记录,退款编号=" + outRefundNo);
        }

        if (ObjectUtil.isNotEmpty(oneByRefundOrderSn.getOrderId()) && oneByRefundOrderSn.getOrderId() > 0) {
            HeOrderEntity orderEntity = orderRepository.getByOrderId(oneByRefundOrderSn.getOrderId());
            if (orderEntity.isNewOrder()) {
                //新订单
                processRefundNew(refundNotifyMq, oneByRefundOrderSn, recordByIncomeSn);
                return;
            }
        }

        //押金和订单退款这块逻辑共用
        if (refundStatus.equals(PayStatusEnum.PAY_STATUS_SUCCESS.getCode())) {
            oneByRefundOrderSn.setActualAmount(Integer.parseInt(RMBUtils.changeY2F(String.valueOf(refundNotifyMq.getRefundAmount()))));
            oneByRefundOrderSn.setTransactionId(refundNotifyMq.getTransactionalNo());
            //设置退款成功
            oneByRefundOrderSn.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode());
            oneByRefundOrderSn.setFinishAt(refundNotifyMq.getSuccessTime().getTime() / 1000);
            //修改支付记录中总退款金额
            recordByIncomeSn.setAlreadyRefundAmount(recordByIncomeSn.getAlreadyRefundAmount() + Integer.parseInt(RMBUtils.changeY2F(String.valueOf(refundAmount))));
        } else {
            //设置退款失败
            oneByRefundOrderSn.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_5.getCode());
        }
        oneByRefundOrderSn.setUpdatedAt(refundNotifyMq.getSuccessTime().getTime() / 1000);
        oneByRefundOrderSn.setPaymentResult(refundNotifyMq.getRequestBody());
        //释放冻结金额
        recordByIncomeSn.setFreezeAmount(recordByIncomeSn.getFreezeAmount() - Integer.parseInt(RMBUtils.changeY2F(String.valueOf(refundAmount))));
        //更新支付记录总退款金额/更新支付记录冻结金额
        incomeRecordRepository.updateRecord(recordByIncomeSn);
        //订单流程
        if (!Objects.equals(recordByIncomeSn.getReceiptType(), IncomeReceiptTypeEnum.RECORD_RECEIPT_TYPE_DEPOSIT.code())) {
            HeOrderEntity byOrderId = orderRepository.getByOrderId(oneByRefundOrderSn.getOrderId());
            if (ObjectUtil.isEmpty(byOrderId)) {
                log.error("退款回调找不到订单记录：{}", oneByRefundOrderSn.getOrderId());
                throw new BusinessException(ResultEnum.NOT_EXIST, "退款回调找不到订单记录,退款编号=" + outRefundNo);
            }
            orderId = oneByRefundOrderSn.getOrderId();
            if (refundStatus.equals(PayStatusEnum.PAY_STATUS_SUCCESS.getCode())) {
                //有业绩时间才退业绩
                if (ObjectUtil.isNotEmpty(byOrderId.getPercentFirstTime()) && !byOrderId.getPercentFirstTime().equals(0)) {
                    HeCustomerComplaintsEntity customerComplaintsEntity = customerComplaintsRepository.selectByOrderRefundId(oneByRefundOrderSn.getId());
                    if (ObjectUtil.isEmpty(customerComplaintsEntity)) {
                        HeCustomerComplaintsEntity heCustomerComplaintsEntity = customerComplaintsRepository.selectByOrderRefundId(oneByRefundOrderSn.getId());
                        if (ObjectUtil.isEmpty(heCustomerComplaintsEntity)) {
                            log.info("开始扣业绩");
                            //  退款业绩计算  退单业绩 = 实际退款金额 * 签单金额  / 累计已付金额
                            oneByRefundOrderSn.setRefundAchievement(calculateRefundAchievement(oneByRefundOrderSn.getActualAmount(), byOrderId.getPaidAmount(), byOrderId.getPayAmount()));
                        }
                    }
                }
                //订单实际金额
                if (OmniOrderTypeEnum.MONTH_ORDER.getCode().equals(byOrderId.getOrderType())) {
                    byOrderId.setRealAmount(byOrderId.getRealAmount() - Integer.parseInt(RMBUtils.changeY2F(String.valueOf(refundAmount))));
                }
                //更新订单实际金额
                orderRepository.updateOrderMonthByOrderId(byOrderId);
                //根据refundId获取审批详情
                //从企微审批表中获取
                if (!OmniOrderTypeEnum.PRODUCTION_ORDER.getCode().equals(byOrderId.getOrderType())) {
                    sendSMS(oneByRefundOrderSn, oneByRefundOrderSn, refundAmount, byOrderId);
                }
            }
        }

        //更新退款记录
        orderRefundRepository.updateOneById(oneByRefundOrderSn);
        //订单补充逻辑
        if (!Objects.equals(recordByIncomeSn.getReceiptType(), IncomeReceiptTypeEnum.RECORD_RECEIPT_TYPE_DEPOSIT.code())) {
            //同步退款状态
            Integer orderRefundStatus = heOrderService.getRefundStatusByActualAmount(orderId, 0, RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode().equals(oneByRefundOrderSn.getStatus()), true);
            orderRepository.syncOrderRefundStatus(orderId, orderRefundStatus);
            //发送订单变更通知
            HeOrderEntity byOrderId = orderRepository.getByOrderId(orderId);
            if (byOrderId.getRefundStatus().equals(OrderRefundStatusEnum.PARTIAL_REFUND.getCode())) {
                orderEventProducer.sendMq(OrderEventEnum.PART_REFUND.getCode(), byOrderId.getOrderSn(), null);
            } else if (byOrderId.getRefundStatus().equals(OrderRefundStatusEnum.FULL_REFUND.getCode())) {
                orderEventProducer.sendMq(OrderEventEnum.FULL_REFUND.getCode(), byOrderId.getOrderSn(), null);
            }
        }

        if (refundStatus.equals(PayStatusEnum.PAY_STATUS_SUCCESS.getCode())) {
            //处理客诉
            HeCustomerComplaintsEntity customerComplaintsEntity = customerComplaintsRepository.selectByOrderRefundId(oneByRefundOrderSn.getId());
            if (ObjectUtil.isNotEmpty(customerComplaintsEntity)) {
                customerComplaintsEntity.setComplaintStatus(CustomerComplaintsStatusEnum.SUCCESS.getCode());
                customerComplaintsEntity.setResolveFinish(new Date());
                customerComplaintsRepository.update(customerComplaintsEntity);
            }
        }
    }


    public Integer calculateRefundAchievement(Integer actualAmount, Integer paidAmount, Integer payAmount) {
        // 使用工具类的计算方法
        return RefundAchievementCalculateUtil.calculateRefundAchievement(actualAmount, paidAmount, payAmount);
    }

    /**
     * 新订单的退款逻辑
     *
     * @param refundNotifyMq
     */
    public void processRefundNew(RefundNotifyMq refundNotifyMq, HeOrderRefundEntity oneByRefundOrderSn, HeIncomeRecordEntity recordByIncomeSn) {
        log.info("新订单退款逻辑入参 orderRefundEntity:{},refundNotifyMq:{},recordByIncomeSn:{}", JSONUtil.toJsonStr(oneByRefundOrderSn), JSONUtil.toJsonStr(refundNotifyMq), JSONUtil.toJsonStr(recordByIncomeSn));
        String parentRefundOrderSn = oneByRefundOrderSn.getParentRefundOrderSn();
        //这个的父类
        HeOrderRefundEntity parentRefund = orderRefundRepository.getOneByRefundOrderSn(parentRefundOrderSn);

        Integer orderId = null;
        //申请记录表id
        String outRefundNo = refundNotifyMq.getOutRefundNo();
        Integer refundStatus = refundNotifyMq.getRefundStatus();
        BigDecimal refundAmount = refundNotifyMq.getRefundAmount();

        //押金和订单退款这块逻辑共用
        if (refundStatus.equals(PayStatusEnum.PAY_STATUS_SUCCESS.getCode())) {
            oneByRefundOrderSn.setActualAmount(Integer.parseInt(RMBUtils.changeY2F(String.valueOf(refundNotifyMq.getRefundAmount()))));
            oneByRefundOrderSn.setTransactionId(refundNotifyMq.getTransactionalNo());
            //设置退款成功
            oneByRefundOrderSn.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode());
            oneByRefundOrderSn.setFinishAt(refundNotifyMq.getSuccessTime().getTime() / 1000);
            //修改支付记录中总退款金额
            recordByIncomeSn.setAlreadyRefundAmount(recordByIncomeSn.getAlreadyRefundAmount() + Integer.parseInt(RMBUtils.changeY2F(String.valueOf(refundAmount))));
            if (ObjectUtil.isNotEmpty(parentRefund)) {
                //退款金额增加
                parentRefund.setActualAmount(parentRefund.getActualAmount() + Integer.parseInt(RMBUtils.changeY2F(String.valueOf(refundNotifyMq.getRefundAmount()))));
            }
        } else {
            //设置退款失败
            oneByRefundOrderSn.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_5.getCode());
        }

        oneByRefundOrderSn.setUpdatedAt(refundNotifyMq.getSuccessTime().getTime() / 1000);
        oneByRefundOrderSn.setPaymentResult(refundNotifyMq.getRequestBody());
        //释放冻结金额
        recordByIncomeSn.setFreezeAmount(recordByIncomeSn.getFreezeAmount() - Integer.parseInt(RMBUtils.changeY2F(String.valueOf(refundAmount))));
        //更新支付记录总退款金额/更新支付记录冻结金额
        incomeRecordRepository.updateRecord(recordByIncomeSn);

        //订单流程
        HeOrderEntity orderEntity = orderRepository.getByOrderId(oneByRefundOrderSn.getOrderId());
        HeOrderEntity modifyOrderEntity = new HeOrderEntity();
        modifyOrderEntity.setOrderId(orderEntity.getOrderId());
        orderId = oneByRefundOrderSn.getOrderId();
        if (ObjectUtil.isEmpty(orderEntity)) {
            log.error("退款回调找不到订单记录：{}", orderId);
            throw new BusinessException(ResultEnum.NOT_EXIST, "退款回调找不到订单记录,退款编号=" + outRefundNo);
        }
        Integer refundAchievement = parentRefund.getRefundAchievement();
        //新的退款业绩计算方法，把退款业绩记录到一个退款的子项里面去
        if (refundStatus.equals(PayStatusEnum.PAY_STATUS_SUCCESS.getCode())) {
            //有业绩时间才退业绩 且非退回重付会退款业绩
            Integer refundNature = parentRefund.getRefundNature();
            //退回重付才会更新订单的实际金额
            log.info("parentRefund.getRefundNature():{},refundAchievement:{}", refundNature, refundAchievement);

            boolean b = ObjectUtil.isNotEmpty(orderEntity.getPercentFirstTime()) && !orderEntity.getPercentFirstTime().equals(0) && !refundNature.equals(OrderRefundNatureEnum.TEMP_REFUND.code());
            if (b && Objects.nonNull(parentRefund.getComplaintId())) {
                log.info("开始扣业绩");
                //  退款业绩计算  退单业绩 = 实际退款金额  / 累计已付金额 * 签单金额
                oneByRefundOrderSn.setRefundAchievement(0);
                //父类退款业绩增加
                parentRefund.setRefundAchievement(0);
            }


            if (refundNature.equals(OrderRefundNatureEnum.TEMP_REFUND.code())) {
                log.info("orderEntity:{}", JsonUtil.write(orderEntity));
                //订单实际金额
                orderEntity.setRealAmount(orderEntity.getRealAmount() - oneByRefundOrderSn.getApplyAmount());
                orderEntity.setFreezeAmount(orderEntity.getFreezeAmount() - oneByRefundOrderSn.getApplyAmount());
                modifyOrderEntity.setRealAmount(orderEntity.getRealAmount());
                modifyOrderEntity.setFreezeAmount(orderEntity.getFreezeAmount());
                //订单实际金额改变
                if ((orderEntity.getRealAmount() + orderEntity.getProductionAmountPay()) > 0) {
                    orderEntity.setPayStatus(PayStatusV2Enum.NO_PAY_OFF.getCode());
                    modifyOrderEntity.setPayStatus(PayStatusV2Enum.NO_PAY_OFF.getCode());
                } else {
                    orderEntity.setPayStatus(PayStatusV2Enum.WAIT_PAY.getCode());
                    modifyOrderEntity.setPayStatus(PayStatusV2Enum.WAIT_PAY.getCode());
                }
            }

            //如果实际金额大于0则是订单退款状态是部分退款,如果实际金额<=0则是全部退款
            if (orderEntity.getRealAmount() > 0) {
                orderEntity.setRefundStatus(OrderRefundStatusEnum.PARTIAL_REFUND.getCode());
            } else {
                orderEntity.setRefundStatus(OrderRefundStatusEnum.FULL_REFUND.getCode());
                modifyOrderEntity.setFullRefundDate(new Date());
            }
            modifyOrderEntity.setRefundStatus(orderEntity.getRefundStatus());

            //更新订单实际金额
            orderRepository.updateOrderMonthByOrderId(modifyOrderEntity);

            //只有这个退款订单全部退款完成的才会触发短信
            if (parentRefund.getActualAmount().compareTo(parentRefund.getApplyAmount()) >= 0) {
                //修改主退款状态
                parentRefund.setStatus(RefundRecordPayStatusSimpleEnum.REFUND_RECORD_4.getCode());
                //修改退款商品
                setRefundGoodsStatus(parentRefund);
                if (!Objects.equals(recordByIncomeSn.getReceiptType(), IncomeReceiptTypeEnum.RECORD_RECEIPT_TYPE_DEPOSIT.code())) {
                    orderRefundDomainService.sendRefundAfter(parentRefund.getOrderId(), parentRefund.getId());
                }
                //处理客诉
                HeCustomerComplaintsEntity customerComplaintsEntity = customerComplaintsRepository.selectByOrderRefundId(parentRefund.getId());
                if (ObjectUtil.isNotEmpty(customerComplaintsEntity)) {
                    customerComplaintsEntity.setComplaintStatus(CustomerComplaintsStatusEnum.SUCCESS.getCode());
                    customerComplaintsEntity.setResolveFinish(new Date());
                    customerComplaintsRepository.update(customerComplaintsEntity);
                }
            }
            //更新主
            orderRefundRepository.updateOneById(parentRefund);
            //更新子
            orderRefundRepository.updateOneById(oneByRefundOrderSn);
            //更新状态
            orderRefundDomainService.updateParentStatus(parentRefund.getId());
            //修改订单订单退款状态
            monthOrderWxCommandService.syncOrderRefundStatusForNew(parentRefund.getOrderId());
            if (!Objects.equals(recordByIncomeSn.getReceiptType(), IncomeReceiptTypeEnum.RECORD_RECEIPT_TYPE_DEPOSIT.code())) {
                //发送订单变更通知
                HeOrderEntity byOrderId = orderRepository.getByOrderId(parentRefund.getOrderId());
                if (byOrderId.getRefundStatus().equals(OrderRefundStatusEnum.PARTIAL_REFUND.getCode())) {
                    orderEventProducer.sendMq(OrderEventEnum.PART_REFUND.getCode(), orderEntity.getOrderSn(), refundNature);
                } else if (byOrderId.getRefundStatus().equals(OrderRefundStatusEnum.FULL_REFUND.getCode())) {
                    orderEventProducer.sendMq(OrderEventEnum.FULL_REFUND.getCode(), orderEntity.getOrderSn(), refundNature);
                }
            }
            try {
                orderAggregateRoot.close(Operator.system(), orderEntity.getOrderId());
            } catch (Exception e) {
                log.info("关闭订单：{}", e.getMessage());
            }
            FlowContext context = new FlowContext();
            context.setAttribute(HeOrderEntity.class, orderEntity);
            orderAmountProcessor.run(context);
        }
    }

    private void sendSMS(HeOrderRefundEntity oneByRefundOrderSn, HeOrderRefundEntity parentRefund, BigDecimal refundAmount, HeOrderEntity byOrderId) {
        //根据refundId获取审批详情
        //从企微审批表中获取（获取主的）
        OaProcessRecordPO byRefundId = oaProcessRecordService.getByRefundId(parentRefund.getId());
        if (ObjectUtil.isNotEmpty(byRefundId)) {
            String params = byRefundId.getBody();
            JSONObject jsonObject = JSONUtil.toBean(params, JSONObject.class);
            String customerName = (String) jsonObject.get("customerName");
            String phone = (String) jsonObject.get("phone");
            try {
                log.info("退款成功发送给发起人:" + phone);
                //发给发起人（审批发起人）
                smsManager.sendMessage(phone, SmsTemplateV2Enum.HELPER_REFUND_ORIGINAL_BACK_USER, new String[]{customerName, refundAmount.setScale(2).toEngineeringString()});
            } catch (Exception e) {
                log.error("退款成功发送给发起人异常：{}，手机号：{}，内容：{}", e.getMessage(), phone, new String[]{customerName, refundAmount.setScale(2).toEngineeringString()});
            }
        } else {
            log.error("退款回调无法找到审批数据：oneByRefundOrderSn.getId" + oneByRefundOrderSn.getId());
        }
        //用户订单用户快照
        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(oneByRefundOrderSn.getOrderId());
        if (ObjectUtil.isNotEmpty(heOrderUserSnapshotEntity)) {

            try {
                log.info("退款成功发送给客户:" + heOrderUserSnapshotEntity.getPhone());
                //发给客户（订单快照）
                smsManager.sendMessage(heOrderUserSnapshotEntity.getPhone(), SmsTemplateV2Enum.HELPER_REFUND_ORIGINAL_BACK_CLIENT, new String[]{byOrderId.getOrderSn().toString(), refundAmount.setScale(2).toEngineeringString()});
            } catch (Exception e) {
                log.error("退款成功发送给客户异常：{}，手机号：{}，内容：{}", e.getMessage(), heOrderUserSnapshotEntity.getPhone(), new String[]{oneByRefundOrderSn.getOrderId().toString(), refundAmount.setScale(2).toEngineeringString()});
            }
        } else {
            log.error("退款回调没有找到订单客户快照");
        }
    }

    private void setRefundGoodsStatus(HeOrderRefundEntity parentRefund) {
        List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList = orderRefundGoodsRepository.queryByOrderId(parentRefund.getOrderId());
        orderRefundGoodsEntityList = orderRefundGoodsEntityList.stream().filter(o -> o.getRefundOrderSn().equals(parentRefund.getRefundOrderSn())).collect(Collectors.toList());
        for (HeOrderRefundGoodsEntity heOrderRefundGoodsEntity : orderRefundGoodsEntityList) {
            heOrderRefundGoodsEntity.setStatus(OrderRefundGoodsStatusEnum.SUCCESS.code());
        }
        //更新订单退款商品
        orderRefundGoodsRepository.updateList(orderRefundGoodsEntityList);
    }

    @Override
    public PayRecordEnum getPayRecordEnum() {
        return PayRecordEnum.BK;
    }
}

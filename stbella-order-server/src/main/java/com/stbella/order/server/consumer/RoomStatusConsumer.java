package com.stbella.order.server.consumer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.asset.api.req.CouponSendActivityReq;
import com.stbella.care.server.care.dto.RoomStateUpdateMsgBody;
import com.stbella.customer.server.ecp.vo.ClientInfoVO;
import com.stbella.order.common.enums.month.OrderStatusV2Enum;
import com.stbella.order.common.enums.month.RoomStatusEnum;
import com.stbella.order.common.utils.JsonUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.manager.AssetManager;
import com.stbella.order.server.manager.TabClientManager;
import com.stbella.platform.order.api.OrderCommandService;
import com.stbella.platform.order.api.res.PromotionInfo;
import com.stbella.pulsar.consumer.PulsarConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.Message;
import org.apache.pulsar.client.api.SubscriptionType;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class RoomStatusConsumer {

    private static final String PULSAR_DESTINATION = "persistent://pulsar-44k4paxzz5vg/nameSpace/room_update_notify";
    private static final String SUBSCRIPTION_NAME = "room_update_notify";
    private static final Date DEFAULT_RESET_DATE = new Date(0);
    private static final long SECONDS_CONVERSION_FACTOR = 1000L;

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private OrderCommandService orderCommandService;

    @Resource
    private AssetManager assetManager;

    @Resource
    private TabClientManager tabClientManager;

    /**
     * 处理房态变更消息
     * 根据房态状态更新订单状态，并执行相关业务逻辑
     */
    @PulsarConsumerListener(
            destination = PULSAR_DESTINATION,
            subscriptionName = SUBSCRIPTION_NAME,
            subscriptionType = SubscriptionType.Shared
    )
    @Transactional(rollbackFor = Exception.class)
    public void handleRoomStatusUpdate(Message message) {
        try {
            RoomStateUpdateMsgBody msgBody = parseMessage(message);
            String messageId = String.valueOf(message.getMessageId());
            String orderNo = msgBody.getOrderNo();

            log.info("开始处理房态变更消息 messageId={}, orderNo={}, 参数={}",
                    messageId, orderNo, JSONUtil.parse(msgBody));

            HeOrderEntity existingOrder = findOrderByOrderNo(orderNo);
            if (existingOrder == null) {
                log.warn("订单不存在，可能是ECP老订单，跳过处理 orderNo={}", orderNo);
                return;
            }

            HeOrderEntity orderUpdate = buildOrderUpdate(existingOrder, msgBody);
            orderRepository.updateOrderMonthByOrderId(orderUpdate);

            log.info("房态变更处理完成 messageId={}, orderNo={}, 更新状态={}",
                    messageId, orderNo, orderUpdate.getOrderStatus());

        } catch (Exception e) {
            log.error("处理房态变更消息失败", e);
            throw e;
        }
    }

    /**
     * 解析消息内容
     */
    private RoomStateUpdateMsgBody parseMessage(Message message) {
        String messageData = new String(message.getData());
        return JsonUtil.read(messageData, RoomStateUpdateMsgBody.class);
    }

    /**
     * 根据订单号查找订单
     */
    private HeOrderEntity findOrderByOrderNo(String orderNo) {
        if (!StringUtils.hasText(orderNo)) {
            log.warn("订单号为空");
            return null;
        }
        return orderRepository.getByOrderSn(orderNo);
    }

    /**
     * 构建订单更新对象
     */
    private HeOrderEntity buildOrderUpdate(HeOrderEntity existingOrder, RoomStateUpdateMsgBody msgBody) {
        HeOrderEntity updateModel = createBaseUpdateModel(existingOrder);
        OrderStatusV2Enum newStatus = determineOrderStatus(msgBody, updateModel);
        updateModel.setOrderStatus(newStatus.getCode());
        return updateModel;
    }

    /**
     * 创建基础更新模型
     */
    private HeOrderEntity createBaseUpdateModel(HeOrderEntity existingOrder) {
        HeOrderEntity updateModel = new HeOrderEntity();
        updateModel.setOrderId(existingOrder.getOrderId());
        updateModel.setUpdatedAt(System.currentTimeMillis() / SECONDS_CONVERSION_FACTOR);
        return updateModel;
    }

    /**
     * 根据房态状态确定订单状态
     * 入住状态映射：0-取消预订, 1-已预订待入住, 2-已入住, 3-已离店
     */
    private OrderStatusV2Enum determineOrderStatus(RoomStateUpdateMsgBody msgBody, HeOrderEntity updateModel) {
        Integer checkInStatus = msgBody.getCheckInStatus();
        RoomStatusEnum roomStatus = RoomStatusEnum.getByCode(checkInStatus);

        switch (roomStatus) {
            case CREATE_RESERVATION:
                return handleCreateReservation();

            case CANCEL_RESERVATION:
                return handleCancelReservation(updateModel);

            case CHECK_IN:
                return handleCheckIn(msgBody, updateModel);

            case CHECK_OUT:
                return handleCheckOut(msgBody, updateModel);

            default:
                log.warn("未识别的房态状态: {}", checkInStatus);
                return OrderStatusV2Enum.NONE;
        }
    }

    /**
     * 处理创建预订状态
     */
    private OrderStatusV2Enum handleCreateReservation() {
        return OrderStatusV2Enum.TO_STAY_IN;
    }

    /**
     * 处理取消预订状态
     */
    private OrderStatusV2Enum handleCancelReservation(HeOrderEntity updateModel) {
        updateModel.setFulfillmentEndDate(DEFAULT_RESET_DATE);
        return OrderStatusV2Enum.TO_STAY_IN;
    }

    /**
     * 处理入住状态
     */
    private OrderStatusV2Enum handleCheckIn(RoomStateUpdateMsgBody msgBody, HeOrderEntity updateModel) {
        if (Objects.nonNull(msgBody.getCheckInDate())) {
            updateModel.setFulfillmentStartDate(msgBody.getCheckInDate());
        }

        HeOrderEntity order = orderRepository.getByOrderId(updateModel.getOrderId());
        processCheckInBusinessLogic(order);

        return OrderStatusV2Enum.STAY_IN;
    }

    /**
     * 处理离店状态
     */
    private OrderStatusV2Enum handleCheckOut(RoomStateUpdateMsgBody msgBody, HeOrderEntity updateModel) {
        if (Objects.nonNull(msgBody.getCheckOutDate())) {
            updateModel.setFulfillmentEndDate(msgBody.getCheckOutDate());
        }
        return OrderStatusV2Enum.OUT_OF_STORE;
    }

    /**
     * 处理入住相关业务逻辑
     */
    private void processCheckInBusinessLogic(HeOrderEntity order) {
        if (order == null) {
            log.warn("订单为空，跳过入住业务逻辑处理");
            return;
        }

        try {
            invalidatePregnancyCareAssets(order);
            distributeCoupons(order);
        } catch (Exception e) {
            log.error("处理入住业务逻辑失败 orderId={}", order.getOrderId(), e);
        }
    }

    /**
     * 使孕期产康金资产失效
     */
    private void invalidatePregnancyCareAssets(HeOrderEntity order) {
        try {
            orderCommandService.checkInDecreaseCKJ(order.getOrderId());
            log.info("孕期产康金资产失效处理完成 orderId={}", order.getOrderId());
        } catch (Exception e) {
            log.error("孕期产康金资产失效处理失败 orderId={}", order.getOrderId(), e);
            throw e;
        }
    }

    /**
     * 发放入住优惠券
     */
    private void distributeCoupons(HeOrderEntity order) {
        log.info("开始发放入住优惠券 orderId={}", order.getOrderId());
        List<PromotionInfo> promotionInfos = order.getPromotionInfos();
        if (CollectionUtil.isEmpty(promotionInfos)) {
            return;
        }
        for (PromotionInfo promotionInfo : promotionInfos) {
            String giftCouponId = promotionInfo.getGiftCouponId();
            if (StringUtils.hasText(giftCouponId)) {
                //发放有赞券
                Integer giftCouponQuantity = promotionInfo.getGiftCouponQuantity();
                for (int i = 0; i < giftCouponQuantity; i++) {
                    CouponSendActivityReq couponSendActivityReq = new CouponSendActivityReq();
                    couponSendActivityReq.setActivityId(Long.parseLong(giftCouponId));
                    ClientInfoVO clientInfoByClientId = tabClientManager.getClientInfoByClientId(order.getClientUid());
                    couponSendActivityReq.setPhone(clientInfoByClientId.getPhone());
                    couponSendActivityReq.setBasicUid(clientInfoByClientId.getBasicUid().longValue());
                    Long uniqueRandomNumber = System.currentTimeMillis() + RandomUtil.randomInt(6);
                    couponSendActivityReq.setPrizeRecordId(uniqueRandomNumber);
                    try {
                        String uniqueId = assetManager.sendCoupon(couponSendActivityReq);
                        log.info("有赞券发放成功，券id为{}", uniqueId);
                    } catch (Exception e) {
                        log.error("优惠券发放失败", e);
                    }
                }
            }
        }
    }
}
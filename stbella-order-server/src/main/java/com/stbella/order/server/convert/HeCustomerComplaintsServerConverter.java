package com.stbella.order.server.convert;

import com.stbella.core.base.PageVO;
import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsEntity;
import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsTypeEntity;
import com.stbella.order.server.config.MappingConfig;
import com.stbella.order.server.order.month.req.ComplaintsPicpAddReq;
import com.stbella.order.server.utils.DateUtils;
import com.stbella.platform.order.api.req.CustomerComplaintsCreateReq;
import com.stbella.platform.order.api.res.CustomerComplaintsDetailVO;
import com.stbella.platform.order.api.res.CustomerComplaintsTypeVO;
import com.stbella.platform.order.api.res.CustomerComplaintsVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

@Mapper(config = MappingConfig.class)
public interface HeCustomerComplaintsServerConverter {


    List<CustomerComplaintsTypeVO> customerComplaintsTypeEntityToVO(List<HeCustomerComplaintsTypeEntity> listByParentId);

    @Mappings({@Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "toDateTimeStringOrEmpty"),})
    List<CustomerComplaintsVO> customerComplaintsEntityToVO(List<HeCustomerComplaintsEntity> page);

    @Mappings({@Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "toDateTimeStringOrEmpty"),})
    CustomerComplaintsVO heCustomerComplaintsEntityToCustomerComplaintsVO(HeCustomerComplaintsEntity heCustomerComplaintsEntity);

    PageVO<CustomerComplaintsVO> customerComplaintsEntityPageToVOPage(PageVO<HeCustomerComplaintsEntity> page);

    HeCustomerComplaintsEntity customerComplaintsCreateReqToEntity(CustomerComplaintsCreateReq customerComplaintsCreateReq);


    @Named("toDateTimeStringOrEmpty")
    default String toDateTimeStringOrEmpty(Date timestamp) {
        return DateUtils.formatDateTime(timestamp);
    }

    HeCustomerComplaintsEntity customerComplaintsCreateReqToEntity(ComplaintsPicpAddReq req);

    CustomerComplaintsDetailVO customerComplaintsEntityToDetailVO(HeCustomerComplaintsEntity heCustomerComplaintsEntity);
}

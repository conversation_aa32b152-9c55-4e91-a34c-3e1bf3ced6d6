package com.stbella.order.server.consumer;

import com.stbella.pulsar.consumer.PulsarConsumerListener;
import org.apache.pulsar.client.api.Message;
import org.apache.pulsar.client.api.SubscriptionType;

public class AssetConsumer {

    @PulsarConsumerListener(destination = "persistent://pulsar-44k4paxzz5vg/nameSpace/room_update_notify", subscriptionName = "room_update_notify", subscriptionType = SubscriptionType.Shared)
    public void getMsg(Message message) {

    }
}

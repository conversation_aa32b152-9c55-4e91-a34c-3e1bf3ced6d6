package com.stbella.order.server.context.component.processor.pay;

import cn.hutool.json.JSONObject;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.server.context.component.spi.PaySpi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.SnowballExtensionInvoker;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

/**
 * @Author: JJ
 * @CreateTime: 2024-05-28  13:55
 * @Description: 支付：线上支付及余额等
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "支付", desc = "发起支付")
public class OrderPayProcessor implements IExecutableAtom<FlowContext> {

    @Override
    public void run(FlowContext bizContext) {

        HeIncomeRecordEntity incomeRecordEntity = bizContext.getAttribute(HeIncomeRecordEntity.class);
        JSONObject jsonObject = SnowballExtensionInvoker.invoke(PaySpi.class, incomeRecordEntity);
        bizContext.setAttribute(JSONObject.class, jsonObject);

    }
}

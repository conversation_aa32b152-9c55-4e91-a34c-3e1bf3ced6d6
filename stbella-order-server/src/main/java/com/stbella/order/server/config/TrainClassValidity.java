package com.stbella.order.server.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2023-03-16  14:21
 * @Description: 予家培训有效期配置
 * 对应： order-app.yml 配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "train-validity")
public class TrainClassValidity {
    /**
     * 线上有效期（月）
     */
    private Integer online;

    /**
     * 线下有效期（月）
     */
    private Integer offline;
}

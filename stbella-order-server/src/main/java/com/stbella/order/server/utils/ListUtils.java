package com.stbella.order.server.utils;

import java.util.ArrayList;
import java.util.List;

public class ListUtils {

    public static List<String> rangeList(List<Integer> nums) {
        List<String> ranges = new ArrayList<>();
        if (nums == null || nums.size() == 0) {
            return ranges;
        }
        int start = nums.get(0);
        int end = nums.get(0);
        for (int i = 1; i < nums.size(); i++) {
            if (nums.get(i) == end + 1) {
                end = nums.get(i);
            } else {
                ranges.add(start == end ? Integer.toString(start) : start + "-" + end);
                start = end = nums.get(i);
            }
        }
        ranges.add(start == end ? Integer.toString(start) : start + "-" + end);
        return ranges;
    }

}

package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.collection.CollectionUtil;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundApplyEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundApplyGoodsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.repository.OrderRefundApplyGoodsRepository;
import com.stbella.order.infrastructure.repository.converter.OrderRefundConverter;
import com.stbella.order.server.order.month.enums.PayMethodEnum;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SerializationUtils;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
@SnowballComponent(name = "根据子退款+退款商品均摊退款费用", desc = "根据子退款+退款商品均摊退款费用")
public class OrderRefundCapitationFeeAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderRefundConverter orderRefundConvert;
    @Resource
    private OrderRefundApplyGoodsRepository orderRefundApplyGoodsRepository;

    @Override
    public void run(FlowContext bizContext) {
        //主退款
        HeOrderRefundEntity parentRefund = bizContext.getAttribute(HeOrderRefundEntity.class);
        //子退款列表
        List<HeOrderRefundEntity> childRefundList = (List<HeOrderRefundEntity>) bizContext.getAttribute(OtherConstant.ORDER_REFUND_CHILD);
        //存储主退款
        HeOrderRefundApplyEntity heOrderRefundApplyEntity = saveParent(parentRefund);
        //处理子退款+均摊
        saveChild(heOrderRefundApplyEntity, childRefundList, bizContext);
    }

    private HeOrderRefundApplyEntity saveParent(HeOrderRefundEntity parentRefund) {
        HeOrderRefundApplyEntity orderRefundApplyEntity = orderRefundConvert.refundEntity2RefundApplyEntity(parentRefund);
        orderRefundApplyEntity.setRefundVoucherUrls(parentRefund.getRefundInfo());
        orderRefundApplyEntity.setRefundSn(parentRefund.getRefundOrderSn());
        orderRefundApplyEntity.setRefundBackType(parentRefund.getRefundMethod());
        orderRefundApplyEntity.setActualAmount(parentRefund.getActualAmount());
        orderRefundApplyEntity.save();
        return orderRefundApplyEntity;
    }

    private List<HeOrderRefundApplyGoodsEntity> saveChild(HeOrderRefundApplyEntity parentRefund, List<HeOrderRefundEntity> childRefundList, FlowContext bizContext) {
        //请求
        CreateRefundReq createRefundReq = bizContext.getAttribute(CreateRefundReq.class);
        //退款商品列表
        ArrayList<CreateRefundReq.GoodsInfo> goodsInfoList = SerializationUtils.clone((ArrayList<CreateRefundReq.GoodsInfo>) createRefundReq.getGoodsInfoList());
        //订单商品
        List<HeOrderGoodsEntity> orderGoodsList = bizContext.getListAttribute(HeOrderGoodsEntity.class);

        List<HeOrderRefundApplyGoodsEntity> orderRefundApplyGoodsEntityList = new ArrayList<>();

        for (PayMethodEnum value : PayMethodEnum.values()) {


            for (HeOrderRefundEntity orderRefundEntity : childRefundList.stream().filter(c -> PayMethodEnum.PayType2Currency(c.getRefundType()).equals(value)).collect(Collectors.toList())) {
                //这笔退款的金额
                BigDecimal refundApply = RMBUtils.bigDecimalF2Y(orderRefundEntity.getApplyAmount());

                for (CreateRefundReq.GoodsInfo goodsInfo : goodsInfoList) {

//                    if (refundApply.compareTo(BigDecimal.ZERO) <= 0) {
//                        break;
//                    }

                    //商品退款数量
                    Integer refundNum = goodsInfo.getRefundNum();
                    //商品退款金额
                    BigDecimal refundAmount = BigDecimal.ZERO;

                    Optional<CreateRefundReq.GoodsRefundAmountInfo> first = goodsInfo.getRefundGoodsAmountInfoList().stream().filter(g -> g.getModel().equals(value.getModel())).findFirst();
                    if (first.isPresent()) {
                        refundAmount = first.get().getAmount();
                    }

                    if (refundNum <= 0 && refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
                        continue;
                    }

                    HeOrderRefundApplyGoodsEntity heOrderRefundApplyGoodsEntity = new HeOrderRefundApplyGoodsEntity();

                    if (refundAmount.compareTo(BigDecimal.ZERO) <= 0 && refundNum > 0) {
                        heOrderRefundApplyGoodsEntity.setApplyNum(refundNum);
                        heOrderRefundApplyGoodsEntity.setActualNum(refundNum);
                        heOrderRefundApplyGoodsEntity.setApplyAmount(RMBUtils.YTFInt(BigDecimal.ZERO));
                        heOrderRefundApplyGoodsEntity.setActualAmount(RMBUtils.YTFInt(BigDecimal.ZERO));
                        goodsInfo.setRefundNum(0);
                    } else {
                        //这笔退款的金额大于商品的退款金额
                        assert refundApply != null;
                        if (refundApply.compareTo(refundAmount) >= 0) {
                            heOrderRefundApplyGoodsEntity.setApplyNum(refundNum);
                            heOrderRefundApplyGoodsEntity.setActualNum(refundNum);
                            heOrderRefundApplyGoodsEntity.setApplyAmount(RMBUtils.YTFInt(refundAmount));
                            heOrderRefundApplyGoodsEntity.setActualAmount(RMBUtils.YTFInt(refundAmount));
                            goodsInfo.setRefundAmount(BigDecimal.ZERO);
                            goodsInfo.setRefundNum(0);

                            refundApply = refundApply.subtract(refundAmount);

                        } else {
                            //商品退款的金额小于退款记录的金额
                            heOrderRefundApplyGoodsEntity.setApplyNum(goodsInfo.getGoodsNum());
                            heOrderRefundApplyGoodsEntity.setActualNum(goodsInfo.getGoodsNum());
                            heOrderRefundApplyGoodsEntity.setApplyAmount(RMBUtils.YTFInt(refundApply));
                            heOrderRefundApplyGoodsEntity.setActualAmount(RMBUtils.YTFInt(refundApply));
                            goodsInfo.setRefundAmount(refundAmount.subtract(refundApply));
                            goodsInfo.setRefundNum(0);

                            refundApply = BigDecimal.ZERO;
                        }
                    }

                    Optional<HeOrderGoodsEntity> goodsFirst = orderGoodsList.stream().filter(o -> o.getOrderGoodsSn().equals(goodsInfo.getOrderGoodsSn())).findFirst();

                    if (goodsFirst.isPresent() && (refundNum > 0 || refundAmount.compareTo(BigDecimal.ZERO) > 0)) {
                        HeOrderGoodsEntity orderGoodsEntity = goodsFirst.get();
                        heOrderRefundApplyGoodsEntity.setRefundApplyId(parentRefund.getId());
                        heOrderRefundApplyGoodsEntity.setRefundId(orderRefundEntity.getId());
                        heOrderRefundApplyGoodsEntity.setGoodsType(orderGoodsEntity.getGoodsType());
                        heOrderRefundApplyGoodsEntity.setAssetType(orderGoodsEntity.getGoodsType());
                        heOrderRefundApplyGoodsEntity.setGoodsId(orderGoodsEntity.getGoodsId());
                        heOrderRefundApplyGoodsEntity.setSkuId(orderGoodsEntity.getSkuId());
                        heOrderRefundApplyGoodsEntity.setRefundSn(orderRefundEntity.getRefundOrderSn());
                        heOrderRefundApplyGoodsEntity.setOrderGoodsSn(orderGoodsEntity.getOrderGoodsSn());
                        heOrderRefundApplyGoodsEntity.setAddTime(new Date());
                        heOrderRefundApplyGoodsEntity.setUpdateTime(new Date());
                        orderRefundApplyGoodsEntityList.add(heOrderRefundApplyGoodsEntity);
                    }
                }
            }
        }
        if (CollectionUtil.isNotEmpty(orderRefundApplyGoodsEntityList)) {
            orderRefundApplyGoodsRepository.saveList(orderRefundApplyGoodsEntityList);
        }
        return orderRefundApplyGoodsEntityList;
    }

    @Override
    public boolean condition(FlowContext context) {
        return (boolean) context.getAttribute(OtherConstant.CONTINUE);
    }
}

package com.stbella.order.server.manager;

import com.alibaba.fastjson.JSONObject;
import com.stbella.core.result.Result;
import com.stbella.marketing.api.ActivityCommandService;
import com.stbella.marketing.api.ActivityManageService;
import com.stbella.marketing.api.req.ActivityOrderCreateReportRequest;
import com.stbella.marketing.api.req.ActivityRuleRequest;
import com.stbella.marketing.api.req.goods.ActivityGoodsReq;
import com.stbella.marketing.api.res.OrderBenefitVO;
import com.stbella.marketing.api.res.OrderProductBenefitVO;
import com.stbella.marketing.api.res.goods.ActivityMinProgramDetailVO;
import com.stbella.marketing.api.service.ActivityQueryService;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import com.stbella.platform.order.api.res.PromotionInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.pulsar.shade.com.google.common.collect.Sets;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 同步参与活动的数据到活动中心
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class MarketingManager {

    @DubboReference
    private ActivityManageService activityManageService;
    @DubboReference
    private ActivityCommandService activityCommandService;
    @DubboReference
    private ActivityQueryService activityQueryService;

    public Integer fetchServiceRoomDays(List<SkuDetailInfo> skuDetailInfos) {
        Integer totalNum = 0;
        for (SkuDetailInfo skuDetailInfo : skuDetailInfos) {
            if (BizConstant.OrderAppKey.ASSET_LIST_ROOM_SERVICE.contains(skuDetailInfo.getGoodsType())) {
                totalNum = totalNum + (skuDetailInfo.getNum() * skuDetailInfo.getSkuNum());
            }
            List<SkuDetailInfo> subList = skuDetailInfo.getSubList();
            if (CollectionUtils.isEmpty(subList)) {
                continue;
            }
            for (SkuDetailInfo detailInfo : subList) {
                if (BizConstant.OrderAppKey.ASSET_LIST_ROOM_SERVICE.contains(detailInfo.getGoodsType())) {
                    totalNum = totalNum + (detailInfo.getNum() * detailInfo.getSkuNum() * skuDetailInfo.getNum());
                }
            }
        }

        return totalNum;

    }

    /**
     * 查询商品命中的活动
     *
     * @param clientId
     * @param storeId
     * @param skuDetailInfos
     * @param scene
     * @return
     */
    public List<ActivityMinProgramDetailVO> queryStoreActivity(Integer clientId, Integer storeId, List<SkuDetailInfo> skuDetailInfos, Integer scene) {
        ActivityRuleRequest request = new ActivityRuleRequest();
        request.setClientId(clientId);
        request.setStoreId(storeId);
        request.setOrderScene(scene);
        Integer orderIncludeDays = fetchServiceRoomDays(skuDetailInfos);
        log.info("orderIncludeDays: {}", orderIncludeDays);
        request.setOrderIncludeDays(orderIncludeDays);
        List<ActivityGoodsReq> goodsList = new ArrayList<>();
        for (SkuDetailInfo skuDetailInfo : skuDetailInfos) {
            ActivityGoodsReq activityGoodsReq = new ActivityGoodsReq();
            activityGoodsReq.setGoodsId(skuDetailInfo.getGoodsId());
            activityGoodsReq.setSkuId(skuDetailInfo.getSkuId());
            List<SkuDetailInfo> subList = skuDetailInfo.getSubList();
            if (CollectionUtils.isNotEmpty(subList)) {
                for (SkuDetailInfo detailInfo : subList) {
                    ActivityGoodsReq subGoodsReq = new ActivityGoodsReq();
                    subGoodsReq.setGoodsId(detailInfo.getGoodsId());
                    subGoodsReq.setSkuId(detailInfo.getSkuId());
                    subGoodsReq.setParentId(skuDetailInfo.getGoodsId());
                    goodsList.add(subGoodsReq);
                }
            }
            goodsList.add(activityGoodsReq);
        }
        request.setGoodsList(goodsList);
        log.info("查询商品命中活动请求参数:{}", JSONObject.toJSONString(request));
        Result<List<ActivityMinProgramDetailVO>> listResult = activityManageService.queryStoreActivity(request);
        log.info("查询商品命中活动请求结果:{}", JSONObject.toJSONString(listResult));
        return listResult.getData();
    }


    public List<ActivityMinProgramDetailVO> queryStoreActivityByReq(ActivityRuleRequest request) {
        log.info("查询商品命中活动请求参数:{}", JSONObject.toJSONString(request));
        Result<List<ActivityMinProgramDetailVO>> listResult = activityManageService.queryStoreActivity(request);
        log.info("查询商品命中活动请求结果:{}", JSONObject.toJSONString(listResult));
        return listResult.getData();
    }


    /**
     * 同步订单到活动中心
     *
     * @param orderEntity
     * @param promotionInfos
     */
    public void syncOrder(HeOrderEntity orderEntity, List<PromotionInfo> promotionInfos) {
        ArrayList<ActivityOrderCreateReportRequest> request = new ArrayList<>();
        promotionInfos.stream().map(PromotionInfo::getPromotionId).distinct().forEach(promotionId -> {
            ActivityOrderCreateReportRequest activityOrderCreateReportRequest = new ActivityOrderCreateReportRequest();
            activityOrderCreateReportRequest.setActivityId(promotionId);
            activityOrderCreateReportRequest.setClientId(orderEntity.getClientUid());
            activityOrderCreateReportRequest.setStoreId(orderEntity.getStoreId());
            activityOrderCreateReportRequest.setBasicId(orderEntity.getBasicUid());
            activityOrderCreateReportRequest.setRecordId(orderEntity.getOrderSn());
            request.add(activityOrderCreateReportRequest);
        });
        log.info("同步到活动中心请求参数:{}", JSONObject.toJSONString(request));
        Result<Boolean> booleanResult = activityCommandService.activityParticipationReport(request);
        log.info("同步到活动中心请求结果:{}", JSONObject.toJSONString(booleanResult));
    }

    public List<OrderBenefitVO> queryOrderBenefitList(List<Integer> orderIdList) {
        Result<List<OrderBenefitVO>> listResult = activityQueryService.queryActivityByOrderIds(orderIdList);
        return listResult.getData();
    }

    public List<OrderProductBenefitVO> queryProductGiftByOrderId(Integer orderId) {
        Result<List<OrderProductBenefitVO>> listResult = activityQueryService.queryOrderProductBenefit(orderId);
        List<OrderProductBenefitVO> data = listResult.getData();
        log.info("查询订单商品赠品信息结果:{}", JSONObject.toJSONString(data));
        return data;

    }
}

package com.stbella.order.server.context.component.contract.checker;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.stbella.contract.common.constant.BizConstant;
import com.stbella.contract.model.enums.ContractStatusEnum;
import com.stbella.contract.model.enums.ContractTypeEnum;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.enums.month.OrderSignTypeEnum;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-23 15:33
 */
@Component
@Slf4j
public class DiscountContractCreateValidator extends AbstractContractCreateValidator {


    @Override
    public boolean doValidate(ContractValidationContext context) {
        HeOrderEntity orderEntity = context.getOrderEntity();
        //新版本订单处理
        if (!orderEntity.isNewOrder()) {
            return true;
        }
        List<OrderContractSignRecordVO> contractSignRecordEntities = context.getSignRecords();
        Optional<OrderContractSignRecordVO> first = contractSignRecordEntities.stream().filter(i -> ListUtil.toList(ContractTypeEnum.ESIGN_TYPE.code(), ContractTypeEnum.OLD_TYPE.code(), ContractTypeEnum.PAPER_TYPE.code()).contains(i.getContractType())).filter(item -> Objects.equals(TemplateContractTypeEnum.ENTRUST.code(), item.getTemplateContractType()) && Objects.equals(ContractStatusEnum.SIGNED.code(), item.getContractStatus())).findFirst();

        if (!first.isPresent() && OrderSignTypeEnum.SIGN_TYPE_BAILOR.code().equals(orderEntity.getSignType())) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "请先签署授权委托书");
        }
        validateMainContractSigned(context);
        checkPaperContract(context);
        return true;
    }

    @Override
    public TemplateContractTypeEnum templateContractType() {
        return TemplateContractTypeEnum.DISCOUNT;
    }
}
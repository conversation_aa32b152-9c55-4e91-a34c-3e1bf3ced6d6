package com.stbella.order.server.manager;

import com.stbella.customer.server.ecp.dto.HeUserCardDTO;
import com.stbella.customer.server.ecp.entity.HeUserCardPO;
import com.stbella.customer.server.ecp.service.HeUserCardService;

import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 卡包
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-01 14:59
 */
@Component
@Slf4j
public class UserCardManager {

    @DubboReference
    private HeUserCardService userCardService;

    public Long saveUserCardInfo(HeUserCardDTO userCardDTO) {
        log.info("更新卡包信息{}", JSONUtil.toJsonStr(userCardDTO));
        Long aLong = userCardService.saveUserCardInfo(userCardDTO);
        log.info("更新卡包信息结果user_card主键{}", aLong);
        return aLong;
    }

    /**
     * 查询用户身份证信息
     * @param basicUid
     * @return
     */
    public HeUserCardPO queryUserCard(Integer basicUid) {
        log.info("查询用户证件信息{}", basicUid);
        HeUserCardPO userCardDTO = userCardService.queryUserCardInfoForOrder(basicUid);
        return userCardDTO;
    }
}

package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.month.server.request.RefundApprovalRequest;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.order.service.OrderRefundDomainService;
import com.stbella.order.domain.repository.OrderRefundRepository;
import com.stbella.order.server.order.month.request.approval.ApprovalStatusInfo;
import com.stbella.order.server.order.month.utils.RMBUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 退款审批通过
 * 1、发送短信
 * 2、减去冻结金额
 * 3、发起真正退款
 */
@Component
@Slf4j
@SnowballComponent(name = "审批结束操作短信", desc = "审批结束操作短信")
public class OrderRefundAfterApproveSMSAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private OrderRefundDomainService orderRefundDomainService;

    @Override
    public boolean condition(FlowContext context) {
        return (boolean) context.getAttribute(OtherConstant.CONTINUE);
    }

    @Override
    public void run(FlowContext bizContext) {
        log.info("审批结束操作短信...");
        //审批发起
        RefundApprovalRequest refundApprovalRequest = bizContext.getAttribute(RefundApprovalRequest.class);
        //审批状态
        ApprovalStatusInfo approvalStatusInfo = bizContext.getAttribute(ApprovalStatusInfo.class);
        //获取审批状态
        HeOrderRefundEntity heOrderRefundEntity = orderRefundRepository.getOneById(refundApprovalRequest.getOrderRefundId());

        log.info("主退款refundSn为 {} 发送审批结果短信", heOrderRefundEntity.getRefundOrderSn());

        bizContext.setAttribute(HeOrderRefundEntity.class, heOrderRefundEntity);

        List<HeOrderRefundEntity> refundByParentSn = orderRefundRepository.getRefundByParentSn(heOrderRefundEntity.getRefundOrderSn());
        bizContext.setListAttribute(HeOrderRefundEntity.class, refundByParentSn);

        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        Boolean deposit = orderEntity.isDepositOrder();

        if (ObjectUtil.isEmpty(heOrderRefundEntity)) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "审批完成退款失败,找不到对应的退款记录,退款ID=" + refundApprovalRequest.getOrderRefundId());
        }
        Boolean isRefundAgree = approvalStatusInfo.isAgreeAndFinish();
        log.info("是否是押金:{}", deposit);
        if (!deposit) {
            try {
                BigDecimal refundAmount = RMBUtils.bigDecimalF2Y(refundByParentSn.stream().mapToInt(HeOrderRefundEntity::getApplyAmount).sum());
                //发起人手机号
                String originatorPhone = refundApprovalRequest.getPhone();
                //非押金
                orderRefundDomainService.sendRefundAfterApproveByAmount(isRefundAgree, originatorPhone, refundApprovalRequest.getCustomerName(), refundApprovalRequest.getOrderId(), heOrderRefundEntity, refundAmount);
            } catch (Exception e) {
                log.error("审批通过短信发送失败:{}", e.getMessage());
            }
        }
        bizContext.setAttribute(OtherConstant.IS_REFUND, isRefundAgree);
    }
}

package com.stbella.order.server.context.component.processor;

import cn.hutool.core.collection.CollectionUtil;
import com.stbella.asset.api.enums.TradeEventEnum;
import com.stbella.asset.api.enums.TradeType;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.repository.OrderGoodsRepository;
import com.stbella.order.server.manager.AssetManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
@SnowballComponent(name = "资产生效", desc = "根据累计累计支付记录总额，订单业绩生效及订单全额支付")
public class AssetGrantProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private AssetManager assetManager;

    @Resource
    private OrderGoodsRepository orderGoodsRepository;

    @Override
    public boolean condition(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        return Objects.nonNull(orderEntity) && orderEntity.isJustEffect();
    }

    @Override
    public void run(FlowContext bizContext) {
        log.info("赠送订单资产生效");
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        log.info("订单id:{}，sn:{}，执行多笔交易", orderEntity.getOrderId(), orderEntity.getOrderSn());
        List<HeOrderGoodsEntity> orderGoodsEntityList = orderGoodsRepository.getByOrderIdList(Collections.singletonList(orderEntity.getOrderId()));
        if (CollectionUtil.isEmpty(orderGoodsEntityList)){
            log.error("订单id:{}，sn:{}，没有商品", orderEntity.getOrderId(), orderEntity.getOrderSn());
            return;
        }
        List<HeOrderGoodsEntity> heOrderGoodsEntityList = orderGoodsEntityList.stream().filter(orderGoodsEntity -> !CombineTypeEnum.COMBINE.code().equals(orderGoodsEntity.getType())).collect(Collectors.toList());
        assetManager.execMultiUserTrade(heOrderGoodsEntityList, orderEntity, TradeEventEnum.PRODUCTION_GOODS_GIFT, 3, orderEntity.getOrderId().toString());
    }
}

package com.stbella.order.server.convert;

import com.stbella.order.domain.order.month.entity.IncomePaidAllocationEntity;
import com.stbella.order.server.context.dto.OrderGoodsAllocationDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2023-07-27  16:19
 * @Description: 支付分摊
 */
@Mapper(componentModel = "spring")
public interface PaidAllocationConverter {

    /**
     * dto 转 entity
     *
     * @param model
     * @return
     */
    @Mappings({
            @Mapping(target = "paidAmount", source = "allocation"),
            @Mapping(target = "orderGoodsSn", source = "orderGoodsSn"),
            @Mapping(target = "orderGoodsId", expression = "java(model.getOrderGoodsId().longValue())"),
    })
    IncomePaidAllocationEntity dto2Entity(OrderGoodsAllocationDto model);

    /**
     * dto 转 entity list
     *
     * @param modelList
     * @return
     */
    List<IncomePaidAllocationEntity> dto2EntityList(List<OrderGoodsAllocationDto> modelList);

}

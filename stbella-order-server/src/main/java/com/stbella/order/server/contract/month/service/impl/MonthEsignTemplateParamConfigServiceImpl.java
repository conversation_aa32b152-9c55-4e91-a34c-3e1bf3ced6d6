package com.stbella.order.server.contract.month.service.impl;

import com.stbella.order.domain.order.month.entity.MonthEsignTemplateParamConfigEntity;
import com.stbella.order.domain.repository.MonthEsignTemplateParamConfigRepository;
import com.stbella.order.server.contract.service.month.MonthEsignTemplateParamConfigService;
import com.stbella.order.server.convert.AppMonthContractSignRecordConverter;
import com.stbella.order.server.order.month.res.MonthEsignTemplateParamConfigVO;

import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 合同参数模版配置表,新版E签宝 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2021-11-04
 */
@Service
@Slf4j
public class MonthEsignTemplateParamConfigServiceImpl implements MonthEsignTemplateParamConfigService {

    @Resource
    private MonthEsignTemplateParamConfigRepository monthEsignTemplateParamConfigRepository;

    @Resource
    private AppMonthContractSignRecordConverter appMonthContractSignRecordConverter;
    @Override
    public List<MonthEsignTemplateParamConfigVO> getList(Integer orderType) {
        List<MonthEsignTemplateParamConfigEntity> list = monthEsignTemplateParamConfigRepository.getList(orderType);
        return appMonthContractSignRecordConverter.entity2MonthEsignTemplateParamConfigVoList(list);
    }

}

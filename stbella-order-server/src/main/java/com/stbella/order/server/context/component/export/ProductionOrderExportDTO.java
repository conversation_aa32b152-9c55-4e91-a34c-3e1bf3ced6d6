package com.stbella.order.server.context.component.export;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2023-07-25  14:43
 * @Description: 产康订单导出数据
 */
@Data
public class ProductionOrderExportDTO implements Serializable {

    @ApiModelProperty(value = "会所门店")
    @ExcelProperty(value = "会所门店")
    private String storeName;


    @ExcelProperty(value = "订单号")
    @ApiModelProperty(value = "订单号")
    private String orderSn;



    @ApiModelProperty(value = "宝妈姓名")
    @ExcelProperty(value = "宝妈姓名")
    private String clientName;

    @ApiModelProperty(value = "关联订单类型")
    @ExcelProperty(value = "关联订单类型")
    private String orderTypeName;

    @ApiModelProperty(value = "订单属性(购买，赠送)")
    @ExcelProperty(value = "订单属性")
    private String orderSellType;

    @ApiModelProperty(value = "服务类型(单，卡)")
    @ExcelProperty(value = "服务类型")
    private String serviceType;

    @ApiModelProperty(value = "服务类型(单，卡) 0=次卡 1=通卡 2=单项服务")
    @ExcelIgnore
    private Integer serviceTypeCode;

    @ApiModelProperty(value = "资产状态")
    @ExcelProperty(value = "资产状态")
    private String assetStatus;

    @ApiModelProperty(value = "资产状态码")
    @ExcelIgnore
    private Integer assetStatusCode;


    @ApiModelProperty(value = "产康项目")
    @ExcelProperty(value = "产康项目")
    private String productionName;

    @ApiModelProperty(value = "产康卡项组合")
    @ExcelProperty(value = "产康卡项组合")
    private String productionGroupName;

    /**
     *  子项目 产康金抵扣 0）不可抵扣 1)可抵扣
     */
    @ExcelIgnore
    private Integer childProduceAmountDeduction;

    /**
     * 子项目 服务类型 0:自营 1:三方
     */
    @ExcelIgnore
    private Integer childServiceType;

    /**
     *  子项目 是否参与折扣 0）参数 1)不参与
     */
    @ExcelIgnore
    private Integer childIsFullMoonType;

    @ApiModelProperty(value = "订单创建时间")
    @ExcelProperty(value = "订单创建时间")
    private String orderTime;

    @ApiModelProperty(value = "订单创建时间 10位时间戳")
    @ExcelIgnore
    private Integer createdAt;

    @ApiModelProperty(value = "付款时间")
    @ExcelProperty(value = "付款时间")
    private String percentFirstTime;

    @ApiModelProperty(value = "资产生效时间")
    @ExcelProperty(value = "资产生效时间")
    private String assetStartTime;

    @ApiModelProperty(value = "订单原价")
    @ExcelProperty(value = "订单原价")
    private String totalPayAmount;

    @ApiModelProperty(value = "签单金额")
    @ExcelProperty(value = "签单金额")
    private String payAmount;

    @ApiModelProperty(value = "付款金额，真实付款金额")
    @ExcelProperty(value = "付款金额")
    private String paidAmount;

    @ApiModelProperty(value = "产康金抵扣金额")
    @ExcelProperty(value = "产康金抵扣金额")
    private String productionAmount;

    @ApiModelProperty(value = "sku单价")
    @ExcelIgnore
    private String skuPrice;

    @ApiModelProperty(value = "sku售价")
    @ExcelIgnore
    private String skuSellingPrice;

    @ApiModelProperty(value = "sku产康金抵扣金额")
    @ExcelIgnore
    private String skuProductionAmount;

    @ApiModelProperty(value = "sku支付金额")
    @ExcelIgnore
    private String skuPayAmount;



    @ApiModelProperty(value = "子项sku单价")
    @ExcelProperty(value = "子项sku单价")
    private String childSkuPrice;

    @ApiModelProperty(value = "子项sku售价")
    @ExcelProperty(value = "子项sku售价")
    private String childSkuSellingPrice;

    @ApiModelProperty(value = "子项sku产康金抵扣金额")
    @ExcelProperty(value = "单次产康金")
    private String childSkuProductionAmount;

    @ApiModelProperty(value = "单次支付金额")
    @ExcelProperty(value = "单次支付金额")
    private String childSkuPayAmount;

    /**
     * 末项skuId
     * 卡项里的skuId
     */
    @ExcelProperty(value = "末项skuId")
    private Integer childSkuId;

    @ApiModelProperty(value = "项目次数")
    @ExcelProperty(value = "项目次数")
    private String num;

    @ApiModelProperty(value = "已核销次数")
    @ExcelIgnore
    private String usedNum;

    @ApiModelProperty(value = "累计核销")
    @ExcelProperty(value = "累计核销")
    private Integer totalCheckoutNum;


    @ApiModelProperty(value = "待核销次数")
    @ExcelProperty(value = "待核销")
    private String residueNum;

    @ApiModelProperty(value = "当月核销次数")
    @ExcelProperty(value = "当月核销次数")
    private String lastNum;

    @ApiModelProperty(value = "李晨当月核销次数")
    @ExcelProperty(value = "李晨当月核销次数")
    private String specificCheckoutNum;

    @ApiModelProperty(value = "核销执行人")
    @ExcelProperty(value = "核销执行人")
    private String checkoutUserName;


    @ApiModelProperty(value = "核销时间")
    @ExcelProperty(value = "核销时间")
    private String checkoutTime;

    @ApiModelProperty(value = "核销门店")
    @ExcelProperty(value = "核销门店")
    private String checkoutStoreName;

    @ApiModelProperty(value = "BasicID")
    @ExcelProperty(value = "BasicID")
    private String basicUid;

    @ApiModelProperty(value = "用户clientUid")
    @ExcelProperty(value = "用户clientUid")
    @ExcelIgnore
    private Integer clientUid;

    @ApiModelProperty(value = "产康项目类型(自营，三方)")
    @ExcelProperty(value = "产康项目类型")
    private String selfSupport;

    /**
     * 产康项目类型(自营，三方)
     * @se ProductionServeTypeEnum
     */
    @ExcelIgnore
    private Integer selfSupportCode;


    @ApiModelProperty(value = "核销门店id")
    @ExcelIgnore
    private Integer checkoutStoreId;



    @ApiModelProperty(value = "过期时间")
    @ExcelProperty(value = "过期时间")
    private String validEndTime;

    @ApiModelProperty(value = "核销备注")
    @ExcelProperty(value = "核销备注")
    private String verificationRemark;


    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称")
    private String supplierName;


    /**
     * 供应商id
     */
    @ExcelProperty(value = "供应商id")
    private String supplierId;

    /**
     * 结算比例
     */
    @ExcelProperty(value = "结算比例")
    private Integer scale;

    @ApiModelProperty(value = "clientPhone")
    @ExcelProperty(value = "clientPhone")
    @ExcelIgnore
    private String clientPhone;

    /**
     * 结算类型：0-指定结算价；1-指定结算比例
     */
    @ExcelProperty(value = "结算类型")
    private Integer settlementRatio;

    /**
     * 三方结算价
     */
    @ExcelProperty(value = "三方结算价")
    private String tripartiteSettlementPrice;

    @ExcelProperty(value = "三方结算比例")
    private Integer tripartiteSettlementRatio;

    /**
     * 卡项 内容 json
     */
    @ExcelIgnore
    private String cardContent;

    @ExcelProperty(value = "实际分摊")
    private String realPaid;

    /**
     * 组合编号，只能通卡有用，其他为0
     */
    @ExcelProperty(value = "groupId")
    @ExcelIgnore
    private Integer groupId;

    @ApiModelProperty(value = "会所门店id")
    @ExcelIgnore
    private Integer storeId;

    /**
     * 订单id
     */
    @ApiModelProperty(value = "orderId")
    @ExcelIgnore
    private Integer orderId;

    /**
     * 关联sku id
     */
    @ExcelProperty(value = "skuId")
    @ExcelIgnore
    private Integer skuId;

    /**
     * 关联产康订单商品表/ gift_extend表id
     */
    @ExcelProperty(value = "orderProductionId")
    @ExcelIgnore
    private Integer orderProductionId;

    /**
     * card extend id.
     */
    @ExcelProperty(value = "cardExtendId")
    @ExcelIgnore
    private Integer cardExtendId;

    /**
     * 末项skuId 对应 goodsId
     * 卡项里的skuId 对应 goodsId
     */
    @ExcelProperty(value = "childGoodsId")
    @ExcelIgnore
    private Integer childGoodsId;

    /**
     * 关联sku id 对应的goodsid
     */
    @ExcelProperty(value = "goodsId")
    @ExcelIgnore
    private Integer goodsId;

    /**
     * 组合可用次数（只有通卡有用）
     */
    @ExcelProperty(value = "goodsId")
    @ExcelIgnore
    private Integer groupShareCount;

    @ApiModelProperty(value = "产康卡项子项")
    @ExcelIgnore
    private String productionChildName;

    /**
     *数据来源：0表示正常，1表示后台核销补录
     */
    @ExcelProperty(value = "补录(1)")
    private Integer dataSource;

    /**
     *使用资产置换的产康金
     */
    @ExcelProperty(value = "使用资产置换的产康金")
    private Integer specialProductionAmount;

    @ExcelProperty(value = "免费产康金")
    private Long freeProductionAmount;

    @ExcelProperty(value = "有价产康金")
    private Long valuableProductionAmount;

    @ExcelProperty(value = "有价产康金价值")
    private String productionAmountValue;
}

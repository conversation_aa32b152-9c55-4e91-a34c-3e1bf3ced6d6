package com.stbella.order.server.order.job;

import com.stbella.order.domain.order.entity.HeCartEntity;
import com.stbella.order.domain.order.entity.HeCartGoodsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.HeCartGoodsRepository;
import com.stbella.order.domain.repository.HeCartRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.context.component.processor.FixGoodsAllocateProcessor;
import com.stbella.order.server.context.component.processor.performance.OrderAmountProcessor;
import com.stbella.platform.order.api.req.QueryCartReq;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.core.context.FlowContext;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 订单金额数据迁移服务
 * 用于将历史订单的金额数据迁移到新的持久化字段中
 */
@Slf4j
@Component
public class OrderAmountMigrationHandler {

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private OrderAmountProcessor orderAmountProcessor;

    @Resource
    private HeCartRepository heCartRepository;

    @Resource
    private HeCartGoodsRepository heCartGoodsRepository;

    @Resource
    private FixGoodsAllocateProcessor fixGoodsAllocateProcessor;

    /**
     * 批量迁移历史订单金额数据 - 多线程版本
     */
    @XxlJob("OrderAmountMigrationHandler")
    public void migrateHistoricalOrderAmounts() {
        log.info("开始迁移历史订单金额数据");

        int pageSize = 1000;
        int offset = 0;
        AtomicInteger totalProcessed = new AtomicInteger(0);
        AtomicInteger totalErrors = new AtomicInteger(0);

        // 创建线程池，使用CPU核心数的2倍作为线程数
        int threadPoolSize = Runtime.getRuntime().availableProcessors() * 6;
        ExecutorService executor = Executors.newFixedThreadPool(threadPoolSize);

        log.info("使用线程池大小: {}", threadPoolSize);

        try {
            while (true) {
                List<HeOrderEntity> orders = orderRepository.queryOrdersForMigration(offset, pageSize);
                if (CollectionUtils.isEmpty(orders)) {
                    log.info("数据迁移完毕");
                    break;
                }

                // 创建异步任务列表
                List<CompletableFuture<Void>> futures = orders.stream().map(order -> CompletableFuture.runAsync(() -> processOrder(order, totalProcessed, totalErrors), executor)).collect(Collectors.toList());

                // 等待当前批次的所有任务完成
                CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

                try {
                    allOf.join();
                } catch (Exception e) {
                    log.error("批次处理过程中发生异常", e);
                }

                offset += pageSize;

                // 每处理一个批次后输出进度
                log.info("已处理批次数据，当前总处理订单数: {}, 错误数: {}", totalProcessed.get(), totalErrors.get());
            }

        } finally {
            // 关闭线程池
            executor.shutdown();
            try {
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                    if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                        log.error("线程池未能正常关闭");
                    }
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        log.info("订单金额数据迁移完成，总处理订单数: {}, 错误数: {}", totalProcessed.get(), totalErrors.get());
    }

    /**
     * 处理单个订单的迁移
     */
    private void processOrder(HeOrderEntity order, AtomicInteger totalProcessed, AtomicInteger totalErrors) {
        try {
            FlowContext flowContext = new FlowContext();
            flowContext.setAttribute(HeOrderEntity.class, order);
            try {
                fixGoodsAllocateProcessor.run(flowContext);
            } catch (Exception e) {
                log.error("修复商品分摊失败, orderId={}, orderSn={}", order.getOrderId(), order.getOrderSn(), e);
            }

            orderAmountProcessor.run(flowContext);

            QueryCartReq queryReq = new QueryCartReq();
            queryReq.setOrderId(order.getOrderId());
            HeCartEntity heCartEntity = heCartRepository.queryOne(queryReq);
            if (Objects.nonNull(heCartEntity)) {
                List<HeCartGoodsEntity> heCartGoodsEntities = heCartGoodsRepository.queryList(heCartEntity.getCartId());
                if (CollectionUtils.isNotEmpty(heCartGoodsEntities)) {
//                    heCartGoodsEntities.forEach(heCartGoodsEntity -> {
//                        heCartGoodsEntity.setGoodsPrice(heCartGoodsEntity.getGoodsPrice() * heCartGoodsEntity.getNum());
//                        heCartGoodsRepository.update(heCartGoodsEntity);
//                    });
                }
            }

            int processed = totalProcessed.incrementAndGet();
            if (processed % 100 == 0) {
                log.info("已处理订单数量: {}", processed);
            }
        } catch (Exception e) {
            totalErrors.incrementAndGet();
            log.error("迁移订单金额失败, orderId={}, orderSn={}", order.getOrderId(), order.getOrderSn(), e);
        }
    }


}

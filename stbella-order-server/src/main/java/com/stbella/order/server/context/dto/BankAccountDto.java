package com.stbella.order.server.context.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class BankAccountDto implements Serializable {

    private  Integer  id;

    @ApiModelProperty(value = "账号")
    private   String accountNo;

    @ApiModelProperty(value = "账户名称")
    private   String accountName;

    @ApiModelProperty(value = "英文账户名称")
    private   String accountEnName;

    @ApiModelProperty(value = "开户行名称")
    private   String openBank;

    @ApiModelProperty(value = "银行类型")
    private   String bankType;

    @ApiModelProperty(value = "银行类型")
    private   String bankTypeName;

    @ApiModelProperty(value = "币种")
    private   String currencyName;

    @ApiModelProperty(value = "币种")
    private   Integer currency;

    @ApiModelProperty(value = "账户性质名称")
    private   String accountNatureName;

    @ApiModelProperty(value = "账户标识")
    private   String accountFlag;

    @ApiModelProperty(value = "账户标识")
    private   String accountFlagName;

    @ApiModelProperty(value = "存款类型:1-活期，2-协定，3-定期，4-通知")
    private   Integer  depositType;

    @ApiModelProperty(value = "存款类型:1-活期，2-协定，3-定期，4-通知")
    private  String depositTypeStr;

    @ApiModelProperty(value = "银企直联标志:0显示未开通， 9显示开通")
    private   Integer  directConnectFlag;

    @ApiModelProperty(value = "银企直联标志:0显示未开通， 9显示开通")
    private   String  directConnectFlagStr;

    @ApiModelProperty(value = "账户状态：0-正常，1-销户")
    private   Integer  accountStatus;

    @ApiModelProperty(value = "账户状态：0-正常，1-销户")
    private   String  accountStatusStr;

    @ApiModelProperty(value = "销户日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private   Date cancelDate;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private   Date   updateDate;

    @ApiModelProperty(value = "创建时间")
    private   Date   createdAt;

    @ApiModelProperty(value = "更新时间")
    private   Date   updatedAt;

    @ApiModelProperty(value = "科目名称")
    private String subjectName;

    @ApiModelProperty(value = "科目编码")
    private String subjectCode;



}

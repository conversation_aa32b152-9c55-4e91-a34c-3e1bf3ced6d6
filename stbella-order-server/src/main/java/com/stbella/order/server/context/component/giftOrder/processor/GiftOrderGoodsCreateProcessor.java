package com.stbella.order.server.context.component.giftOrder.processor;

import cn.hutool.json.JSONUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.order.OrderGiftStatusEnum;
import com.stbella.order.domain.client.IdGenerator;
import com.stbella.order.domain.order.month.entity.HeOrderGiftExtendEntity;
import com.stbella.order.domain.repository.HeOrderGiftInfoRepository;
import com.stbella.order.server.order.month.req.HeOrderGiftInfoGoodsAddReq;
import com.stbella.order.server.order.month.res.HeOrderGiftInfoGoodsAddVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Component
@SnowballComponent(name = "创建礼赠订单", desc = "礼赠订单创建处理逻辑")
public class GiftOrderGoodsCreateProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private HeOrderGiftInfoRepository heOrderGiftInfoRepository;

    @Resource
    private IdGenerator idGenerator;

    @Override
    public void run(FlowContext bizContext) {

        log.info("创建礼赠订单处理逻辑开始");
        HeOrderGiftInfoGoodsAddReq heOrderGiftInfoGoodsAddReq = bizContext.getAttribute(HeOrderGiftInfoGoodsAddReq.class);

        HeOrderGiftExtendEntity heOrderGiftExtendEntity = this.createHeOrderGiftExtendEntity(heOrderGiftInfoGoodsAddReq);
        Long save = heOrderGiftInfoRepository.save(heOrderGiftExtendEntity);
        if (save == 0){
            throw new BusinessException(String.valueOf(ErrorCodeEnum.DB_ERROR.code()), "添加礼赠订单失败");
        }
        heOrderGiftExtendEntity.setId(save);
        bizContext.setAttribute(HeOrderGiftExtendEntity.class, heOrderGiftExtendEntity);
        bizContext.setAttribute(HeOrderGiftInfoGoodsAddVO.class, HeOrderGiftInfoGoodsAddVO.builder().id(save).build());
    }

    private HeOrderGiftExtendEntity createHeOrderGiftExtendEntity(HeOrderGiftInfoGoodsAddReq heOrderGiftInfoGoodsAddReq) {

        HeOrderGiftExtendEntity heOrderGiftExtendEntity = new HeOrderGiftExtendEntity();
        heOrderGiftExtendEntity.setGiftSn(idGenerator.geneStringIdForLocal("GIFT"));
        heOrderGiftExtendEntity.setOrderId(heOrderGiftInfoGoodsAddReq.getOrderId());
        heOrderGiftExtendEntity.setOrderSn(heOrderGiftInfoGoodsAddReq.getOrderSn());
        heOrderGiftExtendEntity.setOrderType(heOrderGiftInfoGoodsAddReq.getOrderType());
        heOrderGiftExtendEntity.setStatus(OrderGiftStatusEnum.APPROVAL.getCode());
        heOrderGiftExtendEntity.setLinkUrl(heOrderGiftInfoGoodsAddReq.getLinkUrl());
        heOrderGiftExtendEntity.setGiftReasonType(heOrderGiftInfoGoodsAddReq.getGiftReasonType());
        heOrderGiftExtendEntity.setStoreId(heOrderGiftInfoGoodsAddReq.getStoreId());
        heOrderGiftExtendEntity.setBasicUid(heOrderGiftInfoGoodsAddReq.getBasicUid());
        heOrderGiftExtendEntity.setClientUid(heOrderGiftInfoGoodsAddReq.getClientUid());
        if (StringUtils.isNotEmpty(heOrderGiftInfoGoodsAddReq.getGiftSpecificReason())){
            if (heOrderGiftInfoGoodsAddReq.getGiftSpecificReason().length() > 500){
                log.info("礼赠订单创建成功，礼赠订单号：" + heOrderGiftExtendEntity.getGiftSpecificReason());
            }
            heOrderGiftExtendEntity.setGiftSpecificReason(StringUtils.isNotEmpty(heOrderGiftInfoGoodsAddReq.getGiftSpecificReason()) ? heOrderGiftInfoGoodsAddReq.getGiftSpecificReason().substring(0, Math.min(500, heOrderGiftInfoGoodsAddReq.getGiftSpecificReason().length())) : StringUtils.EMPTY);
        }
        heOrderGiftExtendEntity.setEvidence(JSONUtil.toJsonStr(heOrderGiftInfoGoodsAddReq.getEvidence()));
        heOrderGiftExtendEntity.setCartId(heOrderGiftInfoGoodsAddReq.getCartId());
        heOrderGiftExtendEntity.setCurrency(heOrderGiftInfoGoodsAddReq.getCurrency());
        heOrderGiftExtendEntity.setRemark(heOrderGiftInfoGoodsAddReq.getRemark());
        heOrderGiftExtendEntity.setCreateBy(Long.valueOf(heOrderGiftInfoGoodsAddReq.getOperator().getOperatorGuid()));
        heOrderGiftExtendEntity.setCreatedAt(new Date());
        heOrderGiftExtendEntity.setUpdatedAt(heOrderGiftExtendEntity.getCreatedAt());
        return heOrderGiftExtendEntity;
    }
}

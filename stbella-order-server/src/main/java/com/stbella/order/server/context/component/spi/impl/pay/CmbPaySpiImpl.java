package com.stbella.order.server.context.component.spi.impl.pay;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.stbella.core.enums.BusinessEnum;
import com.stbella.customer.server.ecp.vo.ClientInfoVO;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.month.PayChannelTypeEnum;
import com.stbella.order.common.enums.month.PayStatusV2Enum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.server.context.component.spi.CustomerQuerySpi;
import com.stbella.order.server.context.component.spi.PaySpi;
import com.stbella.order.server.convert.TabClientConvert;
import com.stbella.order.server.fact.OrderFact;
import com.stbella.order.server.manager.PayManager;
import com.stbella.order.server.manager.PayRuleComponent;
import com.stbella.order.server.manager.TabClientManager;
import com.stbella.order.server.order.month.enums.PayAmountEnum;
import com.stbella.order.server.order.month.enums.PayRecordEnum;
import com.stbella.order.server.order.month.req.PayReqV2;
import com.stbella.pay.server.cmbpay.request.AddOrderRequest;
import com.stbella.pay.server.cmbpay.vo.AddOrderRespVO;
import com.stbella.pay.server.enums.CmbPayTypeEnum;
import com.stbella.pay.server.pay.service.res.PayAccountVo;
import com.stbella.platform.order.api.req.CreateOrderReq;
import com.stbella.rule.api.req.ExecuteRuleReq;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.annotation.FuncPointSpiImpl;
import top.primecare.snowball.extpoint.definition.FuncSpiConfig;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-05-30  14:01
 * @Description: 招行聚合支付扩展点
 */
@Component
@FuncPointSpiImpl(name = "招行聚合支付扩展点")
public class CmbPaySpiImpl implements PaySpi {

    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @Resource
    private PayManager payManager;

    @Override
    public boolean condition(HeIncomeRecordEntity param) {

        List<Integer> goodPayTypeList = Lists.newArrayList(OmniPayTypeEnum.WECHAT.getCode(), OmniPayTypeEnum.ALIPAY.getCode());

        if (CompareUtil.integerEqual(param.getChannelType() , PayChannelTypeEnum.CMB.getCode()) && goodPayTypeList.contains(param.getPayType())){
            return true;
        }
        return false;
    }

    @Override
    public JSONObject invoke(HeIncomeRecordEntity param) {

        AddOrderRequest cmbPayRequest = buildCMBPayRequest(param);
        //因为招商特殊的订单支付机制,需要对订单原先的未支付流水进行删除
        List<HeIncomeRecordEntity> recordListByOrderIdList = incomeRecordRepository.getRecordListByOrderIdList(Arrays.asList(param.getOrderId()), PayStatusV2Enum.WAIT_PAY.getCode(), null);
        if (ObjectUtil.isNotEmpty(recordListByOrderIdList)) {
            List<String> notEqualsIncomeSnList = recordListByOrderIdList.stream().filter(x -> !x.getIncomeSn().equals(param.getIncomeSn())).map(y -> y.getIncomeSn()).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(notEqualsIncomeSnList)) {
                for (String incomeSn : notEqualsIncomeSnList) {
                    //删除招商支付流水号
                    payManager.deleteBill(param.getAccountId(), incomeSn);
                }
                //删除本地流水号
                incomeRecordRepository.deleteByIncomeSn(notEqualsIncomeSnList);
            }
        }
        AddOrderRespVO addOrderRespVO = payManager.cmbPay(cmbPayRequest);
        JSONObject result = JSONUtil.parseObj(addOrderRespVO);
        return result;
    }

    protected AddOrderRequest buildCMBPayRequest(HeIncomeRecordEntity incomeRecordEntity) {
        AddOrderRequest addOrderRequest = new AddOrderRequest();
        addOrderRequest.setOrderId(incomeRecordEntity.getIncomeSn());
        addOrderRequest.setFeeAmt(AmountChangeUtil.changeF2Y(incomeRecordEntity.getIncome().longValue()));
        addOrderRequest.setBody(PayAmountEnum.getValueByCode(incomeRecordEntity.getReceiptType()));
        addOrderRequest.setPayTypeList(Lists.newArrayList(CmbPayTypeEnum.getCodeByValue(OmniPayTypeEnum.getName(incomeRecordEntity.getPayType()))));
        addOrderRequest.setCustName(incomeRecordEntity.getStoreName());
        addOrderRequest.setPayNo(incomeRecordEntity.getOrderSn());
        addOrderRequest.setOrderType(incomeRecordEntity.getOrderType());
        addOrderRequest.setPayAccountId(incomeRecordEntity.getAccountId());
        return addOrderRequest;
    }

    @Override
    public FuncSpiConfig config() {
        //默认优先级高
        return new FuncSpiConfig(true, Integer.MIN_VALUE);
    }
}

package com.stbella.order.server.order.month.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.stbella.core.base.StoreInfoDTO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.core.utils.sso.EmployeeTokenHelper;
import com.stbella.customer.server.ecp.entity.UserPO;
import com.stbella.order.common.enums.core.CartSceneEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.common.enums.order.OrderGiftReasonTypeEnum;
import com.stbella.order.common.enums.order.OrderGiftStatusEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.common.utils.ExcelUtil;
import com.stbella.order.domain.client.BrandClient;
import com.stbella.order.domain.client.StoreGoodsClient;
import com.stbella.order.domain.order.entity.HeCartGoodsEntity;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.production.GoodsEntity;
import com.stbella.order.domain.order.production.GoodsSkuEntity;
import com.stbella.order.domain.repository.*;
import com.stbella.order.domain.repository.condition.StoreQueryCondition;
import com.stbella.order.server.config.DynamicConfig;
import com.stbella.order.server.manager.TabClientManager;
import com.stbella.order.server.order.month.dto.PageDTO;
import com.stbella.order.server.order.month.enums.CustomerFromTypeEnum;
import com.stbella.order.server.order.month.enums.StoreTypeEnum;
import com.stbella.order.server.order.month.req.*;
import com.stbella.order.server.order.month.res.*;
import com.stbella.order.domain.order.month.dto.HeOrderGiftInfoDTO;
import com.stbella.order.server.order.month.service.HeOrderGiftInfoService;
import com.stbella.order.server.utils.DateUtils;
import com.stbella.order.server.utils.JsonUtil;
import com.stbella.order.server.utils.PageUtils;
import com.stbella.order.server.utils.SensitiveInformationUtil;
import com.stbella.platform.order.api.cart.CartQueryService;
import com.stbella.platform.order.api.req.QueryCartReq;
import com.stbella.platform.order.api.res.CartRes;
import com.stbella.store.core.enums.BrandTypeEnum;
import com.stbella.store.core.vo.res.store.BrandVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.FlowIdentity;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class HeOrderGiftInfoServiceImpl implements HeOrderGiftInfoService {

    @Resource
    private HeOrderGiftInfoRepository heOrderGiftInfoRepository;

    @Resource
    private ClientRepository clientRepository;

    @Resource
    private StoreRepository storeRepository;

    @Resource
    private StoreGoodsClient storeClient;

    @Resource
    private DynamicConfig dynamicConfig;

    @Resource
    private TabClientManager tabClientManager;

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private BrandClient brandClient;

    @Resource
    private UserRepository userRepository;

    @Resource
    private HeCartRepository heCartRepository;

    @Resource
    private HeCartGoodsRepository heCartGoodsRepository;

    @Resource
    private GoodsRepository goodsRepository;

    @Resource
    private GoodsSkuRepository goodsSkuRepository;

    @Resource
    private CartQueryService cartQueryService;

    @Resource
    private StoreRepository cfgStoreRepository;


    @Override
    public PageDTO<HeOrderGiftInfoPiVO> queryGiftOrderList(HeOrderGiftInfoPiReq req) {

        if (StringUtils.isNotBlank(req.getKeyWord())) {
            List<TabClientEntity> tabClientList = clientRepository.getTabClientByNameAndPhone(req.getKeyWord());
            req.setClientUidList(CollectionUtils.isEmpty(tabClientList) ? Collections.emptyList() : tabClientList.stream().map(TabClientEntity::getId).collect(Collectors.toList()));
            List<CfgStoreEntity> cfgStoreEntities = storeRepository.queryStoreBaseByCondition(StoreQueryCondition.builder().name(req.getKeyWord()).build());
            req.setStoreIdList(CollectionUtils.isEmpty(cfgStoreEntities) ? Collections.emptyList() : cfgStoreEntities.stream().map(CfgStoreEntity::getStoreId).collect(Collectors.toList()));
            if (CollectionUtil.isEmpty(req.getClientUidList()) && CollectionUtil.isEmpty(req.getStoreIdList())){
                return PageDTO.emptyList(req.getPageNum(), req.getPageSize());
            }
        }
        StoreInfoDTO storeInfoDTO = EmployeeTokenHelper.getCurrentEmployeeStoreInfo();
        log.info("当前登录人门店信息：{}", JSONObject.toJSONString(storeInfoDTO));
        if (!storeInfoDTO.getHasAllStoreInfo()) {
            if (CollectionUtil.isEmpty(storeInfoDTO.getStoreIdList())) {
                return PageDTO.emptyList(req.getPageNum(), req.getPageSize());
            }
            req.setAuthStoreIdList(storeInfoDTO.getStoreIdList());
        }
        Page<HeOrderGiftExtendEntity> pageList = heOrderGiftInfoRepository.pageList(req);
        HeOrderGiftInfoDTO heOrderGiftInfoVO = this.convertHeOrderGiftInfoVO(pageList.getRecords());
        return PageUtils.convert2PageDTO(pageList, temp -> this.convertEntityToPageVO(temp, heOrderGiftInfoVO));
    }

    private HeOrderGiftInfoPiVO convertEntityToPageVO(HeOrderGiftExtendEntity heOrderGiftExtendEntity, HeOrderGiftInfoDTO heOrderGiftInfoVO) {

        HeOrderGiftInfoPiVO heOrderGiftInfoPiVO = new HeOrderGiftInfoPiVO();
        heOrderGiftInfoPiVO.setId(heOrderGiftExtendEntity.getId());
        heOrderGiftInfoPiVO.setCurrency(heOrderGiftExtendEntity.getCurrency());
        heOrderGiftInfoPiVO.setStoreId(heOrderGiftExtendEntity.getStoreId());
        CfgStoreEntity cfgStoreEntity = heOrderGiftInfoVO.getStoreEntityMap().get(heOrderGiftExtendEntity.getStoreId());
        heOrderGiftInfoPiVO.setStoreName(Objects.isNull(cfgStoreEntity) ? StringUtils.EMPTY : cfgStoreEntity.getStoreName());
        heOrderGiftInfoPiVO.setOrderSn(heOrderGiftExtendEntity.getOrderSn());
        heOrderGiftInfoPiVO.setStatus(heOrderGiftExtendEntity.getStatus());
        heOrderGiftInfoPiVO.setStatusStr(OrderGiftStatusEnum.getNameByCode(heOrderGiftExtendEntity.getStatus()));
        heOrderGiftInfoPiVO.setGiftReasonType(heOrderGiftExtendEntity.getGiftReasonType());
        heOrderGiftInfoPiVO.setGiftReasonTypeStr(OrderGiftReasonTypeEnum.getDesc(heOrderGiftExtendEntity.getGiftReasonType()));
        heOrderGiftInfoPiVO.setClientUid(heOrderGiftExtendEntity.getClientUid());
        TabClientEntity tabClientEntity = heOrderGiftInfoVO.getTabClientMap().get(heOrderGiftExtendEntity.getClientUid());
        heOrderGiftInfoPiVO.setClientName(Objects.isNull(tabClientEntity) ? StringUtils.EMPTY : tabClientEntity.getName());
        heOrderGiftInfoPiVO.setPhone(Objects.isNull(tabClientEntity) ? StringUtils.EMPTY : tabClientEntity.getPhone());
        heOrderGiftInfoPiVO.setHidePhone(Objects.isNull(tabClientEntity) ? StringUtils.EMPTY : SensitiveInformationUtil.conductPhoneOrIdCardNo(tabClientEntity.getPhone()));
        heOrderGiftInfoPiVO.setCreateBy(heOrderGiftExtendEntity.getCreateBy());
        heOrderGiftInfoPiVO.setCreateByStr(heOrderGiftInfoVO.getUserNameMap().get(heOrderGiftExtendEntity.getCreateBy()));
        heOrderGiftInfoPiVO.setCreatedAt(heOrderGiftExtendEntity.getCreatedAt());
        heOrderGiftInfoPiVO.setGiftGoodsPrice(AmountChangeUtil.getRoundingMode(heOrderGiftInfoVO.getOrderGiftGoodsAmountMap().get(heOrderGiftExtendEntity.getCartId())));
        return heOrderGiftInfoPiVO;
    }

    private HeOrderGiftInfoDTO convertHeOrderGiftInfoVO(List<HeOrderGiftExtendEntity> reqList) {

        HeOrderGiftInfoDTO heOrderGiftInfoVO = new HeOrderGiftInfoDTO();
        heOrderGiftInfoVO.setWarZoneMap(storeClient.getAttribute(dynamicConfig.getWarZone()).stream().collect(Collectors.toMap(SelectRespVO::getValue, SelectRespVO::getLabel, (v1, v2) -> v1)));
        heOrderGiftInfoVO.setTabClientMap(this.getTabClientMap(reqList));
        heOrderGiftInfoVO.setHeOrderEntityMap(this.getHeOrderEntityMap(reqList));
        heOrderGiftInfoVO.setUserNameMap(this.getUserNameMap(reqList));
        heOrderGiftInfoVO.setStoreEntityMap(this.getStoreEntityMap(reqList));
        heOrderGiftInfoVO.setOrderGiftGoodsAmountMap(this.getOrderGiftGoodsAmountMap(reqList));
        return heOrderGiftInfoVO;
    }

    private Map<Integer, HeOrderEntity> getHeOrderEntityMap(List<HeOrderGiftExtendEntity> reqList) {
        List<Integer> heOrderIdList = reqList.stream().map(HeOrderGiftExtendEntity::getOrderId).filter(Objects::nonNull).collect(Collectors.toList());
        List<HeOrderEntity> orderList = orderRepository.getByOrderList(heOrderIdList);
        return orderList.stream().collect(Collectors.toMap(HeOrderEntity::getOrderId, v -> v, (k1, k2) -> k1));
    }

    private Map<Integer, TabClientEntity> getTabClientMap(List<HeOrderGiftExtendEntity> reqList) {
        List<Integer> clientUidList = reqList.stream().map(HeOrderGiftExtendEntity::getClientUid).filter(Objects::nonNull).collect(Collectors.toList());
        List<TabClientEntity> tabClientByIdList = clientRepository.getTabClientByIdList(clientUidList);
        return tabClientByIdList.stream().collect(Collectors.toMap(TabClientEntity::getId, entity -> entity, (v1, v2) -> v1));
    }

    private Map<Long, String> getUserNameMap(List<HeOrderGiftExtendEntity> reqList) {

        List<UserPO> userPOList = tabClientManager.queryUserListByIds(reqList.stream().map(HeOrderGiftExtendEntity::getCreateBy).filter(Objects::nonNull).collect(Collectors.toList()));
        return userPOList.stream().collect(Collectors.toMap(UserPO::getId, UserPO::getName, (v1, v2) -> v1));
    }

    private Map<Integer, CfgStoreEntity> getStoreEntityMap(List<HeOrderGiftExtendEntity> reqList) {
        List<Integer> storeIdList = reqList.stream().map(HeOrderGiftExtendEntity::getStoreId).filter(Objects::nonNull).collect(Collectors.toList());
        List<CfgStoreEntity> storeByIdList = storeRepository.queryCfgStoreByIdList(storeIdList);
        if (CollectionUtils.isEmpty(storeByIdList)) {
            return Maps.newHashMap();
        }
        return storeByIdList.stream().collect(Collectors.toMap(CfgStoreEntity::getStoreId, v -> v, (k1, k2) -> k1));
    }

    private Map<Integer, BigDecimal> getOrderGiftGoodsAmountMap(List<HeOrderGiftExtendEntity> reqList) {

        Map<Integer, BigDecimal> compensatedGoodsAmountMap = new HashMap<>();
        List<Integer> cartIdList = reqList.stream().map(HeOrderGiftExtendEntity::getCartId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cartIdList)) {
            return compensatedGoodsAmountMap;
        }
        heCartRepository.queryList(cartIdList).forEach(cart -> {
            compensatedGoodsAmountMap.put(cart.getCartId(), AmountChangeUtil.f2YScale2(cart.getOrderAmount()));
        });
        return compensatedGoodsAmountMap;
    }

    @Override
    public PageDTO<HeOrderGiftInfoPicpVO> page(HeOrderGiftInfoPicpReq req) {

        if (StringUtils.isNotEmpty(req.getClientName()) || CollectionUtils.isNotEmpty(req.getCustomerSourceList()) || StringUtils.isNotEmpty(req.getPhone())) {
            List<TabClientEntity> tabClientEntityList = clientRepository.queryByUserIdOrNameOrSource(req.getClientName(), req.getPhone(), req.getCustomerSourceList());
            if (CollectionUtils.isEmpty(tabClientEntityList)) {
                return PageDTO.emptyList(req.getPageNum(), req.getPageSize());
            }
            List<Integer> clientUidList = tabClientEntityList.stream().map(TabClientEntity::getId).filter(Objects::nonNull).collect(Collectors.toList());
            req.setClientUidList(CollectionUtils.isEmpty(req.getClientUidList()) ? clientUidList : (List<Integer>) CollectionUtils.intersection(clientUidList, req.getClientUidList()));
            if (CollectionUtils.isEmpty(req.getClientUidList())) {
                return PageDTO.emptyList(req.getPageNum(), req.getPageSize());
            }
        }

        if (CollectionUtils.isNotEmpty(req.getStoreBrandList())) {
            List<Integer> storeIdByStoreBrandList = getStoreIdByStoreBrand(req.getStoreBrandList());
            if (CollectionUtils.isEmpty(storeIdByStoreBrandList)) {
                return PageDTO.emptyList(req.getPageNum(), req.getPageSize());

            }
            req.setStoreIdList(CollectionUtils.isEmpty(req.getStoreIdList()) ? storeIdByStoreBrandList : (List<Integer>) CollectionUtils.intersection(storeIdByStoreBrandList, req.getStoreIdList()));
            if (CollectionUtils.isEmpty(req.getStoreIdList())) {
                return PageDTO.emptyList(req.getPageNum(), req.getPageSize());
            }
        }

        if (CollectionUtils.isNotEmpty(req.getWarZoneList())) {
            List<CfgStoreEntity> cfgStoreEntitieList = storeRepository.queryStoreBaseByCondition(StoreQueryCondition.builder().warZones(req.getWarZoneList()).build());
            if (CollectionUtils.isEmpty(cfgStoreEntitieList)) {
                return PageDTO.emptyList(req.getPageNum(), req.getPageSize());
            }
            List<Integer> storeIdByWarZoneList = cfgStoreEntitieList.stream().map(CfgStoreEntity::getStoreId).filter(Objects::nonNull).collect(Collectors.toList());
            req.setStoreIdList(CollectionUtils.isEmpty(req.getStoreIdList()) ? storeIdByWarZoneList : (List<Integer>) CollectionUtils.intersection(storeIdByWarZoneList, req.getStoreIdList()));
            if (CollectionUtils.isEmpty(req.getStoreIdList())) {
                return PageDTO.emptyList(req.getPageNum(), req.getPageSize());
            }
        }

        if (StringUtils.isNotEmpty(req.getCreator())) {
            List<UserEntity> userEntities = userRepository.queryUserList(req.getCreator());
            if (CollectionUtils.isEmpty(userEntities)){
                return PageDTO.emptyList(req.getPageNum(), req.getPageSize());
            }
            req.setCreatorList(userEntities.stream().map(UserEntity::getId).filter(Objects :: nonNull).collect(Collectors.toList()));
        }

        //增加门店权限
        StoreInfoDTO storeInfoDTO = EmployeeTokenHelper.getCurrentEmployeeStoreInfo();
        log.info("当前登录人门店信息：{}", JSONObject.toJSONString(storeInfoDTO));
        if (!storeInfoDTO.getHasAllStoreInfo()) {
            List<Integer> storeIdList = storeInfoDTO.getStoreIdList();
            if (CollectionUtil.isEmpty(storeIdList)) {
                req.setStoreIdList(Collections.singletonList(-1));
            } else {
                if (CollectionUtils.isEmpty(req.getStoreIdList())) {
                    req.setStoreIdList(storeIdList);
                } else {
                    req.getStoreIdList().retainAll(storeIdList);
                }
            }
        }
        Page<HeOrderGiftExtendEntity> pageList = heOrderGiftInfoRepository.pageList(req);
        if (CollectionUtils.isEmpty(pageList.getRecords())){
            return PageDTO.emptyList(req.getPageNum(), req.getPageSize());
        }
        HeOrderGiftInfoDTO heOrderGiftInfoVO = this.convertHeOrderGiftInfoVO(pageList.getRecords());
        return PageUtils.convert2PageDTO(pageList, temp -> this.convertEntityToVOPicp(temp, heOrderGiftInfoVO));
    }

    private HeOrderGiftInfoPicpVO convertEntityToVOPicp(HeOrderGiftExtendEntity heOrderGiftExtendEntity, HeOrderGiftInfoDTO heOrderGiftInfoVO) {

        HeOrderGiftInfoPicpVO heOrderGiftInfoPicpVO = BeanMapper.map(heOrderGiftExtendEntity, HeOrderGiftInfoPicpVO.class);
        CfgStoreEntity cfgStoreEntity = heOrderGiftInfoVO.getStoreEntityMap().get(heOrderGiftExtendEntity.getStoreId());
        if (Objects.nonNull(cfgStoreEntity)) {
            heOrderGiftInfoPicpVO.setStoreId(cfgStoreEntity.getStoreId());
            heOrderGiftInfoPicpVO.setWarZone(cfgStoreEntity.getWarzone());
            heOrderGiftInfoPicpVO.setWarZoneDesc(Optional.ofNullable(heOrderGiftInfoPicpVO.getWarZone()).isPresent() ? heOrderGiftInfoVO.getWarZoneMap().get(String.valueOf(heOrderGiftInfoPicpVO.getWarZone())) : StringUtils.EMPTY);
            heOrderGiftInfoPicpVO.setStoreName(cfgStoreEntity.getStoreName());
            heOrderGiftInfoPicpVO.setBrandName(StoreTypeEnum.getBrandName(cfgStoreEntity.getType(), cfgStoreEntity.getChildType()));
        }

        Optional.ofNullable(heOrderGiftInfoVO.getTabClientMap().get(heOrderGiftInfoPicpVO.getClientUid())).ifPresent(client -> {
            heOrderGiftInfoPicpVO.setCustomerName(client.getName());
            heOrderGiftInfoPicpVO.setCustomerMobile(client.getPhone());
            heOrderGiftInfoPicpVO.setClientPhone(SensitiveInformationUtil.conductPhoneOrIdCardNo(client.getPhone()));
            heOrderGiftInfoPicpVO.setCustomerSource(client.getFromType());
            heOrderGiftInfoPicpVO.setCustomerSourceStr(CustomerFromTypeEnum.getOrderListValueByCode(heOrderGiftInfoPicpVO.getCustomerSource()));
        });
        heOrderGiftInfoPicpVO.setGiftReasonTypeStr(OrderGiftReasonTypeEnum.getDesc(heOrderGiftInfoPicpVO.getGiftReasonType()));
        heOrderGiftInfoPicpVO.setGiftGoodsPrice(AmountChangeUtil.getRoundingMode(heOrderGiftInfoVO.getOrderGiftGoodsAmountMap().get(heOrderGiftExtendEntity.getCartId())));
        heOrderGiftInfoPicpVO.setCreateByStr(heOrderGiftInfoVO.getUserNameMap().get(heOrderGiftInfoPicpVO.getCreateBy()));
        heOrderGiftInfoPicpVO.setStatusStr(OrderGiftStatusEnum.getNameByCode(heOrderGiftInfoPicpVO.getStatus()));
        heOrderGiftInfoPicpVO.setLinkUrl(heOrderGiftExtendEntity.getLinkUrl());
        heOrderGiftInfoPicpVO.setRemark(heOrderGiftExtendEntity.getRemark());
        heOrderGiftInfoPicpVO.setPerformanceEffectiveDate(this.getPerformanceEffectiveDate(heOrderGiftExtendEntity.getOrderId(), heOrderGiftInfoVO));
        return heOrderGiftInfoPicpVO;
    }

    @Override
    public List<RefundReasonVO> queryOrderGiftReasonTypeList() {

        List<RefundReasonVO> refundReasonVOList = new ArrayList<>();
        for (OrderGiftReasonTypeEnum typeEnum : OrderGiftReasonTypeEnum.values()) {
            refundReasonVOList.add(new RefundReasonVO(typeEnum.getCode(), typeEnum.getDesc()));
        }
        return refundReasonVOList;
    }

    @Override
    public HeOrderGiftInfoGoodsPiVO queryGiftOrderInfoPi(HeOrderGiftInfoGoodsReq req) {

        HeOrderGiftExtendEntity heOrderGiftExtendEntity = getHeOrderGiftExtendEntity(req.getId());
        return convertEntityToInfoVOPi(heOrderGiftExtendEntity, this.convertHeOrderGiftInfoVO(Collections.singletonList(heOrderGiftExtendEntity)));
    }

    @Override
    public HeOrderGiftInfoGoodsPicpVO queryGiftOrderInfoPicp(HeOrderGiftInfoGoodsReq req) {

        HeOrderGiftExtendEntity heOrderGiftExtendEntity = getHeOrderGiftExtendEntity(req.getId());
        return convertEntityToInfoVOPicp(heOrderGiftExtendEntity, this.convertHeOrderGiftInfoVO(Collections.singletonList(heOrderGiftExtendEntity)));
    }

    private HeOrderGiftExtendEntity getHeOrderGiftExtendEntity(Long id) {

        Assert.notNull(id, "订单id不能为空");
        HeOrderGiftExtendEntity heOrderGiftExtendEntity = heOrderGiftInfoRepository.selectById(id);
        Assert.notNull(heOrderGiftExtendEntity, "订单不存在");
        return heOrderGiftExtendEntity;
    }

    private HeOrderGiftInfoGoodsPicpVO convertEntityToInfoVOPicp(HeOrderGiftExtendEntity heOrderGiftExtendEntity, HeOrderGiftInfoDTO heOrderGiftInfoVO) {

        HeOrderGiftInfoPicpVO heOrderGiftInfoPicpVO = this.convertEntityToVOPicp(heOrderGiftExtendEntity, heOrderGiftInfoVO);
        HeOrderGiftInfoGoodsPicpVO heOrderGiftInfoGoodsPicpVO = BeanMapper.map(heOrderGiftInfoPicpVO, HeOrderGiftInfoGoodsPicpVO.class);
        heOrderGiftInfoGoodsPicpVO.setGoodsRecordList(getGoodsRecordList(heOrderGiftExtendEntity.getCartId()));
        heOrderGiftInfoGoodsPicpVO.setEvidence(StringUtils.isEmpty(heOrderGiftExtendEntity.getEvidence()) ? Collections.emptyList() : JSONObject.parseArray(heOrderGiftExtendEntity.getEvidence(), String.class));
        return heOrderGiftInfoGoodsPicpVO;
    }

    @Override
    public void export(HeOrderGiftInfoPicpReq req, HttpServletResponse response) {

        try {
            this.fillTestStoreIdList(req);
            List<HeOrderGiftInfoPicpExportVO> exportVO = this.getExportVO(this.page(req));
            ExcelUtil.exportData(new HeOrderGiftInfoPicpExportVO(), exportVO, response, DateUtils.formatDate(new Date()) + "产康赠送记录导出", "产康赠送记录");
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.FILE_ERROR.getCode(), "导出异常");
        }
    }

    private void fillTestStoreIdList(HeOrderGiftInfoPicpReq req) {

        List<CfgStoreEntity> cfgStoreEntities = cfgStoreRepository.queryAllStore();
        if (CollectionUtils.isEmpty(cfgStoreEntities)){
            return;
        }
        List<Integer> testStoreIdList = cfgStoreEntities.stream().filter(c -> ObjectUtil.isNotEmpty(c.getWarzone()) && c.getWarzone() == 11).map(CfgStoreEntity::getStoreId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(testStoreIdList)){
            return;
        }
        req.setTestStoreIdList(testStoreIdList);
    }

    private List<HeOrderGiftInfoPicpExportVO> getExportVO(PageDTO<HeOrderGiftInfoPicpVO> pageList) {

        List<HeOrderGiftInfoPicpExportVO> exportVOList = new ArrayList<>();
        log.info("导出数据开始处理");
        if (Objects.isNull(pageList) || CollectionUtils.isEmpty(pageList.getList())){
            return exportVOList;
        }

        final Integer[] i = {0};
        for (HeOrderGiftInfoPicpVO heOrderGiftInfoPicpVO : pageList.getList()) {
            ++i[0];
            HeOrderGiftInfoPicpExportVO exportVO = BeanMapper.map(heOrderGiftInfoPicpVO, HeOrderGiftInfoPicpExportVO.class);
            exportVO.setId(i[0]);
            exportVO.setCreatedAtStr(DateUtils.formatDateTime(heOrderGiftInfoPicpVO.getCreatedAt()));
            exportVO.setApproveFinishStr(DateUtils.formatDateTime(heOrderGiftInfoPicpVO.getApproveFinish()));
            exportVO.setReleaseTimeStr(DateUtils.formatDateTime(heOrderGiftInfoPicpVO.getReleaseTime()));
            exportVO.setPerformanceEffectiveDateStr(DateUtils.formatDateTime(heOrderGiftInfoPicpVO.getPerformanceEffectiveDate()));
            List<HeOrderGiftInfoGoodsPicpVO.GoodsRecord> goodsRecordList = this.getGoodsRecordList(heOrderGiftInfoPicpVO.getCartId());
            if (CollectionUtils.isEmpty(goodsRecordList)) {
                exportVOList.add(exportVO);
                continue;
            }
            for (HeOrderGiftInfoGoodsPicpVO.GoodsRecord goodsRecord : goodsRecordList) {
                HeOrderGiftInfoPicpExportVO subExportVO = BeanMapper.map(exportVO, HeOrderGiftInfoPicpExportVO.class);
                subExportVO.setGoodsName(goodsRecord.getGoodsName());
                subExportVO.setSkuName(goodsRecord.getSkuName());
                subExportVO.setGoodsNum(goodsRecord.getGoodsNum());
                subExportVO.setGoodsPriceOrgin(goodsRecord.getGoodsPriceOrgin());
                exportVOList.add(subExportVO);
            }
        }
        log.info("导出数据处理结束");
        return exportVOList;
    }

    @Override
    public HeOrderGiftInfoGoodsAddVO addGiftOrderInfoPi(HeOrderGiftInfoGoodsAddReq req) {

        FlowContext context = new FlowContext();
        context.setAttribute(HeOrderGiftInfoGoodsAddReq.class, req);

        FlowIdentity identity = FlowIdentity.builder()
                .bizActivity(BizActivityEnum.CREATE_COMMON_GIFT_ORDER.code())
                .idSlice("GiftOrder")
                .idSlice("Create")
                .build();
        SnowballFlowLauncher.fire(identity, context);

        return context.getAttribute(HeOrderGiftInfoGoodsAddVO.class);
    }

    @Override
    public String cancelGiftOrderInfoPi(HeOrderGiftInfoGoodsReq req) {

        HeOrderGiftExtendEntity heOrderGiftExtendEntity = heOrderGiftInfoRepository.selectById(req.getId());
        Assert.notNull(heOrderGiftExtendEntity, "订单不存在");
        Assert.isTrue(OrderGiftStatusEnum.APPROVAL.getCode().equals(heOrderGiftExtendEntity.getStatus()), "当前订单状态不允许取消");
        if (OrderGiftStatusEnum.APPROVAL.getCode().equals(heOrderGiftExtendEntity.getStatus())) {
            heOrderGiftExtendEntity.setStatus(OrderGiftStatusEnum.CANCELED.getCode());
        }
        heOrderGiftInfoRepository.update(heOrderGiftExtendEntity);
        return "成功取消订单";
    }

    private HeOrderGiftInfoGoodsPiVO convertEntityToInfoVOPi(HeOrderGiftExtendEntity heOrderGiftExtendEntity, HeOrderGiftInfoDTO heOrderGiftInfoVO) {

        HeOrderGiftInfoPicpVO heOrderGiftInfoPicpVO = this.convertEntityToVOPicp(heOrderGiftExtendEntity, heOrderGiftInfoVO);
        HeOrderGiftInfoGoodsPiVO heOrderGiftInfoGoodsPiVO = BeanMapper.map(heOrderGiftInfoPicpVO, HeOrderGiftInfoGoodsPiVO.class);
        heOrderGiftInfoGoodsPiVO.setEvidence(StringUtils.isEmpty(heOrderGiftExtendEntity.getEvidence()) ? Collections.emptyList() : JSONObject.parseArray(heOrderGiftExtendEntity.getEvidence(), String.class));
        heOrderGiftInfoGoodsPiVO.setCartRes(this.getCartRes(heOrderGiftInfoPicpVO));
        heOrderGiftInfoGoodsPiVO.setApproveId(heOrderGiftExtendEntity.getApproveId());
        return heOrderGiftInfoGoodsPiVO;
    }

    private CartRes getCartRes(HeOrderGiftInfoPicpVO heOrderGiftInfoPicpVO){

        Result<CartRes> cartResResult = cartQueryService.queryCart(QueryCartReq.builder().cartId(heOrderGiftInfoPicpVO.getCartId()).orderType(Objects.isNull(heOrderGiftInfoPicpVO.getOrderType()) ? OmniOrderTypeEnum.PRODUCTION_ORDER.code() : heOrderGiftInfoPicpVO.getOrderType()).scene(CartSceneEnum.GIFT_ORDER.code()).build());
        if (cartResResult.getSuccess()){
            return cartResResult.getData();
        }
        log.error("查询购物车失败：{}", cartResResult.getMsg());
        return null;
    }

    private List<HeOrderGiftInfoGoodsPicpVO.GoodsRecord> getGoodsRecordList(Integer cartId){

        List<HeOrderGiftInfoGoodsPicpVO.GoodsRecord> goodsRecordList = new ArrayList<>();
        List<HeCartGoodsEntity> goodsEntityList = heCartGoodsRepository.queryList(cartId);
        if (CollectionUtils.isEmpty(goodsEntityList)) {
            return goodsRecordList;
        }
        Map<Integer, GoodsEntity> goodsEntityMap = new HashMap<>();
        List<Integer> goodsIdList = goodsEntityList.stream().map(HeCartGoodsEntity::getGoodsId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(goodsIdList)) {
            List<GoodsEntity> goodsEntities = goodsRepository.selectByIdList(goodsIdList);
            goodsEntityMap = goodsEntities.stream().collect(Collectors.toMap(GoodsEntity::getId, v -> v, (k1, k2) -> k1));
        }

        Map<Integer, String> skuNameMap = new HashMap<>();
        List<Integer> skuIdList = goodsEntityList.stream().map(HeCartGoodsEntity::getSkuId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(skuIdList)) {
            List<GoodsSkuEntity> goodsSkuEntities = goodsSkuRepository.selectByIdList(skuIdList);
            skuNameMap = goodsSkuEntities.stream().collect(Collectors.toMap(GoodsSkuEntity::getId, item -> StringUtils.isEmpty(item.getTemplateSkuProp()) ? item.getSkuName() : item.getTemplateSkuProp(), (k1, k2) -> k1));
        }
        for (HeCartGoodsEntity heCartGoodsEntity : goodsEntityList) {

            HeOrderGiftInfoGoodsPicpVO.GoodsRecord complaintsGoodsRecord = new HeOrderGiftInfoGoodsPicpVO.GoodsRecord();
            complaintsGoodsRecord.setId(heCartGoodsEntity.getId());
            GoodsEntity goodsEntity = goodsEntityMap.get(heCartGoodsEntity.getGoodsId());
            if (Objects.isNull(goodsEntity)) {
                continue;
            }
            complaintsGoodsRecord.setSkuName(Objects.isNull(heCartGoodsEntity.getSkuId()) ? null : skuNameMap.get(heCartGoodsEntity.getSkuId()));
            complaintsGoodsRecord.setGoodsName(getGoodsName(goodsEntity.getGoodsName(), complaintsGoodsRecord.getSkuName()));
            complaintsGoodsRecord.setGoodsNum(heCartGoodsEntity.getNum());
            complaintsGoodsRecord.setGoodsPriceOrgin(AmountChangeUtil.f2YScale2(goodsEntity.getGoodsPrice()));
            complaintsGoodsRecord.setGoodsPrice(AmountChangeUtil.getRoundingMode(complaintsGoodsRecord.getGoodsPriceOrgin().multiply(new BigDecimal(heCartGoodsEntity.getNum()))));
            goodsRecordList.add(complaintsGoodsRecord);
        }
        return goodsRecordList;

    }

    private static String getGoodsName(String goodsName, String skuName) {

        if (StringUtils.isEmpty(goodsName) || StringUtils.isEmpty(skuName)) {
            return goodsName;
        }
        boolean alike = goodsName.equals(skuName);
        skuName = "(" + skuName + ")";
        return goodsName.endsWith(skuName) || alike ? goodsName : goodsName + skuName;
    }

    private Date getPerformanceEffectiveDate(Integer orderId, HeOrderGiftInfoDTO heOrderGiftInfoVO){

        if (Objects.isNull(orderId)){
            return null;
        }
        HeOrderEntity heOrderEntity = heOrderGiftInfoVO.getHeOrderEntityMap().get(orderId);
        Assert.notNull(heOrderEntity, "当前赠送订单对应的关联订单不存在");
        if (Objects.nonNull(heOrderEntity.getPercentFirstTime()) && heOrderEntity.getPercentFirstTime() > 0){
            return new Date(heOrderEntity.getPercentFirstTime().longValue() * 1000);
        }
        return null;
    }

    private List<Integer> getStoreIdByStoreBrand(List<Long> reqList) {

        List<Integer> cfgStoreIdList = Lists.newArrayList();
        List<BrandVO> brandListVOS = brandClient.queryBrandListBy(reqList);
        if (CollectionUtils.isEmpty(brandListVOS)) {
            return cfgStoreIdList;
        }
        List<CfgStoreEntity> cfgStoreEntities = storeRepository.queryStoreBaseByCondition(StoreQueryCondition.builder().build());
        if (CollectionUtils.isEmpty(cfgStoreEntities)) {
            return cfgStoreIdList;
        }
        Map<Long, Integer> brandMap = brandListVOS.stream().filter(item -> BrandTypeEnum.MAIN_BRAND.getCode().equals(item.getBrandType())).collect(Collectors.toMap(BrandVO::getId, BrandVO::getBrandVal));
        brandMap.forEach((key, value) -> {
            List<Integer> idList = cfgStoreEntities.stream().filter(store -> Objects.nonNull(store.getType()) && store.getType().equals(value)).map(CfgStoreEntity::getStoreId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(idList)) {
                cfgStoreIdList.addAll(idList);
            }
        });
        Map<Long, List<BrandVO>> subBrandMap = brandListVOS.stream().filter(item -> BrandTypeEnum.CHILD_BRAND.getCode().equals(item.getBrandType())).collect(Collectors.groupingBy(BrandVO::getParentId));
        List<Long> parentIdList = brandListVOS.stream().filter(item -> BrandTypeEnum.CHILD_BRAND.getCode().equals(item.getBrandType()) && Objects.nonNull(item.getParentId())).map(BrandVO::getParentId).collect(Collectors.toList());
        List<BrandVO> parentBrandListVOS = brandClient.queryBrandListBy(parentIdList);
        if (CollectionUtils.isEmpty(parentBrandListVOS)) {
            return cfgStoreIdList;
        }
        Map<Long, Integer> parentBrandMap = parentBrandListVOS.stream().filter(item -> BrandTypeEnum.MAIN_BRAND.getCode().equals(item.getBrandType())).collect(Collectors.toMap(BrandVO::getId, BrandVO::getBrandVal));
        for (Map.Entry<Long, List<BrandVO>> entry : subBrandMap.entrySet()) {
            Long key = entry.getKey();
            List<BrandVO> valueList = entry.getValue();
            Integer type = parentBrandMap.get(key);
            if (Objects.isNull(type)) {
                continue;
            }
            valueList.forEach(value -> {
                List<Integer> idList = cfgStoreEntities.stream().filter(store -> Objects.nonNull(store.getType()) && Objects.nonNull(store.getChildType()) && store.getType().equals(type) && store.getChildType().equals(value.getBrandVal())).map(CfgStoreEntity::getStoreId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(idList)) {
                    cfgStoreIdList.addAll(idList);
                }
            });
        }
        return cfgStoreIdList;
    }

}

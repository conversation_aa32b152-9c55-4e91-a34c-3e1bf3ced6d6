package com.stbella.order.server.context.service.cart;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.stbella.core.base.Operator;
import com.stbella.core.result.Result;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.core.CartSceneEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.common.enums.order.QuotationOrderStatusEnum;
import com.stbella.order.common.utils.BPCheckUtil;
import com.stbella.order.domain.order.entity.HeCartEntity;
import com.stbella.order.domain.order.month.entity.HeQuotationEntity;
import com.stbella.order.domain.order.remark.HeCartRemark;
import com.stbella.order.domain.repository.HeCartRepository;
import com.stbella.order.domain.repository.HeQuotationRepository;
import com.stbella.platform.order.api.cart.CartCommandService;
import com.stbella.platform.order.api.req.*;
import com.stbella.platform.order.api.res.PromotionInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.FlowIdentity;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 购物车操作服务
 */
@Slf4j
@Service
@DubboService
public class CartCommandServiceImpl implements CartCommandService {

    @Resource
    private HeCartRepository heCartRepository;

    @Resource
    private HeQuotationRepository heQuotationRepository;


    @Override
    public Result<Integer> createOrUpdateCart(CreateOrUpdateCartReq req) {

        log.info("创建或更新购物车 ===>> req={}", req);
        FlowIdentity identity = FlowIdentity.builder()
                .bizActivity(BizActivityEnum.CREATE_CART.code())
                .idSlice("Cart")
                .idSlice("Create")
                .idSlice(OmniOrderTypeEnum.getByCode(req.getOrderType()).name())
                .build();

        List<SkuDetailInfo> skuList = req.getSkuList();
        for (SkuDetailInfo skuDetailInfo : skuList) {
            skuDetailInfo.setOriginalGoodsId(req.getOriginalGoodsId());
            skuDetailInfo.setOriginalGoodsNum(req.getOriginalGoodsNum());
        }

        FlowContext context = new FlowContext();
        context.setAttribute(BizConstant.ExtraKey.storeId, req.getStoreId());
        context.setAttribute(BizConstant.ExtraKey.clientUid, req.getClientUid());
        context.setAttribute(BizConstant.ExtraKey.scene, req.getScene());
        context.setAttribute(Operator.class, req.getOperator());
        List<PromotionInfo> promotionInfos = req.getPromotionInfos();
        if (CollectionUtils.isEmpty(promotionInfos)) {
            promotionInfos = Lists.newArrayList();
        }
        context.setAttribute(BizConstant.ExtraKey.promotionInfos, promotionInfos);
        Integer cartId = req.getCartId();
        if (Objects.isNull(cartId)) {
            cartId = -1;
        }
        context.setAttribute(BizConstant.ExtraKey.cartId, cartId);
        context.setAttribute(CreateOrUpdateCartReq.class, req);
        if (CartSceneEnum.GIFT_ORDER.code().equals(req.getScene())){
            req.setSkuList(skuList.stream().filter(o -> Objects.isNull(o.getPromotionInfo())).collect(Collectors.toList()));
        }
        context.setListAttribute(SkuDetailInfo.class, skuList);
        context.setListAttribute(CustomAttribute.class, req.getExtraInfo().getFulfillExtraList());

        SnowballFlowLauncher.fire(identity, context);

        HeCartEntity cartEntity = context.getAttribute(HeCartEntity.class);
        return Result.success(cartEntity.getCartId());
    }

    /**
     * 提交订单后清空购物车 设置提交状态，
     * 如果
     * @param req
     * @return
     */
    @Override
    public Result<Boolean> finishCart(FinishCartReq req) {

        BPCheckUtil.checkEmptyInBean(new String[]{"cartId", "orderId"}, req, false);

        HeCartEntity heCartEntity = new HeCartEntity()
                .setCartId(req.getCartId());

        heCartEntity.finishCart(req.getOrderId());
        HeCartEntity cartEntity = heCartRepository.queryOne(QueryCartReq.builder().cartId(req.getCartId()).build());
        if (Objects.nonNull(cartEntity) && StringUtils.isNotEmpty(cartEntity.getRemark()) && CartSceneEnum.ORDER.code().equals(cartEntity.getScene())){
            HeCartRemark heCartRemark = JSON.parseObject(cartEntity.getRemark(), HeCartRemark.class);
            if (Objects.isNull(heCartRemark) || Objects.isNull(heCartRemark.getOldOrderId())){
                return Result.success(true);
            }
            if (!CartSceneEnum.QUOTATION.code().equals(cartEntity.getScene())){
                return Result.success(true);
            }
            HeQuotationEntity heQuotationEntity = heQuotationRepository.queryById(heCartRemark.getOldOrderId());
            heQuotationEntity.setOrderStatus(QuotationOrderStatusEnum.YES.getCode());
            heQuotationRepository.update(heQuotationEntity);
        }
        return Result.success(true);
    }

    /**
     * 清空购物
     *
     * @param req
     * @return
     */
    @Override
    public Result<Boolean> clearCart(ClearCartReq req) {
        BPCheckUtil.checkEmptyInBean(new String[]{"cartId"}, req, false);

        HeCartEntity heCartEntity = new HeCartEntity()
                .setCartId(req.getCartId());
        heCartEntity.deleteCartGoods();

        return Result.success(true);
    }

    /**
     * 还原购物车
     * 使用场景：
     * 1， 再来一单 - 从订单还原购物车。
     * 2， 报价单编辑 - 从报价单还原购物车。
     *
     * @param req
     * @return
     */
    @Override
    public Result<Integer> restoreCart(RestoreCartReq req) {

        Assert.isTrue(CartSceneEnum.allList().contains(req.getScene()), "不支持当前场景");
        FlowIdentity identity = FlowIdentity.builder()
                .bizActivity(BizActivityEnum.RESTORE_CART.code())
                .idSlice("Cart")
                .idSlice("Restore")
                .idSlice(CartSceneEnum.getByCode(req.getScene()).name())
                .build();

        FlowContext context = new FlowContext();
        context.setAttribute(RestoreCartReq.class, req);
        SnowballFlowLauncher.fire(identity, context);
        HeCartEntity cartEntity = context.getAttribute(HeCartEntity.class);
        return Result.success(cartEntity.getCartId());

    }

    @Override
    public Result<Integer> copyCart(RestoreCartReq req) {
        Assert.isTrue(CartSceneEnum.allList().contains(req.getScene()), "不支持当前场景");
        FlowIdentity identity = FlowIdentity.builder()
                .bizActivity(BizActivityEnum.COPY_CART.code())
                .idSlice("Cart")
                .idSlice("Copy")
                .idSlice(CartSceneEnum.getByCode(req.getScene()).name())
                .build();

        FlowContext context = new FlowContext();
        context.setAttribute(RestoreCartReq.class, req);
        SnowballFlowLauncher.fire(identity, context);
        HeCartEntity cartEntity = context.getAttribute(HeCartEntity.class);
        return Result.success(cartEntity.getCartId());
    }

}

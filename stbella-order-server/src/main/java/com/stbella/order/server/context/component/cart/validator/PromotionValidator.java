package com.stbella.order.server.context.component.cart.validator;

import com.stbella.core.exception.BusinessException;
import com.stbella.marketing.api.res.goods.ActivityMinProgramDetailVO;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.core.CartSceneEnum;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.repository.HeCartGoodsRepository;
import com.stbella.order.server.manager.MarketingManager;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import com.stbella.platform.order.api.res.PromotionInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.stbella.order.common.enums.ErrorCodeEnum.PROMOTION_CHANGE;


/**
 * 活动变更校验，判断前端提交的活动是否更新了
 */
@Component
@Slf4j
@SnowballComponent(name = "活动校验", desc = "")
public class PromotionValidator implements IExecutableAtom<FlowContext> {

    @Resource
    private MarketingManager marketingManager;

    @Resource
    private HeCartGoodsRepository heCartGoodsRepository;


    @Override
    public void run(FlowContext bizContext) {

        //校验活动商品数量
        List<SkuDetailInfo> skuDetailInfos = bizContext.getListAttribute(SkuDetailInfo.class);
        List<SkuDetailInfo> detailInfos = skuDetailInfos.stream().filter(o -> !o.getGift() && Objects.isNull(o.getPromotionInfo())).collect(Collectors.toList());

        CfgStoreEntity cfgStoreEntity = bizContext.getAttribute(CfgStoreEntity.class);
        Integer clientUid = (Integer) bizContext.getAttribute(BizConstant.ExtraKey.clientUid);

        Integer scene = (Integer)  bizContext.getAttribute(BizConstant.ExtraKey.scene);

        List<ActivityMinProgramDetailVO> activityMinProgramDetailVOS = marketingManager.queryStoreActivity(clientUid, cfgStoreEntity.getStoreId(), detailInfos, scene);
        Map<Integer, Long> activityMap = activityMinProgramDetailVOS.stream().collect(Collectors.toMap(ActivityMinProgramDetailVO::getActivityId, ActivityMinProgramDetailVO::getGmtModified));
        //判断提交上来的活动跟查询出来的活动是否一致

        List<PromotionInfo> promotionInfos = (List<PromotionInfo>) bizContext.getAttribute(BizConstant.ExtraKey.promotionInfos);

        for (PromotionInfo promotionInfo : promotionInfos) {
            Long gmtModified = activityMap.get(promotionInfo.getPromotionId());
            if (Objects.isNull(gmtModified) || Long.valueOf(promotionInfo.getUpdateAt()).compareTo(gmtModified) != 0) {
                promotionChange(bizContext);
            }
        }
//        if (promotionInfos.size() != activityMinProgramDetailVOS.size()) {
//            log.info("活动数量不一致，活动变更");
//            promotionChange(bizContext);
//        }

    }

    private void promotionChange(FlowContext bizContext) {
        Integer cartId = (Integer) bizContext.getAttribute(BizConstant.ExtraKey.cartId);
        if (cartId != -1) {
            log.info("活动变更，清空购物车中的活动商品");
            List<Integer> heCartGoodIdList = new ArrayList<>();
            heCartGoodsRepository.queryListByCartId(cartId).forEach(heCartGoodsEntity -> {
                if (Objects.nonNull(heCartGoodsEntity.getPromotionInfo()) && Objects.nonNull(heCartGoodsEntity.getPromotionInfo().getPromotionId())) {
                    heCartGoodIdList.add(heCartGoodsEntity.getId());
                }
            });
            if (!heCartGoodIdList.isEmpty()) {
                log.info("活动变更，清空购物车中的活动商品，商品ID={}", heCartGoodIdList);
                heCartGoodsRepository.deleteByIds(heCartGoodIdList);
            }
        } else {
            log.info("活动变更，购物车暂时没有创建，不需要清空");
        }
        throw new BusinessException(PROMOTION_CHANGE.code() + "", PROMOTION_CHANGE.desc());
    }

    @Override
    public boolean condition(FlowContext bizContext) {
        Integer scene = (Integer) bizContext.getAttribute(BizConstant.ExtraKey.scene);
        return !CartSceneEnum.CUSTOMIZE_AS_YOU_WISH.code().equals(scene);
    }
}

package com.stbella.order.server.contract.month.service.impl;

import com.stbella.order.domain.repository.MonthEsignTemplateParamRepository;
import com.stbella.order.server.contract.service.month.MonthEsignTemplateParamService;
import com.stbella.order.server.convert.AppMonthContractSignRecordConverter;
import com.stbella.order.server.order.month.res.MonthEsignTemplateParamVO;

import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 合同参数模版表,新版E签宝 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2021-11-03
 */
@Service
@Slf4j
public class MonthEsignTemplateParamServiceImpl implements MonthEsignTemplateParamService {

    @Resource
    private AppMonthContractSignRecordConverter appMonthContractSignRecordConverter;
    @Resource
    private MonthEsignTemplateParamRepository monthEsignTemplateParamRepository;

    @Override
    public List<MonthEsignTemplateParamVO> list(Integer orderType) {
        /**
         * 查询参数模版表中是否有数据
         */
        return appMonthContractSignRecordConverter.entity2MonthEsignTemplateParamVoList(monthEsignTemplateParamRepository.list(orderType));
    }

    @Override
    public List<MonthEsignTemplateParamVO> list(Integer orderType, Long esignTemplateId) {
        /**
         * 查询参数模版表中是否有数据
         */
        return appMonthContractSignRecordConverter.entity2MonthEsignTemplateParamVoList(monthEsignTemplateParamRepository.list(orderType,esignTemplateId));
    }
}

package com.stbella.order.server.order.nutrition.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.order.server.order.nutrition.entity.*;
import com.stbella.order.server.order.nutrition.mapper.OrderNutritionGoodsMapper;
import com.stbella.order.server.utils.BigDecimalUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 广禾堂-订单-商品列表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2022-09-14
 */
@Service
public class OrderNutritionGoodsServiceImpl extends ServiceImpl<OrderNutritionGoodsMapper, OrderNutritionGoodsPO> implements OrderNutritionGoodsService {

    @Resource
    private WdtSpuService wdtSpuService;
    @Resource
    private WdtSkuService wdtSkuService;
    @Resource
    private WdtSuiteService wdtSuiteService;
    @Resource
    private WdtSuiteSkuService wdtSuiteSkuService;


    @Override
    public void saveOrUpdateGoods(List<OrderNutritionGoodsPO> orderNutritionGoodsPOList) {

        //批量新增
        List<OrderNutritionGoodsPO> insert = new ArrayList<>();
        //批量更新
        List<OrderNutritionGoodsPO> update = new ArrayList<>();
        //批量删除
        List<Long> delete = new ArrayList<>();

        List<WdtSpuPO> spuList = wdtSpuService.list();
        List<WdtSkuPO> skuList = wdtSkuService.list();
        List<WdtSuitePO> suiteList = wdtSuiteService.list();
        List<WdtSuiteSkuPO> suiteSkuList = wdtSuiteSkuService.list();
        List<String> collect1 = orderNutritionGoodsPOList.stream().map(x -> x.getGoodsCode()).distinct().collect(Collectors.toList());
        for (OrderNutritionGoodsPO orderNutritionGoodsPO : orderNutritionGoodsPOList) {
            orderNutritionGoodsPO.setRetailPrice(BigDecimal.ZERO);
            orderNutritionGoodsPO.setCommodityTaxRate(BigDecimal.ZERO);
            orderNutritionGoodsPO.setCostPrice(BigDecimal.ZERO);
            Integer suite = orderNutritionGoodsPO.getSuite();
            //订单编号
            String orderNo = orderNutritionGoodsPO.getOutOrderNo();
            //编码
            String goodsCode = orderNutritionGoodsPO.getGoodsCode();
            if (suite == 0) {
                BigDecimal allCommodityTaxRate = BigDecimal.ZERO;
                Integer allNum = 0;
                //组合装
                Optional<WdtSuitePO> first = suiteList.stream().filter(s -> s.getSuiteNo().equals(goodsCode)).findFirst();
                if (first.isPresent()) {
                    WdtSuitePO wdtSuitePO = first.get();
                    orderNutritionGoodsPO.setRetailPrice(wdtSuitePO.getRetailPrice());
                    List<WdtSuiteSkuPO> collect = suiteSkuList.stream().filter(s -> s.getSuiteId().equals(wdtSuitePO.getSuiteId())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(collect)) {
                        BigDecimal allCostPrice = BigDecimal.ZERO;
                        for (WdtSuiteSkuPO wdtSuiteSkuPO : collect) {
                            Optional<WdtSkuPO> skuFirst = skuList.stream().filter(s -> s.getSpecNo().equals(wdtSuiteSkuPO.getSpecNo())).findFirst();
                            if (skuFirst.isPresent()) {
                                WdtSkuPO wdtSkuPO = skuFirst.get();
                                BigDecimal costPrice = wdtSkuPO.getCostPrice();
                                if (ObjectUtil.isNotNull(costPrice)) {
                                    allCostPrice = allCostPrice.add(costPrice.multiply(new BigDecimal(orderNutritionGoodsPO.getQuantity())));
                                }

                                Optional<WdtSpuPO> spuFirst = spuList.stream().filter(s -> s.getGoodsId().equals(wdtSkuPO.getGoodsId())).findFirst();
                                if (spuFirst.isPresent()) {
                                    WdtSpuPO wdtSpuPO = spuFirst.get();
                                    allNum++;
                                    allCommodityTaxRate = allCommodityTaxRate.add(wdtSpuPO.getCommodityTaxRate());
                                }
                            }
                        }
                        orderNutritionGoodsPO.setCostPrice(allCostPrice);
                    }
                }
                if (!allNum.equals(0)) {
                    orderNutritionGoodsPO.setCommodityTaxRate(BigDecimalUtil.divide(allCommodityTaxRate, new BigDecimal(allNum)));
                }
            } else {
                //单SKU
                Optional<WdtSkuPO> first = skuList.stream().filter(s -> s.getSpecNo().equals(goodsCode)).findFirst();
                if (first.isPresent()) {
                    WdtSkuPO wdtSkuPO = first.get();
                    orderNutritionGoodsPO.setRetailPrice(wdtSkuPO.getRetailPrice());
                    orderNutritionGoodsPO.setCostPrice(wdtSkuPO.getCostPrice().multiply(new BigDecimal(orderNutritionGoodsPO.getQuantity())));
                    Optional<WdtSpuPO> first1 = spuList.stream().filter(s -> s.getGoodsId().equals(wdtSkuPO.getGoodsId())).findFirst();
                    if (first1.isPresent()) {
                        WdtSpuPO wdtSpuPO = first1.get();
                        orderNutritionGoodsPO.setCommodityTaxRate(wdtSpuPO.getCommodityTaxRate());
                    }
                }
            }
        }
        List<OrderNutritionGoodsPO> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(orderNutritionGoodsPOList)) {
            list = this.list(new LambdaQueryWrapper<OrderNutritionGoodsPO>().in(OrderNutritionGoodsPO::getOutOrderNo, orderNutritionGoodsPOList.stream().map(OrderNutritionGoodsPO::getOutOrderNo).collect(Collectors.toList())));
        }
        if (CollectionUtil.isNotEmpty(list)) {
            //数据库存在的
            Map<String, List<OrderNutritionGoodsPO>> dbGroupByOrderNo = list.stream().collect(Collectors.groupingBy(OrderNutritionGoodsPO::getOutOrderNo));
            //请求的
            Map<String, List<OrderNutritionGoodsPO>> rqGroupByOrderNo = orderNutritionGoodsPOList.stream().collect(Collectors.groupingBy(OrderNutritionGoodsPO::getOutOrderNo));
            for (String orderNo : rqGroupByOrderNo.keySet()) {
                List<OrderNutritionGoodsPO> orderNutritionGoodsPOListDB = dbGroupByOrderNo.get(orderNo);
                List<OrderNutritionGoodsPO> orderNutritionGoodsPOListEQ = rqGroupByOrderNo.get(orderNo);
                for (OrderNutritionGoodsPO orderNutritionGoodsPO : orderNutritionGoodsPOListEQ) {
                    //编码
                    String goodsCode = orderNutritionGoodsPO.getGoodsCode();
                    if (CollectionUtil.isEmpty(orderNutritionGoodsPOListDB)) {
                        insert.add(orderNutritionGoodsPO);
                    } else {
                        Optional<OrderNutritionGoodsPO> first = orderNutritionGoodsPOListDB.stream().filter(o -> o.getGoodsCode().equals(goodsCode)).findFirst();
                        if (first.isPresent()) {
                            orderNutritionGoodsPO.setId(first.get().getId());
                            update.add(orderNutritionGoodsPO);
                        } else {
                            insert.add(orderNutritionGoodsPO);
                        }
                    }
                }
                if (CollectionUtil.isNotEmpty(orderNutritionGoodsPOListDB)) {
                    //判断有没有数据库有的但是api没有的
                    for (OrderNutritionGoodsPO orderNutritionGoodsPO : orderNutritionGoodsPOListDB) {
                        //编码
                        String goodsCode = orderNutritionGoodsPO.getGoodsCode();
                        Optional<OrderNutritionGoodsPO> first = orderNutritionGoodsPOListEQ.stream().filter(o -> o.getGoodsCode().equals(goodsCode)).findFirst();
                        if (!first.isPresent()) {
                            delete.add(orderNutritionGoodsPO.getId());
                        }
                    }
                }
            }
        } else {
            insert = orderNutritionGoodsPOList;
        }
        if (CollectionUtil.isNotEmpty(insert)) {
            this.saveBatch(insert);
        }
        if (CollectionUtil.isNotEmpty(update)) {
            this.updateBatchById(update);
        }
        if (CollectionUtil.isNotEmpty(delete)) {
            this.getBaseMapper().deleteBatchIds(delete);
        }
    }

    /**
     * 根据List<String> outOrderNoList 获取订单商品数据
     *
     * @param outOrderNoList
     */
    @Override
    public List<OrderNutritionGoodsPO> queryListBy(List<String> outOrderNoList) {
        if (ObjectUtil.isNotEmpty(outOrderNoList)) {
            LambdaQueryWrapper<OrderNutritionGoodsPO> lq = new LambdaQueryWrapper<>();
            lq.in(OrderNutritionGoodsPO::getOutOrderNo, outOrderNoList);
            return this.getBaseMapper().selectList(lq);
        } else {
            return new ArrayList<>();
        }
    }


}

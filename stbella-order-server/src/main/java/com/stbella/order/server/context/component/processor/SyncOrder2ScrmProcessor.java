package com.stbella.order.server.context.component.processor;

import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.server.manager.ScrmManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

/**
 * @Author: JJ
 * @Description: 同步订单到Scrm
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "同步订单到Scrm")
public class SyncOrder2ScrmProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private ScrmManager scrmManager;

    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity heOrder = bizContext.getAttribute(HeOrderEntity.class);
        scrmManager.pushScrmCustomerOrder(heOrder.getOrderId());

    }

    @Override
    public boolean condition(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        return orderEntity.isJustEffect();
    }
}

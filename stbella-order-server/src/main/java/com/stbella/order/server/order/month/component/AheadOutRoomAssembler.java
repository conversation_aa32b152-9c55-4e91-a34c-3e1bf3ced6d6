package com.stbella.order.server.order.month.component;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.care.server.care.vo.room.RoomStateQueryDetailInfoVO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.month.entity.SettingRoomTypeEntity;
import com.stbella.order.domain.order.production.GoodsEntity;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.context.component.StoreCurrencyContainer;
import com.stbella.order.server.convert.AppAheadOutRoomConverter;
import com.stbella.order.server.manager.TabClientManager;
import com.stbella.order.server.order.month.res.RoomStateQueryVO;
import com.stbella.order.server.utils.BigDecimalUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-12 16:36
 */
@Component
@Slf4j
public class AheadOutRoomAssembler {
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderGoodsRepository orderGoodsRepository;
    @Resource
    private GoodsRepository goodsRepository;
    @Resource
    private AppAheadOutRoomConverter appAheadOutRoomConverter;
    @Resource
    TabClientManager tabClientManager;
    @Resource
    private StoreRepository storeRepository;
    @Resource
    private SettingRoomTypeRepository settingRoomTypeRepository;

    /**
     * @param orderId
     * @return {@link RoomStateQueryVO}
     */
    public RoomStateQueryVO queryClientRoomDetail(Integer orderId) {
        HeOrderEntity orderEntity = orderRepository.queryOrderById(orderId);
        if (ObjectUtil.isEmpty(orderEntity)) {
            throw new BusinessException(ResultEnum.PARAM_ERROR, "订单信息不存在");
        }
        RoomStateQueryDetailInfoVO roomStateQueryDetailInfoVO = tabClientManager.queryClientRoomDetail(orderEntity.getOrderSn());
        Optional.ofNullable(roomStateQueryDetailInfoVO).orElseThrow(() -> new BusinessException(ResultEnum.PARAM_ERROR, "房态信息不存在"));
        //查询门店信息,找到门店类型
        log.info("orderEntity.getStoreId()={}", orderEntity.getStoreId());
        CfgStoreEntity cfgStore = storeRepository.queryCfgStoreById(orderEntity.getStoreId());
        log.info("cfgStore={}", JSONUtil.toJsonStr(cfgStore));
        String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(cfgStore.getStoreId());

        Optional.ofNullable(cfgStore).orElseThrow(() -> new BusinessException(ResultEnum.PARAM_ERROR, "门店不存在"));
        RoomStateQueryVO roomStateQueryVO = appAheadOutRoomConverter.cateVo2RoomStateQueryVo(roomStateQueryDetailInfoVO);
        HeOrderGoodsEntity orderGoodsEntity = orderGoodsRepository.getByOrderId(orderId);
        Optional.ofNullable(orderGoodsEntity).orElseThrow(() -> new BusinessException(ResultEnum.PARAM_ERROR, "订单商品不存在"));
        GoodsEntity goodsEntity = goodsRepository.selectById(orderGoodsEntity.getGoodsId());
        Optional.ofNullable(goodsEntity).orElseThrow(() -> new BusinessException(ResultEnum.PARAM_ERROR, "套餐商品不存在"));
        SettingRoomTypeEntity settingRoomTypeEntity = settingRoomTypeRepository.queryById(goodsEntity.getEcpRoomType());
        Optional.ofNullable(settingRoomTypeEntity).orElseThrow(() -> new BusinessException(ResultEnum.PARAM_ERROR, "ecp房型不存在"));
        roomStateQueryVO.setStoreName(cfgStore.getStoreName());
        roomStateQueryVO.setGoodsPriceOrgin(BigDecimalUtil.divide(new BigDecimal(orderGoodsEntity.getGoodsPriceOrgin()), new BigDecimal(100)));
        roomStateQueryVO.setRoomTypeName(settingRoomTypeEntity.getRoomTypeName());
        roomStateQueryVO.setCurrency(storeCurrencyCode);
        return roomStateQueryVO;
    }

    public RoomStateQueryDetailInfoVO queryRoomState(String orderSn) {
        return tabClientManager.queryClientRoomDetail(orderSn);
    }


}

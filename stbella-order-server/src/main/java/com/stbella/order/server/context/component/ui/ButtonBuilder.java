package com.stbella.order.server.context.component.ui;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.notice.entity.OaProcessIdRelationPO;
import com.stbella.notice.server.OaProcessIdRelationService;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.service.ContractOpponentAuthDomainService;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.order.month.res.OrderOperateButton;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: ji<PERSON><PERSON><PERSON>
 * @CreateTime: 2024-08-13  10:38
 * @Description: 按钮构建器
 */
@Component
@Slf4j
public class ButtonBuilder {

    @DubboReference
    private OaProcessIdRelationService oaProcessIdRelationService;
    @Resource
    private MonthContractSignRecordRepository monthContractSignRecordRepository;

    @Resource
    private MonthContractSignAgreementRepository monthContractSignAgreementRepository;

    @Resource
    private ContractSignRecordPaperRepository contractSignRecordPaperRepository;

    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @Resource
    private OrderRefundRepository orderRefundRepository;

    @Autowired
    List<OrderOperate> operateList;

    public List<OrderOperateButton> build(HeOrderEntity order) {

        OrderOperateContext context = buildContext(order);
        operateList.sort(Comparator.comparingInt(operate -> {
            return operate.getPriority();
        }));

        List<OrderOperateButton> buttons =  operateList.stream().map(operate -> {
                return operate.generateButton(context);
            }).filter(button -> {
                return button != null;
            }).collect(Collectors.toList());;

        return buttons;
    }

    protected OrderOperateContext buildContext(HeOrderEntity order) {

        // 退款记录
        List<HeOrderRefundEntity> refundList = orderRefundRepository.getRefundByOrderId(order.getOrderId());
        // 钉钉审批记录
        List<OaProcessIdRelationPO> approveList = oaProcessIdRelationService.getByOrderId(order.getOrderId().toString());
        // 收款记录
        List<HeIncomeRecordEntity> incomeList = incomeRecordRepository.getSuccessfulRecordListByOrderId(order.getOrderId());
        // 签约记录表
        List<MonthContractSignRecordEntity> mainContractList = monthContractSignRecordRepository.getListByOrderId(order.getOrderId().longValue());
        // 合同补充协议
        List<MonthContractSignAgreementEntity> agreementContractList = monthContractSignAgreementRepository.list(order.getOrderId().longValue(), 2);
        // 纸质合同
        List<ContractSignRecordPaperEntity> paperList = contractSignRecordPaperRepository.getByOrderId(order.getOrderId());

        OrderOperateContext context = OrderOperateContext.builder().order(order)
                .incomeList(incomeList)
                .refundList(refundList)
                .approveList(approveList)
                .mainContractList(mainContractList)
                .agreementContractList(agreementContractList)
                .paperList(paperList).build();

        return context;
    }
}

package com.stbella.order.server.context.component.processor.assets;

import cn.hutool.core.collection.CollectionUtil;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.production.GoodsAssertEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.production.OrderGiftExtendEntity;
import com.stbella.order.domain.order.production.OrderProductionExtendEntity;
import com.stbella.order.domain.repository.OrderGiftExtendRepository;
import com.stbella.order.domain.repository.OrderGoodsRepository;
import com.stbella.order.domain.repository.OrderProductionExtendRepository;
import com.stbella.order.server.order.order.req.ProductionExtendQueryListReq;
import com.stbella.order.server.utils.wangdian.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: wangchuang
 * @CreateTime: 2024-05-28  13:55
 * @Description: 生效订单资产
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "生效订单资产", desc = "生效订单中所有资产")
public class EffectAssetsProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    OrderGiftExtendRepository giftExtendRepository;
    @Resource
    private OrderProductionExtendRepository orderProductionExtendRepository;


    @Override
    public boolean condition(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        return orderEntity.isJustFullPayment();
    }

    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);

        //本期这里主要是产康资产生效。直接修改状态为生效。
        //1, 礼赠
        List<OrderGiftExtendEntity> giftExtendEntityList = giftExtendRepository.getByOrderId(order.getOrderId()).stream()
                .filter(gift -> GoodsAssertEnum.FREEZE.code().intValue() == gift.getStatus()).collect(Collectors.toList());


        List<OrderGiftExtendEntity> updateGiftList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(giftExtendEntityList)) {
            giftExtendEntityList.forEach(gift -> {
                OrderGiftExtendEntity updateGift = new OrderGiftExtendEntity();
                updateGift.setValidStartTime(DateUtils.getTenBitTimestamp());

                // 结束时间 = 当前时间加3个月，请使用datetime 加3个月
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime validEndTime = now.plusMonths(BizConstant.OrderAppKey.giftValidityMonth);
                updateGift.setValidEndTime(DateUtils.getTenBitTimestamp(validEndTime).longValue());


                updateGift.setId(gift.getId());
                updateGift.setValidityValue(0);
                updateGift.setValidityType(1);
                updateGift.setStatus(GoodsAssertEnum.OK.code());
                updateGift.setGmtModified(new Date());
                updateGiftList.add(updateGift);
            });
        }
        if (CollectionUtil.isNotEmpty(updateGiftList)) {
            giftExtendRepository.batchUpdateById(updateGiftList);
        }
        log.info("生效礼赠资产数量：{}", updateGiftList.size());

        //2, 产康资产
        ProductionExtendQueryListReq req = new ProductionExtendQueryListReq()
                .setOrderId(order.getOrderId())
                .setStatus(GoodsAssertEnum.FREEZE.code());
        List<OrderProductionExtendEntity> productionExtendEntityList = orderProductionExtendRepository.queryList(req);

        List<OrderProductionExtendEntity> updateProductionList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(productionExtendEntityList)) {
            productionExtendEntityList.forEach(production -> {
                OrderProductionExtendEntity updateModel = new OrderProductionExtendEntity();
                updateModel.setId(production.getId());
                updateModel.setValidStartTime(DateUtils.getTenBitTimestamp());
                updateModel.setValidityType(1);
                updateModel.setValidityValue(-1);
                //有效期12个月
                updateModel.setValidEndTime(BizConstant.OrderAppKey.PreparationForPregnancyTimestamp);
                updateModel.setStatus(GoodsAssertEnum.OK.code());
                updateModel.setGmtModified(new Date());
                updateProductionList.add(updateModel);

            });
        }
        if (CollectionUtil.isNotEmpty(updateProductionList)) {
            orderProductionExtendRepository.saveOrUpdateBatch(updateProductionList);
        }
        log.info("生效康数量：{}", updateProductionList.size());
    }
}

package com.stbella.order.server.context.component.assembler;

import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.server.order.month.enums.DiscountRuleResultEnum;
import com.stbella.platform.order.api.res.DecreaseApprovalRes;
import com.stbella.platform.order.api.res.DiscountRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

/**
 * 减免审批结果组装
 */
@Slf4j
@Component
@SnowballComponent(name = "减免审批结果组装", desc = "减免审批结果组装")
public class DecreaseRespAssembler implements IExecutableAtom<FlowContext> {

    @Override
    public void run(FlowContext bizContext) {

        DecreaseApprovalRes decreaseApprovalRes = new DecreaseApprovalRes();
        decreaseApprovalRes.setDiscountApproval(Boolean.FALSE);
        decreaseApprovalRes.setOrderModifyApproval(Boolean.FALSE);

        OrderReductionEntity orderReductionEntity = bizContext.getAttribute(OrderReductionEntity.class);

        //产品需求 合同修改审批与订单折扣审批互斥,并且以合同修改审批优先
        if (Boolean.TRUE.equals(orderReductionEntity.getModifyContractApprove())) {
            decreaseApprovalRes.setOrderModifyApproval(true);
        } else {
            DiscountRes discountRes = bizContext.getAttribute(DiscountRes.class);
            if (DiscountRuleResultEnum.APPROVAL_REQUIRED.getCode().equals(discountRes.getDiscountRuleResult())) {
                decreaseApprovalRes.setDiscountApproval(true);
                orderReductionEntity.setDisCountApprove(Boolean.TRUE);
            }
        }
        bizContext.setAttribute(DecreaseApprovalRes.class, decreaseApprovalRes);

    }

}

package com.stbella.order.server.strategy.pay;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.month.PayStatusV2Enum;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeUserProductionAmountListEntity;
import com.stbella.order.domain.order.production.UserProductionAmountPiLogEntity;
import com.stbella.order.domain.repository.HeUserProductionAmountListRepository;
import com.stbella.order.domain.repository.HeUserProductionAmountPiLogRepository;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.async.AsyncOrder;
import com.stbella.order.server.manager.CustomerBehaviorManager;
import com.stbella.order.server.order.month.enums.PayAmountEnum;
import com.stbella.order.server.order.month.enums.PayRecordEnum;
import com.stbella.order.server.order.month.request.pay.PayNotityRequest;
import com.stbella.order.server.order.month.service.OrderPayRecordService;
import com.stbella.order.server.order.nutrition.enums.NutritionPayRecordAuditStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * 支付策略-母婴订单
 * @date 2024/3/15 16:01
 */
@Component
@Slf4j
public class MonthOrderPayStrategy implements PayNotificationStrategy {
    @Resource
    private OrderPayRecordService orderPayRecordService;
    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private AsyncOrder asyncOrder;
    @Resource
    private CustomerBehaviorManager customerBehaviorManager;
    @Resource
    private HeUserProductionAmountListRepository heUserProductionAmountListRepository;
    @Resource
    private HeUserProductionAmountPiLogRepository heUserProductionAmountPiLogRepository;

    @Override
    public boolean handlePayNotification(PayNotityRequest payNotityRequest) {
        boolean editOrderInfo = false;
        //本地流水号
        String outTradeNo = payNotityRequest.getOutTradeNo();
        HeIncomeRecordEntity heIncomeRecordEntity = incomeRecordRepository.getRecordByIncomeSn(outTradeNo);
        Integer orderId = heIncomeRecordEntity.getOrderId();

        int payTimeSec = (int) (payNotityRequest.getPayTime().getTime() / 1000);
        //CSP订单处理
        if (!Objects.equals(PayAmountEnum.ANTECEDENT_MONEY.getCode(), heIncomeRecordEntity.getReceiptType())) {
            HeOrderEntity entity = orderRepository.getByOrderId(orderId);

            //todo 这个逻辑应该是写到领域层 by jijunjian
            Integer payAmount = ObjectUtil.isEmpty(entity.getPayAmount()) ? 0 : entity.getPayAmount();
            Integer newPaidAmount = entity.getPaidAmount() + payNotityRequest.getPayAmount().multiply(new BigDecimal(100)).intValue();
            entity.setPaidAmount(newPaidAmount);
            Integer percentFirstTime = entity.getPercentFirstTime();

            if (ObjectUtil.isNotEmpty(entity.getPayFirstTime()) && entity.getPayFirstTime() == 0) {
                entity.setPayFirstTime(payTimeSec);
            }
            //支付金额>=50%
            if (newPaidAmount * 10 >= payAmount * 5 && ObjectUtil.isNotEmpty(percentFirstTime) && percentFirstTime == 0) {

                //添加客户行为
                customerBehaviorManager.signOrderPushCustomerBehavior(orderId, payNotityRequest.getPayTime().getTime() / 1000);

                //修改产康金为可见
                List<HeUserProductionAmountListEntity> byOrderIdList = heUserProductionAmountListRepository.getByOrderId(orderId, Arrays.asList(0, 1));
                if (ObjectUtil.isNotEmpty(byOrderIdList)) {
                    byOrderIdList.forEach(x -> {
                        x.setIsShow(1);
                        x.setUpdatedAt(Math.toIntExact(System.currentTimeMillis() / 1000));
                    });
                    log.info("修改产康金为可见={}", JSONUtil.toJsonStr(byOrderIdList));
                    heUserProductionAmountListRepository.batchUpdateById(byOrderIdList);

                    //往产康金操作记录表存
                    log.info("往产康金操作记录表存={}", entity.getOrderId());
                    //订单总的产康金
                    long sum = byOrderIdList.stream().mapToLong(HeUserProductionAmountListEntity::getTotalAmount).sum();
                    UserProductionAmountPiLogEntity userProductionAmountPiLogEntity = new UserProductionAmountPiLogEntity();
                    userProductionAmountPiLogEntity.setBasicId(entity.getBasicUid());
                    userProductionAmountPiLogEntity.setProductionId(0);
                    userProductionAmountPiLogEntity.setType(1);
                    userProductionAmountPiLogEntity.setOrderId(entity.getOrderId().longValue());
                    userProductionAmountPiLogEntity.setOrderName(OmniOrderTypeEnum.getValueByCode(entity.getOrderType()));
                    userProductionAmountPiLogEntity.setTitle("赠送产康金");
                    userProductionAmountPiLogEntity.setOperateAmount(sum);
                    userProductionAmountPiLogEntity.setDetail("暂不可用：客户未入馆");
                    userProductionAmountPiLogEntity.setCreatedAt((int) (System.currentTimeMillis() / 1000));
                    userProductionAmountPiLogEntity.setUpdatedAt((int) (System.currentTimeMillis() / 1000));
                    Boolean aBoolean = heUserProductionAmountPiLogRepository.saveOne(userProductionAmountPiLogEntity);
                    log.info("添加用户产康金Pi端使用记录={},{}", JSONUtil.toJsonStr(userProductionAmountPiLogEntity), aBoolean);
                }

            }

            Integer realAmount = (ObjectUtil.isEmpty(entity.getRealAmount()) ? 0 : entity.getRealAmount()) + payNotityRequest.getPayAmount().multiply(new BigDecimal(100)).intValue();
            entity.setRealAmount(realAmount);

            //支付状态
            if (newPaidAmount < entity.getPayAmount()) {
                entity.setPayStatus(PayStatusV2Enum.NO_PAY_OFF.getCode());
            } else if (newPaidAmount.equals(entity.getPayAmount())) {
                entity.setPayStatus(PayStatusV2Enum.PAY_OFF.getCode());
                entity.setPayFinishAt(System.currentTimeMillis() / 1000);
            } else {
                entity.setPayStatus(PayStatusV2Enum.EXCESS_PAY.getCode());
            }
            Integer integer = orderRepository.updateOrderMonthByOrderId(entity);
            editOrderInfo = integer > 0;
            if (editOrderInfo) {
                //剔除业绩的不需要报单
                if (!ObjectUtil.equals(percentFirstTime, -1) && !ObjectUtil.equals(entity.getOperationType(), 1)) {
                    // 异步调用报单
                    asyncOrder.asyncClientSendNotice(orderId, (int) (payNotityRequest.getPayTime().getTime() / 1000));
                }
                HeIncomeRecordEntity heIncomeRecord = incomeRecordRepository.getRecordByIncomeSn(outTradeNo);


                HeOrderEntity byOrderId = orderRepository.getByOrderId(orderId);

                //异步调用增加积分
                log.info("异步调用积分添加接口01req:{}", JSONUtil.toJsonStr(payNotityRequest));
                asyncOrder.addOrderIntegral(byOrderId, NutritionPayRecordAuditStatusEnum.AUDIT.getCode(), heIncomeRecord);
                //同步订单支付记录累计金额
                orderPayRecordService.syncSubtotalIncome(orderId);
            }

        } else {
            //押金不用更新订单信息
            editOrderInfo = true;
        }
        return editOrderInfo;
    }

    @Override
    public PayRecordEnum getPayRecordEnum() {
        return PayRecordEnum.BK;
    }

}

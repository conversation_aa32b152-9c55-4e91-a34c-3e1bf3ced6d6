package com.stbella.order.server.context.component.spi;

import com.stbella.base.server.ding.response.CreateOrderApproveRecordVO;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.server.context.component.assembler.CustomerAssembler;
import com.stbella.order.server.context.component.processor.InitiateDiscountApprovalProcessor;
import com.stbella.platform.order.api.req.CreateOrderReq;
import top.primecare.snowball.extpoint.annotation.FuncPointSpi;
import top.primecare.snowball.extpoint.definition.IFuncSpiSupport;

/**
 * <AUTHOR>
 * @description: 折扣审批发起扩展点
 *
 */
@FuncPointSpi(name = "折扣审批发起扩展点", attachToComponent = InitiateDiscountApprovalProcessor.class)
public interface InitiateDiscountApprovalSpi extends IFuncSpiSupport<HeOrderEntity, CreateOrderApproveRecordVO> {

}

package com.stbella.order.server.context.component.processor;

import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundGoodsEntity;
import com.stbella.order.domain.repository.HeOrderRefundGoodsRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
@SnowballComponent(name = "订单退货数量查询", desc = "订单退货数量查询")
public class OrderRefundSuccessGoodsAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private HeOrderRefundGoodsRepository orderRefundGoodsRepository;

    @Override
    public boolean condition(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        return Objects.nonNull(orderEntity);
    }

    @Override
    public void run(FlowContext bizContext) {

        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);

        List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList = orderRefundGoodsRepository.queryByOrderId(orderEntity.getOrderId());

        bizContext.setListAttribute(HeOrderRefundGoodsEntity.class, orderRefundGoodsEntityList);
    }

}

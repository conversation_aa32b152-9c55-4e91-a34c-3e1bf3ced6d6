package com.stbella.order.server.strategy.approval;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.base.server.sms.enums.SmsTemplateV2Enum;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.month.server.request.DepositRefundApprovalRequest;
import com.stbella.month.server.request.RefundApprovalRequest;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OrderRefundStatusEnum;
import com.stbella.order.common.enums.month.AheadOutRoomEnum;
import com.stbella.order.common.enums.month.RefundRecordPayStatusEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.JsonUtil;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.async.AsyncOrder;
import com.stbella.order.server.manager.SmsManager;
import com.stbella.order.server.order.month.enums.CustomerComplaintsStatusEnum;
import com.stbella.order.server.order.month.enums.OrderEventEnum;
import com.stbella.order.server.order.month.req.AheadOutRoomQuery;
import com.stbella.order.server.order.month.request.approval.ApprovalStatusInfo;
import com.stbella.order.server.order.month.service.HeOrderService;
import com.stbella.order.server.order.month.service.OrderPayV2Service;
import com.stbella.order.server.producer.OrderEventProducer;
import com.stbella.order.server.utils.BigDecimalUtil;
import com.stbella.order.server.utils.RefundAchievementCalculateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 门店商品退款-意向金账户处理
 */
@Slf4j
@Component
public class OldOrderBalanceRefundApprovalStrateg implements ApprovalStrategy {

    @Resource
    @Lazy
    private OrderPayV2Service orderPayV2Service;
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private SmsManager smsManager;
    @Resource
    private AheadOutRoomRepository aheadOutRoomRepository;
    @Resource
    private AsyncOrder asyncOrder;
    @Resource
    private HeOrderService heOrderService;

    @Resource
    private OrderEventProducer orderEventProducer;
    @Resource
    private HeCustomerComplaintsRepository customerComplaintsRepository;

    @Override
    public void handleApproval(OrderApproveRecordTypeEnum type, String params, String messageId, ApprovalStatusInfo approvalStatusInfo, String processId) {

        log.info("门店商品发起退款至意向金账户-处理开始params:{}, processId:{}, messageId:{}", params, processId, messageId);
        RefundApprovalRequest refundApprovalRequest =  JSONObject.parseObject(params, RefundApprovalRequest.class);

        if (Objects.isNull(refundApprovalRequest) || Objects.isNull(refundApprovalRequest.getOrderRefundId())){
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "退款记录Id不能为空");
        }
        HeOrderRefundEntity heOrderRefundEntity1 = orderRefundRepository.getOneById(refundApprovalRequest.getOrderRefundId());
        if (ObjectUtil.isEmpty(heOrderRefundEntity1)) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "审批完成退款失败,找不到对应的退款记录,退款ID=" + refundApprovalRequest.getOrderRefundId());
        }

        boolean agree = approvalStatusInfo.isAgree();
        boolean finish = approvalStatusInfo.isFinish();
        boolean refuse = approvalStatusInfo.isRefuse();
        boolean terminate = approvalStatusInfo.isTerminate();
        boolean isRefund = false;
        String originatorPhone = refundApprovalRequest.getPhone();
        Integer refundAmount = AmountChangeUtil.changeY2FFoInt(refundApprovalRequest.getRefundAmount());
        if (finish && agree) {
            isRefund = true;
            heOrderRefundEntity1.setActualAmount(refundAmount);
            heOrderRefundEntity1.setAgreeAt(System.currentTimeMillis() / 1000);
            heOrderRefundEntity1.setFinishAt(heOrderRefundEntity1.getAgreeAt());
            heOrderRefundEntity1.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_3.getCode());
            try {
                log.info("审批通过发送短信通知（意向金）:{},refundType:{}" + originatorPhone, heOrderRefundEntity1.getRefundType());
                smsManager.sendMessage(originatorPhone, null, null, SmsTemplateV2Enum.ORDER1_REFUND_SUCCESS, new String[]{refundApprovalRequest.getCustomerName(), "退款至意向金", refundApprovalRequest.getRefundAmount().setScale(2).toString()});
            } catch (Exception e) {
                log.error("审批通过发送短信通知（意向金）异常：{}，手机号：{}，内容：{}", e.getMessage(), originatorPhone, new String[]{refundApprovalRequest.getCustomerName(), refundApprovalRequest.getRefundAmount().setScale(2).toString()});
            }
        }
        if (terminate) {
            heOrderRefundEntity1.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_2.getCode());
            orderRefundRepository.updateOneById(heOrderRefundEntity1);
        }
        if (finish && refuse) {
            try {
                heOrderRefundEntity1.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_2.getCode());
                log.info("审批拒绝发送短信通知（意向金）:{},refundType:{}" + originatorPhone, heOrderRefundEntity1.getRefundType());
                smsManager.sendMessage(originatorPhone, null, null, SmsTemplateV2Enum.ORDER1_REFUND_REFUSE, new String[]{refundApprovalRequest.getCustomerName()});
            } catch (Exception e) {
                log.error("审批拒绝发送短信通知（意向金）异常：{}，手机号：{}，内容：{}", e.getMessage(), originatorPhone, new String[]{refundApprovalRequest.getCustomerName()});
            }
        }
        HeOrderEntity heOrder = orderRepository.getByOrderId(heOrderRefundEntity1.getOrderId());
        HeIncomeRecordEntity recordByIncomeSn = incomeRecordRepository.getOneById(heOrderRefundEntity1.getOrderGoodId());
        //修改收款记录的冻结金额
        recordByIncomeSn.setFreezeAmount(recordByIncomeSn.getFreezeAmount() - refundAmount);
        if (!isRefund) {
            //修改提前离馆可退金额
            AheadOutRoomQuery query = new AheadOutRoomQuery();
            query.setOrderId(heOrderRefundEntity1.getOrderId());
            query.setState(AheadOutRoomEnum.STATE_DISENABLE.code());
            AheadOutRoomEntity aheadOutRoomEntity = aheadOutRoomRepository.queryByOrderId(query);
            //如果提前离馆同意时间早于退款申请时间则要修改提前离馆可退金额
            if (ObjectUtil.isNotEmpty(aheadOutRoomEntity) && aheadOutRoomEntity.getAgreeAt().before(DateUtil.date(heOrderRefundEntity1.getCreatedAt() * 1000))) {
                aheadOutRoomEntity.setRemainingRefundableAmount(aheadOutRoomEntity.getRemainingRefundableAmount() + refundAmount);
                aheadOutRoomRepository.updateOutRoom(aheadOutRoomEntity);
            }
        } else {
            try {
                List<Long> refundBalance = orderPayV2Service.refundBalance(refundApprovalRequest.getOrderRefundId());
                heOrderRefundEntity1.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode());
                heOrderRefundEntity1.setPaymentResult(JSONUtil.toJsonStr(refundBalance));
                //有业绩时间才退业绩
                if (ObjectUtil.isNotEmpty(heOrder.getPercentFirstTime()) && !heOrder.getPercentFirstTime().equals(0)) {
                    HeCustomerComplaintsEntity customerComplaintsEntity = customerComplaintsRepository.selectByOrderRefundId(heOrderRefundEntity1.getId());
                    if (ObjectUtil.isEmpty(customerComplaintsEntity)) {
                        log.info("开始扣业绩");
                        //退款跟客诉无关联才扣业绩，否则根据业绩的退款金额当做扣除业绩的金额
                        HeCustomerComplaintsEntity heCustomerComplaintsEntity = customerComplaintsRepository.selectByOrderRefundId(heOrderRefundEntity1.getId());
                        if (ObjectUtil.isEmpty(heCustomerComplaintsEntity)) {
                            //  退款业绩计算  退单业绩 = 实际退款金额  * 签单金额 / 累计已付金额
                            heOrderRefundEntity1.setRefundAchievement(calculateRefundAchievement(heOrderRefundEntity1.getActualAmount(), heOrder.getPaidAmount(), heOrder.getPayAmount()));
                        }
                    }
                }
                //修改收款记录的已退金额
                recordByIncomeSn.setAlreadyRefundAmount(recordByIncomeSn.getAlreadyRefundAmount() + refundAmount);
                //订单实际金额
                if (OmniOrderTypeEnum.MONTH_ORDER.getCode().equals(heOrder.getOrderType())){
                    heOrder.setRealAmount(heOrder.getRealAmount() - refundAmount);
                }
                smsManager.sendMessage(originatorPhone, SmsTemplateV2Enum.ORDER1_REFUND_BALANCE_CREATOR, new String[]{heOrder.getOrderSn(), refundApprovalRequest.getRefundAmount().toString(), refundApprovalRequest.getCustomerName()});
                smsManager.sendMessage(refundApprovalRequest.getCustomerPhone(), SmsTemplateV2Enum.ORDER1_REFUND_BALANCE_CUSTOMER, new String[]{heOrder.getOrderSn(), refundApprovalRequest.getRefundAmount().toString()});
            }catch (Exception e){
                log.error("根据退款Id:{}发起退至意向金账户发生异常 msg:{}", refundApprovalRequest.getOrderRefundId(), e.getMessage());
                heOrderRefundEntity1.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_5.getCode());
            }
        }
        heOrder.setRefundStatus(heOrderService.getRefundStatusByActualAmount(heOrderRefundEntity1.getOrderId(), refundAmount, isRefund, true));
        orderRepository.updateOrderMonthByOrderId(heOrder);
        incomeRecordRepository.updateRecord(recordByIncomeSn);
        orderRefundRepository.updateOneById(heOrderRefundEntity1);
        //异步退款-积分处理
        HeOrderRefundEntity orderRefundEntity = orderRefundRepository.getOneById(heOrderRefundEntity1.getId());
        log.info("退款审批（意向金退回）-积分处理,订单参数={},退款参数={}", heOrder, orderRefundEntity);
        asyncOrder.refundDeductIntegral(heOrder, orderRefundEntity);
        if (!heOrder.isDepositOrder()){
            HeOrderEntity heOrderLast = orderRepository.getByOrderId(heOrderRefundEntity1.getOrderId());
            if (heOrderLast.getRefundStatus().equals(OrderRefundStatusEnum.PARTIAL_REFUND.getCode())) {
                orderEventProducer.sendMq(OrderEventEnum.PART_REFUND.getCode(), heOrder.getOrderSn(), heOrderRefundEntity1.getRefundNature());
            } else if (heOrderLast.getRefundStatus().equals(OrderRefundStatusEnum.FULL_REFUND.getCode())) {
                orderEventProducer.sendMq(OrderEventEnum.FULL_REFUND.getCode(), heOrder.getOrderSn(), heOrderRefundEntity1.getRefundNature());
            }
        }

        if (isRefund) {
            this.opComplaint(heOrderRefundEntity1.getId());
        }
    }


    public Integer calculateRefundAchievement(Integer actualAmount, Integer paidAmount, Integer payAmount) {
        // 使用工具类的计算方法
        return RefundAchievementCalculateUtil.calculateRefundAchievement(actualAmount, paidAmount, payAmount);
    }

    private void opComplaint(Integer id) {
        //处理客诉
        HeCustomerComplaintsEntity customerComplaintsEntity = customerComplaintsRepository.selectByOrderRefundId(id);
        if (ObjectUtil.isNotEmpty(customerComplaintsEntity)) {
            customerComplaintsEntity.setComplaintStatus(CustomerComplaintsStatusEnum.SUCCESS.getCode());
            customerComplaintsEntity.setResolveFinish(new Date());
            customerComplaintsRepository.update(customerComplaintsEntity);
        }
    }

    @Override
    public OrderApproveRecordTypeEnum getOrderApproveRecordTypeEnum() {
        return OrderApproveRecordTypeEnum.ORDER_BALANCE_REFUND;
    }
}

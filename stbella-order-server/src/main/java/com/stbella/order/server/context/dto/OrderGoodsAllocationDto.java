package com.stbella.order.server.context.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 分摊记录
 *
 * <AUTHOR> @since 2022-11-01
 */
@Data
@Accessors(chain = true)
public class OrderGoodsAllocationDto {

    @ApiModelProperty(value = "订单商品自增id")
    private Integer orderGoodsId;

    @ApiModelProperty(value = "订单商品sn")
    private String orderGoodsSn;

    @ApiModelProperty(value = "购买数量")
    private Integer goodsNum;

    @ApiModelProperty(value = "商品分摊原单价")
    private Integer goodsPriceOrigin;

    @ApiModelProperty(value = "商品原总价（商品原单价 * num）")
    private Integer totalAmount = 0;

    @ApiModelProperty(value = "已付")
    private Integer paid = 0;

    @ApiModelProperty(value = "本次分摊")
    private Integer allocation = 0;

    @ApiModelProperty(value = "退款退款金额")
    private Integer returnAndRefund = 0;

    @ApiModelProperty(value = "退款退款金额")
    private Integer onlyRefund = 0;

    @ApiModelProperty(value = "退回重付")
    private Integer tempRefund = 0;

    @ApiModelProperty(value = "普通退款金额")
    private Integer normalRefund = 0;

    @ApiModelProperty(value = "记录Id")
    private Integer incomeId = 0;

    @ApiModelProperty(value = "待支付金额")
    private Integer waitPayAmount = 0;
}

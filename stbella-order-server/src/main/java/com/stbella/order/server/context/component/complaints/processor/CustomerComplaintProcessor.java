package com.stbella.order.server.context.component.complaints.processor;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.base.server.ding.MonthDingService;
import com.stbella.base.server.ding.response.CreateOrderApproveRecordVO;
import com.stbella.core.base.Operator;
import com.stbella.core.enums.BrandTypeEnum;
import com.stbella.core.result.Result;
import com.stbella.customer.server.ecp.entity.UserPO;
import com.stbella.customer.server.ecp.service.UserService;
import com.stbella.notice.entity.OaProcessIdRelationPO;
import com.stbella.notice.enums.AuditTypeEnum;
import com.stbella.notice.server.OaProcessIdRelationService;
import com.stbella.order.common.constant.CustomerComplaintConstant;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OmniRefundApproveEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.entity.HeCartGoodsEntity;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.production.GoodsEntity;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.order.cts.enums.WithAndWithoutEnum;
import com.stbella.order.server.order.month.enums.*;
import com.stbella.order.server.order.month.req.SubmitRefundApplyV2Request;
import com.stbella.order.server.order.month.res.SelectRespVO;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.order.server.utils.JsonUtil;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import com.stbella.platform.order.api.req.CustomerComplaintsCreateRefundReq;
import com.stbella.platform.order.api.req.CustomerComplaintsCreateReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
@SnowballComponent(name = "客诉闭环审批", desc = "客诉闭环审批")
public class CustomerComplaintProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private ClientRepository clientRepository;
    @DubboReference(timeout = 50000)
    private MonthDingService monthDingService;
    @Resource
    HeCustomerComplaintsRepository heCustomerComplaintsRepository;
    @Resource
    private GoodsRepository goodsRepository;
    @DubboReference
    private OaProcessIdRelationService oaProcessIdRelationService;
    @Resource
    private HeCartGoodsRepository heCartGoodsRepository;
    @DubboReference
    private UserService userService;
    @Resource
    private StoreRepository storeRepository;

    @Resource
    private OrderRefundRepository orderRefundRepository;


    @Override
    public void run(FlowContext bizContext) {

        HeCustomerComplaintsEntity customerComplaintsEntity = bizContext.getAttribute(HeCustomerComplaintsEntity.class);
        //取最新的
        customerComplaintsEntity = heCustomerComplaintsRepository.selectById(customerComplaintsEntity.getId());

        CustomerComplaintsCreateReq complaintsCreateReq = bizContext.getAttribute(CustomerComplaintsCreateReq.class);

        HeOrderEntity orderEntity = (HeOrderEntity) bizContext.getAttribute(CustomerComplaintConstant.COMPLAINT_ORDER);

        HashMap<String, String> param = getApproveParam(bizContext, complaintsCreateReq, orderEntity, customerComplaintsEntity);

        Result<CreateOrderApproveRecordVO> approveResult = monthDingService.createApprovalV2(param, complaintsCreateReq.getOperator().getOperatorPhone(), AuditTypeEnum.CUSTOMER_COMPLAINT_CLOSED_LOOP.getCode());

        CreateOrderApproveRecordVO data = approveResult.getData();
        Integer status = data.getStatus();


        if (OmniRefundApproveEnum.REFUND_RECORD_1.getCode().equals(status) && ObjectUtil.isNotEmpty(data.getQwId())) {
            //审批发起成功
            customerComplaintsEntity.setComplaintStatus(CustomerComplaintsStatusEnum.APPROVAL_ING.getCode());
            bizContext.setAttribute(CustomerComplaintConstant.APPROVE_STATUS, OmniRefundApproveEnum.REFUND_RECORD_1.getCode());
            customerComplaintsEntity.setApproveId(data.getId());


            OaProcessIdRelationPO oneByLocalProcessId = oaProcessIdRelationService.getOneByLocalProcessId(data.getId());
            if (ObjectUtil.isNotEmpty(oneByLocalProcessId)) {
                LinkedHashMap read = JsonUtil.read(oneByLocalProcessId.getParam(), LinkedHashMap.class);
                Integer refundId = (Integer) bizContext.getAttribute(CustomerComplaintConstant.COMPLAINT_REFUND_ID);
                read.put("orderRefundId", refundId);
                oneByLocalProcessId.setParam(JsonUtil.write(read));
                oaProcessIdRelationService.updateById(oneByLocalProcessId);
            }

        } else {
            //发起失败
            customerComplaintsEntity.setComplaintStatus(CustomerComplaintsStatusEnum.APPROVAL_FAILED.getCode());
            bizContext.setAttribute(CustomerComplaintConstant.APPROVE_STATUS, OmniRefundApproveEnum.REFUND_RECORD_0.getCode());
        }

        customerComplaintsEntity.update();
    }

    private HashMap<String, String> getApproveParam(FlowContext bizContext, CustomerComplaintsCreateReq complaintsCreateReq, HeOrderEntity orderEntity, HeCustomerComplaintsEntity customerComplaintsEntity) {

        Integer clientUid = orderEntity.getClientUid();

        TabClientEntity tabClientById = clientRepository.getTabClientById(clientUid);

        Integer storeId = tabClientById.getStoreId();

        CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(storeId);

        HashMap<String, String> param = new HashMap<>();
        param.put("门店名称", complaintsCreateReq.getStoreName());
        param.put("品牌", OmniOrderTypeEnum.getHomeOrderTypeCodeList().contains(orderEntity.getOrderType()) ? "予家" : BrandTypeEnum.getValueByCode(cfgStoreEntity.getType()));
        param.put("订单号", complaintsCreateReq.getOrderSn());
        param.put("客户姓名", complaintsCreateReq.getCustomerName());
        param.put("客户手机号", complaintsCreateReq.getCustomerPhone());
        param.put("客户来源", CustomerFromTypeEnum.getOrderListValueByCode(tabClientById.getFromType()));
        param.put("时间", DateUtil.formatDateTime(new Date(complaintsCreateReq.getEventTime())));
        param.put("地点", complaintsCreateReq.getEventLocation());
        param.put("人物", complaintsCreateReq.getInvolvedPersons());
        param.put("起因", complaintsCreateReq.getCause());
        param.put("经过", complaintsCreateReq.getProcess());
        param.put("结果", complaintsCreateReq.getResult());

        List<String> evidenceList = complaintsCreateReq.getEvidence();
        if (CollectionUtil.isEmpty(evidenceList)) {
            evidenceList = new ArrayList<>();
        }

        param.put("凭证", JsonUtil.write(evidenceList));
        String complaintLevelStr = complaintsCreateReq.getComplaintLevelStr();
        // 用 —— 代替 / 并且把括号去掉 () ,数据是 一级客诉(测试)/二级客诉 切换成 一级客诉测试——二级客诉
        complaintLevelStr = complaintLevelStr.replaceAll("/", "——").replaceAll("[()]", "");

        param.put("客诉等级", complaintLevelStr);
        if (ObjectUtil.isNotEmpty(complaintsCreateReq.getResponsibilityType())) {
            param.put("定责类型", CustomerComplaintsResponsibilityEnum.getValueByCode(complaintsCreateReq.getResponsibilityType()));
        }
        param.put("主要责任人", complaintsCreateReq.getPrimaryResponsiblePersonStr());

        if (CollectionUtil.isNotEmpty(complaintsCreateReq.getSecondaryResponsiblePersonStr())) {
            param.put("次要责任人", complaintsCreateReq.getSecondaryResponsiblePersonStr().toString());
        }

        param.put("供应商名称", complaintsCreateReq.getSupplierIdStr());
        param.put("产康主供应商名称", complaintsCreateReq.getMainSupplierIdStr());
        param.put("三方产康师", complaintsCreateReq.getThirdPartyTherapistIdStr());
        param.put("三方对接人姓名", complaintsCreateReq.getThirdPartyContactName());
        param.put("三方对接人手机号", complaintsCreateReq.getThirdPartyContactPhone());

        CustomerComplaintsCreateRefundReq customerComplaintsCreateRefundReq = complaintsCreateReq.getCreateRefundReq();

        boolean newOrder = ObjectUtil.isNotEmpty(orderEntity.getVersion()) && orderEntity.getVersion().compareTo(new BigDecimal(3.0)) >= 0;

        BigDecimal refundAmount = BigDecimal.ZERO;

        Integer refundType = null;

        StringBuilder refundInfo = new StringBuilder();

        if (newOrder) {
            CreateRefundReq newRefund = customerComplaintsCreateRefundReq.getNewCreateRefundReq();


            if (ObjectUtil.isNotEmpty(newRefund) && ObjectUtil.isNotEmpty(newRefund.getOrderId())) {
                refundType = newRefund.getRefundType();

                List<CreateRefundReq.GoodsInfo> goodsInfoList = newRefund.getGoodsInfoList();
                for (CreateRefundReq.GoodsInfo goodsInfo : goodsInfoList) {

                    List<CreateRefundReq.GoodsRefundAmountInfo> refundGoodsAmountInfoList = goodsInfo.getRefundGoodsAmountInfoList();

                    Optional<CreateRefundReq.GoodsRefundAmountInfo> ckjFirst = refundGoodsAmountInfoList.stream().filter(r -> r.getModel().equals(PayMethodEnum.CJK.getModel())).findFirst();

                    Optional<CreateRefundReq.GoodsRefundAmountInfo> cashFirst = refundGoodsAmountInfoList.stream().filter(r -> r.getModel().equals(PayMethodEnum.CASH.getModel())).findFirst();

                    BigDecimal ckjAmount = null;
                    BigDecimal cashAmount = null;

                    if (ckjFirst.isPresent()) {
                        ckjAmount = ckjFirst.get().getAmount();
                    }

                    if (cashFirst.isPresent()) {
                        cashAmount = cashFirst.get().getAmount();
                    }
                    //[商品名称]退货数量[n]退款金额[￥0.00]退产康金[￥0.00]；[商品名称]退货数量[n]退款金额[￥0.00]退产康金[￥0.00]；[商品名称]退货数量[n]退款金额[￥0.00]退产康金[￥0.00]
                    refundInfo.append("").append(goodsInfo.getGoodsName()).append("，").append("退货数量：").append(goodsInfo.getRefundNum());
                    if (ObjectUtil.isNotEmpty(cashAmount)) {
                        refundInfo.append("退款金额").append("￥").append(cashAmount.toEngineeringString());
                    }
                    if (ObjectUtil.isNotEmpty(ckjAmount)) {
                        refundInfo.append("退产康金").append("￥").append(ckjAmount.toEngineeringString());
                    }
                    refundInfo.append(";").append("\n");
                }

                List<CreateRefundReq.GoodsRefundAmountInfo> refundAmountInfoList = newRefund.getRefundAmountInfoList();

                refundAmount = refundAmountInfoList.stream().filter(o->o.getModel().equals(PayMethodEnum.CJK.getModel()) || o.getModel().equals(PayMethodEnum.CASH.getModel()))
                        .map(CreateRefundReq.GoodsRefundAmountInfo::getAmount) // 提取 BigDecimal 字段
                        .reduce(BigDecimal.ZERO, BigDecimal::add); // 求和操作
            }


        } else {
            SubmitRefundApplyV2Request oldRefund = customerComplaintsCreateRefundReq.getOldCreateRefundReq();
            if (ObjectUtil.isNotEmpty(oldRefund) && ObjectUtil.isNotEmpty(oldRefund.getOrderId())) {
                refundType = oldRefund.getRefundModel();
                refundAmount = oldRefund.getRefundAmount();
            }
        }


        Integer cartId = complaintsCreateReq.getCreateRefundReq().getCartId();

        if (ObjectUtil.isNotEmpty(cartId)) {
            setCartInfo(cartId, param);
        } else {
            param.put("商品赔偿价值", "0");
        }

        param.put("额外赔偿意见", WithAndWithoutEnum.NO.getValue());

        if (ObjectUtil.isNotEmpty(complaintsCreateReq.getCreateRefundReq().getHasExtraCompensationOpinion())) {
            param.put("额外赔偿意见", WithAndWithoutEnum.getValueByCode(complaintsCreateReq.getCreateRefundReq().getHasExtraCompensationOpinion()));
        }

        param.put("订单处理意见", refundAmount.toEngineeringString());
        param.put("扣除门店业绩", "￥" + "0.00");
        if (Objects.nonNull(customerComplaintsEntity.getCashRefundId())){
            HeOrderRefundEntity oneById = orderRefundRepository.getOneById(customerComplaintsEntity.getCashRefundId());
            if (Objects.nonNull(oneById)){
                param.put("扣除门店业绩", "￥" + AmountChangeUtil.f2YScale2(oneById.getRefundAchievement()));
            }
        }
        param.put("订单退款明细", refundInfo.toString());
        param.put("订单退款方式", RefundTypeEnum.getValueByCode(refundType));

        //0元订单的订单金额+退款金额
        Integer hasExtraCompensationOpinion = customerComplaintsCreateRefundReq.getHasExtraCompensationOpinion();
        param.put("额外赔偿现金", "0");
        param.put("现金赔偿方式", "无");
        if (hasExtraCompensationOpinion == 1) {
            BigDecimal refundAmount1 = customerComplaintsCreateRefundReq.getRefundAmount();
            if (ObjectUtil.isNotEmpty(refundAmount1) && refundAmount1.compareTo(BigDecimal.ZERO) > 0) {
                param.put("额外赔偿现金", refundAmount1.toEngineeringString());
                param.put("现金赔偿方式", "现金赔偿方式：线下汇款，开户名：" + customerComplaintsCreateRefundReq.getAccountName() + ",银行卡号：" + customerComplaintsCreateRefundReq.getBankNo() + "开户行+支行：" + customerComplaintsCreateRefundReq.getAccountBank());
            }
        }
        if (StringUtils.isNotEmpty(customerComplaintsEntity.getOtherCompensationAmount())){
            List<SelectRespVO> respVOList = JSONUtil.toList(customerComplaintsEntity.getOtherCompensationAmount(), SelectRespVO.class);
            for (SelectRespVO selectRespVO : respVOList) {
                param.put(selectRespVO.getValue(), selectRespVO.getLabel());
            }
        }

        BigDecimal otherCompensationAmount = getOtherCompensationAmount(customerComplaintsEntity.getOtherCompensationAmount());
        param.put("总计退赔金额", getRoundingMode(refundAmount.add(getBigDecimal(param.get("商品赔偿价值"))).add(getBigDecimal(param.get("额外赔偿现金"))).add(otherCompensationAmount)).toString());

        Operator operator = complaintsCreateReq.getOperator();

        //创建人
        Integer creator = customerComplaintsEntity.getCreator();
        //提交人
        Integer submitter = customerComplaintsEntity.getSubmitter();
        //创建时间
        Date createdAt = customerComplaintsEntity.getCreatedAt();
        //提交时间
        Date submissionAt = customerComplaintsEntity.getSubmissionAt();

        if (ObjectUtil.isNotEmpty(creator)) {
            UserPO userPO = userService.queryUserById(creator.longValue());
            param.put("创建人", userPO.getName());
        }

        if (ObjectUtil.isNotEmpty(submitter)) {
            UserPO userPO = userService.queryUserById(submitter.longValue());
            param.put("处理人", userPO.getName());
        }

        param.put("创建时间", DateUtil.formatDateTime(createdAt));
        param.put("提交时间", DateUtil.formatDateTime(submissionAt));

        param.put("扣除销售业绩", "￥0.00");
        return param;
    }


    private void setCartInfo(Integer cartId, HashMap<String, String> param) {

        Integer allGoodsPrice = 0;

        if (ObjectUtil.isNotEmpty(cartId)) {

            List<HeCartGoodsEntity> heCartGoodsEntities = heCartGoodsRepository.queryListByCartId(cartId);

            if (CollectionUtil.isNotEmpty(heCartGoodsEntities)) {

                StringBuilder stringBuilder = new StringBuilder();

                List<Integer> goodsIdList = heCartGoodsEntities.stream().map(HeCartGoodsEntity::getGoodsId).collect(Collectors.toList());

                List<GoodsEntity> goodsEntities = goodsRepository.selectByIdList(goodsIdList);

                if (CollectionUtil.isNotEmpty(goodsEntities)) {
                    for (HeCartGoodsEntity heOrderGoodsEntity : heCartGoodsEntities) {
                        Optional<GoodsEntity> first = goodsEntities.stream().filter(g -> g.getId().equals(heOrderGoodsEntity.getGoodsId())).findFirst();
                        if (first.isPresent()) {
                            Integer goodsPrice = first.get().getGoodsPrice();
                            allGoodsPrice += goodsPrice * heOrderGoodsEntity.getNum();
                            stringBuilder.append(heOrderGoodsEntity.getSkuName()).append("，").append("商品数量：").append(heOrderGoodsEntity.getNum()).append("，").append("商品单价：￥").append(RMBUtils.bigDecimalF2Y(first.get().getGoodsPrice())).append(";\n");
                        }
                    }
                    param.put("商品赔偿明细", stringBuilder.toString());
                }
            }
        }
        param.put("商品赔偿价值", Objects.requireNonNull(RMBUtils.bigDecimalF2Y(allGoodsPrice)).toEngineeringString());
    }


    @Override
    public boolean condition(FlowContext context) {
        CustomerComplaintsCreateReq req = context.getAttribute(CustomerComplaintsCreateReq.class);
        return !req.getIsDraft();
    }

    private BigDecimal getBigDecimal(String amount){
        if (StringUtils.isEmpty(amount)){
            return BigDecimal.ZERO;
        }
        return new BigDecimal(amount);
    }

    private BigDecimal getRoundingMode(BigDecimal amount) {

        if (Objects.isNull(amount) || amount.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO.divide(BigDecimal.ONE, 2, RoundingMode.UP);
        }
        return amount.divide(BigDecimal.ONE, 2, RoundingMode.UP);
    }

    private BigDecimal getOtherCompensationAmount(String otherCompensationAmount) {

        final BigDecimal[] otherAmount = {BigDecimal.ZERO};
        try {
            if (StringUtils.isEmpty(otherCompensationAmount)){
                return otherAmount[0];
            }
            List<SelectRespVO> respVOList = JSONUtil.toList(otherCompensationAmount, SelectRespVO.class);
            if (CollectionUtils.isEmpty(respVOList)) {
                return otherAmount[0];
            }
            respVOList.forEach(item ->{
                otherAmount[0] = otherAmount[0].add(new BigDecimal(item.getLabel()));
            });
            return otherAmount[0];
        } catch (Exception e) {
            log.error("提交工单获取其他金额总和发生异常", e);
            return otherAmount[0];
        }
    }
}

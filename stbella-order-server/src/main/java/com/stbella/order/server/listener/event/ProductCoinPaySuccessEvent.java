package com.stbella.order.server.listener.event;

import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.server.order.month.constant.BaseConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * 产康金支付成功事件，
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductCoinPaySuccessEvent {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "订单编号")
    private String orderNo;
    @ApiModelProperty("本地流水号")
    private String outTradeNo;
    @ApiModelProperty("第三方流水号")
    private String transactionalNo;
    @ApiModelProperty(value = BaseConstant.PAY_STATUS)
    private Integer payStatus;
    @ApiModelProperty("支付时间")
    private Date payTime;
    @ApiModelProperty("原始报文")
    private String requestBody;

    /**
     * @see  OmniPayTypeEnum
     */
    @ApiModelProperty(value = BaseConstant.PAY_TYPE)
    private Integer payType;
    /**
     * 支付渠道:1-原生;3-招行
     *  PayChannelTypeEnum
     */
    @ApiModelProperty(value = "//支付渠道:1-原生;3-招行")
    private Integer payChannel;
    @ApiModelProperty("支付金额")
    private BigDecimal payAmount;

}

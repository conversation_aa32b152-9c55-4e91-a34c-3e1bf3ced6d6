package com.stbella.order.server.strategy.approval;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.month.server.request.OrderProductionDiscountApprovalRequest;
import com.stbella.month.server.request.RefundApprovalRequest;
import com.stbella.order.common.utils.JsonUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.domain.repository.OrderReductionRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.order.month.request.approval.ApprovalStatusInfo;
import com.stbella.platform.order.api.reduction.req.ReductionQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 产康折扣
 * @date 2024/3/18 17:45
 */
@Component
@Slf4j
public class ProductionDiscountApprovalStrategy implements ApprovalStrategy {
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderReductionRepository orderReductionRepository;

    @Override
    public void handleApproval(OrderApproveRecordTypeEnum type, String params, String messageId, ApprovalStatusInfo approvalStatusInfo, String processId) {
        OrderProductionDiscountApprovalRequest orderProductionDiscountApprovalRequest =  JSONObject.parseObject(params, OrderProductionDiscountApprovalRequest.class);
        //修改订单折扣审批状态
        HeOrderEntity heOrderEntity = orderRepository.getByOrderId(orderProductionDiscountApprovalRequest.getOrderId());
        if (ObjectUtil.isEmpty(heOrderEntity)) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "产康订单折扣审批消费失败,找不到订单,订单ID=" + orderProductionDiscountApprovalRequest.getOrderId());
        }
        boolean agree = approvalStatusInfo.isAgree();
        boolean finish = approvalStatusInfo.isFinish();
        boolean refuse = approvalStatusInfo.isRefuse();
        boolean terminate = approvalStatusInfo.isTerminate();
        Integer approvalDiscountStatus = null;
        if (finish && agree) {
            approvalDiscountStatus = 2;
        } else if (finish && refuse) {
            approvalDiscountStatus = 3;
        } else if (terminate) {
            approvalDiscountStatus = 3;
        }

        heOrderEntity.setApprovalDiscountStatus(approvalDiscountStatus);
        orderRepository.updateOrderMonthByOrderId(heOrderEntity);

        //判断是否是减免逻辑
        OrderReductionEntity orderReductionEntity = orderReductionRepository.selectByLocalProcessId(processId);
        if (ObjectUtil.isNotNull(orderReductionEntity)) {

        }
    }

    @Override
    public OrderApproveRecordTypeEnum getOrderApproveRecordTypeEnum() {
        return OrderApproveRecordTypeEnum.PRODUCTION_DISCOUNT_APPROVAL;
    }
}

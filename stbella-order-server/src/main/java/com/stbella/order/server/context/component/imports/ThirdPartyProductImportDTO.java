package com.stbella.order.server.context.component.imports;

import com.alibaba.excel.annotation.ExcelProperty;
import com.stbella.order.common.enums.production.OrderProductionTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2023-07-25  14:43
 * @Description: 三方产康资产导入给用户
 */
@Data
public class ThirdPartyProductImportDTO implements Serializable {


    /**
     * 订单编号
     * @see OrderProductionTypeEnum
     */
    @ExcelProperty(value = "订单属性")
    private String orderSellType;
    @ExcelProperty(value = "orderId")
    private String orderId;

    @ExcelProperty(value = "groupId")
    private String groupId;

    @ExcelProperty(value = "orderProductionId")
    private String orderProductionId;

    @ExcelProperty(value = "cardExtendId")
    private String cardExtendId;

    @ExcelProperty(value = "待核销")
    private String unVerificationCount;

    @ExcelProperty(value = "实际核销次数")
    private String fixCount;

}

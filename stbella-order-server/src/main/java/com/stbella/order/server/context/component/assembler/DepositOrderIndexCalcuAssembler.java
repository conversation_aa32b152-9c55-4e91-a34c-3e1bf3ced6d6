package com.stbella.order.server.context.component.assembler;

import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.server.order.OrderIndex;
import com.stbella.order.server.order.OrderIndexFact;
import com.stbella.order.server.order.month.enums.DiscountRuleResultEnum;
import com.stbella.platform.order.api.res.DiscountRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import java.math.BigDecimal;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-05-29  15:20
 * @Description: 押金订单指标计算组装
 */
@Slf4j
@Component
@SnowballComponent(name = "押金订单指标计算组装", desc = "押金订单指标计算组装")
public class DepositOrderIndexCalcuAssembler implements IExecutableAtom<FlowContext> {

    @Override
    public void run(FlowContext bizContext) {

        CfgStoreEntity cfgStore = bizContext.getAttribute(CfgStoreEntity.class);
        BigDecimal deposit = new BigDecimal(cfgStore.getDeposit()/100);

        OrderIndexFact indexFact = OrderIndexFact.builder()
                .bu(cfgStore.getBu())
                .orderAmount(deposit)
                .payAmount(deposit)
                .goodsOriginalPrice(new BigDecimal(0))
                .goodsCostPrice(new BigDecimal(0))
                .giftOriginalPrice(new BigDecimal(0))
                .giftCostPrice(new BigDecimal(0))
                .additionalOriginalPrice(new BigDecimal(0))
                .additionalCostPrice(new BigDecimal(0))
                .effectDate(System.currentTimeMillis() / 1000)
                .storeId(cfgStore.getStoreId())
                .orderType(String.valueOf(OmniOrderTypeEnum.DEPOSIT_ORDER.getCode()))
                .build();

        FlowContext discountContext = new FlowContext();
        discountContext.setAttribute(OrderIndexFact.class, indexFact);
        OrderIndex orderIndex = OrderIndex.builder()
                .discountMargin(new BigDecimal(100))
                .grossMargin(new BigDecimal(100))
                .netMargin(new BigDecimal(100)).build();

        bizContext.setAttribute(OrderIndex.class, orderIndex);

        DiscountRes discountRes = new DiscountRes()
                .setDiscountMargin(orderIndex.getDiscountMargin())
                .setNetMargin(orderIndex.getNetMargin())
                .setGrossMargin(orderIndex.getGrossMargin())
                .setTaxRate(cfgStore.getTaxRate())
                .setApproval(false)
                .setDiscountRuleResult(DiscountRuleResultEnum.NO_APPROVAL_REQUIRED.getCode());

        bizContext.setAttribute(DiscountRes.class, discountRes);

    }


}

package com.stbella.order.server.context.component.assembler;

import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.HeOrderAttachmentEntity;
import com.stbella.order.domain.repository.StoreRepository;
import com.stbella.order.server.manager.GoodsManager;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import com.stbella.store.goodz.res.GoodsAttachmentVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: ji<PERSON><PERSON><PERSON>
 * @CreateTime: 2024-05-29  15:20
 * @Description: sku附件组装
 */
@Component
@Slf4j
@SnowballComponent(name = "sku附件组装器", desc = "组装附件信息")
public class SkuAttachmentAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    GoodsManager goodsManager;

    @Override
    public void run(FlowContext bizContext) {

        List<SkuDetailInfo> skuDetailInfoList = bizContext.getListAttribute(SkuDetailInfo.class);
        //获取所有skuId 放set
        List<Integer> goodsIdList = skuDetailInfoList.stream().map(SkuDetailInfo::getGoodsId).collect(Collectors.toSet()).stream().collect(Collectors.toList());
        //获取附件信息
        List<GoodsAttachmentVo> skuAttachmentList = goodsManager.getGoodsAttachmentBySkuIdList(goodsIdList);

        List<HeOrderAttachmentEntity> attachmentEntityList = new ArrayList<>();

        skuAttachmentList.forEach(goodsAttachmentVo -> {
            HeOrderAttachmentEntity heOrderAttachmentEntity = new HeOrderAttachmentEntity();
            heOrderAttachmentEntity.setOgId(0);
            heOrderAttachmentEntity.setSkuId(goodsAttachmentVo.getSkuId());
            heOrderAttachmentEntity.setUrl(goodsAttachmentVo.getUrl());
            heOrderAttachmentEntity.setName(goodsAttachmentVo.getName());
            attachmentEntityList.add(heOrderAttachmentEntity);
        });

        // 构建 orderEntity 的地方附加上去
        bizContext.addListAttribute(HeOrderAttachmentEntity.class, attachmentEntityList);

    }
}

package com.stbella.order.server.context.component.processor;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.core.base.Operator;
import com.stbella.core.result.Result;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.core.CartSceneEnum;
import com.stbella.order.common.enums.month.RefundReasonEnum;
import com.stbella.order.domain.order.entity.HeCartEntity;
import com.stbella.order.domain.order.entity.HeCartGoodsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.repository.HeCartGoodsRepository;
import com.stbella.order.domain.repository.HeCartRepository;
import com.stbella.order.domain.repository.OrderGoodsRepository;
import com.stbella.order.server.order.month.enums.PayMethodEnum;
import com.stbella.platform.order.api.refund.api.OrderRefundService;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import com.stbella.platform.order.api.refund.req.CreateRefundReq.GoodsInfo;
import com.stbella.platform.order.api.refund.req.CreateRefundReq.GoodsRefundAmountInfo;
import com.stbella.platform.order.api.refund.req.QueryRefundGoodsReq;
import com.stbella.platform.order.api.refund.res.QueryOrderRefundInfoRes;
import com.stbella.platform.order.api.refund.res.QueryRefundGoodsRes;
import com.stbella.platform.order.api.refund.res.RefundGoodsAmountInfo;
import com.stbella.platform.order.api.req.CreateOrderReq;
import com.stbella.platform.order.api.req.QueryCartReq;
import com.stbella.platform.order.api.res.CreateApproveRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 升级订单处理器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "升级订单处理器")
public class UpgradeOrderProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    HeCartRepository heCartRepository;

    @Resource
    OrderRefundService orderRefundService;

    @Resource
    HeCartGoodsRepository cartGoodsRepository;

    @Resource
    OrderGoodsRepository orderGoodsRepository;

    @Override
    public boolean condition(FlowContext context) {
        CreateOrderReq req = context.getAttribute(CreateOrderReq.class);
        // 判断是否是升级订单场景
        if (CartSceneEnum.UPGRADE_ORDER.code().equals(req.getScene())) {
            return true;
        }
        log.info("UpgradeOrderProcessor: not upgrade order scene, cartId={}, scene={}", req.getCartId(), req.getScene());
        return false;
    }

    @Override
    public void run(FlowContext bizContext) {
        CreateOrderReq req = bizContext.getAttribute(CreateOrderReq.class);
        Integer cartId = req.getCartId();

        if (cartId == null) {
            log.info("UpgradeOrderProcessor: cartId is null, skip upgrade order processing");
            return;
        }

        // 获取购物车信息
        QueryCartReq queryReq = new QueryCartReq();
        queryReq.setCartId(cartId);
        queryReq.setScene(CartSceneEnum.UPGRADE_ORDER.code());
        HeCartEntity cartEntity = heCartRepository.queryOne(queryReq);
        if (cartEntity == null) {
            log.info("UpgradeOrderProcessor: cart not found, cartId={}", cartId);
            return;
        }

        // 检查是否有原订单信息
        if (cartEntity.getOriginalOrderId() == null) {
            log.info("UpgradeOrderProcessor: originalOrderId is null, cartId={}", cartId);
            return;
        }

        // 处理原订单商品退款
        Integer refundId = processOriginalOrderRefund(cartEntity);
        if (refundId <= 0) {
            log.error("订单升级发起退款失败: refundId={} failed", refundId);
        } else {
            bizContext.setAttribute(OtherConstant.PARENT_REFUND_ORDER_ID, refundId);
        }
    }

    /**
     * 处理原订单商品退款
     */
    private Integer processOriginalOrderRefund(HeCartEntity cartEntity) {
        Integer originalOrderId = cartEntity.getOriginalOrderId();
        BigDecimal originalGoodsTotalPrice = cartEntity.getOriginalGoodsTotalPrice();

        log.info("UpgradeOrderProcessor: processing original order refund, originalOrderId={}, originalGoodsTotalPrice={}", originalOrderId, originalGoodsTotalPrice);

        // 验证退款金额
        if (originalGoodsTotalPrice == null || originalGoodsTotalPrice.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("UpgradeOrderProcessor: invalid originalGoodsTotalPrice={}, skip refund", originalGoodsTotalPrice);
            return 0;
        }

        // 构建退款商品信息
        List<GoodsInfo> goodsInfoList = buildGoodsInfoList(cartEntity);
        if (CollectionUtils.isEmpty(goodsInfoList)) {
            log.warn("UpgradeOrderProcessor: no goods to refund for originalOrderId={}", originalOrderId);
            return 0;
        }

        // 构建退款请求
        CreateRefundReq refundReq = buildRefundRequest(originalOrderId, originalGoodsTotalPrice, goodsInfoList);

        // 执行退款，带重试机制
        Integer refundId = executeRefundWithRetry(refundReq, originalOrderId);

        return refundId;

    }

    /**
     * 构建退款请求
     */
    private CreateRefundReq buildRefundRequest(Integer originalOrderId, BigDecimal originalGoodsTotalPrice, List<GoodsInfo> goodsInfoList) {
        CreateRefundReq refundReq = new CreateRefundReq();
        refundReq.setOrderId(originalOrderId);
        refundReq.setRefundType(3); // 退款至意向金
        refundReq.setRefundTypeStr("退款至意向金");
        refundReq.setGoodsRefundType(1); // 仅退款
        refundReq.setGoodsRefundTypeStr("退货退款");
        refundReq.setRefundReasonType(1); // 正常退款
        refundReq.setRefundReason(RefundReasonEnum.ORDER_UPGRADE.getCode()); // 订单变更
        refundReq.setRemark("订单升级自动退款，系统自动处理");
        refundReq.setRefundAmount(originalGoodsTotalPrice);
        refundReq.setIsUpgradeOrderAutoRefund(true); // 标记为升级订单自动退款，无需审批
        refundReq.setGoodsInfoList(goodsInfoList);

        // 构建退款金额信息
        List<GoodsRefundAmountInfo> refundAmountInfoList = buildRefundAmountInfoList(originalGoodsTotalPrice);
        refundReq.setRefundAmountInfoList(refundAmountInfoList);

        refundReq.setOperator(Operator.system());

        return refundReq;
    }

    /**
     * 执行退款，带重试机制
     */
    private Integer executeRefundWithRetry(CreateRefundReq refundReq, Integer originalOrderId) {

        log.info("UpgradeOrderProcessor: attempting refund for originalOrderId={}", originalOrderId);

        // 调用退款接口
        Result<CreateApproveRes> result = orderRefundService.create(refundReq);

        log.info("UpgradeOrderProcessor: refund created successfully, originalOrderId={}, refundId={}", originalOrderId, result.getData());
        return result.getData().getRefundId(); // 成功，退出重试循环
    }

    /**
     * 将退款商品列表平铺，包括子商品
     */
    private List<QueryRefundGoodsRes> flattenRefundGoods(List<QueryRefundGoodsRes> goodsList) {
        List<QueryRefundGoodsRes> result = new ArrayList<>();
        if (CollectionUtil.isEmpty(goodsList)) {
            return result;
        }

        for (QueryRefundGoodsRes goods : goodsList) {
            result.add(goods);

            // 添加子商品
            if (CollectionUtil.isNotEmpty(goods.getChild())) {
                result.addAll(flattenRefundGoods(goods.getChild()));
            }

            // 添加附加项
            if (CollectionUtil.isNotEmpty(goods.getAdditionList())) {
                result.addAll(flattenRefundGoods(goods.getAdditionList()));
            }
        }

        return result;
    }

    /**
     * 构建退款商品信息列表
     */
    private List<GoodsInfo> buildGoodsInfoList(HeCartEntity cartEntity) {
        List<GoodsInfo> goodsInfoList = new ArrayList<>();
        Integer originalOrderId = cartEntity.getOriginalOrderId();

        if (originalOrderId == null) {
            log.warn("UpgradeOrderProcessor: originalOrderId is null, cannot build goods info list");
            return goodsInfoList;
        }
        // 构建查询请求
        QueryRefundGoodsReq req = new QueryRefundGoodsReq();
        req.setOrderId(originalOrderId);
        Result<QueryOrderRefundInfoRes> queryOrderRefundInfoResResult = orderRefundService.queryGoods(req);
        QueryOrderRefundInfoRes data = queryOrderRefundInfoResResult.getData();
        List<QueryRefundGoodsRes> queryRefundGoodsResList = data.getQueryRefundGoodsResList();
        List<QueryRefundGoodsRes> queryRefundGoodsRes = flattenRefundGoods(queryRefundGoodsResList);
        Map<Long, List<QueryRefundGoodsRes>> listMap = queryRefundGoodsRes.stream().collect(Collectors.groupingBy(QueryRefundGoodsRes::getOrderGoodsId));

        // 直接获取原订单的商品信息，不包含退款统计数据
        List<HeOrderGoodsEntity> orderGoodsEntities = orderGoodsRepository.getAllItermByOrderId(originalOrderId);

        if (CollectionUtils.isEmpty(orderGoodsEntities)) {
            log.warn("UpgradeOrderProcessor: no goods found for originalOrderId={}", originalOrderId);
            return goodsInfoList;
        }

        // 获取需要退款的商品ID和数量映射
        Map<Integer, Integer> refundGoodsMap = getRefundGoodsMap(cartEntity);

        // 转换为退款商品信息
        for (HeOrderGoodsEntity orderGoods : orderGoodsEntities) {
            Long orderGoodsId = orderGoods.getId().longValue();
            Integer refundNum = refundGoodsMap.get(orderGoodsId.intValue());
            log.info("orderGoodsId={}, refundNum={}", orderGoodsId, refundNum);
            // 只处理需要退款的商品
            if (refundNum != null && refundNum > 0) {
                GoodsInfo goodsInfo = new GoodsInfo();
                goodsInfo.setOrderGoodsId(orderGoodsId);
                goodsInfo.setOrderGoodsSn(orderGoods.getOrderGoodsSn());

                // 安全设置parentCombineSn，确保在orderGoodsEntities中能找到对应的记录
                String parentCombineSn = orderGoods.getParentCombineSn();
                if (parentCombineSn != null && !parentCombineSn.isEmpty()) {
                    // 验证parentCombineSn在当前订单商品列表中存在
                    boolean parentExists = orderGoodsEntities.stream().anyMatch(entity -> parentCombineSn.equals(entity.getOrderGoodsSn()));
                    if (parentExists) {
                        goodsInfo.setParentCombineSn(parentCombineSn);
                    }
                }

                goodsInfo.setType(orderGoods.getType());
                goodsInfo.setGoodsType(orderGoods.getGoodsType());
                goodsInfo.setGoodsId(orderGoods.getGoodsId());
                goodsInfo.setSkuId(orderGoods.getSkuId());
                goodsInfo.setGoodsName(orderGoods.getGoodsName());
                goodsInfo.setSkuName(orderGoods.getSkuName());

                goodsInfo.setRefundNum(refundNum);


                // 初始化refundGoodsAmountInfoList，避免验证器空指针异常
                List<GoodsRefundAmountInfo> refundAmountInfoList = new ArrayList<>();

                List<QueryRefundGoodsRes> queryRefundGoodsRes1 = listMap.get(orderGoodsId);
                QueryRefundGoodsRes queryRefundGoodsRes2 = queryRefundGoodsRes1.get(0);
                List<RefundGoodsAmountInfo> refundGoodsAmountInfoList = queryRefundGoodsRes2.getRefundGoodsAmountInfoList();
                for (RefundGoodsAmountInfo refundGoodsAmountInfo : refundGoodsAmountInfoList) {
                    GoodsRefundAmountInfo amountInfo = getGoodsRefundAmountInfo(refundGoodsAmountInfo, queryRefundGoodsRes2, refundNum);
                    refundAmountInfoList.add(amountInfo);
                }
                goodsInfo.setRefundGoodsAmountInfoList(refundAmountInfoList);
                List<GoodsRefundAmountInfo> collect = refundAmountInfoList.stream().filter(o -> PayMethodEnum.CASH.getModel().equals(o.getModel())).collect(Collectors.toList());
                goodsInfo.setRefundAmount(collect.get(0).getRefundAmount());
                goodsInfo.setGoodsNum(queryRefundGoodsRes2.getGoodsNum());

                goodsInfoList.add(goodsInfo);
            }
        }

        log.info("UpgradeOrderProcessor: goodsInfoList={}", JSONObject.toJSONString(goodsInfoList));


        return goodsInfoList;
    }

    private GoodsRefundAmountInfo getGoodsRefundAmountInfo(RefundGoodsAmountInfo refundGoodsAmountInfo, QueryRefundGoodsRes queryRefundGoodsRes2, Integer refundNum) {
        GoodsRefundAmountInfo amountInfo = new GoodsRefundAmountInfo();
        boolean allRefund = Objects.equals(queryRefundGoodsRes2.getRefundGoodsNum(), refundNum);
        if (allRefund) {
            amountInfo.setAmount(refundGoodsAmountInfo.getAmount());
            amountInfo.setRefundAmount(refundGoodsAmountInfo.getAmount());
        } else {
            amountInfo.setAmount(refundGoodsAmountInfo.getRefundableUnitPrice().multiply(BigDecimal.valueOf(refundNum)));
            amountInfo.setRefundAmount(refundGoodsAmountInfo.getRefundableUnitPrice().multiply(BigDecimal.valueOf(refundNum)));
        }
        String paymentMethod = refundGoodsAmountInfo.getPaymentMethod();
        amountInfo.setModel(PayMethodEnum.getEnumByCode(paymentMethod).getModel());
        amountInfo.setAmountType(Integer.parseInt(paymentMethod)); // 0-现金
        return amountInfo;
    }

    /**
     * 获取需要退款的商品ID和数量映射
     */
    private Map<Integer, Integer> getRefundGoodsMap(HeCartEntity cartEntity) {
        List<HeCartGoodsEntity> heCartGoodsEntities = cartGoodsRepository.queryList(cartEntity.getCartId());
        cartEntity.setGoodsList(heCartGoodsEntities);
        Map<Integer, Integer> refundGoodsMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(cartEntity.getGoodsList())) {
            for (HeCartGoodsEntity goodsEntity : cartEntity.getGoodsList()) {
                if (goodsEntity.getOriginalGoodsId() != null && goodsEntity.getNum() != null) {
                    refundGoodsMap.put(goodsEntity.getOriginalGoodsId(), goodsEntity.getNum());
                }
            }
        }

        return refundGoodsMap;
    }

    /**
     * 构建退款金额信息列表
     */
    private List<GoodsRefundAmountInfo> buildRefundAmountInfoList(BigDecimal totalAmount) {
        GoodsRefundAmountInfo amountInfo = new GoodsRefundAmountInfo();
        amountInfo.setAmount(totalAmount);
        amountInfo.setRefundAmount(totalAmount);
        amountInfo.setAmountType(0); // 0-现金
        amountInfo.setModel("UPGRADE_ORDER_REFUND");

        return Collections.singletonList(amountInfo);
    }
}

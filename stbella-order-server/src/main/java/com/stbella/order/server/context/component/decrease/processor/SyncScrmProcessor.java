package com.stbella.order.server.context.component.decrease.processor;

import com.stbella.customer.server.scrm.dto.OrderReductionRecordDTO;
import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.domain.order.month.entity.UserEntity;
import com.stbella.order.domain.repository.UserRepository;
import com.stbella.order.infrastructure.repository.converter.OrderDecreaseConverter;
import com.stbella.order.server.manager.ScrmManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 减免成功同步到scrm
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "减免成功同步到scrm")
public class SyncScrmProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private ScrmManager scrmManager;
    @Resource
    private OrderDecreaseConverter orderDecreaseConverter;
    @Resource
    private UserRepository userRepository;


    @Override
    public void run(FlowContext bizContext) {
        try {
            //更新折扣信息
            OrderReductionEntity orderReductionEntity = bizContext.getAttribute(OrderReductionEntity.class);
            OrderReductionRecordDTO recordDTO = orderDecreaseConverter.entity2DTO(orderReductionEntity);
            recordDTO.setChangeType("收款减免");

            UserEntity userEntity = userRepository.queryById(Integer.valueOf(orderReductionEntity.getOperatorId()));
            if (Objects.nonNull(userEntity)) {
                recordDTO.setStaffPhone(userEntity.getPhone());
            }
            recordDTO.setSuccessDate(orderReductionEntity.getGmtModified());

            scrmManager.omniOrderReductionRecordSyncScrm(recordDTO);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

}

package com.stbella.order.server.context.component.settlement;

import com.stbella.financial.enums.ApprovalStatus;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.service.OrderIncomeDomainService;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.domain.repository.StoreRepository;
import com.stbella.order.server.context.component.StoreCurrencyContainer;
import com.stbella.order.server.order.month.request.standard.OrderSettlementQuery;
import com.stbella.order.server.order.month.res.OrderSettlementVO;
import com.stbella.order.server.utils.BigDecimalUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 订单结算信息组装
 *
 * @author: jijunjian
 * @date: 8/7/24 11:56
 * @param:
 * @return:
 **/
@Component
@Slf4j
@SnowballComponent(name = "订单结算信息组装")
public class OrderSettlementAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private StoreRepository storeRepository;
    @Resource
    OrderIncomeDomainService incomeDomainService;
    @Resource
    ProductCoinPayAssembler productCoinPayAssembler;

    @Override
    public void run(FlowContext bizContext) {
        OrderSettlementQuery settlementQuery = bizContext.getAttribute(OrderSettlementQuery.class);

        OrderSettlementVO result = new OrderSettlementVO();
        result.setOrderId(settlementQuery.getOrderId());
        result.setClientUid(settlementQuery.getClientUid());
        result.setType(settlementQuery.getType());

        //获取订单主表数据
        HeOrderEntity order = orderRepository.getByOrderId(settlementQuery.getOrderId());
        setResultByStore(order, result);
        //设置金额相关
        setOrderAmount(order, result);

        result.setOrderSn(order.getOrderSn());
        result.setOrderType(order.getOrderType());
        bizContext.setAttribute(OrderSettlementVO.class, result);
    }

    private void setOrderAmount(HeOrderEntity order, OrderSettlementVO result) {
        //套餐应付金额
        Integer payAmount = order.calPayable();
        //门店信息
        result.setBasicUid(order.getBasicUid());
        //套餐应付金额
        List<HeIncomeRecordEntity> incomeRecordEntities = incomeDomainService.queryEffectiveRecord(order);

        boolean hasIncomeRefuse = incomeRecordEntities.stream().anyMatch(incomeRecordEntity -> ApprovalStatus.FAIL.getCode().equals(incomeRecordEntity.getApproveStatus()));
        result.setHasIncomeRecords(hasIncomeRefuse ? 1 : 0);

        int unConfirmAmount = order.fetchUnConfirmAmount();
        //待支付金额
        int unpaidAmount = order.leftPayAmount();
        if (unpaidAmount <= 0) {
            unpaidAmount = 0;
            result.setIsFullAmount(true);
        } else {
            result.setIsFullAmount(false);
        }
        Integer productCoinPayAmount = getProductCoinPayAmount(order, incomeRecordEntities);

        result.setProductCoinPayAmount(AmountChangeUtil.changeF2Y(productCoinPayAmount));
        result.setOrderAmount(AmountChangeUtil.changeF2Y(order.getOrderAmount()));
        result.setOriginalPayAmount(AmountChangeUtil.changeF2Y(order.getPayAmount()));
        result.setPayAmount(AmountChangeUtil.changeF2Y(order.signPayAmount()));
        result.setPaidAmount(AmountChangeUtil.changeF2Y(payAmount - unpaidAmount - unConfirmAmount));

        result.setUnpaidAmount(AmountChangeUtil.changeF2Y(unpaidAmount));

        if (result.getUnpaidAmount().compareTo(result.getProductCoinPayAmount()) < 0) {
            result.setProductCoinPayAmount(result.getUnpaidAmount());
        }

        result.setUnConfirmAmount(BigDecimalUtil.divide(new BigDecimal(unConfirmAmount), new BigDecimal(100)));
        result.setPercentagePayment(BigDecimalUtil.divideRoundingModeAndScale(result.getPaidAmount(), AmountChangeUtil.changeF2Y(payAmount), BigDecimal.ROUND_DOWN, 2).multiply(new BigDecimal(100)).intValue());
    }

    private Integer getProductCoinPayAmount(HeOrderEntity order, List<HeIncomeRecordEntity> incomeRecordEntities) {
        //设置可以抵扣的产康金金额，产康金金额=商品的可抵扣产康金总额-已支付产康金总额-商品已经分摊的非产康金金额
        Integer productCoinPayAmount = 0;

        // 检查订单是否已经使用过产康金支付
        boolean hasUsedProductCoin = hasUsedProductionCoin(incomeRecordEntities);

        // 产康金只能使用一次，且只能在第一次支付时使用
        if (!hasUsedProductCoin) {
            // 检查是否是第一次支付（没有任何成功的非减免支付记录）
            boolean isFirstPayment = isFirstPayment(incomeRecordEntities);
            if (isFirstPayment) {
                productCoinPayAmount = productCoinPayAssembler.fetchOrderAmount(order);
                log.info("订单[{}]允许使用产康金支付，可用金额：{}", order.getOrderSn(), productCoinPayAmount);
            } else {
                log.info("订单[{}]不是第一次支付，不允许使用产康金", order.getOrderSn());
            }
        } else {
            log.info("订单[{}]已经使用过产康金支付，不允许再次使用", order.getOrderSn());
        }
        return productCoinPayAmount;
    }

    private void setResultByStore(HeOrderEntity order, OrderSettlementVO result) {
        CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(order.getStoreId());
        String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(order.getStoreId());
        result.setCurrency(storeCurrencyCode);
        result.setStoreId(cfgStoreEntity.getStoreId());
        result.setStoreName(cfgStoreEntity.getStoreName());
        result.setStoreType(cfgStoreEntity.getType());
    }

    /**
     * 检查订单是否已经使用过产康金支付
     *
     * @param incomeRecordEntities 收款记录列表
     * @return true-已使用过产康金，false-未使用过产康金
     */
    private boolean hasUsedProductionCoin(List<HeIncomeRecordEntity> incomeRecordEntities) {
        if (CollectionUtils.isEmpty(incomeRecordEntities)) {
            return false;
        }

        // 检查是否存在成功的产康金支付记录
        // 只要有过成功的产康金支付，就认为已经使用过
        return incomeRecordEntities.stream()
                .filter(record -> OmniPayTypeEnum.PRODUCTION_COIN.getCode().equals(record.getPayType()))
                .anyMatch(HeIncomeRecordEntity::isActualSuccess);
    }

    /**
     * 检查是否是第一次支付（即没有任何成功的支付记录）
     *
     * @param incomeRecordEntities 收款记录列表
     * @return true-是第一次支付，false-不是第一次支付
     */
    private boolean isFirstPayment(List<HeIncomeRecordEntity> incomeRecordEntities) {
        if (CollectionUtils.isEmpty(incomeRecordEntities)) {
            return true;
        }

        // 检查是否存在任何成功的支付记录（排除减免支付）
        return incomeRecordEntities.stream()
                .filter(record -> !OmniPayTypeEnum.REDUCTION.getCode().equals(record.getPayType()))
                .noneMatch(HeIncomeRecordEntity::isActualSuccess);
    }

}

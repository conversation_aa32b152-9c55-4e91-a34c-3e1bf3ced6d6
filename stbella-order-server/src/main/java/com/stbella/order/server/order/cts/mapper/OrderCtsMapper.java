package com.stbella.order.server.order.cts.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.stbella.order.server.order.cts.dto.CustomerOrderDTO;
import com.stbella.order.server.order.cts.entity.OrderCtsPO;
import com.stbella.order.server.order.cts.excel.AllCtsOrderIdsExcel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 到家订单 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2022-03-18
 */
public interface OrderCtsMapper extends BaseMapper<OrderCtsPO> {

    /**
     * 根据订单编号查询客户信息
     *
     * @param orders
     * @return
     */
    List<CustomerOrderDTO> queryCustomerListByOrderNos(@Param("query") List<String> orders);


    /**
     * 根据上户保证金订单编号查询客户信息
     *
     * @param orders
     * @return
     */
    List<CustomerOrderDTO> queryMarginCustomerListByOrderNos(@Param("query") List<String> orders);

    /**
     * 查询所有予家订单id
     *
     * @return
     */
    List<AllCtsOrderIdsExcel> queryAllOrderIds();

}

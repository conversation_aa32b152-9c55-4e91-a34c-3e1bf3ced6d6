package com.stbella.order.server.context.component.refund.processor;

import com.stbella.core.base.Operator;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.core.OrderRefundNatureEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.platform.order.api.OrderAggregateRoot;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;


@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "退款完成后自动关闭订单")
public class OrderRefundAfterCloseProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderAggregateRoot orderAggregateRoot;

    @Override
    public boolean condition(FlowContext context) {
        HeOrderRefundEntity orderRefundEntity = context.getAttribute(HeOrderRefundEntity.class);
        return (boolean) context.getAttribute(OtherConstant.CONTINUE) && !OrderRefundNatureEnum.TEMP_REFUND.getCode().equals(orderRefundEntity.getRefundNature());
    }

    /**
     * 退款全部完成 && 非退回重付的金额 = 订单商品实付金额
     * 订单关闭
     *
     * @param bizContext
     */
    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        try {
            orderAggregateRoot.close(Operator.system(), orderEntity.getOrderId());
        } catch (Exception e) {
            log.info("关闭订单：{}", e.getMessage());
        }
    }

}

package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.stbella.asset.api.enums.TradeEventEnum;
import com.stbella.asset.api.enums.TradeType;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.repository.GoodsRepository;
import com.stbella.order.domain.repository.HeOrderRefundGoodsRepository;
import com.stbella.order.server.manager.AssetManager;
import com.stbella.order.server.order.month.service.impl.OrderAssetTradeService;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import com.stbella.store.common.enums.core.GoodsTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


@Component
@Slf4j
@SnowballComponent(name = "发起退款，资产处理", desc = "发起退款，资产处理")
public class OrderRefundCreateAssetAssembler implements IExecutableAtom<FlowContext> {


    @Resource
    private OrderAssetTradeService orderAssetTradeService;

    @Resource
    private AssetManager assetManager;


    @Override
    public void run(FlowContext bizContext) {
        //请求参数
        CreateRefundReq attribute = bizContext.getAttribute(CreateRefundReq.class);
        //下单的商品列表
        List<HeOrderGoodsEntity> orderGoodsEntityList = bizContext.getListAttribute(HeOrderGoodsEntity.class);
        HeOrderRefundEntity parentRefund = bizContext.getAttribute(HeOrderRefundEntity.class);
        ;
        //退款商品信息
        List<CreateRefundReq.GoodsInfo> goodsInfoList = attribute.getGoodsInfoList();

        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);

        //处理资产退款
        manyTradeExec(orderEntity, goodsInfoList, orderGoodsEntityList, parentRefund.getId());

        List<CreateRefundReq.GoodsInfo> productionCoinList = goodsInfoList.stream().filter(g -> g.getGoodsType().equals(GoodsTypeEnum.Production_Coin.code())).collect(Collectors.toList());
        log.info("开始处理生产币退款productionCoinList:{}", JSONUtil.toJsonStr(productionCoinList));
        if (CollectionUtil.isNotEmpty(productionCoinList)) {
            for (CreateRefundReq.GoodsInfo goodsInfo : productionCoinList) {
                if (goodsInfo.getRefundNum() > 0) {
                    Optional<HeOrderGoodsEntity> first = orderGoodsEntityList.stream().filter(o -> o.getOrderGoodsSn().equals(goodsInfo.getOrderGoodsSn())).findFirst();
                    if (first.isPresent()) {
                        //扣除的产康金
                        Integer refundNum = goodsInfo.getRefundNum();
                        //是否礼赠
                        Integer gift = first.get().getGift();
                        log.info("开始处理生产币退款,订单号:{},商品名称:{},退款数量:{},是否礼赠:{}", orderEntity.getOrderId(), first.get().getGoodsName(), refundNum, gift);
                        orderAssetTradeService.refundDeductUsableCkj(
                                orderEntity.getOrderId() + "" + System.currentTimeMillis() + "-save",
                                orderEntity.getBasicUid() + "",
                                orderEntity.getOrderId(),
                                -refundNum.longValue(),
                                goodsInfo.getOrderGoodsSn(),
                                orderAssetTradeService.getSellOperator(Integer.valueOf(attribute.getOperator().getOperatorGuid())),
                                first.get().getGoodsName()
                        );
                    }
                }
            }
        }
    }

    private void manyTradeExec(HeOrderEntity orderEntity, List<CreateRefundReq.GoodsInfo> goodsInfoList, List<HeOrderGoodsEntity> orderGoodsEntityList, Integer parentRefundId) {

        log.info("开始处理资产退款goodsInfoList:{}", goodsInfoList);
        Map<Integer, HeOrderGoodsEntity> orderGoodsEntityMap = orderGoodsEntityList.stream().collect(Collectors.toMap(HeOrderGoodsEntity::getId, o -> o, (k, v) -> k));
        List<HeOrderGoodsEntity> reqList = Lists.newArrayList();
        for (CreateRefundReq.GoodsInfo goodsInfo : goodsInfoList) {
            if (goodsInfo.getRefundNum() <= 0) {
                continue;
            }
            HeOrderGoodsEntity heOrderGoodsEntity = orderGoodsEntityMap.get(goodsInfo.getOrderGoodsId().intValue());
            if (Objects.isNull(heOrderGoodsEntity)) {
                continue;
            }
            heOrderGoodsEntity.setGoodsNum(goodsInfo.getRefundNum());
            reqList.add(heOrderGoodsEntity);
        }
        assetManager.execMultiUserTrade(reqList, orderEntity, TradeEventEnum.ORDER_RETURN, null, parentRefundId.toString());
    }

    @Override
    public boolean condition(FlowContext context) {
        return (boolean) context.getAttribute(OtherConstant.CONTINUE);
    }

}

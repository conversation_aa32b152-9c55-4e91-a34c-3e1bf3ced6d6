package com.stbella.order.server.listener;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.mail.MailUtils;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.infrastructure.oss.OSSFactory;
import com.stbella.order.server.context.component.export.ProductionOrderExportAssembler;
import com.stbella.order.server.context.component.export.ProductionOrderExportDTO;
import com.stbella.order.server.context.component.export.VerificationStatAssembler;
import com.stbella.order.server.order.order.req.OrderExportReq;
import com.stbella.order.server.oss.response.OssUploadResponse;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 产康订单核销统计
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ProductionOrderVerificationStatListener {

    @Autowired
    VerificationStatAssembler statAssembler;

    @Resource
    private MailUtils mailUtils;

    @Resource
    RuleLinkClient ruleLinkClient;

    /**
     * 产康订单核销统计，生成excel，上传到oss
     * @param event
     */
    @Async
    @EventListener
    public void build(OrderExportReq event) {

        orderExportProcess(event);
    }

    public void orderExportProcess(OrderExportReq event) {

        log.info("收到事件指令，开始生成数据{}", JSONUtil.toJsonStr(event));

        String fileName = "productionOrderStat-"+ DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
        String path = "stbella/order/excel/" + fileName + ".xlsx";

        List<ProductionOrderExportDTO> assemble = statAssembler.assemble(event);

        log.info("开始上传腾讯云服务器");
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        EasyExcel.write(byteArrayOutputStream, ProductionOrderExportDTO.class).sheet("sheet1").doWrite(assemble);

        InputStream inputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());

        OssUploadResponse ossUploadResponse = OSSFactory.build().upload(inputStream, path);
        if (!ossUploadResponse.isSuccess()) {
            log.info("上传腾讯云服务器失败,msg={}", ossUploadResponse.getMsg());
            throw new BusinessException(ResultEnum.FILE_ERROR.getCode(), "上传腾讯服务器失败");
        }

        log.info("上传腾讯云服务器成功, 开始发送邮件,path={}", ossUploadResponse.getUrl());

        if (Objects.isNull(event.getBizCode())){
            event.setBizCode(1);;
        }

        Map<String, String> factMap = new HashMap<>();
        factMap.put("configCode","production_order_verification_receiver");
        ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req("systemConfig", factMap);

        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
        // simpleRuleValue 按逗号分成数组
        String[] to = hitRuleVo.getSimpleRuleValue().split(",");
        String subject = "产康订单核销统计明细";
        String content = "请点击链接下载文件：".concat("\n").concat(ossUploadResponse.getUrl());

        mailUtils.sendSimpleMail(to, subject, content);
    }


}

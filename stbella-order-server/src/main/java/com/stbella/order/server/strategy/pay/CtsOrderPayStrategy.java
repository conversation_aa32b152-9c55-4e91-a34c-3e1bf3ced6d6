package com.stbella.order.server.strategy.pay;

import com.stbella.order.server.convert.OrderNutritionConvert;
import com.stbella.order.server.order.cts.service.OrderFacade;
import com.stbella.order.server.order.month.enums.PayRecordEnum;
import com.stbella.order.server.order.month.request.pay.PayNotityRequest;
import com.stbella.order.server.utils.ThrowableUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 支付策略-予家订单
 * @date 2024/3/15 16:01
 */
@Component
@Slf4j
public class CtsOrderPayStrategy implements PayNotificationStrategy {
    @Resource
    private OrderFacade orderFacade;
    @Resource
    private OrderNutritionConvert orderNutritionConvert;

    @Override
    public boolean handlePayNotification(PayNotityRequest payNotityRequest) {
        try {
            boolean editOrderInfo = orderFacade.payNotify(orderNutritionConvert.PayNotityRequest2PayNotifyMqDTO(payNotityRequest), payNotityRequest.getOrderNo());
            return editOrderInfo;
        } catch (Exception e) {
            log.error("到家订单支付回调消费失败:{}", ThrowableUtil.getStackTrace(e));
            return false;
        }
    }

    @Override
    public PayRecordEnum getPayRecordEnum() {
        return PayRecordEnum.CTS;
    }
}

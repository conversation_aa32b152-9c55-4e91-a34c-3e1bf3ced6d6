package com.stbella.order.server.order.month.handle;

import com.alibaba.fastjson.JSONObject;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.production.OrderProductionItemEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.production.OrderProductionExtendEntity;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.order.month.req.IncomeAndRefundRecordReq;
import com.stbella.order.server.order.month.res.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProductionServiceImpl extends OrderInfoAbstractServiceImpl {

    private static final List<Integer> typeList = Arrays.asList(OrderProductionItemEnum.TYPE_COUNT.code(), OrderProductionItemEnum.TYPE_GROUP.code());

    @Resource
    private OrderProductionExtendRepository orderProductionExtendRepository;

    @Override
    public OrderInfoNewVO queryOrderInfo(HeOrderEntity orderEntity) {
        log.info("产康订单查看详细信息orderId:{}", orderEntity.getOrderId());
        return super.getCommonOrderInfo(orderEntity);
    }

    @Override
    public List<GoodsNewVO> queryOrderGoodsList(HeOrderEntity orderEntity) {
        List<GoodsNewVO> goodsNewVOList = new ArrayList<>();
        this.fillBuyGoodsInfo(goodsNewVOList, orderEntity.getOrderId(), orderEntity.getCurrency());
        super.fillGiftGoodsList(orderEntity, goodsNewVOList);
        return goodsNewVOList;
    }

    @Override
    public ContractNewVO queryOrderContractList(Integer contractStatus, Integer clientUid, Integer orderId) {
        log.info("产康套餐查询合同信息orderId:{}", orderId);
        return super.getContractList(contractStatus, clientUid, orderId, false);
    }


    @Override
    public IncomeAndRefundVO queryIncomeAndRefundRecord(HeOrderEntity orderEntity) {
        log.info("产康套餐查询收退款记录信息orderId:{}", orderEntity.getOrderId());
        return super.getIncomeAndRefundVO(orderEntity);
    }

    @Override
    public IncomeInfoVO incomeInfo(IncomeAndRefundRecordReq req) {
        log.info("产康套餐查询收入信息orderId:{}", req.getId());
        return super.getIncomeInfo(req);
    }

    @Override
    public RefundInfoVO refundInfo(IncomeAndRefundRecordReq req) {

        log.info("产康套餐查询退款信息orderId:{}", req.getId());
        return super.getRefundInfo(req.getId());
    }

    private void fillBuyGoodsInfo(List<GoodsNewVO> goodsNewVOList, Integer orderId, String currency) {

        List<OrderProductionExtendEntity> orderProductionExtendList = orderProductionExtendRepository.queryByOrderIdList(Collections.singletonList(orderId));

        if (CollectionUtils.isEmpty(orderProductionExtendList)){
            return;
        }
        List<Integer> goodsIdList = orderProductionExtendList.stream().map(OrderProductionExtendEntity::getGoodsId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Integer, JSONObject> backCategoryMap = super.backCategoryMap(goodsIdList);
        for (OrderProductionExtendEntity orderProductionExtend : orderProductionExtendList) {
            GoodsNewVO goodsNewVO = new GoodsNewVO();
            goodsNewVO.setId(orderProductionExtend.getId());
            goodsNewVO.setSkuName(orderProductionExtend.getSkuName());
            goodsNewVO.setSkuId(orderProductionExtend.getSkuId());
            goodsNewVO.setGoodsId(orderProductionExtend.getGoodsId());
            goodsNewVO.setGoodsName(orderProductionExtend.getGoodsName());
            goodsNewVO.setGoodsNum(orderProductionExtend.getGoodsNum());
            goodsNewVO.setGoodsPriceOrgin(currency + AmountChangeUtil.f2YDown(orderProductionExtend.getPrice()));
            goodsNewVO.setGift(0);
            goodsNewVO.setGiftStr("购买");
            goodsNewVO.setGiftTypeStr(StringUtils.EMPTY);
            goodsNewVO.setCurrency(currency);
            JSONObject jsonObject = backCategoryMap.get(orderProductionExtend.getGoodsId());
            goodsNewVO.setCategoryBack(Objects.isNull(jsonObject) ? null : jsonObject.getLong("id"));
            goodsNewVO.setCategoryBackStr(Objects.isNull(jsonObject) ? StringUtils.EMPTY : jsonObject.getString("name"));
            goodsNewVOList.add(goodsNewVO);
        }
    }

    @Override
    public boolean bizHandleType(Integer orderType) {
        return OmniOrderTypeEnum.PRODUCTION_ORDER.getCode().equals(orderType);
    }
}

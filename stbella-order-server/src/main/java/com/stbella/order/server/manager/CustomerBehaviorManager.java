package com.stbella.order.server.manager;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: CustomerBehaviorManager
 * @date 2023/2/22 11:02
 */
@Component
@Slf4j
public class CustomerBehaviorManager {

    @Resource
    private CustomerBehaviorRecordRepository customerBehaviorRecordRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private StoreRepository storeRepository;
    @Resource
    private OrderGoodsRepository orderGoodsRepository;
    @Resource
    private UserRepository userRepository;

    /**
     * 签单推送客户行为日志
     * 支付达到50%
     */
    public Integer signOrderPushCustomerBehavior(Integer orderId, Long payTime) {
        HeOrderEntity heOrderEntity = orderRepository.getByOrderId(orderId);
        if (ObjectUtil.isNotEmpty(heOrderEntity)) {
            CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(heOrderEntity.getStoreId());
            if (ObjectUtil.isNotEmpty(cfgStoreEntity)) {
                HeOrderGoodsEntity orderGoodsEntity = orderGoodsRepository.getByOrderId(orderId);
                if (ObjectUtil.isNotEmpty(orderGoodsEntity)) {
                    UserEntity userEntity = userRepository.queryById(heOrderEntity.getStaffId());
                    HeCustomerBehaviorRecordEntity entity = new HeCustomerBehaviorRecordEntity();
                    String title = "购买了" + orderGoodsEntity.getGoodsName() + "(" + cfgStoreEntity.getStoreName() + ")";
                    Map<String, Object> map = new HashMap<>();
                    map.put("order_id", orderId);
                    map.put("old_or_new", 1);
                    entity.setTitle(title);
                    entity.setBasicUid(heOrderEntity.getBasicUid());
                    entity.setClientUid(heOrderEntity.getClientUid());
                    entity.setClientType(1);
                    entity.setOptBasicUid(ObjectUtil.isNotEmpty(userEntity) ? userEntity.getBasicUid() : 0);
                    entity.setOptClientUid(heOrderEntity.getStaffId());
                    entity.setOptClientType(7);
                    entity.setAddTime(payTime);
                    entity.setType(4);
                    entity.setExtend(JSONUtil.toJsonStr(map));
                    entity.setCreatedAt(System.currentTimeMillis() / 1000);
                    entity.setUpdatedAt(System.currentTimeMillis() / 1000);
                    entity.setIsDelete(0);
                    log.info("签单推送客户行为日志,推送参数={}", entity);
                    Integer id = customerBehaviorRecordRepository.signOrderPushCustomerBehaviorRecord(entity);
                    if (id > 0) {
                        log.info("签单推送客户行为日志,推送成功");
                    }
                    return id;
                } else {
                    log.info("签单推送客户行为日志,套餐数据不存在,订单ID={}", orderId);
                    return null;
                }

            } else {
                log.info("签单推送客户行为日志,门店数据不存在,门店ID={}", heOrderEntity.getStoreId());
                return null;
            }

        } else {
            log.info("签单推送客户行为日志,订单数据不存在,订单ID={}", orderId);
            return null;
        }

    }

}

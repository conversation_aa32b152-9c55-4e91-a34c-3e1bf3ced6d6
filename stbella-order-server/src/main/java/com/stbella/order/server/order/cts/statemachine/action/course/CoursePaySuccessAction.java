package com.stbella.order.server.order.cts.statemachine.action.course;

import com.stbella.order.server.order.cts.enums.OrderStatusStateMachineStatusEnum;
import com.stbella.order.server.order.cts.statemachine.OrderContext;
import com.stbella.order.server.order.cts.statemachine.OrderStateEvent;
import com.stbella.order.server.order.cts.statemachine.action.common.CommonAction;
import com.stbella.order.server.order.nutrition.dto.PayNotifyMqDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.action.Action;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 培训课程订单支付成功
 *
 * <AUTHOR>
 * @date 2022-03-10 11:41
 * @sine 1.0.0
 */
@Slf4j
@Component
public class CoursePaySuccessAction implements Action<OrderStatusStateMachineStatusEnum, OrderStateEvent> {

    @Resource
    private CommonAction commonAction;

    @Override
    public void execute(StateContext<OrderStatusStateMachineStatusEnum, OrderStateEvent> stateContext) {
        log.info("执行育婴师-培训课程订单支付成功" + stateContext.toString());
        OrderContext context = (OrderContext) stateContext.getMessageHeader(OrderContext.HEADER_KEY);
        final PayNotifyMqDTO payNotifyMqDTO = (PayNotifyMqDTO) context.getModel();
        commonAction.paySuccess(payNotifyMqDTO, context.getOrderCtsPO());
    }
}

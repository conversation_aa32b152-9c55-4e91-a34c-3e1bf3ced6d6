package com.stbella.order.server.context.component.validator;

import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.server.order.month.enums.DiscountRuleResultEnum;
import com.stbella.platform.order.api.res.DiscountRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import java.util.Objects;

/**
 * 折扣规则验证
 */
@Component
@Slf4j
@SnowballComponent(name = "折扣规则验证", desc = "折扣规则验证")
public class DiscountRuleValidator implements IExecutableAtom<FlowContext> {


    @Override
    public void run(FlowContext bizContext) {
        DiscountRes discountRes = bizContext.getAttribute(DiscountRes.class);
        if (Objects.isNull(discountRes)) {
            return;
        }

        Integer discountRuleResult = discountRes.getDiscountRuleResult();
        log.info("折扣规则验证结果：{}", discountRuleResult);
        if (DiscountRuleResultEnum.NOT_SUBMITTABLE.getCode().equals(discountRuleResult)) {
            log.warn("折扣规则不满足，不可提交");
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(),"折扣规则不满足，不可提交");
        }

    }
}

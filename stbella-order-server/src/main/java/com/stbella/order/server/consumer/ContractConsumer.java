package com.stbella.order.server.consumer;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.cts.server.entity.HomeContractTemplatePO;
import com.stbella.cts.server.query.HomeContractTemplateBaseQuery;
import com.stbella.cts.service.HomeContractTemplateService;
import com.stbella.order.server.contract.entity.OrderContractConfigPO;
import com.stbella.order.server.contract.service.ESignService;
import com.stbella.order.server.contract.service.OrderContractConfigService;
import com.stbella.order.server.order.cts.enums.YesOrNoEnum;
import com.stbella.pulsar.consumer.PulsarConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.pulsar.client.api.Message;
import org.apache.pulsar.client.api.SubscriptionType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ContractConsumer {

    @Resource
    private ESignService eSignService;
    @DubboReference
    private HomeContractTemplateService homeContractTemplateService;
    @Resource
    private OrderContractConfigService orderContractConfigService;

    /**
     * @param message
     */
    @PulsarConsumerListener(destination = "persistent://pulsar-44k4paxzz5vg/nameSpace/e_sign_notify", subscriptionName = "e_sign_notify", subscriptionType = SubscriptionType.Shared)
    public void revicer(Message message) {
        String jsons = new String(message.getData());
        log.info("e_sign_notify ===message:{}", jsons);
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        JSONObject jsonObject = JSONObject.parseObject(jsons);
        HomeContractTemplateBaseQuery homeContractTemplateBaseQuery = new HomeContractTemplateBaseQuery();
        homeContractTemplateBaseQuery.setId(jsonObject.getLong("id"));
        List<HomeContractTemplatePO> homeContractTemplatePOS = homeContractTemplateService.queryHomeContractTemplateList(homeContractTemplateBaseQuery);
        if (homeContractTemplatePOS != null && !homeContractTemplatePOS.isEmpty()) {
            HomeContractTemplatePO homeContractTemplatePO = homeContractTemplatePOS.stream().findFirst().get();
            List<OrderContractConfigPO> list = orderContractConfigService.list(new LambdaQueryWrapper<OrderContractConfigPO>()
                    .eq(OrderContractConfigPO::getStatus, YesOrNoEnum.YES.getCode()));
            HashMap<Long, String> ret = new HashMap<>();
            for (OrderContractConfigPO orderContractConfigPO : list) {
                log.info("开始上传到esign {}", homeContractTemplatePO.getTemplateAddress());
                String templateId = eSignService.uploadFileByUrlAddress(homeContractTemplatePO.getTemplateAddress(), homeContractTemplatePO.getSbName() + ".pdf", orderContractConfigPO);
                ret.put(orderContractConfigPO.getCtsSiteId(), templateId);
            }
            homeContractTemplatePO.setESignTemplateId(JSONObject.toJSONString(ret));
            homeContractTemplateService.updateById(homeContractTemplatePO);
        } else {
            log.error("esign 回调 未查询到该模版 {}", jsons);
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "未查询到该模版");
        }

    }


}

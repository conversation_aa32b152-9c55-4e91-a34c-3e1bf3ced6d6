package com.stbella.order.server.convert;


import cn.hutool.core.date.DateUtil;
import com.stbella.order.common.enums.production.*;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.production.AppointmentBoardWorkmanshipEntity;
import com.stbella.order.domain.order.production.AppointmentProductionScheduleEntity;
import com.stbella.order.domain.order.production.OrderProductionAppointmentEntity;
import com.stbella.order.server.order.production.res.*;
import com.stbella.order.server.utils.SensitiveInformationUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <p>
 * 产康预约
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-20 13:45
 */
@Mapper(componentModel = "spring", imports = {ProductionAppointmentTypeEnum.class, VerificationStatusEnum.class, ProductionBookTypeEnum.class, OrderProductionItemEnum.class, ProductionServeTypeEnum.class, DateUtil.class, AmountChangeUtil.class, SensitiveInformationUtil.class, ClientVerificationStatusEnum.class, TerminalTypeEnum.class})
public interface AppProductionAppointmentConverter {

    /**
     * OrderProductionAppointmentEntity -> ProductionAppointmentVo 批量
     *
     * @param entityList
     * @return
     */
    List<ProductionAppointmentVo> entity2VoForListProductionAppointment(List<OrderProductionAppointmentEntity> entityList);

    /**
     * entityList -> voList
     *
     * @param entityList
     * @return List<ProductionAppointmentProductionScheduleVo>
     */
    List<ProductionAppointmentProductionScheduleVo> entity2VoForListProductionAppointmentScheduleVo(List<OrderProductionAppointmentEntity> entityList);

    /**
     * OrderProductionAppointmentEntity -> ProductionAppointmentVo
     *
     * @param entity
     * @return
     */
    @Mappings({
            @Mapping(target = "productionName", expression = "java(entity.assemblerProductionName())"),
            @Mapping(target = "serveStart", expression = "java(DateUtil.format(entity.getServeStart(), \"HH:mm\"))"),
            @Mapping(target = "serveEnd", expression = "java(DateUtil.format(entity.getServeEnd(), \"HH:mm\"))"),
            @Mapping(target = "appointmentId", source = "id")
    })
    ProductionAppointmentProductionScheduleVo entity2VoForProductionAppointmentScheduleVo(OrderProductionAppointmentEntity entity);

    /**
     * OrderProductionAppointmentEntity -> ProductionAppointmentVo
     *
     * @param entity
     * @return
     */
    @Mappings({
            @Mapping(target = "verificationStateName", expression = "java(ClientVerificationStatusEnum.fromCode(TerminalTypeEnum.TERMINAL_TYPE_CUSTOMER.code(),entity.getVerificationState()))"),
            @Mapping(target = "verificationPiStateName", expression = "java(ClientVerificationStatusEnum.fromCode(TerminalTypeEnum.TERMINAL_TYPE_PI.code(),entity.getVerificationState()))"),
            @Mapping(target = "bookTypeName", expression = "java(ProductionBookTypeEnum.fromCode(entity.getBookType()))"),
            @Mapping(target = "appointmentTypeName", expression = "java(ProductionAppointmentTypeEnum.fromCode(entity.getAppointmentType()))"),
            @Mapping(target = "productionName", expression = "java(entity.assemblerProductionNameBack())"),
            @Mapping(target = "serveTypeName", expression = "java(ProductionServeTypeEnum.fromCode(entity.getServeType()))"),
            @Mapping(target = "itemTypeName", expression = "java(OrderProductionItemEnum.fromCode(entity.getItemType()))"),
            @Mapping(target = "serveStart", expression = "java(DateUtil.format(entity.getServeStart(), \"HH:mm\"))"),
            @Mapping(target = "serveEnd", expression = "java(DateUtil.format(entity.getServeEnd(), \"HH:mm\"))"),
            @Mapping(target = "serveFee", expression = "java(AmountChangeUtil.changeF2Y(String.valueOf(entity.getServeFee())))")
    })
    ProductionAppointmentVo entity2VoForProductionAppointment(OrderProductionAppointmentEntity entity);

    List<ProductionAppointmentBackVo> entity2VoForListProductionAppointmentBack(List<OrderProductionAppointmentEntity> entityList);

    @Mappings({
            @Mapping(target = "verificationStateName", expression = "java(VerificationStatusEnum.getDescByTerminalType(0,entity.getVerificationState()))"),
            @Mapping(target = "bookTypeName", expression = "java(ProductionBookTypeEnum.fromCode(entity.getBookType()))"),
            @Mapping(target = "appointmentTypeName", expression = "java(ProductionAppointmentTypeEnum.fromCode(entity.getAppointmentType()))"),
            @Mapping(target = "serveTypeName", expression = "java(ProductionServeTypeEnum.fromCode(entity.getServeType()))"),
            @Mapping(target = "itemTypeName", expression = "java(OrderProductionItemEnum.fromCode(entity.getItemType()))"),
            @Mapping(target = "serveStart", expression = "java(DateUtil.format(entity.getServeStart(), \"HH:mm\"))"),
            @Mapping(target = "serveEnd", expression = "java(DateUtil.format(entity.getServeEnd(), \"HH:mm\"))"),
            @Mapping(target = "serveFee", expression = "java(AmountChangeUtil.changeF2Y(Integer.toString(entity.getServeFee())))"),
            @Mapping(target = "productionName", expression = "java(entity.assemblerProductionName())"),
    })
    ProductionAppointmentBackVo entity2VoForProductionAppointmentBack(OrderProductionAppointmentEntity entity);

    /**
     * OrderProductionAppointmentEntity -> ProductionAppointmentInfoBackVo
     *
     * @param entity
     * @return
     */
    @Mappings({
            @Mapping(target = "verificationStateName", expression = "java(VerificationStatusEnum.getDescByTerminalType(0, entity.getVerificationState()))"),
            @Mapping(target = "bookTypeName", expression = "java(ProductionBookTypeEnum.fromCode(entity.getBookType()))"),
            @Mapping(target = "appointmentTypeName", expression = "java(ProductionAppointmentTypeEnum.fromCode(entity.getAppointmentType()))"),
            @Mapping(target = "serveTypeName", expression = "java(ProductionServeTypeEnum.fromCode(entity.getServeType()))"),
            @Mapping(target = "serveStart", expression = "java(DateUtil.format(entity.getServeStart(), \"HH:mm\"))"),
            @Mapping(target = "serveEnd", expression = "java(DateUtil.format(entity.getServeEnd(), \"HH:mm\"))"),
            @Mapping(target = "serveFee", expression = "java(AmountChangeUtil.changeF2Y(String.valueOf(entity.getServeFee())))"),
            @Mapping(target = "itemTypeName", expression = "java(OrderProductionItemEnum.fromCode(entity.getItemType()))"),
            @Mapping(target = "productionName", expression = "java(entity.assemblerProductionName())"),
    })
    ProductionAppointmentInfoBackVo entity2VoForProductionAppointmentInfoBack(OrderProductionAppointmentEntity entity);

    /**
     * OrderProductionAppointmentEntity -> ProductionBoardAppointmentVo.Body
     *
     * @param entity
     * @return
     */
    @Mappings({
            @Mapping(target = "verificationStateName", expression = "java(ClientVerificationStatusEnum.fromCode(TerminalTypeEnum.TERMINAL_TYPE_CUSTOMER.code(),entity.getVerificationState()))"),
            @Mapping(target = "verificationPiStateName", expression = "java(ClientVerificationStatusEnum.fromCode(TerminalTypeEnum.TERMINAL_TYPE_PI.code(),entity.getVerificationState()))"),
            @Mapping(target = "day", expression = "java(DateUtil.formatDate(entity.getServeStart()))"),
            @Mapping(target = "productionName", expression = "java(entity.assemblerProductionName())"),
            @Mapping(target = "serveTypeName", expression = "java(ProductionServeTypeEnum.fromCode(entity.getServeType()))"),
            @Mapping(target = "serveStart", expression = "java(DateUtil.format(entity.getServeStart(), \"HH:mm\"))"),
            @Mapping(target = "serveEnd", expression = "java(DateUtil.format(entity.getServeEnd(), \"HH:mm\"))"),
            @Mapping(target = "status", source = "verificationState"),
            @Mapping(target = "hidePhone", expression = "java(SensitiveInformationUtil.conductPhoneOrIdCardNo(entity.getClientPhone()))"),
            @Mapping(target = "serveFee", expression = "java(AmountChangeUtil.changeF2Y(String.valueOf(entity.getServeFee())))"),
    })
    ProductionBoardAppointmentVo.Body entity2VoForProductionBoard(OrderProductionAppointmentEntity entity);

    ProductionAppointmentBoardWorkmanshipVo entity2VoForProductionAppointmentBoardWorkmanship(AppointmentBoardWorkmanshipEntity entity);


    List<ProductionAppointmentProductionScheduleVo> entity2VoForListProductionSchedule(List<AppointmentProductionScheduleEntity> entityList);

    /**
     * OrderProductionAppointmentEntity -> ProductionAppointmentMiniVo 批量
     *
     * @param entityList
     * @return
     */
    List<ProductionAppointmentMiniVo> entity2VoForListProductionAppointmentMini(List<OrderProductionAppointmentEntity> entityList);

    /**
     * OrderProductionAppointmentEntity -> ProductionAppointmentMiniVo
     *
     * @param entity
     * @return
     */
    @Mappings({
            @Mapping(target = "verificationStateName", expression = "java(ClientVerificationStatusEnum.fromCode(TerminalTypeEnum.TERMINAL_TYPE_CUSTOMER.code(),entity.getVerificationState()))"),
            @Mapping(target = "verificationPiStateName", expression = "java(ClientVerificationStatusEnum.fromCode(TerminalTypeEnum.TERMINAL_TYPE_PI.code(),entity.getVerificationState()))"),
            @Mapping(target = "productionName", expression = "java(entity.assemblerProductionName())"),
            @Mapping(target = "bookTypeName", expression = "java(ProductionBookTypeEnum.fromCode(entity.getBookType()))"),
            @Mapping(target = "itemTypeName", expression = "java(OrderProductionItemEnum.fromCode(entity.getItemType()))"),
            @Mapping(target = "serveStart", expression = "java(DateUtil.format(entity.getServeStart(), \"HH:mm\"))"),
            @Mapping(target = "serveEnd", expression = "java(DateUtil.format(entity.getServeEnd(), \"HH:mm\"))"),
            @Mapping(target = "appointmentTypeName", expression = "java(ProductionAppointmentTypeEnum.fromCode(entity.getAppointmentType()))"),
            @Mapping(target = "serveTypeName", expression = "java(ProductionServeTypeEnum.fromCode(entity.getServeType()))"),
            @Mapping(target = "serveFee", expression = "java(AmountChangeUtil.changeF2Y(String.valueOf(entity.getServeFee())))"),
            @Mapping(target = "hidePhone", expression = "java(SensitiveInformationUtil.conductPhoneOrIdCardNo(entity.getClientPhone()))"),
    })
    ProductionAppointmentMiniVo entity2VoForProductionAppointmentInfoMini(OrderProductionAppointmentEntity entity);

}

package com.stbella.order.server.utils.service.impl;

import com.stbella.core.result.Result;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.server.utils.service.FlowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.FlowIdentity;
import top.primecare.snowball.flow.core.definition.FlowIdentityBuilder;

import java.util.Arrays;

@Slf4j
@Service
public class FLowServiceImpl implements FlowService {

    @Override
    public Object fire(String bizActivity, Object req, Integer orderId, String... idSliceList) {
        log.info(Arrays.asList(idSliceList) + " ===>> req={}", req);
        // 业务线：场景：订单类型
        FlowIdentityBuilder flowIdentityBuilder = FlowIdentity.builder()
                .bizActivity(bizActivity);
        for (String idSlice : idSliceList) {
            flowIdentityBuilder.idSlice(idSlice);
        }
        FlowContext context = new FlowContext();
        context.setAttribute(req.getClass(), req);
        //默认流程一直走
        context.setAttribute(OtherConstant.CONTINUE, true);
        context.setAttribute(OtherConstant.ORDER_ID, orderId);
        SnowballFlowLauncher.fire(flowIdentityBuilder.build(), context);
        return Result.success(context.getAttribute(OtherConstant.RESULT));
    }
}

package com.stbella.order.server.manager;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.order.server.order.month.res.SaleSimpInfoVO;
import com.stbella.sso.server.dingding.entity.DdDepartmentPO;
import com.stbella.sso.server.dingding.entity.DdEmployeePO;
import com.stbella.sso.server.dingding.service.DdDepartmentService;
import com.stbella.sso.server.dingding.service.DdEmployeeService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: SsoManager
 * @date 2023/10/19 15:34
 */
@Component
public class SsoManager {

    private static final Logger log = LoggerFactory.getLogger(SsoManager.class);
    @DubboReference
    private DdEmployeeService ddEmployeeService;

    @DubboReference
    private DdDepartmentService ddDepartmentService;

    public SaleSimpInfoVO getByPhone(String phone) {
        DdEmployeePO byMobile = ddEmployeeService.getByMobile(phone);
        if (ObjectUtil.isNotEmpty(byMobile)) {
            SaleSimpInfoVO vo = new SaleSimpInfoVO();
            vo.setId(String.valueOf(byMobile.getId()));
            vo.setPhone(byMobile.getMobile());
            vo.setName(byMobile.getName());
            vo.setNamePhone(byMobile.getName() + "-" + byMobile.getMobile());
            return vo;
        }
        return null;
    }

    public SaleSimpInfoVO getBySellId(Long sellId) {
        DdEmployeePO byMobile = ddEmployeeService.queryEmployeeById(sellId);
        if (ObjectUtil.isNotEmpty(byMobile)) {
            SaleSimpInfoVO vo = new SaleSimpInfoVO();
            vo.setId(String.valueOf(byMobile.getId()));
            vo.setPhone(byMobile.getMobile());
            vo.setName(byMobile.getName());
            vo.setNamePhone(byMobile.getName() + "-" + byMobile.getMobile());
            return vo;
        }
        return null;
    }

    public List<DdEmployeePO> queryEmployeeByIds(List<Long> employeeIds){

        if (ObjectUtil.isEmpty(employeeIds)){
            return Lists.newArrayList();
        }
        List<DdEmployeePO> ddEmployeePOS = ddEmployeeService.queryEmployeeByIds(employeeIds);
        return CollectionUtils.isEmpty(ddEmployeePOS) ? Lists.newArrayList() : ddEmployeePOS;
    }

    public DdDepartmentPO queryDdDepartmentById(Long departmentId){

        try {
            log.info("部门信息查询departmentId:{}", departmentId);
            DdDepartmentPO ddDepartmentPO = ddDepartmentService.queryDdDepartmentById(departmentId);
            Assert.isTrue(Objects.nonNull(ddDepartmentPO), "部门信息不存在");
            return ddDepartmentPO;
        } catch (Exception e) {
            log.error("部门信息查询失败", e);
            return null;
        }
    }

    public DdEmployeePO queryEmployeeById(Long employeeId){
        try {
            log.info("员工信息查询employeeId:{}", employeeId);
            DdEmployeePO ddEmployeePO = ddEmployeeService.queryEmployeeById(employeeId);
            Assert.isTrue(Objects.nonNull(ddEmployeePO), "员工信息不存在");
            return ddEmployeePO;
        } catch (Exception e) {
            log.error("员工信息查询失败", e);
            return null;
        }
    }

}

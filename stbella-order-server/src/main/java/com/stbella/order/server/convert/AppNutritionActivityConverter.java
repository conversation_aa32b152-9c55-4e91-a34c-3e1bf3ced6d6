package com.stbella.order.server.convert;

import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.AheadOutRoomEntity;
import com.stbella.order.domain.order.production.OrderProductionAppointmentEntity;
import com.stbella.order.server.order.month.req.CreateAheadOutRoomReq;
import com.stbella.order.server.order.month.res.AheadOutRoomVO;
import com.stbella.order.server.order.nutrition.entity.NutritionActivityPO;
import com.stbella.order.server.order.nutrition.entity.NutritionActivityTargetPO;
import com.stbella.order.server.order.nutrition.request.NutritionActivityCreateReq;
import com.stbella.order.server.order.nutrition.request.NutritionActivityTargetCreateReq;
import com.stbella.order.server.order.nutrition.request.NutritionActivityUpdateReq;
import com.stbella.order.server.order.nutrition.response.NutritionActivityVO;
import com.stbella.order.server.order.production.res.ProductionAppointmentVo;

import org.mapstruct.Mapper;

import java.util.List;

import cn.hutool.core.date.DateUtil;

/**
 * <p>
 * 广禾堂活动
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26 16:42
 */
@Mapper(componentModel = "spring", imports = {DateUtil.class, AmountChangeUtil.class})
public interface AppNutritionActivityConverter {

    /**
     * create
     *
     * req -> po
     *
     * @param req
     * @return
     */
    NutritionActivityPO req2NutritionActivityPO(NutritionActivityCreateReq req);

    /**
     * update
     *
     * req -> po
     *
     * @param req
     * @return
     */
    NutritionActivityPO req2NutritionActivityPO(NutritionActivityUpdateReq req);

    /**
     * query one
     *
     * po -> po
     *
     * @param po
     * @return
     */
    NutritionActivityVO po2NutritionActivityVO(NutritionActivityPO po);

    /**
     * req -> po
     *
     * @param po
     * @return
     */
    NutritionActivityTargetPO req2NutritionActivityTargetPo(NutritionActivityTargetCreateReq.ActivityTargetReq po);

    /**
     * List<NutritionActivityPO> -> List<NutritionActivityVO>
     *
     * @param poList
     * @return
     */
    List<NutritionActivityVO> po2VoForListNutritionActivityVO(List<NutritionActivityPO> poList);



}

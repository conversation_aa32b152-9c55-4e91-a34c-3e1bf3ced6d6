package com.stbella.order.server.context.component.processor;

import cn.hutool.json.JSONUtil;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.server.order.ClientContractSignMainFact;
import com.stbella.order.server.order.month.req.ClientContractSignMainReq;
import com.stbella.order.server.order.month.res.ClientContractSignMainVO;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "客户合同签署主体处理器")
public class ClientContractSignMainProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    RuleLinkClient ruleLinkClient;

    @Override
    public void run(FlowContext bizContext) {

        ClientContractSignMainReq clientContractSignMainReq = bizContext.getResultAttribute(ClientContractSignMainReq.class);
        log.info("客户合同签署主体处理器 ==={}", JSONUtil.toJsonStr(clientContractSignMainReq));

        ExecuteRuleV2Req req = new ExecuteRuleV2Req(BizConstant.RuleLinkScene.SIGN_MAIN);
        ClientContractSignMainFact fact = new ClientContractSignMainFact();
        fact.setStoreId(clientContractSignMainReq.getStoreId());
        fact.setOrderType(clientContractSignMainReq.getOrderType());
        req.setFactObj(fact);
        HitRuleVo vo = ruleLinkClient.hitOneRuleWithException(req);
        ClientContractSignMainVO clientContractSignMainVO =  JSONUtil.toBean(vo.getSimpleRuleValue(), ClientContractSignMainVO.class);
        bizContext.addResultAttribute(ClientContractSignMainVO.class, clientContractSignMainVO);
    }
}

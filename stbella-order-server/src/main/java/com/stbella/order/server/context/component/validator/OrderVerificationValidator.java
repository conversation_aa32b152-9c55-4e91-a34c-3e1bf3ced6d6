package com.stbella.order.server.context.component.validator;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

@Slf4j
@Component
@RefreshScope
@SnowballComponent(name = "订单核销/入住状态判断")
public class OrderVerificationValidator implements IExecutableAtom<FlowContext> {

    @Override
    public void run(FlowContext bizContext) {
        /*HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        //订单为入住中 或者 订单已经已经核销过
        if (orderEntity.checkedInOrWrittenOff()) {
            CreateRefundReq createRefundReq = bizContext.getAttribute(CreateRefundReq.class);
            if (!createRefundReq.getRefundType().equals(RefundTypeEnum.BACK_ACCOUNT.getCode())) {
                throw new BusinessException(ErrorCodeEnum.REFUND_NUM_OR_AMOUNT.code().toString(), "订单入住中或产康已核销只允许退至意向金！");
            }
        }*/
    }
}

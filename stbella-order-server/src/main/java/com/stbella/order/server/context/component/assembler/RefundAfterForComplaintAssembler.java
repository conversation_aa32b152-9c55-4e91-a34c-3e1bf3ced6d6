package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.repository.HeCustomerComplaintsRepository;
import com.stbella.order.server.order.month.enums.CustomerComplaintsStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

@Component
@Slf4j
@SnowballComponent(name = "退款都成功后操作客诉", desc = "退款都成功后操作客诉")
public class RefundAfterForComplaintAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private HeCustomerComplaintsRepository customerComplaintsRepository;

    @Override
    public boolean condition(FlowContext context) {
        HeOrderRefundEntity refundEntity = context.getAttribute(HeOrderRefundEntity.class);
        log.info("退款id {},都成功后操作客诉", refundEntity.getId());
        HeCustomerComplaintsEntity customerComplaintsEntity = customerComplaintsRepository.selectByOrderRefundId(refundEntity.getId());
        log.info("退款 退款id {},都成功后操作客诉：customerComplaintsEntity:{}", refundEntity.getId(), customerComplaintsEntity);
        if (ObjectUtil.isEmpty(customerComplaintsEntity)) {
            return false;
        }
        context.setAttribute(HeCustomerComplaintsEntity.class, customerComplaintsEntity);
        return ObjectUtil.isNotEmpty(customerComplaintsEntity);

    }

    @Override
    public void run(FlowContext bizContext) {
        //从企微审批表中获取
        HeCustomerComplaintsEntity attribute = bizContext.getAttribute(HeCustomerComplaintsEntity.class);
        attribute.setComplaintStatus(CustomerComplaintsStatusEnum.SUCCESS.getCode());
        customerComplaintsRepository.update(attribute);
    }


}

//package com.stbella.order.server.context.component.processor;
//
//import com.stbella.order.domain.order.month.entity.HeOrderEntity;
//import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
//import com.stbella.order.domain.order.month.entity.IncomePaidAllocationEntity;
//import com.stbella.order.domain.order.service.OrderGoodsDomainService;
//import com.stbella.order.domain.repository.IncomePaidAllocationRepository;
//import com.stbella.order.domain.repository.OrderGoodsRepository;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.cloud.context.config.annotation.RefreshScope;
//import org.springframework.stereotype.Component;
//import top.primecare.snowball.flow.annotation.SnowballComponent;
//import top.primecare.snowball.flow.core.context.FlowContext;
//import top.primecare.snowball.flow.core.definition.IExecutableAtom;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.math.RoundingMode;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
///**
// * 商品价格分摊处理器
// * 优化点：
// * 1. 提取核心逻辑到独立方法
// * 2. 增加尾差处理
// * 3. 增加参数校验
// * 4. 优化日志输出
// */
//@Component
//@Slf4j
//@RefreshScope
//@SnowballComponent(name = "商品价格信息分摊", desc = "商品价格信息分摊")
//public class GoodsAllocateProcessor implements IExecutableAtom<FlowContext> {
//
//    @Resource
//    private OrderGoodsRepository orderGoodsRepository;
//
//    @Resource
//    private OrderGoodsDomainService orderGoodsDomainService;
//
//    @Resource
//    private IncomePaidAllocationRepository incomePaidAllocationRepository;
//
//    @Override
//    public void run(FlowContext bizContext) {
//        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
//        if (orderEntity == null || orderEntity.getOrderId() == null) {
//            log.error("订单信息为空，无法进行价格分摊");
//            return;
//        }
//
//        log.info("开始处理订单{}的商品价格分摊", orderEntity.getOrderId());
//        processGoodsAllocation(orderEntity);
//        log.info("订单{}的商品价格分摊处理完成", orderEntity.getOrderId());
//
//    }
//
//    private void processGoodsAllocation(HeOrderEntity orderEntity) {
//        // 计算订单应付和实付金额
//        Integer payableAmount = orderEntity.getPaidAmount();
//
//        // 获取订单商品列表
//        List<HeOrderGoodsEntity> orderGoodsList = orderGoodsDomainService.queryOrderGoodsWithGroup(orderEntity.getOrderId());
//        if (orderGoodsList.isEmpty()) {
//            log.warn("订单{}没有商品信息", orderEntity.getOrderId());
//            return;
//        }
//
//
//        // 计算商品待付金额
//        Map<Long, Integer> goodsUnPayAmountMap = calculateGoodsUnpaidAmount(orderGoodsList);
//
//        // 计算待付总金额
//        int goodsUnPayTotal = goodsUnPayAmountMap.values().stream().mapToInt(Integer::intValue).sum();
//        if (goodsUnPayTotal <= 0) {
//            log.info("订单{}所有商品都已支付完成", orderEntity.getOrderId());
//            return;
//        }
//
//        // 分摊未支付金额并处理尾差
//        allocateUnpaidAmountWithRoundingError(orderGoodsList, goodsUnPayAmountMap, goodsPaidAmountMap,
//                goodsUnPayTotal, unPayAmount);
//
//        List<HeOrderGoodsEntity> updatedGoodsList = new ArrayList<>(orderGoodsList);
//
//        // 组合商品子商品再进行分摊一波
//        orderGoodsList.forEach(goods -> {
//            if (goods.isCombine()) {
//                Integer totalAllocationOriginPrice = goods.getTotalAllocationOriginPrice();
//                List<HeOrderGoodsEntity> subList = goods.getSubList().stream().filter(sub -> sub.getTotalAllocationOriginPrice() > 0)
//                        .collect(Collectors.toList());
//                if (!subList.isEmpty()) {
//                    int totalSubAllocated = subList.stream().map(HeOrderGoodsEntity::getTotalAllocationOriginPrice).mapToInt(Integer::intValue).sum();
//                    int remainingSubError = totalAllocationOriginPrice;
//
//                    List<HeOrderGoodsEntity> sortedSubGoods = new ArrayList<>(subList);
//                    sortedSubGoods.sort((a, b) ->
//                            b.getTotalAllocationOriginPrice().compareTo(a.getTotalAllocationOriginPrice()));
//
//                    for (int i = 0; i < sortedSubGoods.size() - 1; i++) {
//                        HeOrderGoodsEntity subGoods = sortedSubGoods.get(i);
//                        Integer subTotalAllocationPrice = subGoods.getTotalAllocationOriginPrice();
//
//                        BigDecimal totalAllocationOriginPriceBD = BigDecimal.valueOf(totalAllocationOriginPrice);
//                        BigDecimal subTotalAllocationPriceBD = BigDecimal.valueOf(subTotalAllocationPrice);
//                        BigDecimal totalSubAllocatedBD = BigDecimal.valueOf(totalSubAllocated);
//
//                        int subAllocation = totalAllocationOriginPriceBD
//                                .multiply(subTotalAllocationPriceBD)
//                                .divide(totalSubAllocatedBD, 8, RoundingMode.HALF_UP)
//                                .setScale(0, RoundingMode.HALF_UP)
//                                .intValue();
//
//                        subGoods.setTotalAllocationOriginPrice(subAllocation);
//
//                        // 使用BigDecimal计算单价
//                        BigDecimal subAllocationBD = BigDecimal.valueOf(subAllocation);
//                        BigDecimal goodsNumBD = BigDecimal.valueOf(subGoods.getGoodsNum());
//                        int unitPrice = subAllocationBD.divide(goodsNumBD, 0, RoundingMode.HALF_UP).intValue();
//
//                        subGoods.setAllocationOriginPrice(unitPrice);
//
//                        remainingSubError -= subAllocation;
//
//                        log.info("组合商品[{}]子商品[{}]分摊结果: 分摊金额={}, 单价={}",
//                                goods.getId(), subGoods.getId(), subAllocation, unitPrice);
//                    }
//
//                    HeOrderGoodsEntity lastSubGoods = sortedSubGoods.get(sortedSubGoods.size() - 1);
//                    lastSubGoods.setTotalAllocationOriginPrice(remainingSubError);
//
//                    // 使用BigDecimal计算最后一个子商品的单价
//                    BigDecimal remainingSubErrorBD = BigDecimal.valueOf(remainingSubError);
//                    BigDecimal lastGoodsNumBD = BigDecimal.valueOf(lastSubGoods.getGoodsNum());
//                    int lastUnitPrice = remainingSubErrorBD.divide(lastGoodsNumBD, 0, RoundingMode.HALF_UP).intValue();
//
//                    lastSubGoods.setAllocationOriginPrice(lastUnitPrice);
//
//                    log.info("组合商品[{}]子商品[{}]分摊结果(尾差处理): 分摊金额={}, 单价={}",
//                            goods.getId(), lastSubGoods.getId(), remainingSubError, lastUnitPrice);
//                    updatedGoodsList.addAll(subList);
//                }
//            }
//        });
//
//        // 批量更新商品信息
//        orderGoodsRepository.batchUpdate(updatedGoodsList);
//    }
//
//    private Map<Long, Integer> calculateGoodsPaidAmount(Integer orderId) {
//        List<IncomePaidAllocationEntity> paidAllocations = incomePaidAllocationRepository.queryListByOrderId(orderId.longValue());
//        return paidAllocations.stream()
//                .collect(Collectors.groupingBy(IncomePaidAllocationEntity::getOrderGoodsId,
//                        Collectors.summingInt(IncomePaidAllocationEntity::getPaidAmount)));
//    }
//
//    private Map<Long, Integer> calculateGoodsUnpaidAmount(
//            List<HeOrderGoodsEntity> orderGoodsList,
//            Map<Long, Integer> goodsPaidAmountMap) {
//        return orderGoodsList.stream()
//                .collect(Collectors.toMap(
//                        goods -> goods.getId().longValue(),
//                        goods -> {
//                            return goods.getTotalAllocationOriginPrice();
//                        }
//                ));
//    }
//
//    private void allocateUnpaidAmountWithRoundingError(
//            List<HeOrderGoodsEntity> orderGoodsList,
//            Map<Long, Integer> goodsUnPayAmountMap,
//            Map<Long, Integer> goodsPaidAmountMap,
//            int goodsUnPayTotal,
//            int unPayAmount) {
//
//        // 按未付金额降序排序
//        List<HeOrderGoodsEntity> sortedGoods = orderGoodsList.stream()
//                .filter(goods -> goodsUnPayAmountMap.get(goods.getId().longValue()) > 0)
//                .sorted((a, b) -> goodsUnPayAmountMap.get(b.getId().longValue())
//                        .compareTo(goodsUnPayAmountMap.get(a.getId().longValue())))
//                .collect(Collectors.toList());
//
//        if (sortedGoods.isEmpty()) {
//            log.info("没有需要分摊的商品");
//            return;
//        }
//
//        int allocatedTotal = 0;
//
//        // 处理除最后一个商品外的所有商品
//        for (int i = 0; i < sortedGoods.size() - 1; i++) {
//            HeOrderGoodsEntity goods = sortedGoods.get(i);
//            int goodsUnPay = goodsUnPayAmountMap.get(goods.getId().longValue());
//
//            // 优化：先乘后除，减少精度损失
//            BigDecimal allocation = BigDecimal.valueOf(goodsUnPay)
//                    .multiply(BigDecimal.valueOf(unPayAmount))
//                    .divide(BigDecimal.valueOf(goodsUnPayTotal), 0, RoundingMode.HALF_UP);
//
//            int allocatedAmount = allocation.intValue();
//            updateGoodsAllocation(goods, allocatedAmount, goodsPaidAmountMap);
//            allocatedTotal += allocatedAmount;
//        }
//
//        // 最后一个商品处理尾差
//        HeOrderGoodsEntity lastGoods = sortedGoods.get(sortedGoods.size() - 1);
//        int remainingAmount = unPayAmount - allocatedTotal;
//        updateGoodsAllocation(lastGoods, remainingAmount, goodsPaidAmountMap);
//
//        log.info("订单商品分摊完成，总分摊金额: {}, 未支付金额: {}", unPayAmount, unPayAmount);
//    }
//
//
//
//    private void updateGoodsAllocation(
//            HeOrderGoodsEntity goods,
//            Integer allocation,
//            Map<Long, Integer> goodsPaidAmountMap) {
//        Integer paidAmount = goodsPaidAmountMap.getOrDefault(goods.getId().longValue(), 0);
//        int totalAllocatePrice = paidAmount + allocation;
//
//        // 使用BigDecimal计算单价
//        BigDecimal totalPriceBD = BigDecimal.valueOf(totalAllocatePrice);
//        BigDecimal goodsNumBD = BigDecimal.valueOf(goods.getGoodsNum());
//        int unitPrice = totalPriceBD.divide(goodsNumBD, 0, RoundingMode.HALF_UP).intValue();
//
//        goods.setAllocationOriginPrice(unitPrice);
//        goods.setTotalAllocationOriginPrice(totalAllocatePrice);
//
//        log.debug("商品[{}]分摊结果: 已付金额={}, 本次分摊={}, 总分摊金额={}, 单价={}",
//                goods.getId(), paidAmount, allocation, totalAllocatePrice, unitPrice);
//    }
//
//    public static void main(String[] args) {
//
//
//        Integer totalAllocationOriginPrice = 20900000;
//        List<HeOrderGoodsEntity> subList = new ArrayList<>();
//
//        HeOrderGoodsEntity orderGoodsEntity = new HeOrderGoodsEntity();
//        orderGoodsEntity.setTotalAllocationOriginPrice(1074592);
//        orderGoodsEntity.setGoodsNum(1);
//        orderGoodsEntity.setGoodsId(26070);
//        orderGoodsEntity.setSkuId(32457);
//        subList.add(orderGoodsEntity);
//
//        HeOrderGoodsEntity orderGoodsEntity2 = new HeOrderGoodsEntity();
//        orderGoodsEntity2.setTotalAllocationOriginPrice(105236);
//        orderGoodsEntity2.setGoodsNum(1);
//        orderGoodsEntity2.setGoodsId(26069);
//        orderGoodsEntity2.setSkuId(32456);
//        subList.add(orderGoodsEntity2);
//
//        HeOrderGoodsEntity orderGoodsEntity3 = new HeOrderGoodsEntity();
//        orderGoodsEntity3.setTotalAllocationOriginPrice(425503);
//        orderGoodsEntity3.setGoodsNum(1);
//        orderGoodsEntity3.setGoodsId(25374);
//        orderGoodsEntity3.setSkuId(31034);
//        subList.add(orderGoodsEntity3);
//
//        HeOrderGoodsEntity orderGoodsEntity4 = new HeOrderGoodsEntity();
//        orderGoodsEntity4.setTotalAllocationOriginPrice(484669);
//        orderGoodsEntity4.setGoodsNum(2);
//        orderGoodsEntity4.setGoodsId(25375);
//        orderGoodsEntity4.setSkuId(31035);
//        subList.add(orderGoodsEntity4);
//
//        HeOrderGoodsEntity orderGoodsEntity5 = new HeOrderGoodsEntity();
//        orderGoodsEntity5.setTotalAllocationOriginPrice(18810000);
//        orderGoodsEntity5.setGoodsNum(3);
//        orderGoodsEntity5.setGoodsId(25281);
//        orderGoodsEntity5.setSkuId(30919);
//        subList.add(orderGoodsEntity5);
//
//
//        if (!subList.isEmpty()) {
//            int totalSubAllocated = subList.stream().map(HeOrderGoodsEntity::getTotalAllocationOriginPrice).mapToInt(Integer::intValue).sum();
//            int remainingSubError = totalAllocationOriginPrice;
//
//            List<HeOrderGoodsEntity> sortedSubGoods = new ArrayList<>(subList);
//            sortedSubGoods.sort((a, b) ->
//                    b.getTotalAllocationOriginPrice().compareTo(a.getTotalAllocationOriginPrice()));
//
//            for (int i = 0; i < sortedSubGoods.size() - 1; i++) {
//                HeOrderGoodsEntity subGoods = sortedSubGoods.get(i);
//                double ratio = (double) subGoods.getTotalAllocationOriginPrice() / totalSubAllocated;
//                int subAllocation = (int) Math.round(totalAllocationOriginPrice * ratio);
//
//                subGoods.setTotalAllocationOriginPrice(subAllocation);
//                subGoods.setAllocationOriginPrice(subAllocation / subGoods.getGoodsNum());
//
//                totalSubAllocated += subAllocation;
//                remainingSubError -= subAllocation;
//
//                log.debug("组合商品[{}]子商品[{}]分摊结果: 分摊比例={}, 分摊金额={}", "a", subGoods.getId(), ratio, subAllocation);
//            }
//
//            HeOrderGoodsEntity lastSubGoods = sortedSubGoods.get(sortedSubGoods.size() - 1);
//            lastSubGoods.setTotalAllocationOriginPrice(remainingSubError);
//            lastSubGoods.setAllocationOriginPrice(remainingSubError / lastSubGoods.getGoodsNum());
//
//        }
//
//        System.out.println(subList);
//
//    }
//
//}

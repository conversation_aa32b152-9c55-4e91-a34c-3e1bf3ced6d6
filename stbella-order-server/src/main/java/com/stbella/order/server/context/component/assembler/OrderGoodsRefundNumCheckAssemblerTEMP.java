package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.collection.CollectionUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.domain.order.refund.dto.OrderRefundGoodsDTO;
import com.stbella.platform.order.api.refund.api.OrderRefundService;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import com.stbella.platform.order.api.refund.req.QueryRefundGoodsReq;
import com.stbella.platform.order.api.refund.res.QueryOrderRefundInfoRes;
import com.stbella.platform.order.api.refund.res.QueryRefundGoodsRes;
import com.stbella.platform.order.api.req.CustomerComplaintsCreateRefundReq;
import com.stbella.platform.order.api.req.CustomerComplaintsCreateReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


@Component
@Slf4j
@SnowballComponent(name = "订单商品的支付退款情况", desc = "获取当前订单的支付信息")
public class OrderGoodsRefundNumCheckAssemblerTEMP implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderRefundService orderRefundService;

    @Override
    public void run(FlowContext bizContext) {
        List<OrderRefundGoodsDTO> orderRefundGoodsDTOS = new ArrayList<>();
        CustomerComplaintsCreateReq complaintsCreateReq = bizContext.getAttribute(CustomerComplaintsCreateReq.class);
        Optional.ofNullable(complaintsCreateReq)
                .map(CustomerComplaintsCreateReq::getCreateRefundReq)
                .map(CustomerComplaintsCreateRefundReq::getNewCreateRefundReq)
                .ifPresent(newCreateRefundReq -> {
                    //再次请求订单可退商品的接口，跟发起的数量进行匹配校验
                    QueryRefundGoodsReq req = new QueryRefundGoodsReq();
                    req.setOrderId(complaintsCreateReq.getOrderId().intValue());
                    Result<QueryOrderRefundInfoRes> queryOrderRefundInfoResResult = orderRefundService.queryGoods(req);
                    Optional.ofNullable(queryOrderRefundInfoResResult)
                            .map(Result::getData)
                            .map(QueryOrderRefundInfoRes::getQueryRefundGoodsResList)
                            .ifPresent(list -> {
                                for (QueryRefundGoodsRes queryRefundGoodsRes : list) {
                                    List<QueryRefundGoodsRes> child = queryRefundGoodsRes.getChild();
                                    if (CollectionUtil.isNotEmpty(child)) {
                                        for (QueryRefundGoodsRes refundGoodsRes : child) {
                                            OrderRefundGoodsDTO orderRefundGoodsDTO = new OrderRefundGoodsDTO();
                                            orderRefundGoodsDTO.setParentOrderGoodsSn(queryRefundGoodsRes.getOrderGoodsSn());
                                            orderRefundGoodsDTO.setOrderGoodsSn(refundGoodsRes.getOrderGoodsSn());
                                            orderRefundGoodsDTO.setOrderId(complaintsCreateReq.getOrderId().intValue());
                                            orderRefundGoodsDTO.setRefundNum(refundGoodsRes.getRefundGoodsNum());
                                            orderRefundGoodsDTOS.add(orderRefundGoodsDTO);
                                        }
                                    } else {
                                        OrderRefundGoodsDTO orderRefundGoodsDTO = new OrderRefundGoodsDTO();
                                        orderRefundGoodsDTO.setParentOrderGoodsSn(null);
                                        orderRefundGoodsDTO.setOrderGoodsSn(queryRefundGoodsRes.getOrderGoodsSn());
                                        orderRefundGoodsDTO.setOrderId(complaintsCreateReq.getOrderId().intValue());
                                        orderRefundGoodsDTO.setRefundNum(queryRefundGoodsRes.getRefundGoodsNum());
                                        orderRefundGoodsDTOS.add(orderRefundGoodsDTO);
                                    }
                                }
                            });
                    List<CreateRefundReq.GoodsInfo> goodsInfoList = newCreateRefundReq.getGoodsInfoList();
                    if (CollectionUtil.isNotEmpty(goodsInfoList)) {
                        for (CreateRefundReq.GoodsInfo goodsInfo : goodsInfoList) {
                            //申请数量
                            Integer refundNum = goodsInfo.getRefundNum();
                            Integer canRefundNum = 0;
                            Optional<OrderRefundGoodsDTO> first = orderRefundGoodsDTOS.stream().filter(o -> o.getOrderGoodsSn().equals(goodsInfo.getOrderGoodsSn())).findFirst();
                            if (first.isPresent()) {
                                canRefundNum = first.get().getRefundNum();
                            }
                            if (refundNum > canRefundNum) {
                                throw new BusinessException(ErrorCodeEnum.REFUND_NUM_OR_AMOUNT.code().toString(), goodsInfo.getGoodsName() + "最多可退数量" + canRefundNum + "！");
                            }
                        }
                    }

                });
    }

}

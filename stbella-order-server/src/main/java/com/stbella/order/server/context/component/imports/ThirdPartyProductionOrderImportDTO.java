package com.stbella.order.server.context.component.imports;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2023-07-25  14:43
 * @Description: 三方产康订单导入
 */
@Data
public class ThirdPartyProductionOrderImportDTO implements Serializable {

    @ApiModelProperty(value = "门店名称")
    @ExcelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "会所门店id")
    @ExcelIgnore
    private Integer storeId;

    @ApiModelProperty(value = "客户名称")
    @ExcelProperty(value = "客户名称")
    private String clientName;

    @ApiModelProperty(value = "三方供应商名称")
    @ExcelProperty(value = "三方供应商名称")
    private String thirdPartyName;

    @ApiModelProperty(value = "产品类型")
    @ExcelProperty(value = "产品类型")
    private String productTypeName;

    @ApiModelProperty(value = "产品名称")
    @ExcelProperty(value = "产品名称")
    private String productName;


    @ApiModelProperty(value = "三方收款日期")
    @ExcelProperty(value = "三方收款日期")
    private String orderTime;

    @ApiModelProperty(value = "爆单日期")
    @ExcelProperty(value = "爆单日期")
    private String percentFirstTime;

    @ApiModelProperty(value = "签单金额")
    @ExcelProperty(value = "签单金额")
    private String payAmount;

    @ApiModelProperty(value = "客户实付金额")
    @ExcelProperty(value = "客户实付金额")
    private String paidAmount;

    @ApiModelProperty(value = "销售")
    @ExcelProperty(value = "销售")
    private String saleName;

    @ApiModelProperty(value = "销售手机号")
    @ExcelProperty(value = "销售手机号")
    private String salePhone;

}

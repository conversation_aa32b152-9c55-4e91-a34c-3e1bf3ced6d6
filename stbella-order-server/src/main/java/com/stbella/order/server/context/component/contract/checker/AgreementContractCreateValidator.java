package com.stbella.order.server.context.component.contract.checker;

import cn.hutool.json.JSONUtil;
import com.stbella.contract.model.enums.ContractStatusEnum;
import com.stbella.contract.model.enums.ContractTypeEnum;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AgreementContractCreateValidator extends AbstractContractCreateValidator {

    @Override
    public boolean doValidate(ContractValidationContext context) {
        List<OrderContractSignRecordVO> signRecords = context.getSignRecords()
                .stream()
                .filter(o ->
                        TemplateContractTypeEnum.YZ_SAINTBELLA.code().equals(o.getTemplateContractType())
                )
                .collect(Collectors.toList());
        Integer orderId = context.getOrderEntity().getOrderId();
        if (signRecords.isEmpty()) {
            log.error("订单{}没有签署合同", orderId);
            throw new IllegalArgumentException("订单没有签署合同");
        }
        OrderContractSignRecordVO orderContractSignRecordVO = signRecords.get(0);
        log.info("订单{}的主合同信息{}", orderId, JSONUtil.toJsonStr(orderContractSignRecordVO));
        if (!Objects.equals(orderContractSignRecordVO.getContractStatus(), ContractStatusEnum.SIGNED.code())) {
            log.error("订单{}的主合同没有签署", orderId);
            throw new IllegalArgumentException("订单的主合同没有签署");
        }
        if (Objects.equals(orderContractSignRecordVO.getContractType(), ContractTypeEnum.PAPER_TYPE.code())) {
            log.error("订单{}的主合同是纸质合同", orderId);
            throw new IllegalArgumentException("订单的主合同是纸质合同");
        }
        return true;
    }

    @Override
    public TemplateContractTypeEnum templateContractType() {
        return TemplateContractTypeEnum.SUPPLIMENT;
    }
}

package com.stbella.order.server.contract.month.service.impl;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.month.*;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.context.component.DiscountCalculator;
import com.stbella.order.server.context.component.StoreCurrencyContainer;
import com.stbella.order.server.contract.enums.ContractExceptionEnum;
import com.stbella.order.server.contract.month.component.MonthContractAddressAssembler;
import com.stbella.order.server.contract.month.component.MonthContractAssembler;
import com.stbella.order.server.contract.req.MonthContractQuery;
import com.stbella.order.server.contract.req.MonthContractSignQuery;
import com.stbella.order.server.contract.req.SignContractQuery;
import com.stbella.order.server.contract.res.*;
import com.stbella.order.server.contract.service.month.MonthContractAuthenticationService;
import com.stbella.order.server.convert.AppMonthContractSignRecordConverter;
import com.stbella.order.server.order.DiscountFact;
import com.stbella.order.server.order.month.req.AheadOutRoomQuery;
import com.stbella.order.server.order.month.res.OrderDiscountsCacheVO;
import com.stbella.order.server.order.month.service.MonthOrderWxQueryService;
import com.stbella.order.server.utils.BigDecimalUtil;

import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
@DubboService
@RefreshScope
public class MonthContractAuthenticationServiceImpl implements MonthContractAuthenticationService {


    @Resource
    private MonthContractSignRecordRepository monthContractSignRecordRepository;

    @Resource
    private MonthContractSignAgreementRepository monthContractSignAgreementRepository;

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private MonthOrderWxQueryService monthOrderWxQueryService;

    @Resource
    private IncomeRecordRepository incomeRecordRepository;

    @Resource
    private StoreRepository storeRepository;

    @Resource
    private ClientRepository clientRepository;

    @Resource
    private OrderGoodsRepository orderGoodsRepository;

    @Resource
    private AppMonthContractSignRecordConverter appMonthContractSignRecordConverter;

    @Resource
    private ContractSignRecordPaperRepository contractSignRecordPaperRepository;

    @Resource
    private MonthContractAddressAssembler monthContractAddressAssembler;

    @Resource
    private MonthContractAssembler monthContractAssembler;

    @Resource
    private AheadOutRoomRepository aheadOutRoomRepository;

    @Resource
    private HeOrderAttachmentRepository heOrderAttachmentRepository;

    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;
    @Resource
    private DiscountCalculator discountCalculator;
    @Value("${xcx-front-helper.host}")
    private String helperUrl;

    @Override
    public Result<ContractSignAddressVO> getContractSignAddress(SignContractQuery signContractQuery) {
        ContractSignAddressVO contractSignAddress = null;
        try {
            contractSignAddress = monthContractAddressAssembler.contractSignAddressAssembler(signContractQuery);
            //点击的是支付宝 特殊处理链接 https://web-activity-test.primecare.top/#/eSignedPo?redirect_url=https%3A%2F%2Fsmlt.esign.cn%2Fyw0zt5xdFepv
            if (Objects.nonNull(contractSignAddress) && Objects.equals(signContractQuery.getContractMode(), ContractModeEnum.ALIPAY_TYPE.code())) {
                contractSignAddress.setContractSignAddress(String.format("%s/#/eSignedPo?redirect_url=%s", this.helperUrl, URLEncoder.encode(contractSignAddress.getContractSignAddress(), "UTF-8")));
            }
        } catch (UnsupportedEncodingException e) {
            log.error("获取合同链接异常：{}", JSONUtil.toJsonStr(e.getStackTrace()));
        }
        return Result.success(contractSignAddress);
    }

    @Override
    public Result<ContractAddressVO> getCheckContractUrl(Long contractId, Integer templateType) {
        ContractAddressVO addressVO = monthContractAddressAssembler.checkContractUrlAssembler(contractId, templateType);
        if (Objects.isNull(addressVO) || StringUtils.isBlank(addressVO.getDownloadUrl())) {
            throw new BusinessException(ContractExceptionEnum.CONTRACT_URL_NULL.getCode(), ContractExceptionEnum.CONTRACT_URL_NULL.getValue());
        }
        return Result.success(addressVO);
    }

    @Override
    public Result<ContractOrderVO> orderList(Long orderId, Integer templateContractType) {

        // todo
        // 1.针对已上传纸质合同（含审批中情况）隐藏线上签约没有签订归档的入口

        ContractOrderVO result = new ContractOrderVO();
        // 补充协议
        List<MonthContractSignAgreementEntity> agreementList = monthContractSignAgreementRepository.getByOrderIdList(ListUtil.toList(orderId));

        // 签订记录
        List<MonthContractSignRecordEntity> signList = monthContractSignRecordRepository.getByOrderIdList(ListUtil.toList(orderId));

        // 订单信息
        HeOrderEntity heOrder = orderRepository.getByOrderId(orderId.intValue());
        String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(heOrder.getStoreId());

        //展示所有已经签订的纸质合同
        List<ContractSignRecordPaperEntity> paperList = contractSignRecordPaperRepository.getByOrderId(orderId.intValue());

        AheadOutRoomEntity aheadOutRoomEntity = aheadOutRoomRepository.queryByOrderId(new AheadOutRoomQuery().setOrderId(orderId.intValue()));
        // 订单类合同
        List<ContractOrderVO.ContractInfo> orderInfoList = new ArrayList<>();
        for (TemplateContractTypeV5Enum value : TemplateContractTypeV5Enum.values()) {
            if (Objects.isNull(aheadOutRoomEntity) && Objects.equals(value.code(), TemplateContractTypeV5Enum.RELEASE.code())) {
                continue;
            }
            ContractOrderVO.ContractInfo contractInfo = new ContractOrderVO.ContractInfo();

            HeOrderUserSnapshotEntity userEsign = orderUserSnapshotRepository.queryByOrderId(Math.toIntExact(orderId));
            contractInfo.setContractType(ContractTypeEnum.ESIGN_TYPE.code());
            contractInfo.setTemplateType(TemplateTypeEnum.MAIN_TYPE.code());
            contractInfo.setTemplateContractType(value.code());
            contractInfo.setContractName(value.title());
            contractInfo.setContractDesc(value.desc());
            contractInfo.setOrderId(orderId);
            contractInfo.setCurrency(storeCurrencyCode);
            contractInfo.setCertType(userEsign.getCertType());
            contractInfo.setAmountEarnest(BigDecimalUtil.divide(new BigDecimal(heOrder.getPayAmount() / 2), new BigDecimal(100)).toString());
            Optional<MonthContractSignRecordEntity> first = signList.stream().filter(item -> Objects.equals(value.code(), item.getTemplateContractType()) && Objects.equals(ContractStatusEnum.SIGNED.code(), item.getContractStatus())).findFirst();
            contractInfo.setSign(first.isPresent());
            contractInfo.setContractId(first.isPresent() ? first.get().getId().intValue() : null);
            if (Objects.equals(value.code(), TemplateContractTypeV5Enum.RELEASE.code())) {
                contractInfo.setApiRoute(1);
            } else {
                contractInfo.setApiRoute(0);
            }
            orderInfoList.add(contractInfo);
        }
        //纸质合同的 母婴合同&预约协议合同&合同解除协议
        paperList.stream().filter(i -> TemplateContractTypeV5Enum.allList().contains(i.getContractType())).forEach(
                item -> {
                    //针对已上传纸质合同（含审批中情况）隐藏线上签约没有签订归档的入口
                    List<ContractOrderVO.ContractInfo> needFilterContractOrderList = orderInfoList.stream().filter(o -> Objects.equals(o.getTemplateContractType(), item.getContractType()) && !o.getSign()).collect(Collectors.toList());

                    if (CollectionUtil.isNotEmpty(needFilterContractOrderList)) {
                        orderInfoList.removeAll(needFilterContractOrderList);
                    }

                    ContractOrderVO.ContractInfo contractInfo = new ContractOrderVO.ContractInfo();
                    contractInfo.setContractId(item.getId());
                    contractInfo.setContractType(ContractTypeEnum.PAPER_TYPE.code());
                    contractInfo.setTemplateType(TemplateTypeEnum.MAIN_TYPE.code());
                    contractInfo.setTemplateContractType(item.getContractType());
                    contractInfo.setContractName(item.getContractName());
                    contractInfo.setContractDesc(Objects.nonNull(TemplateContractTypeV5Enum.from(item.getContractType())) ? TemplateContractTypeV5Enum.from(item.getContractType()).desc() : "");
                    contractInfo.setOrderId(orderId);
                    contractInfo.setSign(Objects.equals(ContractStatusEnum.SIGNED.code(), item.getContractStatus()));
                    contractInfo.setApiRoute(2);
                    orderInfoList.add(contractInfo);
                }
        );

        result.setOrderInfoList(orderInfoList.stream().sorted(Comparator.comparing(ContractOrderVO.ContractInfo::getTemplateContractType).reversed()).collect(Collectors.toList()));

        // 附属合同
        List<ContractOrderVO.AppurtenanceInfo> appurtenanceInfoList = new ArrayList<>();

        // 添加补充协议
        if (CollectionUtil.isNotEmpty(agreementList)) {
            for (MonthContractSignAgreementEntity agreement : agreementList) {
                ContractOrderVO.AppurtenanceInfo contractInfo = new ContractOrderVO.AppurtenanceInfo();
                contractInfo.setContractType(ContractTypeEnum.ESIGN_TYPE.code());
                contractInfo.setTemplateType(TemplateTypeEnum.AGREEMENT_TYPE.code());
                contractInfo.setTemplateContractType(TemplateContractTypeEnum.SUPPLIMENT.code());
                contractInfo.setContractName(agreement.getName());
                contractInfo.setContractDesc(TemplateContractTypeV6Enum.SUPPLIMENT.desc());
                contractInfo.setOrderId(orderId);
                contractInfo.setContractId(agreement.getId());
                contractInfo.setSign(Objects.equals(ContractStatusEnum.SIGNED.code(), agreement.getState()));
                contractInfo.setApiRoute(2);
                HeOrderUserSnapshotEntity userEsign = orderUserSnapshotRepository.queryByOrderId(Math.toIntExact(orderId));
                contractInfo.setCertType(userEsign.getCertType());
                appurtenanceInfoList.add(contractInfo);
            }
        }

        if (Objects.equals(heOrder.getSignType(), OrderSignTypeEnum.SIGN_TYPE_BAILOR.code())) {
            ContractOrderVO.AppurtenanceInfo contractInfo = new ContractOrderVO.AppurtenanceInfo();
            contractInfo.setContractType(ContractTypeEnum.ESIGN_TYPE.code());
            contractInfo.setTemplateType(TemplateTypeEnum.ACCESSORY_TYPE.code());
            contractInfo.setTemplateContractType(TemplateContractTypeV2Enum.ENTRUST.code());
            contractInfo.setContractName(TemplateContractTypeV6Enum.ENTRUST.title());
            contractInfo.setContractDesc(TemplateContractTypeV6Enum.ENTRUST.desc());
            contractInfo.setOrderId(orderId);
            Optional<MonthContractSignRecordEntity> first = signList.stream().filter
                    (item -> Objects.equals(TemplateContractTypeV2Enum.ENTRUST.code(), item.getTemplateContractType()) && Objects.equals(ContractStatusEnum.SIGNED.code(), item.getContractStatus())).findFirst();
            contractInfo.setContractId(first.isPresent() ? first.get().getId() : -1L);
            contractInfo.setSign(first.isPresent());
            contractInfo.setApiRoute(1);
            HeOrderUserSnapshotEntity userEsign = orderUserSnapshotRepository.queryByOrderId(Math.toIntExact(orderId));
            contractInfo.setCertType(userEsign.getCertType());
            appurtenanceInfoList.add(contractInfo);
        }

        // 订单折扣保密协议
        Optional<MonthContractSignRecordEntity> first = signList.stream().filter
                (item -> Objects.equals(TemplateContractTypeV6Enum.DISCOUNT.code(), item.getTemplateContractType()) && Objects.equals(ContractStatusEnum.SIGNED.code(), item.getContractStatus())).findFirst();
        if (StringUtils.isNotBlank(heOrder.getDiscountDetails()) || first.isPresent()) {
            boolean discountFlag = false;
            if (StringUtils.isNotBlank(heOrder.getDiscountDetails())) {
                OrderDiscountsCacheVO discountsCache = JSONObject.toJavaObject(JSON.parseObject(heOrder.getDiscountDetails()), OrderDiscountsCacheVO.class);
                //配置折扣对象
                DiscountFact discountFactReq = DiscountFact.builder()
                        .orderType(heOrder.getOrderType())
                        .grossMargin(discountsCache.getGrossProfitMargin())
                        .build();

                discountFlag = Objects.nonNull(discountsCache.getOrderDiscount()) && discountCalculator.isDiscountApproval(discountFactReq);
            }
            if (discountFlag || first.isPresent()) {
                ContractOrderVO.AppurtenanceInfo contractInfo = new ContractOrderVO.AppurtenanceInfo();
                contractInfo.setContractType(ContractTypeEnum.ESIGN_TYPE.code());
                contractInfo.setTemplateType(TemplateTypeEnum.ACCESSORY_TYPE.code());
                contractInfo.setTemplateContractType(TemplateContractTypeV6Enum.DISCOUNT.code());
                contractInfo.setContractName(TemplateContractTypeV6Enum.DISCOUNT.title());
                contractInfo.setContractDesc(TemplateContractTypeV6Enum.DISCOUNT.desc());
                contractInfo.setOrderId(orderId);
                contractInfo.setContractId(first.isPresent() ? first.get().getId() : -1L);
                contractInfo.setSign(first.isPresent());
                contractInfo.setApiRoute(1);
                HeOrderUserSnapshotEntity userEsign = orderUserSnapshotRepository.queryByOrderId(Math.toIntExact(orderId));
                contractInfo.setCertType(userEsign.getCertType());
                appurtenanceInfoList.add(contractInfo);
            }
        }

        paperList = paperList.stream().filter(i -> !TemplateContractTypeV5Enum.allList().contains(i.getContractType())).collect(Collectors.toList());
        for (ContractSignRecordPaperEntity contractSignRecordPaperEntity : paperList) {
            List<ContractOrderVO.AppurtenanceInfo> needFilterContractAppurtenanceInfoOrderList = appurtenanceInfoList.stream().filter(o -> Objects.equals(o.getTemplateContractType(), contractSignRecordPaperEntity.getContractType()) && !o.getSign()).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(needFilterContractAppurtenanceInfoOrderList)) {
                appurtenanceInfoList.removeAll(needFilterContractAppurtenanceInfoOrderList);
            }
            ContractOrderVO.AppurtenanceInfo contractInfo = new ContractOrderVO.AppurtenanceInfo();
            contractInfo.setContractType(ContractTypeEnum.PAPER_TYPE.code());
            //todo 补充协议没有纸质合同
            contractInfo.setTemplateType(TemplateTypeEnum.ACCESSORY_TYPE.code());
            contractInfo.setTemplateContractType(contractSignRecordPaperEntity.getContractType());
            contractInfo.setContractName(contractSignRecordPaperEntity.getContractName());
            contractInfo.setOrderId(orderId);
            contractInfo.setContractId(contractSignRecordPaperEntity.getId().longValue());
            contractInfo.setContractDesc(Optional.ofNullable(TemplateContractTypeV6Enum.from(contractSignRecordPaperEntity.getContractType())).map(TemplateContractTypeV6Enum::desc).orElse(null));
            contractInfo.setSign(Objects.equals(ContractStatusEnum.SIGNED.code(), contractSignRecordPaperEntity.getContractStatus()));
            contractInfo.setApiRoute(2);
            appurtenanceInfoList.add(contractInfo);
        }

        //单独一栏 其他合同（纸质合同）
        ContractOrderVO.AppurtenanceInfo contractInfo = new ContractOrderVO.AppurtenanceInfo();
        contractInfo.setContractType(ContractTypeEnum.PAPER_TYPE.code());
        contractInfo.setTemplateContractType(TemplateContractTypeV6Enum.OTHER.code());
        contractInfo.setContractName(TemplateContractTypeV6Enum.OTHER.title());
        contractInfo.setOrderId(orderId);
        contractInfo.setContractId(-1L);
        contractInfo.setContractDesc(TemplateContractTypeV6Enum.OTHER.desc());
        contractInfo.setSign(false);
        contractInfo.setApiRoute(2);
        appurtenanceInfoList.add(contractInfo);
        result.setAppurtenanceInfoList(appurtenanceInfoList);

        //只筛选指定合同模板项
        if (Objects.nonNull(templateContractType)) {
            result.setOrderInfoList(result.getOrderInfoList().stream().filter(i -> Objects.equals(i.getTemplateContractType(), templateContractType)).collect(Collectors.toList()));
            result.setAppurtenanceInfoList(result.getAppurtenanceInfoList().stream().filter(i -> Objects.equals(i.getTemplateContractType(), templateContractType)).collect(Collectors.toList()));
        }

        return Result.success(result);
    }

    /**
     * 根据条件返回合同列表(新老合同判断)
     *
     * @param orderId
     * @param templateContractType
     * @return
     */
    @Override
    public Result<ContractOrderVO> oldOrNewContractType(Long orderId, Integer templateContractType) {
        //首先判断已签署的合同是新老合同,后续都是用此类型合同
        MonthContractSignRecordEntity signRecordEntity = monthContractSignRecordRepository.getByOrderIdAndContractStatus(Math.toIntExact(orderId), TemplateContractTypeV5Enum.YZ_SAINTBELLA.code(), ContractStatusEnum.SIGNED.code());
        HeOrderEntity byOrderId = orderRepository.getByOrderId(Math.toIntExact(orderId));
        CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(byOrderId.getStoreId());
        Integer contractType = cfgStoreEntity.getContractType();
        MonthContractSignRecordEntity noSignMainRecordEntity = monthContractSignRecordRepository.getByOrderIdAndContractStatus(orderId.intValue(), TemplateContractTypeV5Enum.YZ_SAINTBELLA.code(), ContractStatusEnum.NOT_SIGNED.code());
        MonthContractSignRecordEntity noSignReleaseRecordEntity = monthContractSignRecordRepository.getByOrderIdAndContractStatus(orderId.intValue(), TemplateContractTypeV5Enum.RELEASE.code(), ContractStatusEnum.NOT_SIGNED.code());
        //是否新合同模板
        boolean isNewContractType = false;
        if (ObjectUtil.isNotEmpty(signRecordEntity)) {
            isNewContractType = signRecordEntity.getContractType() == 1;
        } else {
            isNewContractType = contractType == 1;
            //查询未签署的主合同与合同解除协,根据合同模板是否一直判断是否要删除(只有主合同为未签署,且合同模板不一致才执行)
            if (ObjectUtil.isNotEmpty(noSignMainRecordEntity) && ObjectUtil.notEqual(noSignMainRecordEntity.getContractType(), contractType)) {
                monthContractSignRecordRepository.delContractByOrderIdAndTypeAndStatus(Math.toIntExact(orderId), TemplateContractTypeV5Enum.YZ_SAINTBELLA.code(), ContractStatusEnum.NOT_SIGNED.code());
            }
            if (ObjectUtil.isNotEmpty(noSignReleaseRecordEntity) && ObjectUtil.notEqual(noSignReleaseRecordEntity.getContractType(), contractType)) {
                monthContractSignRecordRepository.delContractByOrderIdAndTypeAndStatus(Math.toIntExact(orderId), TemplateContractTypeV5Enum.RELEASE.code(), ContractStatusEnum.NOT_SIGNED.code());
            }
        }
        //新合同
        log.info("根据条件返回合同列表(新老合同判断),orderId={},templateContractType={},isNewContractType={}", orderId, templateContractType, isNewContractType);
        if (isNewContractType) {
            return orderList(orderId, templateContractType);
        } else {
            //旧合同
            return getOldOrderContract(orderId, templateContractType);
        }
    }

    public Result<ContractOrderVO> getOldOrderContract(Long orderId, Integer templateContractType) {
        // 订单信息
        HeOrderEntity heOrder = orderRepository.getByOrderId(orderId.intValue());
        List<MonthContractSignRecordEntity> signRecordEntityList = monthContractSignRecordRepository.getListByOrderId(orderId, ContractStatusEnum.SIGNED.code());
        ContractOrderVO result = new ContractOrderVO();
        // 附属合同
        List<ContractOrderVO.AppurtenanceInfo> appurtenanceInfoList = new ArrayList<>();
        //展示所有的纸质合同
        List<ContractSignRecordPaperEntity> paperList = contractSignRecordPaperRepository.getByOrderId(orderId.intValue());
        // 订单类合同
        List<ContractOrderVO.ContractInfo> orderInfoList = new ArrayList<>();
        AheadOutRoomEntity aheadOutRoomEntity = aheadOutRoomRepository.queryByOrderId(new AheadOutRoomQuery().setOrderId(orderId.intValue()));
        for (TemplateContractTypeV5Enum value : TemplateContractTypeV5Enum.values()) {
            if (Objects.isNull(aheadOutRoomEntity) && Objects.equals(value.code(), TemplateContractTypeV5Enum.RELEASE.code())) {
                continue;
            }
            //老合同额外逻辑-不返回预约协议
            if (Objects.equals(value.code(), TemplateContractTypeV5Enum.APPOINTMENT.code())) {
                continue;
            }
            ContractOrderVO.ContractInfo contractInfo = new ContractOrderVO.ContractInfo();
            HeOrderUserSnapshotEntity userEsign = orderUserSnapshotRepository.queryByOrderId(Math.toIntExact(orderId));
            boolean isPaper = false;
            if (ObjectUtil.isNotEmpty(paperList)) {
                //纸质合同不为空,并且是主合同的,需要设置为纸质合同
                List<ContractSignRecordPaperEntity> signRecordPaperEntityList = paperList.stream().filter(x -> ObjectUtil.equals(x.getContractType(), value.code())).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(signRecordPaperEntityList)) {
                    contractInfo.setContractType(ContractTypeEnum.PAPER_TYPE.code());
                    contractInfo.setContractId(signRecordPaperEntityList.get(0).getId());
                    contractInfo.setSign(ObjectUtil.equals(signRecordPaperEntityList.get(0).getContractStatus(), ContractStatusEnum.SIGNED.code()));
                    isPaper = true;
                }
            }
            contractInfo.setTemplateType(TemplateTypeEnum.MAIN_TYPE.code());
            contractInfo.setTemplateContractType(value.code());
            contractInfo.setContractName(value.title());
            contractInfo.setContractDesc(value.desc());
            contractInfo.setOrderId(orderId);
            contractInfo.setCertType(userEsign.getCertType());
            contractInfo.setAmountEarnest(BigDecimalUtil.divide(new BigDecimal(heOrder.getPayAmount() / 2), new BigDecimal(100)).toString());
            if (!isPaper) {
                Optional<MonthContractSignRecordEntity> first = signRecordEntityList.stream().filter(item -> Objects.equals(value.code(), item.getTemplateContractType()) && Objects.equals(ContractStatusEnum.SIGNED.code(), item.getContractStatus())).findFirst();
                contractInfo.setSign(first.isPresent());
                contractInfo.setContractType(ContractTypeEnum.OLD_TYPE.code());
                contractInfo.setContractId(first.isPresent() ? first.get().getId().intValue() : null);
            }
            if (Objects.equals(value.code(), TemplateContractTypeV5Enum.RELEASE.code())) {
                contractInfo.setApiRoute(1);
            } else {
                contractInfo.setApiRoute(0);
            }
            orderInfoList.add(contractInfo);
        }

        result.setOrderInfoList(orderInfoList.stream().sorted(Comparator.comparing(ContractOrderVO.ContractInfo::getTemplateContractType).reversed()).collect(Collectors.toList()));

        //纸质合同的 母婴合同&预约协议合同&合同解除协议
        paperList.stream().filter(i -> TemplateContractTypeV5Enum.allList().contains(i.getContractType())).forEach(
                item -> {
                    //针对已上传纸质合同（含审批中情况）隐藏线上签约没有签订归档的入口
                    List<ContractOrderVO.ContractInfo> needFilterContractOrderList = orderInfoList.stream().filter(o -> Objects.equals(o.getTemplateContractType(), item.getContractType()) && !o.getSign()).collect(Collectors.toList());

                    if (CollectionUtil.isNotEmpty(needFilterContractOrderList)) {
                        orderInfoList.removeAll(needFilterContractOrderList);
                    }

                    ContractOrderVO.ContractInfo contractInfo = new ContractOrderVO.ContractInfo();
                    contractInfo.setContractId(item.getId());
                    contractInfo.setContractType(ContractTypeEnum.PAPER_TYPE.code());
                    contractInfo.setTemplateType(TemplateTypeEnum.MAIN_TYPE.code());
                    contractInfo.setTemplateContractType(item.getContractType());
                    contractInfo.setContractName(item.getContractName());
                    contractInfo.setContractDesc(Objects.nonNull(TemplateContractTypeV5Enum.from(item.getContractType())) ? TemplateContractTypeV5Enum.from(item.getContractType()).desc() : "");
                    contractInfo.setOrderId(orderId);
                    contractInfo.setSign(Objects.equals(ContractStatusEnum.SIGNED.code(), item.getContractStatus()));
                    contractInfo.setApiRoute(2);
                    orderInfoList.add(contractInfo);
                }
        );

        paperList = paperList.stream().filter(i -> !TemplateContractTypeV5Enum.allList().contains(i.getContractType())).collect(Collectors.toList());
        for (ContractSignRecordPaperEntity contractSignRecordPaperEntity : paperList) {
            List<ContractOrderVO.AppurtenanceInfo> needFilterContractAppurtenanceInfoOrderList = appurtenanceInfoList.stream().filter(o -> Objects.equals(o.getTemplateContractType(), contractSignRecordPaperEntity.getContractType()) && !o.getSign()).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(needFilterContractAppurtenanceInfoOrderList)) {
                appurtenanceInfoList.removeAll(needFilterContractAppurtenanceInfoOrderList);
            }
            ContractOrderVO.AppurtenanceInfo contractInfo = new ContractOrderVO.AppurtenanceInfo();
            contractInfo.setContractType(ContractTypeEnum.PAPER_TYPE.code());
            //todo 补充协议没有纸质合同
            contractInfo.setTemplateType(TemplateTypeEnum.ACCESSORY_TYPE.code());
            contractInfo.setTemplateContractType(contractSignRecordPaperEntity.getContractType());
            contractInfo.setContractName(contractSignRecordPaperEntity.getContractName());
            contractInfo.setOrderId(orderId);
            contractInfo.setContractId(contractSignRecordPaperEntity.getId().longValue());
            contractInfo.setContractDesc(Optional.ofNullable(TemplateContractTypeV6Enum.from(contractSignRecordPaperEntity.getContractType())).map(TemplateContractTypeV6Enum::desc).orElse(null));
            contractInfo.setSign(Objects.equals(ContractStatusEnum.SIGNED.code(), contractSignRecordPaperEntity.getContractStatus()));
            contractInfo.setApiRoute(2);
            appurtenanceInfoList.add(contractInfo);
        }

        //单独一栏 其他合同（纸质合同）
        ContractOrderVO.AppurtenanceInfo contractInfo = new ContractOrderVO.AppurtenanceInfo();
        contractInfo.setContractType(ContractTypeEnum.PAPER_TYPE.code());
        contractInfo.setTemplateContractType(TemplateContractTypeV6Enum.OTHER.code());
        contractInfo.setContractName(TemplateContractTypeV6Enum.OTHER.title());
        contractInfo.setOrderId(orderId);
        contractInfo.setContractId(-1L);
        contractInfo.setContractDesc(TemplateContractTypeV6Enum.OTHER.desc());
        contractInfo.setSign(false);
        contractInfo.setApiRoute(2);
        appurtenanceInfoList.add(contractInfo);
        result.setAppurtenanceInfoList(appurtenanceInfoList);
        //只筛选指定合同模板项
        if (Objects.nonNull(templateContractType)) {
            result.setOrderInfoList(result.getOrderInfoList().stream().filter(i -> Objects.equals(i.getTemplateContractType(), templateContractType)).collect(Collectors.toList()));
            result.setAppurtenanceInfoList(result.getAppurtenanceInfoList().stream().filter(i -> Objects.equals(i.getTemplateContractType(), templateContractType)).collect(Collectors.toList()));
        }
        return Result.success(result);
    }

    @Override
    public Result<Boolean> signMasterContract(Long orderId) {
        List<MonthContractSignRecordEntity> signList = monthContractSignRecordRepository.getListByOrderId(orderId);
        if (CollectionUtil.isEmpty(signList)) {
            return Result.success(false);
        }
        Optional<MonthContractSignRecordEntity> first = signList.stream()
                .filter(item -> Objects.equals(TemplateContractTypeV3Enum.YZ_SAINTBELLA.code(),
                        item.getTemplateContractType()) && Objects.equals(2, item.getContractStatus())).findFirst();
        return Result.success(first.isPresent());
    }

    /**
     * 判断是否签订了合同
     *
     * @param clientUids
     * @return
     */
    @Override
    public Result<Boolean> signMasterContract(List<Integer> clientUids) {
        List<MonthContractSignRecordEntity> signList = monthContractSignRecordRepository.getListByClientUids(clientUids);
        if (CollectionUtil.isEmpty(signList)) {
            return Result.success(false);
        }
        Optional<MonthContractSignRecordEntity> first = signList.stream()
                .filter(item -> Objects.equals(TemplateContractTypeV3Enum.YZ_SAINTBELLA.code(),
                        item.getTemplateContractType()) && Objects.equals(2, item.getContractStatus())).findFirst();
        return Result.success(first.isPresent());
    }

    @Override
    public Result<ContractRemindCountVO> remindDetailCount(Integer operatorGuid) {
        ContractRemindVO contractRemind = this.remindDetail(operatorGuid).getData();
        ContractRemindCountVO result = new ContractRemindCountVO();
        result.setOrderInfoSize(CollectionUtil.isEmpty(contractRemind.getOrderInfoList()) ? 0 : contractRemind.getOrderInfoList().size());
        result.setAppurtenanceInfoSize(CollectionUtil.isEmpty(contractRemind.getAppurtenanceInfoList()) ? 0 : contractRemind.getAppurtenanceInfoList().size());
        return Result.success(result);
    }

    @Override
    public Result<ContractRemindVO> remindDetail(Integer operatorGuid) {
        log.info("登录销售信息：{}", operatorGuid);
        ContractRemindVO result = new ContractRemindVO();
        // 查询所有销售下订单
        List<HeOrderEntity> heOrderList = orderRepository.getByStaffId(operatorGuid);
        //heOrderList 过滤月子标准订单&小月子订单提醒
        heOrderList = heOrderList.stream()
//                .filter(o -> Objects.equals(o.getOrderType(), OrderMonthTypeV2Enum.MONTH_ORDER.getOrderType()))
                .filter(o -> Arrays.asList(OmniOrderTypeEnum.MONTH_ORDER.getCode()).contains(o.getOrderType()))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(heOrderList)) {
            return Result.success(result);
        }
        List<Integer> orderIdList = heOrderList.stream().map(HeOrderEntity::getOrderId).collect(Collectors.toList());
        // 门店信息
        List<CfgStoreEntity> cfgStoreEntities = storeRepository.queryCfgStoreByIdList(heOrderList.stream().map(HeOrderEntity::getStoreId).collect(Collectors.toList()));
        // 用户信息
        List<TabClientEntity> clientList = clientRepository.getTabClientByIdList(heOrderList.stream().map(HeOrderEntity::getClientUid).collect(Collectors.toList()));
        // 套餐信息
        List<HeOrderGoodsEntity> goodList = orderGoodsRepository.getByOrderIdList(heOrderList.stream().map(HeOrderEntity::getOrderId).collect(Collectors.toList()));

        // 预约协议记录获取
        List<MonthContractSignRecordEntity> appointmentMonthContractSignList = monthContractSignRecordRepository.getByOrderIdList(orderIdList.stream().map(Long::valueOf).collect(Collectors.toList()), TemplateContractTypeEnum.APPOINTMENT.code(), null);
        // 合同签订记录
        List<MonthContractSignRecordEntity> signList = monthContractSignRecordRepository.getByOrderIdList(orderIdList.stream().map(Long::valueOf).collect(Collectors.toList()));
        // 补充协议
        List<MonthContractSignAgreementEntity> agreementList = monthContractSignAgreementRepository.getByOrderIdList(orderIdList.stream().map(Long::valueOf).collect(Collectors.toList()));

        Map<Integer, HeOrderEntity> orderMap = heOrderList.stream().collect(Collectors.toMap(HeOrderEntity::getOrderId, Function.identity()));
        Map<Integer, String> storeNameMap = cfgStoreEntities.stream().collect(Collectors.toMap(CfgStoreEntity::getStoreId, CfgStoreEntity::getStoreName));
        Map<Integer, String> clientNameMap = clientList.stream().collect(Collectors.toMap(TabClientEntity::getId, TabClientEntity::getName));
        Map<Integer, String> goodsNameMap = goodList.stream().collect(Collectors.toMap(HeOrderGoodsEntity::getOrderId, HeOrderGoodsEntity::getGoodsName, (k1, k2) -> k1));
        Map<Long, MonthContractSignRecordEntity> appointmentMonthContractMap = appointmentMonthContractSignList.stream().collect(Collectors.toMap(MonthContractSignRecordEntity::getGuideId, o -> o, (k1, k2) -> k1));

        // 订单类合同列表
        List<ContractRemindVO.ContractInfo> orderInfoList = new ArrayList<>();
        // 主合同map
        // 兼容历史主合同  老合同 templateContractType =1 or templateContractTyp=8
        Map<Long, MonthContractSignRecordEntity> masterContractMap = signList.stream()
                .filter(item -> Objects.equals(item.getTemplateContractType(), TemplateContractTypeEnum.YZ_SAINTBELLA.code()) || Objects.equals(item.getTemplateContractType(), TemplateContractTypeEnum.HOMECARE_MIDDLE_AUNT.code())).collect(Collectors.toList())
                .stream().collect(Collectors.toMap(MonthContractSignRecordEntity::getGuideId, Function.identity(), (k1, k2) -> k1));

        Date now = new Date();
        for (HeOrderEntity orderEntity : heOrderList) {
            //已经签订主合同无需提醒
            MonthContractSignRecordEntity master = masterContractMap.get(orderEntity.getOrderId().longValue());
            if (Objects.nonNull(master) && Objects.equals(master.getContractStatus(), ContractStatusEnum.SIGNED.code())) {
                continue;
            }
            MonthContractSignRecordEntity appointmentContract = appointmentMonthContractMap.get(orderEntity.getOrderId().longValue());

            //预约协议创建了 但未签署 需要创建一条预约协议待签署记录
            if (Objects.equals(orderEntity.getOldOrNew(), 1) && Objects.nonNull(appointmentContract) && !Objects.equals(appointmentContract.getContractStatus(), ContractStatusEnum.SIGNED.code())) {
                String contractName = appointmentContract.getContractName();
                Integer templateContractType = appointmentContract.getTemplateContractType();
                Long createdAt = appointmentContract.getCreatedAt();
                ContractRemindVO.ContractInfo contractInfo = new ContractRemindVO.ContractInfo();
                contractInfo.setStoreId(orderEntity.getStoreId());
                contractInfo.setStoreName(storeNameMap.getOrDefault(orderEntity.getStoreId(), ""));
                contractInfo.setClientName(clientNameMap.getOrDefault(orderEntity.getClientUid(), ""));
                contractInfo.setGoodsName(goodsNameMap.getOrDefault(orderEntity.getOrderId(), ""));
                contractInfo.setContractName(contractName);
                contractInfo.setTemplateContractType(templateContractType);
                contractInfo.setTemplateContractTypeName(TemplateContractTypeEnum.fromCode(templateContractType));
                contractInfo.setCreatedAt(new Date(createdAt * 1000));
                contractInfo.setClientUid(orderEntity.getClientUid());
                contractInfo.setOrderId(orderEntity.getOrderId().longValue());
                contractInfo.setAppointmentCreatedAt(new Date(appointmentContract.getCreatedAt() * 1000));
                contractInfo.setAppointmentStatus(ContractStatusEnum.NOT_SIGNED.code());
                contractInfo.setOldOrNew(orderEntity.getOldOrNew());
                //增加预约协议待签订记录
                orderInfoList.add(contractInfo);
            }
            //订单主合同记录未生成/或者有订单主合同记录产生未签署

            String tempContractName = OmniOrderTypeEnum.MONTH_ORDER.equals(orderEntity.getOrderType()) ? TemplateContractTypeEnum.YZ_SAINTBELLA.desc() : TemplateContractTypeEnum.YZ_SMALL.desc();

            String contractName = Objects.nonNull(master) ? master.getContractName() : tempContractName;
            Integer templateContractType = Objects.nonNull(master) ? master.getTemplateContractType() : TemplateContractTypeEnum.YZ_SAINTBELLA.code();
            Long createdAt = Objects.nonNull(master) ? master.getCreatedAt() : orderEntity.getCreatedAt();
            ContractRemindVO.ContractInfo contractInfo = new ContractRemindVO.ContractInfo();
            contractInfo.setStoreId(orderEntity.getStoreId());
            contractInfo.setStoreName(storeNameMap.getOrDefault(orderEntity.getStoreId(), ""));
            contractInfo.setClientName(clientNameMap.getOrDefault(orderEntity.getClientUid(), ""));
            contractInfo.setGoodsName(goodsNameMap.getOrDefault(orderEntity.getOrderId(), ""));
            contractInfo.setContractName(contractName);
            contractInfo.setTemplateContractType(templateContractType);
            contractInfo.setTemplateContractTypeName(TemplateContractTypeEnum.fromCode(templateContractType));
            contractInfo.setCreatedAt(new Date(createdAt * 1000));
            contractInfo.setClientUid(orderEntity.getClientUid());
            contractInfo.setOrderId(orderEntity.getOrderId().longValue());
            contractInfo.setOldOrNew(orderEntity.getOldOrNew());
            //签订预约协议todo 新订单才有
            if (Objects.equals(orderEntity.getOldOrNew(), 1) && Objects.nonNull(appointmentContract) && Objects.equals(appointmentContract.getContractStatus(), ContractStatusEnum.SIGNED.code())) {
                //todo 没有签订预约协议 缴纳意向金过去天数/优惠还剩天数
                // 预约协议签订时间
                Long signTime = appointmentContract.getUpdatedAt();
                if (Objects.nonNull(signTime) && signTime > 0) {
                    Date signDate = new Date(signTime * 1000);
                    // 失效时间 签订时间增加30天 固定
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(signDate);
                    calendar.add(Calendar.DAY_OF_MONTH, 30);
                    Date failDate = calendar.getTime();
                    // 支付意向金已过去几天
                    int payDay = (int) DateUtil.between(signDate, now, DateUnit.DAY);
                    // 优惠还有几天失效
                    int failDay = (int) DateUtil.between(failDate, now, DateUnit.DAY) + 1;
                    contractInfo.setIntentionDay(Math.max(payDay, 0));
                    contractInfo.setPreferentialDay(Math.max(failDay, 0));
                    contractInfo.setAppointmentCreatedAt(new Date(signTime * 1000));
                    contractInfo.setAppointmentStatus(appointmentContract.getContractStatus());
                }
            } else {
                contractInfo.setAppointmentCreatedAt(new Date(orderEntity.getCreatedAt() * 1000));
                contractInfo.setAppointmentStatus(ContractStatusEnum.NOT_SIGNED.code());
            }
            //增加主合同待签订记录
            orderInfoList.add(contractInfo);
        }
        //根据一项 预约协议状态+签订时间正序排列
        result.setOrderInfoList(orderInfoList.stream()
                .sorted(Comparator.comparing(ContractRemindVO.ContractInfo::getAppointmentStatus).reversed().thenComparing(ContractRemindVO.ContractInfo::getAppointmentCreatedAt))
                .collect(Collectors.toList()));
        // 附属类合同  包含补充协议 订单折扣保密协议  授权委托书
        List<ContractRemindVO.AppurtenanceInfo> appurtenanceInfoList = new ArrayList<>();
        List<ContractRemindVO.AppurtenanceInfo> agreementAddList = new ArrayList<>();
        List<ContractRemindVO.AppurtenanceInfo> discountAddList = new ArrayList<>();
        List<ContractRemindVO.AppurtenanceInfo> entrustAddList = new ArrayList<>();
        // 补充协议
        List<MonthContractSignAgreementEntity> agreementEntityList = agreementList.stream().filter(item -> Objects.equals(item.getState(), ContractStatusEnum.NOT_SIGNED.code())
                && orderIdList.contains(item.getOrderId().intValue())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(agreementEntityList)) {
            for (MonthContractSignAgreementEntity record : agreementEntityList) {
                ContractRemindVO.AppurtenanceInfo appurtenanceInfo = new ContractRemindVO.AppurtenanceInfo();
                appurtenanceInfo.setStoreId(record.getStoreId().intValue());
                appurtenanceInfo.setStoreName(storeNameMap.get(record.getStoreId().intValue()));
                appurtenanceInfo.setClientName(clientNameMap.get(orderMap.get(record.getOrderId().intValue()).getClientUid()));
                appurtenanceInfo.setGoodsName(goodsNameMap.get(record.getOrderId().intValue()));
                appurtenanceInfo.setContractName(record.getName());
                appurtenanceInfo.setTemplateContractType(TemplateContractTypeEnum.SUPPLIMENT.code());
                appurtenanceInfo.setTemplateContractTypeName(TemplateContractTypeEnum.SUPPLIMENT.desc());
                appurtenanceInfo.setCreatedAt(record.getGmtCreate());
                appurtenanceInfo.setClientUid(record.getClientUid());
                appurtenanceInfo.setOrderId(record.getOrderId());
                appurtenanceInfo.setOldOrNew(orderMap.get(record.getOrderId().intValue()).getOldOrNew());
                agreementAddList.add(appurtenanceInfo);
            }
        }
        agreementAddList = agreementAddList.stream().sorted(Comparator.comparing(ContractRemindVO.AppurtenanceInfo::getCreatedAt)).collect(Collectors.toList());
        appurtenanceInfoList.addAll(agreementAddList);

        //过滤新订单
        heOrderList = heOrderList.stream()
                .filter(o -> Objects.equals(o.getOldOrNew(), 1))
                .filter(o -> Objects.equals(o.getOrderType(), OmniOrderTypeEnum.MONTH_ORDER.getCode())).collect(Collectors.toList());
        //  订单折扣保密协议
        List<HeOrderEntity> approval = this.getApprovalV2(heOrderList);
        if (CollectionUtil.isNotEmpty(approval)) {
            Map<Long, MonthContractSignRecordEntity> discountMap = signList.stream().filter(
                            item -> Objects.equals(TemplateContractTypeEnum.DISCOUNT.code(), item.getTemplateContractType())).collect(Collectors.toList())
                    .stream().collect(Collectors.toMap(MonthContractSignRecordEntity::getGuideId, Function.identity(), (k1, k2) -> k1));
            for (HeOrderEntity heOrder : approval) {
                MonthContractSignRecordEntity record = discountMap.get(heOrder.getOrderId().longValue());
                if (Objects.nonNull(record) && record.getContractStatus().equals(ContractStatusEnum.SIGNED.code())) {
                    continue;
                }
                String contractName = Objects.nonNull(record) ? record.getContractName() : TemplateContractTypeEnum.DISCOUNT.desc();
                Integer templateContractType = Objects.nonNull(record) ? record.getTemplateContractType() : TemplateContractTypeEnum.DISCOUNT.code();
                Long createdAt = Objects.nonNull(record) ? record.getCreatedAt() : heOrder.getCreatedAt();
                ContractRemindVO.AppurtenanceInfo appurtenanceInfo = new ContractRemindVO.AppurtenanceInfo();
                appurtenanceInfo.setStoreId(heOrder.getStoreId());
                appurtenanceInfo.setStoreName(storeNameMap.getOrDefault(heOrder.getStoreId(), ""));
                appurtenanceInfo.setClientName(clientNameMap.getOrDefault(heOrder.getClientUid(), ""));
                appurtenanceInfo.setGoodsName(goodsNameMap.getOrDefault(heOrder.getOrderId(), ""));
                appurtenanceInfo.setContractName(contractName);
                appurtenanceInfo.setTemplateContractType(templateContractType);
                appurtenanceInfo.setTemplateContractTypeName(TemplateContractTypeEnum.fromCode(templateContractType));
                appurtenanceInfo.setCreatedAt(new Date(createdAt * 1000));
                appurtenanceInfo.setClientUid(heOrder.getClientUid());
                appurtenanceInfo.setOrderId(heOrder.getOrderId().longValue());
                appurtenanceInfo.setOldOrNew(heOrder.getOldOrNew());
                discountAddList.add(appurtenanceInfo);
            }
        }
        discountAddList = discountAddList.stream().sorted(Comparator.comparing(ContractRemindVO.AppurtenanceInfo::getCreatedAt)).collect(Collectors.toList());
        appurtenanceInfoList.addAll(discountAddList);

        // 授权委托书
        List<HeOrderEntity> entrustList = heOrderList.stream().filter(item -> Objects.equals(item.getSignType(), OrderSignTypeEnum.SIGN_TYPE_BAILOR.code())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(entrustList)) {
            Map<Long, MonthContractSignRecordEntity> entrustMap = signList.stream().filter(
                            item -> Objects.equals(TemplateContractTypeEnum.ENTRUST.code(), item.getTemplateContractType())).collect(Collectors.toList())
                    .stream().collect(Collectors.toMap(MonthContractSignRecordEntity::getGuideId, Function.identity(), (k1, k2) -> k1));
            for (HeOrderEntity heOrder : entrustList) {
                MonthContractSignRecordEntity record = entrustMap.get(heOrder.getOrderId().longValue());
                log.info("recordrecordrecord{}", JSONUtil.toJsonStr(record));
                if (Objects.nonNull(record) && record.getContractStatus().equals(ContractStatusEnum.SIGNED.code())) {
                    continue;
                }
                ContractRemindVO.AppurtenanceInfo appurtenanceInfo = new ContractRemindVO.AppurtenanceInfo();
                String contractName = Objects.nonNull(record) ? record.getContractName() : TemplateContractTypeEnum.ENTRUST.desc();
                Integer templateContractType = Objects.nonNull(record) ? record.getTemplateContractType() : TemplateContractTypeEnum.ENTRUST.code();
                Long createdAt = Objects.nonNull(record) ? record.getCreatedAt() : heOrder.getCreatedAt();
                appurtenanceInfo.setStoreId(heOrder.getStoreId());
                appurtenanceInfo.setStoreName(storeNameMap.getOrDefault(heOrder.getStoreId(), ""));
                appurtenanceInfo.setClientName(clientNameMap.getOrDefault(heOrder.getClientUid(), ""));
                appurtenanceInfo.setGoodsName(goodsNameMap.getOrDefault(heOrder.getOrderId(), ""));
                appurtenanceInfo.setContractName(contractName);
                appurtenanceInfo.setTemplateContractType(templateContractType);
                appurtenanceInfo.setTemplateContractTypeName(TemplateContractTypeEnum.fromCode(templateContractType));
                appurtenanceInfo.setCreatedAt(new Date(createdAt * 1000));
                appurtenanceInfo.setClientUid(heOrder.getClientUid());
                appurtenanceInfo.setOrderId(heOrder.getOrderId().longValue());
                appurtenanceInfo.setOldOrNew(heOrder.getOldOrNew());
                entrustAddList.add(appurtenanceInfo);
            }
        }

        entrustAddList = entrustAddList.stream().sorted(Comparator.comparing(ContractRemindVO.AppurtenanceInfo::getCreatedAt)).collect(Collectors.toList());
        appurtenanceInfoList.addAll(entrustAddList);
        result.setAppurtenanceInfoList(appurtenanceInfoList);

        return Result.success(result);
    }


    /**
     * 废弃
     *
     * 过滤出 存在订单折扣的数据
     *
     * @param orderEntities
     * @return
     */
    /*private List<HeOrderEntity> getApproval(List<HeOrderEntity> orderEntities, List<CfgStoreEntity> cfgStoreEntities) {

        Map<Integer, CfgStoreEntity> storeMap = cfgStoreEntities.stream().collect(Collectors.toMap(CfgStoreEntity::getStoreId, Function.identity()));

        List<HeOrderEntity> result = new ArrayList<>();

        for (HeOrderEntity orderEntity : orderEntities) {
            if (StringUtils.isBlank(orderEntity.getDiscountDetails())) {
                continue;
            }
            OrderDiscountsCacheVO discountsCache = JSONObject.toJavaObject(JSON.parseObject(orderEntity.getDiscountDetails()), OrderDiscountsCacheVO.class);
            BigDecimal orderDiscount = discountsCache.getOrderDiscount();

            if (Objects.isNull(orderDiscount) || orderDiscount.compareTo(BigDecimal.ZERO) == -1) {
                continue;
            }

            CfgStoreEntity cfgStoreEntity = storeMap.get(orderEntity.getStoreId());

            //判断门店品牌
            Integer storeType = cfgStoreEntity.getType();
            //获取门店类型
            OrderMonthStoreTypeCostEnum orderMonthStoreTypeCostEnum = null;

            if (storeType == 0) {
                orderMonthStoreTypeCostEnum = OrderMonthStoreTypeCostEnum.SAINT_BELLA;
            } else if (storeType == 1) {
                Integer childType = cfgStoreEntity.getChildType();
                if (ObjectUtil.isNotEmpty(childType) && childType == 0) {
                    orderMonthStoreTypeCostEnum = OrderMonthStoreTypeCostEnum.DELUXE;
                } else {
                    orderMonthStoreTypeCostEnum = OrderMonthStoreTypeCostEnum.BABY_BELLA;
                }
            }

            Boolean flag = false;
            if (OrderMonthStoreTypeCostEnum.SAINT_BELLA.equals(orderMonthStoreTypeCostEnum)) {
                //圣贝拉,折扣率低于百分之86%
                if (orderDiscount.compareTo(new BigDecimal(86)) >= 0) {
                    flag = true;
                }
            } else {
                //获取开店时间
                Date openDate = new Date(cfgStoreEntity.getOpenTime() * 1000);

                Calendar calendar = Calendar.getInstance();
                calendar.setTime(new Date());
                calendar.add(Calendar.MONTH, -6);
                //6个月前的今天
                Date time = calendar.getTime();

                //开店时间小于6个月，折扣70%
                if (time.compareTo(openDate) < 0) {
                    if (orderDiscount.compareTo(new BigDecimal(70)) >= 0) {
                        flag = true;
                    }
                } else {
                    //开店时间大于6个月，折扣80%
                    if (orderDiscount.compareTo(new BigDecimal(80)) >= 0) {
                        flag = true;
                    }
                }
            }
            //低于设置折扣
            if (!flag) {
                result.add(orderEntity);
            }
        }

        return result;
    }
*/

    /**
     * 过滤出 存在订单折扣的数据 V2
     *
     * @param orderEntities
     * @return
     */
    private List<HeOrderEntity> getApprovalV2(List<HeOrderEntity> orderEntities) {
        List<HeOrderEntity> result = new ArrayList<>();
        for (HeOrderEntity orderEntity : orderEntities) {
            //配置折扣对象
            DiscountFact discountFactReq = DiscountFact.builder()
                    .orderType(orderEntity.getOrderType())
                    .grossMargin(orderEntity.getGrossMargin())
                    .build();
            Boolean discountApproval = discountCalculator.isDiscountApproval(discountFactReq);
            //低于设置折扣
            if (discountApproval) {
                result.add(orderEntity);
            }
        }
        return result;
    }

    @Override
    public Result<ContractSignDetailVO> querySignDetail(MonthContractQuery monthContractQuery) {
        MonthContractSignRecordEntity signRecord = monthContractSignRecordRepository.getByOrderId(monthContractQuery.getOrderId(), monthContractQuery.getTemplateContractType());
        return Result.success(appMonthContractSignRecordConverter.entity2ContractSignDetailVo(signRecord));
    }

    @Override
    public Result<ContractSignDetailVO> querySignDetailWithContractStatus(MonthContractQuery monthContractQuery) {
        MonthContractSignRecordEntity signRecord = monthContractSignRecordRepository.getByOrderIdAndContractStatus(monthContractQuery.getOrderId(), monthContractQuery.getTemplateContractType(), monthContractQuery.getContractStatus());
        return Result.success(appMonthContractSignRecordConverter.entity2ContractSignDetailVo(signRecord));
    }

    /**
     * 已签订合同列表
     *
     * @param signContractQuery
     * @return {@link List}<{@link ContractSignVO}>
     */
    @Override
    public Result<List<ContractSignVO>> querySignList(MonthContractSignQuery signContractQuery) {
        log.info("合同列表状态查询{}", JSONUtil.toJsonStr(signContractQuery));
        if (Objects.isNull(signContractQuery.getOrderId()) && Objects.isNull(signContractQuery.getClientId())) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), MessageFormat.format("参数{0}或者{1}不能同时为空", "orderId", "clientId"));
        }
        List<ContractSignVO> contractSignVOList = getContractSignVOS(signContractQuery);

        //创建时间倒序排列
        contractSignVOList.sort(Comparator.comparing(ContractSignVO::getGmtCreate).reversed());
        return Result.success(contractSignVOList);
    }

    private List<ContractSignVO> getContractSignVOS(MonthContractSignQuery signContractQuery) {
        // 合同签订记录
        List<MonthContractSignRecordEntity> signList = monthContractSignRecordRepository.queryListByCondition(signContractQuery);
        // 补充协议记录
        List<MonthContractSignAgreementEntity> agreementList = monthContractSignAgreementRepository.queryListByCondition(signContractQuery);
        // 纸质合同记录
        List<ContractSignRecordPaperEntity> paperList = contractSignRecordPaperRepository.queryListByCondition(signContractQuery);
        // 组装后的集合
        List<ContractSignVO> contractSignVOList = Lists.newArrayList();

        contractSignVOList.addAll(appMonthContractSignRecordConverter.entity2ContractSignVoList(signList));
        contractSignVOList.addAll(appMonthContractSignRecordConverter.entitySignAgreement2ContractSignVoList(agreementList));
        contractSignVOList.addAll(appMonthContractSignRecordConverter.entitySignPaper2ContractSignVoList(paperList));

        return contractSignVOList;
    }

    @Override
    public Result<ContractSignStatusVO> querySignDetailV2(MonthContractQuery monthContractQuery) {
        MonthContractSignRecordEntity signRecord = monthContractSignRecordRepository.getByOrderId(monthContractQuery.getOrderId(), monthContractQuery.getTemplateContractType());
        List<ContractSignRecordPaperEntity> paperList = contractSignRecordPaperRepository.getByOrderId(monthContractQuery.getOrderId());
        List<ContractSignRecordPaperEntity> filterPaperList = paperList.stream()
                .filter(i -> Objects.equals(i.getContractType(), monthContractQuery.getTemplateContractType()))
                .filter(i -> Objects.equals(i.getContractStatus(), ContractStatusEnum.SIGNED.code()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(filterPaperList) || (ObjectUtil.isNotEmpty(signRecord) && signRecord.getContractStatus().equals(ContractStatusEnum.SIGNED.code()))) {
            ContractSignStatusVO contractSignStatusVO = new ContractSignStatusVO();
            contractSignStatusVO.setContractStatus(ContractStatusEnum.SIGNED.code());
            return Result.success(contractSignStatusVO);
        }

        ContractSignStatusVO contractSignStatusVO = new ContractSignStatusVO();
        contractSignStatusVO.setContractStatus(ContractStatusEnum.NOT_SIGNED.code());

        return Result.success(contractSignStatusVO);
    }

    /**
     * 已签订合同列表&&订单附件表
     *
     * @param signContractQuery
     * @return {@link List}<{@link ContractSignVO}>
     */
    @Override
    public Result<List<ContractAttachmentVO>> querySignAndAttachmentList(MonthContractSignQuery signContractQuery) {
        log.info("已签订合同列表&&订单附件查询{}", JSONUtil.toJsonStr(signContractQuery));
        if (Objects.isNull(signContractQuery.getOrderId())) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), MessageFormat.format("参数{0}不能为空", "orderId"));
        }
        //获取快照操作人姓名
        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(signContractQuery.getOrderId());
        List<ContractAttachmentVO> contractAttachmentVOList = Lists.newArrayList();

        List<ContractSignVO> contractSignVOList = getContractSignVOS(signContractQuery);
        List<ContractAttachmentVO> attachmentVOS = appMonthContractSignRecordConverter.voContractSignVO2ContractAttachmentVoList(contractSignVOList);
        contractAttachmentVOList.addAll(attachmentVOS);

        List<HeOrderAttachmentEntity> orderAttachmentEntityList = heOrderAttachmentRepository.queryListByCondition(signContractQuery);
        List<ContractAttachmentVO> contractAttachmentVOS = appMonthContractSignRecordConverter.entityOrderAttachment2ContractSignVoList(orderAttachmentEntityList);
        contractAttachmentVOList.addAll(contractAttachmentVOS);

        //创建时间倒序排列
        contractAttachmentVOList.sort(Comparator.comparing(ContractAttachmentVO::getGmtCreate).reversed());

        for (ContractAttachmentVO contractAttachmentVO : contractAttachmentVOList) {
            //纸质的单独处理 templateType
            if (Objects.equals(contractAttachmentVO.getContractType(), ContractTypeEnum.PAPER_TYPE.code()) && Objects.nonNull(TemplateContractTypeV5Enum.from(contractAttachmentVO.getTemplateContractType()))) {
                contractAttachmentVO.setTemplateType(TemplateTypeEnum.MAIN_TYPE.code());
            } else if (Objects.equals(contractAttachmentVO.getContractType(), ContractTypeEnum.PAPER_TYPE.code()) && Objects.nonNull(TemplateContractTypeV6Enum.from(contractAttachmentVO.getTemplateContractType()))) {
                contractAttachmentVO.setTemplateType(TemplateTypeEnum.ACCESSORY_TYPE.code());
            }
            contractAttachmentVO.setOperatorName(ObjectUtil.isNotEmpty(heOrderUserSnapshotEntity) ? heOrderUserSnapshotEntity.getSellerName() : "");
            contractAttachmentVO.setContractTypeName(ObjectUtil.isNotEmpty(contractAttachmentVO.getContractType()) ? ContractTypeEnum.fromCode(contractAttachmentVO.getContractType()) : "");
            contractAttachmentVO.setTemplateTypeName(ObjectUtil.isNotEmpty(contractAttachmentVO.getTemplateType()) ? TemplateTypeEnum.fromCode(contractAttachmentVO.getTemplateType()) : "");
        }

        return Result.success(contractAttachmentVOList);
    }
}

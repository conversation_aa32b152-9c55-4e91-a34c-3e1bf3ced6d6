package com.stbella.order.server.context.component.contract.stage;

import cn.hutool.core.date.DateTime;
import com.stbella.contract.common.utils.GeneratorUtil;
import com.stbella.contract.common.utils.RMBUtils;
import com.stbella.contract.model.res.ContractTemplateRes;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.utils.BigDecimalUtil;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.domain.repository.OrderGoodsRepository;
import com.stbella.order.server.contract.dto.UserSignDTO;
import com.stbella.order.server.contract.enums.ContractExceptionEnum;
import com.stbella.order.server.contract.req.MonthUserEsignDTO;
import com.stbella.order.server.order.month.enums.PayAmountEnum;
import com.stbella.platform.order.api.contract.req.ContractBaseReq;
import com.stbella.store.server.ecp.entity.CfgStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-23 15:33
 */
@Component
@Slf4j
public class AppointmentContractFillStrategy extends ContractFillStrategy {

    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @Resource
    private OrderGoodsRepository orderGoodsRepository;


    @Override
    protected Map<String, String> buildParam(ContractParamFillContext contractParamFillContext) {
        //获取信息,填充部分合同信息
        Map<String, String> templateData = new HashMap<>();
        String contractNo = GeneratorUtil.contractNo();

        //client_name 甲方
        //id_card 甲方身份证号
        //phone 联系电话
        //company_name 乙方
        //goods_name  套餐名称
        //store_name 门店名称
        //order_amount 套餐费用
        //order_amount_words 套餐费用大写
        //sign_time_str2   当前时间
        //pay_amount_1 意向金
        //pay_amount_1_words 意向金大写

        List<HeOrderGoodsEntity> orderGoodsEntityList = orderGoodsRepository.getAllItermByOrderId(contractParamFillContext.getOrderId());
        //商品名称拼接成字符串
        String goodsName = orderGoodsEntityList.stream().map(HeOrderGoodsEntity::getGoodsName).reduce((a, b) -> a + "," + b).orElse("");
        //意向金
        AtomicReference<BigDecimal> earnestMoney = new AtomicReference<>(BigDecimal.ZERO);
        List<HeIncomeRecordEntity> heIncomeRecordEntityList = incomeRecordRepository.getAllRecordListByOrderId(contractParamFillContext.getOrderId());
        heIncomeRecordEntityList.forEach(x -> {
            if (x.getReceiptType().equals(PayAmountEnum.EARNEST_MONEY.getCode())) {
                earnestMoney.updateAndGet(a -> a.add(new BigDecimal(x.getIncome())));
            }
        });

        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = contractParamFillContext.getHeOrderUserSnapshotEntity();
        HeOrderEntity orderEntity = contractParamFillContext.getOrderEntity();
        CfgStoreEntity cfgStore = contractParamFillContext.getCfgStore();
        ContractBaseReq req = contractParamFillContext.getRequest();
        MonthUserEsignDTO userSignDTO = contractParamFillContext.getUserSignDTO();
        checkMonthUserEsignDTO(userSignDTO);
             //签署人手机号(个人签署需要)
        templateData.put("signPhone", userSignDTO.getPhone());
        //机构拿到名称在下面
        //合同参数需要的开始
        templateData.put("client_name", heOrderUserSnapshotEntity.getName());
        templateData.put("id_card", heOrderUserSnapshotEntity.getIdCard());
        templateData.put("phone", heOrderUserSnapshotEntity.getPhone());
        templateData.put("goods_name", goodsName);
        templateData.put("store_name", cfgStore.getStoreName());
        templateData.put("order_amount", RMBUtils.formatToseparaDecimals(BigDecimalUtil.divide(new BigDecimal(orderEntity.getPayAmount()), new BigDecimal(100))));
        templateData.put("order_amount_words", RMBUtils.numToRMBStr(BigDecimalUtil.divide(new BigDecimal(orderEntity.getPayAmount()), new BigDecimal(100)).doubleValue()));
        templateData.put("contract_no", contractNo);
        templateData.put("pay_amount_1", RMBUtils.formatToseparaDecimals(req.getAmountEarnest()));
        templateData.put("pay_amount_1_words", RMBUtils.numToRMBStr(req.getAmountEarnest().doubleValue()));
        templateData.put("sign_time_str2", DateTime.now().toDateStr());


        return templateData;
    }

    @Override
    protected String getContractName(ContractParamFillContext contractParamFillContext) {
        return TemplateContractTypeEnum.APPOINTMENT.desc();
    }

    /**
     * @param templateContractType
     * @return
     */
    @Override
    public boolean filter(Integer templateContractType) {
        return TemplateContractTypeEnum.APPOINTMENT.code().equals(templateContractType);
    }
}
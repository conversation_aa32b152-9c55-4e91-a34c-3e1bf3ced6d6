package com.stbella.order.server.utils;

import cn.hutool.core.collection.CollectionUtil;
import com.stbella.order.common.enums.core.OrderRefundGoodsStatusEnum;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundGoodsEntity;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 退款商品数量处理工具类
 * 提供统一的退款商品数量去重和处理逻辑
 */
@Slf4j
public class RefundGoodsNumUtil {

    /**
     * 处理商品数量，扣减退款成功的数量
     * 针对相同的退款单号和相同的商品只取一个数量，避免重复扣减
     *
     * @param heOrderGoodsEntityList 订单商品列表
     * @param refundGoodsEntities 退款商品列表
     * @param goodsRefundStatus 需要过滤的退款状态列表
     */
    public static void dealGoodsNum(List<HeOrderGoodsEntity> heOrderGoodsEntityList,
                                   List<HeOrderRefundGoodsEntity> refundGoodsEntities,
                                   List<Integer> goodsRefundStatus) {
        if (CollectionUtil.isEmpty(heOrderGoodsEntityList) || CollectionUtil.isEmpty(refundGoodsEntities)) {
            return;
        }

        if (CollectionUtil.isEmpty(goodsRefundStatus)) {
            goodsRefundStatus = Collections.singletonList(OrderRefundGoodsStatusEnum.SUCCESS.code());
        }

        // 首先根据退款状态过滤退款商品列表
        List<HeOrderRefundGoodsEntity> filteredRefundGoods = filterByRefundStatus(refundGoodsEntities, goodsRefundStatus);

        if (CollectionUtil.isEmpty(filteredRefundGoods)) {
            log.info("根据退款状态过滤后，没有符合条件的退款商品");
            return;
        }

        // 按退款单号和商品ID组合去重，相同退款单号和相同商品只取一个数量
        Map<String, HeOrderRefundGoodsEntity> uniqueRefundMap = getUniqueRefundMap(filteredRefundGoods);

        // 使用去重后的退款商品列表进行处理
        for (HeOrderRefundGoodsEntity refundGoodsEntity : uniqueRefundMap.values()) {
            heOrderGoodsEntityList.stream()
                    .filter(item -> item.getId().equals(refundGoodsEntity.getOrderGoodsId()))
                    .findFirst()
                    .ifPresent(heOrderGoodsEntity -> {
                        int originalNum = heOrderGoodsEntity.getGoodsNum();
                        int refundNum = refundGoodsEntity.getRefundNum();
                        int newNum = originalNum - refundNum;
                        
                        heOrderGoodsEntity.setGoodsNum(newNum);
                        
                        log.debug("商品数量扣减：商品ID={}, 原数量={}, 退款数量={}, 新数量={}", 
                                heOrderGoodsEntity.getId(), originalNum, refundNum, newNum);
                    });
        }
        
        log.info("商品数量处理完成，处理商品数：{}，过滤后退款记录数：{}，去重后退款记录数：{}",
                heOrderGoodsEntityList.size(), filteredRefundGoods.size(), uniqueRefundMap.size());
    }

    /**
     * 处理商品数量，扣减退款成功的数量（不过滤状态的重载方法）
     * 针对相同的退款单号和相同的商品只取一个数量，避免重复扣减
     *
     * @param heOrderGoodsEntityList 订单商品列表
     * @param refundGoodsEntities 退款商品列表
     */
    public static void dealGoodsNum(List<HeOrderGoodsEntity> heOrderGoodsEntityList,
                                   List<HeOrderRefundGoodsEntity> refundGoodsEntities) {
        // 调用带状态过滤的方法，传入null表示不过滤状态
        dealGoodsNum(heOrderGoodsEntityList, refundGoodsEntities, null);
    }

    /**
     * 根据退款状态过滤退款商品列表
     *
     * @param refundGoodsEntities 退款商品列表
     * @param goodsRefundStatus 需要过滤的退款状态列表，如果为null或空则不过滤
     * @return 过滤后的退款商品列表
     */
    private static List<HeOrderRefundGoodsEntity> filterByRefundStatus(List<HeOrderRefundGoodsEntity> refundGoodsEntities,
                                                                      List<Integer> goodsRefundStatus) {
        if (CollectionUtil.isEmpty(goodsRefundStatus)) {
            // 如果状态列表为空，则不过滤，返回原列表
            return refundGoodsEntities;
        }

        List<HeOrderRefundGoodsEntity> filteredList = refundGoodsEntities.stream()
                .filter(refund -> goodsRefundStatus.contains(refund.getStatus()))
                .collect(Collectors.toList());

        log.info("根据退款状态过滤：原记录数={}，过滤状态={}，过滤后记录数={}",
                refundGoodsEntities.size(), goodsRefundStatus, filteredList.size());

        return filteredList;
    }

    /**
     * 获取去重后的退款商品映射
     * 按退款单号和商品ID组合去重
     *
     * @param refundGoodsEntities 退款商品列表
     * @return 去重后的退款商品映射
     */
    private static Map<String, HeOrderRefundGoodsEntity> getUniqueRefundMap(List<HeOrderRefundGoodsEntity> refundGoodsEntities) {
        Map<String, HeOrderRefundGoodsEntity> uniqueRefundMap = new HashMap<>();
        
        for (HeOrderRefundGoodsEntity refundGoodsEntity : refundGoodsEntities) {
            // 创建组合键：退款单号 + 商品ID
            String combinedKey = refundGoodsEntity.getRefundOrderSn() + "_" + refundGoodsEntity.getOrderGoodsId();
            
            // 如果组合键不存在，则添加；如果存在，则跳过（只保留第一个）
            if (!uniqueRefundMap.containsKey(combinedKey)) {
                uniqueRefundMap.put(combinedKey, refundGoodsEntity);
                log.debug("添加退款记录：退款单号={}, 商品ID={}, 退款数量={}", 
                        refundGoodsEntity.getRefundOrderSn(), refundGoodsEntity.getOrderGoodsId(), refundGoodsEntity.getRefundNum());
            } else {
                log.debug("跳过重复退款记录：退款单号={}, 商品ID={}", 
                        refundGoodsEntity.getRefundOrderSn(), refundGoodsEntity.getOrderGoodsId());
            }
        }
        
        return uniqueRefundMap;
    }

    /**
     * 处理商品数量（简化版本，直接传入FlowContext）
     * 从FlowContext中获取退款商品列表并处理
     * 
     * @param heOrderGoodsEntityList 订单商品列表
     * @param refundGoodsEntities 退款商品列表（从FlowContext获取）
     */
    public static void dealGoodsNumFromContext(List<HeOrderGoodsEntity> heOrderGoodsEntityList, 
                                              List<HeOrderRefundGoodsEntity> refundGoodsEntities) {
        dealGoodsNum(heOrderGoodsEntityList, refundGoodsEntities);
    }

    /**
     * 获取去重统计信息
     * 
     * @param refundGoodsEntities 退款商品列表
     * @return 去重统计信息
     */
    public static RefundDeduplicationStats getDeduplicationStats(List<HeOrderRefundGoodsEntity> refundGoodsEntities) {
        if (CollectionUtil.isEmpty(refundGoodsEntities)) {
            return new RefundDeduplicationStats(0, 0, 0);
        }
        
        Map<String, HeOrderRefundGoodsEntity> uniqueRefundMap = getUniqueRefundMap(refundGoodsEntities);
        
        int originalCount = refundGoodsEntities.size();
        int uniqueCount = uniqueRefundMap.size();
        int duplicateCount = originalCount - uniqueCount;
        
        return new RefundDeduplicationStats(originalCount, uniqueCount, duplicateCount);
    }

    /**
     * 去重统计信息
     */
    public static class RefundDeduplicationStats {
        private final int originalCount;    // 原始记录数
        private final int uniqueCount;      // 去重后记录数
        private final int duplicateCount;   // 重复记录数

        public RefundDeduplicationStats(int originalCount, int uniqueCount, int duplicateCount) {
            this.originalCount = originalCount;
            this.uniqueCount = uniqueCount;
            this.duplicateCount = duplicateCount;
        }

        public int getOriginalCount() { return originalCount; }
        public int getUniqueCount() { return uniqueCount; }
        public int getDuplicateCount() { return duplicateCount; }

        @Override
        public String toString() {
            return String.format("原始记录数: %d, 去重后记录数: %d, 重复记录数: %d", 
                    originalCount, uniqueCount, duplicateCount);
        }
    }
}

package com.stbella.order.server.context.component.processor.notice;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.server.order.month.enums.OrderEventEnum;
import com.stbella.order.server.producer.OrderEventProducer;
import com.stbella.order.server.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "业绩生效通知")
public class PerformanceEffectivenessOrderNoticeProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderEventProducer orderEventProducer;

    @Override
    public boolean condition(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        Integer percentFirstTime = orderEntity.getPercentFirstTime();
        return ObjectUtil.isNotEmpty(percentFirstTime) && percentFirstTime > 0;
    }

    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        log.info("业绩生效通知:{}", JsonUtil.write(orderEntity));
        orderEventProducer.sendMq(OrderEventEnum.PERCENT_FIRST_TIME.getCode(), orderEntity.getOrderSn(), null);
    }
}

package com.stbella.order.server.context.component.imports;

import cn.hutool.core.date.DateUtil;
import com.google.common.base.Strings;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.exception.ApplicationException;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.UserEntity;
import com.stbella.order.domain.order.production.OrderProductionExtendEntity;
import com.stbella.order.domain.repository.OrderProductionExtendRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.domain.repository.StoreRepository;
import com.stbella.order.domain.repository.UserRepository;
import com.stbella.order.server.order.month.enums.OrderTypeEnum;
import com.stbella.order.server.utils.IdGenUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: jijunjian
 * @CreateTime: 2023-08-24  16:12
 * @Description: 三方产康订单导入组装
 */
@Slf4j
@Component
public class ThirdPartyProductOrderImportAssembler {

    @Resource
    private OrderRepository repository;
    @Resource
    StoreRepository storeRepository;
    @Resource
    OrderProductionExtendRepository orderProductionExtendRepository;
    @Resource
    UserRepository userRepository;
    @Autowired
    IdGenUtils IdGenUtils;

    /**
     * 产康通卡初始化
     * @param cardDataList
     * @return
     */
    public Integer run(List<ThirdPartyProductionOrderImportDTO> orderList){

        //根据获取门店名获取门店id
        List<CfgStoreEntity> allStore = storeRepository.queryAllStore();
        Map<String, CfgStoreEntity> storeMap = allStore.stream().collect(Collectors.toMap(CfgStoreEntity::getStoreName, a -> a));

        List<HeOrderEntity> heOrderList = new ArrayList<>();

        //格式化数据，金额里有逗号的直接替换
        int index = 0;
        for (ThirdPartyProductionOrderImportDTO importDTO:orderList){
            index ++;
            importDTO.setPayAmount(importDTO.getPayAmount().replace(",",""));
            importDTO.setPaidAmount(importDTO.getPaidAmount().replace(",",""));
            if (storeMap.containsKey(importDTO.getStoreName())){
                importDTO.setStoreId(storeMap.get(importDTO.getStoreName()).getStoreId());
            }else {
                throw new ApplicationException(ErrorCodeEnum.BIZ_ERROR.code(), "第"+index+"行门店名称错误，请得重新输入");
            }
            //时间格式 2023/8/9 转化成时间戳
            Date orderTime = null;

            Date percentFirstTime = null;
            try {
                orderTime = DateUtils.parse(importDTO.getOrderTime(), DateUtils.YYYY_MM_DD_SLASH);
                percentFirstTime = DateUtils.parse(importDTO.getPercentFirstTime(), DateUtils.YYYY_MM_DD_SLASH);
            } catch (Exception e) {
                throw new ApplicationException(ErrorCodeEnum.BIZ_ERROR.code(), "第"+index+"行时间格式错误, 请输入YYYY/MM/DD格式");
            }

            int dayOfMonth = DateUtil.dayOfMonth(new Date());
            // 每月1号只导入当月或者上月的数据，2号以后只导入当月的数据。

            int addMonth = DateUtil.month(new Date());
            int performanceMonth = DateUtil.month(percentFirstTime);
            if (addMonth != performanceMonth){
                throw new ApplicationException(ErrorCodeEnum.BIZ_ERROR.code(), "第"+index+"行数据错误，只能导入当月业绩");
            }

            if (Strings.isNullOrEmpty(importDTO.getSaleName()) || Strings.isNullOrEmpty(importDTO.getSalePhone())){
                throw new ApplicationException(ErrorCodeEnum.BIZ_ERROR.code(), "第"+index+"行数据错误，请输入销售名称或者手机号");
            }
            int staffId = 0;
            // 根据手机号查询销售
            // 这里storeName 需要增加 销售名称。
            UserEntity userEntity = userRepository.queryByPhone(importDTO.getSalePhone());
            if (Objects.isNull(userEntity)){
                throw new ApplicationException(ErrorCodeEnum.BIZ_ERROR.code(), "第"+index+"行数据错误，销售手机号错误");
            }
            staffId = userEntity.getId();

            importDTO.setOrderTime((orderTime.getTime()/1000)+"");
            importDTO.setPercentFirstTime((percentFirstTime.getTime()/1000)+"");


            HeOrderEntity heOrder = BeanMapper.map(importDTO, HeOrderEntity.class);
            // 金额变成分
            Integer payAmount = Integer.valueOf(importDTO.getPayAmount()) * 100;
            Integer paidAmount = Integer.valueOf(importDTO.getPaidAmount()) * 100;
            heOrder.setOrderAmount(payAmount);
            heOrder.setPayAmount(payAmount);
            heOrder.setPaidAmount(paidAmount);
            heOrder.setStaffId(staffId);

            //备注，创建时间等
            heOrder.setIsDelete(0);
            heOrder.setOrderType(OmniOrderTypeEnum.PRODUCTION_ORDER.code());
            heOrder.setCreatedAt(System.currentTimeMillis()/1000);
            heOrder.setUpdatedAt(System.currentTimeMillis()/1000);

            heOrder.setTaskId(0L);
            heOrder.setBasicUid(0);
            heOrder.setClientUid(0);
            heOrder.setIsClose(0);
            heOrder.setPayStatus(2);
            heOrder.setPayFirstTime(heOrder.getPercentFirstTime());

            // remark
            String remark = importDTO.getClientName()+"-"+importDTO.getThirdPartyName()+"-"+ importDTO.getProductTypeName()+"-"+ importDTO.getProductName();
            heOrder.setRemark(remark);
            heOrder.setOrderTagName(importDTO.getProductTypeName()+"-"+ importDTO.getProductName());

            heOrder.setIsNotice(1);
            // 2表示三方导入
            heOrder.setProductionType(2);
            String orderSn = IdGenUtils.genOrderSn(OmniOrderTypeEnum.PRODUCTION_ORDER.getCode(), new Date());
            heOrder.setOrderSn(orderSn);

            heOrderList.add(heOrder);
        }

        heOrderList.forEach(a->{
            int orderId = repository.saveOrderMonth(a);
            OrderProductionExtendEntity extendEntity = new OrderProductionExtendEntity();
            extendEntity.setOrderId(orderId);
            extendEntity.setGmtCreate(new Date());
            extendEntity.setGmtModified(new Date());
            extendEntity.setBasicId(0);
            extendEntity.setGoodsName(a.getOrderTagName());
            extendEntity.setSkuId(0);
            extendEntity.setType(-1);
            extendEntity.setSkuName(a.getOrderTagName());
            extendEntity.setPrice(a.getPayAmount());
            extendEntity.setCost(0);
            extendEntity.setStoreId(a.getStoreId());
            extendEntity.setGoodsId(0);
            extendEntity.setGoodsNum(1);
            extendEntity.setContent("");
            extendEntity.setValidStartTime(0L);
            extendEntity.setValidEndTime(0L);
            extendEntity.setStatus(1);
            extendEntity.setValidityType(1);
            extendEntity.setValidityValue(12);
            //orderProductionExtendRepository.save(extendEntity);

            log.info("完成导入订单：{}",a.getRemark());
        });

        return heOrderList.size();
    }


}

package com.stbella.order.server.contract.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.stbella.core.result.Result;
import com.stbella.cts.server.entity.HomeContractTemplatePO;
import com.stbella.cts.server.query.HomeContractTemplateBaseQuery;
import com.stbella.cts.service.HomeContractTemplateService;
import com.stbella.mvc.base.BaseController;
import com.stbella.order.server.contract.entity.OrderContractConfigPO;
import com.stbella.order.server.contract.param.BabysittingServiceAgreementParam;
import com.stbella.order.server.contract.param.PersonAuthParam;
import com.stbella.order.server.contract.service.ContractService;
import com.stbella.order.server.contract.service.ESignService;
import com.stbella.order.server.contract.service.OrderContractConfigService;
import com.stbella.order.server.contract.vo.*;
import com.stbella.order.server.order.cts.enums.YesOrNoEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "合同")
@Validated
@RequestMapping("/order/contract")
@RestController
public class ContractController extends BaseController {

    private static final Integer NUMBER = 2;

    @Resource
    private ESignService eSignService;
    @Resource
    private ContractService contractService;
    @DubboReference
    private HomeContractTemplateService homeContractTemplateService;
    @Resource
    private OrderContractConfigService orderContractConfigService;

    @ApiOperation(value = "认证详情")
    @GetMapping("/auth/detail/{customerId}")
    public Result<AuthDetailVO> authDetail(@NotNull(message = "客户id不能为空") @PathVariable Long customerId) {
        return Result.success(contractService.authDetail(customerId));
    }

    @ApiOperation(value = "个人2要素（姓名、身份证号）信息比对")
    @PostMapping("/person/identity")
    public Result<PersonBaseVO> doPersonIdentityComparison(@Valid @RequestBody PersonAuthParam personAuthParam) {
        PersonBaseVO personBaseVO = contractService.compare(personAuthParam);
        return Result.success(personBaseVO);
    }

    @ApiOperation(value = "个人认证")
    @PostMapping("/auth")
    public Result<AuthDetailVO> auth(@Valid @RequestBody PersonAuthParam personAuthParam) {
        return Result.success(contractService.personAuth(personAuthParam));
    }


    @ApiOperation(value = "创建合同")
    @PostMapping("/apply")
    public Result<SignContractVO> sign(@RequestBody BabysittingServiceAgreementParam babysittingServiceAgreementParam) {
        return Result.success(contractService.apply(babysittingServiceAgreementParam));
    }

    @ApiOperation(value = "合同列表")
    @GetMapping("/contract/list/{orderNo}")
    public Result<List<ContractVO>> contractList(@PathVariable String orderNo) {
        return Result.success(contractService.contractListH5(orderNo));
    }

    @ApiOperation(value = "合同详情")
    @GetMapping("/contract/detail/{orderNo}")
    public Result<ContractDetailVO> contractDetail(@PathVariable String orderNo) {
        return Result.success(contractService.contractDetail(orderNo));
    }


    @ApiOperation(value = "合同模板同步")
    @GetMapping("/contract/async/{contractTemplateId}")
    public void contractAsync(@PathVariable Long contractTemplateId) {
        HomeContractTemplateBaseQuery homeContractTemplateBaseQuery = new HomeContractTemplateBaseQuery();
        homeContractTemplateBaseQuery.setId(contractTemplateId);
        List<HomeContractTemplatePO> homeContractTemplatePOS = homeContractTemplateService.queryHomeContractTemplateList(homeContractTemplateBaseQuery);
        if (homeContractTemplatePOS != null && !homeContractTemplatePOS.isEmpty()) {
            HomeContractTemplatePO homeContractTemplatePO = homeContractTemplatePOS.stream().findFirst().get();
            List<OrderContractConfigPO> list = orderContractConfigService.list(new LambdaQueryWrapper<OrderContractConfigPO>()
                    .eq(OrderContractConfigPO::getStatus, YesOrNoEnum.YES.getCode()));
            HashMap<Long, String> ret = new HashMap<>();
            for (OrderContractConfigPO orderContractConfigPO : list) {
                String templateId = eSignService.uploadFileByUrlAddress(homeContractTemplatePO.getTemplateAddress(), homeContractTemplatePO.getSbName() + ".pdf", orderContractConfigPO);
                ret.put(orderContractConfigPO.getCtsSiteId(), templateId);
            }
            homeContractTemplatePO.setESignTemplateId(JSONObject.toJSONString(ret));
            homeContractTemplateService.updateById(homeContractTemplatePO);
        }
    }
}

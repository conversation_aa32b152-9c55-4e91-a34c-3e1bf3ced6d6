package com.stbella.order.server.contract.month.component;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.stbella.core.exception.BusinessException;
import com.stbella.order.common.enums.month.*;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.domain.order.month.entity.MonthContractSignAgreementEntity;
import com.stbella.order.domain.order.month.entity.MonthContractSignRecordEntity;
import com.stbella.order.domain.repository.MonthContractSignAgreementRepository;
import com.stbella.order.domain.repository.MonthContractSignRecordRepository;
import com.stbella.order.server.config.ContractContextConfig;
import com.stbella.order.server.config.MonthESignPropertiesConfig;
import com.stbella.order.server.contract.dto.ContractSignStartRequestDTO;
import com.stbella.order.server.contract.dto.DownloadUrlResponseDTO;
import com.stbella.order.server.contract.dto.SignAddressResponseDTO;
import com.stbella.order.server.contract.enums.ContractExceptionEnum;
import com.stbella.order.server.contract.provider.month.ESignProvider;
import com.stbella.order.server.contract.req.MonthUserEsignDTO;
import com.stbella.order.server.contract.req.SignContractQuery;
import com.stbella.order.server.contract.res.ContractAddressVO;
import com.stbella.order.server.contract.res.ContractSignAddressVO;
import com.stbella.order.server.contract.service.month.MonthContractSignAgreementService;
import com.stbella.order.server.contract.service.month.MonthContractSignRecordService;
import com.stbella.order.server.contract.service.month.MonthEsignTemplateService;
import com.stbella.order.server.order.month.res.MonthContractSignAgreementVO;
import com.stbella.order.server.order.month.res.MonthContractSignRecordVO;
import com.stbella.order.server.order.month.res.MonthEsignTemplateVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-05 18:28
 */
@Component
@Slf4j
@RefreshScope
public class MonthContractAddressAssembler {
    @Resource
    private ESignProvider eSignProvider;
    @Resource
    private MonthContractSignRecordRepository monthContractSignRecordRepository;
    @Resource
    private MonthContractSignAgreementRepository monthContractSignAgreementRepository;
    @Resource
    private MonthContractSignRecordService contractSignRecordService;
    @Resource
    private MonthContractSignAgreementService monthContractSignAgreementService;
    @Resource
    private MonthESignPropertiesConfig eSignPropertiesConfig;
    @Resource
    private MonthContractAssembler monthContractAssembler;
    @Resource
    private MonthEsignTemplateService monthEsignTemplateService;
    @Value("${month-contract-h5.yuezi}")
    private String yuezi;
    @Value("${month-contract-h5.release}")
    private String release;

    /**
     * 主合同签署
     * @param signContractQuery
     * @return
     */
    public ContractSignAddressVO contractSignAddressAssembler(SignContractQuery signContractQuery) {
        ContractSignAddressVO contractSignAddress = new ContractSignAddressVO();

        //region 这是签订主合同/预约协议 签署的情况
        if (TemplateTypeEnum.MAIN_TYPE.code().equals(signContractQuery.getTemplateType())) {
            //判断合同模板是否是E签宝或老合同
            MonthContractSignRecordEntity signRecord = monthContractSignRecordRepository.getByContractSignId(Long.valueOf(signContractQuery.getContractId()));
            if (ObjectUtil.equals(signRecord.getContractType(), ContractTypeEnum.OLD_TYPE.code())) {
                if (ObjectUtil.equals(signRecord.getTemplateContractType(), TemplateContractTypeV5Enum.YZ_SAINTBELLA.code())) {
                    contractSignAddress.setContractSignAddress(yuezi);
                } else if (ObjectUtil.equals(signRecord.getTemplateContractType(), TemplateContractTypeV5Enum.RELEASE.code())) {
                    contractSignAddress.setContractSignAddress(release);
                }
                return contractSignAddress;
            }
            MonthUserEsignDTO userEsign = monthContractAssembler.userEsignAssembler(signRecord.getGuideId().intValue(), signRecord.getTemplateContractType());
            log.info("用户认证信息查询：userEsign{}", JSONUtil.toJsonStr(userEsign));
            //todo 这里签署合同 不用在填充合同数据 暂时屏蔽
//            MonthContractSignRecordVO signRecord = contractSignRecordService.createContract(signContractQuery.getOrderId());
            contractSignAddress = monthContractAssembler.signAddressAssembler(signContractQuery, signRecord);

            ContractContextConfig.setValue(true);

            // 通过sign record 的模版ID再获取模版信息
            MonthEsignTemplateVO esignTemplate = monthEsignTemplateService.getByTemplateId(signRecord.getTemplateId());
            // 获取主合同的签章位置
            if (StringUtils.isAnyBlank(esignTemplate.getCompanySealPos(), esignTemplate.getPersonSignaturePos()) || esignTemplate.getPosPage().equals(0)) {
                throw new BusinessException(ContractExceptionEnum.CONTRACT_TEMPLATE_CONFIGURATION_ERR.getCode(), ContractExceptionEnum.CONTRACT_TEMPLATE_CONFIGURATION_ERR.getValue());
            }
            String[] companySealPos = esignTemplate.getCompanySealPos().split(",");
            String[] personSignaturePos = esignTemplate.getPersonSignaturePos().split(",");
            if (ArrayUtil.isEmpty(companySealPos) || ArrayUtil.isEmpty(personSignaturePos)) {
                throw new BusinessException(ContractExceptionEnum.CONTRACT_TEMPLATE_CONFIGURATION_ERR.getCode(), ContractExceptionEnum.CONTRACT_TEMPLATE_CONFIGURATION_ERR.getValue());
            }

            ContractSignStartRequestDTO contractSignStartRequest = new ContractSignStartRequestDTO();
            // 启动流程基本信息
            ContractSignStartRequestDTO.InnerFlowInfo innerFlowInfo = new ContractSignStartRequestDTO.InnerFlowInfo();
            innerFlowInfo.setBusinessScene("月子标准订单附件合同签订流程发起");
            ContractSignStartRequestDTO.InnerFlowInfo.InnerFlowConfigInfo flowConfigInfo = new ContractSignStartRequestDTO.InnerFlowInfo.InnerFlowConfigInfo();

            // 在此业务场景下不需要发短信或邮件通知客户，所以此字段直接默认"",必须要填e签宝文档规定
            flowConfigInfo.setNoticeType("");
            flowConfigInfo.setBatchDropSeal(false);
            flowConfigInfo.setNoticeDeveloperUrl(eSignPropertiesConfig.getMainCallbackUrl());

            List<String> list = Lists.newArrayList();
            List<String> newList = Lists.newArrayList();
            //合同签订入口0=未知;1=h5;2=微信;3=支付宝
            Integer signFrom = 0;
            if (Objects.equals(ContractModeEnum.NOW_FACE_TYPE.code(), signContractQuery.getContractMode())) {
                signFrom = 2;
                // 个人刷脸认证
                list.add("PSN_FACEAUTH_BYURL");
                // 个人运营商三要素认证
                list.add("PSN_TELECOM_AUTHCODE");
                // 个人银行卡四要素认证
                list.add("PSN_BANK4_AUTHCODE");
                flowConfigInfo.setSignPlatform("1");// 1，2是h5微信和支付宝都兼容
                // 指定个人页面显示的认证方式
                flowConfigInfo.setPersonAvailableAuthTypes(list);
                // 指定意愿微信小程序刷脸
                newList.add("FACE_WE_CHAT_FACE");
                // 指定意愿短信验证
                newList.add("CODE_SMS");
                flowConfigInfo.setWillTypes(newList);
            } else if (ArrayUtil.contains(new int[]{ContractModeEnum.REMOTE_TYPE.code(), ContractModeEnum.ALIPAY_TYPE.code()}, signContractQuery.getContractMode())) {
                signFrom = 2;
                flowConfigInfo.setSignPlatform("1");// 1，2是h5微信和支付宝都兼容
                //入口支付宝 todo 有可能通过浏览器短信验证签署
                if (30 == signContractQuery.getContractMode()) {
                    signFrom = 3;
                    flowConfigInfo.setSignPlatform("2");// 1，2是h5微信和支付宝都兼容
                }
                // 个人刷脸认证
                list.add("PSN_FACEAUTH_BYURL");
                // 个人运营商三要素认证
                list.add("PSN_TELECOM_AUTHCODE");
                // 个人银行卡四要素认证
                list.add("PSN_BANK4_AUTHCODE");
                // 指定个人页面显示的认证方式
                flowConfigInfo.setPersonAvailableAuthTypes(list);
                // 指定意愿腾讯云刷脸
                newList.add("FACE_TECENT_CLOUD_H5");
                // 指定意愿支付宝刷脸
                newList.add("FACE_ZHIMA_XY");
                // 指定意愿短信验证
                newList.add("CODE_SMS");
                flowConfigInfo.setWillTypes(newList);
            }
            innerFlowInfo.setFlowConfigInfo(flowConfigInfo);
            contractSignStartRequest.setFlowInfo(innerFlowInfo);

            List<ContractSignStartRequestDTO.InnerSigners> signersList = new ArrayList<>();

            //丙方

            {
                ContractSignStartRequestDTO.InnerSigners signersC = new ContractSignStartRequestDTO.InnerSigners();
                ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount signerCAccount = new ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount();
                // 乙方企业账号在合同合规初版只使用总部账号，各门店账号暂不用，所以此处写死
                signerCAccount.setAuthorizedAccountId("4782244311e14f1daf0f5044fdfc31cd");
                signersC.setSignerAccount(signerCAccount);
                signersC.setPlatformSign(true);
                List<ContractSignStartRequestDTO.InnerSigners.InnerSignfields> innerSignfieldsList = new ArrayList<>();
                ContractSignStartRequestDTO.InnerSigners.InnerSignfields innerSignfields = new ContractSignStartRequestDTO.InnerSigners.InnerSignfields();
                innerSignfields.setAutoExecute(true);
                innerSignfields.setActorIndentityType(2);
                innerSignfields.setSealId("48659d63-77bc-410e-9282-6f6486c79c4f");

                innerSignfields.setFileId(signRecord.getEsignFileId());

                ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean innerCPosBean = new ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean();

                innerCPosBean.setPosPage(esignTemplate.getPosPage());
                innerCPosBean.setPosX(Integer.valueOf(companySealPos[0]));
                innerCPosBean.setPosY(Integer.valueOf(companySealPos[1]) + 150);

                innerSignfields.setPosBean(innerCPosBean);
                innerSignfieldsList.add(innerSignfields);
                signersC.setSignfields(innerSignfieldsList);
                if (signRecord.getStoreId().intValue() == 1){
                    signersList.add(signersC);
                }
            }

            {
                // 乙方企业签署方信息
                ContractSignStartRequestDTO.InnerSigners signers = new ContractSignStartRequestDTO.InnerSigners();
                ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount signerAccount = new ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount();
                // 乙方企业账号在合同合规初版只使用总部账号，各门店账号暂不用，所以此处写死
                signerAccount.setAuthorizedAccountId(eSignPropertiesConfig.getCompanyAccountId());
                signers.setSignerAccount(signerAccount);
                signers.setPlatformSign(true);
                List<ContractSignStartRequestDTO.InnerSigners.InnerSignfields> innerSignfieldsList = new ArrayList<>();
                ContractSignStartRequestDTO.InnerSigners.InnerSignfields innerSignfields = new ContractSignStartRequestDTO.InnerSigners.InnerSignfields();
                innerSignfields.setAutoExecute(true);
                innerSignfields.setActorIndentityType(2);

                innerSignfields.setFileId(signRecord.getEsignFileId());

                ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean innerPosBean = new ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean();

                // 0420 使用数据库中签章位置
                innerPosBean.setPosPage(esignTemplate.getPosPage());
                innerPosBean.setPosX(Integer.valueOf(companySealPos[0]));
                innerPosBean.setPosY(Integer.valueOf(companySealPos[1]));

                innerSignfields.setPosBean(innerPosBean);
                innerSignfieldsList.add(innerSignfields);
                signers.setSignfields(innerSignfieldsList);
                signersList.add(signers);
            }


            // 甲方个人签署信息
            ContractSignStartRequestDTO.InnerSigners personSigners = new ContractSignStartRequestDTO.InnerSigners();
            ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount personSignerAccount = new ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount();
            personSignerAccount.setSignerAccount(userEsign.getEsignUserId());
            personSigners.setSignerAccount(personSignerAccount);
            List<ContractSignStartRequestDTO.InnerSigners.InnerSignfields> personInnerSignfieldsList = new ArrayList<>();
            ContractSignStartRequestDTO.InnerSigners.InnerSignfields personInnerSignfields = new ContractSignStartRequestDTO.InnerSigners.InnerSignfields();
            personInnerSignfields.setAutoExecute(false);
            personInnerSignfields.setFileId(signRecord.getEsignFileId());
            personInnerSignfields.setSealType("0");
            personSigners.setPlatformSign(false);
            ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean personInnerPosBean = new ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean();

            // 0420 使用数据库中签名位置
            personInnerPosBean.setPosPage(esignTemplate.getPosPage());
            personInnerPosBean.setPosX(Integer.valueOf(personSignaturePos[0]));
            personInnerPosBean.setPosY(Integer.valueOf(personSignaturePos[1]));

            personInnerSignfields.setPosBean(personInnerPosBean);
            personInnerSignfieldsList.add(personInnerSignfields);
            personSigners.setSignfields(personInnerSignfieldsList);
            signersList.add(personSigners);
            contractSignStartRequest.setSigners(signersList);
            // 待签署文件信息
            List<ContractSignStartRequestDTO.InnerDocs> docsList = new ArrayList<>();
            ContractSignStartRequestDTO.InnerDocs innerDocs = new ContractSignStartRequestDTO.InnerDocs();
            innerDocs.setFileId(signRecord.getEsignFileId());
            innerDocs.setFileName(signRecord.getEsignFileName());
            docsList.add(innerDocs);
            contractSignStartRequest.setDocs(docsList);
            log.info("生成签署流程FlowId入参： {}", JSONUtil.toJsonStr(contractSignStartRequest));
            // 获取主合同签订启动流程id
            String signContractFlowId = eSignProvider.doInitiateSigningProcess(contractSignStartRequest);
            //todo 撤销旧的合同流程
            if (StringUtils.isNotBlank(signRecord.getEsignFlowId())) {
                eSignProvider.cancelSignFlow(signRecord.getEsignFlowId());
            }
            log.info("=========================================res:{}", "获取主合同签订启动流程id" + signContractFlowId);
            // 并保存至数据库，如果合同流程启动，却因其他原因未签署合同，想再次签署合同时直接使用这个流程id就可以获取签署链接
            // 不必再重新发起流程，重新再发起流程会重复扣合同费用
            signRecord.setEsignFileId(signRecord.getEsignFileId());
            signRecord.setEsignFlowId(signContractFlowId);
            signRecord.setSignFrom(signFrom);
            signRecord.setUpdatedAt(DateUtils.getTenBitTimestamp());
            monthContractSignRecordRepository.updateBySignRecordId(signRecord);

            SignAddressResponseDTO signAddress = eSignProvider.getSignAddress(userEsign.getEsignUserId(), signContractFlowId);
            signRecord.setContractLongUrl(signAddress.getLongUrl());
            signRecord.setContractShortUrl(signAddress.getShortUrl());
            signRecord.setUpdatedAt(DateUtils.getTenBitTimestamp());
            monthContractSignRecordRepository.updateBySignRecordId(signRecord);
            log.info("=========================================res:{}", "合同查看地址已更新至数据库");
            if (Objects.equals(ContractModeEnum.NOW_FACE_TYPE.code(), signContractQuery.getContractMode())) {
                contractSignAddress.setContractSignAddress(signAddress.getLongUrl());
                return contractSignAddress;
            }
            contractSignAddress.setContractSignAddress(signAddress.getShortUrl());
            return contractSignAddress;
        }
        //endregion
        //region 这是签订附件的情况
        if (TemplateTypeEnum.ACCESSORY_TYPE.code().equals(signContractQuery.getTemplateType())) {
            //判断合同模板是否是E签宝或老合同
            MonthContractSignRecordEntity signRecord = monthContractSignRecordRepository.getByContractSignId(Long.valueOf(signContractQuery.getContractId()));
            // 通过订单id获取主合同相关信息，然后获取e签宝文件id
            contractSignAddress = monthContractAssembler.signAddressAssembler(signContractQuery, signRecord);
            MonthUserEsignDTO userEsign = monthContractAssembler.userEsignAssembler(signRecord.getGuideId().intValue(), signRecord.getTemplateContractType());
            // 授权委托书签署
            if (TemplateContractTypeEnum.ENTRUST.code().equals(signRecord.getTemplateContractType())) {
                return monthContractAssembler.entrustAssembler(signContractQuery, signRecord, userEsign);
            }
            // 订单折扣保密协议签署
            if (TemplateContractTypeEnum.DISCOUNT.code().equals(signRecord.getTemplateContractType())) {
                return monthContractAssembler.discountAssembler(signContractQuery, signRecord, userEsign);
            }
            // 合同解除协议签署
            if (TemplateContractTypeEnum.RELEASE.code().equals(signRecord.getTemplateContractType())) {
                return monthContractAssembler.releaseAssembler(signContractQuery, signRecord, userEsign);
            }
        }
        //endregion
        //region 这是签附加协议合同的情况
        if (TemplateTypeEnum.AGREEMENT_TYPE.code().equals(signContractQuery.getTemplateType())) {
            MonthContractSignAgreementEntity signAgreement = monthContractSignAgreementRepository.getByContractAgreementId(Long.valueOf(signContractQuery.getContractId()));
            MonthUserEsignDTO userEsign = monthContractAssembler.userEsignAssembler(signAgreement.getOrderId().intValue(), signAgreement.getTemplateContractType());
            if (Objects.isNull(signAgreement)) {
                throw new BusinessException(ContractExceptionEnum.CONTRACT_AGREEMENT_INFO_NULL.getCode(), ContractExceptionEnum.CONTRACT_AGREEMENT_INFO_NULL.getValue());
            }

            if (ContractStatusEnum.SIGNED.code().equals(signAgreement.getState())) {
                throw new BusinessException(ContractExceptionEnum.CONTRACT_SIGEN_ERR.getCode(), ContractExceptionEnum.CONTRACT_SIGEN_ERR.getValue());
            }

            if (signAgreement.getState() == 0 && StringUtils.isNotBlank(signAgreement.getContractLongUrl()) && StringUtils.isNotBlank(signAgreement.getContractShortUrl())) {
                if (Objects.equals(ContractModeEnum.NOW_FACE_TYPE.code(), signContractQuery.getContractMode())) {
                    contractSignAddress.setContractSignAddress(signAgreement.getContractLongUrl());
                    return contractSignAddress;
                }
                contractSignAddress.setContractSignAddress(signAgreement.getContractShortUrl());
                return contractSignAddress;
            }

            ContractSignStartRequestDTO contractSignStartRequest = new ContractSignStartRequestDTO();
            // 启动流程基本信息
            ContractSignStartRequestDTO.InnerFlowInfo innerFlowInfo = new ContractSignStartRequestDTO.InnerFlowInfo();
            innerFlowInfo.setBusinessScene("月子标准订单补充合同签订协议流程发起");
            ContractSignStartRequestDTO.InnerFlowInfo.InnerFlowConfigInfo flowConfigInfo = new
                    ContractSignStartRequestDTO.InnerFlowInfo.InnerFlowConfigInfo();

            flowConfigInfo.setNoticeDeveloperUrl(eSignPropertiesConfig.getAddCallbackUrl());
            // 在此业务场景下不需要发短信或邮件通知客户，所以此字段直接默认"",必须要填e签宝文档规定
            flowConfigInfo.setNoticeType("");
            flowConfigInfo.setBatchDropSeal(false);
//            flowConfigInfo.setSignPlatform("1,2");
            List<String> list = Lists.newArrayList();
            List<String> newList = Lists.newArrayList();
            //合同签订入口0=未知;1=h5;2=微信;3=支付宝
            Integer signFrom = 0;
            // 此处需判断是当面签还是远程签，当面签默认刷脸需指定重定向地址，远程签h5无需指定
            if (Objects.equals(ContractModeEnum.NOW_FACE_TYPE.code(), signContractQuery.getContractMode())) {
                signFrom = 2;
                // 个人刷脸认证
                list.add("PSN_BANK4_AUTHCODE");
                // 个人银行卡四要素认证
                list.add("PSN_FACEAUTH_BYURL");
                // 个人运营商三要素认证
                list.add("PSN_TELECOM_AUTHCODE");
                flowConfigInfo.setPersonAvailableAuthTypes(list);
                flowConfigInfo.setSignPlatform("1");
                // 指定意愿微信小程序刷脸
                newList.add("FACE_WE_CHAT_FACE");
                // 指定意愿短信验证
                newList.add("CODE_SMS");
                flowConfigInfo.setWillTypes(newList);
            } else if (ArrayUtil.contains(new int[]{ContractModeEnum.REMOTE_TYPE.code(), ContractModeEnum.ALIPAY_TYPE.code()}, signContractQuery.getContractMode())) {
                signFrom = 2;
                flowConfigInfo.setSignPlatform("1");
                //入口支付宝 todo 有可能通过浏览器短信验证签署
                if (30 == signContractQuery.getContractMode()) {
                    signFrom = 3;
                    flowConfigInfo.setSignPlatform("2");
                }
                // 个人银行卡四要素认证
                list.add("PSN_FACEAUTH_BYURL");
                // 个人运营商三要素认证
                list.add("PSN_TELECOM_AUTHCODE");
                // 个人刷脸认证
                list.add("PSN_BANK4_AUTHCODE");
                // 指定个人页面显示的认证方式
                flowConfigInfo.setPersonAvailableAuthTypes(list);
                newList.add("FACE_TECENT_CLOUD_H5");
                // 指定意愿支付宝刷脸
                newList.add("FACE_ZHIMA_XY");
                // 指定意愿短信验证
                newList.add("CODE_SMS");
                flowConfigInfo.setWillTypes(newList);
            }
            innerFlowInfo.setFlowConfigInfo(flowConfigInfo);
            contractSignStartRequest.setFlowInfo(innerFlowInfo);
            // 乙方企业签署方信息
            List<ContractSignStartRequestDTO.InnerSigners> signersList = new ArrayList<>();
            ContractSignStartRequestDTO.InnerSigners signers = new ContractSignStartRequestDTO.InnerSigners();
            ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount signerAccount = new ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount();
            // 乙方企业账号在合同合规初版只使用总部账号，各门店账号暂不用，所以此处写死
            signerAccount.setAuthorizedAccountId(eSignPropertiesConfig.getCompanyAccountId());
            signers.setSignerAccount(signerAccount);
            signers.setPlatformSign(true);
            List<ContractSignStartRequestDTO.InnerSigners.InnerSignfields> innerSignfieldsList = new ArrayList<>();
            ContractSignStartRequestDTO.InnerSigners.InnerSignfields innerSignfields = new ContractSignStartRequestDTO.InnerSigners.InnerSignfields();
            innerSignfields.setAutoExecute(true);
            innerSignfields.setActorIndentityType(2);

            innerSignfields.setFileId(signAgreement.getEsignFileId());

            ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean innerPosBean = new ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean();
            String positionB = signAgreement.getPositionB();
            String[] split = positionB.split(",");
            if (split.length < 2) {
                throw new BusinessException(ContractExceptionEnum.COORDINATE_DATA_IS_ABNORMAL.getCode(), ContractExceptionEnum.COORDINATE_DATA_IS_ABNORMAL.getValue());
            }
            String newPosX = split[0];
            String newPosY = split[1];
            innerPosBean.setPosPage(signAgreement.getPageNum());
            innerPosBean.setPosX(Integer.valueOf(newPosX));
            innerPosBean.setPosY(Integer.valueOf(newPosY));
            innerSignfields.setPosBean(innerPosBean);
            innerSignfieldsList.add(innerSignfields);
            signers.setSignfields(innerSignfieldsList);
            signersList.add(signers);

            // 甲方个人签署信息
            ContractSignStartRequestDTO.InnerSigners personSigners = new ContractSignStartRequestDTO.InnerSigners();
            ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount personSignerAccount = new ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount();
            personSignerAccount.setSignerAccount(userEsign.getEsignUserId());
            personSigners.setSignerAccount(personSignerAccount);
            List<ContractSignStartRequestDTO.InnerSigners.InnerSignfields> personInnerSignfieldsList = new ArrayList<>();
            ContractSignStartRequestDTO.InnerSigners.InnerSignfields personInnerSignfields = new ContractSignStartRequestDTO.InnerSigners.InnerSignfields();
            personInnerSignfields.setAutoExecute(false);
            personInnerSignfields.setSealType("0");
            personInnerSignfields.setFileId(signAgreement.getEsignFileId());
            personSigners.setPlatformSign(false);
            ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean personInnerPosBean = new ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean();
            String positionA = signAgreement.getPositionA();
            String[] split1 = positionA.split(",");
            if (split1.length < 2) {
                throw new BusinessException(ContractExceptionEnum.COORDINATE_DATA_IS_ABNORMAL.getCode(), ContractExceptionEnum.COORDINATE_DATA_IS_ABNORMAL.getValue());
            }
            String posX = split1[0];
            String posY = split1[1];
            personInnerPosBean.setPosPage(signAgreement.getPageNum());
            personInnerPosBean.setPosX(Integer.valueOf(posX));
            personInnerPosBean.setPosY(Integer.valueOf(posY));
            personInnerSignfields.setPosBean(personInnerPosBean);
            personInnerSignfieldsList.add(personInnerSignfields);
            personSigners.setSignfields(personInnerSignfieldsList);
            signersList.add(personSigners);
            contractSignStartRequest.setSigners(signersList);
            // 待签署文件信息
            List<ContractSignStartRequestDTO.InnerDocs> docsList = new ArrayList<>();
            ContractSignStartRequestDTO.InnerDocs innerDocs = new ContractSignStartRequestDTO.InnerDocs();
            innerDocs.setFileId(signAgreement.getEsignFileId());
            innerDocs.setFileName(signAgreement.getEsignFileName());
            docsList.add(innerDocs);
            contractSignStartRequest.setDocs(docsList);
            //todo 补充协议不用撤销流程,在生产主合同产生补充协议 已撤销过 获取补充协议合同签订启动流程id
            String signContractFlowId = eSignProvider.doInitiateSigningProcess(contractSignStartRequest);
            log.info("=========================================res:{}", "获取补充协议合同签订启动流程id" + signContractFlowId);
            // 并保存至数据库，如果合同流程启动，却因其他原因未签署合同，想再次签署合同时直接使用这个流程id就可以获取签署链接
            // 不必再重新发起流程，重新再发起流程会重复扣合同费用
            signAgreement.setEsignFileId(signAgreement.getEsignFileId());
            signAgreement.setEsignFlowId(signContractFlowId);
            monthContractSignAgreementRepository.updateMonthContractSignAgreement(signAgreement);
            SignAddressResponseDTO signAddress = eSignProvider.getSignAddress(userEsign.getEsignUserId(), signContractFlowId);
            signAgreement.setContractLongUrl(signAddress.getLongUrl());
            signAgreement.setContractShortUrl(signAddress.getShortUrl());
            signAgreement.setSignFrom(signFrom);
            monthContractSignAgreementRepository.updateMonthContractSignAgreement(signAgreement);
            log.info("=========================================res:{}", "补充合同查看地址已更新至数据库");
            if (Objects.equals(ContractModeEnum.NOW_FACE_TYPE.code(), signContractQuery.getContractMode())) {
                contractSignAddress.setContractSignAddress(signAddress.getLongUrl());
                return contractSignAddress;
            }
            contractSignAddress.setContractSignAddress(signAddress.getShortUrl());
            return contractSignAddress;
        }
        //endregion

        return null;


    }

    public ContractAddressVO checkContractUrlAssembler(Long contractId, Integer templateType) {
        ContractAddressVO contractAddress = new ContractAddressVO();
        // 这里1表示主合同
        if (Arrays.asList(TemplateTypeEnum.MAIN_TYPE.code(), TemplateTypeEnum.ACCESSORY_TYPE.code()).contains(templateType)) {
            MonthContractSignRecordVO monthContractSignRecordVO = contractSignRecordService.getByContractSignId(contractId);
            if (Objects.isNull(monthContractSignRecordVO)) {
                throw new BusinessException(ContractExceptionEnum.CONTRACT_NOT_EXIT.getCode(), ContractExceptionEnum.CONTRACT_NOT_EXIT.getValue());
            }
            // 2表示合同已经签署成功可以正常打开链接查看，如果合同未签署成功打开链接是去签署
            if (Objects.equals(ContractStatusEnum.SIGNED.code(), monthContractSignRecordVO.getContractStatus())) {
                //如果是老合同需要特殊处理查看链接和下载链接
                if (monthContractSignRecordVO.getContractType().equals(2)) {
                    contractAddress.setCheckLongUrl(monthContractSignRecordVO.getViewpdfUrl());
                    contractAddress.setDownloadUrl(monthContractSignRecordVO.getContractLongUrl());
                } else {
                    contractAddress.setCheckLongUrl(monthContractSignRecordVO.getContractLongUrl());
                    contractAddress.setShortUrl(monthContractSignRecordVO.getContractShortUrl());
                    DownloadUrlResponseDTO downloadUrlResponse = eSignProvider.downloadContractFile(monthContractSignRecordVO.getEsignFlowId());
                    contractAddress.setDownloadUrl(downloadUrlResponse.getFileUrl());
                }
                contractAddress.setSignFrom(monthContractSignRecordVO.getSignFrom());
                return contractAddress;
            }
        }
        // 这里3表示补充合同
        if (Objects.equals(TemplateTypeEnum.AGREEMENT_TYPE.code(), templateType)) {
            MonthContractSignAgreementVO signAgreement = monthContractSignAgreementService.getByContractAgreementId(contractId);
            if (Objects.isNull(signAgreement)) {
                throw new BusinessException(ContractExceptionEnum.CONTRACT_NOT_EXIT.getCode());
            }
            // 2表示附属合同已成功签署，可以获取地址查看，如果未签署成功打开地址是去签署
            if (Objects.equals(ContractStatusEnum.SIGNED.code(), signAgreement.getState())) {
                contractAddress.setCheckLongUrl(signAgreement.getContractLongUrl());
                contractAddress.setShortUrl(signAgreement.getContractShortUrl());
                DownloadUrlResponseDTO downloadUrlResponse = eSignProvider.downloadContractFile(signAgreement.getEsignFlowId());
                contractAddress.setDownloadUrl(downloadUrlResponse.getFileUrl());
                contractAddress.setSignFrom(signAgreement.getSignFrom());
                return contractAddress;
            }
        }
        return null;
    }
}

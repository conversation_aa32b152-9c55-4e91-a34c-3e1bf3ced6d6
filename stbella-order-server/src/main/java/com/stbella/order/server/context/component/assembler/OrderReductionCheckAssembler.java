package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.stbella.contract.model.res.ContractSignRecordVO2;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.month.server.enums.OrderApproveStatusEnum;
import com.stbella.notice.entity.OaProcessIdRelationPO;
import com.stbella.notice.entity.OaProcessRecordPO;
import com.stbella.notice.server.OaProcessIdRelationService;
import com.stbella.notice.server.OaProcessRecordService;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.month.ContractStatusEnum;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.domain.order.service.OrderIncomeDomainService;
import com.stbella.order.infrastructure.gateway.ContractGateway;
import com.stbella.order.server.order.month.enums.ApprovalDiscountStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 减免前置条件组装
 */
@Slf4j
@Component
@SnowballComponent(name = "减免前置条件组装", desc = "减免前置条件组装")
public class OrderReductionCheckAssembler implements IExecutableAtom<FlowContext> {


    @DubboReference
    private OaProcessRecordService oaProcessRecordService;

    @DubboReference
    private OaProcessIdRelationService oaProcessIdRelationService;

    @Resource
    OrderIncomeDomainService orderIncomeDomainService;
    @Resource
    ContractGateway contractGateway;

    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity req = bizContext.getAttribute(HeOrderEntity.class);
        OrderReductionEntity orderReductionEntity = new OrderReductionEntity();
        OrderReductionEntity orderReduction = req.fetchLastOrderReduction();
        bizContext.setAttribute(OrderReductionEntity.class, orderReductionEntity);

        //判断查询合同签署状态
        if (req.isNeedSign()) {
            ContractSignRecordVO2 contract = contractGateway.queryOrderMainContract(req.getOrderId());
            if (contract == null || !CompareUtil.integerEqual(ContractStatusEnum.SIGNED.code(), contract.getContractStatus())) {
                throw new BusinessException(ErrorCodeEnum.CONTRACT_NOT_SIGN.code() + "", ErrorCodeEnum.CONTRACT_NOT_SIGN.desc());
            }
        }

        //判断是否存在减免审批
        if (Objects.nonNull(orderReduction) && orderReduction.isApproving()) {
            //根据减免id查询审批id
            orderReductionEntity.setDecreaseApprove(Boolean.TRUE);
            orderReductionEntity.setUnDealApprovalId(orderReduction.getLocalProcessId());
            return;
        }

        //判断是否存在审阅中的收款
        List<HeIncomeRecordEntity> effectiveRecord = orderIncomeDomainService.queryEffectiveRecord(req);
        boolean hasUnsuccessfulRecord = effectiveRecord.stream()
                .anyMatch(record -> !record.isActualSuccess());
        if (hasUnsuccessfulRecord) {
            throw new BusinessException(
                    ResultEnum.ERROR_AND_SHOW.getCode(),
                    ErrorCodeEnum.DECREASE_INCOME_APPROVING_ERROR.desc()
            );
        }

        // 退款记录
        HeOrderRefundEntity heOrderRefundEntity = req.fetchLastApprovingOrderRefund();
        if (Objects.nonNull(heOrderRefundEntity)) {
            orderReductionEntity.setOrderHasRefundApprove(Boolean.TRUE);
            OaProcessRecordPO oaProcessRecordPO = oaProcessRecordService.getByRefundId(heOrderRefundEntity.getId());
            if (ObjectUtil.isNotNull(oaProcessRecordPO)) {
                OaProcessIdRelationPO oneByProcessId = oaProcessIdRelationService.getOneByProcessId(oaProcessRecordPO.getProcessId());
                if (ObjectUtil.isNotNull(oneByProcessId)) {
                    orderReductionEntity.setUnDealApprovalId(oneByProcessId.getLocalProcessId());
                }
            }
            return;
        }
        //判断是否存在折扣审批
        if (CompareUtil.integerEqual(ApprovalDiscountStatusEnum.APPROVING.getCode(), req.getApprovalDiscountStatus())) {
            List<Long> idList = oaProcessIdRelationService.selectByOrderIdAndTypeAndStatus(req.getOrderId(), OrderApproveRecordTypeEnum.DISCOUNT_APPROVAL.getPlatformId(), OrderApproveStatusEnum.PROCESS.getCode());
            //删除
            if (CollectionUtil.isNotEmpty(idList)) {
                orderReductionEntity.setUnDealApprovalId(String.valueOf(idList.get(0)));
            }
            orderReductionEntity.setDisCountApprove(Boolean.TRUE);
        }

    }
}

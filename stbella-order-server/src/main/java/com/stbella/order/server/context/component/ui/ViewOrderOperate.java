package com.stbella.order.server.context.component.ui;

import com.stbella.order.common.enums.month.OrderButtonEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.server.order.month.res.OrderOperateButton;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

/**
 * @Author: ji<PERSON><PERSON>an
 * @CreateTime: 2024-08-12  16:28
 * @Description: 查看订单
 */
@Component
@Slf4j
public class ViewOrderOperate implements OrderOperate{
    @Override
    public void setPriority(int priority) {

    }

    @Override
    public int getPriority() {
        return 5;
    }

    /**
     * 生成订单操作按钮
     *
     * @param order
     * @return
     */
    @Override
    public OrderOperateButton generateButton(OrderOperateContext context) {
        return OrderOperateButton.builder().code(getButtonEnum().getCode()).value(getButtonEnum().getValue()).build();
    }

    /**
     * 获取按钮枚举
     *
     * @return
     */
    @Override
    public OrderButtonEnum getButtonEnum() {
        return OrderButtonEnum.ORDER_DETAIL;
    }

    @Override
    public int compareTo(@NotNull IPriority o) {
        return this.getPriority() - o.getPriority();
    }
}

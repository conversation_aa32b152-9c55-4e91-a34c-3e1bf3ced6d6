package com.stbella.order.server.order.cts.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.core.utils.JwtUtil;
import com.stbella.cts.server.entity.BaseSitterPO;
import com.stbella.cts.service.BaseSitterService;
import com.stbella.order.server.order.cts.entity.OrderCtsPO;
import com.stbella.order.server.order.cts.entity.OrderCtsSitterSnapshotPO;
import com.stbella.order.server.order.cts.mapper.OrderCtsSitterSnapshotMapper;
import com.stbella.order.server.order.cts.service.OrderCtsService;
import com.stbella.order.server.order.cts.service.OrderCtsSitterSnapshotService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 到家订单-育婴师快照 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2022-03-21
 */
@Service
@Slf4j
public class OrderCtsSitterSnapshotServiceImpl extends ServiceImpl<OrderCtsSitterSnapshotMapper, OrderCtsSitterSnapshotPO> implements OrderCtsSitterSnapshotService {

    @DubboReference
    private BaseSitterService baseSitterService;

    @Resource
    private OrderCtsService orderCtsService;

    /**
     * 根据订单编号获取绑定育婴师
     *
     * @param orderNos
     * @return java.util.List<com.stbella.order.server.order.cts.entity.OrderCtsSitterSnapshotPO>
     * @throws
     * <AUTHOR>
     * @date 2022/4/19 18:52
     * @since 1.0.0
     */
    @Override
    public List<OrderCtsSitterSnapshotPO> listByOrderNos(List<String> orderNos) {
        if (CollectionUtils.isEmpty(orderNos)) {
            return new ArrayList<>();
        }
        return this.list(new LambdaQueryWrapper<OrderCtsSitterSnapshotPO>().in(OrderCtsSitterSnapshotPO::getOrderNo, orderNos));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrderSnapshot(String orderNo, Long sitterId) {
        log.info("绑定订单育婴师快照入参：{}, 育婴师id{}", orderNo, sitterId);
        if (StringUtils.isBlank(orderNo) || sitterId == null) {
            return false;
        }
        //查询 order_cts_sitter_snapshot 信息
        OrderCtsSitterSnapshotPO orderCtsSitterSnapshotPO = this.baseMapper.selectOne(
                new LambdaQueryWrapper<OrderCtsSitterSnapshotPO>().eq(OrderCtsSitterSnapshotPO::getOrderNo, orderNo)
                        .eq(OrderCtsSitterSnapshotPO::getIsTermination, 0).eq(OrderCtsSitterSnapshotPO::getSitterId, sitterId));
        log.info("查询订单育婴师快照表 orderNo：{},返回信息;{}", orderNo, orderCtsSitterSnapshotPO);
        if (orderCtsSitterSnapshotPO != null) {
            throw new BusinessException("", "此育婴师已被该雇主绑定");
        }
        OrderCtsPO orderCtsPO = orderCtsService.getByOrderNo(orderNo);
        if (orderCtsPO == null) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "订单不存在!");
        }
        log.info("查询订单表 orderNo：{},返回信息;{}", orderNo, orderCtsPO);

        BigDecimal realityAmount = orderCtsPO.getRealityAmount();
        if (realityAmount.compareTo(BigDecimal.ZERO) < 1) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "订单金额小于等于0不可以匹配育婴师!");
        }
        //查询育婴师基本信息
        BaseSitterPO baseSitterPO = baseSitterService.getById(sitterId);
        log.info("查询育婴师信息 sitterId：{},返回信息;{}", sitterId, baseSitterPO);
        if (baseSitterPO == null) {
            throw new BusinessException("", "没有找到该育婴师");
        }
        OrderCtsSitterSnapshotPO snapshotPO = new OrderCtsSitterSnapshotPO();
        snapshotPO.setOrderNo(orderNo);
        snapshotPO.setOrderType(orderCtsPO.getOrderType());
        snapshotPO.setProductId(orderCtsPO.getProductId());
        snapshotPO.setComboId(orderCtsPO.getComboId());
        //绑定育婴师
        snapshotPO.setSitterId(sitterId);
        snapshotPO.setCtsSiteId(baseSitterPO.getCtsSiteId());
        snapshotPO.setSitterName(baseSitterPO.getName());
        snapshotPO.setMobile(baseSitterPO.getMobile());
        snapshotPO.setSitterLevel(baseSitterPO.getSitterLevel());
        snapshotPO.setSitterWage(baseSitterPO.getSitterWage());
        snapshotPO.setAuntLevel(baseSitterPO.getAuntLevel());
        snapshotPO.setAuntWage(baseSitterPO.getAuntWage());
        snapshotPO.setRealityServeStart(null);
        snapshotPO.setRealityServeEnd(null);
        snapshotPO.setNonHolidays(null);
        snapshotPO.setDoubleHolidays(null);
        snapshotPO.setTripleHolidays(null);
        snapshotPO.setBonus(null);
        snapshotPO.setSalary(null);
        snapshotPO.setMatchDate(new Date());
        snapshotPO.setTerminationServiceDate(null);
        snapshotPO.setIsTermination(0);
        snapshotPO.setContent(JSON.toJSONString(baseSitterPO));
        snapshotPO.setCreateBy(JwtUtil.getJwtTokenUserInfo().getUserId());
        snapshotPO.setCreateByName(JwtUtil.getJwtTokenUserInfo().getUserName());
        snapshotPO.setUpdateBy(JwtUtil.getJwtTokenUserInfo().getUserId());
        snapshotPO.setUpdateByName(JwtUtil.getJwtTokenUserInfo().getUserName());
        snapshotPO.setGmtCreate(new Date());
        snapshotPO.setGmtModified(new Date());
        snapshotPO.setDeleted(0);

        int rows = this.baseMapper.insert(snapshotPO);
        log.info("保存订单育婴师快照 返回的影响行数{}", rows);
        if (rows > 0) {
            return true;
        }
        return false;
    }
}

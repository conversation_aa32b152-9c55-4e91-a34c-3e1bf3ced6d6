package com.stbella.order.server.context.component.validator;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.core.OrderRefundGoodsStatusEnum;
import com.stbella.order.common.enums.core.OrderRefundNatureEnum;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.service.OrderRefundDomainService;
import com.stbella.order.server.order.cts.enums.OrderRefundResultEnum;
import com.stbella.order.server.order.month.enums.PayMethodEnum;
import com.stbella.order.server.order.month.enums.PayStatusEnum;
import com.stbella.order.server.order.month.enums.RefundTypeEnum;
import com.stbella.order.server.order.month.service.impl.OrderAssetTradeService;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import com.stbella.platform.order.api.refund.res.QueryRefundInfoRes;
import com.stbella.platform.order.api.res.CreateApproveRes;
import com.stbella.store.common.enums.core.GoodsTypeEnum;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
@RefreshScope
@SnowballComponent(name = "订单发起退款校验")
public class OrderRefundCreateValidator implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderRefundDomainService orderRefundDomainService;
    @Resource
    private OrderAssetTradeService assetTradeService;


    /**
     * 得出不同退款类型的金额
     * 优化：使用Stream API进行聚合，提高代码可读性和性能
     *
     * @param createRefundReq 退款请求
     * @return 按支付类型聚合后的退款金额信息列表
     */
    private static @NotNull List<CreateRefundReq.GoodsRefundAmountInfo> setAmountInfo(CreateRefundReq createRefundReq) {
        List<CreateRefundReq.GoodsInfo> goodsInfoList = createRefundReq.getGoodsInfoList();

        if (CollectionUtil.isEmpty(goodsInfoList)) {
            createRefundReq.setRefundAmountInfoList(Collections.emptyList());
            return Collections.emptyList();
        }

        // 使用Stream API将所有商品的退款金额信息按支付类型进行聚合
        List<CreateRefundReq.GoodsRefundAmountInfo> refundAmountInfoList = goodsInfoList.stream()
                .filter(goodsInfo -> CollectionUtil.isNotEmpty(goodsInfo.getRefundGoodsAmountInfoList()))
                .flatMap(goodsInfo -> goodsInfo.getRefundGoodsAmountInfoList().stream())
                .filter(Objects::nonNull)
                .filter(amountInfo -> Objects.nonNull(amountInfo.getAmountType()) && Objects.nonNull(amountInfo.getAmount()))
                .collect(Collectors.groupingBy(
                        CreateRefundReq.GoodsRefundAmountInfo::getAmountType,
                        Collectors.reducing(
                                null,
                                Function.identity(),
                                (existing, replacement) -> {
                                    if (existing == null) {
                                        return createRefundAmountInfo(replacement);
                                    }
                                    // 使用精确的 BigDecimal 加法，并设置正确的精度
                                    BigDecimal sum = existing.getAmount().add(replacement.getAmount())
                                            .setScale(2, RoundingMode.HALF_UP);
                                    existing.setAmount(sum);
                                    return existing;
                                }
                        )
                ))
                .values()
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 验证和修正总金额精度
        validateAndFixTotalAmount(createRefundReq, refundAmountInfoList);

        createRefundReq.setRefundAmountInfoList(refundAmountInfoList);
        return refundAmountInfoList;
    }

    /**
     * 创建退款金额信息对象
     *
     * @param source 源对象
     * @return 新的退款金额信息对象
     */
    private static CreateRefundReq.GoodsRefundAmountInfo createRefundAmountInfo(CreateRefundReq.GoodsRefundAmountInfo source) {
        CreateRefundReq.GoodsRefundAmountInfo refundAmountInfo = new CreateRefundReq.GoodsRefundAmountInfo();
        refundAmountInfo.setAmountType(source.getAmountType());
        refundAmountInfo.setModel(source.getModel());
        // 确保金额精度正确
        BigDecimal amount = source.getAmount() != null ?
                source.getAmount().setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
        refundAmountInfo.setAmount(amount);
        return refundAmountInfo;
    }

    private static void checkOrderRefundAmount(CreateRefundReq createRefundReq, List<HeIncomeRecordEntity> incomeRecordEntities, List<HeOrderRefundEntity> refundEntityList) {
        List<CreateRefundReq.GoodsRefundAmountInfo> refundAmountInfoList = setAmountInfo(createRefundReq);

        //判断每个退款类型的金额情况
        refundAmountInfoList.forEach(type -> {

            PayMethodEnum payMethodEnum = PayMethodEnum.getEnumByCode(type.getAmountType());

            //类型支付（取成功，分）
            Integer typeIncome = incomeRecordEntities.stream().filter(i ->
                    PayMethodEnum.PayType2Currency(i.getPayType()).getCode().equals(type.getAmountType().toString()) && i.getStatus()
                            .equals(PayStatusEnum.PAY_STATUS_SUCCESS.getCode())).mapToInt(HeIncomeRecordEntity::getIncome).sum();
            //类型退款（取成功+冻结分）
            Integer typeRefund = refundEntityList.stream().filter(i -> {
                //当前类型
                return PayMethodEnum.PayType2Currency(i.getRefundType()).getCode().equals(type.toString()) &&
                        //进行中+成功
                        HeOrderRefundEntity.REFUNDING_AND_REFUNDED_STATUS.contains(i.getStatus()) &&
                        //子退款
                        StringUtil.isNotBlank(i.getParentRefundOrderSn());
            }).mapToInt(HeOrderRefundEntity::getApplyAmount).sum();

            //当前类型退款金额
            BigDecimal applyTypeAmount = type.getAmount();

            BigDecimal orderCanRefund = RMBUtils.bigDecimalF2Y(typeIncome - typeRefund);
            log.info("订单退款校验，订单号：{}，退款类型：{}，申请金额：{}，可退：{}",
                    createRefundReq.getOrderId(),
                    payMethodEnum.getValue(),
                    applyTypeAmount,
                    orderCanRefund);

            assert orderCanRefund != null;
            if (orderCanRefund.compareTo(applyTypeAmount) < 0) {
                throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), payMethodEnum.getValue() + "可退金额小于申请退款金额、无法提交审批");
            }
        });
    }

    @Override
    public void run(FlowContext bizContext) {

        CreateApproveRes createApproveRes = bizContext.getAttribute(CreateApproveRes.class);
        if (ObjectUtil.isEmpty(createApproveRes)) {
            createApproveRes = new CreateApproveRes();
        }
        CreateRefundReq createRefundReq = bizContext.getAttribute(CreateRefundReq.class);

        bizContext.setAttribute(OtherConstant.ONLY_REFUND_NUM, false);

        List<HeOrderRefundEntity> refundEntityList = bizContext.getListAttribute(HeOrderRefundEntity.class);
        List<HeIncomeRecordEntity> incomeRecordEntities = bizContext.getListAttribute(HeIncomeRecordEntity.class);

        //检查申请和可退
        checkOrderRefundAmount(createRefundReq, incomeRecordEntities, refundEntityList);

        //获取这个订单收退总和
        QueryRefundInfoRes queryRefundInfoRes = orderRefundDomainService.refundInfoRes(createRefundReq.getOrderId(), refundEntityList, incomeRecordEntities);

        //原路退回场景需要判断支付记录有效期
        if (RefundTypeEnum.BACK_TRACK.getCode().equals(createRefundReq.getRefundType())) {
            validateByRefundType(createRefundReq, createApproveRes, queryRefundInfoRes);
            if (createApproveRes.getType() != null) {
                bizContext.setAttribute(OtherConstant.CONTINUE, false);
                throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), createApproveRes.getMsg());
            }
        }

        //检查商品
        checkNumAndAmount(createRefundReq, bizContext);
    }

    private void validateByRefundType(CreateRefundReq createRefundReq, CreateApproveRes createApproveRes, QueryRefundInfoRes queryRefundInfoRes) {
        //如果是原路退回，需要判断金额，如果线下汇款，需要判断是否有快过期的
        if (!isContinue(createRefundReq, createApproveRes, queryRefundInfoRes)) {
            log.info("订单退款前校验失败，订单号：{}", createRefundReq.getOrderId());
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), createApproveRes.getMsg());
        }
    }

    private boolean isContinue(CreateRefundReq createRefundReq, CreateApproveRes createApproveRes, QueryRefundInfoRes queryRefundInfoRes) {
        boolean checkRefundAmount = checkRefundAmount(createRefundReq, createApproveRes, queryRefundInfoRes);

        if (checkRefundAmount) return false;
        //判断是否有支付宝或者微信即将过期的
        QueryRefundInfoRes.AboutExpireInfo aboutExpireInfo = queryRefundInfoRes.getAboutExpireInfo();
        if (aboutExpireInfo.getAliPayNum() > 0) {
            createApproveRes.setType(OrderRefundResultEnum.RESULT_0.getCode());
            createApproveRes.setMsg(String.format(OrderRefundResultEnum.RESULT_0.getValue(), aboutExpireInfo.getAliPayNum(), OmniPayTypeEnum.ALIPAY.getValue()));
            return false;
        }
        if (aboutExpireInfo.getWechatPayNum() > 0) {
            createApproveRes.setType(OrderRefundResultEnum.RESULT_0.getCode());
            createApproveRes.setMsg(String.format(OrderRefundResultEnum.RESULT_0.getValue(), aboutExpireInfo.getWechatPayNum(), OmniPayTypeEnum.WECHAT.getValue()));
            return false;
        }
        if (aboutExpireInfo.getPosNum() > 0) {
            createApproveRes.setType(OrderRefundResultEnum.RESULT_0.getCode());
            createApproveRes.setMsg(String.format(OrderRefundResultEnum.RESULT_0.getValue(), aboutExpireInfo.getPosNum(), OmniPayTypeEnum.ONLINE_POS.getValue()));
            return false;
        }

        return true;
    }

    private boolean checkRefundAmount(CreateRefundReq createRefundReq, CreateApproveRes createApproveRes, QueryRefundInfoRes queryRefundInfoRes) {
        QueryRefundInfoRes.AmountInfo canRefundIncome = queryRefundInfoRes.getCanRefundAmountInfo();
        BigDecimal refundAmount = BigDecimal.ZERO;

        Optional<CreateRefundReq.GoodsRefundAmountInfo> first = createRefundReq.getRefundAmountInfoList().stream().filter(c -> c.getModel().equals(PayMethodEnum.CASH.getModel())).findFirst();

        if (first.isPresent()) {
            refundAmount = first.get().getAmount();
        }

        //获取第三方可退的总金额 = 支付(微信+支付宝+pos+余额) - 退款(微信+支付宝+pos+余额)
        BigDecimal subtract = canRefundIncome.getAliPay().add(canRefundIncome.getWechatPay()).add(canRefundIncome.getPosPay()).add(canRefundIncome.getBalance());
        //原路退回判断金额是否够
        if (subtract.compareTo(refundAmount) < 0) {
            createApproveRes.setType(OrderRefundResultEnum.RESULT_2.getCode());
            createApproveRes.setMsg(OrderRefundResultEnum.RESULT_2.getValue());
            return true;
        }
        return false;
    }

    //校验本次退是否达到最多可退以及金额
    private void checkNumAndAmount(CreateRefundReq createRefundReq, FlowContext bizContext) {
        List<HeOrderGoodsEntity> orderGoodsEntityList = bizContext.getListAttribute(HeOrderGoodsEntity.class);

        for (CreateRefundReq.GoodsInfo goodsInfo : createRefundReq.getGoodsInfoList()) {
            if (!createRefundReq.getGoodsRefundType().equals(OrderRefundNatureEnum.RETURN_AND_REFUND.code()) && goodsInfo.getRefundNum() > 0) {
                throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "仅退款、退回重付不允许修改退款数量！");
            }
            if (goodsInfo.getRefundNum() < 0) {
                throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "退款数量不能小于0");
            }
//            if (goodsInfo.getRefundAmount().compareTo(BigDecimal.ZERO) < 0) {
//                throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "退款金额不能小于0");
//            }

            //仅退数量
            List<CreateRefundReq.GoodsRefundAmountInfo> collect = goodsInfo.getRefundGoodsAmountInfoList().stream().filter(g -> g.getAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());

            if (CollectionUtil.isEmpty(collect) && goodsInfo.getRefundNum() > 0) {
                bizContext.setAttribute(OtherConstant.ONLY_REFUND_NUM, true);
            }

            //特殊逻辑：组合商品中的节假日和多胞胎在传给后端的时候，是合并的，即 一个组合中的两个商品都有一天的节假日和多胞胎，那么这个组合最多可退两天的多胞胎和节假日，传给服务器的时候只会是2天的节假日和多胞胎，所以需要特殊判断
            boolean combinationAdditionList = false;
            if ((goodsInfo.getGoodsType().equals(GoodsTypeEnum.HOLIDAY_SERVICE.code())
                    || goodsInfo.getGoodsType().equals(GoodsTypeEnum.MULTIPLE_BIRTHS.code()))
                    && StringUtils.isNotEmpty(goodsInfo.getParentCombineSn())) {
                //如果这个商品的类型是 节假日|多胞胎 且有上级，需要再次判断当前上级是否还有上级，如果有且上级的类型是组合
                HeOrderGoodsEntity orderGoodsEntity = orderGoodsEntityList.stream().filter(g -> g.getOrderGoodsSn().equals(goodsInfo.getParentCombineSn())).findFirst().get();
                //还有上级
                if (StringUtils.isNotEmpty(orderGoodsEntity.getParentCombineSn())) {
                    HeOrderGoodsEntity parentOrderGoodsEntity = orderGoodsEntityList.stream().filter(g -> g.getOrderGoodsSn().equals(orderGoodsEntity.getParentCombineSn())).findFirst().get();
                    if (parentOrderGoodsEntity.getGoodsType().equals(GoodsTypeEnum.COMBINATION.code()) || Objects.equals(parentOrderGoodsEntity.getType(), GoodsTypeEnum.COMBINATION.code())) {
                        combinationAdditionList = true;
                        goodsInfo.setCombinationSn(parentOrderGoodsEntity.getOrderGoodsSn());
                    }
                }
            }
            goodsInfo.setCombinationAddition(combinationAdditionList);
            if (combinationAdditionList) {
                //检查组合中的附加项
                checkCombinationAddition(bizContext, goodsInfo);
            } else {
                Optional<HeOrderGoodsEntity> first = orderGoodsEntityList.stream().filter(o -> o.getOrderGoodsSn().equals(goodsInfo.getOrderGoodsSn())).findFirst();
                HeOrderGoodsEntity orderGoodsEntity = first.orElse(null);
                //检查普通商品
                checkGoods(bizContext.getListAttribute(IncomePaidAllocationEntity.class), bizContext.getListAttribute(HeOrderRefundGoodsEntity.class), Collections.singletonList(goodsInfo.getOrderGoodsSn()), goodsInfo.getGoodsNum(), goodsInfo.getRefundNum(), goodsInfo.getRefundGoodsAmountInfoList(), goodsInfo.getGoodsName(), orderGoodsEntity);
            }
        }
    }

    private void checkCombinationAddition(FlowContext bizContext, CreateRefundReq.GoodsInfo goodsInfo) {
        List<HeOrderGoodsEntity> orderGoodsList = bizContext.getListAttribute(HeOrderGoodsEntity.class);
        //获取这个组合中的所有子项，并获取子项的加收项，再获取这些加收项的已经退款或者正在退款的情况
        String parentCombineSn = goodsInfo.getParentCombineSn();
        //这个附加项的上级
        HeOrderGoodsEntity parentGoods = orderGoodsList.stream().filter(g -> g.getOrderGoodsSn().equals(parentCombineSn)).findFirst().get();
        //组合
        HeOrderGoodsEntity orderCombinationGoodsEntity = orderGoodsList.stream().filter(g -> g.getOrderGoodsSn().equals(parentGoods.getParentCombineSn())).findFirst().get();
        //获取这个组合下所有的节假日或者多胞胎
        List<String> orderGoodsSnList = new ArrayList<>();
        orderGoodsList.stream().filter(g -> StringUtils.isNotEmpty(g.getParentCombineSn()) && g.getParentCombineSn().equals(orderCombinationGoodsEntity.getOrderGoodsSn())).forEach(child -> {
            //获取所有符合的子项
            orderGoodsList.stream().filter(g -> StringUtils.isNotEmpty(g.getParentCombineSn()) && g.getParentCombineSn().equals(child.getOrderGoodsSn()) && g.getGoodsType().equals(goodsInfo.getGoodsType())).forEach(add -> {
                orderGoodsSnList.add(add.getOrderGoodsSn());
            });
        });
        List<HeOrderGoodsEntity> collect = orderGoodsList.stream().filter(b -> orderGoodsSnList.contains(b.getOrderGoodsSn())).collect(Collectors.toList());
        //根据订单商品表获取总数量
        Integer allGoodsNum = collect.stream().mapToInt(HeOrderGoodsEntity::getGoodsNum).sum();

        checkGoods(bizContext.getListAttribute(IncomePaidAllocationEntity.class), bizContext.getListAttribute(HeOrderRefundGoodsEntity.class), orderGoodsSnList, allGoodsNum, goodsInfo.getRefundNum(), goodsInfo.getRefundGoodsAmountInfoList(), goodsInfo.getGoodsName(), null);
    }

    private void checkGoods(List<IncomePaidAllocationEntity> incomePaidAllocationEntities, List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList, List<String> orderGoodsSnList, Integer allGoodsNum, Integer refundNum, List<CreateRefundReq.GoodsRefundAmountInfo> goodsRefundAmountInfoList, String goodsName, HeOrderGoodsEntity orderGoodsEntity) {
        List<IncomePaidAllocationEntity> paidList = incomePaidAllocationEntities.stream().filter(i -> orderGoodsSnList.contains(i.getOrderGoodsSn())).collect(Collectors.toList());
        List<HeOrderRefundGoodsEntity> orderRefundList = orderRefundGoodsEntityList.stream().filter(o -> orderGoodsSnList.contains(o.getOrderGoodsSn())
                && (o.getStatus().equals(OrderRefundGoodsStatusEnum.REFUNDING.code())
                || o.getStatus().equals(OrderRefundGoodsStatusEnum.SUCCESS.code()))
        ).collect(Collectors.toList());

        for (PayMethodEnum value : PayMethodEnum.values()) {
            Optional<CreateRefundReq.GoodsRefundAmountInfo> first = goodsRefundAmountInfoList.stream().filter(g -> g.getAmountType().equals(new Integer(value.getCode()))).findFirst();
            if (!first.isPresent()) {
                continue;
            }
            CreateRefundReq.GoodsRefundAmountInfo goodsRefundAmountInfo = first.get();

            List<IncomePaidAllocationEntity> payMethodPaidList = paidList.stream().filter(p -> PayMethodEnum.PayType2Currency(Integer.valueOf(p.getPaymentMethod())).equals(value)).collect(Collectors.toList());
            List<HeOrderRefundGoodsEntity> payMethodRefundList = orderRefundList.stream().filter(p -> PayMethodEnum.PayType2Currency(p.getPayType()).equals(value)).collect(Collectors.toList());

            //当前已付总金额
            BigDecimal paidAmount = RMBUtils.bigDecimalF2Y(payMethodPaidList.stream().mapToInt(IncomePaidAllocationEntity::getPaidAmount).sum());
            //冻结+已退款总金额
            BigDecimal freezeAndRefundAmount = RMBUtils.bigDecimalF2Y(payMethodRefundList.stream().mapToInt(HeOrderRefundGoodsEntity::getRefundAmount).sum());
            //可退金额
            BigDecimal canRefundAmount = paidAmount.subtract(freezeAndRefundAmount);
            if (goodsRefundAmountInfo.getAmount().compareTo(canRefundAmount) > 0) {
                throw new BusinessException(ErrorCodeEnum.REFUND_NUM_OR_AMOUNT.code().toString(), goodsName + "最多可退" + value.getValue() + canRefundAmount + "！");
            }
        }

        // 冻结+已退款总数量
        // 按退款sn和orderGoodsSn组合分组，相同退款sn且相同orderGoodsSn的记录只算一条，避免重复计算
        Integer freezeAndRefundNum = orderRefundList.stream()
                .collect(Collectors.groupingBy(refund -> refund.getRefundOrderSn() + "_" + refund.getOrderGoodsSn()))
                .values()
                .stream()
                .mapToInt(refundList -> refundList.get(0).getRefundNum()) // 每组只取第一条记录的退款数量
                .sum();
        //可退数量
        Integer canRefundNum = allGoodsNum - freezeAndRefundNum;
        if (ObjectUtil.isNotEmpty(orderGoodsEntity) && orderGoodsEntity.getGoodsType().equals(GoodsTypeEnum.Production_Coin.code())) {
            //产康金最大可退数量通过资产中心获取
            Long ckjByOrderId = assetTradeService.getCkjByOrderId(incomePaidAllocationEntities.get(0).getOrderId(), orderGoodsEntity.getOrderGoodsSn());
            if (Objects.nonNull(ckjByOrderId) && refundNum > ckjByOrderId) {
                throw new BusinessException(ErrorCodeEnum.REFUND_NUM_OR_AMOUNT.code().toString(), goodsName + "最多可退数量" + ckjByOrderId + "！");
            }
        } else {
            if (refundNum.compareTo(canRefundNum) > 0) {
                throw new BusinessException(ErrorCodeEnum.REFUND_NUM_OR_AMOUNT.code().toString(), goodsName + "最多可退数量" + canRefundNum + "！");
            }
        }
    }

    /**
     * 验证和修正总金额精度问题
     * 解决 BigDecimal 加法可能导致的精度丢失问题（如 83.33 + 416.67 = 499.99）
     *
     * @param createRefundReq      退款请求
     * @param refundAmountInfoList 聚合后的退款金额列表
     */
    private static void validateAndFixTotalAmount(CreateRefundReq createRefundReq,
                                                  List<CreateRefundReq.GoodsRefundAmountInfo> refundAmountInfoList) {
        if (createRefundReq.getRefundAmount() == null || refundAmountInfoList.isEmpty()) {
            return;
        }

        // 计算聚合后的总金额
        BigDecimal calculatedTotal = refundAmountInfoList.stream()
                .filter(Objects::nonNull)
                .map(CreateRefundReq.GoodsRefundAmountInfo::getAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);

        // 原始请求的总金额
        BigDecimal originalTotal = createRefundReq.getRefundAmount().setScale(2, RoundingMode.HALF_UP);

        // 检查是否存在精度差异（差异在0.01元以内认为是精度问题）
        BigDecimal difference = originalTotal.subtract(calculatedTotal).abs();
        if (difference.compareTo(new BigDecimal("0.01")) <= 0 && difference.compareTo(BigDecimal.ZERO) > 0) {

            log.warn("检测到退款金额精度差异，原始总额：{}，计算总额：{}，差异：{}",
                    originalTotal, calculatedTotal, difference);

            // 修正最大金额的退款项，避免精度丢失
            refundAmountInfoList.stream()
                    .filter(Objects::nonNull)
                    .filter(info -> info.getAmount() != null)
                    .max(Comparator.comparing(CreateRefundReq.GoodsRefundAmountInfo::getAmount))
                    .ifPresent(maxAmountInfo -> {
                        BigDecimal adjustedAmount = maxAmountInfo.getAmount().add(originalTotal.subtract(calculatedTotal))
                                .setScale(2, RoundingMode.HALF_UP);
                        maxAmountInfo.setAmount(adjustedAmount);

                        log.info("已修正退款金额精度问题，调整项目：{}，调整前：{}，调整后：{}",
                                maxAmountInfo.getAmountType(), maxAmountInfo.getAmount(), adjustedAmount);
                    });
        }
    }

    @Override
    public boolean condition(FlowContext context) {
        CreateRefundReq createRefundReq = context.getAttribute(CreateRefundReq.class);
        HeOrderEntity orderEntity = context.getAttribute(HeOrderEntity.class);
        return ObjectUtil.isNotEmpty(createRefundReq) && ObjectUtil.isNotEmpty(createRefundReq.getOrderId()) && orderEntity.isNewOrder();
    }

}

package com.stbella.order.server.order.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.utils.TraceIdUtils;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.repository.OrderRefundRepository;
import com.stbella.order.server.context.component.StoreCurrencyContainer;
import com.stbella.order.server.utils.wangdian.StringUtils;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


/**
 * 统一获取没有设置货币符号的退款记录，并根据门店的货币符号进行赋值
 */
@Component
@Slf4j
public class RefundCurrencyHandler {

    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Value("${refresh.startDate}")
    private Long startDate;


    @XxlJob("refundCurrencyHandler")
    public void refundCurrencyHandler() {
        //直接从缓存取
        TraceIdUtils.resetTraceId();
        List<HeOrderRefundEntity> refundEntityList = orderRefundRepository.queryCurrencyIsNull(startDate);
        log.info("处理没有货币的退款记录：{}", refundEntityList.size());
        if (CollectionUtil.isNotEmpty(refundEntityList)) {
            List<HeOrderRefundEntity> storeIdNull = refundEntityList.stream().filter(r -> ObjectUtil.isEmpty(r.getStoreId())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(storeIdNull)) {
                log.error("错误数据：" + storeIdNull);
            }
            refundEntityList = refundEntityList.stream().filter(r -> ObjectUtil.isNotEmpty(r.getStoreId())).collect(Collectors.toList());
            for (HeOrderRefundEntity orderRefundEntity : refundEntityList) {
                String currency =StoreCurrencyContainer.getStoreCurrencyCode(orderRefundEntity.getStoreId());
                currency = currency.trim();
                orderRefundEntity.setCurrency(currency);
            }
            orderRefundRepository.batchUpdateById(refundEntityList);
        }
    }
}

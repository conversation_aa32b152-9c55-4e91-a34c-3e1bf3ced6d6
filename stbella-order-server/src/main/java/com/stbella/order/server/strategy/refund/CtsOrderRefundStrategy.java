package com.stbella.order.server.strategy.refund;

import cn.hutool.json.JSONUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.server.order.cts.dto.RefundNotifyMqDTO;
import com.stbella.order.server.order.cts.service.OrderFacade;
import com.stbella.order.server.order.month.enums.PayRecordEnum;
import com.stbella.pay.server.entity.mq.RefundNotifyMq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 退款策略-予家订单
 * @date 2024/3/15 10:56
 */
@Slf4j
@Component
public class CtsOrderRefundStrategy implements RefundStrategy {
    @Resource
    private OrderFacade orderFacade;

    @Override
    public void processRefund(RefundNotifyMq refundNotifyMq) {
        //到家订单处理
        RefundNotifyMqDTO refundNotifyMqDTO = new RefundNotifyMqDTO();
        BeanUtils.copyProperties(refundNotifyMq, refundNotifyMqDTO);
        //处理到家的业务逻辑：支付记录、订单状态、申请记录
        Boolean ret = orderFacade.refundNotify(refundNotifyMqDTO);
        if (!ret) {
            //抛出异常，让MQ重试发现消息
            throw new BusinessException(ResultEnum.MESSAGE_CONSUMPTION_ERROR, "CTS退款MQ消费失败RefundNotifyMqDTO:{}" + JSONUtil.toJsonStr(refundNotifyMqDTO));
        }
    }

    @Override
    public PayRecordEnum getPayRecordEnum() {
        return PayRecordEnum.CTS;
    }
}
package com.stbella.order.server.contract.month.service.impl;

import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.month.*;
import com.stbella.order.common.utils.GeneratorUtil;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.config.StoreConfig;
import com.stbella.order.server.contract.dto.ContractParamFillRequestDTO;
import com.stbella.order.server.contract.dto.ContractParamFillResponseDTO;
import com.stbella.order.server.contract.month.component.MonthContractAssembler;
import com.stbella.order.server.contract.provider.month.ESignProvider;
import com.stbella.order.server.contract.req.*;
import com.stbella.order.server.contract.service.month.MonthContractSignRecordService;
import com.stbella.order.server.contract.service.month.MonthOrderParamHistoryService;
import com.stbella.order.server.convert.AppMonthContractSignRecordConverter;
import com.stbella.order.server.manager.TabClientManager;
import com.stbella.order.server.order.month.enums.PayAmountEnum;
import com.stbella.order.server.order.month.req.AheadOutRoomQuery;
import com.stbella.order.server.order.month.req.MonthContractSignRecordReq;
import com.stbella.order.server.order.month.req.OrderContractQuery;
import com.stbella.order.server.order.month.res.*;
import com.stbella.order.server.order.month.service.MonthOrderWxCommandService;
import com.stbella.order.server.order.month.service.MonthOrderWxQueryService;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.order.server.utils.BigDecimalUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RefreshScope
public class MonthContractSignRecordServiceImpl implements MonthContractSignRecordService {

    @Resource
    private AppMonthContractSignRecordConverter appMonthContractSignRecordConverter;
    @Resource
    private StoreRepository storeRepository;

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private MonthContractSignRecordRepository monthContractSignRecordRepository;

    @Resource
    private MonthOrderParamHistoryRepository monthOrderParamHistoryRepository;

    @Resource
    private MonthEsignTemplateRepository esignTemplateRepository;

    @Resource
    private MonthOrderParamHistoryService orderParamHistoryService;

    @Resource
    private ESignProvider eSignProvider;

    @Resource
    private TabClientManager tabClientManager;

    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;

    @Resource
    private AheadOutRoomRepository aheadOutRoomRepository;

    @Resource
    private MonthOrderWxQueryService monthOrderWxQueryService;

    @Resource
    @Lazy
    private MonthOrderWxCommandService monthOrderWxCommandService;

    @Resource
    private OrderGoodsRepository orderGoodsRepository;

    @Resource
    private IncomeRecordRepository incomeRecordRepository;

    @Resource
    private MonthContractAssembler monthContractAssembler;

    @Resource
    private ContractSignRecordPaperRepository contractSignRecordPaperRepository;
    @Value("${month-contract-h5.check}")
    private String checkUrl;
    @Resource
    private MonthContractSignAgreementRepository monthContractSignAgreementRepository;

    /**
     * 生成合同接口
     *
     * @param req@return 生成的合同
     */
    @Override
    public Result<MonthContractSignRecordVO> createContract(CreateMainContractReq req) {
        log.info("生成合同填充{}", JSONUtil.toJsonStr(req));
        HeOrderEntity orderEntity = orderRepository.queryOrderById(req.getOrderId());
        if (ObjectUtil.isEmpty(orderEntity)) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "订单orderId：" + req.getOrderId());
        }
        //查询当前订单的纸质合同是否已经签署过  您已签订纸质合同，无需再次签订
        List<ContractSignRecordPaperEntity> contractSignRecordPaperEntities = contractSignRecordPaperRepository.getByOrderId(req.getOrderId());
        boolean isPaperExists = contractSignRecordPaperEntities.stream().anyMatch(contract -> Objects.equals(contract.getContractType(), req.getTemplateContractType()));
        if (isPaperExists) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "您已提交/已签订纸质合同，无需再次签订");
        }

        //查询门店信息,找到门店类型
        CfgStoreEntity cfgStore = storeRepository.queryCfgStoreById(orderEntity.getStoreId());
        if (ObjectUtil.isEmpty(cfgStore)) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "门店storeId：" + orderEntity.getStoreId());
        }

        Integer storeType = cfgStore.getType();
        if (StoreConfig.SPECIAL_TYPE_STORE_MAP.containsKey(cfgStore.getStoreId())) {
            storeType = StoreConfig.SPECIAL_TYPE_STORE_MAP.get(cfgStore.getStoreId());
            cfgStore.setType(storeType);
        }
        log.info("门店类型 storeType=> {}", storeType);
        //查询,如果已有合同则重新生成合同文本,直接返回合同
        MonthContractSignRecordReq recordReq = new MonthContractSignRecordReq();
        recordReq.setGuideId(Long.valueOf(req.getOrderId()));
        recordReq.setTemplateType(TemplateTypeEnum.MAIN_TYPE.code());
        //模板关联合同类型8.母婴护理服务合同书 21.预约协议
        recordReq.setTemplateContractTypes(Collections.singletonList(req.getTemplateContractType()));
        MonthContractSignRecordVO byOrderId = this.queryByCondition(recordReq);

        // 如果已有合同，直接返回合同
        if (byOrderId != null) {
            return Result.success(byOrderId);
        }

        return Result.success(this.createContractData(cfgStore, orderEntity, req.getTemplateContractType()));
    }

    /**
     * 创建合同数据,这个是因为查询门店信息的时候涉及到了跨数据源,所以单独拆出来做单独事物
     *
     * @param order 订单数据
     * @return 创建好的合同
     */
    @Transactional
    public MonthContractSignRecordVO createContractData(CfgStoreEntity cfgStore, HeOrderEntity order, Integer templateContractType) {
        //订单类型
        Integer orderType = order.getOrderType();
        //门店类型
        Integer storeType = cfgStore.getType();
        //合同模板
        Integer contractType = cfgStore.getContractType();
        //生成合同基础信息  继承订单信息,根据类型生成合同名称,根据规则生成合同编号
        MonthContractSignRecordReq contract = new MonthContractSignRecordReq();
        //客户id
        contract.setClientUid(order.getClientUid());
        contract.setStaffId(order.getStaffId());
        contract.setStoreId(Long.valueOf(order.getStoreId()));
        contract.setGuideId(Long.valueOf(order.getOrderId()));
        Integer storeId = order.getStoreId();
        /**
         * 现在的业务暂时只有订单和E签宝合同 不支持其他,所以这里固定写死
         */
        //默认为1  订单
        contract.setGuideType(1);
        //默认为1  E签宝合同
        contract.setContractType(contractType);
        //合同类型
        contract.setTemplateContractType(templateContractType);

        //根据合同类型拿到合同名称
        String typeName = TemplateContractTypeEnum.fromCode(contract.getTemplateContractType());
        contract.setContractName(typeName);
        //生成合同编号
        String contractNo = GeneratorUtil.contractNo();
        //查询合同对应的模版  找到对应的合同模版文件
        MonthEsignTemplateVO esignTemplate;
        // todo 先按最小维度查询具体门店
        //      其次按门店类型 订单类型 维度查询
        esignTemplate = appMonthContractSignRecordConverter.entity2MonthEsignTemplateVo(esignTemplateRepository.specialGetByStoreId(storeId, orderType, storeType, TemplateTypeEnum.MAIN_TYPE.code(), templateContractType, 0));
        log.info("门店维度：esignTemplate {}", JSONUtil.toJsonStr(esignTemplate));
        if (Objects.isNull(esignTemplate)) {
            esignTemplate = appMonthContractSignRecordConverter.entity2MonthEsignTemplateVo(esignTemplateRepository.getByTypes(orderType, storeType, TemplateTypeEnum.MAIN_TYPE.code(), templateContractType));
            log.info("品牌维度：esignTemplate {}", JSONUtil.toJsonStr(esignTemplate));
        }

        //生成合同数据
        contract.setContractNo(contractNo);
        contract.setCreatedAt(System.currentTimeMillis() / 1000);
        contract.setUpdatedAt(System.currentTimeMillis() / 1000);
        contract.setTemplateId(esignTemplate.getId());
        contract.setContractStatus(ContractStatusEnum.NOT_SIGNED.code());
        //填充参数,生成签署文件
        ContractParamFillRequestDTO contractParamFillRequestDTO = new ContractParamFillRequestDTO();
        contractParamFillRequestDTO.setName(contract.getContractName() + ".pdf");
        contractParamFillRequestDTO.setTemplateId(esignTemplate.getEsignFileId());
        //获取信息,填充部分合同信息
        Map<String, String> templateData = new HashMap<>();

        List<MonthOrderParamHistoryVO> contractTemplateData = orderParamHistoryService.getContractTemplateData(contract.getGuideId(), 1);
        for (MonthOrderParamHistoryVO orderParamHistory : contractTemplateData) {
            templateData.put(orderParamHistory.getMark(), orderParamHistory.getParamValue());
        }
        templateData.put("contract_no", contractNo);
        templateData.put("sign_time_str2", DateTime.now().toDateStr());
        contractParamFillRequestDTO.setContent(templateData);
        if (ObjectUtil.equals(contractType, ContractTypeEnum.ESIGN_TYPE.code())) {
            ContractParamFillResponseDTO paramFillResponseDTO = eSignProvider.doFillContentTemplate(contractParamFillRequestDTO);
            //回写参数文件id
            contract.setEsignFileId(paramFillResponseDTO.getFileId());
            contract.setEsignFileName(paramFillResponseDTO.getFileName());
        }
        //填充,保证插入
        contract.setContractImageSignature("");
        contract.setContractImageHtml("");
        contract.setContractTempUrl("");
        //保存合同
        Long contractId = monthContractSignRecordRepository.create(contract);
        //反写合同id给历史参数
        for (MonthOrderParamHistoryVO orderParamHistory : contractTemplateData) {
            orderParamHistory.setContractId(contractId);
        }
        contract.setId(contractId);
        monthOrderParamHistoryRepository.updateBatchByParamHistoryId(contractTemplateData);
        return appMonthContractSignRecordConverter.req2MonthContractSignRecordVo(contract);

    }

    @Override
    @Transactional
    public MonthContractSignRecordVO reloadContract(Long id, Integer storeType, Integer templateContractType) {
        //拿到合同原信息
        MonthContractSignRecordEntity contract = monthContractSignRecordRepository.getByContractSignId(id);
        // 模版会根据运营调整，之前生成的合同数据还是老数据的templateId 所以这里需要根据订单列表 门店类型查询
        // 订单信息
        HeOrderEntity order = orderRepository.queryOrderById(contract.getGuideId().intValue());
        Integer storeId = order.getStoreId();
        //订单类型
        Integer orderType = order.getOrderType();
        MonthEsignTemplateVO esignTemplate;
        // todo 先按最小维度查询具体门店
        //      其次按门店类型 订单类型 维度查询
        esignTemplate = appMonthContractSignRecordConverter.entity2MonthEsignTemplateVo(esignTemplateRepository.specialGetByStoreId(storeId, orderType, storeType, TemplateTypeEnum.MAIN_TYPE.code(), templateContractType, 0));
        log.info("门店维度：esignTemplate {}", JSONUtil.toJsonStr(esignTemplate));
        if (Objects.isNull(esignTemplate)) {
            esignTemplate = appMonthContractSignRecordConverter.entity2MonthEsignTemplateVo(esignTemplateRepository.getByTypes(orderType, storeType, TemplateTypeEnum.MAIN_TYPE.code(), templateContractType));
            log.info("品牌维度：esignTemplate {}", JSONUtil.toJsonStr(esignTemplate));
        }
        //拿到模版信息
//        EsignTemplate esignTemplate = esignTemplateService.getById(contract.getTemplateId());
        //填充参数,调用E签宝接口生成签署文件
        ContractParamFillRequestDTO contractParamFillRequestDTO = new ContractParamFillRequestDTO();
        contractParamFillRequestDTO.setName(contract.getContractName());
        contractParamFillRequestDTO.setTemplateId(esignTemplate.getEsignFileId());


        HashMap<String, String> contentData = new HashMap<>();
        List<MonthOrderParamHistoryVO> contractTemplateData = orderParamHistoryService.listByContractId(contract.getId());
        for (MonthOrderParamHistoryVO orderParamHistory : contractTemplateData) {
            contentData.put(orderParamHistory.getMark(), orderParamHistory.getParamValue());
        }
        contentData.put("contract_no", contract.getContractNo());
        contentData.put("sign_time_str2", DateTime.now().toDateStr());
        contractParamFillRequestDTO.setContent(contentData);
        ContractParamFillResponseDTO paramFillResponseDTO = eSignProvider.doFillContentTemplate(contractParamFillRequestDTO);

        String esignFlowId = contract.getEsignFlowId();

        //回写参数文件id
        // 撤销合同流程的时候 把flowId url给置空
        // 每次reload的时候 用上面查到的 esignTemplate id更新到记录表中 主要是解决模版更换了的问题
        contract.setEsignFileId(paramFillResponseDTO.getFileId());
        contract.setTemplateId(esignTemplate.getId());
        contract.setEsignFlowId("");
        contract.setContractLongUrl("");
        contract.setContractShortUrl("");
        //变更文件
        monthContractSignRecordRepository.updateBySignRecordId(contract);
        //撤销旧的合同流程
        if (StringUtils.isNotBlank(esignFlowId)) {
            eSignProvider.cancelSignFlow(esignFlowId);
        }
        return appMonthContractSignRecordConverter.entity2MonthContractSignRecordVo(contract);
    }

    /**
     * 获取订单所属的合同信息
     *
     * @param orderId 订单id
     * @return 合同
     */
    @Override
    public MonthContractSignRecordVO getByOrderId(Integer orderId, Integer templateContractType) {
        return appMonthContractSignRecordConverter.entity2MonthContractSignRecordVo(monthContractSignRecordRepository.getByOrderId(orderId, templateContractType));
    }

    @Override
    public MonthContractSignRecordVO queryByCondition(MonthContractSignRecordReq monthContractSignRecordReq) {
        return appMonthContractSignRecordConverter.entity2MonthContractSignRecordVo(monthContractSignRecordRepository.queryByCondition(monthContractSignRecordReq));
    }

    @Override
    public MonthContractSignRecordVO getByContractSignId(Long id) {
        return appMonthContractSignRecordConverter.entity2MonthContractSignRecordVo(monthContractSignRecordRepository.getByContractSignId(id));
    }

    /**
     * 老合同签署
     *
     * @param oldContractSignReq
     */
    @Override
    public void oldContractSign(OldContractSignReq oldContractSignReq) {
        Integer contractId = oldContractSignReq.getContractId();
        String pdfUrl = oldContractSignReq.getPdfUrl();
        String contractImageSignature = oldContractSignReq.getContractImageSignature();
        MonthContractSignRecordEntity byContractSignId = monthContractSignRecordRepository.getByContractSignId(Long.valueOf(contractId));
        if (ObjectUtil.isNotEmpty(pdfUrl)) {
            byContractSignId.setContractStatus(2);
            byContractSignId.setViewpdfUrl(checkUrl + pdfUrl);
        }
        byContractSignId.setContractLongUrl(pdfUrl);
        byContractSignId.setContractImageSignature(contractImageSignature);
        byContractSignId.setUpdatedAt(System.currentTimeMillis() / 1000);
        monthContractSignRecordRepository.updateBySignRecordId(byContractSignId);
        //如果是提前离馆协议,需要设置提前离馆状态为不可修改
        if (ObjectUtil.equals(byContractSignId.getTemplateContractType(), TemplateContractTypeV5Enum.RELEASE.code()) && ObjectUtil.isNotEmpty(pdfUrl)) {
            AheadOutRoomQuery query = new AheadOutRoomQuery();
            query.setOrderId(Math.toIntExact(byContractSignId.getGuideId()));
            AheadOutRoomEntity aheadOutRoomEntity = aheadOutRoomRepository.queryByOrderId(query);
            aheadOutRoomEntity.setState(1);
            aheadOutRoomRepository.updateOutRoom(aheadOutRoomEntity);
        }
    }

    /**
     * 重置合同签署状态
     *
     * @param contractId
     */
    @Override
    public void resetContract(Integer contractId) {
        MonthContractSignRecordEntity contract = monthContractSignRecordRepository.getByContractSignId(Long.valueOf(contractId));
        if (ObjectUtil.isEmpty(contract)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "合同不存在ID：" + contractId);
        }
        monthContractSignRecordRepository.resetContract(contractId);
    }

    /**
     * 客户详情-已签署合同列表
     *
     * @param query
     */
    @Override
    public List<ContractSignRecordVO> contractSignRecordListByClientId(OrderContractQuery query) {
        final Integer clientId = query.getClientId();
        final Integer storeId = query.getStoreId();
        final String orderTypes = query.getOrderTypes();

        List<ContractSignRecordVO> contractSignRecordVOS = new ArrayList<>();
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        MonthContractSignQuery monthContractSignQuery = new MonthContractSignQuery();
        monthContractSignQuery.setClientId(clientId);
        monthContractSignQuery.setContractStatus(ContractStatusEnum.SIGNED.code());
        //已签署合同
        List<MonthContractSignRecordEntity> contractSignRecordEntityList = monthContractSignRecordRepository.queryListByCondition(monthContractSignQuery);
        //补充协议列表
        List<MonthContractSignAgreementEntity> contractSignAgreementEntityList = monthContractSignAgreementRepository.queryListByCondition(monthContractSignQuery);
        //纸质合同
        List<ContractSignRecordPaperEntity> contractSignRecordPaperEntityList = contractSignRecordPaperRepository.queryListByCondition(monthContractSignQuery);

        Function<MonthContractSignAgreementEntity, MonthContractSignRecordEntity> fun = i -> {
            MonthContractSignRecordEntity contractSignRecord = new MonthContractSignRecordEntity();
            BeanUtil.copyProperties(i, contractSignRecord);
//            contractSignRecord.setId(0);
            contractSignRecord.setGuideId(i.getOrderId());
            contractSignRecord.setCreatedAt(i.getGmtCreate().getTime() / 1000); //时间格式
            contractSignRecord.setContractType(1); //默认新合同E签宝
            contractSignRecord.setTemplateType(3); //默认类型 附属协议 - 补充协议
            contractSignRecord.setContractName(i.getName()); //合同名称

            return contractSignRecord;

        };

        List<MonthContractSignRecordEntity> collect = contractSignAgreementEntityList.stream().map(fun).collect(Collectors.toList());
        contractSignRecordEntityList.addAll(collect);

        contractSignRecordEntityList.forEach(x -> {
            ContractSignRecordVO contractSignRecordVO = new ContractSignRecordVO();
            Long time = TimeUnit.SECONDS.toMillis(x.getCreatedAt());
            String date = df.format(time);
            contractSignRecordVO.setId(Math.toIntExact(x.getId()));
            contractSignRecordVO.setContractName(x.getContractName());
            contractSignRecordVO.setClientUid(x.getClientUid());
            if (ObjectUtil.equals(ContractTypeEnum.OLD_TYPE.code(), x.getContractType())) {
                contractSignRecordVO.setDownloadUrl(x.getContractLongUrl());
            } else {
                contractSignRecordVO.setDownloadUrl(x.getDownloadUrl());
            }
            contractSignRecordVO.setGuideType(x.getGuideType());
            contractSignRecordVO.setTemplateId(String.valueOf(x.getTemplateId()));
//            contractSignRecordVO.setTemplateType(x.getTemplateContractType());
            contractSignRecordVO.setTemplateType(x.getTemplateType());
            contractSignRecordVO.setTemplateContractType(x.getTemplateContractType());
            contractSignRecordVO.setViewPdfUrl(x.getViewpdfUrl());

            contractSignRecordVO.setContractType(x.getContractType());
            contractSignRecordVO.setEsignFileId(x.getEsignFileId());
            contractSignRecordVO.setEsignFlowId(x.getEsignFlowId());
            contractSignRecordVO.setEsignFileName(x.getEsignFileName());
            contractSignRecordVO.setOrderId(Math.toIntExact(x.getGuideId()));
            contractSignRecordVO.setPaper(0);
            HeOrderEntity orderEntity = orderRepository.getByOrderId(x.getGuideId().intValue());
            contractSignRecordVO.setOrderType(String.valueOf(orderEntity.getOrderType()));
            if (x.getTaskId() != null && x.getTaskId() != 0) {
                contractSignRecordVO.setTaskId(x.getTaskId());
            } else {
                contractSignRecordVO.setTaskId(orderEntity.getTaskId());
            }
//            if (x.getTemplateType() == 1) {
//                contractSignRecordVO.setOrderType(x.getOrderType());
//            }
            contractSignRecordVO.setSignDate(date);
            contractSignRecordVO.setContractTempUrl(x.getContractTempUrl());
            contractSignRecordVOS.add(contractSignRecordVO);
        });

        contractSignRecordPaperEntityList.forEach(x -> {
            ContractSignRecordVO contractSignRecordVO = new ContractSignRecordVO();
            Long time = TimeUnit.SECONDS.toMillis(x.getCreatedAt());
            String date = df.format(time);
            contractSignRecordVO.setId(x.getId());
            contractSignRecordVO.setContractName(x.getContractName());
            contractSignRecordVO.setClientUid(x.getClientUid());
            contractSignRecordVO.setContractType(x.getContractType());
            contractSignRecordVO.setOrderId(x.getOrderId());
            HeOrderEntity orderEntity = orderRepository.getByOrderId(x.getOrderId());
            contractSignRecordVO.setOrderType(String.valueOf(orderEntity.getOrderType()));
            contractSignRecordVO.setPaper(1);
            contractSignRecordVO.setSignDate(date);
            contractSignRecordVOS.add(contractSignRecordVO);
        });

        contractSignRecordVOS.sort((o1, o2) -> o2.getSignDate().compareTo(o1.getSignDate()));
        return contractSignRecordVOS;
    }

    /**
     * 创建预约/附属 合同
     *
     * @param createAccessoryContractQuery
     * @return {@link MonthContractSignRecordVO}
     */
    @Override
    public Result<MonthContractSignRecordVO> createAccessoryContract(CreateAccessoryContractReq createAccessoryContractQuery) {
        //订单类型
        HeOrderEntity order = orderRepository.queryOrderById(createAccessoryContractQuery.getOrderId());
        Optional.ofNullable(order).orElseThrow(() -> new BusinessException(ResultEnum.PARAM_ERROR, "订单不存在"));
        if (TemplateContractTypeV2Enum.ENTRUST.code().equals(createAccessoryContractQuery.getTemplateContractType()) && !order.getSignType().equals(OrderSignTypeEnum.SIGN_TYPE_BAILOR.code())) {
            throw new BusinessException(ResultEnum.PARAM_ERROR, "订单orderId：" + createAccessoryContractQuery.getOrderId() + "合同主体请选择委托人");
        }

        //查询门店信息,找到门店类型
        CfgStoreEntity cfgStore = storeRepository.queryCfgStoreById(order.getStoreId());
        Optional.ofNullable(cfgStore).orElseThrow(() -> new BusinessException(ResultEnum.PARAM_ERROR, "门店不存在"));

        //查找客户名称
        //region 取订单快照信息表
        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(createAccessoryContractQuery.getOrderId());
        Optional.ofNullable(heOrderUserSnapshotEntity).orElseThrow(() -> new BusinessException(ResultEnum.PARAM_ERROR, "订单快照不存在"));
        //check 合同已归档补充重新在生成
        MonthContractSignRecordVO contract = this.checkContractAccessoryStatus(createAccessoryContractQuery);
        //默认新增
        Integer flag = 0;
        if (Objects.nonNull(contract)) {
            //修改action
            flag = 1;
        }
        if (Objects.isNull(contract)) {
            contract = new MonthContractSignRecordVO();
        }
        Integer storeType = cfgStore.getType();
        Integer orderType = order.getOrderType();
        //生成合同基础信息  继承订单信息,根据类型生成合同名称,根据规则生成合同编号
        //客户id
        contract.setClientUid(order.getClientUid());
        contract.setStaffId(order.getStaffId());
        contract.setStoreId(Long.valueOf(order.getStoreId()));
        contract.setGuideId(Long.valueOf(order.getOrderId()));
        contract.setTemplateType(createAccessoryContractQuery.getTemplateType());
        /**
         * 现在的业务暂时只有订单和E签宝合同 不支持其他,所以这里固定写死
         */
        //默认为1  订单
        contract.setGuideType(1);
        //默认为1  E签宝合同
        contract.setContractType(ContractTypeEnum.ESIGN_TYPE.code());
        //合同类型
        contract.setTemplateContractType(createAccessoryContractQuery.getTemplateContractType());
        //生成合同编号
        String contractNo = GeneratorUtil.contractNo();
        //获取信息,填充部分合同信息
        Map<String, String> templateData = new HashMap<>();
        //预约协议
        if (TemplateContractTypeV2Enum.APPOINTMENT.code().equals(createAccessoryContractQuery.getTemplateContractType())) {
            //查询当前订单的纸质合同是否已经签署过  您已签订纸质合同，无需再次签订
            List<ContractSignRecordPaperEntity> contractSignRecordPaperEntities = contractSignRecordPaperRepository.getByOrderId(createAccessoryContractQuery.getOrderId());
            boolean isPaperExists = contractSignRecordPaperEntities.stream().anyMatch(paperEntity -> Objects.equals(paperEntity.getContractType(), TemplateContractTypeV2Enum.APPOINTMENT.code()));
            if (isPaperExists) {
                throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "您已提交/已签订纸质合同，无需再次签订");
            }
            //预约协议 客户输入金额判断是否大于50%
            if (Objects.isNull(createAccessoryContractQuery.getAmountEarnest())) {
                throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "预约协议支付意向金不能为空");
            }

            if (createAccessoryContractQuery.getAmountEarnest().compareTo(new BigDecimal(0)) <= 0) {
                throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "只能输入大于0,且小于订单金额50%的数字");
            }
            if (createAccessoryContractQuery.getAmountEarnest().compareTo(BigDecimalUtil.divide(new BigDecimal(order.getPayAmount()), new BigDecimal(200))) >= 0) {
                throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "超出订单金额50%,请签订<<母婴护理服务合同书>>");
            }

            //client_name 甲方
            //id_card 甲方身份证号
            //phone 联系电话
            //company_name 乙方
            //goods_name  套餐名称
            //store_name 门店名称
            //order_amount 套餐费用
            //order_amount_words 套餐费用大写
            //sign_time_str2   当前时间
            //pay_amount_1 意向金
            //pay_amount_1_words 意向金大写
            HeOrderGoodsEntity orderGoodsEntity = orderGoodsRepository.getByOrderId(order.getOrderId());
            if (ObjectUtil.isEmpty(orderGoodsEntity)) {
                throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "订单商品不存在,订单orderId=" + order.getOrderId());
            }
            //意向金
            AtomicReference<BigDecimal> earnestMoney = new AtomicReference<>(BigDecimal.ZERO);
            List<HeIncomeRecordEntity> heIncomeRecordEntityList = incomeRecordRepository.getSuccessfulRecordListByOrderId(order.getOrderId());
            heIncomeRecordEntityList.forEach(x -> {
                if (x.getReceiptType().equals(PayAmountEnum.EARNEST_MONEY.getCode())) {
                    earnestMoney.updateAndGet(a -> a.add(new BigDecimal(x.getIncome())));
                }
            });
            templateData.put("client_name", heOrderUserSnapshotEntity.getName());
            templateData.put("id_card", heOrderUserSnapshotEntity.getIdCard());
            templateData.put("phone", heOrderUserSnapshotEntity.getPhone());
            templateData.put("company_name", BizConstant.COMPANY_NAME);
            templateData.put("goods_name", orderGoodsEntity.getGoodsName());
            templateData.put("store_name", cfgStore.getStoreName());
            templateData.put("order_amount", RMBUtils.formatToseparaDecimals(BigDecimalUtil.divide(new BigDecimal(orderGoodsEntity.getPayAmount()), new BigDecimal(100))));
            templateData.put("order_amount_words", RMBUtils.numToRMBStr(BigDecimalUtil.divide(new BigDecimal(orderGoodsEntity.getPayAmount()), new BigDecimal(100)).doubleValue()));
            templateData.put("contract_no", contractNo);
            templateData.put("pay_amount_1", RMBUtils.formatToseparaDecimals(createAccessoryContractQuery.getAmountEarnest()));
            templateData.put("pay_amount_1_words", RMBUtils.numToRMBStr(createAccessoryContractQuery.getAmountEarnest().doubleValue()));
            templateData.put("sign_time_str2", DateTime.now().toDateStr());
        }
        //委托书填充
        if (createAccessoryContractQuery.getTemplateContractType().equals(TemplateContractTypeEnum.ENTRUST.code())) {
            MonthUserEsignDTO userEsign = monthContractAssembler.userEsignAssembler(order.getOrderId(), createAccessoryContractQuery.getTemplateContractType());
            //委托人信息
            MonthUserEsignDTO monthUserEsignDTO = monthContractAssembler.userEsignAssembler(order.getOrderId(), null);
            templateData.put("contract_no", contractNo);
            templateData.put("bailor_name", monthUserEsignDTO.getName());
            templateData.put("bailor_id_card", monthUserEsignDTO.getIdCardNo());
            templateData.put("client_name", userEsign.getName());
            templateData.put("sign_time_str2", DateTime.now().toDateStr());
        }
        //订单折扣保密协议
        if (TemplateContractTypeEnum.DISCOUNT.code().equals(createAccessoryContractQuery.getTemplateContractType())) {
            //client_name 乙方
            //address 地址
            //phone 手机
            //company_name 甲方
            //id_card 身份证号
            //pay_amount_words 套餐价格大写
            //sign_time_str2 签署时间
            //remark  备注+额外礼赠
            OrderInfoByOrderVO orderInfoByOrderVO = monthOrderWxQueryService.getOrderInfoByOrderId(createAccessoryContractQuery.getOrderId());
            //"套餐额外礼赠信息")
            templateData.put("client_name", heOrderUserSnapshotEntity.getName());
            templateData.put("contract_no", contractNo);
            templateData.put("address", heOrderUserSnapshotEntity.getAddress());
            templateData.put("phone", heOrderUserSnapshotEntity.getPhone());
            templateData.put("company_name", BizConstant.COMPANY_NAME);
            templateData.put("id_card", heOrderUserSnapshotEntity.getIdCard());
            templateData.put("pay_amount_words", RMBUtils.numToRMBStr(BigDecimalUtil.divide(new BigDecimal(order.getPayAmount()), new BigDecimal(100)).doubleValue()));
            templateData.put("sign_time_str2", DateTime.now().toDateStr());
            //todo 额外礼赠备注处理拼装
            List<OrderInfoByGiftExtendSkuVO> orderInfoByGiftExtendSkuVOList = orderInfoByOrderVO.getOrderInfoByGiftExtendVO().getOrderInfoByGiftExtendSkuVOList();
            String extraGift = monthOrderWxCommandService.convertOrderGift2String(orderInfoByGiftExtendSkuVOList);
            String remark = StringUtils.isNotBlank(extraGift) ? BizConstant.GIFT + "：\n\n" + extraGift + "\n\n" + BizConstant.REMARK + "：\n\n" + order.getRemark() : BizConstant.REMARK + "：\n\n" + order.getRemark();
            templateData.put("remark", remark);
        }
        //提前离管合同解除协议填充
        if (TemplateContractTypeEnum.RELEASE.code().equals(createAccessoryContractQuery.getTemplateContractType())) {
            //查询主合同签订信息
            //contract_no 协议编号
            //client_name 甲方客户
            //company_name 乙方公司
            //main_contract_time_str 主合同签订时间
            //main_contract_no 主合同编号
            //amount 未消费金额
            //amount_words 未消费金额大写
            //sign_time_str2 签署日期
            //in_room_time_str 入住房态时间
            //取提前离管表
            AheadOutRoomQuery roomQuery = new AheadOutRoomQuery();
            roomQuery.setOrderId(createAccessoryContractQuery.getOrderId());
            AheadOutRoomEntity aheadOutRoomEntity = aheadOutRoomRepository.queryByOrderId(roomQuery);
            if (Objects.isNull(aheadOutRoomEntity)) {
                throw new BusinessException(ResultEnum.PARAM_ERROR, "提前离管未申请,不能签署合同");
            }
            if (AheadOutRoomEnum.STATE_DISENABLE.code().equals(aheadOutRoomEntity.getState())) {
                throw new BusinessException(ResultEnum.PARAM_ERROR, "合同已经签署无需在创建");
            }
            //获取主合同信息
            MonthContractSignRecordEntity yzManContract = monthContractSignRecordRepository.getByOrderIdAndContractStatus(createAccessoryContractQuery.getOrderId(), TemplateContractTypeEnum.YZ_SAINTBELLA.code(), ContractStatusEnum.SIGNED.code());
            List<ContractSignRecordPaperEntity> paperRepositoryByOrderId = contractSignRecordPaperRepository.getByOrderId(createAccessoryContractQuery.getOrderId(), ContractStatusEnum.SIGNED.code(), TemplateContractTypeV5Enum.YZ_SAINTBELLA.code());
            templateData.put("contract_no", contractNo);
            templateData.put("client_name", heOrderUserSnapshotEntity.getName());
            templateData.put("company_name", BizConstant.COMPANY_NAME);
            templateData.put("sign_time_str2", DateTime.now().toDateStr());
            templateData.put("in_room_time_str", DateUtil.formatDate(aheadOutRoomEntity.getCheckInRoomDate()));
            //未消费金额
            templateData.put("amount", RMBUtils.formatToseparaDecimals(BigDecimalUtil.divide(new BigDecimal(aheadOutRoomEntity.getRefundedAmount()), new BigDecimal(100))));
            //未消费金额大写
            templateData.put("amount_words", RMBUtils.numToRMBStr(BigDecimalUtil.divide(new BigDecimal(aheadOutRoomEntity.getRefundedAmount()), new BigDecimal(100)).doubleValue()));
            //e签宝或老合同
            if (ObjectUtil.isNotEmpty(yzManContract)) {
                //主合同编号
                templateData.put("main_contract_no", Objects.nonNull(yzManContract) ? yzManContract.getContractNo() : "");
                //主合同签订时间
                templateData.put("main_contract_time_str", Objects.nonNull(yzManContract) ? DateUtil.date(yzManContract.getUpdatedAt() * 1000).toDateStr() : "");
                //提前离馆合同类型要与主合同相同(如果是纸质合同,需要取门店实时的合同类型)
                contract.setContractType(Objects.nonNull(yzManContract) ? yzManContract.getContractType() : 1);
            }
            //纸质合同
            if (ObjectUtil.isNotEmpty(paperRepositoryByOrderId)) {
                ContractSignRecordPaperEntity contractSignRecordPaperEntity = paperRepositoryByOrderId.get(0);
                //主合同编号
                templateData.put("main_contract_no", Objects.nonNull(contractSignRecordPaperEntity) ? contractSignRecordPaperEntity.getCode() : "");
                //主合同签订时间
                templateData.put("main_contract_time_str", Objects.nonNull(contractSignRecordPaperEntity) ? DateUtil.date(contractSignRecordPaperEntity.getUpdatedAt() * 1000).toDateStr() : "");
                //提前离馆合同类型要与主合同相同(如果是纸质合同,需要取门店实时的合同类型)
                contract.setContractType(Objects.nonNull(contractSignRecordPaperEntity) ? cfgStore.getContractType() : 1);
            }
        }


        //根据合同类型拿到合同名称
        String typeName = TemplateContractTypeEnum.fromCode(contract.getTemplateContractType());
        contract.setContractName(typeName);
        //查询合同对应的模版  找到对应的合同模版文件
        MonthEsignTemplateVO esignTemplate;

        //代码层转换品牌类型
        if (StoreConfig.SPECIAL_TYPE_STORE_MAP.containsKey(order.getStoreId())) {
            storeType = StoreConfig.SPECIAL_TYPE_STORE_MAP.get(order.getStoreId());
        }
        //根据订单查到合同模版表实体 查询参数,  订单类型,门店类型
        //根据条件找第一个模版
        // todo 先按最小维度查询具体门店
        //      其次按门店类型 订单类型 维度查询
        esignTemplate = appMonthContractSignRecordConverter.entity2MonthEsignTemplateVo(esignTemplateRepository.specialGetByStoreId(order.getStoreId(), orderType, storeType, createAccessoryContractQuery.getTemplateType(), createAccessoryContractQuery.getTemplateContractType(), 0));
        log.info("门店维度：esignTemplate {}", JSONUtil.toJsonStr(esignTemplate));
        if (Objects.isNull(esignTemplate)) {
            esignTemplate = appMonthContractSignRecordConverter.entity2MonthEsignTemplateVo(esignTemplateRepository.getByTypes(orderType, storeType, createAccessoryContractQuery.getTemplateType(), createAccessoryContractQuery.getTemplateContractType()));
            log.info("品牌维度：esignTemplate {}", JSONUtil.toJsonStr(esignTemplate));
        }

        //生成合同数据
        contract.setContractNo(contractNo);
        contract.setUpdatedAt(System.currentTimeMillis() / 1000);
        contract.setTemplateId(esignTemplate.getId());
        contract.setContractStatus(ContractStatusEnum.NOT_SIGNED.code());
        if (ObjectUtil.equals(contract.getContractType(), ContractTypeEnum.ESIGN_TYPE.code())) {
            //填充参数,生成签署文件
            ContractParamFillRequestDTO contractParamFillRequestDTO = new ContractParamFillRequestDTO();
            contractParamFillRequestDTO.setName(contract.getContractName() + ".pdf");
            contractParamFillRequestDTO.setTemplateId(esignTemplate.getEsignFileId());
            contractParamFillRequestDTO.setContent(templateData);

            try {
                ContractParamFillResponseDTO paramFillResponseDTO = eSignProvider.doFillContentTemplate(contractParamFillRequestDTO);
                //回写参数文件id
                contract.setEsignFileId(paramFillResponseDTO.getFileId());
                contract.setEsignFileName(paramFillResponseDTO.getFileName());
            } catch (Exception e) {
                throw new BusinessException(ResultEnum.PARAM_ERROR, e.getMessage());
            }
        }
        //填充,保证插入
        contract.setContractImageSignature("");
        contract.setContractImageHtml("");
        contract.setContractTempUrl("");
        if (1 == flag) {
            //更新合同
            monthContractSignRecordRepository.updateBySignRecordId(appMonthContractSignRecordConverter.vo2MonthContractSignRecordEntity(contract));
        } else {
            //保存合同
            contract.setCreatedAt(System.currentTimeMillis() / 1000);
            contract.setId(monthContractSignRecordRepository.create(appMonthContractSignRecordConverter.vo2MonthContractSignRecordReq(contract)));
        }
        return Result.success(contract);
    }

    /**
     * check 状态
     *
     * @param createAccessoryContractQuery
     */
    private MonthContractSignRecordVO checkContractAccessoryStatus(CreateAccessoryContractReq createAccessoryContractQuery) {
        MonthContractSignRecordReq monthContractSignRecordReq = new MonthContractSignRecordReq();
        monthContractSignRecordReq.setTemplateContractTypes(Collections.singletonList(createAccessoryContractQuery.getTemplateContractType()));
        monthContractSignRecordReq.setGuideId(Long.valueOf(createAccessoryContractQuery.getOrderId()));
        monthContractSignRecordReq.setTemplateType(createAccessoryContractQuery.getTemplateType());
        MonthContractSignRecordVO monthContractSignRecordVO = this.queryByCondition(monthContractSignRecordReq);
        if (Objects.nonNull(monthContractSignRecordVO) && ContractStatusEnum.SIGNED.code().equals(monthContractSignRecordVO.getContractStatus())) {
            throw new BusinessException(ResultEnum.PARAM_ERROR, TemplateContractTypeEnum.fromCode(createAccessoryContractQuery.getTemplateContractType()) + "已签署");
        }

        return monthContractSignRecordVO;
    }
}

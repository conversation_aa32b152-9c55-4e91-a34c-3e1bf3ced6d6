package com.stbella.order.server.context.component.assembler;

import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.server.context.component.spi.CustomerQuerySpi;
import com.stbella.platform.order.api.req.CreateOrderReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.SnowballExtensionInvoker;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

/**
 * @Author: ji<PERSON><PERSON>an
 * @CreateTime: 2024-05-29  15:20
 * @Description: 客户信息组装器
 */
@Component
@Slf4j
@SnowballComponent(name = "客户信息组装器", desc = "查询客户信息")
public class CustomerAssembler implements IExecutableAtom<FlowContext> {
    @Override
    public void run(FlowContext bizContext) {
        CreateOrderReq req = bizContext.getAttribute(CreateOrderReq.class);
        HeOrderUserSnapshotEntity userSnapshotEntity = SnowballExtensionInvoker.invoke(CustomerQuerySpi.class,req);

        //保存到上下文，组装到OrderEntity 时使用。
        bizContext.setAttribute(HeOrderUserSnapshotEntity.class,userSnapshotEntity);

    }
}

package com.stbella.order.server.context.service.gift;

import com.stbella.core.result.Result;
import com.stbella.order.common.utils.BPCheckUtil;
import com.stbella.order.domain.repository.OrderGiftExtendRepository;
import com.stbella.platform.order.api.gift.GiftCommandService;
import com.stbella.platform.order.api.gift.req.PassGiftCommand;
import com.stbella.platform.order.api.gift.req.RejectGiftCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author: ji<PERSON><PERSON>an
 * @CreateTime: 2024-11-14  10:59
 * @Description: 礼赠命令服务
 */
@Service
@DubboService
@Slf4j
public class GiftCommandServiceImpl implements GiftCommandService {

    @Resource
    OrderGiftExtendRepository orderGiftExtendRepository;

    /**
     * 产康礼赠审核通过
     *
     * @param command
     */
    @Override
    public Result<Void> pass(PassGiftCommand command) {
        BPCheckUtil.checkEmptyInBean(new String[]{"batchNo"}, command, false);
        orderGiftExtendRepository.takeEffectByBatchNo(command.getBatchNo());
        return Result.success();
    }

    /**
     * 产康礼赠审核拒绝
     *
     * @param command
     */
    @Override
    public Result<Void> reject(RejectGiftCommand command) {
        BPCheckUtil.checkEmptyInBean(new String[]{"batchNo"}, command, false);
        orderGiftExtendRepository.deleteByBatchNo(command.getBatchNo());
        return Result.success();
    }
}

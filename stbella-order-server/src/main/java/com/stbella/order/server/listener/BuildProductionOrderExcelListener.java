package com.stbella.order.server.listener;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.mail.MailUtils;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.server.context.component.export.ProductionOrderExportAssembler;
import com.stbella.order.server.context.component.export.ProductionOrderExportDTO;
import com.stbella.order.server.order.order.req.OrderExportReq;
import com.stbella.order.infrastructure.oss.OSSFactory;
import com.stbella.order.server.oss.response.OssUploadResponse;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 生成产康订单excel
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class BuildProductionOrderExcelListener {

    @Autowired
    ProductionOrderExportAssembler productionOrderExportAssembler;

    @Resource
    private MailUtils mailUtils;

    @Resource
    RuleLinkClient ruleLinkClient;

    /**
     * 查询产康订单数据，生成excel，上传到oss
     * @param event
     */
    @Async
    @EventListener
    public void build(OrderExportReq event) {

        orderExportProcess(event);
    }

    public void orderExportProcess(OrderExportReq event) {

        log.info("收到事件指令，开始生成数据{}", JSONUtil.toJsonStr(event));

        String fileName = "productionOrderExport-"+ DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
        String path = "stbella/order/excel/" + fileName + ".xlsx";
        event.setSkipSplit(0);
        List<ProductionOrderExportDTO> assemble = productionOrderExportAssembler.assemble(event);

        log.info("开始上传腾讯云服务器");
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        EasyExcel.write(byteArrayOutputStream, ProductionOrderExportDTO.class).sheet("sheet1").doWrite(assemble);

        InputStream inputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());

        OssUploadResponse ossUploadResponse = OSSFactory.build().upload(inputStream, path);
        if (!ossUploadResponse.isSuccess()) {
            log.info("上传腾讯云服务器失败,msg={}", ossUploadResponse.getMsg());
            throw new BusinessException(ResultEnum.FILE_ERROR.getCode(), "上传腾讯服务器失败");
        }

        log.info("上传腾讯云服务器成功, 开始发送邮件,path={}", ossUploadResponse.getUrl());

        if (Objects.isNull(event.getBizCode())){
            event.setBizCode(1);;
        }

        String configCode = event.getBizCode() == 1 ? "production_order_spilt_receiver" : "production_order_week_receiver";
        Map<String, String> factMap = new HashMap<>();
        factMap.put("configCode",configCode);
        ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req("systemConfig", factMap);

        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
        // simpleRuleValue 按逗号分成数组
        String[] to = hitRuleVo.getSimpleRuleValue().split(",");
        String subject = "产康订单及产康礼赠导出";
        String content = "请点击链接下载文件：".concat("\n").concat(ossUploadResponse.getUrl());
        if (Objects.nonNull(event.getBizCode())){
            String lastWeek = "上周四12点到本周一12点之间的核销数据";
            String week = "本周一12点到本周四12点之间的核销数据";
            String month = "本月一号0点至当前时间产康订单的核销信息";
            if (event.getBizCode() == 2){
                content = lastWeek.concat("\n").concat(content);
            }
            if (event.getBizCode() == 3){
                content = week.concat("\n").concat(content);
            }
            if (event.getBizCode() == 4){
                content = month.concat("\n").concat(content);
            }
        }
        mailUtils.sendSimpleMail(to, subject, content);
    }


}

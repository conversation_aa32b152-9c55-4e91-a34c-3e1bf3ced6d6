package com.stbella.order.server.context.component.validator;

import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.repository.OrderRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

@Slf4j
@Component
@RefreshScope
@SnowballComponent(name = "订单全部成功后续处理")
public class OrderRefundAfterValidator implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderRepository orderRepository;

    @Override
    public boolean condition(FlowContext context) {
        return (boolean) context.getAttribute(OtherConstant.CONTINUE);
    }

    @Override
    public void run(FlowContext bizContext) {
        HeOrderRefundEntity refundEntity = bizContext.getAttribute(HeOrderRefundEntity.class);
        HeOrderEntity orderEntity = orderRepository.getByOrderId(refundEntity.getOrderId());
        bizContext.setAttribute(HeOrderRefundEntity.class, refundEntity);
        bizContext.setAttribute(HeOrderEntity.class, orderEntity);
        bizContext.setAttribute(OtherConstant.DEPOSIT, orderEntity.getOrderType().equals(OmniOrderTypeEnum.DEPOSIT_ORDER.code()));
    }


}

package com.stbella.order.server.contract.month.controller;

import com.stbella.core.annotations.PreventDuplication;
import com.stbella.core.result.Result;
import com.stbella.order.common.constant.RedisConstant;
import com.stbella.order.server.contract.provider.month.ESignProvider;
import com.stbella.order.server.contract.req.CanPaperContractQuery;
import com.stbella.order.server.contract.req.CreateAccessoryContractReq;
import com.stbella.order.server.contract.req.CreateMainContractReq;
import com.stbella.order.server.contract.req.CreatePaperContractReq;
import com.stbella.order.server.contract.req.OrderParamHistoryPushDTO;
import com.stbella.order.server.contract.req.SavePaperContractReq;
import com.stbella.order.server.contract.req.*;
import com.stbella.order.server.contract.res.CanContractSignPaperVO;
import com.stbella.order.server.contract.res.ContractSignPaperVO;
import com.stbella.order.server.contract.service.month.ContractSignRecordPaperService;
import com.stbella.order.server.contract.service.month.MonthContractSignRecordService;
import com.stbella.order.server.contract.service.month.MonthOrderParamHistoryService;
import com.stbella.order.server.order.month.req.OrderContractQuery;
import com.stbella.order.server.order.month.res.ContractSignRecordVO;
import com.stbella.order.server.order.month.res.MonthContractSignRecordVO;
import com.stbella.order.server.utils.BigDecimalUtil;
import com.stbella.redisson.DistributedLocker;

import org.redisson.api.RLock;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;
import javax.validation.Valid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

@Api(tags = "【废弃】-已迁移合同stbella-contract服务-月子母婴合同中心")
@Validated
@RequestMapping("/order/month/contract")
@RestController
public class MonthContractController {

    @Resource
    private MonthContractSignRecordService signRecordService;

    @Resource
    private MonthOrderParamHistoryService orderParamHistoryService;

    @Resource
    private ContractSignRecordPaperService signRecordPaperService;

    @Resource
    private DistributedLocker redisson;
    @Resource
    private ESignProvider eSignProvider;

    @ApiOperation(value = "添加纸质合同--(添加合同、发起审批都在这个接口中)")
    @PostMapping("/add/paper")
    @PreventDuplication
    public Result<Integer> doPaperContract(@Valid @RequestBody CreatePaperContractReq paperContractParam) {
        return signRecordPaperService.createPaperContract(paperContractParam);
    }

    @ApiOperation(value = "纸质合同查看详情")
    @GetMapping("/detail/paper")
    public Result<ContractSignPaperVO> queryByPaperId(Integer contractId) {
        return signRecordPaperService.queryById(contractId);
    }

    @ApiOperation(value = "删除纸质合同")
    @PostMapping("/delete/paper")
    public Result<Boolean> deleteByPaperId(@Valid @RequestBody SavePaperContractReq req) {
        return signRecordPaperService.deleteById(req);
    }

    @ApiOperation(value = "纸质合同列表配置")
    @PostMapping("/can/paper")
    public Result<List<CanContractSignPaperVO>> queryPaperList(@Valid @RequestBody CanPaperContractQuery canPaperContractQuery) {
        return signRecordPaperService.queryCanPaperList(canPaperContractQuery);
    }

    /**
     * 接收订单id生成合同
     * 合同列表查询接口
     * 合同详情接口
     */
    @ApiOperation(value = "主合同护理服务合同书生成接口")
    @PostMapping("/createContract")
    @PreventDuplication
    public Result<MonthContractSignRecordVO> createContract(@Valid @RequestBody CreateMainContractReq req) {
        RLock lock = redisson.lock(MessageFormat.format(RedisConstant.MONTH_ORDER_CONTRACT_TEMPLATE_TYPE_LOCK, req.getTemplateContractType(), req.getOrderId()));
        try {
            return signRecordService.createContract(req);
        } finally {
            if (lock != null && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    @ApiOperation(value = "模拟修改合同参数,仅供debug使用")
    @ApiIgnore
    @PostMapping("/updateContract")
    public Result<Boolean> updateContract(@RequestBody OrderParamHistoryPushDTO orderParamHistoryPushDTO) {
        //TODO 推送订单参数变更历史
        return Result.success(orderParamHistoryService.pushOrderParamHistory(orderParamHistoryPushDTO));
    }

    @ApiOperation(value = "预约协议/解除协议/附属合同生成接口")
    @PostMapping("/createAccessoryContract")
    public Result<MonthContractSignRecordVO> createAccessoryContract(@Valid @RequestBody CreateAccessoryContractReq createAccessoryContractQuery) {
        String lockKey = MessageFormat.format(RedisConstant.MONTH_ORDER_CONTRACT_TEMPLATE_TYPE_LOCK, createAccessoryContractQuery.getTemplateContractType(), createAccessoryContractQuery.getOrderId());
        RLock lock = redisson.lock(lockKey, TimeUnit.SECONDS, 10);
        try {
            return signRecordService.createAccessoryContract(createAccessoryContractQuery);
        } catch (Throwable e) {
            return Result.failed(e.getMessage());
        } finally {
            if (lock != null && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    @ApiOperation(value = "可重入锁测试")
    @PostMapping("/test")
    public Result<String> test() {
        String lockKey = "lock123";
        if (redisson.tryLock(lockKey, TimeUnit.SECONDS, 120,130)) {
            try {
                return Result.success("test6 success--" + Thread.currentThread().getId() + "--" + Thread.currentThread().getName());
            } catch (Throwable e) {
                return Result.success("test6 error" + Thread.currentThread().getId() + "--" + Thread.currentThread().getName());
            } finally {
                System.out.println("test6 finally 1");
            }
        }
        return Result.success("test7 success" + Thread.currentThread().getId() + "--" + Thread.currentThread().getName());
    }

    @ApiOperation(value = "可重入锁测试")
    @PostMapping("/test2")
    public Result<String> test2() {
        BigDecimal bigDecimal = BigDecimalUtil.divideRoundingModeAndScale(new BigDecimal(100), new BigDecimal(110), BigDecimal.ROUND_DOWN, 2);
        System.out.println("bigDecimal = " + bigDecimal);
        return Result.success("test2 success--" + Thread.currentThread().getId() + "--" + Thread.currentThread().getName());
    }

    @ApiOperation(value = "E签宝三要素")
    @GetMapping("/telecom3Factors")
    public Result<Boolean> telecom3Factors(String name, String idNo, String mobileNo) {
        boolean f = eSignProvider.telecom3Factors(name, idNo, mobileNo);
        return Result.success(f);
    }
    @ApiOperation(value = "根据合同ID获取合同填充参数")
    @GetMapping("/getContractParam/contractId")
    public Result<HashMap<String, String>> getContractParamByContractId(Integer contractId) {
        return Result.success(orderParamHistoryService.getContractParamByContractId(contractId));
    }

    @ApiOperation(value = "老合同签署")
    @PostMapping("/oldContractSign")
    public Result oldContractSign(@Valid @RequestBody OldContractSignReq oldContractSignReq) {
        signRecordService.oldContractSign(oldContractSignReq);
        return Result.success();
    }

    @ApiOperation(value = "重置合同签署状态")
    @GetMapping("/resetContract/contractId")
    public Result resetContract(Integer contractId) {
        signRecordService.resetContract(contractId);
        return Result.success();
    }

    @ApiOperation(value = "客户签署合同列表")
    @PostMapping(value = "/contract-signed-list")
    public Result<List<ContractSignRecordVO>> contractSignedList(@Valid @RequestBody OrderContractQuery query) {
        List<ContractSignRecordVO> list = signRecordService.contractSignRecordListByClientId(query);
        return Result.success(list);
    }

}

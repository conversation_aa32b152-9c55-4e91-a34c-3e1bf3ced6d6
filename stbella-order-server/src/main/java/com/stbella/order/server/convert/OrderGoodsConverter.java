package com.stbella.order.server.convert;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.month.server.request.OrderGiftExtendRequest;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.server.context.component.export.ProductionOrderExportDTO;
import com.stbella.order.server.order.ProductionOrderGoodsModel;
import com.stbella.order.server.order.month.res.OrderGoodsInfoVO;
import com.stbella.platform.order.api.refund.res.QueryRefundGoodsRes;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import com.stbella.platform.order.api.res.SkuAdditionalInfo;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: jijunjian
 * @CreateTime: 2023-07-27  16:19
 * @Description: 订单明细转化器
 */
@Mapper(componentModel = "spring")
public interface OrderGoodsConverter {

    /**
     * 产康sku 转导出模型
     *
     * @param model
     * @return
     */
    @Mappings({
            @Mapping(target = "productionName", source = "goodsName"),
            @Mapping(target = "orderTime", expression = "java(cn.hutool.core.date.DateUtil.formatDateTime(new java.util.Date(model.getCreatedAt()*1000L)))"),
            @Mapping(target = "percentFirstTime", expression = "java(cn.hutool.core.date.DateUtil.formatDateTime(new java.util.Date(model.getPercentFirstTime()*1000L)))"),
            @Mapping(target = "assetStartTime", expression = "java(cn.hutool.core.date.DateUtil.formatDateTime(new java.util.Date(model.getValidStartTime()*1000L)))"),
            @Mapping(target = "validEndTime", expression = "java(cn.hutool.core.date.DateUtil.formatDateTime(new java.util.Date(model.getValidEndTime()*1000L)))"),
            @Mapping(target = "productionAmount", source = "productionAmountPay"),
            @Mapping(target = "num", source = "goodsNum"),
            @Mapping(target = "orderId", source = "orderId"),
            @Mapping(target = "skuPrice", source = "price"),
            @Mapping(target = "totalPayAmount", source = "orderAmount"),
            @Mapping(target = "orderProductionId", source = "id"),
            @Mapping(target = "assetStatusCode", source = "status"),
            @Mapping(target = "assetStatus", expression = "java(com.stbella.order.common.enums.production.GoodsAssertEnum.fromCode(model.getStatus()))"),
    })
    ProductionOrderExportDTO goodsSku2Export(ProductionOrderGoodsModel model);

    /**
     * 产康sku 转导出模型
     *
     * @param modelList
     * @return
     */
    List<ProductionOrderExportDTO> goodsSku2ExportList(List<ProductionOrderGoodsModel> modelList);

    /**
     * 购物车商品 转 订单商品
     *
     * @param model
     * @return
     */
    @Mappings({
            @Mapping(target = "goodsName", source = "skuName"),
            @Mapping(target = "skuName", source = "specification"),
            @Mapping(target = "goodsType", source = "assetType"),
            @Mapping(target = "roomId", source = "roomType"),
            @Mapping(target = "goodsImage", source = "skuUrl"),
            @Mapping(target = "definedProperty", source = "propertyValue"),
            @Mapping(target = "goodsNum", expression = "java(model.getNum() * model.getSkuNum() * model.getParentNum())"),
            @Mapping(target = "skuNum", source = "skuNum"),
            @Mapping(target = "num", source = "num"),
            @Mapping(target = "goodsCost", expression = "java(com.stbella.order.common.utils.AmountChangeUtil.changeY2FFoInt(model.getCostPrice()))"),
            @Mapping(target = "goodsPriceOrgin", expression = "java(com.stbella.order.common.utils.AmountChangeUtil.changeY2FFoInt(model.getPrice().divide(java.math.BigDecimal.valueOf(model.getSkuNum()), 4, java.math.RoundingMode.HALF_UP)))"),
            @Mapping(target = "gift", expression = "java(model.getGift() == true ? 1: 0)"),
    })
    HeOrderGoodsEntity skuDetail2OrderGoods(SkuDetailInfo model);

    /**
     * 节假日等服务费 转 订单商品
     *
     * @param model
     * @return
     */
    @Mappings({
            @Mapping(target = "goodsName", source = "name"),
            @Mapping(target = "goodsNum", source = "num"),
            @Mapping(target = "assetType", source = "goodsType"),
            @Mapping(target = "goodsCost", expression = "java(com.stbella.order.common.utils.AmountChangeUtil.changeY2FFoInt(model.getCost()))"),
            @Mapping(target = "goodsPriceOrgin", expression = "java(com.stbella.order.common.utils.AmountChangeUtil.changeY2FFoInt(model.getPrice()))"),
            @Mapping(target = "allocationOriginPrice", expression = "java(com.stbella.order.common.utils.AmountChangeUtil.changeY2FFoInt(model.getPrice()))"),
            @Mapping(target = "totalAllocationOriginPrice", expression = "java(com.stbella.order.common.utils.AmountChangeUtil.changeY2FFoInt(model.getPrice())*model.getNum())"),
            @Mapping(target = "gift", constant = "0"),
    })
    HeOrderGoodsEntity additional2OrderGoods(SkuAdditionalInfo model);

    /**
     * 订单条目转礼赠
     *
     * @param model
     * @return
     */
    @Mappings({
            @Mapping(target = "price", source = "goodsPriceOrgin"),
    })
    OrderGiftExtendRequest orderGoods2OrderGift(HeOrderGoodsEntity model);

    /**
     * 订单条目转礼赠 list
     *
     * @param list
     * @return
     */
    List<OrderGiftExtendRequest> orderGoods2OrderGiftList(List<HeOrderGoodsEntity> list);

    @Mapping(target = "orderGoodsId", source = "id")
    @Mapping(target = "refundGoodsNum", constant = "0")
    QueryRefundGoodsRes toQueryRefundGoodsRes(HeOrderGoodsEntity orderGoods);


    default BigDecimal toYuan(Integer amount) {
        return AmountChangeUtil.changeF2Y(amount);
    }


    @Mapping(target = "orderGoodsId", source = "orderGoods.id")
    @Mapping(target = "allocationOriginPrice", expression = "java(toYuan(orderGoods.getAllocationOriginPrice()))")
    @Mapping(target = "goodsPrice", expression = "java(toYuan(orderGoods.getGoodsPriceOrgin()))")
    @Mapping(target = "refundGoodsNum", constant = "0")
    @Mapping(target = "child", expression = "java(mapChildren(orderGoods, children, goodsMap))")
    @Mapping(target = "additionList", expression = "java(mapAdditions(orderGoods, additions))")
    @Mapping(target = "goodsNum", expression = "java(calculateGoodsNum(orderGoods, children))")
    @Mapping(target = "payAmount", expression = "java(calculatePayAmount(orderGoods, children))")
    QueryRefundGoodsRes toQueryRefundGoodsResWithChildren(HeOrderGoodsEntity orderGoods,
                                                          List<HeOrderGoodsEntity> children,
                                                          List<HeOrderGoodsEntity> additions,
                                                          @Context Map<String, List<HeOrderGoodsEntity>> goodsMap);

    List<QueryRefundGoodsRes> toQueryRefundGoodsResList(List<HeOrderGoodsEntity> orderGoods);

    default List<QueryRefundGoodsRes> mapChildren(HeOrderGoodsEntity orderGoods,
                                                  List<HeOrderGoodsEntity> children,
                                                  Map<String, List<HeOrderGoodsEntity>> goodsMap) {
        return children.stream()
                .map(child -> {
                    List<HeOrderGoodsEntity> grandChildren = goodsMap.getOrDefault(child.getOrderGoodsSn(), Collections.emptyList())
                            .stream()
                            .filter(g -> !g.isAddition())
                            .collect(Collectors.toList());

                    List<HeOrderGoodsEntity> childAdditions = goodsMap.getOrDefault(child.getOrderGoodsSn(), Collections.emptyList())
                            .stream()
                            .filter(HeOrderGoodsEntity::isAddition)
                            .collect(Collectors.toList());

                    return toQueryRefundGoodsResWithChildren(child, grandChildren, childAdditions, goodsMap);
                })
                .collect(Collectors.toList());
    }

    default List<QueryRefundGoodsRes> mapAdditions(HeOrderGoodsEntity orderGoods, List<HeOrderGoodsEntity> additions) {
        return additions.stream()
                .map(addition -> {
                    QueryRefundGoodsRes res = toQueryRefundGoodsRes(addition);
                    res.setPayAmount(AmountChangeUtil.changeF2Y(orderGoods.getPayAmount()));
                    return res;
                })
                .collect(Collectors.toList());
    }


    default Integer calculateGoodsNum(HeOrderGoodsEntity orderGoods, List<HeOrderGoodsEntity> children) {
        if (ObjectUtil.notEqual(CombineTypeEnum.COMBINE.code(), orderGoods.getType())) {
            return orderGoods.getGoodsNum();
        }
        return children.stream().mapToInt(HeOrderGoodsEntity::getGoodsNum).sum();
    }

    default BigDecimal calculatePayAmount(HeOrderGoodsEntity orderGoods, List<HeOrderGoodsEntity> children) {
        if (ObjectUtil.notEqual(CombineTypeEnum.COMBINE.code(), orderGoods.getType())) {
            return AmountChangeUtil.changeF2Y(orderGoods.getPayAmount());
        }
        return children.stream()
                .map(HeOrderGoodsEntity::getPayAmount)
                .map(AmountChangeUtil::changeF2Y)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    List<OrderGoodsInfoVO> entity2DTO(List<HeOrderGoodsEntity> goodsEntities);
}

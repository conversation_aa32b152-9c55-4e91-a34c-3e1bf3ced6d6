package com.stbella.order.server.consumer;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.stbella.core.enums.BusinessEnum;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.HeUserProductionAmountListRepository;
import com.stbella.order.domain.repository.HeUserProductionAmountPiLogRepository;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.domain.utils.PayTypeConvertUtil;
import com.stbella.order.server.async.AsyncOrder;
import com.stbella.order.server.convert.OrderNutritionConvert;
import com.stbella.order.server.convert.PayRecordConvert;
import com.stbella.order.server.manager.CustomerBehaviorManager;
import com.stbella.order.server.order.cts.service.OrderFacade;
import com.stbella.order.server.order.month.entity.OrderPayRecordPO;
import com.stbella.order.server.order.month.enums.CtsPayTypeEnum;
import com.stbella.order.server.order.month.enums.PayRecordEnum;
import com.stbella.order.server.order.month.enums.PayStatusEnum;
import com.stbella.order.server.order.month.enums.RecordTypeEnum;
import com.stbella.order.server.order.month.request.pay.PayNotityRequest;
import com.stbella.order.server.order.month.service.OrderPayRecordService;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.order.server.order.nutrition.service.OrderNutritionMealService;
import com.stbella.order.server.strategy.pay.PayNotificationStrategyFactory;
import com.stbella.order.server.utils.AccountTypeUtil;
import com.stbella.pay.server.alipay.enums.AccountTypeEnum;
import com.stbella.pay.server.entity.mq.PayNotifyMq;
import com.stbella.pay.server.paymq.service.PayMqService;
import com.stbella.pulsar.consumer.PulsarConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.pulsar.client.api.Message;
import org.apache.pulsar.client.api.MessageId;
import org.apache.pulsar.client.api.SubscriptionType;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.FlowIdentity;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

@Component
@Slf4j
public class OrderPayConsumer {

    @Resource
    private OrderPayRecordService orderPayRecordService;
    @Resource
    private PayRecordConvert payRecordConvert;
    @Resource
    private OrderNutritionMealService orderNutritionMealService;
    @Resource
    private OrderNutritionConvert orderNutritionConvert;
    @DubboReference
    private PayMqService payMqService;
    @Resource
    private OrderFacade orderFacade;
    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private AsyncOrder asyncOrder;
    @Resource
    private CustomerBehaviorManager customerBehaviorManager;
    @Resource
    private HeUserProductionAmountListRepository heUserProductionAmountListRepository;
    @Resource
    private HeUserProductionAmountPiLogRepository heUserProductionAmountPiLogRepository;
    @Resource
    private PayNotificationStrategyFactory payNotificationStrategyFactory;


    /**
     * 支付成功mq消费
     */
    @PulsarConsumerListener(destination = "persistent://pulsar-44k4paxzz5vg/nameSpace/pay_notify", subscriptionName = "pay_notify", subscriptionType = SubscriptionType.Shared)
    public void getMsg(Message message) {

        String jsons = new String(message.getData());
        PayNotifyMq payNotifyMq = JSONUtil.toBean(jsons, PayNotifyMq.class);

        log.info("支付回调消费者消费：" + jsons);

        if (payNotifyMq.getPayType().equals(CtsPayTypeEnum.WX.getCode())) {
            try {
                payNotifyMq.setPayAmount(new BigDecimal(RMBUtils.changeF2Y(payNotifyMq.getPayAmount().toString())));
            } catch (Exception e) {
                log.error("支付回调金额转换失败，messageId：{},data：{}", message.getMessageId(), payNotifyMq.getPayAmount());
                throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, "支付回调金额转换失败");
            }
        }
        //本地流水号
        String outTradeNo = payNotifyMq.getOutTradeNo();
        PayRecordEnum accountTypeByLocalTransactionalNo = AccountTypeUtil.getAccountTypeByLocalTransactionalNo(outTradeNo);
        boolean updatePayRecord = false;
        //更新支付记录
        PayNotityRequest payNotityRequest = payRecordConvert.payRecordConvert2PayNotityRequest(payNotifyMq);
        if (accountTypeByLocalTransactionalNo.getCode().equals(AccountTypeEnum.BEI_KANG.getCode())) {
            HeIncomeRecordEntity heIncomeRecordEntity = incomeRecordRepository.getRecordByIncomeSn(outTradeNo);

            Integer orderId = heIncomeRecordEntity.getOrderId();
            //非老版本的押金订单
            if (!Objects.equals(0, orderId)) {
                HeOrderEntity orderEntity = orderRepository.getByOrderId(orderId);
                if (Objects.nonNull(orderEntity) && orderEntity.isNewOrder()) {
                    log.info("新订单支付回调 orderSn={}", orderEntity.getOrderSn());
                    payNotityRequest.setPayType(PayTypeConvertUtil.payTypeConvertNew2Old(payNotityRequest.getPayType()));
                    callNewProcess(orderEntity, payNotityRequest);

                    //获取messageId
                    MessageId messageId = message.getMessageId();
                    //更新
                    payMqService.updateStatusByMessageId(String.valueOf(messageId));
                    return;
                }
            }

            heIncomeRecordEntity.setParams(payNotityRequest.getRequestBody());
            heIncomeRecordEntity.setStatus(payNotityRequest.getPayStatus());
            heIncomeRecordEntity.setPayTime((int) (payNotifyMq.getPayTime().getTime() / 1000));
            //支付单位-分
            heIncomeRecordEntity.setIncome(payNotityRequest.getPayAmount().multiply(new BigDecimal(100)).intValue());
            //新payType转为旧payType
            heIncomeRecordEntity.setPayType(PayTypeConvertUtil.payTypeConvertNew2Old(payNotityRequest.getPayType()));
            heIncomeRecordEntity.setTransactionId(payNotityRequest.getTransactionalNo());
            heIncomeRecordEntity.setUpdatedAt(payNotifyMq.getPayTime().getTime() / 1000);
            updatePayRecord = incomeRecordRepository.updateRecord(heIncomeRecordEntity);
        } else {
            LambdaQueryWrapper<OrderPayRecordPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(OrderPayRecordPO::getLocalTransactionalNo, outTradeNo)
                    .eq(OrderPayRecordPO::getRecordType, RecordTypeEnum.PAY.getCode());
            OrderPayRecordPO orderPayRecordPO = orderPayRecordService.getBaseMapper().selectOne(lambdaQueryWrapper);

            if (orderPayRecordPO.getPayStatus().equals(PayStatusEnum.PAY_STATUS_SUCCESS.getCode())) {
                return;
            }
            updatePayRecord = orderPayRecordService.updatePayRecord(payNotityRequest, orderPayRecordPO);
            payNotityRequest.setOrderNo(orderPayRecordPO.getOrderNo());
        }
        log.info("支付回调更新支付记录成功，messageId：{},data：{}", message.getMessageId(), payNotifyMq);

        if (updatePayRecord && payNotityRequest.getPayStatus().equals(PayStatusEnum.PAY_STATUS_SUCCESS.getCode())) {

            boolean editOrderInfo = payNotificationStrategyFactory.processPayment(accountTypeByLocalTransactionalNo, payNotityRequest);
            if (!editOrderInfo) {
                log.error("支付回调消费失败:，messageId：{},data：{}", message.getMessageId(), payNotifyMq);
                throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, "支付回调消费失败");
            }
            log.info("支付回调更新订单成功，messageId：{},data：{}", message.getMessageId(), payNotifyMq);


            //获取messageId
            MessageId messageId = message.getMessageId();
            //更新
            payMqService.updateStatusByMessageId(String.valueOf(messageId));

        } else {
            log.error("支付回调消费失败，messageId：{},data：{}", message.getMessageId(), payNotifyMq);
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, "支付回调消费失败");
        }

    }

    /**
     * 新支付流程
     *
     * @param order
     * @param payNotityRequest
     */
    protected void callNewProcess(HeOrderEntity order, PayNotityRequest payNotityRequest) {

        BusinessEnum bu = BusinessEnum.getEnumByCode(order.getBu());
        OmniPayTypeEnum payTypeEnum = OmniPayTypeEnum.getByCode(payNotityRequest.getPayType());

        FlowIdentity identity = FlowIdentity.builder().bizActivity(BizActivityEnum.SUCCEED_PAY.code())
                .idSlice(bu.name())
                .idSlice(payTypeEnum.name())
                .build();

        FlowContext context = new FlowContext();
        context.setAttribute(PayNotityRequest.class, payNotityRequest);
        context.setAttribute(HeOrderEntity.class, order);
        SnowballFlowLauncher.fire(identity, context);

    }

}

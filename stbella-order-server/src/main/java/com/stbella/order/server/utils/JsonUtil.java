package com.stbella.order.server.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * Json帮助类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2018-11-05 23:48
 */
@Slf4j
public class JsonUtil {
    private static final ObjectMapper mapper = new ObjectMapper();

    public static final String DATE_FORMAT_SEARCHDATE = "yyyy-MM-dd'T'HH:mm:ss'Z'";

    static {
        mapper.disable(MapperFeature.SORT_PROPERTIES_ALPHABETICALLY);
        mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    }

    /**
     * 序列化为json字符串
     *
     * @param obj
     * @return
     */
    public static String write(Object obj) {
        if (obj == null) {
            return "null";
        }

        try {
            return mapper.writeValueAsString(obj);
        } catch (Exception var2) {
            throw new RuntimeException(var2);
        }
    }

    /**
     * 序列化异常为json字符串
     *
     * @param obj
     * @return
     */
    public static String write(Throwable obj) {
        if (obj == null) {
            return "null";
        }

        try {
            return mapper.writeValueAsString(mailFormat(ThrowableUtil.getStackTrace(obj)));
        } catch (Exception var2) {
            throw new RuntimeException(var2);
        }
    }

    /**
     * 序列化为json字符串，带有格式，可读性比较好
     *
     * @param obj
     * @return
     */
    public static String writePretty(Object obj) {
        if (obj == null) {
            return "null";
        }

        try {
            return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(obj);
        } catch (Exception var2) {
            throw new RuntimeException(var2);
        }
    }

    /**
     * 反序列化为对象
     *
     * @param json
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> T read(String json, Class<T> clazz) {
        if (StringUtils.isBlank(json)) {
            return null;
        } else {
            try {
                return mapper.readValue(json, clazz);
            } catch (Exception var3) {
                throw new RuntimeException(var3);
            }
        }
    }

    /**
     * 反序列化含有范型类
     *
     * @param json
     * @param typeReference
     * @param <T>
     * @return
     */
    @SneakyThrows
    public static <T> T read(String json, TypeReference<T> typeReference) {
        if (StringUtils.isBlank(json)) {
            return null;
        }

        return mapper.readValue(json, typeReference);
    }

    /**
     * 反序列化为列表
     *
     * @param json
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> List<T> readList(String json, Class<T> clazz) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        CollectionType arrayType = mapper.getTypeFactory().constructCollectionType(ArrayList.class, clazz);
        try {
            return mapper.readValue(json, arrayType);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 暴露objectMapper
     *
     * @return
     */
    public static ObjectMapper getObjectMapper() {
        return mapper;
    }

    public static String getStringByJsonObject(JSONObject jsonObject, String key) {
        if (jsonObject.containsKey(key) && Objects.nonNull(jsonObject.get(key))) {
            return jsonObject.getString(key);
        }
        return "";
    }

    public static boolean canJsonObject(JSONObject jsonObject, String key) {
        return jsonObject.containsKey(key) && Objects.nonNull(jsonObject.get(key));
    }

    public static Integer getIntegerByJsonObject(JSONObject jsonObject, String key) {
        if (jsonObject.containsKey(key) && Objects.nonNull(jsonObject.get(key))) {
            return jsonObject.getInteger(key);
        }
        return null;
    }

    public static Float getFloatByJsonObject(JSONObject jsonObject, String key) {
        if (jsonObject.containsKey(key) && Objects.nonNull(jsonObject.get(key))) {
            return jsonObject.getFloat(key);
        }
        return null;
    }

    public static Date getDateByJsonObject(JSONObject jsonObject, String key) {
        if (jsonObject.containsKey(key) && Objects.nonNull(jsonObject.get(key))) {
            return jsonObject.getDate(key);
        }
        return null;
    }

    /**
     * 转换搜索返回的时间
     *
     * @param jsonObject
     * @param key
     * @return
     */
    public static Date getSearchDateByJsonObject(JSONObject jsonObject, String key) {
        if (jsonObject.containsKey(key) && Objects.nonNull(jsonObject.get(key))) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DATE_FORMAT_SEARCHDATE);
            try {
                return simpleDateFormat.parse(String.valueOf(jsonObject.get(key)));
            } catch (ParseException e) {
                log.error("转换搜索返回的处方时间失败!{}", JsonUtil.write(e));
                e.printStackTrace();
            }
        }
        return null;
    }

    public static List<String> getListStringByJsonObject(JSONObject jsonObject, String key) {
        List<String> lists = new ArrayList<>();
        if (jsonObject.containsKey(key) && Objects.nonNull(jsonObject.getJSONArray(key))) {
            JSONArray jsonArray = jsonObject.getJSONArray(key);
            int size = jsonArray.size();
            for (int i = 0; i < size; i++) {
                lists.add(jsonArray.getString(i));
            }
        }
        return lists;
    }

    public static List<Integer> getListIntegerByJsonObject(JSONObject jsonObject, String key) {
        List<Integer> lists = new ArrayList<>();
        if (jsonObject.containsKey(key) && Objects.nonNull(jsonObject.getJSONArray(key))) {
            JSONArray jsonArray = jsonObject.getJSONArray(key);
            int size = jsonArray.size();
            for (int i = 0; i < size; i++) {
                lists.add(jsonArray.getInteger(i));
            }
        }
        return lists;
    }

    public static String mailFormat(String content) {
        content = content.replaceAll("\\n", "<br>").replaceAll("\\t", "&nbsp;&nbsp;&nbsp;&nbsp;");
        return content;
    }
}

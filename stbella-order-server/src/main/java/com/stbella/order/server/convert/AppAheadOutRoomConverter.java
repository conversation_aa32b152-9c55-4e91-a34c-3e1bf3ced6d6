package com.stbella.order.server.convert;

import com.stbella.care.server.care.vo.room.RoomStateQueryDetailInfoVO;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.AheadOutRoomEntity;
import com.stbella.order.server.order.month.req.CreateAheadOutRoomReq;
import com.stbella.order.server.order.month.req.SaveAheadOutRoomReq;
import com.stbella.order.server.order.month.res.AheadOutRoomRefundAmountVO;
import com.stbella.order.server.order.month.res.AheadOutRoomVO;
import com.stbella.order.server.order.month.res.RoomStateQueryVO;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import cn.hutool.core.date.DateUtil;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-12 11:28
 */
@Mapper(componentModel = "spring", imports = {DateUtil.class, AmountChangeUtil.class})
public interface AppAheadOutRoomConverter {

    @Mappings({
            @Mapping(target = "originalAmount", expression = "java(AmountChangeUtil.changeF2Y(entity.getOriginalAmount().longValue()))"),
            @Mapping(target = "babysAmount", expression = "java(AmountChangeUtil.changeF2Y(entity.getBabysAmount().longValue()))"),
            @Mapping(target = "holidayAmount", expression = "java(AmountChangeUtil.changeF2Y(entity.getHolidayAmount().longValue()))"),
            @Mapping(target = "refundedAmount", expression = "java(AmountChangeUtil.changeF2Y(entity.getRefundedAmount().longValue()))"),
            @Mapping(target = "checkOutDate", expression = "java(DateUtil.formatDate(entity.getCheckOutDate()))"),
    })
    AheadOutRoomVO entity2AheadOutRoomVo(AheadOutRoomEntity entity);

    @Mappings({
            @Mapping(target = "originalAmount", expression = "java(null!=req.getOriginalAmount()?AmountChangeUtil.changeY2F(req.getOriginalAmount()).intValue():null)"),
            @Mapping(target = "babysAmount", expression = "java(null!=req.getBabysAmount()?AmountChangeUtil.changeY2F(req.getBabysAmount()).intValue():null)"),
            @Mapping(target = "holidayAmount", expression = "java(null!=req.getHolidayAmount()?AmountChangeUtil.changeY2F(req.getHolidayAmount()).intValue():null)"),
            @Mapping(target = "refundedAmount", expression = "java(null!=req.getRefundedAmount()?AmountChangeUtil.changeY2F(req.getRefundedAmount()).intValue():null)"),
    })
    AheadOutRoomEntity req2AheadOutRoomEntity(CreateAheadOutRoomReq req);

    @Mappings({
            @Mapping(target = "originalAmount", expression = "java(null!=req.getOriginalAmount()?AmountChangeUtil.changeY2F(req.getOriginalAmount()).intValue():null)"),
            @Mapping(target = "babysAmount", expression = "java(null!=req.getBabysAmount()?AmountChangeUtil.changeY2F(req.getBabysAmount()).intValue():null)"),
            @Mapping(target = "holidayAmount", expression = "java(null!=req.getHolidayAmount()?AmountChangeUtil.changeY2F(req.getHolidayAmount()).intValue():null)"),
            @Mapping(target = "refundedAmount", expression = "java(null!=req.getRefundedAmount()?AmountChangeUtil.changeY2F(req.getRefundedAmount()).intValue():null)"),
    })
    AheadOutRoomEntity req2AheadOutRoomEntity(SaveAheadOutRoomReq req);


    /**
     * @param vo
     * @return {@link RoomStateQueryVO}
     */
    RoomStateQueryVO cateVo2RoomStateQueryVo(RoomStateQueryDetailInfoVO vo);


    AheadOutRoomRefundAmountVO outRoomVo2RefundAmountVO(AheadOutRoomVO vo);
}

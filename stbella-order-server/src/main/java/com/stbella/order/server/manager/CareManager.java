
package com.stbella.order.server.manager;

import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.order.month.enums.OrderEventEnum;
import com.stbella.order.server.producer.OrderEventProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 房态服务
 * @date 2023/2/7 17:53
 */
@Component
@Slf4j
public class CareManager {

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderEventProducer orderEventProducer;

    /**
     * 订单自动同步房态
     */
    public void autoCreateFangTai(Integer orderId) {

        HeOrderEntity byOrderId = orderRepository.getByOrderId(orderId);

        orderEventProducer.sendMq(OrderEventEnum.PAY_FIFTY_PERCENT.getCode(), byOrderId.getOrderSn(), null);

//        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(orderId);
//        HeOrderGoodsEntity heOrderGoodsEntity = orderGoodsRepository.getByOrderId(orderId);
//        RoomStateAutoCommitRequest request = new RoomStateAutoCommitRequest();
//        request.setStoreId(Long.valueOf(byOrderId.getStoreId()));
//        request.setRoomTypeId(Long.valueOf(heOrderGoodsEntity.getEcpRoomType()));
//        request.setOrderNo(byOrderId.getOrderSn());
//        request.setCustomerId(Long.valueOf(heOrderUserSnapshotEntity.getClientUid()));
//        request.setCustomerName(heOrderUserSnapshotEntity.getName());
//        request.setMobile(heOrderUserSnapshotEntity.getPhone());
//        //备孕中的要特殊处理成2099-01-01
//        Long wantIn = byOrderId.getWantIn();
//        if (ObjectUtil.isEmpty(byOrderId.getWantIn()) || 0 == wantIn) {
//            wantIn = 4070880000L;
//        }
//        request.setCheckInDate(Instant.ofEpochMilli(wantIn * 1000).atZone(ZoneOffset.ofHours(8)).toLocalDate());
//        request.setCheckOutDate(Instant.ofEpochMilli(wantIn * 1000).atZone(ZoneOffset.ofHours(8)).toLocalDate().plusDays(Long.valueOf(heOrderGoodsEntity.getServiceDays())));
//        request.setOvernightDays(heOrderGoodsEntity.getServiceDays());
//        request.setOperationType(100);
//        // TODO: 2023/2/16  因为房间ecp与小助手房间未打通 ,这里续住取得套餐房型ID`
//        List<HeOrderAdditionalRevenueEntity> heOrderAdditionalRevenueEntities = orderAdditionalRevenueRepository.queryAdditionalByOrderIdAndType(orderId, MonthAdditionalRevenueEnum.STAY_COST.getCode());
//        if (ObjectUtil.isNotEmpty(heOrderAdditionalRevenueEntities)) {
//            List<RoomStateAutoCommitRequest.StayOver> stayOverList = new ArrayList<>();
//            for (HeOrderAdditionalRevenueEntity entity : heOrderAdditionalRevenueEntities) {
//                RoomStateAutoCommitRequest.StayOver stayOver = new RoomStateAutoCommitRequest.StayOver();
//                stayOver.setRoomTypeId(Long.valueOf(heOrderGoodsEntity.getEcpRoomType()));
//                stayOver.setOvernightDays(entity.getDays());
//                stayOverList.add(stayOver);
//            }
//            request.setStayOverList(stayOverList);
//        }
//        log.info("订单自动创建房态参数,request={}", request);
//        log.info("订单自动创建房态json参数,request={}", JSONUtil.parse(request));
//        try {
//            Integer integer = roomStateService.autoCommit(request);
//            if (integer == 1) {
//                log.info("订单自动创建房态成功,result={}", integer);
//            } else {
//                log.info("订单自动创建房态失败,result={}", integer);
//            }
//        } catch (Exception e) {
//            log.info("订单自动创建房态失败,异常信息={}", e.getMessage());
//        }
    }

}


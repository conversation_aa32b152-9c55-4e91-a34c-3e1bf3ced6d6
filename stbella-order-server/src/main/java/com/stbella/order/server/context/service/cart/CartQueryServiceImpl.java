package com.stbella.order.server.context.service.cart;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.core.CartSceneEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.order.entity.HeCartEntity;
import com.stbella.order.domain.order.entity.HeCartGoodsEntity;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.UserEntity;
import com.stbella.order.domain.order.production.GoodsEntity;
import com.stbella.order.domain.repository.GoodsRepository;
import com.stbella.order.domain.repository.HeCartGoodsRepository;
import com.stbella.order.domain.repository.HeCartRepository;
import com.stbella.order.domain.repository.UserRepository;
import com.stbella.order.server.context.component.assembler.CartSyncPayAmountAssembler;
import com.stbella.order.server.convert.CartEntityConverter;
import com.stbella.order.server.order.CartSyncFact;
import com.stbella.order.server.order.OrderPrice;
import com.stbella.platform.order.api.cart.CartQueryService;
import com.stbella.platform.order.api.req.*;
import com.stbella.platform.order.api.res.CartInventoryRes;
import com.stbella.platform.order.api.res.CartRes;
import com.stbella.platform.order.api.res.DiscountRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.FlowIdentity;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 购物车查询服务
 */
@Slf4j
@Service
@DubboService
public class CartQueryServiceImpl implements CartQueryService {

    @Resource
    private HeCartRepository heCartRepository;

    @Resource
    private HeCartGoodsRepository heCartGoodsRepository;

    @Resource
    private CartEntityConverter cartEntityConverter;

    @Resource
    private CartSyncPayAmountAssembler cartSyncPayAmountAssembler;

    @Resource
    private GoodsRepository goodsRepository;
    @Resource
    private UserRepository userRepository;

    /**
     * 查询购物车信息，补加 节假日费用等
     *
     * @param req
     * @return
     */
    @Override
    public Result<CartRes> queryCart(QueryCartReq req) {
        Integer scene = req.getScene();
        if (Objects.isNull(req.getCartId())) {
            if (Arrays.asList(CartSceneEnum.QUOTATION.code(), CartSceneEnum.CUSTOMER_COMPLAINTS.code(), CartSceneEnum.GIFT_ORDER.code()).contains(scene)) {
                return Result.success(new CartRes());
            }
        }
        Integer orderType = req.getOrderType();
        if (Objects.nonNull(orderType) && !OmniOrderTypeEnum.SMALL_MONTH_ORDER.code().equals(orderType)) {
            req.setOrderType(orderType);
        } else {
            req.setOrderType(OmniOrderTypeEnum.MONTH_ORDER.code());
        }
        HeCartEntity heCartEntity = heCartRepository.queryOne(req);
        if (ObjectUtil.isNull(heCartEntity)) {
            return Result.success(new CartRes());
        }
        if (Objects.nonNull(heCartEntity.getStaffId())) {
            UserEntity userEntity = userRepository.queryById(heCartEntity.getStaffId());
            if (Objects.nonNull(userEntity)) {
                heCartEntity.setStaffName(userEntity.getName());
            }
        }

        FlowIdentity identity = FlowIdentity.builder().bizActivity(BizActivityEnum.QUERY_CART.code()).idSlice("Cart").idSlice("Query").build();

        FlowContext context = new FlowContext();
        context.setAttribute(BizConstant.ExtraKey.storeId, heCartEntity.getStoreId());
        context.setAttribute(BizConstant.ExtraKey.scene, scene);
        context.setAttribute(BizConstant.ExtraKey.cartId, heCartEntity.getCartId());
        context.setAttribute(BizConstant.ExtraKey.clientUid, heCartEntity.getClientUid());
        context.setAttribute(HeCartEntity.class, heCartEntity);

        CartRes cart = cartEntityConverter.entity2Res(heCartEntity);
        context.setListAttribute(CustomAttribute.class, cart.getExtraInfo().getFulfillExtraList());

        SnowballFlowLauncher.fire(identity, context);

        cart.setSkuList(context.getListAttribute(SkuDetailInfo.class));
        OrderPrice orderPrice = context.getAttribute(OrderPrice.class);
        cart.setTotalAmount(orderPrice.getTotalAmount());

        cart.setSelectedAmount(orderPrice.getSelectedAmount());
        cart.setGiftAmount(orderPrice.getTotalAmount().subtract(orderPrice.getSelectedAmount()));

        //重置下特殊订单状态
        if (ObjectUtil.isNotNull(cart.getExtraInfo())) {
            if (cart.getExtraInfo().getOrderTag() > 0 || Strings.isNullOrEmpty(cart.getExtraInfo().getRemark()) || CollectionUtil.isNotEmpty(cart.getExtraInfo().getVoucherUrlList())) {
                cart.getExtraInfo().setSpecial(true);
            }
        }

        if (!cart.getOrderType().equals(OmniOrderTypeEnum.PRODUCTION_ORDER.getCode())) {
            for (SkuDetailInfo skuDetailInfo : cart.getSkuList()) {
                skuDetailInfo.setProduceAmountDeduction(null);
                skuDetailInfo.setProductionDiscountRuleType(null);
            }
        }


        return Result.success(cart);
    }

    @Override
    public Result<DiscountRes> queryDiscount(DiscountReq req) {

        QueryCartReq cartReq = new QueryCartReq();
        cartReq.setCartId(req.getCartId());
        cartReq.setScene(req.getScene());
        Integer orderType = req.getOrderType();
        if (Objects.nonNull(orderType) && !OmniOrderTypeEnum.SMALL_MONTH_ORDER.code().equals(orderType)) {
            cartReq.setOrderType(orderType);
        } else {
            req.setOrderType(OmniOrderTypeEnum.MONTH_ORDER.code());
        }
        HeCartEntity heCartEntity = heCartRepository.queryOne(cartReq);
        if (ObjectUtil.isNull(heCartEntity)) {
            throw new BusinessException(ErrorCodeEnum.CART_NOT_EXIST.code().toString(), ErrorCodeEnum.CART_NOT_EXIST.desc());
        }

        FlowIdentity identity = FlowIdentity.builder().bizActivity(BizActivityEnum.COMPUTE_DISCOUNT.code()).idSlice("Cart").idSlice("Discount").build();

        FlowContext context = new FlowContext();
        context.setAttribute(BizConstant.ExtraKey.storeId, req.getStoreId());
        context.setAttribute(BizConstant.ExtraKey.scene, req.getScene());
        context.setAttribute(HeCartEntity.class, heCartEntity);
        context.setAttribute(DiscountReq.class, req);
        CartRes cart = cartEntityConverter.entity2Res(heCartEntity);
        context.setListAttribute(CustomAttribute.class, cart.getExtraInfo().getFulfillExtraList());

        SnowballFlowLauncher.fire(identity, context);
        DiscountRes discountRes = context.getAttribute(DiscountRes.class);
        CfgStoreEntity store = context.getAttribute(CfgStoreEntity.class);
        discountRes.setTaxRate(store.getTaxRate());

        return Result.success(discountRes);
    }

    @Override
    public List<CartInventoryRes> queryInventory(QueryCartReq req) {

        Assert.isTrue(Objects.nonNull(req.getCartId()), "购物车ID不能为空");
        HeCartEntity heCartEntity = heCartRepository.queryOne(req);
        Assert.isTrue(Objects.nonNull(heCartEntity) && Objects.isNull(heCartEntity.getOrderId()), String.format("购物车ID:%s不合法", req.getCartId()));
        List<CartInventoryRes> resList = Lists.newArrayList();
        List<HeCartGoodsEntity> heCartGoodsEntities = heCartGoodsRepository.queryListByCartId(req.getCartId());
        if (CollectionUtils.isEmpty(heCartGoodsEntities)) {
            return resList;
        }
        heCartGoodsEntities = heCartGoodsEntities.stream().filter(item -> !item.isGift()).collect(Collectors.toList());
        List<Integer> goodsIdList = heCartGoodsEntities.stream().map(HeCartGoodsEntity::getGoodsId).collect(Collectors.toList());
        Map<Integer, HeCartGoodsEntity> goodsEntityMap = heCartGoodsEntities.stream().collect(Collectors.toMap(HeCartGoodsEntity::getGoodsId, e1 -> e1, (v1, v2) -> v1));
        List<GoodsEntity> goodsEntities = goodsRepository.selectByIdList(goodsIdList);
        goodsEntities.sort(Comparator.comparing(GoodsEntity::getGoodsPrice).reversed());
        for (GoodsEntity item : goodsEntities) {
            HeCartGoodsEntity heCartGoodsEntity = goodsEntityMap.get(item.getId());
            if (Objects.isNull(heCartGoodsEntity)) {
                continue;
            }
            resList.add(BeanMapper.map(heCartGoodsEntity, CartInventoryRes.class));
        }
        return resList;
    }

    @Override
    public Result<CartRes> updatePayAmount(CartUpdateReq req) {
        cartSyncPayAmountAssembler.run(BeanMapper.map(req, CartSyncFact.class));
        return Result.success(CartRes.builder().cartId(req.getCartId()).build());
    }


}

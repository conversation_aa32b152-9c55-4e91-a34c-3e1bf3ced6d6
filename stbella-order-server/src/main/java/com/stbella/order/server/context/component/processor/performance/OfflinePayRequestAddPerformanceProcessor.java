package com.stbella.order.server.context.component.processor.performance;

import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

/**
 * @Author: JJ
 * @CreateTime: 2024-05-28  13:55
 * @Description: 提交线下支付添加业绩
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "提交线下支付添加业绩")
public class OfflinePayRequestAddPerformanceProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    OrderRepository orderRepository;

    @Override
    public void run(FlowContext bizContext) {

        log.info("线下支付添加业绩");
        HeIncomeRecordEntity incomeRecordEntity = bizContext.getAttribute(HeIncomeRecordEntity.class);
        HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);
        order.setPaidAmount(incomeRecordEntity.getIncome() + order.getPaidAmount());

        HeOrderEntity updateModel = new HeOrderEntity();
        updateModel.setOrderId(order.getOrderId());
        updateModel.setPaidAmount(order.getPaidAmount());
        orderRepository.updateOrderMonthByOrderId(updateModel);

        bizContext.setAttribute(HeOrderEntity.class, order);

    }
}

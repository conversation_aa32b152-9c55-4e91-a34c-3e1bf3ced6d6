package com.stbella.order.server.context.component.processor.pay;

import cn.hutool.core.date.DateUtil;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.month.IncomeReceiptTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.server.fact.OrderFact;
import com.stbella.order.server.manager.PayRuleComponent;
import com.stbella.order.server.order.month.req.PayReqV2;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.pay.server.pay.service.res.PayAccountVo;
import com.stbella.rule.api.req.ExecuteRuleReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-05-29  15:20
 * @Description: 保存交易记录
 */
@Slf4j
@Component
@SnowballComponent(name = "保存交易记录", desc = "包括余额支付，产康金支付，线下、上都使用此对象")
public class SaveOrderTransactionProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    com.stbella.order.server.utils.IdGenUtils IdGenUtils;
    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @Resource
    private PayRuleComponent payRuleComponent;

    private static String Transaction_Pre = "TX";

    @Override
    public void run(FlowContext bizContext) {

        PayReqV2 payReqV2 = bizContext.getAttribute(PayReqV2.class);
        HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);

        HeIncomeRecordEntity heIncomeRecordEntity = new HeIncomeRecordEntity();
        heIncomeRecordEntity.setChannelType(payReqV2.getChannelType());
        heIncomeRecordEntity.setClientUid(order.getClientUid());
        heIncomeRecordEntity.setBasicUid(order.getBasicUid());
        heIncomeRecordEntity.setIncomeSn(IdGenUtils.genOmniLocalSn(Transaction_Pre, new Date()));
        heIncomeRecordEntity.setOrderId(payReqV2.getOrderId());
        heIncomeRecordEntity.setOrderSn(order.getOrderSn());
        heIncomeRecordEntity.setPayType(payReqV2.getPayType());
        heIncomeRecordEntity.setRemark(payReqV2.getRemark());

        //原来支付状态有这样一个逻辑，线上支付的全是0，线下是1.这里要兼容原来逻辑。
        heIncomeRecordEntity.setStatus(0);
        if (OmniPayTypeEnum.OFFLINE.getCode().intValue() == (payReqV2.getPayType())){
            heIncomeRecordEntity.setStatus(1);
            heIncomeRecordEntity.setPayTime(Long.valueOf(DateUtil.currentSeconds()).intValue());
        }else {
            heIncomeRecordEntity.setPayTime(0);
        }

        heIncomeRecordEntity.setIncome(AmountChangeUtil.changeY2FFoInt(payReqV2.getPayAmount()));
        heIncomeRecordEntity.setStoreId(order.getStoreId());
        heIncomeRecordEntity.setOrderType(order.getOrderType());
        heIncomeRecordEntity.setBu(order.getBu());
        heIncomeRecordEntity.setStoreName(payReqV2.getStoreName());
        heIncomeRecordEntity.setReceiptType(payReqV2.getAmountType());

        if (OmniOrderTypeEnum.DEPOSIT_ORDER.getCode().intValue() == order.getOrderType()){
            heIncomeRecordEntity.setReceiptType(IncomeReceiptTypeEnum.RECORD_RECEIPT_TYPE_DEPOSIT.code());
        }
        heIncomeRecordEntity.setCreatedAt(System.currentTimeMillis() / 1000);
        heIncomeRecordEntity.setUpdatedAt(System.currentTimeMillis() / 1000);
        heIncomeRecordEntity.setCurrency(order.getCurrency());
        PayAccountVo payAccountVo = selectPayAccount(heIncomeRecordEntity);
        heIncomeRecordEntity.setAccountId(payAccountVo.getId());
        //heIncomeRecordEntity.setOptId(Integer.valueOf(payReqV2.getOperator().getOperatorGuid()));
        //这里是客户操作的，无法获取操作人id, 后面考虑亮码的销售的操作人id
        heIncomeRecordEntity.setOptId(0);

        Integer recordId = incomeRecordRepository.saveOne(heIncomeRecordEntity);
        heIncomeRecordEntity.setId(recordId);
        heIncomeRecordEntity.setOperator(payReqV2.getOperator());

        bizContext.setAttribute(HeIncomeRecordEntity.class, heIncomeRecordEntity);
    }

    /**
     * 根据条件查询支付账号
     * @param incomeRecordEntity
     * @return
     */
    protected PayAccountVo selectPayAccount(HeIncomeRecordEntity incomeRecordEntity) {

        ExecuteRuleReq ruleReq = new ExecuteRuleReq();
        ruleReq.setSceneCode(1);
        OrderFact orderFact = new OrderFact();
        orderFact.setStoreId(incomeRecordEntity.getStoreId());
        orderFact.setBu(incomeRecordEntity.getBu());
        orderFact.setPayEvn(incomeRecordEntity.getPayType());
        ruleReq.setFactObj(orderFact);
        PayAccountVo payAccountVo = payRuleComponent.getPayAccountVo(ruleReq);

        return payAccountVo;
    }

}

package com.stbella.order.server.context.component.processor.cachier;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.core.result.Result;
import com.stbella.order.common.enums.core.OrderProcessTypeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.infrastructure.gateway.QrcodeGateway;
import com.stbella.order.infrastructure.gateway.req.QrcodeConfig;
import com.stbella.order.server.context.component.checker.OrderProcessCheckerContext;
import com.stbella.order.server.order.month.request.standard.QrCodeRequest;
import com.stbella.pay.server.alipay.enums.AccountTypeEnum;
import com.stbella.platform.order.api.req.PosPayQrCodeRequest;
import com.stbella.platform.order.api.res.ReceiveQrcodeRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: JJ
 * @Description: 创建Pos支付码
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "创建Pos支付码")
public class CreatePosPayQrcodeProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    QrcodeGateway qrcodeGateway;

    @Value("${qrCode.month-url-v2}")
    private String monthUrlV2;

    @Value("${qrCode.width}")
    private Integer width;

    @Value("${qrCode.height}")
    private Integer height;

    @Value("${qrCode.margin}")
    private Integer margin;

    @Override
    public void run(FlowContext bizContext) {

        PosPayQrCodeRequest request = bizContext.getAttribute(PosPayQrCodeRequest.class);

        Map<String, Object> map = new HashMap<>();
        map.put("incomeSn", request.getIncomeSn());
        map.put("bizType", request.getBizType());
        String content = JSONUtil.toJsonStr(map);

        log.info("createPosPayQrcode 参数 {}", content);

        QrcodeConfig config = QrcodeConfig.builder().width(width)
                .height(height)
                .margin(margin)
                .content(content).build();
        String qrcodeUrl = qrcodeGateway.createDefaultQrcode(config);

        ReceiveQrcodeRes qrcodeRes = ReceiveQrcodeRes.builder().url(qrcodeUrl).build();

        bizContext.setAttribute(ReceiveQrcodeRes.class, qrcodeRes);
    }

}

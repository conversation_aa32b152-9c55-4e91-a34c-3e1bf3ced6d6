package com.stbella.order.server.order.job;

import com.stbella.core.utils.TraceIdUtils;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.server.listener.BuildProductionOrderExcelListener;
import com.stbella.order.server.order.order.req.OrderExportReq;
import com.stbella.order.server.utils.DateUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class ProductionOrderWriteOffInfoJob {

    @Resource
    private BuildProductionOrderExcelListener buildProductionOrderExcelListener;

    @Resource
    private RedissonClient redissonClient;

    @XxlJob("ProductionOrderTaskHandler")
    public ReturnT<?> productionOrderTaskHandler() {
        TraceIdUtils.resetTraceId();
        long start = System.currentTimeMillis();
        log.info("统计产康订单核销信息任务开始处理，time:{}, param:{}", start, XxlJobHelper.getJobParam());
        String key = "Order:Production:Task:Handler:Export:Mail:PRE";
        if (StringUtils.isNotEmpty(XxlJobHelper.getJobParam()) && !"1".equals(XxlJobHelper.getJobParam())){
            key = key.concat(":").concat(XxlJobHelper.getJobParam());
        }
        RLock lockName = redissonClient.getLock(key);
        try {
            boolean tryLock = lockName.tryLock(0, 30, TimeUnit.MINUTES);
            Assert.isTrue(tryLock, "ProductionOrderTaskHandler正在处理，禁止重复提交");
            OrderExportReq orderExportReq = buildOrderExportReq(XxlJobHelper.getJobParam());
            Assert.isTrue(Objects.nonNull(orderExportReq.getBizCode()) && Arrays.asList(1, 2, 3, 4).contains(orderExportReq.getBizCode()), "ProductionOrderTaskHandler Job参数配置存在误差");
            buildProductionOrderExcelListener.orderExportProcess(orderExportReq);
            log.info("统计产康订单核销信息任务处理完成, key:{}, time:{}", key, System.currentTimeMillis() - start);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("统计产康订单核销信息发生异常",  e);
            return ReturnT.FAIL;
        } finally {
            if (Objects.nonNull(lockName)){
                lockName.unlock();
            }
        }
    }


    private static OrderExportReq buildOrderExportReq(String param) {

        OrderExportReq res = new OrderExportReq();
        res.setStartTime(1577808000L);
        res.setEndTime(new Date().getTime() / 1000);
        res.setOrderType(OmniOrderTypeEnum.PRODUCTION_ORDER.getCode());
        res.setBizCode(StringUtils.isEmpty(param) ? 1 : Integer.parseInt(param));

        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 12); // 设置为中午12点
        calendar.set(Calendar.MINUTE, 0);       // 设置分钟为0
        calendar.set(Calendar.SECOND, 0);       // 设置秒为0
        calendar.set(Calendar.MILLISECOND, 0);  // 设置毫秒为0

        if (res.getBizCode() == 1) {
            calendar.add(Calendar.MONTH, -1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            res.setWriteOffStartTime(calendar.getTime().getTime() / 1000);
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH)); // 设置为上个月的最后一天
            calendar.set(Calendar.HOUR_OF_DAY, 23); // 设置小时为23点（晚上11点）
            calendar.set(Calendar.MINUTE, 59); // 分钟设置为59
            calendar.set(Calendar.SECOND, 59); // 秒设置为59
            calendar.set(Calendar.MILLISECOND, 999);
            res.setWriteOffEndTime(calendar.getTime().getTime() / 1000);
        }
        if (res.getBizCode() == 2) {
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY); // 周一
            Long endTime = calendar.getTime().getTime() / 1000;
            res.setWriteOffEndTime(endTime);
            calendar.add(Calendar.WEEK_OF_YEAR, -1); // 上一周
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.THURSDAY);// 周四
            res.setWriteOffStartTime(calendar.getTime().getTime() / 1000);
        }
        if (res.getBizCode() == 3) {
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY); // 周一
            res.setWriteOffStartTime(calendar.getTime().getTime() / 1000);
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.THURSDAY);// 周四
            res.setWriteOffEndTime(calendar.getTime().getTime() / 1000);
        }
        if (res.getBizCode() == 4){
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            res.setWriteOffStartTime(calendar.getTime().getTime() / 1000);
            System.out.println(DateUtils.formatDateTime(calendar.getTime()));
            res.setWriteOffEndTime(res.getEndTime());
        }
        return res;
    }

}

package com.stbella.order.server.context.component.contract.stage;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.stbella.contract.common.utils.GeneratorUtil;
import com.stbella.contract.common.utils.RMBUtils;
import com.stbella.contract.model.req.OrderParamHistoryValuePushDTO;
import com.stbella.contract.model.res.ContractTemplateRes;
import com.stbella.contract.model.res.OrderParamHistoryVO;
import com.stbella.core.utils.BigDecimalUtil;
import com.stbella.order.common.enums.month.ContractTypeEnum;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.domain.order.month.entity.AheadOutRoomEntity;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.domain.repository.AheadOutRoomRepository;
import com.stbella.order.domain.repository.MonthOrderParamHistoryRepository;
import com.stbella.order.server.contract.req.MonthUserEsignDTO;
import com.stbella.order.server.order.cts.enums.ContractSignStatusEnum;
import com.stbella.order.server.order.month.req.AheadOutRoomQuery;
import com.stbella.order.server.order.month.res.MonthOrderParamHistoryVO;
import com.stbella.platform.order.api.contract.req.ContractBaseReq;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-23 15:33
 */
@Component
@Slf4j
public class ReleaseContractFillStrategy extends ContractFillStrategy {

    @Resource
    private AheadOutRoomRepository aheadOutRoomRepository;
    @Resource
    private MonthOrderParamHistoryRepository monthOrderParamHistoryRepository;

    @Override
    protected Map<String, String> buildParam(ContractParamFillContext contractParamFillContext) {

        //region 取订单快照信息表
        AheadOutRoomQuery roomQuery = new AheadOutRoomQuery();
        Integer orderId = contractParamFillContext.getOrderId();
        roomQuery.setOrderId(orderId);
        AheadOutRoomEntity aheadOutRoomEntity = aheadOutRoomRepository.queryByOrderId(roomQuery);

        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = contractParamFillContext.getHeOrderUserSnapshotEntity();
        MonthUserEsignDTO userSignDTO = contractParamFillContext.getUserSignDTO();
        ContractBaseReq req = contractParamFillContext.getRequest();
        Integer contractType = req.getContractType();
        if (ContractTypeEnum.ESIGN_TYPE.code().equals(contractType)) {
            checkMonthUserEsignDTO(userSignDTO);
        }

        String contractNo = GeneratorUtil.contractNo();
        //获取信息,填充部分合同信息
        Map<String, String> templateData = new HashMap<>();
        //获取主合同信息
        OrderContractSignRecordVO orderContractSignRecordVO = contractParamFillContext.getSignRecords().stream()
                .filter(o -> o.getTemplateContractType().equals(TemplateContractTypeEnum.YZ_SAINTBELLA.code())
                        || o.getContractStatus().equals(ContractSignStatusEnum.SIGN_FINISH.getCode())).findFirst().orElse(null);
        //签署人手机号(个人签署需要)
        templateData.put("signPhone", userSignDTO.getPhone());

        //合同参数需要的开始
        // 签署机构名称(机构签署需要) 动态获取
        ContractTemplateRes contractTemplateRes = contractParamFillContext.getContractTemplateRes();
        String companyName = contractTemplateRes.getCompanyName();
        templateData.put("companyName", companyName);
        templateData.put("company_name", companyName);

        templateData.put("contract_no", contractNo);
        templateData.put("client_name", heOrderUserSnapshotEntity.getName());
        templateData.put("sign_time_str2", DateTime.now().toDateStr());
        templateData.put("in_room_time_str", DateUtil.formatDate(aheadOutRoomEntity.getCheckInRoomDate()));
        //未消费金额
        templateData.put("amount", RMBUtils.formatToseparaDecimals(BigDecimalUtil.divide(new BigDecimal(aheadOutRoomEntity.getRefundedAmount()), new BigDecimal(100))));
        //未消费金额大写
        templateData.put("amount_words", RMBUtils.numToRMBStr(BigDecimalUtil.divide(new BigDecimal(aheadOutRoomEntity.getRefundedAmount()), new BigDecimal(100)).doubleValue()));

        //e签宝或老合同或纸质
        if (ObjectUtil.isNotEmpty(orderContractSignRecordVO)) {
            //主合同编号
            templateData.put("main_contract_no", Objects.nonNull(orderContractSignRecordVO) ? orderContractSignRecordVO.getContractNo() : "");
            //主合同签订时间
            templateData.put("main_contract_time_str", Objects.nonNull(orderContractSignRecordVO.getSignTime()) ? DateUtil.date(orderContractSignRecordVO.getSignTime()).toDateStr() : "");
            //提前离馆合同类型要与主合同相同(如果是纸质合同,需要取门店实时的合同类型)
//            contract.setContractType(Objects.nonNull(yzManContract) ? yzManContract.getContractType() : TemplateFormatEnum.E_SIGN.code());
        }

        List<OrderParamHistoryValuePushDTO> orderParamHistoryValuePushDTOS =  templateData.entrySet().stream().map(entry -> new OrderParamHistoryValuePushDTO(entry.getKey(), entry.getValue())).collect(Collectors.toList());
        List<MonthOrderParamHistoryVO> contractTemplateData = new ArrayList<>();
        for (OrderParamHistoryValuePushDTO param : orderParamHistoryValuePushDTOS) {
            String name = param.getName();
            String value = param.getValue();
            MonthOrderParamHistoryVO orderParamHistory = new MonthOrderParamHistoryVO();
            orderParamHistory.setName(name);
            orderParamHistory.setParamValue(value);
            orderParamHistory.setMark(name);
            orderParamHistory.setItem(0);
            orderParamHistory.setTerms(0);
            orderParamHistory.setValid(1);
            orderParamHistory.setOrderId(Long.valueOf(orderId));
            orderParamHistory.setType(TemplateContractTypeEnum.RELEASE.code());
            contractTemplateData.add(orderParamHistory);
        }
        monthOrderParamHistoryRepository.saveBatchByParamHistoryId(contractTemplateData);
        return templateData;
    }

    @Override
    protected String getContractName(ContractParamFillContext contractParamFillContext) {
        return "";
    }


    @Override
    public boolean filter(Integer templateContractType) {
        return TemplateContractTypeEnum.RELEASE.code().equals(templateContractType);
    }
}
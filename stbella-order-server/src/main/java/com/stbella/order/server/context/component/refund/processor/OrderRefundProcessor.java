package com.stbella.order.server.context.component.refund.processor;

import cn.hutool.json.JSONObject;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.server.context.component.spi.RefundSpi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.SnowballExtensionInvoker;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "退款", desc = "发起退款")
public class OrderRefundProcessor implements IExecutableAtom<FlowContext> {

    @Override
    public void run(FlowContext bizContext) {
        HeOrderRefundEntity refundEntity = bizContext.getAttribute(HeOrderRefundEntity.class);
        JSONObject jsonObject = SnowballExtensionInvoker.invoke(RefundSpi.class, refundEntity);
        bizContext.setAttribute(JSONObject.class, jsonObject);
    }
}

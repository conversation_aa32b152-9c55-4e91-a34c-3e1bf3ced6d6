package com.stbella.order.server.order.month.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.production.OrderProductionItemEnum;
import com.stbella.order.common.enums.production.OrderProductionVerificationStateEnum;
import com.stbella.order.common.enums.production.VerificationStatusEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.order.month.dto.OrderGoodsInfoDTO;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.production.*;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.order.month.dto.PageDTO;
import com.stbella.order.server.order.month.enums.BuyTypeEnum;
import com.stbella.order.server.order.month.req.OrderPaySuccessReq;
import com.stbella.order.server.order.month.res.OrderPaySuccessGoodsRes;
import com.stbella.order.server.order.month.res.OrderPaySuccessRes;
import com.stbella.order.server.order.month.res.OrderProductionCardContentRes;
import com.stbella.order.server.order.month.res.OrderProductionCardExtendRes;
import com.stbella.order.server.order.month.service.FinanceQueryOrderInfoService;
import com.stbella.order.server.utils.PageUtils;
import com.stbella.platform.order.api.res.GoodsInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@DubboService
public class FinanceQueryOrderInfoServiceImpl implements FinanceQueryOrderInfoService {

    private static final List<Integer> typeList = Arrays.asList(OrderProductionItemEnum.TYPE_COUNT.code(), OrderProductionItemEnum.TYPE_GROUP.code());

    private static final Integer minusOne = -1;

    private static final Integer zero = 0;

    private static final Integer one = 1;

    private static final Integer two = 2;

    private static final Integer six = 6;

    private static final Integer tenThousand = 10000;

    private static final Integer oneThousand = 1000;

    private static final Integer twoThousand = 2000;

    private static final String num = "num";

    private static final String usedNum = "usedNum";

    @Resource
    private GoodsRepository goodsRepository;

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private OrderGoodsRepository orderGoodsRepository;

    @Resource
    private ClientRepository clientRepository;

    @Resource
    private StoreRepository storeRepository;

    @Resource
    private OrderGiftExtendRepository orderGiftExtendRepository;

    @Resource
    private OrderProductionExtendRepository orderProductionExtendRepository;

    @Resource
    private OrderProductionCardExtendRepository orderProductionCardExtendRepository;

    @Resource
    private IncomePaidAllocationRepository incomePaidAllocationRepository;

    @Resource
    private OrderProductionVerificationLogRepository orderProductionVerificationLogRepository;

    @Resource
    private GoodsSkuRepository goodsSkuRepository;

    @Override
    public PageDTO<OrderPaySuccessRes> queryPaySuccessOrderList(OrderPaySuccessReq req) {

        try {
            log.info("queryPaySuccessOrderList req: {}", JSONUtil.toJsonStr(req));
            this.checkParams(req);
            Page<HeOrderEntity> pageList = orderRepository.queryList(req, giftOrderIdList(req));
            if (CollectionUtils.isEmpty(pageList.getRecords())) {
                return PageDTO.emptyList(req.getPageNum(), req.getPageSize(), pageList.getTotal());
            }
            Map<Integer, String> clientNameAndIdMap = this.getClientNameAndIdMap(pageList.getRecords());
            Map<Integer, CfgStoreEntity> storeEntityMap = this.getStoreEntityMap(pageList.getRecords());
            OrderGoodsInfoDTO orderGoodsInfoDTO = this.getOrderGoodsInfoDTO(pageList.getRecords());
            return PageUtils.convert2PageDTO(pageList, heOrderEntity -> convertOrderPaySuccessRes(heOrderEntity, orderGoodsInfoDTO, clientNameAndIdMap, storeEntityMap));
        } catch (Exception e){
            log.error(String.format("queryPaySuccessOrderList error, error: %s", e.getMessage()), e);
            return PageDTO.fail(req.getPageNum(), req.getPageSize(), e.getMessage());
        }
    }

    private List<Integer> giftOrderIdList(OrderPaySuccessReq req){
        List<OrderGiftExtendEntity> orderGiftExtendEntityList = orderGiftExtendRepository.selectListByTime(req.getPayTimeStart(), req.getPayTimeEnd());
        if (CollectionUtils.isEmpty(orderGiftExtendEntityList)){
            return new ArrayList<>();
        }
        return orderGiftExtendEntityList.stream().map(OrderGiftExtendEntity::getOrderId).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private OrderGoodsInfoDTO getOrderGoodsInfoDTO(List<HeOrderEntity> heOrderEntityList){

        OrderGoodsInfoDTO orderGoodsInfoDTO = new OrderGoodsInfoDTO();
        List<Integer> newOrderIdList = heOrderEntityList.stream().filter(HeOrderEntity::isNewOrder).map(HeOrderEntity::getOrderId).collect(Collectors.toList());
        log.info("newOrderIdList: {}", JSONUtil.toJsonStr(newOrderIdList));
        List<Integer> oldOrderIdList = heOrderEntityList.stream().filter(heOrderEntity -> !heOrderEntity.isNewOrder()).map(HeOrderEntity::getOrderId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(newOrderIdList) && CollectionUtils.isEmpty(oldOrderIdList)){
            return orderGoodsInfoDTO;
        }
        orderGoodsInfoDTO.setOrderGoodsEntityMap(this.orderGoodsEntityMap(newOrderIdList));
        log.info("orderGoodsInfoDTO.getOrderGoodsEntityMap: {}", JSONUtil.toJsonStr(orderGoodsInfoDTO.getOrderGoodsEntityMap()));
        orderGoodsInfoDTO.setOrderProductionVerificationMap(this.getOrderProductionVerificationMap(newOrderIdList));
        orderGoodsInfoDTO.setOrderProductionExtendList(CollectionUtils.isEmpty(oldOrderIdList) ? new ArrayList<>() : orderProductionExtendRepository.queryByOrderIdList(oldOrderIdList));
        orderGoodsInfoDTO.setNewOrderProductionExtendList(CollectionUtils.isEmpty(newOrderIdList) ? new ArrayList<>() : orderProductionExtendRepository.queryByOrderIdList(newOrderIdList));
        orderGoodsInfoDTO.setOrderProductionCardExtendList(this.getOrderProductionCardExtendList(orderGoodsInfoDTO.getOrderProductionExtendList()));
        orderGoodsInfoDTO.setOrderGiftExtendList(orderGiftExtendRepository.getByOrderId(heOrderEntityList.stream().map(HeOrderEntity::getOrderId).filter(Objects::nonNull).collect(Collectors.toList())));
        orderGoodsInfoDTO.setIncomePaidAllocationMap(this.getIncomePaidAllocationMap(newOrderIdList));
        List<GoodsEntity> goodsList = goodsRepository.selectByIdList(this.getGoodsId(orderGoodsInfoDTO));
        orderGoodsInfoDTO.setGoodsEntityMap(CollectionUtils.isEmpty(goodsList) ? new HashMap<>() : goodsList.stream().collect(Collectors.toMap(GoodsEntity::getId, entity -> entity, (v1, v2) -> v1)));
        orderGoodsInfoDTO.setGoodsSkuPriceMap(this.getGoodsSkuPriceMap(orderGoodsInfoDTO.getOrderProductionExtendList()));
        return orderGoodsInfoDTO;
    }

    private Map<Integer, BigDecimal> getGoodsSkuPriceMap(List<OrderProductionExtendEntity> orderProductionExtendList){

        HashMap<Integer, BigDecimal> goodsSkuPriceMap = new HashMap<>();
        if (CollectionUtils.isEmpty(orderProductionExtendList)){
            return goodsSkuPriceMap;
        }

        List<Integer> skuIdList = orderProductionExtendList.stream().map(OrderProductionExtendEntity::getSkuId).filter(Objects::nonNull).collect(Collectors.toList());

        List<GoodsSkuEntity> goodsSkuEntitieList = goodsSkuRepository.selectByIdList(skuIdList);
        if (CollectionUtils.isEmpty(goodsSkuEntitieList)){
            return goodsSkuPriceMap;
        }
        return goodsSkuEntitieList.stream().collect(Collectors.toMap(GoodsSkuEntity::getId, sku -> {
            BigDecimal goodsPrice = AmountChangeUtil.f2YScale2(sku.getGoodsPrice());
            return Objects.isNull(goodsPrice) ? BigDecimal.ZERO : goodsPrice;
        }));
    }

    private Map<Integer, List<IncomePaidAllocationEntity>> getIncomePaidAllocationMap(List<Integer> newOrderIdList){

        List<IncomePaidAllocationEntity> incomePaidAllocationEntities = incomePaidAllocationRepository.queryListByOrderIdList(newOrderIdList);
        if (CollectionUtils.isEmpty(incomePaidAllocationEntities)){
            return new HashMap<>();
        }
        return incomePaidAllocationEntities.stream().collect(Collectors.groupingBy(IncomePaidAllocationEntity::getOrderId));
    }

    private List<OrderProductionCardExtendEntity> getOrderProductionCardExtendList(List<OrderProductionExtendEntity> orderProductionExtendList){

        if (CollectionUtils.isEmpty(orderProductionExtendList)){
            return new ArrayList<>();
        }
        Set<Integer> productionIdList = orderProductionExtendList.stream().filter(item -> typeList.contains(item.getType())).map(OrderProductionExtendEntity :: getId).collect(Collectors.toSet());
        List<OrderProductionCardExtendEntity> orderProductionCardExtendEntities = orderProductionCardExtendRepository.queryByProductionIdList(productionIdList);
        return CollectionUtils.isEmpty(orderProductionCardExtendEntities) ? new ArrayList<>() : orderProductionCardExtendEntities;
    }

    private Map<Long, List<OrderProductionVerificationLogEntity>> getOrderProductionVerificationMap(List<Integer> orderIdList){

        if (CollectionUtils.isEmpty(orderIdList)){
            return new HashMap<>();
        }
        List<OrderProductionVerificationLogEntity> orderProductionVerificationLogEntities = orderProductionVerificationLogRepository.queryVerificationList(orderIdList);
        if (CollectionUtils.isEmpty(orderProductionVerificationLogEntities)){
            return new HashMap<>();
        }
        return orderProductionVerificationLogEntities.stream().collect(Collectors.groupingBy(OrderProductionVerificationLogEntity::getOrderId));
    }


    private List<Integer> getGoodsId(OrderGoodsInfoDTO orderGoodsInfoDTO) {

        // 合并商品ID收集逻辑，避免重复查询
        List<Integer> goodsId = new ArrayList<>();

        // 新订单商品ID
        if (!orderGoodsInfoDTO.getOrderGoodsEntityMap().isEmpty()) {
            goodsId.addAll(orderGoodsInfoDTO.getOrderGoodsEntityMap().values().stream().flatMap(Collection::stream).map(HeOrderGoodsEntity::getGoodsId).filter(Objects::nonNull).collect(Collectors.toList()));
        }
        // 旧订单商品ID
        if (!CollectionUtils.isEmpty(orderGoodsInfoDTO.getOrderProductionExtendList())) {
            goodsId.addAll(orderGoodsInfoDTO.getOrderProductionExtendList().stream().map(OrderProductionExtendEntity::getGoodsId).filter(Objects::nonNull).collect(Collectors.toList()));
        }
        // 礼品商品ID
        if (!CollectionUtils.isEmpty(orderGoodsInfoDTO.getOrderGiftExtendList())) {
            goodsId.addAll(orderGoodsInfoDTO.getOrderGiftExtendList().stream().map(OrderGiftExtendEntity::getGoodsId).filter(Objects::nonNull).collect(Collectors.toList()));
        }
        return goodsId;
    }


    private Map<Integer, List<HeOrderGoodsEntity>> orderGoodsEntityMap(List<Integer> orderIdList){

        if (CollectionUtils.isEmpty(orderIdList)){
            return new HashMap<>();
        }
        List<HeOrderGoodsEntity> orderGoodsByOrderIdList = orderGoodsRepository.getByOrderIdList(orderIdList);
        if (CollectionUtils.isEmpty(orderGoodsByOrderIdList)){
            return new HashMap<>();
        }
        return orderGoodsByOrderIdList.stream().collect(Collectors.groupingBy(HeOrderGoodsEntity::getOrderId));
    }

    private Map<Integer, CfgStoreEntity> getStoreEntityMap(List<HeOrderEntity> heOrderEntityList){

        List<CfgStoreEntity> storeEntityList = storeRepository.queryCfgStoreByIdList(heOrderEntityList.stream().map(HeOrderEntity::getStoreId).collect(Collectors.toList()));
        return storeEntityList.stream().collect(Collectors.toMap(CfgStoreEntity::getStoreId, entity -> entity, (v1, v2) -> v1));
    }

    private Map<Integer, String> getClientNameAndIdMap(List<HeOrderEntity> heOrderEntityList){

        List<TabClientEntity> clientEntityList = clientRepository.getTabClientByIdList(heOrderEntityList.stream().map(HeOrderEntity::getClientUid).collect(Collectors.toList()));
        return clientEntityList.stream().collect(Collectors.toMap(TabClientEntity::getId, TabClientEntity::getName));
    }

    private OrderPaySuccessRes convertOrderPaySuccessRes(HeOrderEntity heOrderEntity, OrderGoodsInfoDTO orderGoodsInfoDTO, Map<Integer, String> clientNameAndIdMap, Map<Integer, CfgStoreEntity> storeEntityMap) {

        Assert.notNull(heOrderEntity, "heOrderEntity must not be null");
        OrderPaySuccessRes orderPaySuccessRes = new OrderPaySuccessRes();
        orderPaySuccessRes.setClientUid(heOrderEntity.getClientUid());
        orderPaySuccessRes.setCustomerName(clientNameAndIdMap.get(heOrderEntity.getClientUid()));
        orderPaySuccessRes.setBasicUid(heOrderEntity.getBasicUid());
        orderPaySuccessRes.setOrderId(heOrderEntity.getOrderId());
        orderPaySuccessRes.setOrderSn(heOrderEntity.getOrderSn());
        orderPaySuccessRes.setOrderType(heOrderEntity.getOrderType());
        orderPaySuccessRes.setOrderTypeStr(OmniOrderTypeEnum.getOrderName(heOrderEntity.getOrderType()));
        orderPaySuccessRes.setRealAmount(AmountChangeUtil.f2YScale2(heOrderEntity.isNewOrder() ? heOrderEntity.getRealAmount() : heOrderEntity.getPaidAmount()));
        if (!heOrderEntity.isNewOrder() && OmniOrderTypeEnum.PRODUCTION_ORDER.getCode().equals(heOrderEntity.getOrderType())){
            orderPaySuccessRes.setRealAmount(AmountChangeUtil.f2YScale2(heOrderEntity.getPaidAmount() - heOrderEntity.getProductionAmountPay()));
        }
        orderPaySuccessRes.setProductionAmountPay(AmountChangeUtil.f2YScale2(heOrderEntity.getProductionAmountPay()));
        orderPaySuccessRes.setStoreId(heOrderEntity.getStoreId());
        CfgStoreEntity cfgStoreEntity = storeEntityMap.get(heOrderEntity.getStoreId());
        Assert.notNull(cfgStoreEntity, "cfgStore must not be null");
        orderPaySuccessRes.setStoreName(cfgStoreEntity.getStoreName());
        orderPaySuccessRes.setVersion(Objects.isNull(heOrderEntity.getVersion()) ? BigDecimal.ZERO : heOrderEntity.getVersion());
        orderPaySuccessRes.setPayFinishAt(Objects.isNull(heOrderEntity.getPayFinishAt()) ? null : new Date(heOrderEntity.getPayFinishAt() * oneThousand));
        orderPaySuccessRes.setCreatedAt(Objects.isNull(heOrderEntity.getCreatedAt()) ? null : new Date(heOrderEntity.getCreatedAt() * oneThousand));
        orderPaySuccessRes.setGoodsList(heOrderEntity.isNewOrder() ? this.newOrderPaySuccessGoodsResProcess(orderGoodsInfoDTO, heOrderEntity) : this.oldOrderPaySuccessGoodsResProcess(orderGoodsInfoDTO, heOrderEntity));
        fillGoodsListProductionPayInfo(orderPaySuccessRes.getGoodsList(), orderPaySuccessRes.getProductionAmountPay(), heOrderEntity.isNewOrder());
        return orderPaySuccessRes;
    }

    private void fillGoodsListProductionPayInfo(List<OrderPaySuccessGoodsRes> goodsList, BigDecimal productionAmountPay, Boolean isNewOrder) {

        if (CollectionUtils.isEmpty(goodsList) || productionAmountPay.compareTo(BigDecimal.ZERO) <= zero || isNewOrder){
            return;
        }
        List<OrderPaySuccessGoodsRes> buyGoodsList = goodsList.stream().filter(item -> item.getSource() < zero).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(buyGoodsList)){
            return;
        }

        BigDecimal orderPayProductionAmountMax = getOrderPayProductionAmountMax(buyGoodsList);
        log.info("订单支付的产康金最大值: {}", orderPayProductionAmountMax);
        if (orderPayProductionAmountMax.compareTo(BigDecimal.ZERO) <= zero){
            return;
        }

        buyGoodsList.sort(Comparator.comparing(OrderPaySuccessGoodsRes::getGoodsSkuPrice));
        OrderPaySuccessGoodsRes orderPaySuccessGoodsRes = buyGoodsList.get(buyGoodsList.size() - one);

        BigDecimal allocationAmount = BigDecimal.ZERO;
        for (OrderPaySuccessGoodsRes goodsInfo : buyGoodsList) {
            if (zero.equals(goodsInfo.getProduceAmountDeduction())){
                continue;
            }
            BigDecimal payable;
            if (orderPaySuccessGoodsRes.getId().equals(goodsInfo.getId())) {
                payable =productionAmountPay.subtract(allocationAmount);
            } else {
                // A商品分摊总金额 = 订单支付的产康金 * (商品A原数量 * 商品A原单价 / 订单可支付的产康金最大值)
                payable = productionAmountPay.multiply(new BigDecimal(goodsInfo.getGoodsNum()).multiply(goodsInfo.getGoodsSkuPrice())).divide(orderPayProductionAmountMax, 2, BigDecimal.ROUND_DOWN);
                allocationAmount = allocationAmount.add(payable);
            }
            goodsInfo.setPayProductionPriceTotal(payable);
            goodsInfo.setPayProductionPrice(goodsInfo.getPayProductionPriceTotal().divide(new BigDecimal(goodsInfo.getGoodsNum()), two, BigDecimal.ROUND_DOWN));
        }

    }

    private BigDecimal getOrderPayProductionAmountMax(List<OrderPaySuccessGoodsRes> buyGoodsList) {

        BigDecimal totalPayProduction = BigDecimal.ZERO;
        for (OrderPaySuccessGoodsRes goods : buyGoodsList) {
            Integer productionDiscountRuleType = goods.getProduceAmountDeduction();
            if (zero.equals(productionDiscountRuleType)){
                continue;
            }
            totalPayProduction = totalPayProduction.add(goods.getGoodsSkuPrice());
        }
        return totalPayProduction;
    }

    private List<OrderPaySuccessGoodsRes> newOrderPaySuccessGoodsResProcess(OrderGoodsInfoDTO orderGoodsInfoDTO, HeOrderEntity heOrderEntity) {

        log.info("新订单商品信息处理, orderId: {}", heOrderEntity.getOrderId());
        List<HeOrderGoodsEntity> heOrderGoodsEntitieList = orderGoodsInfoDTO.getOrderGoodsEntityMap().get(heOrderEntity.getOrderId());
        if (CollectionUtils.isEmpty(heOrderGoodsEntitieList)){
            log.info("订单商品表信息不存在, orderId: {}", heOrderEntity.getOrderId());
            return Lists.newArrayList();
        }
        List<IncomePaidAllocationEntity> incomePaidAllocationList = orderGoodsInfoDTO.getIncomePaidAllocationMap().get(heOrderEntity.getOrderId());
        if (CollectionUtils.isEmpty(incomePaidAllocationList)){
            log.info("订单对应的实付分摊信息不存在, orderId: {}", heOrderEntity.getOrderId());
            incomePaidAllocationList = Lists.newArrayList();
        }
        Map<Long, List<IncomePaidAllocationEntity>> incomePaidAllocationEntityMap = incomePaidAllocationList.stream().collect(Collectors.groupingBy(IncomePaidAllocationEntity::getOrderGoodsId));

        List<OrderGiftExtendEntity> orderGiftList = this.getOrderGift(orderGoodsInfoDTO, heOrderEntity.getOrderId());

        Map<Integer, List<Integer>> orderGiftIdByOrderGoodsIdMap = this.getOrderGiftIdByOrderGoodsIdMap(orderGiftList, heOrderGoodsEntitieList);
        Map<String, List<Integer>> extendIdByOrderGoodsSnMap = orderGoodsInfoDTO.getNewOrderProductionExtendList().stream().filter(item -> StringUtils.isNotEmpty(item.getOrderGoodsSn()) && item.getOrderId().equals(heOrderEntity.getOrderId())).collect(Collectors.groupingBy(OrderProductionExtendEntity::getOrderGoodsSn, Collectors.mapping(OrderProductionExtendEntity::getId, Collectors.toList())));
        Map<Integer, Integer> newOrderGiftDataSourceMap = CollectionUtils.isEmpty(orderGiftList) ? new HashMap<>() : orderGiftList.stream().collect(Collectors.toMap(OrderGiftExtendEntity::getId, OrderGiftExtendEntity::getDataSource));
        Map<Integer, Integer> newOrderDataSourceMap = orderGoodsInfoDTO.getNewOrderProductionExtendList().stream().collect(Collectors.toMap(OrderProductionExtendEntity::getId, OrderProductionExtendEntity::getDataSource));
        List<OrderPaySuccessGoodsRes> orderPaySuccessGoodsResList = new ArrayList<>();
        heOrderGoodsEntitieList.forEach(heOrderGoodsEntity -> {

            log.info("订单商品信息: {}", JSONUtil.toJsonStr(heOrderGoodsEntity));
            OrderPaySuccessGoodsRes orderPaySuccessGoodsRes = new OrderPaySuccessGoodsRes();
            orderPaySuccessGoodsRes.setId(heOrderGoodsEntity.getId());
            List<Integer> cardIdList = orderGiftIdByOrderGoodsIdMap.get(heOrderGoodsEntity.getId());
            orderPaySuccessGoodsRes.setCardIdList(Lists.newArrayList());
            if (CollectionUtils.isNotEmpty(cardIdList)){
                orderPaySuccessGoodsRes.setCardIdList(cardIdList);
                Map<Integer, Integer> orderDataSourceMap = cardIdList.stream().filter(item -> Objects.nonNull(newOrderGiftDataSourceMap.get(item))).collect(Collectors.toMap(item -> item, newOrderGiftDataSourceMap::get));
                orderPaySuccessGoodsRes.setNewOrderDataSourceMap(orderDataSourceMap);
                orderDataSourceMap.entrySet().stream().findFirst().ifPresent(entry -> orderPaySuccessGoodsRes.setDataSource(entry.getValue()));
            }

            List<Integer> extendIdList = extendIdByOrderGoodsSnMap.get(heOrderGoodsEntity.getOrderGoodsSn());
            if (CollectionUtils.isNotEmpty(extendIdList) && zero.equals(heOrderGoodsEntity.getGift())){
                orderPaySuccessGoodsRes.setCardIdList(extendIdList);
                if (CollectionUtils.isNotEmpty(extendIdList)){
                    Map<Integer, Integer> orderDataSourceMap = extendIdList.stream().filter(item -> Objects.nonNull(newOrderDataSourceMap.get(item))).collect(Collectors.toMap(item -> item, newOrderDataSourceMap::get));
                    orderPaySuccessGoodsRes.setNewOrderDataSourceMap(orderDataSourceMap);
                    orderDataSourceMap.entrySet().stream().findFirst().ifPresent(entry -> orderPaySuccessGoodsRes.setDataSource(entry.getValue()));
                }
            }

            orderPaySuccessGoodsRes.setGoodsId(heOrderGoodsEntity.getGoodsId());
            String goodsName = heOrderGoodsEntity.getGoodsName();
            orderPaySuccessGoodsRes.setGoodsName(getGoodsName(goodsName, heOrderGoodsEntity.getSkuName()));
            orderPaySuccessGoodsRes.setSource(BuyTypeEnum.GIFT.getCode().equals(heOrderGoodsEntity.getGift()) ? minusOne : this.getSource(orderGoodsInfoDTO, heOrderEntity, heOrderGoodsEntity));
            orderPaySuccessGoodsRes.setGoodsGiftType(getGoodsGiftType(orderPaySuccessGoodsRes.getSource()));

            GoodsEntity goodsEntity = this.getGoodsEntity(orderGoodsInfoDTO.getGoodsEntityMap(), heOrderGoodsEntity.getGoodsInfo(), heOrderGoodsEntity.getGoodsId());
            log.info("goodsName is {}",goodsName);
            if (StringUtils.isNotEmpty(goodsName) && goodsName.contains("刷数据")) {
                orderPaySuccessGoodsRes.setMainSupplierId(Objects.isNull(goodsEntity) ? null : goodsEntity.getMainSupplierId());
                orderPaySuccessGoodsRes.setGoodsType(heOrderGoodsEntity.getGoodsType());
                orderPaySuccessGoodsRes.setServiceType(heOrderGoodsEntity.getServiceType());
                orderPaySuccessGoodsRes.setProductionDiscountRuleType(Objects.isNull(goodsEntity) ? minusOne : goodsEntity.getProductionDiscountRuleType());
                orderPaySuccessGoodsRes.setProduceAmountDeduction(Objects.isNull(goodsEntity) ? zero : goodsEntity.getProductionDiscountRuleType());
                orderPaySuccessGoodsRes.setGoodsPrice(Objects.isNull(goodsEntity) ? BigDecimal.ZERO : AmountChangeUtil.changeF2Y(heOrderGoodsEntity.getGoodsPriceOrgin()));
                orderPaySuccessGoodsRes.setGoodsSkuPrice(BigDecimal.ZERO);
            }else {

                orderPaySuccessGoodsRes.setMainSupplierId(Objects.isNull(goodsEntity) ? null : goodsEntity.getMainSupplierId());
                orderPaySuccessGoodsRes.setGoodsType(Objects.isNull(goodsEntity) ? null : goodsEntity.getGoodsType());
                orderPaySuccessGoodsRes.setServiceType(Objects.isNull(goodsEntity) ? null : goodsEntity.getServiceType());
                orderPaySuccessGoodsRes.setProductionDiscountRuleType(Objects.isNull(goodsEntity) ? minusOne : goodsEntity.getProductionDiscountRuleType());
                orderPaySuccessGoodsRes.setProduceAmountDeduction(Objects.isNull(goodsEntity) ? zero : goodsEntity.getProductionDiscountRuleType());
                orderPaySuccessGoodsRes.setGoodsPrice(Objects.isNull(goodsEntity) ? BigDecimal.ZERO : AmountChangeUtil.changeF2Y(goodsEntity.getGoodsPrice()));
                orderPaySuccessGoodsRes.setTimeClearingPoint(Objects.isNull(goodsEntity) ? null : goodsEntity.getTimeClearingPoint());
                orderPaySuccessGoodsRes.setGoodsSkuPrice(BigDecimal.ZERO);
            }

            orderPaySuccessGoodsRes.setGoodsNum(heOrderGoodsEntity.getGoodsNum());

            List<IncomePaidAllocationEntity> incomePaidAllocationEntitiesList = Objects.isNull(incomePaidAllocationEntityMap.get(heOrderGoodsEntity.getId().longValue())) ? Lists.newArrayList() : incomePaidAllocationEntityMap.get(heOrderGoodsEntity.getId().longValue()) ;
            int paidAmount = incomePaidAllocationEntitiesList.stream().filter(item -> !OmniPayTypeEnum.PRODUCTION_COIN.getCode().toString().equals(item.getPaymentMethod()) && Objects.nonNull(item.getPaidAmount())).mapToInt(IncomePaidAllocationEntity::getPaidAmount).sum();
//            int alreadyRefundAmount = incomePaidAllocationEntitiesList.stream().filter(item -> !OmniPayTypeEnum.PRODUCTION_COIN.getCode().toString().equals(item.getPaymentMethod()) && Objects.nonNull(item.getAlreadyRefundAmount())).mapToInt(IncomePaidAllocationEntity::getAlreadyRefundAmount).sum();
            orderPaySuccessGoodsRes.setPayGoodsPriceTotal(AmountChangeUtil.changeF2Y(paidAmount));
            orderPaySuccessGoodsRes.setTotalAllocationOriginPrice(AmountChangeUtil.changeF2Y(heOrderGoodsEntity.getTotalAllocationOriginPrice()));
            if (paidAmount != 0){
                orderPaySuccessGoodsRes.setPayGoodsPrice(orderPaySuccessGoodsRes.getPayGoodsPriceTotal().divide(new BigDecimal(heOrderGoodsEntity.getGoodsNum()), 2, BigDecimal.ROUND_HALF_UP));
            } else {
                orderPaySuccessGoodsRes.setPayGoodsPrice(BigDecimal.ZERO);
            }

            int productionAmount = incomePaidAllocationEntitiesList.stream().filter(item -> OmniPayTypeEnum.PRODUCTION_COIN.getCode().toString().equals(item.getPaymentMethod()) && Objects.nonNull(item.getPaidAmount())).mapToInt(IncomePaidAllocationEntity::getPaidAmount).sum();
//            int alreadyProductionRefundAmount = incomePaidAllocationEntitiesList.stream().filter(item -> OmniPayTypeEnum.PRODUCTION_COIN.getCode().toString().equals(item.getPaymentMethod()) && Objects.nonNull(item.getAlreadyRefundAmount())).mapToInt(IncomePaidAllocationEntity::getAlreadyRefundAmount).sum();

            orderPaySuccessGoodsRes.setPayProductionPriceTotal(AmountChangeUtil.changeF2Y(productionAmount));
            if (productionAmount != 0){
                orderPaySuccessGoodsRes.setPayProductionPrice(orderPaySuccessGoodsRes.getPayProductionPriceTotal().divide(new BigDecimal(heOrderGoodsEntity.getGoodsNum()), 2, BigDecimal.ROUND_HALF_UP));
            } else {
                orderPaySuccessGoodsRes.setPayProductionPrice(BigDecimal.ZERO);
            }
            orderPaySuccessGoodsRes.setSkuId(heOrderGoodsEntity.getSkuId());
            orderPaySuccessGoodsRes.setSkuName(heOrderGoodsEntity.getSkuName());
            orderPaySuccessGoodsRes.setGmtCreate(Objects.isNull(heOrderGoodsEntity.getCreatedAt()) ? null : new Date(heOrderGoodsEntity.getCreatedAt() * oneThousand));

            orderPaySuccessGoodsRes.setNumTotal(heOrderGoodsEntity.getGoodsNum());
            orderPaySuccessGoodsRes.setOrderGoodsSn(heOrderGoodsEntity.getOrderGoodsSn());

            List<OrderProductionVerificationLogEntity> orderProductionVerificationLogList = orderGoodsInfoDTO.getOrderProductionVerificationMap().get(heOrderGoodsEntity.getId());
            orderPaySuccessGoodsRes.setUsedNum(CollectionUtils.isEmpty(orderProductionVerificationLogList) ? zero : orderProductionVerificationLogList.size());

            orderPaySuccessGoodsRes.setStatus(heOrderGoodsEntity.getState());
            orderPaySuccessGoodsResList.add(orderPaySuccessGoodsRes);
        });
        fillGiftOrderGoods(orderGiftList, orderGoodsInfoDTO.getGoodsEntityMap(), orderPaySuccessGoodsResList);
        return orderPaySuccessGoodsResList;
    }

    private void fillGiftOrderGoods(List<OrderGiftExtendEntity> orderGiftList, Map<Integer, GoodsEntity> goodsEntityMap, List<OrderPaySuccessGoodsRes> orderPaySuccessGoodsResList){

        if (CollectionUtils.isEmpty(orderGiftList)){
            return;
        }
        List<OrderGiftExtendEntity> orderGiftGoodsList = orderGiftList.stream().filter(item -> Objects.nonNull(item.getSource()) && item.getSource() > six).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderGiftGoodsList)){
            return;
        }
        this.commonFillOrderGiftGoodsList(orderGiftGoodsList, goodsEntityMap, orderPaySuccessGoodsResList);
    }

    private Map<Integer, List<Integer>> getOrderGiftIdByOrderGoodsIdMap(List<OrderGiftExtendEntity> orderGiftList, List<HeOrderGoodsEntity> heOrderGoodsList){

        Map<Integer, List<Integer>> orderGiftIdByOrderGoodsIdMap = new HashMap<>();
        if (CollectionUtils.isEmpty(orderGiftList) || CollectionUtils.isEmpty(heOrderGoodsList)){
            return orderGiftIdByOrderGoodsIdMap;
        }
        List<HeOrderGoodsEntity> orderGoodsEntityList = heOrderGoodsList.stream().filter(item -> one.equals(item.getGift())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderGoodsEntityList)){
            return orderGiftIdByOrderGoodsIdMap;
        }
        List<OrderGiftExtendEntity> giftGoodsList = orderGiftList.stream().filter(item -> Objects.nonNull(item.getSource()) && item.getSource() <= six).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(giftGoodsList)){
            return orderGiftIdByOrderGoodsIdMap;
        }
        orderGoodsEntityList.sort(Comparator.comparing(HeOrderGoodsEntity::getId));
        giftGoodsList.sort(Comparator.comparing(OrderGiftExtendEntity::getId));
        Map<Integer, List<Integer>> giftIdsBySkuIdMap = giftGoodsList.stream().collect(Collectors.groupingBy(OrderGiftExtendEntity::getSkuId, Collectors.mapping(OrderGiftExtendEntity::getId, Collectors.toList())));
        orderGoodsEntityList.forEach(orderGoodsEntity -> {

            List<Integer> giftIds = giftIdsBySkuIdMap.get(orderGoodsEntity.getSkuId());
            orderGiftIdByOrderGoodsIdMap.put(orderGoodsEntity.getId(), CollectionUtils.isEmpty(giftIds) ? Lists.newArrayList() : giftIds.stream().limit(orderGoodsEntity.getGoodsNum()).collect(Collectors.toList()));
            giftIdsBySkuIdMap.put(orderGoodsEntity.getSkuId(), CollectionUtils.isEmpty(giftIds) ? Lists.newArrayList() : giftIds.stream().skip(orderGoodsEntity.getGoodsNum()).collect(Collectors.toList()));
        });
        return orderGiftIdByOrderGoodsIdMap;
    }

    private List<OrderGiftExtendEntity> getOrderGift(OrderGoodsInfoDTO orderGoodsInfoDTO, Integer orderId){

        if (CollectionUtils.isEmpty(orderGoodsInfoDTO.getOrderGiftExtendList())){
            return Lists.newArrayList();
        }
        return orderGoodsInfoDTO.getOrderGiftExtendList().stream().filter(item -> orderId.equals(item.getOrderId())).collect(Collectors.toList());
    }

    private GoodsEntity getGoodsEntity(Map<Integer, GoodsEntity> goodsEntityMap, GoodsInfo goodsInfo, Integer goodsId) {



        if (Objects.isNull(goodsInfo)){
            return goodsEntityMap.get(goodsId);
        }
        GoodsEntity goodsEntity = BeanMapper.map(goodsInfo, GoodsEntity.class);
        boolean checkGoodsEntity = this.checkGoodsEntity(goodsEntity);
        return checkGoodsEntity ? goodsEntity : goodsEntityMap.get(goodsId);
    }

    private boolean checkGoodsEntity(GoodsEntity goodsEntity) {
        return Objects.nonNull(goodsEntity.getId()) && StringUtils.isNotEmpty(goodsEntity.getGoodsName()) && Objects.nonNull(goodsEntity.getGoodsPrice()) && Objects.nonNull(goodsEntity.getServiceType()) && Objects.nonNull(goodsEntity.getMainSupplierId());
    }

    private Integer getSource(OrderGoodsInfoDTO orderGoodsInfoDTO, HeOrderEntity heOrderEntity, HeOrderGoodsEntity heOrderGoodsEntity) {
        List<OrderGiftExtendEntity> orderGiftExtendList = orderGoodsInfoDTO.getOrderGiftExtendList();
        OrderGiftExtendEntity orderGiftExtendEntity = orderGiftExtendList.stream().filter(item -> item.getOrderId().equals(heOrderEntity.getOrderId()) && item.getGoodsId().equals(heOrderGoodsEntity.getGoodsId()) && item.getSkuId().equals(heOrderGoodsEntity.getSkuId())).findFirst().orElse(null);
        log.info("orderGiftExtendEntity: {}", JSONUtil.toJsonStr(orderGiftExtendEntity));
        return Objects.isNull(orderGiftExtendEntity) ? zero : orderGiftExtendEntity.getSource();
    }

    private String getGoodsGiftType(Integer source){
        if (source > six){
            return "资产中心-赠送";
        }
        if (source >= zero){
            return "礼赠";
        }
        return "购买";
    }

    private List<OrderPaySuccessGoodsRes> oldOrderPaySuccessGoodsResProcess(OrderGoodsInfoDTO orderGoodsInfoDTO, HeOrderEntity heOrderEntity) {

        log.info("旧订单商品信息处理, orderId: {}", heOrderEntity.getOrderId());
        List<OrderPaySuccessGoodsRes> orderPaySuccessGoodsResList = new ArrayList<>();
        orderGift2OrderPaySuccessGoodsRes(orderGoodsInfoDTO, orderPaySuccessGoodsResList, heOrderEntity.getOrderId());
        if (CollectionUtils.isEmpty(orderGoodsInfoDTO.getOrderProductionExtendList())){
            log.info("orderProductionExtendList is empty, orderId: {}", heOrderEntity.getOrderId());
            return orderPaySuccessGoodsResList;
        }
        orderProduction2OrderPaySuccessGoodsRes(orderGoodsInfoDTO, orderPaySuccessGoodsResList, heOrderEntity.getOrderId());
        return orderPaySuccessGoodsResList;
    }

    private void orderProduction2OrderPaySuccessGoodsRes(OrderGoodsInfoDTO orderGoodsInfoDTO, List<OrderPaySuccessGoodsRes> orderPaySuccessGoodsResList, Integer orderId){

        List<OrderProductionExtendEntity> orderProductionExtendEntityList = orderGoodsInfoDTO.getOrderProductionExtendList().stream().filter(item -> orderId.equals(item.getOrderId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderProductionExtendEntityList)){
            return;
        }
        orderProductionExtendEntityList.forEach(orderProductionExtendEntity -> {

            log.info("orderProductionExtendEntity: {}", JSONUtil.toJsonStr(orderProductionExtendEntity));
            OrderPaySuccessGoodsRes orderPaySuccessGoodsRes = new OrderPaySuccessGoodsRes();
            orderPaySuccessGoodsRes.setId(orderProductionExtendEntity.getId());
            orderPaySuccessGoodsRes.setCardIdList(this.getOldOrderCardIdList(orderGoodsInfoDTO.getOrderProductionCardExtendList(), orderProductionExtendEntity.getType(), orderProductionExtendEntity.getId()));
            orderPaySuccessGoodsRes.setOrderProductionCardExtendList(this.buildOrderProductionCardExtendList(orderPaySuccessGoodsRes.getCardIdList(),orderGoodsInfoDTO.getOrderProductionCardExtendList(), orderProductionExtendEntity.getId()));
            orderPaySuccessGoodsRes.setGoodsId(orderProductionExtendEntity.getGoodsId());
            orderPaySuccessGoodsRes.setGoodsName(getGoodsName(orderProductionExtendEntity.getGoodsName(), orderProductionExtendEntity.getSkuName()));
            orderPaySuccessGoodsRes.setSource(minusOne);
            orderPaySuccessGoodsRes.setGoodsGiftType(this.getGoodsGiftType(orderPaySuccessGoodsRes.getSource()));

            GoodsEntity goodsEntity = orderGoodsInfoDTO.getGoodsEntityMap().get(orderPaySuccessGoodsRes.getGoodsId());
            orderPaySuccessGoodsRes.setMainSupplierId(Objects.isNull(goodsEntity) ? null : goodsEntity.getMainSupplierId());
            orderPaySuccessGoodsRes.setGoodsType(Objects.isNull(goodsEntity) ? null : goodsEntity.getGoodsType());
            orderPaySuccessGoodsRes.setServiceType(Objects.isNull(goodsEntity) ? null : goodsEntity.getServiceType());
            orderPaySuccessGoodsRes.setProductionDiscountRuleType(Objects.isNull(goodsEntity) ? minusOne : goodsEntity.getProductionDiscountRuleType());
            orderPaySuccessGoodsRes.setProduceAmountDeduction(Objects.isNull(goodsEntity) ? zero : goodsEntity.getProduceAmountDeduction());
            orderPaySuccessGoodsRes.setGoodsPrice(Objects.isNull(goodsEntity) ? BigDecimal.ZERO : AmountChangeUtil.changeF2Y(goodsEntity.getGoodsPrice()));
            orderPaySuccessGoodsRes.setTimeClearingPoint(Objects.isNull(goodsEntity) ? null : goodsEntity.getTimeClearingPoint());
            BigDecimal goodsSkuPrice = orderGoodsInfoDTO.getGoodsSkuPriceMap().get(orderProductionExtendEntity.getSkuId());
            orderPaySuccessGoodsRes.setGoodsSkuPrice(Objects.isNull(goodsSkuPrice) ? BigDecimal.ZERO : goodsSkuPrice);

            orderPaySuccessGoodsRes.setGoodsNum(orderProductionExtendEntity.getGoodsNum());
            orderPaySuccessGoodsRes.setPayGoodsPrice(orderProductionExtendEntity.getRealPaid());
            orderPaySuccessGoodsRes.setPayGoodsPriceTotal(orderPaySuccessGoodsRes.getPayGoodsPrice().multiply(new BigDecimal(orderPaySuccessGoodsRes.getGoodsNum())));

            orderPaySuccessGoodsRes.setPayProductionPrice(BigDecimal.ZERO);
            orderPaySuccessGoodsRes.setPayProductionPriceTotal(orderPaySuccessGoodsRes.getPayProductionPrice());

            orderPaySuccessGoodsRes.setSkuId(orderProductionExtendEntity.getSkuId());
            orderPaySuccessGoodsRes.setSkuName(orderProductionExtendEntity.getSkuName());
            orderPaySuccessGoodsRes.setGmtCreate(orderProductionExtendEntity.getGmtCreate());
            orderPaySuccessGoodsRes.setStatus(orderProductionExtendEntity.getStatus());

            JSONObject goodsNumAndUsedNumMap = this.getGoodsNumAndUsedNumMap(orderGoodsInfoDTO.getOrderProductionCardExtendList(), orderProductionExtendEntity);
            orderPaySuccessGoodsRes.setNumTotal(Integer.parseInt(goodsNumAndUsedNumMap.getString(num)));
            orderPaySuccessGoodsRes.setUsedNum(Integer.parseInt(goodsNumAndUsedNumMap.getString(usedNum)));
            orderPaySuccessGoodsRes.setDataSource(orderProductionExtendEntity.getDataSource());
            orderPaySuccessGoodsResList.add(orderPaySuccessGoodsRes);
        });
    }

    private List<OrderProductionCardExtendRes> buildOrderProductionCardExtendList(List<Integer> cardIdList, List<OrderProductionCardExtendEntity> orderProductionCardExtendList, Integer orderProductionId) {

        List<OrderProductionCardExtendRes> resList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(cardIdList)){
            log.info("cardIdList is empty, orderProductionId: {}", orderProductionId);
            return resList;
        }

        Map<Integer, List<OrderProductionCardExtendEntity>> orderProductionCardMap = orderProductionCardExtendList.stream().filter(item -> Objects.nonNull(item.getOrderProductionId())).collect(Collectors.groupingBy(OrderProductionCardExtendEntity::getOrderProductionId));
        if (orderProductionCardMap.isEmpty()){
            log.info("orderProductionCardMap is empty, orderProductionId: {}", orderProductionId);
            return resList;
        }
        List<OrderProductionCardExtendEntity> orderProductionCardExtendEntities = orderProductionCardMap.get(orderProductionId);
        log.info("orderProductionCardExtendEntities: {}", JSONUtil.toJsonStr(orderProductionCardExtendEntities));
        for (OrderProductionCardExtendEntity orderProductionCardExtendEntity : orderProductionCardExtendEntities) {

            if (!typeList.contains(orderProductionCardExtendEntity.getType()) || StringUtils.isEmpty(orderProductionCardExtendEntity.getContent()) || Objects.isNull(orderProductionCardExtendEntity.getNum())){
                log.info("orderProductionCardExtendEntity content or num is null, orderProductionCardExtendEntity: {}", JSONUtil.toJsonStr(orderProductionCardExtendEntity));
                continue;
            }
            OrderProductionCardExtendRes orderProductionCardExtendRes = BeanMapper.map(orderProductionCardExtendEntity, OrderProductionCardExtendRes.class);
            List<OrderProductionCardContentRes> contentList = Lists.newArrayList();
            if (OrderProductionItemEnum.TYPE_COUNT.code().equals(orderProductionCardExtendRes.getType())) {
                OrderProductionCardContentRes orderProductionCardContentRes = JSONUtil.toBean(orderProductionCardExtendEntity.getContent(), OrderProductionCardContentRes.class);
                if (Objects.isNull(orderProductionCardContentRes.getRealPaid())) {
                    orderProductionCardContentRes.setRealPaid(orderProductionCardExtendEntity.getRealPaid());
                }
                orderProductionCardContentRes.setRealPaidTotal(orderProductionCardContentRes.getRealPaid().multiply(new BigDecimal(orderProductionCardExtendEntity.getNum())));
                contentList.add(orderProductionCardContentRes);
            } else {
                List<OrderProductionCardContentRes> productionCardContentResList = JSONUtil.toList(orderProductionCardExtendEntity.getContent(), OrderProductionCardContentRes.class);
                productionCardContentResList.forEach(productionCardContentRes -> {
                    if (Objects.isNull(productionCardContentRes.getRealPaid())) {
                        productionCardContentRes.setRealPaid(orderProductionCardExtendEntity.getRealPaid());
                    }
                    productionCardContentRes.setRealPaidTotal(productionCardContentRes.getRealPaid().multiply(new BigDecimal(orderProductionCardExtendEntity.getNum())));
                    contentList.add(productionCardContentRes);
                });
            }
            orderProductionCardExtendRes.setContentList(contentList);
            resList.add(orderProductionCardExtendRes);
        }
        return resList;
    }

    private List<Integer> getOldOrderCardIdList(List<OrderProductionCardExtendEntity> orderProductionCardExtendList, Integer type, Integer orderProductionId) {

        List<Integer> cardIdList = Lists.newArrayList();
        if (Objects.isNull(type) || !typeList.contains(type)) {
            return cardIdList;
        }
        Map<Integer, List<OrderProductionCardExtendEntity>> orderProductionCardMap = orderProductionCardExtendList.stream().filter(item -> Objects.nonNull(item.getOrderProductionId())).collect(Collectors.groupingBy(OrderProductionCardExtendEntity::getOrderProductionId));
        if (Objects.isNull(orderProductionCardMap) || orderProductionCardMap.isEmpty()){
            return cardIdList;
        }
        List<OrderProductionCardExtendEntity> orderProductionCardExtendEntities = orderProductionCardMap.get(orderProductionId);
        if (CollectionUtils.isEmpty(orderProductionCardExtendEntities)){
            return cardIdList;
        }
        return orderProductionCardExtendEntities.stream().map(OrderProductionCardExtendEntity::getId).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private JSONObject getGoodsNumAndUsedNumMap(List<OrderProductionCardExtendEntity> orderProductionCardExtendList, OrderProductionExtendEntity orderProductionExtendEntity){

        Assert.notNull(orderProductionExtendEntity, "产康订单商品不能为空");

        int numValue = orderProductionExtendEntity.getGoodsNum();
        int usedNumValue = VerificationStatusEnum.DONE_WRITE_OFF.code().equals(orderProductionExtendEntity.getVerificationStatus()) ? orderProductionExtendEntity.getGoodsNum() : zero;

        JSONObject goodsNumAndUsedNum = new JSONObject();
        if (!typeList.contains(orderProductionExtendEntity.getType()) || CollectionUtils.isEmpty(orderProductionCardExtendList)){
            goodsNumAndUsedNum.put(usedNum, usedNumValue);
            goodsNumAndUsedNum.put(num, numValue);
            return goodsNumAndUsedNum;
        }

        Map<Integer, List<OrderProductionCardExtendEntity>> cardExtendEntityMap = orderProductionCardExtendList.stream().filter(item -> Objects.nonNull(item.getNum()) && Objects.nonNull(item.getUsedNum())).collect(Collectors.groupingBy(OrderProductionCardExtendEntity::getOrderProductionId));
        Assert.notNull(orderProductionExtendEntity.getId(), String.format("订单商品%s没有产康商品卡项子表数据（he_order_production_card_extend）", orderProductionExtendEntity.getId()));
        List<OrderProductionCardExtendEntity> orderProductionCardExtendEntityList = cardExtendEntityMap.get(orderProductionExtendEntity.getId());
        if (CollectionUtils.isEmpty(orderProductionCardExtendEntityList)){
            goodsNumAndUsedNum.put(usedNum, usedNumValue);
            goodsNumAndUsedNum.put(num, numValue);
            return goodsNumAndUsedNum;
        }
        goodsNumAndUsedNum.put(usedNum, orderProductionCardExtendEntityList.stream().mapToInt(OrderProductionCardExtendEntity::getUsedNum).sum());
        goodsNumAndUsedNum.put(num, orderProductionCardExtendEntityList.stream().mapToInt(OrderProductionCardExtendEntity::getNum).sum());
        return goodsNumAndUsedNum;
    }

    private void orderGift2OrderPaySuccessGoodsRes(OrderGoodsInfoDTO orderGoodsInfoDTO, List<OrderPaySuccessGoodsRes> orderPaySuccessGoodsResList, Integer orderId) {

        if (CollectionUtils.isEmpty(orderGoodsInfoDTO.getOrderGiftExtendList())){
            return;
        }
        List<OrderGiftExtendEntity> orderGiftExtendList = orderGoodsInfoDTO.getOrderGiftExtendList().stream().filter(item -> orderId.equals(item.getOrderId())).collect(Collectors.toList());
        this.commonFillOrderGiftGoodsList(orderGiftExtendList, orderGoodsInfoDTO.getGoodsEntityMap(), orderPaySuccessGoodsResList);
    }

    private void commonFillOrderGiftGoodsList(List<OrderGiftExtendEntity> orderGiftExtendList, Map<Integer, GoodsEntity> goodsEntityMap, List<OrderPaySuccessGoodsRes> orderPaySuccessGoodsResList){

        if (CollectionUtils.isEmpty(orderGiftExtendList)){
            return;
        }
        orderGiftExtendList.forEach(orderGiftExtendEntity -> {
            log.info("orderGiftExtendEntity: {}", JSONUtil.toJsonStr(orderGiftExtendEntity));
            OrderPaySuccessGoodsRes orderPaySuccessGoodsRes = new OrderPaySuccessGoodsRes();
            orderPaySuccessGoodsRes.setId(orderGiftExtendEntity.getId());
            orderPaySuccessGoodsRes.setCardIdList(Lists.newArrayList());
            orderPaySuccessGoodsRes.setGoodsId(orderGiftExtendEntity.getGoodsId());
            orderPaySuccessGoodsRes.setGoodsName(getGoodsName(orderGiftExtendEntity.getGoodsName(), orderGiftExtendEntity.getSkuName()));
            orderPaySuccessGoodsRes.setSource(orderGiftExtendEntity.getSource());
            orderPaySuccessGoodsRes.setGoodsGiftType(this.getGoodsGiftType(orderPaySuccessGoodsRes.getSource()));
            orderPaySuccessGoodsRes.setGoodsNum(orderGiftExtendEntity.getGoodsNum());

            GoodsEntity goodsEntity = goodsEntityMap.get(orderPaySuccessGoodsRes.getGoodsId());
            orderPaySuccessGoodsRes.setMainSupplierId(Objects.isNull(goodsEntity) ? null : goodsEntity.getMainSupplierId());
            orderPaySuccessGoodsRes.setGoodsType(Objects.isNull(goodsEntity) ? null : goodsEntity.getGoodsType());
            orderPaySuccessGoodsRes.setServiceType(Objects.isNull(goodsEntity) ? null : goodsEntity.getServiceType());
            orderPaySuccessGoodsRes.setProductionDiscountRuleType(Objects.isNull(goodsEntity) ? minusOne : goodsEntity.getProductionDiscountRuleType());
            orderPaySuccessGoodsRes.setProduceAmountDeduction(Objects.isNull(goodsEntity) ? zero : goodsEntity.getProductionDiscountRuleType());
            orderPaySuccessGoodsRes.setGoodsPrice(Objects.isNull(goodsEntity) ? BigDecimal.ZERO : AmountChangeUtil.changeF2Y(goodsEntity.getGoodsPrice()));
            orderPaySuccessGoodsRes.setTimeClearingPoint(Objects.isNull(goodsEntity) ? null : goodsEntity.getTimeClearingPoint());
            orderPaySuccessGoodsRes.setGoodsSkuPrice(BigDecimal.ZERO);

            orderPaySuccessGoodsRes.setPayGoodsPrice(orderGiftExtendEntity.getRealPaid());
            orderPaySuccessGoodsRes.setPayGoodsPriceTotal(orderGiftExtendEntity.getRealPaid().multiply(new BigDecimal(orderGiftExtendEntity.getGoodsNum())));

            orderPaySuccessGoodsRes.setPayProductionPrice(BigDecimal.ZERO);
            orderPaySuccessGoodsRes.setPayProductionPriceTotal(orderPaySuccessGoodsRes.getPayProductionPrice());

            orderPaySuccessGoodsRes.setSkuId(orderGiftExtendEntity.getSkuId());
            orderPaySuccessGoodsRes.setSkuName(orderGiftExtendEntity.getSkuName());
            orderPaySuccessGoodsRes.setGmtCreate(orderGiftExtendEntity.getGmtCreate());
            orderPaySuccessGoodsRes.setNumTotal(orderGiftExtendEntity.getGoodsNum());
            orderPaySuccessGoodsRes.setUsedNum(OrderProductionVerificationStateEnum.WRITTEN_OFF.code().equals(orderGiftExtendEntity.getVerificationStatus()) ? orderGiftExtendEntity.getGoodsNum() : zero);
            orderPaySuccessGoodsRes.setStatus(orderGiftExtendEntity.getStatus());
            orderPaySuccessGoodsRes.setDataSource(orderGiftExtendEntity.getDataSource());
            orderPaySuccessGoodsResList.add(orderPaySuccessGoodsRes);
        });
    }

    private static String getGoodsName(String goodsName, String skuName){

        if (StringUtils.isEmpty(goodsName) || StringUtils.isEmpty(skuName)){
            return goodsName;
        }
        boolean alike = goodsName.equals(skuName);
        skuName ="(" + skuName + ")";
        return goodsName.endsWith(skuName) || alike ? goodsName : goodsName + skuName;
    }

    private void checkParams(OrderPaySuccessReq req) {

        Assert.isTrue(req.getPageNum() > zero, "pageNum must be greater than 0");
        Assert.isTrue(req.getPageSize() > zero, "pageSize must be greater than 0");
        if (Objects.isNull(req.getPayTimeStart()) && Objects.isNull(req.getPayTimeEnd())){
            if (!req.getQueryNewOrder()) {
                Assert.isTrue(CollectionUtils.isNotEmpty(req.getOrderIdList())||CollectionUtils.isNotEmpty(req.getOrderSnList()), "orderIdList must not be null or empty");
            }
            return;
        }
        Assert.isTrue(Objects.nonNull(req.getPayTimeStart()), "payTimeStart must not be null or empty");
        Assert.isTrue(Objects.nonNull(req.getPayTimeEnd()), "payTimeEnd must not be null or empty");
    }
}

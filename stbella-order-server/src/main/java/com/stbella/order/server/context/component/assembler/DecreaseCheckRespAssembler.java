package com.stbella.order.server.context.component.assembler;

import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.server.order.cts.enums.ApprovalTypeEnum;
import com.stbella.platform.order.api.res.DecreaseCheckRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import java.util.Objects;

/**
 * 减免审批结果组装
 */
@Slf4j
@Component
@SnowballComponent(name = "减免审批结果组装", desc = "减免审批结果组装")
public class DecreaseCheckRespAssembler implements IExecutableAtom<FlowContext> {

    @Override
    public void run(FlowContext bizContext) {

        DecreaseCheckRes decreaseApprovalRes = new DecreaseCheckRes();
        OrderReductionEntity orderReductionEntity = bizContext.getAttribute(OrderReductionEntity.class);

        boolean containApprove = Objects.nonNull(orderReductionEntity.getUnDealApprovalId());
        decreaseApprovalRes.setContainApprove(containApprove);

        if (containApprove) {
            decreaseApprovalRes.setApproveId(orderReductionEntity.getUnDealApprovalId());
            if (Boolean.TRUE.equals(orderReductionEntity.getOrderHasRefundApprove())) {
                decreaseApprovalRes.setApproveType(ApprovalTypeEnum.REFUND.name());
            } else if (Boolean.TRUE.equals(orderReductionEntity.getDecreaseApprove())) {
                decreaseApprovalRes.setApproveType(ApprovalTypeEnum.DECREASE.name());
            } else if (Boolean.TRUE.equals(orderReductionEntity.getDisCountApprove())) {
                decreaseApprovalRes.setApproveType(ApprovalTypeEnum.DISCOUNT.name());
            }
        }

        bizContext.setAttribute(DecreaseCheckRes.class, decreaseApprovalRes);

    }

}

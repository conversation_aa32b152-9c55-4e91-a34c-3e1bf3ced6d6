package com.stbella.order.server.context.component.giftOrder.processor;

import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.domain.order.production.OrderGiftExtendEntity;
import com.stbella.order.domain.order.production.OrderProductionExtendEntity;
import com.stbella.order.domain.repository.OrderGiftExtendRepository;
import com.stbella.order.domain.repository.OrderProductionExtendRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.*;

@Component
@Slf4j
@SnowballComponent(name = "赠送订单资产处理器", desc = "设置资产有效期")
public class GiftOrderAssetHandleProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderGiftExtendRepository giftExtendRepository;
    @Resource
    private OrderProductionExtendRepository orderProductionExtendRepository;



    @Override
    public void run(FlowContext bizContext) {

        Integer giftOrderId = (Integer) bizContext.getAttribute(BizConstant.ExtraKey.giftOrderId);

        List<OrderGiftExtendEntity> giftExtendEntityList = giftExtendRepository.getByOrderId(giftOrderId);
        if (CollectionUtils.isNotEmpty(giftExtendEntityList)){
            for (OrderGiftExtendEntity giftExtendEntity : giftExtendEntityList){
                // 设置资产有效期
                giftExtendEntity.setValidEndTime(getEndTime(giftExtendEntity.getValidStartTime()).getTime() / 1000);
                giftExtendEntity.setValidityType(1);
                giftExtendEntity.setValidityValue(3);
            }
            giftExtendRepository.batchUpdateById(giftExtendEntityList);
        }

        List<OrderProductionExtendEntity> orderProductionExtendEntities = orderProductionExtendRepository.queryAllByOrderIdList(Collections.singletonList(giftOrderId));
        if (CollectionUtils.isEmpty(orderProductionExtendEntities)) {
            return;
        }
        for (OrderProductionExtendEntity orderProductionExtendEntity : orderProductionExtendEntities){

            // 设置资产有效期
            orderProductionExtendEntity.setValidEndTime(getEndTime(orderProductionExtendEntity.getValidStartTime()).getTime() / 1000);
            orderProductionExtendEntity.setValidityType(1);
            orderProductionExtendEntity.setValidityValue(3);
        }
        orderProductionExtendRepository.saveOrUpdateBatch(orderProductionExtendEntities);
    }

    private static Date getEndTime(Long seconds) {

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(seconds * 1000));
        calendar.add(Calendar.MONTH, 3);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }
}

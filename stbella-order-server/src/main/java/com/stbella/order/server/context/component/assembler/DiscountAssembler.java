package com.stbella.order.server.context.component.assembler;

import com.stbella.order.server.order.OrderIndex;
import com.stbella.platform.order.api.res.DiscountRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

/**
 * @Author: ji<PERSON><PERSON>an
 * @CreateTime: 2024-05-29  15:20
 * @Description: 折扣组装器
 * 判断是否发起折扣审批
 */
@Slf4j
@Component
@SnowballComponent(name = "折扣组装器", desc = "折扣组装，并判断是否发起折扣审批")
public class DiscountAssembler implements IExecutableAtom<FlowContext> {


    @Override
    public void run(FlowContext bizContext) {

        OrderIndex orderIndex = bizContext.getAttribute(OrderIndex.class);

        DiscountRes discountRes = new DiscountRes()
                .setDiscountMargin(orderIndex.getDiscountMargin())
                .setNetMargin(orderIndex.getNetMargin())
                .setGrossMargin(orderIndex.getGrossMargin())
                .setSignOrderDiscountMargin(orderIndex.getSignOrderDiscountMargin());

        bizContext.setAttribute(DiscountRes.class, discountRes);

    }

}

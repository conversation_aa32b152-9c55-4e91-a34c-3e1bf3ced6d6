package com.stbella.order.server.manager;

import cn.hutool.json.JSONUtil;
import com.stbella.core.base.PageVO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.order.month.req.OrderGiftByStoreReq;
import com.stbella.order.server.order.month.req.OrderMonthGiftExtendCacheReq;
import com.stbella.order.server.order.month.res.*;
import com.stbella.order.server.utils.BigDecimalUtil;
import com.stbella.platform.order.api.res.GoodsInfo;
import com.stbella.store.base.Operator;
import com.stbella.store.goodz.api.GoodsAttachmentQueryService;
import com.stbella.store.goodz.api.GoodsQueryService;
import com.stbella.store.goodz.api.SkuQueryService;
import com.stbella.store.goodz.api.StoreRoomQueryService;
import com.stbella.store.goodz.api.template.GoodsGiftStoreQueryService;
import com.stbella.store.goodz.req.*;
import com.stbella.store.goodz.res.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 商品套餐
 * @date 2021/12/6 3:06 下午
 */
@Component
@Slf4j
public class GoodsManager {
    @DubboReference
    private GoodsQueryService goodsQueryService;

    @DubboReference
    private SkuQueryService skuQueryService;

    @DubboReference
    private GoodsGiftStoreQueryService goodsGiftStoreQueryService;
    @DubboReference
    private StoreRoomQueryService storeRoomQueryService;
    @Resource
    private OrderRepository orderRepository;
    @DubboReference
    private GoodsAttachmentQueryService goodsAttachmentQueryService;
    @Value("${default.skuSpecification}")
    private String skuSpecification;


    public PackageInformationVo getSimpleGoodsInfoByGoodsIdAndSkuId(Integer goodsId, Integer skuId) {
        try {
            return goodsQueryService.getSimpleGoodsInfoByGoodsIdAndSkuId(goodsId, skuId);
        } catch (Exception e) {
            log.error("获取商品信息异常：{}", e.getMessage());
            return null;
        }

    }

    /**
     * spu 批量查询
     *
     * @param ids
     * @return List<SpuVo>
     */

    public List<SpuVo> getGoodsListWithIds(List<Integer> ids) {
        Result<List<SpuVo>> spuListWithIds = goodsQueryService.getSpuListWithIds(ids);
        return spuListWithIds.getData();
    }

    public List<SpuVo> getGoodsListWithIdsV2(List<Integer> ids) {
        Result<List<SpuVo>> spuListWithIds = goodsQueryService.getSpuListWithIdsV2(ids);
        return spuListWithIds.getData();
    }


    public List<GoodsStoreGiftVo> getOrderGiftByStoreId(Integer storeId, Integer goodsType) {
        GoodsGiftStoreQuery query = new GoodsGiftStoreQuery();
        query.setStoreId(storeId.longValue());
        //查询已上架
        query.setShelvesState(1);
        query.setPageNum(1);
        query.setGoodsType(goodsType);
        //查询全部
        query.setPageSize(Integer.MAX_VALUE);
        Result<PageVO<List<GoodsStoreGiftVo>>> goodsGiftStorePageList = goodsGiftStoreQueryService.getGoodsGiftStorePageList(query);
        PageVO data = goodsGiftStorePageList.getData();
        return data.getList();
    }

    public OrderGiftByStoreIdVO getGoodsGiftStorePageList(OrderGiftByStoreReq req) {

        String searchName = req.getName();

        OrderGiftByStoreIdVO orderGiftByStoreIdVO = new OrderGiftByStoreIdVO();
        //默认没有产康金
        orderGiftByStoreIdVO.setProductionGoldQuantity(0);
        orderGiftByStoreIdVO.setNetDiscountRate(BigDecimal.ZERO);
        orderGiftByStoreIdVO.setGrossProfitMargin(BigDecimal.ZERO);
        orderGiftByStoreIdVO.setChooseTotal(0);
        orderGiftByStoreIdVO.setChooseTotalPrice(BigDecimal.ZERO.toEngineeringString());
        orderGiftByStoreIdVO.setOrderGiftByStoreCategoryVOList(new ArrayList<>());
        Integer total = 0;
        BigDecimal totalPrice = BigDecimal.ZERO;

        OrderMonthGiftExtendCacheReq orderMonthGiftExtendCacheReq = new OrderMonthGiftExtendCacheReq();
        orderMonthGiftExtendCacheReq.setOrderType(req.getOrderType());
        orderMonthGiftExtendCacheReq.setClientUid(req.getClientUid());
        orderMonthGiftExtendCacheReq.setStoreId(req.getStoreId());
        orderMonthGiftExtendCacheReq.setOperator(req.getOperator());
        orderMonthGiftExtendCacheReq.setOrderId(req.getOrderId());
        orderMonthGiftExtendCacheReq.setFromCache(req.isFromCache());
        List<OrderGiftCacheVO> orderMonthGiftExtendCache = orderRepository.getOrderMonthGiftExtendCache(orderMonthGiftExtendCacheReq);

        /*if (CollectionUtils.isEmpty(orderMonthGiftExtendCache)) {
            return orderGiftByStoreIdVO;
        }*/

        Optional<OrderGiftCacheVO> first1 = orderMonthGiftExtendCache.stream().filter(o -> ObjectUtils.isEmpty(o.getCategoryFront()) && ObjectUtils.isEmpty(o.getGoodsId())).findFirst();
        if (first1.isPresent()) {
            orderGiftByStoreIdVO.setProductionGoldQuantity(first1.get().getQuantity());
            totalPrice = totalPrice.add(new BigDecimal(first1.get().getQuantity()));
        }

        orderMonthGiftExtendCache = orderMonthGiftExtendCache.stream().filter(o -> ObjectUtils.isNotEmpty(o.getCategoryFront()) && ObjectUtils.isNotEmpty(o.getGoodsId()) && ObjectUtils.isNotEmpty(o.getSkuId())).collect(Collectors.toList());

        List<OrderGiftByStoreCategoryVO> orderGiftByStoreCategoryList = new ArrayList<>();
        //先根据套餐id获取房型的信息
        List<RoomByStoreIdVo> roomByStoreIdVos = storeRoomQueryService.queryRoomByStoreId(req.getStoreId());

        if (StringUtils.isNotEmpty(searchName)) {
            //搜索不包含家属房
//            roomByStoreIdVos = roomByStoreIdVos.stream().filter(r -> r.getRoomName().contains(req.getName())).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(roomByStoreIdVos)) {

            Integer chooseQuantity = 0;
            DecimalFormat decimalFormat = new DecimalFormat("0.00#");

            //房间没有分类，强制写死
            OrderGiftByStoreCategoryVO orderGiftByStoreCategoryVO = new OrderGiftByStoreCategoryVO();
            orderGiftByStoreCategoryVO.setCategoryFront(-1);
            orderGiftByStoreCategoryVO.setCategoryName("家属房");
            List<OrderGiftByStoreGoodsInfoVO> orderGiftByStoreGoodsInfoVOList = new ArrayList<>();
            for (RoomByStoreIdVo roomByGoodsIdVo : roomByStoreIdVos) {
                OrderGiftByStoreGoodsInfoVO orderGiftByStoreGoodsInfoVO = new OrderGiftByStoreGoodsInfoVO();
                orderGiftByStoreGoodsInfoVO.setIsShowSkuList(0);
                orderGiftByStoreGoodsInfoVO.setGoodsId(roomByGoodsIdVo.getId());
                orderGiftByStoreGoodsInfoVO.setGoodsName(roomByGoodsIdVo.getRoomName());
                orderGiftByStoreGoodsInfoVO.setLowGoodsPrice(roomByGoodsIdVo.getRoomPrice().toEngineeringString()/*RMBUtils.changeF2Y(roomByGoodsIdVo.getRoomPrice().longValue())*/);
                orderGiftByStoreGoodsInfoVO.setChooseQuantity(0);
                orderGiftByStoreGoodsInfoVOList.add(orderGiftByStoreGoodsInfoVO);

                List<OrderGiftByStoreGoodsSkuInfoVO> orderGiftByStoreGoodsInfoSkuList = new ArrayList<>();
                OrderGiftByStoreGoodsSkuInfoVO orderGiftByStoreGoodsSkuInfoVO = new OrderGiftByStoreGoodsSkuInfoVO();
                orderGiftByStoreGoodsSkuInfoVO.setSkuId(roomByGoodsIdVo.getId());
                orderGiftByStoreGoodsSkuInfoVO.setGoodsPrice(roomByGoodsIdVo.getRoomPrice().toEngineeringString()/*decimalFormat.format(roomByGoodsIdVo.getRoomPrice().divide(new BigDecimal(100)))*/);
                orderGiftByStoreGoodsSkuInfoVO.setSkuName(roomByGoodsIdVo.getRoomName());
                orderGiftByStoreGoodsSkuInfoVO.setQuantity(0);
                //判断是否已经选择了房间
                Optional<OrderGiftCacheVO> first = orderMonthGiftExtendCache.stream().filter(o -> o.getGoodsId().equals(roomByGoodsIdVo.getId()) && o.getSkuId().equals(roomByGoodsIdVo.getId())).findFirst();
                if (first.isPresent()) {
                    orderGiftByStoreGoodsSkuInfoVO.setQuantity(first.get().getQuantity());
                    total += first.get().getQuantity();
                    chooseQuantity += first.get().getQuantity();
                    orderGiftByStoreGoodsInfoVO.setChooseQuantity(first.get().getQuantity());
                    totalPrice = totalPrice.add(BigDecimalUtil.multiplication(roomByGoodsIdVo.getRoomPrice(), new BigDecimal(first.get().getQuantity())));
                }
                orderGiftByStoreGoodsInfoSkuList.add(orderGiftByStoreGoodsSkuInfoVO);

                orderGiftByStoreGoodsSkuInfoVO.setCostPrice(decimalFormat.format(roomByGoodsIdVo.getRoomCost()));

                //总原价
                orderGiftByStoreGoodsSkuInfoVO.setTotalGoodsPrice(BigDecimalUtil.multiplication(BigDecimalUtil.divide(new BigDecimal(orderGiftByStoreGoodsSkuInfoVO.getGoodsPrice()), new BigDecimal(100)), new BigDecimal(orderGiftByStoreGoodsSkuInfoVO.getQuantity())));
                //总成本
                orderGiftByStoreGoodsSkuInfoVO.setTotalCostPrice(BigDecimalUtil.multiplication(BigDecimalUtil.divide(new BigDecimal(orderGiftByStoreGoodsSkuInfoVO.getCostPrice()), new BigDecimal(100)), new BigDecimal(orderGiftByStoreGoodsSkuInfoVO.getQuantity())));
                orderGiftByStoreGoodsInfoVO.setOrderGiftByStoreGoodsInfoSkuList(orderGiftByStoreGoodsInfoSkuList);
            }
            orderGiftByStoreCategoryVO.setOrderGiftByStoreGoodsInfoVOList(orderGiftByStoreGoodsInfoVOList);
            orderGiftByStoreCategoryVO.setChooseQuantity(chooseQuantity);
            orderGiftByStoreCategoryList.add(orderGiftByStoreCategoryVO);
        }

        List<GoodsStoreGiftVo> list = getOrderGiftByStoreId(req.getStoreId(), req.getOrderType());

        if (StringUtils.isNotEmpty(searchName)) {
            //搜索
            list = list.stream().filter(l -> l.getGoodsName().contains(searchName)).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(list)) {

            //根据分类名称进行排序
            Map<Integer, List<GoodsStoreGiftVo>> byCategoryFront = list.stream().collect(Collectors.groupingBy(GoodsStoreGiftVo::getCategoryFront));

            //根据分组id
            for (Integer categoryFront : byCategoryFront.keySet()) {
                Integer chooseQuantity = 0;
                OrderGiftByStoreCategoryVO orderGiftByStoreCategoryVO = new OrderGiftByStoreCategoryVO();
                List<GoodsStoreGiftVo> goodsStoreGiftVos = byCategoryFront.get(categoryFront);
                if (CollectionUtils.isNotEmpty(goodsStoreGiftVos)) {
                    Map<Integer, List<GoodsStoreGiftVo>> byGoodsId = goodsStoreGiftVos.stream().collect(Collectors.groupingBy(GoodsStoreGiftVo::getParentId));

                    List<OrderGiftByStoreGoodsInfoVO> orderGiftByStoreGoodsInfoVO = new ArrayList<>();

//                    List<SpuVo> goodsListWithIds = getGoodsListWithIdsV2(new ArrayList<>(byGoodsId.keySet()));

                    for (Integer goodsId : byGoodsId.keySet()) {

                        OrderGiftByStoreGoodsInfoVO orderGiftByStoreGoodsInfoVO1 = new OrderGiftByStoreGoodsInfoVO();
                        orderGiftByStoreGoodsInfoVO1.setPicUrl("");
//                        if (CollectionUtils.isNotEmpty(goodsListWithIds)) {
//                            Optional<SpuVo> first = goodsListWithIds.stream().filter(s -> s.getGoodsId().equals(goodsId)).findFirst();
//                            if (first.isPresent()) {
//                                orderGiftByStoreGoodsInfoVO1.setPicUrl(first.get().getGoodsUrl());
//                            }
//                        }

                        Integer goodsChooseQuantity = 0;
                        List<GoodsStoreGiftVo> goodsStoreGiftVoList = byGoodsId.get(goodsId);
                        List<OrderGiftByStoreGoodsSkuInfoVO> orderGiftByStoreGoodsInfoSkuList = new ArrayList<>();
                        orderGiftByStoreGoodsInfoVO1.setPicUrl(goodsStoreGiftVoList.get(0).getImage());
                        BigDecimal lowPrice = BigDecimal.ZERO;


                        Integer isShowSkuList = 0;
                        for (GoodsStoreGiftVo goodsStoreGiftVo : goodsStoreGiftVoList) {
                            OrderGiftByStoreGoodsSkuInfoVO orderGiftByStoreGoodsSkuInfoVO = new OrderGiftByStoreGoodsSkuInfoVO();

                            //分类数据
                            orderGiftByStoreCategoryVO.setCategoryFront(goodsStoreGiftVo.getCategoryFront());
                            orderGiftByStoreCategoryVO.setCategoryName(goodsStoreGiftVo.getCategoryName());

                            //goods基本信息
                            orderGiftByStoreGoodsInfoVO1.setGoodsId(goodsStoreGiftVo.getGoodsId());
                            orderGiftByStoreGoodsInfoVO1.setGoodsName(goodsStoreGiftVo.getGoodsName());

                            //sku基本信息
                            orderGiftByStoreGoodsSkuInfoVO.setSkuId(goodsStoreGiftVo.getSkuId());

                            List<GoodsStoreGiftVo> skuCount = goodsStoreGiftVoList.stream().filter(g -> g.getGoodsId().equals(goodsStoreGiftVo.getGoodsId())).collect(Collectors.toList());
                            if (!(skuCount.size() == 1 && goodsStoreGiftVo.getSkuName().equals(skuSpecification)) && isShowSkuList == 0 && StringUtils.isNotEmpty(goodsStoreGiftVo.getSkuName())) {
                                isShowSkuList = 1;
                            }
                            orderGiftByStoreGoodsSkuInfoVO.setSkuName(goodsStoreGiftVo.getSkuName());
                            String goodsPrice = goodsStoreGiftVo.getGoodsPrice();
                            orderGiftByStoreGoodsSkuInfoVO.setGoodsPrice(goodsPrice);
                            orderGiftByStoreGoodsSkuInfoVO.setPicUrl("");
                            orderGiftByStoreGoodsSkuInfoVO.setQuantity(0);
                            orderGiftByStoreGoodsSkuInfoVO.setCostPrice(goodsStoreGiftVo.getCostPrice());

                            if (lowPrice.compareTo(BigDecimal.ZERO) == 0 || new BigDecimal(goodsPrice).compareTo(lowPrice) < 0) {
                                lowPrice = new BigDecimal(goodsPrice);
                            }

                            //判断是否已经选择了规格
                            Optional<OrderGiftCacheVO> first = orderMonthGiftExtendCache.stream().filter(o -> /*o.getGoodsId().equals(goodsStoreGiftVo.getGoodsId()) &&*/ o.getSkuId().equals(goodsStoreGiftVo.getSkuId())).findFirst();
                            if (first.isPresent()) {
                                orderGiftByStoreGoodsSkuInfoVO.setQuantity(first.get().getQuantity());
                                total += first.get().getQuantity();
                                chooseQuantity += first.get().getQuantity();
                                goodsChooseQuantity += first.get().getQuantity();
                                totalPrice = totalPrice.add(BigDecimalUtil.multiplication(new BigDecimal(goodsStoreGiftVo.getGoodsPrice()), new BigDecimal(first.get().getQuantity())));
                            }

                            //总原价
                            orderGiftByStoreGoodsSkuInfoVO.setTotalGoodsPrice(BigDecimalUtil.multiplication(BigDecimalUtil.divide(new BigDecimal(orderGiftByStoreGoodsSkuInfoVO.getGoodsPrice()), new BigDecimal(100)), new BigDecimal(orderGiftByStoreGoodsSkuInfoVO.getQuantity())));
                            //总成本
                            orderGiftByStoreGoodsSkuInfoVO.setTotalCostPrice(BigDecimalUtil.multiplication(BigDecimalUtil.divide(new BigDecimal(orderGiftByStoreGoodsSkuInfoVO.getCostPrice()), new BigDecimal(100)), new BigDecimal(orderGiftByStoreGoodsSkuInfoVO.getQuantity())));

                            orderGiftByStoreGoodsInfoSkuList.add(orderGiftByStoreGoodsSkuInfoVO);
                        }

                        orderGiftByStoreGoodsInfoVO1.setIsShowSkuList(isShowSkuList);
                        orderGiftByStoreGoodsInfoVO1.setChooseQuantity(goodsChooseQuantity);
                        orderGiftByStoreGoodsInfoVO1.setOrderGiftByStoreGoodsInfoSkuList(orderGiftByStoreGoodsInfoSkuList);
                        orderGiftByStoreGoodsInfoVO1.setLowGoodsPrice(lowPrice.toEngineeringString());
                        orderGiftByStoreGoodsInfoVO.add(orderGiftByStoreGoodsInfoVO1);
                    }
                    orderGiftByStoreCategoryVO.setChooseQuantity(chooseQuantity);
                    orderGiftByStoreCategoryVO.setOrderGiftByStoreGoodsInfoVOList(orderGiftByStoreGoodsInfoVO);
                }
                orderGiftByStoreCategoryList.add(orderGiftByStoreCategoryVO);
            }
        }

        Integer productionGoldQuantity = orderGiftByStoreIdVO.getProductionGoldQuantity();

        orderGiftByStoreIdVO.setChooseTotal(total + (productionGoldQuantity > 0 ? 1 : 0));
        orderGiftByStoreIdVO.setChooseTotalPrice(totalPrice.toEngineeringString());
        orderGiftByStoreIdVO.setOrderGiftByStoreCategoryVOList(orderGiftByStoreCategoryList);


        return orderGiftByStoreIdVO;
    }

    public RoomByGoodsIdVo getRoomInfoByGoodsId(Integer goodsId) {
        return storeRoomQueryService.queryRoomByGoodsId(goodsId);
    }

    public RoomByGoodsIdVo getRoomInfoById(Integer id) {
        return storeRoomQueryService.queryRoomById(id);
    }

    public StorePackageInfo getStorePackageInfo(Integer goodsId) {
        StorePackageQuery query = new StorePackageQuery();
        Operator operator = new Operator();
        operator.setOperatorGuid("720");
        query.setOperator(operator);
        query.setGoodsId(goodsId);
        Result<StorePackageInfo> storePackageInfo = goodsQueryService.getStorePackageInfo(query);
        return storePackageInfo.getData();
    }

    public GoodsAttachmentVo goodsAttachmentVo(Integer goodsId) {
        AttachmentDetailByGoodsIdQuery query = new AttachmentDetailByGoodsIdQuery();
        query.setGoodsId(goodsId);
        Result<GoodsAttachmentVo> goodsAttachmentVoResult = goodsAttachmentQueryService.queryAttachmentByGoodsId(query);
        log.info("goodsAttachmentVoResult==={}", JSONUtil.toJsonStr(goodsAttachmentVoResult));
        return goodsAttachmentVoResult.getData();
    }

    /**
     * 根据skuIdList查询商品附件
     *
     * @param skuIdList
     * @return
     */
    public List<GoodsAttachmentVo> getGoodsAttachmentBySkuIdList(List<Integer> goodsIdList) {
        if (CollectionUtils.isEmpty(goodsIdList)) {
            return new ArrayList<>();
        }
        GoodsAttachmentQuery query = new GoodsAttachmentQuery();
        query.setGoodsIds(goodsIdList);
        query.setOperator(Operator.system());
        Result<List<GoodsAttachmentVo>> listResult = goodsAttachmentQueryService.querySearchAttachmentV2(query);
        return listResult.getData();
    }

    /**
     * 根据skuIdList查询商品
     *
     * @param skuIds
     * @return
     */
    public Map<Integer, SkuVo> getSkuByIds(Set<Integer> skuIds) {

        SkuQueryReq ids = new SkuQueryReq();
        ids.defaultPageSet();
        ids.setPageSize(100);
        ids.setSkuIds(skuIds.stream().collect(Collectors.toList()));
        Result<List<SkuVo>> skuResult = skuQueryService.queryAllSku(ids);
        if (!skuResult.getSuccess()) {
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code() + "", skuResult.getMsg());
        }
        log.info("商品服务查询结果[{}]", JSONUtil.toJsonStr(skuResult.getData()));

        Map<Integer, SkuVo> skuMap = skuResult.getData().stream().collect(Collectors.toMap(SkuVo::getId, Function.identity(), (item1, item2) -> item1));

        return skuMap;
    }

    public List<GoodsInfo> queryGoodsInfoByGoodsIdList(List<Integer> goodsIdList) {
        List<GoodsInfo> result = new ArrayList<>();
        Result<List<GoodsInfoVO>> listResult = goodsQueryService.queryGoodsInfoByGoodsIdList(goodsIdList);
        log.info("商品服务查询结果[{}]", JSONUtil.toJsonStr(listResult.getData()));
        List<GoodsInfoVO> goodsInfoVOList = listResult.getData();
        if (CollectionUtils.isNotEmpty(goodsInfoVOList)) {
            // 将GoodsInfoVO转换为GoodsInfo
            result = goodsInfoVOList.stream().map(goodsInfoVO -> {
                GoodsInfo goodsInfo = BeanMapper.map(goodsInfoVO, GoodsInfo.class);
                // 如果有自定义属性，将可用阶段字段赋值到goodsInfo
                if (goodsInfoVO.getUserDefinedProperties() != null) {
                    for (GoodsUserDefinedPropertyVO property : goodsInfoVO.getUserDefinedProperties()) {
                        if ("可用阶段".equals(property.getPropertyName()) &&
                            property.getPropertyValue() != null &&
                            !property.getPropertyValue().isEmpty()) {
                            // 取第一个值作为availableStage
                            goodsInfo.setAvailableStage(property.getPropertyValue().get(0));
                            break;
                        }
                    }
                }
                return goodsInfo;
            }).collect(Collectors.toList());
        }

        return result;
    }

}

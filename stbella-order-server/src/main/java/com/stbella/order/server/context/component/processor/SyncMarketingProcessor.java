package com.stbella.order.server.context.component.processor;

import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.core.CartSceneEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.server.manager.MarketingManager;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import com.stbella.platform.order.api.res.PromotionInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 同步到活动中心
 */
@Component
@Slf4j
@SnowballComponent(name = "发起折扣审批", desc = "")
public class SyncMarketingProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private MarketingManager marketingManager;

    @Override
    public void run(FlowContext bizContext) {
        List<SkuDetailInfo> skuDetailInfos = bizContext.getListAttribute(SkuDetailInfo.class);
        List<PromotionInfo> promotionInfos = skuDetailInfos.stream().filter(o -> Objects.nonNull(o.getPromotionInfo())).map(SkuDetailInfo::getPromotionInfo).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(promotionInfos)) {
            log.info("同步到活动中心");
            HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
            marketingManager.syncOrder(orderEntity, promotionInfos);
        }
    }

//    @Override
//    public boolean condition(FlowContext bizContext) {
//        Integer scene  = (Integer)bizContext.getAttribute(BizConstant.ExtraKey.scene);
//        return !CartSceneEnum.CUSTOMIZE_AS_YOU_WISH.code().equals(scene);
//    }

}

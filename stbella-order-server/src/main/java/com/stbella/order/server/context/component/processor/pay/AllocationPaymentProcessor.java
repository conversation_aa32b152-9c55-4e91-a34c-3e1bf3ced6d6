package com.stbella.order.server.context.component.processor.pay;

import cn.hutool.json.JSONObject;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.server.context.component.spi.AllocateSpi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.SnowballExtensionInvoker;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

@Slf4j
@Component
@SnowballComponent(name = "分摊实付", desc = "分摊一笔实付金额到指定商品")
public class AllocationPaymentProcessor implements IExecutableAtom<FlowContext> {

    @Override
    public void run(FlowContext bizContext) {
        HeIncomeRecordEntity incomeRecordEntity = bizContext.getAttribute(HeIncomeRecordEntity.class);
        JSONObject jsonObject = SnowballExtensionInvoker.invoke(AllocateSpi.class, incomeRecordEntity);
//        bizContext.setAttribute(JSONObject.class, jsonObject);
    }
}
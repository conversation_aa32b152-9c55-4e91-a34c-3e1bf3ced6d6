package com.stbella.order.server.order.month.service.impl;

import com.stbella.base.server.ding.response.CreateOrderApproveRecordVO;
import com.stbella.core.base.Operator;
import com.stbella.core.result.Result;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.server.manager.MonthDingManager;
import com.stbella.order.server.order.cts.enums.ApprovalTypeEnum;
import com.stbella.order.server.order.month.enums.CreateApproveStatusEnum;
import com.stbella.order.server.order.month.req.MonthDingContractPaperReq;
import com.stbella.order.server.order.month.req.MonthDingReq;
import com.stbella.order.server.order.month.req.OrderProductionDiscountApprovalReq;
import com.stbella.order.server.order.month.req.SubmitDepositRefundApplyV2Request;
import com.stbella.order.server.order.month.service.MonthDingService;
import com.stbella.platform.order.api.reduction.req.DecreaseReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: MonthDingServiceImpl
 * @date 2022/12/12 3:47 下午
 */
@Slf4j
@Service
@DubboService
public class MonthDingServiceImpl implements MonthDingService {

    @Resource
    private MonthDingManager monthDingManager;

    /**
     * 创建合同修改审批
     *
     * @param monthDingReq
     * @return
     */
    @Override
    public Result<CreateOrderApproveRecordVO> createContractModificationApproval(MonthDingReq monthDingReq) {
        return Result.success(monthDingManager.createContractModificationApproval(monthDingReq));
    }

    @Override
    public Result<CreateOrderApproveRecordVO> createContractModificationApproval(MonthDingReq monthDingReq, String content) {
        return Result.success(monthDingManager.createContractModificationApproval(monthDingReq, content));
    }

    /**
     * 创建订单折扣审批
     *
     * @param monthDingReq
     * @return
     */
    @Override
    public Result<CreateOrderApproveRecordVO> createOrderDiscountApproval(MonthDingReq monthDingReq) {
        return Result.success(monthDingManager.createOrderDiscountApproval(monthDingReq.getOrderId(), monthDingReq.getOperator().getOperatorPhone()));
    }

    /**
     * 创建产康订单折扣审批
     *
     * @param req
     * @return
     */
    @Override
    public Result<CreateOrderApproveRecordVO> createProductionDiscountApproval(OrderProductionDiscountApprovalReq req) {
        return Result.success(monthDingManager.createProductionDiscountApproval(req));
    }

    /**
     * 重新发起-产康订单折扣审批
     *
     * @param localInstanceId
     * @return
     */
    @Override
    public Result<CreateOrderApproveRecordVO> relaunchProductionDiscountApproval(String localInstanceId) {
        return Result.success(monthDingManager.relaunchProductionDiscountApproval(localInstanceId));
    }

    /**
     * 创建纸质合同
     *
     * @param dingContractPaperReq
     * @return
     */
    @Override
    public Result<CreateOrderApproveRecordVO> createPaperContractApproval(MonthDingContractPaperReq dingContractPaperReq) {
        return Result.success(monthDingManager.createPaperContractApproval(dingContractPaperReq));
    }

    /**
     * 创建退款审批（线下汇款）
     *
     * @param monthDingReq
     * @return
     */
    @Override
    public Result<CreateOrderApproveRecordVO> createRefundApprovalOfflineRemittance(MonthDingReq monthDingReq) {
        return null;
    }

    /**
     * 创建退款审批（原路退回）
     *
     * @param monthDingReq
     * @return
     */
    @Override
    public Result<CreateOrderApproveRecordVO> createRefundApprovalOriginalRoad(MonthDingReq monthDingReq) {
        return null;
    }


    /**
     * 创建押金退款审批（原路退回）
     *
     * @param request
     * @return {@link Result}<{@link CreateOrderApproveRecordVO}>
     */
    @Override
    public Result<CreateOrderApproveRecordVO> createDepositApproval(SubmitDepositRefundApplyV2Request request) {
        return Result.success(monthDingManager.createDepositApproval(request, request.getOrderRefundId()));
    }

    /**
     * 更新订单折扣审批
     * 折扣审批状态 0=无需审批 1=审批中 2=审批通过 3=审批失败 4=发起失败
     *
     * @param orderId
     * @param discountStatus
     */
    @Override
    public Result<Integer> updateOrderDiscountStatus(Integer orderId, Integer discountStatus) {
        return monthDingManager.updateOrderDiscountStatus(orderId, discountStatus);
    }

    @Override
    public Result<CreateOrderApproveRecordVO> discountRelaunchApprove(String id,String phone){
        return monthDingManager.discountRelaunchApprove(id,phone);
    }


}

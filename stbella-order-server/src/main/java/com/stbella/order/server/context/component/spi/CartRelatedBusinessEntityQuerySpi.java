package com.stbella.order.server.context.component.spi;

import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.server.context.component.assembler.CustomerAssembler;
import com.stbella.order.server.order.OrderPrice;
import com.stbella.platform.order.api.req.CreateOrderReq;
import com.stbella.platform.order.api.req.RestoreCartReq;
import com.stbella.platform.order.api.res.CartRes;
import top.primecare.snowball.extpoint.annotation.FuncPointSpi;
import top.primecare.snowball.extpoint.definition.IFuncSpiSupport;

/**
 * <AUTHOR>
 * 购物车关联业务对象查询扩展点
 * 区分订单与报价单
 */
@FuncPointSpi(name = "购物车关联业务对象查询扩展点", attachToComponent = CustomerAssembler.class)
public interface CartRelatedBusinessEntityQuerySpi extends IFuncSpiSupport<RestoreCartReq, CartRes> {

}

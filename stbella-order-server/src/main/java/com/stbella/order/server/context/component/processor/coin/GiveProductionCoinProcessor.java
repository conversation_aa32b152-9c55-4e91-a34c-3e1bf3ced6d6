package com.stbella.order.server.context.component.processor.coin;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.core.base.Operator;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OrderRefundGoodsStatusEnum;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.common.enums.production.OrderProductionTypeEnum;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundGoodsEntity;
import com.stbella.order.domain.order.month.entity.HeUserProductionAmountListEntity;
import com.stbella.order.domain.order.service.OrderGoodsDomainService;
import com.stbella.order.domain.repository.HeUserProductionAmountListRepository;
import com.stbella.order.server.order.month.service.impl.OrderAssetTradeService;
import com.stbella.order.server.utils.RefundGoodsNumUtil;
import com.stbella.store.common.enums.core.GoodsTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: JJ
 * @CreateTime: 2024-05-28  13:55
 * @Description: 发放产康金组件
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "发放产康金组件")
public class GiveProductionCoinProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderAssetTradeService orderAssetTradeService;
    @Resource
    private HeUserProductionAmountListRepository heUserProductionAmountListRepository;
    @Resource
    OrderGoodsDomainService orderGoodsDomainService;

    @Override
    public boolean condition(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        return Objects.nonNull(orderEntity) && (orderEntity.isJustEffect() || orderEntity.isJustFullPayment());
    }



    @Override
    public void run(FlowContext bizContext) {

        //判断商品中是否有产康金商品（assetType=22），分两种场景发放：商品中包含的，额外送的。(只处理单品和组合中的单品)
        HeOrderEntity heOrder = bizContext.getAttribute(HeOrderEntity.class);
        List<HeOrderGoodsEntity> orderGoodsEntities = orderGoodsDomainService.queryOrderGoodsWithExpand(heOrder.getOrderId());
        //heOrderGoodsEntityList的goodsNum 需要减去退款成功、退款中的
        List<HeOrderRefundGoodsEntity> refundGoodsEntities = bizContext.getListAttribute(HeOrderRefundGoodsEntity.class);
        RefundGoodsNumUtil.dealGoodsNum(orderGoodsEntities, refundGoodsEntities, Arrays.asList(OrderRefundGoodsStatusEnum.REFUNDING.code(), OrderRefundGoodsStatusEnum.SUCCESS.code()));

        List<HeOrderGoodsEntity> productionCoinList = new ArrayList<>();

        orderGoodsEntities.forEach(sku -> {
            //处理组合商品
            if (CompareUtil.integerEqual(CombineTypeEnum.COMBINE.code(), sku.getType())) {
                if (CollectionUtil.isNotEmpty(sku.getSubList())) {
                    sku.getSubList().forEach(childSku -> {
                        HeOrderGoodsEntity productionCoin = filterProductionCoin(childSku);
                        if (ObjectUtil.isNotNull(productionCoin)) {
                            productionCoinList.add(productionCoin);//组合商品的产康金
                        }
                    });
                }
            } else {
                //单品
                HeOrderGoodsEntity productionCoin = filterProductionCoin(sku);
                if (ObjectUtil.isNotNull(productionCoin)) {
                    productionCoinList.add(productionCoin);//单品
                }
            }
        });

        if (CollectionUtil.isEmpty(productionCoinList)) {
            log.info("订单中没有产康金商品，无需发放");
            return;
        }

        // 根据可用阶段分组：孕期和非孕期
        Map<Boolean, List<HeOrderGoodsEntity>> groupedByAvailableStage = productionCoinList.stream().collect(Collectors.groupingBy(HeOrderGoodsEntity::isPregnancyStageGoods));

        //孕期发放的资产
        List<HeOrderGoodsEntity> pregnancyGoods = groupedByAvailableStage.get(Boolean.TRUE);
        List<HeOrderGoodsEntity> fullPayGoods = groupedByAvailableStage.get(Boolean.FALSE);


        if (heOrder.isJustEffect() && CollectionUtil.isNotEmpty(pregnancyGoods)) {
            log.info("孕期需要发放产康金数量为{}", pregnancyGoods.size());
            // 分组：商品中包含的 or 额外送的
            {

                pregnancyGoods.stream().filter(p -> p.getGift() == 0).forEach(combinationProductionCoin -> {
                    Integer combinationProductionCoiNum = combinationProductionCoin.getGoodsNum() * 100;
                    givePregnancy(heOrder, Operator.system(), getOrderProductionTypeEnum(heOrder, combinationProductionCoin),
                            combinationProductionCoiNum.longValue(), combinationProductionCoin.getOrderGoodsSn(),
                            combinationProductionCoin.getGoodsName());
                    log.info("发放孕期生效产康金，组合中包含的固定部分，订单号：{}，产康金：{}，商品SN {}", heOrder.getOrderId(), combinationProductionCoiNum, combinationProductionCoin.getOrderGoodsSn());
                });
            }
            {
                pregnancyGoods.stream().filter(p -> p.getGift() == 1).forEach(productionCoin -> {
                    Integer combinationProductionCoiNum = productionCoin.getGoodsNum() * 100;
                    givePregnancy(heOrder, Operator.system(), getOrderProductionTypeEnum(heOrder, productionCoin), combinationProductionCoiNum.longValue(), productionCoin.getOrderGoodsSn(), productionCoin.getGoodsName());
                    log.info("发放孕期生效产康金，礼赠部分，订单号：{}，产康金：{}，商品SN {}", heOrder.getOrderId(), combinationProductionCoiNum, productionCoin.getOrderGoodsSn());
                });
            }
        }

        if (heOrder.isJustFullPayment() && CollectionUtil.isNotEmpty(fullPayGoods)) {
            log.info("完款需要发放产康金数量为{}", fullPayGoods.size());
            // 分组：商品中包含的 or 额外送的
            {

                fullPayGoods.stream().filter(p -> p.getGift() == 0).forEach(combinationProductionCoin -> {
                    Integer combinationProductionCoiNum = combinationProductionCoin.getGoodsNum() * 100;
                    give(heOrder, Operator.system(), getOrderProductionTypeEnum(heOrder, combinationProductionCoin), combinationProductionCoiNum.longValue(), combinationProductionCoin.getOrderGoodsSn(), combinationProductionCoin.getGoodsName());
                    log.info("发放产康金，组合中包含的固定部分，订单号：{}，产康金：{}，商品SN {}", heOrder.getOrderId(), combinationProductionCoiNum, combinationProductionCoin.getOrderGoodsSn());
                });
            }
            {
                fullPayGoods.stream().filter(p -> p.getGift() == 1).forEach(productionCoin -> {
                    Integer combinationProductionCoiNum = productionCoin.getGoodsNum() * 100;
                    give(heOrder, Operator.system(), getOrderProductionTypeEnum(heOrder, productionCoin), combinationProductionCoiNum.longValue(), productionCoin.getOrderGoodsSn(), productionCoin.getGoodsName());
                    log.info("发放产康金，礼赠部分，订单号：{}，产康金：{}，商品SN {}", heOrder.getOrderId(), combinationProductionCoiNum, productionCoin.getOrderGoodsSn());
                });
            }
        }


    }

    private void givePregnancy(HeOrderEntity order, Operator operator,
                               OrderProductionTypeEnum productionTypeEnum,
                               Long productionAmount, String orderGoodsSn,
                               String goodsName) {
        HeUserProductionAmountListEntity heUserProductionAmountListEntity = new HeUserProductionAmountListEntity();
        heUserProductionAmountListEntity.setBasicId(order.getBasicUid());
        heUserProductionAmountListEntity.setTotalAmount(productionAmount);
        heUserProductionAmountListEntity.setSource(productionTypeEnum.code());
        heUserProductionAmountListEntity.setClientType(7);
        heUserProductionAmountListEntity.setOperatorId(Integer.valueOf(operator.getOperatorGuid()));
        heUserProductionAmountListEntity.setOperator(operator.getOperatorName());
        heUserProductionAmountListEntity.setOrderId(Long.valueOf(order.getOrderId()));
        heUserProductionAmountListEntity.setCreatedAt(Math.toIntExact(System.currentTimeMillis() / 1000));
        log.info("新增产康金参数={}", JSONUtil.toJsonStr(heUserProductionAmountListEntity));
        heUserProductionAmountListRepository.saveOne(heUserProductionAmountListEntity);
        BigDecimal fxRate = Optional.ofNullable(order.getFxRate()).orElse(BigDecimal.ONE);
        Long amount = fxRate.multiply(new BigDecimal(productionAmount)).divide(BigDecimal.ONE, 0, RoundingMode.HALF_UP).longValue();
        String uniqueId = order.getOrderId() + "" + productionTypeEnum.code() + "-save-" + orderGoodsSn;
        List<Long> streamIdList = orderAssetTradeService.savePregnancyUsableCkj(uniqueId, order.getBasicUid() + "",
                order.getOrderId(), amount, productionTypeEnum.code(),
                orderAssetTradeService.getSellOperator(Integer.valueOf(operator.getOperatorGuid())), orderGoodsSn, goodsName);

        log.info("streamIdList 列表为 {}", JSONObject.toJSONString(streamIdList));
    }


    private OrderProductionTypeEnum getOrderProductionTypeEnum(HeOrderEntity heOrder, HeOrderGoodsEntity heOrderGoodsEntity) {

        if (Arrays.asList(OmniOrderTypeEnum.MONTH_ORDER.getCode(), OmniOrderTypeEnum.SMALL_MONTH_ORDER.getCode(),
                OmniOrderTypeEnum.OTHER_MONTH_ORDER.getCode(), OmniOrderTypeEnum.NURSE_OUTSIDE_ORDER.getCode()).contains(heOrder.getOrderType())) {

            if (heOrderGoodsEntity.getGift() == 0) {
                return OrderProductionTypeEnum.BUY;
            } else {
                if (Objects.isNull(heOrderGoodsEntity.getPromotionInfo())) {
                    return OrderProductionTypeEnum.GIFT;
                } else {
                    return OrderProductionTypeEnum.PROMOTION_GIFT;
                }
            }
        } else {
            if (heOrderGoodsEntity.getGift() == 0) {
                return OrderProductionTypeEnum.PRODUCTION_BUY;
            } else {
                if (Objects.isNull(heOrderGoodsEntity.getPromotionInfo())) {
                    return OrderProductionTypeEnum.PRODUCTION_GIFT;
                } else {
                    return OrderProductionTypeEnum.PRODUCTION_PROMOTION_GIFT;
                }
            }
        }
    }

    /**
     * 判断是否产康金
     *
     * @param sku
     * @return
     */
    protected HeOrderGoodsEntity filterProductionCoin(HeOrderGoodsEntity sku) {
        if (CompareUtil.integerEqual(GoodsTypeEnum.Production_Coin.code(), sku.getGoodsType())) {
            return sku;
        }
        return null;
    }

    /**
     * 发放产康金，本地记录+ 资产中心记录
     *
     * @param order
     * @param operator
     * @param productionTypeEnum
     * @param productionAmount
     */
    protected void give(HeOrderEntity order, Operator operator,
                        OrderProductionTypeEnum productionTypeEnum,
                        Long productionAmount, String orderGoodsSn,
                        String goodsName) {
        HeUserProductionAmountListEntity heUserProductionAmountListEntity = new HeUserProductionAmountListEntity();
        heUserProductionAmountListEntity.setBasicId(order.getBasicUid());
        heUserProductionAmountListEntity.setTotalAmount(productionAmount);
        heUserProductionAmountListEntity.setSource(productionTypeEnum.code());
        heUserProductionAmountListEntity.setClientType(7);
        heUserProductionAmountListEntity.setOperatorId(Integer.valueOf(operator.getOperatorGuid()));
        heUserProductionAmountListEntity.setOperator(operator.getOperatorName());
        heUserProductionAmountListEntity.setOrderId(Long.valueOf(order.getOrderId()));
        heUserProductionAmountListEntity.setCreatedAt(Math.toIntExact(System.currentTimeMillis() / 1000));
        log.info("新增产康金参数={}", JSONUtil.toJsonStr(heUserProductionAmountListEntity));
        heUserProductionAmountListRepository.saveOne(heUserProductionAmountListEntity);
        BigDecimal fxRate = Optional.ofNullable(order.getFxRate()).orElse(BigDecimal.ONE);
        Long amount = fxRate.multiply(new BigDecimal(productionAmount)).divide(BigDecimal.ONE, 0, RoundingMode.HALF_UP).longValue();
        String uniqueId = order.getOrderId() + "" + productionTypeEnum.code() + "-save-" + orderGoodsSn;
        List<Long> streamIdList = orderAssetTradeService.saveUsableCkj(uniqueId, order.getBasicUid() + "",
                order.getOrderId(), amount, productionTypeEnum.code(),
                orderAssetTradeService.getSellOperator(Integer.valueOf(operator.getOperatorGuid())), orderGoodsSn, goodsName);

        log.info("streamIdList 列表为 {}", JSONObject.toJSONString(streamIdList));
    }
}

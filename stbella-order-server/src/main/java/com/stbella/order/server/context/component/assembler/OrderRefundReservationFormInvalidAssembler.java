package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.collection.CollectionUtil;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.server.order.production.api.ProductionAppointmentCommandService;
import com.stbella.order.server.order.production.req.AppointmentRefundReq;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
@SnowballComponent(name = "预约单失效", desc = "预约单失效")
public class OrderRefundReservationFormInvalidAssembler implements IExecutableAtom<FlowContext> {


    @Resource
    private ProductionAppointmentCommandService productionAppointmentCommandService;

    @Override
    public boolean condition(FlowContext context) {
        return (boolean) context.getAttribute(OtherConstant.CONTINUE);
    }

    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        //主退款
        HeOrderRefundEntity refundEntity = bizContext.getAttribute(HeOrderRefundEntity.class);
        //请求参数
        CreateRefundReq attribute = bizContext.getAttribute(CreateRefundReq.class);
        //退款商品信息
        List<CreateRefundReq.GoodsInfo> goodsInfoList = attribute.getGoodsInfoList();
        //根据goodsId分
        Map<Integer, List<CreateRefundReq.GoodsInfo>> groupByGoodsId = goodsInfoList.stream().collect(Collectors.groupingBy(CreateRefundReq.GoodsInfo::getGoodsId));

        AppointmentRefundReq req = new AppointmentRefundReq();
        req.setOrderId(orderEntity.getOrderId());
        req.setRefundId(refundEntity.getId());
        req.setOptUserId(attribute.getOperator().getOperatorGuid());
        List<AppointmentRefundReq.GoodsInfo> goodsInfos = new ArrayList<>();

        for (Integer goodsId : groupByGoodsId.keySet()) {

            List<CreateRefundReq.GoodsInfo> goodsInfosList = groupByGoodsId.get(goodsId);
            int totalRefundNum = goodsInfosList.stream().mapToInt(CreateRefundReq.GoodsInfo::getRefundNum).sum();
            if (totalRefundNum <= 0) {
                continue;
            }

            AppointmentRefundReq.GoodsInfo goodsInfoReq = new AppointmentRefundReq.GoodsInfo();
            goodsInfoReq.setGoodsId(goodsId);
            goodsInfoReq.setSkuId(goodsInfosList.get(0).getSkuId());
            goodsInfos.add(goodsInfoReq);
        }
        req.setGoodsInfos(goodsInfos);

        //只有有退款商品才去失效预约单
        if (CollectionUtil.isNotEmpty(goodsInfos)) {
            productionAppointmentCommandService.appointmentRefund(req);
        }



    }

}

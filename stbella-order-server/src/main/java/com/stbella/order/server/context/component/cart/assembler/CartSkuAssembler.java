package com.stbella.order.server.context.component.cart.assembler;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.domain.order.entity.HeCartEntity;
import com.stbella.order.domain.order.entity.HeCartGoodsEntity;
import com.stbella.order.domain.repository.HeCartGoodsRepository;
import com.stbella.order.server.convert.CartGoodsEntityConverter;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: ji<PERSON><PERSON><PERSON>
 * @CreateTime: 2024-05-29  15:20
 * @Description: 购物车id构建skuList
 */
@Component
@Slf4j
@SnowballComponent(name = "购物车商品组装", desc = "购物车id构建skuList")
public class CartSkuAssembler implements IExecutableAtom<FlowContext> {
    @Resource
    private HeCartGoodsRepository heCartGoodsRepository;
    @Resource
    private CartGoodsEntityConverter cartGoodsEntityConverter;

    @Override
    public void run(FlowContext bizContext) {

        HeCartEntity cartEntity = bizContext.getAttribute(HeCartEntity.class);

        List<HeCartGoodsEntity> goodsEntityList = heCartGoodsRepository.queryListByCartId(cartEntity.getCartId());
        cartEntity.setGoodsList(goodsEntityList);

        List<SkuDetailInfo> skuList = ObjectUtil.defaultIfNull(goodsEntityList, new ArrayList<HeCartGoodsEntity>()).stream()
               .map(goodsEntity -> {
                    SkuDetailInfo sku = cartGoodsEntityConverter.entity2Res(goodsEntity);
                    if (goodsEntity.getType().intValue() == CombineTypeEnum.COMBINE.code()){
                        List<SkuDetailInfo> subList = cartGoodsEntityConverter.entityList2ResList(goodsEntity.getSubGoodsList());
                        sku.setSubList(subList);
                    }
                   return sku;
                }).collect(Collectors.toList());

        bizContext.setListAttribute(SkuDetailInfo.class, skuList);


    }
}

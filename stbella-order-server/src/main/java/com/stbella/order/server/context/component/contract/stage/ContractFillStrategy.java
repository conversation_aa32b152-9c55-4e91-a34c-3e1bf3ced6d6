package com.stbella.order.server.context.component.contract.stage;

import cn.hutool.core.collection.CollectionUtil;
import com.stbella.contract.model.enums.TemplateContractTypeEnum;
import com.stbella.contract.model.enums.TemplateTypeEnum;
import com.stbella.contract.model.req.v3.ContractCreateV3Req;
import com.stbella.contract.model.res.ContractTemplateRes;
import com.stbella.contract.model.res.OrganizationVO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.service.OrderParamHistoryService;
import com.stbella.order.server.context.component.store.StoreFinanceAssembler;
import com.stbella.order.server.context.dto.StoreFinanceDetailDto;
import com.stbella.order.server.contract.enums.ContractExceptionEnum;
import com.stbella.order.server.contract.req.MonthUserEsignDTO;
import com.stbella.order.server.contract.req.OrderParamHistoryPushDTO;
import com.stbella.order.server.contract.req.OrderParamHistoryValuePushDTO;
import com.stbella.order.server.convert.OrderContractConverter;
import com.stbella.order.server.fact.ContractFact;
import com.stbella.order.server.manager.ContractManager;
import com.stbella.platform.order.api.contract.req.ContractBaseReq;
import com.stbella.platform.order.api.req.PosPayQrCodeRequest;
import com.stbella.platform.order.api.res.ReceiveQrcodeRes;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import com.stbella.store.server.ecp.entity.CfgStore;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.core.context.FlowContext;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 合同填充
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-23 14:51
 */
@Slf4j
public abstract class ContractFillStrategy {

    private static final List<Integer> djOrderTypeList = Arrays.asList(OmniOrderTypeEnum.DJ_MOTHER_CARE_ORDER.code(), OmniOrderTypeEnum.DJ_INFANT_CARE_ORDER.code());

    @Resource
    OrderContractConverter orderContractConverter;
    @Resource
    ContractManager contractManager;
    @Resource
    OrderParamHistoryService orderParamHistoryService;
    @Resource
    RuleLinkClient ruleLinkClient;
    @Resource
    StoreFinanceAssembler storeFinanceAssembler;

    /**
     * 特殊门店门店类型转换
     * 1030 -> 6 厦门七尚 Bella Villa
     */
    Map<Integer, Integer> SPECIAL_TYPE_MAP = new HashMap<Integer, Integer>() {
        {
            put(TemplateContractTypeEnum.YZ_SAINTBELLA.code(), 1);
            put(TemplateContractTypeEnum.SUPPLIMENT.code(), 2);
        }
    };

    /**
     * 填充合同需要的参数
     *
     * @param contractParamFillContext
     * @return {@link Map}<{@link String}, {@link String}>
     */
    public ContractCreateV3Req handle(ContractParamFillContext contractParamFillContext) {

        ContractBaseReq contractBaseReq = contractParamFillContext.getRequest();
        HeOrderEntity orderEntity = contractParamFillContext.getOrderEntity();

        ContractCreateV3Req orderContractCreateReq = orderContractConverter.toContractCreateV3Req(contractBaseReq, orderEntity);
        Long contractTemplateId = getTemplateId(contractParamFillContext);
        if (Objects.isNull(contractTemplateId)) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "当前门店暂不支持e签宝链接签合同");
        }

        //获取合同主体 - 根据门店中心
        FlowContext context = new FlowContext();
        context.setAttribute(BizConstant.ExtraKey.storeId, orderEntity.getStoreId());
        SnowballFlowLauncher.fire(context, storeFinanceAssembler);
        StoreFinanceDetailDto financeDetailDto = context.getAttribute(StoreFinanceDetailDto.class);

        OrganizationVO organizationVO = new OrganizationVO();
        organizationVO.setName(financeDetailDto.getCompanySubjectName());
        organizationVO.setBankOpenAccountBank(financeDetailDto.getBankAccountList().get(0).getOpenBank());
        organizationVO.setBankAccount(financeDetailDto.getBankAccountList().get(0).getAccountNo());
        organizationVO.setBankOpenAccountName(financeDetailDto.getBankAccountList().get(0).getAccountName());

        orderContractCreateReq.setContractTemplateId(contractTemplateId);
        log.info("订单={}合同模板ID:{}", orderEntity.getOrderSn(), contractTemplateId);
        //查找合同模版对应的参数
        ContractTemplateRes contractTemplateRes = contractManager.getContractTemplateParam(contractTemplateId);
        contractTemplateRes.setCompanyName(financeDetailDto.getCompanySubjectName());
        contractTemplateRes.setOrganizationVO(organizationVO);
        contractParamFillContext.setContractTemplateRes(contractTemplateRes);

        Map<String, String> buildParam = buildParam(contractParamFillContext);


        String contractName = getContractName(contractParamFillContext);
        //填充字符串公司名称单独处理一下
        buildParam.put("company_name", financeDetailDto.getCompanySubjectName());
        buildParam.put("companyName", financeDetailDto.getCompanySubjectName());

        orderContractCreateReq.setContractName(contractName);
        orderContractCreateReq.setParamsMap(buildParam);

        //设置合同编码
        if (buildParam.containsKey("contract_no")) {
            orderContractCreateReq.setContractNo(buildParam.get("contract_no"));
        }

        return orderContractCreateReq;

    }

    protected abstract Map<String, String> buildParam(ContractParamFillContext contractParamFillContext);

    protected abstract String getContractName(ContractParamFillContext contractParamFillContext);

    protected Long getTemplateId(ContractParamFillContext contractParamFillContext) {

        ContractFact contractFact = setContractFact(contractParamFillContext, TemplateTypeEnum.MAIN_TYPE);
        contractFact = buildFactExtra(contractFact);
        ExecuteRuleV2Req req = new ExecuteRuleV2Req();
        req.setSceneCode(RuleLinkClient.SCENE_TEMPLATE_ID);
        req.setFactObj(contractFact);
        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(req);
        if (Objects.isNull(hitRuleVo)) {
            return null;
        }
        return Long.valueOf(hitRuleVo.getSimpleRuleValue());
    }

    protected ContractFact buildFactExtra(ContractFact contractFact) {
        return contractFact;
    }

    private ContractFact setContractFact(ContractParamFillContext contractParamFillContext, TemplateTypeEnum templateTypeEnum) {
        ContractBaseReq contractBaseReq = contractParamFillContext.getRequest();
        CfgStoreEntity cfgStore = contractParamFillContext.getCfgStore();
        HeOrderEntity order = contractParamFillContext.getOrderEntity();
        ContractFact contractFact = new ContractFact();
        Integer templateContractType = contractBaseReq.getTemplateContractType();
        contractFact.setSecondSort(templateContractType);
        contractFact.setTemplateContractType(templateContractType);
        contractFact.setContractType(contractBaseReq.getContractType());
        contractFact.setStoreId(cfgStore.getStoreId());
        contractFact.setStoreType(cfgStore.getType());
        contractFact.setChildType(cfgStore.getChildType());
        contractFact.setOrderType(order.getOrderType());
        contractFact.setOrderId(order.getOrderId());
        contractFact.setOrderSn(order.getOrderSn());
        contractFact.setClientUid(order.getClientUid());
        contractFact.setTemplateType(templateTypeEnum.code());
        contractFact.setFirstSort(templateTypeEnum.code());
        contractFact.setStandardOrderV1(order.isNewOrder());
        if (djOrderTypeList.contains(order.getOrderType())){
            contractFact.setBu(order.getBu());
            contractFact.setCareType(order.getCareType());
        }
        return contractFact;
    }

    abstract boolean filter(Integer templateContractType);


    protected Map<String, String> getOrderParam(HeOrderEntity order) {
        OrderParamHistoryPushDTO orderParamHistoryPushDTO = orderParamHistoryService.buildOrderParamHistory(order);
        List<OrderParamHistoryValuePushDTO> paramList = orderParamHistoryPushDTO.getParamList();
        if (CollectionUtil.isNotEmpty(paramList)) {
            return paramList.stream().filter(item -> StringUtils.isNotEmpty(item.getValue())).collect(Collectors.toMap(OrderParamHistoryValuePushDTO::getName, OrderParamHistoryValuePushDTO::getValue));
        }
        return new HashMap<>();
    }

    protected void checkMonthUserEsignDTO(MonthUserEsignDTO userSignDTO) {
        if (Objects.isNull(userSignDTO) || Objects.isNull(userSignDTO.getPhone())) {
            throw new BusinessException(ContractExceptionEnum.USER_ESIGN_INFO_NULL.getCode(), ContractExceptionEnum.USER_ESIGN_INFO_NULL.getValue());
        }
    }
}

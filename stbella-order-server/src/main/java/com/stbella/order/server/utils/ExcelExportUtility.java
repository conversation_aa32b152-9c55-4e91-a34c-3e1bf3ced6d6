package com.stbella.order.server.utils;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.order.server.order.cts.temp.po.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;

public class ExcelExportUtility {

	public static void export(List<PayDTO> payDTOList, List<RefundDTO> refundDTOList, List<ComboDTO> comboDTOList, List<TrainPayDTO> trainPayDTOList, String fileName, HttpServletResponse response) throws IOException {
		Workbook workbook = new XSSFWorkbook();
		Sheet pay = workbook.createSheet("支付");
		pay.autoSizeColumn(0);
		Sheet refund = workbook.createSheet("退款");
		refund.autoSizeColumn(0);
		Sheet combo = workbook.createSheet("套餐");
		combo.autoSizeColumn(0);
		Sheet trainPay = workbook.createSheet("培训支付");
		trainPay.autoSizeColumn(0);

		if(CollectionUtils.isNotEmpty(payDTOList)){
			int rowNumber = 0;
			for (int i = 0; i < payDTOList.size(); i++) {
				PayDTO payDTO = payDTOList.get(i);
				Row row = pay.createRow(rowNumber++);
				int columnNumber = 0;
				Cell cell = row.createCell(columnNumber++);
				cell.setCellValue(payDTO.getOrderNumber());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(payDTO.getOrderType());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(payDTO.getSellerName());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(payDTO.getEmployerName());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(payDTO.getNannyName());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(payDTO.getStoreName());
				cell = row.createCell(columnNumber++);
				if(i>0 && ObjectUtil.isNotEmpty(payDTO.getTotalAmount())){
					cell.setCellValue(new Double(payDTO.getTotalAmount()));
				}else{
					cell.setCellValue(payDTO.getTotalAmount());
				}
				cell = row.createCell(columnNumber++);
				cell.setCellValue(payDTO.getCreateTime());
				cell = row.createCell(columnNumber++);
				if(i>0 && ObjectUtil.isNotEmpty(payDTO.getPayAmount()) ){
					cell.setCellValue(new Double(payDTO.getPayAmount()));
				}else{
					cell.setCellValue(payDTO.getPayAmount());
				}
				cell = row.createCell(columnNumber++);
				cell.setCellValue(payDTO.getPayTime());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(payDTO.getFirstPayTime());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(payDTO.getAuditStatus());
			}

		}

		if(CollectionUtils.isNotEmpty(refundDTOList)){
			int rowNumber = 0;
			for (int i = 0; i < refundDTOList.size(); i++) {
				RefundDTO refundDTO = refundDTOList.get(i);
				Row row = refund.createRow(rowNumber++);
				int columnNumber = 0;
				Cell cell = row.createCell(columnNumber++);
				cell.setCellValue(refundDTO.getOrderNumber());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(refundDTO.getOrderType());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(refundDTO.getRegion());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(refundDTO.getSellerName());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(refundDTO.getEmployerName());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(refundDTO.getNannyName());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(refundDTO.getRefundTime());
				cell = row.createCell(columnNumber++);
				if(i>0&& ObjectUtil.isNotEmpty(refundDTO.getRefundAmount())  ){
					cell.setCellValue(new Double(refundDTO.getRefundAmount()));
				}else{
					cell.setCellValue(refundDTO.getRefundAmount());
				}

				cell = row.createCell(columnNumber++);
				cell.setCellValue(refundDTO.getRefundMethod());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(refundDTO.getAttribute());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(refundDTO.getFirstPaymentTime());
			}

		}

		if(CollectionUtils.isNotEmpty(comboDTOList)){
			int rowNumber = 0;

			for (int i = 0; i < comboDTOList.size(); i++) {
				ComboDTO comboDTO = comboDTOList.get(i);
				Row row = combo.createRow(rowNumber++);
				int columnNumber = 0;
				Cell cell = row.createCell(columnNumber++);
				cell.setCellValue(comboDTO.getOrderNumber());

				cell = row.createCell(columnNumber++);
				cell.setCellValue(comboDTO.getOrderType());

				cell = row.createCell(columnNumber++);
				cell.setCellValue(comboDTO.getSubstationName());

				cell = row.createCell(columnNumber++);
				cell.setCellValue(comboDTO.getSellerName());

				cell = row.createCell(columnNumber++);
				cell.setCellValue(comboDTO.getEmployerName());

				cell = row.createCell(columnNumber++);
				cell.setCellValue(comboDTO.getPackageLevel());

				cell = row.createCell(columnNumber++);
				if(i>0 && ObjectUtil.isNotEmpty(comboDTO.getContractDuration())  ){
					cell.setCellValue(new Double(comboDTO.getContractDuration()));
				}else{
					cell.setCellValue(comboDTO.getContractDuration());
				}

				cell = row.createCell(columnNumber++);
				cell.setCellValue(comboDTO.getNannyName());

				cell = row.createCell(columnNumber++);
				if(i>0 && ObjectUtil.isNotEmpty(comboDTO.getNannySalary())  ){
					cell.setCellValue(new Double(comboDTO.getNannySalary()));
				}else{
					cell.setCellValue(comboDTO.getNannySalary());
				}

				cell = row.createCell(columnNumber++);
				if(i>0 && ObjectUtil.isNotEmpty(comboDTO.getTotalAmount())  ){
					cell.setCellValue(new Double(comboDTO.getTotalAmount()));
				}else{
					cell.setCellValue(comboDTO.getTotalAmount());
				}

				cell = row.createCell(columnNumber++);
				if(i>0 && ObjectUtil.isNotEmpty(comboDTO.getDiscount())  ){
					cell.setCellValue(new Double(comboDTO.getDiscount()));
				}else{
					cell.setCellValue(comboDTO.getDiscount());
				}

				cell = row.createCell(columnNumber++);
				if(i>0 && ObjectUtil.isNotEmpty(comboDTO.getPaidAmount())  ){
					cell.setCellValue(new Double(comboDTO.getPaidAmount()));
				}else{
					cell.setCellValue(comboDTO.getPaidAmount());
				}

				cell = row.createCell(columnNumber++);
				cell.setCellValue(comboDTO.getCreateTime());

				cell = row.createCell(columnNumber++);
				cell.setCellValue(comboDTO.getFirstPaymentTime());

				cell = row.createCell(columnNumber++);
				cell.setCellValue(comboDTO.getAttribute());

				cell = row.createCell(columnNumber++);
				cell.setCellValue(comboDTO.getSourceChannel());

				cell = row.createCell(columnNumber++);
				cell.setCellValue(comboDTO.getStoreNumber());

				cell = row.createCell(columnNumber++);
				cell.setCellValue(comboDTO.getStoreName());

				cell = row.createCell(columnNumber++);
				cell.setCellValue(comboDTO.getContractBeginTime());

				cell = row.createCell(columnNumber++);
				cell.setCellValue(comboDTO.getContractEndTime());
			}

		}

		if(CollectionUtils.isNotEmpty(trainPayDTOList)){
			int rowNumber = 0;

			for (int i = 0; i < trainPayDTOList.size(); i++) {
				TrainPayDTO trainPayDTO = trainPayDTOList.get(i);
				Row row = trainPay.createRow(rowNumber++);
				int columnNumber = 0;
				Cell cell = row.createCell(columnNumber++);
				cell.setCellValue(trainPayDTO.getOrderNumber());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(trainPayDTO.getOrderType());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(trainPayDTO.getSellerName());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(trainPayDTO.getEmployerName());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(trainPayDTO.getNannyName());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(trainPayDTO.getStoreName());
				cell = row.createCell(columnNumber++);
				if(i>0 && ObjectUtil.isNotEmpty(trainPayDTO.getTotalAmount())  ){
					cell.setCellValue(new Double(trainPayDTO.getTotalAmount()));
				}else{
					cell.setCellValue(trainPayDTO.getTotalAmount());
				}

				cell = row.createCell(columnNumber++);
				cell.setCellValue(trainPayDTO.getCreateTime());

				cell = row.createCell(columnNumber++);
				if(i>0 && ObjectUtil.isNotEmpty(trainPayDTO.getPaymentAmount())  ){
					cell.setCellValue(new Double(trainPayDTO.getPaymentAmount()));
				}else{
					cell.setCellValue(trainPayDTO.getPaymentAmount());
				}

				cell = row.createCell(columnNumber++);
				cell.setCellValue(trainPayDTO.getPaymentTime());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(trainPayDTO.getFirstPaymentTime());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(trainPayDTO.getAuditStatus());
			}

		}

		//输出Excel文件
		OutputStream output = response.getOutputStream();
		response.reset();
		response.setHeader("Content-disposition", "attachment; filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
		response.setContentType("application/msexcel");
		workbook.write(output);
		output.flush();
		output.close();
	}


	public static void exportPaymentFlow(List<ExportPaymentFlowDTO> paymentFlowDTOArrayList, String fileName, HttpServletResponse response) throws IOException {
		Workbook workbook = new XSSFWorkbook();
		Sheet pay = workbook.createSheet("流水");
		pay.autoSizeColumn(0);


		if (CollectionUtils.isNotEmpty(paymentFlowDTOArrayList)) {
			int rowNumber = 0;
			for (int i = 0; i < paymentFlowDTOArrayList.size(); i++) {
				ExportPaymentFlowDTO exportPaymentFlowDTO = paymentFlowDTOArrayList.get(i);
				Row row = pay.createRow(rowNumber++);
				int columnNumber = 0;
				Cell cell = row.createCell(columnNumber++);
				cell.setCellValue(exportPaymentFlowDTO.getOrderId());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(exportPaymentFlowDTO.getDjjSerialNumber());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(exportPaymentFlowDTO.getPaymentSerialNumber());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(exportPaymentFlowDTO.getOrderType());
				cell = row.createCell(columnNumber++);
				if (i > 0 && ObjectUtil.isNotEmpty(exportPaymentFlowDTO.getRefundAmount())) {
					cell.setCellValue(new Double(exportPaymentFlowDTO.getRefundAmount()));
				} else {
					cell.setCellValue(exportPaymentFlowDTO.getRefundAmount());
				}
				cell = row.createCell(columnNumber++);
				cell.setCellValue(exportPaymentFlowDTO.getPaymentRefundType());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(exportPaymentFlowDTO.getPaymentMethod());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(exportPaymentFlowDTO.getPaymentAccount());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(exportPaymentFlowDTO.getFinancialCheckStatus());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(exportPaymentFlowDTO.getPaymentTime());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(exportPaymentFlowDTO.getSubstation());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(exportPaymentFlowDTO.getPackageType());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(exportPaymentFlowDTO.getClientName());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(exportPaymentFlowDTO.getClientPhone());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(exportPaymentFlowDTO.getSalesPerson());
				cell = row.createCell(columnNumber++);
				cell.setCellValue(exportPaymentFlowDTO.getOrderCreateTime());
				cell = row.createCell(columnNumber++);
				if (i > 0 && ObjectUtil.isNotEmpty(exportPaymentFlowDTO.getContractAmount())) {
					cell.setCellValue(new Double(exportPaymentFlowDTO.getContractAmount()));
				} else {
					cell.setCellValue(exportPaymentFlowDTO.getContractAmount());
				}
				cell = row.createCell(columnNumber++);
				String contractAmount = exportPaymentFlowDTO.getContractAmount();
				if (i > 0 && ObjectUtil.isNotEmpty(contractAmount) && !"/".equals(contractAmount)) {
					cell.setCellValue(new Double(contractAmount));
				} else {
					cell.setCellValue(contractAmount);
				}
				cell = row.createCell(columnNumber++);
				String serviceFee = exportPaymentFlowDTO.getServiceFee();

				if (i > 0 && ObjectUtil.isNotEmpty(serviceFee) && !"/".equals(serviceFee)) {
					cell.setCellValue(new Double(serviceFee));
				} else {
					cell.setCellValue(serviceFee);
				}
				cell = row.createCell(columnNumber++);
				cell.setCellValue(exportPaymentFlowDTO.getIsRenewal());
			}

		}


		//输出Excel文件
		OutputStream output = response.getOutputStream();
		response.reset();
		response.setHeader("Content-disposition", "attachment; filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
		response.setContentType("application/msexcel");
		workbook.write(output);
		output.flush();
		output.close();
	}

}

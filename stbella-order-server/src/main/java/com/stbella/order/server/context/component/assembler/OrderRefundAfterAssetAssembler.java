package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.stbella.asset.api.enums.TradeEventEnum;
import com.stbella.asset.api.enums.TradeType;
import com.stbella.core.base.Operator;
import com.stbella.customer.server.ecp.request.OrderRefundMinusAssetRequest;
import com.stbella.customer.server.ecp.service.HeUserIntegralRecordV2Service;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OmniRefundTypeEnum;
import com.stbella.order.common.enums.core.OrderRefundNatureEnum;
import com.stbella.order.common.enums.month.AheadOutRoomEnum;
import com.stbella.order.common.enums.production.OrderProductionTypeEnum;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.manager.AssetManager;
import com.stbella.order.server.order.month.req.AheadOutRoomQuery;
import com.stbella.order.server.order.month.request.approval.ApprovalStatusInfo;
import com.stbella.order.server.order.month.service.impl.OrderAssetTradeService;
import com.stbella.store.common.enums.core.GoodsTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
@SnowballComponent(name = "审批结束操作资产", desc = "审批结束操作资产")
public class OrderRefundAfterAssetAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderAssetTradeService assetTradeService;
    @Resource
    private HeOrderRefundGoodsRepository orderRefundGoodsRepository;
    @Resource
    private OrderGoodsRepository orderGoodsRepository;
    @Resource
    private AssetManager assetManager;
    @Resource
    private HeUserProductionAmountListRepository heUserProductionAmountListRepository;
    @DubboReference
    private HeUserIntegralRecordV2Service heUserIntegralRecordV2Service;
    @Resource
    private AheadOutRoomRepository aheadOutRoomRepository;
    @Resource
    private OrderRepository orderRepository;

    @Override
    public boolean condition(FlowContext context) {
        HeOrderEntity orderEntity = context.getAttribute(HeOrderEntity.class);
        return !orderEntity.isDepositOrder();
    }

    @Override
    public void run(FlowContext bizContext) {
        Boolean isRefund = (Boolean) bizContext.getAttribute(OtherConstant.IS_REFUND);

        if (Objects.isNull(isRefund)) {
            log.info("isRefund is null, skip refund asset");
            ApprovalStatusInfo approvalStatusInfo = bizContext.getAttribute(ApprovalStatusInfo.class);
            Boolean agree = approvalStatusInfo.isAgree();
            Boolean finish = approvalStatusInfo.isFinish();
            isRefund = finish && agree;
        }

        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        log.info("订单审批结果{},订单{}", isRefund, orderEntity.getOrderSn());

        //获取退款的商品
        List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList = (List<HeOrderRefundGoodsEntity>) bizContext.getAttribute(OtherConstant.ORDER_REFUND_GOODS_LIST);
        List<HeOrderRefundEntity> childRefundList = bizContext.getListAttribute(HeOrderRefundEntity.class);

        if (CollectionUtil.isEmpty(orderRefundGoodsEntityList)) {
            HeOrderRefundEntity refundEntity = bizContext.getAttribute(HeOrderRefundEntity.class);
            if (ObjectUtil.isNotEmpty(refundEntity)) {
                orderRefundGoodsEntityList = orderRefundGoodsRepository.queryByRefundOrderSn(refundEntity.getRefundOrderSn());
            }
        }
        if (!isRefund && CollectionUtil.isNotEmpty(orderRefundGoodsEntityList)) {
            //审批被拒绝，进行时间判断逻辑
            HeOrderRefundEntity refundEntity = bizContext.getAttribute(HeOrderRefundEntity.class);
            handleRefundRejection(orderEntity, refundEntity, orderRefundGoodsEntityList, childRefundList);
        }
    }

    /**
     * 处理退款拒绝的天才逻辑
     * 根据发起时间和订单生效时间判断是否需要补发资产
     *
     * @param orderEntity                订单实体
     * @param refundEntity               退款实体
     * @param orderRefundGoodsEntityList 退款商品列表
     */
    private void handleRefundRejection(HeOrderEntity orderEntity, HeOrderRefundEntity refundEntity,
                                       List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList, List<HeOrderRefundEntity> childRefundList) {

        // 检查订单业绩是否已生效
        Integer percentFirstTime = orderEntity.getPercentFirstTime();
        Integer bkPercentFirstTime = orderEntity.getBkPercentFirstTime();
        if ((percentFirstTime == null || percentFirstTime <= 0) && !(Objects.nonNull(bkPercentFirstTime) && bkPercentFirstTime > 0)) {
            log.info("订单{}业绩还未生效，退款拒绝时不处理资产", orderEntity.getOrderSn());
            return;
        }

        // 获取退款发起时间和订单业绩生效时间（都是秒级时间戳）
        Long refundCreateTime = refundEntity.getCreatedAt();

        log.info("退款拒绝时间判断：订单{}，退款发起时间：{}，订单业绩生效时间：{}",
                orderEntity.getOrderSn(), refundCreateTime, percentFirstTime);

        //修改提前离馆可退金额（提前离馆的逻辑继续保留）
        updateAheadOutRoomAmount(orderEntity, refundEntity);

        if (Objects.nonNull(percentFirstTime) && refundCreateTime > percentFirstTime && !(Objects.nonNull(bkPercentFirstTime) && bkPercentFirstTime > 0)) {
            // 发起时间在订单生效时间之后，不处理（资产已经正确发放了）
            log.info("退款发起时间在订单生效之后，不需要处理资产。订单：{}", orderEntity.getOrderSn());
            handleProductionCoinRefund(orderEntity, orderRefundGoodsEntityList);
            handlerRefund(orderEntity, refundEntity);
            addUserCkj(childRefundList, orderEntity.getOrderId(), refundEntity.getRefundNature());
        } else {
            // 发起时间在订单生效时间之前，需要补发资产（因为之前可能漏发了）
            log.info("退款发起时间在订单生效之前，需要补发资产。订单：{}", orderEntity.getOrderSn());
            compensateAssets(refundEntity, orderEntity, orderRefundGoodsEntityList);

        }
    }

    private void addUserCkj(List<HeOrderRefundEntity> refundByParentSn, Integer orderId, Integer refundNature) {
        if (ObjectUtil.isNotEmpty(refundNature) && OrderRefundNatureEnum.TEMP_REFUND.code().equals(refundNature)) {
            log.info("退回重付不处理产康金支付相关");
            return;
        }
        HeOrderEntity orderEntity = orderRepository.queryOrderById(orderId);
        List<HeOrderRefundEntity> cjkRefund = refundByParentSn.stream().filter(r -> r.getRefundType().equals(OmniRefundTypeEnum.PRODUCTION_COIN.getCode())).collect(Collectors.toList());
        log.info("cjkRefund size:{}", cjkRefund.size());
        for (HeOrderRefundEntity heOrderRefundEntity : cjkRefund) {
            BigDecimal fxRate = Optional.ofNullable(orderEntity.getFxRate()).orElse(BigDecimal.ONE);
            Long amount = fxRate.multiply(new BigDecimal(heOrderRefundEntity.getApplyAmount())).divide(BigDecimal.ONE, 0, RoundingMode.HALF_UP).longValue();
            assetTradeService.saveUsableCkj(
                    heOrderRefundEntity.getRefundOrderSn() + System.currentTimeMillis(),
                    orderEntity.getBasicUid().toString(),
                    orderEntity.getOrderId(),
                    amount,
                    20010,
                    assetTradeService.getSellOperator(1),
                    heOrderRefundEntity.getRefundOrderSn(),
                    null
            );
        }
    }

    private void handlerRefund(HeOrderEntity orderEntity, HeOrderRefundEntity refundEntity) {
        if (refundEntity.getApplyAmount().longValue() > 0) {
            //增加成长值和积分
            OrderRefundMinusAssetRequest orderRefundMinusAssetRequest = new OrderRefundMinusAssetRequest();
            orderRefundMinusAssetRequest.setRefundId(refundEntity.getId());
            orderRefundMinusAssetRequest.setRefundSn(refundEntity.getRefundOrderSn());
            orderRefundMinusAssetRequest.setOrderId(refundEntity.getOrderId());
            orderRefundMinusAssetRequest.setOrderSn(orderEntity.getOrderSn());
            orderRefundMinusAssetRequest.setBasicUid(orderEntity.getBasicUid());
            orderRefundMinusAssetRequest.setClientUid(orderEntity.getClientUid());
            if (Objects.isNull(orderEntity.getFxRate())) {
                orderEntity.setFxRate(BigDecimal.ONE);
            }
            BigDecimal applyAmount = orderEntity.getFxRate().multiply(new BigDecimal(refundEntity.getApplyAmount()));
            orderRefundMinusAssetRequest.setAmount(applyAmount.longValue());
            orderRefundMinusAssetRequest.setOrderRefundNature(refundEntity.getRefundNature());
            orderRefundMinusAssetRequest.setOrderType(orderEntity.getOrderType());
            heUserIntegralRecordV2Service.orderRefundFailAddAsset(orderRefundMinusAssetRequest);
        }
    }

    /**
     * 处理产康金退款（原有逻辑）
     */
    private void handleProductionCoinRefund(HeOrderEntity orderEntity, List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList) {
        List<HeOrderRefundGoodsEntity> productionCoinGoods = orderRefundGoodsEntityList.stream()
                .filter(o -> o.getGoodsType().equals(GoodsTypeEnum.Production_Coin.code()))
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(productionCoinGoods)) {
            log.info("返还产康金，订单：{}，商品数量：{}", orderEntity.getOrderSn(), productionCoinGoods.size());
            //返还可用产康金
            for (HeOrderRefundGoodsEntity heOrderRefundGoodsEntity : productionCoinGoods) {
                if (heOrderRefundGoodsEntity.getRefundNum() > 0) {
                    assetTradeService.refundDeductUsableCkj(
                            orderEntity.getOrderId() + "" + System.currentTimeMillis() + "-save",
                            orderEntity.getBasicUid() + "",
                            orderEntity.getOrderId(),
                            heOrderRefundGoodsEntity.getRefundNum().longValue(),
                            heOrderRefundGoodsEntity.getOrderGoodsSn(),
                            assetTradeService.getSellOperator(orderEntity.getStaffId()),
                            heOrderRefundGoodsEntity.getGoodsName()
                    );
                }
            }
        }
    }

    /**
     * 补发资产（参考GiveProductionCoinProcessor和AssetEffectProcessor的逻辑）
     */
    private void compensateAssets(HeOrderRefundEntity refundEntity, HeOrderEntity orderEntity, List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList) {
        log.info("开始补发资产，订单：{}，退款商品数量：{}", orderEntity.getOrderSn(), orderRefundGoodsEntityList.size());

        // 1. 补发产康金
        compensateProductionCoin(orderEntity, orderRefundGoodsEntityList);

        // 2. 补发其他资产
        compensateOtherAssets(refundEntity, orderEntity, orderRefundGoodsEntityList);
    }

    /**
     * 补发产康金（参考GiveProductionCoinProcessor的逻辑）
     * 需要判断商品是孕期产康金还是要完款之后再发的
     */
    private void compensateProductionCoin(HeOrderEntity orderEntity, List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList) {
        List<HeOrderRefundGoodsEntity> productionCoinGoods = orderRefundGoodsEntityList.stream()
                .filter(o -> o.getGoodsType().equals(GoodsTypeEnum.Production_Coin.code()))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(productionCoinGoods)) {
            return;
        }

        log.info("补发产康金，订单：{}，产康金商品数量：{}", orderEntity.getOrderSn(), productionCoinGoods.size());

        // 获取订单所有商品信息
        List<HeOrderGoodsEntity> allOrderGoods = orderGoodsRepository.getAllItermByOrderId(orderEntity.getOrderId());
        if (CollectionUtil.isEmpty(allOrderGoods)) {
            log.warn("未找到订单商品信息，订单：{}", orderEntity.getOrderSn());
            return;
        }

        for (HeOrderRefundGoodsEntity refundGoods : productionCoinGoods) {
            if (refundGoods.getRefundNum() > 0) {
                // 获取对应的订单商品信息
                HeOrderGoodsEntity orderGoods = allOrderGoods.stream()
                        .filter(goods -> goods.getId().equals(refundGoods.getOrderGoodsId()))
                        .findFirst()
                        .orElse(null);

                if (orderGoods == null) {
                    log.warn("未找到对应的订单商品，orderGoodsId={}", refundGoods.getOrderGoodsId());
                    continue;
                }

                // 判断是孕期产康金还是完款后产康金
                boolean isPregnancyGoods = orderGoods.isPregnancyStageGoods();

                // 计算补发的产康金数量：退款数量 * 100（参考GiveProductionCoinProcessor的逻辑）
                Integer compensateAmount = refundGoods.getRefundNum() * 100;

                // 获取产康金类型
                OrderProductionTypeEnum productionTypeEnum = getOrderProductionTypeEnum(orderEntity, orderGoods);

                if (isPregnancyGoods) {
                    // 孕期产康金：使用 givePregnancy 方法
                    givePregnancyProductionCoin(orderEntity, Operator.system(), productionTypeEnum,
                            compensateAmount.longValue(), refundGoods.getOrderGoodsSn(), refundGoods.getGoodsName());

                    log.info("补发孕期产康金成功：订单={}，商品={}，退款数量={}，补发产康金={}",
                            orderEntity.getOrderSn(), refundGoods.getOrderGoodsSn(), refundGoods.getRefundNum(), compensateAmount);
                } else if (Boolean.TRUE.equals(orderEntity.getIsPreviouslyFullyPaid())) {
                    // 完款后产康金：使用 give 方法
                    giveProductionCoin(orderEntity, Operator.system(), productionTypeEnum,
                            compensateAmount.longValue(), refundGoods.getOrderGoodsSn(), refundGoods.getGoodsName());

                    log.info("补发完款后产康金成功：订单={}，商品={}，退款数量={}，补发产康金={}",
                            orderEntity.getOrderSn(), refundGoods.getOrderGoodsSn(), refundGoods.getRefundNum(), compensateAmount);
                }
            }
        }
    }

    /**
     * 补发其他资产（使用manyTradeExec方法）
     */
    private void compensateOtherAssets(HeOrderRefundEntity refundEntity, HeOrderEntity orderEntity, List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList) {
//        // 获取订单所有商品信息
//        List<HeOrderGoodsEntity> allOrderGoods = orderGoodsRepository.getAllItermByOrderId(orderEntity.getOrderId());
//        if (CollectionUtil.isEmpty(allOrderGoods)) {
//            log.warn("未找到订单商品信息，订单：{}", orderEntity.getOrderSn());
//            return;
//        }
//
//        // 过滤出需要补发资产的商品（排除产康金和组合商品）
//        List<HeOrderGoodsEntity> compensateGoods = allOrderGoods.stream()
//                .filter(goods -> orderRefundGoodsEntityList.stream()
//                        .anyMatch(refund -> refund.getOrderGoodsId().equals(goods.getId())))
//                .filter(goods -> !goods.getGoodsType().equals(GoodsTypeEnum.Production_Coin.code()))
//                .filter(goods -> !CombineTypeEnum.COMBINE.code().equals(goods.getType()))
//                .collect(Collectors.toList());
//
//        if (CollectionUtil.isEmpty(compensateGoods)) {
//            log.info("没有需要补发的其他资产，订单：{}", orderEntity.getOrderSn());
//            return;
//        }
//
//        log.info("补发其他资产，订单：{}，商品数量：{}", orderEntity.getOrderSn(), compensateGoods.size());
//
//        // 设置商品数量为退款数量
//        for (HeOrderGoodsEntity goods : compensateGoods) {
//            orderRefundGoodsEntityList.stream()
//                    .filter(refund -> refund.getOrderGoodsId().equals(goods.getId()))
//                    .findFirst()
//                    .ifPresent(refund -> {
//                        goods.setGoodsNum(refund.getRefundNum()); // 使用退款数量
//                        log.debug("设置补发商品数量：商品ID={}，数量={}", goods.getId(), refund.getRefundNum());
//                    });
//        }
        manyTradeExec(refundEntity, orderEntity);
//        assetManager.manyTradeExec(compensateGoods, orderEntity, TradeType.ORDER_PAYED, null);
        log.info("补发其他资产完成，订单：{}", orderEntity.getOrderSn());
    }

    /**
     * 获取订单产康金类型（参考GiveProductionCoinProcessor的逻辑）
     */
    private OrderProductionTypeEnum getOrderProductionTypeEnum(HeOrderEntity heOrder, HeOrderGoodsEntity heOrderGoodsEntity) {

        if (Arrays.asList(OmniOrderTypeEnum.MONTH_ORDER.getCode(), OmniOrderTypeEnum.SMALL_MONTH_ORDER.getCode(),
                OmniOrderTypeEnum.OTHER_MONTH_ORDER.getCode(), OmniOrderTypeEnum.NURSE_OUTSIDE_ORDER.getCode()).contains(heOrder.getOrderType())) {

            if (heOrderGoodsEntity.getGift() == 0) {
                return OrderProductionTypeEnum.BUY;
            } else {
                if (Objects.isNull(heOrderGoodsEntity.getPromotionInfo())) {
                    return OrderProductionTypeEnum.GIFT;
                } else {
                    return OrderProductionTypeEnum.PROMOTION_GIFT;
                }
            }
        } else {
            if (heOrderGoodsEntity.getGift() == 0) {
                return OrderProductionTypeEnum.PRODUCTION_BUY;
            } else {
                if (Objects.isNull(heOrderGoodsEntity.getPromotionInfo())) {
                    return OrderProductionTypeEnum.PRODUCTION_GIFT;
                } else {
                    return OrderProductionTypeEnum.PRODUCTION_PROMOTION_GIFT;
                }
            }
        }
    }

    /**
     * 发放产康金（参考GiveProductionCoinProcessor的give方法）
     */
    private void giveProductionCoin(HeOrderEntity order, Operator operator,
                                    OrderProductionTypeEnum productionTypeEnum,
                                    Long productionAmount, String orderGoodsSn,
                                    String goodsName) {
        // 1. 保存本地记录
        HeUserProductionAmountListEntity heUserProductionAmountListEntity = new HeUserProductionAmountListEntity();
        heUserProductionAmountListEntity.setBasicId(order.getBasicUid());
        heUserProductionAmountListEntity.setTotalAmount(productionAmount);
        heUserProductionAmountListEntity.setSource(productionTypeEnum.code());
        heUserProductionAmountListEntity.setClientType(7);
        heUserProductionAmountListEntity.setOperatorId(Integer.valueOf(operator.getOperatorGuid()));
        heUserProductionAmountListEntity.setOperator(operator.getOperatorName());
        heUserProductionAmountListEntity.setOrderId(Long.valueOf(order.getOrderId()));
        heUserProductionAmountListEntity.setCreatedAt(Math.toIntExact(System.currentTimeMillis() / 1000));

        log.info("补发产康金本地记录参数={}", JSONUtil.toJsonStr(heUserProductionAmountListEntity));
        heUserProductionAmountListRepository.saveOne(heUserProductionAmountListEntity);

        // 2. 调用资产中心
        BigDecimal fxRate = Optional.ofNullable(order.getFxRate()).orElse(BigDecimal.ONE);
        Long amount = fxRate.multiply(new BigDecimal(productionAmount)).divide(BigDecimal.ONE, 0, RoundingMode.HALF_UP).longValue();
        String uniqueId = order.getOrderId() + "" + productionTypeEnum.code() + "-compensate-" + orderGoodsSn;

        List<Long> streamIdList = assetTradeService.saveUsableCkj(uniqueId, order.getBasicUid() + "",
                order.getOrderId(), amount, productionTypeEnum.code(),
                assetTradeService.getSellOperator(Integer.valueOf(operator.getOperatorGuid())), orderGoodsSn, goodsName);

        log.info("补发产康金资产中心返回streamIdList={}", JSONObject.toJSONString(streamIdList));
    }

    /**
     * 发放孕期产康金（参考GiveProductionCoinProcessor的givePregnancy方法）
     */
    private void givePregnancyProductionCoin(HeOrderEntity order, Operator operator,
                                             OrderProductionTypeEnum productionTypeEnum,
                                             Long productionAmount, String orderGoodsSn,
                                             String goodsName) {
        // 1. 保存本地记录
        HeUserProductionAmountListEntity heUserProductionAmountListEntity = new HeUserProductionAmountListEntity();
        heUserProductionAmountListEntity.setBasicId(order.getBasicUid());
        heUserProductionAmountListEntity.setTotalAmount(productionAmount);
        heUserProductionAmountListEntity.setSource(productionTypeEnum.code());
        heUserProductionAmountListEntity.setClientType(7);
        heUserProductionAmountListEntity.setOperatorId(Integer.valueOf(operator.getOperatorGuid()));
        heUserProductionAmountListEntity.setOperator(operator.getOperatorName());
        heUserProductionAmountListEntity.setOrderId(Long.valueOf(order.getOrderId()));
        heUserProductionAmountListEntity.setCreatedAt(Math.toIntExact(System.currentTimeMillis() / 1000));

        log.info("补发孕期产康金本地记录参数={}", JSONUtil.toJsonStr(heUserProductionAmountListEntity));
        heUserProductionAmountListRepository.saveOne(heUserProductionAmountListEntity);

        // 2. 调用资产中心（孕期产康金）
        BigDecimal fxRate = Optional.ofNullable(order.getFxRate()).orElse(BigDecimal.ONE);
        Long amount = fxRate.multiply(new BigDecimal(productionAmount)).divide(BigDecimal.ONE, 0, RoundingMode.HALF_UP).longValue();
        String uniqueId = order.getOrderId() + "" + productionTypeEnum.code() + "-compensate-pregnancy-" + orderGoodsSn;

        List<Long> streamIdList = assetTradeService.savePregnancyUsableCkj(uniqueId, order.getBasicUid() + "",
                order.getOrderId(), amount, productionTypeEnum.code(),
                assetTradeService.getSellOperator(Integer.valueOf(operator.getOperatorGuid())), orderGoodsSn, goodsName);

        log.info("补发孕期产康金资产中心返回streamIdList={}", JSONObject.toJSONString(streamIdList));
    }

    private void updateAheadOutRoomAmount(HeOrderEntity order, HeOrderRefundEntity refundEntity) {

        AheadOutRoomQuery query = new AheadOutRoomQuery();
        query.setOrderId(order.getOrderId());
        query.setState(AheadOutRoomEnum.STATE_DISENABLE.code());
        AheadOutRoomEntity aheadOutRoomEntity = aheadOutRoomRepository.queryByOrderId(query);
        //如果提前离馆同意时间早于退款申请时间则要修改提前离馆可退金额
        if (ObjectUtil.isNotEmpty(aheadOutRoomEntity) && aheadOutRoomEntity.getAgreeAt().before(DateUtil.date(refundEntity.getCreatedAt() * 1000))) {
            aheadOutRoomEntity.setRemainingRefundableAmount(aheadOutRoomEntity.getRemainingRefundableAmount() + refundEntity.getApplyAmount());
            aheadOutRoomRepository.updateOutRoom(aheadOutRoomEntity);
        }
    }

    private void manyTradeExec(HeOrderRefundEntity refundEntity, HeOrderEntity orderEntity) {

        List<HeOrderGoodsEntity> reqList = Lists.newArrayList();
        List<HeOrderGoodsEntity> orderGoodsEntityList = orderGoodsRepository.getByOrderIdList(Collections.singletonList(orderEntity.getOrderId()));
        Map<Integer, HeOrderGoodsEntity> orderGoodsEntityMap = orderGoodsEntityList.stream().collect(Collectors.toMap(HeOrderGoodsEntity::getId, o -> o, (k, v) -> k));

        List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList = orderRefundGoodsRepository.queryByRefundOrderSn(refundEntity.getRefundOrderSn());
        for (HeOrderRefundGoodsEntity orderRefundGoods : orderRefundGoodsEntityList){
            if (orderRefundGoods.getRefundNum() <= 0){
                continue;
            }
            HeOrderGoodsEntity heOrderGoodsEntity = orderGoodsEntityMap.get(orderRefundGoods.getOrderGoodsId());
            if (Objects.isNull(heOrderGoodsEntity)){
                continue;
            }
            heOrderGoodsEntity.setGoodsNum(orderRefundGoods.getRefundNum());
            reqList.add(heOrderGoodsEntity);
        }
        assetManager.execMultiUserTrade(reqList, orderEntity, TradeEventEnum.ORDER_RETURN_FAIL, null, refundEntity.getRefundOrderSn());
    }

}

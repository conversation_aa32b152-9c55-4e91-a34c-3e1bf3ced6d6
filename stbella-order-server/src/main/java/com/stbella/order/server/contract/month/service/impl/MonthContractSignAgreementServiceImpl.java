package com.stbella.order.server.contract.month.service.impl;

import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.month.TemplateTypeEnum;
import com.stbella.order.common.utils.GeneratorUtil;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.MonthContractSignAgreementEntity;
import com.stbella.order.domain.repository.MonthContractSignAgreementRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.domain.repository.StoreRepository;
import com.stbella.order.server.config.StoreConfig;
import com.stbella.order.server.contract.dto.ContractParamFillRequestDTO;
import com.stbella.order.server.contract.dto.ContractParamFillResponseDTO;
import com.stbella.order.server.contract.provider.month.ESignProvider;
import com.stbella.order.server.contract.service.month.MonthContractSignAgreementService;
import com.stbella.order.server.contract.service.month.MonthContractSignRecordService;
import com.stbella.order.server.contract.service.month.MonthEsignTemplateService;
import com.stbella.order.server.contract.service.month.MonthOrderParamHistoryService;
import com.stbella.order.server.convert.AppMonthContractSignRecordConverter;
import com.stbella.order.server.order.month.enums.OrderHistoryTypeEnum;
import com.stbella.order.server.order.month.req.MonthContractSignAgreementReq;
import com.stbella.order.server.order.month.res.MonthContractSignAgreementVO;
import com.stbella.order.server.order.month.res.MonthContractSignRecordVO;
import com.stbella.order.server.order.month.res.MonthEsignTemplateVO;
import com.stbella.order.server.order.month.res.MonthOrderParamHistoryVO;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;

import javax.annotation.Resource;

import cn.hutool.core.date.DateTime;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 合同补充协议 服务实现类
 * </p>
 *
 * <AUTHOR>
@Service
@Slf4j
public class MonthContractSignAgreementServiceImpl implements MonthContractSignAgreementService {

    @Resource
    private AppMonthContractSignRecordConverter appMonthContractSignRecordConverter;
    @Resource
    private MonthOrderParamHistoryService orderParamHistoryService;
    @Resource
    private ESignProvider eSignProvider;
    @Resource
    MonthEsignTemplateService esignTemplateService;
    @Resource
    private MonthContractSignRecordService monthContractSignRecordService;
    @Resource
    private MonthContractSignAgreementRepository monthContractSignAgreementRepository;
    @Resource
    private StoreRepository storeRepository;
    @Resource
    private OrderRepository orderRepository;


    @Override
    public Long create(Integer orderId, MonthContractSignRecordVO signRecord, List<MonthOrderParamHistoryVO> paramHistoryList, Integer templateContractType) {
        //订单信息
        HeOrderEntity order = orderRepository.queryOrderById(orderId);
        Optional.ofNullable(order).orElseThrow(()-> new BusinessException(ResultEnum.PARAM_ERROR, "订单不存在"));
        //查询门店信息,找到门店类型
        CfgStoreEntity cfgStore = storeRepository.queryCfgStoreById(order.getStoreId());
        Optional.ofNullable(cfgStore).orElseThrow(()-> new BusinessException(ResultEnum.PARAM_ERROR, "门店不存在"));
        if (StoreConfig.SPECIAL_TYPE_STORE_MAP.containsKey(cfgStore.getStoreId())) {
            //如果是厦门七尚 Bella Villa  id为:1030 则设置类型为6
            cfgStore.setType(StoreConfig.SPECIAL_TYPE_STORE_MAP.get(cfgStore.getStoreId()));
        }

        MonthContractSignAgreementReq contractSignAgreement = new MonthContractSignAgreementReq();
        //生成编号
        String contractNo = GeneratorUtil.contractNo();
        contractSignAgreement.setCode(contractNo);
        /**
         * 生成协议名称
         */
        //查询之前有几个签订了的协议
        List<MonthContractSignAgreementVO> oldAgreement = this.get(Long.valueOf(order.getOrderId()), 2);
        //取名规则为 {合同名称}+"补充协议"+{之前的协议数量+1}
        String agreementName = signRecord.getContractName() + "之补充协议" + (oldAgreement == null ? 0 : oldAgreement.size() + 1);
        contractSignAgreement.setName(agreementName);
        contractSignAgreement.setState(0);
        contractSignAgreement.setStoreId(Long.valueOf(order.getStoreId()));
        contractSignAgreement.setOrderId(Long.valueOf(order.getOrderId()));
        contractSignAgreement.setClientUid(order.getClientUid());
        contractSignAgreement.setSignTime(LocalDateTime.now());
        contractSignAgreement.setTemplateContractType(templateContractType);

        //region
        HashMap<String, String> contentData = this.generateTemplateData(Long.valueOf(order.getOrderId()), oldAgreement, paramHistoryList);
        //如果他是空,则说明参数内容无修改
        if (contentData == null) {
            return -1L;
        }
        //endregion

        //查询模版
        // generateTemplateData 方法返回pageNum 通过pageNum获取对应的补充协议
        MonthEsignTemplateVO esignTemplate = esignTemplateService.getByTypes(order.getOrderType(), cfgStore.getType(), TemplateTypeEnum.AGREEMENT_TYPE.code(), templateContractType, contentData.containsKey("pageNum") ? Integer.parseInt(contentData.get("pageNum")) : 0);
        //填充参数,生成签署文件
        ContractParamFillRequestDTO contractParamFillRequestDTO = new ContractParamFillRequestDTO();
        contractParamFillRequestDTO.setName(agreementName);
        contractParamFillRequestDTO.setTemplateId(esignTemplate.getEsignFileId());

        for (MonthOrderParamHistoryVO orderParamHistory : paramHistoryList) {
            contentData.put(orderParamHistory.getMark(), orderParamHistory.getParamValue());
        }
        contentData.put("contract_no", contractNo);
        //主合同编号
        contentData.put("main_contract_no", signRecord.getContractNo());
        contentData.put("contract_name", agreementName);
        contentData.put("company_address", BizConstant.COMPANY_ADDRESS);
        contentData.put("sign_time_str2", contentData.getOrDefault("sign_time_str3",""));
        contractParamFillRequestDTO.setContent(contentData);
        ContractParamFillResponseDTO paramFillResponseDTO = eSignProvider.doFillContentTemplate(contractParamFillRequestDTO);

        contractSignAgreement.setEsignTemplateId(esignTemplate.getEsignFileId());
        contractSignAgreement.setEsignFileId(paramFillResponseDTO.getFileId());


        //回写签章位置
        contractSignAgreement.setPageNum(Integer.valueOf(contentData.get("pageNum")));
        contractSignAgreement.setPositionA(contentData.get("positionA"));
        contractSignAgreement.setPositionB(contentData.get("positionB"));
        contractSignAgreement.setEsignFileName(contractSignAgreement.getName() + ".pdf");
        return  monthContractSignAgreementRepository.create(contractSignAgreement);
    }

    /**
     * 取消补充协议,直接逻辑删除了,删除前会撤销E签宝的签章
     *
     * @param id 协议id
     * @return 成功/失败
     */
    @Override
    public Boolean cancal(Long id) {
        MonthContractSignAgreementEntity signAgreement = monthContractSignAgreementRepository.getByContractAgreementId(id);
        if (StringUtils.hasText(signAgreement.getEsignFlowId())) {
            eSignProvider.cancelSignFlow(signAgreement.getEsignFlowId());
        }
        return monthContractSignAgreementRepository.removeByAgreementId(id);
    }

    /**
     * 生成补充协议所需的数据,因逻辑非常复杂,所以单独拆出来作一块
     * 还需要在这里创建数据修改记录,有点恶心
     *
     * @param orderId          订单id
     * @param oldAgreement     涉及的旧协议数据
     * @param paramHistoryList 本次变更后的参数
     * @return 合同模版生成所需要的参数
     */
    public HashMap<String, String> generateTemplateData(Long orderId, List<MonthContractSignAgreementVO> oldAgreement, List<MonthOrderParamHistoryVO> paramHistoryList) {
        /*
          整体逻辑:
          先查历史记录,和主合同对应的参数
          对比原始数据和历史数据,列出修改过的数据(要注意处理ABA)
          对每个修改过的数据进行向上追溯,追溯逻辑为只要上一个版本数据不一样就停止追溯,生成修改描述字符串
          拼接前缀和后缀
          生成最终的修改描述字符串,计算字符串高度,生成tab1,tab2,tab3
          返回
         */

        /*“
          第一部分:
          查询主合同对应的参数
          查询已签署的补充协议的参数和位置
         */
        List<MonthOrderParamHistoryVO> orderParamHistories = orderParamHistoryService.list(orderId, 1, Arrays.asList(OrderHistoryTypeEnum.MAIN.getCode(),OrderHistoryTypeEnum.AGREEMENT.getCode()));
        /*
          第二部分:
          检查和装配数据变更部分,便于操作
         */
        //map结构  {mark}:List<OrderParamHistory>  用来给参数进行分组,方便追溯修改记录
        HashMap<String, List<MonthOrderParamHistoryVO>> paramGroupMap = new HashMap<>();
        for (MonthOrderParamHistoryVO orderParamHistory : orderParamHistories) {
            String mark = orderParamHistory.getMark();
            List<MonthOrderParamHistoryVO> histories = paramGroupMap.get(mark);
            if (histories == null) {
                histories = new ArrayList<>();
                paramGroupMap.put(mark, histories);
            }
            histories.add(orderParamHistory);
        }
        //map结构  id:ContractSignAgreement  用来根据id快速检索协议信息
        HashMap<Long, MonthContractSignAgreementVO> agreementMap = new HashMap<>();
        for (MonthContractSignAgreementVO contractSignAgreement : oldAgreement) {
            agreementMap.put(contractSignAgreement.getId(), contractSignAgreement);
        }
        /*
          第三部分:
          对比历史数据,找出更的数据项
         */
        //下面2个map记录变更后和变更前的数据  使用mark作为key
        HashMap<String, MonthOrderParamHistoryVO> updateParamMap = new HashMap<>();
        HashMap<String, MonthOrderParamHistoryVO> oldUpdateParamMap = new HashMap<>();

        //记录涉及修改的主合同和补充协议
        Long mainContractId = -1L;
        List<MonthContractSignAgreementVO> updateAgreement = new ArrayList<>();
        //开始遍历本次数据信息
        for (MonthOrderParamHistoryVO orderParamHistory : paramHistoryList) {
            orderParamHistory.setType(2);
            String mark = orderParamHistory.getMark();
            String paramValue = orderParamHistory.getParamValue();
            List<MonthOrderParamHistoryVO> histories = paramGroupMap.get(mark);
            //变更标记,如为true则说明这个参数有进行变更
            boolean update = false;
            //循环历史数据,进行对比  这里在查询的时候就已经使用时间倒序排序了,所以这里不需要排序
            if (histories != null) {
                for (MonthOrderParamHistoryVO history : histories) {
                    //如果"条款"为-1 则直接检查下一条数据
                    if (history.getTerms() == -1) {
                        continue;
                    }
                    //如果数据不一致了,则说明出现变更  记录该数据
                    if (!paramValue.equals(history.getParamValue())) {
                        update = true;
                        if (history.getType() == 1) {
                            updateParamMap.put(mark, orderParamHistory);
                            oldUpdateParamMap.put(mark, history);
                            mainContractId = history.getContractId();
                        } else if (history.getType() == 2) {
                            MonthContractSignAgreementVO signAgreement = agreementMap.get(history.getContractId());
                            if (signAgreement == null) {
                                break;
                            }
                            updateParamMap.put(mark, orderParamHistory);
                            oldUpdateParamMap.put(mark, history);
                            updateAgreement.add(signAgreement);

                        }
                    }
                    //跳出循环
                    break;
                }
            }

            //如果数据没经历变更 则下沉该数据
            if (!update) {
                orderParamHistory.setTerms(-1);
                orderParamHistory.setItem(-1);
            }

        }
        /*
          第四部分:
          生成变更描述字符串,同时回写数据记录行数
          template 1~5为基础模版字段  没有用上这个模版也不要删除,留着能直观看到最后的拼接结果
         */

        //所有文字段落的数组,这里将每段先放到数组中,全部生成完成后再拼接,切割分页
        List<String> textList = new ArrayList<>();

//        String template1 = "一、鉴于，甲、乙双方";
//        String template2 = "于【】年【】月【】日签订了编号为【合同编号】的《合同名称》，";
//        String template3 = "现经双方友好协商，就调整原合同部分内容的相关事项，签署补充协议如下：";
//        String template4 = "1.双方协商一致同意就《合同名称》第【】条的第【】项条款进行调整，《合同名称》【】变更为【】。";
        String template5 = "二、除本协议明确所作修改、变更及补充的内容外，原合同的其他条款均继续有效。本协议签订生效后，即成为原合同不可分割的部分，与原合同具有同等法律效力。\n";
        String template6 = "三、原合同与本协议约定不一致的，以本协议约定为准；本协议尚未约定或约定不明的，以原合同约定的为准。\n";
        String template7 = "四、本协议于双方法定代表人或授权代表签字（或盖章）并加盖公章或合同专用章后生效，本协议一式两份，甲方持一份，乙方执一份，每份具有同等法律效力。\n";
        String template8 = "\n\n\n甲方：\n" +
                "法定代表人或授权代表（签章）：\n\n\n\n\n\n\n" +
                "乙方：\n" +
                "法定代表人或授权代表（签章）：\n\n" +
                "签订日期：" + DateTime.now().year() + "年" + DateTime.now().monthBaseOne() + "月" + DateTime.now().dayOfMonth() + "日";
        String signTimeStr3 = "";
        //如果没有关联合同,也没有修改协议  则直接抛出异常,不需要生成补充协议
        if (updateParamMap.size() == 0) {
            //合同内容无变更,直接返回null
            return null;
        }

        //不是-1说明修改内容中涉及合同  生成合同相关描述
        if (mainContractId != -1) {
            MonthContractSignRecordVO signRecord = monthContractSignRecordService.getByContractSignId(mainContractId);
            DateTime dateTime = new DateTime(signRecord.getUpdatedAt() * 1000);
            int year = dateTime.year();
            int month = dateTime.monthBaseOne();
            int day = dateTime.dayOfMonth();
            String contractNo = signRecord.getContractNo();
            String contractName = signRecord.getContractName();
            String text = "一、鉴于，甲、乙双方于【 " + year + " 】年【 " + month + " 】月【 " + day + " 】日签订了编号为【" + contractNo + "】的《" + contractName + "》，现经双方友好协商，就调整原合同部分内容的相关事项，签署补充协议如下：\n";
            textList.add(text);
            signTimeStr3 = dateTime.toDateStr();
        }
        HashMap<Long, MonthContractSignAgreementVO> printAgreement = new HashMap<>();
        //数组中有数据说明涉及补充协议的修改
        if (updateAgreement.size() > 0) {
            //先排序,然后拼接补充协议的文本
            updateAgreement.sort(Comparator.comparing(MonthContractSignAgreementVO::getGmtCreate));
            for (MonthContractSignAgreementVO contractSignAgreement : updateAgreement) {
                if (printAgreement.get(contractSignAgreement.getId()) != null) {
                    continue;
                }
                printAgreement.put(contractSignAgreement.getId(), contractSignAgreement);
                DateTime dateTime = new DateTime(contractSignAgreement.getSignTime());
                int year = dateTime.year();
                int month = dateTime.monthBaseOne();
                int day = dateTime.dayOfMonth();
                String contractNo = contractSignAgreement.getCode();
                String contractName = contractSignAgreement.getName();
                String text = "一、鉴于，甲、乙双方于【 " + year + " 】年【 " + month + " 】月【" + day + "】日签订了编号为【" + contractNo + "】的《" + contractName + "》，现经双方友好协商，就调整原合同部分内容的相关事项，签署补充协议如下：\n";
                textList.add(text);
                signTimeStr3 = dateTime.toDateStr();
            }
        }
        //如果map有数据说明有参数变更了
        if (updateParamMap.size() > 0) {
            //索引,每次数据变更都+1
            Integer index = 0;
            for (String mark : updateParamMap.keySet()) {
                MonthOrderParamHistoryVO updateOrderParamHistory = updateParamMap.get(mark);
                MonthOrderParamHistoryVO oldUpdateOrderParamHistory = oldUpdateParamMap.get(mark);
                Integer terms = oldUpdateOrderParamHistory.getTerms();
                Integer item = oldUpdateOrderParamHistory.getItem();
                //如果条款为-1  则直接略过该条款 不进行显示
                if (terms == -1) {
                    continue;
                }
                //合同名称
                String contractName = "";
                //这里根据参数归属的类型不同走合同和走补充协议
                if (oldUpdateOrderParamHistory.getType() == 1) {
                    MonthContractSignRecordVO signRecord = monthContractSignRecordService.getByContractSignId(mainContractId);
                    contractName = signRecord.getContractName();
                } else if (oldUpdateOrderParamHistory.getType() == 2) {
                    MonthContractSignAgreementVO signAgreement = agreementMap.get(oldUpdateOrderParamHistory.getContractId());
                    contractName = signAgreement.getName();
                }

                String oldValue = oldUpdateOrderParamHistory.getParamValue();
                String value = updateOrderParamHistory.getParamValue();
                String paramName = updateOrderParamHistory.getName() + ": ";
                String text;
                if (terms == 0 && item == 0) {
                    //todo 由于remark 字段包含额外礼赠+特殊事项 这里截取特殊事项比对
                    if ("remark".equals(mark)) {
                        oldValue = oldValue.substring(oldValue.indexOf( BizConstant.REMARK+"：\n\n") + 9);
                        value = value.substring(value.indexOf(BizConstant.REMARK+"：\n\n") + 9);
                        if (oldValue.equals(value)) {
                            continue;
                        }
                    }
                    index++;
                    text = index + ".双方协商一致同意就《" + contractName + "》进行调整，《" + contractName + "》【" + paramName + oldValue + "】变更为【" + paramName + value + "】。\n";
                } else {
                    index++;
                    text = index + ".双方协商一致同意就《" + contractName + "》第【" + terms + "】条的第【" + item + "】项条款进行调整，《" + contractName + "》【" + paramName + oldValue + "】变更为【" + paramName + value + "】。\n";
                }

                textList.add(text);
                //条默认1  项与打印顺序有关
                updateOrderParamHistory.setTerms(1);
                updateOrderParamHistory.setItem(index);
            }
        }

        textList.add(template5);
        textList.add(template6);
        textList.add(template7);

        /*
          第六部分:
          计算高低,切分tab1,2,3
          查询甲乙方字段,填充
          返回结果
         */

        /*
          这里是变量配置
          positionA,positionB 为签章X轴位置  如需左右移动签章位置,可编辑该参数
          pageNum 签章页码
          rowWidth 行宽,计算常量 如果合同模版中样式发生改变,需要同时变动该常量  后续可考虑根据合同模版进行配置化
          rowHeight 行高,计算常量 如果合同模版中样式发生改变,需要同时变动该常量  后续可考虑根据合同模版进行配置化
          tabHigh 每页行数,计算常量   如果合同模版中样式发生改变,需要同时变动该常量  后续可考虑根据合同模版进行配置化
          height  页高,第一页设置为380 后面设置为560  根据每页的文本域来决定,后续可扩展为配置化
          textStrList 每页内容,这里默认为4页
          textIndex 当前写入的文本序号 textList保存着生成的所有内容,使用textIndex来控制当前需要读取内容
          tabHighIndex 当前记录页,0-3  记录当前内容生成到哪页
         */

        String positionA = "270,";
        String positionB = "270,";
        //基础高度 第一页默认430,第二页670
        int height = 380;
        //行高,计算常量
        int rowHeight = 18;
        //这里写定模版的高度行,现在模版一共四页
        List<Integer> tabHigh = new ArrayList<>();
        tabHigh.add(32);
        tabHigh.add(41);
        tabHigh.add(41);
        tabHigh.add(41);
        int tabHighIndex = 0;
        int textIndex = 0;

        // 正文 根据textStrList的数据来具体生成文本
        List<StringBuffer> textStrList = new ArrayList<>();
        textStrList.add(new StringBuffer());
        textStrList.add(new StringBuffer());
        textStrList.add(new StringBuffer());
        textStrList.add(new StringBuffer());
        int rows = 0;
        StringBuilder builder = new StringBuilder();
        int length;
        for (String s : textList) {
            length = 0;
            for (char aChar : s.toCharArray()) {
                builder.append(aChar);
                if (!"\n".equals(String.valueOf(aChar))) {
                    length += String.valueOf(aChar).getBytes(StandardCharsets.UTF_8).length > 1 ? 2 : 1;
                    if (length >= 69) {
                        length = 0;
                        builder.append("\n");
                    }
                }
            }
        }
        String[] splits = builder.toString().split("\n");
        for (String split : splits) {
            if (StringUtils.hasText(split)) {
                StringBuffer stringBuffer = textStrList.get(textIndex);
                stringBuffer.append(split).append("\n");
                rows++;
                if (tabHigh.get(tabHighIndex) == rows) {
                    rows = 0;
                    textIndex++;
                    tabHighIndex++;
                    height = 560;
                }
            }
        }
        if (tabHigh.get(tabHighIndex) < (rows + 15)) {
            rows = 15;
            textIndex++;
            height = 560;
        } else {
            rows += 15;
        }
        textStrList.get(textIndex).append(template8);

        height -= (rows * rowHeight);

        HashMap<String, String> contentData = new HashMap<>();
        //合同信息,这个是为了拿到甲方,乙方等信息
        List<MonthOrderParamHistoryVO> templateData = orderParamHistoryService.getContractTemplateData(orderId, 1);
        for (MonthOrderParamHistoryVO orderParamHistory : templateData) {
            contentData.put(orderParamHistory.getMark(), orderParamHistory.getParamValue());
        }
        /*
          计算签章位置
         */
        //pageNum :签章位置页码 position_a:甲方盖章坐标,x,y格式 position_b 乙方盖章坐标,x,y格式
        int pageNum = textIndex + 1;
        positionA = positionA + (height + 400);
        positionB = positionB + (height + 280);
        for (int i = 1; i < 5; i++) {
            //给每页的文本域填写对应的数值
            contentData.put("tab_" + i, textStrList.get(i - 1).toString());
        }
        contentData.put("pageNum", String.valueOf(pageNum));
        contentData.put("positionA", positionA);
        contentData.put("positionB", positionB);
        contentData.put("sign_time_str3", signTimeStr3);

//        //暂时写死 服务期限/入住房型/医院陪护/其他service_types_0,service_types_1,service_types_2,service_types_3
//        contentData.put("service_types_0", "√");
//        contentData.put("service_types_2", "√");
        return contentData;
    }

    /**
     * 查询符合条件的协议
     *
     * @param orderId 订单id
     * @param state   签署状态
     * @return list
     */
    public List<MonthContractSignAgreementVO> get(Long orderId, Integer state) {
        List<MonthContractSignAgreementEntity> contractSignAgreements = monthContractSignAgreementRepository.list(orderId, state);
        return appMonthContractSignRecordConverter.entity2MonthContractSignAgreementVoList(contractSignAgreements);
    }


    @Override
    public MonthContractSignAgreementVO getByContractAgreementId(Long id) {
        return appMonthContractSignRecordConverter.entity2MonthContractSignAgreementVo(monthContractSignAgreementRepository.getByContractAgreementId(id));
    }
}

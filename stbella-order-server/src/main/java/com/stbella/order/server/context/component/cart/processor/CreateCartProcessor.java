package com.stbella.order.server.context.component.cart.processor;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Maps;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.domain.client.IdGenerator;
import com.stbella.order.domain.order.entity.HeCartEntity;
import com.stbella.order.domain.order.entity.HeCartGoodsEntity;
import com.stbella.order.domain.order.month.entity.TabClientEntity;
import com.stbella.order.domain.order.production.GoodsEntity;
import com.stbella.order.domain.repository.GoodsRepository;
import com.stbella.order.server.convert.CartEntityConverter;
import com.stbella.order.server.convert.CartGoodsEntityConverter;
import com.stbella.platform.order.api.req.CreateOrUpdateCartReq;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-05-29  15:20
 * @Description: 创建购物车
 */
@Component
@Slf4j
@SnowballComponent(name = "创建购物车", desc = "")
public class CreateCartProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private CartEntityConverter cartEntityConverter;

    @Resource
    IdGenerator idGenerator;
    @Resource
    private CartGoodsEntityConverter cartGoodsEntityConverter;

    @Resource
    private GoodsRepository goodsRepository;

    @Override
    public void run(FlowContext bizContext) {
        CreateOrUpdateCartReq req = bizContext.getAttribute(CreateOrUpdateCartReq.class);
        TabClientEntity tabClientEntity = bizContext.getAttribute(TabClientEntity.class);
        
        // 使用经过 SkuDetailInfoAssembler 处理后的 skuList，而不是原始请求中的
        List<SkuDetailInfo> processedSkuList = bizContext.getListAttribute(SkuDetailInfo.class);
        if (processedSkuList == null || processedSkuList.isEmpty()) {
            log.warn("经过处理的商品列表为空，使用原始请求数据");
            processedSkuList = req.getSkuList();
        }
        log.info("CreateCartProcessor: 使用商品列表，原始数量={}, 处理后数量={}", 
                req.getSkuList().size(), processedSkuList.size());
        
        HeCartEntity heCartEntity = cartEntityConverter.req2Entity(req);
        String cartSn = idGenerator.geneStringIdForLocal("Cart");
        heCartEntity.setCartSn(cartSn);
        heCartEntity.setBu(req.getBu());
        if (Objects.nonNull(tabClientEntity)) {
            heCartEntity.setClientUid(tabClientEntity.getClientTypeId());
        }
        Map<Integer, String> inventoryUrlMap = getInventoryUrl(processedSkuList);
        List<HeCartGoodsEntity> cartGoodsList = processedSkuList.stream().map(sku -> {
            log.info("CreateCartProcessor: 处理商品 skuId={}, goodsId={}, subList.size={}",
                    sku.getSkuId(), sku.getGoodsId(), sku.getSubList() != null ? sku.getSubList().size() : 0);
            HeCartGoodsEntity goodsEntity = cartGoodsEntityConverter.req2Entity(sku);
            goodsEntity.setItermSn(idGenerator.geneStringIdForLocal(""));
            goodsEntity.setParentSn("");
            goodsEntity.setInventoryUrl(inventoryUrlMap.get(sku.getGoodsId()));
            if (CollectionUtil.isNotEmpty(sku.getSubList())) {
                log.info("CreateCartProcessor: 保存子商品，数量={}", sku.getSubList().size());
                List<HeCartGoodsEntity> subEntityList = cartGoodsEntityConverter.reqList2EntityList(sku.getSubList());
                subEntityList.forEach(subSku -> {
                    log.info("CreateCartProcessor: 子商品 skuId={}, goodsId={}", subSku.getSkuId(), subSku.getGoodsId());
                    subSku.setParentSn(goodsEntity.getItermSn());
                    subSku.setItermSn(idGenerator.geneStringIdForLocal(""));
                    subSku.setType(CombineTypeEnum.SIMPLE.code());
                    subSku.setInventoryUrl(inventoryUrlMap.get(subSku.getGoodsId()));
                });
                goodsEntity.setSubGoodsList(subEntityList);
                goodsEntity.setType(CombineTypeEnum.COMBINE.code());
            } else {
                log.info("CreateCartProcessor: 无子商品");
                goodsEntity.setType(CombineTypeEnum.SIMPLE.code());
            }

            return goodsEntity;
        }).collect(Collectors.toList());
        heCartEntity.setGoodsList(cartGoodsList);
        heCartEntity.saveOrUpdateCart();
        bizContext.setAttribute(HeCartEntity.class, heCartEntity);
    }

    private Map<Integer, String> getInventoryUrl(List<SkuDetailInfo> skuList) {

        Map<Integer, String> resultMap = Maps.newHashMap();
        if (CollectionUtil.isEmpty(skuList)) {
            return resultMap;
        }
        List<Integer> goodsIdList = skuList.stream().map(SkuDetailInfo::getGoodsId).filter(Objects::nonNull).collect(Collectors.toList());
        for (SkuDetailInfo sku : skuList) {
            if (CollectionUtil.isEmpty(sku.getSubList())) {
                continue;
            }
            List<Integer> subIdList = sku.getSubList().stream().map(SkuDetailInfo::getGoodsId).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(subIdList)) {
                goodsIdList.addAll(subIdList);
            }
        }
        if (CollectionUtil.isEmpty(goodsIdList)) {
            return resultMap;
        }
        List<GoodsEntity> goodsEntityList = goodsRepository.selectByIdList(goodsIdList);
        if (CollectionUtil.isEmpty(goodsEntityList)) {
            return resultMap;
        }
        return goodsEntityList.stream().filter(item -> Objects.nonNull(item.getInventoryUrl())).collect(Collectors.toMap(GoodsEntity::getId, GoodsEntity::getInventoryUrl, (v1, v2) -> v1));
    }
}

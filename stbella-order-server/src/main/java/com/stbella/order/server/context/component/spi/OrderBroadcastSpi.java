package com.stbella.order.server.context.component.spi;

import com.stbella.core.result.Result;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.server.context.component.processor.pay.OrderPayProcessor;
import top.primecare.snowball.extpoint.annotation.FuncPointSpi;
import top.primecare.snowball.extpoint.definition.IFuncSpiSupport;
import top.primecare.snowball.extpoint.definition.IFuncSpiSupportWithNoResult;

/**
 * <AUTHOR>
 * 订单播报展点点（报单）
 */
@FuncPointSpi(name = "支付扩展点", attachToComponent = OrderPayProcessor.class)
public interface OrderBroadcastSpi extends IFuncSpiSupport<HeOrderEntity, Result> {

}

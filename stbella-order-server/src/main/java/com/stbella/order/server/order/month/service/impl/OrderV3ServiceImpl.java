package com.stbella.order.server.order.month.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.stbella.base.server.security.PermissionService;
import com.stbella.care.server.care.service.RoomExternalQuery;
import com.stbella.core.base.StoreInfoDTO;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.core.utils.JwtUtil;
import com.stbella.core.utils.sso.EmployeeTokenHelper;
import com.stbella.customer.server.ecp.enums.PhoneTypeEnum;
import com.stbella.customer.server.ecp.request.ClientCardTypeRequest;
import com.stbella.customer.server.ecp.service.TabClientService;
import com.stbella.customer.server.ecp.vo.ClientCardTypeVO;
import com.stbella.order.common.constant.PhpApiConstant;
import com.stbella.order.common.constant.RoleConstant;
import com.stbella.order.common.enums.core.*;
import com.stbella.order.common.enums.month.*;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.client.StoreGoodsClient;
import com.stbella.order.domain.order.factory.OrderFactory;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.production.GoodsCategoryBackEntity;
import com.stbella.order.domain.order.production.GoodsEntity;
import com.stbella.order.domain.order.production.OrderGiftExtendEntity;
import com.stbella.order.domain.order.production.OrderProductionExtendEntity;
import com.stbella.order.domain.order.service.OrderIncomeDomainService;
import com.stbella.order.domain.repository.*;
import com.stbella.order.domain.repository.condition.StoreQueryCondition;
import com.stbella.order.infrastructure.repository.converter.OrderDecreaseConverter;
import com.stbella.order.server.config.DynamicConfig;
import com.stbella.order.server.context.component.ui.ButtonBuilder;
import com.stbella.order.server.contract.enums.IdTypeEnum;
import com.stbella.order.server.convert.OrderConvert;
import com.stbella.order.server.convert.OrderGoodsConverter;
import com.stbella.order.server.manager.AssetManager;
import com.stbella.order.server.manager.StoreManager;
import com.stbella.order.server.order.month.dto.PageDTO;
import com.stbella.order.server.order.month.enums.*;
import com.stbella.order.server.order.month.req.*;
import com.stbella.order.server.order.month.res.*;
import com.stbella.order.server.order.month.response.OrderUserSnapshotVO;
import com.stbella.order.server.order.month.response.TabClientVO;
import com.stbella.order.server.order.month.service.AddressQueryService;
import com.stbella.order.server.order.month.service.OrderV3Service;
import com.stbella.order.server.order.template.OrderInfoQueryTemplate;
import com.stbella.order.server.utils.DateUtils;
import com.stbella.order.server.utils.JsonUtil;
import com.stbella.order.server.utils.PageUtils;
import com.stbella.order.server.utils.SensitiveInformationUtil;
import com.stbella.platform.order.api.OrderQueryService;
import com.stbella.platform.order.api.contract.api.HeOrderSnapshotService;
import com.stbella.platform.order.api.contract.req.OrderUserSnapshotReq;
import com.stbella.platform.order.api.req.CustomAttribute;
import com.stbella.platform.order.api.req.ExtraInfo;
import com.stbella.platform.order.api.res.PromotionInfo;
import com.stbella.store.common.enums.core.GoodsTypeEnum;
import com.stbella.store.goodz.res.GoodsPropertyVO;
import com.stbella.store.goodz.res.PropertyDetailVO;
import com.stbella.store.goodz.res.PropertyValueVO;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@DubboService
public class OrderV3ServiceImpl implements OrderV3Service {

    private static final Integer one = 1;

    private static final Integer six = 6;

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderGoodsRepository orderGoodsRepository;
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @Resource
    private IncomeProofRecordRepository incomeProofRecordRepository;
    @Resource
    private MonthContractSignRecordRepository monthContractSignRecordRepository;
    @Resource
    private MonthContractSignAgreementRepository monthContractSignAgreementRepository;
    @Resource
    private GoodsRepository goodsRepository;
    @Resource
    private ClientRepository clientRepository;
    @Resource
    private OrderVoucherRepository orderVoucherRepository;
    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;
    @Resource
    private OrderBailorSnapshotRepository orderBailorSnapshotRepository;
    @Resource
    private UserRepository userRepository;
    @Resource
    private DynamicConfig dynamicConfig;
    @Resource
    private StoreGoodsClient storeClient;
    @Resource
    private HeOrderRefundGoodsRepository orderRefundGoodsRepository;
    @Resource
    private AddressQueryService addressQueryService;
    @Resource
    private HeOrderAttachmentRepository heOrderAttachmentRepository;
    @Resource
    private GoodsCategoryBackRepository goodsCategoryBackRepository;
    @Resource
    OrderFactory orderFactory;
    @Resource
    StoreRepository storeRepository;
    @Resource
    ButtonBuilder buttonBuilder;
    @Resource
    OrderIncomeDomainService orderIncomeDomainService;
    @Resource
    private IncomePaidAllocationRepository incomePaidAllocationRepository;
    @Resource
    private OrderReductionRepository orderReductionRepository;
    @Resource
    private OrderConvert orderConvert;
    @Resource
    private OrderGoodsConverter orderGoodsConverter;
    @Resource
    private SettingRoomTypeRepository settingRoomTypeRepository;
    @Resource
    private OrderQueryService orderQueryService;
    @Resource
    private OrderDecreaseConverter orderDecreaseConverter;
    @Resource
    private StoreManager storeManager;

    @Resource
    private OrderInfoQueryTemplate[] orderInfoQueryTemplate;

    @DubboReference
    private TabClientService tabClientService;

    @Resource
    private HeOrderSnapshotService heOrderSnapshotService;

    @Resource
    private OrderProductionExtendRepository orderProductionExtendRepository;

    @DubboReference
    private RoomExternalQuery roomExternalQuery;

    @DubboReference
    private PermissionService permissionService;

    @Resource
    private OrderGiftExtendRepository orderGiftExtendRepository;

    @Resource
    private AssetManager assetManager;


    @Override
    public PageDTO<WechatMyOrderNewVO> myOrderList(WechatMyOrderNewQuery req) {

        Assert.isTrue(Objects.nonNull(req.getOperator().getOperatorGuid()), "操作人ID不能为空");
        if (req.getBizType() == 1) {
            Assert.isTrue(Objects.nonNull(req.getStoreId()), "门店ID不能为空");
            Assert.isTrue(Objects.nonNull(req.getClientId()), "客户ID不能为空");
            req.getOperator().setOperatorGuid(null);
        }
        if (StringUtils.isNotEmpty(req.getKeyword())) {
            List<TabClientEntity> tabClientEntities = clientRepository.getClientInfoByPhone(req.getKeyword(), req.getKeyword());
            if (CollectionUtils.isNotEmpty(tabClientEntities)) {
                req.setClientIds(tabClientEntities.stream().map(TabClientEntity::getId).collect(Collectors.toList()));
            }
            List<HeOrderGoodsEntity> listByCategoryIds = orderGoodsRepository.getListByCategoryIds(null, req.getKeyword(), true);
            if (CollectionUtils.isNotEmpty(listByCategoryIds)) {
                req.setOrderIds(listByCategoryIds.stream().map(HeOrderGoodsEntity::getOrderId).collect(Collectors.toList()));
            }
            if (CollectionUtils.isEmpty(req.getClientIds()) && CollectionUtils.isEmpty(req.getOrderIds())) {
                PageDTO pageDTO = new PageDTO();
                pageDTO.setPages(req.getPageNum());
                pageDTO.setPageSize(req.getPageSize());
                return pageDTO;
            }
        }
        Page<HeOrderEntity> pageList = orderRepository.queryList(req);
        if (CollectionUtils.isEmpty(pageList.getRecords())) {
            return PageUtils.convert2PageDTO(pageList, null);
        }
        List<Integer> orderIds = pageList.getRecords().stream().map(HeOrderEntity::getOrderId).collect(Collectors.toList());
        Map<Integer, HeOrderEntity> heOrderEntityMap = pageList.getRecords().stream().collect(Collectors.toMap(HeOrderEntity::getOrderId, entity -> entity, (v1, v2) -> v1));

        List<Integer> clientUidList = pageList.getRecords().stream().map(HeOrderEntity::getClientUid).collect(Collectors.toList());
        List<TabClientEntity> tabClientByIdList = clientRepository.getTabClientByIdList(clientUidList);
        Map<Integer, TabClientEntity> clientNameByIdMap = tabClientByIdList.stream().collect(Collectors.toMap(TabClientEntity::getId, Function.identity()));

        List<Integer> storeIdList = pageList.getRecords().stream().map(HeOrderEntity::getStoreId).collect(Collectors.toList());
        List<CfgStoreEntity> storeByIdList = storeRepository.queryCfgStoreByIdList(storeIdList);
        Map<Integer, CfgStoreEntity> storeInfoMap = storeByIdList.stream().collect(Collectors.toMap(CfgStoreEntity::getStoreId, entity -> entity, (v1, v2) -> v1));

        List<HeOrderGoodsEntity> orderGoodsByOrderIdList = orderGoodsRepository.getByOrderIdList(orderIds);
        Map<Integer, List<HeOrderGoodsEntity>> orderGoodsByOrderIdMap = orderGoodsByOrderIdList.stream().collect(Collectors.groupingBy(HeOrderGoodsEntity::getOrderId));

        orderGoodsByOrderIdMap.forEach((key, value) -> {
            HeOrderEntity heOrderEntity = heOrderEntityMap.get(key);
            BigDecimal calPayableAmount = AmountChangeUtil.changeF2Y(heOrderEntity.calPayable());
            BigDecimal orderAmount = AmountChangeUtil.changeF2Y(heOrderEntity.getOrderAmount());
            fillOrderGoods(value, orderAmount, calPayableAmount);
        });
        List<HeOrderRefundEntity> refundList = orderRefundRepository.getRefundByOrderIdList(orderIds);
        Map<Integer, String> refundMap = CollectionUtils.isEmpty(refundList) ? Maps.newHashMap() : refundList.stream().collect(Collectors.toMap(HeOrderRefundEntity::getOrderId, HeOrderRefundEntity::getRefundOrderSn, (v1, v2) -> v2));
        return PageUtils.convert2PageDTO(pageList, temp -> buildWechatMyOrderNewVO(temp, orderGoodsByOrderIdMap, clientNameByIdMap, storeInfoMap, refundMap));
    }

    @Override
    public WechatMyOrderInfoNewVO myOrderInfo(WechatMyOrderInfoNewQuery req) {

        HeOrderEntity orderEntity = orderFactory.restoreWithStore(req.getOrderId());

        TabClientEntity tabClientById = clientRepository.getTabClientById(orderEntity.getClientUid());
        Map<Integer, TabClientEntity> clientNameByIdMap = Maps.newHashMap();
        clientNameByIdMap.put(tabClientById.getId(), tabClientById);

        Map<Integer, CfgStoreEntity> storeNameByIdMap = Maps.newHashMap();
        if (Objects.nonNull(orderEntity.getStore())) {
            storeNameByIdMap.put(orderEntity.getStore().getStoreId(), orderEntity.getStore());
        }

        List<HeOrderGoodsEntity> orderGoodsByOrderIdList = orderGoodsRepository.getByOrderIdList(Collections.singletonList(orderEntity.getOrderId()));

        BigDecimal calPayableAmount = AmountChangeUtil.changeF2Y(orderEntity.calPayable());
        BigDecimal orderAmount = AmountChangeUtil.changeF2Y(orderEntity.getOrderAmount());
        fillOrderGoods(orderGoodsByOrderIdList, orderAmount, calPayableAmount);
        Map<Integer, List<HeOrderGoodsEntity>> orderGoodsByOrderIdMap = orderGoodsByOrderIdList.stream().collect(Collectors.groupingBy(HeOrderGoodsEntity::getOrderId));

        List<HeOrderRefundEntity> refundList = orderRefundRepository.getRefundByOrderId(req.getOrderId());
        Map<Integer, String> collect = CollectionUtils.isEmpty(refundList) ? Maps.newHashMap() : refundList.stream().collect(Collectors.toMap(HeOrderRefundEntity::getOrderId, HeOrderRefundEntity::getRefundOrderSn, (v1, v2) -> v2));

        WechatMyOrderNewVO wechatMyOrderNewVO = buildWechatMyOrderNewVO(orderEntity, orderGoodsByOrderIdMap, clientNameByIdMap, storeNameByIdMap, collect);
        List<OrderGoodsInfoVO> orderGoodsList = wechatMyOrderNewVO.getOrderGoodsList();
        Map<Integer, List<OrderGoodsInfoVO>> listMap = orderGoodsList.stream().collect(Collectors.groupingBy(OrderGoodsInfoVO::getGift));
        wechatMyOrderNewVO.setNoGiftGoodsList(listMap.get(0));
        List<OrderGoodsInfoVO> giftGoodsList = listMap.get(1);
        if (CollectionUtils.isNotEmpty(giftGoodsList)) {
            Map<Boolean, List<OrderGoodsInfoVO>> promotionGoodsMap = giftGoodsList.stream().collect(Collectors.groupingBy(o -> Objects.isNull(o.getPromotionInfo()) || Objects.isNull(o.getPromotionInfo().getPromotionId())));
            wechatMyOrderNewVO.setGiftGoodsList(promotionGoodsMap.get(Boolean.TRUE));
            wechatMyOrderNewVO.setPromotionGoodsList(promotionGoodsMap.get(Boolean.FALSE));
        }

        WechatMyOrderInfoNewVO myOrderInfoNewVO = new WechatMyOrderInfoNewVO();
        List<OrderReductionEntity> incomeProofRecordByOrderIdList = orderReductionRepository.listByOrderIdList(Collections.singletonList(orderEntity.getOrderId()), com.google.common.collect.Lists.newArrayList(OrderDecreaseStatusEnum.APPROVED.getCode(), OrderDecreaseStatusEnum.NO_APPROVAL_NEEDED.getCode()));
        Long totalReductionAmount = incomeProofRecordByOrderIdList.stream().map(OrderReductionEntity::getDecreaseAmount).reduce(Long::sum).orElse(0L);
        myOrderInfoNewVO.setTotalReductionAmount(AmountChangeUtil.f2YScale2(totalReductionAmount));
        myOrderInfoNewVO.setOrderInfo(wechatMyOrderNewVO);
        myOrderInfoNewVO.setOrderSn(orderEntity.getOrderSn());
        myOrderInfoNewVO.setOrderTagId(orderEntity.getOrderTag());
        myOrderInfoNewVO.setOrderTagName(orderEntity.getOrderTagName());
        myOrderInfoNewVO.setStaffId(orderEntity.getStaffId());
        myOrderInfoNewVO.setCreateBy(orderEntity.getCreateBy());
        myOrderInfoNewVO.setCurrency(orderEntity.getCurrency());
        myOrderInfoNewVO.setOriginalGoodsTotalPrice(orderEntity.getOriginalGoodsTotalPrice());
        List<UserEntity> userEntityList = userRepository.queryUserByIdList(Arrays.asList(myOrderInfoNewVO.getStaffId(), myOrderInfoNewVO.getCreateBy().intValue()));

        if (CollectionUtils.isNotEmpty(userEntityList)) {
            userEntityList.stream()
                    .filter(item -> myOrderInfoNewVO.getStaffId().equals(item.getId()))
                    .findFirst().ifPresent(userEntity -> myOrderInfoNewVO.setStaffName(userEntity.getName()));
            userEntityList.stream()
                    .filter(item -> myOrderInfoNewVO.getCreateBy().intValue() == item.getId())
                    .findFirst().ifPresent(userEntity -> myOrderInfoNewVO.setCreateName(userEntity.getName()));

        }

        List<HeOrderVoucherEntity> orderVoucher = orderVoucherRepository.getByOrderId(req.getOrderId());
        myOrderInfoNewVO.setUrlList(CollectionUtils.isEmpty(orderVoucher) ? Lists.newArrayList() : orderVoucher.stream().map(HeOrderVoucherEntity::getUrl).collect(Collectors.toList()));
        myOrderInfoNewVO.setSpecial(CollectionUtils.isEmpty(orderVoucher) ? Boolean.FALSE : Boolean.TRUE);
        myOrderInfoNewVO.setRemark(orderEntity.getRemark());
        HeOrderUserSnapshotEntity userEsign = orderUserSnapshotRepository.queryByOrderId(req.getOrderId());
        if (Objects.nonNull(userEsign)) {
            myOrderInfoNewVO.setPredictBornDate(userEsign.getPredictBornDate());
            myOrderInfoNewVO.setPredictBornDateStr(DateUtil.formatDate(userEsign.getPredictBornDate()));
            myOrderInfoNewVO.setFetusNum(userEsign.getFetusNum());
            myOrderInfoNewVO.setFetusNumStr(StayinProductionModeEnum.fromCode(userEsign.getFetusNum()));
            myOrderInfoNewVO.setWantIn(userEsign.getWantIn());
            myOrderInfoNewVO.setWantInStr(DateUtil.formatDate(userEsign.getWantIn()));
            myOrderInfoNewVO.setPhone(SensitiveInformationUtil.conductPhoneOrIdCardNo(userEsign.getPhone()));
        }
        myOrderInfoNewVO.setPayAmount(AmountChangeUtil.f2YScale2(orderEntity.getPayAmount()));
        myOrderInfoNewVO.setCalPayable(AmountChangeUtil.f2YScale2(orderEntity.calPayable()));
        myOrderInfoNewVO.setOrderAmount(AmountChangeUtil.f2YScale2(orderEntity.getOrderAmount()));
        Integer selectedAmount = orderEntity.fetchSelectedAmount();
        myOrderInfoNewVO.setSelectedAmount(AmountChangeUtil.f2YScale2(selectedAmount));
        myOrderInfoNewVO.setGiftAmount(AmountChangeUtil.f2YScale2(orderEntity.getOrderAmount() - selectedAmount));
        myOrderInfoNewVO.setDiscountAmount(AmountChangeUtil.f2YScale2(orderEntity.getOrderAmount() - orderEntity.signPayAmount()));
        myOrderInfoNewVO.setOrderDateStr(DateUtil.formatDateTime(new Date(orderEntity.getCreatedAt() * 1000)));
        List<OrderReductionVO> orderReductionVOS = orderConvert.orderReductionEntityList2OrderReductionVOList(incomeProofRecordByOrderIdList);
        for (OrderReductionVO orderReductionVO : orderReductionVOS) {
            orderReductionVO.setAfterAmount(AmountChangeUtil.f2YScale2(orderReductionVO.getAfterAmount().intValue()));
            orderReductionVO.setBeforeAmount(AmountChangeUtil.f2YScale2(orderReductionVO.getBeforeAmount().intValue()));
            orderReductionVO.setDecreaseAmount(AmountChangeUtil.f2YScale2(orderReductionVO.getDecreaseAmount().intValue()));
        }

        myOrderInfoNewVO.setOrderReductionVOList(orderReductionVOS);
        List<HeIncomeRecordEntity> incomeList = orderIncomeDomainService.queryEffectiveRecord(orderEntity);
        if (CollectionUtils.isEmpty(incomeList)) {
            return myOrderInfoNewVO;
        }
        List<WechatMyOrderInfoNewVO.IncomeRecord> incomeRecordList = Lists.newArrayList();
        incomeList.forEach(income -> {

            WechatMyOrderInfoNewVO.IncomeRecord incomeRecord = new WechatMyOrderInfoNewVO.IncomeRecord();
            incomeRecord.setId(income.getId());
            incomeRecord.setIncomeSn(income.getIncomeSn());
            incomeRecord.setPayType(income.getPayType());
            incomeRecord.setPayTypeStr(OmniPayTypeEnum.getName(income.getPayType()));
            incomeRecord.setStatus(income.getStatus());
            incomeRecord.setStatusStr(IncomeRecordPayStatusEnum.getName(income.getStatus()));
            incomeRecord.setPayTime(DateUtils.secondsToTimeStr(income.getPayTime()));
            incomeRecord.setIncome(AmountChangeUtil.changeF2Y(income.getIncome()));
            incomeRecord.setCurrency(income.getCurrency());
            incomeRecordList.add(incomeRecord);
        });
        myOrderInfoNewVO.setIncomeRecordList(incomeRecordList);
        if (CollectionUtils.isEmpty(refundList)) {
            return myOrderInfoNewVO;
        }
        List<HeOrderRefundEntity> parentRefundList = refundList.stream().filter(item -> Objects.isNull(item.getParentRefundOrderSn())).collect(Collectors.toList());
        parentRefundList = parentRefundList.stream().filter(o -> !OmniPayTypeEnum.REDUCTION.getCode().equals(o.getRefundType())).collect(Collectors.toList());
        List<HeOrderRefundEntity> childRefundList = refundList.stream().filter(item -> ObjectUtil.isNotEmpty(item.getParentRefundOrderSn())).collect(Collectors.toList());
        childRefundList = childRefundList.stream().filter(o -> !OmniPayTypeEnum.REDUCTION.getCode().equals(o.getRefundType())).collect(Collectors.toList());

        List<WechatMyOrderInfoNewVO.OrderRefund> orderRefundList = Lists.newArrayList();
        List<HeOrderRefundEntity> finalChildRefundList = childRefundList;
        parentRefundList.forEach(refund -> {
            WechatMyOrderInfoNewVO.OrderRefund orderRefund = new WechatMyOrderInfoNewVO.OrderRefund();
            orderRefund.setId(refund.getId());
            orderRefund.setRefundOrderSn(refund.getRefundOrderSn());
            orderRefund.setCreatedAt(DateUtils.secondsToTimeStr(refund.getCreatedAt()));

            List<HeOrderRefundEntity> thisChild = finalChildRefundList.stream().filter(r -> ObjectUtil.isNotEmpty(r.getParentRefundOrderSn())
                    && r.getParentRefundOrderSn().equals(refund.getRefundOrderSn())
            ).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(thisChild)) {
                Integer applySum = thisChild.stream().mapToInt(HeOrderRefundEntity::getApplyAmount).sum();
                Integer actualSum = thisChild.stream().mapToInt(HeOrderRefundEntity::getActualAmount).sum();
                orderRefund.setActualAmount(AmountChangeUtil.changeF2Y(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode().equals(refund.getStatus()) ? actualSum : applySum));
            } else {
                orderRefund.setActualAmount(AmountChangeUtil.changeF2Y(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode().equals(refund.getStatus()) ? refund.getActualAmount() : refund.getApplyAmount()));
            }
            orderRefund.setStatus(refund.getStatus());
            orderRefund.setStatusStr(OmniRefundApproveEnum.getName(refund.getStatus()));
            orderRefund.setCurrency(refund.getCurrency());
            orderRefundList.add(orderRefund);
        });
        myOrderInfoNewVO.setOrderRefundList(orderRefundList);
        return myOrderInfoNewVO;
    }

    private void fillOrderGoods(List<HeOrderGoodsEntity> orderGoodsByOrderIdList, BigDecimal orderAmount, BigDecimal calPayableAmount) {

        orderGoodsByOrderIdList = orderGoodsByOrderIdList.stream().filter(item -> Objects.nonNull(item.getType()) && !CombineTypeEnum.COMBINE_SUB.code().equals(item.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderGoodsByOrderIdList)) {
            return;
        }
        orderGoodsByOrderIdList.sort(Comparator.comparing(HeOrderGoodsEntity::getGoodsPriceOrgin));
        HeOrderGoodsEntity orderGoodsLast = orderGoodsByOrderIdList.get(orderGoodsByOrderIdList.size() - 1);
        BigDecimal allocationAmount = BigDecimal.ZERO;
        for (HeOrderGoodsEntity orderGoodsEntity : orderGoodsByOrderIdList) {
            orderGoodsEntity.setPayAmount(1);
//            if (orderGoodsLast.getId().equals(orderGoodsEntity.getId())) {
//                orderGoodsEntity.setPayAmount(AmountChangeUtil.changeY2FFoInt(calPayableAmount.subtract(allocationAmount)));
//            } else {
//                // A应付 = 订单应付 * (商品A原数量 * 商品A原单价 / 订单总原价)
//                BigDecimal payable = calPayableAmount.multiply(new BigDecimal(orderGoodsEntity.getGoodsNum()).multiply(AmountChangeUtil.changeF2Y(orderGoodsEntity.getGoodsPriceOrgin().longValue()))).divide(orderAmount, 2, BigDecimal.ROUND_DOWN);
//                orderGoodsEntity.setPayAmount(AmountChangeUtil.changeY2FFoInt(payable));
//                allocationAmount = allocationAmount.add(payable);
//            }
        }
    }

    @Override
    public Result<List<OrderOperateButton>> queryButtonByOrderId(Integer orderId) {

        HeOrderEntity orderEntity = orderRepository.getByOrderId(orderId);
        if (!orderEntity.isNewOrder()) {
            return Result.failed(ResultEnum.NOT_EXIST.getCode(), "不支持旧订单查询");
        }
        List<OrderOperateButton> buttons = buttonBuilder.build(orderEntity);

        return Result.success(buttons);
    }

    @Override
    public Result<List<OrderOperateButton>> queryButtonByOrderIdPOST(ButtonReq buttonReq) {
        Result<List<OrderOperateButton>> listResult = queryButtonByOrderId(buttonReq.getOrderId());
        List<OrderOperateButton> data = listResult.getData();

        List<Long> roles = JwtUtil.getJwtTokenUserInfo().getRoleIds();
        log.info("当前员工所有角色：{}", roles);

        Result<List<String>> rolePermission = permissionService.queryPermissionListByRoleIdList(roles);
        List<String> permissionList = rolePermission.getData();
        log.info("当前员工所有角色权限：{}", permissionList);

        if (CollectionUtils.isEmpty(permissionList) || (CollectionUtils.isNotEmpty(permissionList) && !permissionList.contains(RoleConstant.REFUND_REPAYMENT))) {
            return Result.success(data.stream().filter(d -> !d.getCode().equals(OrderButtonEnum.REFUND_RETURN_PAY.getCode())).collect(Collectors.toList()));
        }
        return listResult;
    }

    @Override
    public OrderInfoNewVO orderInfo(WechatMyOrderInfoNewQuery req) {

        HeOrderEntity orderEntity = orderFactory.restoreWithStore(req.getOrderId());
        if (!orderEntity.isNewOrder()) {
            OrderInfoQueryTemplate orderInfoQueryTemplate = getOrderInfoQueryTemplate(orderEntity.getOrderType());
            return orderInfoQueryTemplate.queryOrderInfo(orderEntity);
        }
        OrderInfoNewVO orderInfoNewVO = orderConvert.entity2OrderInfoNewVO(orderEntity);
        if (Objects.nonNull(orderEntity.getStore())) {
            List<SelectRespVO> attribute = storeClient.getAttribute(dynamicConfig.getWarZone());
            Map<String, String> warZoneMap = attribute.stream().collect(Collectors.toMap(SelectRespVO::getValue, SelectRespVO::getLabel, (v1, v2) -> v1));
            orderInfoNewVO.setStoreName(orderEntity.getStore().getStoreName());
            orderInfoNewVO.setTypeName(this.getTypeName(orderEntity.getStore().getType(), orderEntity.getStore().getChildType()));
            orderInfoNewVO.setWarZone(orderEntity.getStore().getWarzone());
            orderInfoNewVO.setWarZoneDesc(warZoneMap.get(String.valueOf(orderInfoNewVO.getWarZone())));
        }
        TabClientEntity tabClientById = clientRepository.getTabClientById(orderEntity.getClientUid());
        if (Objects.nonNull(tabClientById)) {
            orderInfoNewVO.setCustomerName(tabClientById.getName());
            orderInfoNewVO.setCustomerMobile(tabClientById.getPhone());
        }
        List<UserEntity> userEntityList = userRepository.queryUserByIdList(Arrays.asList(orderInfoNewVO.getStaffId(), orderInfoNewVO.getCreateBy().intValue()));
        if (CollectionUtils.isNotEmpty(userEntityList)) {
            userEntityList.stream()
                    .filter(item -> orderInfoNewVO.getStaffId().equals(item.getId()))
                    .findFirst().ifPresent(userEntity -> orderInfoNewVO.setStaffName(userEntity.getName()));
            userEntityList.stream()
                    .filter(item -> orderInfoNewVO.getCreateBy().intValue() == item.getId())
                    .findFirst().ifPresent(userEntity -> orderInfoNewVO.setCreateName(userEntity.getName()));

        }

        List<HeOrderVoucherEntity> orderVoucher = orderVoucherRepository.getByOrderId(req.getOrderId());
        orderInfoNewVO.setUrlList(CollectionUtils.isEmpty(orderVoucher) ? Lists.newArrayList() : orderVoucher.stream().map(HeOrderVoucherEntity::getUrl).collect(Collectors.toList()));
        orderInfoNewVO.setRemark(orderEntity.getRemark());
        HeOrderUserSnapshotEntity userEsign = orderUserSnapshotRepository.queryByOrderId(req.getOrderId());
        if (Objects.nonNull(userEsign)) {
            orderInfoNewVO.setPredictBornDate(DateUtils.FormatDate(userEsign.getPredictBornDate()));
            orderInfoNewVO.setFetusNum(userEsign.getFetusNum());
            orderInfoNewVO.setFetusNumStr(StayinProductionModeEnum.fromCode(userEsign.getFetusNum()));
            orderInfoNewVO.setWantIn(DateUtils.FormatDate(userEsign.getWantIn()));
            orderInfoNewVO.setWantInStr(DateUtils.FormatDate(userEsign.getWantIn()));
        }
        orderInfoNewVO.setPayAmount(convertBigDecimal2Str(orderEntity.getCurrency(), AmountChangeUtil.f2YDown(orderEntity.getPayAmount())));
        Integer i = orderEntity.signPayAmount();
        orderInfoNewVO.setCalPayableAmount(convertBigDecimal2Str(orderEntity.getCurrency(), AmountChangeUtil.f2YDown(i)));

        Integer discountAmount = orderEntity.getOrderAmount() - i;

        orderInfoNewVO.setDiscountAmount(convertBigDecimal2Str(orderEntity.getCurrency(), AmountChangeUtil.f2YDown(discountAmount)));
        orderInfoNewVO.setOrderAmount(convertBigDecimal2Str(orderEntity.getCurrency(), AmountChangeUtil.f2YDown(orderEntity.getOrderAmount())));
        Integer selectedAmount = orderEntity.fetchSelectedAmount();
        orderInfoNewVO.setGiftAmount(convertBigDecimal2Str(orderEntity.getCurrency(), AmountChangeUtil.f2YDown(orderEntity.getOrderAmount() - selectedAmount)));
        orderInfoNewVO.setSelectedAmount(convertBigDecimal2Str(orderEntity.getCurrency(), AmountChangeUtil.f2YDown(selectedAmount)));

        if (Boolean.TRUE.equals(orderEntity.getTransferOrder())) {
            ExtraInfo extraInfo = new ExtraInfo();
            ArrayList<CustomAttribute> fulfillExtraList = new ArrayList<>();

            //设置胎数、预产期、预计入住日期
            CustomAttribute customAttribute = new CustomAttribute();
            customAttribute.setCode("predictBornDate");
            customAttribute.setValue(orderInfoNewVO.getPredictBornDate());
            customAttribute.setShowStr(orderInfoNewVO.getPredictBornDate());
            customAttribute.setPropertyName("预产期");


            CustomAttribute customAttribute1 = new CustomAttribute();
            customAttribute1.setCode("wantIn");
            customAttribute1.setValue(orderInfoNewVO.getWantIn());
            customAttribute1.setShowStr(orderInfoNewVO.getWantInStr());
            customAttribute1.setPropertyName("预计入住日期");

            CustomAttribute customAttribute2 = new CustomAttribute();
            customAttribute2.setCode("fetusNum");
            Integer fetusNum = orderInfoNewVO.getFetusNum();
            if (Objects.nonNull(fetusNum)) {
                customAttribute2.setValue(fetusNum + "");
                customAttribute2.setShowStr(StayinProductionModeEnum.fromCode(fetusNum));
            }
            customAttribute2.setPropertyName("胎数");

            fulfillExtraList.add(customAttribute);
            fulfillExtraList.add(customAttribute1);
            fulfillExtraList.add(customAttribute2);
            extraInfo.setFulfillExtraList(fulfillExtraList);
            orderInfoNewVO.setExtraInfo(extraInfo);

            orderInfoNewVO.setGiftAmount("￥0.00");
        }

        return orderInfoNewVO;
    }

    @Override
    public List<GoodsNewVO> goodsList(WechatMyOrderInfoNewQuery req) {
        HeOrderEntity orderEntity = orderRepository.getByOrderId(req.getOrderId());
        if (ObjectUtil.isEmpty(orderEntity)) {
            orderEntity = orderRepository.getByOrderSn(req.getOrderSn());
        }

        Assert.isTrue(Objects.nonNull(orderEntity), "该订单不存在");
        if (!orderEntity.isNewOrder()) {
            OrderInfoQueryTemplate orderInfoQueryTemplate = getOrderInfoQueryTemplate(orderEntity.getOrderType());
            return orderInfoQueryTemplate.queryOrderGoodsList(orderEntity);
        }
        List<HeOrderGoodsEntity> orderGoodsByOrderIdList = orderGoodsRepository.getByOrderIdList(Collections.singletonList(req.getOrderId()));
        if (CollectionUtils.isEmpty(orderGoodsByOrderIdList)) {
            return Lists.newArrayList();
        }
        List<Integer> goodsCategoryIdList = orderGoodsByOrderIdList.stream().filter(item -> Objects.nonNull(item.getBackCategoryId())).map(a -> a.getBackCategoryId().intValue()).collect(Collectors.toList());
        List<GoodsCategoryBackEntity> goodsCategoryList = goodsCategoryBackRepository.queryCategoryListByIds(goodsCategoryIdList);
        Map<Long, String> backCategoryMap = CollectionUtils.isEmpty(goodsCategoryList) ? Maps.newHashMap() : goodsCategoryList.stream().collect(Collectors.toMap(a -> Long.valueOf(a.getId()), GoodsCategoryBackEntity::getName));
        List<GoodsNewVO> resultList = Lists.newArrayList();

        List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList = orderRefundGoodsRepository.queryByOrderId(orderEntity.getOrderId());
        Map<Integer, List<HeOrderRefundGoodsEntity>> heOrderRefundGoodsMap = orderRefundGoodsEntityList.stream().filter(item -> Objects.nonNull(item.getOrderGoodsId())).collect(Collectors.groupingBy(HeOrderRefundGoodsEntity::getOrderGoodsId));

        List<OrderGiftExtendEntity> orderGiftExtendEntityList = orderGiftExtendRepository.getByOrderId(orderEntity.getOrderId());
        Map<Integer, List<Integer>> orderGiftIdByOrderGoodsIdMap = this.getOrderGiftIdByOrderGoodsIdMap(orderGiftExtendEntityList, orderGoodsByOrderIdList);
        Map<Integer, OrderGiftExtendEntity> orderGiftExtendEntityMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(orderGiftExtendEntityList)) {
            orderGiftExtendEntityMap = orderGiftExtendEntityList.stream().collect(Collectors.toMap(OrderGiftExtendEntity::getId, v -> v, (k1, k2) -> k1));
        }

        List<OrderProductionExtendEntity> orderProductionExtendList = orderProductionExtendRepository.queryByOrderId(orderEntity.getOrderId());
        Map<Integer, List<Integer>> orderExtendIdByOrderGoodsIdMap = this.getOrderExtendIdByOrderGoodsIdMap(orderProductionExtendList, orderGoodsByOrderIdList);
        Map<Integer, OrderProductionExtendEntity> orderBuyExtendEntityMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(orderProductionExtendList)) {
            orderBuyExtendEntityMap = orderProductionExtendList.stream().collect(Collectors.toMap(OrderProductionExtendEntity::getId, v -> v, (k1, k2) -> k1));
        }

        Map<String, Integer> availableNumMap = assetManager.getCountByGoodsSn(orderEntity.getOrderSn(), orderGoodsByOrderIdList);

        for (HeOrderGoodsEntity orderGoods : orderGoodsByOrderIdList) {
            GoodsNewVO goodsNewVO = BeanMapper.map(orderGoods, GoodsNewVO.class);
            goodsNewVO.setGoodsPriceOrgin(orderEntity.getCurrency() + AmountChangeUtil.f2YDown(orderGoods.getGoodsPriceOrgin()));
            goodsNewVO.setGiftStr(orderGoods.getGift() == 0 ? "购买" : "礼赠");

            PromotionInfo promotionInfo = orderGoods.getPromotionInfo();
            String giftTypeStr = orderGoods.getGift() == 0 ? "" : Objects.isNull(promotionInfo) ? "定制礼赠" : ("活动礼赠-" + promotionInfo.getPromotionName());
            goodsNewVO.setGiftTypeStr(giftTypeStr);

            goodsNewVO.setCurrency(orderEntity.getCurrency());
            goodsNewVO.setCategoryBack(orderGoods.getBackCategoryId());
            goodsNewVO.setCategoryBackStr(Objects.isNull(orderGoods.getBackCategoryId()) ? StringUtils.EMPTY : backCategoryMap.get(orderGoods.getBackCategoryId()));
            int verificationNum = 0;
            if (Objects.equals(orderGoods.getGift(), one)) {
                List<Integer> ids = orderGiftIdByOrderGoodsIdMap.get(orderGoods.getId());
                if (CollectionUtils.isNotEmpty(ids) && CollectionUtils.isNotEmpty(orderGiftExtendEntityList)) {
                    verificationNum = getGiftVerificationNum(ids, orderGiftExtendEntityMap);
                }
            } else {
                List<Integer> ids = orderExtendIdByOrderGoodsIdMap.get(orderGoods.getId());
                if (CollectionUtils.isNotEmpty(ids) && CollectionUtils.isNotEmpty(orderProductionExtendList)) {
                    verificationNum = getBuyVerificationNum(ids, orderBuyExtendEntityMap);
                }
            }
            goodsNewVO.setRefundGoodsNum(getRefundGoodsTotalNum(heOrderRefundGoodsMap, orderGoods, verificationNum));
            Integer num = availableNumMap.get(orderGoods.getOrderGoodsSn());
            if (Objects.nonNull(num)) {
                goodsNewVO.setRefundGoodsNum(num);
            }
            resultList.add(goodsNewVO);
        }
        return resultList;
    }


    private int getBuyVerificationNum(List<Integer> ids, Map<Integer, OrderProductionExtendEntity> orderBuyExtendEntityMap) {

        int verificationNum = 0;
        for (Integer id : ids) {
            OrderProductionExtendEntity orderBuyExtend = orderBuyExtendEntityMap.get(id);
            if (Objects.isNull(orderBuyExtend)) {
                continue;
            }
            if (Objects.equals(orderBuyExtend.getVerificationStatus(), one)) {
                verificationNum = verificationNum + 1;
            }
        }
        return verificationNum;
    }

    private Integer getGiftVerificationNum(List<Integer> ids, Map<Integer, OrderGiftExtendEntity> orderGiftExtendEntityMap) {

        int verificationNum = 0;
        for (Integer id : ids) {
            OrderGiftExtendEntity orderGiftExtend = orderGiftExtendEntityMap.get(id);
            if (Objects.isNull(orderGiftExtend)) {
                continue;
            }
            if (Objects.equals(orderGiftExtend.getVerificationStatus(), one)) {
                verificationNum = verificationNum + 1;
            }
        }
        return verificationNum;
    }

    private Map<Integer, List<Integer>> getOrderGiftIdByOrderGoodsIdMap(List<OrderGiftExtendEntity> orderGiftList, List<HeOrderGoodsEntity> heOrderGoodsList) {

        Map<Integer, List<Integer>> orderGiftIdByOrderGoodsIdMap = new HashMap<>();
        if (CollectionUtils.isEmpty(orderGiftList) || CollectionUtils.isEmpty(heOrderGoodsList)) {
            return orderGiftIdByOrderGoodsIdMap;
        }
        List<HeOrderGoodsEntity> orderGoodsEntityList = heOrderGoodsList.stream().filter(item -> one.equals(item.getGift())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderGoodsEntityList)) {
            return orderGiftIdByOrderGoodsIdMap;
        }
        List<OrderGiftExtendEntity> giftGoodsList = orderGiftList.stream().filter(item -> Objects.nonNull(item.getSource()) && item.getSource() <= six).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(giftGoodsList)) {
            return orderGiftIdByOrderGoodsIdMap;
        }
        orderGoodsEntityList.sort(Comparator.comparing(HeOrderGoodsEntity::getId));
        giftGoodsList.sort(Comparator.comparing(OrderGiftExtendEntity::getId));
        Map<Integer, List<Integer>> giftIdsBySkuIdMap = giftGoodsList.stream().collect(Collectors.groupingBy(OrderGiftExtendEntity::getSkuId, Collectors.mapping(OrderGiftExtendEntity::getId, Collectors.toList())));
        orderGoodsEntityList.forEach(orderGoodsEntity -> {
            List<Integer> giftIds = giftIdsBySkuIdMap.get(orderGoodsEntity.getSkuId());
            orderGiftIdByOrderGoodsIdMap.put(orderGoodsEntity.getId(), CollectionUtils.isEmpty(giftIds) ? Lists.newArrayList() : giftIds.stream().limit(orderGoodsEntity.getGoodsNum()).collect(Collectors.toList()));
            giftIdsBySkuIdMap.put(orderGoodsEntity.getSkuId(), CollectionUtils.isEmpty(giftIds) ? Lists.newArrayList() : giftIds.stream().skip(orderGoodsEntity.getGoodsNum()).collect(Collectors.toList()));
        });
        return orderGiftIdByOrderGoodsIdMap;
    }

    private Map<Integer, List<Integer>> getOrderExtendIdByOrderGoodsIdMap(List<OrderProductionExtendEntity> orderProductionExtendList, List<HeOrderGoodsEntity> heOrderGoodsList) {

        Map<Integer, List<Integer>> orderBuyIdByOrderGoodsIdMap = new HashMap<>();
        if (CollectionUtils.isEmpty(orderProductionExtendList) || CollectionUtils.isEmpty(heOrderGoodsList)) {
            return orderBuyIdByOrderGoodsIdMap;
        }
        List<HeOrderGoodsEntity> orderGoodsEntityList = heOrderGoodsList.stream().filter(item -> !one.equals(item.getGift())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderGoodsEntityList)) {
            return orderBuyIdByOrderGoodsIdMap;
        }
        orderGoodsEntityList.sort(Comparator.comparing(HeOrderGoodsEntity::getId));
        orderProductionExtendList.sort(Comparator.comparing(OrderProductionExtendEntity::getId));
        Map<Integer, List<Integer>> buyIdsBySkuIdMap = orderProductionExtendList.stream().collect(Collectors.groupingBy(OrderProductionExtendEntity::getSkuId, Collectors.mapping(OrderProductionExtendEntity::getId, Collectors.toList())));
        orderGoodsEntityList.forEach(orderGoodsEntity -> {
            List<Integer> buyIds = buyIdsBySkuIdMap.get(orderGoodsEntity.getSkuId());
            orderBuyIdByOrderGoodsIdMap.put(orderGoodsEntity.getId(), CollectionUtils.isEmpty(buyIds) ? Lists.newArrayList() : buyIds.stream().limit(orderGoodsEntity.getGoodsNum()).collect(Collectors.toList()));
            buyIdsBySkuIdMap.put(orderGoodsEntity.getSkuId(), CollectionUtils.isEmpty(buyIds) ? Lists.newArrayList() : buyIds.stream().skip(orderGoodsEntity.getGoodsNum()).collect(Collectors.toList()));
        });
        return orderBuyIdByOrderGoodsIdMap;
    }

    private Integer getRefundGoodsTotalNum(Map<Integer, List<HeOrderRefundGoodsEntity>> heOrderRefundGoodsMap, HeOrderGoodsEntity orderGoods, Integer verificationNum) {

        int refundGoodsNum = orderGoods.getGoodsNum();
        List<HeOrderRefundGoodsEntity> heOrderRefundGoodsEntityList = heOrderRefundGoodsMap.get(orderGoods.getId());
        if (CollectionUtils.isEmpty(heOrderRefundGoodsEntityList)) {
            return refundGoodsNum - verificationNum;
        }

        // 按退款sn和orderGoodsSn组合分组，相同退款sn且相同orderGoodsSn的记录只算一条，避免重复计算
        int refundSumNum = heOrderRefundGoodsEntityList.stream()
                .collect(Collectors.groupingBy(refund -> refund.getRefundOrderSn() + "_" + refund.getOrderGoodsSn()))
                .values()
                .stream()
                .mapToInt(refundList -> refundList.get(0).getRefundNum()) // 每组只取第一条记录的退款数量
                .sum();

        return refundGoodsNum - refundSumNum - verificationNum;
    }

    @Override
    public OrderNewVO goodsListBySn(String orderSn) {
        OrderNewVO orderNewVO = new OrderNewVO();
        orderNewVO.setGoodsNewVOList(new ArrayList<>());
        HeOrderEntity orderEntity = orderRepository.getByOrderSn(orderSn);
        Assert.isTrue(Objects.nonNull(orderEntity), "该订单不存在");

        orderNewVO.setExtraInfo(orderEntity.getExtraInfo());

        if (!orderEntity.isNewOrder()) {
            OrderInfoQueryTemplate orderInfoQueryTemplate = getOrderInfoQueryTemplate(orderEntity.getOrderType());
            orderNewVO.setGoodsNewVOList(orderInfoQueryTemplate.queryOrderGoodsList(orderEntity));
            return orderNewVO;
        }
        List<HeOrderGoodsEntity> orderGoodsByOrderIdList = orderGoodsRepository.getByOrderIdList(Collections.singletonList(orderEntity.getOrderId()));
        if (CollectionUtils.isEmpty(orderGoodsByOrderIdList)) {
            return orderNewVO;
        }
        List<Integer> goodsCategoryIdList = orderGoodsByOrderIdList.stream().filter(item -> Objects.nonNull(item.getBackCategoryId())).map(a -> a.getBackCategoryId().intValue()).collect(Collectors.toList());
        List<GoodsCategoryBackEntity> goodsCategoryList = goodsCategoryBackRepository.queryCategoryListByIds(goodsCategoryIdList);
        Map<Long, String> backCategoryMap = CollectionUtils.isEmpty(goodsCategoryList) ? Maps.newHashMap() : goodsCategoryList.stream().collect(Collectors.toMap(a -> Long.valueOf(a.getId()), GoodsCategoryBackEntity::getName));
        List<GoodsNewVO> resultList = Lists.newArrayList();
        for (HeOrderGoodsEntity orderGoods : orderGoodsByOrderIdList) {
            GoodsNewVO goodsNewVO = BeanMapper.map(orderGoods, GoodsNewVO.class);
            goodsNewVO.setGoodsPriceOrgin(orderEntity.getCurrency() + AmountChangeUtil.f2YDown(orderGoods.getGoodsPriceOrgin()));
            goodsNewVO.setGiftStr(orderGoods.getGift() == 0 ? "购买" : "礼赠");

            PromotionInfo promotionInfo = orderGoods.getPromotionInfo();
            String giftTypeStr = orderGoods.getGift() == 0 ? "" : Objects.isNull(promotionInfo) ? "定制礼赠" : ("活动礼赠-" + promotionInfo.getPromotionName());
            goodsNewVO.setGiftTypeStr(giftTypeStr);

            goodsNewVO.setCurrency(orderEntity.getCurrency());
            goodsNewVO.setCategoryBack(orderGoods.getBackCategoryId());
            goodsNewVO.setCategoryBackStr(Objects.isNull(orderGoods.getBackCategoryId()) ? StringUtils.EMPTY : backCategoryMap.get(orderGoods.getBackCategoryId()));
            resultList.add(goodsNewVO);
        }
        orderNewVO.setGoodsNewVOList(resultList);
        return orderNewVO;
    }

    @Override
    public ContractNewVO contractList(WechatMyOrderInfoNewQuery req) {

        Assert.isTrue(Objects.nonNull(req.getContractStatus()), "合同状态不能为空");
        HeOrderEntity orderEntity = orderRepository.getByOrderId(req.getOrderId());
        Assert.isTrue(Objects.nonNull(orderEntity), "该订单不存在");
        if (!orderEntity.isNewOrder()) {
            OrderInfoQueryTemplate orderInfoQueryTemplate = getOrderInfoQueryTemplate(orderEntity.getOrderType());
            return orderInfoQueryTemplate.queryOrderContractList(req.getContractStatus(), orderEntity.getClientUid(), orderEntity.getOrderId());
        }
        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(req.getOrderId());
        Assert.isTrue(Objects.nonNull(heOrderUserSnapshotEntity), "订单客户主体信息不存在");
        ContractNewVO contractNewVO = new ContractNewVO();
        ContractNewVO.ClientInfo clientInfo = BeanMapper.map(heOrderUserSnapshotEntity, ContractNewVO.ClientInfo.class);
        clientInfo.setAuthTypeStr(Objects.isNull(clientInfo.getAuthType()) || clientInfo.getAuthType() == 0 ? "实名认证" : "邮箱");
        ClientCardTypeRequest request = new ClientCardTypeRequest();
        request.setBasicUid(orderEntity.getBasicUid());
        Result<ClientCardTypeVO> clientCardTypeVOResult = tabClientService.queryCardList(request);
        Boolean supportOption = !clientCardTypeVOResult.getSuccess() ? Boolean.FALSE : clientCardTypeVOResult.getData().getSupportOption();
        clientInfo.setRelationWithClientStr(RelationshipWithEnum.getValueByCode(clientInfo.getRelationWithClient()));
        clientInfo.setCertTypeStr(IdTypeEnum.getValueByCode(clientInfo.getCertType()));
        String provincesRegions = addressQueryService.getProvincesRegions(heOrderUserSnapshotEntity.getProvince(), heOrderUserSnapshotEntity.getCity(), heOrderUserSnapshotEntity.getRegion());
        clientInfo.setAllAddress(provincesRegions + heOrderUserSnapshotEntity.getAddress());
        contractNewVO.setClientInfo(clientInfo);
        HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity = orderBailorSnapshotRepository.queryByOrderId(req.getOrderId());
        String bailorName = StringUtils.EMPTY;
        String userName = heOrderUserSnapshotEntity.getName();
        Boolean supportOptionBailor = Boolean.FALSE;
        if (Objects.nonNull(heOrderBailorSnapshotEntity)) {
            ContractNewVO.BailorInfo bailorInfo = BeanMapper.map(heOrderBailorSnapshotEntity, ContractNewVO.BailorInfo.class);
            PhoneTypeEnum enumByCode = PhoneTypeEnum.getEnumByCode(bailorInfo.getPhoneType());
            bailorInfo.setPhoneTypeStr(Objects.isNull(enumByCode) ? StringUtils.EMPTY : enumByCode.desc());
            bailorInfo.setCertTypeStr(IdTypeEnum.getValueByCode(bailorInfo.getCertType()));
            bailorInfo.setAuthTypeStr(Objects.isNull(bailorInfo.getAuthType()) || bailorInfo.getAuthType() == 0 ? "实名认证" : "邮箱");
            contractNewVO.setBailorInfo(bailorInfo);
            bailorName = heOrderBailorSnapshotEntity.getName();
            if (StringUtils.isNotEmpty(heOrderBailorSnapshotEntity.getExt())) {
                List<HeOrderBailorSnapshotExtEntity> list = JSONUtil.toList(heOrderBailorSnapshotEntity.getExt(), HeOrderBailorSnapshotExtEntity.class);
                supportOptionBailor = list.size() > 1 ? Boolean.TRUE : Boolean.FALSE;
            }
        }
        List<ContractNewVO.ContractSignRecord> contractSignRecordList = Lists.newArrayList();
        List<HeOrderAttachmentEntity> heOrderAttachmentEntities = heOrderAttachmentRepository.queryListByCondition(req.getOrderId());
        Long orderId = Long.valueOf(req.getOrderId());
        List<MonthContractSignRecordEntity> mainContractList = monthContractSignRecordRepository.getListByOrderId(orderId, req.getContractStatus());
        List<MonthContractSignAgreementEntity> agreementContractList = monthContractSignAgreementRepository.list(orderId, req.getContractStatus());
        convertContractSignRecord1(heOrderAttachmentEntities, contractSignRecordList);
        convertContractSignRecord2(mainContractList, contractSignRecordList, req.getOrderId(), userName, bailorName);
        convertContractSignRecord3(agreementContractList, contractSignRecordList, userName, bailorName);
        if (supportOption && CollectionUtils.isEmpty(contractSignRecordList)) {
            clientInfo.setAuthType(0);
            clientInfo.setAuthTypeStr("实名认证");
        }
        if (supportOptionBailor && CollectionUtils.isEmpty(contractSignRecordList)) {
            contractNewVO.getBailorInfo().setAuthType(0);
            contractNewVO.getBailorInfo().setAuthTypeStr("实名认证");
        }
        if (CollectionUtils.isNotEmpty(contractSignRecordList)) {
            ContractNewVO.ContractSignRecord contractSignRecord = contractSignRecordList.stream().filter(item -> Objects.nonNull(item.getContractType()) && item.getContractType() == 1).findFirst().orElse(null);
            if (supportOption && Objects.isNull(contractSignRecord)) {
                clientInfo.setAuthType(0);
                clientInfo.setAuthTypeStr("实名认证");
            }
            ContractNewVO.ContractSignRecord contractSignRecord1 = contractSignRecordList.stream().filter(item -> Objects.nonNull(item.getContractType()) && item.getContractType() == 1 && TemplateContractTypeEnum.YZ_SAINTBELLA.code().equals(item.getTemplateContractType())).findFirst().orElse(null);
            if (Objects.nonNull(heOrderBailorSnapshotEntity) && supportOptionBailor && Objects.isNull(contractSignRecord1)) {
                contractNewVO.getBailorInfo().setAuthType(0);
                contractNewVO.getBailorInfo().setAuthTypeStr("实名认证");
            }
            contractSignRecordList.sort(Comparator.comparing(ContractNewVO.ContractSignRecord::getSignTime).reversed());
        }
        contractNewVO.setContractSignRecordList(contractSignRecordList);

        contractNewVOAfter(contractNewVO, req.getOrderId());
        return contractNewVO;
    }

    private void contractNewVOAfter(ContractNewVO contractNewVO, Integer orderId) {

        if (Objects.isNull(contractNewVO.getClientInfo()) || Objects.isNull(contractNewVO.getClientInfo().getAuthType())) {
            return;
        }

        OrderUserSnapshotReq req = new OrderUserSnapshotReq();
        req.setOrderId(orderId);
        req.setAuthType(contractNewVO.getClientInfo().getAuthType());
        TabClientVO tabClientVO = heOrderSnapshotService.queryClientInfo(req);
        if (Objects.isNull(tabClientVO)) {
            return;
        }
        contractNewVO.getClientInfo().setPhone(tabClientVO.getPhone());
        contractNewVO.getClientInfo().setCertType(tabClientVO.getCertType());
        contractNewVO.getClientInfo().setCertTypeStr(IdTypeEnum.getValueByCode(contractNewVO.getClientInfo().getCertType()));
        contractNewVO.getClientInfo().setIdCard(tabClientVO.getIdCard());
        contractNewVO.getClientInfo().setEmail(contractNewVO.getClientInfo().getAuthType() == 0 ? "" : tabClientVO.getEmail());
        if (StringUtils.isNotEmpty(contractNewVO.getClientInfo().getPhone())) {
            contractNewVO.getClientInfo().setHidePhone(SensitiveInformationUtil.conductPhoneOrIdCardNo(contractNewVO.getClientInfo().getPhone()));
        }
        if (Objects.isNull(contractNewVO.getBailorInfo())) {
            return;
        }

        OrderUserSnapshotReq bailorReq = new OrderUserSnapshotReq();
        bailorReq.setOrderId(orderId);
        bailorReq.setAuthType(contractNewVO.getBailorInfo().getAuthType());
        OrderUserSnapshotVO orderUserSnapshotVO = heOrderSnapshotService.queryBailorInfo(bailorReq);
        if (Objects.isNull(orderUserSnapshotVO)) {
            return;
        }
        contractNewVO.getBailorInfo().setPhone(contractNewVO.getBailorInfo().getAuthType() == 1 ? "" : orderUserSnapshotVO.getPhone());
        contractNewVO.getBailorInfo().setCertType(orderUserSnapshotVO.getCertType());
        contractNewVO.getBailorInfo().setCertTypeStr(IdTypeEnum.getValueByCode(contractNewVO.getBailorInfo().getCertType()));
        contractNewVO.getBailorInfo().setIdCard(orderUserSnapshotVO.getIdCard());
        contractNewVO.getBailorInfo().setEmail(contractNewVO.getBailorInfo().getAuthType() == 0 ? "" : orderUserSnapshotVO.getEmail());
        contractNewVO.getBailorInfo().setIdCardBack(orderUserSnapshotVO.getIdCardBack());
        contractNewVO.getBailorInfo().setIdCardFront(orderUserSnapshotVO.getIdCardFront());
        if (StringUtils.isNotEmpty(contractNewVO.getBailorInfo().getPhone())) {
            contractNewVO.getBailorInfo().setHidePhone(SensitiveInformationUtil.conductPhoneOrIdCardNo(contractNewVO.getBailorInfo().getPhone()));
        }
    }

    private List<ContractNewVO.ContractSignRecord> convertContractSignRecord1(List<HeOrderAttachmentEntity> entityList, List<ContractNewVO.ContractSignRecord> contractSignRecordList) {

        if (CollectionUtils.isEmpty(entityList)) {
            return contractSignRecordList;
        }
        entityList.forEach(orderAttachment -> {
            ContractNewVO.ContractSignRecord record = new ContractNewVO.ContractSignRecord();
            record.setContractType(ContractTypeEnum.ATTACHMENT_TYPE.code());
            record.setContractTypeName(ContractTypeEnum.ATTACHMENT_TYPE.desc());
            record.setTemplateType(TemplateTypeEnum.ATTACHMENT_TYPE.code());
            record.setTemplateTypeName(TemplateTypeEnum.ATTACHMENT_TYPE.desc());
            record.setContractName(orderAttachment.getName());
            record.setContractLongUrl(orderAttachment.getUrl());
            record.setSignTime(DateUtil.formatDateTime(new Date(orderAttachment.getCreatedAt() * 1000)));
            contractSignRecordList.add(record);
        });
        return contractSignRecordList;
    }

    private List<ContractNewVO.ContractSignRecord> convertContractSignRecord2(List<MonthContractSignRecordEntity> entityList, List<ContractNewVO.ContractSignRecord> contractSignRecordList, Integer orderId, String userName, String bailorName) {

        if (CollectionUtils.isEmpty(entityList)) {
            return contractSignRecordList;
        }
        entityList.forEach(contractSignRecord -> {
            ContractNewVO.ContractSignRecord record = new ContractNewVO.ContractSignRecord();
            record.setId(contractSignRecord.getId());
            record.setTemplateContractType(contractSignRecord.getTemplateContractType());
            record.setTemplateType(contractSignRecord.getTemplateType());
            record.setTemplateTypeName(TemplateTypeEnum.fromCode(contractSignRecord.getTemplateType()));
            record.setContractType(contractSignRecord.getContractType());
            record.setContractTypeName(ContractTypeEnum.fromCode(contractSignRecord.getContractType()));
            record.setContractName(contractSignRecord.getContractName());
            record.setDownloadUrl(contractSignRecord.getContractLongUrl());
            record.setSignTime(DateUtil.formatDateTime(contractSignRecord.getSignTime()));
            if (Objects.nonNull(contractSignRecord.getContractStatus()) && contractSignRecord.getContractStatus() == 2) {
                if (StringUtils.isEmpty(bailorName)) {
                    record.setSignName(userName);
                } else {
                    record.setSignName(TemplateContractTypeEnum.ENTRUST.code().equals(contractSignRecord.getTemplateContractType()) ? userName : bailorName);
                }
            }
            if (Objects.nonNull(contractSignRecord.getContractType())) {
                if (contractSignRecord.getContractType() == 1) {
                    record.setContractLongUrl(contractSignRecord.getContractLongUrl());
                }
                if (contractSignRecord.getContractType() == 3 && StringUtils.isNotEmpty(contractSignRecord.getImg())) {
                    record.setImg(JSON.parseArray(contractSignRecord.getImg(), String.class));
                }
            }
            contractSignRecordList.add(record);
        });
        return contractSignRecordList;
    }

    private List<ContractNewVO.ContractSignRecord> convertContractSignRecord3(List<MonthContractSignAgreementEntity> entityList, List<ContractNewVO.ContractSignRecord> contractSignRecordList, String userName, String bailorName) {

        if (CollectionUtils.isEmpty(entityList)) {
            return contractSignRecordList;
        }
        entityList.forEach(contractSignAgreement -> {
            ContractNewVO.ContractSignRecord record = new ContractNewVO.ContractSignRecord();
            record.setId(contractSignAgreement.getId());
            record.setTemplateContractType(contractSignAgreement.getTemplateContractType());
            record.setContractName(contractSignAgreement.getName());
            record.setContractLongUrl(contractSignAgreement.getContractShortUrl());
            record.setDownloadUrl(contractSignAgreement.getContractLongUrl());
            record.setSignTime(DateUtil.formatDateTime(DateUtil.date(contractSignAgreement.getSignTime().toLocalTime())));
            record.setContractType(ContractTypeEnum.ESIGN_TYPE.code());
            record.setContractTypeName(ContractTypeEnum.ESIGN_TYPE.desc());
            record.setTemplateType(TemplateTypeEnum.AGREEMENT_TYPE.code());
            record.setTemplateTypeName( TemplateContractTypeEnum.fromCode(contractSignAgreement.getTemplateContractType()));
            if (Objects.nonNull(contractSignAgreement.getState()) && contractSignAgreement.getState() == 2) {
                record.setSignName(StringUtils.isEmpty(bailorName) ? userName : bailorName);
            }
            contractSignRecordList.add(record);
        });
        return contractSignRecordList;
    }

    @Override
    public IncomeAndRefundVO incomeAndRefundRecord(WechatMyOrderInfoNewQuery req) {

        HeOrderEntity orderEntity = orderRepository.queryOrderById(req.getOrderId());
        Assert.isTrue(Objects.nonNull(orderEntity), "订单不存在");
        if (!orderEntity.isNewOrder()) {
            OrderInfoQueryTemplate orderInfoQueryTemplate = getOrderInfoQueryTemplate(orderEntity.getOrderType());
            IncomeAndRefundVO incomeAndRefundVO = orderInfoQueryTemplate.queryIncomeAndRefundRecord(orderEntity);
            incomeAndRefundVO.setTotalRefreshPayAmount(convertBigDecimal2Str(orderEntity.getCurrency(), AmountChangeUtil.f2YScale2(BigDecimal.ZERO.intValue())));
            incomeAndRefundVO.setTotalReductionAmount(incomeAndRefundVO.getTotalRefreshPayAmount());
            incomeAndRefundVO.setTotalOnlyRefundAmount(incomeAndRefundVO.getTotalRefreshPayAmount());
            return incomeAndRefundVO;
        }
        IncomeAndRefundVO incomeAndRefundVO = new IncomeAndRefundVO();
        String currency = orderEntity.getCurrency();
        incomeAndRefundVO.setCurrency(currency);
        List<OrderReductionEntity> orderReductionEntities = orderReductionRepository.listByOrderIdList(Collections.singletonList(orderEntity.getOrderId()), com.google.common.collect.Lists.newArrayList(OrderDecreaseStatusEnum.APPROVED.getCode(), OrderDecreaseStatusEnum.NO_APPROVAL_NEEDED.getCode()));
        if (CollectionUtils.isEmpty(orderReductionEntities)) {
            incomeAndRefundVO.setTotalReductionAmount(convertBigDecimal2Str(currency, AmountChangeUtil.f2YScale2(BigDecimal.ZERO.intValue())));
        } else {
            Long totalReductionAmount = orderReductionEntities.stream().map(OrderReductionEntity::getDecreaseAmount).reduce(Long::sum).orElse(0L);
            incomeAndRefundVO.setTotalReductionAmount(convertBigDecimal2Str(currency, AmountChangeUtil.f2YScale2(totalReductionAmount)));
        }
        incomeAndRefundVO.setTotalRefreshPayAmount(convertBigDecimal2Str(currency, AmountChangeUtil.f2YScale2(BigDecimal.ZERO.intValue())));
        incomeAndRefundVO.setTotalOnlyRefundAmount(incomeAndRefundVO.getTotalRefreshPayAmount());
        //应收金额
        incomeAndRefundVO.setPayAmount(convertBigDecimal2Str(currency, AmountChangeUtil.changeF2Y(orderEntity.signPayAmount().longValue())));
        //累计已付
        incomeAndRefundVO.setPaidAmount(convertBigDecimal2Str(currency, AmountChangeUtil.changeF2Y(orderEntity.getPaidAmount())));
        //实际已付
        int totalRealAmount = orderEntity.getRealAmount() + orderEntity.getProductionAmountPay();
        incomeAndRefundVO.setRealAmount(convertBigDecimal2Str(currency, AmountChangeUtil.changeF2Y(totalRealAmount)));
        incomeAndRefundVO.setNoPayAmount(convertBigDecimal2Str(currency, AmountChangeUtil.changeF2Y(orderEntity.leftPayAmount())));
        incomeAndRefundVO.setActualReceiveAmount(totalRealAmount);

        List<HeIncomeRecordEntity> incomeList = incomeRecordRepository.getAllRecordListByOrderId(req.getOrderId());
        incomeList = incomeList.stream().filter(o -> !OmniPayTypeEnum.REDUCTION.getCode().equals(o.getPayType())).collect(Collectors.toList());
        List<HeIncomeProofRecordEntity> incomeProofRecordByOrderId = incomeProofRecordRepository.getIncomeProofRecordByOrderId(req.getOrderId());

        List<HeIncomeRecordEntity> heIncomeRecordEntities = orderIncomeDomainService.filterEffectiveRecord(incomeList, incomeProofRecordByOrderId);
        int income = 0;
        List<Integer> approvingIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(heIncomeRecordEntities)) {
            income = heIncomeRecordEntities.stream().filter(o -> !o.isActualSuccess()).mapToInt(HeIncomeRecordEntity::getIncome).sum();
            approvingIdList = heIncomeRecordEntities.stream().filter(o -> !o.isActualSuccess()).map(HeIncomeRecordEntity::getId).collect(Collectors.toList());
        }
        incomeAndRefundVO.setIncome(convertBigDecimal2Str(currency, AmountChangeUtil.changeF2Y(income)));
        List<IncomeAndRefundVO.IncomeInfo> incomeInfoList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(incomeList)) {
            List<Integer> finalApprovingIdList = approvingIdList;
            List<Integer> optIdList = incomeList.stream().map(HeIncomeRecordEntity::getOptId).collect(Collectors.toList());
            List<UserEntity> userEntities = userRepository.queryUserByIdList(optIdList);
            Map<Integer, UserEntity> userEntityMap = userEntities.stream().collect(Collectors.toMap(UserEntity::getId, entity -> entity, (v1, v2) -> v1));
            for (HeIncomeRecordEntity incomeOrder : incomeList) {
                if (Objects.isNull(incomeOrder.getPayTime()) || incomeOrder.getPayTime() == 0) {
                    continue;
                }
                IncomeAndRefundVO.IncomeInfo incomeInfo = BeanMapper.map(incomeOrder, IncomeAndRefundVO.IncomeInfo.class);
                incomeInfo.setPayTypeStr(OmniPayTypeEnum.getName(incomeOrder.getPayType()));
                incomeInfo.setStatusStr(finalApprovingIdList.contains(incomeOrder.getId()) ? PayStatusEnum.PAY_STATUS_PROCESS.getValue() : PayStatusEnum.getValueByCode(incomeInfo.getStatus()));
                UserEntity user = userEntityMap.get(incomeOrder.getOptId());
                if (Objects.nonNull(user)) {
                    incomeInfo.setSubmitter(user.getName());
                }
                incomeInfo.setApproveStatusStr(OfflineAuditStatusV2Enum.getValueByCode(incomeOrder.getApproveStatus()));
                if (Objects.nonNull(incomeOrder.getPayTime()) && incomeOrder.getPayTime() == 0) {
                    incomeOrder.setPayTime(null);
                }
                incomeInfo.setPayTime(com.stbella.order.common.utils.DateUtils.secondsToTimeStr(incomeOrder.getPayTime()));
                incomeInfo.setIncome(convertBigDecimal2Str(currency, AmountChangeUtil.changeF2Y(incomeOrder.getIncome())));
                incomeInfoList.add(incomeInfo);
            }
        }
        incomeAndRefundVO.setIncomeInfoList(incomeInfoList);


        List<HeOrderRefundEntity> allRefundEntityList = orderRefundRepository.getRefundByOrderId(req.getOrderId());
        if (CollectionUtils.isEmpty(allRefundEntityList)) {
            return incomeAndRefundVO;
        }

        allRefundEntityList = allRefundEntityList.stream().filter(item -> !OmniPayTypeEnum.REDUCTION.getCode().equals(item.getRefundType())).collect(Collectors.toList());

        List<HeOrderRefundEntity> refundByOrderId = allRefundEntityList.stream().filter(item -> StringUtils.isEmpty(item.getParentRefundOrderSn())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(refundByOrderId)) {
            incomeAndRefundVO.setRefundInfoList(Lists.newArrayList());
            return incomeAndRefundVO;
        }

        List<HeOrderRefundEntity> finalAllRefundEntityList = allRefundEntityList;
        int totalRefreshPayAmount = refundByOrderId.stream().map(parent -> {
            List<HeOrderRefundEntity> child = finalAllRefundEntityList.stream().filter(item ->
                    OrderRefundNatureEnum.TEMP_REFUND.code().equals(item.getRefundNature())
                            && RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode().equals(item.getStatus()) && StringUtils.isNotEmpty(item.getParentRefundOrderSn()) && item.getParentRefundOrderSn().equals(parent.getRefundOrderSn())).collect(Collectors.toList());
            return child.stream().mapToInt(HeOrderRefundEntity::getApplyAmount).sum();
        }).reduce(Integer::sum).orElse(0);

        int totalOnlyRefundAmount = refundByOrderId.stream().map(parent -> {
            List<HeOrderRefundEntity> child = finalAllRefundEntityList.stream().filter(item -> OrderRefundNatureEnum.ONLY_REFUND.code().equals(item.getRefundNature()) && RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode().equals(item.getStatus()) && StringUtils.isNotEmpty(item.getParentRefundOrderSn()) && item.getParentRefundOrderSn().equals(parent.getRefundOrderSn())).collect(Collectors.toList());
            return child.stream().mapToInt(HeOrderRefundEntity::getApplyAmount).sum();
        }).reduce(Integer::sum).orElse(0);
        incomeAndRefundVO.setTotalRefreshPayAmount(convertBigDecimal2Str(currency, AmountChangeUtil.f2YScale2(totalRefreshPayAmount)));
        incomeAndRefundVO.setTotalOnlyRefundAmount(convertBigDecimal2Str(currency, AmountChangeUtil.f2YScale2(totalOnlyRefundAmount)));

        for (HeOrderRefundEntity heOrderRefundEntity : refundByOrderId) {
            List<HeOrderRefundEntity> child = finalAllRefundEntityList.stream().filter(item -> StringUtils.isNotEmpty(item.getParentRefundOrderSn()) && item.getParentRefundOrderSn().equals(heOrderRefundEntity.getRefundOrderSn())).collect(Collectors.toList());
            heOrderRefundEntity.setApplyAmount(child.stream().mapToInt(HeOrderRefundEntity::getApplyAmount).sum());
        }

        //退款成功
        Integer actualAmount = refundByOrderId.stream().filter(item -> Objects.equals(item.getStatus(), 4)).map(m -> {
            List<HeOrderRefundEntity> child = finalAllRefundEntityList.stream().filter(item ->
                    StringUtils.isNotEmpty(item.getParentRefundOrderSn())
                            && item.getParentRefundOrderSn().equals(m.getRefundOrderSn())
                            && !OmniPayTypeEnum.REDUCTION.getCode().equals(item.getRefundType())).collect(Collectors.toList());
            return child.stream().mapToInt(HeOrderRefundEntity::getApplyAmount).sum();
        }).reduce(Integer::sum).orElse(0);
        incomeAndRefundVO.setRefundAmount(convertBigDecimal2Str(currency, AmountChangeUtil.changeF2Y(actualAmount)));

        //退款中金额
        Integer applyAmount = refundByOrderId.stream().filter(item -> HeOrderRefundEntity.NOT_FINISH_STATUS.contains(item.getStatus())).map(HeOrderRefundEntity::getApplyAmount).reduce(Integer::sum).orElse(0);
        incomeAndRefundVO.setRefundProcessAmount(convertBigDecimal2Str(currency, AmountChangeUtil.changeF2Y(applyAmount)));

        //设置实际支付金额
        incomeAndRefundVO.setActualReceiveAmount(orderEntity.getPaidAmount() - actualAmount);

        List<IncomeAndRefundVO.RefundInfo> refundInfoList = Lists.newArrayList();
        refundByOrderId.forEach(refundOrder -> {
            IncomeAndRefundVO.RefundInfo refundInfo = new IncomeAndRefundVO.RefundInfo();
            refundInfo.setId(refundOrder.getId());
            refundInfo.setRefundOrderSn(refundOrder.getRefundOrderSn());
            BigDecimal subActualAmount = AmountChangeUtil.changeF2Y(refundOrder.getApplyAmount());
            refundInfo.setActualAmount(convertBigDecimal2Str(currency, subActualAmount));
            refundInfo.setRefundReasonType(refundOrder.getRefundReasonType());
            refundInfo.setRefundReasonTypeStr(refundOrder.getRefundReasonType() == 1 ? "正常退款" : "⾮正常退款（客诉）");
            refundInfo.setRefundReason(refundOrder.getRefundReason());
            refundInfo.setRefundReasonStr(RefundReasonEnum.getName(refundOrder.getRefundReason()));
            refundInfo.setRefundType(refundOrder.getRefundType());
            RefundTypeEnum typeEnum = RefundTypeEnum.getType(refundInfo.getRefundType());
            refundInfo.setRefundTypeStr(Objects.isNull(typeEnum) ? StringUtils.EMPTY : typeEnum.getValue());

            refundInfo.setRefundMethod(refundOrder.getRefundMethod());
            refundInfo.setRefundNature(refundOrder.getRefundNature());
            refundInfo.setRefundNatureStr(OrderRefundNatureEnum.getValueByCode(refundOrder.getRefundNature()));
            refundInfo.setRefundTypeStr(Objects.isNull(refundOrder.getRefundMethod()) ? StringUtils.EMPTY : RefundTypeEnum.getValueByCode(refundOrder.getRefundMethod()));
            refundInfo.setStatus(refundOrder.getStatus());
            refundInfo.setStatusStr(OmniRefundApproveEnum.getName(refundOrder.getStatus()));
            refundInfo.setApplyId(refundOrder.getApplyId());
            refundInfo.setApplyName(refundOrder.getApplyName());
            refundInfoList.add(refundInfo);
        });
        incomeAndRefundVO.setRefundInfoList(refundInfoList);
        return incomeAndRefundVO;
    }

    @Override
    public IncomeInfoVO incomeInfo(IncomeAndRefundRecordReq req) {

        Assert.isTrue(Objects.nonNull(req.getId()), "收入记录ID不能为空");
        if (!req.getIsNewOrder()) {
            OrderInfoQueryTemplate orderInfoQueryTemplate = getOrderInfoQueryTemplate(req.getOrderType());
            return orderInfoQueryTemplate.incomeInfo(req);
        }
        HeIncomeRecordEntity incomeRecord = incomeRecordRepository.getOneById(req.getId());
        if (Objects.isNull(incomeRecord)) {
            return null;
        }
        IncomeInfoVO incomeInfoVO = new IncomeInfoVO();
        incomeInfoVO.setId(incomeRecord.getId());
        incomeInfoVO.setIncomeSn(incomeRecord.getIncomeSn());
        incomeInfoVO.setIncome(convertBigDecimal2Str(incomeRecord.getCurrency(), AmountChangeUtil.changeF2Y(incomeRecord.getIncome())));
        incomeInfoVO.setPayType(incomeRecord.getPayType());
        incomeInfoVO.setPayTypeStr(OmniPayTypeEnum.getName(incomeRecord.getPayType()));
        incomeInfoVO.setStatus(incomeRecord.getStatus());
        incomeInfoVO.setStatusStr(req.getStatusStr());
        incomeInfoVO.setPayTime(DateUtil.formatDateTime(new Date(Long.valueOf(incomeRecord.getPayTime()) * 1000)));
        HeIncomeProofRecordEntity incomeProofRecordEntity = incomeProofRecordRepository.getLastOneByIncomeId(incomeRecord.getId());
        if (Objects.nonNull(incomeProofRecordEntity)) {
            incomeInfoVO.setPayProof(Collections.singletonList(incomeProofRecordEntity.getPayProof()));
            incomeInfoVO.setRemark(incomeProofRecordEntity.getRemark());
        }
        String remark = incomeRecord.getRemark();
        if (StringUtils.isNotBlank(remark)) {
            incomeInfoVO.setRemark(remark);
        }
        incomeInfoVO.setCurrency(incomeRecord.getCurrency());
        HeOrderEntity byOrderSn = orderRepository.queryOrderById(incomeRecord.getOrderId());
        if (Objects.nonNull(byOrderSn)) {
            incomeInfoVO.setStaffId(byOrderSn.getStaffId());
            UserEntity userEntity = userRepository.queryById(incomeInfoVO.getStaffId());
            if (Objects.nonNull(userEntity)) {
                incomeInfoVO.setStaffName(userEntity.getName());
            }
        }
        incomeInfoVO.setCreatedAt(DateUtil.formatDateTime(new Date(incomeRecord.getCreatedAt() * 1000)));
        return incomeInfoVO;
    }

    @Override
    public RefundInfoVO refundInfo(IncomeAndRefundRecordReq req) {

        if (!req.getIsNewOrder()) {
            Assert.isTrue(Objects.nonNull(req.getId()), "订单退款ID不能为空");
            Assert.isTrue(Objects.nonNull(req.getOrderType()), "订单类型不能为空");
            OrderInfoQueryTemplate orderInfoQueryTemplate = getOrderInfoQueryTemplate(req.getOrderType());
            return orderInfoQueryTemplate.refundInfo(req);
        }
        Assert.isTrue(Objects.nonNull(req.getRefundOrderSn()), "订单退款编码不能为空");
        HeOrderRefundEntity orderRefund = orderRefundRepository.getOneByRefundOrderSn(req.getRefundOrderSn());
        //子项退款
        List<HeOrderRefundEntity> refundByParentSn = orderRefundRepository.getRefundByParentSn(orderRefund.getRefundOrderSn());
        Assert.isTrue(Objects.nonNull(orderRefund), "当前订单暂无退款记录");
        //过滤掉减免的
        refundByParentSn = refundByParentSn.stream().filter(item -> !OmniPayTypeEnum.REDUCTION.getCode().equals(item.getRefundType())).collect(Collectors.toList());
        RefundInfoVO refundInfoVO = new RefundInfoVO();
        refundInfoVO.setId(orderRefund.getId());
        refundInfoVO.setRefundOrderSn(orderRefund.getRefundOrderSn());
        refundInfoVO.setActualAmount(convertBigDecimal2Str(orderRefund.getCurrency(), AmountChangeUtil.changeF2Y(refundByParentSn.stream().mapToInt(HeOrderRefundEntity::getApplyAmount).sum())));
        refundInfoVO.setRefundReason(orderRefund.getRefundReason());
        refundInfoVO.setRefundReasonStr(RefundReasonEnum.getName(orderRefund.getRefundReason()));
        refundInfoVO.setStatus(orderRefund.getStatus());
        refundInfoVO.setStatusStr(OmniRefundApproveEnum.getName(orderRefund.getStatus()));
        refundInfoVO.setRefundReasonType(orderRefund.getRefundReasonType());
        refundInfoVO.setRefundReasonTypeStr(orderRefund.getRefundReasonType() == 1 ? "正常退款" : "⾮正常退款（客诉）");
        refundInfoVO.setRefundType(orderRefund.getRefundMethod());
        refundInfoVO.setRefundTypeStr(RefundTypeEnum.getValueByCode(orderRefund.getRefundMethod()));
        refundInfoVO.setCreatedAt(DateUtil.formatDateTime(new Date(orderRefund.getCreatedAt() * 1000)));
        refundInfoVO.setRemark(orderRefund.getRemark());
        refundInfoVO.setApplyId(orderRefund.getApplyId());
        refundInfoVO.setApplyName(orderRefund.getApplyName());
        if (StringUtils.isNotEmpty(orderRefund.getRefundInfo())) {
            JSONObject refundInfo = JSONObject.parseObject(orderRefund.getRefundInfo());
            refundInfoVO.setName(refundInfo.getString("name"));
            refundInfoVO.setBankName(refundInfo.getString("bank_name"));
            refundInfoVO.setBankNumber(refundInfo.getString("bank_number"));
            String refundVoucher = refundInfo.getString("images");
            refundInfoVO.setRefundVoucherList(StringUtils.isEmpty(refundVoucher) ? null : JSONObject.parseArray(refundVoucher, String.class));
        }
        Assert.isTrue(Objects.nonNull(orderRefund.getOrderGoodId()), "订单涉及商品Id不存在");

        List<RefundInfoVO.RefundRecord> refundRecordList = Lists.newArrayList();
        List<HeOrderGoodsEntity> allItermByOrderId = orderGoodsRepository.getAllItermByOrderId(orderRefund.getOrderId());
        Map<String, Integer> goodsNumMap = CollectionUtils.isEmpty(allItermByOrderId) ? Maps.newHashMap() : allItermByOrderId.stream().collect(Collectors.toMap(HeOrderGoodsEntity::getOrderGoodsSn, HeOrderGoodsEntity::getGoodsNum, (v1, v2) -> v1));
        List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList = orderRefundGoodsRepository.queryByOrderId(orderRefund.getOrderId());
        List<IncomePaidAllocationEntity> incomePaidAllocationEntities = incomePaidAllocationRepository.queryListByOrderId(orderRefund.getOrderId().longValue());
        List<HeOrderRefundGoodsEntity> tempRefundGoodsList = orderRefundGoodsEntityList.stream().filter(orderGoods -> OrderRefundNatureEnum.TEMP_REFUND.getCode().equals(orderGoods.getRefundNature()) && OrderRefundGoodsStatusEnum.SUCCESS.code().equals(orderGoods.getStatus())).collect(Collectors.toList());
        Map<String, Integer> paidSumMap = new HashMap<>();
        tempRefundGoodsList.forEach(tempRefundGoods -> {
            Integer paidSum = paidSumMap.get(tempRefundGoods.getOrderGoodsSn());
            if (Objects.isNull(paidSum)) {
                List<IncomePaidAllocationEntity> paidList = incomePaidAllocationEntities.stream().filter(i -> i.getOrderGoodsSn().equals(tempRefundGoods.getOrderGoodsSn()) && !(OmniPayTypeEnum.REDUCTION.getCode() + "").equals(i.getPaymentMethod())).collect(Collectors.toList());
                paidSum = paidList.stream().mapToInt(IncomePaidAllocationEntity::getPaidAmount).sum();
            }
            paidSumMap.put(tempRefundGoods.getOrderGoodsSn(), paidSum - tempRefundGoods.getRefundAmount());
        });
        List<HeOrderRefundGoodsEntity> heOrderRefundGoodsEntities = orderRefundGoodsEntityList.stream().filter(item -> req.getRefundOrderSn().equals(item.getRefundOrderSn()) && !OmniPayTypeEnum.REDUCTION.getCode().equals(item.getPayType())).collect(Collectors.toList());
        heOrderRefundGoodsEntities.forEach(orderGoods -> {
            RefundInfoVO.RefundRecord refundRecord = BeanMapper.map(orderGoods, RefundInfoVO.RefundRecord.class);
            refundRecord.setRefundAmount(convertBigDecimal2Str(orderRefund.getCurrency(), AmountChangeUtil.changeF2Y(orderGoods.getRefundAmount())));
            refundRecord.setGoodsPriceOrgin(convertBigDecimal2Str(orderRefund.getCurrency(), AmountChangeUtil.changeF2Y(orderGoods.getGoodsPriceOrgin())));
            //这个商品的实际支付金额（从均摊里取）
            Integer paidSum = paidSumMap.get(orderGoods.getOrderGoodsSn());
            if (Objects.isNull(paidSum)) {
                List<IncomePaidAllocationEntity> paidList = incomePaidAllocationEntities.stream().filter(i -> i.getOrderGoodsSn().equals(orderGoods.getOrderGoodsSn()) && !(OmniPayTypeEnum.REDUCTION.getCode() + "").equals(i.getPaymentMethod())).collect(Collectors.toList());
                paidSum = paidList.stream().mapToInt(IncomePaidAllocationEntity::getPaidAmount).sum();
            }
            refundRecord.setGoodsPricePay(convertBigDecimal2Str(orderRefund.getCurrency(), AmountChangeUtil.changeF2Y(paidSum)));
            refundRecord.setGiftStr(orderGoods.getGift() == 0 ? "购买" : "礼赠");
            Integer goodsNum = goodsNumMap.get(orderGoods.getOrderGoodsSn());
            refundRecord.setGoodsNum(Objects.isNull(goodsNum) ? null : goodsNum);
            refundRecordList.add(refundRecord);
        });
        refundInfoVO.setRefundRecordList(refundRecordList);


        List<HeIncomeRecordEntity> allRecordListByOrderId = incomeRecordRepository.getAllRecordListByOrderId(orderRefund.getOrderId());
        Map<String, Integer> incomeSnMap = allRecordListByOrderId.stream().collect(Collectors.toMap(HeIncomeRecordEntity::getIncomeSn, HeIncomeRecordEntity::getId, (v1, v2) -> v2));
        List<RefundInfoVO.RefundProcess> refundProcessList = Lists.newArrayList();
        refundByParentSn.forEach(item -> {
            RefundInfoVO.RefundProcess refundProcess = new RefundInfoVO.RefundProcess();
            refundProcess.setId(item.getId());
            refundProcess.setRefundOrderSn(item.getRefundOrderSn());
            refundProcess.setActualAmount(convertBigDecimal2Str(orderRefund.getCurrency(), AmountChangeUtil.f2YDown((Objects.isNull(item.getActualAmount()) || item.getActualAmount() <= 0) ? item.getApplyAmount() : item.getActualAmount())));
            refundProcess.setStatus(item.getStatus());
            Integer refundType = item.getRefundType();
            OmniPayTypeEnum omniPayTypeEnum = OmniPayTypeEnum.getByCode(refundType);
            switch (omniPayTypeEnum) {
                case ALIPAY:
                case WECHAT:
                case ONLINE_POS:
                case BALANCE:
                case PRODUCTION_COIN:
                    //线上统一用退款中
                    refundProcess.setStatusStr(OmniRefundApproveEnum.getName(item.getStatus()));
                    break;
                case OFFLINE:
                    //线下显示等待财务打款
                    refundProcess.setStatusStr(OmniRefundApproveEnum.getOfflineName(item.getStatus()));
                    break;
            }
            refundProcess.setIncomeId(incomeSnMap.get(item.getIncomeSn()));
            refundProcess.setIncomeSn(item.getIncomeSn());
            refundProcess.setFinishAt(DateUtil.formatDateTime(new Date(item.getFinishAt() * 1000)));
            refundProcess.setCreatedAt(DateUtil.formatDateTime(new Date(item.getCreatedAt() * 1000)));
            refundProcessList.add(refundProcess);
        });
        refundInfoVO.setRefundProcessList(refundProcessList);
        return refundInfoVO;
    }


    @Override
    public Boolean updateSignType(OrderSignTypeReq req) {
        HeOrderEntity orderEntity = orderRepository.queryOrderById(req.getOrderId());
        Assert.isTrue(Objects.nonNull(orderEntity), "订单不存在");
        String signTypeStr = OrderSignTypeEnum.fromCode(req.getSignType());
        Assert.isTrue(StringUtils.isNotEmpty(signTypeStr), "签署状态不存在");
        orderEntity.setSignType(req.getSignType());
        orderEntity.setUpdatedAt(System.currentTimeMillis() / 1000);
        Integer i = orderRepository.updateOne(orderEntity);
        return i > 0;
    }

    @Override
    public Integer querySignType(OrderSignTypeReq req) {
        HeOrderEntity orderEntity = orderRepository.queryOrderById(req.getOrderId());
        Assert.isTrue(Objects.nonNull(orderEntity), "订单不存在");
        return orderEntity.getSignType();
    }

    @Override
    public OrderInfoNewV3VO queryOrderInfo(OrderInfoNewV3Query req) {

        Integer orderId = req.getOrderId();
        HeOrderEntity orderEntity = orderRepository.queryOrderById(orderId);
        Assert.isTrue(Objects.nonNull(orderEntity), "订单不存在");
        OrderInfoNewV3VO orderInfoNewV3VO = orderConvert.convertOrderEntity2NewV3VO(orderEntity);
        UserEntity userEntity = userRepository.queryById(orderInfoNewV3VO.getStaffId());
        if (Objects.nonNull(userEntity)) {
            orderInfoNewV3VO.setStaffPhone(userEntity.getPhone());
        }
        //设置一些金额
        orderInfoNewV3VO.setRemainingAmount(AmountChangeUtil.changeF2Y(orderEntity.leftPayAmount()));
        orderInfoNewV3VO.setReviewAmount(AmountChangeUtil.changeF2Y(orderEntity.fetchUnConfirmAmount()));

        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(orderId);
        orderInfoNewV3VO.setCustomerName(heOrderUserSnapshotEntity.getName());
        orderInfoNewV3VO.setCustomerPhone(heOrderUserSnapshotEntity.getPhone());

        CfgStoreEntity store = storeRepository.queryCfgStoreById(orderEntity.getStoreId());
        if (Objects.nonNull(store)) {
            orderInfoNewV3VO.setStoreType(store.getType());
        }


        List<HeOrderRefundEntity> refundList = orderRefundRepository.getRefundByOrderId(orderId);
        if (CollectionUtils.isNotEmpty(refundList)) {
            int refundAmount = refundList.stream()
                    .filter(item -> Objects.nonNull(item.getParentRefundOrderSn()) && RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode().equals(item.getStatus()))
                    .mapToInt(HeOrderRefundEntity::getActualAmount).sum();
            orderInfoNewV3VO.setRefundAmount(AmountChangeUtil.changeF2Y(refundAmount));

            //统计退款中的总额
            BigDecimal refundingAmount = refundList.stream()
                    .filter(r -> HeOrderRefundEntity.NOT_FINISH_STATUS.contains(r.getStatus()) && StringUtil.isNotBlank(r.getParentRefundOrderSn())).map(r -> BigDecimal.valueOf(r.getApplyAmount())).reduce(BigDecimal.ZERO, BigDecimal::add);
            orderInfoNewV3VO.setRefundingAmount(AmountChangeUtil.changeF2Y(refundingAmount));
        }

        List<HeOrderGoodsEntity> goodsEntities = orderGoodsRepository.getByOrderIdList(Arrays.asList(orderId.intValue()));
        List<OrderGoodsInfoVO> goodsInfoVOS = orderGoodsConverter.entity2DTO(goodsEntities);

        for (OrderGoodsInfoVO goodsInfoVO : goodsInfoVOS) {

            SettingRoomTypeEntity settingRoomTypeEntity = settingRoomTypeRepository.queryById(goodsInfoVO.getEcpRoomType());
            if (Objects.nonNull(settingRoomTypeEntity)) {
                goodsInfoVO.setRoomTypeName(settingRoomTypeEntity.getRoomTypeName());
            }
            Long backCategoryId = goodsInfoVO.getBackCategoryId();
            if (Objects.nonNull(backCategoryId)) {
                String allBackName = storeManager.getAllBackName(backCategoryId.intValue());
                goodsInfoVO.setCategoryName(allBackName);
            }
            goodsInfoVO.setPayAmount(AmountChangeUtil.changeF2Y(goodsInfoVO.getPayAmount()));
            goodsInfoVO.setPayTotalAmount(AmountChangeUtil.changeF2Y(goodsInfoVO.getPayTotalAmount()));
            goodsInfoVO.setGoodsPriceOrgin(AmountChangeUtil.changeF2Y(goodsInfoVO.getGoodsPriceOrgin()));

            //获取护理模式
            String definedProperty = goodsInfoVO.getDefinedProperty();
            if (ObjectUtil.isNotEmpty(definedProperty)) {
                List<GoodsPropertyVO> voList = JSONUtil.toList(definedProperty, GoodsPropertyVO.class);
                if (ObjectUtil.isNotEmpty(voList)) {
                    List<GoodsPropertyVO> serviceType = voList.stream().filter(x -> ObjectUtil.isNotEmpty(x.getPropertyCode()) && x.getPropertyCode().equals("serviceType")).collect(Collectors.toList());
                    if (ObjectUtil.isNotEmpty(serviceType)) {
                        GoodsPropertyVO goodsPropertyVO = serviceType.get(0);
                        List<GoodsPropertyVO.GoodsPropertyDetailsVO> goodsPropertyDetailsVOS = goodsPropertyVO.getGoodsPropertyDetailsVOS();
                        if (ObjectUtil.isNotEmpty(goodsPropertyDetailsVOS)) {
                            GoodsPropertyVO.GoodsPropertyDetailsVO goodsPropertyDetailsVO = goodsPropertyDetailsVOS.get(0);
                            if (ObjectUtil.isNotEmpty(goodsPropertyDetailsVO.getValue())) {
                                goodsInfoVO.setCareServiceType(Integer.valueOf(goodsPropertyDetailsVO.getValue()));
                                goodsInfoVO.setCareServiceName(goodsPropertyDetailsVO.getLabel());
                            }
                        }


                    }
                }
            }
        }
        orderInfoNewV3VO.setOrderGoodsInfoVOS(goodsInfoVOS);
        List<ScrmOrderReductionRecordVO> scrmOrderReductionRecordVOS = new ArrayList<>();
        orderInfoNewV3VO.setScrmOrderReductionRecordVOS(scrmOrderReductionRecordVOS);
        Result<List<OrderReductionVO>> successListByOrderId = orderQueryService.getSuccessListByOrderId(orderId);
        List<OrderReductionVO> data = successListByOrderId.getData();
        if (CollectionUtils.isNotEmpty(data)) {
            for (OrderReductionVO datum : data) {
                ScrmOrderReductionRecordVO recordDTO = orderDecreaseConverter.vo2DTO(datum);
                scrmOrderReductionRecordVOS.add(recordDTO);
                recordDTO.setChangeType("收款减免");

                UserEntity userEntity2 = userRepository.queryById(Integer.valueOf(datum.getOperatorId()));
                if (Objects.nonNull(userEntity2)) {
                    recordDTO.setStaffPhone(userEntity2.getPhone());
                }
                recordDTO.setSuccessDate(datum.getGmtModified());
                scrmOrderReductionRecordVOS.add(recordDTO);
            }
        }

        return orderInfoNewV3VO;
    }


    private String getTypeName(Integer type, Integer childType) {

        if (StoreTypeEnum.SAINT_BELLA.getCode().equals(type)) {
            if (StoreChildTypeEnum.BELLA_VILLA.getCode().equals(childType)) {
                return StoreChildTypeEnum.BELLA_VILLA.getValue();
            }
            return StoreTypeEnum.SAINT_BELLA.getValue();
        }
        if (StoreTypeEnum.BABY_BELLA.getCode().equals(type)) {
            if (StoreChildTypeEnum.BABY_BELLA_DELUXE.getCode().equals(childType)) {
                return StoreChildTypeEnum.BABY_BELLA_DELUXE.getValue();
            }
            return StoreTypeEnum.BABY_BELLA.getValue();
        }
        return StoreTypeEnum.getValueByCode(type);
    }


    private WechatMyOrderNewVO buildWechatMyOrderNewVO(HeOrderEntity order, Map<Integer, List<HeOrderGoodsEntity>> orderGoodsByOrderIdMap, Map<Integer, TabClientEntity> clientNameByIdMap, Map<Integer, CfgStoreEntity> storeInfoMap, Map<Integer, String> refundMap) {
        //节假日多胞胎商品
        List<Integer> goodsTypeList = Arrays.asList(GoodsTypeEnum.HOLIDAY_SERVICE.code(), GoodsTypeEnum.MULTIPLE_BIRTHS.code());
        List<HeIncomeRecordEntity> heIncomeRecordEntities = orderIncomeDomainService.queryEffectiveRecord(order);
        int sum = heIncomeRecordEntities.stream().filter(o -> !o.isActualSuccess()).mapToInt(HeIncomeRecordEntity::getIncome).sum();

        WechatMyOrderNewVO result = new WechatMyOrderNewVO();
        result.setRefundStatus(order.getRefundStatus());
        result.setVersion(order.getVersion());
        result.setOrderSn(order.getOrderSn());
        result.setCreatedAt(DateUtils.formatNormal(order.getCreatedAt()));

        result.setRefundStatusStr(OrderRefundStatusEnum.getName(result.getRefundStatus()));
        result.setBasicUid(order.getBasicUid());
        result.setOrderType(order.getOrderType());
        result.setClientUid(order.getClientUid());
        result.setExtraInfo(order.getExtraInfo());
        result.setScene(order.getScene());
        TabClientEntity tabClientEntity = clientNameByIdMap.get(result.getClientUid());
        if (Objects.nonNull(tabClientEntity)) {

            result.setCustomerName(tabClientEntity.getName());
            result.setCustomerPhone(SensitiveInformationUtil.conductPhoneOrIdCardNo(tabClientEntity.getPhone()));
        }
        result.setOrderStatusName(order.fetchOrderStateLabel());
        result.setRefundStr(Objects.isNull(refundMap.get(order.getOrderId())) ? "" : "有退款");
        result.setStoreId(order.getStoreId());
        CfgStoreEntity storeEntity = storeInfoMap.get(result.getStoreId());
        if (Objects.nonNull(storeEntity)) {
            result.setStoreName(storeEntity.getStoreName());
            result.setStoreType(storeEntity.getType());
        }
        result.setOrderId(order.getOrderId());
        result.setCurrency(order.getCurrency());
        result.setPayStatus(order.getPayStatus());
        result.setPayStatusName(OmniPayStatusEnum.getName(order.getPayStatus()));
        if (OrderStatusV2Enum.CLOSE.getCode().intValue() == order.getOrderStatus()) {
            result.setPayStatusName(OmniPayStatusEnum.CLOSED.getValue());
        }

        result.setOrderAmount(AmountChangeUtil.f2YDown(order.getOrderAmount()));
        result.setPayAmount(AmountChangeUtil.f2YDown(order.getPayAmount()));
        result.setCalPayable(AmountChangeUtil.f2YDown(order.signPayAmount()));
        BigDecimal realAmount = AmountChangeUtil.f2YDown(order.getRealAmount() + order.getProductionAmountPay() + sum);
        if (order.isNewOrder()) {
            result.setRealAmount(realAmount);
        } else {
            result.setRealAmount(AmountChangeUtil.f2YDown(order.getPaidAmount()));
        }
        result.setNoPayAmount(AmountChangeUtil.f2YDown(Math.max(order.leftPayAmount(), 0)));
        List<HeOrderGoodsEntity> heOrderGoodsList = orderGoodsByOrderIdMap.get(order.getOrderId());
        if (CollectionUtils.isEmpty(heOrderGoodsList)) {
            return result;
        }
        Map<Integer, String> goodsUnitMap = getGoodsUnit(heOrderGoodsList.stream().map(HeOrderGoodsEntity::getGoodsId).collect(Collectors.toList()));
        List<HeOrderGoodsEntity> orderGoodsParentList = Lists.newArrayList();
        List<HeOrderGoodsEntity> orderGoodsChildList = Lists.newArrayList();
        heOrderGoodsList.forEach(orderGoods -> {
            if (StringUtils.isEmpty(orderGoods.getParentCombineSn())) {
                orderGoodsParentList.add(orderGoods);
            } else {
                orderGoodsChildList.add(orderGoods);
            }
        });
        Map<String, List<HeOrderGoodsEntity>> goodsOrderMap = orderGoodsChildList.stream().collect(Collectors.groupingBy(HeOrderGoodsEntity::getParentCombineSn));
        result.setOrderGoodsList(buildOrderInfoVO(orderGoodsParentList, goodsOrderMap, goodsTypeList, goodsUnitMap));
        return result;
    }

    private WechatMyOrderNewVO buildWechatMyOrderNewVOV2(HeOrderEntity order, Map<Integer, List<HeOrderGoodsEntity>> orderGoodsByOrderIdMap, Map<Integer, TabClientEntity> clientNameByIdMap, Map<Integer, CfgStoreEntity> storeInfoMap, Map<Integer, String> refundMap, Map<Integer, List<OrderProductionExtendEntity>> orderProductionExtendMap) {
        log.info("是否是老产康{},{}", order.getOrderSn(), !order.isNewOrder() && order.getOrderType().equals(OmniOrderTypeEnum.PRODUCTION_ORDER.code()));
        //节假日多胞胎商品
        List<Integer> goodsTypeList = Arrays.asList(GoodsTypeEnum.HOLIDAY_SERVICE.code(), GoodsTypeEnum.MULTIPLE_BIRTHS.code());
        List<HeIncomeRecordEntity> heIncomeRecordEntities = orderIncomeDomainService.queryEffectiveRecord(order);
        int sum = heIncomeRecordEntities.stream().filter(o -> !o.isActualSuccess()).mapToInt(HeIncomeRecordEntity::getIncome).sum();

        WechatMyOrderNewVO result = new WechatMyOrderNewVO();
        result.setRemark(order.getRemark());
        result.setRefundStatus(order.getRefundStatus());
        result.setVersion(order.getVersion());
        result.setOrderSn(order.getOrderSn());
        result.setCreatedAt(DateUtils.formatNormal(order.getCreatedAt()));
        if (Objects.nonNull(order.getPayFirstTime()) && order.getPayFirstTime() > 0) {
            result.setPerformanceEffectiveDateStr(DateUtils.formatNormal(order.getPercentFirstTime().longValue()));
        }

        result.setRefundStatusStr(OrderRefundStatusEnum.getName(result.getRefundStatus()));
        result.setBasicUid(order.getBasicUid());
        result.setOrderType(order.getOrderType());
        result.setClientUid(order.getClientUid());
        result.setExtraInfo(order.getExtraInfo());
        result.setScene(order.getScene());
        TabClientEntity tabClientEntity = clientNameByIdMap.get(result.getClientUid());
        if (Objects.nonNull(tabClientEntity)) {

            result.setCustomerName(tabClientEntity.getName());
            result.setCustomerPhone(SensitiveInformationUtil.conductPhoneOrIdCardNo(tabClientEntity.getPhone()));
        }
        result.setOrderStatusName(order.fetchOrderStateLabel());
        result.setRefundStr(Objects.isNull(refundMap.get(order.getOrderId())) ? "" : "有退款");
        result.setStoreId(order.getStoreId());
        CfgStoreEntity storeEntity = storeInfoMap.get(result.getStoreId());
        if (Objects.nonNull(storeEntity)) {
            result.setStoreName(storeEntity.getStoreName());
            result.setStoreType(storeEntity.getType());
        }
        result.setOrderId(order.getOrderId());
        result.setCurrency(order.getCurrency());
        result.setPayStatus(order.getPayStatus());
        result.setPayStatusName(OmniPayStatusEnum.getName(order.getPayStatus()));
        if (OrderStatusV2Enum.CLOSE.getCode().intValue() == order.getOrderStatus()) {
            result.setPayStatusName(OmniPayStatusEnum.CLOSED.getValue());
        }

        result.setOrderAmount(AmountChangeUtil.f2YDown(order.getOrderAmount()));
        result.setPayAmount(AmountChangeUtil.f2YDown(order.getPayAmount()));
        result.setCalPayable(AmountChangeUtil.f2YDown(order.calPayable()));
        BigDecimal realAmount = AmountChangeUtil.f2YDown(order.getRealAmount() + order.getProductionAmountPay() + sum);
        if (order.isNewOrder()) {
            result.setRealAmount(realAmount);
        } else {
            result.setRealAmount(AmountChangeUtil.f2YDown(order.getPaidAmount()));
        }
        result.setNoPayAmount(AmountChangeUtil.f2YDown(Math.max(order.leftPayAmount(), 0)));

        if (!order.isNewOrder() && order.getOrderType().equals(OmniOrderTypeEnum.PRODUCTION_ORDER.code())) {
            log.info("老产康订单设置商品");
            result.setOrderGoodsList(buildOldProductOrderInfoVO(orderProductionExtendMap.get(order.getOrderId())));
            return result;
        }

        List<HeOrderGoodsEntity> heOrderGoodsList = orderGoodsByOrderIdMap.get(order.getOrderId());
        if (CollectionUtils.isEmpty(heOrderGoodsList)) {
            return result;
        }
        Map<Integer, String> goodsUnitMap = getGoodsUnit(heOrderGoodsList.stream().map(HeOrderGoodsEntity::getGoodsId).collect(Collectors.toList()));
        List<HeOrderGoodsEntity> orderGoodsParentList = Lists.newArrayList();
        List<HeOrderGoodsEntity> orderGoodsChildList = Lists.newArrayList();
        heOrderGoodsList.forEach(orderGoods -> {
            if (StringUtils.isEmpty(orderGoods.getParentCombineSn())) {
                orderGoodsParentList.add(orderGoods);
            } else {
                orderGoodsChildList.add(orderGoods);
            }
        });
        Map<String, List<HeOrderGoodsEntity>> goodsOrderMap = orderGoodsChildList.stream().collect(Collectors.groupingBy(HeOrderGoodsEntity::getParentCombineSn));

        result.setOrderGoodsList(buildOrderInfoVO(orderGoodsParentList, goodsOrderMap, goodsTypeList, goodsUnitMap));
        return result;
    }


    private List<OrderGoodsInfoVO> buildOrderInfoVO(List<HeOrderGoodsEntity> orderGoodsParentList, Map<String, List<HeOrderGoodsEntity>> goodsOrderMap, List<Integer> goodsTypeList, Map<Integer, String> goodsUnitMap) {

        List<OrderGoodsInfoVO> orderGoodsListResult = Lists.newArrayList();
        orderGoodsParentList.forEach(orderGoodsParent -> {
            OrderGoodsInfoVO orderGoodsInfoVO = new OrderGoodsInfoVO();
            orderGoodsInfoVO.setPromotionInfo(orderGoodsParent.getPromotionInfo());
            orderGoodsInfoVO.setId(orderGoodsParent.getId());
            orderGoodsInfoVO.setGoodsId(orderGoodsParent.getGoodsId());
            orderGoodsInfoVO.setGoodsImage(orderGoodsParent.getGoodsImage());
            orderGoodsInfoVO.setGoodsName(orderGoodsParent.getGoodsName());
            orderGoodsInfoVO.setGoodsType(orderGoodsParent.getGoodsType());
            orderGoodsInfoVO.setGoodsNum(orderGoodsParent.getGoodsNum());
            orderGoodsInfoVO.setGoodsPriceOrgin(AmountChangeUtil.f2YScale2(orderGoodsParent.getGoodsPriceOrgin()));
            orderGoodsInfoVO.setGoodsTotalPriceOrgin(AmountChangeUtil.f2YScale2(orderGoodsParent.getGoodsPriceOrgin() * orderGoodsParent.getGoodsNum()));
            BigDecimal payTotalAmount = AmountChangeUtil.f2YScale2(orderGoodsParent.getPayAmount());
            BigDecimal payAmount = Objects.isNull(orderGoodsParent.getGoodsNum()) || orderGoodsParent.getGoodsNum() == 0 ? BigDecimal.ZERO : Objects.isNull(payTotalAmount) ? BigDecimal.ZERO : payTotalAmount.divide(new BigDecimal(orderGoodsParent.getGoodsNum()), 2, RoundingMode.UP);
            orderGoodsInfoVO.setPayAmount(payAmount);
            orderGoodsInfoVO.setPayTotalAmount(payTotalAmount);
            Map<Integer, OrderGoodsVO> orderGoodsChildMap = Maps.newHashMap();
            List<String> orderGoodsSubList = Lists.newArrayList();
            fillOrderGoodsInfoVO(goodsOrderMap, goodsTypeList, orderGoodsParent.getOrderGoodsSn(), orderGoodsChildMap, orderGoodsSubList, goodsUnitMap);
            fillOrderGoodsChildPayAmount(orderGoodsChildMap);
            orderGoodsInfoVO.setOrderGoodsSubList(orderGoodsSubList);
            orderGoodsInfoVO.setOrderGoodsChildList(new ArrayList<>(orderGoodsChildMap.values()));
            Integer gift = orderGoodsParent.getGift();
            if (Objects.nonNull(gift)) {
                orderGoodsInfoVO.setGift(gift);
                orderGoodsInfoVO.setGiftStr(gift == 0 ? "购买" : "礼赠");
            } else {
                orderGoodsInfoVO.setGift(0);
                orderGoodsInfoVO.setGiftStr("购买");
            }
            orderGoodsInfoVO.setMonthAgeGoods(Objects.nonNull(orderGoodsParent.getGoodsType()) && 28 == orderGoodsParent.getGoodsType() ? Boolean.TRUE : Boolean.FALSE);
            orderGoodsListResult.add(orderGoodsInfoVO);
        });
        return orderGoodsListResult;
    }


    private List<OrderGoodsInfoVO> buildOldProductOrderInfoVO(List<OrderProductionExtendEntity> goodsList) {
        List<OrderGoodsInfoVO> orderGoodsListResult = Lists.newArrayList();
        try {
            log.info("老产康设置商品：{}", JsonUtil.write(goodsList));
            if (CollectionUtils.isNotEmpty(goodsList)) {
                Map<Integer, List<OrderProductionExtendEntity>> collect = goodsList.stream().collect(Collectors.groupingBy(OrderProductionExtendEntity::getSkuId));
                collect.keySet().forEach(c -> {
                    List<OrderProductionExtendEntity> orderProductionExtendEntities = collect.get(c);
                    OrderProductionExtendEntity orderProductionExtendEntity = orderProductionExtendEntities.get(0);
                    OrderGoodsInfoVO orderGoodsInfoVO = new OrderGoodsInfoVO();
                    orderGoodsInfoVO.setId(orderProductionExtendEntity.getId());
                    orderGoodsInfoVO.setGoodsId(orderProductionExtendEntity.getGoodsId());
                    orderGoodsInfoVO.setGoodsName(orderProductionExtendEntity.getGoodsName());
                    orderGoodsInfoVO.setGoodsNum(orderProductionExtendEntities.size());
                    orderGoodsInfoVO.setGoodsPriceOrgin(AmountChangeUtil.f2YScale2(orderProductionExtendEntity.getPrice()));
                    orderGoodsInfoVO.setGoodsTotalPriceOrgin(AmountChangeUtil.f2YScale2(orderProductionExtendEntity.getPrice() * orderProductionExtendEntity.getGoodsNum()));
                    orderGoodsInfoVO.setPayAmount(AmountChangeUtil.f2YScale2(orderProductionExtendEntity.getPrice()));
                    orderGoodsInfoVO.setPayTotalAmount(AmountChangeUtil.f2YScale2(orderProductionExtendEntity.getPrice()));
                    orderGoodsListResult.add(orderGoodsInfoVO);
                    log.info("老产康设置商品结果：{}", JsonUtil.write(orderGoodsListResult));
                });
            }
        } catch (Exception e) {
            log.info("老产康设置商品异常：{}", e.getMessage());
        }
        return orderGoodsListResult;
    }


    private void fillOrderGoodsChildPayAmount(Map<Integer, OrderGoodsVO> orderGoodsChildMap) {

        if (orderGoodsChildMap.isEmpty()) {
            return;
        }
        for (Map.Entry<Integer, OrderGoodsVO> entry : orderGoodsChildMap.entrySet()) {
            OrderGoodsVO value = entry.getValue();
            value.setPayAmount(Objects.isNull(value.getGoodsNum()) || Objects.isNull(value.getPayTotalAmount()) || value.getGoodsNum() == 0 || value.getPayTotalAmount().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : value.getPayTotalAmount().divide(new BigDecimal(value.getGoodsNum()), 2, RoundingMode.UP));
            value.setGoodsPriceOrgin(Objects.isNull(value.getGoodsNum()) || Objects.isNull(value.getGoodsPriceOrginTotal()) || value.getGoodsNum() == 0 || value.getGoodsPriceOrginTotal().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : value.getGoodsPriceOrginTotal().divide(new BigDecimal(value.getGoodsNum()), 2, RoundingMode.UP));
        }
    }

    private void fillOrderGoodsInfoVO(Map<String, List<HeOrderGoodsEntity>> goodsOrderMap, List<Integer> goodsTypeList, String orderGoodsSn, Map<Integer, OrderGoodsVO> orderGoodsChildMap, List<String> orderGoodsSubList, Map<Integer, String> goodsUnitMap) {

        List<HeOrderGoodsEntity> heOrderGoodsEntityList = goodsOrderMap.get(orderGoodsSn);
        if (CollectionUtils.isEmpty(heOrderGoodsEntityList)) {
            return;
        }
        heOrderGoodsEntityList.forEach(orderGoodsChild -> {
            if (goodsTypeList.contains(orderGoodsChild.getGoodsType())) {
                OrderGoodsVO orderGoodsChildVO = orderGoodsChildMap.get(orderGoodsChild.getGoodsType());
                if (Objects.nonNull(orderGoodsChildVO)) {
                    orderGoodsChildVO.setGoodsNum(orderGoodsChildVO.getGoodsNum() + orderGoodsChild.getGoodsNum());
                    orderGoodsChildVO.setGoodsPriceOrginTotal(orderGoodsChildVO.getGoodsPriceOrginTotal().add(AmountChangeUtil.f2YDown(orderGoodsChild.getGoodsPriceOrgin() * orderGoodsChild.getGoodsNum())));
                    orderGoodsChildVO.setPayTotalAmount(orderGoodsChildVO.getPayTotalAmount().add(AmountChangeUtil.f2YDown(orderGoodsChild.getPayAmount())));
                } else {
                    OrderGoodsVO orderGoodsVO = new OrderGoodsVO();
                    orderGoodsVO.setCode(Objects.nonNull(orderGoodsChild.getGoodsType()) && orderGoodsChild.getGoodsType() == 23 ? "holidayServiceCost" : "multipleBirths");
                    orderGoodsVO.setGoodsImage(orderGoodsChild.getGoodsImage());
                    orderGoodsVO.setGoodsName(orderGoodsChild.getGoodsName());
                    orderGoodsVO.setGoodsType(orderGoodsChild.getGoodsType());
                    orderGoodsVO.setGoodsNum(orderGoodsChild.getGoodsNum());
                    orderGoodsVO.setGoodsPriceOrginTotal(AmountChangeUtil.f2YDown(orderGoodsChild.getGoodsPriceOrgin() * orderGoodsChild.getGoodsNum()));
                    orderGoodsVO.setPayTotalAmount(AmountChangeUtil.f2YDown(orderGoodsChild.getPayAmount()));
                    orderGoodsChildMap.put(orderGoodsChild.getGoodsType(), orderGoodsVO);
                }
            }
            List<Integer> combineTypeList = Arrays.asList(CombineTypeEnum.COMBINE.code(), CombineTypeEnum.COMBINE_SUB.code());
            if (Objects.nonNull(orderGoodsChild.getType()) && combineTypeList.contains(orderGoodsChild.getType()) && StringUtils.isNotEmpty(orderGoodsChild.getParentCombineSn())) {
                String orderGoodsSubName = orderGoodsChild.getGoodsName().concat("*").concat(String.valueOf(orderGoodsChild.getGoodsNum()));
                String goodsUnit = goodsUnitMap.get(orderGoodsChild.getGoodsId());
                if (StringUtils.isNotEmpty(goodsUnit)) {
                    orderGoodsSubName = orderGoodsSubName.concat(goodsUnit);
                }
                orderGoodsSubList.add(orderGoodsSubName);
            }
            List<HeOrderGoodsEntity> heOrderGoodsEntities = goodsOrderMap.get(orderGoodsChild.getOrderGoodsSn());
            if (CollectionUtils.isNotEmpty(heOrderGoodsEntities)) {
                fillOrderGoodsInfoVO(goodsOrderMap, goodsTypeList, orderGoodsChild.getOrderGoodsSn(), orderGoodsChildMap, orderGoodsSubList, goodsUnitMap);
            }
        });
    }

    public Map<Integer, String> getGoodsUnit(List<Integer> goodsIdList) {

        Map<Integer, String> goodsUnitMap = new HashMap<>();
        if (CollectionUtils.isEmpty(goodsIdList)) {
            return goodsUnitMap;
        }
        PropertyDetailVO propertyDetailVO = storeManager.queryPropertyDetail("goodsUnit");
        if (Objects.isNull(propertyDetailVO)) {
            return goodsUnitMap;
        }
        List<GoodsEntity> goodsEntities = goodsRepository.queryByIdForDelete(goodsIdList);
        Map<Integer, Integer> goodsIdAndUnit = goodsEntities.stream().filter(goods -> Objects.nonNull(goods.getGoodsUnit())).collect(Collectors.toMap(GoodsEntity::getId, GoodsEntity::getGoodsUnit));
        Map<String, String> propertyMap = propertyDetailVO.getValueList().stream().collect(Collectors.toMap(PropertyValueVO::getCode, PropertyValueVO::getValue, (v1, v2) -> v1));
        for (Integer goodsId : goodsIdList) {
            Integer goodsUnit = goodsIdAndUnit.get(goodsId);
            if (Objects.isNull(goodsUnit)) {
                continue;
            }
            String property = propertyMap.get(goodsUnit.toString());
            if (StringUtils.isEmpty(property) || "无".equals(property)) {
                continue;
            }
            goodsUnitMap.put(goodsId, property);
        }
        return goodsUnitMap;
    }

    @Override
    public Result<List<ActivityOrderInfoVO>> getActivityOrderInfo(List<String> orderSnList) {
        List<ActivityOrderInfoVO> result = new ArrayList<>();
        List<HeOrderEntity> byOrderSnList = orderRepository.getByOrderSnList(orderSnList);
        if (CollectionUtils.isNotEmpty(byOrderSnList)) {
            result = byOrderSnList.stream().map(o -> {
                ActivityOrderInfoVO activityOrderInfoVO = new ActivityOrderInfoVO();
                activityOrderInfoVO.setOrderId(o.getOrderId());
                activityOrderInfoVO.setOrderSn(o.getOrderSn());
                activityOrderInfoVO.setIsNotice(o.getIsNotice());
                activityOrderInfoVO.setOrderStatus(o.getOrderStatus());
                Instant instant = Instant.ofEpochMilli(o.getCreatedAt() * 1000);
                LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String formattedDateTime = dateTime.format(formatter);
                activityOrderInfoVO.setCreateTime(formattedDateTime);
                activityOrderInfoVO.setOrderStatusStr(OmniOrderStatusEnum.from(o.getOrderStatus()).getValue());
                return activityOrderInfoVO;
            }).collect(Collectors.toList());
        }
        return Result.success(result);
    }

    @Override
    public PageDTO<WechatMyOrderNewVO> getOrderByCustomerBasicUid(WechatMyOrderQuery wechatMyOrderQuery) {
        String keyword = wechatMyOrderQuery.getKeyword();
        if (StringUtils.isNotEmpty(keyword)) {
            List<Integer> orderIdList = new ArrayList<>();
            StoreQueryCondition condition = new StoreQueryCondition();
            condition.setName(keyword);
            List<CfgStoreEntity> cfgStoreEntities = storeRepository.queryStoreBaseByCondition(condition);
            if (CollectionUtils.isNotEmpty(cfgStoreEntities)) {
                List<Integer> storeIdList = cfgStoreEntities.stream().map(CfgStoreEntity::getStoreId).collect(Collectors.toList());
                wechatMyOrderQuery.setStoreIds(storeIdList);
            }

            List<HeOrderGoodsEntity> listByCategoryIds = orderGoodsRepository.getListByCategoryIds(null, keyword, true);
            if (CollectionUtils.isNotEmpty(listByCategoryIds)) {
                List<Integer> categoryOrderIdList = listByCategoryIds.stream().map(HeOrderGoodsEntity::getOrderId).collect(Collectors.toList());
                List<HeOrderEntity> heOrderEntities = orderRepository.filterBasicUid(categoryOrderIdList, wechatMyOrderQuery.getBasicUid());
                orderIdList.addAll(heOrderEntities.stream().map(HeOrderEntity::getOrderId).collect(Collectors.toList()));
            }

            List<HeOrderEntity> listByOrderSn = orderRepository.getListByOrderSn(keyword, wechatMyOrderQuery.getBasicUid());
            if (CollectionUtils.isNotEmpty(listByOrderSn)) {
                orderIdList.addAll(listByOrderSn.stream().map(HeOrderEntity::getOrderId).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(orderIdList)) {
                wechatMyOrderQuery.setOrderIds(orderIdList);
            }
            if (CollectionUtils.isEmpty(orderIdList) && CollectionUtils.isEmpty(wechatMyOrderQuery.getStoreIds())) {
                return new PageDTO<>();
            }
        }
        StoreInfoDTO storeInfoDTO = EmployeeTokenHelper.getCurrentEmployeeStoreInfo();
        log.info("当前登录人门店信息：{}", JSONObject.toJSONString(storeInfoDTO));
        if (!storeInfoDTO.getHasAllStoreInfo()) {
            List<Integer> storeIdList = storeInfoDTO.getStoreIdList();
            if (CollectionUtil.isEmpty(storeIdList)) {
                wechatMyOrderQuery.setStoreIds(Collections.singletonList(-1));
            } else {
                if (CollectionUtils.isEmpty(wechatMyOrderQuery.getStoreIds())) {
                    wechatMyOrderQuery.setStoreIds(storeIdList);
                } else {
                    wechatMyOrderQuery.getStoreIds().retainAll(storeIdList);
                }
            }
            if (CollectionUtils.isEmpty(wechatMyOrderQuery.getStoreIds())) {
                return new PageDTO<>();
            }
        }
        if (Objects.nonNull(wechatMyOrderQuery.getOrderId())) {
            List<Integer> orderIds = Lists.newArrayList();
            orderIds.add(wechatMyOrderQuery.getOrderId());
            wechatMyOrderQuery.setOrderIds(orderIds);
        }
        Page<HeOrderEntity> orderEntityPage = orderRepository.queryListByBasicUid(wechatMyOrderQuery);
        if (CollectionUtils.isEmpty(orderEntityPage.getRecords())) {
            return new PageDTO<>();
        }

        List<HeOrderEntity> records = orderEntityPage.getRecords();
        List<Integer> orderIds = records.stream().map(HeOrderEntity::getOrderId).collect(Collectors.toList());
        Map<Integer, HeOrderEntity> heOrderEntityMap = records.stream().collect(Collectors.toMap(HeOrderEntity::getOrderId, entity -> entity, (v1, v2) -> v1));

        List<Integer> clientUidList = records.stream().map(HeOrderEntity::getClientUid).collect(Collectors.toList());
        List<TabClientEntity> tabClientByIdList = clientRepository.getTabClientByIdList(clientUidList);
        Map<Integer, TabClientEntity> clientNameByIdMap = tabClientByIdList.stream().collect(Collectors.toMap(TabClientEntity::getId, Function.identity()));

        List<Integer> storeIdList = records.stream().map(HeOrderEntity::getStoreId).collect(Collectors.toList());
        List<CfgStoreEntity> storeByIdList = storeRepository.queryCfgStoreByIdList(storeIdList);
        Map<Integer, CfgStoreEntity> storeInfoMap = storeByIdList.stream().collect(Collectors.toMap(CfgStoreEntity::getStoreId, entity -> entity, (v1, v2) -> v1));

        List<HeOrderGoodsEntity> orderGoodsByOrderIdList = orderGoodsRepository.getByOrderIdList(orderIds);
        Map<Integer, List<HeOrderGoodsEntity>> orderGoodsByOrderIdMap = orderGoodsByOrderIdList.stream().collect(Collectors.groupingBy(HeOrderGoodsEntity::getOrderId));

        orderGoodsByOrderIdMap.forEach((key, value) -> {
            HeOrderEntity heOrderEntity = heOrderEntityMap.get(key);
            BigDecimal calPayableAmount = AmountChangeUtil.changeF2Y(heOrderEntity.calPayable());
            BigDecimal orderAmount = AmountChangeUtil.changeF2Y(heOrderEntity.getOrderAmount());
            fillOrderGoods(value, orderAmount, calPayableAmount);
        });
        List<HeOrderRefundEntity> refundList = orderRefundRepository.getRefundByOrderIdList(orderIds);
        Map<Integer, String> refundMap = CollectionUtils.isEmpty(refundList) ? Maps.newHashMap() : refundList.stream().collect(Collectors.toMap(HeOrderRefundEntity::getOrderId, HeOrderRefundEntity::getRefundOrderSn, (v1, v2) -> v2));
        return PageUtils.convert2PageDTO(orderEntityPage, temp -> buildWechatMyOrderNewVO(temp, orderGoodsByOrderIdMap, clientNameByIdMap, storeInfoMap, refundMap));
    }

    @Override
    public PageDTO<WechatMyOrderNewVO> getOrderByCustomerBasicUidV2(WechatMyOrderQuery wechatMyOrderQuery) {

        String keyword = wechatMyOrderQuery.getKeyword();
        if (StringUtils.isNotEmpty(keyword)) {
            List<Integer> orderIdList = new ArrayList<>();
            setOrderIdList(wechatMyOrderQuery, keyword, orderIdList);
            if (CollectionUtils.isEmpty(orderIdList) && CollectionUtils.isEmpty(wechatMyOrderQuery.getStoreIds())) {
                return new PageDTO<>();
            }
        }

        StoreInfoDTO storeInfoDTO = EmployeeTokenHelper.getCurrentEmployeeStoreInfo();
        log.info("当前登录人门店信息：{}", JSONObject.toJSONString(storeInfoDTO));
        if (!storeInfoDTO.getHasAllStoreInfo()) {
            List<Integer> storeIdList = storeInfoDTO.getStoreIdList();
            if (CollectionUtil.isEmpty(storeIdList)) {
                wechatMyOrderQuery.setStoreIds(Collections.singletonList(-1));
            } else {
                if (CollectionUtils.isEmpty(wechatMyOrderQuery.getStoreIds())) {
                    wechatMyOrderQuery.setStoreIds(storeIdList);
                } else {
                    wechatMyOrderQuery.getStoreIds().retainAll(storeIdList);
                }
            }
            if (CollectionUtils.isEmpty(wechatMyOrderQuery.getStoreIds())) {
                return new PageDTO<>();
            }
        }

        if (Objects.nonNull(wechatMyOrderQuery.getOrderId())) {
            List<Integer> orderIds = Lists.newArrayList();
            orderIds.add(wechatMyOrderQuery.getOrderId());
            wechatMyOrderQuery.setOrderIds(orderIds);
        }

        wechatMyOrderQuery.setPageNum(1);
        wechatMyOrderQuery.setPageSize(Integer.MAX_VALUE);
        log.info("查询订单：{}", JsonUtil.write(wechatMyOrderQuery));
        Page<HeOrderEntity> orderEntityPage = orderRepository.queryListByBasicUid(wechatMyOrderQuery);
        log.info("查询订单结果：{}", JsonUtil.write(orderEntityPage));
        if (CollectionUtils.isEmpty(orderEntityPage.getRecords())) {
            return new PageDTO<>();
        }

//        List<String> orderSnList = orderEntityPage.getRecords().stream().map(HeOrderEntity::getOrderSn).collect(Collectors.toList());

//        ListCheckInDateReq listCheckInDateReq = new ListCheckInDateReq();
//        listCheckInDateReq.setOrderNos(orderSnList);
//
//        log.info("客诉-查询订单常规搜索结果：{}", JsonUtil.write(orderSnList));
//        List<RoomCheckInDateVO> roomCheckInDateVOS = roomExternalQuery.listCheckInDate(listCheckInDateReq).stream().filter(r -> Arrays.asList(2, 3).contains(r.getCheckInStatus())).collect(Collectors.toList());
//        log.info("客诉-查询订单入住：{}", JsonUtil.write(roomCheckInDateVOS));

//        List<HeOrderEntity> collect = orderEntityPage.getRecords().stream().filter(o -> {
//            if (ObjectUtil.isNotEmpty(o.getVerificationStatus()) && o.getVerificationStatus().equals(BooleanUtil.toInt(true))) {
//                //该笔订单在核销表中有已完成的核销记录
//                return true;
//            }
//            log.info("订单sn:{},该笔订单在核销表中有已完成的核销记录：{}", o.getOrderSn(), ObjectUtil.isNotEmpty(o.getVerificationStatus()) && o.getVerificationStatus().equals(BooleanUtil.toInt(true)));
//            log.info("订单sn:{},是否是新订单：{}", o.getOrderSn(), o.isNewOrder());
//            if (o.isNewOrder()) {
//                Optional<RoomCheckInDateVO> first = roomCheckInDateVOS.stream().filter(r -> r.getOrderNo().equals(o.getOrderSn())).findFirst();
//                log.info("订单sn:{},新订单是都有入住记录：{}", first.isPresent());
//                if (first.isPresent()) {
//                    log.info("订单sn:{},新订单入住时间：{}", first.get().getCheckInDate());
//                    return ObjectUtil.isNotEmpty(first.get().getCheckInDate());
//                }
//            } else {
//                log.info("订单sn:{},老订单根据订单状态判断，订单状态为入住中/已离店/已关闭/已离馆：{}", Arrays.asList(OrderStatusV2Enum.STAY_IN.getCode(), OrderStatusV2Enum.OUT_OF_STORE.getCode(), OrderStatusV2Enum.CLOSE.getCode(), OrderStatusV2Enum.ADVANCE_OUT_OF_STORE.getCode()).contains(o.getOrderStatus()));
//                //老订单根据订单状态判断，订单状态为入住中/已离店/已关闭/已离馆
//                return Arrays.asList(OrderStatusV2Enum.STAY_IN.getCode(), OrderStatusV2Enum.OUT_OF_STORE.getCode(), OrderStatusV2Enum.CLOSE.getCode(), OrderStatusV2Enum.ADVANCE_OUT_OF_STORE.getCode()).contains(o.getOrderStatus());
//            }
//            return false;
//        }).collect(Collectors.toList());
//
//        if (CollectionUtils.isEmpty(collect)) {
//            return new PageDTO<>();
//        }

//        Page<HeOrderEntity> heOrderEntityPage = orderRepository.pageByOrderIdList(wechatMyOrderQuery.getPageNum(), wechatMyOrderQuery.getPageSize(), collect.stream().map(HeOrderEntity::getOrderId).collect(Collectors.toList()));

        if (CollectionUtils.isEmpty(orderEntityPage.getRecords())) {
            return new PageDTO<>();
        }

        List<OrderProductionExtendEntity> orderProductionExtendEntities = orderProductionExtendRepository.queryAllByOrderIdList(orderEntityPage.getRecords().stream().map(HeOrderEntity::getOrderId).collect(Collectors.toList()));
        Map<Integer, List<OrderProductionExtendEntity>> orderProductionExtendMap = orderProductionExtendEntities.stream().collect(Collectors.groupingBy(OrderProductionExtendEntity::getOrderId));

        List<HeOrderEntity> records = orderEntityPage.getRecords();
        List<Integer> orderIds = records.stream().map(HeOrderEntity::getOrderId).collect(Collectors.toList());
        Map<Integer, HeOrderEntity> heOrderEntityMap = records.stream().collect(Collectors.toMap(HeOrderEntity::getOrderId, entity -> entity, (v1, v2) -> v1));

        List<Integer> clientUidList = records.stream().map(HeOrderEntity::getClientUid).collect(Collectors.toList());
        List<TabClientEntity> tabClientByIdList = clientRepository.getTabClientByIdList(clientUidList);
        Map<Integer, TabClientEntity> clientNameByIdMap = tabClientByIdList.stream().collect(Collectors.toMap(TabClientEntity::getId, Function.identity()));

        List<Integer> storeIdList = records.stream().map(HeOrderEntity::getStoreId).collect(Collectors.toList());
        List<CfgStoreEntity> storeByIdList = storeRepository.queryCfgStoreByIdList(storeIdList);
        Map<Integer, CfgStoreEntity> storeInfoMap = storeByIdList.stream().collect(Collectors.toMap(CfgStoreEntity::getStoreId, entity -> entity, (v1, v2) -> v1));

        List<HeOrderGoodsEntity> orderGoodsByOrderIdList = orderGoodsRepository.getByOrderIdList(orderIds);
        Map<Integer, List<HeOrderGoodsEntity>> orderGoodsByOrderIdMap = orderGoodsByOrderIdList.stream().collect(Collectors.groupingBy(HeOrderGoodsEntity::getOrderId));

        orderGoodsByOrderIdMap.forEach((key, value) -> {
            HeOrderEntity heOrderEntity = heOrderEntityMap.get(key);
            BigDecimal calPayableAmount = AmountChangeUtil.changeF2Y(heOrderEntity.calPayable());
            BigDecimal orderAmount = AmountChangeUtil.changeF2Y(heOrderEntity.getOrderAmount());
            fillOrderGoods(value, orderAmount, calPayableAmount);
        });
        List<HeOrderRefundEntity> refundList = orderRefundRepository.getRefundByOrderIdList(orderIds);
        Map<Integer, String> refundMap = CollectionUtils.isEmpty(refundList) ? Maps.newHashMap() : refundList.stream().collect(Collectors.toMap(HeOrderRefundEntity::getOrderId, HeOrderRefundEntity::getRefundOrderSn, (v1, v2) -> v2));
        log.info("查到的订单列表,{}", JsonUtil.write(orderEntityPage));
        return PageUtils.convert2PageDTO(orderEntityPage, temp -> buildWechatMyOrderNewVOV2(temp, orderGoodsByOrderIdMap, clientNameByIdMap, storeInfoMap, refundMap, orderProductionExtendMap));
    }

    private void setOrderIdList(WechatMyOrderQuery wechatMyOrderQuery, String keyword, List<Integer> orderIdList) {
        StoreQueryCondition condition = new StoreQueryCondition();
        condition.setName(keyword);
        List<CfgStoreEntity> cfgStoreEntities = storeRepository.queryStoreBaseByCondition(condition);
        if (CollectionUtils.isNotEmpty(cfgStoreEntities)) {
            List<Integer> storeIdList = cfgStoreEntities.stream().map(CfgStoreEntity::getStoreId).collect(Collectors.toList());
            wechatMyOrderQuery.setStoreIds(storeIdList);
        }

        List<HeOrderGoodsEntity> listByCategoryIds = orderGoodsRepository.getListByCategoryIds(null, keyword, true);
        if (CollectionUtils.isNotEmpty(listByCategoryIds)) {
            List<Integer> categoryOrderIdList = listByCategoryIds.stream().map(HeOrderGoodsEntity::getOrderId).collect(Collectors.toList());
            List<HeOrderEntity> heOrderEntities = orderRepository.filterBasicUid(categoryOrderIdList, wechatMyOrderQuery.getBasicUid());
            orderIdList.addAll(heOrderEntities.stream().map(HeOrderEntity::getOrderId).collect(Collectors.toList()));
        }

        //产康订单商品表
        List<Integer> productOrderIdByGoodsName = orderProductionExtendRepository.getProductOrderIdByGoodsName(keyword);
        if (CollectionUtils.isNotEmpty(productOrderIdByGoodsName)) {
            orderIdList.addAll(productOrderIdByGoodsName);
        }

        List<HeOrderEntity> listByOrderSn = orderRepository.getListByOrderSn(keyword, wechatMyOrderQuery.getBasicUid());
        if (CollectionUtils.isNotEmpty(listByOrderSn)) {
            orderIdList.addAll(listByOrderSn.stream().map(HeOrderEntity::getOrderId).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(orderIdList)) {
            wechatMyOrderQuery.setOrderIds(orderIdList);
        }
    }

    @Override
    public Result<LinkedHashMap> queryButtonByOldOrderId(Integer orderId) {
        String s = HttpUtil.get(PhpApiConstant.phpApiUrl + "/api/order-btn-list?orderId=" + orderId);

        LinkedHashMap btn = JsonUtil.read(s, LinkedHashMap.class);

        LinkedHashMap data = (LinkedHashMap) btn.get("data");

        ArrayList<LinkedHashMap> button = (ArrayList<LinkedHashMap>) data.get("button");

        Integer realIncome = incomeRecordRepository.getRealIncome(orderId);

        if (ObjectUtil.isNotEmpty(realIncome) && realIncome > 0) {
            //添加按妞
            LinkedHashMap<String, Object> refundToBalance = new LinkedHashMap<>();
            refundToBalance.put("code", OrderButtonEnum.REFUND_TO_BALANCE.getCode());
            refundToBalance.put("value", OrderButtonEnum.REFUND_TO_BALANCE.getValue());
            button.add(refundToBalance);
        }
        return Result.success(data);
    }

    @Override
    public PageDTO<WechatMyOrderNewVO> queryGiftOrderInfoList(WechatMyOrderQuery wechatMyOrderQuery) {

        Assert.isTrue(CollectionUtils.isNotEmpty(wechatMyOrderQuery.getStoreIds()), "门店不能为空");

        //验证当前操作客户门店权限信息
        StoreInfoDTO storeInfoDTO = EmployeeTokenHelper.getCurrentEmployeeStoreInfo();
        if (!storeInfoDTO.getHasAllStoreInfo()) {
            if (CollectionUtil.isEmpty(storeInfoDTO.getStoreIdList())) {
                return PageDTO.emptyList(wechatMyOrderQuery.getPageNum(), wechatMyOrderQuery.getPageSize());
            } else {
                if (CollectionUtils.isEmpty(storeInfoDTO.getStoreIdList().stream().filter(wechatMyOrderQuery.getStoreIds()::contains).collect(Collectors.toList()))) {
                    return PageDTO.emptyList(wechatMyOrderQuery.getPageNum(), wechatMyOrderQuery.getPageSize());
                }
            }
        }

        if (StringUtils.isNotEmpty(wechatMyOrderQuery.getKeyword())) {
            List<Integer> orderIdList = new ArrayList<>();
            List<CfgStoreEntity> cfgStoreEntities = storeRepository.queryStoreBaseByCondition(StoreQueryCondition.builder().name(wechatMyOrderQuery.getKeyword()).build());
            if (CollectionUtils.isNotEmpty(cfgStoreEntities)) {
                List<Integer> storeIdList = cfgStoreEntities.stream().map(CfgStoreEntity::getStoreId).filter(wechatMyOrderQuery.getStoreIds()::contains).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(storeIdList)) {
                    return PageDTO.emptyList(wechatMyOrderQuery.getPageNum(), wechatMyOrderQuery.getPageSize());
                }
            }

            List<HeOrderGoodsEntity> listByCategoryIds = orderGoodsRepository.getListByCategoryIds(null, wechatMyOrderQuery.getKeyword(), true);
            if (CollectionUtils.isNotEmpty(listByCategoryIds)) {
                orderIdList.addAll(listByCategoryIds.stream().map(HeOrderGoodsEntity::getOrderId).filter(Objects::nonNull).collect(Collectors.toList()));
            }

            List<Integer> productOrderIdByGoodsName = orderProductionExtendRepository.getProductOrderIdByGoodsName(wechatMyOrderQuery.getKeyword());
            if (CollectionUtils.isNotEmpty(productOrderIdByGoodsName)) {
                orderIdList.addAll(productOrderIdByGoodsName);
            }

            List<Integer> productGiftOrderIdByGoodsName = orderGiftExtendRepository.getProductOrderIdByGoodsName(wechatMyOrderQuery.getKeyword());
            if (CollectionUtils.isNotEmpty(productGiftOrderIdByGoodsName)) {
                orderIdList.addAll(productGiftOrderIdByGoodsName);
            }

            List<HeOrderEntity> listByOrderSn = orderRepository.getListByOrderSn(wechatMyOrderQuery.getKeyword(), wechatMyOrderQuery.getBasicUid());
            if (CollectionUtils.isNotEmpty(listByOrderSn)) {
                orderIdList.addAll(listByOrderSn.stream().map(HeOrderEntity::getOrderId).collect(Collectors.toList()));
            }
            if (CollectionUtils.isEmpty(orderIdList) && CollectionUtils.isEmpty(cfgStoreEntities)) {
                return PageDTO.emptyList(wechatMyOrderQuery.getPageNum(), wechatMyOrderQuery.getPageSize());
            }
            wechatMyOrderQuery.setOrderIds(orderIdList);
        }

        Page<HeOrderEntity> heOrderEntityPage = orderRepository.queryGiftOrderList(wechatMyOrderQuery);
        if (CollectionUtils.isEmpty(heOrderEntityPage.getRecords())) {
            return PageDTO.emptyList(wechatMyOrderQuery.getPageNum(), wechatMyOrderQuery.getPageSize());
        }

        List<OrderProductionExtendEntity> orderProductionExtendEntities = orderProductionExtendRepository.queryAllByOrderIdList(heOrderEntityPage.getRecords().stream().map(HeOrderEntity::getOrderId).collect(Collectors.toList()));
        Map<Integer, List<OrderProductionExtendEntity>> orderProductionExtendMap = orderProductionExtendEntities.stream().collect(Collectors.groupingBy(OrderProductionExtendEntity::getOrderId));

        List<HeOrderEntity> records = heOrderEntityPage.getRecords();
        List<Integer> orderIds = records.stream().map(HeOrderEntity::getOrderId).collect(Collectors.toList());
        Map<Integer, HeOrderEntity> heOrderEntityMap = records.stream().collect(Collectors.toMap(HeOrderEntity::getOrderId, entity -> entity, (v1, v2) -> v1));

        List<Integer> clientUidList = records.stream().map(HeOrderEntity::getClientUid).collect(Collectors.toList());
        List<TabClientEntity> tabClientByIdList = clientRepository.getTabClientByIdList(clientUidList);
        Map<Integer, TabClientEntity> clientNameByIdMap = tabClientByIdList.stream().collect(Collectors.toMap(TabClientEntity::getId, Function.identity()));

        List<Integer> storeIdList = records.stream().map(HeOrderEntity::getStoreId).collect(Collectors.toList());
        List<CfgStoreEntity> storeByIdList = storeRepository.queryCfgStoreByIdList(storeIdList);
        Map<Integer, CfgStoreEntity> storeInfoMap = storeByIdList.stream().collect(Collectors.toMap(CfgStoreEntity::getStoreId, entity -> entity, (v1, v2) -> v1));

        List<HeOrderGoodsEntity> orderGoodsByOrderIdList = orderGoodsRepository.getByOrderIdList(orderIds);
        Map<Integer, List<HeOrderGoodsEntity>> orderGoodsByOrderIdMap = orderGoodsByOrderIdList.stream().collect(Collectors.groupingBy(HeOrderGoodsEntity::getOrderId));

        orderGoodsByOrderIdMap.forEach((key, value) -> {
            HeOrderEntity heOrderEntity = heOrderEntityMap.get(key);
            BigDecimal calPayableAmount = AmountChangeUtil.changeF2Y(heOrderEntity.calPayable());
            BigDecimal orderAmount = AmountChangeUtil.changeF2Y(heOrderEntity.getOrderAmount());
            fillOrderGoods(value, orderAmount, calPayableAmount);
        });
        List<HeOrderRefundEntity> refundList = orderRefundRepository.getRefundByOrderIdList(orderIds);
        Map<Integer, String> refundMap = CollectionUtils.isEmpty(refundList) ? Maps.newHashMap() : refundList.stream().collect(Collectors.toMap(HeOrderRefundEntity::getOrderId, HeOrderRefundEntity::getRefundOrderSn, (v1, v2) -> v2));
        return PageUtils.convert2PageDTO(heOrderEntityPage, temp -> buildWechatMyOrderNewVOV2(temp, orderGoodsByOrderIdMap, clientNameByIdMap, storeInfoMap, refundMap, orderProductionExtendMap));
    }


    private static String convertBigDecimal2Str(String currency, BigDecimal amount) {

        if (Objects.isNull(amount)) {
            return currency + "0.00";
        }
        return currency + amount.divide(BigDecimal.ONE, 2, BigDecimal.ROUND_UP);
    }

    public OrderInfoQueryTemplate getOrderInfoQueryTemplate(Integer orderType) {

        log.info("获取订单信息查询模板开始处理: dataType:{}", orderType);
        if (orderType == null) {
            return null;
        }
        return Arrays.stream(orderInfoQueryTemplate).filter(item -> item.bizHandleType(orderType)).findAny().orElse(null);
    }


}

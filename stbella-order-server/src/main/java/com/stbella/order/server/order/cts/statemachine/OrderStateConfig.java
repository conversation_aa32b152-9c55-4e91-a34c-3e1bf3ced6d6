package com.stbella.order.server.order.cts.statemachine;

import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.server.order.cts.constant.CtsOrderConstant;
import com.stbella.order.server.order.cts.enums.OrderStatusStateMachineStatusEnum;
import com.stbella.order.server.order.cts.service.OrderCtsService;
import com.stbella.order.server.order.cts.statemachine.action.auntsitter.*;
import com.stbella.order.server.order.cts.statemachine.action.babysitter.*;
import com.stbella.order.server.order.cts.statemachine.action.course.*;
import com.stbella.order.server.order.cts.statemachine.action.generalcustomer.*;
import com.stbella.order.server.order.cts.statemachine.action.generalsitter.*;
import com.stbella.order.server.order.cts.statemachine.action.margin.*;
import com.stbella.order.server.order.cts.statemachine.action.servefee.*;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.config.StateMachineBuilder;
import org.springframework.statemachine.listener.StateMachineListener;
import org.springframework.statemachine.listener.StateMachineListenerAdapter;
import org.springframework.statemachine.persist.DefaultStateMachinePersister;
import org.springframework.statemachine.persist.StateMachinePersister;
import org.springframework.statemachine.state.State;
import org.springframework.statemachine.transition.Transition;

import javax.annotation.Resource;
import java.util.EnumSet;
import java.util.Objects;

/**
 * OrderStateConfig
 *
 * <AUTHOR>
 * @date 2022-03-10 11:43
 * @sine 1.0.0
 */
@Configuration
@Slf4j
@Aspect
public class OrderStateConfig {

    @Resource
    private AuntSitterApplyRefundAction auntSitterApplyRefundAction;
    @Resource
    private AuntSitterCancelAction auntSitterCancelAction;
    @Resource
    private AuntSitterCreateAction auntSitterCreateAction;
    @Resource
    private AuntSitterPaySuccessAction auntSitterPaySuccessAction;
    @Resource
    private AuntSitterRefundEndAction auntSitterRefundEndAction;

    @Resource
    private BabySitterApplyRefundAction babySitterApplyRefundAction;
    @Resource
    private BabySitterCancelAction babySitterCancelAction;
    @Resource
    private BabySitterCreateAction babySitterCreateAction;
    @Resource
    private BabySitterPaySuccessAction babySitterPaySuccessAction;
    @Resource
    private BabySitterRefundEndAction babySitterRefundEndAction;

    @Resource
    private CourseApplyRefundAction courseApplyRefundAction;
    @Resource
    private CourseCancelAction courseCancelAction;
    @Resource
    private CourseCreateAction courseCreateAction;
    @Resource
    private CoursePaySuccessAction coursePaySuccessAction;
    @Resource
    private CourseRefundEndAction courseRefundEndAction;

    @Resource
    private MarginApplyRefundAction marginApplyRefundAction;
    @Resource
    private MarginCancelAction marginCancelAction;
    @Resource
    private MarginCreateAction marginCreateAction;
    @Resource
    private MarginPaySuccessAction marginPaySuccessAction;
    @Resource
    private MarginRefundEndAction marginRefundEndAction;

    @Resource
    private ServeFeeApplyRefundAction serveFeeApplyRefundAction;
    @Resource
    private ServeFeeCancelAction serveFeeCancelAction;
    @Resource
    private ServeFeeCreateAction serveFeeCreateAction;
    @Resource
    private ServeFeePaySuccessAction serveFeePaySuccessAction;
    @Resource
    private ServeFeeRefundEndAction serveFeeRefundEndAction;

    @Resource
    private GeneralSitterApplyRefundAction generalSitterApplyRefundAction;
    @Resource
    private GeneralSitterCancelAction generalSitterCancelAction;
    @Resource
    private GeneralSitterCreateAction generalSitterCreateAction;
    @Resource
    private GeneralSitterPaySuccessAction generalSitterPaySuccessAction;
    @Resource
    private GeneralSitterRefundEndAction generalSitterRefundEndAction;

    @Resource
    private GeneralCustomerApplyRefundAction generalCustomerApplyRefundAction;
    @Resource
    private GeneralCustomerCancelAction generalCustomerCancelAction;
    @Resource
    private GeneralCustomerCreateAction generalCustomerCreateAction;
    @Resource
    private GeneralCustomerPaySuccessAction generalCustomerPaySuccessAction;
    @Resource
    private GeneralCustomerRefundEndAction generalCustomerRefundEndAction;


    @Bean
    public StateMachinePersister<OrderStatusStateMachineStatusEnum, OrderStateEvent, OrderContext> orderStateMachinePersister(OrderCtsService orderCtsService) {
        return new DefaultStateMachinePersister<>(new OrderStateMachinePersist(orderCtsService));
    }

    public StateMachine<OrderStatusStateMachineStatusEnum, OrderStateEvent> buildOrderMachine(OrderStateEvent event) {
        try {
            StateMachineBuilder.Builder<OrderStatusStateMachineStatusEnum, OrderStateEvent> builder = StateMachineBuilder.builder();
            builder.configureConfiguration().withConfiguration().listener(this.listener());

            builder.configureStates().withStates()
                    .initial(OrderStatusStateMachineStatusEnum.MACHINE_NOT_CREATED)
                    .states(EnumSet.allOf(OrderStatusStateMachineStatusEnum.class));

            final int source = event.getSource();
            if (Objects.equals(source, CtsOrderConstant.EVENT_TYPE_BABY_SITTER)) {
                this.buildBabySitter(builder);
            } else if (Objects.equals(source, CtsOrderConstant.EVENT_TYPE_AUNT_SITTER)) {
                this.buildAuntSitter(builder);
            } else if (Objects.equals(source, CtsOrderConstant.EVENT_TYPE_COURSE)) {
                this.buildCourse(builder);
            } else if (Objects.equals(source, CtsOrderConstant.EVENT_TYPE_MARGIN)) {
                this.buildMargin(builder);
            } else if (Objects.equals(source, CtsOrderConstant.EVENT_TYPE_SERVE_FEE)) {
                this.buildServeFee(builder);
            } else if (Objects.equals(source, CtsOrderConstant.EVENT_TYPE_GENERAL_CUSTOMER)) {
                this.buildGeneralCustomer(builder);
            } else if (Objects.equals(source, CtsOrderConstant.EVENT_TYPE_GENERAL_SITTER)) {
                this.buildGeneralSitter(builder);
            } else {
                log.error("订单状态机异常:接受未知订单事件" + event);
                throw new BusinessException(ResultEnum.SYSTEM_RESOURCE_ERROR.getCode(), "订单状态机异常:接受未知订单事件");
            }

            return builder.build();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Around("execution(* org.springframework.statemachine.action.Action.execute(..))")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            return joinPoint.proceed();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            // TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            StateMachineUtil.onActionError((StateContext) joinPoint.getArgs()[0], e);
            return null;
        }
    }

    public StateMachineListener<OrderStatusStateMachineStatusEnum, OrderStateEvent> listener() {
        return new StateMachineListenerAdapter<OrderStatusStateMachineStatusEnum, OrderStateEvent>() {
            @Override
            public void stateChanged(State<OrderStatusStateMachineStatusEnum, OrderStateEvent> from, State<OrderStatusStateMachineStatusEnum, OrderStateEvent> to) {
                String stringBuilder = "状态变化" +
                        " from " + (null != from ? from.getId() : null) +
                        " to " + (null != to ? to.getId() : null);
                log.info(stringBuilder);
            }

            @Override
            public void transition(Transition<OrderStatusStateMachineStatusEnum, OrderStateEvent> transition) {
                String stringBuilder = "状态流转" +
                        " kind " + (null != transition.getKind() ? transition.getKind() : null) +
                        " from " + (null != transition.getSource() ? transition.getSource().getId() : null) +
                        " to " + (null != transition.getTarget() ? transition.getTarget().getId() : null) +
                        " trigger " + (null != transition.getTrigger() ? transition.getTrigger().getEvent() : null);
                log.info(stringBuilder);
            }

            @Override
            public void stateEntered(State<OrderStatusStateMachineStatusEnum, OrderStateEvent> state) {
                super.stateEntered(state);
            }

            @Override
            public void stateExited(State<OrderStatusStateMachineStatusEnum, OrderStateEvent> state) {
                super.stateExited(state);
            }

            @Override
            public void eventNotAccepted(Message<OrderStateEvent> event) {
                super.eventNotAccepted(event);
            }

            @Override
            public void transitionStarted(Transition<OrderStatusStateMachineStatusEnum, OrderStateEvent> transition) {
                super.transitionStarted(transition);
            }

            @Override
            public void transitionEnded(Transition<OrderStatusStateMachineStatusEnum, OrderStateEvent> transition) {
                super.transitionEnded(transition);
            }

            @Override
            public void stateMachineStarted(StateMachine<OrderStatusStateMachineStatusEnum, OrderStateEvent> stateMachine) {
                super.stateMachineStarted(stateMachine);
            }

            @Override
            public void stateMachineStopped(StateMachine<OrderStatusStateMachineStatusEnum, OrderStateEvent> stateMachine) {
                super.stateMachineStopped(stateMachine);
            }

            @Override
            public void stateMachineError(StateMachine<OrderStatusStateMachineStatusEnum, OrderStateEvent> stateMachine, Exception exception) {
                super.stateMachineError(stateMachine, exception);
                log.error("状态机异常" + stateMachine.getId(), exception);
            }

            @Override
            public void extendedStateChanged(Object key, Object value) {
                super.extendedStateChanged(key, value);
            }

            @Override
            public void stateContext(StateContext<OrderStatusStateMachineStatusEnum, OrderStateEvent> stateContext) {
                super.stateContext(stateContext);
            }
        };
    }

    private void buildBabySitter(StateMachineBuilder.Builder<OrderStatusStateMachineStatusEnum, OrderStateEvent> builder) throws Exception {
        builder.configureTransitions()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_NOT_CREATED).target(OrderStatusStateMachineStatusEnum.MACHINE_WAIT_PAY)
                .event(OrderStateEvent.BABY_SITTER_EVENT_CREATE).action(babySitterCreateAction)
                // 育婴师-待支付-取消订单
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_WAIT_PAY).target(OrderStatusStateMachineStatusEnum.MACHINE_CANCEL)
                .event(OrderStateEvent.BABY_SITTER_EVENT_CANCEL).action(babySitterCancelAction)
                // 育婴师-待支付-支付成功
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_WAIT_PAY).target(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS)
                .event(OrderStateEvent.BABY_SITTER_EVENT_PAY_SUCCESS).action(babySitterPaySuccessAction)
                // 育婴师-部分支付-支付成功
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS).target(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS)
                .event(OrderStateEvent.BABY_SITTER_EVENT_PAY_SUCCESS).action(babySitterPaySuccessAction)
                // 申请退款 育婴师-支付成功-退款中
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS).target(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND)
                .event(OrderStateEvent.BABY_SITTER_EVENT_APPLY_FOR_REFUND).action(babySitterApplyRefundAction)
                // 申请退款 育婴师-部分退款-退款中
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_REBATES).target(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND)
                .event(OrderStateEvent.BABY_SITTER_EVENT_APPLY_FOR_REFUND).action(babySitterApplyRefundAction)
                // 申请退款 育婴师-退款中-退款中
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND).target(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND)
                .event(OrderStateEvent.BABY_SITTER_EVENT_APPLY_FOR_REFUND).action(babySitterApplyRefundAction)
                // 育婴师-退款中-退款结束
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND).target(OrderStatusStateMachineStatusEnum.MACHINE_REFUND_END)
                .event(OrderStateEvent.BABY_SITTER_EVENT_REFUND_END).action(babySitterRefundEndAction);
    }

    private void buildAuntSitter(StateMachineBuilder.Builder<OrderStatusStateMachineStatusEnum, OrderStateEvent> builder) throws Exception {
        builder.configureTransitions()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_NOT_CREATED).target(OrderStatusStateMachineStatusEnum.MACHINE_WAIT_PAY)
                .event(OrderStateEvent.AUNT_SITTER_EVENT_CREATE).action(auntSitterCreateAction)
                // 母婴-待支付-取消订单
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_WAIT_PAY).target(OrderStatusStateMachineStatusEnum.MACHINE_CANCEL)
                .event(OrderStateEvent.AUNT_SITTER_EVENT_CANCEL).action(auntSitterCancelAction)
                // 母婴-待支付-支付成功
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_WAIT_PAY).target(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS)
                .event(OrderStateEvent.AUNT_SITTER_EVENT_PAY_SUCCESS).action(auntSitterPaySuccessAction)
                // 母婴-部分支付-支付成功
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS).target(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS)
                .event(OrderStateEvent.AUNT_SITTER_EVENT_PAY_SUCCESS).action(auntSitterPaySuccessAction)
                // 母婴-支付成功-退款中
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS).target(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND)
                .event(OrderStateEvent.AUNT_SITTER_EVENT_APPLY_FOR_REFUND).action(auntSitterApplyRefundAction)
                // 申请退款 母婴-部分退款-退款中
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_REBATES).target(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND)
                .event(OrderStateEvent.AUNT_SITTER_EVENT_APPLY_FOR_REFUND).action(auntSitterApplyRefundAction)
                // 申请退款 母婴-退款中-退款中
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND).target(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND)
                .event(OrderStateEvent.AUNT_SITTER_EVENT_APPLY_FOR_REFUND).action(auntSitterApplyRefundAction)
                // 母婴-退款中-退款结束
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND).target(OrderStatusStateMachineStatusEnum.MACHINE_REFUND_END)
                .event(OrderStateEvent.AUNT_SITTER_EVENT_REFUND_END).action(auntSitterRefundEndAction);
    }

    private void buildCourse(StateMachineBuilder.Builder<OrderStatusStateMachineStatusEnum, OrderStateEvent> builder) throws Exception {
        builder.configureTransitions()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_NOT_CREATED).target(OrderStatusStateMachineStatusEnum.MACHINE_WAIT_PAY)
                .event(OrderStateEvent.COURSE_EVENT_CREATE).action(courseCreateAction)
                // 培训课程-待支付-取消订单
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_WAIT_PAY).target(OrderStatusStateMachineStatusEnum.MACHINE_CANCEL)
                .event(OrderStateEvent.COURSE_EVENT_CANCEL).action(courseCancelAction)
                // 培训课程-待支付-支付成功
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_WAIT_PAY).target(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS)
                .event(OrderStateEvent.COURSE_EVENT_PAY_SUCCESS).action(coursePaySuccessAction)
                // 培训课程-部分支付-支付成功
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS).target(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS)
                .event(OrderStateEvent.COURSE_EVENT_PAY_SUCCESS).action(coursePaySuccessAction)
                // 培训课程-支付成功-退款中
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS).target(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND)
                .event(OrderStateEvent.COURSE_EVENT_APPLY_FOR_REFUND).action(courseApplyRefundAction)
                // 申请退款 培训课程-部分退款-退款中
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_REBATES).target(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND)
                .event(OrderStateEvent.COURSE_EVENT_APPLY_FOR_REFUND).action(courseApplyRefundAction)
                // 申请退款 培训课程-退款中-退款中
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND).target(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND)
                .event(OrderStateEvent.COURSE_EVENT_APPLY_FOR_REFUND).action(courseApplyRefundAction)
                // 培训课程-退款中-退款结束
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND).target(OrderStatusStateMachineStatusEnum.MACHINE_REFUND_END)
                .event(OrderStateEvent.COURSE_EVENT_REFUND_END).action(courseRefundEndAction);
    }

    private void buildServeFee(StateMachineBuilder.Builder<OrderStatusStateMachineStatusEnum, OrderStateEvent> builder) throws Exception {
        builder.configureTransitions()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_NOT_CREATED).target(OrderStatusStateMachineStatusEnum.MACHINE_WAIT_PAY)
                .event(OrderStateEvent.SERVE_FEE_EVENT_CREATE).action(serveFeeCreateAction)
                // 平台管理费-待支付-取消订单
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_WAIT_PAY).target(OrderStatusStateMachineStatusEnum.MACHINE_CANCEL)
                .event(OrderStateEvent.SERVE_FEE_EVENT_CANCEL).action(serveFeeCancelAction)
                // 平台管理费-待支付-支付成功
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_WAIT_PAY).target(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS)
                .event(OrderStateEvent.SERVE_FEE_EVENT_PAY_SUCCESS).action(serveFeePaySuccessAction)
                // 平台管理费-部分支付-支付成功
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS).target(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS)
                .event(OrderStateEvent.SERVE_FEE_EVENT_PAY_SUCCESS).action(serveFeePaySuccessAction)
                // 平台管理费-支付成功-退款中
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS).target(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND)
                .event(OrderStateEvent.SERVE_FEE_EVENT_APPLY_FOR_REFUND).action(serveFeeApplyRefundAction)
                // 申请退款 平台管理费-部分退款-退款中
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_REBATES).target(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND)
                .event(OrderStateEvent.SERVE_FEE_EVENT_APPLY_FOR_REFUND).action(serveFeeApplyRefundAction)
                // 申请退款 平台管理费-退款中-退款中
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND).target(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND)
                .event(OrderStateEvent.SERVE_FEE_EVENT_APPLY_FOR_REFUND).action(serveFeeApplyRefundAction)
                // 平台管理费-退款中-退款结束
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND).target(OrderStatusStateMachineStatusEnum.MACHINE_REFUND_END)
                .event(OrderStateEvent.SERVE_FEE_EVENT_REFUND_END).action(serveFeeRefundEndAction);
    }

    private void buildMargin(StateMachineBuilder.Builder<OrderStatusStateMachineStatusEnum, OrderStateEvent> builder) throws Exception {
        builder.configureTransitions()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_NOT_CREATED).target(OrderStatusStateMachineStatusEnum.MACHINE_WAIT_PAY)
                .event(OrderStateEvent.MARGIN_EVENT_CREATE).action(marginCreateAction)
                // 保证金-待支付-取消订单
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_WAIT_PAY).target(OrderStatusStateMachineStatusEnum.MACHINE_CANCEL)
                .event(OrderStateEvent.MARGIN_EVENT_CANCEL).action(marginCancelAction)
                // 保证金-待支付-支付成功
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_WAIT_PAY).target(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS)
                .event(OrderStateEvent.MARGIN_EVENT_PAY_SUCCESS).action(marginPaySuccessAction)
                // 保证金-部分支付-支付成功
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS).target(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS)
                .event(OrderStateEvent.MARGIN_EVENT_PAY_SUCCESS).action(marginPaySuccessAction)
                // 保证金-支付成功-退款中
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS).target(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND)
                .event(OrderStateEvent.MARGIN_EVENT_APPLY_FOR_REFUND).action(marginApplyRefundAction)
                // 申请退款 保证金-部分退款-退款中
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_REBATES).target(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND)
                .event(OrderStateEvent.MARGIN_EVENT_APPLY_FOR_REFUND).action(marginApplyRefundAction)
                // 申请退款 保证金-退款中-退款中
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND).target(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND)
                .event(OrderStateEvent.MARGIN_EVENT_APPLY_FOR_REFUND).action(marginApplyRefundAction)
                // 保证金-退款中-退款结束
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND).target(OrderStatusStateMachineStatusEnum.MACHINE_REFUND_END)
                .event(OrderStateEvent.MARGIN_EVENT_REFUND_END).action(marginRefundEndAction);
    }

    private void buildGeneralCustomer(StateMachineBuilder.Builder<OrderStatusStateMachineStatusEnum, OrderStateEvent> builder) throws Exception {
        builder.configureTransitions()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_NOT_CREATED).target(OrderStatusStateMachineStatusEnum.MACHINE_WAIT_PAY)
                .event(OrderStateEvent.GENERAL_CUSTOMER_EVENT_CREATE).action(generalCustomerCreateAction)
                // 平台管理费-待支付-取消订单
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_WAIT_PAY).target(OrderStatusStateMachineStatusEnum.MACHINE_CANCEL)
                .event(OrderStateEvent.GENERAL_CUSTOMER_EVENT_CANCEL).action(generalCustomerCancelAction)
                // 平台管理费-待支付-支付成功
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_WAIT_PAY).target(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS)
                .event(OrderStateEvent.GENERAL_CUSTOMER_EVENT_PAY_SUCCESS).action(generalCustomerPaySuccessAction)
                // 平台管理费-部分支付-支付成功
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS).target(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS)
                .event(OrderStateEvent.GENERAL_CUSTOMER_EVENT_PAY_SUCCESS).action(generalCustomerPaySuccessAction)
                // 平台管理费-支付成功-退款中
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS).target(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND)
                .event(OrderStateEvent.GENERAL_CUSTOMER_EVENT_APPLY_FOR_REFUND).action(generalCustomerApplyRefundAction)
                // 申请退款 平台管理费-部分退款-退款中
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_REBATES).target(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND)
                .event(OrderStateEvent.GENERAL_CUSTOMER_EVENT_APPLY_FOR_REFUND).action(generalCustomerApplyRefundAction)
                // 申请退款 平台管理费-退款中-退款中
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND).target(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND)
                .event(OrderStateEvent.GENERAL_CUSTOMER_EVENT_APPLY_FOR_REFUND).action(generalCustomerApplyRefundAction)
                // 平台管理费-退款中-退款结束
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND).target(OrderStatusStateMachineStatusEnum.MACHINE_REFUND_END)
                .event(OrderStateEvent.GENERAL_CUSTOMER_EVENT_REFUND_END).action(generalCustomerRefundEndAction);
    }

    private void buildGeneralSitter(StateMachineBuilder.Builder<OrderStatusStateMachineStatusEnum, OrderStateEvent> builder) throws Exception {
        builder.configureTransitions()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_NOT_CREATED).target(OrderStatusStateMachineStatusEnum.MACHINE_WAIT_PAY)
                .event(OrderStateEvent.GENERAL_SITTER_EVENT_CREATE).action(generalSitterCreateAction)
                // 平台管理费-待支付-取消订单
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_WAIT_PAY).target(OrderStatusStateMachineStatusEnum.MACHINE_CANCEL)
                .event(OrderStateEvent.GENERAL_SITTER_EVENT_CANCEL).action(generalSitterCancelAction)
                // 平台管理费-待支付-支付成功
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_WAIT_PAY).target(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS)
                .event(OrderStateEvent.GENERAL_SITTER_EVENT_PAY_SUCCESS).action(generalSitterPaySuccessAction)
                // 平台管理费-部分支付-支付成功
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS).target(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS)
                .event(OrderStateEvent.GENERAL_SITTER_EVENT_PAY_SUCCESS).action(generalSitterPaySuccessAction)
                // 平台管理费-支付成功-退款中
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_PAY_SUCCESS).target(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND)
                .event(OrderStateEvent.GENERAL_SITTER_EVENT_APPLY_FOR_REFUND).action(generalSitterApplyRefundAction)
                // 申请退款 平台管理费-部分退款-退款中
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_REBATES).target(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND)
                .event(OrderStateEvent.GENERAL_SITTER_EVENT_APPLY_FOR_REFUND).action(generalSitterApplyRefundAction)
                // 申请退款 平台管理费-退款中-退款中
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND).target(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND)
                .event(OrderStateEvent.GENERAL_SITTER_EVENT_APPLY_FOR_REFUND).action(generalSitterApplyRefundAction)
                // 平台管理费-退款中-退款结束
                .and()
                .withExternal()
                .source(OrderStatusStateMachineStatusEnum.MACHINE_APPLY_FOR_REFUND).target(OrderStatusStateMachineStatusEnum.MACHINE_REFUND_END)
                .event(OrderStateEvent.GENERAL_SITTER_EVENT_REFUND_END).action(generalSitterRefundEndAction);
    }


}

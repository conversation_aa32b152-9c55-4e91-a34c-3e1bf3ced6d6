package com.stbella.order.server.context.component.contract.checker;

import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.server.context.component.contract.stage.ContractFillStrategy;
import com.stbella.platform.order.api.contract.req.ContractBaseReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ProjectName stbella-contract
 * @PackageName com.stbella.contract.application.context.component.strategy
 * @ClassName ContractFillContext
 * @Description 合同填充上下文 用于路由
 * <AUTHOR>
 * @CreateDate 2024-03-26 13:10
 * @Version 1.0
 */
@Component
@Slf4j
public class ContractCreateValidateContext {

    /**
     * 接口注入器
     */
    @Autowired
    List<AbstractContractCreateValidator> abstractContractCreateValidators;

    @Resource
    private DefaultContractCreateValidator defaultContractCreateValidator;


    /**
     * @param contractValidationContext
     * @return {@link ContractFillStrategy}
     */
    public AbstractContractCreateValidator getValidator(ContractValidationContext contractValidationContext) {
        ContractBaseReq req = contractValidationContext.getRequest();
        for (AbstractContractCreateValidator contractCreateValidator : abstractContractCreateValidators) {
            if (contractCreateValidator.matchValidator(TemplateContractTypeEnum.from(req.getTemplateContractType()))) {
                return contractCreateValidator;
            }
        }
        log.info("未找到合适的合同创建校验器,使用默认校验器");
        return defaultContractCreateValidator;
    }

}
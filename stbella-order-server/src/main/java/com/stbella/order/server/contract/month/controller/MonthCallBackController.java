package com.stbella.order.server.contract.month.controller;

import com.alibaba.fastjson.JSONObject;
import com.stbella.order.server.contract.month.component.MonthContractNotifyAssembler;
import com.stbella.order.server.contract.req.MonthEsignNotifyReq;
import com.stbella.order.server.contract.vo.EsignCallbackVO;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.UnsupportedEncodingException;

import javax.annotation.Resource;

import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import springfox.documentation.annotations.ApiIgnore;

@Slf4j
@RestController
@ApiIgnore
@Api(tags = "【废弃】-已迁移合同stbella-contract服务-月子合同回调服务")
@RequestMapping("/order/callback/contract/month")
public class MonthCallBackController {

    @Resource
    private MonthContractNotifyAssembler monthContractNotifyAssembler;

    /**
     * 主合同签署回调签署状态
     *
     * @param jsonObject json
     * @return http状态码
     * @throws UnsupportedEncodingException
     */
    @PostMapping("/esign/notify")
    public EsignCallbackVO mainAccessoryCallback(@RequestBody JSONObject jsonObject) {
        return monthContractNotifyAssembler.mainAccessoryCallback(JSONUtil.toBean(jsonObject.toJSONString(), MonthEsignNotifyReq.class));
    }

    /**
     * 这是补充协议合同的签署回调通知
     *
     * @param jsonObject json
     * @return http状态码
     * @throws UnsupportedEncodingException
     */
    @PostMapping("/esign/add/notify")
    public EsignCallbackVO agreementCallback(@RequestBody JSONObject jsonObject) {
        return monthContractNotifyAssembler.agreementCallback(JSONUtil.toBean(jsonObject.toJSONString(), MonthEsignNotifyReq.class));
    }

}

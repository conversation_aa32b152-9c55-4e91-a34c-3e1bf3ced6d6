package com.stbella.order.server.context.component.selector;

import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.server.context.component.processor.ContractUpdateApprovalProcessor;
import com.stbella.order.server.context.component.processor.DiscountProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

/**
 * @Author: wangchuang
 * @CreateTime: 2024-05-28  13:55
 * @Description: 订单折扣审批或合同修改审批选择器
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "订单折扣审批或合同修改审批选择器")
public class DiscountOrUpdateApprovalSelector implements IExecutableAtom<FlowContext> {

    @Resource
    private ContractUpdateApprovalProcessor contractUpdateApprovalProcessor;
    @Resource
    private DiscountProcessor discountProcessor;

    @Override
    public void run(FlowContext bizContext) {
        OrderReductionEntity orderReductionEntity = bizContext.getAttribute(OrderReductionEntity.class);
        Boolean orderModifyApproval = orderReductionEntity.getModifyContractApprove();
        //产品逻辑如果有合同修改审批,则不发起折扣审批
        if (Boolean.TRUE.equals(orderModifyApproval)) {
            contractUpdateApprovalProcessor.run(bizContext);
        } else {
            Boolean disCountApprove = orderReductionEntity.getDisCountApprove();
            if (Boolean.TRUE.equals(disCountApprove)) {
                discountProcessor.run(bizContext);
            }
        }
    }
}

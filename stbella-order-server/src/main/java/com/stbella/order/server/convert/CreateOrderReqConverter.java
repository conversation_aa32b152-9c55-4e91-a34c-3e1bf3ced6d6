package com.stbella.order.server.convert;

import com.stbella.platform.order.api.req.CreateOrderReq;
import com.stbella.platform.order.api.req.DiscountReq;
import org.mapstruct.Mapper;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2023-07-27  16:19
 * @Description: 订单创建请求转换
 */
@Mapper(componentModel = "spring")
public interface CreateOrderReqConverter {

    /**
     * 创建订单请求转 折扣请求
     *
     * @param model
     * @return
     */
    DiscountReq toDiscountReq(CreateOrderReq model);


}

package com.stbella.order.server.context.component.processor;

import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.server.manager.AssetManager;
import com.stbella.order.server.order.OrderPrice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * 礼赠金占用
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "礼赠金占用")
public class OccupyGiftAccountProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private AssetManager assetManager;

    @Override
    public void run(FlowContext bizContext) {
        OrderPrice orderPrice = bizContext.getAttribute(OrderPrice.class);
        BigDecimal giftAmount = orderPrice.getGiftAmount();
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        if (giftAmount == null || giftAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("订单号为[{}],礼赠金金额为0，不需要占用", orderEntity.getOrderSn());
            return;
        }
        assetManager.occupyGiftAccount(giftAmount, orderEntity);

    }

//    @Override
//    public boolean condition(FlowContext bizContext) {
//        Integer scene  = (Integer)bizContext.getAttribute(BizConstant.ExtraKey.scene);
//        return !CartSceneEnum.CUSTOMIZE_AS_YOU_WISH.code().equals(scene);
//    }
}

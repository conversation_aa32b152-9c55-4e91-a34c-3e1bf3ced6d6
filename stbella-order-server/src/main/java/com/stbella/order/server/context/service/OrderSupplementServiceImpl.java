package com.stbella.order.server.context.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.contract.model.req.ContractQueryV4;
import com.stbella.contract.model.req.v3.ContractCreateV3Req;
import com.stbella.contract.model.res.ContractSignAgreementVO;
import com.stbella.contract.model.res.ContractSignRecordVO2;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.customer.server.ecp.entity.TabClientPO;
import com.stbella.order.common.enums.month.ContractStatusEnum;
import com.stbella.order.common.enums.month.ContractTypeEnum;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.common.utils.GeneratorUtil;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.domain.repository.StoreRepository;
import com.stbella.order.infrastructure.repository.mapper.saas.HeAddressMapper;
import com.stbella.order.infrastructure.repository.po.HeAddressPO;
import com.stbella.order.server.convert.AppMonthContractSignRecordConverter;
import com.stbella.order.server.manager.ContractManager;
import com.stbella.order.server.manager.TabClientManager;
import com.stbella.order.server.order.month.req.OrderMonthClientReq;
import com.stbella.order.server.utils.DateUtils;
import com.stbella.platform.order.api.OrderSupplementService;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import com.stbella.platform.order.api.req.CheckSupplementAgreementReq;
import com.stbella.platform.order.api.res.CheckSupplementAgreementRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单补充协议服务实现
 */
@Slf4j
@Service
public class OrderSupplementServiceImpl implements OrderSupplementService {

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private TabClientManager tabClientManager;

    @Resource
    private ContractManager contractManager;

    @Resource
    StoreRepository storeRepository;

    @Resource
    HeAddressMapper heAddressService;
    @Resource
    AppMonthContractSignRecordConverter appMonthContractSignRecordConverter;


    @Override
    public Result<CheckSupplementAgreementRes> checkAndCreateSupplementAgreement(CheckSupplementAgreementReq req) {
        return Result.success(CheckSupplementAgreementRes.noNeedSupplement());
//        try {
//            // 1. 查询订单信息
//            HeOrderEntity orderEntity = orderRepository.getByOrderId(req.getOrderId());
//            if (orderEntity == null) {
//                throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "订单不存在");
//            }
//
//            if (!orderEntity.isNeedSign()) {
//                return Result.success(CheckSupplementAgreementRes.noNeedSupplement());
//            }
//
//            Integer orderType = orderEntity.getOrderType();
//            if (orderType != 0 && orderType != 1) {
//                log.info("非月子订单，不用校验 {}", orderEntity.getOrderId());
//                return Result.success(CheckSupplementAgreementRes.noNeedSupplement());
//            }
//
//            Integer orderId = orderEntity.getOrderId();
//
//            //看下门店合同是否必签
////            Set<Integer> noNeedSignStoreIds = ruleLinkClient.getNoNeedSignStoreIds();
////            if (noNeedSignStoreIds.contains(orderEntity.getStoreId())) {
////                return Result.success(CheckSupplementAgreementRes.noNeedSupplement());
////            }
//
//            // 2. 根据订单的basicUid查询客户信息
//            List<TabClientPO> tabClientPOS = tabClientManager.listByBasicUid(orderEntity.getBasicUid());
//            if (CollectionUtil.isEmpty(tabClientPOS)) {
//                throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "客户信息不存在");
//            }
//
//            log.info("订单{}的客户信息：{}", req.getOrderId(), JSONObject.toJSONString(tabClientPOS));
//
//            // 3. 构造合同查询条件，查询已签署的合同
//            ContractQueryV4 contractQueryV4 = new ContractQueryV4();
//            contractQueryV4.setContractStatus(Collections.singletonList(ContractStatusEnum.SIGNED.code()));
//            contractQueryV4.setClientIdList(tabClientPOS.stream().map(tabClientPO -> tabClientPO.getId().intValue()).collect(Collectors.toList()));
//
//            List<OrderContractSignRecordVO> orderContractSignRecordVOS = contractManager.queryByV4(contractQueryV4);
//
//            // 4. 筛选母婴月子类合同，判断signerEntityName
//            List<OrderContractSignRecordVO> contractSignRecordVOS = orderContractSignRecordVOS.stream()
//                    .filter(contract -> isMaternityContract(contract.getTemplateContractType()) && Objects.equals(contract.getContractStatus(), ContractStatusEnum.SIGNED.code()))
//                    .sorted(Comparator.comparing(OrderContractSignRecordVO::getCreatedAt).reversed()).collect(Collectors.toList());
//
//            if (CollectionUtil.isEmpty(contractSignRecordVOS)) {
//                log.info("用户 {} 未签署过主合同", orderEntity.getBasicUid());
//                return Result.success(CheckSupplementAgreementRes.noNeedSupplement());
//            }
//
//            OrderContractSignRecordVO orderContractSignRecordVO = contractSignRecordVOS.get(0);
//            if ("杭州贝康健康科技集团有限公司".equals(orderContractSignRecordVO.getSignerEntityName())) {
//                return Result.success(CheckSupplementAgreementRes.noNeedSupplement());
//            }
//
//            // 查询有没有签过补充协议
//            List<ContractSignAgreementVO> agreementVOS = contractManager.getAgreementByOrderId(orderId.longValue());
//
//            ContractSignAgreementVO agreementVO = agreementVOS.stream().filter(a -> a.getName().contains("主体变更补充协议")).findFirst().orElse(null);
//
//            if (Objects.nonNull(agreementVO) && Objects.equals(agreementVO.getState(), ContractStatusEnum.SIGNED.code())) {
//                return Result.success(CheckSupplementAgreementRes.noNeedSupplement());
//            }
//            CheckSupplementAgreementRes data = CheckSupplementAgreementRes.needSupplement("需要签订补充协议，已创建一份补充协议");
//            if (Objects.nonNull(agreementVO) && (Objects.equals(agreementVO.getState(), ContractStatusEnum.NOT_SIGNED.code())
//                    || Objects.equals(agreementVO.getState(), ContractStatusEnum.WAITING.code())))
//            {
//                // 判断补充协议创建时间是否是当天
//                LocalDate localDate = LocalDateTime.now().toLocalDate();
//                LocalDate localDate1 = LocalDateTime.ofEpochSecond(agreementVO.getGmtCreate().getTime() / 1000, 0, ZoneOffset.UTC).toLocalDate();
//                if (localDate.equals(localDate1)) {
//                    data.setOrderId(orderId);
//                    return Result.success(data);
//                }
//                log.info("补充协议生成时间不是当前，取消之前的补充协议重新签署");
//                // 补充协议未签署，生成一个新的
//                contractManager.cancelSupplement(agreementVO.getId());
//            }
//
//            // 5. 根据判断结果处理
//            ContractSignRecordVO2 supplementAgreement = createSupplementAgreement(orderEntity, orderContractSignRecordVO);
//            OrderContractSignRecordVO supplementAgreement2 = appMonthContractSignRecordConverter.signRecord2OrderSign(supplementAgreement);
//            data.setSupplementAgreement(supplementAgreement2);
//
//            data.setOrderId(orderId);
//            return Result.success(data);
//        } catch (BusinessException e) {
//            log.error("检查补充协议业务异常，订单ID：{}", req.getOrderId(), e);
//            return Result.failed(e.getCode(), e.getMessage());
//        } catch (Exception e) {
//            log.error("检查补充协议系统异常，订单ID：{}", req.getOrderId(), e);
//            return Result.failed("检查补充协议失败: " + e.getMessage());
//        }
    }

    /**
     * 判断是否为母婴月子类合同
     */
    private boolean isMaternityContract(Integer templateContractType) {
        return TemplateContractTypeEnum.YZ_SAINTBELLA.code().equals(templateContractType) || TemplateContractTypeEnum.YZ_BABYBELLA.code().equals(templateContractType) || TemplateContractTypeEnum.YZ_SMALL.code().equals(templateContractType);
    }

    /**
     * 创建补充协议
     */
    private ContractSignRecordVO2 createSupplementAgreement(HeOrderEntity orderEntity, OrderContractSignRecordVO orderContractSignRecordVO) {


        Integer storeId = orderEntity.getStoreId();
        CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(storeId);
        String cityCode = cfgStoreEntity.getCityCode();

        HeAddressPO heAddressPO = heAddressService.selectById(Integer.parseInt(cityCode));
        String cityName = "杭州市";
        if (Objects.nonNull(heAddressPO)) {
            cityName = heAddressPO.getName();
        }

        //TODO 得到城市
        Integer clientUid = orderEntity.getClientUid();
        Integer orderId = orderEntity.getOrderId();
        OrderMonthClientReq clientInfoById = tabClientManager.getClientInfoById(clientUid);
        ContractCreateV3Req contractCreateV3Req = new ContractCreateV3Req();
        contractCreateV3Req.setContractName("主体变更补充协议");
        contractCreateV3Req.setContractTemplateId(499L);
        contractCreateV3Req.setContractType(ContractTypeEnum.ESIGN_TYPE.code());
        contractCreateV3Req.setTemplateType(TemplateContractTypeEnum.SUPPLIMENT.code());
        contractCreateV3Req.setGuideId(orderId.longValue());
        contractCreateV3Req.setClientUid(orderEntity.getClientUid());
        contractCreateV3Req.setStoreId(orderEntity.getStoreId().longValue());
        contractCreateV3Req.setStaffId(orderEntity.getStaffId());
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("contract_no", GeneratorUtil.contractNo("STBELLA"));
        LocalDateTime signTime = orderContractSignRecordVO.getSignTime();
        //createAt转换成年月日的字符串
        paramsMap.put("sign_time_str1", signTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        paramsMap.put("sign_time_str2", DateUtils.FormatDate(new Date()));
        paramsMap.put("pageNum", "2");
        paramsMap.put("contract_no1", orderContractSignRecordVO.getContractNo());
        paramsMap.put("client_name", clientInfoById.getName());
        paramsMap.put("company_name", orderContractSignRecordVO.getSignerEntityName());
        paramsMap.put("city", cityName);
        contractCreateV3Req.setParamsMap(paramsMap);
        Result<ContractSignRecordVO2> contract = contractManager.createContract(contractCreateV3Req);
        log.info("已为订单{}创建补充协议,创建结果为{}", orderId, JSONObject.toJSONString(contract));
        return contract.getData();
    }

    public static void main(String[] args) {
        LocalDate localDate = LocalDateTime.now().toLocalDate();
        LocalDate localDate1 = LocalDateTime.ofEpochSecond(new Date().getTime() / 1000, 0, ZoneOffset.UTC).toLocalDate();

        System.out.printf("localDate: %s, localDate1: %s", localDate, localDate1);
        System.out.printf("localDate: %s", DateUtils.FormatDate(new Date()));
    }
}
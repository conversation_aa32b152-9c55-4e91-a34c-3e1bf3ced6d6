package com.stbella.order.server.convert;

import com.stbella.order.server.config.MappingConfig;
import com.stbella.order.server.order.nutrition.dto.NutritionExtensionListDTO;
import com.stbella.order.server.order.nutrition.entity.NutritionExtensionCostPO;
import com.stbella.order.server.order.nutrition.entity.NutritionExtensionTypePO;
import com.stbella.order.server.order.nutrition.response.ExtensionChannelListVO;
import com.stbella.order.server.order.nutrition.response.ExtensionListDetailVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description: OrderNutritionConvert
 * @date 2022/1/12 2:30 下午
 */
@Mapper(config = MappingConfig.class)
public interface ExtensionConvert {

    List<ExtensionListDetailVO> listExtensionListDTO2DetailVO(List<NutritionExtensionListDTO> list);

    List<NutritionExtensionListDTO> listPO2DTO(List<NutritionExtensionCostPO> list);

    List<ExtensionChannelListVO> listTypePO2VO(List<NutritionExtensionTypePO> list);
}

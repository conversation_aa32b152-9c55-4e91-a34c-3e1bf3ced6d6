package com.stbella.order.server.consumer;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.financial.enums.CheckBillStatusEnum;
import com.stbella.financial.message.CheckBillMessage;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.order.month.entity.UserEntity;
import com.stbella.order.domain.repository.OrderRefundRepository;
import com.stbella.order.domain.repository.UserRepository;
import com.stbella.order.server.context.component.processor.pay.OfflineCallbackProcessor;
import com.stbella.order.server.order.month.enums.PayRecordEnum;
import com.stbella.order.server.order.month.service.OrderPayV2Service;
import com.stbella.order.server.order.pay.OfflinePayAuditV2Request;
import com.stbella.order.server.strategy.refund.RefundStrategyFactory;
import com.stbella.order.server.utils.AccountTypeUtil;
import com.stbella.pay.server.entity.mq.RefundNotifyMq;
import com.stbella.pulsar.consumer.PulsarConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.Message;
import org.apache.pulsar.client.api.SubscriptionType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
public class PayOfflineNotifyConsumer {

    @Resource
    private RefundStrategyFactory refundStrategyFactory;
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private UserRepository userRepository;
    @Resource
    OfflineCallbackProcessor offlineCallbackProcessor;

    /**
     * 线下支付通知MQ消费
     */
    @PulsarConsumerListener(
            destination = "persistent://pulsar-44k4paxzz5vg/nameSpace/financial_bill_check",
            subscriptionName = "financial_bill_check",
            subscriptionType = SubscriptionType.Shared
    )
    public void getMsg(Message message) {
        String messageId = String.valueOf(message.getMessageId());
        String messageData = new String(message.getData());
        try {
            log.info("线下支付通知MQ消费 ===>> messageId={}, messageData={}", messageId, messageData);

            CheckBillMessage checkBillMessage = JSONObject.parseObject(messageData, CheckBillMessage.class);
            if (!Arrays.asList(CheckBillStatusEnum.VOID.getCode(), CheckBillStatusEnum.RECONCILED.getCode())
                    .contains(checkBillMessage.getCheckBillStatus())) {
                log.info("线下支付通知MQ消费 ===>> messageId={}, ignore=true", messageId);
                return;
            }

            // 线下支付退款确认
            if (checkBillMessage.getTradeType() == 1) {
                HeOrderRefundEntity refundEntity = orderRefundRepository.getOneByRefundOrderSn(checkBillMessage.getRefundId());
                PayRecordEnum payRecordEnum = AccountTypeUtil.getAccountTypeByLocalTransactionalNo(checkBillMessage.getRefundId());
                RefundNotifyMq refundNotifyMq = new RefundNotifyMq();
                refundNotifyMq.setRefundType(refundEntity.getRefundType());
                refundNotifyMq.setRefundStatus(checkBillMessage.getCheckBillStatus().equals(CheckBillStatusEnum.VOID.getCode()) ? 2 : 1);
                refundNotifyMq.setTransactionalNo(checkBillMessage.getRefundId());
                refundNotifyMq.setOutRefundNo(String.valueOf(refundEntity.getId()));
                refundNotifyMq.setRefundAmount(checkBillMessage.getTradeAmt());
                refundNotifyMq.setSuccessTime(checkBillMessage.getTradeDate());
                refundStrategyFactory.executeRefundStrategy(payRecordEnum, refundNotifyMq);
            }

            // 线下支付审批
            if (checkBillMessage.getTradeType() == 2) {
                Long operatorUid = checkBillMessage.getOperatorUId();
                Integer auditBasicId = null;
                if (Objects.nonNull(operatorUid)) {
                    UserEntity userEntity = userRepository.queryById(operatorUid.intValue());
                    auditBasicId = Optional.ofNullable(userEntity).map(UserEntity::getBasicUid).orElse(null);
                }
                OfflinePayAuditV2Request offlinePayAuditV2Request = new OfflinePayAuditV2Request()
                        .setIncomeId(ObjectUtil.isNull(checkBillMessage.getPayRecordId()) ? null : checkBillMessage.getPayRecordId().intValue())
                        .setTransactionalNo(checkBillMessage.getChannelSerialNumber())
                        .setIncomeFinance(ObjectUtil.isNull(checkBillMessage.getTradeAmt()) ? null : checkBillMessage.getTradeAmt().multiply(BigDecimal.valueOf(100)).intValue())
                        .setIncomeFinanceTime(ObjectUtil.isNull(checkBillMessage.getTradeDate()) ? 0 : (int) (checkBillMessage.getTradeDate().getTime() / 1000))
                        .setAgree(Objects.equals(checkBillMessage.getCheckBillStatus(), CheckBillStatusEnum.RECONCILED.getCode()))
                        .setAuditBasicId(auditBasicId)
                        .setAuditRemark(checkBillMessage.getRemark());

                offlineCallbackProcessor.run(offlinePayAuditV2Request);
            }

            log.info("线下支付通知MQ消费成功 ===>> messageId={}, success=true", messageId);
        } catch (Exception e) {
            log.error(String.format("线下支付通知MQ消费异常 ===>> messageId=%s", messageId), e);
            throw new BusinessException(ResultEnum.MESSAGE_CONSUMPTION_ERROR, e.getMessage());
        }
    }


    public void getMsg(CheckBillMessage checkBillMessage) {
        if (!Arrays.asList(CheckBillStatusEnum.VOID.getCode(), CheckBillStatusEnum.RECONCILED.getCode())
                .contains(checkBillMessage.getCheckBillStatus())) {
            log.info("线下支付通知MQ消费 ===>> messageId={}, ignore=true", checkBillMessage);
            return;
        }
        // 线下支付退款确认
        if (checkBillMessage.getTradeType() == 1) {
            HeOrderRefundEntity refundEntity = orderRefundRepository.getOneByRefundOrderSn(checkBillMessage.getRefundId());
            PayRecordEnum payRecordEnum = AccountTypeUtil.getAccountTypeByLocalTransactionalNo(checkBillMessage.getRefundId());
            RefundNotifyMq refundNotifyMq = new RefundNotifyMq();
            refundNotifyMq.setRefundType(refundEntity.getRefundType());
            refundNotifyMq.setRefundStatus(checkBillMessage.getCheckBillStatus().equals(CheckBillStatusEnum.VOID.getCode()) ? 2 : 1);
            refundNotifyMq.setTransactionalNo(checkBillMessage.getRefundId());
            refundNotifyMq.setOutRefundNo(String.valueOf(refundEntity.getId()));
            refundNotifyMq.setRefundAmount(checkBillMessage.getTradeAmt());
            refundNotifyMq.setSuccessTime(checkBillMessage.getTradeDate());
            refundStrategyFactory.executeRefundStrategy(payRecordEnum, refundNotifyMq);
        }
    }

}

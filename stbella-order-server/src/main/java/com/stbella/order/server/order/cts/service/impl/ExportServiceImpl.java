package com.stbella.order.server.order.cts.service.impl;

import cn.hutool.core.date.DateUtil;
import com.stbella.order.server.order.cts.mapper.ExportMapper;
import com.stbella.order.server.order.cts.service.ExportService;
import com.stbella.order.server.order.cts.temp.po.*;
import com.stbella.order.server.utils.ExcelExportUtility;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class ExportServiceImpl implements ExportService {

    @Resource
    private ExportMapper exportMapper;

    @Override
    public void ctsExportOrder(Date start, Date end, HttpServletResponse response) {

        List<PayDTO> payDTOList = new ArrayList<>();
        payDTOList.add(new PayDTO("订单编号", "订单类型", "销售名字", "雇主名字", "育婴师名字", "门店名称", "订单总金额", "创建时间", "支付金额", "支付时间", "订单首次支付时间", "审核状态"));
        payDTOList.addAll(exportMapper.ctsPay(start, end));


        List<RefundDTO> refundDTOList = new ArrayList<>();
        refundDTOList.add(new RefundDTO("订单编号", "订单类型", "区域", "销售名字", "雇主名字", "育婴师名字", "实际退款时间", "退款金额", "退款方式", "属性", "订单首次支付时间"));
        refundDTOList.addAll(exportMapper.ctsRefund(start, end));


        List<ComboDTO> comboDTOList = new ArrayList<>();
        comboDTOList.add(new ComboDTO("订单编号", "订单类型", "分站名称", "销售名称", "雇主名称", "套餐等级", "签约时长（天）", "上户育婴师", "育婴师工资", "订单总金额", "折扣", "已付金额", "创建时间", "首次支付时间", "属性", "来源渠道", "门店编号", "门店名称", "合同开始时间", "合同结束时间"));
        comboDTOList.addAll(exportMapper.ctsCombo(start, end));


        List<TrainPayDTO> trainPayDTOList = new ArrayList<>();
        trainPayDTOList.add(new TrainPayDTO("订单编号", "订单类型", "销售名字", "雇主名字", "育婴师名字", "门店名称", "订单总金额", "创建时间", "支付金额", "支付时间", "订单首次支付时间", "审核状态"));
        trainPayDTOList.addAll(exportMapper.ctsTrainPay(start, end));

        try {
            String fileName = "到家-" + DateUtil.format(start, "MMdd") + "-" + DateUtil.format(end, "MMdd");
            ExcelExportUtility.export(payDTOList, refundDTOList, comboDTOList, trainPayDTOList, fileName + ".xlsx", response);
        } catch (IOException e) {
            log.error("导出到家数据失败", e);
        }


    }

    @Override
    public void payRecordExport(Date start, Date end, HttpServletResponse response) {


        List<ExportPaymentFlowDTO> paymentFlowDTOArrayList = new ArrayList<>();
        paymentFlowDTOArrayList.add(new ExportPaymentFlowDTO("订单编号", "到家系统流水号", "支付宝微信线下汇款流水号", "订单类型", "支退金额", "收款退款类型", "支付类型", "微信支付宝账号", "财务审核状态", "到账时间", "分站", "套餐名称", "客户姓名", "客户手机号", "归属销售", "订单创建时间 ", "合同金额", "育婴师工资", "服务费", "是否续签"));
        paymentFlowDTOArrayList.addAll(exportMapper.ctsPaymentFlow(start, end));

        try {
            String fileName = "到家-支付流水" + DateUtil.format(start, "MMdd") + "-" + DateUtil.format(end, "MMdd");
            ExcelExportUtility.exportPaymentFlow(paymentFlowDTOArrayList, fileName + ".xlsx", response);
            System.out.println("Excel file created successfully.");
        } catch (IOException e) {
            System.out.println("Error creating Excel file: " + e.getMessage());
        }
    }
}

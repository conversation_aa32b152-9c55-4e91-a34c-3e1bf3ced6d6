package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.production.OrderGiftExtendTypeEnum;
import com.stbella.order.common.enums.production.OrderProductionVerificationStateEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundDisabledProductEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.order.production.OrderGiftExtendEntity;
import com.stbella.order.domain.order.production.OrderProductionExtendEntity;
import com.stbella.order.domain.repository.HeOrderRefundDisabledProductRepository;
import com.stbella.order.domain.repository.OrderGiftExtendRepository;
import com.stbella.order.domain.repository.OrderProductionExtendRepository;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
@SnowballComponent(name = "审批通过后失效产康商品", desc = "审批通过后失效产康商品")
public class OrderRefundAfterDisabledCKGoodsAssemblerTEMP implements IExecutableAtom<FlowContext> {


    @Resource
    private OrderGiftExtendRepository orderGiftExtendRepository;
    @Resource
    private OrderProductionExtendRepository orderProductionExtendRepository;
    @Resource
    private HeOrderRefundDisabledProductRepository orderRefundDisabledProductRepository;


    @Override
    public boolean condition(FlowContext context) {
        return (boolean) context.getAttribute(OtherConstant.CONTINUE);
    }

    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        //主退款
        HeOrderRefundEntity refundEntity = bizContext.getAttribute(HeOrderRefundEntity.class);
        /*
        //订单类型是产康 && 有完款时间 &&  退货退款 || 仅退款
        if (orderEntity.getOrderType().equals(OmniOrderTypeEnum.PRODUCTION_ORDER.getCode()) &&
                ObjectUtil.isNotEmpty(orderEntity.getPayFirstTime()) &&
                orderEntity.getPayFirstTime() > 0 &&
                (refundEntity.getRefundNature().equals(OrderRefundNatureEnum.RETURN_AND_REFUND.code()) ||
                        refundEntity.getRefundNature().equals(OrderRefundNatureEnum.ONLY_REFUND.code()))
        ) {
            //失效这个订单所有的产康资产
            orderGiftExtendRepository.disabledAllProductService(orderEntity.getOrderId());
            orderProductionExtendRepository.disabledAllProductService(orderEntity.getOrderId());
        }*/


        //请求参数
        CreateRefundReq attribute = bizContext.getAttribute(CreateRefundReq.class);
        //退款商品信息
        List<CreateRefundReq.GoodsInfo> goodsInfoList = attribute.getGoodsInfoList();

        //礼赠的产康
        List<OrderGiftExtendEntity> orderGiftExtendEntityList = orderGiftExtendRepository.getByOrderId(orderEntity.getOrderId());
        //购买的产康
        List<OrderProductionExtendEntity> orderProductionExtendEntities = orderProductionExtendRepository.queryByOrderId(orderEntity.getOrderId());

        List<HeOrderRefundDisabledProductEntity> orderRefundDisabledProductEntityList = new ArrayList<>();


        Map<Integer, List<CreateRefundReq.GoodsInfo>> groupByGoodsId = goodsInfoList.stream().collect(Collectors.groupingBy(CreateRefundReq.GoodsInfo::getGoodsId));

        for (Integer goodsId : groupByGoodsId.keySet()) {

            List<CreateRefundReq.GoodsInfo> goodsInfos = groupByGoodsId.get(goodsId);

            Integer refundNum = 0;

            if (CollectionUtil.isNotEmpty(goodsInfos)) {
                for (CreateRefundReq.GoodsInfo goodsInfo : goodsInfos) {
                    refundNum += goodsInfo.getRefundNum();
                }
            }

            CreateRefundReq.GoodsInfo goodsInfo = goodsInfos.get(0);

            if (ObjectUtil.isNotEmpty(refundNum) && refundNum > 0) {
                //礼赠
                List<OrderGiftExtendEntity> orderGiftExtendEntities = orderGiftExtendEntityList.stream().filter(b ->
                        b.getType().equals(OrderGiftExtendTypeEnum.INDUSTRIAL_HEALTH_SERVICE.code()) &&
                                !b.getVerificationStatus().equals(OrderProductionVerificationStateEnum.WRITTEN_OFF.code())
                                && b.getGoodsId().equals(goodsInfo.getGoodsId()) && b.getSkuId().equals(goodsInfo.getSkuId())
                ).collect(Collectors.toList());
                //购买的
                List<OrderProductionExtendEntity> productionExtendEntities = orderProductionExtendEntities.stream()
                        .filter(o -> !o.getVerificationStatus().equals(OrderProductionVerificationStateEnum.WRITTEN_OFF.code())
                                && o.getGoodsId().equals(goodsInfo.getGoodsId()) && o.getSkuId().equals(goodsInfo.getSkuId())
                        ).collect(Collectors.toList());

                if (CollectionUtil.isNotEmpty(orderGiftExtendEntities) && CollectionUtil.isNotEmpty(productionExtendEntities)) {
                    if (orderGiftExtendEntities.size() <= refundNum) {
                        orderGiftExtendEntities.forEach(f -> {
                            getHeOrderRefundDisabledProductEntity(orderEntity.getOrderId(), refundEntity.getId(), 1, f.getId(), f.getStatus(), orderRefundDisabledProductEntityList);
                            f.setStatus(2);
                        });


                        refundNum = refundNum - orderGiftExtendEntities.size();
                        if (refundNum > 0) {
                            productionExtendEntities.subList(0, refundNum).forEach(f -> {
                                getHeOrderRefundDisabledProductEntity(orderEntity.getOrderId(), refundEntity.getId(), 0, f.getId(), f.getStatus(), orderRefundDisabledProductEntityList);
                                f.setStatus(2);
                            });
                        }
                    } else {
                        orderGiftExtendEntities.subList(0, refundNum).forEach(f -> {
                            getHeOrderRefundDisabledProductEntity(orderEntity.getOrderId(), refundEntity.getId(), 1, f.getId(), f.getStatus(), orderRefundDisabledProductEntityList);
                            f.setStatus(2);
                        });
                    }
                } else {
                    if (CollectionUtil.isNotEmpty(orderGiftExtendEntities)) {
                        if (orderGiftExtendEntities.size() <= refundNum) {
                            orderGiftExtendEntities.forEach(f -> {
                                getHeOrderRefundDisabledProductEntity(orderEntity.getOrderId(), refundEntity.getId(), 1, f.getId(), f.getStatus(), orderRefundDisabledProductEntityList);
                                f.setStatus(2);
                            });
                        } else {
                            orderGiftExtendEntities.subList(0, refundNum).forEach(f -> {
                                getHeOrderRefundDisabledProductEntity(orderEntity.getOrderId(), refundEntity.getId(), 1, f.getId(), f.getStatus(), orderRefundDisabledProductEntityList);
                                f.setStatus(2);
                            });
                        }
                    }

                    if (CollectionUtil.isNotEmpty(productionExtendEntities)) {
                        if (productionExtendEntities.size() <= refundNum) {
                            productionExtendEntities.forEach(f -> {
                                getHeOrderRefundDisabledProductEntity(orderEntity.getOrderId(), refundEntity.getId(), 0, f.getId(), f.getStatus(), orderRefundDisabledProductEntityList);
                                f.setStatus(2);
                            });
                        } else {
                            productionExtendEntities.subList(0, refundNum).forEach(f -> {
                                getHeOrderRefundDisabledProductEntity(orderEntity.getOrderId(), refundEntity.getId(), 0, f.getId(), f.getStatus(), orderRefundDisabledProductEntityList);
                                f.setStatus(2);
                            });
                        }
                    }
                }
            }
        }


        orderGiftExtendRepository.batchUpdateById(orderGiftExtendEntityList);
        orderProductionExtendRepository.saveOrUpdateBatch(orderProductionExtendEntities);
        orderRefundDisabledProductRepository.saveList(orderRefundDisabledProductEntityList);
    }

    private HeOrderRefundDisabledProductEntity getHeOrderRefundDisabledProductEntity(Integer orderId, Integer refundId, Integer productType, Integer productId, Integer sourceStatus, List<HeOrderRefundDisabledProductEntity> orderRefundDisabledProductEntityList) {
        HeOrderRefundDisabledProductEntity productEntity = new HeOrderRefundDisabledProductEntity();
        productEntity.setRefundId(orderId);
        productEntity.setOrderId(refundId);
        productEntity.setProductType(productType);
        productEntity.setProductId(productId);
        productEntity.setSourceStatus(sourceStatus);
        orderRefundDisabledProductEntityList.add(productEntity);
        return productEntity;
    }

}

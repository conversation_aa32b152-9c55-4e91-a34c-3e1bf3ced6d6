package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.core.OrderRefundGoodsStatusEnum;
import com.stbella.order.common.enums.month.RefundRecordPayStatusEnum;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundGoodsEntity;
import com.stbella.order.domain.order.service.OrderRefundDomainService;
import com.stbella.order.domain.repository.HeOrderRefundGoodsRepository;
import com.stbella.order.domain.repository.OrderRefundRepository;
import com.stbella.order.server.utils.wangdian.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 退款审批通过
 * 更新仅退数量的退款的状态为退款成功
 */
@Component
@Slf4j
@SnowballComponent(name = "审批结束，处理仅退款数量的", desc = "审批结束，处理仅退款数量的")
public class OrderRefundAfterApproveOnlyNumAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private HeOrderRefundGoodsRepository orderRefundGoodsRepository;
    @Resource
    private OrderRefundDomainService orderRefundDomainService;

    @Override
    public boolean condition(FlowContext context) {
        boolean continueBoolean = (boolean) context.getAttribute(OtherConstant.CONTINUE);
        Boolean isRefund = (Boolean) context.getAttribute(OtherConstant.IS_REFUND);
        return continueBoolean && isRefund;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void run(FlowContext bizContext) {
        //主退款
        HeOrderRefundEntity refundEntity = bizContext.getAttribute(HeOrderRefundEntity.class);
        log.info("主退款refundSn为 {} 处理仅退数量的数据，refundEntity is{}", refundEntity.getRefundOrderSn(), JSONObject.toJSONString(refundEntity));
        //获取子退款
        List<HeOrderRefundEntity> childRefundEntity = bizContext.getListAttribute(HeOrderRefundEntity.class);
        //获取退款的商品
        List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList = orderRefundGoodsRepository.queryByRefundOrderSn(refundEntity.getRefundOrderSn());
        //只退数量的退款商品
        List<HeOrderRefundGoodsEntity> onlyNum = orderRefundGoodsEntityList.stream().filter(o -> o.getRefundAmount() == 0).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(onlyNum)) {
            for (HeOrderRefundGoodsEntity heOrderRefundGoodsEntity : onlyNum) {
                heOrderRefundGoodsEntity.setStatus(OrderRefundGoodsStatusEnum.SUCCESS.code());
            }
            //获取仅退数量的退款记录
            List<HeOrderRefundEntity> orderRefundEntityList = childRefundEntity.stream().filter(r -> StringUtils.isEmpty(r.getIncomeSn())
                    && r.getRefundType().equals(OmniPayTypeEnum.OTHER.getCode())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(orderRefundEntityList)) {
                orderRefundEntityList.forEach(o -> {
                    o.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode());
                    o.setFinishAt(System.currentTimeMillis() / 1000);
                });
            }
        }
        orderRefundRepository.batchUpdateById(childRefundEntity);
        orderRefundGoodsRepository.updateList(onlyNum);
        orderRefundDomainService.updateParentStatus(refundEntity.getId());

        bizContext.setAttribute(OtherConstant.ORDER_REFUND_GOODS_LIST, orderRefundGoodsEntityList);
        bizContext.setAttribute(OtherConstant.ONLY_REFUND_NUM, onlyNum);
    }


}

package com.stbella.order.server.context.component.ui;

import cn.hutool.core.lang.Assert;
import com.google.common.collect.Maps;
import com.stbella.order.common.enums.month.OrderButtonEnum;
import com.stbella.order.common.enums.month.OrderStatusV2Enum;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.month.entity.MonthContractSignRecordEntity;
import com.stbella.order.domain.order.production.GoodsCategoryBackEntity;
import com.stbella.order.domain.repository.GoodsCategoryBackRepository;
import com.stbella.order.domain.repository.OrderGoodsRepository;
import com.stbella.order.server.order.month.res.OrderOperateButton;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import com.stbella.store.server.order.enums.OrderTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-08-12  16:28
 * @Description: 发送分娩喜报
 */
@Component
@Slf4j
public class SendBirthNewsOperate implements OrderOperate{

    @Resource
    private OrderGoodsRepository orderGoodsRepository;
    @Resource
    private GoodsCategoryBackRepository goodsCategoryBackRepository;

    @Resource
    private RuleLinkClient ruleLinkClient;

    @Override
    public void setPriority(int priority) {

    }

    @Override
    public int getPriority() {
        return 6;
    }

    /**
     * 生成订单操作按钮
     * @return
     */
    @Override
    public OrderOperateButton generateButton(OrderOperateContext context) {
        if (OrderTypeEnum.ORDER_TYPE_PRODUCTION.getCode().equals(context.getOrder().getOrderType())){
            return null;
        }
        if (context.getOrder().getOrderStatus().equals(OrderStatusV2Enum.NONE.getCode()) || context.getOrder().getOrderStatus().equals(OrderStatusV2Enum.TO_STAY_IN.getCode()) || context.getOrder().getOrderStatus().equals(OrderStatusV2Enum.STAY_IN.getCode())) {
            ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req();
            executeRuleV2Req.setSceneCode("goodsBack");
            Map<String, Object> map = Maps.newHashMap();
            map.put("configCode", "monthServer");
            executeRuleV2Req.setFactObj(map);
            HitRuleVo listResult = ruleLinkClient.hitOneRule(executeRuleV2Req);
            Assert.isTrue(StringUtils.isNotEmpty(listResult.getSimpleRuleValue()), "规则引擎未配置");
            List<GoodsCategoryBackEntity> goodsCategoryBackEntities = goodsCategoryBackRepository.queryChildAndParentByParentId(Integer.valueOf(listResult.getSimpleRuleValue()));
            if (CollectionUtils.isNotEmpty(goodsCategoryBackEntities)){
                List<Long> idList = goodsCategoryBackEntities.stream().map(item ->Long.valueOf(item.getId())).collect(Collectors.toList());
                List<HeOrderGoodsEntity> orderGoodsByOrderIdList = orderGoodsRepository.getByCategoryBackIdList(idList, context.getOrder().getOrderId());
                if (CollectionUtils.isNotEmpty(orderGoodsByOrderIdList)) {
                    if (CollectionUtils.isEmpty(context.getMainContractList())){
                        return null;
                    }
                    MonthContractSignRecordEntity recordEntity = context.getMainContractList().stream().filter(item -> TemplateContractTypeEnum.YZ_SAINTBELLA.code().equals(item.getTemplateContractType()) && Objects.nonNull(item.getContractStatus()) && item.getContractStatus() == 2).findFirst().orElse(null);
                    if (Objects.nonNull(recordEntity)){
                        return OrderOperateButton.builder().code(getButtonEnum().getCode()).value("分娩喜报").build();
                    }
                }
            }
        }
        return null;
    }

    /**
     * 获取按钮枚举
     *
     * @return
     */
    @Override
    public OrderButtonEnum getButtonEnum() {
        return OrderButtonEnum.SEND_CHILDBIRTH_PROSPERITY;
    }

    @Override
    public int compareTo(@NotNull IPriority o) {
        return this.getPriority() - o.getPriority();
    }
}

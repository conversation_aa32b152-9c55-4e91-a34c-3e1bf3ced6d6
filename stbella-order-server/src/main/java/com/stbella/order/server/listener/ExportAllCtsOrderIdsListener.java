package com.stbella.order.server.listener;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.mail.MailUtils;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.server.listener.event.ExportAllCtsOrderIdsEvent;
import com.stbella.order.server.order.cts.excel.AllCtsOrderIdsExcel;
import com.stbella.order.server.order.cts.mapper.OrderCtsMapper;
import com.stbella.order.infrastructure.oss.OSSFactory;
import com.stbella.order.server.oss.response.OssUploadResponse;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 导出所有予家订单号
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ExportAllCtsOrderIdsListener {
    @Resource
    private OrderCtsMapper ctsMapper;

    @Resource
    private MailUtils mailUtils;

    @Resource
    RuleLinkClient ruleLinkClient;


    /**
     * 招行退款成功，开始处理业务逻辑
     * @param event
     */
    @Async
    @EventListener
    public void exportEvent(ExportAllCtsOrderIdsEvent event) {

        log.info("收到导出予家所有订单id事件{}", JSONUtil.toJsonStr(event));

        String fileName = "CtsOrderIds-"+ DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
        String path = "stbella/order/excel/" + fileName + ".xlsx";

        List<AllCtsOrderIdsExcel> allCtsOrderIdsExcels = ctsMapper.queryAllOrderIds();

        log.info("一共{}条，开始上传腾讯云服务器", allCtsOrderIdsExcels.size());
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        EasyExcel.write(byteArrayOutputStream, AllCtsOrderIdsExcel.class).sheet("sheet1").doWrite(allCtsOrderIdsExcels);

        InputStream inputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());

        OssUploadResponse ossUploadResponse = OSSFactory.build().upload(inputStream, path);
        if (!ossUploadResponse.isSuccess()) {
            log.info("上传腾讯云服务器失败,msg={}", ossUploadResponse.getMsg());
            throw new BusinessException(ResultEnum.FILE_ERROR.getCode(), "上传腾讯服务器失败");
        }

        log.info("上传腾讯云服务器成功, 开始发送邮件,path={}", ossUploadResponse.getUrl());
        Map<String, String> factMap = new HashMap<>();
        factMap.put("configCode","yj_all_order_receiver");
        ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req("systemConfig", factMap);

        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
        // simpleRuleValue 按逗号分成数组
        String[] to = hitRuleVo.getSimpleRuleValue().split(",");
        String subject = "予家所有订单id导出";
        String content = "请点击链接下载文件：<a href='"+ossUploadResponse.getUrl()+"'>" + ossUploadResponse.getUrl() + "</a>";

        mailUtils.sendSimpleMail(to, subject, content);


    }

}

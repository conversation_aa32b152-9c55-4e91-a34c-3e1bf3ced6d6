package com.stbella.order.server.order.month.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.stbella.core.base.Operator;
import com.stbella.core.base.StoreInfoDTO;
import com.stbella.core.result.Result;
import com.stbella.core.utils.sso.EmployeeTokenHelper;
import com.stbella.customer.server.ecp.entity.UserPO;
import com.stbella.financial.res.MainSupplierQueryVO;
import com.stbella.financial.res.SupplierPageVO;
import com.stbella.message.scene.req.SceneTriggerReq;
import com.stbella.order.common.enums.core.*;
import com.stbella.order.common.enums.month.RefundRecordPayStatusEnum;
import com.stbella.order.common.enums.order.CustomerComplaintsRefundEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.client.BrandClient;
import com.stbella.order.domain.client.MessageClient;
import com.stbella.order.domain.client.StoreGoodsClient;
import com.stbella.order.domain.order.entity.HeCartEntity;
import com.stbella.order.domain.order.entity.HeCartGoodsEntity;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.production.GoodsEntity;
import com.stbella.order.domain.order.production.GoodsSkuEntity;
import com.stbella.order.domain.repository.*;
import com.stbella.order.domain.repository.condition.StoreQueryCondition;
import com.stbella.order.server.config.DynamicConfig;
import com.stbella.order.server.convert.HeCustomerComplaintsServerConverter;
import com.stbella.order.server.manager.FinanceManager;
import com.stbella.order.server.manager.SsoManager;
import com.stbella.order.server.manager.TabClientManager;
import com.stbella.order.server.order.month.dto.PageDTO;
import com.stbella.order.server.order.month.enums.*;
import com.stbella.order.server.order.month.req.*;
import com.stbella.order.server.order.month.res.*;
import com.stbella.order.server.order.month.service.ComplaintsPicpService;
import com.stbella.order.server.order.month.service.OrderV3Service;
import com.stbella.order.server.order.order.res.OrderComplaintsExport;
import com.stbella.order.server.utils.*;
import com.stbella.platform.order.api.res.ExtraRefundTypeRes;
import com.stbella.sso.server.dingding.entity.DdDepartmentPO;
import com.stbella.sso.server.dingding.entity.DdEmployeePO;
import com.stbella.sso.therapist.api.TherapistQueryService;
import com.stbella.sso.therapist.req.TherapistDetailReq;
import com.stbella.sso.therapist.res.TherapistSelectOptionVo;
import com.stbella.store.core.enums.BrandTypeEnum;
import com.stbella.store.core.vo.res.store.BrandVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ComplaintsPicpServiceImpl implements ComplaintsPicpService {

    @Resource
    private HeCustomerComplaintsServerConverter heCustomerComplaintsServerConverter;

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private StoreRepository storeRepository;

    @Resource
    private DistrictRepository districtRepository;

    @Resource
    private HeCustomerComplaintsRepository heCustomerComplaintsRepository;

    @Resource
    private ClientRepository clientRepository;

    @Resource
    private BrandClient brandClient;

    @Resource
    private StoreGoodsClient storeClient;

    @Resource
    private DynamicConfig dynamicConfig;

    @Resource
    private HeCustomerComplaintsTypeRepository heCustomerComplaintsTypeRepository;

    @Resource
    private SsoManager ssoManager;

    @Resource
    private MessageClient messageClient;

    @Resource
    private TabClientManager tabClientManager;

    @Resource
    private OrderRefundRepository orderRefundRepository;

    @Resource
    private HeOrderRefundGoodsRepository orderRefundGoodsRepository;

    @Resource
    private OrderGoodsRepository orderGoodsRepository;

    @Resource
    private IncomePaidAllocationRepository incomePaidAllocationRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private FinanceManager financeManager;

    @Resource
    private HeCartRepository heCartRepository;

    @Resource
    private HeCartGoodsRepository heCartGoodsRepository;

    @Resource
    private GoodsRepository goodsRepository;

    @Resource
    private OrderProcessCheckUtil orderProcessCheckUtil;

    @Resource
    private ProductionAmountPayRepository productionAmountPayRepository;

    @Resource
    private OrderV3Service orderV3Service;

    @Resource
    private GoodsSkuRepository goodsSkuRepository;

    @DubboReference
    private TherapistQueryService therapistQueryService;

    @Override
    public PageDTO<WechatMyOrderNewVO> queryOrderList(ComplaintsOrderReq req) {

        WechatMyOrderQuery query = new WechatMyOrderQuery();
        query.setBasicUid(req.getBasicId().longValue());
        query.setOrderNo(req.getOrderNo());
        query.setPageNum(req.getPageNum());
        query.setPageSize(req.getPageSize());
        return orderV3Service.getOrderByCustomerBasicUid(query);
    }

    @Override
    public Boolean add(ComplaintsPicpAddReq req) {

        // 检查是否存在未完成的退款流程
        if (orderProcessCheckUtil.hasUnfinishedRefund(req.getOrderId())) {
            String description = orderProcessCheckUtil.getUnfinishedProcessDescription(req.getOrderId());
            log.warn("创建客诉工单失败，订单{}存在未完成的流程：{}", req.getOrderId(), description);
            throw new RuntimeException("当前订单" + description + "，请等待处理完成后再次尝试！");
        }

        HeOrderEntity heOrderEntity = orderRepository.queryOrderById(req.getOrderId());
        Assert.notNull(heOrderEntity, "订单不存在");
        CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(heOrderEntity.getStoreId());
        Assert.notNull(cfgStoreEntity, "门店不存在");
        HeCustomerComplaintsEntity heCustomerComplaintsEntity = heCustomerComplaintsServerConverter.customerComplaintsCreateReqToEntity(req);
        heCustomerComplaintsEntity.setOrderSn(heOrderEntity.getOrderSn());
        heCustomerComplaintsEntity.setOrderType(heOrderEntity.getOrderType());
        heCustomerComplaintsEntity.setStoreId(heOrderEntity.getStoreId());
        heCustomerComplaintsEntity.setClientUid(heOrderEntity.getClientUid());
        heCustomerComplaintsEntity.setComplaintStatus(CustomerComplaintsStatusEnum.WAIT_PROCESS.getCode());
        heCustomerComplaintsEntity.setCreatedAt(new Date());
        Long id = heCustomerComplaintsRepository.save(heCustomerComplaintsEntity);
        Assert.notNull(id, "保存失败");
        sendMsg(cfgStoreEntity, heOrderEntity, req.getOperator(), id);
        return true;
    }

    private void sendMsg(CfgStoreEntity cfgStoreEntity, HeOrderEntity heOrderEntity, Operator operator, Long id) {

        SceneTriggerReq req = new SceneTriggerReq();
        req.setRequestId("ComplaintsOrderPre" + id);
        req.setSceneId(dynamicConfig.getSceneId());
        req.setTargetList(Collections.singletonList(this.getMobile(cfgStoreEntity)));
        TabClientEntity tabClientEntity = clientRepository.getTabClientById(heOrderEntity.getClientUid());
        Map<String, String> contextData = Maps.newHashMap();
        contextData.put("customerPhone", tabClientEntity.getPhone());
        contextData.put("customerName", tabClientEntity.getName());
        contextData.put("orderNo", heOrderEntity.getOrderSn());
        contextData.put("creatorName", operator.getOperatorName());
        contextData.put("storeName", cfgStoreEntity.getStoreName());
        req.setContextData(contextData);
        Result<Long> longResult = messageClient.triggerScene(req);
        Assert.isTrue(longResult.getSuccess(), "贝康小助手消息发送失败");
    }

    private String getMobile(CfgStoreEntity cfgStoreEntity) {

        try {
            DdDepartmentPO ddDepartmentPO = ssoManager.queryDdDepartmentById(cfgStoreEntity.getHrDepartmentId());
            Assert.notNull(ddDepartmentPO, "部门不存在");
            DdEmployeePO ddEmployeePO = ssoManager.queryEmployeeById(ddDepartmentPO.getManagerId());
            Assert.notNull(ddEmployeePO, "员工不存在");
            return ddEmployeePO.getMobile();
        } catch (Exception e) {
            log.error(String.format("创建客诉单，获取门店负责人信息发生异常：%s", e.getMessage()), e);
            return dynamicConfig.getMobile();
        }
    }

    @Override
    public PageDTO<ComplaintsPicpPageVO> page(ComplaintsPicpPageReq req) {

        if (CollectionUtils.isEmpty(req.getComplaintsStatusList())) {
            req.setComplaintsStatusList(CustomerComplaintsStatusEnum.getPicpQueryStatusList());
        }
        if (StringUtils.isNotEmpty(req.getClientName()) || CollectionUtils.isNotEmpty(req.getCustomerSourceList()) || StringUtils.isNotEmpty(req.getPhone())) {
            List<TabClientEntity> tabClientEntityList = clientRepository.queryByUserIdOrNameOrSource(req.getClientName(), req.getPhone(), req.getCustomerSourceList());
            if (CollectionUtils.isEmpty(tabClientEntityList)) {
                return PageDTO.emptyList(req.getPageNum(), req.getPageSize());
            }
            List<Integer> clientUidList = tabClientEntityList.stream().map(TabClientEntity::getId).filter(Objects::nonNull).collect(Collectors.toList());
            req.setClientUidList(CollectionUtils.isEmpty(req.getClientUidList()) ? clientUidList : (List<Integer>) CollectionUtils.intersection(clientUidList, req.getClientUidList()));
            if (CollectionUtils.isEmpty(req.getClientUidList())) {
                return PageDTO.emptyList(req.getPageNum(), req.getPageSize());
            }
        }
        if (Objects.nonNull(req.getBasicUid())) {
            List<TabClientEntity> tabClientByBasicUid = clientRepository.getTabClientByBasicUid(req.getBasicUid());
            if (CollectionUtils.isEmpty(tabClientByBasicUid)) {
                return PageDTO.emptyList(req.getPageNum(), req.getPageSize());
            }
            List<Integer> clientUidList = tabClientByBasicUid.stream().map(TabClientEntity::getId).filter(Objects::nonNull).collect(Collectors.toList());
            req.setClientUidList(CollectionUtils.isEmpty(req.getClientUidList()) ? clientUidList : (List<Integer>) CollectionUtils.intersection(clientUidList, req.getClientUidList()));
            if (CollectionUtils.isEmpty(req.getClientUidList())) {
                return PageDTO.emptyList(req.getPageNum(), req.getPageSize());
            }
        }
        if (CollectionUtils.isNotEmpty(req.getStoreBrandList())) {
            List<Integer> storeIdByStoreBrandList = getStoreIdByStoreBrand(req.getStoreBrandList());
            if (CollectionUtils.isEmpty(storeIdByStoreBrandList)) {
                return PageDTO.emptyList(req.getPageNum(), req.getPageSize());

            }
            req.setStoreIdList(CollectionUtils.isEmpty(req.getStoreIdList()) ? storeIdByStoreBrandList : (List<Integer>) CollectionUtils.intersection(storeIdByStoreBrandList, req.getStoreIdList()));
            if (CollectionUtils.isEmpty(req.getStoreIdList())) {
                return PageDTO.emptyList(req.getPageNum(), req.getPageSize());
            }
        }

        if (CollectionUtils.isNotEmpty(req.getWarZoneList())) {
            List<CfgStoreEntity> cfgStoreEntitieList = storeRepository.queryStoreBaseByCondition(StoreQueryCondition.builder().warZones(req.getWarZoneList()).build());
            if (CollectionUtils.isEmpty(cfgStoreEntitieList)) {
                return PageDTO.emptyList(req.getPageNum(), req.getPageSize());
            }
            List<Integer> storeIdByWarZoneList = cfgStoreEntitieList.stream().map(CfgStoreEntity::getStoreId).filter(Objects::nonNull).collect(Collectors.toList());
            req.setStoreIdList(CollectionUtils.isEmpty(req.getStoreIdList()) ? storeIdByWarZoneList : (List<Integer>) CollectionUtils.intersection(storeIdByWarZoneList, req.getStoreIdList()));
            if (CollectionUtils.isEmpty(req.getStoreIdList())) {
                return PageDTO.emptyList(req.getPageNum(), req.getPageSize());
            }
        }
        if (CollectionUtils.isNotEmpty(req.getComplaintLevelList())) {
            req.setComplaintLevelList(this.getComplaintLevelList(req.getComplaintLevelList()));
        }

        if (StringUtils.isNotEmpty(req.getCreator())) {
            List<UserEntity> userEntities = userRepository.queryUserList(req.getCreator());
            req.setCreatorList(CollectionUtils.isEmpty(userEntities) ? Lists.newArrayList(-1) : userEntities.stream().map(UserEntity::getId).collect(Collectors.toList()));
        }
        if (req.getDateType() == 1 && Objects.nonNull(req.getBizTimeStart()) && Objects.nonNull(req.getBizTimeEnd())) {
            req.setBizDateStart(req.getBizTimeStart().getTime());
            req.setBizDateEnd(req.getBizTimeEnd().getTime());
        }
        //增加门店权限
        StoreInfoDTO storeInfoDTO = EmployeeTokenHelper.getCurrentEmployeeStoreInfo();
        log.info("当前登录人门店信息：{}", JSONObject.toJSONString(storeInfoDTO));
        if (!storeInfoDTO.getHasAllStoreInfo()) {
            List<Integer> storeIdList = storeInfoDTO.getStoreIdList();
            if (CollectionUtil.isEmpty(storeIdList)) {
                req.setStoreIdList(Collections.singletonList(-1));
            } else {
                if (CollectionUtils.isEmpty(req.getStoreIdList())) {
                    req.setStoreIdList(storeIdList);
                } else {
                    req.getStoreIdList().retainAll(storeIdList);
                }
            }
        }

        Page<HeCustomerComplaintsEntity> pageList = heCustomerComplaintsRepository.pageList(req);
        Map<String, String> warZoneMap = storeClient.getAttribute(dynamicConfig.getWarZone()).stream().collect(Collectors.toMap(SelectRespVO::getValue, SelectRespVO::getLabel, (v1, v2) -> v1));
        Map<Integer, CfgStoreEntity> storeNameMap = this.getStoreName(pageList.getRecords());
        List<HeOrderEntity> orderList = orderRepository.getByOrderList(pageList.getRecords().stream().map(HeCustomerComplaintsEntity::getOrderId).filter(Objects::nonNull).map(Math::toIntExact).collect(Collectors.toList()));

        List<Integer> clientUidList = orderList.stream().map(HeOrderEntity::getClientUid).collect(Collectors.toList());
        List<TabClientEntity> tabClientByIdList = clientRepository.getTabClientByIdList(clientUidList);
        Map<Integer, TabClientEntity> tabClientEntityMap = tabClientByIdList.stream().collect(Collectors.toMap(TabClientEntity::getId, entity -> entity, (v1, v2) -> v1));

        Map<Integer, HeOrderEntity> heOrderEntityMap = orderList.stream().collect(Collectors.toMap(HeOrderEntity::getOrderId, v -> v, (k1, k2) -> k1));
        Map<Integer, String> complaintLevelDescMap = getComplaintLevelMap(pageList.getRecords());

        List<UserPO> userPOList = tabClientManager.queryUserListByIds(pageList.getRecords().stream().map(HeCustomerComplaintsEntity::getCreator).filter(Objects::nonNull).map(Long::valueOf).collect(Collectors.toList()));
        Map<Long, String> userNameMap = userPOList.stream().collect(Collectors.toMap(UserPO::getId, UserPO::getName, (v1, v2) -> v1));

        Map<Integer, BigDecimal> compensatedGoodsAmountMap = getCompensatedGoodsAmountMap(pageList.getRecords());

        Map<Long, BigDecimal> orderRefundAmountMap = this.getOrderRefundAmount(pageList.getRecords(), orderList);

        return PageUtils.convert2PageDTO(pageList, temp -> this.convertEntityToPageVO(temp, storeNameMap, warZoneMap, heOrderEntityMap, tabClientEntityMap,
                complaintLevelDescMap, userNameMap, compensatedGoodsAmountMap, orderRefundAmountMap));
    }

    @Override
    public ComplaintsPicpInfoVO info(ComplaintsPicpInfoReq req) {

        HeCustomerComplaintsEntity heCustomerComplaintsEntity = heCustomerComplaintsRepository.selectById(req.getId());
        Assert.notNull(heCustomerComplaintsEntity, "客诉工单不存在");
        Assert.notNull(heCustomerComplaintsEntity.getOrderId(), "客诉工单对应的订单ID不存在");
        HeOrderEntity heOrderEntity = orderRepository.queryOrderById(heCustomerComplaintsEntity.getOrderId().intValue());
        Assert.notNull(heOrderEntity, "订单不存在");
        Assert.notNull(heOrderEntity.getBasicUid(), "订单对应的客户ID不存在");
        TabClientEntity tabClientById = clientRepository.getTabClientById(heOrderEntity.getClientUid());
        Assert.notNull(tabClientById, "客户不存在");
        CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(heOrderEntity.getStoreId());
        Assert.notNull(cfgStoreEntity, "门店不存在");
        Map<String, String> warZoneMap = storeClient.getAttribute(dynamicConfig.getWarZone()).stream().collect(Collectors.toMap(SelectRespVO::getValue, SelectRespVO::getLabel, (v1, v2) -> v1));
        Assert.notNull(heCustomerComplaintsEntity.getCreator(), "客诉工单创建人不存在");
        Map<Long, String> userNameMap = getUserNameMap(heCustomerComplaintsEntity);
        Map<Integer, String> complaintLevelDescMap = getComplaintLevelMap(Collections.singletonList(heCustomerComplaintsEntity));
        ComplaintsPicpInfoVO complaintsPicpInfoVO = new ComplaintsPicpInfoVO();
        complaintsPicpInfoVO.setOrderSn(heCustomerComplaintsEntity.getOrderSn());
        complaintsPicpInfoVO.setOrderType(heOrderEntity.getOrderType());
        complaintsPicpInfoVO.setOrderTypeStr(OmniOrderTypeEnum.getValueByCode(heCustomerComplaintsEntity.getOrderType()));
        complaintsPicpInfoVO.setBasicId(heOrderEntity.getBasicUid());
        complaintsPicpInfoVO.setClientUid(heOrderEntity.getClientUid());
        complaintsPicpInfoVO.setCustomerName(tabClientById.getName());
        complaintsPicpInfoVO.setCustomerMobile(tabClientById.getPhone());
        complaintsPicpInfoVO.setClientPhone(SensitiveInformationUtil.conductPhoneOrIdCardNo(tabClientById.getPhone()));
        complaintsPicpInfoVO.setWarZone(cfgStoreEntity.getWarzone());
        complaintsPicpInfoVO.setWarZoneDesc(Optional.ofNullable(complaintsPicpInfoVO.getWarZone()).isPresent() ? warZoneMap.get(String.valueOf(complaintsPicpInfoVO.getWarZone())) : StringUtils.EMPTY);
        complaintsPicpInfoVO.setBrandName(this.getBrandName(cfgStoreEntity.getType(), cfgStoreEntity.getChildType()));
        complaintsPicpInfoVO.setStoreId(cfgStoreEntity.getStoreId());
        complaintsPicpInfoVO.setStoreName(cfgStoreEntity.getStoreName());
        complaintsPicpInfoVO.setComplaintStatus(heCustomerComplaintsEntity.getComplaintStatus());
        complaintsPicpInfoVO.setComplaintStatusStr(CustomerComplaintsStatusEnum.getValueByCode(heCustomerComplaintsEntity.getComplaintStatus()));
        complaintsPicpInfoVO.setCreator(heCustomerComplaintsEntity.getCreator());
        complaintsPicpInfoVO.setCreatorStr(userNameMap.get(heCustomerComplaintsEntity.getCreator().longValue()));
        complaintsPicpInfoVO.setCreatedAt(DateUtils.formatDateTime(heCustomerComplaintsEntity.getCreatedAt()));
        complaintsPicpInfoVO.setEventTime(DateUtils.FormatDate(new Date(heCustomerComplaintsEntity.getEventTime())));
        complaintsPicpInfoVO.setEventLocation(heCustomerComplaintsEntity.getEventLocation());
        complaintsPicpInfoVO.setInvolvedPersons(heCustomerComplaintsEntity.getInvolvedPersons());
        complaintsPicpInfoVO.setCause(heCustomerComplaintsEntity.getCause());
        complaintsPicpInfoVO.setProcess(heCustomerComplaintsEntity.getProcess());
        complaintsPicpInfoVO.setResult(heCustomerComplaintsEntity.getResult());
        complaintsPicpInfoVO.setEvidence(heCustomerComplaintsEntity.getEvidence());
        complaintsPicpInfoVO.setComplaintLevel(heCustomerComplaintsEntity.getComplaintLevel());
        complaintsPicpInfoVO.setComplaintLevelStr(complaintLevelDescMap.get(heCustomerComplaintsEntity.getComplaintLevel()));

        if (Objects.nonNull(heCustomerComplaintsEntity.getComplaintLevel())) {
            HeCustomerComplaintsTypeEntity complaintsTypeEntity = heCustomerComplaintsTypeRepository.getOne(heCustomerComplaintsEntity.getComplaintLevel().longValue());
            complaintsPicpInfoVO.setComplaintParentLevel(complaintsTypeEntity.getParentId());
        }
        complaintsPicpInfoVO.setResponsibilityType(heCustomerComplaintsEntity.getResponsibilityType());
        complaintsPicpInfoVO.setResponsibilityTypeStr(CustomerComplaintsResponsibilityEnum.getValueByCode(heCustomerComplaintsEntity.getResponsibilityType()));
        complaintsPicpInfoVO.setPrimaryResponsiblePerson(heCustomerComplaintsEntity.getPrimaryResponsiblePerson());
        if (Objects.nonNull(heCustomerComplaintsEntity.getPrimaryResponsiblePerson())) {
            complaintsPicpInfoVO.setPrimaryResponsiblePersonStr(userNameMap.get(heCustomerComplaintsEntity.getPrimaryResponsiblePerson().longValue()));
        }
        complaintsPicpInfoVO.setSecondaryResponsiblePersonList(getSecondaryResponsiblePersonList(heCustomerComplaintsEntity.getSecondaryResponsiblePerson(), userNameMap));

        complaintsPicpInfoVO.setSupplierId(heCustomerComplaintsEntity.getSupplierId());
        if (Objects.nonNull(heCustomerComplaintsEntity.getSupplierId())) {
            List<SupplierPageVO> supplierPageVOS = financeManager.queryHLYSupplier(Collections.singletonList(heCustomerComplaintsEntity.getSupplierId()));
            if (CollectionUtils.isNotEmpty(supplierPageVOS)) {
                complaintsPicpInfoVO.setSupplierIdStr(supplierPageVOS.get(0).getName());
            }
        }
        complaintsPicpInfoVO.setMainSupplierId(heCustomerComplaintsEntity.getMainSupplierId());

        if (Objects.nonNull(heCustomerComplaintsEntity.getMainSupplierId())) {

            List<MainSupplierQueryVO> mainSupplierQueryVOS = financeManager.queryMainSupplier(Collections.singletonList(heCustomerComplaintsEntity.getMainSupplierId().longValue()));
            if (CollectionUtils.isNotEmpty(mainSupplierQueryVOS)) {
                complaintsPicpInfoVO.setMainSupplierIdStr(mainSupplierQueryVOS.get(0).getName());
            }
        }


        if (Objects.nonNull(heCustomerComplaintsEntity.getTherapistId())) {


            TherapistDetailReq req1 = new TherapistDetailReq();
            req1.setId(heCustomerComplaintsEntity.getTherapistId().longValue());
            req1.setOperator(new com.stbella.sso.base.Operator());
            Result<TherapistSelectOptionVo> therapistSelectOptionVoResult = therapistQueryService.queryTherapistDetail(req1);
            TherapistSelectOptionVo therapistSelectOptionVo = therapistSelectOptionVoResult.getData();
            if (Objects.nonNull(therapistSelectOptionVo)) {
                complaintsPicpInfoVO.setTherapistId(heCustomerComplaintsEntity.getTherapistId());
                complaintsPicpInfoVO.setTherapistIdStr(therapistSelectOptionVo.getName());
            }
        }

        if (Objects.nonNull(heCustomerComplaintsEntity.getThirdPartyTherapistId())) {
            TherapistDetailReq req2 = new TherapistDetailReq();
            req2.setId(heCustomerComplaintsEntity.getThirdPartyTherapistId().longValue());
            req2.setOperator(new com.stbella.sso.base.Operator());
            Result<TherapistSelectOptionVo> therapistSelectOptionVoResult = therapistQueryService.queryTherapistDetail(req2);
            TherapistSelectOptionVo therapistSelectOptionVo = therapistSelectOptionVoResult.getData();
            if (Objects.nonNull(therapistSelectOptionVo)) {
                complaintsPicpInfoVO.setThirdPartyTherapistId(heCustomerComplaintsEntity.getThirdPartyTherapistId());
                complaintsPicpInfoVO.setThirdPartyTherapistIdStr(therapistSelectOptionVo.getName());
            }
        }
        complaintsPicpInfoVO.setThirdPartyContactName(heCustomerComplaintsEntity.getThirdPartyContactName());
        complaintsPicpInfoVO.setThirdPartyContactPhone(heCustomerComplaintsEntity.getThirdPartyContactPhone());
        Map<Long, BigDecimal> orderRefundAmountMap = getOrderRefundAmount(Collections.singletonList(heCustomerComplaintsEntity), Collections.singletonList(heOrderEntity));
        complaintsPicpInfoVO.setOrderRefundAmount(getRoundingMode(orderRefundAmountMap.get(heCustomerComplaintsEntity.getId())));

        Map<Integer, BigDecimal> compensatedGoodsAmountMap = getCompensatedGoodsAmountMap(Collections.singletonList(heCustomerComplaintsEntity));
        complaintsPicpInfoVO.setCompensatedGoodsAmount(this.getRoundingMode(Objects.isNull(heCustomerComplaintsEntity.getCartId()) ? null : compensatedGoodsAmountMap.get(heCustomerComplaintsEntity.getCartId())));
        complaintsPicpInfoVO.setCompensatedCashAmount(this.getCompensatedCashAmount(heCustomerComplaintsEntity.getCompensationAmount()));
        BigDecimal otherCompensationAmount = getOtherCompensationAmount(heCustomerComplaintsEntity.getOtherCompensationAmount());
        complaintsPicpInfoVO.setComplaintsRefundAmount(this.getRoundingMode(complaintsPicpInfoVO.getCompensatedCashAmount().add(complaintsPicpInfoVO.getCompensatedGoodsAmount()).add(complaintsPicpInfoVO.getOrderRefundAmount()).add(otherCompensationAmount)));
        complaintsPicpInfoVO.setNetSalesPerformance(this.getRoundingMode(heCustomerComplaintsEntity.getNetSalesPerformance()));
        complaintsPicpInfoVO.setNetStorePerformance(this.getRoundingMode(heCustomerComplaintsEntity.getNetStorePerformance()));
        if (Objects.nonNull(heCustomerComplaintsEntity.getCashRefundId())) {
            HeOrderRefundEntity orderCashRefund = orderRefundRepository.getOneById(heCustomerComplaintsEntity.getCashRefundId());
            if (Objects.nonNull(orderCashRefund)) {
                complaintsPicpInfoVO.setNetStorePerformance(this.getRoundingMode(AmountChangeUtil.f2YScale2(orderCashRefund.getRefundAchievement())));
            }
        }
        complaintsPicpInfoVO.setCurrency(heOrderEntity.getCurrency());
        if (StringUtils.isNotEmpty(heCustomerComplaintsEntity.getOtherCompensationAmount())) {
            complaintsPicpInfoVO.setOtherCompensationAmountList(JSONUtil.toList(heCustomerComplaintsEntity.getOtherCompensationAmount(), SelectRespVO.class));
        } else {
            List<SelectRespVO> otherCompensationAmountList = new ArrayList<>();
            for (CustomerComplaintsRefundEnum item : CustomerComplaintsRefundEnum.values()) {
                otherCompensationAmountList.add(new SelectRespVO(item.getDesc(), getRoundingMode(BigDecimal.ZERO).toString()));
            }
            complaintsPicpInfoVO.setOtherCompensationAmountList(otherCompensationAmountList);
        }
        return complaintsPicpInfoVO;
    }

    private Map<Long, String> getUserNameMap(HeCustomerComplaintsEntity heCustomerComplaintsEntity) {

        List<Long> staffIdList = Lists.newArrayList();
        staffIdList.add(heCustomerComplaintsEntity.getCreator().longValue());
        if (CollectionUtils.isNotEmpty(heCustomerComplaintsEntity.getSecondaryResponsiblePerson())) {
            staffIdList.addAll(heCustomerComplaintsEntity.getSecondaryResponsiblePerson().stream().map(Long::valueOf).collect(Collectors.toList()));
        }
        if (Objects.nonNull(heCustomerComplaintsEntity.getPrimaryResponsiblePerson())) {
            staffIdList.add(heCustomerComplaintsEntity.getPrimaryResponsiblePerson().longValue());
        }
        if (Objects.nonNull(heCustomerComplaintsEntity.getTherapistId())) {
            staffIdList.add(heCustomerComplaintsEntity.getTherapistId().longValue());
        }
        List<UserPO> userEntityList = tabClientManager.queryUserListByIds(staffIdList);
        if (CollectionUtils.isEmpty(userEntityList)) {
            return new HashMap<>();
        }
        return userEntityList.stream().collect(Collectors.toMap(UserPO::getId, UserPO::getName, (v1, v2) -> v1));
    }

    private List<ComplaintsPicpInfoVO.SecondaryResponsiblePerson> getSecondaryResponsiblePersonList(List<Integer> secondaryResponsiblePerson, Map<Long, String> userNameMap) {

        List<ComplaintsPicpInfoVO.SecondaryResponsiblePerson> resultList = new ArrayList<>();

        if (CollectionUtils.isEmpty(secondaryResponsiblePerson)) {
            return resultList;
        }
        for (Integer secondaryResponsiblePersonId : secondaryResponsiblePerson) {

            ComplaintsPicpInfoVO.SecondaryResponsiblePerson responsiblePerson = new ComplaintsPicpInfoVO.SecondaryResponsiblePerson();
            responsiblePerson.setSecondaryResponsiblePerson(secondaryResponsiblePersonId);
            responsiblePerson.setSecondaryResponsiblePersonStr(userNameMap.get(secondaryResponsiblePersonId.longValue()));
            resultList.add(responsiblePerson);
        }
        return resultList;
    }

    @Override
    public ComplaintsPicpHandleResultVO handleResult(ComplaintsPicpInfoReq req) {

        HeCustomerComplaintsEntity heCustomerComplaintsEntity = heCustomerComplaintsRepository.selectById(req.getId());
        Assert.notNull(heCustomerComplaintsEntity, "客诉工单不存在");
        Assert.notNull(heCustomerComplaintsEntity.getOrderId(), "客诉工单对应的订单ID不存在");
        HeOrderEntity heOrderEntity = orderRepository.queryOrderById(heCustomerComplaintsEntity.getOrderId().intValue());
        Assert.notNull(heOrderEntity, "订单不存在");
        boolean newOrder = heOrderEntity.isNewOrder();

        List<HeOrderRefundEntity> refundAllByOrderId = new ArrayList<>();

        List<HeOrderRefundEntity> refundByOrderIdList = orderRefundRepository.getRefundByOrderId(heCustomerComplaintsEntity.getOrderId().intValue());
        refundByOrderIdList = refundByOrderIdList.stream().filter(item -> !OmniPayTypeEnum.REDUCTION.getCode().equals(item.getRefundType())).collect(Collectors.toList());
        if (Objects.nonNull(heCustomerComplaintsEntity.getRefundOrderId())) {
            List<HeOrderRefundEntity> finalRefundByOrderIdList = refundByOrderIdList;
            refundByOrderIdList.stream().filter(item -> item.getId().equals(heCustomerComplaintsEntity.getRefundOrderId().intValue())).findFirst().ifPresent(orderRefund -> {
                refundAllByOrderId.add(orderRefund);
                List<HeOrderRefundEntity> subHeOrderRefundList = finalRefundByOrderIdList.stream().filter(item -> StringUtils.isNotEmpty(item.getParentRefundOrderSn()) && item.getParentRefundOrderSn().equals(orderRefund.getRefundOrderSn())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(subHeOrderRefundList)) {
                    refundAllByOrderId.addAll(subHeOrderRefundList);
                }
            });
        }

        if (Objects.nonNull(heCustomerComplaintsEntity.getCashRefundId())) {
            HeOrderRefundEntity cashRefundEntity = orderRefundRepository.getOneById(heCustomerComplaintsEntity.getCashRefundId());
            if (Objects.nonNull(cashRefundEntity)) {
                refundAllByOrderId.add(cashRefundEntity);
            }
        }
        ComplaintsPicpHandleResultVO complaintsPicpHandleResultVO = new ComplaintsPicpHandleResultVO();
        complaintsPicpHandleResultVO.setCurrency(heOrderEntity.getCurrency());
        if (StringUtils.isNotEmpty(heCustomerComplaintsEntity.getOtherCompensationAmount())) {
            complaintsPicpHandleResultVO.setOtherCompensationAmountList(JSONUtil.toList(heCustomerComplaintsEntity.getOtherCompensationAmount(), SelectRespVO.class));
        } else {
            List<SelectRespVO> otherCompensationAmountList = new ArrayList<>();
            for (CustomerComplaintsRefundEnum item : CustomerComplaintsRefundEnum.values()) {
                otherCompensationAmountList.add(new SelectRespVO(item.getDesc(), getRoundingMode(BigDecimal.ZERO).toString()));
            }
            complaintsPicpHandleResultVO.setOtherCompensationAmountList(otherCompensationAmountList);
        }
        if (CollectionUtils.isEmpty(refundAllByOrderId)) {
            this.amountInit(complaintsPicpHandleResultVO);
            complaintsPicpHandleResultVO.setCashRefundAmount(getRoundingMode(heCustomerComplaintsEntity.getCompensationAmount()));
            return complaintsPicpHandleResultVO;
        }
        List<HeOrderRefundEntity> orderNoCashRefundParentAllList = refundAllByOrderId.stream().filter(item -> Objects.nonNull(heCustomerComplaintsEntity.getRefundOrderId()) && heCustomerComplaintsEntity.getRefundOrderId().equals(item.getId().longValue())).collect(Collectors.toList());
        List<HeOrderRefundEntity> orderCashRefundList = refundAllByOrderId.stream().filter(item -> item.getId().equals(heCustomerComplaintsEntity.getCashRefundId()) && StringUtils.isEmpty(item.getParentRefundOrderSn())).collect(Collectors.toList());
        complaintsPicpHandleResultVO.setOrderRefundAmount(getOrderRefundAmount(heCustomerComplaintsEntity.getRefundOrderId(), refundAllByOrderId, newOrder));
        List<HeOrderRefundEntity> orderOtherRefundList = getOrderOtherRefundList(heCustomerComplaintsEntity.getOtherCompensationId());
        complaintsPicpHandleResultVO.setRefundProcessAmount(getRefundAmount(orderNoCashRefundParentAllList, orderCashRefundList, orderOtherRefundList, refundAllByOrderId, HeOrderRefundEntity.NOT_FINISH_STATUS, newOrder));
        complaintsPicpHandleResultVO.setRefundSuccessAmount(getRefundAmount(orderNoCashRefundParentAllList, orderCashRefundList, orderOtherRefundList, refundAllByOrderId, Collections.singletonList(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode()), newOrder));
        complaintsPicpHandleResultVO.setRefundFailAmount(getRefundAmount(orderNoCashRefundParentAllList, orderCashRefundList, orderOtherRefundList, refundAllByOrderId, Collections.singletonList(RefundRecordPayStatusEnum.REFUND_RECORD_5.getCode()), newOrder));
        complaintsPicpHandleResultVO.setCashRefundAmount(getRoundingMode(heCustomerComplaintsEntity.getCompensationAmount()));
        complaintsPicpHandleResultVO.setRefundProductionAmount(getRefundProductionAmount(heCustomerComplaintsEntity.getRefundOrderId(), refundAllByOrderId, newOrder));
        List<ComplaintsPicpHandleResultVO.RefundRecordVO> refundRecordVOList = getRefundRecordVOList(newOrder, heCustomerComplaintsEntity, refundAllByOrderId);
        if (CollectionUtils.isNotEmpty(orderOtherRefundList)) {
            List<ComplaintsPicpHandleResultVO.RefundRecordVO> otherRefundList = this.getOrderOtherRefundList(orderOtherRefundList, newOrder, heCustomerComplaintsEntity);
            if (CollectionUtils.isNotEmpty(otherRefundList)) {
                refundRecordVOList.addAll(otherRefundList);
            }
        }
        complaintsPicpHandleResultVO.setRefundRecordVOList(refundRecordVOList);
        return complaintsPicpHandleResultVO;
    }

    private List<ComplaintsPicpHandleResultVO.RefundRecordVO> getOrderOtherRefundList(List<HeOrderRefundEntity> orderOtherRefundList, Boolean newOrder, HeCustomerComplaintsEntity heCustomerComplaintsEntity) {

        List<ComplaintsPicpHandleResultVO.RefundRecordVO> refundRecordVOList = new ArrayList<>();
        for (HeOrderRefundEntity orderOtherRefund : orderOtherRefundList) {
            refundRecordVOList.add(getRefundRecordVO(newOrder, orderOtherRefund, heCustomerComplaintsEntity));
        }
        return refundRecordVOList;
    }

    private List<HeOrderRefundEntity> getOrderOtherRefundList(String otherCompensationId) {

        List<HeOrderRefundEntity> orderOtherRefundList = new ArrayList<>();
        if (StringUtils.isEmpty(otherCompensationId)) {
            return orderOtherRefundList;
        }
        Map<String, Integer> otherCompensationIdMap = new HashMap<>();
        ;
        try {
            otherCompensationIdMap = JSONObject.parseObject(otherCompensationId, Map.class);
            if (!otherCompensationIdMap.isEmpty()) {
                return orderRefundRepository.batchQueryEntityByIds(new ArrayList<>(otherCompensationIdMap.values()));
            }
        } catch (Exception e) {
            log.error("解析其他补偿Id失败：{}", e.getMessage());
        }
        return orderOtherRefundList;
    }

    private List<ComplaintsPicpHandleResultVO.RefundRecordVO> getRefundRecordVOList(Boolean newOrder, HeCustomerComplaintsEntity heCustomerComplaintsEntity, List<HeOrderRefundEntity> refundAllByOrderId) {

        List<Integer> refundIdList = new ArrayList<>();
        if (Objects.nonNull(heCustomerComplaintsEntity.getRefundOrderId())) {
            refundIdList.add(heCustomerComplaintsEntity.getRefundOrderId().intValue());
        }
        if (Objects.nonNull(heCustomerComplaintsEntity.getCashRefundId())) {
            refundIdList.add(heCustomerComplaintsEntity.getCashRefundId());
        }
        List<ComplaintsPicpHandleResultVO.RefundRecordVO> refundRecordVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(refundIdList)) {
            return refundRecordVOList;
        }

        List<HeOrderRefundEntity> heOrderRefundEntityList = refundAllByOrderId.stream().filter(item -> refundIdList.contains(item.getId())).collect(Collectors.toList());
        heOrderRefundEntityList = heOrderRefundEntityList.stream().filter(item -> !OmniPayTypeEnum.REDUCTION.getCode().equals(item.getRefundType())).collect(Collectors.toList());
        for (HeOrderRefundEntity heOrderRefundEntity : heOrderRefundEntityList) {
            if (newOrder && Objects.nonNull(heCustomerComplaintsEntity.getCashRefundId()) && !heOrderRefundEntity.getId().equals(heCustomerComplaintsEntity.getCashRefundId())) {
                refundAllByOrderId.stream().filter(item -> StringUtils.isNotEmpty(item.getParentRefundOrderSn()) &&
                        item.getParentRefundOrderSn().equals(heOrderRefundEntity.getRefundOrderSn())).forEach(child -> {
                    refundRecordVOList.add(getRefundRecordVO(newOrder, child, heCustomerComplaintsEntity));
                });
                continue;
            }
            refundRecordVOList.add(getRefundRecordVO(newOrder, heOrderRefundEntity, heCustomerComplaintsEntity));
        }
        return refundRecordVOList;
    }

    private ComplaintsPicpHandleResultVO.RefundRecordVO getRefundRecordVO(Boolean newOrder, HeOrderRefundEntity heOrderRefundEntity, HeCustomerComplaintsEntity heCustomerComplaintsEntity) {

        ComplaintsPicpHandleResultVO.RefundRecordVO refundRecordVO = new ComplaintsPicpHandleResultVO.RefundRecordVO();
        refundRecordVO.setId(heOrderRefundEntity.getId());
        refundRecordVO.setRefundOrderSn(heOrderRefundEntity.getRefundOrderSn());
        refundRecordVO.setActualAmount(AmountChangeUtil.f2YScale2(heOrderRefundEntity.getApplyAmount()));
        if (Objects.nonNull(heCustomerComplaintsEntity.getCashRefundId()) && heOrderRefundEntity.getId().equals(heCustomerComplaintsEntity.getCashRefundId())) {
            refundRecordVO.setActualAmount(this.getRoundingMode(heCustomerComplaintsEntity.getCompensationAmount()));
        }
        refundRecordVO.setStatus(heOrderRefundEntity.getStatus());
        refundRecordVO.setStatusStr(OmniRefundApproveEnum.getName(heOrderRefundEntity.getStatus()));
        refundRecordVO.setIncomeType(heOrderRefundEntity.getRefundType());
        refundRecordVO.setIncomeTypeStr(OmniPayTypeEnum.getName(heOrderRefundEntity.getRefundType()));
        if (OmniPayTypeEnum.PRODUCTION_COIN.getCode().equals(heOrderRefundEntity.getRefundType()) && !newOrder) {
            HeUserProductionAmountPayLogEntity productionPayEntity = productionAmountPayRepository.getOneById(heOrderRefundEntity.getProjectId().intValue());
            refundRecordVO.setIncomeId(productionPayEntity.getId());
            refundRecordVO.setIncomeSn(Objects.isNull(productionPayEntity.getProductionId()) ? StringUtils.EMPTY : productionPayEntity.getProductionId().toString());
        } else {
            refundRecordVO.setIncomeId(heOrderRefundEntity.getOrderGoodId());
            refundRecordVO.setIncomeSn(heOrderRefundEntity.getIncomeSn());
        }
        if (Objects.nonNull(heOrderRefundEntity.getCreatedAt()) && heOrderRefundEntity.getCreatedAt() > 0) {
            refundRecordVO.setCreatedAt(DateUtil.formatDateTime(new Date(heOrderRefundEntity.getCreatedAt() * 1000)));
        }
        if (Objects.nonNull(heOrderRefundEntity.getFinishAt()) && heOrderRefundEntity.getFinishAt() > 0) {
            refundRecordVO.setFinishAt(DateUtil.formatDateTime(new Date(heOrderRefundEntity.getFinishAt() * 1000)));
        }
        return refundRecordVO;
    }

    private Map<String, BigDecimal> getCompensatedGoodsAmount(List<HeOrderRefundEntity> refundAllByOrderId, Long refundOrderId, Boolean newOrder) {

        Map<String, BigDecimal> compensatedRefundAmountMap = new HashMap<>();
        if (!newOrder) {
            return compensatedRefundAmountMap;
        }
        refundAllByOrderId.stream().filter(item -> item.getId().equals(refundOrderId.intValue()) && StringUtils.isEmpty(item.getParentRefundOrderSn())).forEach(parentEntity -> {
            int applyAmountSum = refundAllByOrderId.stream().filter(item -> StringUtils.isNotEmpty(item.getParentRefundOrderSn()) &&
                    item.getParentRefundOrderSn().equals(parentEntity.getRefundOrderSn())).mapToInt(HeOrderRefundEntity::getApplyAmount).sum();
            compensatedRefundAmountMap.put(parentEntity.getRefundOrderSn(), AmountChangeUtil.f2YScale2(applyAmountSum));
        });
        return compensatedRefundAmountMap;
    }

    private BigDecimal getOrderRefundAmount(Long refundOrderId, List<HeOrderRefundEntity> refundAllByOrderId, Boolean newOrder) {

        int orderRefundSum = 0;
        if (Objects.isNull(refundOrderId)) {
            return getRoundingMode(null);
        }
        List<HeOrderRefundEntity> heOrderRefundEntityList = refundAllByOrderId.stream().filter(item -> StringUtils.isEmpty(item.getParentRefundOrderSn()) && item.getId().equals(refundOrderId.intValue())).collect(Collectors.toList());
        if (newOrder) {
            orderRefundSum = heOrderRefundEntityList.stream().map(sub -> {
                List<HeOrderRefundEntity> child = refundAllByOrderId.stream().filter(item -> StringUtils.isNotEmpty(item.getParentRefundOrderSn()) &&
                        item.getParentRefundOrderSn().equals(sub.getRefundOrderSn())).collect(Collectors.toList());
                return child.stream().mapToInt(HeOrderRefundEntity::getApplyAmount).sum();
            }).reduce(Integer::sum).orElse(0);
        } else {
            orderRefundSum = heOrderRefundEntityList.stream().mapToInt(HeOrderRefundEntity::getApplyAmount).sum();
        }
        return AmountChangeUtil.f2YScale2(orderRefundSum);
    }

    private BigDecimal getRefundProductionAmount(Long refundOrderId, List<HeOrderRefundEntity> refundAllByOrderId, Boolean newOrder) {

        if (Objects.isNull(refundOrderId)) {
            return getRoundingMode(null);
        }
        int orderProductionRefundSum = 0;
        List<HeOrderRefundEntity> orderProductionRefundList = refundAllByOrderId.stream().filter(item -> StringUtils.isEmpty(item.getParentRefundOrderSn()) && item.getId().equals(refundOrderId.intValue())).collect(Collectors.toList());
        if (newOrder) {
            orderProductionRefundSum = orderProductionRefundList.stream().map(sub -> {
                List<HeOrderRefundEntity> child = refundAllByOrderId.stream().filter(item -> StringUtils.isNotEmpty(item.getParentRefundOrderSn()) &&
                        item.getParentRefundOrderSn().equals(sub.getRefundOrderSn()) && OmniRefundTypeEnum.PRODUCTION_COIN.getCode().equals(item.getRefundType())).collect(Collectors.toList());
                return child.stream().mapToInt(HeOrderRefundEntity::getApplyAmount).sum();
            }).reduce(Integer::sum).orElse(0);
        } else {
            orderProductionRefundSum = orderProductionRefundList.stream().filter(item -> OmniRefundTypeEnum.PRODUCTION_COIN.getCode().equals(item.getRefundType())).mapToInt(HeOrderRefundEntity::getApplyAmount).sum();
        }
        return AmountChangeUtil.f2YScale2(orderProductionRefundSum);
    }

    private BigDecimal getRefundAmount(List<HeOrderRefundEntity> orderNoCashRefundParentAllList, List<HeOrderRefundEntity> orderCashRefundList, List<HeOrderRefundEntity> orderOtherRefundList, List<HeOrderRefundEntity> refundAllByOrderId, List<Integer> orderRefundStatusList, Boolean newOrder) {

        if (CollectionUtils.isEmpty(orderNoCashRefundParentAllList)) {
            orderNoCashRefundParentAllList = new ArrayList<>();
        }
        int orderNoCashRefundSuccessSum = 0;
        // 新旧订单，退款结构体系不同
        if (newOrder) {
            orderNoCashRefundSuccessSum = orderNoCashRefundParentAllList.stream().filter(item -> orderRefundStatusList.contains(item.getStatus())).map(sub -> {
                List<HeOrderRefundEntity> child = refundAllByOrderId.stream().filter(item -> StringUtils.isNotEmpty(item.getParentRefundOrderSn()) &&
                        item.getParentRefundOrderSn().equals(sub.getRefundOrderSn())).collect(Collectors.toList());
                return child.stream().mapToInt(HeOrderRefundEntity::getApplyAmount).sum();
            }).reduce(Integer::sum).orElse(0);
        } else {
            orderNoCashRefundSuccessSum = orderNoCashRefundParentAllList.stream().filter(item -> orderRefundStatusList.contains(item.getStatus())).mapToInt(HeOrderRefundEntity::getApplyAmount).sum();
        }
        int orderCashRefundSuccessSum = orderCashRefundList.stream().filter(item -> orderRefundStatusList.contains(item.getStatus())).mapToInt(HeOrderRefundEntity::getApplyAmount).sum();
        int orderOtherRefundSuccessSum = orderOtherRefundList.stream().filter(item -> orderRefundStatusList.contains(item.getStatus())).mapToInt(HeOrderRefundEntity::getApplyAmount).sum();
        return AmountChangeUtil.f2YScale2(orderNoCashRefundSuccessSum + orderCashRefundSuccessSum + orderOtherRefundSuccessSum);
    }


    private void amountInit(ComplaintsPicpHandleResultVO complaintsPicpHandleResultVO) {

        complaintsPicpHandleResultVO.setOrderRefundAmount(getRoundingMode(null));
        complaintsPicpHandleResultVO.setRefundProcessAmount(getRoundingMode(null));
        complaintsPicpHandleResultVO.setRefundSuccessAmount(getRoundingMode(null));
        complaintsPicpHandleResultVO.setRefundFailAmount(getRoundingMode(null));
        complaintsPicpHandleResultVO.setCashRefundAmount(getRoundingMode(null));
        complaintsPicpHandleResultVO.setRefundProductionAmount(getRoundingMode(null));
    }


    @Override
    public ComplaintsPicpHandleOpinionVO handleOpinion(ComplaintsPicpInfoReq req) {

        HeCustomerComplaintsEntity heCustomerComplaintsEntity = heCustomerComplaintsRepository.selectById(req.getId());
        Assert.notNull(heCustomerComplaintsEntity, "客诉工单不存在");
        ComplaintsPicpHandleOpinionVO complaintsPicpHandleOpinionVO = new ComplaintsPicpHandleOpinionVO();
        complaintsPicpHandleOpinionVO.setOrderRefundOpinionVO(getOrderRefundOpinionVO(heCustomerComplaintsEntity.getRefundOrderId()));
        complaintsPicpHandleOpinionVO.setOrderComplaintsOpinionVO(getOrderComplaintsOpinionVO(heCustomerComplaintsEntity));
        return complaintsPicpHandleOpinionVO;
    }

    @Override
    public List<OrderComplaintsExport> export(ComplaintsPicpPageReq req) {
        req.setPageNum(1);
        req.setPageSize(Integer.MAX_VALUE);
        log.info("客诉导出请求参数:{}", JsonUtil.write(req));
        PageDTO<ComplaintsPicpPageVO> page = page(req);
        log.info("客诉导出查询到的条数:{}", JsonUtil.write(page.getTotal()));
        List<ComplaintsPicpPageVO> list = page.getList();


        List<Long> staffIdList = Lists.newArrayList();
        list.stream().forEach(l -> {
            if (ObjectUtil.isNotEmpty(l.getPrimaryResponsiblePerson())) {
                staffIdList.add(l.getPrimaryResponsiblePerson().longValue());
            }
            if (CollectionUtils.isNotEmpty(l.getSecondaryResponsiblePerson())) {
                staffIdList.addAll(l.getSecondaryResponsiblePerson().stream().map(Integer::longValue).collect(Collectors.toList()));
            }
        });
        Map<Long, String> collect = new HashMap<>();
        if (CollectionUtils.isNotEmpty(staffIdList)) {
            {
                List<UserPO> userEntityList = tabClientManager.queryUserListByIds(staffIdList);
                log.info("获取责任人返回：{}", JsonUtil.write(userEntityList));
                collect = userEntityList.stream().collect(Collectors.toMap(UserPO::getId, UserPO::getName, (v1, v2) -> v1));
            }
        }

        List<OrderComplaintsExport> orderComplaintsExports = BeanMapper.mapList(list, OrderComplaintsExport.class);
        Map<Long, String> finalCollect = collect;
        log.info("获取责任人返回：{}", JsonUtil.write(finalCollect));
        orderComplaintsExports.forEach(o -> {
            if (CollectionUtils.isEmpty(o.getOtherCompensationAmountList())) {
                o.setOtherMedicalChargesAmount(getRoundingMode(null));
            } else {
                SelectRespVO selectRespVO = o.getOtherCompensationAmountList().get(0);
                o.setOtherMedicalChargesAmount(getRoundingMode(new BigDecimal(selectRespVO.getLabel())));
            }
            Optional<ComplaintsPicpPageVO> first = list.stream().filter(l -> l.getId().equals(o.getId())).findFirst();
            if (first.isPresent()) {
                if (ObjectUtil.isNotEmpty(first.get().getPrimaryResponsiblePerson())) {
                    o.setPrimaryResponsiblePersonStr(finalCollect.get(first.get().getPrimaryResponsiblePerson().longValue()));
                }
                if (CollectionUtils.isNotEmpty(first.get().getSecondaryResponsiblePerson())) {
                    first.get().getSecondaryResponsiblePerson().forEach(f -> {
                        o.setSecondaryResponsiblePersonStr(o.getPrimaryResponsiblePersonStr() + " " + finalCollect.get(f.longValue()));
                    });
                }
            }
        });

        return orderComplaintsExports;
    }

    @Override
    public List<SelectRespVO> queryCustomerComplaintsRefundEnum() {

        List<SelectRespVO> selectRespVOList = new ArrayList<>();
        for (CustomerComplaintsRefundEnum item : CustomerComplaintsRefundEnum.values()) {
            selectRespVOList.add(new SelectRespVO(item.getCode(), item.getDesc()));
        }
        return selectRespVOList;
    }


    private OrderComplaintsOpinionVO getOrderComplaintsOpinionVO(HeCustomerComplaintsEntity heCustomerComplaintsEntity) {

        OrderComplaintsOpinionVO orderComplaintsOpinionVO = new OrderComplaintsOpinionVO();
        orderComplaintsOpinionVO.setComplaintsCashAmount(getRoundingMode(heCustomerComplaintsEntity.getCompensationAmount()));
        orderComplaintsOpinionVO.setComplaintsGoodsAmount(getRoundingMode(null));

        Assert.notNull(heCustomerComplaintsEntity.getOrderId(), "客诉工单对应的订单Id不能为空");
        HeOrderEntity heOrderEntity = orderRepository.queryOrderById(heCustomerComplaintsEntity.getOrderId().intValue());
        Assert.notNull(heOrderEntity, "订单不存在");
        orderComplaintsOpinionVO.setCurrency(heOrderEntity.getCurrency());
        if (Objects.nonNull(heCustomerComplaintsEntity.getCashRefundId())) {
            HeOrderRefundEntity orderCashRefund = orderRefundRepository.getOneById(heCustomerComplaintsEntity.getCashRefundId());
            orderComplaintsOpinionVO.setCashRefundType(orderCashRefund.getRefundType());
            orderComplaintsOpinionVO.setCashRefundTypeStr(OmniRefundTypeEnum.getName(orderCashRefund.getRefundType()));
            if (StringUtils.isNotEmpty(orderCashRefund.getRefundInfo())) {
                JSONObject refundInfo = JSONObject.parseObject(orderCashRefund.getRefundInfo());
                orderComplaintsOpinionVO.setRefundName(refundInfo.getString("name"));
                orderComplaintsOpinionVO.setBankName(refundInfo.getString("bank_name"));
                orderComplaintsOpinionVO.setBankNumber(refundInfo.getString("bank_number"));
                String refundVoucher = refundInfo.getString("images");
                List<String> refundVoucherList = StringUtils.isEmpty(refundVoucher) ? null : JSONObject.parseArray(refundVoucher, String.class);
                orderComplaintsOpinionVO.setRefundVoucherList(refundVoucherList);
            }
        }

        if (Objects.isNull(heCustomerComplaintsEntity.getCartId())) {
            return orderComplaintsOpinionVO;
        }

        HeCartEntity heCartEntity = heCartRepository.queryList(Collections.singletonList(heCustomerComplaintsEntity.getCartId())).stream().findFirst().orElse(null);
        if (Objects.isNull(heCartEntity)) {
            return orderComplaintsOpinionVO;
        }
        orderComplaintsOpinionVO.setComplaintsGoodsAmount(AmountChangeUtil.f2YScale2(heCartEntity.getOrderAmount()));
        orderComplaintsOpinionVO.setGoodsRecordList(this.getGoodsRecordList(heCartEntity.getCartId()));

        if (StringUtils.isNotEmpty(heCustomerComplaintsEntity.getExtraRefundInfo())) {
            List<ExtraRefundTypeRes> otherCompensationAmountList = JSONUtil.toList(heCustomerComplaintsEntity.getExtraRefundInfo(), ExtraRefundTypeRes.class);
            otherCompensationAmountList.forEach(item -> {
                item.setExtraValue(CustomerComplaintsRefundEnum.fromCode(item.getExtraCode()));
            });
            orderComplaintsOpinionVO.setOtherCompensationAmountList(otherCompensationAmountList);
        } else {
            orderComplaintsOpinionVO.setOtherCompensationAmountList(Collections.emptyList());
        }
        return orderComplaintsOpinionVO;
    }

    private List<OrderComplaintsOpinionVO.ComplaintsGoodsRecord> getGoodsRecordList(Integer cartId) {

        List<OrderComplaintsOpinionVO.ComplaintsGoodsRecord> complaintsGoodsRecordList = new ArrayList<>();
        List<HeCartGoodsEntity> goodsEntityList = heCartGoodsRepository.queryListByCartId(cartId);
        if (CollectionUtils.isEmpty(goodsEntityList)) {
            return complaintsGoodsRecordList;
        }
        Map<Integer, GoodsEntity> goodsEntityMap = new HashMap<>();
        List<Integer> goodsIdList = goodsEntityList.stream().map(HeCartGoodsEntity::getGoodsId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(goodsIdList)) {
            List<GoodsEntity> goodsEntities = goodsRepository.selectByIdList(goodsIdList);
            goodsEntityMap = goodsEntities.stream().collect(Collectors.toMap(GoodsEntity::getId, v -> v, (k1, k2) -> k1));
        }

        Map<Integer, String> skuNameMap = new HashMap<>();
        List<Integer> skuIdList = goodsEntityList.stream().map(HeCartGoodsEntity::getSkuId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(skuIdList)) {
            List<GoodsSkuEntity> goodsSkuEntities = goodsSkuRepository.selectByIdList(skuIdList);
            skuNameMap = goodsSkuEntities.stream().collect(Collectors.toMap(GoodsSkuEntity::getId, item -> StringUtils.isEmpty(item.getTemplateSkuProp()) ? item.getSkuName() : item.getTemplateSkuProp(), (k1, k2) -> k1));
        }
        for (HeCartGoodsEntity heCartGoodsEntity : goodsEntityList) {

            OrderComplaintsOpinionVO.ComplaintsGoodsRecord complaintsGoodsRecord = new OrderComplaintsOpinionVO.ComplaintsGoodsRecord();
            complaintsGoodsRecord.setId(heCartGoodsEntity.getId());
            GoodsEntity goodsEntity = goodsEntityMap.get(heCartGoodsEntity.getGoodsId());
            if (Objects.isNull(goodsEntity)) {
                continue;
            }
            complaintsGoodsRecord.setSkuName(Objects.isNull(heCartGoodsEntity.getSkuId()) ? null : skuNameMap.get(heCartGoodsEntity.getSkuId()));
            complaintsGoodsRecord.setGoodsName(getGoodsName(goodsEntity.getGoodsName(), complaintsGoodsRecord.getSkuName()));
            complaintsGoodsRecord.setGoodsNum(heCartGoodsEntity.getNum());
            complaintsGoodsRecord.setGoodsPriceOrgin(AmountChangeUtil.f2YScale2(goodsEntity.getGoodsPrice()));
            complaintsGoodsRecord.setGoodsPrice(getRoundingMode(complaintsGoodsRecord.getGoodsPriceOrgin().multiply(new BigDecimal(heCartGoodsEntity.getNum()))));
            complaintsGoodsRecordList.add(complaintsGoodsRecord);
        }
        return complaintsGoodsRecordList;
    }

    private static String getGoodsName(String goodsName, String skuName) {

        if (StringUtils.isEmpty(goodsName) || StringUtils.isEmpty(skuName)) {
            return goodsName;
        }
        boolean alike = goodsName.equals(skuName);
        skuName = "(" + skuName + ")";
        return goodsName.endsWith(skuName) || alike ? goodsName : goodsName + skuName;
    }

    private OrderRefundOpinionVO getOrderRefundOpinionVO(Long refundOrderId) {

        log.info("获取订单退款信息，退款订单ID：{}", refundOrderId);
        if (Objects.isNull(refundOrderId)) {
            return null;
        }
        HeOrderRefundEntity orderRefund = orderRefundRepository.getOneById(refundOrderId.intValue());
        Assert.notNull(orderRefund, "退款订单不存在");

        HeOrderEntity heOrderEntity = orderRepository.queryOrderById(orderRefund.getOrderId());
        Assert.notNull(heOrderEntity, "订单不存在");
        BigDecimal applyAmountSum = AmountChangeUtil.f2YScale2(orderRefund.getApplyAmount());
        boolean newOrder = heOrderEntity.isNewOrder();
        if (newOrder) {
            List<HeOrderRefundEntity> orderRefundSubList = orderRefundRepository.getRefundByParentSn(orderRefund.getRefundOrderSn());
            applyAmountSum = CollectionUtils.isEmpty(orderRefundSubList) ? this.getRoundingMode(BigDecimal.ZERO) : AmountChangeUtil.f2YScale2(orderRefundSubList.stream().filter(item -> Objects.nonNull(item.getApplyAmount())).mapToInt(HeOrderRefundEntity::getApplyAmount).sum());
        }
        OrderRefundOpinionVO orderRefundOpinionVO = new OrderRefundOpinionVO();
        orderRefundOpinionVO.setActualAmount(getRoundingMode(applyAmountSum));
        orderRefundOpinionVO.setRefundType(orderRefund.getRefundMethod());
        orderRefundOpinionVO.setRefundTypeStr(RefundTypeEnum.getValueByCode(orderRefund.getRefundMethod()));
        orderRefundOpinionVO.setCurrency(orderRefund.getCurrency());
        if (StringUtils.isNotEmpty(orderRefund.getRefundInfo())) {
            JSONObject refundInfo = JSONObject.parseObject(orderRefund.getRefundInfo());
            orderRefundOpinionVO.setRefundName(refundInfo.getString("name"));
            orderRefundOpinionVO.setBankName(refundInfo.getString("bank_name"));
            orderRefundOpinionVO.setBankNumber(refundInfo.getString("bank_number"));
            String refundVoucher = refundInfo.getString("images");
            List<String> refundVoucherList = StringUtils.isEmpty(refundVoucher) ? null : JSONObject.parseArray(refundVoucher, String.class);
            orderRefundOpinionVO.setRefundVoucherList(refundVoucherList);
        }
        orderRefundOpinionVO.setRefundGoodsRecordList(getRefundGoodsRecordList(orderRefund.getOrderId(), orderRefund.getRefundOrderSn(), refundOrderId));
        return orderRefundOpinionVO;
    }

    private List<OrderRefundOpinionVO.RefundGoodsRecord> getRefundGoodsRecordList(Integer orderId, String refundOrderSn, Long refundOrderId) {

        List<OrderRefundOpinionVO.RefundGoodsRecord> refundGoodsRecordList = new ArrayList<>();

        List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList = orderRefundGoodsRepository.queryList(orderId);
        if (CollectionUtils.isEmpty(orderRefundGoodsEntityList)) {
            log.warn("订单{}没有退款商品记录", orderId);
            return refundGoodsRecordList;
        }
        List<HeOrderRefundGoodsEntity> heOrderRefundGoodsEntities = orderRefundGoodsEntityList.stream().filter(item -> refundOrderSn.equals(item.getRefundOrderSn())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(heOrderRefundGoodsEntities)) {
            log.warn("订单{}对应的退款refundOrderSn:{}没有退款商品记录", orderId, refundOrderSn);
            return refundGoodsRecordList;
        }

        List<HeOrderGoodsEntity> allItermByOrderId = orderGoodsRepository.getAllItermByOrderId(orderId);
        Map<String, Integer> goodsNumMap = CollectionUtils.isEmpty(allItermByOrderId) ? Maps.newHashMap() : allItermByOrderId.stream().collect(Collectors.toMap(HeOrderGoodsEntity::getOrderGoodsSn, HeOrderGoodsEntity::getGoodsNum, (v1, v2) -> v1));

        List<IncomePaidAllocationEntity> incomePaidAllocationList = incomePaidAllocationRepository.queryListByOrderId(orderId.longValue());
        Map<String, Integer> goodsPricePayMap = getGoodsPricePayMap(orderRefundGoodsEntityList, incomePaidAllocationList);

        Map<String, List<HeOrderRefundGoodsEntity>> heOrderRefundGoodsGroupByRefundOrderSn = heOrderRefundGoodsEntities.stream().filter(item -> Objects.nonNull(item.getOrderGoodsSn())).collect(Collectors.groupingBy(HeOrderRefundGoodsEntity::getOrderGoodsSn));

        heOrderRefundGoodsGroupByRefundOrderSn.forEach((key, orderRefundGoodsList) -> {

            HeOrderRefundGoodsEntity orderRefundGoodsEntity = orderRefundGoodsList.get(0);
            int refundProductionAmountSum = orderRefundGoodsList.stream().filter(item -> OmniRefundTypeEnum.PRODUCTION_COIN.getCode().equals(item.getPayType())).mapToInt(HeOrderRefundGoodsEntity::getRefundAmount).sum();
            int refundCashAmountSum = orderRefundGoodsList.stream().filter(item -> !OmniRefundTypeEnum.PRODUCTION_COIN.getCode().equals(item.getPayType())).mapToInt(HeOrderRefundGoodsEntity::getRefundAmount).sum();

            OrderRefundOpinionVO.RefundGoodsRecord refundGoodsRecord = new OrderRefundOpinionVO.RefundGoodsRecord();
            refundGoodsRecord.setGoodsName(orderRefundGoodsEntity.getGoodsName());
            refundGoodsRecord.setSkuName(orderRefundGoodsEntity.getSkuName());
            refundGoodsRecord.setGoodsNum(goodsNumMap.get(orderRefundGoodsEntity.getOrderGoodsSn()));
            refundGoodsRecord.setGoodsPriceOrgin(AmountChangeUtil.f2YScale2(orderRefundGoodsEntity.getGoodsPriceOrgin()));
            Integer paidSum = goodsPricePayMap.get(orderRefundGoodsEntity.getOrderGoodsSn());
            refundGoodsRecord.setGoodsPricePay(Objects.isNull(paidSum) ? getGoodsPricePay(incomePaidAllocationList, orderRefundGoodsEntity.getOrderGoodsSn()) : AmountChangeUtil.f2YScale2(paidSum));
            refundGoodsRecord.setGiftStr(orderRefundGoodsEntity.getGift() == 0 ? "购买" : "礼赠");
            refundGoodsRecord.setRefundNum(orderRefundGoodsEntity.getRefundNum());
            refundGoodsRecord.setRefundCashAmount(AmountChangeUtil.f2YScale2(refundCashAmountSum));
            refundGoodsRecord.setRefundProductionAmount(AmountChangeUtil.f2YScale2(refundProductionAmountSum));
            refundGoodsRecord.setRefundAmount(AmountChangeUtil.f2YScale2(refundCashAmountSum + refundProductionAmountSum));
            refundGoodsRecordList.add(refundGoodsRecord);
        });
        return refundGoodsRecordList;
    }

    private BigDecimal getGoodsPricePay(List<IncomePaidAllocationEntity> incomePaidAllocationEntities, String orderGoodsSn) {

        List<IncomePaidAllocationEntity> paidList = incomePaidAllocationEntities.stream().filter(i -> i.getOrderGoodsSn().equals(orderGoodsSn)).collect(Collectors.toList());
        int paidAmountSum = paidList.stream().mapToInt(IncomePaidAllocationEntity::getPaidAmount).sum();
        return AmountChangeUtil.f2YScale2(paidAmountSum);
    }

    private Map<String, Integer> getGoodsPricePayMap(List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList, List<IncomePaidAllocationEntity> incomePaidAllocationEntities) {

        Map<String, Integer> paidSumMap = new HashMap<>();
        List<HeOrderRefundGoodsEntity> tempRefundGoodsList = orderRefundGoodsEntityList.stream().filter(orderGoods -> OrderRefundNatureEnum.TEMP_REFUND.getCode().equals(orderGoods.getRefundNature()) && OrderRefundGoodsStatusEnum.SUCCESS.code().equals(orderGoods.getStatus())).collect(Collectors.toList());
        tempRefundGoodsList.forEach(tempRefundGoods -> {
            Integer paidSum = paidSumMap.get(tempRefundGoods.getOrderGoodsSn());
            if (Objects.isNull(paidSum)) {
                List<IncomePaidAllocationEntity> paidList = incomePaidAllocationEntities.stream().filter(i -> i.getOrderGoodsSn().equals(tempRefundGoods.getOrderGoodsSn())).collect(Collectors.toList());
                paidSum = paidList.stream().mapToInt(IncomePaidAllocationEntity::getPaidAmount).sum();
            }
            paidSumMap.put(tempRefundGoods.getOrderGoodsSn(), paidSum - tempRefundGoods.getRefundAmount());
        });
        return paidSumMap;
    }

    private Map<Integer, CfgStoreEntity> getStoreName(List<HeCustomerComplaintsEntity> heCustomerComplaintsEntities) {

        List<Integer> storeIdList = heCustomerComplaintsEntities.stream().map(HeCustomerComplaintsEntity::getStoreId).filter(Objects::nonNull).collect(Collectors.toList());
        List<CfgStoreEntity> storeByIdList = storeRepository.queryCfgStoreByIdList(storeIdList);
        if (CollectionUtils.isEmpty(storeByIdList)) {
            return Maps.newHashMap();
        }
        return storeByIdList.stream().collect(Collectors.toMap(CfgStoreEntity::getStoreId, v -> v, (k1, k2) -> k1));
    }

    private Map<Integer, String> getComplaintLevelMap(List<HeCustomerComplaintsEntity> heCustomerComplaintsEntities) {

        Map<Integer, String> complaintLevelMap = new HashMap<>();
        if (CollectionUtils.isEmpty(heCustomerComplaintsEntities)) {
            return complaintLevelMap;
        }
        List<HeCustomerComplaintsTypeEntity> complaintsTypeEntities = heCustomerComplaintsTypeRepository.getAll();
        if (CollectionUtils.isEmpty(complaintsTypeEntities)) {
            return complaintLevelMap;
        }
        Map<Long, HeCustomerComplaintsTypeEntity> complaintsTypeEntitieMap = complaintsTypeEntities.stream().collect(Collectors.toMap(HeCustomerComplaintsTypeEntity::getId, v -> v, (k1, k2) -> k1));
        heCustomerComplaintsEntities.stream().map(HeCustomerComplaintsEntity::getComplaintLevel).filter(Objects::nonNull).collect(Collectors.toList()).forEach(complaintLevel -> {
            complaintLevelMap.put(complaintLevel, getComplaintLevelDesc(complaintLevel, complaintsTypeEntitieMap));
        });
        return complaintLevelMap;
    }

    private String getComplaintLevelDesc(Integer complaintLevel, Map<Long, HeCustomerComplaintsTypeEntity> complaintsTypeEntitieMap) {

        String complaintLevelDesc = null;
        HeCustomerComplaintsTypeEntity heCustomerComplaintsTypeEntity = complaintsTypeEntitieMap.get(complaintLevel.longValue());
        do {
            complaintLevelDesc = StringUtils.isEmpty(complaintLevelDesc) ? heCustomerComplaintsTypeEntity.getName() : heCustomerComplaintsTypeEntity.getName().concat("/").concat(complaintLevelDesc.toString());
            heCustomerComplaintsTypeEntity = complaintsTypeEntitieMap.get(heCustomerComplaintsTypeEntity.getParentId().longValue());
        } while (Objects.nonNull(heCustomerComplaintsTypeEntity));
        return complaintLevelDesc;
    }

    private ComplaintsPicpPageVO convertEntityToPageVO(HeCustomerComplaintsEntity heCustomerComplaintsEntity, Map<Integer, CfgStoreEntity> storeNameMap, Map<String, String> warZoneMap, Map<Integer, HeOrderEntity> heOrderEntityMap,
                                                       Map<Integer, TabClientEntity> tabClientEntityMap, Map<Integer, String> complaintLevelDescMap, Map<Long, String> userNameMap, Map<Integer, BigDecimal> compensatedGoodsAmountMap,
                                                       Map<Long, BigDecimal> orderRefundAmountMap) {

        ComplaintsPicpPageVO complaintsPicpPageVO = new ComplaintsPicpPageVO();
        complaintsPicpPageVO.setId(heCustomerComplaintsEntity.getId());
        complaintsPicpPageVO.setOrderId(heCustomerComplaintsEntity.getOrderId());
        complaintsPicpPageVO.setOrderSn(heCustomerComplaintsEntity.getOrderSn());
        complaintsPicpPageVO.setOrderType(heCustomerComplaintsEntity.getOrderType());
        complaintsPicpPageVO.setOrderTypeStr(OmniOrderTypeEnum.getValueByCode(heCustomerComplaintsEntity.getOrderType()));
        CfgStoreEntity cfgStoreEntity = storeNameMap.get(heCustomerComplaintsEntity.getStoreId());
        if (Objects.nonNull(cfgStoreEntity)) {
            complaintsPicpPageVO.setWarZone(cfgStoreEntity.getWarzone());
            complaintsPicpPageVO.setWarZoneDesc(Optional.ofNullable(complaintsPicpPageVO.getWarZone()).isPresent() ? warZoneMap.get(String.valueOf(complaintsPicpPageVO.getWarZone())) : StringUtils.EMPTY);
            complaintsPicpPageVO.setStoreId(cfgStoreEntity.getStoreId());
            complaintsPicpPageVO.setStoreName(cfgStoreEntity.getStoreName());
            complaintsPicpPageVO.setBrandName(this.getBrandName(cfgStoreEntity.getType(), cfgStoreEntity.getChildType()));
        }
        HeOrderEntity heOrderEntity = heOrderEntityMap.get(heCustomerComplaintsEntity.getOrderId().intValue());
        if (Objects.nonNull(heOrderEntity)) {
            complaintsPicpPageVO.setBasicId(heOrderEntity.getBasicUid());
            complaintsPicpPageVO.setCalPayableAmount(AmountChangeUtil.f2YScale2(heOrderEntity.getPayAmount()));
            complaintsPicpPageVO.setClientUid(heOrderEntity.getClientUid());
            Optional.ofNullable(tabClientEntityMap.get(heOrderEntity.getClientUid())).ifPresent(client -> {
                complaintsPicpPageVO.setCustomerName(client.getName());
                complaintsPicpPageVO.setCustomerMobile(client.getPhone());
                complaintsPicpPageVO.setClientPhone(SensitiveInformationUtil.conductPhoneOrIdCardNo(client.getPhone()));
                complaintsPicpPageVO.setCustomerSource(client.getFromType());
                complaintsPicpPageVO.setCustomerSourceStr(CustomerFromTypeEnum.getOrderListValueByCode(complaintsPicpPageVO.getCustomerSource()));
            });
        }
        complaintsPicpPageVO.setComplaintLevel(heCustomerComplaintsEntity.getComplaintLevel());
        complaintsPicpPageVO.setComplaintLevelStr(complaintLevelDescMap.get(heCustomerComplaintsEntity.getComplaintLevel()));
        complaintsPicpPageVO.setResponsibilityType(heCustomerComplaintsEntity.getResponsibilityType());
        complaintsPicpPageVO.setResponsibilityTypeStr(CustomerComplaintsResponsibilityEnum.getValueByCode(complaintsPicpPageVO.getResponsibilityType()));
        complaintsPicpPageVO.setOrderRefundAmount(getRoundingMode(orderRefundAmountMap.get(heCustomerComplaintsEntity.getId())));
        complaintsPicpPageVO.setCompensatedGoodsAmount(getRoundingMode(Objects.isNull(heCustomerComplaintsEntity.getCartId()) ? null : compensatedGoodsAmountMap.get(heCustomerComplaintsEntity.getCartId())));
        complaintsPicpPageVO.setCompensatedCashAmount(getRoundingMode(heCustomerComplaintsEntity.getCompensationAmount()));
        BigDecimal otherCompensationAmount = BigDecimal.ZERO;
        if (StringUtils.isNotEmpty(heCustomerComplaintsEntity.getOtherCompensationAmount())) {
            complaintsPicpPageVO.setOtherCompensationAmountList(JSONUtil.toList(heCustomerComplaintsEntity.getOtherCompensationAmount(), SelectRespVO.class));
            otherCompensationAmount = getOtherCompensationAmount(complaintsPicpPageVO.getOtherCompensationAmountList());
        } else {
            List<SelectRespVO> otherCompensationAmountList = new ArrayList<>();
            for (CustomerComplaintsRefundEnum item : CustomerComplaintsRefundEnum.values()) {
                otherCompensationAmountList.add(new SelectRespVO(item.getDesc(), getRoundingMode(BigDecimal.ZERO).toString()));
            }
            complaintsPicpPageVO.setOtherCompensationAmountList(otherCompensationAmountList);
        }
        complaintsPicpPageVO.setComplaintsRefundAmount(getRoundingMode(complaintsPicpPageVO.getOrderRefundAmount().add(complaintsPicpPageVO.getCompensatedCashAmount().add(complaintsPicpPageVO.getCompensatedGoodsAmount()).add(otherCompensationAmount))));
        complaintsPicpPageVO.setComplaintStatus(heCustomerComplaintsEntity.getComplaintStatus());
        complaintsPicpPageVO.setComplaintStatusStr(CustomerComplaintsStatusEnum.getValueByCode(heCustomerComplaintsEntity.getComplaintStatus()));
        complaintsPicpPageVO.setCreator(heCustomerComplaintsEntity.getCreator());
        complaintsPicpPageVO.setCreatorStr(userNameMap.get(heCustomerComplaintsEntity.getCreator().longValue()));
        complaintsPicpPageVO.setCreatedAt(DateUtils.formatDateTime(heCustomerComplaintsEntity.getCreatedAt()));
        complaintsPicpPageVO.setApprovalFinishTime(DateUtils.formatDateTime(heCustomerComplaintsEntity.getApproveFinish()));
        complaintsPicpPageVO.setSuccessTime(DateUtils.formatDateTime(heCustomerComplaintsEntity.getResolveFinish()));
        if (ObjectUtil.isNotEmpty(heCustomerComplaintsEntity.getEventLocation())) {
            complaintsPicpPageVO.setEventTime(DateUtil.formatDate(new Date(heCustomerComplaintsEntity.getEventTime())));
        }
        complaintsPicpPageVO.setEventLocation(heCustomerComplaintsEntity.getEventLocation());
        complaintsPicpPageVO.setInvolvedPersons(heCustomerComplaintsEntity.getInvolvedPersons());
        complaintsPicpPageVO.setCause(heCustomerComplaintsEntity.getCause());
        complaintsPicpPageVO.setProcess(heCustomerComplaintsEntity.getProcess());
        complaintsPicpPageVO.setResult(heCustomerComplaintsEntity.getResult());
        complaintsPicpPageVO.setPrimaryResponsiblePerson(heCustomerComplaintsEntity.getPrimaryResponsiblePerson());
        complaintsPicpPageVO.setSecondaryResponsiblePerson(heCustomerComplaintsEntity.getSecondaryResponsiblePerson());
        return complaintsPicpPageVO;
    }

    private BigDecimal getOtherCompensationAmount(String otherCompensationAmount) {

        final BigDecimal[] otherAmount = {BigDecimal.ZERO};
        try {
            if (StringUtils.isEmpty(otherCompensationAmount)) {
                return otherAmount[0];
            }
            List<SelectRespVO> respVOList = JSONUtil.toList(otherCompensationAmount, SelectRespVO.class);
            return getOtherCompensationAmount(respVOList);
        } catch (Exception e) {
            log.error("getOtherCompensationAmount error", e);
            return otherAmount[0];
        }
    }

    private BigDecimal getOtherCompensationAmount(List<SelectRespVO> otherCompensationAmountList) {

        final BigDecimal[] otherCompensationAmount = {BigDecimal.ZERO};
        if (CollectionUtils.isEmpty(otherCompensationAmountList)) {
            return otherCompensationAmount[0];
        }
        otherCompensationAmountList.forEach(item -> {
            otherCompensationAmount[0] = otherCompensationAmount[0].add(new BigDecimal(item.getLabel()));
        });
        return otherCompensationAmount[0];
    }

    private Map<Integer, BigDecimal> getCompensatedGoodsAmountMap(List<HeCustomerComplaintsEntity> heCustomerComplaintsEntities) {

        Map<Integer, BigDecimal> compensatedGoodsAmountMap = new HashMap<>();
        List<Integer> cartIdList = heCustomerComplaintsEntities.stream().map(HeCustomerComplaintsEntity::getCartId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cartIdList)) {
            return compensatedGoodsAmountMap;
        }
        heCartRepository.queryList(cartIdList).forEach(cart -> {
            compensatedGoodsAmountMap.put(cart.getCartId(), AmountChangeUtil.f2YScale2(cart.getOrderAmount()));
        });
        return compensatedGoodsAmountMap;
    }

    private Map<Long, BigDecimal> getOrderRefundAmount(List<HeCustomerComplaintsEntity> heCustomerComplaintsEntities, List<HeOrderEntity> orderList) {

        Map<Long, BigDecimal> orderRefundAmountMap = new HashMap<>();
        List<Integer> refundOrderIdList = heCustomerComplaintsEntities.stream().map(HeCustomerComplaintsEntity::getRefundOrderId).filter(Objects::nonNull).map(Math::toIntExact).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(refundOrderIdList)) {
            return orderRefundAmountMap;
        }
        Map<Integer, Boolean> isNewOrderMap = orderList.stream().collect(Collectors.toMap(HeOrderEntity::getOrderId, HeOrderEntity::isNewOrder));
        List<HeOrderRefundEntity> orderRefundAllList = orderRefundRepository.getRefundByOrderIdList(Lists.newArrayList(isNewOrderMap.keySet()));
        //过滤掉减免的记录
        orderRefundAllList = orderRefundAllList.stream().filter(o -> !OmniPayTypeEnum.REDUCTION.getCode().equals(o.getRefundType())).collect(Collectors.toList());
        for (HeCustomerComplaintsEntity heCustomerComplaintsEntity : heCustomerComplaintsEntities) {
            if (Objects.isNull(heCustomerComplaintsEntity.getRefundOrderId())) {
                continue;
            }
            Boolean isNewOrder = isNewOrderMap.get(heCustomerComplaintsEntity.getOrderId().intValue());
            if (Objects.isNull(isNewOrder)) {
                continue;
            }
            if (isNewOrder) {
                List<HeOrderRefundEntity> finalOrderRefundAllList = orderRefundAllList;
                Integer newOrderApplyAmountSum = orderRefundAllList.stream().filter(item -> Objects.nonNull(item.getId()) && heCustomerComplaintsEntity.getRefundOrderId().intValue() == item.getId()).map(sub -> {
                    List<HeOrderRefundEntity> child = finalOrderRefundAllList.stream().filter(item -> StringUtils.isNotEmpty(item.getParentRefundOrderSn()) &&
                            item.getParentRefundOrderSn().equals(sub.getRefundOrderSn())).collect(Collectors.toList());
                    return child.stream().mapToInt(HeOrderRefundEntity::getApplyAmount).sum();
                }).reduce(Integer::sum).orElse(0);
                orderRefundAmountMap.put(heCustomerComplaintsEntity.getId(), AmountChangeUtil.f2YScale2(newOrderApplyAmountSum));
            } else {
                int oldOrderApplyAmountSum = orderRefundAllList.stream().filter(item -> item.getId() == heCustomerComplaintsEntity.getRefundOrderId().intValue()).mapToInt(HeOrderRefundEntity::getApplyAmount).sum();
                orderRefundAmountMap.put(heCustomerComplaintsEntity.getId(), AmountChangeUtil.f2YScale2(oldOrderApplyAmountSum));
            }
        }
        return orderRefundAmountMap;

    }

    private List<Integer> getComplaintLevelList(List<Integer> complaintLevelList) {

        List<HeCustomerComplaintsTypeEntity> complaintsTypeEntities = heCustomerComplaintsTypeRepository.getAll();
        if (CollectionUtils.isEmpty(complaintsTypeEntities)) {
            return Lists.newArrayList(-1);
        }
        List<Integer> resultList = new ArrayList<>(complaintLevelList);
        do {
            List<Integer> finalComplaintLevelList = complaintLevelList;
            List<HeCustomerComplaintsTypeEntity> complaintsTypeSubList = complaintsTypeEntities.stream().filter(item -> finalComplaintLevelList.contains(item.getParentId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(complaintsTypeSubList)) {
                break;
            }
            complaintLevelList = complaintsTypeSubList.stream().map(HeCustomerComplaintsTypeEntity::getId).map(Math::toIntExact).collect(Collectors.toList());
            resultList.addAll(complaintLevelList);
        } while (true);
        return resultList;
    }

    private Map<Integer, CfgStoreEntity> getStoreInfoMap(Set<Integer> storeIdList) {

        Map<Integer, CfgStoreEntity> storeInfoMap = new HashMap<>();
        storeIdList.forEach(storeId -> {
            CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(storeId);
            if (Objects.nonNull(cfgStoreEntity)) {
                storeInfoMap.put(storeId, cfgStoreEntity);
            }
        });
        return storeInfoMap;
    }

    private ComplaintsOrderInfoVO buildComplaintsOrderInfoVO(HeOrderEntity heOrderEntity, Map<Integer, CfgStoreEntity> storeInfoMap) {

        ComplaintsOrderInfoVO complaintsOrderInfoVO = new ComplaintsOrderInfoVO();
        complaintsOrderInfoVO.setOrderId(heOrderEntity.getOrderId());
        complaintsOrderInfoVO.setOrderSn(heOrderEntity.getOrderSn());
        complaintsOrderInfoVO.setStoreId(heOrderEntity.getStoreId());
        complaintsOrderInfoVO.setCreatedAt(DateUtils.formatNormal(heOrderEntity.getCreatedAt()));
        Optional.ofNullable(heOrderEntity.getStoreId()).ifPresent(storeId -> {
            CfgStoreEntity cfgStoreEntity = storeInfoMap.get(storeId);
            complaintsOrderInfoVO.setStoreName(Objects.isNull(cfgStoreEntity) ? StringUtils.EMPTY : cfgStoreEntity.getStoreName());
            complaintsOrderInfoVO.setProvince(Objects.isNull(cfgStoreEntity) ? null : cfgStoreEntity.getProvince());
            complaintsOrderInfoVO.setCity(Objects.isNull(cfgStoreEntity) ? null : cfgStoreEntity.getCity());
            complaintsOrderInfoVO.setRegion(Objects.isNull(cfgStoreEntity) ? null : cfgStoreEntity.getRegion());
            complaintsOrderInfoVO.setAddress(Objects.isNull(cfgStoreEntity) ? StringUtils.EMPTY : cfgStoreEntity.getAddress());
        });
        this.fillComplaintsOrderInfoVO(complaintsOrderInfoVO);
        return complaintsOrderInfoVO;
    }

    private void fillComplaintsOrderInfoVO(ComplaintsOrderInfoVO resVO) {

        Assert.notNull(resVO.getProvince(), "省份不能为空");
        CfgDistrictEntity oneById = districtRepository.getOneById(resVO.getProvince());
        if (Objects.nonNull(oneById)) {
            resVO.setProvinceStr(oneById.getName());
        }
        Assert.notNull(resVO.getCity(), "城市不能为空");
        CfgDistrictEntity city = districtRepository.getOneById(resVO.getCity());
        if (Objects.nonNull(city)) {
            resVO.setCityStr(city.getName());
        }
        Assert.notNull(resVO.getRegion(), "区域不能为空");
        CfgDistrictEntity region = districtRepository.getOneById(resVO.getRegion());
        if (Objects.nonNull(region)) {
            resVO.setRegionStr(region.getName());
        }
    }

    private String getBrandName(Integer type, Integer childType) {

        if (StoreTypeEnum.SAINT_BELLA.getCode().equals(type)) {
            if (StoreChildTypeEnum.BELLA_VILLA.getCode().equals(childType)) {
                return StoreChildTypeEnum.BELLA_VILLA.getValue();
            }
            return StoreTypeEnum.SAINT_BELLA.getValue();
        }
        if (StoreTypeEnum.BABY_BELLA.getCode().equals(type)) {
            if (StoreChildTypeEnum.BABY_BELLA_DELUXE.getCode().equals(childType)) {
                return StoreChildTypeEnum.BABY_BELLA_DELUXE.getValue();
            }
            return StoreTypeEnum.BABY_BELLA.getValue();
        }
        return StoreTypeEnum.getValueByCode(type);
    }

    private List<Integer> getStoreIdByStoreBrand(List<Long> reqList) {

        List<Integer> cfgStoreIdList = Lists.newArrayList();
        List<BrandVO> brandListVOS = brandClient.queryBrandListBy(reqList);
        if (CollectionUtils.isEmpty(brandListVOS)) {
            return cfgStoreIdList;
        }
        List<CfgStoreEntity> cfgStoreEntities = storeRepository.queryStoreBaseByCondition(StoreQueryCondition.builder().build());
        if (CollectionUtils.isEmpty(cfgStoreEntities)) {
            return cfgStoreIdList;
        }
        Map<Long, Integer> brandMap = brandListVOS.stream().filter(item -> BrandTypeEnum.MAIN_BRAND.getCode().equals(item.getBrandType())).collect(Collectors.toMap(BrandVO::getId, BrandVO::getBrandVal));
        brandMap.forEach((key, value) -> {
            List<Integer> idList = cfgStoreEntities.stream().filter(store -> Objects.nonNull(store.getType()) && store.getType().equals(value)).map(CfgStoreEntity::getStoreId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(idList)) {
                cfgStoreIdList.addAll(idList);
            }
        });
        Map<Long, List<BrandVO>> subBrandMap = brandListVOS.stream().filter(item -> BrandTypeEnum.CHILD_BRAND.getCode().equals(item.getBrandType())).collect(Collectors.groupingBy(BrandVO::getParentId));
        List<Long> parentIdList = brandListVOS.stream().filter(item -> BrandTypeEnum.CHILD_BRAND.getCode().equals(item.getBrandType()) && Objects.nonNull(item.getParentId())).map(BrandVO::getParentId).collect(Collectors.toList());
        List<BrandVO> parentBrandListVOS = brandClient.queryBrandListBy(parentIdList);
        if (CollectionUtils.isEmpty(parentBrandListVOS)) {
            return cfgStoreIdList;
        }
        Map<Long, Integer> parentBrandMap = parentBrandListVOS.stream().filter(item -> BrandTypeEnum.MAIN_BRAND.getCode().equals(item.getBrandType())).collect(Collectors.toMap(BrandVO::getId, BrandVO::getBrandVal));
        for (Map.Entry<Long, List<BrandVO>> entry : subBrandMap.entrySet()) {
            Long key = entry.getKey();
            List<BrandVO> valueList = entry.getValue();
            Integer type = parentBrandMap.get(key);
            if (Objects.isNull(type)) {
                continue;
            }
            valueList.forEach(value -> {
                List<Integer> idList = cfgStoreEntities.stream().filter(store -> Objects.nonNull(store.getType()) && Objects.nonNull(store.getChildType()) && store.getType().equals(type) && store.getChildType().equals(value.getBrandVal())).map(CfgStoreEntity::getStoreId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(idList)) {
                    cfgStoreIdList.addAll(idList);
                }
            });
        }
        return cfgStoreIdList;
    }

    private BigDecimal getCompensatedGoodsAmount(Long compensationOrderId) {

        BigDecimal refundAmount = BigDecimal.ZERO.divide(BigDecimal.ONE, 2, RoundingMode.UP);
        if (Objects.isNull(compensationOrderId)) {
            return refundAmount;
        }
        HeOrderEntity heOrderEntity = orderRepository.queryOrderById(compensationOrderId.intValue());
        Assert.notNull(heOrderEntity, "订单记录不存在");
        Assert.notNull(heOrderEntity.getOrderAmount(), "订单原价总金额不能为空");
        return AmountChangeUtil.f2YScale2(heOrderEntity.getOrderAmount());
    }

    private BigDecimal getCompensatedCashAmount(BigDecimal compensatedCashAmount) {

        BigDecimal refundAmount = BigDecimal.ZERO.divide(BigDecimal.ONE, 2, RoundingMode.UP);
        if (Objects.isNull(compensatedCashAmount) || compensatedCashAmount.compareTo(BigDecimal.ZERO) == 0) {
            return refundAmount;
        }
        return compensatedCashAmount.divide(BigDecimal.ONE, 2, RoundingMode.UP);
    }

    private BigDecimal getRoundingMode(BigDecimal amount) {

        if (Objects.isNull(amount) || amount.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO.divide(BigDecimal.ONE, 2, RoundingMode.UP);
        }
        return amount.divide(BigDecimal.ONE, 2, RoundingMode.UP);
    }


}

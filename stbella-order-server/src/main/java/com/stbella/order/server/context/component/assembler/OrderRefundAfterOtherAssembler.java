package com.stbella.order.server.context.component.assembler;

import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.core.OrderRefundStatusEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.async.AsyncOrder;
import com.stbella.order.server.order.month.enums.OrderEventEnum;
import com.stbella.order.server.order.month.service.MonthOrderWxCommandService;
import com.stbella.order.server.producer.OrderEventProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

@Component
@Slf4j
@SnowballComponent(name = "退款都成功后其他处理", desc = "退款都成功后其他处理")
public class OrderRefundAfterOtherAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private MonthOrderWxCommandService monthOrderWxCommandService;
    @Resource
    private OrderEventProducer orderEventProducer;
    @Resource
    private OrderRepository orderRepository;

    @Override
    public boolean condition(FlowContext context) {
        return (boolean) context.getAttribute(OtherConstant.CONTINUE);
    }

    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        boolean deposit = orderEntity.isDepositOrder();
        //非押金处理
        HeOrderRefundEntity refundEntity = bizContext.getAttribute(HeOrderRefundEntity.class);
        //发送订单变更通知
        monthOrderWxCommandService.syncOrderRefundStatusForNew(refundEntity.getOrderId());
        if (!deposit) {
            //异步退款-积分处理
            HeOrderEntity heOrder = orderRepository.getByOrderId(refundEntity.getOrderId());
            log.info("退款审批（原路退回）-积分处理,订单参数={},退款参数={}", heOrder, refundEntity);
            //同步退款状态
            if (heOrder.getRefundStatus().equals(OrderRefundStatusEnum.PARTIAL_REFUND.getCode())) {
                orderEventProducer.sendMq(OrderEventEnum.PART_REFUND.getCode(), heOrder.getOrderSn(), refundEntity.getRefundNature());
            } else if (heOrder.getRefundStatus().equals(OrderRefundStatusEnum.FULL_REFUND.getCode())) {
                orderEventProducer.sendMq(OrderEventEnum.FULL_REFUND.getCode(), heOrder.getOrderSn(), refundEntity.getRefundNature());
            }
        }
    }


}

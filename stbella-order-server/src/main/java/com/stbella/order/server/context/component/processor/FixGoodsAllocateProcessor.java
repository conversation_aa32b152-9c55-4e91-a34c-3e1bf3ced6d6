package com.stbella.order.server.context.component.processor;

import com.alibaba.fastjson.JSONObject;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.service.OrderGoodsDomainService;
import com.stbella.order.domain.repository.OrderGoodsRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.List;

/**
 * 商品价格分摊处理器
 * 优化点：
 * 1. 提取核心逻辑到独立方法
 * 2. 增加尾差处理
 * 3. 增加参数校验
 * 4. 优化日志输出
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "修复商品价格信息分摊", desc = "商品价格信息分摊")
public class FixGoodsAllocateProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderGoodsRepository orderGoodsRepository;

    @Resource
    private OrderGoodsDomainService orderGoodsDomainService;


    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        if (orderEntity == null || orderEntity.getOrderId() == null) {
            log.error("订单信息为空，无法进行价格分摊");
            return;
        }
        List<HeOrderGoodsEntity> orderGoodsList = orderGoodsDomainService.queryOrderGoodsWithExpand(orderEntity.getOrderId());
        for (HeOrderGoodsEntity heOrderGoodsEntity : orderGoodsList) {
            if (heOrderGoodsEntity.getNum() == 0) {
                heOrderGoodsEntity.setNum(1);
                heOrderGoodsEntity.setSkuNum(heOrderGoodsEntity.getGoodsNum());
            }
        }
        log.info("开始处理订单{}的商品价格分摊", orderEntity.getOrderId());
        List<HeOrderGoodsEntity> orderGoodsEntities = GoodsAllocateUtil.processGoodsAllocation(orderEntity, orderGoodsList);
        log.info("订单{}的商品价格分摊处理完成,结果为{}", orderEntity.getOrderId(), JSONObject.toJSONString(orderGoodsEntities));
        orderGoodsRepository.batchUpdate(orderGoodsEntities);

    }

}
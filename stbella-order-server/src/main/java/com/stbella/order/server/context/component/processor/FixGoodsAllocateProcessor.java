package com.stbella.order.server.context.component.processor;

import com.alibaba.fastjson.JSONObject;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.domain.order.service.OrderGoodsDomainService;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.domain.repository.OrderGoodsRepository;
import com.stbella.order.domain.repository.OrderReductionRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.List;

/**
 * 商品价格分摊处理器
 * 优化点：
 * 1. 提取核心逻辑到独立方法
 * 2. 增加尾差处理
 * 3. 增加参数校验
 * 4. 优化日志输出
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "修复商品价格信息分摊", desc = "商品价格信息分摊")
public class FixGoodsAllocateProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderGoodsRepository orderGoodsRepository;

    @Resource
    private OrderGoodsDomainService orderGoodsDomainService;

    @Resource
    private OrderReductionRepository orderReductionRepository;

    @Resource
    private IncomeRecordRepository incomeRecordRepository;


    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        if (orderEntity == null || orderEntity.getOrderId() == null) {
            log.error("订单信息为空，无法进行价格分摊");
            return;
        }
        List<HeOrderGoodsEntity> orderGoodsList = orderGoodsDomainService.queryOrderGoodsWithExpand(orderEntity.getOrderId());
        for (HeOrderGoodsEntity heOrderGoodsEntity : orderGoodsList) {
            if (heOrderGoodsEntity.getNum() == 0) {
                heOrderGoodsEntity.setNum(1);
                heOrderGoodsEntity.setSkuNum(heOrderGoodsEntity.getGoodsNum());
            }
        }
        //增加判断orderEntity 是否被减免，并产生减免收款记录，如果发生减免，没有收款记录，那金额payAmount需要减去减免的金额
        processOrderReductionAndPayment(orderEntity);
        
        log.info("开始处理订单{}的商品价格分摊", orderEntity.getOrderId());
        List<HeOrderGoodsEntity> orderGoodsEntities = GoodsAllocateUtil.processGoodsAllocation(orderEntity, orderGoodsList);
        log.info("订单{}的商品价格分摊处理完成,结果为{}", orderEntity.getOrderId(), JSONObject.toJSONString(orderGoodsEntities));
        orderGoodsRepository.batchUpdate(orderGoodsEntities);

    }

    /**
     * 处理订单减免和收款记录
     * 如果订单有减免但没有对应的收款记录，需要调整payAmount金额
     */
    private void processOrderReductionAndPayment(HeOrderEntity orderEntity) {
        Long orderId = orderEntity.getOrderId().longValue();
        log.info("开始处理订单[{}]的减免和收款记录", orderId);

        try {
            // 查询订单减免记录
            OrderReductionEntity reductionEntity = orderReductionRepository.selectFirstReductionEntity(orderId);
            if (reductionEntity == null || !reductionEntity.isSuccessAuthState()) {
                log.info("订单[{}]无有效减免记录或减免审批未通过", orderId);
                return;
            }

            log.info("订单[{}]存在减免记录，减免金额: {}", orderId, reductionEntity.getDecreaseAmount());

            // 查询是否存在减免类型的收款记录
            List<HeIncomeRecordEntity> reductionPayments = incomeRecordRepository
                    .getAllRecordListByOrderId(orderEntity.getOrderId())
                    .stream()
                    .filter(payment -> OmniPayTypeEnum.REDUCTION.getCode().equals(payment.getPayType()))
                    .collect(java.util.stream.Collectors.toList());

            if (reductionPayments.isEmpty()) {
                // 没有减免收款记录，需要创建并调整payAmount
                log.info("订单[{}]没有减免收款记录，开始创建减免收款记录", orderId);
//                createReductionPaymentRecord(orderEntity, reductionEntity);
                
                // 调整订单payAmount，减去减免金额
                int originalPayAmount = orderEntity.getPayAmount();
//                int adjustedPayAmount = originalPayAmount - reductionEntity.getDecreaseAmount().intValue();
                orderEntity.setPayAmount(orderEntity.getPayAmount()-orderEntity.getTotalReductionAmount()); // 确保不会为负数
                
                log.info("订单[{}]payAmount已调整: {} -> {}, 减免金额: {}", 
                        orderId, originalPayAmount, orderEntity.getPayAmount(), reductionEntity.getDecreaseAmount());
            } else {
                log.info("订单[{}]已存在减免收款记录，数量: {}", orderId, reductionPayments.size());
            }
            
        } catch (Exception e) {
            log.error("处理订单[{}]减免和收款记录时发生异常", orderId, e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 创建减免收款记录
     */
    private void createReductionPaymentRecord(HeOrderEntity orderEntity, OrderReductionEntity reductionEntity) {
        HeIncomeRecordEntity reductionIncome = new HeIncomeRecordEntity();
        reductionIncome.setOrderId(orderEntity.getOrderId())
                .setBasicUid(orderEntity.getBasicUid())
                .setClientUid(orderEntity.getClientUid())
                .setStoreId(orderEntity.getStoreId())
                .setPayType(OmniPayTypeEnum.REDUCTION.getCode())
                .setIncome(reductionEntity.getDecreaseAmount().intValue())
                .setStatus(1) // 已支付
                .setApproveStatus(999) // 无需审核
                .setIsDelete(0)
                .setRemark("系统自动创建减免收款记录")
                .setCreatedAt(System.currentTimeMillis())
                .setUpdatedAt(System.currentTimeMillis());
        
        // 生成收款编号
        String incomeSn = "REDUCTION_" + orderEntity.getOrderId() + "_" + System.currentTimeMillis();
        reductionIncome.setIncomeSn(incomeSn);
        
        // 插入减免收款记录
        incomeRecordRepository.saveOne(reductionIncome);
        log.info("订单[{}]减免收款记录创建成功，收款编号: {}, 金额: {}", 
                orderEntity.getOrderId(), incomeSn, reductionEntity.getDecreaseAmount());
    }

}
package com.stbella.order.server.order.month.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.core.date.DatePattern;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stbella.base.server.ding.DingService;
import com.stbella.core.base.PageVO;
import com.stbella.core.base.UserTokenInfoDTO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.core.utils.ExcelUtils;
import com.stbella.core.utils.JwtUtil;
import com.stbella.cts.service.BaseSitterService;
import com.stbella.customer.server.cts.entity.CustomerInfoCtsPO;
import com.stbella.customer.server.cts.request.CustomerInfoCtsRequest;
import com.stbella.customer.server.cts.service.CustomerInfoCtsService;
import com.stbella.customer.server.customer.service.dubbo.CustomerManagerService;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.month.PayChannelTypeEnum;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.server.contract.entity.UserESignPO;
import com.stbella.order.server.contract.service.UserESignService;
import com.stbella.order.server.convert.OrderConvert;
import com.stbella.order.server.convert.PayRecordConvert;
import com.stbella.order.server.manager.PayManager;
import com.stbella.order.server.manager.StoreManager;
import com.stbella.order.server.order.cts.constant.RedisConstant;
import com.stbella.order.server.order.cts.dto.RefundNotifyMqDTO;
import com.stbella.order.server.order.cts.entity.*;
import com.stbella.order.server.order.cts.enums.*;
import com.stbella.order.server.order.cts.entity.OrderCtsApplyPayPO;
import com.stbella.order.server.order.cts.entity.OrderCtsApplyRefundPO;
import com.stbella.order.server.order.cts.entity.OrderCtsPO;
import com.stbella.order.server.order.cts.entity.OrderCtsPaySignPO;
import com.stbella.order.server.order.cts.entity.OrderCtsSitterSnapshotPO;
import com.stbella.order.server.order.cts.enums.*;
import com.stbella.order.server.order.cts.excel.OfflinePayRecordExcel;
import com.stbella.order.server.order.cts.entity.*;
import com.stbella.order.server.order.cts.enums.*;
import com.stbella.order.server.order.cts.excel.OfflinePayRecordExcel;
import com.stbella.order.server.order.cts.request.order.OfflinePayAdminPageQuery;
import com.stbella.order.server.order.cts.request.order.OfflinePayAuditRequest;
import com.stbella.order.server.order.cts.request.order.OfflineRefundAuditRequest;
import com.stbella.order.server.order.cts.request.order.SelectVO;
import com.stbella.order.server.order.cts.response.order.*;
import com.stbella.order.server.order.cts.service.*;
import com.stbella.order.server.order.cts.statemachine.OrderStateEvent;
import com.stbella.order.server.order.cts.statemachine.OrderStateMachineService;
import com.stbella.order.server.order.month.entity.OrderPayRecordPO;
import com.stbella.order.server.order.month.enums.*;
import com.stbella.order.server.order.month.request.pay.OfflinePayRequest;
import com.stbella.order.server.order.month.request.pay.PayRequest;
import com.stbella.order.server.order.month.request.standard.PayRecordDetailQueryRequest;
import com.stbella.order.server.order.month.response.PayRecordDetailVO;
import com.stbella.order.server.order.month.service.OrderPayRecordService;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.order.server.order.nutrition.dto.PayNotifyMqDTO;
import com.stbella.order.server.order.nutrition.entity.OrderNutritionMonthFoodPO;
import com.stbella.order.server.order.nutrition.mapper.OrderNutritionMonthFoodMapper;
import com.stbella.order.server.pay.PayService;
import com.stbella.order.server.utils.IdGenUtils;
import com.stbella.pay.server.alipay.enums.AccountTypeEnum;
import com.stbella.pay.server.cmbpay.request.RefundRequest;
import com.stbella.pay.server.cmbpay.vo.RefundRespVO;
import com.stbella.pay.server.core.enums.CmbRefundStatusEnum;
import com.stbella.pay.server.core.kit.WxPayKit;
import com.stbella.pay.server.entity.*;
import com.stbella.redis.service.RedisService;
import com.stbella.redisson.DistributedLocker;
import com.stbella.sso.server.dingding.entity.DdEmployeePO;
import com.stbella.sso.server.dingding.service.DdEmployeeService;
import com.stbella.store.server.cts.entity.CtsSitePO;
import com.stbella.store.server.cts.service.CtsSiteService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: PayServiceImpl
 * @date 2021/12/15 8:15 下午
 */
@Service
@DubboService
@Slf4j
public class PayServiceImpl implements PayService {

    @Resource
    private PayManager payManager;
    @Resource
    private OrderPayRecordService orderPayRecordService;

    @Resource
    private OrderCtsApplyRefundService orderCtsApplyRefundService;
    @Resource
    private PayRecordConvert payRecordConvert;
    @DubboReference
    private DingService dingService;
    @Resource
    private StoreManager storeManager;
    @Resource
    private OrderNutritionMonthFoodMapper orderNutritionMonthFoodMapper;
    @Resource
    private OrderCtsService orderCtsService;
    @Resource
    private CtsPayService ctsPayService;
    @Resource
    private OrderCtsApplyPayService orderCtsApplyPayService;
    @Resource
    private OrderStateMachineService orderStateMachineService;
    @DubboReference
    private CtsSiteService ctsSiteService;
    @Resource
    private UserESignService userESignService;
    @Resource
    private OrderConvert orderConvert;
    @Resource
    private OrderCtsPaySignService orderCtsPaySignService;
    @DubboReference
    private DdEmployeeService ddEmployeeService;
    @Resource
    private RedisService redisService;
    @Resource
    private DistributedLocker redisson;
    @DubboReference
    private CustomerManagerService customerManagerService;
    @DubboReference
    private CustomerInfoCtsService customerInfoCtsService;
    @Resource
    private OrderCtsSitterSnapshotService orderCtsSitterSnapshotService;
    @DubboReference
    private BaseSitterService baseSitterService;
    @Resource
    private OrderFacade orderFacade;
    @Autowired
    private ApplicationContext context;

    private final static String CODE = "code";

    private final static String POS_SUCCESS_CODE = "200";
    //线下支付类型
    private final static List<Integer> offlinePayTypes = Arrays.asList(CtsPayTypeEnum.CASH.getCode(), CtsPayTypeEnum.OTHER.getCode(), CtsPayTypeEnum.REMITTANCE.getCode());

    //填写支付凭证=>审核=> 发起退款=>审核
    //如果驳回 就重新发起

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean offlinePay(OfflinePayRequest request) {
        Integer accountType = request.getAccountType();

        OrderCtsApplyPayPO orderCtsApplyPayPO = new OrderCtsApplyPayPO();

        if (!Objects.equals(accountType, AccountTypeEnum.CTS.getCode())) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "非到家不支持线下支付");
        }
        final OrderCtsPO orderCtsPO = orderCtsService.getByOrderNo(request.getOrderNo());
        if (Objects.isNull(orderCtsPO)) {
            throw new BusinessException(ResultEnum.NOT_EXIST);
        }

        String localTransactionalNo = IdGenUtils.createLocalTransactionalNo(request.getAccountType(), new Date());
        //生成支付记录并初始化
        OrderPayRecordPO orderPayRecordPO = new OrderPayRecordPO();
        orderPayRecordPO.setPayType(request.getPayType());
        orderPayRecordPO.setAuditStatus(PayRecordAuditStatusEnum.AUDIT.getCode());
        orderPayRecordPO.setOrderType(orderCtsPO.getOrderType());
        orderPayRecordPO.setOrderNo(request.getOrderNo());
        orderPayRecordPO.setLocalTransactionalNo(localTransactionalNo);
        orderPayRecordPO.setRecordType(RecordTypeEnum.PAY.getCode());
        final OrderCtsPaySignPO paySignPO = orderCtsPaySignService.getByPaySign(request.getPaySign());
        if (paySignPO != null) {
            orderPayRecordPO.setAmountType(paySignPO.getAmountType());
            orderPayRecordPO.setPayAmount(paySignPO.getTargetPayAmount());
        }
        orderPayRecordPO.setPayStatus(PayStatusEnum.PAY_STATUS_PROCESS.getCode());
        orderPayRecordPO.setPayTime(new Date());
        orderPayRecordService.save(orderPayRecordPO);

        if (Objects.equals(accountType, AccountTypeEnum.CTS.getCode()) && StringUtils.isNotBlank(request.getPaySign())) {
            ctsPayService.savePaySignRecord(request.getPaySign(), localTransactionalNo);
        }

        orderCtsApplyPayPO.setOrderNo(orderCtsPO.getOrderNo());
        orderCtsApplyPayPO.setOrderType(orderCtsPO.getOrderType());
        orderCtsApplyPayPO.setCtsSiteId(orderCtsPO.getCtsSiteId());
        orderCtsApplyPayPO.setCustomerId(orderCtsPO.getCustomId());
        orderCtsApplyPayPO.setSitterId(orderCtsPO.getSitterId());
        orderCtsApplyPayPO.setPayAmount(orderPayRecordPO.getPayAmount());
        orderCtsApplyPayPO.setPayTime(orderPayRecordPO.getPayTime());
        orderCtsApplyPayPO.setPayRecordId(orderPayRecordPO.getId());
        orderCtsApplyPayPO.setPayType(orderPayRecordPO.getPayType());
        orderCtsApplyPayPO.setLocalTransactionalNo(localTransactionalNo);
        orderCtsApplyPayPO.setAmountType(orderPayRecordPO.getAmountType());
        orderCtsApplyPayPO.setPayProof(request.getPayProof());
        orderCtsApplyPayPO.setSellRemark(request.getSellRemark());

        orderCtsApplyPayPO.setAuditStatus(OfflineAuditStatusEnum.UNDER_REVIEW.getCode());
        orderCtsApplyPayPO.setCreateBy(JwtUtil.getJwtTokenUserInfo().getUserId());
        orderCtsApplyPayPO.setCreateByName(JwtUtil.getJwtTokenUserInfo().getUserName());
        return orderCtsApplyPayService.save(orderCtsApplyPayPO);
    }



    @Override
    public List<CtsMiniProgramOfflinePayRecordVO> offlinePayList(String orderNo) {
        List<OrderCtsApplyPayPO> list = orderCtsApplyPayService.list(new LambdaQueryWrapper<OrderCtsApplyPayPO>()
                .eq(OrderCtsApplyPayPO::getOrderNo, orderNo));
        Map<Long, Integer> ret = new HashMap<>();
        if(!list.isEmpty()){
            List<Long> payRecordIds = list.stream().map(OrderCtsApplyPayPO::getPayRecordId).collect(Collectors.toList());
            ret.putAll(orderPayRecordService.list(new LambdaQueryWrapper<OrderPayRecordPO>()
                    .in(OrderPayRecordPO::getId, payRecordIds)).stream().collect(Collectors.toMap(OrderPayRecordPO::getId, OrderPayRecordPO::getPayStatus)));
        }
        Function<OrderCtsApplyPayPO, CtsMiniProgramOfflinePayRecordVO> offlinePayRecordVOFunction = item -> {
            CtsMiniProgramOfflinePayRecordVO ctsMiniProgramOfflinePayRecordVO = new CtsMiniProgramOfflinePayRecordVO();
            ctsMiniProgramOfflinePayRecordVO.setAuditStatus(item.getAuditStatus());
            ctsMiniProgramOfflinePayRecordVO.setGmtCreate(item.getGmtCreate());
            ctsMiniProgramOfflinePayRecordVO.setPayAmount(item.getPayAmount());
            ctsMiniProgramOfflinePayRecordVO.setPayStatus(ret.get(item.getPayRecordId()));
            ctsMiniProgramOfflinePayRecordVO.setCreateByName(item.getCreateByName());
            ctsMiniProgramOfflinePayRecordVO.setDisagreeReason(item.getDisagreeReason());
            ctsMiniProgramOfflinePayRecordVO.setAccountTime(item.getAccountTime());
            ctsMiniProgramOfflinePayRecordVO.setActuallyPayAmount(item.getActuallyPayAmount());
            ctsMiniProgramOfflinePayRecordVO.setAuditUserName(item.getAuditUserName());
            return ctsMiniProgramOfflinePayRecordVO;
        };

        return list.stream().map(offlinePayRecordVOFunction).collect(Collectors.toList());
    }

    Function<OfflinePayAdminAuditVO, OfflinePayRecordExcel> payRecordExcelFunction = item->{
        OfflinePayRecordExcel offlinePayRecordExcel = new OfflinePayRecordExcel();
        offlinePayRecordExcel.setPaySubjectName(item.getPaySubjectName());
        offlinePayRecordExcel.setLocalTransactionalNo(item.getLocalTransactionalNo());
        offlinePayRecordExcel.setTransactionalNo(item.getTransactionalNo());
        offlinePayRecordExcel.setPayAmount(item.getPayAmount());
        offlinePayRecordExcel.setActuallyPayAmount(item.getActuallyPayAmount());

        if (item.getAccountTime() != null) {
            offlinePayRecordExcel.setAccountTime(DateUtil.format(item.getAccountTime(), DatePattern.NORM_DATETIME_PATTERN));
        }
        if(item.getAuditStatus() != null){
            offlinePayRecordExcel.setAuditStatusName(PayRecordAuditStatusEnum.getValueByCode(item.getAuditStatus()));
        }
        offlinePayRecordExcel.setDisagreeReason(item.getDisagreeReason());
        offlinePayRecordExcel.setOrderNo(item.getOrderNo());
        if(item.getOrderType() != null){
            offlinePayRecordExcel.setOrderTypeName(OrderTypeEnum.getValueByCode(item.getOrderType()));
        }
        offlinePayRecordExcel.setCustomerName(item.getCustomerName());
        offlinePayRecordExcel.setPhoneNum(item.getPhone());
        offlinePayRecordExcel.setSignSiteName(item.getCtsSiteName());
        offlinePayRecordExcel.setCreateByName(item.getCreateByName());
        if (item.getGmtCreate() != null) {
            offlinePayRecordExcel.setGmtCreate(DateUtil.format(item.getGmtCreate(), DatePattern.NORM_DATETIME_PATTERN));
        }
        offlinePayRecordExcel.setAuditUserName(item.getAuditUserName());
        if (item.getAuditTime() != null) {
            offlinePayRecordExcel.setAuditTime(DateUtil.format(item.getAuditTime(), DatePattern.NORM_DATETIME_PATTERN));
        }

        return offlinePayRecordExcel;
    };

    @SneakyThrows
    @Override
    public void exportExcel(OfflinePayAdminPageQuery offlinePayAdminPageQuery, HttpServletResponse httpServletResponse){
        offlinePayAdminPageQuery.setPageSize(Integer.MAX_VALUE);
        PageVO<OfflinePayAdminAuditVO> offlinePayAdminAuditVOPageVO = this.queryPayAuditPage(offlinePayAdminPageQuery);
        List<OfflinePayAdminAuditVO> list = offlinePayAdminAuditVOPageVO.getList();
        List<OfflinePayRecordExcel> collect = list.stream().map(payRecordExcelFunction).collect(Collectors.toList());
        try {
            ExcelUtils.exportExcel(httpServletResponse,"线下支付记录",collect,OfflinePayRecordExcel.class);
        } catch (IOException e) {
            throw new BusinessException(ResultEnum.FILE_ERROR.getCode(), "线下支付列表导出异常");
        }
    }

    @Override
    public PageVO<OfflinePayAdminAuditVO> queryPayAuditPage(OfflinePayAdminPageQuery offlinePayAdminPageQuery) {
        Set<String> orderNosByCustomer = new HashSet<>();
        if (StringUtils.isNoneBlank(offlinePayAdminPageQuery.getCustomerName()) || StringUtils.isNotBlank(offlinePayAdminPageQuery.getMobile())) {
            orderNosByCustomer = orderFacade.getOrderNosByCustomer(offlinePayAdminPageQuery.getCustomerName(), offlinePayAdminPageQuery.getMobile());
        }
        //序列化过来的时候 Integer 和Long 默认值被赋了0
        Page<OrderCtsApplyPayPO> page = orderCtsApplyPayService.page(new Page(offlinePayAdminPageQuery.getPageNum(), offlinePayAdminPageQuery.getPageSize()),
                new LambdaQueryWrapper<OrderCtsApplyPayPO>()
                        .eq(offlinePayAdminPageQuery.getOrderType() != null && offlinePayAdminPageQuery.getOrderType() != 0, OrderCtsApplyPayPO::getOrderType, offlinePayAdminPageQuery.getOrderType())
                        .eq(offlinePayAdminPageQuery.getPaySubject()!=null, OrderCtsApplyPayPO::getPaySubject, offlinePayAdminPageQuery.getPaySubject())
                        .eq(StringUtils.isNotBlank(offlinePayAdminPageQuery.getOrderNo()), OrderCtsApplyPayPO::getOrderNo, offlinePayAdminPageQuery.getOrderNo())
                        .eq(StringUtils.isNotBlank(offlinePayAdminPageQuery.getLocalTransactionNo()), OrderCtsApplyPayPO::getLocalTransactionalNo, offlinePayAdminPageQuery.getLocalTransactionNo())
                        .eq(StringUtils.isNotBlank(offlinePayAdminPageQuery.getTransactionNo()), OrderCtsApplyPayPO::getTransactionalNo, offlinePayAdminPageQuery.getTransactionNo())
                        .eq(offlinePayAdminPageQuery.getAuditStatus() != null && offlinePayAdminPageQuery.getAuditStatus() != 0, OrderCtsApplyPayPO::getAuditStatus, offlinePayAdminPageQuery.getAuditStatus())
                        .eq(offlinePayAdminPageQuery.getCreateId() != null && offlinePayAdminPageQuery.getCreateId() != 0, OrderCtsApplyPayPO::getCreateBy, offlinePayAdminPageQuery.getCreateId())
                        .eq(offlinePayAdminPageQuery.getCtsSiteId() != null && offlinePayAdminPageQuery.getCtsSiteId() != 0, OrderCtsApplyPayPO::getCtsSiteId, offlinePayAdminPageQuery.getCtsSiteId())
                        .eq(offlinePayAdminPageQuery.getAuditUserId() != null && offlinePayAdminPageQuery.getAuditUserId() != 0, OrderCtsApplyPayPO::getAuditUserId, offlinePayAdminPageQuery.getAuditUserId())
                        .in(!orderNosByCustomer.isEmpty(),OrderCtsApplyPayPO::getOrderNo,orderNosByCustomer)
                        .between((offlinePayAdminPageQuery.getAuditStartDate() != null && offlinePayAdminPageQuery.getAuditEndDate() != null), OrderCtsApplyPayPO::getAuditTime, offlinePayAdminPageQuery.getAuditStartDate(), offlinePayAdminPageQuery.getAuditEndDate())
                        .between(offlinePayAdminPageQuery.getCreateStartDate() != null && offlinePayAdminPageQuery.getCreateEndDate() != null, OrderCtsApplyPayPO::getGmtCreate, offlinePayAdminPageQuery.getCreateStartDate(), offlinePayAdminPageQuery.getCreateEndDate())
                        .orderByDesc(OrderCtsApplyPayPO::getGmtCreate));
        List<OrderCtsApplyPayPO> records = page.getRecords();

        List<Long> siteIdList = records.stream().map(OrderCtsApplyPayPO::getCtsSiteId)
                                                .filter(Objects::nonNull).collect(Collectors.toList());
        List<String> orderIds = records.stream().map(OrderCtsApplyPayPO::getOrderNo).collect(Collectors.toList());
        List<CtsSitePO> ctsSiteList = ctsSiteService.listSitesByIds(new ArrayList<>(siteIdList));
        Map<Long, String> siteNameMapById = ctsSiteList.stream().collect(Collectors.toMap(CtsSitePO::getId, CtsSitePO::getSiteName,(oldValue,newValue) -> newValue));

        HashMap<String, AdminCustomerVO> customerVOHashMap = orderFacade.customerByOrderIds(orderIds);
        Function<OrderCtsApplyPayPO,OfflinePayAdminAuditVO> fun = i->{
            OfflinePayAdminAuditVO offlinePayAdminAuditVO = new OfflinePayAdminAuditVO();
            BeanUtil.copyProperties(i,offlinePayAdminAuditVO);
            offlinePayAdminAuditVO.setCtsSiteName(siteNameMapById.get(i.getCtsSiteId()));
            AdminCustomerVO adminCustomerVO = customerVOHashMap.get(i.getOrderNo());
            if(adminCustomerVO != null){
                offlinePayAdminAuditVO.setPhone(adminCustomerVO.getMobile());
                offlinePayAdminAuditVO.setCustomerName(adminCustomerVO.getName());
            }
            if(i.getPaySubject() != null){
                offlinePayAdminAuditVO.setPaySubjectName(PaySubjectEnum.getValueByCode(i.getPaySubject()));
            }
            return offlinePayAdminAuditVO;
        };
        return new PageVO(records.stream().map(fun).collect(Collectors.toList()), (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
    }

    @Override
    public Map<String, List<SelectVO>> querySelectVO() {
        HashMap<String, List<SelectVO>> ret = new HashMap<>();
        List<OrderCtsApplyPayPO> list = orderCtsApplyPayService.list(new LambdaQueryWrapper<>());
        if(list.isEmpty()){
            return ret;
        }
        List<Long> auditEmployeeIds = list.stream().map(OrderCtsApplyPayPO::getAuditUserId).distinct().collect(Collectors.toList());
        List<DdEmployeePO> auditEmployeePOS = ddEmployeeService.queryEmployeeByIds(auditEmployeeIds);
        ret.put("auditSelect", auditEmployeePOS.stream().map(employeeFun).collect(Collectors.toList()));

        List<Long> createEmployeeIds = list.stream().map(OrderCtsApplyPayPO::getCreateBy).distinct().collect(Collectors.toList());
        List<DdEmployeePO> createEmployeePOS = ddEmployeeService.queryEmployeeByIds(createEmployeeIds);
        ret.put("createSelect", createEmployeePOS.stream().map(employeeFun).collect(Collectors.toList()));

        List<Long> ctsSiteIds = list.stream().map(OrderCtsApplyPayPO::getCtsSiteId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        List<CtsSitePO> ctsSitePOS = ctsSiteService.listSitesByIds(ctsSiteIds);
        ret.put("ctsSiteSelect", ctsSitePOS.stream().map(ctsSiteFun).collect(Collectors.toList()));
        return ret;
    }

    Function<DdEmployeePO, SelectVO> employeeFun = i -> {
        SelectVO selectVO = new SelectVO();
        selectVO.setId(i.getId());
        selectVO.setName(i.getName());
        return selectVO;
    };

    Function<CtsSitePO, SelectVO> ctsSiteFun = i -> {
        SelectVO selectVO = new SelectVO();
        selectVO.setId(i.getId());
        selectVO.setName(i.getSiteName());
        return selectVO;
    };


    @Override
    public void offlinePayAudit(OfflinePayAuditRequest offlinePayAuditRequest) {
        Integer agree = offlinePayAuditRequest.getAgree();
        AuditAgreeEnum enumByCode = AuditAgreeEnum.getEnumByCode(agree);
        OrderCtsApplyPayPO orderCtsApplyPayData = orderCtsApplyPayService.getOne(new LambdaQueryWrapper<OrderCtsApplyPayPO>().eq(OrderCtsApplyPayPO::getId, offlinePayAuditRequest.getId()));
        if (orderCtsApplyPayData == null) {
            throw new BusinessException(ResultEnum.NOT_EXIST, "申请记录不存在!");
        }

        OrderPayRecordPO orderPayRecordData = orderPayRecordService.getOne(new LambdaQueryWrapper<OrderPayRecordPO>().eq(OrderPayRecordPO::getId, orderCtsApplyPayData.getPayRecordId()));
        if (orderPayRecordData == null) {
            throw new BusinessException(ResultEnum.NOT_EXIST, "支付流水不存在!");
        }

        OrderCtsPO orderCtsPO = orderCtsService.getByOrderNo(orderPayRecordData.getOrderNo());
        if (orderCtsPO == null) {
            throw new BusinessException(ResultEnum.NOT_EXIST, "订单不存在!");
        }

        OrderCtsApplyPayPO orderCtsApplyPayPO = new OrderCtsApplyPayPO();
        orderCtsApplyPayPO.setId(orderCtsApplyPayData.getId());
        OrderPayRecordPO orderPayRecordPO = new OrderPayRecordPO();
        orderPayRecordPO.setId(orderPayRecordData.getId());
        orderPayRecordPO.setPayType(orderCtsApplyPayData.getPayType());
        orderPayRecordPO.setPayAmount(orderCtsApplyPayData.getPayAmount());

        if (AuditAgreeEnum.AGREE.getCode().equals(enumByCode.getCode())) {
            if(offlinePayAuditRequest.getActuallyPayAmount().compareTo(BigDecimal.ZERO) < 0){
                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "确认金额不可小于0!");
            }
            if(offlinePayAuditRequest.getActuallyPayAmount().compareTo(orderCtsApplyPayData.getPayAmount()) > 0){
                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "确认金额不可高于用户输入金额,请联系用户确认!");
            }
            if(orderCtsPO.getStatus().equals(com.stbella.order.server.order.cts.enums.OrderStatusEnum.CLOSE_REFUNDED.getCode())){
                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "当前订单已关单，此笔支付只能驳回，请联系销售，如无需给客户退款，请销售重新建单，提交本笔支付凭证，重新审核！");
            }

            PayRecordDetailQueryRequest payRecordDetailQueryRequest = new PayRecordDetailQueryRequest();
            payRecordDetailQueryRequest.setTransactionalNo(offlinePayAuditRequest.getTransactionalNo());
            List<PayRecordDetailVO> payRecordList = orderPayRecordService.queryPayRecordDetailByRecordType(payRecordDetailQueryRequest);
            if (CollectionUtil.isNotEmpty(payRecordList)){
                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "此三方流水已经使用过了，请重新填写");
            }

            orderCtsApplyPayPO.setAccountTime(offlinePayAuditRequest.getAccountTime());
            orderCtsApplyPayPO.setActuallyPayAmount(offlinePayAuditRequest.getActuallyPayAmount());
            orderCtsApplyPayPO.setAuditProof(offlinePayAuditRequest.getAuditProof());
            orderCtsApplyPayPO.setAuditTime(new Date());
            orderCtsApplyPayPO.setAuditStatus(OfflineAuditStatusEnum.EXAMINATION_PASSED.getCode());
            orderCtsApplyPayPO.setAuditUserId(JwtUtil.getJwtTokenUserInfo().getUserId());
            orderCtsApplyPayPO.setAuditUserName(JwtUtil.getJwtTokenUserInfo().getUserName());
            orderCtsApplyPayPO.setUpdateBy(JwtUtil.getJwtTokenUserInfo().getUserId());
            orderCtsApplyPayPO.setUpdateByName(JwtUtil.getJwtTokenUserInfo().getUserName());
            orderCtsApplyPayPO.setPaySubject(offlinePayAuditRequest.getPaySubject());
            orderCtsApplyPayPO.setTransactionalNo(offlinePayAuditRequest.getTransactionalNo());

            orderPayRecordPO.setAuditStatus(PayRecordAuditStatusEnum.AUDIT_SUCCESS.getCode());
            orderPayRecordPO.setPayStatus(PayStatusEnum.PAY_STATUS_SUCCESS.getCode());
            orderPayRecordPO.setTransactionalNo(offlinePayAuditRequest.getTransactionalNo());
            //TODO 有疑问
            orderPayRecordPO.setPayTime(offlinePayAuditRequest.getAccountTime());
            orderPayRecordPO.setRequestBody(com.alibaba.fastjson.JSONObject.toJSONString(offlinePayAuditRequest));


            //更新订单状态
            final OrderStateEvent event = OrderStateEvent.getPayNotifyEventByOrderType(orderCtsPO.getOrderType());
            PayNotifyMqDTO payNotifyMqDTO = new PayNotifyMqDTO();
            //TODO 和业绩有关
            payNotifyMqDTO.setPayTime(offlinePayAuditRequest.getAccountTime());
            payNotifyMqDTO.setPayType(orderPayRecordData.getPayType());
            payNotifyMqDTO.setPayAmount(orderPayRecordData.getPayAmount());
            payNotifyMqDTO.setTransactionalNo(offlinePayAuditRequest.getTransactionalNo());
            payNotifyMqDTO.setOutTradeNo(orderCtsApplyPayData.getLocalTransactionalNo());
            orderStateMachineService.sendEvent(event, orderCtsPO.getOrderNo(), payNotifyMqDTO, orderCtsPO);

        } else if (AuditAgreeEnum.DISAGREE.getCode().equals(enumByCode.getCode())) {
            orderCtsApplyPayPO.setDisagreeProof(offlinePayAuditRequest.getDisagreeProof());
            orderCtsApplyPayPO.setDisagreeReason(offlinePayAuditRequest.getDisagreeReason());
            orderCtsApplyPayPO.setAuditTime(new Date());
            orderCtsApplyPayPO.setAuditStatus(OfflineAuditStatusEnum.REVIEW_REJECTED.getCode());
            orderCtsApplyPayPO.setAuditUserId(JwtUtil.getJwtTokenUserInfo().getUserId());
            orderCtsApplyPayPO.setAuditUserName(JwtUtil.getJwtTokenUserInfo().getUserName());
            orderCtsApplyPayPO.setUpdateBy(JwtUtil.getJwtTokenUserInfo().getUserId());
            orderCtsApplyPayPO.setTransactionalNo(offlinePayAuditRequest.getTransactionalNo());
            orderPayRecordPO.setAuditReason(offlinePayAuditRequest.getDisagreeReason());
            orderPayRecordPO.setPayStatus(PayStatusEnum.PAY_STATUS_FAIL.getCode());
            orderPayRecordPO.setAuditStatus(PayRecordAuditStatusEnum.AUDIT_FAIL.getCode());
            //更新订单状态 在不同意的情况下 订单金额的处理
        }
        orderPayRecordService.updateById(orderPayRecordPO);
        orderCtsApplyPayService.updateById(orderCtsApplyPayPO);
    }

    /**
     * 退款审核
     *
     * @param offlineRefundAuditRequest
     */
    @Override
    public void offlineRefundAudit(OfflineRefundAuditRequest offlineRefundAuditRequest) {

        //判断是否重新退款
        if (Objects.isNull(offlineRefundAuditRequest.getRandom())) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "随机校验参数不能为空");
        }
        UserTokenInfoDTO jwtTokenInfo = JwtUtil.getJwtTokenUserInfo();
        String random = redisService.getCacheObject(RedisConstant.CTS_RANDOM_CODE + jwtTokenInfo.getUserId());
        if (!(offlineRefundAuditRequest.getRandom().equals(random))) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "随机校验错误");
        }
        final String orderNo = offlineRefundAuditRequest.getOrderNo();
        final RLock lock = redisson.lock(RedisConstant.CTS_ORDER_LOCK + orderNo);

        try {
            OrderCtsApplyRefundPO applyCtsRefundPO = orderCtsApplyRefundService.getById(offlineRefundAuditRequest.getId());
            if (applyCtsRefundPO == null) {
                throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "申请退款记录不存在");
            }
            //线下支付类型
            if (!offlinePayTypes.contains(applyCtsRefundPO.getPayType())){
                throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, " 该笔退款不是线下申请退款!");
            }
            //判断钉钉审批是否通过
            if (!ObjectUtil.equals(DingApproveStatusEnum.DING_SUCCESS.getCode(),applyCtsRefundPO.getDingApproveStatus())){
                throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, " 该笔线下申请退款钉钉审批还未通过!");
            }

            OrderPayRecordPO orderPayRecordData = orderPayRecordService.getById(applyCtsRefundPO.getPayRecordId());
            if (orderPayRecordData == null) {
                throw new BusinessException(ResultEnum.NOT_EXIST, "支付流水不存在!");
            }

            //防止已经审核通过，正在执行退款中的操作再次发起退款
            if (OrderRefundStatusEnum.SUCCESS_AUDIT.getCode().equals(applyCtsRefundPO.getRefundStatus()) ||
                    OrderRefundStatusEnum.FAILED_AUDIT.getCode().equals(applyCtsRefundPO.getRefundStatus()) ||
                    OrderRefundStatusEnum.AUDITING_AUDIT.getCode().equals(applyCtsRefundPO.getRefundStatus())) {
                throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "该退款已审核,请刷新页面");
            }

            //查询订单
            LambdaQueryWrapper<OrderCtsPO> orderWrapper = new LambdaQueryWrapper<OrderCtsPO>()
                    .eq(OrderCtsPO::getOrderNo, offlineRefundAuditRequest.getOrderNo());
            OrderCtsPO orderCtsPO = orderCtsService.getOne(orderWrapper);
            if (orderCtsPO == null) {
                throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "申请退款订单不存在");
            }
            OrderCtsApplyRefundPO orderCtsApplyRefundPO = new OrderCtsApplyRefundPO();
            orderCtsApplyRefundPO.setId(applyCtsRefundPO.getId());
            //同意退款
            if(offlineRefundAuditRequest.getRefundAmount().compareTo(BigDecimal.ZERO) < 0){
                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "退款金额不可小于0!");
            }
            //线下金额判断
            if(offlineRefundAuditRequest.getRefundAmount().compareTo(orderCtsPO.getRealityAmount()) > 0){
                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "退款金额大于订单可退金额!");
            }
            //更新申请表
            orderCtsApplyRefundPO.setTransactionalNo(offlineRefundAuditRequest.getTransactionalNo());
            orderCtsApplyRefundPO.setTransferTime(offlineRefundAuditRequest.getTransferTime());
            orderCtsApplyRefundPO.setAuditRefundProof(offlineRefundAuditRequest.getAuditRefundProof());
            orderCtsApplyRefundPO.setActuallyAmount(offlineRefundAuditRequest.getRefundAmount());
            orderCtsApplyRefundPO.setAuditName(JwtUtil.getJwtTokenUserInfo().getUserName());
            orderCtsApplyRefundPO.setAuditTime(new Date());
            orderCtsApplyRefundPO.setRefundStatus(OrderRefundStatusEnum.AUDITING_AUDIT.getCode());
            orderCtsApplyRefundService.updateById(orderCtsApplyRefundPO);
            //改变订单状态
            final OrderStateEvent event = OrderStateEvent.getRefundEndEventByOrderType(orderCtsPO.getOrderType());
            RefundNotifyMqDTO refundNotifyMqDTO = new RefundNotifyMqDTO();
            refundNotifyMqDTO.setOutRefundNo(applyCtsRefundPO.getId().toString());
            refundNotifyMqDTO.setRefundStatus(PayStatusEnum.PAY_STATUS_SUCCESS.getCode());
            refundNotifyMqDTO.setRefundAmount(offlineRefundAuditRequest.getRefundAmount());
            refundNotifyMqDTO.setTransactionalNo(offlineRefundAuditRequest.getTransactionalNo());
            refundNotifyMqDTO.setSuccessTime(offlineRefundAuditRequest.getTransferTime());
            orderStateMachineService.sendEvent(event, applyCtsRefundPO.getOrderNo(), refundNotifyMqDTO, orderCtsPO);

        } finally {
            if (lock != null && lock.isLocked()) {
                lock.unlock();
            }
        }
    }


    @Override
    public OfflinePayAdminAuditDetailVO offlinePayAuditDetail(Long id) {
        OrderCtsApplyPayPO orderCtsApplyPayData = orderCtsApplyPayService.getById(id);
        if (orderCtsApplyPayData == null) {
            throw new BusinessException(ResultEnum.NOT_EXIST, "线下支付记录不存在");
        }
        OrderCtsPO orderCtsPO = orderCtsService.getByOrderNo(orderCtsApplyPayData.getOrderNo());
        if (orderCtsPO == null) {
            throw new BusinessException(ResultEnum.NOT_EXIST, "订单不存在!");
        }
        OfflinePayAdminAuditDetailVO offlinePayAdminAuditDetailVO = new OfflinePayAdminAuditDetailVO();
        offlinePayAdminAuditDetailVO.setOrderStatus(orderCtsPO.getStatus());
        Integer orderType = orderCtsPO.getOrderType();
        final List<OrderCtsSitterSnapshotPO> sitterPOList = new ArrayList<>();
        if (Objects.equals(orderType, OrderTypeEnum.MARGIN.getCode()) || Objects.equals(orderType, OrderTypeEnum.SITTER_GENERAL.getCode())) {
            sitterPOList.addAll(orderCtsSitterSnapshotService.listByOrderNos(Collections.singletonList(orderCtsPO.getOrderNo())));
        }
        if (Objects.equals(OrderTypeEnum.MARGIN.getCode(), orderType)) {
            final Optional<OrderCtsSitterSnapshotPO> first = sitterPOList.stream().filter(sitterPO -> Objects.equals(sitterPO.getSitterId(), orderCtsPO.getSitterId())).findFirst();
            first.ifPresent(sitter -> {
                offlinePayAdminAuditDetailVO.setCustomerName(sitter.getSitterName());
                offlinePayAdminAuditDetailVO.setMobile(sitter.getMobile());
            });
        } else if (Objects.equals(orderType, OrderTypeEnum.SITTER_GENERAL.getCode())) {
            final Optional<OrderCtsSitterSnapshotPO> first = sitterPOList.stream().filter(sitterPO -> Objects.equals(sitterPO.getSitterId(), orderCtsPO.getSitterId())).findFirst();
            first.ifPresent(sitter -> {
                offlinePayAdminAuditDetailVO.setCustomerName(sitter.getSitterName());
                offlinePayAdminAuditDetailVO.setMobile(sitter.getMobile());
            });
        }else if (Objects.equals(orderType, OrderTypeEnum.CUSTOMER_GENERAL.getCode())) {
            CustomerInfoCtsRequest customerInfoCtsRequest = new CustomerInfoCtsRequest();
            customerInfoCtsRequest.setIdList(Collections.singletonList(orderCtsPO.getCustomId()));
            List<CustomerInfoCtsPO> customerInfoCtsPOS = customerInfoCtsService.listCustomer(customerInfoCtsRequest);

            final Optional<CustomerInfoCtsPO> first = customerInfoCtsPOS.stream().filter(customerInfoCtsPO -> Objects.equals(customerInfoCtsPO.getId(), orderCtsPO.getCustomId())).findFirst();
            first.ifPresent(customerInfoCtsPO -> {
                offlinePayAdminAuditDetailVO.setCustomerName(customerInfoCtsPO.getCustomerName());
                offlinePayAdminAuditDetailVO.setMobile(customerInfoCtsPO.getPhoneNumber());
            });
        } else {
            UserESignPO sign = userESignService.getById(orderCtsPO.getOrderCustomCertificationId());
            if(sign!=null){
                offlinePayAdminAuditDetailVO.setCustomerName(sign.getName());
                offlinePayAdminAuditDetailVO.setMobile(sign.getPhone());
            };
        }
        offlinePayAdminAuditDetailVO.setPayableAmount(orderCtsPO.getPayableAmount());
        offlinePayAdminAuditDetailVO.setRealityAmount(orderCtsPO.getRealityAmount());

        CtsSitePO detail = ctsSiteService.detail(orderCtsPO.getCtsSiteId());
        if (detail != null) {
            offlinePayAdminAuditDetailVO.setCtsSiteName(detail.getSiteName());
        }
        offlinePayAdminAuditDetailVO.setOrderNo(orderCtsPO.getOrderNo());
        offlinePayAdminAuditDetailVO.setOrderTypeName(OrderTypeEnum.getValueByCode(orderCtsPO.getOrderType()));
        offlinePayAdminAuditDetailVO.setSellName(orderCtsApplyPayData.getCreateByName());
        offlinePayAdminAuditDetailVO.setSubmitTime(orderCtsApplyPayData.getGmtCreate());
        offlinePayAdminAuditDetailVO.setPayProof(orderCtsApplyPayData.getPayProof());
        offlinePayAdminAuditDetailVO.setPayAmount(orderCtsApplyPayData.getPayAmount());
        offlinePayAdminAuditDetailVO.setPayTypeName(CtsPayTypeEnum.getValueByCode(orderCtsApplyPayData.getPayType()));
        offlinePayAdminAuditDetailVO.setRemark(orderCtsApplyPayData.getSellRemark());
        offlinePayAdminAuditDetailVO.setAuditStatusName(OfflineAuditStatusEnum.getValueByCode(orderCtsApplyPayData.getAuditStatus()));
        offlinePayAdminAuditDetailVO.setAuditStatus(orderCtsApplyPayData.getAuditStatus());
        offlinePayAdminAuditDetailVO.setTransactionalNo(orderCtsApplyPayData.getTransactionalNo());
        offlinePayAdminAuditDetailVO.setActuallyPayAmount(orderCtsApplyPayData.getActuallyPayAmount());
        offlinePayAdminAuditDetailVO.setAccountTime(orderCtsApplyPayData.getAccountTime());
        if (OfflineAuditStatusEnum.REVIEW_REJECTED.getCode().equals(orderCtsApplyPayData.getAuditStatus())) {
            offlinePayAdminAuditDetailVO.setAuditProof(orderCtsApplyPayData.getDisagreeProof());
        } else {
            offlinePayAdminAuditDetailVO.setAuditProof(orderCtsApplyPayData.getAuditProof());
        }
        offlinePayAdminAuditDetailVO.setDisagreeReason(orderCtsApplyPayData.getDisagreeReason());
        offlinePayAdminAuditDetailVO.setPaySubject(orderCtsApplyPayData.getPaySubject());
        return offlinePayAdminAuditDetailVO;
    }

    /**
     * 支付宝付款
     *
     * @param request
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payment(PayRequest request) {

        log.info("予家支付宝请求{}", JSONUtil.toJsonStr(request));

        Integer accountType = request.getAccountType();
        if (ObjectUtil.isEmpty(accountType) || accountType.equals(AccountTypeEnum.BEI_KANG.getCode())) {

        } else if (accountType.equals(AccountTypeEnum.NUTRITION.getCode())) {
            LambdaQueryWrapper<OrderNutritionMonthFoodPO> lq = new LambdaQueryWrapper<>();
            lq.eq(OrderNutritionMonthFoodPO::getOrderNo, request.getOrderNo());
            OrderNutritionMonthFoodPO orderNutritionMonthFoodPO = orderNutritionMonthFoodMapper.selectOne(lq);
            if (ObjectUtil.isEmpty(orderNutritionMonthFoodPO)) {
                throw new BusinessException(ResultEnum.NOT_EXIST);
            }
            request.setOrderType(orderNutritionMonthFoodPO.getOrderType());
        } else if (Objects.equals(accountType, AccountTypeEnum.CTS.getCode())) {
            final OrderCtsPO orderCtsPO = orderCtsService.getByOrderNo(request.getOrderNo());
            if (Objects.isNull(orderCtsPO)) {
                throw new BusinessException(ResultEnum.NOT_EXIST);
            }
            request.setSellId(orderCtsPO.getSellId());
            request.setOrderType(orderCtsPO.getOrderType());
        }


        //生成本地交易流水号
        String localTransactionalNo = IdGenUtils.createLocalTransactionalNo(request.getAccountType(), new Date());
        Integer payType = request.getPayType();
        String result = "";
        //调用支付中心支付
        //如果是支付宝
        if (ObjectUtil.equals(CtsPayTypeEnum.ZFB.getCode(), payType)) {
            //转换支付宝需要的数据
            CtsSitePO ctsSitePO = getCtsSitePo(request.getSellId());
            AliPayRequest aliPayRequest = getAliPayRequest(request, localTransactionalNo, ctsSitePO);
            result = payManager.aliWapPayStr(aliPayRequest);

        } else {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "支付宝付款类型错误");
        }

        //生成支付记录并初始化
        OrderPayRecordPO orderPayRecordPO = getOrderPayRecordPO(request, localTransactionalNo);
        orderPayRecordService.save(orderPayRecordPO);

        if (Objects.equals(accountType, AccountTypeEnum.CTS.getCode()) && StringUtils.isNotBlank(request.getPaySign())) {
            ctsPayService.savePaySignRecord(request.getPaySign(), localTransactionalNo);
        }
        return result;
    }

    private CtsSitePO getCtsSitePo(Long customerId) {
        if (customerId == null) {
            return null;
        }
        return ctsSiteService.getSiteByUserId(customerId);
    }

    /**
     * 微信付款
     *
     * @param request
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONObject wxPayment(PayRequest request) {
        Integer accountType = request.getAccountType();
        if (ObjectUtil.isEmpty(accountType) || accountType.equals(AccountTypeEnum.BEI_KANG.getCode())) {

        } else if (accountType.equals(AccountTypeEnum.NUTRITION.getCode())) {
            LambdaQueryWrapper<OrderNutritionMonthFoodPO> lq = new LambdaQueryWrapper<>();
            lq.eq(OrderNutritionMonthFoodPO::getOrderNo, request.getOrderNo());
            OrderNutritionMonthFoodPO orderNutritionMonthFoodPO = orderNutritionMonthFoodMapper.selectOne(lq);
            if (ObjectUtil.isEmpty(orderNutritionMonthFoodPO)) {
                throw new BusinessException(ResultEnum.NOT_EXIST);
            }
            request.setOrderType(orderNutritionMonthFoodPO.getOrderType());
        } else if (Objects.equals(accountType, AccountTypeEnum.CTS.getCode())) {
            final OrderCtsPO orderCtsPO = orderCtsService.getByOrderNo(request.getOrderNo());
            if (Objects.isNull(orderCtsPO)) {
                throw new BusinessException(ResultEnum.NOT_EXIST);
            }
            request.setOrderType(orderCtsPO.getOrderType());
            request.setSellId(orderCtsPO.getSellId());
        }
        //生成本地交易流水号
        String localTransactionalNo = IdGenUtils.createLocalTransactionalNo(request.getAccountType(), new Date());
        Integer payType = request.getPayType();
        JSONObject result = new JSONObject();
        //调用支付中心支付
        //如果是微信
        if (ObjectUtil.equals(CtsPayTypeEnum.WX.getCode(), payType)) {
            //转换微信需要的数据
            WxPayRequest wxPayRequest = getWxPayRequest(request, localTransactionalNo);
            CtsSitePO ctsSitePo = getCtsSitePo(request.getSellId());
            if (ctsSitePo != null) {
                wxPayRequest.setBizId(ctsSitePo.getId());
            }
            wxPayRequest.setOrderType(request.getOrderType());
            result = payManager.wxJsPay(wxPayRequest);
        } else {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "微信付款类型错误");
        }


        //生成支付记录并初始化
        OrderPayRecordPO orderPayRecordPO = getOrderPayRecordPO(request, localTransactionalNo);
        orderPayRecordService.save(orderPayRecordPO);

        if (Objects.equals(accountType, AccountTypeEnum.CTS.getCode()) && StringUtils.isNotBlank(request.getPaySign())) {
            ctsPayService.savePaySignRecord(request.getPaySign(), localTransactionalNo);
        }
        return result;
    }


    private OrderPayRecordPO getOrderPayRecordPO(PayRequest request, String localTransactionalNo) {
        OrderPayRecordPO orderPayRecordPO = new OrderPayRecordPO();
        orderPayRecordPO.setOrderNo(request.getOrderNo());
        orderPayRecordPO.setLocalTransactionalNo(localTransactionalNo);
        //TODO 解析result 获取第三方交易流水号
        //orderPayRecordPO.setTransactionalNo();
        orderPayRecordPO.setRecordType(RecordTypeEnum.PAY.getCode());
        orderPayRecordPO.setAmountType(request.getAmountType());
        orderPayRecordPO.setPayStatus(PayStatusEnum.PAY_STATUS_PROCESS.getCode());
        orderPayRecordPO.setPayTime(new Date());
        return orderPayRecordPO;
    }

    private AliPayRequest getAliPayRequest(PayRequest request, String localTransactionalNo, CtsSitePO ctsSitePO) {
        AliPayRequest aliPayRequest = new AliPayRequest();
        aliPayRequest.setAccountType(request.getAccountType());
        aliPayRequest.setOrderNo(request.getOrderNo());
        aliPayRequest.setOutTradeNo(localTransactionalNo);
        aliPayRequest.setSubject(PayAmountEnum.getValueByCode(request.getAmountType()));
        aliPayRequest.setTotalAmount(String.valueOf(request.getPayAmount()));
        if (ctsSitePO != null) {
            aliPayRequest.setBizId(ctsSitePO.getId());
        }
        aliPayRequest.setOrderType(request.getOrderType());
        log.info("aliAppPay参数={}", aliPayRequest);
        return aliPayRequest;
    }

    private WxPayRequest getWxPayRequest(PayRequest request, String localTransactionalNo) {
        WxPayRequest wxPayRequest = new WxPayRequest();
        wxPayRequest.setAccountType(request.getAccountType());
        wxPayRequest.setOutTradeNo(localTransactionalNo);
        if (ObjectUtil.isNull(request.getOpenId())) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "微信支付openId不能为空");
        }
        wxPayRequest.setAccountType(request.getAccountType());
        wxPayRequest.setOpenId(request.getOpenId());
        wxPayRequest.setDescription(PayAmountEnum.getValueByCode(request.getAmountType()));
        //微信要将元转换为分
        wxPayRequest.setTotalFee(RMBUtils.changeY2F(String.valueOf(request.getPayAmount())));
        log.info("wxPay参数={}", wxPayRequest);
        return wxPayRequest;
    }

    private PosPayRequest getPosPayRequest(PayRequest request, String localTransactionalNo) {
        PosPayRequest posPayRequest = new PosPayRequest();
        posPayRequest.setOrderNo(request.getOrderNo());
        posPayRequest.setStoreId(request.getStoreId());
        posPayRequest.setOutTradeNo(localTransactionalNo);
        posPayRequest.setBody(PayAmountEnum.getValueByCode(request.getAmountType()));
        posPayRequest.setTotalAmount(String.valueOf(request.getPayAmount()));
        log.info("posPay参数={}", posPayRequest);
        return posPayRequest;
    }

    /**
     * 退款
     *
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PayRefundResult refund(Long id, boolean posScanTransCode, Integer accountType) {

        AccountTypeEnum enumByCode = AccountTypeEnum.getEnumByCode(accountType);

        //本地流水号
        String localTransactionalNo = null;

        //门店编号：POS机专用
        Long storeId = null;

        //退款金额
        String refundAmount = null;

        //退款原因
        String refundCause = null;

        //支付方式
        Integer payType = null;

        //退款记录
        OrderPayRecordPO record = null;
        //id
        String orderNo = null;

        switch (enumByCode) {
            case NUTRITION:

                //如果是营养的订单，id就是退款记录
                record = orderPayRecordService.getBaseMapper().selectById(id);
                //退款金额
                refundAmount = String.valueOf(record.getPayAmount());
                //退款原因
                refundCause = record.getAuditReason();
                //本地流水号
                localTransactionalNo = record.getLocalTransactionalNo();
                //支付方式
                payType = record.getPayType();

                //订单id
                orderNo = record.getOrderNo();

                break;
            case CTS:
                //到家订单，id是申请退款表的主键id，注意，一笔支付流水可以分多次申请退款
                OrderCtsApplyRefundPO orderCtsApplyRefundPO = orderCtsApplyRefundService.getById(id);
                if (ObjectUtil.isNull(orderCtsApplyRefundPO)){
                    throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "cts业务申请退款记录不存在");
                }
                LambdaQueryWrapper<OrderPayRecordPO> wrapper = new LambdaQueryWrapper<OrderPayRecordPO>()
                        .eq(OrderPayRecordPO::getId,orderCtsApplyRefundPO.getPayRecordId());
                record = orderPayRecordService.getOne(wrapper);
                if (record == null){
                    throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "cts业务退款记录不存在");
                }
                // 判断该笔支付流水的支付状态是否为已支付
                if ( CompareUtil.integerEqual(PayStatusEnum.PAY_STATUS_SUCCESS.getCode(), record.getPayStatus())){
                    log.info("cts业务退款记录支付状态已支付{}", record.getId());
                    throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "cts业务退款记录支付状态不是已支付");
                }


                //本地流水号
                localTransactionalNo = record.getLocalTransactionalNo();
                //支付方式
                payType = record.getPayType();
                // 判断本次申请退款金额是否大于该笔支付流水的支付金额
                if ((orderCtsApplyRefundPO.getRefundAmount()).compareTo((record.getPayAmount())) > 0 ){
                    throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "cts业务申请退款金额大于该笔支付金额");
                }
                //退款金额
                refundAmount = String.valueOf(orderCtsApplyRefundPO.getRefundAmount());
                //退款原因
                refundCause = orderCtsApplyRefundPO.getRefundRemark();

                refundCause = "退款";

                orderNo = record.getOrderNo();

                break;
            default:


        }

        LambdaQueryWrapper<OrderPayRecordPO> lq = new LambdaQueryWrapper<OrderPayRecordPO>()
                .eq(OrderPayRecordPO::getLocalTransactionalNo, localTransactionalNo)
                .eq(OrderPayRecordPO::getRecordType, RecordTypeEnum.PAY.getCode());
        OrderPayRecordPO payRecord = orderPayRecordService.getOne(lq);
        if (ObjectUtil.isNull(payRecord)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "资金记录不存在");
        }

        //支付记录的支付金额
        String payAmount = String.valueOf(payRecord.getPayAmount());

        PayRefundResult result = new PayRefundResult();


        if (ObjectUtil.equals(PayChannelTypeEnum.CMB.getCode(), payRecord.getPayChannel())){
            result = getCmbResult(id, record, payRecord.getLocalTransactionalNo(), payAmount, refundAmount, refundCause, accountType, orderNo);
        }else {
            //如果是支付宝
            if (ObjectUtil.equals(CtsPayTypeEnum.ZFB.getCode(), payType)) {
                result = getAliResult(id, record, payRecord.getLocalTransactionalNo(), refundAmount, refundCause, accountType, orderNo);
            } else if (ObjectUtil.equals(CtsPayTypeEnum.WX.getCode(), payType)) {
                result = getWxResult(id, record, payRecord.getLocalTransactionalNo(), payAmount, refundAmount, refundCause, accountType, orderNo);
            }
        }


        //修改支付记录表
        orderPayRecordService.updateById(record);

        log.info("发起退款：{}", JSONUtil.toJsonStr(record));
        return result;
    }


    /**
     * @param id           本地流水号
     * @param record
     * @param outTradeNo   订单NO
     * @param payAmount
     * @param refundAmount
     * @param refundCause
     * @param accountType
     * @return
     */
    @Override
    public PayRefundResult getWxResult(Long id, OrderPayRecordPO record, String outTradeNo, String payAmount, String refundAmount, String refundCause, Integer accountType, String orderNo) {

        String result;
        //转换微信需要的数据
        WxpayTradeRefundRequest request = new WxpayTradeRefundRequest();
        request.setOutRefundNo(String.valueOf(id));
        request.setOutTradeNo(outTradeNo);
        request.setRefundCause(refundCause);
        //元转分
        request.setTotalAmount(RMBUtils.changeY2F(payAmount));
        request.setRefundAmount(RMBUtils.changeY2F(refundAmount));
        request.setAccountType(accountType);
        if (AccountTypeEnum.CTS.getCode().equals(accountType)) {
            OrderCtsPO orderCtsPO = orderCtsService.getByOrderNo(orderNo);
            if (Objects.isNull(orderCtsPO)) {
                throw new BusinessException("订单退款异常, 订单不存在 orderNo:" + orderNo);
            }
            CtsSitePO ctsSitePo = getCtsSitePo(orderCtsPO.getSellId());
            if (ctsSitePo != null) {
                request.setBizId(ctsSitePo.getId());
            }
            request.setOrderType(orderCtsPO.getOrderType());
        }
        String xmlResult = payManager.wxTradeRefund(request);
        Map<String, String> map = WxPayKit.xmlToMap(xmlResult);
        log.info("微信退款结果,={}", map.toString());
        result = map.toString();
        String returnCode = map.get("return_code");
        String resultCode = map.get("result_code");
        String refundFee = map.get("refund_fee");
        String transactionalNo = map.get("transaction_id");
        //设置原报文信息
        record.setRequestBody(result);
        PayRefundResult payRefundResult = new PayRefundResult();
        payRefundResult.setResult(result);
        payRefundResult.setPayType(CtsPayTypeEnum.WX.getCode());
        payRefundResult.setPayStatus(PayStatusEnum.PAY_STATUS_SUCCESS.getCode());
        //只做失败的处理，成功的处理都在回调中处理
        if (!WxPayKit.codeIsOk(returnCode) || !WxPayKit.codeIsOk(resultCode)) {
            //设置result状态
            payRefundResult.setPayStatus(PayStatusEnum.PAY_STATUS_FAIL.getCode());
            //审核失败
            record.setAuditStatus(PayRecordAuditStatusEnum.AUDIT_FAIL.getCode());
            //退款失败
            record.setPayStatus(PayStatusEnum.PAY_STATUS_FAIL.getCode());
            //银行卡余额不足或网络问题
            String errCodeDes = "银行卡余额不足或网络问题";
            record.setRemark(errCodeDes);
            payRefundResult.setFailReason(errCodeDes);
        }
        return payRefundResult;
    }

    /**
     * @param id           本地流水号
     * @param record
     * @param outTradeNo   订单NO
     * @param payAmount
     * @param refundAmount
     * @param refundCause
     * @param accountType
     * @return
     */
    public PayRefundResult getCmbResult(Long id, OrderPayRecordPO record, String outTradeNo, String payAmount, String refundAmount, String refundCause, Integer accountType, String orderNo) {


        RefundRequest request = new RefundRequest();
        request.setPayAccountId(record.getPayAccountId());
        request.setRfdAmt(new BigDecimal(refundAmount));
        request.setOrderId(outTradeNo);
        request.setRfdSerial(record.getId()+"");


        if (AccountTypeEnum.CTS.getCode().equals(accountType)) {
            OrderCtsPO orderCtsPO = orderCtsService.getByOrderNo(orderNo);
            if (Objects.isNull(orderCtsPO)) {
                throw new BusinessException("订单退款异常, 订单不存在 orderNo:" + orderNo);
            }
            request.setOrderType(orderCtsPO.getOrderType());
        }

        RefundRespVO refundRespVO = payManager.cmbRefund(request);
        log.info("退款结果,={}", JSONUtil.toJsonStr(refundRespVO));
        PayRefundResult payRefundResult = new PayRefundResult();
        String result = JSONUtil.toJsonStr(refundRespVO);
        record.setRequestBody(result);
        payRefundResult.setResult(result);
        //只做失败的处理，成功的处理都在回调中处理

        //响应code码，LX11C000：成功，LX11CXXX：失败，LX11C610：退款处理中。订单带子单的退款，LX11C000仅表示操作成功，每笔子单的错误信息请查询detail rfdCode值
        if (CompareUtil.stringEqual(CmbRefundStatusEnum.SUCCESS.getCode(), refundRespVO.getRespCode())) {

            //设置原报文信息
            record.setTransactionalNo(refundRespVO.getRfdorderid());
            payRefundResult.setPayType(OmniPayTypeEnum.CMB.getCode());
            payRefundResult.setPayStatus(PayStatusEnum.PAY_STATUS_SUCCESS.getCode());
            record.setPayStatus(PayStatusEnum.PAY_STATUS_SUCCESS.getCode());

            // 退款成功，执行后续逻辑（支付宝和微信是发送的mq  refund_notify）
            RefundNotifyMqDTO refundNotifyMqDTO = new RefundNotifyMqDTO();
            refundNotifyMqDTO.setAccountType(2);
            refundNotifyMqDTO.setOutTradeNo(record.getLocalTransactionalNo());
            refundNotifyMqDTO.setTransactionalNo(refundRespVO.getRfdorderid());
            //申请表的id
            refundNotifyMqDTO.setOutRefundNo(id+"");
            refundNotifyMqDTO.setRefundStatus(1);
            refundNotifyMqDTO.setOrderNo(record.getOrderNo());
            refundNotifyMqDTO.setRefundAmount(record.getPayAmount());
            refundNotifyMqDTO.setSuccessTime(new Date());
            refundNotifyMqDTO.setRefundType(1);
            refundNotifyMqDTO.setRequestBody(JSONUtil.toJsonStr(refundRespVO));

            context.publishEvent(refundNotifyMqDTO);

        }

        if (CompareUtil.stringEqual(CmbRefundStatusEnum.FAIL.getCode(), refundRespVO.getRespCode())) {
            //设置result状态
            payRefundResult.setPayStatus(PayStatusEnum.PAY_STATUS_FAIL.getCode());
            //审核失败
            record.setAuditStatus(PayRecordAuditStatusEnum.AUDIT_FAIL.getCode());
            //退款失败
            record.setPayStatus(PayStatusEnum.PAY_STATUS_FAIL.getCode());
            //银行卡余额不足或网络问题
            record.setRemark(refundRespVO.getRespDesc());
            payRefundResult.setFailReason(refundRespVO.getRespDesc());
        }
        if (CompareUtil.stringEqual(CmbRefundStatusEnum.IN_PROCESS.getCode(), refundRespVO.getRespCode())) {
            //设置result状态
            payRefundResult.setPayStatus(PayStatusEnum.PAY_STATUS_PROCESS.getCode());
            //退款失败
            record.setPayStatus(PayStatusEnum.PAY_STATUS_PROCESS.getCode());
            record.setAuditStatus(PayRecordAuditStatusEnum.AUDIT_SUCCESS.getCode());
        }

        return payRefundResult;
    }



    /**
     * 获取支付宝退款返回信息
     */
    @Override
    public PayRefundResult getAliResult(Long id, OrderPayRecordPO record, String outTradeNo, String refundAmount, String refundCause, Integer accountType, String orderNo) {
        String result;
        //转换支付宝需要的数据
        AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
        request.setOutTradeNo(outTradeNo);
        request.setRefundAmount(refundAmount);
        request.setRefundCause(refundCause);
        request.setOutRequestNo(String.valueOf(id));
        request.setAccountType(accountType);
        if (AccountTypeEnum.CTS.getCode().equals(accountType)) {
            OrderCtsPO orderCtsPO = orderCtsService.getByOrderNo(orderNo);
            if (Objects.isNull(orderCtsPO)) {
                throw new BusinessException("订单退款异常, 订单不存在 orderNo:" + orderNo);
            }
            CtsSitePO ctsSitePo = getCtsSitePo(orderCtsPO.getSellId());
            if (ctsSitePo != null) {
                request.setBizId(ctsSitePo.getId());
            }
            request.setOrderType(orderCtsPO.getOrderType());
        }
        AlipayTradeRefundResponse alipayTradeRefundResponse = payManager.aliTradeRefund(request);
        result = alipayTradeRefundResponse.getBody();
        log.info("支付宝退款结果,={}", result);
        //设置原报文信息
        record.setRequestBody(result);
        PayRefundResult payRefundResult = new PayRefundResult();
        payRefundResult.setResult(result);
        payRefundResult.setPayType(CtsPayTypeEnum.ZFB.getCode());
        payRefundResult.setPayStatus(PayStatusEnum.PAY_STATUS_SUCCESS.getCode());
        //只做失败的处理，成功的处理都在回调中处理
        if (!(alipayTradeRefundResponse.isSuccess() && StringUtils.isNotEmpty(alipayTradeRefundResponse.getCode()) && "10000".equals(alipayTradeRefundResponse.getCode()))) {
            //退款失败
            record.setPayStatus(PayStatusEnum.PAY_STATUS_FAIL.getCode());
            //审核失败
            record.setAuditStatus(PayRecordAuditStatusEnum.AUDIT_FAIL.getCode());
            String failReason = alipayTradeRefundResponse.getMsg();
            //其他
            record.setRemark("银行卡余额不足或网络问题");
            payRefundResult.setPayStatus(PayStatusEnum.PAY_STATUS_FAIL.getCode());
            payRefundResult.setFailReason(failReason);
        }
        return payRefundResult;
    }


}

package com.stbella.order.server.strategy.approval;

import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.notice.entity.OaProcessIdRelationPO;
import com.stbella.notice.server.OaProcessIdRelationService;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.common.enums.order.OrderGiftStatusEnum;
import com.stbella.order.domain.order.month.entity.HeOrderGiftExtendEntity;
import com.stbella.order.domain.repository.HeOrderGiftInfoRepository;
import com.stbella.order.server.order.month.request.approval.ApprovalStatusInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.FlowIdentity;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

@Slf4j
@Component
public class GiftOrderApprovalStrateg implements ApprovalStrategy {

    @DubboReference(timeout = 5000)
    private OaProcessIdRelationService oaProcessIdRelationService;

    @Resource
    private HeOrderGiftInfoRepository heOrderGiftInfoRepository;


    @Override
    public void handleApproval(OrderApproveRecordTypeEnum type, String params, String messageId, ApprovalStatusInfo approvalStatusInfo, String processId) {

        log.info("GiftOrderApprovalStrateg handleApproval type:{}, params:{}, messageId:{}, approvalStatusInfo:{}, processId:{}", type, params, messageId, approvalStatusInfo, processId);
        OaProcessIdRelationPO oneByLocalProcessId = oaProcessIdRelationService.getOneByLocalProcessId(processId);
        HeOrderGiftExtendEntity heOrderGiftExtendEntity = heOrderGiftInfoRepository.selectByApproveId(processId);
        if (Objects.isNull(heOrderGiftExtendEntity)) {
            log.warn("礼赠订单不存在 processId: {}", oneByLocalProcessId.getQwProcessId());
            return;
        }

        if (!OrderGiftStatusEnum.APPROVAL.getCode().equals(heOrderGiftExtendEntity.getStatus())){
            log.warn("礼赠订单状态不正确 processId: {}, status: {}, giftSn: {}", oneByLocalProcessId.getQwProcessId(), heOrderGiftExtendEntity.getStatus(), heOrderGiftExtendEntity.getGiftSn());
            return;
        }

        if (approvalStatusInfo.isFinish()) {

            log.info("礼赠订单审批完成,客户已收到礼品 processId: {}", oneByLocalProcessId.getQwProcessId());
            if (approvalStatusInfo.isAgree()) {
                heOrderGiftExtendEntity.setApproveFinish(new Date());
                FlowContext context = new FlowContext();
                context.setAttribute(HeOrderGiftExtendEntity.class, heOrderGiftExtendEntity);
                FlowIdentity identity = FlowIdentity.builder()
                        .bizActivity(BizActivityEnum.GIFT_ORDER_APPROVAL_AGREE.code())
                        .idSlice("GiftOrder")
                        .idSlice("ApprovalAgree")
                        .build();
                SnowballFlowLauncher.fire(identity, context);
                Integer giftOrderStatus = (Integer) context.getAttribute(BizConstant.ExtraKey.giftOrderStatus);
                String giftOrderSn = (String) context.getAttribute(BizConstant.ExtraKey.giftOrderSn);
                heOrderGiftExtendEntity.setStatus(giftOrderStatus);
                heOrderGiftExtendEntity.setGiftOrderSn(giftOrderSn);
                if (OrderGiftStatusEnum.APPROVAL_SEND.getCode().equals(giftOrderStatus)){
                    heOrderGiftExtendEntity.setReleaseTime(heOrderGiftExtendEntity.getApproveFinish());
                }
            }
            if (approvalStatusInfo.isRefuse()) {
                log.info("礼赠订单审批拒绝 processId: {}", oneByLocalProcessId.getQwProcessId());
                heOrderGiftExtendEntity.setStatus(OrderGiftStatusEnum.APPROVAL_REJECTION.getCode());
            }
        }

        if (approvalStatusInfo.isTerminate()) {
            log.info("礼赠订单终止,客户手动取消赠送订单 processId: {}", oneByLocalProcessId.getQwProcessId());
            heOrderGiftExtendEntity.setStatus(OrderGiftStatusEnum.CANCELED.getCode());
        }
        heOrderGiftInfoRepository.update(heOrderGiftExtendEntity);
    }

    @Override
    public OrderApproveRecordTypeEnum getOrderApproveRecordTypeEnum() {
        return OrderApproveRecordTypeEnum.GIFT_ORDER_APPROVE;
    }
}

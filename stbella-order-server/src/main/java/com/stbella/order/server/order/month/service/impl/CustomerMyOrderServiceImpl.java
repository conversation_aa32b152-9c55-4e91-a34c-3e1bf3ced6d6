package com.stbella.order.server.order.month.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.stbella.core.result.Result;
import com.stbella.customer.server.customer.enums.goodsAppointment.GoodsAppointStatusEnum;
import com.stbella.customer.server.customer.request.goodsappointment.GoodsAppointmentQueryRequest;
import com.stbella.customer.server.customer.vo.goodsappointment.GoodsAppointmentListVO;
import com.stbella.marketing.api.res.OrderBenefitVO;
import com.stbella.marketing.api.res.OrderProductBenefitVO;
import com.stbella.order.common.enums.core.OmniOrderStatusEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OmniPayStatusEnum;
import com.stbella.order.common.enums.core.OrderRefundStatusEnum;
import com.stbella.order.common.enums.month.*;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.domain.client.CustomerGrowthClient;
import com.stbella.order.domain.order.factory.OrderFactory;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.context.component.StoreCurrencyContainer;
import com.stbella.order.server.convert.OrderBenefitsConverter;
import com.stbella.order.server.manager.MarketingManager;
import com.stbella.order.server.order.month.dto.PageDTO;
import com.stbella.order.server.order.month.req.MyBenefitsQuery;
import com.stbella.order.server.order.month.req.MyOrderNewQuery;
import com.stbella.order.server.order.month.req.OrderGoodsInfoReq;
import com.stbella.order.server.order.month.req.WechatMyOrderInfoNewQuery;
import com.stbella.order.server.order.month.res.*;
import com.stbella.order.server.order.month.service.AddressQueryService;
import com.stbella.order.server.order.month.service.CustomerMyOrderService;
import com.stbella.order.server.order.month.service.OrderV3Service;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.order.server.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CustomerMyOrderServiceImpl implements CustomerMyOrderService {

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private OrderGoodsRepository orderGoodsRepository;

    @Resource
    private HeTaskRepository heTaskRepository;

    @Resource
    private OrderProductionExtendRepository orderProductionExtendRepository;

    @Resource
    private OrderOtherRepository orderOtherRepository;

    @Resource
    private MonthContractSignRecordRepository monthContractSignRecordRepository;

    @Resource
    private AheadOutRoomRepository aheadOutRoomRepository;

    @Resource
    private ContractSignRecordPaperRepository contractSignRecordPaperRepository;

    @Resource
    private OrderV3Service orderV3Service;

    @Resource
    private StoreRepository storeRepository;

    @Resource
    private CustomerGrowthClient customerGrowthClient;

    @Resource
    private OrderAdditionalRevenueRepository orderAdditionalRevenueRepository;

    @Resource
    private OrderFactory orderFactory;

    @Resource
    private AddressQueryService addressQueryService;

    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;

    @Resource
    private AddressRepository addressRepository;

    @Resource
    private DistrictRepository districtRepository;

    @Resource
    private OrderRefundRepository orderRefundRepository;

    @Resource
    private MarketingManager marketingManager;

    @Resource
    private OrderBenefitsConverter orderBenefitsConverter;

    @Resource
    private OrderReductionRepository orderReductionRepository;

    @Override
    public PageDTO<MyOrderNewVO> myOrderList(MyOrderNewQuery req) {

        Page<HeOrderEntity> pageList = orderRepository.queryList(req);
        if (CollectionUtils.isEmpty(pageList.getRecords())) {
            return PageUtils.convert2PageDTO(pageList, null);
        }
        Map<Integer, String> storeNameMap = getStoreName(pageList.getRecords());
        return PageUtils.convert2PageDTO(pageList, temp -> heOrderModel2Req(temp, storeNameMap));
    }

    @Override
    public PageDTO<MyTerminalOrderNewVO> terminalOrderList(MyOrderNewQuery req) {

        if (CollectionUtils.isNotEmpty(req.getOrderStatusList())) {

            if (req.getOrderStatusList().contains(OmniOrderStatusEnum.WAIT_PAY.getCode())) {
                req.setOrderTypeList(Arrays.asList(OmniOrderTypeEnum.MONTH_ORDER.getCode(), OmniOrderTypeEnum.SMALL_MONTH_ORDER.getCode(), OmniOrderTypeEnum.PRODUCTION_ORDER.getCode()));
                req.setPayStatusList(Arrays.asList(OmniPayStatusEnum.WAIT_PAY.getCode(), OmniPayStatusEnum.NO_PAY_OFF.getCode()));
                req.setWaitPay(true);
            }
            if (req.getOrderStatusList().contains(OmniOrderStatusEnum.WAIT_DELIVERY.getCode())) {
                req.setOrderTypeList(Collections.singletonList(OmniOrderTypeEnum.MONTH_ORDER.getCode()));
                req.setPayStatusList(Arrays.asList(OmniPayStatusEnum.PAY_OFF.getCode(), OmniPayStatusEnum.EXCESS_PAY.getCode()));
                req.setWaitDelivery(true);
            }
        }

        List<Integer> refundOrderIdList = new ArrayList<>();
        List<Integer> refundStatusList = req.getRefundStatusList();
        if (
                CollectionUtil.isNotEmpty(refundStatusList) &&
                        refundStatusList.contains(OrderRefundStatusEnum.PART_OF_THE_REFUND.getCode()) &&
                        refundStatusList.contains(OrderRefundStatusEnum.PARTIAL_REFUND.getCode()) &&
                        refundStatusList.contains(OrderRefundStatusEnum.FULL_REFUND_IN_PROGRESS.getCode()) &&
                        refundStatusList.contains(OrderRefundStatusEnum.FULL_REFUND.getCode())
        ) {
            //如果有退款
            refundOrderIdList.addAll(orderRefundRepository.queryAllOrderId());
            req.setOrderIdList(refundOrderIdList);
            req.setRefundStatusList(new ArrayList<>());
        }

        Page<HeOrderEntity> pageList = orderRepository.queryList(req);
        if (CollectionUtils.isEmpty(pageList.getRecords())) {
            return PageUtils.convert2PageDTO(pageList, null);
        }
        Map<Integer, String> storeNameMap = getStoreName(pageList.getRecords());

        List<Integer> orderIdList = pageList.getRecords().stream().map(HeOrderEntity::getOrderId).collect(Collectors.toList());
        //根据orderId查询订单是否有其他权益
        List<OrderBenefitVO> orderBenefitVOS = marketingManager.queryOrderBenefitList(orderIdList);
        Map<Integer, OrderBenefitVO> orderBenefitVOMap = orderBenefitVOS.stream().collect(Collectors.toMap(OrderBenefitVO::getOrderId, orderBenefitVO -> orderBenefitVO));

        List<MyTerminalOrderNewVO> myTerminalOrderNewVOList = Lists.newArrayList();
        pageList.getRecords().forEach(orderEntity -> {
            MyTerminalOrderNewVO myTerminalOrderNewVO = terminalOrderProsess(orderEntity, storeNameMap);
            OrderBenefitVO orderBenefitVO = orderBenefitVOMap.get(orderEntity.getOrderId());
            myTerminalOrderNewVO.getOrderInfo().setHasOtherBenefits(orderBenefitVO.getIsBenefit());
            myTerminalOrderNewVOList.add(myTerminalOrderNewVO);
        });
        this.fillBookedStatus(myTerminalOrderNewVOList);
        PageDTO<MyTerminalOrderNewVO> pageDTO = new PageDTO<>();
        pageDTO.setPageNo(req.getPageNum());
        pageDTO.setPageSize(req.getPageSize());
        pageDTO.setPages((int) pageList.getPages());
        pageDTO.setTotal((int) pageList.getTotal());
        pageDTO.setList(myTerminalOrderNewVOList);
        return pageDTO;
    }

    @Override
    public HeOrderGoodsInfoVO orderGoodsInfo(OrderGoodsInfoReq req) {

        HeOrderEntity orderEntity = orderFactory.restoreWithStore(req.getOrderId());
        CfgStoreEntity store = orderEntity.getStore();
        HeOrderGoodsInfoVO result = BeanMapper.map(store, HeOrderGoodsInfoVO.class);
        result.setOrderSn(orderEntity.getOrderSn());
        List<Integer> ids = Lists.newArrayList();
        CfgDistrictEntity oneById = districtRepository.getOneById(store.getProvince());
        if (Objects.nonNull(oneById)) {
            ids.add(Integer.valueOf(oneById.getCode()));
        }
        CfgDistrictEntity city = districtRepository.getOneById(store.getCity());
        if (Objects.nonNull(city)) {
            ids.add(Integer.valueOf(city.getCode()));
        }
        CfgDistrictEntity region = districtRepository.getOneById(store.getRegion());
        if (Objects.nonNull(region)) {
            ids.add(Integer.valueOf(region.getCode()));
        }
        if (CollectionUtils.isNotEmpty(ids)) {
            List<String> nameList = addressRepository.getNameList(ids);
            if (CollectionUtils.isNotEmpty(nameList)) {
                if (nameList.size() > 0) {
                    result.setProvinceStr(nameList.get(0));
                }
                if (nameList.size() > 1) {
                    result.setCityStr(nameList.get(1));
                }
                if (nameList.size() > 2) {
                    result.setRegionStr(nameList.get(2));
                }
            }
        }
        result.setStandardOrderV1(Boolean.FALSE);
        if (Objects.nonNull(orderEntity.getVersion()) && orderEntity.getVersion().compareTo(new BigDecimal("3")) >= 0) {
            WechatMyOrderInfoNewVO myOrderInfoNewVO = orderV3Service.myOrderInfo(WechatMyOrderInfoNewQuery.builder().orderId(req.getOrderId()).build());
            if (Objects.nonNull(myOrderInfoNewVO)) {
                OrderGoodsInfoVO orderGoodsInfoVO = myOrderInfoNewVO.getOrderInfo().getOrderGoodsList().stream().filter(item -> req.getOrderGoodsId().equals(item.getId())).findFirst().orElse(null);
                result.setOrderGoodsInfo(orderGoodsInfoVO);
                result.setServiceDays(Objects.isNull(orderGoodsInfoVO) ? "0" : orderGoodsInfoVO.getGoodsNum().toString());
            }
            result.setStandardOrderV1(Boolean.TRUE);
        } else {
            Map<Integer, String> storeNameMap = new HashMap<>();
            MyOrderNewVO myOrderNewVO = heOrderModel2Req(orderEntity, storeNameMap);
            result.setMyOrderNewVO(myOrderNewVO);
            List<HeOrderAdditionalRevenueEntity> orderAdditionalRevenueInfoList = orderAdditionalRevenueRepository.getByOrderId(orderEntity.getOrderId());
            HeOrderAdditionalRevenueEntity additionalRevenueEntity = orderAdditionalRevenueInfoList.stream().filter(item -> MonthAdditionalRevenueEnum.STAY_COST.getCode().equals(item.getType())).findFirst().orElse(null);
            result.setServiceDays(Objects.isNull(additionalRevenueEntity) ? myOrderNewVO.getServiceDays().toString() : String.valueOf(myOrderNewVO.getServiceDays() + additionalRevenueEntity.getDays()));
        }
        return result;
    }

    @Override
    public PageDTO<OtherBenefitsVO> otherBenefit(MyBenefitsQuery req) {

        PageDTO<OtherBenefitsVO> objectPageDTO = new PageDTO<>();

        List<OrderProductBenefitVO> orderProductBenefitVOS = marketingManager.queryProductGiftByOrderId(req.getOrderId());
        //调用营销服务，查询所有的其它权益的产康任务
        if (Objects.nonNull(req.getType()) && !req.getType().equals(-1)) {
            orderProductBenefitVOS = orderProductBenefitVOS.stream().filter(o -> o.getType().equals(req.getType())).collect(Collectors.toList());
        }
        if (Objects.nonNull(req.getStatus())) {
            orderProductBenefitVOS = orderProductBenefitVOS.stream().filter(o -> o.getStatus().equals(req.getStatus())).collect(Collectors.toList());
        }

        //orderProductBenefitVOS 转成 otherBenefitsVOS
        List<OtherBenefitsVO> otherBenefitsVOS =  orderBenefitsConverter.convertOrderProductBenefitVO2OtherBenefitsVO(orderProductBenefitVOS);

        objectPageDTO.setList(otherBenefitsVOS);
        objectPageDTO.setPageNo(req.getPageNum());
        objectPageDTO.setTotal(otherBenefitsVOS.size());
        return objectPageDTO;
    }

    private void fillBookedStatus(List<MyTerminalOrderNewVO> myTerminalOrderNewVOList) {

        List<GoodsAppointmentQueryRequest> reqList = Lists.newArrayList();
        myTerminalOrderNewVOList.forEach(order -> {
            GoodsAppointmentQueryRequest req = new GoodsAppointmentQueryRequest();
            req.setStoreId(order.getOrderInfo().getStoreId());
            req.setOrderId(order.getOrderInfo().getOrderId().longValue());
            req.setOrderSn(order.getOrderInfo().getOrderSn());
            req.setBasicUid(order.getOrderInfo().getBasicUid().longValue());
            if (order.getStandardOrderV1()) {
                WechatMyOrderNewVO orderGoodsInfo = order.getOrderGoodsInfo();
                List<OrderGoodsInfoVO> orderGoodsList = orderGoodsInfo.getOrderGoodsList();
                orderGoodsList.forEach(orderGoods -> {
                    if (orderGoods.getMonthAgeGoods()) {
                        GoodsAppointmentQueryRequest queryReq = BeanMapper.map(req, GoodsAppointmentQueryRequest.class);
                        queryReq.setOrderGoodsId(orderGoods.getId().longValue());
                        reqList.add(queryReq);
                    }
                });
            } else {
                if (order.getOrderInfo().getMonthAgeGoods()) {
                    req.setOrderGoodsId(order.getOrderInfo().getOrderId().longValue());
                    reqList.add(req);
                }
            }
        });
        if (CollectionUtils.isEmpty(reqList)) {
            return;
        }
        List<GoodsAppointmentListVO> appointmentListVOList = customerGrowthClient.queryUserAppointment(reqList);
        Map<String, GoodsAppointmentListVO> goodsAppointmentListVOMap = new HashMap<>();
        appointmentListVOList.forEach(item -> {
            goodsAppointmentListVOMap.put(item.getOrderId().toString().concat(item.getOrderGoodsId().toString()), item);
        });
        myTerminalOrderNewVOList.forEach(order -> {
            if (order.getStandardOrderV1()) {
                WechatMyOrderNewVO orderGoodsInfo = order.getOrderGoodsInfo();
                orderGoodsInfo.getOrderGoodsList().forEach(orderGoods -> {
                    if (orderGoods.getMonthAgeGoods()) {
                        GoodsAppointmentListVO goodsAppointmentListVO = goodsAppointmentListVOMap.get(orderGoodsInfo.getOrderId().toString().concat(orderGoods.getId().toString()));
                        int status = Objects.isNull(goodsAppointmentListVO) ? 0 : goodsAppointmentListVO.getStatus();
                        orderGoods.setMonthAgeOrderStatus(status);
                        orderGoods.setMonthAgeOrderStatusStr(GoodsAppointStatusEnum.getDescription(status));
                        orderGoods.setMonthAgeOrderDesc(setMonthAgeOrderDesc(status, goodsAppointmentListVO));
                        if (Objects.nonNull(goodsAppointmentListVO)) {
                            orderGoods.setAppointmentId(goodsAppointmentListVO.getId());
                        }
                        Calendar calendar = Calendar.getInstance();
                        calendar.add(Calendar.DATE, 1);
                        orderGoods.setCancelMonthAgeOrder(Objects.isNull(goodsAppointmentListVO) || Objects.isNull(goodsAppointmentListVO.getStartTime()) ? Boolean.FALSE : calendar.getTime().before(goodsAppointmentListVO.getStartTime()));
                        if (GoodsAppointStatusEnum.WAITING_CONFIRMATION.getCode().equals(orderGoods.getMonthAgeOrderStatus())) {
                            orderGoods.setCancelMonthAgeOrder(Boolean.TRUE);
                        }
                    }
                });
            } else {
                if (order.getOrderInfo().getMonthAgeGoods()) {
                    GoodsAppointmentListVO goodsAppointmentListVO = goodsAppointmentListVOMap.get(order.getOrderInfo().getOrderId().toString().concat(order.getOrderInfo().getOrderId().toString()));
                    int status = Objects.isNull(goodsAppointmentListVO) ? 0 : goodsAppointmentListVO.getStatus();
                    order.getOrderInfo().setMonthAgeOrderStatus(status);
                    order.getOrderInfo().setMonthAgeOrderStatusStr(GoodsAppointStatusEnum.getDescription(status));
                    order.getOrderInfo().setMonthAgeOrderDesc(setMonthAgeOrderDesc(status, goodsAppointmentListVO));
                    if (Objects.nonNull(goodsAppointmentListVO)) {
                        order.getOrderInfo().setAppointmentId(goodsAppointmentListVO.getId());
                    }
                    Calendar calendar = Calendar.getInstance();
                    calendar.add(Calendar.DATE, 1);
                    order.getOrderInfo().setCancelMonthAgeOrder(Objects.isNull(goodsAppointmentListVO) || Objects.isNull(goodsAppointmentListVO.getStartTime()) ? Boolean.FALSE : calendar.getTime().before(goodsAppointmentListVO.getStartTime()));
                    if (GoodsAppointStatusEnum.WAITING_CONFIRMATION.getCode().equals(order.getOrderInfo().getMonthAgeOrderStatus())) {
                        order.getOrderInfo().setCancelMonthAgeOrder(Boolean.TRUE);
                    }
                }
            }
        });
    }

    private String setMonthAgeOrderDesc(Integer status, GoodsAppointmentListVO goodsAppointmentListVO) {

        log.info("goodsAppointmentListVO:{}", JSONUtil.toJsonStr(goodsAppointmentListVO));
        if (GoodsAppointStatusEnum.WAITING_APPOINTMENT.getCode() == status) {
            return "当前商品待预约服务";
        }
        if (Objects.isNull(goodsAppointmentListVO)) {
            return StringUtils.EMPTY;
        }
        if (GoodsAppointStatusEnum.WAITING_CONFIRMATION.getCode() == status || GoodsAppointStatusEnum.CONFIRMED.getCode() == status) {
            if (Objects.isNull(goodsAppointmentListVO.getStartTime()) || Objects.isNull(goodsAppointmentListVO.getEndTime())) {
                return StringUtils.EMPTY;
            }
            return String.format("时间：%s 至 %s", DateUtils.format(goodsAppointmentListVO.getStartTime(), DateUtils.YYYY_MM_DD_1), DateUtils.format(goodsAppointmentListVO.getEndTime(), DateUtils.YYYY_MM_DD_1));
        }
        if (GoodsAppointStatusEnum.CHECKED_IN.getCode() == status) {
            return "本次预约服务已完成";
        }
        return StringUtils.EMPTY;
    }

    private MyTerminalOrderNewVO terminalOrderProsess(HeOrderEntity heOrderEntity, Map<Integer, String> storeNameMap) {

        MyOrderNewVO myOrderNewVO = heOrderModel2Req(heOrderEntity, storeNameMap);
        MyTerminalOrderNewVO myTerminalOrderNewVO = new MyTerminalOrderNewVO();
        myTerminalOrderNewVO.setOrderInfo(myOrderNewVO);
        myTerminalOrderNewVO.setStandardOrderV1(Objects.nonNull(heOrderEntity.getVersion()) && heOrderEntity.getVersion().compareTo(new BigDecimal(3)) >= 0 ? Boolean.TRUE : Boolean.FALSE);
        if (myTerminalOrderNewVO.getStandardOrderV1()) {
            WechatMyOrderInfoNewVO myOrderInfoNewVO = orderV3Service.myOrderInfo(WechatMyOrderInfoNewQuery.builder().orderId(heOrderEntity.getOrderId()).build());
            myTerminalOrderNewVO.setOrderGoodsInfo(myOrderInfoNewVO.getOrderInfo());
        }
        return myTerminalOrderNewVO;
    }

    private MyOrderNewVO heOrderModel2Req(HeOrderEntity orderEntity, Map<Integer, String> storeNameMap) {

        MyOrderNewVO myOrderNewVO = new MyOrderNewVO();
        myOrderNewVO.setOrderId(orderEntity.getOrderId());
        myOrderNewVO.setOrderSn(orderEntity.getOrderSn());
        myOrderNewVO.setOrderType(orderEntity.getOrderType());
        myOrderNewVO.setClientUid(orderEntity.getClientUid());
        myOrderNewVO.setBasicUid(orderEntity.getBasicUid());
        myOrderNewVO.setStoreId(orderEntity.getStoreId());
        myOrderNewVO.setStoreName(storeNameMap.get(orderEntity.getStoreId()));
        myOrderNewVO.setOrderStatus(orderEntity.getOrderStatus());
        myOrderNewVO.setPayStatus(orderEntity.getPayStatus());
        myOrderNewVO.setPayStatusName(OmniPayStatusEnum.getName(orderEntity.getPayStatus()));
        myOrderNewVO.setOrderAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(orderEntity.getOrderAmount())));
        myOrderNewVO.setPaidAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(orderEntity.getPaidAmount())));
        myOrderNewVO.setRefundStatus(orderEntity.getRefundStatus());
        myOrderNewVO.setScene(orderEntity.getScene());
        if (orderEntity.isNewOrder()) {
            //订单1.0版本数据集
            myOrderNewVO.setOldOrNew(2);
            myOrderNewVO.setPayAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(orderEntity.getPayAmount())));
            myOrderNewVO.setRealAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(orderEntity.getRealAmount() + orderEntity.getProductionAmountPay())));
            Integer calPayable = orderEntity.calPayable();
            myOrderNewVO.setRemainAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(orderEntity.leftPayAmount())));
            myOrderNewVO.setDiscountAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(orderEntity.getOrderAmount() - calPayable)));
            myOrderNewVO.setCurrency(orderEntity.getCurrency());
            Result<List<OrderOperateButton>> listResult = orderV3Service.queryButtonByOrderId(orderEntity.getOrderId());
            if (listResult.getSuccess()) {
                for (OrderOperateButton item : listResult.getData()) {
                    if (OrderButtonEnum.COLLECTION.getCode().equals(item.getCode())) {
                        myOrderNewVO.setButton(item);
                    }
                }
            }
        } else {
            Integer payAmount = Objects.isNull(orderEntity.getPayAmount()) ? 0 : orderEntity.getPayAmount();
            myOrderNewVO.setPayAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(payAmount)));
            Integer realAmount = Objects.isNull(orderEntity.getRealAmount()) ? 0 : orderEntity.getRealAmount();
            myOrderNewVO.setRealAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(realAmount)));
            Integer remainAmount = payAmount - realAmount;
            myOrderNewVO.setRemainAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(remainAmount < 0 ? 0 : remainAmount)));
            myOrderNewVO.setDiscountAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(orderEntity.getOrderAmount() - orderEntity.getPayAmount())));
            myOrderNewVO.setButton(oldButtonList(orderEntity));
            String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(orderEntity.getStoreId());
            myOrderNewVO.setCurrency(storeCurrencyCode);
            if (Objects.isNull(orderEntity.getOldOrNew()) || orderEntity.getOldOrNew() == 0) {
                //老订单处理过程
                fillNowpaytask(orderEntity, myOrderNewVO);
                myOrderNewVO.setOldOrNew(0);
            }
            if (orderEntity.getOldOrNew() == 1) {
                //订单1.0版本之前的新订单数据
                myOrderNewVO.setOldOrNew(1);
            }
        }
        fillMyOrderNewVO(orderEntity, myOrderNewVO);
        if (orderEntity.getPayStatus().equals(PayStatusV2Enum.EXCESS_PAY.getCode())) {
            myOrderNewVO.setPayStatusName(OmniPayStatusEnum.PAY_OFF.getValue());
        }
        return myOrderNewVO;
    }

    private void fillMyOrderNewVO(HeOrderEntity orderEntity, MyOrderNewVO myOrderNewVO) {

        if (myOrderNewVO.getOldOrNew() == 2) {
            List<HeOrderGoodsEntity> goodsEntityList = orderGoodsRepository.getByOrderIdList(Collections.singletonList(orderEntity.getOrderId()));
            int sum = goodsEntityList.stream().filter(item -> Objects.nonNull(item.getGoodsNum())).mapToInt(HeOrderGoodsEntity::getGoodsNum).sum();
            myOrderNewVO.setGoodsNum(sum);
            HeOrderGoodsEntity orderGoodsEntity = goodsEntityList.stream().filter(item -> Objects.nonNull(item.getGoodsPriceOrgin())).max(Comparator.comparing(HeOrderGoodsEntity::getGoodsPriceOrgin)).orElse(null);
            myOrderNewVO.setGoodsImage(Objects.isNull(orderGoodsEntity) ? StringUtils.EMPTY : orderGoodsEntity.getGoodsImage());
            myOrderNewVO.setGoodsName(Objects.isNull(orderGoodsEntity) ? StringUtils.EMPTY : orderGoodsEntity.getGoodsName());
            return;
        }
        //查出所有orderType 0,1,50,70 月子标准订单,小月子订单,护士外派,S-BAR
        List<Integer> commonOrderTypeList = Arrays.asList(OmniOrderTypeEnum.MONTH_ORDER.code(), OmniOrderTypeEnum.SMALL_MONTH_ORDER.code(), OmniOrderTypeEnum.NURSE_OUTSIDE_ORDER.code(), OmniOrderTypeEnum.SBRA_ORDER.code());
        if (commonOrderTypeList.contains(orderEntity.getOrderType())) {
            HeOrderGoodsEntity heOrderGoodsEntity = orderGoodsRepository.getByOrderId(orderEntity.getOrderId());
            if (ObjectUtil.isNotEmpty(heOrderGoodsEntity)) {
                myOrderNewVO.setGoodsName(heOrderGoodsEntity.getGoodsName());
                myOrderNewVO.setGoodsImage(heOrderGoodsEntity.getGoodsImage());
                myOrderNewVO.setServiceDays(heOrderGoodsEntity.getServiceDays());
            }
        } else if (OmniOrderTypeEnum.PRODUCTION_ORDER.code().equals(orderEntity.getOrderType())) {
            myOrderNewVO.setGoodsName(orderProductionExtendRepository.getGoodsNameByOrderId(orderEntity.getOrderId()));
        } else if (OmniOrderTypeEnum.OTHER_MONTH_ORDER.code().equals(orderEntity.getOrderType())) {
            HeOrderOtherEntity heOrderOtherEntity = orderOtherRepository.getByOrderId(orderEntity.getOrderId());
            if (ObjectUtil.isNotEmpty(heOrderOtherEntity)) {
                myOrderNewVO.setGoodsName(heOrderOtherEntity.getGoodsName());
            }
        }
        myOrderNewVO.setMonthAgeGoods(StringUtils.isNotEmpty(myOrderNewVO.getGoodsName()) && myOrderNewVO.getGoodsName().contains("小月龄") ? Boolean.TRUE : Boolean.FALSE);
        myOrderNewVO.setGoodsNum(1);
    }

    private void fillNowpaytask(HeOrderEntity orderEntity, MyOrderNewVO myOrderNewVO) {

        if (ObjectUtil.isNotEmpty(orderEntity.getNowpaytask()) && 0 != orderEntity.getNowpaytask()) {
            return;
        }
        HeTaskEntity byTaskId = heTaskRepository.getByTaskId(orderEntity.getTaskId());
        if (ObjectUtil.isEmpty(byTaskId)) {
            return;
        }
        HeTaskEntity oneByProjectIdAndFormteplateType = heTaskRepository.getOneByProjectIdAndFormteplateType(byTaskId.getProjectId(), 7L);
        if (Objects.nonNull(oneByProjectIdAndFormteplateType)) {
            myOrderNewVO.setNowpaytask(oneByProjectIdAndFormteplateType.getId());
        }
    }

    private Map<Integer, String> getStoreName(List<HeOrderEntity> heOrderEntityList) {

        List<Integer> storeIdList = heOrderEntityList.stream().filter(item -> Objects.nonNull(item.getStoreId())).map(HeOrderEntity::getStoreId).collect(Collectors.toList());
        List<CfgStoreEntity> storeByIdList = storeRepository.queryCfgStoreByIdList(storeIdList);
        if (CollectionUtils.isEmpty(storeByIdList)) {
            return Maps.newHashMap();
        }
        return storeByIdList.stream().collect(Collectors.toMap(CfgStoreEntity::getStoreId, CfgStoreEntity::getStoreName));
    }

    private OrderOperateButton oldButtonList(HeOrderEntity orderEntity) {

        // 纸质合同
        List<ContractSignRecordPaperEntity> paperList = contractSignRecordPaperRepository.getByOrderId(orderEntity.getOrderId(), ContractStatusEnum.SIGNED.code());
        Long orderId = Long.valueOf(orderEntity.getOrderId());
        // 签约记录表
        List<MonthContractSignRecordEntity> mainContractList = monthContractSignRecordRepository.getListByOrderId(orderId, ContractStatusEnum.SIGNED.code());
        List<Integer> orderIdList = Collections.singletonList(orderEntity.getOrderId());
        //提前离馆列表
        List<AheadOutRoomEntity> aheadOutRoomEntities = aheadOutRoomRepository.queryByOrderIds(orderIdList);
        //查询合同解除协议
        List<MonthContractSignRecordEntity> byOrderIdListAndContractStatus = monthContractSignRecordRepository.getByOrderIdListAndContractStatus(orderIdList, TemplateContractTypeEnum.RELEASE.code(), ContractStatusEnum.SIGNED.code());
        //判断是否签署离馆协议
        Integer hasSignContractReleaseAgreement = null;
        if (ObjectUtil.isNotEmpty(byOrderIdListAndContractStatus)) {
            List<MonthContractSignRecordEntity> collect = byOrderIdListAndContractStatus.stream().filter(x -> x.getGuideId().equals(orderId)).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(collect)) {
                hasSignContractReleaseAgreement = collect.get(0).getContractStatus();
            }
        }
        return setPayButton(orderEntity, mainContractList, paperList, aheadOutRoomEntities, hasSignContractReleaseAgreement);
    }

    private OrderOperateButton setPayButton(HeOrderEntity orderEntity, List<MonthContractSignRecordEntity> contractSignRecordList, List<ContractSignRecordPaperEntity> signRecordPaperList, List<AheadOutRoomEntity> aheadOutRoomEntityList, Integer hasSignContractReleaseAgreement) {

        OrderOperateButton button = null;
        //新订单收款-实际已付金额<签单金额;一旦发起提起离馆且签署合同解除协议，则该按钮隐藏
        if (ObjectUtil.isNotEmpty(orderEntity.getOldOrNew()) && orderEntity.getOldOrNew().equals(1)) {
            if (orderEntity.getRealAmount() < orderEntity.getPayAmount() && !(CollectionUtils.isNotEmpty(aheadOutRoomEntityList) && ContractStatusEnum.SIGNED.code().equals(hasSignContractReleaseAgreement))) {
                button = getButton(OrderButtonEnum.COLLECTION);
                //判断订单签订类型
                Integer signType = orderEntity.getSignType();
                if (ObjectUtil.isNotEmpty(signType)) {

                    //委托协议书
                    List<MonthContractSignRecordEntity> powerAttorneyList = CollectionUtil.isEmpty(contractSignRecordList) ? new ArrayList<>() : contractSignRecordList.stream().filter(c -> c.getTemplateContractType().equals(TemplateContractTypeEnum.ENTRUST.code()) && ContractStatusEnum.SIGNED.code().equals(c.getContractStatus())).collect(Collectors.toList());
                    //订单类合同
                    List<MonthContractSignRecordEntity> orderContract = CollectionUtil.isEmpty(contractSignRecordList) ? new ArrayList<>() : contractSignRecordList.stream().filter(c -> c.getTemplateType() == 1 && ContractStatusEnum.SIGNED.code().equals(c.getContractStatus())).collect(Collectors.toList());
                    //纸质合同
                    List<ContractSignRecordPaperEntity> paperEntityList = CollectionUtil.isEmpty(signRecordPaperList) ? new ArrayList<>() : signRecordPaperList.stream().filter(c -> (TemplateContractTypeEnum.APPOINTMENT.code().equals(c.getContractType()) || TemplateContractTypeEnum.YZ_SAINTBELLA.code().equals(c.getContractType())) && ContractStatusEnum.SIGNED.code().equals(c.getContractStatus())).collect(Collectors.toList());
                    //查看是否是老合同
                    MonthContractSignRecordEntity byOrderId = monthContractSignRecordRepository.getByOrderId(orderEntity.getOrderId(), TemplateContractTypeEnum.YZ_SAINTBELLA.code());
                    if (ObjectUtil.isEmpty(byOrderId)) {
                        byOrderId = new MonthContractSignRecordEntity();
                        byOrderId.setContractType(null);
                    }
                    if (signType.equals(OrderSignTypeEnum.SIGN_TYPE_BAILOR.code()) && ObjectUtil.notEqual(byOrderId.getContractType(), ContractTypeEnum.OLD_TYPE.code())) {
                        //委托人的方式签订合同，首先判断是否签订授权委托书
                        if (CollectionUtils.isEmpty(contractSignRecordList)) {
                            button.setText("请先签订授权委托书");
                        } else {
                            if (CollectionUtils.isEmpty(powerAttorneyList)) {
                                button.setText("请先签订授权委托书");
                            } else {
                                //签订授权委托书判断是否签订订单类合同
                                if (CollectionUtils.isEmpty(orderContract) && CollectionUtils.isEmpty(paperEntityList)) {
                                    button.setText("请先签订一个订单类合同");
                                }
                            }
                        }
                    } else {
                        if (CollectionUtils.isEmpty(contractSignRecordList) || (CollectionUtils.isEmpty(orderContract) && CollectionUtils.isEmpty(paperEntityList))) {
                            button.setText("请先签订一个订单类合同");
                        }
                    }
                }
            }
        } else {
            //C端老订单列表，当签单金额-累计支付金额<=0，则隐藏支付按钮即可
            if (orderEntity.getPaidAmount() < orderEntity.getPayAmount()) {
                button = getButton(OrderButtonEnum.COLLECTION);
            }
        }
        return button;
    }

    private OrderOperateButton getButton(OrderButtonEnum buttonEnum) {
        return OrderOperateButton.builder().code(buttonEnum.getCode()).value(buttonEnum.getValue()).build();
    }

}

package com.stbella.order.server.convert;

import cn.hutool.json.JSONUtil;
import com.stbella.order.domain.order.entity.HeCartEntity;
import com.stbella.platform.order.api.req.CreateOrUpdateCartReq;
import com.stbella.platform.order.api.req.CustomAttribute;
import com.stbella.platform.order.api.res.CartRes;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring", imports = {Collectors.class, Arrays.class, ArrayList.class,
        StringUtils.class, CollectionUtils.class, JSONUtil.class, CustomAttribute.class})
public interface CartEntityConverter {

    @Mappings({
            @Mapping(target = "special", source = "extraInfo.special"),
            @Mapping(target = "specialTag", source = "extraInfo.orderTag"),
            @Mapping(target = "specialTagName", source = "extraInfo.orderTagName"),
            @Mapping(target = "specialDesc", source = "extraInfo.remark"),
            @Mapping(target = "specialVoucherUrls", expression = "java(CollectionUtils.isEmpty(req.getExtraInfo().getVoucherUrlList()) ? \"\" : req.getExtraInfo().getVoucherUrlList().stream().collect(Collectors.joining(\",\")))"),
            @Mapping(target = "extendContent", expression = "java(JSONUtil.toJsonStr(req.getExtraInfo().getFulfillExtraList()))"),
            @Mapping(target = "originalOrderId", source = "originalOrderId"),
            @Mapping(target = "originalGoodsTotalPrice", source = "originalGoodsTotalPrice"),
    })
    HeCartEntity req2Entity(CreateOrUpdateCartReq req);

    @Mappings({
            @Mapping(target = "totalAmount", expression = "java(com.stbella.order.common.utils.AmountChangeUtil.f2YScale2( entity.getOrderAmount()))"),
            @Mapping(target = "payAmount", expression = "java(com.stbella.order.common.utils.AmountChangeUtil.f2YScale2( entity.getPayAmount() ))"),
            @Mapping(target = "extraInfo.special", source = "special"),
            @Mapping(target = "extraInfo.orderTag", source = "specialTag"),
            @Mapping(target = "extraInfo.orderTagName", source = "specialTagName"),
            @Mapping(target = "extraInfo.remark", source = "specialDesc"),
            @Mapping(target = "extraInfo.voucherUrlList", expression = "java(StringUtils.isBlank(heCartEntity.getSpecialVoucherUrls()) ? new ArrayList<>() : Arrays.stream(heCartEntity.getSpecialVoucherUrls().split(\",\")).collect(Collectors.toList()))"),
            @Mapping(target = "extraInfo.fulfillExtraList", expression = "java(JSONUtil.toList(heCartEntity.getExtendContent(), CustomAttribute.class))"),
            @Mapping(target = "originalOrderId", source = "originalOrderId"),
            @Mapping(target = "originalGoodsTotalPrice", source = "originalGoodsTotalPrice"),
    })
    CartRes entity2Res(HeCartEntity entity);

}

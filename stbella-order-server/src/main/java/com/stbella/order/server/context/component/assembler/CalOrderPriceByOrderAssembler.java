package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.domain.order.entity.HeCartEntity;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.HeCartRepository;
import com.stbella.order.server.convert.CartEntityConverter;
import com.stbella.order.server.order.OrderPrice;
import com.stbella.platform.order.api.req.CustomAttribute;
import com.stbella.platform.order.api.req.DiscountReq;
import com.stbella.platform.order.api.req.QueryCartReq;
import com.stbella.platform.order.api.res.CartRes;
import com.stbella.platform.order.api.res.DiscountRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.FlowIdentity;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * 根据订单价格详情价格组装费用信息
 */
@Slf4j
@Component
@SnowballComponent(name = "费用计算组装", desc = "费用计算组装")
public class CalOrderPriceByOrderAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private HeCartRepository heCartRepository;
    @Resource
    private CartEntityConverter cartEntityConverter;

    /**
     * 计算价格，成本
     *
     * @return
     */
    @Override
    public void run(FlowContext bizContext) {

        HeOrderEntity heOrder = bizContext.getAttribute(HeOrderEntity.class);

        QueryCartReq queryCartReq = QueryCartReq.builder().orderId(heOrder.getOrderId()).build();
        HeCartEntity cartEntity = heCartRepository.queryOne(queryCartReq);
        bizContext.setAttribute(BizConstant.ExtraKey.storeId, heOrder.getStoreId());
        bizContext.setAttribute(BizConstant.ExtraKey.scene, heOrder.getScene());
        CartRes cart = cartEntityConverter.entity2Res(cartEntity);
        bizContext.setListAttribute(CustomAttribute.class, cart.getExtraInfo().getFulfillExtraList());
        bizContext.setAttribute(HeCartEntity.class, cartEntity);

    }

}

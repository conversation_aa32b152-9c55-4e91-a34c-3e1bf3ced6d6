package com.stbella.order.server.context.component.refund.processor;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundDisabledProductEntity;
import com.stbella.order.domain.order.production.OrderGiftExtendEntity;
import com.stbella.order.domain.order.production.OrderProductionExtendEntity;
import com.stbella.order.domain.repository.HeOrderRefundDisabledProductRepository;
import com.stbella.order.domain.repository.OrderGiftExtendRepository;
import com.stbella.order.domain.repository.OrderProductionExtendRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "客诉闭环审批被拒绝后资产回退")
public class ComplaintApproveAssetBecomesEffectiveProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderGiftExtendRepository orderGiftExtendRepository;
    @Resource
    private OrderProductionExtendRepository orderProductionExtendRepository;
    @Resource
    private HeOrderRefundDisabledProductRepository orderRefundDisabledProductRepository;

    @Override
    public boolean condition(FlowContext context) {
        HeCustomerComplaintsEntity customerComplaintsEntity = context.getAttribute(HeCustomerComplaintsEntity.class);
        return ObjectUtil.isNotEmpty(customerComplaintsEntity.getRefundOrderId());
    }

    @Override
    public void run(FlowContext bizContext) {
        HeCustomerComplaintsEntity customerComplaintsEntity = bizContext.getAttribute(HeCustomerComplaintsEntity.class);
        //退款单
        Long refundOrderId = customerComplaintsEntity.getRefundOrderId();
        rollBackAssets(refundOrderId);


    }

    public void rollBackAssets(Long refundOrderId) {
        List<HeOrderRefundDisabledProductEntity> orderRefundDisabledProductEntityList = orderRefundDisabledProductRepository.queryByRefundId(refundOrderId);

        List<HeOrderRefundDisabledProductEntity> buy = orderRefundDisabledProductEntityList.stream().filter(o -> o.getProductType() == 0).collect(Collectors.toList());

        List<Integer> buyIds = buy.stream().map(HeOrderRefundDisabledProductEntity::getProductId).collect(Collectors.toList());

        List<HeOrderRefundDisabledProductEntity> gift = orderRefundDisabledProductEntityList.stream().filter(o -> o.getProductType() == 1).collect(Collectors.toList());

        List<Integer> giftIds = gift.stream().map(HeOrderRefundDisabledProductEntity::getProductId).collect(Collectors.toList());


        if (CollectionUtil.isNotEmpty(buyIds)) {
            List<OrderProductionExtendEntity> orderProductionExtendEntities = orderProductionExtendRepository.listByExtendIds(buyIds);
            for (OrderProductionExtendEntity orderProductionExtendEntity : orderProductionExtendEntities) {
                Optional<HeOrderRefundDisabledProductEntity> first = buy.stream().filter(b -> b.getProductId().equals(orderProductionExtendEntity.getId())).findFirst();
                if (first.isPresent()) {
                    orderProductionExtendEntity.setStatus(first.get().getSourceStatus());
                }
            }
            orderProductionExtendRepository.saveOrUpdateBatch(orderProductionExtendEntities);
        }

        if (CollectionUtil.isNotEmpty(giftIds)) {
            List<OrderGiftExtendEntity> orderGiftExtendEntities = orderGiftExtendRepository.listByGiftIds(giftIds);
            for (OrderGiftExtendEntity orderGiftExtendEntity : orderGiftExtendEntities) {
                Optional<HeOrderRefundDisabledProductEntity> first = gift.stream().filter(b -> b.getProductId().equals(orderGiftExtendEntity.getId())).findFirst();
                if (first.isPresent()) {
                    orderGiftExtendEntity.setStatus(first.get().getSourceStatus());
                }
            }
            orderGiftExtendRepository.batchUpdateById(orderGiftExtendEntities);
        }
    }

}

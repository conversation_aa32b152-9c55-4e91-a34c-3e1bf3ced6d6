package com.stbella.order.server.context.service.contract;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.contract.model.enums.ContractStatusEnum;
import com.stbella.contract.model.enums.TemplateContractTypeEnum;
import com.stbella.contract.model.req.ContractSignRecordReq;
import com.stbella.contract.model.res.ContractSignRecordVO2;
import com.stbella.core.enums.BusinessEnum;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.HeOrderBailorSnapshotEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.domain.repository.OrderBailorSnapshotRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.domain.repository.OrderUserSnapshotRepository;
import com.stbella.order.server.manager.ContractManager;
import com.stbella.order.server.manager.StoreManager;
import com.stbella.platform.order.api.OrderSupplementService;
import com.stbella.platform.order.api.contract.api.ContractCommandService;
import com.stbella.platform.order.api.contract.req.ContractBaseReq;
import com.stbella.platform.order.api.contract.res.OrderContractPreviewVO;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import com.stbella.platform.order.api.req.CheckSupplementAgreementReq;
import com.stbella.platform.order.api.res.CheckSupplementAgreementRes;
import com.stbella.store.server.ecp.entity.CfgStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.FlowIdentity;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
@Slf4j
public class ContractCommandServiceImpl implements ContractCommandService {

    @Resource
    private ContractManager contractManager;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    StoreManager storeManager;
    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;
    @Resource
    private OrderBailorSnapshotRepository orderBailorSnapshotRepository;
    @Resource
    private OrderSupplementService orderSupplementService;


    @Override
    public void checkContractStatus(ContractBaseReq req) {
//        contractStatusChecker.checkContractStatus(req);
    }


    @Override
    public Result<OrderContractSignRecordVO> createContract(ContractBaseReq req) {
        log.info("创建合同请求参数={}", JSONUtil.toJsonStr(req));
        Integer orderId = req.getOrderId();
        boolean isMonthOrder = ObjectUtil.equals(req.getTemplateContractType(), TemplateContractTypeEnum.YZ_SAINTBELLA.getCode());

        HeOrderEntity orderEntity = orderRepository.getByOrderId(orderId);
        OrderContractSignRecordVO contractSignQueryRecordVO = null;
        if (TemplateContractTypeEnum.SUPPLIMENT.code().equals(req.getTemplateContractType())) {
            if (Objects.nonNull(req.getContractId())) {
                contractSignQueryRecordVO = contractManager.getAgreementById(req.getContractId());
            }
        } else {
            contractSignQueryRecordVO = contractManager.queryContractSignRecordVo(req);
        }
        if (ObjectUtil.isNotEmpty(contractSignQueryRecordVO)) {
            //已经签署则直接返回
            if (ContractStatusEnum.SIGNED.code().equals(contractSignQueryRecordVO.getContractStatus())) {
                return Result.success(contractSignQueryRecordVO);
            }
            //老订单，已经有合同了直接返回
            if (isMonthOrder && !orderEntity.isNewOrder()) {
                return Result.success(contractSignQueryRecordVO);
            }
            //如果订单合同没有生成过短链发送短信
            if (ObjectUtil.isEmpty(contractSignQueryRecordVO.getContractShortUrl()) && !TemplateContractTypeEnum.DISCOUNT.code().equals(req.getTemplateContractType())) {
                return Result.success(contractSignQueryRecordVO);
            }
        } else {
            contractSignQueryRecordVO = new OrderContractSignRecordVO();
        }

        //特殊处理下，判断是主体变更补充协议，走额外的逻辑
        if (TemplateContractTypeEnum.SUPPLIMENT.code().equals(req.getTemplateContractType()) && "主体变更补充协议".equals(contractSignQueryRecordVO.getContractName())) {
            log.info("主体变更补充协议重新创建");
            contractManager.cancelSupplement(req.getContractId().longValue());

            CheckSupplementAgreementReq req1 = new CheckSupplementAgreementReq();
            req1.setOrderId(orderId);
            Result<CheckSupplementAgreementRes> checkSupplementAgreementResResult = orderSupplementService.checkAndCreateSupplementAgreement(req1);
            CheckSupplementAgreementRes data = checkSupplementAgreementResResult.getData();
            log.info("重新创建结果为{}", JSONObject.toJSONString(data));
            OrderContractSignRecordVO supplementAgreement = data.getSupplementAgreement();
            return Result.success(supplementAgreement);
        }

        //进入创建合同流程
        FlowIdentity identity = FlowIdentity.builder().bizActivity(BizActivityEnum.CONTRACT_CREATE.code())
                .idSlice(BusinessEnum.CARE_CENTER.name())
                .idSlice("contract_create")
                .delimiter(":").build();

        CfgStoreEntity cfgStore = storeManager.queryByStoreId(orderEntity.getStoreId());
        HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity = orderBailorSnapshotRepository.queryByOrderId(orderId);
        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(orderId);
        List<OrderContractSignRecordVO> recordAndAgreementListByOrderIdList = contractManager.getContractSignRecordAndAgreementListByOrderIdList(Collections.singletonList(orderId.longValue()));

        FlowContext context = new FlowContext();

        context.setAttribute(ContractBaseReq.class, req);
        context.setAttribute(OrderContractSignRecordVO.class, contractSignQueryRecordVO);
        context.setAttribute(HeOrderEntity.class, orderEntity);
        context.setAttribute(CfgStoreEntity.class, cfgStore);
        HeOrderUserSnapshotEntity orderUserSnapshotEntity = Optional.ofNullable(heOrderUserSnapshotEntity).orElse(new HeOrderUserSnapshotEntity());
        log.info("创建合同请求参数,订单用户快照信息为={}", JSONUtil.toJsonStr(orderUserSnapshotEntity));
        context.setAttribute(HeOrderUserSnapshotEntity.class, orderUserSnapshotEntity);
        context.setAttribute(HeOrderBailorSnapshotEntity.class, Optional.ofNullable(heOrderBailorSnapshotEntity).orElse(new HeOrderBailorSnapshotEntity()));
        context.setListAttribute(OrderContractSignRecordVO.class, recordAndAgreementListByOrderIdList);

        SnowballFlowLauncher.fire(identity, context);

        OrderContractSignRecordVO result = context.getAttribute(OrderContractSignRecordVO.class);

        log.info("创建合同返回结果={}", JSONUtil.toJsonStr(result));

        return Result.success(result);
    }

    @Override
    public Result<OrderContractPreviewVO> previewContract(ContractBaseReq req) {
        Integer orderId = req.getOrderId();
        HeOrderEntity orderEntity = orderRepository.getByOrderId(orderId);
        if (Objects.isNull(orderEntity)) {
            throw new BusinessException("订单不存在");
        }
        if (!orderEntity.isNewOrder()) {
            throw new BusinessException("老订单不支持预览合同");
        }
        OrderContractSignRecordVO contractSignQueryRecordVO = null;
        //首先判断有没有合同id，没有合同id的话，调用创建流程
        Integer contractId = req.getContractId();
        if (Objects.isNull(contractId) || contractId == -1) {
            contractSignQueryRecordVO = createContract(req).getData();
            contractId = contractSignQueryRecordVO.getId().intValue();
        }
        ContractSignRecordReq contractSignRecordReq = new ContractSignRecordReq();
        contractSignRecordReq.setId(contractId.longValue());
        contractSignRecordReq.setTemplateContractType(req.getTemplateContractType());
        ContractSignRecordVO2 contractPreviewUrl = contractManager.getContractPreviewUrl(contractSignRecordReq);

        OrderContractPreviewVO orderContractPreviewVO = new OrderContractPreviewVO();
        orderContractPreviewVO.setPreviewUrl(contractPreviewUrl.getPreviewUrl());
        orderContractPreviewVO.setSignUrl(contractPreviewUrl.getContractShortUrl());
        orderContractPreviewVO.setId(contractPreviewUrl.getId());
        orderContractPreviewVO.setFileName(contractPreviewUrl.getEsignFileName());
        return Result.success(orderContractPreviewVO);
    }
}

package com.stbella.order.server.context.component.validator;

import com.stbella.order.common.enums.core.OrderProcessTypeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.server.context.component.checker.OrderProcessCheckerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

/**
 * 订单减状态校验校验
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "订单减状态校验校验")
public class DecreaseApprovalValidator implements IExecutableAtom<FlowContext> {

    @Resource
    OrderProcessCheckerContext orderProcessCheckerContext;

    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity heOrderEntity = bizContext.getAttribute(HeOrderEntity.class);
        orderProcessCheckerContext.check(heOrderEntity, OrderProcessTypeEnum.DERATE);
    }
}

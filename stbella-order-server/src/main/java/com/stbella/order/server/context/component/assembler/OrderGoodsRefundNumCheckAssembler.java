package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.collection.CollectionUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.domain.order.refund.dto.OrderRefundGoodsDTO;
import com.stbella.platform.order.api.refund.api.OrderRefundService;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import com.stbella.platform.order.api.refund.req.QueryRefundGoodsReq;
import com.stbella.platform.order.api.refund.res.QueryOrderRefundInfoRes;
import com.stbella.platform.order.api.refund.res.QueryRefundGoodsRes;
import com.stbella.platform.order.api.req.CustomerComplaintsCreateRefundReq;
import com.stbella.platform.order.api.req.CustomerComplaintsCreateReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订单商品退款数量校验组件
 * 该组件负责校验客诉退款申请中的商品退款数量是否超过可退数量
 */
@Component
@Slf4j
@SnowballComponent(name = "订单商品的支付退款情况", desc = "获取当前订单的支付信息")
public class OrderGoodsRefundNumCheckAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderRefundService orderRefundService;

    @Override
    public void run(FlowContext bizContext) {
        log.info("开始校验订单商品退款数量");


        CreateRefundReq newCreateRefundReq = bizContext.getAttribute(CreateRefundReq.class);
        if (newCreateRefundReq == null) {
            log.info("非正常退款，走客诉退款校验");

            // 获取客诉创建请求
            CustomerComplaintsCreateReq complaintsCreateReq = bizContext.getAttribute(CustomerComplaintsCreateReq.class);
            if (complaintsCreateReq == null) {
                log.info("客诉创建请求为空，跳过退款数量校验");
                return;
            }

            // 获取退款请求
            CustomerComplaintsCreateRefundReq customerComplaintsCreateRefundReq = complaintsCreateReq.getCreateRefundReq();
            if (customerComplaintsCreateRefundReq == null) {
                log.info("退款请求为空，跳过退款数量校验");
                return;
            }

            // 获取新的退款请求
            newCreateRefundReq = customerComplaintsCreateRefundReq.getNewCreateRefundReq();
            if (newCreateRefundReq == null) {
                log.info("新的退款请求为空，跳过退款数量校验");
                return;
            }
        }

        // 获取商品信息列表
        List<CreateRefundReq.GoodsInfo> goodsInfoList = newCreateRefundReq.getGoodsInfoList();
        if (CollectionUtil.isEmpty(goodsInfoList)) {
            log.info("商品信息列表为空，跳过退款数量校验");
            return;
        }

        // 查询可退商品信息
        Integer orderId = newCreateRefundReq.getOrderId().intValue();
        List<OrderRefundGoodsDTO> refundableGoods = queryRefundableGoods(orderId);

        // 将可退商品信息转换为Map，方便查询
        Map<String, Integer> orderGoodsSnToRefundNumMap = refundableGoods.stream()
                .collect(Collectors.toMap(OrderRefundGoodsDTO::getOrderGoodsSn, OrderRefundGoodsDTO::getRefundNum));

        // 校验每个商品的退款数量
        validateRefundNum(goodsInfoList, orderGoodsSnToRefundNumMap);

        log.info("订单商品退款数量校验通过");
    }

    /**
     * 查询可退商品信息
     *
     * @param orderId 订单ID
     * @return 可退商品信息列表
     */
    private List<OrderRefundGoodsDTO> queryRefundableGoods(Integer orderId) {
        List<OrderRefundGoodsDTO> orderRefundGoodsDTOS = new ArrayList<>();

        try {
            // 构建查询请求
            QueryRefundGoodsReq req = new QueryRefundGoodsReq();
            req.setOrderId(orderId);

            // 调用服务查询可退商品
            Result<QueryOrderRefundInfoRes> queryResult = orderRefundService.queryGoods(req);
            log.info("查询订单[{}]可退商品结果: {}", orderId, queryResult);

            if (queryResult == null || queryResult.getData() == null) {
                log.warn("查询订单[{}]可退商品返回为空", orderId);
                return orderRefundGoodsDTOS;
            }

            List<QueryRefundGoodsRes> goodsResList = queryResult.getData().getQueryRefundGoodsResList();
            if (CollectionUtil.isEmpty(goodsResList)) {
                log.info("订单[{}]没有可退商品", orderId);
                return orderRefundGoodsDTOS;
            }

            // 处理商品列表，包括子商品
            for (QueryRefundGoodsRes queryRefundGoodsRes : goodsResList) {
                processRefundGoodsRes(queryRefundGoodsRes, orderRefundGoodsDTOS, orderId);
            }
        } catch (Exception e) {
            log.error("查询订单[{}]可退商品异常", orderId, e);
        }

        return orderRefundGoodsDTOS;
    }

    /**
     * 处理退款商品响应，提取商品信息
     *
     * @param queryRefundGoodsRes  退款商品响应
     * @param orderRefundGoodsDTOS 结果列表
     * @param orderId              订单ID
     */
    private void processRefundGoodsRes(QueryRefundGoodsRes queryRefundGoodsRes,
                                       List<OrderRefundGoodsDTO> orderRefundGoodsDTOS,
                                       Integer orderId) {
        List<QueryRefundGoodsRes> childList = queryRefundGoodsRes.getChild();

        if (CollectionUtil.isNotEmpty(childList)) {
            // 处理子商品
            for (QueryRefundGoodsRes childRes : childList) {
                OrderRefundGoodsDTO dto = createOrderRefundGoodsDTO(
                        queryRefundGoodsRes.getOrderGoodsSn(), // 父商品SN
                        childRes.getOrderGoodsSn(),
                        orderId,
                        childRes.getRefundGoodsNum()
                );
                orderRefundGoodsDTOS.add(dto);
                if (CollectionUtil.isNotEmpty(childRes.getAdditionList())) {
                    for (QueryRefundGoodsRes refundGoodsRes : childRes.getAdditionList()) {

                        OrderRefundGoodsDTO orderRefundGoodsDTO = createOrderRefundGoodsDTO(
                                childRes.getOrderGoodsSn(), // 父商品SN
                                refundGoodsRes.getOrderGoodsSn(),
                                orderId,
                                refundGoodsRes.getRefundGoodsNum()
                        );
                        orderRefundGoodsDTOS.add(orderRefundGoodsDTO);
                    }
                }
            }
        } else {
            // 处理普通商品
            OrderRefundGoodsDTO dto = createOrderRefundGoodsDTO(
                    null, // 无父商品
                    queryRefundGoodsRes.getOrderGoodsSn(),
                    orderId,
                    queryRefundGoodsRes.getRefundGoodsNum()
            );
            orderRefundGoodsDTOS.add(dto);

            if (CollectionUtil.isNotEmpty(queryRefundGoodsRes.getAdditionList())) {
                for (QueryRefundGoodsRes refundGoodsRes : queryRefundGoodsRes.getAdditionList()) {
                    OrderRefundGoodsDTO orderRefundGoodsDTO = createOrderRefundGoodsDTO(
                            queryRefundGoodsRes.getOrderGoodsSn(), // 父商品SN
                            refundGoodsRes.getOrderGoodsSn(),
                            orderId,
                            refundGoodsRes.getRefundGoodsNum()
                    );
                    orderRefundGoodsDTOS.add(orderRefundGoodsDTO);
                }
            }
        }
    }

    /**
     * 创建OrderRefundGoodsDTO对象
     */
    private OrderRefundGoodsDTO createOrderRefundGoodsDTO(String parentOrderGoodsSn,
                                                          String orderGoodsSn,
                                                          Integer orderId,
                                                          Integer refundNum) {
        OrderRefundGoodsDTO dto = new OrderRefundGoodsDTO();
        dto.setParentOrderGoodsSn(parentOrderGoodsSn);
        dto.setOrderGoodsSn(orderGoodsSn);
        dto.setOrderId(orderId);
        dto.setRefundNum(refundNum);
        return dto;
    }

    /**
     * 校验退款数量
     *
     * @param goodsInfoList              商品信息列表
     * @param orderGoodsSnToRefundNumMap 商品SN到可退数量的映射
     */
    private void validateRefundNum(List<CreateRefundReq.GoodsInfo> goodsInfoList,
                                   Map<String, Integer> orderGoodsSnToRefundNumMap) {
        for (CreateRefundReq.GoodsInfo goodsInfo : goodsInfoList) {
            // 申请退款数量
            Integer refundNum = goodsInfo.getRefundNum();
            if (refundNum == null || refundNum <= 0) {
                log.warn("商品[{}]申请退款数量为空或小于等于0，跳过校验", goodsInfo.getGoodsName());
                continue;
            }

            // 可退数量
            Integer canRefundNum = orderGoodsSnToRefundNumMap.getOrDefault(goodsInfo.getOrderGoodsSn(), 0);

            log.info("商品[{}]申请退款数量: {}, 可退数量: {}",
                    goodsInfo.getGoodsName(), refundNum, canRefundNum);

            // 校验退款数量
            if (refundNum > canRefundNum) {
                log.warn("商品[{}]申请退款数量[{}]超过可退数量[{}]",
                        goodsInfo.getGoodsName(), refundNum, canRefundNum);
                throw new BusinessException(
                        ErrorCodeEnum.REFUND_NUM_OR_AMOUNT.code().toString(),
                        goodsInfo.getGoodsName() + "最多可退数量" + canRefundNum + "！"
                );
            }
        }
    }
}

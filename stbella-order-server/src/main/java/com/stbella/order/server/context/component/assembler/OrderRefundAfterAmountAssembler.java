package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.result.Result;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.core.OrderRefundGoodsStatusEnum;
import com.stbella.order.common.enums.core.OrderRefundNatureEnum;
import com.stbella.order.common.enums.month.PayStatusV2Enum;
import com.stbella.order.common.enums.month.RefundRecordPayStatusEnum;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.order.month.enums.PayMethodEnum;
import com.stbella.order.server.utils.JsonUtil;
import com.stbella.platform.order.api.refund.api.OrderRefundService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
@SnowballComponent(name = "退款都成功后金额处理", desc = "退款都成功后金额处理")
public class OrderRefundAfterAmountAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private HeOrderRefundGoodsRepository orderRefundGoodsRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderRefundService orderRefundService;

    @Override
    public boolean condition(FlowContext context) {
        HeOrderEntity orderEntity = context.getAttribute(HeOrderEntity.class);
        return (boolean) context.getAttribute(OtherConstant.CONTINUE) && ObjectUtil.isNotEmpty(orderEntity) && orderEntity.isNewOrder();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void run(FlowContext bizContext) {
        HeOrderRefundEntity orderRefundEntity = bizContext.getAttribute(HeOrderRefundEntity.class);
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);

        log.info("orderRefundEntity:{}", JsonUtil.write(orderRefundEntity));

        boolean deposit = orderEntity.isDepositOrder();
        List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList = orderRefundGoodsRepository.queryByOrderId(orderRefundEntity.getOrderId());
        //子项退款
        List<HeOrderRefundEntity> childRefundEntity = orderRefundRepository.getRefundByParentSn(orderRefundEntity.getRefundOrderSn());
        //子项退款的支付
        List<HeIncomeRecordEntity> recordListByIncomeSnList = incomeRecordRepository.getRecordListByIncomeSnList(childRefundEntity.stream().map(HeOrderRefundEntity::getIncomeSn).collect(Collectors.toList()));
        orderRefundGoodsEntityList = orderRefundGoodsEntityList.stream().filter(r -> r.getRefundOrderSn().equals(orderRefundEntity.getRefundOrderSn())).collect(Collectors.toList());

        Integer refundNature = orderRefundEntity.getRefundNature();

        //退款主项
        orderRefundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode());
        orderRefundEntity.setTransactionId("");//无第三方流水
        orderRefundEntity.setFinishAt(System.currentTimeMillis() / 1000);
        orderRefundEntity.setActualAmount(orderRefundEntity.getApplyAmount());

        for (HeOrderRefundEntity heOrderRefundEntity : childRefundEntity) {
            heOrderRefundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode());
            heOrderRefundEntity.setTransactionId("");//无第三方流水
            heOrderRefundEntity.setFinishAt(System.currentTimeMillis() / 1000);
            heOrderRefundEntity.setActualAmount(heOrderRefundEntity.getApplyAmount());
            if (ObjectUtil.isNotNull(orderRefundEntity.getComplaintId())) {
                heOrderRefundEntity.setRefundAchievement(0);
            }
            Optional<HeIncomeRecordEntity> first = recordListByIncomeSnList.stream().filter(r -> r.getIncomeSn().equals(heOrderRefundEntity.getIncomeSn())).findFirst();
            if (first.isPresent()) {
                //支付记录修改
                HeIncomeRecordEntity heIncomeRecordEntity = first.get();
                heIncomeRecordEntity.setFreezeAmount((ObjectUtil.isEmpty(heIncomeRecordEntity.getFreezeAmount()) ? 0 : heIncomeRecordEntity.getFreezeAmount()) - heOrderRefundEntity.getApplyAmount());
                heIncomeRecordEntity.setAlreadyRefundAmount((ObjectUtil.isEmpty(heIncomeRecordEntity.getAlreadyRefundAmount()) ? 0 : heIncomeRecordEntity.getAlreadyRefundAmount()) + heOrderRefundEntity.getApplyAmount());
            }
        }
        //订单退款商品表
        for (HeOrderRefundGoodsEntity heOrderRefundGoodsEntity : orderRefundGoodsEntityList) {
            heOrderRefundGoodsEntity.setStatus(OrderRefundGoodsStatusEnum.SUCCESS.code());
        }

        //只有退回重付才去更新订单的冻结金和订单待付金额
        if (refundNature.equals(OrderRefundNatureEnum.TEMP_REFUND.code())) {

            log.info("childRefundEntity:{}", childRefundEntity);

            List<HeOrderRefundEntity> cashList = childRefundEntity.stream().filter(r -> ObjectUtil.isNotEmpty(r.getRefundType()) && PayMethodEnum.PayType2Currency(r.getRefundType()).equals(PayMethodEnum.CASH)).collect(Collectors.toList());
            Integer cash = 0;

            log.info("cashList:{}", cashList);

            if (CollectionUtil.isNotEmpty(cashList)) {
                cash = cashList.stream().mapToInt(HeOrderRefundEntity::getApplyAmount).sum();
            }

            log.info("cash:{}", cash);

            log.info("orderEntity:{}", JsonUtil.write(orderEntity));

            orderEntity.setFreezeAmount(orderEntity.getFreezeAmount() - cash);
            //订单实际金额改变
            orderEntity.setRealAmount(orderEntity.getRealAmount() - cash);
            if ((orderEntity.getRealAmount() + orderEntity.getProductionAmountPay()) > 0) {
                orderEntity.setPayStatus(PayStatusV2Enum.NO_PAY_OFF.getCode());
            } else {
                orderEntity.setPayStatus(PayStatusV2Enum.WAIT_PAY.getCode());
            }
        }

        //更新订单
        orderRepository.updateOne(orderEntity);
        //更新订单退款商品
        orderRefundGoodsRepository.updateList(orderRefundGoodsEntityList);
        //更新支付记录表
        childRefundEntity.add(orderRefundEntity);
        //更新退款记录表
        orderRefundRepository.batchUpdateById(childRefundEntity);
        //子项支付修改
        incomeRecordRepository.batchUpdateRecordList(recordListByIncomeSnList);
    }



}

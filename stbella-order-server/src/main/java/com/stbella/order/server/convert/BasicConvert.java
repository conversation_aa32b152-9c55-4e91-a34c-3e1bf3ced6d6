package com.stbella.order.server.convert;

import com.stbella.core.base.PageVO;
import com.stbella.customer.server.ecp.entity.HeUserBasicPO;
import com.stbella.order.server.config.MappingConfig;
import com.stbella.order.server.order.month.res.BasicSimpInfoVO;
import org.mapstruct.Mapper;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: OrderNutritionConvert
 * @date 2022/1/12 2:30 下午
 */
@Mapper(config = MappingConfig.class)
public interface BasicConvert {

    BasicSimpInfoVO po2vo(HeUserBasicPO po);

    default PageVO<BasicSimpInfoVO> pagePO2VO(PageVO<HeUserBasicPO> poPage) {
        if (poPage == null) {
            return null;
        }

        PageVO<BasicSimpInfoVO> pageVO = new PageVO<BasicSimpInfoVO>();

        if (poPage.getTotalCount() != null) {
            pageVO.setTotalCount(poPage.getTotalCount());
        }
        if (poPage.getPageSize() != null) {
            pageVO.setPageSize(poPage.getPageSize());
        }
        if (poPage.getTotalPage() != null) {
            pageVO.setTotalPage(poPage.getTotalPage());
        }
        if (poPage.getPageNo() != null) {
            pageVO.setPageNo(poPage.getPageNo());
        }
        List<BasicSimpInfoVO> list = heUserBasicPOListToBasicSimpInfoVOList(poPage.getList());
        if (list != null) {
            pageVO.setList(list);
        }

        return pageVO;
    }

    default BasicSimpInfoVO heUserBasicPOToBasicSimpInfoVO(HeUserBasicPO heUserBasicPO) {
        if (heUserBasicPO == null) {
            return null;
        }

        BasicSimpInfoVO basicSimpInfoVO = new BasicSimpInfoVO();

        if (heUserBasicPO.getId() != null) {
            basicSimpInfoVO.setId(heUserBasicPO.getId());
        }
        if (heUserBasicPO.getPhone() != null) {
            basicSimpInfoVO.setPhone(heUserBasicPO.getPhone());
        }
        if (heUserBasicPO.getName() != null) {
            basicSimpInfoVO.setName(heUserBasicPO.getName());
        }
        if (heUserBasicPO.getName() != null && heUserBasicPO.getPhone() != null) {
            basicSimpInfoVO.setNamePhone(heUserBasicPO.getName() + "-" + heUserBasicPO.getPhone());
        }
        return basicSimpInfoVO;
    }

    default List<BasicSimpInfoVO> heUserBasicPOListToBasicSimpInfoVOList(List<HeUserBasicPO> list) {
        if (list == null) {
            return null;
        }

        List<BasicSimpInfoVO> list1 = new ArrayList<BasicSimpInfoVO>(list.size());
        for (HeUserBasicPO heUserBasicPO : list) {
            list1.add(heUserBasicPOToBasicSimpInfoVO(heUserBasicPO));
        }

        return list1;
    }
}

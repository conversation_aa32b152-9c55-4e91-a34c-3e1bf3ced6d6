package com.stbella.order.server.listener;

import cn.hutool.json.JSONUtil;
import com.stbella.core.enums.BusinessEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.listener.event.ProductCoinPaySuccessEvent;
import com.stbella.order.server.order.month.request.pay.PayNotityRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.FlowIdentity;

import javax.annotation.Resource;

/**
 * 产康金支付成功
 */
@Slf4j
@Component
public class ProductCoinPaySuccessListener {

    @Resource
    private OrderRepository orderRepository;

    /**
     * 余额支付成功 同步执行
     *
     * @param event
     */
    @EventListener
    public void productCoinPaySuccess(ProductCoinPaySuccessEvent event) {
        log.info("产康金支付成功 事件 {}", JSONUtil.toJsonStr(event));

        HeOrderEntity order = orderRepository.getByOrderSn(event.getOrderNo());
        BusinessEnum bu = BusinessEnum.getEnumByCode(order.getBu());
        OmniPayTypeEnum payTypeEnum = OmniPayTypeEnum.getByCode(event.getPayType());

        FlowIdentity identity = FlowIdentity.builder().bizActivity(BizActivityEnum.SUCCEED_PAY.code())
                .idSlice(bu.name())
                .idSlice(payTypeEnum.name())
                .build();

        PayNotityRequest payNotityRequest = BeanMapper.map(event, PayNotityRequest.class);

        FlowContext context = new FlowContext();
        context.setAttribute(PayNotityRequest.class, payNotityRequest);
        context.setAttribute(HeOrderEntity.class, order);
        SnowballFlowLauncher.fire(identity, context);
    }

}

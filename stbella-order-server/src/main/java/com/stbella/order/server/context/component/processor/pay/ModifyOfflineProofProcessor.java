package com.stbella.order.server.context.component.processor.pay;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.stbella.order.domain.order.month.entity.HeIncomeProofRecordEntity;
import com.stbella.order.domain.repository.IncomeProofRecordRepository;
import com.stbella.order.common.enums.core.OfflineAuditStatusV2Enum;
import com.stbella.order.server.order.pay.OfflinePayAuditV2Request;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-05-29  15:20
 * @Description: 更新线下支付凭证
 */
@Slf4j
@Component
@SnowballComponent(name = "更新线下支付凭证")
public class ModifyOfflineProofProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private IncomeProofRecordRepository incomeProofRecordRepository;

    @Override
    public void run(FlowContext bizContext) {
        OfflinePayAuditV2Request request = bizContext.getAttribute(OfflinePayAuditV2Request.class);
        HeIncomeProofRecordEntity incomeProofRecordEntity = incomeProofRecordRepository.getLastOneByIncomeId(request.getIncomeId());
        incomeProofRecordEntity.setStatus(request.isAgree() ?
                        OfflineAuditStatusV2Enum.EXAMINATION_PASSED.getCode() :
                        OfflineAuditStatusV2Enum.REVIEW_REJECTED.getCode())
                .setAuditRemark(request.getAuditRemark())
                .setAuditTime((int)DateUtil.currentSeconds())
                .setAuditBasicId(request.getAuditBasicId())
                .setAuditType(1)
                .setIncomeSn(request.getTransactionalNo())
                .setIncomeFinance(request.getIncomeFinance());
        if (ObjectUtil.isNotEmpty(request.getIncomeFinanceTime())) {
            incomeProofRecordEntity.setIncomeFinanceTime(request.getIncomeFinanceTime());
        }
        incomeProofRecordRepository.updateOne(incomeProofRecordEntity);
    }


}

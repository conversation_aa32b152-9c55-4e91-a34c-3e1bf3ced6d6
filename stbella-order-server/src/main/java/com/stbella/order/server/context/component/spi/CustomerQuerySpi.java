package com.stbella.order.server.context.component.spi;

import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.server.context.component.assembler.CustomerAssembler;
import com.stbella.platform.order.api.req.CreateOrderReq;
import top.primecare.snowball.extpoint.annotation.FuncPointSpi;
import top.primecare.snowball.extpoint.definition.IFuncSpiSupport;

/**
 * <AUTHOR>
 * @description: 查询客户
 * 因为不同的渠道来源，客户信息不一样，所以需要根据渠道来源，组装客户信息
 * 如 母婴要用 basic 与 client 表一起。
 * 予家不用
 */
@FuncPointSpi(name = "客户信息组装扩展点", attachToComponent = CustomerAssembler.class)
public interface CustomerQuerySpi extends IFuncSpiSupport<CreateOrderReq, HeOrderUserSnapshotEntity> {

}

package com.stbella.order.server.convert;

import com.stbella.order.domain.order.entity.HeCartGoodsEntity;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CartGoodsEntityConverter {

    @Mappings({
            @Mapping(target = "num", source = "num"),
            @Mapping(target = "skuNum", source = "skuNum"),
            @Mapping(target = "originalGoodsId", source = "originalGoodsId"),
            @Mapping(target = "originalGoodsNum", source = "originalGoodsNum"),
    })
    HeCartGoodsEntity req2Entity(SkuDetailInfo req);

    List<HeCartGoodsEntity> reqList2EntityList(List<SkuDetailInfo> reqList);

    @Mappings({
            @Mapping(target = "num", source = "num"),
            @Mapping(target = "skuNum", source = "skuNum"),
            @Mapping(target = "price", constant = "0"),
            @Mapping(target = "costPrice", constant = "0"),
            @Mapping(target = "originalGoodsId", source = "originalGoodsId"),
            @Mapping(target = "originalGoodsNum", source = "originalGoodsNum"),
    })
    SkuDetailInfo entity2Res(HeCartGoodsEntity entity);

    List<SkuDetailInfo> entityList2ResList(List<HeCartGoodsEntity> entityList);

}

package com.stbella.order.server.convert;

import com.stbella.platform.order.api.req.SkuDetailInfo;
import com.stbella.platform.order.api.res.GoodsInfo;
import com.stbella.store.goodz.res.SkuVo;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.Map;

/**
 * SKU详情信息转换器
 * 用于将SkuVo转换为SkuDetailInfo
 */
@Mapper(componentModel = "spring", 
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SkuDetailInfoConverter {

    SkuDetailInfoConverter INSTANCE = Mappers.getMapper(SkuDetailInfoConverter.class);

    /**
     * 将SkuVo转换为SkuDetailInfo
     *
     * @param target 目标SkuDetailInfo对象（会被修改）
     * @param source 源SkuVo对象
     * @param goodsInfoMap 商品信息映射
     */
    @Mappings({
            @Mapping(target = "skuParentId", source = "source.skuParentId"),
            @Mapping(target = "skuSaleState", source = "source.skuSaleState"),
            @Mapping(target = "skuName", source = "source.spuName"),
            @Mapping(target = "specification", source = "source.templateSkuProp"),
            @Mapping(target = "price", source = "source.goodsPrice", qualifiedByName = "stringToBigDecimal"),
            @Mapping(target = "costPrice", source = "source.costPrice", qualifiedByName = "stringToBigDecimal"),
            @Mapping(target = "backCategoryId", source = "source.categoryBack"),
            @Mapping(target = "assetType", source = "source.goodsType"),
            @Mapping(target = "costPricingMethod", source = "source.costPricingMethod"),
            @Mapping(target = "costRatio", source = "source.costRatio"),
            @Mapping(target = "roomType", source = "source.roomType"),
            @Mapping(target = "roomName", source = "source.roomTypeName"),
            @Mapping(target = "ecpRoomType", source = "source.ecpRoomType"),
            @Mapping(target = "unitStr", source = "source.goodsUnitStr"),
            @Mapping(target = "categoryBackTitle", source = "source.categoryBackTitle"),
            @Mapping(target = "produceAmountDeduction", source = "source.produceAmountDeduction"),
            @Mapping(target = "productionDiscountRuleType", source = "source.productionDiscountRuleType"),
            @Mapping(target = "serviceTime", source = "source.serviceTime"),
            @Mapping(target = "serviceType", source = "source.serviceType"),
            @Mapping(target = "propertyValue", source = "source", qualifiedByName = "convertPropertyValue")
    })
    void updateSkuDetailInfoFromSkuVo(@MappingTarget SkuDetailInfo target, SkuVo source,
                                     @Context Map<Integer, GoodsInfo> goodsInfoMap);

    /**
     * 转换完成后的处理
     */
    @AfterMapping
    default void afterMapping(@MappingTarget SkuDetailInfo target, SkuVo source,
                             @Context Map<Integer, GoodsInfo> goodsInfoMap) {
        // 设置商品信息
        if (target.getGoodsId() != null && goodsInfoMap != null) {
            GoodsInfo goodsInfo = goodsInfoMap.getOrDefault(target.getGoodsId(), new GoodsInfo());
            target.setGoodsInfo(goodsInfo);
        } else {
            target.setGoodsInfo(new GoodsInfo());
        }
    }

    /**
     * 字符串转BigDecimal
     */
    @Named("stringToBigDecimal")
    default BigDecimal stringToBigDecimal(String value) {
        if (value == null || value.trim().isEmpty()) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 转换属性值
     */
    @Named("convertPropertyValue")
    default String convertPropertyValue(SkuVo source) {
        return SkuDetailInfoConverterHelper.convertPropertyValueToJson(source);
    }


}

package com.stbella.order.server.context.service;

import cn.hutool.core.date.DateUtil;
import com.stbella.contract.model.res.ContractSignAgreementVO;
import com.stbella.contract.model.res.ContractSignRecordVO2;
import com.stbella.core.base.Operator;
import com.stbella.core.exception.BusinessException;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.core.OfflineAuditStatusV2Enum;
import com.stbella.order.common.enums.core.OrderRefundNatureEnum;
import com.stbella.order.common.enums.core.OrderRefundStatusEnum;
import com.stbella.order.common.enums.month.*;
import com.stbella.order.common.utils.SpringContextHolder;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.order.service.OrderIncomeDomainService;
import com.stbella.order.domain.repository.OrderRefundRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.manager.AssetManager;
import com.stbella.order.server.manager.ContractManager;
import com.stbella.platform.order.api.OrderAggregateRoot;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-07-26  10:49
 * @Description: 订单聚合根
 * 复合命令模式 使用此类
 */
@Service
@Slf4j
public class OrderAggregateRootImpl implements OrderAggregateRoot {

    long monthSecond = 30 * 24 * 60 * 60;
    @Resource
    private OrderIncomeDomainService incomeDomainService;
    @Resource
    OrderRepository orderRepository;
    @Resource
    ContractManager contractManager;
    @Resource
    AssetManager assetManager;


    /**
     * 关闭订单
     * @param operator
     * @param orderId
     * 1，  创建订单[付款状态]=[待付款]且[实际已付金额]=[0]时起30天(自然日)内若无有效收款记录([实际已付金额]＞0)则系统自动关闭订单
     * 2，  申请退款[退款状态]=[全部退款]即[实际已付金额]=[0]时起30天(自然日)内若无有效收款记录([实际已付金额]＞0)则系统自动关闭订单
     * 3.   合同已签署的订单不可关闭，合同待签署状态的需要进行撤回
     */
    @Override
    public void  close(Operator operator, Integer orderId) {
        HeOrderEntity order = orderRepository.getByOrderId(orderId);
        if (!(order.getPayStatus().intValue() == PayStatusV2Enum.WAIT_PAY.getCode() ||
                order.getRefundStatus().intValue() == OrderRefundStatusEnum.FULL_REFUND.getCode())){
            log.info("订单未待支付，或者全额退款，不可关闭，orderid {}", orderId);
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code()+"","此订单当前不可关闭");
        }

        if (order.getOrderStatus().intValue() == OrderStatusV2Enum.CLOSE.getCode()){
            return;
        }
        List<HeIncomeRecordEntity> incomeList = incomeDomainService.queryEffectiveRecord(order);

        //有审核中的不能关闭
        Optional<HeIncomeRecordEntity> approveIng = incomeList.stream().filter(a -> OfflineAuditStatusV2Enum.UNDER_REVIEW.getCode().equals(a.getApproveStatus())).findFirst();
        if (approveIng.isPresent()){
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code()+"","订单存在审阅中的转账汇款，无法关闭订单");
        }

        //没有收款未操作过30天，
        if (order.getPayStatus().intValue() == PayStatusV2Enum.WAIT_PAY.getCode() && order.getCreatedAt() + monthSecond > DateUtil.currentSeconds()){
            if (!order.isNewOrder()){
                throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code()+"","此订单当前不可关闭");
            }
        }
        //没有退款未操作过30天，
        if (order.getRefundStatus().intValue() == OrderRefundStatusEnum.FULL_REFUND.getCode()){
            // 获取最近的退款记录
            OrderRefundRepository orderRefundRepository = SpringContextHolder.getBean(OrderRefundRepository.class);
            List<HeOrderRefundEntity> refundList = orderRefundRepository.getRefundByOrderId(order.getOrderId());

            // 按创建时间降序排序，获取最近的退款记录
            // 只考虑成功的退款
            Optional<HeOrderRefundEntity> latestRefund = refundList.stream()
                    // 只考虑成功的退款
                    .filter(r -> Objects.equals(r.getStatus(), RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode())).max(Comparator.comparing(HeOrderRefundEntity::getCreatedAt));

            // 判断最近的退款记录是否为退回重付,并且在近30天
            if (latestRefund.isPresent() &&
                    Objects.nonNull(latestRefund.get().getRefundNature()) &&
                    latestRefund.get().getRefundNature().equals(OrderRefundNatureEnum.TEMP_REFUND.code()) &&
                    latestRefund.get().getCreatedAt() + monthSecond > DateUtil.currentSeconds()) {
                // 最近的退款是退回重付，不关闭订单
                log.info("订单最近的退款记录为退回重付，不可关闭，orderId {}", order.getOrderId());
                throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code()+"","此订单最近有退回重付记录，不可关闭");
            }
        }
        //对于未签署的合同进行撤回
        List<ContractSignRecordVO2> contractSignRecordVOList = contractManager.getContractSignRecordListByOrderId(orderId.longValue());
        contractSignRecordVOList.forEach(c -> {
            if (ContractTypeEnum.ESIGN_TYPE.code().equals(c.getContractType()) && !ContractStatusEnum.SIGNED.code().equals(c.getContractStatus())){
                contractManager.cancelContract(c.getId());
            }
        });
        //对于未签署的补充协议进行撤回
        List<ContractSignAgreementVO> agreementList = contractManager.getAgreementByOrderId(orderId.longValue());
        agreementList.forEach(a -> {
            if (!ContractStatusEnum.SIGNED.code().equals(a.getState())){
                contractManager.cancelSupplement(a.getId());
            }
        });

        order.close(operator);

        if (Objects.nonNull(order.getGiftAmount()) && order.getGiftAmount() > 0){
            //退回礼赠金
            assetManager.giveBackGiftAmount(order);
        }

    }
}

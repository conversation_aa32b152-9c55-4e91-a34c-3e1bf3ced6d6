package com.stbella.order.server.context.component;

import com.stbella.core.utils.BkSpringContextHolder;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.*;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Objects;


/**
 * @Author: jijunjian
 * @CreateTime: 2023-10-10  16:38
 * @Description: 汇率配置，按门店配置币种，币种配置汇率（分时段）。从规则中查询，初始化。 放在本地缓存。每小时更新一次
 */
@Slf4j
@Component
public class StoreCurrencyContainer implements ApplicationListener<ApplicationReadyEvent> {


    @Setter
    @Getter
    private static RuleLinkClient ruleLinkClient;

    /**
     * Handle an application event.
     *
     * @param event the event to respond to
     */
    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        log.info("初始化 RuleLinkClient");
        ruleLinkClient = BkSpringContextHolder.getBean(RuleLinkClient.class);
        log.info("初始化 RuleLinkClient 完成");
    }

    /**
     * 默认币种, 汇率为1
     */
    public static String Default_Currency = "￥";


    /**
     * 查询门店汇率
     * 汇率 1个单位其他币种对多少人民币。
     *
     * @return
     */
    public static String getStoreCurrencyCode(Integer storeId) {
        log.info("查询门店汇率, storeId:{}", storeId);
//        log.info("storeCurrencyCache, storeCurrencyCache:{}", JSONUtil.toJsonStr(storeCurrencyCache));
//        if (storeCurrencyCache.containsKey(storeId)) {
//            return storeCurrencyCache.get(storeId);
//        }
        String currency = Default_Currency;
        // 根据规则查询，并添加到map 中
        Map<String, Object> factMap = new HashMap<>(1);
        factMap.put("storeId", storeId);
        ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req("store_currency", factMap);
        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
        if (hitRuleVo != null) {
            // 增加一个空格
            currency = hitRuleVo.getSimpleRuleValue()+" ";
        }
        return currency;
    }

    /**
     * 查询门店汇率
     * 汇率 1个单位其他币种对多少人民币。
     *
     * @return
     */
    public static BigDecimal getStoreCurrencyFxRate(String currency, Long bizTime) {

        currency = currency.trim();
        log.info("查询当前币种兑换人民币的汇率, currency:{}, startTime:{}", currency, bizTime);
        // 根据规则查询，并添加到map 中
        Map<String, Object> factMap = new HashMap<>(2);
        factMap.put("currency", currency);
        if (Objects.nonNull(bizTime)) {
            factMap.put("startTime", bizTime);
        }
        ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req("currency_exchange_rate", factMap);
        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
        if (Objects.isNull(hitRuleVo)) {
            return new BigDecimal(1);
        }
        return new BigDecimal(hitRuleVo.getSimpleRuleValue());
    }

    /**
     * 根据门店和时间获取汇率
     *
     * @return
     */
    public static BigDecimal getStoreRate(Integer storeId, Long bizTime) {

        String storeCurrencyCode = getStoreCurrencyCode(storeId);
        storeCurrencyCode = storeCurrencyCode.trim();
        return getStoreCurrencyFxRate(storeCurrencyCode, bizTime);
    }

}

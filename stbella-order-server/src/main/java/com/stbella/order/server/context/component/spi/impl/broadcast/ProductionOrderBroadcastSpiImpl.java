package com.stbella.order.server.context.component.spi.impl.broadcast;

import com.stbella.core.enums.BusinessEnum;
import com.stbella.core.result.Result;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.server.context.component.assembler.GoodsNamesForBroadcastAssembler;
import com.stbella.order.server.context.component.spi.OrderBroadcastSpi;
import com.stbella.order.server.context.dto.OrderBroadcastDto;
import com.stbella.order.server.order.month.component.NoticeAssembler;
import com.stbella.order.server.order.month.req.OrderNoticeReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.annotation.FuncPointSpiImpl;
import top.primecare.snowball.extpoint.definition.FuncSpiConfig;
import top.primecare.snowball.flow.core.context.FlowContext;

import javax.annotation.Resource;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-05-30  14:01
 * @Description: 产康订单报单扩展点实现
 */
@Component
@Slf4j
@FuncPointSpiImpl(name = "产康订单报单扩展点实现")
public class ProductionOrderBroadcastSpiImpl implements OrderBroadcastSpi {

    @Resource
    private NoticeAssembler noticeAssembler;
    @Resource
    GoodsNamesForBroadcastAssembler goodsNamesForBroadcastAssembler;

    @Override
    public boolean condition(HeOrderEntity param) {
        if (CompareUtil.integerEqual(param.getBu(), BusinessEnum.CARE_CENTER.getCode())
                && CompareUtil.integerEqual(param.getOrderType(), OmniOrderTypeEnum.PRODUCTION_ORDER.getCode())) {
            return true;
        }
        return false;
    }

    @Override
    public Result invoke(HeOrderEntity order) {
        log.info("产康订单报单扩展点实现");
        FlowContext bizContext = new FlowContext();
        bizContext.setAttribute(HeOrderEntity.class, order);
        goodsNamesForBroadcastAssembler.run(bizContext);
        OrderBroadcastDto broadcastDto = bizContext.getAttribute(OrderBroadcastDto.class);
        if (broadcastDto.getBroadcast()) {
            log.info("产康订单报单扩展点实现");
            order.noticed();
            order.effectPerformance();

            OrderNoticeReq req = new OrderNoticeReq()
                    .setOrderId(order.getOrderId())
                    .setBasicUid(order.getBasicUid())
                    .setStaffId(order.getStaffId())
                    .setPercentFirstTime(order.getPercentFirstTime());
            final String ddMsg = noticeAssembler.queryProductNoticeAssembler(req);
            noticeAssembler.pushProductMsg(ddMsg, order.getStoreId());
            try {
                if (noticeAssembler.isSTBNewLead(order.getBasicUid())) {
                    log.info("触发stb商机报单逻辑");
                    noticeAssembler.pushSTBNewLeadMsg(req, order.getStoreId());
                }
            } catch (Exception e) {
                log.warn("exception : ", e);
            }
            return Result.success();
        } else {
            log.info("此订单不报单{}", order.getOrderSn());
            return Result.failed("此订单不能报单");
        }
    }

    @Override
    public FuncSpiConfig config() {
        //默认优先级高
        return new FuncSpiConfig(true, 100);
    }
}

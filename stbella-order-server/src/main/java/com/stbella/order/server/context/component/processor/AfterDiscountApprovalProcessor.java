package com.stbella.order.server.context.component.processor;

import cn.hutool.json.JSONObject;
import com.stbella.core.result.Result;
import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.core.CartSceneEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.repository.OrderRefundRepository;
import com.stbella.order.server.context.service.cashier.CashierCommandServiceImpl;
import com.stbella.order.server.order.month.enums.DiscountRuleResultEnum;
import com.stbella.order.server.order.month.req.PayReqV2;
import com.stbella.order.server.order.month.request.approval.ApprovalStatusInfo;
import com.stbella.order.server.strategy.approval.OrderRefundApprovalStrategy;
import com.stbella.platform.order.api.req.CreateOrderReq;
import com.stbella.platform.order.api.res.DiscountRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR> 赵志锋
 * 发起折扣审批之后的处理
 */
@Component
@Slf4j
@SnowballComponent(name = "发起折扣审批之后的处理", desc = "")
public class AfterDiscountApprovalProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderRefundRepository orderRefundRepository;

    @Resource
    private OrderRefundApprovalStrategy orderRefundApprovalStrategy;

    @Resource
    private CashierCommandServiceImpl cashierCommandService;

    @Override
    public boolean condition(FlowContext bizContext) {
        DiscountRes discountRes = bizContext.getAttribute(DiscountRes.class);
        CreateOrderReq req = bizContext.getAttribute(CreateOrderReq.class);
        // 判断是否是升级订单场景
        if (!CartSceneEnum.UPGRADE_ORDER.code().equals(req.getScene())) {
            return false;
        }
        // 判断是否需要发起折扣审批
        if (DiscountRuleResultEnum.NO_APPROVAL_REQUIRED.getCode().equals(discountRes.getDiscountRuleResult())) {
            log.info("无需审批，直接通过退款审批");
            return true;
        }
        return false;
    }

    @Override
    public void run(FlowContext bizContext) {

        log.info("无须发起折扣审批，直接通过退款审批");
        // 无需折扣审批，直接将退款审批通过
        handleRefundApprovalDirectly(bizContext);

        log.info("退款完成之后，直接调用余额支付逻辑");
        // 退款完成后，调用余额支付逻辑
        handleBalancePayment(bizContext);

    }

    /**
     * 无需折扣审批时，直接通过退款审批
     */
    private void handleRefundApprovalDirectly(FlowContext bizContext) {

        log.info("无需折扣审批，调用OrderRefundApprovalStrategy处理退款审批");

        // 创建审批状态信息，模拟审批通过
        ApprovalStatusInfo approvalStatusInfo = new ApprovalStatusInfo();
        approvalStatusInfo.setFinish(true);
        approvalStatusInfo.setAgree(true);
        approvalStatusInfo.setRefuse(false);
        approvalStatusInfo.setTerminate(false);

        // 从上下文获取必要参数
        Integer parentRefundOrderId = (Integer) bizContext.getAttribute(OtherConstant.PARENT_REFUND_ORDER_ID);
        if (parentRefundOrderId == null) {
            log.error("未找到退款ID，无法处理退款审批");
            return;
        }

        // 调用OrderRefundApprovalStrategy处理审批通过逻辑
        // 注意：这里需要构造审批参数，可能需要从上下文或退款记录中获取
        orderRefundApprovalStrategy.handleApproval(OrderApproveRecordTypeEnum.ORDER_REFUND, buildRefundApprovalParams(bizContext), "AUTO_APPROVE_" + parentRefundOrderId, approvalStatusInfo, "AUTO_PROCESS_" + parentRefundOrderId);

        log.info("无需折扣审批，退款审批自动通过: refundId={}", parentRefundOrderId);


    }

    /**
     * 构建退款审批参数
     */
    private String buildRefundApprovalParams(FlowContext bizContext) {
        // 这里需要构建RefundApprovalRequest的JSON字符串
        // 可以从上下文中获取订单信息等参数
        // 简化处理，返回最基本的参数
        Integer parentRefundOrderId = (Integer) bizContext.getAttribute(OtherConstant.PARENT_REFUND_ORDER_ID);

        // 查询退款记录获取订单ID
        HeOrderRefundEntity refundEntity = orderRefundRepository.getOneById(parentRefundOrderId);
        if (refundEntity != null) {
            return String.format("{\"orderId\":%d,\"orderRefundId\":%d}", refundEntity.getOrderId(), parentRefundOrderId);
        }

        return "{\"orderRefundId\":" + parentRefundOrderId + "}";
    }

    /**
     * 退款完成后，调用余额支付逻辑（意向金支付到新订单）
     */
    private void handleBalancePayment(FlowContext bizContext) {
        try {
            // 获取新订单信息（当前正在创建的订单）
            HeOrderEntity newOrderEntity = bizContext.getAttribute(HeOrderEntity.class);
            if (newOrderEntity == null) {
                log.error("未找到新订单信息，无法进行余额支付");
                return;
            }

            Integer parentRefundOrderId = (Integer) bizContext.getAttribute(OtherConstant.PARENT_REFUND_ORDER_ID);
            if (parentRefundOrderId == null) {
                log.error("未找到退款ID，无法进行余额支付");
                return;
            }

            // 查询退款记录获取退款金额
            HeOrderRefundEntity refundEntity = orderRefundRepository.getOneById(parentRefundOrderId);
            if (refundEntity == null) {
                log.error("未找到退款记录，无法进行余额支付，refundId={}", parentRefundOrderId);
                return;
            }

            // 构建支付请求，支付到新订单
            PayReqV2 payRequest = buildBalancePayRequest(refundEntity, newOrderEntity);

            // 调用余额支付逻辑
            log.info("开始调用余额支付逻辑，newOrderId={}, refundAmount={}, originalOrderId={}", newOrderEntity.getOrderId(), refundEntity.getApplyAmount(), refundEntity.getOrderId());
            Result<JSONObject> payResult = cashierCommandService.onlinePay(payRequest);

            if (payResult != null && payResult.getSuccess()) {
                log.info("余额支付成功，newOrderId={}, refundId={}, payResult={}", newOrderEntity.getOrderId(), parentRefundOrderId, payResult.getData());
            } else {
                log.error("余额支付失败，newOrderId={}, refundId={}, error={}", newOrderEntity.getOrderId(), parentRefundOrderId, payResult != null ? payResult.getMsg() : "result is null");
            }

        } catch (Exception e) {
            log.error("处理余额支付失败", e);
        }
    }

    /**
     * 构建余额支付请求
     */
    private PayReqV2 buildBalancePayRequest(HeOrderRefundEntity refundEntity, HeOrderEntity newOrderEntity) {
        PayReqV2 payRequest = new PayReqV2();

        // 设置新订单信息（支付到新订单）
        payRequest.setOrderId(newOrderEntity.getOrderId());

        // 设置支付方式为意向金支付
        payRequest.setPayType(OmniPayTypeEnum.BALANCE.getCode());

        // 设置支付金额（退款金额，从分转换为元）
        payRequest.setPayAmount(new java.math.BigDecimal(refundEntity.getApplyAmount()).divide(new java.math.BigDecimal(100), 2, java.math.RoundingMode.HALF_UP));

        // 设置账户类型（默认为0-贝康）
        payRequest.setAccountType(0);

        // 设置其他必要参数
        payRequest.setAmountType(1); // 假设为1，具体值需要根据业务确定
        payRequest.setChannelType(1); // 1-原生
        payRequest.setCallType(1); // 1-URL重定向
        payRequest.setRemark("商品升级，原订单商品退款后重新支付至新订单");

        log.info("构建余额支付请求：newOrderId={}, payType={}, payAmount={}, accountType={}, refundFromOrderId={}", payRequest.getOrderId(), payRequest.getPayType(), payRequest.getPayAmount(), payRequest.getAccountType(), refundEntity.getOrderId());

        return payRequest;
    }
}

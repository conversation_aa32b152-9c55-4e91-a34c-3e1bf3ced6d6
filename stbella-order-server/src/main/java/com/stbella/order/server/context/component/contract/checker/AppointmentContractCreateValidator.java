package com.stbella.order.server.context.component.contract.checker;

import cn.hutool.core.collection.CollectionUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.core.utils.BigDecimalUtil;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.repository.OrderGoodsRepository;
import com.stbella.platform.order.api.contract.req.ContractBaseReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 预约协议
 */
@Component
@Slf4j
public class AppointmentContractCreateValidator extends AbstractContractCreateValidator {

    @Resource
    private OrderGoodsRepository orderGoodsRepository;

    /**
     * 预约协议
     *
     * @param contractValidationContext
     * @return
     */
    @Override
    public boolean doValidate(ContractValidationContext contractValidationContext) {

        checkPaperContract(contractValidationContext);
        ContractBaseReq req = contractValidationContext.getRequest();
        HeOrderEntity orderEntity = contractValidationContext.getOrderEntity();
        //预约协议 客户输入金额判断是否大于50%
        if (Objects.isNull(req.getAmountEarnest())) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "预约协议支付意向金不能为空");
        }

        if (req.getAmountEarnest().compareTo(new BigDecimal(0)) <= 0) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "只能输入大于0,且小于订单金额50%的数字");
        }

        if (req.getAmountEarnest().compareTo(BigDecimalUtil.divide(new BigDecimal(orderEntity.getPayAmount()), new BigDecimal(200))) >= 0) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "超出订单金额50%,请签订<<母婴护理服务合同书>>");
        }

        List<HeOrderGoodsEntity> orderGoodsEntityList = orderGoodsRepository.getAllItermByOrderId(orderEntity.getOrderId());
        if (CollectionUtil.isEmpty(orderGoodsEntityList)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "订单商品不存在,订单orderId=" + orderEntity.getOrderId());
        }


        return true;
    }

    @Override
    public TemplateContractTypeEnum templateContractType() {
        return TemplateContractTypeEnum.APPOINTMENT;
    }

}
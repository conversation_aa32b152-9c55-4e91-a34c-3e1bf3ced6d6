package com.stbella.order.server.fact;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "合同规则实体", description = "合同规则实体")
public class ContractFact extends BasicReq implements Serializable {
    private static final long serialVersionUID = 6616565746205526624L;

    @ApiModelProperty(value = "业务线 BusinessEnum")
    @NotNull
    private Integer bu = 0;

    @ApiModelProperty(value = "订单id")
    private Integer orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    @ApiModelProperty(value = "订单类型")
    private Integer orderType;

    @ApiModelProperty(value = "门店id")
    private Integer storeId;

    @ApiModelProperty(value = "门店类型 看枚举 StoreTypeEnum")
    private Integer storeType;

    @ApiModelProperty(value = "门店子品牌 看枚举 StoreChildTypeEnum ")
    private Integer childType;

    @ApiModelProperty(value = "客户id")
    public Integer clientUid;

    @ApiModelProperty(value = "模板关联合同类型不能为空", required = true)
    private Integer secondSort;

    @ApiModelProperty(value = "合同类型不能为空 TemplateTypeEnum")
    private Integer firstSort;

    @ApiModelProperty(value = "模板类型1-e签宝(默认),2-手工模板 ContractTypeEnum")
    private Integer contractType;

    @ApiModelProperty(value = "合同类型", required = true)
    private Integer templateType;

    @ApiModelProperty(required = true)
    private Integer templateContractType;

    @ApiModelProperty(value = "签章所在页面第几页")
    private Integer posPage;

    private Boolean standardOrderV1 = Boolean.FALSE;

}

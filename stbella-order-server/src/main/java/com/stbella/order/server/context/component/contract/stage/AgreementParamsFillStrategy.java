package com.stbella.order.server.context.component.contract.stage;

import cn.hutool.core.date.DateTime;
import com.stbella.contract.common.constant.BizConstant;
import com.stbella.contract.model.req.OrderParamHistoryChangeValuePushDTO;
import com.stbella.core.utils.BigDecimalUtil;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.GeneratorUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.domain.repository.OrderReductionRepository;
import com.stbella.order.server.contract.req.OrderParamHistoryPushDTO;
import com.stbella.order.server.contract.req.OrderParamHistoryValuePushDTO;
import com.stbella.order.server.fact.ContractFact;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class AgreementParamsFillStrategy extends ContractFillStrategy {

    @Resource
    private OrderReductionRepository orderReductionRepository;

    @Override
    protected Map<String, String> buildParam(ContractParamFillContext contractParamFillContext) {
        HeOrderEntity order = contractParamFillContext.getOrderEntity();
        //创建补充协议
        Long fetchTotalReductionAmount = 0L;
        OrderContractSignRecordVO currentSignRecord = contractParamFillContext.getCurrentSignRecord();
        if (Objects.nonNull(currentSignRecord) && Objects.nonNull(currentSignRecord.getId())) {
            OrderReductionEntity orderReductionEntity = orderReductionRepository.selectByContractId(currentSignRecord.getId());
            OrderReductionEntity firstOrderReductionEntity = orderReductionRepository.selectFirstReductionEntity(orderReductionEntity.getOrderId());
            fetchTotalReductionAmount = firstOrderReductionEntity.getBeforeAmount() - orderReductionEntity.getAfterAmount();
        } else {
            fetchTotalReductionAmount = order.fetchTotalReductionAmount();
        }

        List<OrderParamHistoryChangeValuePushDTO> changeValuePushDTOS = new ArrayList<>();
        //减免折扣
        addDiscount(order, fetchTotalReductionAmount, changeValuePushDTOS);
        //签单金额
        addPayAmount(order, fetchTotalReductionAmount, changeValuePushDTOS);
        //预定金
        addReservation(order, fetchTotalReductionAmount, changeValuePushDTOS);
        //尾款
        addBalancePayment(order, fetchTotalReductionAmount, changeValuePushDTOS);

        for (OrderParamHistoryChangeValuePushDTO changeValuePushDTO : changeValuePushDTOS) {
            changeValuePushDTO.setParam1("专用条款");
            changeValuePushDTO.setParam2("【附件一：购买清单】");
        }
        OrderContractSignRecordVO orderContractSignRecordVO = contractParamFillContext.fetchMainSignRecord();
        Map<String, String> templateContext = generateTemplateData(orderContractSignRecordVO, changeValuePushDTOS, order);
        String contractNo = GeneratorUtil.contractNo();
        templateContext.put("contract_no", contractNo);
        //主合同编号
        templateContext.put("main_contract_no", orderContractSignRecordVO.getContractNo());
        templateContext.put("company_address", BizConstant.COMPANY_ADDRESS);
        DateTime dateTime = new DateTime(orderContractSignRecordVO.getUpdatedAt() * 1000);
        templateContext.put("sign_time_str2", dateTime.toDateStr());

        return templateContext;
    }

    public HashMap<String, String> generateTemplateData(OrderContractSignRecordVO signRecord, List<OrderParamHistoryChangeValuePushDTO> orderParamHistoryChangeValuePushDTOS, HeOrderEntity orderEntity) {


        //所有文字段落的数组,这里将每段先放到数组中,全部生成完成后再拼接,切割分页
        List<String> textList = new ArrayList<>();

        String signTimeStr3 = "";
        //如果没有关联合同,也没有修改协议  则直接抛出异常,不需要生成补充协议
        //不是-1说明修改内容中涉及合同  生成合同相关描述
        DateTime dateTime = new DateTime(signRecord.getUpdatedAt() * 1000);
        int year = dateTime.year();
        int month = dateTime.monthBaseOne();
        int day = dateTime.dayOfMonth();
        String contractNo = signRecord.getContractNo();
        String firsText = "一、鉴于，甲、乙双方于【" + year + "年" + month + "月" + day + "日】签订了编号为【" + contractNo + "】的《服务合同》，现经双方友好协商，就调整原合同部分内容的相关事项，签署补充协议如下：\n";
        textList.add(firsText);
        signTimeStr3 = dateTime.toDateStr();
        //如果map有数据说明有参数变更了
        //索引,每次数据变更都+1
        Integer index = 0;
        for (OrderParamHistoryChangeValuePushDTO orderParamHistoryChangeValuePushDTO : orderParamHistoryChangeValuePushDTOS) {
            //合同名称
            String oldValue = orderParamHistoryChangeValuePushDTO.getOldValue();
            String value = orderParamHistoryChangeValuePushDTO.getNewValue();
            String paramName = orderParamHistoryChangeValuePushDTO.getName() + ": ";
            index++;
            String contentText = index + ".双方协商一致同意就《服务合同》中【" + orderParamHistoryChangeValuePushDTO.getParam1() + "】的" + orderParamHistoryChangeValuePushDTO.getParam2() + "项进行调整，《服务合同》【" + paramName + oldValue + "】变更为【" + paramName + value + "】。\n";
            textList.add(contentText);
        }

        String template5 = "二、除本协议明确所作修改、变更及补充的内容外，原合同的其他条款均继续有效。本协议签订生效后，即成为原合同不可分割的部分，与原合同具有同等法律效力。\n";
        String template6 = "三、原合同与本协议约定不一致的，以本协议约定为准；本协议尚未约定或约定不明的，以原合同约定的为准。\n";
        String template7 = "四、本协议于双方法定代表人或授权代表签字（或盖章）并加盖公章或合同专用章后生效，本协议一式两份，甲方持一份，乙方执一份，每份具有同等法律效力。\n";
        String template8 = "\n\n\n甲方：\n" + "法定代表人或授权代表（签章）：\n\n\n\n\n\n\n" + "乙方：\n" + "法定代表人或授权代表（签章）：\n\n" + "签订日期：" + DateTime.now().year() + "年" + DateTime.now().monthBaseOne() + "月" + DateTime.now().dayOfMonth() + "日";

        textList.add(template5);
        textList.add(template6);
        textList.add(template7);

        /*
          第六部分:
          计算高低,切分tab1,2,3
          查询甲乙方字段,填充
          返回结果
         */

        /*
          这里是变量配置
          positionA,positionB 为签章X轴位置  如需左右移动签章位置,可编辑该参数
          pageNum 签章页码
          rowWidth 行宽,计算常量 如果合同模版中样式发生改变,需要同时变动该常量  后续可考虑根据合同模版进行配置化
          rowHeight 行高,计算常量 如果合同模版中样式发生改变,需要同时变动该常量  后续可考虑根据合同模版进行配置化
          tabHigh 每页行数,计算常量   如果合同模版中样式发生改变,需要同时变动该常量  后续可考虑根据合同模版进行配置化
          height  页高,第一页设置为380 后面设置为560  根据每页的文本域来决定,后续可扩展为配置化
          textStrList 每页内容,这里默认为4页
          textIndex 当前写入的文本序号 textList保存着生成的所有内容,使用textIndex来控制当前需要读取内容
          tabHighIndex 当前记录页,0-3  记录当前内容生成到哪页
         */

        String positionA = "270,";
        String positionB = "270,";
        //基础高度 第一页默认430,第二页670
        int height = 380;
        //行高,计算常量
        int rowHeight = 18;
        //这里写定模版的高度行,现在模版一共四页
        List<Integer> tabHigh = new ArrayList<>();
        tabHigh.add(32);
        tabHigh.add(41);
        tabHigh.add(41);
        tabHigh.add(41);
        int tabHighIndex = 0;
        int textIndex = 0;

        // 正文 根据textStrList的数据来具体生成文本
        List<StringBuffer> textStrList = new ArrayList<>();
        textStrList.add(new StringBuffer());
        textStrList.add(new StringBuffer());
        textStrList.add(new StringBuffer());
        textStrList.add(new StringBuffer());
        int rows = 0;
        StringBuilder builder = new StringBuilder();
        int length;
        for (String s : textList) {
            length = 0;
            for (char aChar : s.toCharArray()) {
                builder.append(aChar);
                if (!"\n".equals(String.valueOf(aChar))) {
                    length += String.valueOf(aChar).getBytes(StandardCharsets.UTF_8).length > 1 ? 2 : 1;
                    if (length >= 69) {
                        length = 0;
                        builder.append("\n");
                    }
                }
            }
        }
        String[] splits = builder.toString().split("\n");
        for (String split : splits) {
            if (StringUtils.hasText(split)) {
                StringBuffer stringBuffer = textStrList.get(textIndex);
                stringBuffer.append(split).append("\n");
                rows++;
                if (tabHigh.get(tabHighIndex) == rows) {
                    rows = 0;
                    textIndex++;
                    tabHighIndex++;
                    height = 560;
                }
            }
        }
        if (tabHigh.get(tabHighIndex) < (rows + 15)) {
            rows = 15;
            textIndex++;
            height = 560;
        } else {
            rows += 15;
        }
        textStrList.get(textIndex).append(template8);

        height -= (rows * rowHeight);

        HashMap<String, String> contentData = new HashMap<>();
        //合同信息,这个是为了拿到甲方,乙方等信息
        OrderParamHistoryPushDTO orderParamHistoryPushDTO = orderParamHistoryService.buildOrderParamHistory(orderEntity);
        List<OrderParamHistoryValuePushDTO> templateData = orderParamHistoryPushDTO.getParamList();
        for (OrderParamHistoryValuePushDTO orderParamHistory : templateData) {
            contentData.put(orderParamHistory.getName(), orderParamHistory.getValue());
        }
        /*
          计算签章位置
         */
        //pageNum :签章位置页码 position_a:甲方盖章坐标,x,y格式 position_b 乙方盖章坐标,x,y格式
        int pageNum = textIndex + 1;
        positionA = positionA + (height + 400);
        positionB = positionB + (height + 280);
        for (int i = 1; i < 5; i++) {
            //给每页的文本域填写对应的数值
            contentData.put("tab_" + i, textStrList.get(i - 1).toString());
        }
        contentData.put("pageNum", String.valueOf(pageNum));
        contentData.put("positionA", positionA);
        contentData.put("positionB", positionB);
        contentData.put("sign_time_str3", signTimeStr3);

        return contentData;
    }


    @Override
    protected ContractFact buildFactExtra(ContractFact contractFact) {
        //补充协议默认使用两页的模板
        contractFact.setPosPage(2);
        return contractFact;
    }

    @Override
    protected String getContractName(ContractParamFillContext contractParamFillContext) {
        OrderContractSignRecordVO orderContractSignRecordVO = contractParamFillContext.fetchMainSignRecord();
        OrderContractSignRecordVO currentSignRecord = contractParamFillContext.getCurrentSignRecord();
        if (Objects.nonNull(currentSignRecord) && Objects.nonNull(currentSignRecord.getId())) {
            return currentSignRecord.getContractName();
        }
        List<OrderContractSignRecordVO> signRecords = contractParamFillContext.getSignRecords();
        List<OrderContractSignRecordVO> signRecordVOS = signRecords.stream().filter(o -> TemplateContractTypeEnum.SUPPLIMENT.code().equals(o.getTemplateContractType())).collect(Collectors.toList());
        return orderContractSignRecordVO.getContractName() + "之补充协议" + (signRecordVOS.size() + 1);
    }

    @Override
    boolean filter(Integer templateContractType) {
        return TemplateContractTypeEnum.SUPPLIMENT.code().equals(templateContractType);
    }

    private void addBalancePayment(HeOrderEntity order, Long fetchTotalReductionAmount, List<OrderParamHistoryChangeValuePushDTO> changeValuePushDTOS) {
        Integer calPayable = order.getPayAmount() - fetchTotalReductionAmount.intValue();
        BigDecimal newDecreaseAmount = BigDecimalUtil.divide(AmountChangeUtil.changeF2Y(calPayable), new BigDecimal(2));
        BigDecimal oldDecreaseAmount = BigDecimalUtil.divide(AmountChangeUtil.changeF2Y(order.getPayAmount()), new BigDecimal(2));

//        addParams("pay_amount_3", newDecreaseAmount, paramList, "pay_amount_3_words", dto);
        addChangeDTO("尾款", oldDecreaseAmount, newDecreaseAmount, changeValuePushDTOS, "尾款大写");

    }

    private void addReservation(HeOrderEntity order, Long fetchTotalReductionAmount, List<OrderParamHistoryChangeValuePushDTO> changeValuePushDTOS) {
        Integer calPayable = order.getPayAmount() - fetchTotalReductionAmount.intValue();
        BigDecimal newDecreaseAmount = BigDecimalUtil.divide(AmountChangeUtil.changeF2Y(calPayable), new BigDecimal(2));
        BigDecimal oldDecreaseAmount = BigDecimalUtil.divide(AmountChangeUtil.changeF2Y(order.getPayAmount()), new BigDecimal(2));

        addChangeDTO("预定金", oldDecreaseAmount, newDecreaseAmount, changeValuePushDTOS, "预定金大写");
    }

    private void addDiscount(HeOrderEntity order, Long fetchTotalReductionAmount, List<OrderParamHistoryChangeValuePushDTO> changeValuePushDTOS) {
        BigDecimal newDecreaseAmount = AmountChangeUtil.changeF2Y(order.fetchFirstDiscountAmount() + fetchTotalReductionAmount);
        BigDecimal oldDecreaseAmount = AmountChangeUtil.changeF2Y(order.fetchFirstDiscountAmount());

        addChangeDTO("折扣减免", oldDecreaseAmount, newDecreaseAmount, changeValuePushDTOS, "折扣减免大写");


    }

    private static void addPayAmount(HeOrderEntity order, Long fetchTotalReductionAmount, List<OrderParamHistoryChangeValuePushDTO> changeValuePushDTOS) {
        Integer calPayable = order.getPayAmount() - fetchTotalReductionAmount.intValue();
        BigDecimal newPayAmount = AmountChangeUtil.changeF2Y(calPayable);
        BigDecimal payAmount = AmountChangeUtil.changeF2Y(order.getPayAmount());

        addChangeDTO("签单金额", payAmount, newPayAmount, changeValuePushDTOS, "签单金额大写");

    }

    private static void addChangeDTO(String name1, BigDecimal oldDecreaseAmount, BigDecimal newDecreaseAmount, List<OrderParamHistoryChangeValuePushDTO> changeValuePushDTOS, String name2) {
        OrderParamHistoryChangeValuePushDTO orderParamHistoryChangeValuePushDTO = new OrderParamHistoryChangeValuePushDTO();
        orderParamHistoryChangeValuePushDTO.setName(name1);
        orderParamHistoryChangeValuePushDTO.setOldValue(RMBUtils.formatToseparaDecimals(oldDecreaseAmount));
        orderParamHistoryChangeValuePushDTO.setNewValue(RMBUtils.formatToseparaDecimals(newDecreaseAmount));
        changeValuePushDTOS.add(orderParamHistoryChangeValuePushDTO);


        OrderParamHistoryChangeValuePushDTO chineseShow = new OrderParamHistoryChangeValuePushDTO();
        chineseShow.setName(name2);
        chineseShow.setOldValue(RMBUtils.numToRMBStr(oldDecreaseAmount.doubleValue()));
        chineseShow.setNewValue(RMBUtils.numToRMBStr(newDecreaseAmount.doubleValue()));
        changeValuePushDTOS.add(chineseShow);
    }


    private static void addParams(String price_reduction, BigDecimal newDecreaseAmount, List<OrderParamHistoryValuePushDTO> paramList, String name, OrderParamHistoryPushDTO dto) {
        OrderParamHistoryValuePushDTO orderParamHistoryValuePushDTO = new OrderParamHistoryValuePushDTO();
        orderParamHistoryValuePushDTO.setName(price_reduction);
        orderParamHistoryValuePushDTO.setValue(RMBUtils.formatToseparaDecimals(newDecreaseAmount));

        paramList.add(orderParamHistoryValuePushDTO);

        OrderParamHistoryValuePushDTO param18 = new OrderParamHistoryValuePushDTO();
        param18.setName(name);
        param18.setValue(RMBUtils.numToRMBStr(newDecreaseAmount.doubleValue()));
        paramList.add(param18);

        dto.setParamList(paramList);
    }


}

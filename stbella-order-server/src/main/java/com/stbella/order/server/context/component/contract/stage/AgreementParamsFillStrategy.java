package com.stbella.order.server.context.component.contract.stage;

import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.server.context.component.contract.stage.template.AgreementParamsFillTemplate;
import com.stbella.order.server.contract.req.OrderParamHistoryPushDTO;
import com.stbella.order.server.contract.req.OrderParamHistoryValuePushDTO;
import com.stbella.order.server.fact.ContractFact;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AgreementParamsFillStrategy extends ContractFillStrategy {

    @Resource
    private AgreementParamsFillTemplate[] agreementParamsFillTemplate;

    @Override
    protected Map<String, String> buildParam(ContractParamFillContext contractParamFillContext) {

        HeOrderEntity order = contractParamFillContext.getOrderEntity();
        AgreementParamsFillTemplate template = getTemplate(order.getBu());
        return template.buildParam(contractParamFillContext);
    }


    @Override
    protected ContractFact buildFactExtra(ContractFact contractFact) {
        //补充协议默认使用两页的模板
        contractFact.setPosPage(2);
        return contractFact;
    }

    @Override
    protected String getContractName(ContractParamFillContext contractParamFillContext) {
        OrderContractSignRecordVO orderContractSignRecordVO = contractParamFillContext.fetchMainSignRecord();
        OrderContractSignRecordVO currentSignRecord = contractParamFillContext.getCurrentSignRecord();
        if (Objects.nonNull(currentSignRecord) && Objects.nonNull(currentSignRecord.getId())) {
            return currentSignRecord.getContractName();
        }
        List<OrderContractSignRecordVO> signRecords = contractParamFillContext.getSignRecords();
        List<OrderContractSignRecordVO> signRecordVOS = signRecords.stream().filter(o -> TemplateContractTypeEnum.SUPPLIMENT.code().equals(o.getTemplateContractType())).collect(Collectors.toList());
        return orderContractSignRecordVO.getContractName() + "之补充协议" + (signRecordVOS.size() + 1);
    }

    @Override
    boolean filter(Integer templateContractType) {
        return TemplateContractTypeEnum.SUPPLIMENT.code().equals(templateContractType);
    }

    private static void addParams(String price_reduction, BigDecimal newDecreaseAmount, List<OrderParamHistoryValuePushDTO> paramList, String name, OrderParamHistoryPushDTO dto) {
        OrderParamHistoryValuePushDTO orderParamHistoryValuePushDTO = new OrderParamHistoryValuePushDTO();
        orderParamHistoryValuePushDTO.setName(price_reduction);
        orderParamHistoryValuePushDTO.setValue(RMBUtils.formatToseparaDecimals(newDecreaseAmount));

        paramList.add(orderParamHistoryValuePushDTO);

        OrderParamHistoryValuePushDTO param18 = new OrderParamHistoryValuePushDTO();
        param18.setName(name);
        param18.setValue(RMBUtils.numToRMBStr(newDecreaseAmount.doubleValue()));
        paramList.add(param18);

        dto.setParamList(paramList);
    }

    public AgreementParamsFillTemplate getTemplate(Integer bizType) {

        log.info("获取补充协议参数模板开始处理: dataType:{}", bizType);
        if (bizType == null) {
            return null;
        }
        return Arrays.stream(agreementParamsFillTemplate).filter(item -> item.bizHandleType().equals(bizType)).findAny()
                .orElse(null);
    }


}

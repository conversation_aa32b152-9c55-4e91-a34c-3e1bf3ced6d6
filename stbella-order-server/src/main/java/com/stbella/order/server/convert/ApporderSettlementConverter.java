package com.stbella.order.server.convert;

import cn.hutool.json.JSONUtil;
import com.stbella.order.domain.order.entity.HeCartEntity;
import com.stbella.order.server.order.month.res.DepositSettlementVO;
import com.stbella.order.server.order.month.res.OrderSettlementVO;
import com.stbella.platform.order.api.req.CreateOrUpdateCartReq;
import com.stbella.platform.order.api.req.CustomAttribute;
import com.stbella.platform.order.api.res.CartRes;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring", imports = {Collectors.class, Arrays.class, ArrayList.class,
        StringUtils.class, CollectionUtils.class, JSONUtil.class, CustomAttribute.class})
public interface ApporderSettlementConverter {

    @Mappings({
            @Mapping(target = "deposit", source = "payAmount"),
            @Mapping(target = "receivableAmount", source = "unpaidAmount"),
            @Mapping(target = "totalIncome", source = "paidAmount"),
    })
    DepositSettlementVO order2Deposite(OrderSettlementVO settlementVO);

}

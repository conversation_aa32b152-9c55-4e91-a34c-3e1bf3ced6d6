package com.stbella.order.server.context.component.assembler;

import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.platform.order.api.reduction.req.DecreaseReq;
import com.stbella.platform.order.api.req.DiscountReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import java.math.BigDecimal;

/**
 * 折扣请求参数组装
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@SnowballComponent(name = "折扣请求参数组装", desc = "折扣请求参数组装")
public class DiscountReqAssembler implements IExecutableAtom<FlowContext> {


    @Override
    public void run(FlowContext bizContext) {

        HeOrderEntity heOrderEntity = bizContext.getAttribute(HeOrderEntity.class);
        DecreaseReq req = bizContext.getAttribute(DecreaseReq.class);

        DiscountReq discountReq = new DiscountReq();
        discountReq.setScene(heOrderEntity.getScene());
        discountReq.setBu(heOrderEntity.getBu());
        discountReq.setOrderType(heOrderEntity.getOrderType());
        discountReq.setStoreId(heOrderEntity.getStoreId());
        discountReq.setPayAmount(AmountChangeUtil.changeF2Y(BigDecimal.valueOf(heOrderEntity.calPayable() - AmountChangeUtil.changeY2FFoInt(req.getDecreaseAmount())).longValue()));

        bizContext.setAttribute(DiscountReq.class, discountReq);
    }
}

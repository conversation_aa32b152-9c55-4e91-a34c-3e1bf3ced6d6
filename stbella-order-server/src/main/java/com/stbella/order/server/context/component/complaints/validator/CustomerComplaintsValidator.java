package com.stbella.order.server.context.component.complaints.validator;

import com.stbella.core.exception.BusinessException;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsEntity;
import com.stbella.order.domain.repository.HeCustomerComplaintsRepository;
import com.stbella.order.server.order.month.enums.CustomerComplaintsStatusEnum;
import com.stbella.platform.order.api.req.CustomerComplaintsCreateReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@RefreshScope
@SnowballComponent(name = "客诉校验")
public class CustomerComplaintsValidator implements IExecutableAtom<FlowContext> {

    @Resource
    HeCustomerComplaintsRepository heCustomerComplaintsRepository;

    @Override
    public void run(FlowContext bizContext) {
        CustomerComplaintsCreateReq complaintsCreateReq = bizContext.getAttribute(CustomerComplaintsCreateReq.class);
        List<HeCustomerComplaintsEntity> customerComplaintsEntities = heCustomerComplaintsRepository.getListByOrderId(complaintsCreateReq.getOrderId());
        if (CollectionUtils.isNotEmpty(customerComplaintsEntities)) {
            List<Integer> complainStatusList = customerComplaintsEntities.stream().map(HeCustomerComplaintsEntity::getComplaintStatus).collect(Collectors.toList());
            if (complainStatusList.contains(CustomerComplaintsStatusEnum.APPROVAL_ING.getCode())) {
                throw new BusinessException(ErrorCodeEnum.ALREADY_HAS_APPROVAL.code() + "", ErrorCodeEnum.ALREADY_HAS_APPROVAL.desc());
            }
        }
        //退款金额校验

    }


    @Override
    public boolean condition(FlowContext bizContext) {
        CustomerComplaintsCreateReq complaintsCreateReq = bizContext.getAttribute(CustomerComplaintsCreateReq.class);
        return Boolean.FALSE.equals(complaintsCreateReq.getIsDraft());
    }

}

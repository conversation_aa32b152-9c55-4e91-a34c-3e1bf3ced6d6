package com.stbella.order.server.context.component.refund.processor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.base.server.ding.MonthDingService;
import com.stbella.base.server.ding.response.CreateOrderApproveRecordVO;
import com.stbella.core.result.Result;
import com.stbella.month.server.request.DepositRefundApprovalRequest;
import com.stbella.month.server.request.RefundApprovalRequest;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OrderRefundNatureEnum;
import com.stbella.order.common.enums.month.RefundReasonEnum;
import com.stbella.order.common.utils.JsonUtil;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.domain.repository.OrderUserSnapshotRepository;
import com.stbella.order.domain.repository.StoreRepository;
import com.stbella.order.server.order.cts.enums.OrderRefundResultEnum;
import com.stbella.order.server.order.month.enums.PayMethodEnum;
import com.stbella.order.server.order.month.enums.RefundTypeEnum;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import com.stbella.platform.order.api.res.CreateApproveRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;


@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "退款发起审批")
public class OrderRefundCreateApprovalProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private StoreRepository storeRepository;
    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;
    @DubboReference
    private MonthDingService monthDingService;

    @Override
    public void run(FlowContext bizContext) {
        //本次退款sn
        Integer parentRefundOrderId = (Integer) bizContext.getAttribute(OtherConstant.PARENT_REFUND_ORDER_ID);
        //本次请求参数
        CreateRefundReq attribute = bizContext.getAttribute(CreateRefundReq.class);
        //主退款
        HeOrderRefundEntity orderRefundEntity = bizContext.getAttribute(HeOrderRefundEntity.class);
        //押金
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        Boolean deposit = orderEntity.isDepositOrder();
        log.info("退款发起审批开始处理parentRefundOrderId:{}, attribute:{}, orderRefundEntity:{}, deposit:{}", parentRefundOrderId, JSONUtil.toJsonStr(attribute), JSONUtil.toJsonStr(orderRefundEntity), deposit);

        //订单信息
        CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(orderEntity.getStoreId());
        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(attribute.getOrderId());
        Result<CreateOrderApproveRecordVO> refundApprovalOriginalRoad;
        if (deposit) {
            DepositRefundApprovalRequest request = getDepositRefundApprovalRequest(attribute, orderEntity, cfgStoreEntity, heOrderUserSnapshotEntity, orderRefundEntity, parentRefundOrderId);
            refundApprovalOriginalRoad = monthDingService.createDepositRefundApproval(request, null);
        } else {
            RefundApprovalRequest request = getRefundApprovalRequest(attribute, orderEntity, cfgStoreEntity, heOrderUserSnapshotEntity, parentRefundOrderId);
            refundApprovalOriginalRoad = monthDingService.createRefundApproval(request, null);
        }
        CreateApproveRes createApproveRes = new CreateApproveRes();
        CreateOrderApproveRecordVO createOrderApproveRecordVO = refundApprovalOriginalRoad.getData();
        if (!refundApprovalOriginalRoad.getSuccess() || createOrderApproveRecordVO.getStatus() != 1) {
            createApproveRes.setType(OrderRefundResultEnum.RESULT_3.getCode());
            createApproveRes.setMsg(OrderRefundResultEnum.RESULT_3.getValue());
            //只有成功才往下走
            bizContext.setAttribute(OtherConstant.CONTINUE, false);
            log.info("审批发起失败,msg={}", refundApprovalOriginalRoad.getMsg());
        } else {
            createApproveRes.setType(OrderRefundResultEnum.SUCCESS.getCode());
            createApproveRes.setMsg(OrderRefundResultEnum.SUCCESS.getValue());
        }
        createApproveRes.setRefundId(parentRefundOrderId);
        bizContext.setAttribute(CreateApproveRes.class, createApproveRes);
    }


    //押金退款
    private DepositRefundApprovalRequest getDepositRefundApprovalRequest(
            CreateRefundReq attribute,
            HeOrderEntity orderEntity,
            CfgStoreEntity cfgStoreEntity,
            HeOrderUserSnapshotEntity heOrderUserSnapshotEntity,
            HeOrderRefundEntity orderRefundEntity,
            Integer parentRefundOrderId
    ) {
        DepositRefundApprovalRequest request = new DepositRefundApprovalRequest();
        request.setBasicUid(orderEntity.getBasicUid().longValue());
        request.setCustomerName(heOrderUserSnapshotEntity.getName());
        request.setStoreName(cfgStoreEntity.getStoreName());
        request.setCustomerPhone(heOrderUserSnapshotEntity.getPhone());
        request.setTotalDepositCollection(RMBUtils.bigDecimalF2Y(orderEntity.getPayAmount()));
        request.setAmountRefundApplied(RMBUtils.bigDecimalF2Y(orderRefundEntity.getApplyAmount()));
        request.setRefundReason(RefundReasonEnum.getName(orderRefundEntity.getRefundReason()));
        request.setOrderRefundId(parentRefundOrderId);
        request.setPhone(attribute.getOperator().getOperatorPhone());
        request.setOrderId(attribute.getOrderId());
        request.setImagesFileIds(attribute.getAuditRefundProof());
        request.setRemark(attribute.getRemark());
        String refundInfo = orderRefundEntity.getRefundInfo();
        LinkedHashMap read = JsonUtil.read(refundInfo, LinkedHashMap.class);
        request.setImagesFileIds((List<String>) read.get("images"));
        return request;
    }

    //原路退回审批
    private RefundApprovalRequest getRefundApprovalRequest(CreateRefundReq attribute, HeOrderEntity orderEntity, CfgStoreEntity cfgStoreEntity, HeOrderUserSnapshotEntity heOrderUserSnapshotEntity, Integer parentRefundOrderId) {
        RefundApprovalRequest request = new RefundApprovalRequest();

        List<CreateRefundReq.GoodsRefundAmountInfo> refundAmountInfoList = attribute.getRefundAmountInfoList();

        BigDecimal refundAmount = BigDecimal.ZERO;


        Optional<CreateRefundReq.GoodsRefundAmountInfo> cashFirst = refundAmountInfoList.stream().filter(r -> r.getAmountType().toString().equals(PayMethodEnum.CASH.getCode())).findFirst();
        Optional<CreateRefundReq.GoodsRefundAmountInfo> ckjFirst = refundAmountInfoList.stream().filter(r -> r.getAmountType().toString().equals(PayMethodEnum.CJK.getCode())).findFirst();
        if (cashFirst.isPresent()) {
            BigDecimal amount = cashFirst.get().getAmount();
            request.setApplyCashRefundAmount(amount);
            request.setCashRefundMethod(RefundTypeEnum.getValueByCode(attribute.getRefundType()));
            refundAmount = refundAmount.add(amount);
        }
        if (ckjFirst.isPresent()) {
            BigDecimal amount = ckjFirst.get().getAmount();
            request.setProductionGoldRefundMethod("原路退回");
            request.setApplyProductionGoldRefundAmount(amount);
            refundAmount = refundAmount.add(amount);
        }
        request.setRefundAmount(refundAmount);
        Integer refundReasonType = attribute.getRefundReasonType();
        request.setOrderSn(orderEntity.getOrderSn());
        request.setRefundType(refundReasonType == 1 ? "正常退款" : "⾮正常退款（客诉）");
        request.setRefundReason(RefundReasonEnum.getName(attribute.getRefundReason()));
        request.setStoreName(cfgStoreEntity.getStoreName());
        request.setCustomerName(heOrderUserSnapshotEntity.getName());
        request.setCustomerPhone(heOrderUserSnapshotEntity.getPhone());
        request.setContractAmount(RMBUtils.bigDecimalF2Y(orderEntity.calPayable()));
        request.setPaidAmount(RMBUtils.bigDecimalF2Y(orderEntity.getRealAmount() + (ObjectUtil.isEmpty(orderEntity.getProductionAmountPay()) ? 0 : orderEntity.getProductionAmountPay())).setScale(2));
        request.setOrderId(attribute.getOrderId());
        request.setPhone(attribute.getOperator().getOperatorPhone());
        request.setRefundMethod(RefundTypeEnum.getValueByCode(attribute.getRefundType()));
        request.setOrderRefundId(parentRefundOrderId);
        request.setRefundMethodId(attribute.getRefundType());
        request.setImagesList(attribute.getAuditRefundProof());
        request.setRemark(attribute.getRemark());
        request.setRefundNature(OrderRefundNatureEnum.getByCode(attribute.getGoodsRefundType()).getDesc());
        request.setOrderType(orderEntity.getOrderType().equals(OmniOrderTypeEnum.PRODUCTION_ORDER.getCode()) ? OmniOrderTypeEnum.PRODUCTION_ORDER.getDesc() : OmniOrderTypeEnum.MONTH_ORDER.getDesc());
        StringBuffer stringBuffer = new StringBuffer();
        for (CreateRefundReq.GoodsInfo goodsInfo : attribute.getGoodsInfoList()) {
            stringBuffer.append(goodsInfo.getGoodsName()).append("*").append(goodsInfo.getRefundNum()).append("\n");
        }
        request.setRefundGoodsInfo(stringBuffer.toString());

        return request;
    }

    @Override
    public boolean condition(FlowContext context) {
        CreateRefundReq attribute = context.getAttribute(CreateRefundReq.class);
        Long complaintId = attribute.getComplaintId();
        Boolean isUpgradeOrderAutoRefund = attribute.getIsUpgradeOrderAutoRefund();
        
        // 客诉退款：跳过审批流程
        if (ObjectUtil.isNotEmpty(complaintId)) {
            Integer parentRefundOrderId = (Integer) context.getAttribute(OtherConstant.PARENT_REFUND_ORDER_ID);
            CreateApproveRes createApproveRes = new CreateApproveRes();
            createApproveRes.setType(OrderRefundResultEnum.SUCCESS.getCode());
            createApproveRes.setMsg(OrderRefundResultEnum.SUCCESS.getValue());
            createApproveRes.setRefundId(parentRefundOrderId);
            context.setAttribute(CreateApproveRes.class, createApproveRes);
            log.info("客诉退款，跳过审批流程，parentRefundOrderId={}", parentRefundOrderId);
            return false;
        }
        
        // 升级订单自动退款：跳过审批流程
        if (isUpgradeOrderAutoRefund != null && isUpgradeOrderAutoRefund) {
            Integer parentRefundOrderId = (Integer) context.getAttribute(OtherConstant.PARENT_REFUND_ORDER_ID);
            CreateApproveRes createApproveRes = new CreateApproveRes();
            createApproveRes.setType(OrderRefundResultEnum.SUCCESS.getCode());
            createApproveRes.setMsg(OrderRefundResultEnum.SUCCESS.getValue());
            createApproveRes.setRefundId(parentRefundOrderId);
            context.setAttribute(CreateApproveRes.class, createApproveRes);
            log.info("升级订单自动退款，跳过审批流程，parentRefundOrderId={}", parentRefundOrderId);
            return false;
        }
        
        return (boolean) context.getAttribute(OtherConstant.CONTINUE);
    }
}

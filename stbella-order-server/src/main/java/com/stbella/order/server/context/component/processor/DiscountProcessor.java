package com.stbella.order.server.context.component.processor;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.order.common.constant.FlowContextConstant;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.server.order.month.req.MonthDingReq;
import com.stbella.order.server.order.month.req.OrderProductionDiscountApprovalReq;
import com.stbella.order.server.order.month.service.MonthDingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

/**
 * @Author: wangchuang
 * @CreateTime: 2024-05-28  13:55
 * @Description: 折扣审批组件
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "折扣审批组件")
public class DiscountProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private MonthDingService monthDingService;

    @Override
    public void run(FlowContext bizContext) {
        OrderReductionEntity orderReductionEntity = bizContext.getAttribute(OrderReductionEntity.class);
        Boolean approved = orderReductionEntity.getDisCountApprove();
        if (ObjectUtil.isNotEmpty(approved) && approved) {
            HeOrderEntity heOrderEntity = bizContext.getAttribute(HeOrderEntity.class);
            Integer orderType = heOrderEntity.getOrderType();
            //月子
            if (ObjectUtil.equals(orderType, OmniOrderTypeEnum.MONTH_ORDER.getCode())) {
                MonthDingReq monthDingReq = new MonthDingReq();
                monthDingReq.setOrderId(heOrderEntity.getOrderId());
                monthDingService.createOrderDiscountApproval(monthDingReq);
            } else if (ObjectUtil.equals(orderType, OmniOrderTypeEnum.PRODUCTION_ORDER.getCode())) {
                // TODO: 2024/5/30  产康
                OrderProductionDiscountApprovalReq orderProductionDiscountApprovalReq = new OrderProductionDiscountApprovalReq();
                monthDingService.createProductionDiscountApproval(orderProductionDiscountApprovalReq);
            }
        }
    }
}

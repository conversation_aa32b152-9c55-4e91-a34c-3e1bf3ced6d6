package com.stbella.order.server.context.component.validator;

import com.stbella.core.exception.BusinessException;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.server.context.component.settlement.ProductCoinPayAssembler;
import com.stbella.order.server.manager.AssetManager;
import com.stbella.order.server.order.month.req.PayReqV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

/**
 * 产康金支付校验
 */
@Slf4j
@Component
@RefreshScope
@SnowballComponent(name = "产康金支付校验")
public class ProductCoinPayValidator implements IExecutableAtom<FlowContext> {

    @Resource
    ProductCoinPayAssembler productCoinPayAssembler;
    @Resource
    AssetManager assetManager;

    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);
        PayReqV2 payReq = bizContext.getAttribute(PayReqV2.class);
        Integer fetchOrderAmount = productCoinPayAssembler.fetchOrderAmount(order);
        Integer payAmount = AmountChangeUtil.changeY2FFoInt(payReq.getPayAmount());
        if (payAmount > fetchOrderAmount) {
            log.error("产康金支付校验失败，订单号：{}，产康金可抵扣金额：{}，支付金额：{}", order.getOrderSn(), fetchOrderAmount, payAmount);
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code() + "", "产康金支付金额不可大于抵扣金额！");
        }
        //查询当前用户产康金余额
        Integer productCoinBalance = assetManager.queryProductCoinBalance(order.getBasicUid());
        if (payAmount > productCoinBalance) {
            log.error("产康金支付校验失败，订单号：{}，产康金余额：{}，支付金额：{}", order.getOrderSn(), productCoinBalance, payAmount);
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code() + "", "产康金余额不足！");
        }
    }

    @Override
    public boolean condition(FlowContext context) {
        HeOrderEntity order = context.getAttribute(HeOrderEntity.class);
        PayReqV2 payReq = context.getAttribute(PayReqV2.class);
        return OmniOrderTypeEnum.PRODUCTION_ORDER.getCode().equals(order.getOrderType()) && OmniPayTypeEnum.PRODUCTION_COIN.getCode().equals(payReq.getPayType());
    }

}

package com.stbella.order.server.context.component.validator;

import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Slf4j
@Component
@RefreshScope
@SnowballComponent(name = "订单退款前校验查询")
public class OrderRefundBeforeAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @Resource
    private IncomePaidAllocationRepository incomePaidAllocationRepository;
    @Resource
    private HeOrderRefundGoodsRepository orderRefundGoodsRepository;
    @Resource
    private OrderGoodsRepository orderGoodsRepository;


    @Override
    public void run(FlowContext bizContext) {
        Object objectOrderId = bizContext.getAttribute(OtherConstant.ORDER_ID);
        Integer orderId = new Integer(objectOrderId.toString());
        //订单
        HeOrderEntity orderEntity = orderRepository.getByOrderId(orderId);
        bizContext.setAttribute(HeOrderEntity.class, orderEntity);
        //退款记录
        List<HeOrderRefundEntity> refundByOrderId = orderRefundRepository.getRefundByOrderId(orderId);
        bizContext.setListAttribute(HeOrderRefundEntity.class, refundByOrderId);
        //支付记录
        List<HeIncomeRecordEntity> allRecordListByOrderId = incomeRecordRepository.getAllRecordListByOrderId(orderId);
        bizContext.setListAttribute(HeIncomeRecordEntity.class, allRecordListByOrderId);
        //分摊记录
        List<IncomePaidAllocationEntity> incomePaidAllocationEntities = incomePaidAllocationRepository.queryListByOrderId(orderId.longValue());
        //退款商品记录
        List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList = orderRefundGoodsRepository.queryByOrderId(orderId);
        //订单商品列表
        List<HeOrderGoodsEntity> orderGoodsList = orderGoodsRepository.getByOrderIdList(Collections.singletonList(orderId));
        bizContext.setListAttribute(HeOrderRefundGoodsEntity.class, orderRefundGoodsEntityList);
        bizContext.setListAttribute(IncomePaidAllocationEntity.class, incomePaidAllocationEntities);
        bizContext.setListAttribute(HeOrderGoodsEntity.class, orderGoodsList);
    }


    @Override
    public boolean condition(FlowContext context) {
        return true;
    }
}

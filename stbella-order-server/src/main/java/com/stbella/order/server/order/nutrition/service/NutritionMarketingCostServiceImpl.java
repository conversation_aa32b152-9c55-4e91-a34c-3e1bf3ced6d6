package com.stbella.order.server.order.nutrition.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.order.server.order.nutrition.entity.NutritionMarketingCostPO;
import com.stbella.order.server.order.nutrition.mapper.NutritionMarketingCostMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 广禾堂-费用管理-品牌营销费用 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2022-09-14
 */
@Service
public class NutritionMarketingCostServiceImpl extends ServiceImpl<NutritionMarketingCostMapper, NutritionMarketingCostPO> implements NutritionMarketingCostService {

    @Override
    public List<NutritionMarketingCostPO> selectByYear(Integer year) {
        return this.list(new LambdaQueryWrapper<NutritionMarketingCostPO>().eq(NutritionMarketingCostPO::getYear, year));
    }
}

package com.stbella.order.server.order.job;

import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.platform.order.api.OldOrderTransService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 老订单切换成新订单
 *
 * <AUTHOR>
 * <p>
 * 标准月子订单转月子订单
 * <p>
 * <p>
 * 1. 修改数据库，old_or_new 1 - 3
 * version  1.00 - 3.00
 * 订单标签为普通订单，
 * <p>
 * 后台查看商品清单报错
 * <p>
 * 原因 order_goods 里面gift字段为空，设置成购买  0
 * <p>
 * <p>
 * 小程序发起退单报错
 * <p>
 * order_goods 缺少type， 填充为 0，单件商品
 * <p>
 * 退单显示可退金额为0
 * <p>
 * 尝试先把分摊流水这些修复掉
 * <p>
 * 修复流水的时候发现，缺少orderGoodsSn
 * <p>
 * 增加逻辑
 * <p>
 * goodsEntity.setOrderGoodsSn(IdGenUtils.genOmniSn(BizConstant.OrderAppKey.OMNI_SN_PRE_KEY, null));
 */
@Component
@Slf4j
public class OldOrder2NewOrderHandler {


    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OldOrderTransService oldOrderTransService;


    @XxlJob("oldOrder2NewOrderHandler")
    public void oldOrder2NewOrderHandler() {

        // 1. 查询所有的老订单
        List<HeOrderEntity> orderEntities = orderRepository.listOldOrders();
        log.info("orderEntities size:{}", orderEntities.size());
        if (orderEntities.isEmpty()) {
            log.info("没有需要转换的老订单");
            return;
        }
        for (HeOrderEntity orderEntity : orderEntities) {
            try {
                oldOrderTransService.transOldOrder2NewOrder(orderEntity.getOrderId());
            } catch (Exception e) {
                log.error("订单转化失败", e);
            }
        }


    }


}

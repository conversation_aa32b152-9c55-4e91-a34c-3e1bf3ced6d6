package com.stbella.order.server.context.component.validator;

import com.stbella.order.domain.repository.IncomePaidAllocationRepository;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.time.ZoneOffset;

@Slf4j
@Component
@RefreshScope
@SnowballComponent(name = "订单退款校验")
public class OrderRefundInfoValidator implements IExecutableAtom<FlowContext> {

    private static final ZoneOffset ZONE_OFFSET = ZoneOffset.ofHours(8);

    @Resource
    private IncomeRecordRepository incomeRecordRepository;

    @Resource
    private IncomePaidAllocationRepository incomePaidAllocationRepository;

    @Override
    public void run(FlowContext bizContext) {
        /*SubmitRefundApplyV3Request request = bizContext.getAttribute(SubmitRefundApplyV3Request.class);
        List<HeIncomeRecordEntity> incomeRecordEntityList = incomeRecordRepository.getAllRecordListByOrderId(request.getOrderId());

        // 退款方式为原路退回
        if (RefundTypeEnum.BACK_TRACK.getCode().equals(request.getRefundModel())) {
            incomeRecordEntityList = incomeRecordEntityList.stream().filter(income -> {
                // 过滤掉微信或支付宝支付已经过期的
                if (OmniPayTypeEnum.WECHAT.getCode().equals(income.getPayType())) {
                    return income.getPayTime() < LocalDateTime.now().minusMonths(12).plusDays(3).toEpochSecond(ZONE_OFFSET);
                }
                if (OmniPayTypeEnum.ALIPAY.getCode().equals(income.getPayType())) {
                    return income.getPayTime() < LocalDateTime.now().minusMonths(6).plusDays(3).toEpochSecond(ZONE_OFFSET);
                }
                return false;
            }).filter(income -> {
                // 过滤掉线下支付的
                return !OmniPayTypeEnum.OFFLINE.getCode().equals(income.getPayType());
            }).collect(Collectors.toList());

            Map<Integer, LocalDateTime> payTimeMap = incomeRecordEntityList.stream().collect(Collectors.toMap(HeIncomeRecordEntity::getId, income -> {
                if (OmniPayTypeEnum.WECHAT.getCode().equals(income.getPayType())) {
                    return LocalDateTime.ofInstant(Instant.ofEpochSecond(income.getPayTime()), ZONE_OFFSET).minusMonths(12);
                }
                if (OmniPayTypeEnum.ALIPAY.getCode().equals(income.getPayType())) {
                    return LocalDateTime.ofInstant(Instant.ofEpochSecond(income.getPayTime()), ZONE_OFFSET).minusMonths(6);
                }
                return LocalDateTime.ofInstant(Instant.ofEpochSecond(income.getPayTime()), ZONE_OFFSET);
            }));

            Map<String, BigDecimal> refundMap = request.getRefundGoodsList().stream().collect(Collectors
                    .toMap(SubmitRefundGoodsApplyV3Request::getOrderGoodsSn, SubmitRefundGoodsApplyV3Request::getRefundAmount));

            List<Integer> incomeIdList = incomeRecordEntityList.stream().map(HeIncomeRecordEntity::getId).collect(Collectors.toList());

            List<IncomePaidAllocationEntity> paidList = incomePaidAllocationRepository.queryListByIncomeIds(incomeIdList).stream()
                    .sorted((p1, p2) -> Math.toIntExact(payTimeMap.get(p1.getIncomeId()).toEpochSecond(ZONE_OFFSET) - payTimeMap.get(p2.getIncomeId()).toEpochSecond(ZONE_OFFSET)))
                    .filter(paid -> {
                        BigDecimal reqRefundAmount = refundMap.get(paid.getOrderGoodsSn());
                        // 申请退款金额已经分摊完毕
                        if (reqRefundAmount.compareTo(BigDecimal.ZERO) <= 0) {
                            return false;
                        }

                        BigDecimal paidAmount = paid.getPaidAmount();
//                        BigDecimal freezeAmount = paid.getFreezeAmount();
//                        BigDecimal alreadyRefundAmount = paid.getAlreadyRefundAmount();
                        // 校验是否还有可退金额
                        BigDecimal ableRefundAmount = paidAmount.subtract(freezeAmount).subtract(alreadyRefundAmount);
                        if (ableRefundAmount.compareTo(BigDecimal.ZERO) > 0) {
                            return false;
                        }
                        BigDecimal actualRefundAmount = ableRefundAmount.compareTo(reqRefundAmount) >= 0 ? reqRefundAmount : ableRefundAmount;
                        paid.setFreezeAmount(freezeAmount.add(actualRefundAmount));
                        refundMap.put(paid.getOrderGoodsSn(), reqRefundAmount.subtract(actualRefundAmount));
                        return true;
                    }).collect(Collectors.toList());

            incomePaidAllocationRepository.saveOrUpdateBatch(paidList);
        }*/

    }

}

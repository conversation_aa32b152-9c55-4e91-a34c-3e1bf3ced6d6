package com.stbella.order.server.manager;

import com.alibaba.fastjson.JSONObject;
import com.stbella.core.result.Result;
import com.stbella.financial.api.MainSupplierFacade;
import com.stbella.financial.api.SupplierService;
import com.stbella.financial.res.MainSupplierQueryVO;
import com.stbella.financial.res.SupplierPageVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class FinanceManager {

    @DubboReference
    private MainSupplierFacade mainSupplierFacade;


    @DubboReference
    private SupplierService supplierService;

    /**
     * 查询主供应商id列表
     *
     * @param supplierIds
     */
    public List<MainSupplierQueryVO> queryMainSupplier(List<Long> supplierIds) {
        log.info("查询主供应商数据[{}]", JSONObject.toJSONString(supplierIds));
        Result<List<MainSupplierQueryVO>> result = mainSupplierFacade.getMainSupplierFromIds(supplierIds);
        log.info("查询主供应商数据结果[{}]", JSONObject.toJSONString(result));
        return result.getData();
    }

    public List<SupplierPageVO> queryHLYSupplier(List<Long> supplierIds) {
        log.info("查询汇联易供应商数据[{}]", JSONObject.toJSONString(supplierIds));
        Result<List<SupplierPageVO>> listResult = supplierService.querySupplierByIds(supplierIds);
        log.info("查询汇联易供应商数据结果[{}]", JSONObject.toJSONString(listResult));
        return listResult.getData();
    }
}

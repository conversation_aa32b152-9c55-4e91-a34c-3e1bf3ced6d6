package com.stbella.order.server.context.component.spi.impl.pay;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.stbella.asset.api.enums.AssetType;
import com.stbella.asset.api.enums.TradeType;
import com.stbella.asset.api.req.trade.UserTradeReq;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.month.PayChannelTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.infrastructure.gateway.asset.AssetRemoteService;
import com.stbella.order.server.context.component.spi.PaySpi;
import com.stbella.order.server.listener.event.BalancePaySuccessEvent;
import com.stbella.order.server.order.month.enums.PayStatusEnum;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.annotation.FuncPointSpiImpl;
import top.primecare.snowball.extpoint.definition.FuncSpiConfig;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-05-30  14:01
 * @Description: 余额支付扩展点
 * 使用资产中心能力
 */
@Component
@FuncPointSpiImpl(name = "余额支付扩展点")
public class BalancePaySpiImpl implements PaySpi {

    @Resource
    AssetRemoteService assetRemoteService;
    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private OrderRepository orderRepository;

    @Override
    public boolean condition(HeIncomeRecordEntity param) {
        if (CompareUtil.integerEqual(param.getPayType() , OmniPayTypeEnum.BALANCE.getCode())){
            return true;
        }
        return false;
    }

    @Override
    public JSONObject invoke(HeIncomeRecordEntity param) {

        HeOrderEntity heOrderEntity = orderRepository.queryOrderById(param.getOrderId());
        BigDecimal fxRate = Optional.ofNullable(heOrderEntity.getFxRate()).orElse(BigDecimal.ONE);
        UserTradeReq tradeReq = new UserTradeReq();
        tradeReq.setUniqueId(param.getIncomeSn());
        tradeReq.setOperator(param.getOperator());
        tradeReq.setAssetType(AssetType.AVAILABLE_BALANCE.getCode());
        tradeReq.setTradeType(TradeType.BALANCE_PAY.getCode());
        tradeReq.setTitle(OmniOrderTypeEnum.getOrderName(param.getOrderType())+ "-意向金付款");
        tradeReq.setRemark("支付订单"+param.getOrderSn());
        tradeReq.setUserId(param.getBasicUid()+"");
        Long amount =fxRate.multiply(new BigDecimal(param.getIncome())).divide(BigDecimal.ONE, 0, RoundingMode.HALF_UP).longValue();
        tradeReq.setAmount(amount);
        tradeReq.setOrderNo(param.getOrderSn());
        tradeReq.setSourceBizId(param.getIncomeSn());
        List<Long> result = assetRemoteService.execTrade(tradeReq);

        JSONObject payResult = new JSONObject();
        payResult.putOnce("result", payResult);

        String transactionalNo = result.get(0) + "";
        BalancePaySuccessEvent paySuccessEvent = BalancePaySuccessEvent.builder()
                .orderNo(param.getOrderSn())
                .payAmount(AmountChangeUtil.changeF2Y(param.getIncome().longValue()))
                .payChannel(PayChannelTypeEnum.NATIVE.getCode())
                .payType(param.getPayType())
                .payTime(new Date())
                .transactionalNo(transactionalNo)
                .payStatus(PayStatusEnum.PAY_STATUS_SUCCESS.getCode())
                .requestBody(JSONUtil.toJsonStr(tradeReq))
                .outTradeNo(param.getIncomeSn()).build();

        applicationContext.publishEvent(paySuccessEvent);

        return new JSONObject(result);
    }

    @Override
    public FuncSpiConfig config() {
        //默认优先级高
        return new FuncSpiConfig(true, Integer.MIN_VALUE);
    }
}

package com.stbella.order.server.context.component.validator;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeUserProductionAmountPayLogEntity;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.domain.repository.ProductionAmountPayRepository;
import com.stbella.order.server.order.month.req.SubmitRefundApplyV2Request;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
@RefreshScope
@SnowballComponent(name = "老订单订单发起退款校验")
public class OldOrderRefundValidator implements IExecutableAtom<FlowContext> {

    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @Resource
    private ProductionAmountPayRepository productionAmountPayRepository;

    @Override
    public void run(FlowContext bizContext) {
        SubmitRefundApplyV2Request request = bizContext.getAttribute(SubmitRefundApplyV2Request.class);
        long start = System.currentTimeMillis();
        log.info("提交退款申请req:{}， start:{}", JSONUtil.toJsonStr(request), start);
        this.validateImageCount(request.getImages());
        Integer refundAmountFen = request.getRefundAmount().multiply(new BigDecimal(100)).intValue();

        HeIncomeRecordEntity record = null;
        if (Objects.isNull(request.getProductionPayId())) {
            checkRefund(request, refundAmountFen);
        } else {
            checkCkjRefund(request, refundAmountFen);
        }

    }

    private void validateImageCount(List<String> images) throws BusinessException {
        if (CollectionUtils.isNotEmpty(images) && images.size() > 6) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "上传的图片数量最多6张");
        }
    }

    private @NotNull HeIncomeRecordEntity checkRefund(SubmitRefundApplyV2Request request, Integer refundAmountFen) {
        HeIncomeRecordEntity record;
        if (request.getId() == null) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "支付流水ID不能为空");
        }
        record = incomeRecordRepository.getOneById(request.getId());
        Assert.isTrue(ObjectUtil.isNotNull(record), "资金记录不存在");
        //校验退款金额
        this.validateRefundAmount(record.getFreezeAmount(), record.getAlreadyRefundAmount(), record.getIncome(), refundAmountFen);
        return record;
    }

    private void validateRefundAmount(Integer freezeAmount, Integer alreadyRefundAmount, Integer payAmount, Integer refundAmountFen) throws BusinessException {
        //校验退款金额
        if (refundAmountFen + freezeAmount + alreadyRefundAmount > payAmount) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "申请退款金额过大");
        }
    }

    private @NotNull HeUserProductionAmountPayLogEntity checkCkjRefund(SubmitRefundApplyV2Request request, Integer refundAmountFen) {
        HeUserProductionAmountPayLogEntity productionPayEntity = productionAmountPayRepository.getOneById(request.getProductionPayId());
        Assert.isTrue(ObjectUtil.isNotNull(productionPayEntity), "产康金记录不存在");
        //校验退款金额
        this.validateRefundAmount(productionPayEntity.getFreezeAmount(), productionPayEntity.getRefundAmount(), productionPayEntity.getPayAmount(), refundAmountFen);
        return productionPayEntity;
    }

    @Override
    public boolean condition(FlowContext context) {
        SubmitRefundApplyV2Request createRefundReq = context.getAttribute(SubmitRefundApplyV2Request.class);
        HeOrderEntity orderEntity = context.getAttribute(HeOrderEntity.class);
        return ObjectUtil.isNotEmpty(createRefundReq) && ObjectUtil.isNotEmpty(orderEntity) && !orderEntity.isNewOrder();
    }


}

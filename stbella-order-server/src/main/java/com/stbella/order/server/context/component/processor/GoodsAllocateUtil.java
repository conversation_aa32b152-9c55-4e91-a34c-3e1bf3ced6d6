package com.stbella.order.server.context.component.processor;

import com.alibaba.fastjson.JSONObject;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品价格分摊处理器
 * <p>
 * 主要功能：
 * 1. 按比例分摊订单未支付金额到各个商品
 * 2. 处理组合商品的子商品分摊
 * 3. 处理分摊过程中的尾差问题
 * <p>
 * 优化特性：
 * 1. 参数校验和异常处理
 * 2. 精确的尾差处理算法
 * 3. 清晰的方法职责分离
 * 4. 详细的日志记录
 */
@Slf4j
public final class GoodsAllocateUtil {

    // 精度常量
    private static final int CALCULATION_SCALE = 10;
    private static final RoundingMode ROUNDING_MODE = RoundingMode.HALF_EVEN;

    // 私有构造函数，防止实例化
    private GoodsAllocateUtil() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * 处理商品价格分摊
     *
     * @param orderEntity    订单实体
     * @param orderGoodsList 订单商品列表
     * @return 处理后的商品列表
     * @throws IllegalArgumentException 参数校验失败时抛出
     */
    public static List<HeOrderGoodsEntity> processGoodsAllocation(HeOrderEntity orderEntity, List<HeOrderGoodsEntity> orderGoodsList) {
        log.info("分摊的商品信息为{}", JSONObject.toJSONString(orderGoodsList));
        // 参数校验
        validateInputParameters(orderEntity, orderGoodsList);

        // 计算订单应付金额
        int unPayAmount = orderEntity.getPayAmount();
        log.info("开始处理订单[{}]商品分摊，应付金额: {}, 商品数量: {}", orderEntity.getOrderId(), unPayAmount, orderGoodsList.size());

        // 如果应付金额为0，直接返回
        if (unPayAmount <= 0) {
            log.info("订单[{}]应付金额为0，无需分摊", orderEntity.getOrderId());
            return orderGoodsList;
        }

        try {
            // 1. 第一步：将平铺的数据分类组装好
            List<HeOrderGoodsEntity> structuredGoodsList = assembleGoodsStructure(orderGoodsList);

            // 2. 主商品分摊（组合商品和additional商品）
            allocateMainGoods(structuredGoodsList, unPayAmount);

            // 3. 组合商品子商品分摊
            allocateCombineSubGoods(structuredGoodsList);

            // 4. 最后：重新平铺所有商品到同一层级
            List<HeOrderGoodsEntity> flattenedGoodsList = flattenGoodsStructure(structuredGoodsList);

            log.info("订单[{}]商品分摊处理完成", orderEntity.getOrderId());
            return flattenedGoodsList;

        } catch (Exception e) {
            log.error("订单[{}]商品分摊处理失败", orderEntity.getOrderId(), e);
            throw new RuntimeException("商品分摊处理失败: " + e.getMessage(), e);
        }
    }


    /**
     * 第一步：将平铺的数据分类组装好
     * - 组合商品保持在主商品层级
     * - 子商品放到对应组合商品的subList中
     * - additional商品直接放到主商品层级（不放入subList）
     *
     * @param flatGoodsList 平铺的商品列表
     * @return 组装好的商品结构
     */
    private static List<HeOrderGoodsEntity> assembleGoodsStructure(List<HeOrderGoodsEntity> flatGoodsList) {
        log.info("开始组装商品结构，平铺商品数量: {}", flatGoodsList.size());

        // 创建商品映射
        Map<String, HeOrderGoodsEntity> goodsSnMap = flatGoodsList.stream().collect(Collectors.toMap(HeOrderGoodsEntity::getOrderGoodsSn, goods -> goods));

        // 分类商品
        List<HeOrderGoodsEntity> combineGoodsList = new ArrayList<>(); // 组合商品
        List<HeOrderGoodsEntity> subGoodsList = new ArrayList<>();     // 子商品
        List<HeOrderGoodsEntity> additionalGoodsList = new ArrayList<>(); // additional商品
        List<HeOrderGoodsEntity> normalGoodsList = new ArrayList<>();  // 普通商品

        for (HeOrderGoodsEntity goods : flatGoodsList) {
            if (goods.isCombine()) {
                combineGoodsList.add(goods);
                // 清空原有的subList，重新组装
                goods.setSubList(new ArrayList<>());
            } else if (goods.isAddition()) {
                additionalGoodsList.add(goods);
            } else if (goods.getParentCombineSn() != null && !goods.getParentCombineSn().isEmpty()) {
                subGoodsList.add(goods);
            } else {
                normalGoodsList.add(goods);
            }
        }

        log.info("商品分类完成 - 组合商品: {}, 子商品: {}, additional商品: {}, 普通商品: {}", combineGoodsList.size(), subGoodsList.size(), additionalGoodsList.size(), normalGoodsList.size());

        // 将子商品放到对应组合商品的subList中
        for (HeOrderGoodsEntity subGoods : subGoodsList) {
            String parentSn = subGoods.getParentCombineSn();
            HeOrderGoodsEntity parentGoods = goodsSnMap.get(parentSn);

            if (parentGoods != null && parentGoods.isCombine()) {
                parentGoods.getSubList().add(subGoods);
                log.debug("将子商品[{}]添加到组合商品[{}]的subList中", subGoods.getOrderGoodsSn(), parentGoods.getOrderGoodsSn());
            } else {
                log.warn("子商品[{}]的父商品[{}]不存在或不是组合商品", subGoods.getOrderGoodsSn(), parentSn);
            }
        }

        // 组装最终的主商品列表：组合商品 + additional商品 + 普通商品
        List<HeOrderGoodsEntity> structuredGoodsList = new ArrayList<>();
        structuredGoodsList.addAll(combineGoodsList);
        structuredGoodsList.addAll(additionalGoodsList);
        structuredGoodsList.addAll(normalGoodsList);

        // 打印组装结果
        for (HeOrderGoodsEntity goods : combineGoodsList) {
            int subCount = goods.getSubList() != null ? goods.getSubList().size() : 0;
            log.info("组合商品[{}]包含{}个子商品", goods.getOrderGoodsSn(), subCount);
        }

        log.info("商品结构组装完成，主商品数量: {} (组合商品: {}, additional商品: {}, 普通商品: {})", structuredGoodsList.size(), combineGoodsList.size(), additionalGoodsList.size(), normalGoodsList.size());

        return structuredGoodsList;
    }

    /**
     * 最后：重新平铺所有商品到同一层级
     *
     * @param structuredGoodsList 组装好的商品结构
     * @return 平铺的商品列表
     */
    private static List<HeOrderGoodsEntity> flattenGoodsStructure(List<HeOrderGoodsEntity> structuredGoodsList) {
        log.info("开始平铺商品结构，结构化商品数量: {}", structuredGoodsList.size());

        List<HeOrderGoodsEntity> flattenedGoodsList = new ArrayList<>();

        for (HeOrderGoodsEntity goods : structuredGoodsList) {
            // 添加主商品（组合商品、additional商品、普通商品）
            flattenedGoodsList.add(goods);

            // 如果是组合商品，添加其子商品
            if (goods.isCombine() && !CollectionUtils.isEmpty(goods.getSubList())) {
                for (HeOrderGoodsEntity subGoods : goods.getSubList()) {
                    flattenedGoodsList.add(subGoods);
                    log.debug("将子商品[{}]从组合商品[{}]的subList中平铺出来", subGoods.getOrderGoodsSn(), goods.getOrderGoodsSn());
                }
            }
        }

        log.info("商品结构平铺完成，平铺商品数量: {}", flattenedGoodsList.size());
        return flattenedGoodsList;
    }


    /**
     * 参数校验
     */
    private static void validateInputParameters(HeOrderEntity orderEntity, List<HeOrderGoodsEntity> orderGoodsList) {
        if (orderEntity == null) {
            throw new IllegalArgumentException("订单实体不能为空");
        }

        if (CollectionUtils.isEmpty(orderGoodsList)) {
            throw new IllegalArgumentException("订单商品列表不能为空");
        }

        // 校验商品数据完整性
        for (HeOrderGoodsEntity goods : orderGoodsList) {
            if (goods.getGoodsNum() == null || goods.getGoodsNum() <= 0) {
                throw new IllegalArgumentException("商品数量必须大于0");
            }
            if (goods.getGoodsPriceOrgin() == null || goods.getGoodsPriceOrgin() < 0) {
                throw new IllegalArgumentException("商品原价不能为负数");
            }
        }
    }

    /**
     * 主商品分摊处理
     */
    private static void allocateMainGoods(List<HeOrderGoodsEntity> orderGoodsList, int unPayAmount) {
        // 计算商品原价总额
        int totalOriginalAmount = orderGoodsList.stream().mapToInt(o -> o.getGoodsNum() * o.getGoodsPriceOrgin()).sum();

        if (totalOriginalAmount <= 0) {
            log.warn("商品原价总额为0，无法进行分摊");
            return;
        }

        // 按比例分摊并处理尾差
        allocateAmountWithTailHandling(orderGoodsList, totalOriginalAmount, unPayAmount);
    }

    /**
     * 计算商品原价总额
     */
    private static Map<Long, Integer> calculateGoodsOriginalAmount(List<HeOrderGoodsEntity> orderGoodsList) {
        Map<Long, Integer> amountMap = new HashMap<>(orderGoodsList.size());

        for (HeOrderGoodsEntity goods : orderGoodsList) {
            Integer id = goods.getId();
            if (Objects.isNull(id)) {
                id = 0;
            }
            Long goodsId = id.longValue();
            Integer originalAmount = goods.getGoodsPriceOrgin() * goods.getGoodsNum();

            if (amountMap.containsKey(goodsId)) {
                throw new IllegalStateException("发现重复的商品ID: " + goodsId);
            }

            amountMap.put(goodsId, originalAmount);
        }

        return amountMap;
    }

    /**
     * 按比例分摊金额并处理尾差
     */
    private static void allocateAmountWithTailHandling(List<HeOrderGoodsEntity> orderGoodsList, int totalAmount, int allocateAmount) {
        if (orderGoodsList.isEmpty() || totalAmount <= 0) {
            return;
        }

        // 分离出有原价的商品和0原价商品
        List<HeOrderGoodsEntity> validGoodsList = new ArrayList<>();
        List<HeOrderGoodsEntity> zeroGoodsList = new ArrayList<>();
        
        for (HeOrderGoodsEntity goods : orderGoodsList) {
            int goodsAmount = goods.getGoodsNum() * goods.getGoodsPriceOrgin();
            if (goodsAmount > 0) {
                validGoodsList.add(goods);
            } else {
                zeroGoodsList.add(goods);
            }
        }

        // 0原价商品直接设置分摊金额为0
        for (HeOrderGoodsEntity zeroGoods : zeroGoodsList) {
            updateGoodsAllocation(zeroGoods, 0);
            log.info("商品[{}]原价为0，分摊金额设置为0", zeroGoods.getId());
        }

        // 如果没有有效商品参与分摊，直接返回
        if (validGoodsList.isEmpty()) {
            log.warn("所有商品原价均为0，无法进行分摊");
            return;
        }

        // 按原价降序排序，确保尾差分配给金额最大的商品
        validGoodsList.sort((a, b) -> Integer.compare(b.getGoodsNum() * b.getGoodsPriceOrgin(), a.getGoodsNum() * a.getGoodsPriceOrgin()));

        int totalAllocated = 0;
        List<AllocationResult> allocationResults = new ArrayList<>();

        // 计算除最后一个商品外的所有商品分摊
        for (int i = 0; i < validGoodsList.size() - 1; i++) {
            HeOrderGoodsEntity goods = validGoodsList.get(i);
            int goodsAmount = goods.getGoodsNum() * goods.getGoodsPriceOrgin();

            // 计算分摊比例和金额
            BigDecimal ratio = BigDecimal.valueOf(goodsAmount).divide(BigDecimal.valueOf(totalAmount), CALCULATION_SCALE, ROUNDING_MODE);
            int allocation = ratio.multiply(BigDecimal.valueOf(allocateAmount)).setScale(0, ROUNDING_MODE).intValue();

            allocationResults.add(new AllocationResult(goods, allocation, ratio));
            totalAllocated += allocation;
        }

        // 最后一个商品承担尾差
        HeOrderGoodsEntity lastGoods = validGoodsList.get(validGoodsList.size() - 1);
        int lastAllocation = allocateAmount - totalAllocated;
        int lastGoodsAmount = lastGoods.getGoodsNum() * lastGoods.getGoodsPriceOrgin();
        BigDecimal lastRatio = BigDecimal.valueOf(lastGoodsAmount).divide(BigDecimal.valueOf(totalAmount), CALCULATION_SCALE, ROUNDING_MODE);

        allocationResults.add(new AllocationResult(lastGoods, lastAllocation, lastRatio));

        // 应用分摊结果
        for (AllocationResult result : allocationResults) {
            updateGoodsAllocation(result.goods, result.allocation);
            log.info("商品[{}]分摊: 原价={}, 比例={}, 分摊金额={}", result.goods.getId(), result.goods.getGoodsNum() * result.goods.getGoodsPriceOrgin(), result.ratio.setScale(4, ROUNDING_MODE), result.allocation);
        }

        log.info("主商品分摊完成，总分摊金额: {}, 目标分摊金额: {}, 0原价商品数量: {}", totalAllocated + lastAllocation, allocateAmount, zeroGoodsList.size());
    }

    /**
     * 分摊结果内部类
     */
    private static class AllocationResult {
        final HeOrderGoodsEntity goods;
        final int allocation;
        final BigDecimal ratio;

        AllocationResult(HeOrderGoodsEntity goods, int allocation, BigDecimal ratio) {
            this.goods = goods;
            this.allocation = allocation;
            this.ratio = ratio;
        }
    }

    /**
     * 组合商品子商品分摊处理
     */
    private static void allocateCombineSubGoods(List<HeOrderGoodsEntity> orderGoodsList) {
        List<HeOrderGoodsEntity> combineGoods = orderGoodsList.stream().filter(HeOrderGoodsEntity::isCombine).collect(Collectors.toList());

        for (HeOrderGoodsEntity goods : combineGoods) {
            allocateSubGoodsProportionally(goods);
        }
    }


    /**
     * 按比例分摊子商品
     */
    private static void allocateSubGoodsProportionally(HeOrderGoodsEntity combineGoods) {
        List<HeOrderGoodsEntity> allSubGoods = combineGoods.getSubList();
        if (allSubGoods == null || allSubGoods.isEmpty()) {
            return;
        }

        // 分离出有原价的子商品和0原价子商品
        List<HeOrderGoodsEntity> validSubGoods = new ArrayList<>();
        List<HeOrderGoodsEntity> zeroSubGoods = new ArrayList<>();
        
        for (HeOrderGoodsEntity subGoods : allSubGoods) {
            int subGoodsAmount = subGoods.getGoodsNum() * subGoods.getGoodsPriceOrgin();
            if (subGoodsAmount > 0) {
                validSubGoods.add(subGoods);
            } else {
                zeroSubGoods.add(subGoods);
            }
        }

        // 0原价子商品直接设置分摊金额为0
        for (HeOrderGoodsEntity zeroSubGood : zeroSubGoods) {
            updateGoodsAllocation(zeroSubGood, 0);
            log.info("组合商品[{}]子商品[{}]原价为0，分摊金额设置为0", combineGoods.getId(), zeroSubGood.getId());
        }

        Integer totalAllocationOriginPrice = combineGoods.getTotalAllocationOriginPrice();

        // 如果没有有效子商品参与分摊，直接返回
        if (validSubGoods.isEmpty()) {
            log.warn("组合商品[{}]所有子商品原价均为0，无法进行分摊", combineGoods.getId());
            return;
        }

        // 按原价降序排序，确保尾差分配给金额最大的子商品
        validSubGoods.sort((a, b) -> Integer.compare(b.getGoodsNum() * b.getGoodsPriceOrgin(), a.getGoodsNum() * a.getGoodsPriceOrgin()));

        int totalOriginPrice = validSubGoods.stream().mapToInt(o -> o.getGoodsNum() * o.getGoodsPriceOrgin()).sum();
        int totalAllocated = 0;

        // 处理除最后一个子商品外的所有子商品
        for (int i = 0; i < validSubGoods.size() - 1; i++) {
            HeOrderGoodsEntity currentSubGoods = validSubGoods.get(i);
            int subOriginalAllocation = currentSubGoods.getGoodsNum() * currentSubGoods.getGoodsPriceOrgin();

            // 计算分摊比例和金额
            BigDecimal ratio = BigDecimal.valueOf(subOriginalAllocation).divide(BigDecimal.valueOf(totalOriginPrice), CALCULATION_SCALE, ROUNDING_MODE);
            int subAllocation = ratio.multiply(BigDecimal.valueOf(totalAllocationOriginPrice)).setScale(0, ROUNDING_MODE).intValue();

            updateGoodsAllocation(currentSubGoods, subAllocation);
            totalAllocated += subAllocation;

            log.debug("组合商品[{}]子商品[{}]分摊: 原分摊={}, 比例={}, 新分摊={}", combineGoods.getId(), currentSubGoods.getId(), subOriginalAllocation, ratio.setScale(4, ROUNDING_MODE), subAllocation);
        }

        // 最后一个有效子商品承担尾差
        HeOrderGoodsEntity lastValidSubGoods = validSubGoods.get(validSubGoods.size() - 1);
        int lastAllocation = totalAllocationOriginPrice - totalAllocated;
        updateGoodsAllocation(lastValidSubGoods, lastAllocation);

        log.info("组合商品[{}]子商品分摊完成: 总分摊={}, 有效子商品数量={}, 0原价子商品数量={}", 
                combineGoods.getId(), totalAllocationOriginPrice, validSubGoods.size(), zeroSubGoods.size());
    }

    /**
     * 更新商品分摊信息
     */
    private static void updateGoodsAllocation(HeOrderGoodsEntity goods, Integer allocation) {
        if (allocation < 0) {
            log.warn("商品[{}]分摊金额为负数: {}, 设置为0", goods.getId(), allocation);
            allocation = 0;
        }

        Integer goodsNum = goods.getGoodsNum();
        if (goodsNum == null || goodsNum <= 0) {
            log.error("商品[{}]数量无效: {}", goods.getId(), goodsNum);
            return;
        }

        // 计算单价（向下取整，避免单价*数量超过总额）
        Integer unitPrice = allocation / goodsNum;

        goods.setTotalAllocationOriginPrice(allocation);
        goods.setAllocationOriginPrice(unitPrice);

        log.debug("商品[{}]分摊更新: 总分摊={}, 单价={}, 数量={}", goods.getId(), allocation, unitPrice, goodsNum);
    }
}
package com.stbella.order.server.context.component.contract.checker;

import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class DefaultContractCreateValidator extends AbstractContractCreateValidator {

    @Override
    public boolean doValidate(ContractValidationContext context) {
        return true;
    }

    @Override
    public TemplateContractTypeEnum templateContractType() {
        return TemplateContractTypeEnum.OHTER;
    }
}

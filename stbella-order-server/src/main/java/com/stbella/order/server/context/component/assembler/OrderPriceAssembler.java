package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.server.order.OrderPrice;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import com.stbella.platform.order.api.res.SkuAdditionalInfo;
import com.stbella.store.server.goods.enums.CombinationTypeEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 订单总价，成本计算组装器
 * <p>
 * <p>
 * - 活动礼赠商品关联的节日费用商品和多胎费用商品不计入签单折扣率、但计入订单折扣率和订单毛利率
 * - 定制礼赠商品关联的节日费用商品和多胎费用商品不计入签单折扣率、但计入订单折扣率和订单毛利率，需额外支付礼赠金
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@SnowballComponent(name = "费用计算组装", desc = "指标计算组装")
public class OrderPriceAssembler implements IExecutableAtom<FlowContext> {

    /**
     * 计算价格，成本
     *
     * @return
     */
    @Override
    public void run(FlowContext bizContext) {

        List<SkuDetailInfo> skuList = bizContext.getListAttribute(SkuDetailInfo.class);


        //选购商品
        List<SkuDetailInfo> selectedSkuList = skuList.stream().filter(o -> !o.getGift() && Objects.isNull(o.getPromotionInfo())).collect(Collectors.toList());
        SkuCostAndPrice selectedCostAndPrice = getSkuCostAndPrice(selectedSkuList);

        //活动商品
        List<SkuDetailInfo> promotionSkuList = skuList.stream().filter(o -> Objects.nonNull(o.getPromotionInfo())).collect(Collectors.toList());
        SkuCostAndPrice promotionCostAndPrice = getSkuCostAndPrice(promotionSkuList);

        //定制礼赠商品
        List<SkuDetailInfo> giftSkuList = skuList.stream().filter(o -> Objects.nonNull(o.getGift()) && o.getGift() && Objects.isNull(o.getPromotionInfo())).collect(Collectors.toList());
        SkuCostAndPrice giftCostAndPrice = getSkuCostAndPrice(giftSkuList);

        BigDecimal totalPrice = selectedCostAndPrice.getPrice().add(promotionCostAndPrice.getPrice()).add(giftCostAndPrice.getPrice());

        BigDecimal totalCost = selectedCostAndPrice.getCost().add(promotionCostAndPrice.getCost()).add(giftCostAndPrice.getCost());

        //商品附加项总价
        BigDecimal additionalTotalPrice = selectedCostAndPrice.getAdditionalPrice().add(promotionCostAndPrice.getAdditionalPrice()).add(giftCostAndPrice.getAdditionalPrice());

        BigDecimal additionalTotalCost = selectedCostAndPrice.getAdditionalCost().add(promotionCostAndPrice.getAdditionalCost()).add(giftCostAndPrice.getAdditionalCost());

        BigDecimal selectedTotalPrice = selectedCostAndPrice.getPrice().add(selectedCostAndPrice.getAdditionalPrice());

        BigDecimal giftPrice = giftCostAndPrice.getPrice().add(giftCostAndPrice.getAdditionalPrice());

        OrderPrice price = OrderPrice.builder()
                .goodsAmount(totalPrice)
                .giftAmount(giftPrice)
                .selectedAmount(selectedTotalPrice)
                .totalAmount(totalPrice.add(additionalTotalPrice))
                .payAmount(totalPrice.add(additionalTotalPrice))
                .totalCost(totalCost.add(additionalTotalCost)).build();
        log.info("价格计算详情为 {}", JSONObject.toJSONString(price));
        bizContext.setAttribute(OrderPrice.class, price);


    }

    private static @NotNull BigDecimal additionalTotalPrice(List<SkuAdditionalInfo> additionalList) {
        return additionalList.stream()
                .map(addInfo -> NumberUtil.mul(addInfo.getPrice(), addInfo.getNum()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private static @NotNull BigDecimal additionalTotalCost(List<SkuAdditionalInfo> additionalList) {
        return additionalList.stream()
                .map(addInfo -> NumberUtil.mul(addInfo.getCost(), addInfo.getNum()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取商品的附加项
     *
     * @param skuList
     * @return
     */
    private static @NotNull List<SkuAdditionalInfo> getSkuAdditionalInfos(List<SkuDetailInfo> skuList) {
        // 商品附加项
        List<SkuAdditionalInfo> additionalList = skuList.stream().flatMap(sku -> {
            if (sku.getType().intValue() == CombineTypeEnum.SIMPLE.code()) {
                return CollectionUtil.isEmpty(sku.getAdditionalList()) ? new ArrayList<SkuAdditionalInfo>().stream() : sku.getAdditionalList().stream();
            } else {
                return sku.getSubList().stream().flatMap(subSku -> CollectionUtil.isEmpty(subSku.getAdditionalList()) ? new ArrayList<SkuAdditionalInfo>().stream() : subSku.getAdditionalList().stream());
            }
        }).collect(Collectors.toList());
        return additionalList;
    }


    private SkuCostAndPrice getSkuCostAndPrice(List<SkuDetailInfo> skuDetailInfoList) {
        List<SkuAdditionalInfo> additionalList = getSkuAdditionalInfos(skuDetailInfoList);
        //商品附加项成本
        BigDecimal additionalTotalCost = additionalTotalCost(additionalList);
        //商品附加项价格
        BigDecimal additionalTotalPrice = additionalTotalPrice(additionalList);
        log.info("附加项成本项：{},  总价（元)：{}, 成本（元）:{}", additionalList.size(), additionalTotalPrice, additionalTotalCost);
        SkuCostAndPrice skuCostAndPrice = new SkuCostAndPrice();
        skuCostAndPrice.setAdditionalCost(additionalTotalCost);
        skuCostAndPrice.setAdditionalPrice(additionalTotalPrice);
        //先把固定搭配的组合商品拉出来
        List<SkuDetailInfo> fixedSkuList = skuDetailInfoList.stream()
                .filter(sku -> CombinationTypeEnum.FIXED_COLLOCATION.getCode().equals(sku.getCombinationPricingMethod()))
                .collect(Collectors.toList());
        //初始化加价，后期会用上
        fixedSkuList.forEach(sku -> {
            if (Objects.isNull(sku.getExtraPrice())) {
                sku.setExtraPrice(BigDecimal.ZERO);
            }
            if (Objects.isNull(sku.getSkuNum())) {
                sku.setSkuNum(1);
            }
        });

        // 要计算价格的商品（主要处理 组合中有些是固定价格，有些是每个子商品价格之和）
        List<SkuDetailInfo> pricingSkuList = skuDetailInfoList.stream().filter(sku -> !CombinationTypeEnum.FIXED_COLLOCATION.getCode().equals(sku.getCombinationPricingMethod())).flatMap(sku -> {
            if (sku.getType().intValue() == CombineTypeEnum.SIMPLE.code()) {
                return Stream.of(sku);
            } else {
                List<SkuDetailInfo> subList = sku.getSubList();
                return subList.stream();
            }
        }).collect(Collectors.toList());

        //初始化加价，后期会用上
        pricingSkuList.forEach(sku -> {
            if (Objects.isNull(sku.getExtraPrice())) {
                sku.setExtraPrice(BigDecimal.ZERO);
            }
            if (Objects.isNull(sku.getSkuNum())) {
                sku.setSkuNum(1);
            }
        });

        List<SkuDetailInfo> totalSkuList = new ArrayList<>();
        totalSkuList.addAll(pricingSkuList);
        totalSkuList.addAll(fixedSkuList);


        //正常商品成本 注： num*SkuNum = 总的商品数
        BigDecimal totalCost = totalSkuList.stream()
                .map(skuDetailInfo -> NumberUtil.mul(skuDetailInfo.getCostPrice(), skuDetailInfo.getNum() * skuDetailInfo.getSkuNum() * skuDetailInfo.getParentNum()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        //正常商品原价 注：price 是一份的价格（一份可能有多个商品包），所以不用再乘以skuNum了
        BigDecimal totalPrice = totalSkuList.stream()
                .map(skuDetailInfo -> NumberUtil.mul(skuDetailInfo.getPrice().add(skuDetailInfo.getExtraPrice()), skuDetailInfo.getNum() * skuDetailInfo.getParentNum()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        skuCostAndPrice.setCost(totalCost);
        skuCostAndPrice.setPrice(totalPrice);

        log.info("普通商品数量：{},  总价（元)：{}, 成本（元）:{}", pricingSkuList.size(), totalPrice, totalCost);
        return skuCostAndPrice;

    }

    @Data
    class SkuCostAndPrice {

        private BigDecimal additionalCost;

        private BigDecimal additionalPrice;

        private BigDecimal cost;

        private BigDecimal price;
    }

}

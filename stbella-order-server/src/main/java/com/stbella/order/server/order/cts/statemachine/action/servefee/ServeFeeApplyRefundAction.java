package com.stbella.order.server.order.cts.statemachine.action.servefee;

import com.stbella.order.server.order.cts.enums.OrderStatusStateMachineStatusEnum;
import com.stbella.order.server.order.cts.request.order.OrderApplyRefundRequest;
import com.stbella.order.server.order.cts.statemachine.OrderContext;
import com.stbella.order.server.order.cts.statemachine.OrderStateEvent;
import com.stbella.order.server.order.cts.statemachine.action.common.CommonAction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.action.Action;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 管理费订单申请退款
 *
 * <AUTHOR>
 * @date 2022-03-10 11:41
 * @sine 1.0.0
 */
@Slf4j
@Component
public class ServeFeeApplyRefundAction implements Action<OrderStatusStateMachineStatusEnum, OrderStateEvent> {

    @Resource
    private CommonAction commonAction;

    @Override
    public void execute(StateContext<OrderStatusStateMachineStatusEnum, OrderStateEvent> stateContext) {
        log.info("执行育婴师-管理费订单申请退款" + stateContext.toString());
        OrderContext context = (OrderContext) stateContext.getMessageHeader(OrderContext.HEADER_KEY);
        final Long applyRefundId = commonAction.applyRefund((OrderApplyRefundRequest) context.getModel(), context.getOrderCtsPO());
        stateContext.getStateMachine().getExtendedState().getVariables().put("applyRefundId", applyRefundId);
    }
}

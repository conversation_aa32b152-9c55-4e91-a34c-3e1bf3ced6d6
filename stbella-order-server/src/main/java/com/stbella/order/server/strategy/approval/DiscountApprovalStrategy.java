package com.stbella.order.server.strategy.approval;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.stbella.core.base.Operator;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.month.OrderDecreaseStatusEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.repository.OrderRefundRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.context.service.cashier.CashierCommandServiceImpl;
import com.stbella.order.server.order.cts.enums.DecreaseTypeEnum;
import com.stbella.order.server.order.month.enums.ApprovalDiscountStatusEnum;
import com.stbella.order.server.order.month.req.PayReqV2;
import com.stbella.order.server.order.month.request.approval.ApprovalStatusInfo;
import com.stbella.platform.order.api.OrderAggregateRoot;
import com.stbella.platform.order.api.reduction.api.OrderReductionService;
import com.stbella.platform.order.api.reduction.req.OrderReductionApprovalDealReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.stream.Collectors;

/**
 * 折扣审批策略处理器
 * 支持普通折扣审批和升级订单折扣审批
 *
 * <AUTHOR>
 * <AUTHOR> (升级订单支持)
 * @date 2024/3/18 17:45
 */
@Component
@Slf4j
public class DiscountApprovalStrategy implements ApprovalStrategy {

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private OrderReductionService orderReductionService;

    @Resource
    private OrderRefundRepository orderRefundRepository;

    @Resource
    private OrderRefundApprovalStrategy orderRefundApprovalStrategy;

    @Resource
    private CashierCommandServiceImpl cashierCommandService;

    @Resource
    private OrderAggregateRoot orderAggregateRoot;

    @Override
    public void handleApproval(OrderApproveRecordTypeEnum type, String params, String messageId,
                               ApprovalStatusInfo approvalStatusInfo, String processId) {

        log.info("开始处理折扣审批，params={}, processId={}, messageId={}", params, processId, messageId);

        try {
            // 解析审批参数
            ApprovalParams approvalParams = parseApprovalParams(params);

            // 获取并验证订单
            HeOrderEntity order = validateAndGetOrder(approvalParams.getOrderId());

            // 解析审批状态
            ApprovalResult approvalResult = parseApprovalStatus(approvalStatusInfo);

            // 判断是否为升级订单审批
            if (isUpgradeOrderApproval(order)) {
                handleUpgradeOrderApproval(order, approvalParams, approvalResult, processId);
            } else {
                handleNormalDiscountApproval(order, approvalResult, processId);
            }

        } catch (Exception e) {
            log.error("处理折扣审批异常，params={}, processId={}", params, processId, e);
//            throw new BusinessException(ResultEnum.SYSTEM_ERROR.getCode(), "折扣审批处理失败: " + e.getMessage());
        }
    }

    /**
     * 解析审批参数
     */
    private ApprovalParams parseApprovalParams(String params) {
        try {
            JSONObject jsonObject = JSONUtil.parseObj(params);
            ApprovalParams approvalParams = new ApprovalParams();
            approvalParams.setOrderId(jsonObject.getInt("orderId"));
            approvalParams.setRefundOrderId(jsonObject.getInt("refundOrderId", null));
            return approvalParams;
        } catch (Exception e) {
            log.error("解析审批参数失败，params={}", params, e);
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "审批参数格式错误");
        }
    }

    /**
     * 验证并获取订单
     */
    private HeOrderEntity validateAndGetOrder(Integer orderId) {
        if (orderId == null) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "订单ID不能为空");
        }

        HeOrderEntity order = orderRepository.getByOrderId(orderId);
        if (ObjectUtil.isEmpty(order)) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(),
                    "订单折扣审批失败，找不到订单，订单ID=" + orderId);
        }

        return order;
    }

    /**
     * 解析审批状态
     */
    private ApprovalResult parseApprovalStatus(ApprovalStatusInfo approvalStatusInfo) {
        boolean isApproved = approvalStatusInfo.isFinish() && approvalStatusInfo.isAgree();
        boolean isRejected = approvalStatusInfo.isFinish() && approvalStatusInfo.isRefuse()
                || approvalStatusInfo.isTerminate();

        return new ApprovalResult(isApproved, isRejected);
    }

    /**
     * 判断是否为升级订单审批
     */
    private boolean isUpgradeOrderApproval(HeOrderEntity params) {
        return params.getOriginalOrderId() != null;
    }

    /**
     * 处理升级订单审批
     */
    private void handleUpgradeOrderApproval(HeOrderEntity order, ApprovalParams params,
                                            ApprovalResult result, String processId) {

        log.info("处理升级订单折扣审批，orderId={}, refundOrderId={}, approved={}, rejected={}",
                order.getOrderId(), params.getRefundOrderId(), result.isApproved(), result.isRejected());

        // 查找该订单最近的一条退款记录ID并设置到ApprovalParams中
        Integer recentRefundId = findRecentRefundId(order.getOriginalOrderId());
        if (recentRefundId != null) {
            params.setRecentRefundId(recentRefundId);
            params.setRefundOrderId(recentRefundId);
            log.info("找到最近的退款记录ID并设置到ApprovalParams，recentRefundId={}, originalOrderId={}",
                    recentRefundId, order.getOriginalOrderId());
        } else {
            log.warn("未找到该订单的最近退款记录，originalOrderId={}", order.getOriginalOrderId());
        }

        // 更新订单折扣审批状态
        updateOrderDiscountStatus(order, result);

        // 处理减免审批回调
        handleReductionApprovalCallback(result, processId);

        if (result.isApproved()) {
            handleUpgradeOrderApprovalSuccess(order, params, processId);
        } else if (result.isRejected()) {
            handleUpgradeOrderApprovalRejection(order, params, processId);
        }
    }

    /**
     * 处理升级订单审批通过
     */
    private void handleUpgradeOrderApprovalSuccess(HeOrderEntity order, ApprovalParams params, String processId) {
        log.info("升级订单折扣审批通过，orderId={}, refundOrderId={}", order.getOrderId(), params.getRefundOrderId());

        try {
            // 调用退款审批处理逻辑（和AfterDiscountApprovalProcessor一样的逻辑）
            handleRefundApprovalDirectly(params.getRefundOrderId(), true);

            // 调用余额支付逻辑（将退款金额支付到新订单）
            handleBalancePaymentForUpgradeOrder(order, params);

        } catch (Exception e) {
            log.error("升级订单审批通过处理异常，orderId={}, refundOrderId={}",
                    order.getOrderId(), params.getRefundOrderId(), e);
//            throw new BusinessException(ResultEnum.SYSTEM_ERROR.getCode(),
//                "升级订单审批通过处理失败: " + e.getMessage());
        }
    }

    /**
     * 处理升级订单审批驳回
     */
    private void handleUpgradeOrderApprovalRejection(HeOrderEntity order, ApprovalParams params, String processId) {
        log.info("升级订单折扣审批驳回，orderId={}, refundOrderId={}", order.getOrderId(), params.getRefundOrderId());

        try {
            // 调用退款审批处理逻辑（审批驳回）
            handleRefundApprovalDirectly(params.getRefundOrderId(), false);

            // 关闭升级订单
            closeUpgradeOrder(order);

        } catch (Exception e) {
            log.error("升级订单审批驳回处理异常，orderId={}, refundOrderId={}",
                    order.getOrderId(), params.getRefundOrderId(), e);
//            throw new BusinessException(ResultEnum.SYSTEM_ERROR.getCode(),
//                "升级订单审批驳回处理失败: " + e.getMessage());
        }
    }

    /**
     * 处理普通折扣审批
     */
    private void handleNormalDiscountApproval(HeOrderEntity order, ApprovalResult result, String processId) {
        log.info("处理普通折扣审批，orderId={}, approved={}, rejected={}",
                order.getOrderId(), result.isApproved(), result.isRejected());

        // 更新订单折扣审批状态
        updateOrderDiscountStatus(order, result);

        // 处理减免审批回调
        handleReductionApprovalCallback(result, processId);
    }

    /**
     * 直接处理退款审批（复用AfterDiscountApprovalProcessor的逻辑）
     */
    private void handleRefundApprovalDirectly(Integer refundOrderId, boolean isApproved) {
        try {
            HeOrderRefundEntity refundEntity = orderRefundRepository.getOneById(refundOrderId);
            if (refundEntity == null) {
                log.error("未找到退款记录，refundId={}", refundOrderId);
                return;
            }

            // 创建审批状态信息
            ApprovalStatusInfo approvalStatusInfo = createApprovalStatusInfo(isApproved);

            // 构建审批参数
            String approvalParams = buildRefundApprovalParams(refundEntity);

            // 调用退款审批策略
            orderRefundApprovalStrategy.handleApproval(
                    OrderApproveRecordTypeEnum.ORDER_REFUND,
                    approvalParams,
                    "DISCOUNT_APPROVAL_" + refundOrderId,
                    approvalStatusInfo,
                    "DISCOUNT_PROCESS_" + refundOrderId
            );

            String action = isApproved ? "通过" : "驳回";
            log.info("退款审批{}处理完成，refundId={}", action, refundOrderId);

        } catch (Exception e) {
            String action = isApproved ? "通过" : "驳回";
            log.error("处理退款审批{}失败，refundId={}", action, refundOrderId, e);
//            throw new BusinessException(ResultEnum.SYSTEM_ERROR.getCode(),
//                "退款审批" + action + "处理失败: " + e.getMessage());
        }
    }

    /**
     * 处理升级订单的余额支付
     */
    private void handleBalancePaymentForUpgradeOrder(HeOrderEntity newOrder, ApprovalParams params) {
        Integer refundOrderId = params.getRefundOrderId();
        try {
            log.info("处理升级订单余额支付，newOrderId={}, refundOrderId={}", newOrder.getOrderId(), refundOrderId);

            // 查询指定的退款记录获取退款金额
            HeOrderRefundEntity refundEntity = orderRefundRepository.getOneById(refundOrderId);
            if (refundEntity == null) {
                log.error("未找到指定的退款记录，无法进行余额支付，refundId={}", refundOrderId);
                return;
            }

            // 构建余额支付请求
            PayReqV2 payRequest = buildBalancePayRequest(refundEntity, newOrder);

            // 调用余额支付逻辑
            log.info("开始调用余额支付逻辑，newOrderId={}, refundAmount={}, originalOrderId={}",
                    newOrder.getOrderId(), refundEntity.getApplyAmount(), refundEntity.getOrderId());

            Result<cn.hutool.json.JSONObject> payResult = cashierCommandService.onlinePay(payRequest);

            if (payResult != null && payResult.getSuccess()) {
                log.info("升级订单余额支付成功，newOrderId={}, refundId={}, payResult={}",
                        newOrder.getOrderId(), refundOrderId, payResult.getData());
            } else {
                log.error("升级订单余额支付失败，newOrderId={}, refundId={}, error={}",
                        newOrder.getOrderId(), refundOrderId, payResult != null ? payResult.getMsg() : "result is null");
            }

        } catch (Exception e) {
            log.error("处理升级订单余额支付失败，newOrderId={}, refundOrderId={}",
                    newOrder.getOrderId(), refundOrderId, e);
//            throw new BusinessException(ResultEnum.SYSTEM_ERROR.getCode(),
//                "升级订单余额支付失败: " + e.getMessage());
        }
    }

    /**
     * 构建余额支付请求
     */
    private PayReqV2 buildBalancePayRequest(HeOrderRefundEntity refundEntity, HeOrderEntity newOrder) {
        PayReqV2 payRequest = new PayReqV2();

        // 设置新订单信息（支付到新订单）
        payRequest.setOrderId(newOrder.getOrderId());

        // 设置支付方式为意向金支付
        payRequest.setPayType(OmniPayTypeEnum.BALANCE.getCode());

        // 设置支付金额（退款金额，从分转换为元）
        payRequest.setPayAmount(new java.math.BigDecimal(refundEntity.getApplyAmount())
                .divide(new java.math.BigDecimal(100), 2, java.math.RoundingMode.HALF_UP));

        // 设置账户类型（默认为0-贝康）
        payRequest.setAccountType(0);

        // 设置其他必要参数
        payRequest.setAmountType(1); // 假设为1，具体值需要根据业务确定
        payRequest.setChannelType(1); // 1-原生
        payRequest.setCallType(1); // 1-URL重定向
        payRequest.setRemark("商品升级，原订单商品退款后重新支付至新订单");

        log.info("构建余额支付请求：newOrderId={}, payType={}, payAmount={}, accountType={}, refundFromOrderId={}",
                payRequest.getOrderId(), payRequest.getPayType(), payRequest.getPayAmount(),
                payRequest.getAccountType(), refundEntity.getOrderId());

        return payRequest;
    }

    /**
     * 关闭升级订单
     */
    private void closeUpgradeOrder(HeOrderEntity order) {
        try {
            log.info("关闭升级订单，orderId={}", order.getOrderId());
            orderAggregateRoot.close(Operator.system(), order.getOrderId());
            log.info("升级订单关闭成功，orderId={}", order.getOrderId());
        } catch (Exception e) {
            log.error("关闭升级订单失败，orderId={}", order.getOrderId(), e);
//            throw new BusinessException(ResultEnum.SYSTEM_ERROR.getCode(),
//                "关闭升级订单失败: " + e.getMessage());
        }
    }

    /**
     * 更新订单折扣审批状态
     */
    private void updateOrderDiscountStatus(HeOrderEntity order, ApprovalResult result) {
        Integer approvalDiscountStatus = null;

        if (result.isApproved()) {
            approvalDiscountStatus = ApprovalDiscountStatusEnum.APPROVED.getCode();
        } else if (result.isRejected()) {
            approvalDiscountStatus = ApprovalDiscountStatusEnum.APPROVAL_FAILED.ordinal();
        }

        if (approvalDiscountStatus != null) {
            order.setApprovalDiscountStatus(approvalDiscountStatus);
            orderRepository.updateOrderMonthByOrderId(order);
            log.info("订单折扣审批状态更新完成，orderSn={}, status={}", order.getOrderSn(), approvalDiscountStatus);
        }
    }

    /**
     * 处理减免审批回调
     */
    private void handleReductionApprovalCallback(ApprovalResult result, String processId) {
        if (result.isApproved() || result.isRejected()) {
            OrderReductionApprovalDealReq dealReq = new OrderReductionApprovalDealReq();
            dealReq.setLocalProcessId(processId);
            dealReq.setType(DecreaseTypeEnum.DISCOUNT);

            if (result.isApproved()) {
                dealReq.setAuthState(OrderDecreaseStatusEnum.APPROVED.getCode());
            } else {
                dealReq.setAuthState(OrderDecreaseStatusEnum.APPROVAL_FAILED.getCode());
            }

            orderReductionService.reductionApprovalCallback(dealReq);
            log.info("减免审批回调处理完成，processId={}, approved={}", processId, result.isApproved());
        }
    }

    /**
     * 创建审批状态信息
     */
    private ApprovalStatusInfo createApprovalStatusInfo(boolean isApproved) {
        ApprovalStatusInfo statusInfo = new ApprovalStatusInfo();
        statusInfo.setFinish(true);
        statusInfo.setAgree(isApproved);
        statusInfo.setRefuse(!isApproved);
        statusInfo.setTerminate(false);
        return statusInfo;
    }

    /**
     * 构建退款审批参数
     */
    private String buildRefundApprovalParams(HeOrderRefundEntity refundEntity) {
        return String.format("{\"orderId\":%d,\"orderRefundId\":%d}",
                refundEntity.getOrderId(), refundEntity.getId());
    }

    /**
     * 查找指定订单最近的一条退款记录ID
     */
    private Integer findRecentRefundId(Integer orderId) {
        try {
            if (orderId == null) {
                return null;
            }

            // 使用现有的getRefundByOrderId方法，它已经按创建时间降序排列
            java.util.List<HeOrderRefundEntity> refunds = orderRefundRepository.getRefundByOrderId(orderId);
            refunds = refunds.stream().filter(o -> StringUtils.isEmpty(o.getParentRefundOrderSn())).collect(Collectors.toList());
            if (!refunds.isEmpty()) {
                // 第一个就是最近的退款记录
                HeOrderRefundEntity recentRefund = refunds.get(0);
                log.info("查找到最近的退款记录，orderId={}, recentRefundId={}, createTime={}",
                        orderId, recentRefund.getId(), recentRefund.getCreatedAt());
                return recentRefund.getId();
            }

            log.info("未找到该订单的退款记录，orderId={}", orderId);
            return null;

        } catch (Exception e) {
            log.error("查找最近退款记录失败，orderId={}", orderId, e);
            return null;
        }
    }

    @Override
    public OrderApproveRecordTypeEnum getOrderApproveRecordTypeEnum() {
        return OrderApproveRecordTypeEnum.DISCOUNT_APPROVAL;
    }

    /**
     * 审批参数封装类
     */
    private static class ApprovalParams {
        private Integer orderId;
        private Integer refundOrderId;
        private Integer recentRefundId;

        public Integer getOrderId() {
            return orderId;
        }

        public void setOrderId(Integer orderId) {
            this.orderId = orderId;
        }

        public Integer getRefundOrderId() {
            return refundOrderId;
        }

        public void setRefundOrderId(Integer refundOrderId) {
            this.refundOrderId = refundOrderId;
        }

        public Integer getRecentRefundId() {
            return recentRefundId;
        }

        public void setRecentRefundId(Integer recentRefundId) {
            this.recentRefundId = recentRefundId;
        }
    }

    /**
     * 审批结果封装类
     */
    private static class ApprovalResult {
        private final boolean approved;
        private final boolean rejected;

        public ApprovalResult(boolean approved, boolean rejected) {
            this.approved = approved;
            this.rejected = rejected;
        }

        public boolean isApproved() {
            return approved;
        }

        public boolean isRejected() {
            return rejected;
        }
    }
}

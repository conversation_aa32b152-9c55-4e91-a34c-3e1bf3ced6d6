package com.stbella.order.server.order.month.handle;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.stbella.customer.server.ecp.enums.PhoneTypeEnum;
import com.stbella.customer.server.ecp.vo.ClientInfoVO;
import com.stbella.order.common.enums.core.*;
import com.stbella.order.common.enums.month.*;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.client.StoreGoodsClient;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.production.GoodsCategoryBackEntity;
import com.stbella.order.domain.order.production.GoodsEntity;
import com.stbella.order.domain.order.production.OrderGiftExtendEntity;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.config.DynamicConfig;
import com.stbella.order.server.contract.enums.IdTypeEnum;
import com.stbella.order.server.manager.TabClientManager;
import com.stbella.order.server.order.month.enums.*;
import com.stbella.order.server.order.month.req.IncomeAndRefundRecordReq;
import com.stbella.order.server.order.month.req.TagReq;
import com.stbella.order.server.order.month.res.*;
import com.stbella.order.server.order.month.service.AddressQueryService;
import com.stbella.order.server.order.month.service.HeOrderService;
import com.stbella.order.server.order.template.OrderInfoQueryTemplate;
import com.stbella.order.server.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public abstract class OrderInfoAbstractServiceImpl implements OrderInfoQueryTemplate {

    @Resource
    private StoreGoodsClient storeClient;

    @Resource
    private DynamicConfig dynamicConfig;

    @Resource
    private UserRepository userRepository;

    @Resource
    private ClientRepository clientRepository;

    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;

    @Resource
    private OrderVoucherRepository orderVoucherRepository;

    @Resource
    private TagsRepository tagsRepository;

    @Resource
    private OrderGoodsRepository orderGoodsRepository;

    @Resource
    private GoodsCategoryBackRepository goodsCategoryBackRepository;

    @Resource
    private OrderRefundRepository orderRefundRepository;

    @Resource
    private IncomeRecordRepository incomeRecordRepository;

    @Resource
    private IncomeProofRecordRepository incomeProofRecordRepository;

    @Resource
    private MonthContractSignRecordRepository monthContractSignRecordRepository;

    @Resource
    private MonthContractSignAgreementRepository monthContractSignAgreementRepository;

    @Resource
    private GoodsRepository goodsRepository;

    @Resource
    private OrderBailorSnapshotRepository orderBailorSnapshotRepository;

    @Resource
    private AddressQueryService addressQueryService;

    @Resource
    private HeOrderAttachmentRepository heOrderAttachmentRepository;

    @Resource
    private TabClientManager tabClientManager;

    @Resource
    private OrderGiftExtendRepository orderGiftExtendRepository;

    @Resource
    private ProductionAmountPayRepository productionAmountPayRepository;

    @Resource
    private ProductionAmountRefundRepository productionAmountRefundRepository;

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private HeOrderService heOrderService;


    public List<GoodsNewVO> getBuyGoodsList(HeOrderEntity orderEntity) {

        List<HeOrderGoodsEntity> orderGoodsByOrderIdList = orderGoodsRepository.getByOrderIdList(Collections.singletonList(orderEntity.getOrderId()));
        if (CollectionUtils.isEmpty(orderGoodsByOrderIdList)) {
            return Lists.newArrayList();
        }
        List<Integer> goodsCategoryIdList = orderGoodsByOrderIdList.stream().filter(item -> Objects.nonNull(item.getBackCategoryId())).map(a -> a.getBackCategoryId().intValue()).collect(Collectors.toList());
        List<GoodsCategoryBackEntity> goodsCategoryList = goodsCategoryBackRepository.queryCategoryListByIds(goodsCategoryIdList);
        Map<Long, String> backCategoryMap = CollectionUtils.isEmpty(goodsCategoryList) ? Maps.newHashMap() : goodsCategoryList.stream().collect(Collectors.toMap(a -> Long.valueOf(a.getId()), GoodsCategoryBackEntity::getName));
        List<GoodsNewVO> resultList = Lists.newArrayList();
        orderGoodsByOrderIdList.forEach(orderGoods -> {
            GoodsNewVO goodsNewVO = BeanMapper.map(orderGoods, GoodsNewVO.class);
            goodsNewVO.setGoodsPriceOrgin(orderEntity.getCurrency() + AmountChangeUtil.f2YDown(orderGoods.getGoodsPriceOrgin()));
            goodsNewVO.setGiftStr("购买");
            goodsNewVO.setGiftTypeStr(StringUtils.EMPTY);
            goodsNewVO.setCurrency(orderEntity.getCurrency());
            goodsNewVO.setCategoryBack(orderGoods.getBackCategoryId());
            goodsNewVO.setCategoryBackStr(Objects.isNull(orderGoods.getBackCategoryId()) ? StringUtils.EMPTY : backCategoryMap.get(orderGoods.getBackCategoryId()));
            resultList.add(goodsNewVO);
        });
        return resultList;
    }

    public void fillGiftGoodsList(HeOrderEntity orderEntity, List<GoodsNewVO> goodsNewVOList) {

        List<OrderGiftExtendEntity> orderGiftExtendList = orderGiftExtendRepository.getByOrderId(orderEntity.getOrderId());
        if (org.springframework.util.CollectionUtils.isEmpty(orderGiftExtendList)) {
            return;
        }
        List<Integer> goodsIdList = orderGiftExtendList.stream().map(OrderGiftExtendEntity::getGoodsId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Integer, JSONObject> backCategoryMap = this.backCategoryMap(goodsIdList);
        for (OrderGiftExtendEntity orderGiftExtend : orderGiftExtendList) {

            GoodsNewVO goodsNewVO = new GoodsNewVO();
            goodsNewVO.setId(orderGiftExtend.getId());
            goodsNewVO.setSkuName(orderGiftExtend.getSkuName());
            goodsNewVO.setSkuId(orderGiftExtend.getSkuId());
            goodsNewVO.setGoodsId(orderGiftExtend.getGoodsId());
            goodsNewVO.setGoodsName(orderGiftExtend.getGoodsName());
            goodsNewVO.setGoodsNum(orderGiftExtend.getGoodsNum());
            goodsNewVO.setGoodsPriceOrgin(orderEntity.getCurrency() + AmountChangeUtil.f2YDown(orderGiftExtend.getPrice()));
            goodsNewVO.setGift(1);
            goodsNewVO.setGiftStr("礼赠");
            goodsNewVO.setGiftTypeStr("定制礼赠");
            goodsNewVO.setCurrency(orderEntity.getCurrency());
            JSONObject jsonObject = backCategoryMap.get(orderGiftExtend.getGoodsId());
            goodsNewVO.setCategoryBack(Objects.isNull(jsonObject) ? null : jsonObject.getLong("id"));
            goodsNewVO.setCategoryBackStr(Objects.isNull(jsonObject) ? StringUtils.EMPTY : jsonObject.getString("name"));
            goodsNewVOList.add(goodsNewVO);
        }

    }

    public Map<Integer, JSONObject> backCategoryMap(List<Integer> goodsIdList){


        if (org.springframework.util.CollectionUtils.isEmpty(goodsIdList)) {
            return Maps.newHashMap();
        }
        List<GoodsEntity> goodsEntityList = goodsRepository.selectByIdList(goodsIdList);
        if (org.springframework.util.CollectionUtils.isEmpty(goodsEntityList)) {
            return Maps.newHashMap();
        }
        List<Integer> categoryBackIdList = goodsEntityList.stream().map(GoodsEntity::getCategoryBack).filter(Objects::nonNull).collect(Collectors.toList());
        if (org.springframework.util.CollectionUtils.isEmpty(categoryBackIdList)){
            return Maps.newHashMap();
        }
        List<GoodsCategoryBackEntity> goodsCategoryList = goodsCategoryBackRepository.queryCategoryListByIds(categoryBackIdList);
        Map<Long, String> backCategoryMap = org.springframework.util.CollectionUtils.isEmpty(goodsCategoryList) ? Maps.newHashMap() : goodsCategoryList.stream().collect(Collectors.toMap(a -> Long.valueOf(a.getId()), GoodsCategoryBackEntity::getName));
        Map<Integer, JSONObject> goodsBackCategoryMap = new HashMap<>();
        goodsEntityList.forEach(goodsEntity -> {
            String categoryBackName = backCategoryMap.get(goodsEntity.getCategoryBack());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", goodsEntity.getCategoryBack());
            jsonObject.put("name", categoryBackName);
            goodsBackCategoryMap.put(goodsEntity.getId(), jsonObject);
        });
        return goodsBackCategoryMap;
    }

    private String getTagName(Integer orderTag) {

        TagReq tagReq = new TagReq();
        tagReq.setId(orderTag);
        TagVO tagVO = tagsRepository.queryTagByReq(tagReq);
        return Objects.isNull(tagVO) ? StringUtils.EMPTY : tagVO.getTagName();
    }

    public ContractNewVO getContractList(Integer contractStatus, Integer clientUid, Integer orderId, Boolean isSnapshot) {

        ContractNewVO contractNewVO = new ContractNewVO();
        contractNewVO.setClientInfo(isSnapshot ? this.getNewOrderClientInfo(orderId) : this.getOldOrderClientInfo(clientUid));
        contractNewVO.setBailorInfo(this.getBailorInfo(orderId));
        String bailorName = Objects.isNull(contractNewVO.getBailorInfo()) ? StringUtils.EMPTY : contractNewVO.getBailorInfo().getName();
        String userName = contractNewVO.getClientInfo().getName();

        List<ContractNewVO.ContractSignRecord> contractSignRecordList = Lists.newArrayList();
        List<HeOrderAttachmentEntity> heOrderAttachmentEntities = heOrderAttachmentRepository.queryListByCondition(orderId);
        List<MonthContractSignRecordEntity> mainContractList = monthContractSignRecordRepository.getListByOrderId(orderId.longValue(), contractStatus);
        List<MonthContractSignAgreementEntity> agreementContractList = monthContractSignAgreementRepository.list(orderId.longValue(), contractStatus);

        convertContractSignRecord1(heOrderAttachmentEntities, contractSignRecordList);
        convertContractSignRecord2(mainContractList, contractSignRecordList, userName, bailorName);
        convertContractSignRecord3(agreementContractList, contractSignRecordList, userName, bailorName);

        if (CollectionUtils.isNotEmpty(contractSignRecordList)) {
            contractSignRecordList.sort(Comparator.comparing(ContractNewVO.ContractSignRecord::getSignTime).reversed());
        }
        contractNewVO.setContractSignRecordList(contractSignRecordList);
        return contractNewVO;
    }

    private ContractNewVO.ClientInfo getOldOrderClientInfo(Integer clientUid){

        ClientInfoVO clientInfoByClientId = tabClientManager.getClientInfoByClientId(clientUid);
        ContractNewVO.ClientInfo clientInfo = BeanMapper.map(clientInfoByClientId, ContractNewVO.ClientInfo.class);
        clientInfo.setAuthTypeStr(Objects.isNull(clientInfo.getAuthType()) || clientInfo.getAuthType() == 0 ? "实名认证" : "邮箱");
        clientInfo.setAuthTypeStr(Objects.isNull(clientInfo.getAuthType()) || clientInfo.getAuthType() == 0 ? "实名认证" : "邮箱");
        clientInfo.setRelationWithClientStr(RelationshipWithEnum.getValueByCode(clientInfo.getRelationWithClient()));
        clientInfo.setCertTypeStr(IdTypeEnum.getValueByCode(clientInfo.getCertType()));
        String provincesRegions = addressQueryService.getProvincesRegions(clientInfoByClientId.getProvince(), clientInfoByClientId.getCity(), clientInfoByClientId.getRegion());
        clientInfo.setAllAddress(provincesRegions + clientInfoByClientId.getAddress());
        return clientInfo;
    }

    private ContractNewVO.BailorInfo getBailorInfo(Integer orderId) {

        HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity = orderBailorSnapshotRepository.queryByOrderId(orderId);
        if (Objects.isNull(heOrderBailorSnapshotEntity)) {
            return null;
        }
        ContractNewVO.BailorInfo bailorInfo = BeanMapper.map(heOrderBailorSnapshotEntity, ContractNewVO.BailorInfo.class);
        PhoneTypeEnum enumByCode = PhoneTypeEnum.getEnumByCode(bailorInfo.getPhoneType());
        bailorInfo.setPhoneTypeStr(Objects.isNull(enumByCode) ? StringUtils.EMPTY : enumByCode.desc());
        bailorInfo.setCertTypeStr(IdTypeEnum.getValueByCode(bailorInfo.getCertType()));
        bailorInfo.setAuthTypeStr(Objects.isNull(bailorInfo.getAuthType()) || bailorInfo.getAuthType() == 0 ? "实名认证" : "邮箱");
        return bailorInfo;
    }

    private ContractNewVO.ClientInfo getNewOrderClientInfo(Integer orderId){

        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(orderId);
        Assert.isTrue(Objects.nonNull(heOrderUserSnapshotEntity), "订单客户主体信息不存在");
        ContractNewVO.ClientInfo clientInfo = BeanMapper.map(heOrderUserSnapshotEntity, ContractNewVO.ClientInfo.class);
        clientInfo.setAuthTypeStr(Objects.isNull(clientInfo.getAuthType()) || clientInfo.getAuthType() == 0 ? "实名认证" : "邮箱");
        clientInfo.setRelationWithClientStr(RelationshipWithEnum.getValueByCode(clientInfo.getRelationWithClient()));
        clientInfo.setCertTypeStr(IdTypeEnum.getValueByCode(clientInfo.getCertType()));
        String provincesRegions = addressQueryService.getProvincesRegions(heOrderUserSnapshotEntity.getProvince(), heOrderUserSnapshotEntity.getCity(), heOrderUserSnapshotEntity.getRegion());
        clientInfo.setAllAddress(provincesRegions + heOrderUserSnapshotEntity.getAddress());
        return clientInfo;
    }

    public IncomeInfoVO getIncomeInfo(IncomeAndRefundRecordReq req) {

        HeUserProductionAmountPayLogEntity productionAmountPayLogEntity = productionAmountPayRepository.getOneById(req.getId());
        if (Objects.isNull(productionAmountPayLogEntity)){
            return this.queryincomeInfo(req);
        }
        HeOrderEntity byOrderSn = orderRepository.queryOrderById(productionAmountPayLogEntity.getOrderId());
        IncomeInfoVO incomeInfoVO = new IncomeInfoVO();
        incomeInfoVO.setId(productionAmountPayLogEntity.getId());
        incomeInfoVO.setIncome(convertBigDecimal2Str(byOrderSn.getCurrency(), AmountChangeUtil.changeF2Y(productionAmountPayLogEntity.getPayAmount())));
        incomeInfoVO.setPayType(OmniPayTypeEnum.PRODUCTION_COIN.getCode());
        incomeInfoVO.setPayTypeStr(OmniPayTypeEnum.PRODUCTION_COIN.getValue());
        incomeInfoVO.setStatus(productionAmountPayLogEntity.getStatus());
        incomeInfoVO.setStatusStr(req.getStatusStr());
        incomeInfoVO.setPayTime(DateUtil.formatDateTime(new Date(productionAmountPayLogEntity.getPayTime() * 1000)));
        incomeInfoVO.setCurrency(byOrderSn.getCurrency());
        incomeInfoVO.setStaffId(productionAmountPayLogEntity.getApplyUserId());
        UserEntity userEntity = userRepository.queryById(incomeInfoVO.getStaffId());
        incomeInfoVO.setStaffName(Objects.isNull(userEntity) ? StringUtils.EMPTY : userEntity.getName());
        incomeInfoVO.setCreatedAt(DateUtil.formatDateTime(new Date(productionAmountPayLogEntity.getCreatedAt() * 1000)));
        return incomeInfoVO;
    }


    public IncomeInfoVO queryincomeInfo(IncomeAndRefundRecordReq req) {

        Assert.isTrue(Objects.nonNull(req.getId()), "收入记录ID不能为空");
        HeIncomeRecordEntity incomeRecord = incomeRecordRepository.getOneById(req.getId());
        if (Objects.isNull(incomeRecord)) {
            return null;
        }
        IncomeInfoVO incomeInfoVO = new IncomeInfoVO();
        incomeInfoVO.setId(incomeRecord.getId());
        incomeInfoVO.setIncomeSn(incomeRecord.getIncomeSn());
        incomeInfoVO.setIncome(convertBigDecimal2Str(incomeRecord.getCurrency(), AmountChangeUtil.changeF2Y(incomeRecord.getIncome())));
        incomeInfoVO.setPayType(incomeRecord.getPayType());
        incomeInfoVO.setPayTypeStr(OmniPayTypeEnum.getName(incomeRecord.getPayType()));
        incomeInfoVO.setStatus(incomeRecord.getStatus());
        incomeInfoVO.setStatusStr(req.getStatusStr());
        incomeInfoVO.setPayTime(DateUtil.formatDateTime(new Date(Long.valueOf(incomeRecord.getPayTime()) * 1000)));
        HeIncomeProofRecordEntity incomeProofRecordEntity = incomeProofRecordRepository.getLastOneByIncomeId(incomeRecord.getId());
        if (Objects.nonNull(incomeProofRecordEntity)) {
            incomeInfoVO.setPayProof(Collections.singletonList(incomeProofRecordEntity.getPayProof()));
            incomeInfoVO.setRemark(incomeProofRecordEntity.getRemark());
        }
        incomeInfoVO.setCurrency(incomeRecord.getCurrency());
        HeOrderEntity byOrderSn = orderRepository.queryOrderById(incomeRecord.getOrderId());
        if (Objects.nonNull(byOrderSn)) {
            incomeInfoVO.setStaffId(byOrderSn.getStaffId());
            UserEntity userEntity = userRepository.queryById(incomeInfoVO.getStaffId());
            if (Objects.nonNull(userEntity)) {
                incomeInfoVO.setStaffName(userEntity.getName());
            }
        }
        incomeInfoVO.setCreatedAt(DateUtil.formatDateTime(new Date(incomeRecord.getCreatedAt() * 1000)));
        return incomeInfoVO;
    }


    public OrderInfoNewVO getCommonOrderInfo(HeOrderEntity orderEntity) {

        OrderInfoNewVO orderInfoNewVO = new OrderInfoNewVO();
        orderInfoNewVO.setOrderSn(orderEntity.getOrderSn());
        orderInfoNewVO.setExtraInfo(orderEntity.getExtraInfo());
        orderInfoNewVO.setOrderStatusStr(OmniOrderStatusEnum.from(orderEntity.getOrderStatus()).getValue());
        orderInfoNewVO.setPerformanceEffectiveDate(this.getPerformanceEffectiveDate(orderEntity.getPercentFirstTime()));

        orderInfoNewVO.setApprovalDiscountStatus(orderEntity.getApprovalDiscountStatus());
        orderInfoNewVO.setApprovalDiscountStatusStr(OldOrderApproveStatusEnum.getValueByCode(orderInfoNewVO.getApprovalDiscountStatus()));
        orderInfoNewVO.setCreatedAt(DateUtil.formatDateTime(new Date(orderEntity.getCreatedAt() * 1000)));
        Integer orderTag = orderEntity.getOrderTag();
        orderInfoNewVO.setOrderTagId(orderTag);
        orderInfoNewVO.setOrderTagName(this.getTagName(orderTag));
        orderInfoNewVO.setRemark(orderEntity.getRemark());

        orderInfoNewVO.setPayAmount(convertBigDecimal2Str(orderEntity.getCurrency(), AmountChangeUtil.f2YDown(orderEntity.getPayAmount())));
        orderInfoNewVO.setCalPayableAmount(convertBigDecimal2Str(orderEntity.getCurrency(), AmountChangeUtil.f2YDown(orderEntity.calPayable())));
        orderInfoNewVO.setDiscountAmount(convertBigDecimal2Str(orderEntity.getCurrency(), AmountChangeUtil.f2YDown(orderEntity.getOrderAmount() - orderEntity.calPayable())));
        orderInfoNewVO.setOrderAmount(convertBigDecimal2Str(orderEntity.getCurrency(), AmountChangeUtil.f2YDown(orderEntity.getOrderAmount())));
        orderInfoNewVO.setOrderDiscount(orderEntity.getDiscountMargin() + "%");
        orderInfoNewVO.setGrossProfitMargin(orderEntity.getGrossMargin() + "%");
        orderInfoNewVO.setSignDiscount(Objects.isNull(orderEntity.getSignOrderDiscountMargin()) ? StringUtils.EMPTY : orderEntity.getSignOrderDiscountMargin() + "%");

        this.fillStoreInfo(orderInfoNewVO, orderEntity);
        this.fillOrderCustomerInfo(orderInfoNewVO, orderEntity);
        this.fillOrderStaffAndCreateInfo(orderInfoNewVO, orderEntity);
        this.fillOrderUserSnapshot(orderInfoNewVO, orderEntity);
        this.fillUrlList(orderInfoNewVO, orderEntity);
        return orderInfoNewVO;
    }

    private List<ContractNewVO.ContractSignRecord> convertContractSignRecord1(List<HeOrderAttachmentEntity> entityList, List<ContractNewVO.ContractSignRecord> contractSignRecordList) {

        if (CollectionUtils.isEmpty(entityList)) {
            return contractSignRecordList;
        }
        entityList.forEach(orderAttachment -> {
            ContractNewVO.ContractSignRecord record = new ContractNewVO.ContractSignRecord();
            record.setContractType(ContractTypeEnum.ATTACHMENT_TYPE.code());
            record.setContractTypeName(ContractTypeEnum.ATTACHMENT_TYPE.desc());
            record.setTemplateType(TemplateTypeEnum.ATTACHMENT_TYPE.code());
            record.setTemplateTypeName(TemplateTypeEnum.ATTACHMENT_TYPE.desc());
            record.setContractName(orderAttachment.getName());
            record.setContractLongUrl(orderAttachment.getUrl());
            record.setSignTime(DateUtil.formatDateTime(new Date(orderAttachment.getCreatedAt() * 1000)));
            contractSignRecordList.add(record);
        });
        return contractSignRecordList;
    }

    private List<ContractNewVO.ContractSignRecord> convertContractSignRecord2(List<MonthContractSignRecordEntity> entityList, List<ContractNewVO.ContractSignRecord> contractSignRecordList, String userName, String bailorName) {

        if (CollectionUtils.isEmpty(entityList)) {
            return contractSignRecordList;
        }
        entityList.forEach(contractSignRecord -> {
            ContractNewVO.ContractSignRecord record = new ContractNewVO.ContractSignRecord();
            record.setId(contractSignRecord.getId());
            record.setTemplateContractType(contractSignRecord.getTemplateContractType());
            record.setTemplateType(contractSignRecord.getTemplateType());
            record.setTemplateTypeName(TemplateTypeEnum.fromCode(contractSignRecord.getTemplateType()));
            record.setContractType(contractSignRecord.getContractType());
            record.setContractTypeName(ContractTypeEnum.fromCode(contractSignRecord.getContractType()));
            record.setContractName(contractSignRecord.getContractName());
            record.setDownloadUrl(contractSignRecord.getContractLongUrl());
            record.setSignTime(DateUtil.formatDateTime(contractSignRecord.getSignTime()));
            if (Objects.nonNull(contractSignRecord.getContractStatus()) && contractSignRecord.getContractStatus() == 2) {
                if (StringUtils.isEmpty(bailorName)) {
                    record.setSignName(userName);
                } else {
                    record.setSignName(TemplateContractTypeEnum.ENTRUST.code().equals(contractSignRecord.getTemplateContractType()) ? userName : bailorName);
                }
            }
            if (Objects.nonNull(contractSignRecord.getContractType())) {
                if (contractSignRecord.getContractType() == 1) {
                    record.setContractLongUrl(contractSignRecord.getContractLongUrl());
                }
                if (contractSignRecord.getContractType() == 3 && StringUtils.isNotEmpty(contractSignRecord.getImg())) {
                    record.setImg(JSON.parseArray(contractSignRecord.getImg(), String.class));
                }
            }
            contractSignRecordList.add(record);
        });
        return contractSignRecordList;
    }

    private List<ContractNewVO.ContractSignRecord> convertContractSignRecord3(List<MonthContractSignAgreementEntity> entityList, List<ContractNewVO.ContractSignRecord> contractSignRecordList, String userName, String bailorName) {

        if (CollectionUtils.isEmpty(entityList)) {
            return contractSignRecordList;
        }
        entityList.forEach(contractSignAgreement -> {
            ContractNewVO.ContractSignRecord record = new ContractNewVO.ContractSignRecord();
            record.setId(contractSignAgreement.getId());
            record.setTemplateContractType(contractSignAgreement.getTemplateContractType());
            record.setContractName(TemplateContractTypeEnum.fromCode(contractSignAgreement.getTemplateContractType()));
            record.setContractLongUrl(contractSignAgreement.getContractShortUrl());
            record.setDownloadUrl(contractSignAgreement.getContractLongUrl());
            record.setSignTime(DateUtil.formatDateTime(DateUtil.date(contractSignAgreement.getSignTime().toLocalTime())));
            record.setContractType(ContractTypeEnum.ESIGN_TYPE.code());
            record.setContractTypeName(ContractTypeEnum.ESIGN_TYPE.desc());
            record.setTemplateType(TemplateTypeEnum.AGREEMENT_TYPE.code());
            record.setTemplateTypeName(TemplateTypeEnum.AGREEMENT_TYPE.desc());
            if (Objects.nonNull(contractSignAgreement.getState()) && contractSignAgreement.getState() == 2) {
                record.setSignName(StringUtils.isEmpty(bailorName) ? userName : bailorName);
            }
            contractSignRecordList.add(record);
        });
        return contractSignRecordList;
    }


    public IncomeAndRefundVO getIncomeAndRefundVO(HeOrderEntity orderEntity){


        IncomeAndRefundVO incomeAndRefundVO = new IncomeAndRefundVO();
        String currency = orderEntity.getCurrency();
        incomeAndRefundVO.setCurrency(currency);
        //应收金额
        incomeAndRefundVO.setPayAmount(convertBigDecimal2Str(currency, AmountChangeUtil.changeF2Y(orderEntity.getPayAmount())));
        //累计已付
        incomeAndRefundVO.setPaidAmount(convertBigDecimal2Str(currency, AmountChangeUtil.changeF2Y(orderEntity.getPaidAmount() + orderEntity.getProductionAmountPay())));
        incomeAndRefundVO.setRealAmount(incomeAndRefundVO.getPaidAmount());
        incomeAndRefundVO.setNoPayAmount(convertBigDecimal2Str(currency, AmountChangeUtil.changeF2Y(orderEntity.getPayAmount() - orderEntity.getProductionAmountPay() - orderEntity.getPaidAmount())));


        List<HeOrderRefundEntity> refundByOrderIdList = orderRefundRepository.getRefundByOrderId(orderEntity.getOrderId());
        List<HeIncomeProofRecordEntity> incomeProofRecordByOrderIdList = incomeProofRecordRepository.getIncomeProofRecordByOrderId(orderEntity.getOrderId());
        incomeAndRefundVO.setIncome(convertBigDecimal2Str(currency, getIncome(incomeProofRecordByOrderIdList)));

        List<HeUserProductionAmountRefundLogEntity> heUserProductionAmountRefundLogEntities = productionAmountRefundRepository.queryRecordListByOrderId(orderEntity.getOrderId());
        incomeAndRefundVO.setRefundProcessAmount(convertBigDecimal2Str(currency, getRefundProcessAmount(heUserProductionAmountRefundLogEntities, refundByOrderIdList, true)));
        incomeAndRefundVO.setRefundAmount(convertBigDecimal2Str(currency, getRefundProcessAmount(heUserProductionAmountRefundLogEntities, refundByOrderIdList, false)));
        incomeAndRefundVO.setRefundInfoList(refundInfoList(currency, refundByOrderIdList, heUserProductionAmountRefundLogEntities));
        incomeAndRefundVO.setIncomeInfoList(incomeInfoList(orderEntity.getOrderId(), orderEntity.getCurrency(), orderEntity.getStaffId(), incomeProofRecordByOrderIdList));
        incomeAndRefundVO.setIsNewOrder(false);
        incomeAndRefundVO.setOrderType(orderEntity.getOrderType());
        incomeAndRefundVO.setActualReceiveAmount(heOrderService.getActualReceiveAmount(orderEntity.getOrderId()));
        return incomeAndRefundVO;
    }

    private List<IncomeAndRefundVO.RefundInfo> refundInfoList(String currency, List<HeOrderRefundEntity> refundByOrderIdList, List<HeUserProductionAmountRefundLogEntity> heUserProductionAmountRefundLogEntities){
        List<IncomeAndRefundVO.RefundInfo> refundInfoList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(heUserProductionAmountRefundLogEntities)){
            heUserProductionAmountRefundLogEntities.forEach(productionRefundEntity -> {
                IncomeAndRefundVO.RefundInfo refundInfo = new IncomeAndRefundVO.RefundInfo();
                refundInfo.setId(productionRefundEntity.getId());
                refundInfo.setActualAmount(convertBigDecimal2Str(currency, AmountChangeUtil.changeF2Y(productionRefundEntity.getApplyAmount())));
                refundInfo.setRefundType(productionRefundEntity.getRefundType());
                refundInfo.setRefundTypeStr(Objects.isNull(productionRefundEntity.getRefundType()) ? StringUtils.EMPTY : RefundTypeEnum.getValueByCode(productionRefundEntity.getRefundType()));
                refundInfo.setStatus(productionRefundEntity.getStatus());
                refundInfo.setStatusStr(OmniRefundApproveEnum.getName(productionRefundEntity.getStatus()));
                refundInfo.setApplyId(productionRefundEntity.getApplyUserId());
                UserEntity user = userRepository.queryById(productionRefundEntity.getApplyUserId());
                refundInfo.setApplyName(Objects.isNull(user) ? StringUtils.EMPTY : user.getName());
                refundInfo.setCreatedAt(productionRefundEntity.getCreatedAt());
                refundInfoList.add(refundInfo);
            });
        }
        List<HeOrderRefundEntity> heOrderRefundEntityList = refundByOrderIdList.stream().filter(item -> Objects.nonNull(item.getType()) && item.getType() != 2).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(heOrderRefundEntityList)){
            return refundInfoList;
        }
        heOrderRefundEntityList.forEach(refundOrder -> {
            IncomeAndRefundVO.RefundInfo refundInfo = new IncomeAndRefundVO.RefundInfo();
            refundInfo.setId(refundOrder.getId());
            refundInfo.setRefundOrderSn(refundOrder.getRefundOrderSn());
            BigDecimal subActualAmount = AmountChangeUtil.changeF2Y(refundOrder.getStatus() == 4 ? refundOrder.getActualAmount() : refundOrder.getApplyAmount());
            refundInfo.setActualAmount(convertBigDecimal2Str(currency, subActualAmount));
            refundInfo.setRefundReasonType(refundOrder.getRefundReasonType());
            refundInfo.setRefundReasonTypeStr(refundOrder.getRefundReasonType() == 1 ? "正常退款" : "⾮正常退款（客诉）");
            refundInfo.setRefundReason(refundOrder.getRefundReason());
            refundInfo.setRefundReasonStr(RefundReasonEnum.getName(refundOrder.getRefundReason()));
            refundInfo.setRefundType(refundOrder.getRefundType());
            RefundTypeEnum typeEnum = RefundTypeEnum.getType(refundInfo.getRefundReason());
            refundInfo.setRefundTypeStr(Objects.isNull(typeEnum) ? StringUtils.EMPTY : typeEnum.getValue());
            if (Objects.nonNull(refundOrder.getRefundType()) && refundOrder.getRefundType() == 10) {
                refundInfo.setRefundTypeStr(Objects.isNull(refundOrder.getRefundMethod()) ? StringUtils.EMPTY : RefundTypeEnum.getValueByCode(refundOrder.getRefundMethod()));
            }
            refundInfo.setRefundNature(Objects.isNull(refundOrder.getRefundNature()) ? OrderRefundNatureEnum.ONLY_REFUND.getCode() : refundOrder.getRefundNature());
            refundInfo.setRefundNatureStr(OrderRefundNatureEnum.getValueByCode(refundOrder.getRefundNature()));
            refundInfo.setStatus(refundOrder.getStatus());
            refundInfo.setStatusStr(OmniRefundApproveEnum.getName(refundOrder.getStatus()));
            refundInfo.setApplyId(refundOrder.getApplyId());
            refundInfo.setApplyName(refundOrder.getApplyName());
            refundInfo.setCreatedAt(refundOrder.getCreatedAt());
            refundInfoList.add(refundInfo);
        });
        refundInfoList.sort(Comparator.comparing(IncomeAndRefundVO.RefundInfo::getCreatedAt).reversed());
        return refundInfoList;
    }

    private List<IncomeAndRefundVO.IncomeInfo> incomeInfoList(Integer orderId, String currency, Integer staffId, List<HeIncomeProofRecordEntity> incomeProofRecordByOrderIdList){

        UserEntity userEntity = userRepository.queryById(staffId);
        List<Integer> approvingIdList = CollectionUtils.isEmpty(incomeProofRecordByOrderIdList) ? Lists.newArrayList() : incomeProofRecordByOrderIdList.stream().filter(item -> OfflineAuditStatusV2Enum.UNDER_REVIEW.getCode().equals(item.getStatus())).map(HeIncomeProofRecordEntity::getIncomeId).collect(Collectors.toList());
        List<HeIncomeRecordEntity> incomeList = incomeRecordRepository.getAllRecordListByOrderId(orderId);
        //过滤掉减免支付的
        incomeList = incomeList.stream().filter(item -> !OmniPayTypeEnum.REDUCTION.getCode().equals(item.getPayType())).collect(Collectors.toList());
        List<HeUserProductionAmountPayLogEntity> heUserProductionAmountPayLogEntities = productionAmountPayRepository.querySuccessRecordListByOrderId(orderId);
        List<IncomeAndRefundVO.IncomeInfo> incomeInfoList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(incomeList)){
            incomeList.forEach(income -> {
                IncomeAndRefundVO.IncomeInfo incomeInfo = new IncomeAndRefundVO.IncomeInfo();
                incomeInfo.setId(income.getId());
                incomeInfo.setIncomeSn(income.getIncomeSn());
                incomeInfo.setCurrency(currency);
                incomeInfo.setPayType(income.getPayType());
                incomeInfo.setPayTypeStr(OmniPayTypeEnum.getName(income.getPayType()));
                incomeInfo.setApproveStatus(income.getApproveStatus());
                incomeInfo.setApproveStatusStr(OfflineAuditStatusV2Enum.getValueByCode(income.getApproveStatus()));
                incomeInfo.setStatus(income.getStatus());
                incomeInfo.setStatusStr(approvingIdList.contains(income.getId()) ? PayStatusEnum.PAY_STATUS_PROCESS.getValue() : PayStatusEnum.getValueByCode(incomeInfo.getStatus()));
                incomeInfo.setSubmitter(Objects.isNull(userEntity) ? StringUtils.EMPTY : userEntity.getName());
                incomeInfo.setPayTime(DateUtils.secondsToTimeStr(income.getPayTime()));
                incomeInfo.setIncome(convertBigDecimal2Str(currency, AmountChangeUtil.changeF2Y(income.getIncome())));
                incomeInfoList.add(incomeInfo);
            });
        }
        if (CollectionUtils.isEmpty(heUserProductionAmountPayLogEntities)){
            return incomeInfoList;
        }
        heUserProductionAmountPayLogEntities.forEach(productionPayEntity -> {
            IncomeAndRefundVO.IncomeInfo incomeInfo = new IncomeAndRefundVO.IncomeInfo();
            incomeInfo.setId(productionPayEntity.getId());
            incomeInfo.setCurrency(currency);
            incomeInfo.setIncome(convertBigDecimal2Str(currency, AmountChangeUtil.changeF2Y(productionPayEntity.getPayAmount())));
            incomeInfo.setPayType(OmniPayTypeEnum.PRODUCTION_COIN.getCode());
            incomeInfo.setPayTypeStr(OmniPayTypeEnum.PRODUCTION_COIN.getValue());
            incomeInfo.setApproveStatus(productionPayEntity.getStatus());
            incomeInfo.setApproveStatusStr("支付完成");
            incomeInfo.setStatus(productionPayEntity.getStatus());
            incomeInfo.setStatusStr(productionPayEntity.getStatus() == 1 ? "支付完成" : productionPayEntity.getStatus() == 2 ? "部分退款" : "已退完");
            UserEntity user = userRepository.queryById(productionPayEntity.getApplyUserId());
            incomeInfo.setSubmitter(Objects.isNull(user) ? StringUtils.EMPTY : user.getName());
            incomeInfo.setPayTime(DateUtils.secondsToTimeStr(productionPayEntity.getPayTime()));
            incomeInfoList.add(incomeInfo);
        });
        incomeInfoList.sort(Comparator.comparing(IncomeAndRefundVO.IncomeInfo::getPayTime).reversed());
        return incomeInfoList;
    }



    private BigDecimal getRefundProcessAmount(List<HeUserProductionAmountRefundLogEntity> heUserProductionAmountRefundLogEntities, List<HeOrderRefundEntity> refundByOrderIdList, Boolean isProcess){

        BigDecimal refundProcessAmount = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(heUserProductionAmountRefundLogEntities)){
            int sum = heUserProductionAmountRefundLogEntities.stream().filter(item ->Objects.nonNull(item.getStatus())).filter(item -> (isProcess ? 1: 4) == item.getStatus()).mapToInt(HeUserProductionAmountRefundLogEntity::getApplyAmount).sum();
            refundProcessAmount = AmountChangeUtil.changeF2Y(sum);
        }
        if (CollectionUtils.isNotEmpty(refundByOrderIdList)){
            int sum = refundByOrderIdList.stream().filter(item ->Objects.nonNull(item.getStatus()) && Objects.nonNull(item.getType()) && item.getType() != 2).filter(item -> isProcess ? 1 == item.getStatus() : Arrays.asList(3, 4).contains(item.getStatus()) ).mapToInt(HeOrderRefundEntity::getApplyAmount).sum();
            refundProcessAmount = refundProcessAmount.add(AmountChangeUtil.changeF2Y(sum));
        }
        return refundProcessAmount;
    }

    private BigDecimal getIncome(List<HeIncomeProofRecordEntity> incomeProofRecordByOrderIdList){

        if (CollectionUtils.isEmpty(incomeProofRecordByOrderIdList)){
            return null;
        }
        int incomeProofSum = incomeProofRecordByOrderIdList.stream().filter(item -> OfflineAuditStatusV2Enum.UNDER_REVIEW.getCode().equals(item.getStatus())).mapToInt(HeIncomeProofRecordEntity::getIncomeProof).sum();
        return AmountChangeUtil.changeF2Y(incomeProofSum);
    }

    public RefundInfoVO getRefundInfo(Integer refundId){

        Assert.isTrue(Objects.nonNull(refundId), "订单退款Id不能为空");
        HeOrderRefundEntity orderRefund = orderRefundRepository.getOneById(refundId);
        if (Objects.isNull(orderRefund)){
            return getProductionGoldRefundInfo(refundId);
        }
        RefundInfoVO refundInfoVO = new RefundInfoVO();
        refundInfoVO.setId(orderRefund.getId());
        refundInfoVO.setRefundOrderSn(orderRefund.getRefundOrderSn());
        refundInfoVO.setActualAmount(convertBigDecimal2Str(orderRefund.getCurrency(), AmountChangeUtil.changeF2Y(orderRefund.getActualAmount())));
        refundInfoVO.setRefundReason(orderRefund.getRefundReason());
        refundInfoVO.setRefundReasonStr(RefundReasonEnum.getName(orderRefund.getRefundReason()));
        refundInfoVO.setStatus(orderRefund.getStatus());
        refundInfoVO.setStatusStr(OmniRefundApproveEnum.getName(orderRefund.getStatus()));
        refundInfoVO.setRefundReasonType(orderRefund.getRefundReasonType());
        refundInfoVO.setRefundReasonTypeStr(orderRefund.getRefundReasonType() == 1 ? "正常退款" : "⾮正常退款（客诉）");
        refundInfoVO.setRefundType(orderRefund.getRefundMethod());
        refundInfoVO.setRefundTypeStr(RefundTypeEnum.getValueByCode(orderRefund.getRefundMethod()));
        refundInfoVO.setCreatedAt(DateUtil.formatDateTime(new Date(orderRefund.getCreatedAt() * 1000)));
        refundInfoVO.setRemark(orderRefund.getRemark());
        refundInfoVO.setApplyId(orderRefund.getApplyId());
        refundInfoVO.setApplyName(orderRefund.getApplyName());
        if (StringUtils.isNotEmpty(orderRefund.getRefundInfo())) {
            JSONObject refundInfo = JSONObject.parseObject(orderRefund.getRefundInfo());
            refundInfoVO.setName(refundInfo.getString("name"));
            refundInfoVO.setBankName(refundInfo.getString("bank_name"));
            refundInfoVO.setBankNumber(refundInfo.getString("bank_number"));
            String refundVoucher = refundInfo.getString("images");
            refundInfoVO.setRefundVoucherList(StringUtils.isEmpty(refundVoucher) ? null : JSONObject.parseArray(refundVoucher, String.class));
        }

        RefundInfoVO.RefundProcess refundProcess = new RefundInfoVO.RefundProcess();
        refundProcess.setId(orderRefund.getId());
        refundProcess.setRefundOrderSn(orderRefund.getRefundOrderSn());
        refundProcess.setActualAmount(convertBigDecimal2Str(orderRefund.getCurrency(), AmountChangeUtil.f2YDown(Objects.isNull(orderRefund.getActualAmount()) ? orderRefund.getApplyAmount() : orderRefund.getActualAmount())));
        refundProcess.setStatus(orderRefund.getStatus());
        Integer refundType = orderRefund.getRefundType();
        OmniPayTypeEnum omniPayTypeEnum = OmniPayTypeEnum.getByCode(refundType);
        switch (omniPayTypeEnum) {
            case ALIPAY:
            case WECHAT:
            case ONLINE_POS:
            case BALANCE:
                refundProcess.setStatusStr(OmniRefundApproveEnum.getName(orderRefund.getStatus()));
                break;
            case OFFLINE:
                refundProcess.setStatusStr(OmniRefundApproveEnum.getOfflineName(orderRefund.getStatus()));
                break;
        }
        refundProcess.setIncomeId(orderRefund.getOrderGoodId());
        refundProcess.setIncomeSn(orderRefund.getIncomeSn());
        refundProcess.setFinishAt(DateUtil.formatDateTime(new Date(orderRefund.getFinishAt() * 1000)));
        refundProcess.setCreatedAt(DateUtil.formatDateTime(new Date(orderRefund.getCreatedAt() * 1000)));
        refundInfoVO.setRefundProcessList(Collections.singletonList(refundProcess));
        return refundInfoVO;
    }

    public RefundInfoVO getProductionGoldRefundInfo(Integer refundId){

        HeUserProductionAmountRefundLogEntity productionRefundEntity = productionAmountRefundRepository.queryById(refundId);
        Assert.isTrue(Objects.nonNull(productionRefundEntity), "当前订单暂无退款记录");

        HeOrderEntity byOrderSn = orderRepository.queryOrderById(productionRefundEntity.getOrderId());

        RefundInfoVO refundInfoVO = new RefundInfoVO();
        refundInfoVO.setId(productionRefundEntity.getId());
        refundInfoVO.setActualAmount(convertBigDecimal2Str(byOrderSn.getCurrency(), AmountChangeUtil.changeF2Y(productionRefundEntity.getApplyAmount())));
        refundInfoVO.setRemark(productionRefundEntity.getRemark());
        refundInfoVO.setRefundVoucherList(getRefundVoucherList(productionRefundEntity.getRefundInfo()));
        refundInfoVO.setCreatedAt(DateUtil.formatDateTime(new Date(productionRefundEntity.getCreatedAt() * 1000)));
        refundInfoVO.setRefundType(productionRefundEntity.getRefundType());
        refundInfoVO.setRefundTypeStr(Objects.isNull(productionRefundEntity.getRefundType()) ? StringUtils.EMPTY : RefundTypeEnum.getValueByCode(productionRefundEntity.getRefundType()));
        refundInfoVO.setStatus(productionRefundEntity.getStatus());
        refundInfoVO.setStatusStr(OmniRefundApproveEnum.getName(productionRefundEntity.getStatus()));
        refundInfoVO.setApplyId(productionRefundEntity.getApplyUserId());
        UserEntity user = userRepository.queryById(productionRefundEntity.getApplyUserId());
        refundInfoVO.setApplyName(Objects.isNull(user) ? StringUtils.EMPTY : user.getName());

        RefundInfoVO.RefundProcess refundProcess = new RefundInfoVO.RefundProcess();
        refundProcess.setId(productionRefundEntity.getId());
        refundProcess.setActualAmount(refundInfoVO.getActualAmount());
        refundProcess.setStatus(refundInfoVO.getStatus());
        refundProcess.setStatusStr(refundInfoVO.getStatusStr());
        refundProcess.setIncomeId(productionRefundEntity.getPayId());
        refundProcess.setFinishAt(DateUtil.formatDateTime(new Date(productionRefundEntity.getFinishAt() * 1000)));
        refundProcess.setCreatedAt(DateUtil.formatDateTime(new Date(productionRefundEntity.getCreatedAt() * 1000)));
        refundInfoVO.setRefundProcessList(Collections.singletonList(refundProcess));
        return refundInfoVO;
    }


    public static List<String> getRefundVoucherList(String refundInfo){

        if (StringUtils.isEmpty(refundInfo)){
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(refundInfo);
        if (Objects.isNull(jsonObject)){
            return null;
        }
        String images = jsonObject.getString("images");
        if (StringUtils.isEmpty(images)){
            return null;
        }
        return JSON.parseArray(images, String.class);
    }

    private void fillStoreInfo(OrderInfoNewVO orderInfoNewVO, HeOrderEntity orderEntity) {


        orderInfoNewVO.setStoreId(orderEntity.getStoreId());
        if (Objects.isNull(orderEntity.getStore())) {
            return;
        }
        List<SelectRespVO> attribute = storeClient.getAttribute(dynamicConfig.getWarZone());
        Map<String, String> warZoneMap = attribute.stream().collect(Collectors.toMap(SelectRespVO::getValue, SelectRespVO::getLabel, (v1, v2) -> v1));
        orderInfoNewVO.setStoreName(orderEntity.getStore().getStoreName());
        orderInfoNewVO.setTypeName(this.getTypeName(orderEntity.getStore().getType(), orderEntity.getStore().getChildType()));
        orderInfoNewVO.setWarZone(orderEntity.getStore().getWarzone());
        orderInfoNewVO.setWarZoneDesc(warZoneMap.get(String.valueOf(orderInfoNewVO.getWarZone())));
    }

    private void fillOrderCustomerInfo(OrderInfoNewVO orderInfoNewVO, HeOrderEntity orderEntity) {


        orderInfoNewVO.setClientUid(orderEntity.getClientUid());
        TabClientEntity tabClientById = clientRepository.getTabClientById(orderEntity.getClientUid());
        if (Objects.isNull(tabClientById)) {
            return;
        }
        orderInfoNewVO.setCustomerName(tabClientById.getName());
        orderInfoNewVO.setCustomerMobile(tabClientById.getPhone());

    }

    private void fillOrderStaffAndCreateInfo(OrderInfoNewVO orderInfoNewVO, HeOrderEntity orderEntity) {

        orderInfoNewVO.setStaffId(orderEntity.getStaffId());
        orderInfoNewVO.setCreateBy(orderEntity.getCreateBy());
        if (Objects.isNull(orderInfoNewVO.getCreateBy()) || Objects.isNull(orderInfoNewVO.getStaffId())){
            return;
        }
        List<UserEntity> userEntityList = userRepository.queryUserByIdList(Arrays.asList(orderInfoNewVO.getStaffId(), orderInfoNewVO.getCreateBy().intValue()));
        if (CollectionUtils.isNotEmpty(userEntityList)) {
            userEntityList.stream()
                    .filter(item -> orderInfoNewVO.getStaffId().equals(item.getId()))
                    .findFirst().ifPresent(userEntity -> orderInfoNewVO.setStaffName(userEntity.getName()));
            userEntityList.stream()
                    .filter(item -> orderInfoNewVO.getCreateBy().intValue() == item.getId())
                    .findFirst().ifPresent(userEntity -> orderInfoNewVO.setCreateName(userEntity.getName()));
        }
    }

    private void fillOrderUserSnapshot(OrderInfoNewVO orderInfoNewVO, HeOrderEntity orderEntity) {

        HeOrderUserSnapshotEntity userEsign = orderUserSnapshotRepository.queryByOrderId(orderEntity.getOrderId());
        if (Objects.isNull(userEsign)) {
            return;
        }
        orderInfoNewVO.setPredictBornDate(DateUtils.FormatDate(userEsign.getPredictBornDate()));
        orderInfoNewVO.setFetusNum(userEsign.getFetusNum());
        orderInfoNewVO.setFetusNumStr(StayinProductionModeEnum.fromCode(userEsign.getFetusNum()));
        orderInfoNewVO.setWantIn(DateUtils.FormatDate(userEsign.getWantIn()));
        orderInfoNewVO.setWantInStr(DateUtils.FormatDate(userEsign.getWantIn()));
    }

    private void fillUrlList(OrderInfoNewVO orderInfoNewVO, HeOrderEntity orderEntity) {

        List<HeOrderVoucherEntity> orderVoucher = orderVoucherRepository.getByOrderId(orderEntity.getOrderId());
        orderInfoNewVO.setUrlList(CollectionUtils.isEmpty(orderVoucher) ? Lists.newArrayList() : orderVoucher.stream().map(HeOrderVoucherEntity::getUrl).collect(Collectors.toList()));

    }

    private String getPerformanceEffectiveDate(Integer percentFirstTime) {

        if (Objects.isNull(percentFirstTime) || percentFirstTime < 1) {
            return StringUtils.EMPTY;
        }
        return DateUtils.formatDateTime(new Date(percentFirstTime.longValue() * 1000));
    }

    private static String convertBigDecimal2Str(String currency, BigDecimal amount) {

        if (Objects.isNull(amount)) {
            return currency + "0.00";
        }
        return currency + amount.divide(BigDecimal.ONE, 2, BigDecimal.ROUND_UP);
    }


    private String getTypeName(Integer type, Integer childType) {

        if (StoreTypeEnum.SAINT_BELLA.getCode().equals(type)) {
            if (StoreChildTypeEnum.BELLA_VILLA.getCode().equals(childType)) {
                return StoreChildTypeEnum.BELLA_VILLA.getValue();
            }
            return StoreTypeEnum.SAINT_BELLA.getValue();
        }
        if (StoreTypeEnum.BABY_BELLA.getCode().equals(type)) {
            if (StoreChildTypeEnum.BABY_BELLA_DELUXE.getCode().equals(childType)) {
                return StoreChildTypeEnum.BABY_BELLA_DELUXE.getValue();
            }
            return StoreTypeEnum.BABY_BELLA.getValue();
        }
        return StoreTypeEnum.getValueByCode(type);
    }
}

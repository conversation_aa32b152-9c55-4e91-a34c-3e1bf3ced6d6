package com.stbella.order.server.manager;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.base.server.ding.MonthDingService;
import com.stbella.base.server.ding.response.CreateOrderApproveRecordVO;
import com.stbella.contract.api.ContractSignRecordService;
import com.stbella.contract.model.res.ContractSignRecordVO2;
import com.stbella.core.base.Operator;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.month.server.request.*;
import com.stbella.notice.enums.AuditTypeEnum;
import com.stbella.notice.server.OaProcessIdRelationService;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.month.ContractStatusEnum;
import com.stbella.order.common.enums.month.FromTypeEnum;
import com.stbella.order.common.enums.month.RefundReasonEnum;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.convert.MonthDingConvert;
import com.stbella.order.server.convert.OrderConvert;
import com.stbella.order.server.order.cts.enums.ApprovalTypeEnum;
import com.stbella.order.server.order.month.component.OrderIncomeAssembler;
import com.stbella.order.server.order.month.enums.ApprovalDiscountStatusEnum;
import com.stbella.order.server.order.month.enums.CreateApproveStatusEnum;
import com.stbella.order.server.order.month.enums.RefundTypeEnum;
import com.stbella.order.server.order.month.req.*;
import com.stbella.order.server.order.month.res.*;
import com.stbella.order.server.order.month.service.MonthOrderWxQueryService;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.platform.order.api.OrderCommandService;
import com.stbella.platform.order.api.reduction.req.DecreaseReq;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 月子钉钉钉钉审批服务
 * @date 2021/12/6 3:06 下午
 */
@Component
@Slf4j
public class MonthDingManager {
    @DubboReference(timeout = 60000)
    private MonthDingService monthDingService;
    @Resource
    private MonthOrderWxQueryService monthOrderWxQueryService;
    @Resource
    private OrderConvert orderConvert;
    @Resource
    private ContractSignRecordPaperRepository signRecordPaperRepository;
    @Resource
    private TabClientManager tabClientManager;
    @Resource
    private MonthDingConvert dingConvert;
    @Resource
    private StoreRepository storeRepository;
    @Resource
    private MonthContractSignRecordRepository contractSignRecordRepository;
    @Resource
    private MonthContractSignAgreementRepository monthContractSignAgreementRepository;
    @Resource
    private MonthOrderParamHistoryRepository monthOrderParamHistoryRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private OrderIncomeAssembler orderIncomeAssembler;
    @Resource
    private HeTaskRepository heTaskRepository;
    @Resource
    private HeTaskHisRepository heTaskHisRepository;
    @Resource
    private OrderCommandService orderCommandService;

    @Resource
    private OrderReductionRepository orderReductionRepository;

    @DubboReference
    private ContractSignRecordService contractSignRecordService;
    @DubboReference
    private OaProcessIdRelationService oaProcessIdRelationService;


    /**
     * 创建订单折扣审批
     *
     * @param orderId
     * @param phone
     * @return
     */
    public CreateOrderApproveRecordVO createOrderDiscountApproval(Integer orderId, String phone) {
        OrderInfoByOrderVO orderInfoByOrderVO = monthOrderWxQueryService.getOrderInfoByOrderId(orderId);
        HeOrderEntity byOrderId = orderRepository.getByOrderId(orderId);


        OrderMonthClientVO orderMonthClientVO = orderInfoByOrderVO.getOrderMonthClientVO();
        OrderInfoByGoodsVO orderInfoByGoodsVO = orderInfoByOrderVO.getOrderInfoByGoodsVO();
        OrderInfoByGiftExtendVO orderInfoByGiftExtendVO = orderInfoByOrderVO.getOrderInfoByGiftExtendVO();
        OrderInfoByOtherInfoVO orderInfoByOtherInfoVO = orderInfoByOrderVO.getOrderInfoByOtherInfoVO();
        OrderDiscountsCacheVO orderDiscountsCacheVO = orderInfoByOrderVO.getOrderDiscountsCacheVO();
        if (!orderDiscountsCacheVO.getApproval()) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "该订单不需要申请订单折扣审批,orderId=" + orderId);
        }
        OrderDiscountApprovalRequest orderDiscountApprovalRequest = new OrderDiscountApprovalRequest();

        orderDiscountApprovalRequest.setStoreName(orderMonthClientVO.getStoreName());
        orderDiscountApprovalRequest.setGoodsName(orderInfoByGoodsVO.getGoodsName());
        orderDiscountApprovalRequest.setGoodsAmount(orderDiscountsCacheVO.getAmountReceivablePackageCurrentPrice());
        orderDiscountApprovalRequest.setHolidayPrice(orderDiscountsCacheVO.getAmountReceivableHolidaysCurrentPrice());
        orderDiscountApprovalRequest.setContractAmount(orderDiscountsCacheVO.getOrderReceivableAmountCurrentPrice());
        orderDiscountApprovalRequest.setOrderGiftExtendList(orderConvert.orderInfoByGiftExtendSkuVOList2OrderGiftExtendRequest(orderInfoByGiftExtendVO.getOrderInfoByGiftExtendSkuVOList()));
        orderDiscountApprovalRequest.setRemark(orderInfoByOtherInfoVO.getRemark());
        orderDiscountApprovalRequest.setOrderTagName(orderInfoByOtherInfoVO.getOrderTagName());
        orderDiscountApprovalRequest.setVoucherUrlList(orderInfoByOtherInfoVO.getVoucherUrlList());
        orderDiscountApprovalRequest.setCustomerName(orderMonthClientVO.getName());
        orderDiscountApprovalRequest.setCustomerPhone(orderMonthClientVO.getPhone());
        orderDiscountApprovalRequest.setCustomerSource(FromTypeEnum.fromCode(orderMonthClientVO.getFromType()));
        orderDiscountApprovalRequest.setOrderId(orderId);
        orderDiscountApprovalRequest.setOrderDiscount(orderDiscountsCacheVO.getOrderDiscount());
        orderDiscountApprovalRequest.setNetDiscountRate(orderDiscountsCacheVO.getNetDiscountRate());
        orderDiscountApprovalRequest.setGrossProfitMargin(orderDiscountsCacheVO.getGrossProfitMargin());
        orderDiscountApprovalRequest.setPhone(phone);
        orderDiscountApprovalRequest.setOrderSn(byOrderId.getOrderSn());

        try {
            Result<CreateOrderApproveRecordVO> orderDiscountApproval = monthDingService.createOrderDiscountApproval(orderDiscountApprovalRequest, null);
            if (!orderDiscountApproval.getCode().equals(ResultEnum.SUCCESS.getCode())) {
                //发起审批失败
                byOrderId.setApprovalDiscountStatus(ApprovalDiscountStatusEnum.INITIATION_FAILED.getCode());
                orderRepository.updateOrderMonthByOrderId(byOrderId);
                throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), orderDiscountApproval.getMsg());
            }
            if (orderDiscountApproval.getData().getStatus().equals(1)) {
                //发起审批后改为审批中
                byOrderId.setApprovalDiscountStatus(ApprovalDiscountStatusEnum.APPROVING.getCode());
                orderRepository.updateOrderMonthByOrderId(byOrderId);
            } else {
                //发起审批失败
                byOrderId.setApprovalDiscountStatus(ApprovalDiscountStatusEnum.INITIATION_FAILED.getCode());
                orderRepository.updateOrderMonthByOrderId(byOrderId);
            }
            return orderDiscountApproval.getData();
        } catch (Exception e) {
            //发起审批失败
            byOrderId.setApprovalDiscountStatus(ApprovalDiscountStatusEnum.INITIATION_FAILED.getCode());
            orderRepository.updateOrderMonthByOrderId(byOrderId);
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), "调用审批平台失败，请联系技术人员");
        }
    }

    /**
     * 创建产康折扣审批
     *
     * @param req
     * @return
     */
    public CreateOrderApproveRecordVO createProductionDiscountApproval(OrderProductionDiscountApprovalReq req) {
        HeTaskEntity byTaskId = heTaskRepository.getByTaskId(req.getTaskId());
        HeOrderEntity byOrderId = orderRepository.queryOrderInfoByTaskId(req.getTaskId());
        CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(req.getStoreId());
        OrderMonthClientReq clientInfoById = tabClientManager.getClientInfoById(req.getClientId());
        OrderProductionDiscountApprovalRequest request = convertOrderProductionDiscountApprovalRequest(req, byOrderId, cfgStoreEntity, clientInfoById);
        try {
            Result<CreateOrderApproveRecordVO> productionDiscountApproval = monthDingService.createProductionDiscountApproval(request, null);
            return updateOrderAndTask(byOrderId, byTaskId, productionDiscountApproval);
        } catch (Exception e) {
            //发起审批失败
            byOrderId.setApprovalDiscountStatus(ApprovalDiscountStatusEnum.INITIATION_FAILED.getCode());
            orderRepository.updateOrderMonthByOrderId(byOrderId);
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), "调用审批平台失败，请联系技术人员");
        }
    }

    private OrderProductionDiscountApprovalRequest convertOrderProductionDiscountApprovalRequest(OrderProductionDiscountApprovalReq req, HeOrderEntity byOrderId, CfgStoreEntity cfgStoreEntity, OrderMonthClientReq clientInfoById) {
        OrderProductionDiscountApprovalRequest request = new OrderProductionDiscountApprovalRequest();
        if (ObjectUtil.isNotEmpty(cfgStoreEntity)) {
            request.setStoreName(cfgStoreEntity.getStoreName());
        }
        request.setGoodsName(req.getGoodsName());
        request.setOrderAmount(req.getOrderAmount());
        request.setPayAmount(req.getPayAmount());
        request.setDiscountAmount(req.getDiscountAmount());
        request.setDeductionAmount(req.getDeductionAmount());
        request.setDiscountOfferAmount(req.getDiscountOfferAmount());
        request.setDiscountMargin(req.getDiscountMargin());
        request.setNetMargin(req.getNetMargin());
        request.setGrossMargin(req.getGrossMargin());
        request.setRemark(req.getRemark());
        request.setCustomerName(req.getCustomerName());
        request.setCustomerPhone(req.getCustomerPhone());
        if (ObjectUtil.isNotEmpty(clientInfoById)) {
            request.setCustomerSource(FromTypeEnum.fromCode(clientInfoById.getFromType()));
        }
        if (ObjectUtil.isNotEmpty(byOrderId)) {
            request.setOrderSn(byOrderId.getOrderSn());
            request.setOrderId(byOrderId.getOrderId());
        }
        request.setPhone(req.getOperator().getOperatorPhone());
        return request;
    }


    private CreateOrderApproveRecordVO updateOrderAndTask(HeOrderEntity byOrderId, HeTaskEntity byTaskId, Result<CreateOrderApproveRecordVO> productionDiscountApproval) {
        //更新审批流ID
        String id = productionDiscountApproval.getData().getId();
        byTaskId.setDdInstanceId(id);
        heTaskRepository.updateByTaskId(byTaskId);
        //更新HeTaskHis表审批
        HeTaskHisEntity entity = new HeTaskHisEntity();
        entity.setTaskId(byTaskId.getId());
        entity.setFormTemplateType(byTaskId.getFormtemplateType());
        entity.setApprovalType(30);
        entity.setTaskName("产康订单折扣审批");
        entity.setInstanceId(id);
        heTaskHisRepository.insertOne(entity);

        if (!productionDiscountApproval.getCode().equals(ResultEnum.SUCCESS.getCode())) {
            //发起审批失败
            byOrderId.setApprovalDiscountStatus(ApprovalDiscountStatusEnum.INITIATION_FAILED.getCode());
            orderRepository.updateOrderMonthByOrderId(byOrderId);
            throw new BusinessException(ResultEnum.MIDDLEWARE_SERVICE_ERROR.getCode(), productionDiscountApproval.getMsg());
        }
        if (productionDiscountApproval.getData().getStatus().equals(1)) {
            //发起审批后改为审批中
            byOrderId.setApprovalDiscountStatus(ApprovalDiscountStatusEnum.APPROVING.getCode());
            orderRepository.updateOrderMonthByOrderId(byOrderId);
            byTaskId.setDdInstanceId(id);
        } else {
            //发起审批失败
            byOrderId.setApprovalDiscountStatus(ApprovalDiscountStatusEnum.INITIATION_FAILED.getCode());
            orderRepository.updateOrderMonthByOrderId(byOrderId);
        }
        return productionDiscountApproval.getData();
    }

    /**
     * 重新发起-产康折扣审批
     *
     * @param localInstanceId
     * @return
     */
    public CreateOrderApproveRecordVO relaunchProductionDiscountApproval(String localInstanceId) {
        HeTaskEntity byTaskId = heTaskRepository.getByDdInstanceId(localInstanceId);
        HeOrderEntity byOrderId = orderRepository.queryOrderInfoByTaskId(byTaskId.getId());
        try {
            //重新发起要删除原先的审批
            heTaskHisRepository.deleteByInstanceId(localInstanceId);
            Result<CreateOrderApproveRecordVO> productionDiscountApproval = monthDingService.relaunchApprove(localInstanceId);
            CreateOrderApproveRecordVO createOrderApproveRecordVO = updateOrderAndTask(byOrderId, byTaskId, productionDiscountApproval);
            //如果发起失败特殊处理
            if (createOrderApproveRecordVO.getStatus() == 2) {
                throw new BusinessException(ResultEnum.MIDDLEWARE_SERVICE_ERROR.getCode(), productionDiscountApproval.getMsg());
            }
            return createOrderApproveRecordVO;
        } catch (Exception e) {
            //发起审批失败
            byOrderId.setApprovalDiscountStatus(ApprovalDiscountStatusEnum.INITIATION_FAILED.getCode());
            orderRepository.updateOrderMonthByOrderId(byOrderId);
            throw new BusinessException(ResultEnum.MIDDLEWARE_SERVICE_ERROR.getCode(), "调用审批平台失败，请联系技术人员");
        }
    }

    /**
     * 创建退款审批（原路退回）
     *
     * @param submitRefundApplyV2Request
     * @return
     */
    public CreateOrderApproveRecordVO createRefundApprovalOriginalRoad(SubmitRefundApplyV2Request submitRefundApplyV2Request, Integer orderRefundId, HeOrderEntity heOrderEntity) {
        RefundApprovalOriginalRoadRequest request = getRefundApprovalOriginalRoadRequest(submitRefundApplyV2Request, heOrderEntity);
        request.setOrderSn(heOrderEntity.getOrderSn());
        request.setContractAmount(new BigDecimal(heOrderEntity.getPayAmount()).divide(new BigDecimal(100)).setScale(2));
        request.setOrderRefundId(orderRefundId);
        request.setPhotoCertificateListFileIdList(submitRefundApplyV2Request.getImagesFileIds());
        Result<CreateOrderApproveRecordVO> refundApprovalOriginalRoad = monthDingService.createRefundApprovalOriginalRoad(request, null);
        if (!refundApprovalOriginalRoad.getSuccess()) {
            log.info("原路退回审批发起失败,msg={}", refundApprovalOriginalRoad.getMsg());
        }
        return refundApprovalOriginalRoad.getData();
    }

    public RefundApprovalOriginalRoadRequest createRefundApprovalOriginalRoadParam(SubmitRefundApplyV2Request submitRefundApplyV2Request, Integer orderRefundId, HeOrderEntity heOrderEntity) {
        RefundApprovalOriginalRoadRequest request = getRefundApprovalOriginalRoadRequest(submitRefundApplyV2Request, heOrderEntity);
        request.setOrderSn(heOrderEntity.getOrderSn());
        request.setContractAmount(new BigDecimal(heOrderEntity.getPayAmount()).divide(new BigDecimal(100)).setScale(2));
        request.setOrderRefundId(orderRefundId);
        request.setPhotoCertificateListFileIdList(submitRefundApplyV2Request.getImagesFileIds());
        return request;
    }

    public CreateOrderApproveRecordVO createBalanceRefundApproval(SubmitRefundApplyV2Request submitRefundApplyV2Request, HeOrderEntity heOrderEntity, Integer orderRefundId) {
        RefundApprovalRequest refundApprovalReq = getRefundApprovalReq(submitRefundApplyV2Request, heOrderEntity, orderRefundId, RefundTypeEnum.BACK_ACCOUNT);
        Result<CreateOrderApproveRecordVO> refundApprovalOriginalRoad = monthDingService.createBalanceRefundApproval(refundApprovalReq);
        if (!refundApprovalOriginalRoad.getSuccess()) {
            log.info("意向金退回审批发起失败,msg={}", refundApprovalOriginalRoad.getMsg());
        }
        return refundApprovalOriginalRoad.getData();
    }

    public RefundApprovalRequest createBalanceRefundApprovalParam(SubmitRefundApplyV2Request submitRefundApplyV2Request, HeOrderEntity heOrderEntity, Integer orderRefundId) {
        return getRefundApprovalReq(submitRefundApplyV2Request, heOrderEntity, orderRefundId, RefundTypeEnum.BACK_ACCOUNT);
    }

    public CreateOrderApproveRecordVO createProductionRefundApproval(SubmitRefundApplyV2Request submitRefundApplyV2Request, HeOrderEntity heOrderEntity, Integer orderRefundId) {
        RefundApprovalRequest refundApprovalReq = getRefundApprovalReq(submitRefundApplyV2Request, heOrderEntity, orderRefundId, RefundTypeEnum.BACK_TRACK);
        Result<CreateOrderApproveRecordVO> refundApprovalOriginalRoad = monthDingService.createProductionRefundApproval(refundApprovalReq);
        if (!refundApprovalOriginalRoad.getSuccess()) {
            log.info("产康金退回审批发起失败,msg={}", refundApprovalOriginalRoad.getMsg());
        }
        return refundApprovalOriginalRoad.getData();
    }


    public RefundApprovalRequest createProductionRefundApprovalParam(SubmitRefundApplyV2Request submitRefundApplyV2Request, HeOrderEntity heOrderEntity, Integer orderRefundId) {
        return getRefundApprovalReq(submitRefundApplyV2Request, heOrderEntity, orderRefundId, RefundTypeEnum.BACK_TRACK);
    }

    public CreateOrderApproveRecordVO createDepositBalanceRefundApproval(SubmitDepositRefundV2Request submitRefundApplyV2Request, Long basicUid, Integer orderRefundId) {
        DepositRefundApprovalRequest refundApprovalReq = getDepositRefundApprovalRequest(submitRefundApplyV2Request, basicUid, orderRefundId);
        Result<CreateOrderApproveRecordVO> refundApproval = monthDingService.createDepositRefundApproval(refundApprovalReq);
        if (!refundApproval.getSuccess()) {
            log.info("意向金退回审批发起失败,msg={}", refundApproval.getMsg());
        }
        return refundApproval.getData();
    }

    /**
     * 创建退款审批（线下汇款）
     *
     * @param submitRefundApplyV2Request
     * @param heOrderEntity
     * @return
     */
    public CreateOrderApproveRecordVO createRefundApprovalOfflineRemittance(SubmitRefundApplyV2Request submitRefundApplyV2Request, Integer orderRefundId, HeOrderEntity heOrderEntity) {
        RefundApprovalOfflineRemittanceRequest request = getRefundApprovalOfflineRemittanceRequest(submitRefundApplyV2Request, heOrderEntity);
        request.setOrderSn(heOrderEntity.getOrderSn());
        request.setContractAmount(new BigDecimal(heOrderEntity.getPayAmount()).divide(new BigDecimal(100)).setScale(2));
        request.setPhotoCertificateListFileIdList(submitRefundApplyV2Request.getImagesFileIds());
        request.setOrderRefundId(orderRefundId);
        Result<CreateOrderApproveRecordVO> refundApprovalOfflineRemittance = monthDingService.createRefundApprovalOfflineRemittance(request, null);
        if (!refundApprovalOfflineRemittance.getSuccess()) {
            log.info("线下汇款审批发起失败,msg={}", refundApprovalOfflineRemittance.getMsg());
        }
        return refundApprovalOfflineRemittance.getData();
    }

    public RefundApprovalOfflineRemittanceRequest createRefundApprovalOfflineRemittanceParam(SubmitRefundApplyV2Request submitRefundApplyV2Request, Integer orderRefundId, HeOrderEntity heOrderEntity) {
        RefundApprovalOfflineRemittanceRequest request = getRefundApprovalOfflineRemittanceRequest(submitRefundApplyV2Request, heOrderEntity);
        request.setOrderSn(heOrderEntity.getOrderSn());
        request.setContractAmount(new BigDecimal(heOrderEntity.getPayAmount()).divide(new BigDecimal(100)).setScale(2));
        request.setPhotoCertificateListFileIdList(submitRefundApplyV2Request.getImagesFileIds());
        request.setOrderRefundId(orderRefundId);
        return request;
    }


    private RefundApprovalOriginalRoadRequest getRefundApprovalOriginalRoadRequest(SubmitRefundApplyV2Request submitRefundApplyV2Request, HeOrderEntity heOrderEntity) {
        RefundApprovalOriginalRoadRequest request = new RefundApprovalOriginalRoadRequest();
        request.setRefundAmount(submitRefundApplyV2Request.getRefundAmount().setScale(2));
        Integer refundReasonType = submitRefundApplyV2Request.getRefundReasonType();
        request.setRefundType(refundReasonType == 1 ? "正常退款" : "⾮正常退款（客诉）");
        request.setRefundReason(RefundReasonEnum.getName(submitRefundApplyV2Request.getRefundReason()));
        request.setSpecificReason(submitRefundApplyV2Request.getRemark());
        request.setLiquidatedDamages(submitRefundApplyV2Request.getHasLiquidatedDamages().equals(1));
        request.setPhotoCertificateList(submitRefundApplyV2Request.getImages());
        request.setStoreName(submitRefundApplyV2Request.getStoreName());
        request.setCustomerName(submitRefundApplyV2Request.getClientName());
        request.setCustomerPhone(submitRefundApplyV2Request.getClientPhone());
        request.setOrderName(submitRefundApplyV2Request.getGoodsName());
        request.setContractAmount(submitRefundApplyV2Request.getContractAmount());
        request.setPaidAmount(RMBUtils.bigDecimalF2Y(OmniOrderTypeEnum.MONTH_ORDER.getCode().equals(heOrderEntity.getOrderType()) ? heOrderEntity.getRealAmount() : heOrderEntity.getPaidAmount()).setScale(2));
        request.setOrderId(submitRefundApplyV2Request.getOrderId());
        request.setPhone(submitRefundApplyV2Request.getOperator().getOperatorPhone());
        request.setOrderType(heOrderEntity.getOrderType().equals(OmniOrderTypeEnum.PRODUCTION_ORDER.getCode()) ? OmniOrderTypeEnum.PRODUCTION_ORDER.getDesc() : OmniOrderTypeEnum.MONTH_ORDER.getDesc());
        return request;
    }

    private RefundApprovalOfflineRemittanceRequest getRefundApprovalOfflineRemittanceRequest(SubmitRefundApplyV2Request submitRefundApplyV2Request, HeOrderEntity heOrderEntity) {
        RefundApprovalOfflineRemittanceRequest request = new RefundApprovalOfflineRemittanceRequest();
        request.setRefundAmount(submitRefundApplyV2Request.getRefundAmount().setScale(2));
        Integer refundReasonType = submitRefundApplyV2Request.getRefundReasonType();
        request.setRefundType(refundReasonType == 1 ? "正常退款" : "⾮正常退款（客诉）");
        request.setRefundReason(RefundReasonEnum.getName(submitRefundApplyV2Request.getRefundReason()));
        request.setSpecificReason(submitRefundApplyV2Request.getRemark());
        request.setLiquidatedDamages(submitRefundApplyV2Request.getHasLiquidatedDamages().equals(1));
        request.setPhotoCertificateList(submitRefundApplyV2Request.getImages());
        request.setStoreName(submitRefundApplyV2Request.getStoreName());
        request.setCustomerName(submitRefundApplyV2Request.getClientName());
        request.setCustomerPhone(submitRefundApplyV2Request.getClientPhone());
        request.setOrderName(submitRefundApplyV2Request.getGoodsName());
        request.setContractAmount(submitRefundApplyV2Request.getContractAmount());
        request.setPaidAmount(RMBUtils.bigDecimalF2Y(OmniOrderTypeEnum.MONTH_ORDER.getCode().equals(heOrderEntity.getOrderType()) ? heOrderEntity.getRealAmount() : heOrderEntity.getPaidAmount()).setScale(2));
        request.setOrderId(submitRefundApplyV2Request.getOrderId());
        request.setPhone(submitRefundApplyV2Request.getOperator().getOperatorPhone());
        request.setOrderType(heOrderEntity.getOrderType().equals(OmniOrderTypeEnum.PRODUCTION_ORDER.getCode()) ? OmniOrderTypeEnum.PRODUCTION_ORDER.getDesc() : OmniOrderTypeEnum.MONTH_ORDER.getDesc());
        return request;
    }


    private RefundApprovalRequest getRefundApprovalReq(SubmitRefundApplyV2Request req, HeOrderEntity orderEntity, Integer orderRefundId, RefundTypeEnum refundType) {
        RefundApprovalRequest res = new RefundApprovalRequest();
        res.setRefundAmount(req.getRefundAmount());
        res.setOrderSn(orderEntity.getOrderSn());
        res.setRefundType(req.getRefundReasonType() == 1 ? "正常退款" : "⾮正常退款（客诉）");
        res.setRefundReason(RefundReasonEnum.getName(req.getRefundReason()));
        res.setStoreName(req.getStoreName());
        res.setCustomerName(req.getClientName());
        res.setCustomerPhone(req.getPhone());
        res.setContractAmount(AmountChangeUtil.f2YScale2(orderEntity.getPayAmount()));
        res.setPaidAmount(AmountChangeUtil.f2YScale2(OmniOrderTypeEnum.MONTH_ORDER.getCode().equals(orderEntity.getOrderType()) ? orderEntity.getRealAmount() : orderEntity.getPaidAmount()));
        res.setOrderId(req.getOrderId());
        res.setPhone(req.getOperator().getOperatorPhone());
        res.setRefundMethod(refundType.getValue());
        res.setOrderRefundId(orderRefundId);
        res.setRefundMethodId(refundType.getCode());
        res.setImagesList(req.getImages());
        res.setRemark(req.getRemark());
        res.setRefundGoodsInfo(req.getGoodsName());
        if (Objects.nonNull(req.getProductionPayId())){
            res.setProductionPayId(req.getProductionPayId());
            res.setProductionRefundId(req.getProductionRefundId());
        }
        if (RefundTypeEnum.BACK_TRACK.equals(refundType)){
            res.setOperatorName(req.getOperator().getOperatorName());
            res.setOperatorGuid(req.getOperator().getOperatorGuid());
            res.setOperatorPhone(req.getOperator().getOperatorPhone());
        }
        res.setOrderType(orderEntity.getOrderType().equals(OmniOrderTypeEnum.PRODUCTION_ORDER.getCode()) ? OmniOrderTypeEnum.PRODUCTION_ORDER.getDesc() : OmniOrderTypeEnum.MONTH_ORDER.getDesc());
        return res;
    }

    //押金退款
    private DepositRefundApprovalRequest getDepositRefundApprovalRequest(SubmitDepositRefundV2Request req, Long basicUid, Integer orderRefundId) {
        DepositRefundApprovalRequest request = new DepositRefundApprovalRequest();
        request.setBasicUid(basicUid);
        request.setCustomerName(req.getClientName());
        request.setStoreName(req.getStoreName());
        request.setCustomerPhone(req.getPhone());
        request.setTotalDepositCollection(req.getMaxRefund());
        request.setAmountRefundApplied(req.getRefundAmount());
        request.setRefundReason(RefundReasonEnum.REFUND_OF_DEPOSIT.getValue());
        request.setOrderRefundId(orderRefundId);
        request.setId(orderRefundId);
        request.setPhone(req.getOperator().getOperatorPhone());
        request.setOrderId(0);
        request.setImagesFileIds(req.getImages());
        request.setImages(req.getImages());
        request.setRemark(req.getRemark());
        return request;
    }

    //合同修改审批
    public CreateOrderApproveRecordVO createContractModificationApproval(MonthDingReq monthDingReq) {
        //修改后的内容
        String changeContent = compareOrderParamsHistory(monthDingReq.getOrderId());
        if (StringUtils.isBlank(changeContent)) {
            //无需审批
            return null;
        }
        return createContractModificationApproval(monthDingReq, changeContent);
    }


    //合同修改审批
    public CreateOrderApproveRecordVO createContractModificationApproval(MonthDingReq monthDingReq, String content) {
        HeOrderEntity orderEntity = orderRepository.queryOrderById(monthDingReq.getOrderId());
        Optional.ofNullable(orderEntity).orElseThrow(() -> new BusinessException(ResultEnum.PARAM_ERROR, "订单信息不存在"));

        ContractSignRecordVO2 contractSignRecordVO2 = contractSignRecordService.getByOrderId(monthDingReq.getOrderId(), TemplateContractTypeEnum.YZ_SAINTBELLA.code());

        OrderMonthClientReq clientInfo = tabClientManager.getClientInfoById(orderEntity.getClientUid());
        CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(clientInfo.getStoreId());
        ContractModificationApprovalRequest modificationApprovalRequest = dingConvert.contractWithClient2Approval(clientInfo, cfgStoreEntity, contractSignRecordVO2);
        String operatorPhone = monthDingReq.getOperator().getOperatorPhone();
        //取操作人的，下这样处理，如果没有的话，用
        if (StringUtil.isNotBlank(operatorPhone)) {
            modificationApprovalRequest.setPhone(operatorPhone);
        }
        modificationApprovalRequest.setIsReduceDiscount(monthDingReq.getIsReduceDiscount());
        //补充协议地址
        modificationApprovalRequest.setSupplementalAgreementAddressList(getContractShortUrl(monthDingReq.getOrderId()));
        //修改后的内容
        modificationApprovalRequest.setOrderId(monthDingReq.getOrderId());
        modificationApprovalRequest.setChangeContent(content);
        log.info("订单合同修改审批{}", JSONUtil.toJsonStr(modificationApprovalRequest));

        try {
            Result<CreateOrderApproveRecordVO> modificationApproval = monthDingService.createContractModificationApproval(modificationApprovalRequest, null);
            log.info("订单合同修改审批响应 = {}", JSONUtil.toJsonStr(modificationApproval));

            if (!modificationApproval.getCode().equals(ResultEnum.SUCCESS.getCode())) {
                throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), modificationApproval.getMsg());
            }
            return modificationApproval.getData();
        } catch (Exception e) {
            log.error("发起订单合同修改审批异常：" + e.getMessage());
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), "调用审批平台失败，请联系技术人员");
        }
    }

    /**
     * 创建纸质合同审批
     *
     * @param dingContractPaperReq
     * @return {@link Long}
     */
    public CreateOrderApproveRecordVO createPaperContractApproval(MonthDingContractPaperReq dingContractPaperReq) {
        ContractSignRecordVO2 contractSignRecordEntity = contractSignRecordService.getByContractSignId(Long.valueOf(dingContractPaperReq.getContractId()));
        ContractSignRecordVO2 monthContractSignRecordEntity = contractSignRecordService.getByOrderId(Math.toIntExact(contractSignRecordEntity.getGuideId()), TemplateContractTypeEnum.YZ_SAINTBELLA.code());

        if (Objects.isNull(monthContractSignRecordEntity) || !Objects.equals(monthContractSignRecordEntity.getContractStatus(), ContractStatusEnum.SIGNED.code())) {
            monthContractSignRecordEntity = null;
        }

        //已签署补充协议
        OrderMonthClientReq clientInfo = tabClientManager.getClientInfoById(contractSignRecordEntity.getClientUid());
        CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(clientInfo.getStoreId());
        PaperContractApprovalRequest contractApprovalRequest = dingConvert.paperWithClient2Approval(contractSignRecordEntity, clientInfo, cfgStoreEntity, monthContractSignRecordEntity);
        contractApprovalRequest.setOrderId(contractSignRecordEntity.getGuideId().intValue());
        contractApprovalRequest.setPhone(dingContractPaperReq.getOperator().getOperatorPhone());
        //补充协议地址
        contractApprovalRequest.setSupplementalAgreementAddressList(getContractShortUrl(Math.toIntExact(contractSignRecordEntity.getGuideId())));
        if (Objects.nonNull(contractSignRecordEntity.getGuideId()) && Objects.isNull(contractApprovalRequest.getOrderId())) {
            HeOrderEntity byOrderId = orderRepository.getByOrderId(contractSignRecordEntity.getGuideId().intValue());
            if (Objects.nonNull(byOrderId) && byOrderId.getVersion().compareTo(new BigDecimal(3)) >= 0) {
                contractApprovalRequest.setOrderId(byOrderId.getOrderId());
            }
        }

        log.info("纸质合同审批{}", JSONUtil.toJsonStr(contractApprovalRequest));

        if (CollectionUtils.isNotEmpty(contractApprovalRequest.getContractContentsList()) && contractApprovalRequest.getContractContentsList().size() > 6) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "上传的图片数量最多6张");
        }

        try {
            Result<CreateOrderApproveRecordVO> paperContractApproval = monthDingService.createPaperContractApproval(contractApprovalRequest, null);
            log.info("纸质合同审批响应 = {}", JSONUtil.toJsonStr(paperContractApproval));
            if (!paperContractApproval.getCode().equals(ResultEnum.SUCCESS.getCode())) {
                throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), paperContractApproval.getMsg());
            }
            return paperContractApproval.getData();
        } catch (Exception e) {
            log.error("发起纸质合同审批异常：" + e.getMessage());
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), "调用审批平台失败，请联系技术人员");
        }


    }

    //处理获取补充协议地址
    private List<String> getContractShortUrl(Integer orderId) {
        List<MonthContractSignAgreementEntity> agreementEntities = monthContractSignAgreementRepository.list(orderId.longValue(), ContractStatusEnum.SIGNED.code());
        return agreementEntities.stream().map(MonthContractSignAgreementEntity::getContractShortUrl).filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

    /**
     * 对比最近2次订单历史参数变更记录
     *
     * @param orderId
     * @return {@link String}
     */
    private String compareOrderParamsHistory(Integer orderId) {
        List<MonthOrderParamHistoryEntity> list = monthOrderParamHistoryRepository.list(orderId.longValue(), null, null, Arrays.asList(1, 2));
        Map<Date, List<MonthOrderParamHistoryEntity>> listMap = list.stream().collect(Collectors.groupingBy(MonthOrderParamHistoryEntity::getGmtCreate, LinkedHashMap::new, Collectors.toList()));
        List<MonthOrderParamHistoryEntity> firstList = Lists.newArrayList();
        List<MonthOrderParamHistoryEntity> twoList = Lists.newArrayList();
        //只循环2次
        int i = 0;
        for (Map.Entry<Date, List<MonthOrderParamHistoryEntity>> entry : listMap.entrySet()) {
            if (i == 2) {
                break;
            }
            if (i == 0) {
                firstList = entry.getValue();
            }
            if (i == 1) {
                twoList = entry.getValue();
            }
            i++;
        }
        int j = 1;
        StringBuilder stringBuilder = new StringBuilder();
        for (MonthOrderParamHistoryEntity firstOrderParamHistoryEntity : firstList) {
            for (MonthOrderParamHistoryEntity twoOrderParamHistoryEntity : twoList) {
                if (firstOrderParamHistoryEntity.getMark().equals(twoOrderParamHistoryEntity.getMark()) && !firstOrderParamHistoryEntity.getParamValue().equals(twoOrderParamHistoryEntity.getParamValue())) {
                    if ("remark".equals(twoOrderParamHistoryEntity.getMark())) {
                        //截取特殊事项备注字段值
                        String oldValue = twoOrderParamHistoryEntity.getParamValue().substring(twoOrderParamHistoryEntity.getParamValue().indexOf(BizConstant.REMARK + "：\n\n") + 9);
                        String nowValue = firstOrderParamHistoryEntity.getParamValue().substring(firstOrderParamHistoryEntity.getParamValue().indexOf(BizConstant.REMARK + "：\n\n") + 9);
                        if (!oldValue.equals(nowValue)) {
                            stringBuilder.append(j + ".").append(firstOrderParamHistoryEntity.getName()).append(" 由").append(oldValue).append("改为了").append(nowValue).append("\n");
                            j++;
                        }
                    } else {
                        stringBuilder.append(j + ".").append(firstOrderParamHistoryEntity.getName()).append(" 由").append(twoOrderParamHistoryEntity.getParamValue()).append("改为了").append(firstOrderParamHistoryEntity.getParamValue()).append("\n");
                        j++;
                    }
                }
            }
        }
        return StringUtils.isNotBlank(stringBuilder) ? stringBuilder.substring(0, stringBuilder.lastIndexOf("\n")) : "";
    }


    /**
     * 押金退款审批
     *
     * @param request
     * @param orderRefundId
     * @return {@link CreateOrderApproveRecordVO}
     */
    public CreateOrderApproveRecordVO createDepositApproval(SubmitDepositRefundV2Request request, Integer orderRefundId) {
        //押金退款的审批字段：客户姓名、所属门店、手机号、押金收款总额、申请退款金额 、退款原因
        HeOrderRefundEntity heOrderRefundEntity = orderRefundRepository.getOneById(orderRefundId);
        if (ObjectUtil.isNull(heOrderRefundEntity)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "申请退款记录不存在");
        }
        OrderMonthClientReq clientInfo = tabClientManager.getClientInfoById(request.getClientUid());
        CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(request.getStoreId());
//        MonthOrderStatisticsIncomeVO statisticsIncomeVO = orderIncomeAssembler.queryDepositAssembler(new OrderMonthIncomeQuery().setClientId(orderEntity.getClientUid()));
        DepositRefundApprovalRequest depositRefundApprovalRequest = dingConvert.refundWithClient2Approval(clientInfo, cfgStoreEntity, heOrderRefundEntity);
        depositRefundApprovalRequest.setImagesFileIds(Optional.ofNullable(request.getImagesFileIds()).orElse(new ArrayList<>()));
        depositRefundApprovalRequest.setImages(Optional.ofNullable(request.getImages()).orElse(new ArrayList<>()));
        //操作人手机号
        depositRefundApprovalRequest.setPhone(request.getOperator().getOperatorPhone());
        //押金收款总额
//        BigDecimal receivableAmount = BigDecimalUtil.subtract(statisticsIncomeVO.getTotalIncome(), statisticsIncomeVO.getDepositRefund());
        depositRefundApprovalRequest.setTotalDepositCollection(new BigDecimal(request.getMaxRefund().toString()));
        depositRefundApprovalRequest.setCustomerName(request.getClientName());
        try {
            log.info("押金退款审批请求参数 = {}", JSONUtil.toJsonStr(depositRefundApprovalRequest));
            Result<CreateOrderApproveRecordVO> orderApproveRecordVOResult = monthDingService.createDepositRefundApproval(depositRefundApprovalRequest, null);
            log.info("押金退款审批响应 = {}", JSONUtil.toJsonStr(depositRefundApprovalRequest));
            if (!orderApproveRecordVOResult.getCode().equals(ResultEnum.SUCCESS.getCode())) {
                throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), orderApproveRecordVOResult.getMsg());
            }
            return orderApproveRecordVOResult.getData();
        } catch (Exception e) {
            log.error("发起押金退款审批审批异常：" + e.getMessage());
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), "调用审批平台失败，请联系技术人员");
        }


    }


    /**
     * 更新订单折扣审批状态
     */
    public Result<Integer> updateOrderDiscountStatus(Integer orderId, Integer discountStatus) {
        HeOrderEntity byOrderId = orderRepository.getByOrderId(orderId);
        if (ObjectUtil.isEmpty(byOrderId)) {
            log.error("更新订单折扣审批状态,订单ID不存在={}", orderId);
            return Result.success();
        }
        byOrderId.setApprovalDiscountStatus(discountStatus);
        orderRepository.updateOrderMonthByOrderId(byOrderId);
        return Result.success(orderId);
    }


    public Result<CreateOrderApproveRecordVO> discountRelaunchApprove(String id, String phone) {
        CreateOrderApproveRecordVO createOrderApproveRecordVO = new CreateOrderApproveRecordVO();
        //判断一下是不是减免审批
        OrderReductionEntity orderReductionEntity = orderReductionRepository.selectByLocalProcessId(id);
        if (orderReductionEntity == null) {
            log.error("减免审批发起失败，审批id {}被删除", id);
            return Result.success();
        }
        //对于减免审批的重新发起应该另外处理
        DecreaseReq req = new DecreaseReq();
        req.setOrderId(orderReductionEntity.getOrderId());
        req.setDecreaseAmount(AmountChangeUtil.changeF2Y(orderReductionEntity.getDecreaseAmount()));
        req.setReason(orderReductionEntity.getReason());
        req.setOperator(Operator.of(orderReductionEntity.getOperator()));
        req.getOperator().setOperatorPhone(phone);
        log.info("减免重新发起审批的提交人手机号码为[{}]", phone);
        Result reduction = orderCommandService.reduction(req);
        if (reduction.getSuccess()) {
            createOrderApproveRecordVO.setStatus(CreateApproveStatusEnum.SUCCESS.getCode());
            createOrderApproveRecordVO.setTitle(ApprovalTypeEnum.DECREASE.getValue() + "发起成功");
        } else {
            createOrderApproveRecordVO.setStatus(CreateApproveStatusEnum.FAILED.getCode());
            createOrderApproveRecordVO.setTitle(ApprovalTypeEnum.DECREASE.getValue() + "发起失败");
        }
        return Result.success(createOrderApproveRecordVO);

    }

    public CreateOrderApproveRecordVO createApproval(HashMap<String, String> param, String phone, Integer oaProcessConfig){

        try {
            log.info("发起审批请求参数param：{} phone：{} oaProcessConfig：{}", JSONUtil.toJsonStr(param), phone, oaProcessConfig);
            Result<CreateOrderApproveRecordVO> approveResult = monthDingService.createApprovalV2(param, phone, oaProcessConfig);
            if (approveResult.getSuccess()){
                log.info("发起审批成功，审批记录：{}", approveResult.getData());
                return approveResult.getData();
            }
            log.error("发起审批失败，原因：{}", approveResult.getMsg());
            return null;
        } catch (Exception e) {
            log.error("发起审批异常", e);
            return null;
        }
    }
}

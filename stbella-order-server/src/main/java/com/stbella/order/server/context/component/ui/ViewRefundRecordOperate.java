package com.stbella.order.server.context.component.ui;

import com.stbella.order.common.enums.month.OrderButtonEnum;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.server.order.month.res.OrderOperateButton;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-08-12  16:28
 * @Description: 查看退款记录
 */
@Component
@Slf4j
public class ViewRefundRecordOperate implements OrderOperate{


    @Override
    public void setPriority(int priority) {

    }

    @Override
    public int getPriority() {
        return 10;
    }

    /**
     * 生成订单操作按钮
     *
     * @param order
     * @return
     */
    @Override
    public OrderOperateButton generateButton(OrderOperateContext context) {
        // 退款记录
        List<HeOrderRefundEntity> refundList = context.getRefundList();
        if (CollectionUtils.isNotEmpty(refundList)) {
            return OrderOperateButton.builder().code(getButtonEnum().getCode()).value("退款记录").build();
        }
        return null;
    }

    /**
     * 获取按钮枚举
     *
     * @return
     */
    @Override
    public OrderButtonEnum getButtonEnum() {
        return OrderButtonEnum.LIST_REFUND;
    }

    @Override
    public int compareTo(@NotNull IPriority o) {
        return this.getPriority() - o.getPriority();
    }
}

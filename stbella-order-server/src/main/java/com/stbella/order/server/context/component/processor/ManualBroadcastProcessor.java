package com.stbella.order.server.context.component.processor;

import com.stbella.core.result.Result;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderNoticeLogEntity;
import com.stbella.order.domain.repository.OrderNoticeLogRepository;
import com.stbella.order.server.order.cts.enums.ClientTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

/**
 * 手动报单组件
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "手动报单组件")
public class ManualBroadcastProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    OrderNoticeLogRepository orderNoticeLogRepository;

    @Override
    public boolean condition(FlowContext bizContext) {
        Result attribute = bizContext.getAttribute(Result.class);
        return attribute != null && attribute.getSuccess();
    }

    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        log.info("订单手动报单，订单号：{}", orderEntity.getOrderSn());

        HeOrderNoticeLogEntity orderNoticeLogEntity = new HeOrderNoticeLogEntity();
        orderNoticeLogEntity.setOrderId(orderEntity.getOrderId().longValue())
                .setBasicUid(orderEntity.getBasicUid())
                .setClientUid(orderEntity.getStaffId())
                .setClientType(ClientTypeEnum.ADMIN.getCode());
        orderNoticeLogRepository.save(orderNoticeLogEntity);

    }
}

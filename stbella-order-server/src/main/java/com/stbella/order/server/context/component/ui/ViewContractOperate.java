package com.stbella.order.server.context.component.ui;

import com.stbella.order.common.enums.month.ContractStatusEnum;
import com.stbella.order.common.enums.month.OrderButtonEnum;
import com.stbella.order.domain.order.month.entity.ContractSignRecordPaperEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.MonthContractSignAgreementEntity;
import com.stbella.order.domain.order.month.entity.MonthContractSignRecordEntity;
import com.stbella.order.domain.repository.ContractSignRecordPaperRepository;
import com.stbella.order.domain.repository.MonthContractSignAgreementRepository;
import com.stbella.order.domain.repository.MonthContractSignRecordRepository;
import com.stbella.order.server.order.month.res.OrderOperateButton;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-08-12  16:28
 * @Description: 查看合同操作
 */
@Component
@Slf4j
public class ViewContractOperate implements OrderOperate{

    @Resource
    private MonthContractSignRecordRepository monthContractSignRecordRepository;

    @Resource
    private MonthContractSignAgreementRepository monthContractSignAgreementRepository;

    @Resource
    private ContractSignRecordPaperRepository contractSignRecordPaperRepository;

    @Override
    public void setPriority(int priority) {

    }

    @Override
    public int getPriority() {
        return 2;
    }

    /**
     * 生成订单操作按钮
     *
     * @param order
     * @return
     */
    @Override
    public OrderOperateButton generateButton(OrderOperateContext context) {
        if (Objects.nonNull(context.getOrder().getNeedSign()) && context.getOrder().getNeedSign() != 1) {
            return null;
        }

        // 签约记录表
        List<MonthContractSignRecordEntity> mainContractList = context.getMainContractList();
        // 合同补充协议
        List<MonthContractSignAgreementEntity> agreementContractList = context.agreementContractList;
        // 纸质合同
        List<ContractSignRecordPaperEntity> paperList = context.paperList;


        MonthContractSignRecordEntity recordEntity = mainContractList.stream().filter(item -> ContractStatusEnum.SIGNED.code().equals(item.getContractStatus())).findFirst().orElse(null);
        boolean flag = Objects.nonNull(recordEntity) || CollectionUtils.isNotEmpty(agreementContractList) || CollectionUtils.isNotEmpty(paperList);
        if (flag) {
            return OrderOperateButton.builder().code(getButtonEnum().getCode()).value(getButtonEnum().getValue()).build();
        }
        return null;
    }

    /**
     * 获取按钮枚举
     *
     * @return
     */
    @Override
    public OrderButtonEnum getButtonEnum() {
        return OrderButtonEnum.VIEW_CONTRACT;
    }

    @Override
    public int compareTo(@NotNull IPriority o) {
        return this.getPriority() - o.getPriority();
    }
}

package com.stbella.order.server.convert;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.stbella.customer.server.ecp.request.OrderAddIntegralRequest;
import com.stbella.customer.server.ecp.request.OrderUpdateAssetRequest;
import com.stbella.month.server.request.NewOrderDiscountApprovalRequest;
import com.stbella.month.server.request.OrderGiftExtendRequest;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OrderNoticeEnum;
import com.stbella.order.common.enums.core.OrderRefundStatusEnum;
import com.stbella.order.common.enums.month.FromTypeEnum;
import com.stbella.order.common.enums.month.PayStatusV2Enum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.server.config.DateMapper;
import com.stbella.order.server.config.MappingConfig;
import com.stbella.order.server.order.month.enums.OldOrderApproveStatusEnum;
import com.stbella.order.server.order.month.excel.STMOrderExport;
import com.stbella.order.server.order.month.excel.STMOrderRoomChangeExport;
import com.stbella.order.server.order.month.excel.STMOrderStayOverExport;
import com.stbella.order.server.order.month.req.OrderMonthClientReq;
import com.stbella.order.server.order.month.req.OrderMonthOtherReq;
import com.stbella.order.server.order.month.res.*;
import com.stbella.order.server.order.order.res.OrderMainInfo;
import com.stbella.order.server.utils.BigDecimalUtil;
import com.stbella.order.server.utils.DateUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: StoreConvert 门店转换类
 * @date 2021/5/17 4:26 下午
 */
@Mapper(config = MappingConfig.class, uses = DateMapper.class, imports = {AmountChangeUtil.class, Date.class,
        ObjectUtil.class, BigDecimal.class, BigDecimalUtil.class, FromTypeEnum.class, PayStatusV2Enum.class,
        OrderRefundStatusEnum.class,
        OmniOrderTypeEnum.class,
        OrderNoticeEnum.class, OldOrderApproveStatusEnum.class})
public interface OrderConvert {


    @Mappings({@Mapping(source = "serviceDays", target = "serviceDays")})
    OrderInfoByGoodsVO orderMonthGoodsCacheVO2OrderInfoByGoodsVO(OrderMonthGoodsCacheVO vo);

    OrderMonthClientVO orderMonthClientReq2OrderMonthClientVO(OrderMonthClientReq req);

    List<OrderInfoByAdditionalRevenueVO> cacheOrderInfoByAdditionalRevenueVOList(List<OrderAdditionalRevenueCacheVO> voList);

    List<OrderInfoByGiftExtendSkuVO> orderInfoByGiftExtendSkuVOList2OrderInfoByGiftExtendSkuVOList(List<OrderGiftCacheByUserVO> voList);

    @Mappings({@Mapping(source = "goodsNum", target = "quantity")})
    OrderInfoByGiftExtendSkuVO orderGiftCacheByUserVOToOrderInfoByGiftExtendSkuVO(OrderGiftCacheByUserVO vo);

    OrderInfoByOtherInfoVO orderMonthOtherReq2OrderInfoByOtherInfoVO(OrderMonthOtherReq orderMonthOtherReq);

    @Mappings({@Mapping(source = "quantity", target = "goodsNum")})
    OrderGiftExtendRequest orderInfoByGiftExtendSkuVO2OrderGiftExtendRequest(OrderInfoByGiftExtendSkuVO orderInfoByGiftExtendSkuVO);

    List<OrderGiftExtendRequest> orderInfoByGiftExtendSkuVOList2OrderGiftExtendRequest(List<OrderInfoByGiftExtendSkuVO> orderInfoByGiftExtendSkuVOList);

    OrderAddIntegralRequest heOrderEntity2OrderAddIntegralRequest(HeOrderEntity entity);

    OrderUpdateAssetRequest heOrderEntity2OrderUpdateAssetRequest(HeOrderEntity entity);


    List<STMOOrderBasicContractInfoVO> monthContractSignRecordEntity2VOList(List<MonthContractSignRecordEntity> monthContractSignRecordEntityList);

    List<STMOOrderBasicContractInfoVO> contractSignRecordPaperEntity2VOList(List<ContractSignRecordPaperEntity> contractSignRecordPaperEntityList);


    OrderMainInfo entity2infoVO(HeOrderEntity orderEntity);

    List<OrderMainInfo> entity2infoVOList(List<HeOrderEntity> orderEntityList);

    List<STMOrderStayOverExport> stmSTMOrderExport2stmOrderStayOverExportsList(List<STMOrderExport> stmOrderExportList);

    List<STMOrderRoomChangeExport> stmSTMOrderExport2STMOrderRoomChangeExport(List<STMOrderExport> stmOrderExportList);


    List<OrderReductionVO> orderReductionEntityList2OrderReductionVOList(List<OrderReductionEntity> orderReductionEntityList);


    @Mappings({@Mapping(target = "orderAmount", expression = "java(AmountChangeUtil.changeF2Y(order.getOrderAmount()))"),
            @Mapping(target = "contractAmount", expression = "java(AmountChangeUtil.changeF2Y(order.calPayable() - order.getDisCountAmount()))"),
            @Mapping(target = "customerName", source = "order.customer.name"),
            @Mapping(target = "customerPhone", source = "order.customer.phone"),
            @Mapping(target = "orderTypeName", expression = "java(com.stbella.order.common.enums.core.OmniOrderTypeEnum.getValueByCode(order.getOrderType()))"),
            @Mapping(target = "customerSource", expression = "java(FromTypeEnum.fromCode(order.getCustomer().getFromType()))"),
            @Mapping(target = "storeName", source = "order.store.storeName"),
            @Mapping(target = "brandName", expression = "java(com.stbella.order.server.order.month.enums.StoreTypeEnum.getValueByCode(order.getStore().getType()))"),
            @Mapping(target = "goodsName", expression = "java(mapGoodsList(order.getGoodsList()))"),
            @Mapping(target = "giftGoods", expression = "java(mapGiftGoodsList(order.getGoodsList()))"),
            @Mapping(target = "grossProfitMargin", source = "grossMargin", qualifiedByName = "marginToString"),
            @Mapping(target = "signOrderDiscountMargin", source = "signOrderDiscountMargin", qualifiedByName = "marginToString"),
            @Mapping(target = "orderDiscount", source = "discountMargin", qualifiedByName = "marginToString"),
            @Mapping(target = "orderId", source = "order.orderId"),
            @Mapping(target = "orderSn", source = "order.orderSn"),
            @Mapping(target = "orderTagName", source = "order.orderTagName"),
            @Mapping(target = "sceneStr", expression = "java(com.stbella.order.common.enums.core.CartSceneEnum.getValueByCode(order.getScene()))"),
            @Mapping(target = "phone", source = "order.discountApprovePhone"),
            @Mapping(target = "remark", source = "order.remark"),
            @Mapping(target = "voucherUrlList", expression = "java(order.getVoucherEntityList().stream().map(a -> a.getUrl()).collect(java.util.stream.Collectors.toList()))"),})
    NewOrderDiscountApprovalRequest order2NewOrderDiscountApprovalRequestList(HeOrderEntity order);


    default String mapGoodsList(List<HeOrderGoodsEntity> goodsList) {
        List<String> goodsNameList = new ArrayList<>();
        List<HeOrderGoodsEntity> notGiftGoods = goodsList.stream().filter(o -> !o.isGift()).collect(Collectors.toList());
        for (HeOrderGoodsEntity heOrderGoodsEntity : notGiftGoods) {
            StringBuffer goodsName = new StringBuffer();
            goodsName.append("[").append(heOrderGoodsEntity.getGoodsName()).append("(").append(AmountChangeUtil.changeF2Y(heOrderGoodsEntity.getGoodsPriceOrgin())).append(")").append("*").append(heOrderGoodsEntity.getGoodsNum()).append("]");
            goodsNameList.add(goodsName.toString());
        }
        return String.join(",", goodsNameList);
    }

    default String mapGiftGoodsList(List<HeOrderGoodsEntity> goodsList) {
        List<String> goodsNameList = new ArrayList<>();
        List<HeOrderGoodsEntity> giftGoods = goodsList.stream().filter(o -> o.isGift()).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(giftGoods)) {
            return "";
        }
        for (HeOrderGoodsEntity heOrderGoodsEntity : giftGoods) {
            StringBuffer goodsName = new StringBuffer();
            goodsName.append("[").append(heOrderGoodsEntity.getGoodsName()).append("(").append(AmountChangeUtil.changeF2Y(heOrderGoodsEntity.getGoodsPriceOrgin())).append(")").append("*").append(heOrderGoodsEntity.getGoodsNum()).append("]");
            goodsNameList.add(goodsName.toString());
        }
        return String.join(",", goodsNameList);
    }

    @Mappings({@Mapping(target = "orderAmount", expression = "java(AmountChangeUtil.f2YScale2(order.getOrderAmount()))"),
            @Mapping(target = "calPayableAmount", expression = "java(AmountChangeUtil.f2YScale2(order.getPayAmount()))"),
            @Mapping(target = "realAmount", expression = "java(AmountChangeUtil.f2YScale2(order.getRealAmount()+order.getProductionAmountPay()))"),
            @Mapping(target = "paidAmount", expression = "java(AmountChangeUtil.f2YScale2(order.getPaidAmount()))"),
            @Mapping(target = "orderDiscount", expression = "java(order.getDiscountMargin() +\"%\")"),
            @Mapping(target = "signDiscount", expression = "java(order.getSignOrderDiscountMargin() != null ? order.getSignOrderDiscountMargin() + \"%\" : \"\")"),
            @Mapping(target = "grossProfitMargin", expression = "java(order.getGrossMargin() +\"%\")"),
            @Mapping(target = "approvalDiscountStatusStr", expression = "java(OldOrderApproveStatusEnum.getValueByCode(order.getApprovalDiscountStatus()))"),
            @Mapping(target = "orderTagId", source = "orderTag"),
            @Mapping(target = "basicId", source = "basicUid"),
            @Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "toDateTimeString"),
            @Mapping(target = "payFirstTime", source = "payFirstTime", qualifiedByName = "toDateTimeStringOrEmpty"),
            @Mapping(target = "performanceEffectiveDate", source = "percentFirstTime", qualifiedByName = "toDateTimeStringOrEmpty"),
            @Mapping(target = "isPerformanceNotice", expression = "java(OrderNoticeEnum.NOTICE_NO.code().equals(order.getIsNotice()) && order.getPaidAmount() > 0 ? 1 : 0)"),
            @Mapping(target = "operationType", expression = "java(order.getOperationType() == 0 ? 0 : 1)"),
            @Mapping(target = "orderTypeStr", expression = "java(com.stbella.order.common.enums.core.OmniOrderTypeEnum.getValueByCode(order.getOrderType()))"),
            @Mapping(target = "refundStatusStr", expression = "java(OrderRefundStatusEnum.getName(order.getRefundStatus()))"),
            @Mapping(target = "orderStatusStr", expression = "java(order.fetchOrderStateStr())"),
            @Mapping(target = "payStatusStr", expression = "java(PayStatusV2Enum.getName(order.getPayStatus()))"),
            @Mapping(target = "sceneStr", expression = "java(com.stbella.order.common.enums.core.CartSceneEnum.getValueByCode(order.getScene()))"),
            @Mapping(target = "income", ignore = true),
            @Mapping(target = "remainingAmount", ignore = true),
            @Mapping(target = "calPayable", ignore = true)

    })
    HeOrderVO entity2OrderVO(HeOrderEntity order);


    @Named("toYuanString")
    default String toYuanString(Integer amount) {
        return AmountChangeUtil.f2YScale2(amount) + "";
    }

    @Named("marginToString")
    default String marginToString(BigDecimal margin) {
        // 将 grossMargin 乘以 100，然后去掉小数部分
        BigDecimal result = margin.multiply(BigDecimal.valueOf(1)).setScale(0, RoundingMode.DOWN);

        // 转换为字符串
        return result.toString();
    }

    @Named("toDateTimeString")
    default String toDateTimeString(Long timestamp) {
        return DateUtils.formatDateTime(new Date(timestamp * 1000));
    }

    @Named("toDateTimeStringOrEmpty")
    default String toDateTimeStringOrEmpty(Long timestamp) {
        boolean ifShow = Objects.isNull(timestamp) || timestamp == 0 || timestamp == -1;
        return ifShow ? StringUtils.EMPTY : DateUtils.formatDateTime(new Date(timestamp * 1000));
    }

    @Named("toDateTimeOrEmpty")
    default Date toDateTimeOrEmpty(Long timestamp) {
        boolean ifShow = Objects.isNull(timestamp) || timestamp == 0 || timestamp == -1;
        return ifShow ? null : new Date(timestamp * 1000);
    }

    @Mappings({@Mapping(target = "orderAmount", expression = "java(AmountChangeUtil.f2YScale2(orderEntity.getOrderAmount()))"),
            @Mapping(target = "realAmount", expression = "java(AmountChangeUtil.f2YScale2(orderEntity.getRealAmount()+orderEntity.getProductionAmountPay()))"),
            @Mapping(target = "productionAmountPay", expression = "java(AmountChangeUtil.f2YScale2(orderEntity.getProductionAmountPay()))"),
            @Mapping(target = "paidAmount", expression = "java(AmountChangeUtil.f2YScale2(orderEntity.getPaidAmount()))"),
            @Mapping(target = "payAmount", expression = "java(AmountChangeUtil.f2YScale2(orderEntity.getPayAmount()))"),
            @Mapping(target = "firstPayTime", source = "payFirstTime", qualifiedByName = "toDateTimeOrEmpty"),
            @Mapping(target = "percentFirstTime", source = "percentFirstTime", qualifiedByName = "toDateTimeOrEmpty"),
    })
    OrderInfoNewV3VO convertOrderEntity2NewV3VO(HeOrderEntity orderEntity);

    default LastOrderInfoVO entity2OrderInfoVO(HeOrderEntity orderEntity, List<CfgStoreEntity> cfgStoreEntities) {
        LastOrderInfoVO lastOrderInfoVO = new LastOrderInfoVO();
        lastOrderInfoVO.setOrderType(orderEntity.getOrderType());
        lastOrderInfoVO.setOrderSn(orderEntity.getOrderSn());
        lastOrderInfoVO.setOrderId(orderEntity.getOrderId());
        lastOrderInfoVO.setStoreId(orderEntity.getStoreId());
        lastOrderInfoVO.setBasicUid(orderEntity.getBasicUid());
        lastOrderInfoVO.setClientUid(orderEntity.getClientUid());
        Optional<CfgStoreEntity> storeFirst = cfgStoreEntities.stream().filter(c -> c.getStoreId().equals(orderEntity.getStoreId())).findFirst();
        if (storeFirst.isPresent()) {
            lastOrderInfoVO.setStoreName(storeFirst.get().getStoreName());
            lastOrderInfoVO.setStoreType(storeFirst.get().getType());
        }
        return lastOrderInfoVO;
    }

    @Mappings({@Mapping(target = "orderAmount", ignore = true),
            @Mapping(target = "orderDiscount", expression = "java(order.getDiscountMargin() +\"%\")"),
            @Mapping(target = "signDiscount", expression = "java(order.getSignOrderDiscountMargin() != null ? order.getSignOrderDiscountMargin() + \"%\" : \"\")"),
            @Mapping(target = "grossProfitMargin", expression = "java(order.getGrossMargin() +\"%\")"),
            @Mapping(target = "approvalDiscountStatusStr", expression = "java(OldOrderApproveStatusEnum.getValueByCode(order.getApprovalDiscountStatus()))"),
            @Mapping(target = "orderTagId", source = "orderTag"),
            @Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "toDateTimeString"),
            @Mapping(target = "performanceEffectiveDate", source = "percentFirstTime", qualifiedByName = "toDateTimeStringOrEmpty"),
            @Mapping(target = "orderStatusStr", expression = "java(order.fetchOrderStateStr())"),
            @Mapping(target = "sceneStr", expression = "java(com.stbella.order.common.enums.core.CartSceneEnum.getValueByCode(order.getScene()))"),
            @Mapping(target = "orderTypeStr", expression = "java(OmniOrderTypeEnum.getValueByCode(order.getOrderType()))"),

    })
    OrderInfoNewVO entity2OrderInfoNewVO(HeOrderEntity order);
}

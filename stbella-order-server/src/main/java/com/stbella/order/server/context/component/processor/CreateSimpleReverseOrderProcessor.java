package com.stbella.order.server.context.component.processor;

import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.exception.ApplicationException;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderOtherEntity;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.domain.repository.OrderOtherRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.utils.IdGenUtils;
import com.stbella.platform.order.api.refund.req.CreateSimpleReverseOrderReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-03-26  13:55
 * @Description: 创建简单反向订单处理器
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "创建简单反向订单处理器")
public class CreateSimpleReverseOrderProcessor implements IExecutableAtom<FlowContext> {

    @Autowired
    OrderRepository orderRepository;
    @Autowired
    IdGenUtils IdGenUtils;
    @Autowired
    OrderOtherRepository orderOtherRepository;
    @Autowired
    IncomeRecordRepository incomeRecordRepository;

    @Override
    public void run(FlowContext bizContext) {
        CreateSimpleReverseOrderReq req = bizContext.getAttribute(CreateSimpleReverseOrderReq.class);
        HeOrderEntity parentOrder = orderRepository.getByOrderSn(req.getParentOrderNo());
        if (parentOrder == null) {
            throw new ApplicationException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code(), "订单["+ req.getParentOrderNo()+"]不存在");
        }

        //金额
        Long amount = Math.abs(req.getOrderAmount().multiply(new BigDecimal(100)).longValue());
        if (amount.intValue() == 0) {
            throw new ApplicationException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code(), "订单金额不能为0，请重新输入");
        }

        //金额不能超过原订单金额
        if (amount >= parentOrder.getPaidAmount()) {
            throw new ApplicationException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code(), "退款金额不能超过原订单支付金额");
        }
        if (amount >= parentOrder.getPayAmount()) {
            throw new ApplicationException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code(), "退款金额不能超过原订单金额");
        }
        // 订单金额为负数
        amount *= -1;
        Long orderTime = System.currentTimeMillis()/1000;

        HeOrderEntity heOrder = new HeOrderEntity();
        heOrder.setOrderAmount(amount.intValue());
        heOrder.setPayAmount(amount.intValue());
        heOrder.setPaidAmount(amount.intValue());
        heOrder.setIsDelete(0);
        heOrder.setOrderType(OmniOrderTypeEnum.OTHER_MONTH_ORDER.getCode());
        heOrder.setCreatedAt(System.currentTimeMillis()/1000);
        heOrder.setUpdatedAt(System.currentTimeMillis()/1000);
        heOrder.setStaffId(parentOrder.getStaffId());
        heOrder.setBasicUid(parentOrder.getBasicUid());
        heOrder.setClientUid(parentOrder.getClientUid());
        heOrder.setClientType(1);
        heOrder.setPayStatus(2);
        heOrder.setExtendsContent(req.getParentOrderNo());
        heOrder.setSignType(0);
        heOrder.setTaskId(0L);
        heOrder.setIsClose(0);
        heOrder.setPayStatus(2);
        //支付金额是负数，方便统计业绩
        heOrder.setPaidAmount(-Math.abs(amount.intValue()));
        heOrder.setStoreId(parentOrder.getStoreId());
        heOrder.setPayFirstTime(orderTime.intValue());
        heOrder.setPercentFirstTime(orderTime.intValue());
        heOrder.setOldOrNew(1);
        heOrder.setRemark(req.getRemark());
        heOrder.setOrderTagName(req.getOperator().getOperatorName());
        heOrder.setIsNotice(2);
        heOrder.setProductionType(0);
        heOrder.setOrderSn(IdGenUtils.genOrderSn(OmniOrderTypeEnum.PRODUCTION_ORDER.getCode(), new Date()));


        Integer orderId =heOrder.add(req.getOperator());
        heOrder.setOrderId(orderId);

        HeOrderOtherEntity heOrderOtherEntity = new HeOrderOtherEntity();
        heOrderOtherEntity.setOrderId(heOrder.getOrderId());
        heOrderOtherEntity.setGoodsType("母婴资产置换订单");
        heOrderOtherEntity.setGoodsName(req.getParentOrderNo());
        heOrderOtherEntity.setRemark("母婴资产置换");
        heOrderOtherEntity.setDiscount(new BigDecimal(0));
        heOrderOtherEntity.setDiscountAmount(0);


        // 其他订单扩展信息
        orderOtherRepository.save(heOrderOtherEntity);
        bizContext.setAttribute(HeOrderEntity.class, heOrder);
        log.info("创建简单反向订单成功，订单号：{}", heOrder.getOrderSn());

        //生成支付记录
        HeIncomeRecordEntity incomeRecordEntity = new HeIncomeRecordEntity();
        incomeRecordEntity.setOrderId(orderId);
        incomeRecordEntity.setOrderGoodsId(0);
        incomeRecordEntity.setIncome(-Math.abs(amount.intValue()));
        incomeRecordEntity.setClientUid(heOrder.getClientUid());
        incomeRecordEntity.setIncomeSn(IdGenUtils.genOmniSn("BK", new Date()));
        incomeRecordEntity.setStatus(1);
        incomeRecordEntity.setStoreId(heOrder.getStoreId());
        incomeRecordEntity.setReceiptType(3);
        incomeRecordEntity.setPayType(10);
        incomeRecordEntity.setPayTime(Integer.valueOf(String.valueOf(System.currentTimeMillis() / 1000)));
        incomeRecordEntity.setCreatedAt(System.currentTimeMillis() / 1000);
        incomeRecordEntity.setUpdatedAt(System.currentTimeMillis() / 1000);
        //变动金额
        incomeRecordEntity.setChangeAmount(incomeRecordEntity.getIncome());
        incomeRecordRepository.saveOne(incomeRecordEntity);

    }
}

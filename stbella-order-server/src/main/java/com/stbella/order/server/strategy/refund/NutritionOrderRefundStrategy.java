package com.stbella.order.server.strategy.refund;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.server.order.month.entity.OrderPayRecordPO;
import com.stbella.order.server.order.month.enums.PayRecordEnum;
import com.stbella.order.server.order.month.enums.PayStatusEnum;
import com.stbella.order.server.order.month.service.OrderPayRecordService;
import com.stbella.order.server.order.nutrition.entity.OrderNutritionApplyRefundPO;
import com.stbella.order.server.order.nutrition.entity.OrderNutritionAuditPO;
import com.stbella.order.server.order.nutrition.entity.OrderNutritionMonthFoodPO;
import com.stbella.order.server.order.nutrition.enums.NutritionPayRecordAuditStatusEnum;
import com.stbella.order.server.order.nutrition.enums.OrderNutritionStatusEnum;
import com.stbella.order.server.order.nutrition.service.OrderNutritionApplyRefundService;
import com.stbella.order.server.order.nutrition.service.OrderNutritionAuditService;
import com.stbella.order.server.order.nutrition.service.OrderNutritionMonthFoodService;
import com.stbella.order.server.utils.BigDecimalUtil;
import com.stbella.pay.server.entity.mq.RefundNotifyMq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Optional;

/**
 * <AUTHOR>
 * 退款策略-营养订单
 * @date 2024/3/15 10:55
 */
@Slf4j
@Component
public class NutritionOrderRefundStrategy implements RefundStrategy {
    @Resource
    private OrderPayRecordService orderPayRecordService;
    @Resource
    private OrderNutritionMonthFoodService orderNutritionMonthFoodService;
    @Resource
    private OrderNutritionAuditService orderNutritionAuditService;
    @Resource
    private OrderNutritionApplyRefundService orderNutritionApplyRefundService;

    @Override
    public void processRefund(RefundNotifyMq refundNotifyMq) {
        /**
         RefundNotifyMq(accountType=null, outTradeNo=**********************, transactionalNo=4200001374202203226312556209, outRefundNo=1506169470144483329, refundType=2, refundStatus=1, refundAmount=0.03, successTime=Tue Mar 22 15:22:51 GMT+08:00 2022, requestBody={"transaction_id":"4200001374202203226312556209","refund_status":"SUCCESS","out_refund_no":"1506169470144483329","settlement_refund_fee":"3","success_time":"2022-03-22 15:22:51","refund_recv_accout":"支付用户零钱","refund_id":"50302001322022032218548387178","out_trade_no":"**********************","refund_account":"REFUND_SOURCE_RECHARGE_FUNDS","refund_fee":"3","total_fee":"3","settlement_total_fee":"3","refund_request_source":"API"})
         */
        //申请记录表id
        String outRefundNo = refundNotifyMq.getOutRefundNo();
        Integer refundStatus = refundNotifyMq.getRefundStatus();
        BigDecimal refundAmount = refundNotifyMq.getRefundAmount();
        //营养订单处理
        OrderPayRecordPO orderPayRefundRecordPO = orderPayRecordService.getBaseMapper().selectById(outRefundNo);
        if (ObjectUtil.isNull(orderPayRefundRecordPO)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "退款回调处理失败，退款流水不存在：" + JSONUtil.toJsonStr(refundNotifyMq));
        }

        if (refundStatus.equals(PayStatusEnum.PAY_STATUS_FAIL.getCode())) {
            orderPayRefundRecordPO.setPayStatus(PayStatusEnum.PAY_STATUS_FAIL.getCode());
            orderPayRefundRecordPO.setRemark("银行卡余额不足或网络问题");
            orderPayRecordService.updateById(orderPayRefundRecordPO);
            log.error("退款回调处理失败：" + JSONUtil.toJsonStr(refundNotifyMq));
            return;
        }

        //退款表
        orderPayRefundRecordPO.setRequestBody(refundNotifyMq.getRequestBody());
        orderPayRefundRecordPO.setPayAmount(refundNotifyMq.getRefundAmount());
        orderPayRefundRecordPO.setTransactionalNo(refundNotifyMq.getTransactionalNo());
        orderPayRefundRecordPO.setPayTime(refundNotifyMq.getSuccessTime());

        //退款成功
        orderPayRefundRecordPO.setPayStatus(PayStatusEnum.PAY_STATUS_SUCCESS.getCode());
        orderPayRefundRecordPO.setRemark("");

        //修改订单金额以及状态

        String orderNo = orderPayRefundRecordPO.getOrderNo();
        OrderNutritionMonthFoodPO orderNutritionMonthFoodPO = orderNutritionMonthFoodService.getBaseMapper().selectOne(new LambdaQueryWrapper<OrderNutritionMonthFoodPO>().eq(OrderNutritionMonthFoodPO::getOrderNo, orderNo));
        //减少冻结金额
        orderNutritionMonthFoodPO.setFreezeAmount(BigDecimalUtil.subtract(orderNutritionMonthFoodPO.getFreezeAmount(), refundAmount));
        //减少实际金额
        orderNutritionMonthFoodPO.setRealityAmount(BigDecimalUtil.subtract(orderNutritionMonthFoodPO.getRealityAmount(), refundAmount));
        //增加退款金额
        orderNutritionMonthFoodPO.setRefundedAmount(BigDecimalUtil.add(orderNutritionMonthFoodPO.getRefundedAmount(), refundAmount));


        //根据申请的金额和已退的金额
        //获取最新的申请的单子
        Optional<OrderNutritionAuditPO> first = orderNutritionAuditService.getBaseMapper().selectList(
                new LambdaQueryWrapper<OrderNutritionAuditPO>()
                        .eq(OrderNutritionAuditPO::getOrderNo, orderNutritionMonthFoodPO.getOrderNo())
                        .eq(OrderNutritionAuditPO::getAuditStatus, NutritionPayRecordAuditStatusEnum.AUDIT_SUCCESS.getCode())
                        .orderByDesc(OrderNutritionAuditPO::getAuditTime)

        ).stream().findFirst();

        if (!first.isPresent()) {
            log.error("支付回调审批数据不存在！", JSONUtil.toJsonStr(refundNotifyMq));
            return;
        }

        OrderNutritionAuditPO orderNutritionAuditPO = first.get();

        Long applyId = orderNutritionAuditPO.getApplyId();

        OrderNutritionApplyRefundPO orderNutritionApplyRefundPO = orderNutritionApplyRefundService.getBaseMapper().selectById(applyId);

        if (ObjectUtil.isNull(orderNutritionApplyRefundPO)) {
            log.error("支付回调申请退款数据不存在！", JSONUtil.toJsonStr(refundNotifyMq));
            return;
        }

        if (orderNutritionApplyRefundPO.getRefundAmount().compareTo(orderNutritionMonthFoodPO.getRefundedAmount()) > 0) {
            //申请退款的金额>实际退款的金额
            orderNutritionMonthFoodPO.setStatus(OrderNutritionStatusEnum.PART_REFUNDED.getCode());
        } else {
            //申请退款的金额<=实际退款的金额
            orderNutritionMonthFoodPO.setStatus(OrderNutritionStatusEnum.CLOSE_REFUNDED.getCode());
        }

        orderNutritionMonthFoodService.updateById(orderNutritionMonthFoodPO);

        //更新支付记录
        orderPayRecordService.updateById(orderPayRefundRecordPO);
    }

    @Override
    public PayRecordEnum getPayRecordEnum() {
        return PayRecordEnum.GH;
    }
}

package com.stbella.order.server.context.component.giftOrder.processor;

import cn.hutool.core.lang.Assert;
import com.stbella.core.base.Operator;
import com.stbella.core.result.Result;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.core.CartSceneEnum;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.common.enums.order.OrderGiftReasonTypeEnum;
import com.stbella.order.common.enums.order.OrderGiftStatusEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGiftExtendEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.platform.order.api.OrderCreateService;
import com.stbella.platform.order.api.cart.CartCommandService;
import com.stbella.platform.order.api.cart.CartQueryService;
import com.stbella.platform.order.api.req.CreateOrderReq;
import com.stbella.platform.order.api.req.FinishCartReq;
import com.stbella.platform.order.api.req.QueryCartReq;
import com.stbella.platform.order.api.res.CartRes;
import com.stbella.platform.order.api.res.CreateOrderRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.FlowIdentity;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

@Component
@Slf4j
@SnowballComponent(name = "赠送订单审批通过逻辑处理", desc = "创建对应的赠送订单与资产")
public class GiftOrderApprovalAgreeProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private CartQueryService cartQueryService;

    @Resource
    private OrderCreateService orderCreateService;

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private CartCommandService cartCommandService;

    @Override
    public void run(FlowContext bizContext) {

        HeOrderGiftExtendEntity heOrderGiftExtendEntity = bizContext.getAttribute(HeOrderGiftExtendEntity.class);

        log.info("查询购物车信息，cartId:{}", heOrderGiftExtendEntity.getCartId());
        Result<CartRes> cartResResult = cartQueryService.queryCart(QueryCartReq.builder().cartId(heOrderGiftExtendEntity.getCartId()).orderType(heOrderGiftExtendEntity.getOrderType()).scene(CartSceneEnum.GIFT_ORDER.code()).build());
        Assert.isTrue(cartResResult.getSuccess(), "查询购物车失败");
        CreateOrderReq createOrderReq = new CreateOrderReq();
        createOrderReq.setScene(CartSceneEnum.GIFT_ORDER.code());
        createOrderReq.setSkuList(cartResResult.getData().getSkuList());
        createOrderReq.setCartId(heOrderGiftExtendEntity.getCartId());
        createOrderReq.setOrderType(cartResResult.getData().getOrderType());
        createOrderReq.setOriginalPrice(cartResResult.getData().getOriginalPrice());
        createOrderReq.setStaffId(heOrderGiftExtendEntity.getCreateBy().intValue());
        createOrderReq.setStoreId(cartResResult.getData().getStoreId());
        createOrderReq.setClientUid(cartResResult.getData().getClientUid());
        createOrderReq.setExtraInfo(cartResResult.getData().getExtraInfo());
        createOrderReq.setBasicUid(cartResResult.getData().getBasicUid());
        createOrderReq.setStaffPhone(cartResResult.getData().getStaffPhone());
        createOrderReq.setOriginalPrice(cartResResult.getData().getTotalAmount());
        createOrderReq.setPayAmount(BigDecimal.ZERO);
        createOrderReq.setBu(0);
        Operator operator = new Operator();
        operator.setOperatorGuid(String.valueOf(heOrderGiftExtendEntity.getCreateBy()));
        createOrderReq.setOperator(operator);
        Result<CreateOrderRes> orderRes = orderCreateService.createOrder(createOrderReq);
        Assert.isTrue(orderRes.getSuccess(), "赠送订单创建订单失败");
        Assert.isTrue(orderRes.getData().getOrderId() != null, "赠送订单ID不能为空");
        log.info("赠送订单创建订单成功，orderId:{}", orderRes.getData().getOrderId());
        cartCommandService.finishCart(FinishCartReq.builder().cartId(heOrderGiftExtendEntity.getCartId()).orderId(orderRes.getData().getOrderId()).build());
        bizContext.setAttribute(BizConstant.ExtraKey.giftOrderId, orderRes.getData().getOrderId());
        HeOrderEntity order = orderRepository.getByOrderId(orderRes.getData().getOrderId());
        bizContext.setAttribute(BizConstant.ExtraKey.giftOrderSn, order.getOrderSn());
        log.info("订单全额支付，订单id:{}，sn:{}", order.getOrderId(), order.getOrderSn());
        order.setPaidAmount(0);
        order.setRealAmount(0);
        order.setProductionAmountPay(0);
        if (OrderGiftReasonTypeEnum.ORDER_REMARK_GIFT.getCode().equals(heOrderGiftExtendEntity.getGiftReasonType())){
            Assert.isTrue(heOrderGiftExtendEntity.getOrderId() != null, "关联订单id不能为空");
            HeOrderEntity heOrderEntity = orderRepository.getByOrderId(heOrderGiftExtendEntity.getOrderId());
            Assert.isTrue(Objects.nonNull(heOrderEntity), "关联订单不存在");
            if (Objects.isNull(heOrderEntity.getPayFinishAt()) || heOrderEntity.getPayFinishAt() == 0){
                log.info("关联订单未支付全款，giftSn:{}，orderSn:{}", heOrderGiftExtendEntity.getGiftSn(), heOrderEntity.getOrderSn());
                orderRepository.updateOne(order);
                bizContext.setAttribute(BizConstant.ExtraKey.giftOrderStatus, OrderGiftStatusEnum.APPROVAL_NOT_SEND.getCode());
                return;
            }
        }
        order.setJustEffect(Boolean.TRUE);
        order.fullPayment(null);
        order.setIsPreviouslyFullyPaid(Boolean.FALSE);
        FlowContext context = new FlowContext();
        context.setAttribute(HeOrderEntity.class, order);
        FlowIdentity identity = FlowIdentity.builder()
                .bizActivity(BizActivityEnum.GIFT_ORDER_ASSET_GRANT.code())
                .idSlice("GiftOrder")
                .idSlice("AssetGrant")
                .build();
        SnowballFlowLauncher.fire(identity, context);
        bizContext.setAttribute(BizConstant.ExtraKey.giftOrderStatus, OrderGiftStatusEnum.APPROVAL_SEND.getCode());
    }
}

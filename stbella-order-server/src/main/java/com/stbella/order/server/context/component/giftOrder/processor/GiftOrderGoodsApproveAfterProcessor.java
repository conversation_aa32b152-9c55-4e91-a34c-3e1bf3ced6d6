package com.stbella.order.server.context.component.giftOrder.processor;

import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.order.OrderGiftStatusEnum;
import com.stbella.order.domain.order.month.entity.HeOrderGiftExtendEntity;
import com.stbella.order.domain.repository.HeOrderGiftInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

@Slf4j
@Component
@SnowballComponent(name = "创建礼赠订单审批成功后的业务处理", desc = "创建礼赠订单审批成功后的业务处理流程")
public class GiftOrderGoodsApproveAfterProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private HeOrderGiftInfoRepository heOrderGiftInfoRepository;

    @Override
    public void run(FlowContext bizContext) {

        HeOrderGiftExtendEntity heOrderGiftExtendEntity = bizContext.getAttribute(HeOrderGiftExtendEntity.class);
        log.info("giftSn: {}", heOrderGiftExtendEntity.getGiftSn());
        Boolean approveResult = (Boolean) bizContext.getAttribute(BizConstant.ExtraKey.approveResult);
        if (!approveResult){
            heOrderGiftExtendEntity.setStatus(OrderGiftStatusEnum.APPROVAL_FAIL.getCode());
        } else {
            String approveIdKey = (String) bizContext.getAttribute(BizConstant.ExtraKey.approveIdKey);
            log.info("approveIdKey: {}", approveIdKey);
            heOrderGiftExtendEntity.setApproveId(approveIdKey);
        }
        heOrderGiftInfoRepository.update(heOrderGiftExtendEntity);
    }
}

package com.stbella.order.server.order.month.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.base.server.test.req.SwapPhoneReq;
import com.stbella.contract.model.enums.AuthTypeEnum;
import com.stbella.core.base.PageVO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.customer.server.ecp.entity.HeUserCardPO;
import com.stbella.customer.server.ecp.service.HeUserCardService;
import com.stbella.customer.server.ecp.vo.ClientInfoVO;
import com.stbella.order.common.enums.core.OrderRefundStatusEnum;
import com.stbella.order.common.enums.month.PayStatusV2Enum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.domain.order.month.entity.HeTaskEntity;
import com.stbella.order.domain.order.month.entity.UserEntity;
import com.stbella.order.domain.repository.HeTaskRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.domain.repository.OrderUserSnapshotRepository;
import com.stbella.order.domain.repository.UserRepository;
import com.stbella.order.server.convert.TabClientConvert;
import com.stbella.order.server.manager.BaseManager;
import com.stbella.order.server.manager.BasicManager;
import com.stbella.order.server.manager.SsoManager;
import com.stbella.order.server.manager.TabClientManager;
import com.stbella.order.server.order.cts.entity.OrderCtsPO;
import com.stbella.order.server.order.cts.service.OrderCtsService;
import com.stbella.order.server.order.month.enums.OrderEventEnum;
import com.stbella.order.server.order.month.req.*;
import com.stbella.order.server.order.month.request.standard.UpdateCustomerNamePhoneReq;
import com.stbella.order.server.order.month.res.BasicSimpInfoVO;
import com.stbella.order.server.order.month.res.OrderSimpInfoVO;
import com.stbella.order.server.order.month.res.SaleSimpInfoVO;
import com.stbella.order.server.order.month.service.OperationalToolService;
import com.stbella.order.server.producer.OrderEventProducer;
import com.sun.xml.bind.v2.TODO;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * 运营工具服务类
 * @date 2023/10/19 10:39
 */
@Service
@DubboService
@Slf4j
public class OperationalToolServiceImpl implements OperationalToolService {
    @Resource
    private BasicManager basicManager;
    @Resource
    private BaseManager baseManager;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private HeTaskRepository taskRepository;
    @Resource
    private OrderCtsService orderCtsService;
    @Resource
    private SsoManager ssoManager;
    @Resource
    private UserRepository userRepository;
    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;
    @Resource
    private OrderEventProducer orderEventProducer;
    @Autowired
    private HeUserCardService userCardService;
    @Resource
    private TabClientManager tabClientManager;

    @Resource
    private TabClientConvert tabClientConvert;

    /**
     * 根据姓名或手机号模糊查询客户信息
     *
     * @param request
     * @return
     */
    @Override
    public Result<PageVO<BasicSimpInfoVO>> getByCustomerNameOrPhone(BasicQueryRequest request) {
        PageVO<BasicSimpInfoVO> byCustomerNameOrPhone = basicManager.getByCustomerNameOrPhone(request);
        return Result.success(byCustomerNameOrPhone);
    }

    /**
     * 根据手机号精确查询客户信息
     *
     * @param request
     * @return
     */
    @Override
    public Result<BasicSimpInfoVO> getByCustomerPhone(BasicQueryRequest request) {
        BasicSimpInfoVO byCustomerPhone = basicManager.getByCustomerPhone(request.getKeyword());
        return Result.success(byCustomerPhone);
    }

    /**
     * 根据姓手机号查询销售简洁信息
     *
     * @param request
     * @return
     */
    @Override
    public Result<SaleSimpInfoVO> getSaleSimpInfoByPhone(SaleQueryRequest request) {
        SaleSimpInfoVO vo = new SaleSimpInfoVO();
        //如果是母婴
        if (request.getOrderType().equals(0)) {
            UserEntity userEntity = userRepository.queryByPhone(request.getPhone());
            if (ObjectUtil.isNotEmpty(userEntity)) {
                vo.setId(String.valueOf(userEntity.getId()));
                vo.setPhone(userEntity.getPhone());
                vo.setName(userEntity.getName());
                vo.setNamePhone(userEntity.getName() + "-" + userEntity.getPhone());
            } else {
                return Result.failed("订单或销售不存在");
            }
        } else if (request.getOrderType().equals(1)) {
            //予家
            vo = ssoManager.getByPhone(request.getPhone());
        } else {
            return Result.failed("请传入正确的订单场景,OrderType=" + request.getOrderType());
        }
        return Result.success(vo);
    }

    /**
     * 根据订单号查询销售简洁信息
     *
     * @param request
     * @return
     */
    @Override
    public Result<SaleSimpInfoVO> getSaleSimpInfoByOrderNo(SaleQueryByOrderRequest request) {
        SaleSimpInfoVO vo = new SaleSimpInfoVO();
        //如果是母婴
        if (request.getOrderType().equals(0)) {
            HeOrderEntity byOrderSn = orderRepository.getByOrderSn(request.getOrderNo());
            if (ObjectUtil.isNotEmpty(byOrderSn) && ObjectUtil.isNotEmpty(byOrderSn.getStaffId()) && byOrderSn.getStaffId() != 0) {
                UserEntity userEntity = userRepository.queryById(byOrderSn.getStaffId());
                if (ObjectUtil.isNotEmpty(userEntity)) {
                    vo.setId(String.valueOf(userEntity.getId()));
                    vo.setPhone(userEntity.getPhone());
                    vo.setName(userEntity.getName());
                    vo.setNamePhone(userEntity.getName() + "-" + userEntity.getPhone());
                }
            } else {
                return Result.failed("订单或销售不存在");
            }
        } else if (request.getOrderType().equals(1)) {
            //予家
            OrderCtsPO byOrderNo = orderCtsService.getByOrderNo(request.getOrderNo());
            if (ObjectUtil.isNotEmpty(byOrderNo) && ObjectUtil.isNotEmpty(byOrderNo.getSellId()) && byOrderNo.getSellId() != 0) {
                vo = ssoManager.getBySellId(byOrderNo.getSellId());
            } else {
                return Result.failed("订单或销售不存在");
            }
        } else {
            return Result.failed("请传入正确的订单场景,OrderType=" + request.getOrderType());
        }
        return Result.success(vo);
    }

    /**
     * 根据订单号查询订单简洁信息
     *
     * @param orderNo
     * @return
     */
    @Override
    public Result<OrderSimpInfoVO> getOrderSimpInfo(OrderSimpInfoQuery query) {
        String orderSn = query.getOrderSn();
        HeOrderEntity byOrderSn = orderRepository.getByOrderSn(orderSn);
        if (ObjectUtil.isEmpty(byOrderSn)) {
            return Result.failed("订单不存在,订单编号=" + orderSn);
        }
        OrderSimpInfoVO vo = new OrderSimpInfoVO();
        vo.setOrderSn(orderSn);
        vo.setOriginalAmount(Long.valueOf(byOrderSn.getPaidAmount()));
        return Result.success(vo);
    }

    /**
     * 修改订单已付金额
     *
     * @param request
     * @return
     */
    @Override
    public Result<String> orderChangeAmount(OrderChangeAmountRequest request) {
        if (request.getNewAmount() >= request.getOriginalAmount() || request.getNewAmount() < 0L) {
            return Result.failed("金额有误");
        }
        HeOrderEntity byOrderSn = orderRepository.getByOrderSn(request.getOrderSn());
        if (ObjectUtil.isEmpty(byOrderSn)) {
            return Result.failed("订单不存在,订单编号=" + request.getOrderSn());
        }
        //支付状态
        Integer payStatus;
        if (request.getNewAmount() == 0L) {
            payStatus = PayStatusV2Enum.WAIT_PAY.getCode();
        } else {
            payStatus = PayStatusV2Enum.NO_PAY_OFF.getCode();
        }
        byOrderSn.setPayStatus(payStatus);
        byOrderSn.setPaidAmount(Math.toIntExact(request.getNewAmount()));
        //老订单逻辑
        if (ObjectUtil.isEmpty(byOrderSn.getOldOrNew()) || ObjectUtil.equals(byOrderSn.getOldOrNew(), 0)) {
            if (ObjectUtil.isNotEmpty(byOrderSn.getTaskId())) {
                HeTaskEntity byTaskId = taskRepository.getByTaskId(byOrderSn.getTaskId());
                if (ObjectUtil.isNotEmpty(byTaskId)) {
                    Boolean aBoolean = taskRepository.openWeikuanByProjectId(byTaskId.getProjectId());
                    if (!aBoolean) {
                        return Result.failed("修改订单已付金额操作失败");
                    }
                }
            }
        } else {
            //新订单逻辑
            byOrderSn.setRealAmount(Math.toIntExact(request.getNewAmount()));
        }
        orderRepository.updateOrderMonthByOrderId(byOrderSn);
        return Result.success("操作成功");
    }

    /**
     * 更改客户手机号
     *
     * @param req
     * @return
     */
    @Override
    public Result<String> swapPhone(SwapPhoneReq req) {
        Result<String> stringResult = baseManager.swapPhone(req);
        //修改订单客户快照表
        List<HeOrderUserSnapshotEntity> heOrderUserSnapshotEntities = orderUserSnapshotRepository.queryByUserIdOrNameOrSource(null, req.getOriginalPhone(), null);
        if (ObjectUtil.isNotEmpty(heOrderUserSnapshotEntities)) {
            heOrderUserSnapshotEntities.forEach(x -> {
                x.setPhone(req.getNewPhone());
                orderUserSnapshotRepository.updateOne(x);
            });
        }
        return stringResult;
    }

    /**
     * 更改销售
     *
     * @param req
     * @return
     */
    @Override
    public Result<String> swapSale(OrderSwapSaleRequest req) {
        log.info("更改销售,req={}", JSONUtil.parse(req));
        String orderNo = req.getOrderNo();
        //判断是哪个订单场景
        //母婴
        if (req.getOrderType().equals(0)) {
            HeOrderEntity byOrderSn = orderRepository.getByOrderSn(orderNo);
            orderRepository.updateSaleByOrderId(byOrderSn.getOrderId(), Integer.valueOf(req.getSaleId()));
            //老订单额外逻辑
            if (ObjectUtil.isEmpty(byOrderSn.getOldOrNew()) || ObjectUtil.equals(byOrderSn.getOldOrNew(), 0)) {
                if (ObjectUtil.isNotEmpty(byOrderSn.getTaskId())) {
                    HeTaskEntity byTaskId = taskRepository.getByTaskId(byOrderSn.getTaskId());
                    if (ObjectUtil.isNotEmpty(byTaskId)) {
                        Boolean aBoolean = taskRepository.updateTaskUidByProjectId(byTaskId.getProjectId(), Long.valueOf(req.getSaleId()));
                        if (!aBoolean) {
                            return Result.failed("母婴旧订单修改销售操作失败");
                        }
                    }

                }
            }

        } else if (req.getOrderType().equals(1)) {
            //予家
            Boolean aBoolean = orderCtsService.updateSaleByOrderNo(orderNo, Long.valueOf(req.getSaleId()), req.getNewSaleName());
            if (!aBoolean) {
                return Result.failed("予家修改销售操作失败");
            }
        } else {
            return Result.failed("请传入正确的订单场景,OrderType=" + req.getOrderType());
        }
        return Result.success("操作成功");
    }

    /**
     * 发送订单变更MQ
     *
     * @param orderId
     * @return
     */
    @Override
    public Result sendEventMq(Integer orderId) {
        HeOrderEntity byOrderId = orderRepository.getByOrderId(orderId);
        if (ObjectUtil.isEmpty(byOrderId)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "订单不存在,订单ID=" + orderId);
        }
        if (byOrderId.getRefundStatus().equals(OrderRefundStatusEnum.PARTIAL_REFUND.getCode())) {
            orderEventProducer.sendMq(OrderEventEnum.PART_REFUND.getCode(), byOrderId.getOrderSn(), null);
        } else if (byOrderId.getRefundStatus().equals(OrderRefundStatusEnum.FULL_REFUND.getCode())) {
            orderEventProducer.sendMq(OrderEventEnum.FULL_REFUND.getCode(), byOrderId.getOrderSn(), null);
        }
        return Result.success();
    }

    @Override
    public Void updateCustomerNamePhone(UpdateCustomerNamePhoneReq req) {
        //查询订单是否存在
        HeOrderEntity byOrderSn = orderRepository.getByOrderSn(req.getOrderSn());
        if (ObjectUtil.isEmpty(byOrderSn)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "订单不存在,订单编号=" + req.getOrderSn());
        }
        //修改订单客户快照表
        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(byOrderSn.getOrderId());
        if (ObjectUtil.isNotEmpty(heOrderUserSnapshotEntity)) {
            if (ObjectUtil.isNotEmpty(req.getClientName())) {
                heOrderUserSnapshotEntity.setName(req.getClientName());
            }
            if (ObjectUtil.isNotEmpty(req.getPhone())) {
                heOrderUserSnapshotEntity.setPhone(req.getPhone());
            }
            orderUserSnapshotRepository.updateOne(heOrderUserSnapshotEntity);
        }
        //修改userCard表
        List<HeUserCardPO> heUserCardPOS = userCardService.queryUserCardInfoByBasicUid(byOrderSn.getBasicUid());
        if (ObjectUtil.isNotEmpty(heUserCardPOS)) {
            heUserCardPOS.forEach(x -> {
                if (ObjectUtil.isNotEmpty(req.getClientName())) {
                    x.setName(req.getClientName());
                }
                if (ObjectUtil.isNotEmpty(req.getPhone())) {
                    x.setPhone(req.getPhone());
                }
                userCardService.updateById(x);
            });
        }

        // 改名字
        ClientInfoVO clientInfoByClientId = tabClientManager.getClientInfoByClientId(byOrderSn.getClientUid());
        if (ObjectUtil.isNotEmpty(clientInfoByClientId)) {
            OrderMonthClientReq orderMonthClientReq = tabClientConvert.clientInfoVO2Req(clientInfoByClientId);
            if (StringUtil.isNotBlank(req.getClientName())) {
                orderMonthClientReq.setName(req.getClientName());
                tabClientManager.updateClientInfo(orderMonthClientReq);
            }
        }

        return null;
    }

    @Override
    public Void updateClientAuthType(UpdateCustomerNamePhoneReq req) {
        //查询订单是否存在
        HeOrderEntity byOrderSn = orderRepository.getByOrderSn(req.getOrderSn());
        if (ObjectUtil.isEmpty(byOrderSn)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "订单不存在,订单编号=" + req.getOrderSn());
        }
        //修改订单客户快照表
        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(byOrderSn.getOrderId());
        if (ObjectUtil.isNotEmpty(heOrderUserSnapshotEntity)) {
            if (ObjectUtil.isNotEmpty(req.getClientName())) {
                heOrderUserSnapshotEntity.setName(req.getClientName());
            }
            if (ObjectUtil.isNotEmpty(req.getPhone())) {
                heOrderUserSnapshotEntity.setPhone(req.getPhone());
            }
            if (ObjectUtil.isNotEmpty(req.getEmail())) {
                heOrderUserSnapshotEntity.setEmail(req.getEmail());
                heOrderUserSnapshotEntity.setAuthType(AuthTypeEnum.AUTH_EMAIL.code());
                heOrderUserSnapshotEntity.setCertType(req.getCertType());
                heOrderUserSnapshotEntity.setEmailVerify(1);
            }
            if (StringUtil.isNotBlank(req.getIdCard())) {
                heOrderUserSnapshotEntity.setIdCard(req.getIdCard());
            }
            orderUserSnapshotRepository.updateOne(heOrderUserSnapshotEntity);
        }
        //修改userCard表
        List<HeUserCardPO> heUserCardPOS = userCardService.queryUserCardInfoByBasicUid(byOrderSn.getBasicUid());
        if (ObjectUtil.isNotEmpty(heUserCardPOS)) {
            heUserCardPOS.forEach(x -> {
                if (ObjectUtil.isNotEmpty(req.getClientName())) {
                    x.setName(req.getClientName());
                }
                if (ObjectUtil.isNotEmpty(req.getPhone())) {
                    x.setPhone(req.getPhone());
                }
                if (ObjectUtil.isNotEmpty(req.getEmail())) {
                    x.setEmail(req.getEmail());
                    x.setAuthType(AuthTypeEnum.AUTH_EMAIL.code());
                    x.setCertType(req.getCertType());
                    x.setVerifyState(1);
                }
                if (StringUtil.isNotBlank(req.getIdCard())) {
                    x.setIdCard(req.getIdCard());
                }
                userCardService.updateById(x);
            });
        }

        return null;
    }
}

package com.stbella.order.server.contract.month.service.impl;

import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.notice.entity.OaProcessIdRelationPO;
import com.stbella.notice.entity.OaProcessRecordPO;
import com.stbella.notice.server.OaProcessIdRelationService;
import com.stbella.notice.server.OaProcessRecordService;
import com.stbella.order.common.enums.month.ContractStatusEnum;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.common.enums.month.TemplateContractTypeV6Enum;
import com.stbella.order.common.enums.month.TemplateContractTypeV7Enum;
import com.stbella.order.domain.order.month.entity.ContractSignRecordPaperEntity;
import com.stbella.order.domain.order.month.entity.MonthContractSignRecordEntity;
import com.stbella.order.domain.order.month.entity.UserEntity;
import com.stbella.order.domain.repository.ContractSignRecordPaperRepository;
import com.stbella.order.domain.repository.MonthContractSignRecordRepository;
import com.stbella.order.domain.repository.OrderApproveRecordRepository;
import com.stbella.order.domain.repository.UserRepository;
import com.stbella.order.server.contract.req.CanPaperContractQuery;
import com.stbella.order.server.contract.req.CreatePaperContractReq;
import com.stbella.order.server.contract.req.SavePaperContractReq;
import com.stbella.order.server.contract.res.CanContractSignPaperVO;
import com.stbella.order.server.contract.res.ContractSignPaperVO;
import com.stbella.order.server.contract.service.month.ContractSignRecordPaperService;
import com.stbella.order.server.convert.AppMonthContractSignRecordConverter;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 纸质合同表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2022-11-02
 */
@Service
@Slf4j
public class ContractSignRecordPaperServiceImpl implements ContractSignRecordPaperService {

    @Resource
    private ContractSignRecordPaperRepository contractSignRecordPaperRepository;
    @Resource
    private AppMonthContractSignRecordConverter appMonthContractSignRecordConverter;
    @Resource
    private OrderApproveRecordRepository orderApproveRecordRepository;

    @Resource
    private UserRepository userRepository;
    @DubboReference
    private OaProcessIdRelationService oaProcessIdRelationService;
    @DubboReference
    private OaProcessRecordService oaProcessRecordService;

    @Resource
    private MonthContractSignRecordRepository monthContractSignRecordRepository;

    @Override
    public Result<Integer> createPaperContract(CreatePaperContractReq createPaperContractReq) {
        log.info("添加纸质合同{}", JSONUtil.toJsonStr(createPaperContractReq));
        //查询当前订单的纸质合同是否已经签署过  您已签订纸质合同，无需再次签订
        MonthContractSignRecordEntity byOrderIdAndContractStatus = monthContractSignRecordRepository.getByOrderIdAndContractStatus(createPaperContractReq.getOrderId(), createPaperContractReq.getPaperContractType(), ContractStatusEnum.SIGNED.code());
        if(ObjectUtil.isNotEmpty(byOrderIdAndContractStatus)){
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "您已签订线上E签宝合同，无需再次签订");
        }

        //每个纸质合同只能上传一份
        List<ContractSignPaperVO> contractSignPaperVOS = this.paperList(createPaperContractReq.getOrderId());
        boolean isExists = contractSignPaperVOS.stream().anyMatch(i -> Objects.equals(i.getTemplateContractType(), createPaperContractReq.getPaperContractType()));
        if (isExists) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "当前纸质合同已经存在,不能重复上传");
        }
        if (CollectionUtils.isNotEmpty(createPaperContractReq.getImg()) && createPaperContractReq.getImg().size() > 6) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "上传的图片数量最多6张");
        }
        return Result.success(contractSignRecordPaperRepository.savePaperContract(createPaperContractReq));
    }

    @Override
    public Result<ContractSignPaperVO> queryById(Integer id) {
        ContractSignRecordPaperEntity contractSignRecordPaperEntity = contractSignRecordPaperRepository.queryById(id);

        Integer recordId = contractSignRecordPaperEntity.getId();

        OaProcessRecordPO oaProcessRecordPO = oaProcessRecordService.getByRefundByRecordId(recordId);

        ContractSignPaperVO contractSignDetailVO = appMonthContractSignRecordConverter.entitySignPaper2ContractSignPaperVo(contractSignRecordPaperEntity);
        Optional.ofNullable(contractSignDetailVO).ifPresent(
                i -> {
                    UserEntity userEntity = userRepository.queryById(contractSignDetailVO.getStaffId());
                    contractSignDetailVO.setOptName(Objects.nonNull(userEntity) ? userEntity.getName() : "");
                    if (ObjectUtil.isNotEmpty(oaProcessRecordPO)) {
                        OaProcessIdRelationPO oneByProcessId = oaProcessIdRelationService.getOneByProcessId(oaProcessRecordPO.getProcessId());
                        if (ObjectUtil.isNotEmpty(oneByProcessId)) {
                            contractSignDetailVO.setApproveId(oneByProcessId.getLocalProcessId());
                        }
                    }
                });
        return Result.success(contractSignDetailVO);
    }


    @Override
    public Result<Boolean> deleteById(SavePaperContractReq req) {
        log.info("删除纸质合同{}", JSONUtil.toJsonStr(req));
        return Result.success(contractSignRecordPaperRepository.deleteById(req.getContractId()));
    }

    @Override
    public Result<List<CanContractSignPaperVO>> queryCanPaperList(CanPaperContractQuery canPaperContractQuery) {
        List<ContractSignPaperVO> contractSignPaperVOS = this.paperList(canPaperContractQuery.getOrderId());
        List<CanContractSignPaperVO> canContractSignPaperVOS = Lists.newArrayList();
        //其他合同
        if (Objects.equals(canPaperContractQuery.getPaperContractType(), TemplateContractTypeV6Enum.OTHER.code())) {
            for (TemplateContractTypeV7Enum i : TemplateContractTypeV7Enum.values()) {
                CanContractSignPaperVO paperVO = new CanContractSignPaperVO();
                paperVO.setTemplateContractType(i.code());
                paperVO.setContractName(i.desc());
                paperVO.setSign(contractSignPaperVOS.stream().anyMatch(x -> x.getTemplateContractType().equals(i.code())));
                canContractSignPaperVOS.add(paperVO);
            }
        } else {
            CanContractSignPaperVO paperVO = new CanContractSignPaperVO();
            paperVO.setTemplateContractType(canPaperContractQuery.getPaperContractType());
            paperVO.setContractName(TemplateContractTypeEnum.fromCode(canPaperContractQuery.getPaperContractType()));
            paperVO.setSign(contractSignPaperVOS.stream().anyMatch(x -> x.getTemplateContractType().equals(canPaperContractQuery.getPaperContractType())));
            canContractSignPaperVOS.add(paperVO);
        }

        return Result.success(canContractSignPaperVOS);
    }

    private List<ContractSignPaperVO> paperList(Integer orderId) {
        List<ContractSignRecordPaperEntity> contractSignRecordPaperEntities = contractSignRecordPaperRepository.getByOrderId(orderId);
        return appMonthContractSignRecordConverter.entitySignPaper2ContractSignPaperVoList(contractSignRecordPaperEntities);
    }

}

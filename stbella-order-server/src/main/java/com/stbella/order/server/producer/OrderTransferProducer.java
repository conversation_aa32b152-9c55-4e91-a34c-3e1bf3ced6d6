package com.stbella.order.server.producer;

import com.alibaba.fastjson.JSON;
import com.stbella.order.server.order.month.dto.OrderTransferEventBody;
import com.stbella.pulsar.template.PulsarTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class OrderTransferProducer {

    @Resource
    private PulsarTemplate pulsarTemplate;

    private String ORDER_TRANSFER_EVENT_TOPIC = "";

    @Value("${pulsar.nameSpace}")
    private void setNameSpace(String nameSpace) {
        ORDER_TRANSFER_EVENT_TOPIC = "persistent://pulsar-44k4paxzz5vg/" + nameSpace + "/order_transfer_notify";
    }

    @Async
    public void sendMq(Integer incomeId) {

        OrderTransferEventBody orderEventBody = new OrderTransferEventBody();
        orderEventBody.setIncomeId(incomeId);
        log.info("发送支付记录转移事件mq topic:{} 消息: {}", ORDER_TRANSFER_EVENT_TOPIC, JSON.toJSONString(orderEventBody));
        try {
            pulsarTemplate.send(ORDER_TRANSFER_EVENT_TOPIC, JSON.toJSONString(orderEventBody));
        } catch (Exception e) {
            log.error("发送 mq 消息失败: {}", JSON.toJSONString(orderEventBody));
            log.error("发送 mq 消息失败: 异常：", e);
        }

    }
}

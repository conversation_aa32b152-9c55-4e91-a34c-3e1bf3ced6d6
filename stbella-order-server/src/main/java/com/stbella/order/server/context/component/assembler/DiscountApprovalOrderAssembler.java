package com.stbella.order.server.context.component.assembler;

import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.repository.OrderUserSnapshotRepository;
import com.stbella.order.domain.repository.OrderVoucherRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.List;

/**
 * 折扣审批订单组装
 */
@Slf4j
@Component
@SnowballComponent(name = "折扣审批订单组装", desc = "折扣审批订单组装")
public class DiscountApprovalOrderAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    OrderUserSnapshotRepository orderUserSnapshotRepository;
    @Resource
    OrderVoucherRepository orderVoucherRepository;

    @Override
    public void run(FlowContext bizContext) {

        HeOrderEntity heOrderEntity = bizContext.getAttribute(HeOrderEntity.class);

        OrderReductionEntity orderReductionEntity = bizContext.getAttribute(OrderReductionEntity.class);
        if (!Boolean.TRUE.equals(orderReductionEntity.getDisCountApprove())) {
            return;
        }

        CfgStoreEntity storeEntity = bizContext.getAttribute(CfgStoreEntity.class);
        heOrderEntity.setStore(storeEntity);
        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(heOrderEntity.getOrderId());
        heOrderEntity.setCustomer(heOrderUserSnapshotEntity);

        List<HeOrderVoucherEntity> heOrderVoucherEntities = orderVoucherRepository.getByOrderId(heOrderEntity.getOrderId());
        heOrderEntity.setVoucherEntityList(heOrderVoucherEntities);

        bizContext.setAttribute(HeOrderEntity.class, heOrderEntity);

    }

}

package com.stbella.order.server.utils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

public class DateUtils extends DateUtil {


    public static String format = "yyyy-MM-dd HH:mm:ss";
    public static String formatyyyyMM = "yyyyMM";
    public static String formatyyyy_MM = "yyyy-MM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String dateFormatDateTime(Date date) {
        return DateUtils.format(date, DatePattern.NORM_DATETIME_PATTERN);
    }

    /**
     * 日期格式化
     * @param date
     * @return
     */
    public static String FormatDate(Date date) {
        return DateUtils.format(date, YYYY_MM_DD);
    }

    public static String formatNormal(Long timeSpanTenBit) {
        Date date = new Date(timeSpanTenBit * 1000L);
        return new SimpleDateFormat(format).format(date);
    }


    /**
     * 日期格式化
     * @param time
     * @return
     */
    public static String FormatDate(Long time) {
        return DateUtils.format(new Date(time), YYYY_MM_DD);
    }

    /**
     * 日期格式化
     * @param time
     * @return
     */
    public static String FormatDate(Long time, String format) {
        return DateUtils.format(new Date(time), format);
    }


    /**
     * 获取本月第一天
     *
     * @return
     */
    public static Date getDayOfMonth() {
        Calendar calendar = getCalendar(null);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        return calendar.getTime();
    }

    /**
     * 日期 分割
     *
     * @return
     */
    public static int[] getYearMonthDay(Date date) {
        Calendar calendar = getCalendar(date);

        int[] timeArray = new int[]{calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH)+1,calendar.get(Calendar.DATE)};

        return timeArray;
    }


    /**
     * 获取今天中午某个小时的时刻
     *
     * @return
     */
    public static Date getHourOfDay(Date date, Integer hours) {
        Calendar calendar = getCalendar(date);
        calendar.set(Calendar.HOUR_OF_DAY, hours);
        return calendar.getTime();
    }

    private static Calendar getCalendar(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(ObjectUtil.isNull(date) ? new Date() : date);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar;
    }


    public static boolean isFirstDayOfMonth() {
        return DateUtil.dayOfMonth(new Date()) == 1;
    }

    public static boolean isFirstDayOfYear() {
        return DateUtil.dayOfYear(new Date()) == 1;
    }

    /**
     * 本月或上月第一天00:00:00
     * flag=1--本月
     * flag=2--上月
     * getFirstDay()
     *
     * @param flag
     * @return
     */
    public static Date getFirstDay(Integer flag) {
        Calendar c = Calendar.getInstance();
        if (flag == 1) {
            c.add(Calendar.MONTH, 0); //获取当前月第一天
        } else if (flag == 2) {
            c.add(Calendar.MONTH, -1); //获取上月第一天
        }
        c.set(Calendar.DAY_OF_MONTH, 1); //设置为1号,当前日期既为本月第一天
        c.set(Calendar.HOUR_OF_DAY, 0); //将小时至0
        c.set(Calendar.MINUTE, 0); //将分钟至0
        c.set(Calendar.SECOND, 0); //将秒至0
        c.set(Calendar.MILLISECOND, 0); //将毫秒至0
        return c.getTime();
    }


    /**
     * 获取某年的第一天
     *
     * @param year
     * @return
     */
    public static Date getFirstDayOfYear(Integer year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        Date currYearFirst = calendar.getTime();
        return currYearFirst;
    }

    /**
     * 获取某年的最后一天
     *
     * @param year
     * @return
     */
    public static Date getLastDayOfYear(Integer year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.roll(Calendar.DAY_OF_YEAR, -1);
        calendar.set(Calendar.HOUR, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Date currYearLast = calendar.getTime();
        return currYearLast;
    }

    /**
     * 获取某月的第一天
     *
     * @param month
     * @return
     */
    public static Date getFirstDayOfMonth(Integer year, Integer month) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1);
        Date currYearFirst = calendar.getTime();
        return currYearFirst;
    }

    /**
     * 获取某月的最后一天
     *
     * @param month
     * @return
     */
    public static Date getLastDayOfMonth(Integer year, Integer month) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1);
        calendar.set(Calendar.HOUR, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.roll(Calendar.DAY_OF_MONTH, -1);
        Date currYearFirst = calendar.getTime();
        return currYearFirst;
    }

    public static Date getAfterDate(int field, int nums, Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(field, nums);
        return calendar.getTime();
    }

    public static Date getYesterdayStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    public static Date getTodayStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    // 获取指定年指定月的开始天数和结束天数
    public static Map<String, Date> getFirstDayAndLastDayOfTheSpecifiedMonth(int year, int month) {
        // 获取当前分区的日历信息
        Calendar calendar = Calendar.getInstance();
        // 设置年
        calendar.set(Calendar.YEAR, year);
        // 设置月，月份从0开始
        calendar.set(Calendar.MONTH, month - 1);
        // 设置为指定月的第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        // 获取指定月第一天的时间
        Date start = calendar.getTime();
        // 设置日历天数为当前月实际天数的最大值，即指定月份的最后一天
        calendar.set(Calendar.DATE, calendar.getActualMaximum(Calendar.DATE));
        // 获取最后一天的时间
        Date end = calendar.getTime();
        // 设置返回信息,返回样式根据需求自行格式化
        Map<String, Date> dateMap = new HashMap<>();
        dateMap.put("start", start);
        dateMap.put("end", end);
        return dateMap;
    }

    /**
     * 指定年开始
     *
     * @param year
     * @return
     */
    public static Date getStartByYear(Integer year) {
        try {
            return new SimpleDateFormat(format).parse(year + "-01-01 00:00:00");
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取上月第一天
     * @return
     */
    public static Date getLastMonthStart() {

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1); //获取上月第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1); //设置为1号,当前日期既为本月第一天
        calendar.set(Calendar.HOUR_OF_DAY, 0); //将小时至0
        calendar.set(Calendar.MINUTE, 0); //将分钟至0
        calendar.set(Calendar.SECOND, 0); //将秒至0
        calendar.set(Calendar.MILLISECOND, 0); //将毫秒至0
        return calendar.getTime();
    }


    public static int getYear(Date date) {
        Calendar calendar = getCalendar(date);
        return calendar.get(Calendar.YEAR);
    }

    public static int getMonth(Date date) {
        Calendar calendar = getCalendar(date);
        return calendar.get(Calendar.MONTH)+1;
    }

    public static int getDay(Date date) {
        Calendar calendar = getCalendar(date);
        return calendar.get(Calendar.DATE)+1;
    }

    /**
     * 指定年结束
     *
     * @param year
     * @return
     */
    public static Date getEndByYear(Integer year) {
        try {
            return new SimpleDateFormat(format).parse(year + "-12-31 23:59:59");
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 指定季度开始
     *
     * @param year
     * @return
     */
    public static Date getStartByQuarter(Integer year, Integer quarter) {
        try {
            if (quarter == 1) {
                return new SimpleDateFormat(format).parse(year + "-01-01 00:00:00");
            } else if (quarter == 2) {
                return new SimpleDateFormat(format).parse(year + "-04-01 00:00:00");
            } else if (quarter == 3) {
                return new SimpleDateFormat(format).parse(year + "-07-01 00:00:00");
            } else {
                return new SimpleDateFormat(format).parse(year + "-10-01 00:00:00");
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 指定季度开始
     *
     * @param year
     * @return
     */
    public static Date getEndByQuarter(Integer year, Integer quarter) {
        try {
            if (quarter == 1) {
                return new SimpleDateFormat(format).parse(year + "-03-31 23:59:59");
            } else if (quarter == 2) {
                return new SimpleDateFormat(format).parse(year + "-06-30 23:59:59");
            } else if (quarter == 3) {
                return new SimpleDateFormat(format).parse(year + "-09-30 23:59:59");
            } else {
                return new SimpleDateFormat(format).parse(year + "-12-31 23:59:59");
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }


    public static Calendar getFirstDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar;
    }

    /**
     * 根据两个日期获取区间月份
     *
     * @param start 小日期（如果传入的小日期和大日期顺序是反的，会自动修改）
     * @param end   大日期
     * @return
     */
    public static List<Date> getMonthByStartAndEnd(Date start, Date end) {
        List<Date> dateList = new ArrayList<>();
        //如果传的时间是反的
        if (start.after(end)) {
            Date temp = start;
            start = end;
            end = temp;
        }

        //获取当月第一天
        Calendar startDay = getFirstDayOfMonth(start);
        dateList.add(startDay.getTime());
        Calendar endDay = getFirstDayOfMonth(end);

        while (startDay.getTime().compareTo(endDay.getTime()) != 0) {
            startDay.add(Calendar.MONTH, 1);
            dateList.add(startDay.getTime());
        }
        return dateList;
    }

    /**
     * 根据两个日期获取区间月份，根据format返回对应格式
     *
     * @param start
     * @param end
     * @param format 格式化
     * @return
     */
    public static List<String> getMonthStrByStartAndEnd(Date start, Date end, String format) {
        List<String> returnList = new ArrayList<>();

        List<Date> monthByStartAndEnd = getMonthByStartAndEnd(start, end);

        monthByStartAndEnd.forEach(m -> {
            returnList.add(new SimpleDateFormat(format).format(m));
        });

        return returnList;
    }

    public static String addMonth(Date date, Integer num, String format) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, num);
        return new SimpleDateFormat(format).format(calendar.getTime());
    }

    public static Date addMonth(Date date, Integer num) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, num);
        return calendar.getTime();
    }

    /**
     * @param date         传入时间的中文
     * @param num          加减月份（传0月当月日期格式转换）
     * @param inFormat     传入时间的格式化
     * @param returnFormat 输出时间的格式化
     * @return
     */
    public static String addMonthFormat(String date, Integer num, String inFormat, String returnFormat) {
        try {
            Date parse = new SimpleDateFormat(inFormat).parse(date);
            return addMonth(parse, num, returnFormat);
        } catch (ParseException e) {
            throw new BusinessException("格式化日期失败");
        }
    }

    public static String addMonth(String date, Integer num) {
        return addMonthFormat(date, num, formatyyyyMM, formatyyyyMM);
    }


    /**
     * 获取两个时间之间的所有小时
     *
     * @param start
     * @param end
     * @return
     */
    public static List<String> getHoursByStartAndEnd(Date start, Date end) {
        List<String> result = new ArrayList<>();
        //防止时间相反
        if (start.getTime() > end.getTime()) {
            Date temp = end;
            start = end;
            end = temp;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(start);


        while (calendar.getTime().getTime() <= end.getTime()) {
            result.add(addMonth(calendar.getTime(), 0, "yyyy-MM-dd HH:mm:ss"));
            calendar.add(Calendar.HOUR, 1);
        }
        return result;
    }

    public static List<Integer> getAllDayOfMonth(Integer year, Integer month) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1);
        int actualMaximum = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        List<Integer> day = new ArrayList<>();
        for (int i = 1; i <= actualMaximum; i++) {
            day.add(i);
        }
        return day;
    }

    /**
     * 获取两个日期之前的月份
     *
     * @param start 2021-01-13 12:11:11
     * @param end   2021-03-13 12:11:11
     * @return [2021-1, 2021-2, 2021-3]
     */
    public static List<String> getMonthOfYear(Date start, Date end) {

        //防止时间相反
        if (start.getTime() > end.getTime()) {
            Date temp = end;
            start = end;
            end = temp;
        }

        start = formatData(start, formatyyyy_MM);
        end = formatData(end, formatyyyy_MM);

        List<String> result = new ArrayList();
        Calendar firstDayOfMonth = getFirstDayOfMonth(start);
        while (firstDayOfMonth.getTime().getTime() <= end.getTime()) {
            result.add(DateUtil.year(firstDayOfMonth.getTime()) + "-" + (DateUtil.month(firstDayOfMonth.getTime()) + 1));
            firstDayOfMonth.add(Calendar.MONTH, 1);
        }
        return result;
    }

    public static Date formatData(Date date, String format) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        String str = simpleDateFormat.format(date);
        try {
            return simpleDateFormat.parse(str);
        } catch (ParseException e) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "其他错误");
        }
    }

    public static Calendar getByYearMonthDay(Integer year, Integer month, Integer day) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1);
        calendar.set(Calendar.DAY_OF_MONTH, day);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        return calendar;
    }

    /**
     * 秒转成DateStr
     *
     * @param seconds
     * @return
     */
    public static String secondsToTimeStr(Integer seconds) {
        return DateUtil.formatDateTime(secondsTaoDate(seconds));
    }



    /**
     * 秒转成DateStr
     *
     * @param seconds
     * @return
     */
    public static String secondsToTimeStr(Long seconds) {
        return DateUtil.formatDateTime(new Date(seconds * 1000L));
    }

    /**
     * 秒转成Date
     * @param seconds
     * @return
     */
    public static Date secondsTaoDate(Integer seconds) {
        return new Date(seconds * 1000L);
    }


    public static Date toDate(LocalDate localDate) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
        return Date.from(instant);
    }

    public static Date[] getLastMonthFirstDayAndLastDay(LocalDate currentDate) {
        // 获取上个月的 YearMonth 对象
        YearMonth lastMonth = YearMonth.from(currentDate.minusMonths(1));

        // 获取上个月的首日
        LocalDate firstDayOfLastMonth = lastMonth.atDay(1);
        // 获取上个月的末日
        LocalDate lastDayOfLastMonth = lastMonth.atEndOfMonth();

        return new Date[]{toDate(firstDayOfLastMonth), toDate(lastDayOfLastMonth)};
    }



}

package com.stbella.order.server.listener.event;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 订单减免成功监听事件
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderDecreaseSuccessEvent implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单编号")
    private Integer orderId;

    @ApiModelProperty("减免id")
    private Long reductionId;

    /**
     * @see com.stbella.order.server.order.cts.enums.DecreaseTypeEnum
     */
    @ApiModelProperty(value = "减免通过类型")
    private Integer successType;

}

package com.stbella.order.server.order.month.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stbella.order.server.order.cts.request.order.OrderPayRecordAdminSearchRequest;
import com.stbella.order.server.order.cts.request.order.PayRecordPerformanceQuery;
import com.stbella.order.server.order.cts.response.order.OrderPayRecordVO;
import com.stbella.order.server.order.month.entity.MoneyRecordPO;
import com.stbella.order.server.order.month.entity.OrderPayRecordPO;
import com.stbella.order.server.order.month.entity.SuccessAndProcessRefundAmountPO;
import com.stbella.order.server.order.nutrition.dto.CapitalRecordExportDTO;
import com.stbella.order.server.order.nutrition.request.QueryCapitalRecordRequest;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 订单支付记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2021-11-29
 */
public interface OrderPayRecordMapper extends BaseMapper<OrderPayRecordPO> {


    /**
     * 导出月子餐订单资金记录
     *
     * @param capitalRecordRequest 筛选参数
     * @return list
     */
    List<CapitalRecordExportDTO> doExportCapitalRecord(@Param("capitalRecordRequest") QueryCapitalRecordRequest capitalRecordRequest);


    /**
     * 获取到家业务的资金流水
     *
     * @param page
     * @param query
     * @return
     */
    Page<OrderPayRecordPO> getCtsPayRecordList(Page page, @Param("query") OrderPayRecordAdminSearchRequest query);


    /**
     * @param payRecordPerformanceQuery 支付记录查询参数
     * @return List<OrderPayRecordPO> list结果集
     */
    List<OrderPayRecordVO> queryPerformanceOfPayRecord(PayRecordPerformanceQuery payRecordPerformanceQuery);


}

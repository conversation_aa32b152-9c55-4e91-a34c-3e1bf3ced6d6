package com.stbella.order.server.convert;

import cn.hutool.core.date.DateUtil;
import com.stbella.marketing.api.res.OrderProductBenefitVO;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.server.order.month.res.OtherBenefitsVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-12 11:28
 */
@Mapper(componentModel = "spring", imports = {DateUtil.class, AmountChangeUtil.class})
public interface OrderBenefitsConverter {


    List<OtherBenefitsVO> convertOrderProductBenefitVO2OtherBenefitsVO(List<OrderProductBenefitVO> orderProductBenefitVOS);
}

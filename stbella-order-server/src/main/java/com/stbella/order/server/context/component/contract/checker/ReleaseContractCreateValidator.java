package com.stbella.order.server.context.component.contract.checker;

import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.enums.month.AheadOutRoomEnum;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.domain.order.month.entity.AheadOutRoomEntity;
import com.stbella.order.domain.repository.AheadOutRoomRepository;
import com.stbella.order.server.order.month.req.AheadOutRoomQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-23 15:33
 */
@Component
@Slf4j
public class ReleaseContractCreateValidator extends AbstractContractCreateValidator {

    @Resource
    private AheadOutRoomRepository aheadOutRoomRepository;

    @Override
    public boolean doValidate(ContractValidationContext context) {
        //查找客户名称
        //region 取订单快照信息表
        AheadOutRoomQuery roomQuery = new AheadOutRoomQuery();
        roomQuery.setOrderId(context.getRequest().getOrderId());
        AheadOutRoomEntity aheadOutRoomEntity = aheadOutRoomRepository.queryByOrderId(roomQuery);
        if (Objects.isNull(aheadOutRoomEntity)) {
            throw new BusinessException(ResultEnum.PARAM_ERROR, "提前离管未申请,不能签署合同");
        }

        if (AheadOutRoomEnum.STATE_DISENABLE.code().equals(aheadOutRoomEntity.getState())) {
            throw new BusinessException(ResultEnum.PARAM_ERROR, "合同已经签署无需在创建");
        }
        return true;
    }

    @Override
    public TemplateContractTypeEnum templateContractType() {
        return TemplateContractTypeEnum.RELEASE;
    }
}
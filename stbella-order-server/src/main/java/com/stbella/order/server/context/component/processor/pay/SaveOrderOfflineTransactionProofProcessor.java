package com.stbella.order.server.context.component.processor.pay;

import com.stbella.order.common.enums.month.IncomeProofRecordPayTypeEnum;
import com.stbella.order.domain.order.month.entity.HeIncomeProofRecordEntity;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.repository.IncomeProofRecordRepository;
import com.stbella.order.common.enums.core.OfflineAuditStatusV2Enum;
import com.stbella.platform.order.api.req.OmniOfflinePayRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-05-29  15:20
 * @Description: 保存线下交易记录
 */
@Slf4j
@Component
@SnowballComponent(name = "保存线下交易记录", desc = "保存线下交易相关凭证与备注")
public class SaveOrderOfflineTransactionProofProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    com.stbella.order.server.utils.IdGenUtils IdGenUtils;

    @Resource
    private IncomeProofRecordRepository incomeProofRecordRepository;

    @Override
    public void run(FlowContext bizContext) {

        OmniOfflinePayRequest request = bizContext.getAttribute(OmniOfflinePayRequest.class);
        HeIncomeRecordEntity incomeRecordEntity = bizContext.getAttribute(HeIncomeRecordEntity.class);

        HeIncomeProofRecordEntity heIncomeProofRecordEntity = new HeIncomeProofRecordEntity();
        heIncomeProofRecordEntity.setIncomeSn(incomeRecordEntity.getIncomeSn());
        heIncomeProofRecordEntity.setIncomeId(incomeRecordEntity.getId());
        heIncomeProofRecordEntity.setCreatedAt(new Date());
        heIncomeProofRecordEntity.setUpdatedAt(new Date());
        heIncomeProofRecordEntity.setOrderId(incomeRecordEntity.getOrderId());
        heIncomeProofRecordEntity.setStatus(OfflineAuditStatusV2Enum.UNDER_REVIEW.getCode());
        heIncomeProofRecordEntity.setPayProof(request.getPayProof());
        heIncomeProofRecordEntity.setPayType(IncomeProofRecordPayTypeEnum.REMITTANCE.getCode());
        heIncomeProofRecordEntity.setRemark(request.getRemark());
        heIncomeProofRecordEntity.setIncomeProof(incomeRecordEntity.getIncome());
        heIncomeProofRecordEntity.setCreatedId(Integer.valueOf(request.getOperator().getOperatorGuid()));
        heIncomeProofRecordEntity.setCreatedName(request.getOperator().getOperatorName());
        heIncomeProofRecordEntity.setCreatedPhone(request.getOperator().getOperatorPhone());
        Integer proofId = incomeProofRecordRepository.saveOne(heIncomeProofRecordEntity);
        heIncomeProofRecordEntity.setId(proofId);

    }


}

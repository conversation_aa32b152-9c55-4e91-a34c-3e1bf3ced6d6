package com.stbella.order.server.context.component.cancel.validate;

import com.stbella.core.exception.BusinessException;
import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import java.util.List;
import java.util.Objects;

@Slf4j
@Component
@SnowballComponent(name = "客诉工单取消审批-基础信息验证")
public class ComplaintCancelValidateProcessor implements IExecutableAtom<FlowContext> {

    @Override
    public void run(FlowContext bizContext) {

        HeCustomerComplaintsEntity heCustomerComplaintsEntity = bizContext.getAttribute(HeCustomerComplaintsEntity.class);
        if (heCustomerComplaintsEntity == null) {
            log.error("heCustomerComplaintsEntity is null");
            throw new BusinessException("客诉工单不存在");
        }
        if (heCustomerComplaintsEntity.getOrderId() == null) {
            log.error("heCustomerComplaintsEntity.getOrderId is null");
            throw new BusinessException("客诉工单关联的订单ID为空");
        }

        HeOrderEntity heOrderEntity = bizContext.getAttribute(HeOrderEntity.class);
        if (heOrderEntity == null) {
            log.error("heOrderEntity is null");
            throw new BusinessException("对应的订单不存在");
        }

        boolean newOrder = heOrderEntity.isNewOrder();

        if (Objects.isNull(heCustomerComplaintsEntity.getCashRefundId()) && Objects.isNull(heCustomerComplaintsEntity.getRefundOrderId())){
            log.error("当前客诉工单无退款信息");
            return;
        }
        List<HeOrderRefundEntity> heOrderRefundList = bizContext.getListAttribute(HeOrderRefundEntity.class);

    }
}
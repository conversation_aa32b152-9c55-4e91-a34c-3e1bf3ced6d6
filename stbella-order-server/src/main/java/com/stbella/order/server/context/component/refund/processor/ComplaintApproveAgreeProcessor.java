package com.stbella.order.server.context.component.refund.processor;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.customer.server.ecp.entity.UserPO;
import com.stbella.customer.server.ecp.service.UserService;
import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.month.server.request.RefundApprovalRequest;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsEntity;
import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsRefundDraftEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.TabClientEntity;
import com.stbella.order.domain.repository.ClientRepository;
import com.stbella.order.domain.repository.HeCustomerComplaintsRefundDraftRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.order.month.enums.CustomerComplaintsStatusEnum;
import com.stbella.order.server.order.month.request.approval.ApprovalStatusInfo;
import com.stbella.order.server.strategy.approval.OfflineRemittanceRefundApprovalStrategy;
import com.stbella.order.server.strategy.approval.OldOrderBalanceRefundApprovalStrateg;
import com.stbella.order.server.strategy.approval.OriginalRoadRefundApprovalStrategy;
import com.stbella.order.server.strategy.approval.ProductionRefundApprovalStrategy;
import com.stbella.order.server.utils.JsonUtil;
import com.stbella.order.server.utils.wangdian.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.FlowIdentity;
import top.primecare.snowball.flow.core.definition.FlowIdentityBuilder;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;


@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "客诉闭环退款审批成功")
public class ComplaintApproveAgreeProcessor implements IExecutableAtom<FlowContext> {

    @DubboReference
    private UserService userService;
    @Resource
    private ClientRepository clientRepository;
    @Resource
    private OfflineRemittanceRefundApprovalStrategy offlineRemittanceRefundApprovalStrategy;
    @Resource
    private OldOrderBalanceRefundApprovalStrateg oldOrderBalanceRefundApprovalStrateg;
    @Resource
    private OriginalRoadRefundApprovalStrategy originalRoadRefundApprovalStrategy;
    @Resource
    private ProductionRefundApprovalStrategy productionRefundApprovalStrategy;
    @Resource
    private HeCustomerComplaintsRefundDraftRepository customerComplaintsRefundDraftRepository;
    @Resource
    private OrderRepository orderRepository;

    @Override
    public boolean condition(FlowContext context) {
        HeCustomerComplaintsEntity customerComplaintsEntity = context.getAttribute(HeCustomerComplaintsEntity.class);
        return ObjectUtil.isNotEmpty(customerComplaintsEntity.getRefundOrderId());
    }

    @Override
    public void run(FlowContext bizContext) {

        HeCustomerComplaintsEntity customerComplaintsEntity = bizContext.getAttribute(HeCustomerComplaintsEntity.class);

        Long orderId = customerComplaintsEntity.getOrderId();

        HeOrderEntity heOrderEntity = orderRepository.getByOrderId(orderId.intValue());


        ApprovalStatusInfo approvalStatusInfo = bizContext.getAttribute(ApprovalStatusInfo.class);

        OrderApproveRecordTypeEnum type = (OrderApproveRecordTypeEnum) bizContext.getAttribute("type");
        String params = null;
        String messageId = (String) bizContext.getAttribute("messageId");
        String processId = (String) bizContext.getAttribute("processId");

        if (heOrderEntity.isNewOrder()) {
            //TODO ，临时改成通过
            customerComplaintsEntity.setComplaintStatus(CustomerComplaintsStatusEnum.PROCESS.getCode());
            //走新逻辑
            // 业务线：场景：订单类型
            FlowIdentityBuilder flowIdentityBuilder = FlowIdentity.builder()
                    .bizActivity(BizActivityEnum.ORDER_REFUND_AFTER_APPROVE.code())
                    .idSlice("Order")
                    .idSlice("Refund")
                    .idSlice("After")
                    .idSlice("Approve");
            FlowContext context = new FlowContext();

            RefundApprovalRequest refundApprovalRequest = new RefundApprovalRequest();

            Integer creator = customerComplaintsEntity.getCreator();

            UserPO creatorInfo = userService.queryUserById(creator.longValue());
            if (ObjectUtil.isNotEmpty(creatorInfo)) {
                refundApprovalRequest.setPhone(creatorInfo.getPhone());
            }

            TabClientEntity tabClientEntity = clientRepository.getTabClientById(heOrderEntity.getClientUid());
            if (ObjectUtil.isNotEmpty(tabClientEntity)) {
                refundApprovalRequest.setCustomerName(tabClientEntity.getName());
            }

            refundApprovalRequest.setOrderId(customerComplaintsEntity.getOrderId().intValue());
            refundApprovalRequest.setOrderRefundId(customerComplaintsEntity.getRefundOrderId().intValue());

            context.setAttribute(RefundApprovalRequest.class, refundApprovalRequest);
            context.setAttribute(ApprovalStatusInfo.class, approvalStatusInfo);
            context.setAttribute(HeOrderEntity.class, heOrderEntity);
            context.setAttribute(OtherConstant.CONTINUE, true);
            boolean deposit = heOrderEntity.isDepositOrder();

            bizContext.setAttribute(OtherConstant.DEPOSIT, deposit);
            SnowballFlowLauncher.fire(flowIdentityBuilder.build(), context);
        } else {

            log.info("客诉闭环开始处理老订单的退款逻辑：{}", customerComplaintsEntity.getId());

            HeCustomerComplaintsRefundDraftEntity complaintsRefundDraftEntity = customerComplaintsRefundDraftRepository.queryByComplaintsId(customerComplaintsEntity.getId());

            log.info("客诉闭环开始处理老订单的退款逻辑：complaintsRefundDraftEntity{}", JsonUtil.write(complaintsRefundDraftEntity));

            String refundEnum = complaintsRefundDraftEntity.getRefundEnum();

            if (StringUtils.isEmpty(refundEnum)) {
                log.error("老订单客诉审批，找不到分支:{}", customerComplaintsEntity.getId());
                return;
            }

            params = complaintsRefundDraftEntity.getApproveReqJson();

            OrderApproveRecordTypeEnum enumByPlatformId = OrderApproveRecordTypeEnum.getEnumByPlatformId(new Integer(refundEnum));

            log.info("客诉闭环开始处理老订单的退款逻辑：enumByPlatformId{}", JsonUtil.write(enumByPlatformId));

            try {
                switch (enumByPlatformId) {
                    case ORDER_PRODUCTION_REFUND_APPROVE:
                        productionRefundApprovalStrategy.handleApproval(type, params, messageId, approvalStatusInfo, processId);
                        break;
                    case REFUND_APPROVAL_OF_THE_ORIGINAL_RETURN:
                        originalRoadRefundApprovalStrategy.handleApproval(type, params, messageId, approvalStatusInfo, processId);
                        break;
                    case ORDER_BALANCE_REFUND:
                        oldOrderBalanceRefundApprovalStrateg.handleApproval(type, params, messageId, approvalStatusInfo, processId);
                        break;
                    case OFFLINE_REMITTANCE_FOR_REFUND_APPROVAL:
                        offlineRemittanceRefundApprovalStrategy.handleApproval(type, params, messageId, approvalStatusInfo, processId);
                        break;
                }
            } catch (Exception e) {
                log.error("客诉闭环处理老订单出错：{}", e.getMessage());
            }
        }
    }

}

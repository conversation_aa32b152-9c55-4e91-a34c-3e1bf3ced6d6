package com.stbella.order.server.context.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.care.api.appointment.req.ListCheckInDateReq;
import com.stbella.care.api.appointment.res.RoomCheckInDateVO;
import com.stbella.care.server.care.service.RoomExternalQuery;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.customer.server.ecp.vo.ClientInfoVO;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.core.*;
import com.stbella.order.common.enums.month.IncomeReceiptTypeEnum;
import com.stbella.order.common.enums.month.OrderStatusV2Enum;
import com.stbella.order.common.enums.month.RefundRecordPayStatusEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.repository.*;
import com.stbella.order.infrastructure.repository.impl.IncomePaidAllocationRepositoryImpl;
import com.stbella.order.server.context.component.StoreCurrencyContainer;
import com.stbella.order.server.convert.OrderConvert;
import com.stbella.order.server.manager.StoreManager;
import com.stbella.order.server.manager.TabClientManager;
import com.stbella.order.server.order.month.req.OrderMonthIncomeQuery;
import com.stbella.order.server.order.month.req.OrderQuery;
import com.stbella.order.server.order.month.res.*;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.order.server.order.order.res.OrderMainInfo;
import com.stbella.platform.order.api.OrderQueryService;
import com.stbella.platform.order.api.deposit.DepositQueryFacade;
import com.stbella.platform.order.api.reduction.req.ReductionQuery;
import com.stbella.platform.order.api.req.OrderDetailQuery;
import com.stbella.platform.order.api.res.QueryDepositAmountRes;
import com.stbella.store.common.enums.core.GoodsTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-03-27  13:07
 * @Description: 订单查询服务
 */
@Service
@DubboService
@Slf4j
public class OrderQueryServiceImpl implements OrderQueryService {

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderGoodsRepository orderGoodsRepository;

    @Resource
    private OrderConvert orderConvert;
    @Resource
    StoreRepository storeRepository;
    @Resource
    OrderUserSnapshotRepository orderUserSnapshotRepository;
    @Resource
    private OrderReductionRepository orderReductionRepository;
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @Resource
    private IncomeProofRecordRepository incomeProofRecordRepository;
    @Resource
    private TabClientManager tabClientManager;
    @Resource
    private StoreManager storeManager;
    @Autowired
    DepositQueryFacade depositQueryFacade;
    @Resource
    UserRepository userRepository;
    @DubboReference
    private RoomExternalQuery roomExternalQuery;
    @Autowired
    private IncomePaidAllocationRepositoryImpl incomePaidAllocationRepositoryImpl;


    /**
     * 查询订单
     *
     * @param query
     * @return
     */
    @Override
    public Result<OrderMainInfo> getOne(OrderDetailQuery query) {

        HeOrderEntity orderEntity = null;
        //根据参数判断是通过id 还是sn
        if (ObjectUtil.isNotNull(query.getOrderSn())) {
            orderEntity = orderRepository.getByOrderSn(query.getOrderSn());
        } else {
            orderEntity = orderRepository.getByOrderId(query.getOrderId());
        }

        if (ObjectUtil.isNull(orderEntity)) {
            throw new BusinessException(ErrorCodeEnum.BIZ_ERROR.code() + "", "订单号不存在，请重新输入");
        }

        if (!orderEntity.isNewOrder() && (ObjectUtil.isEmpty(orderEntity.getPayFinishAt()) || orderEntity.getPayFinishAt() == 0)) {
            //老订单的完款时间根据支付状态为 全部支付或者超额支付
            if (orderEntity.getPayStatus().equals(OmniPayStatusEnum.PAY_OFF.getCode()) || orderEntity.getPayStatus().equals(OmniPayStatusEnum.EXCESS_PAY.getCode())) {
                List<HeIncomeRecordEntity> allRecordListByOrderId = incomeRecordRepository.getAllRecordListByOrderId(orderEntity.getOrderId());
                allRecordListByOrderId = Collections.unmodifiableList(allRecordListByOrderId.stream().filter(a -> a.getStatus().equals(1)).collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(allRecordListByOrderId)) {
                    List<HeIncomeRecordEntity> sortedList = allRecordListByOrderId.stream()
                            .sorted(Comparator.comparingLong(HeIncomeRecordEntity::getPayTime).reversed()).collect(Collectors.toList());
                    orderEntity.setPayFinishAt(sortedList.get(0).getPayTime() * 1000L);
                }

            }
        }

        OrderMainInfo orderMainInfo = orderConvert.entity2infoVO(orderEntity);
        orderMainInfo.setPayAmount(orderEntity.calPayable());
        orderMainInfo.setPayAmountYuan(AmountChangeUtil.changeF2Y(orderEntity.calPayable()));
        orderMainInfo.setRealAmountYuan(AmountChangeUtil.changeF2Y(orderEntity.getRealAmount()));

        //是否全额支付 不是全退款，及实际支付金额大于等于应付金额
        Boolean fullPaid = orderEntity.getRealAmount() >= orderEntity.calPayable();
        Boolean fullRefund = OrderRefundStatusEnum.FULL_REFUND.getCode().equals(orderEntity.getRefundStatus());
        fullPaid = fullPaid && !fullRefund;
        orderMainInfo.setIsFullAmount(fullPaid);

        orderMainInfo.setStaffName("");
        UserEntity userEntity = userRepository.queryById(orderEntity.getStaffId());
        if (Objects.nonNull(userEntity)) {
            orderMainInfo.setStaffName(userEntity.getName());
        }

        if (ObjectUtil.isNotNull(orderMainInfo)) {
            HeOrderGoodsEntity orderGoodsEntity = orderGoodsRepository.queryOrderGoodsInfoByOrderId(orderEntity.getOrderId());

            if (ObjectUtil.isNotNull(orderGoodsEntity)) {
                orderMainInfo.setGoodsName(orderGoodsEntity.getGoodsName());
            }

            CfgStoreEntity cfgStore = storeRepository.queryCfgStoreById(orderEntity.getStoreId());
            if (ObjectUtil.isNotNull(cfgStore)) {
                orderMainInfo.setStoreName(cfgStore.getStoreName());
                orderMainInfo.setStoreNameAlias(cfgStore.getNameAlias());
                orderMainInfo.setStoreType(cfgStore.getType());
            }
        }
        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(orderEntity.getOrderId());
        if (ObjectUtil.isNotNull(heOrderUserSnapshotEntity)) {
            orderMainInfo.setClientName(heOrderUserSnapshotEntity.getName());
        } else {
            ClientInfoVO clientInfoVO = tabClientManager.getClientInfoByClientId(orderEntity.getClientUid());
            if (ObjectUtil.isNotNull(clientInfoVO)) {
                orderMainInfo.setClientName(clientInfoVO.getName());
            }
        }

        OrderMonthIncomeQuery orderMonthIncomeQuery = new OrderMonthIncomeQuery();
        orderMonthIncomeQuery.setClientId(orderEntity.getClientUid());
        if (orderEntity.isNewOrder()) {
            // 押金模块
            OrderQuery orderQuery = new OrderQuery()
                    .setOrderType(OmniOrderTypeEnum.DEPOSIT_ORDER.code())
                    .setBasicUid(orderEntity.getBasicUid())
                    .setStoreId(orderEntity.getStoreId());
            List<HeOrderEntity> heOrderEntities = orderRepository.queryByCondition(orderQuery);
            Optional<HeOrderEntity> ableOrderOpt = heOrderEntities.stream().filter(order -> OrderStatusV2Enum.CLOSE.getCode().intValue() != order.getOrderStatus() && OrderRefundStatusEnum.FULL_REFUND.getCode().intValue() != order.getRefundStatus()).findFirst();
            if (ableOrderOpt.isPresent()) {
                orderMonthIncomeQuery.setOrderId(ableOrderOpt.get().getOrderId());
            }
        }

        Result<DepositSettlementVO> depositSettlementVOResult = depositQueryFacade.queryDeposit(orderMonthIncomeQuery);
        orderMainInfo.setDepositSettlementVO(depositSettlementVOResult.getData());

        //全部收款
        List<HeIncomeRecordEntity> allRecordListByOrderId = incomeRecordRepository.getAllRecordListByOrderId(orderEntity.getOrderId());
        //线下收款
        List<HeIncomeProofRecordEntity> incomeProofRecordByOrderId = incomeProofRecordRepository.getIncomeProofRecordByOrderId(orderEntity.getOrderId());
        //退款
        List<HeOrderRefundEntity> refundByOrderId = orderRefundRepository.getRefundByOrderId(orderEntity.getOrderId());
        refundByOrderId = refundByOrderId.stream().filter(r -> r.getType() == 1).collect(Collectors.toList());

        Integer incomeAll = 0;
        Integer refundAll = 0;
        Integer beingRefundedAll = 0;

        for (HeIncomeRecordEntity incomeRecordEntity : allRecordListByOrderId) {
            if (incomeRecordEntity.getPayType().equals(OmniPayTypeEnum.OFFLINE.getCode())) {

                Optional<HeIncomeProofRecordEntity> first = incomeProofRecordByOrderId.stream().filter(i -> incomeRecordEntity.getId().equals(i.getIncomeId())).findFirst();
                if (first.isPresent()) {
                    HeIncomeProofRecordEntity heIncomeProofRecordEntity = first.get();
                    if (heIncomeProofRecordEntity.getStatus().equals(OfflineAuditStatusV2Enum.EXAMINATION_PASSED.getCode()) || heIncomeProofRecordEntity.getStatus().equals(OfflineAuditStatusV2Enum.OTHER.getCode())) {
                        incomeAll += incomeRecordEntity.getIncome();
                    }
                }
            } else {
                if (incomeRecordEntity.getStatus().equals(1)) {
                    incomeAll += incomeRecordEntity.getIncome();
                }
            }
        }
        // 退款状态，1：审批中 2：审批失败  3：审批成功/等待打款/待确认 4：退款已到帐/已确认 5：到账失败/已拒绝
        for (HeOrderRefundEntity heOrderRefundEntity : refundByOrderId) {
            Integer status = heOrderRefundEntity.getStatus();
            RefundRecordPayStatusEnum byCode = RefundRecordPayStatusEnum.getEnumByCode(status);
            switch (byCode) {
                case REFUND_RECORD_1:
                    beingRefundedAll += heOrderRefundEntity.getApplyAmount();
                    break;
                case REFUND_RECORD_3:
                    beingRefundedAll += heOrderRefundEntity.getApplyAmount();
                    break;
                case REFUND_RECORD_4:
                    refundAll += heOrderRefundEntity.getApplyAmount();
                    break;
            }
        }

        orderMainInfo.setIncomeAll(incomeAll);
        orderMainInfo.setRefundAll(refundAll);
        orderMainInfo.setBeingRefunded(beingRefundedAll);
        log.info("查询订单主信息，res:{}", JSONUtil.toJsonStr(orderMainInfo));
        return Result.success(orderMainInfo);
    }


    @Override
    public Result<List<OrderReductionVO>> getSuccessListByOrderId(Integer orderId) {
        ReductionQuery query = new ReductionQuery();
        query.setOrderId(orderId);
        query.setAuthStateList(OrderReductionEntity.SUCCESS_AUTH_STATE);
        List<OrderReductionEntity> reductionEntities = orderReductionRepository.select(query);
        List<OrderReductionVO> orderReductionVOS = orderConvert.orderReductionEntityList2OrderReductionVOList(reductionEntities);
        for (OrderReductionVO orderReductionVO : orderReductionVOS) {
            orderReductionVO.setAfterAmount(AmountChangeUtil.changeF2Y(orderReductionVO.getAfterAmount()));
            orderReductionVO.setBeforeAmount(AmountChangeUtil.changeF2Y(orderReductionVO.getBeforeAmount()));
            orderReductionVO.setDecreaseAmount(AmountChangeUtil.changeF2Y(orderReductionVO.getDecreaseAmount()));
        }
        return Result.success(orderReductionVOS);
    }

    @Override
    public Result<QueryDepositAmountRes> getDepositAmount(OrderMonthIncomeQuery query) {
        QueryDepositAmountRes queryDepositAmountRes = new QueryDepositAmountRes();
        ClientInfoVO clientInfoVO = tabClientManager.getClientInfoByClientId(query.getClientId());
        Integer storeId = clientInfoVO.getStoreId();
        //门店信息
        String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(storeId);
        List<HeIncomeRecordEntity> recordListByClientUid = incomeRecordRepository.getRecordListByClientUid(query.getClientId(), IncomeReceiptTypeEnum.RECORD_RECEIPT_TYPE_DEPOSIT.code());

        Optional<HeIncomeRecordEntity> hasOldDepositCanRefund = recordListByClientUid.stream().filter(o -> o.getOrderId() == 0 && o.getRefundIncome() > 0).findFirst();
        queryDepositAmountRes.setIsOldDepositRecord(hasOldDepositCanRefund.isPresent());

        //押金应付金额
        Integer paidAmount = recordListByClientUid.stream().mapToInt(HeIncomeRecordEntity::getIncome).sum();
        //押金已退金额
        List<HeOrderRefundEntity> refundByOrderIncomeIdList = orderRefundRepository.getRefundSuccessByOrderIncomeIdList(recordListByClientUid.stream().map(HeIncomeRecordEntity::getId).collect(Collectors.toList()));

        //审批成功退款已到账的
        Integer depositRefund = refundByOrderIncomeIdList.stream().filter(i -> i.getStatus().equals(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode())).mapToInt(HeOrderRefundEntity::getActualAmount).sum();
        Integer depositRefundApplyAmt = refundByOrderIncomeIdList.stream().filter(i -> i.getStatus().equals(RefundRecordPayStatusEnum.REFUND_RECORD_1.getCode())).mapToInt(HeOrderRefundEntity::getApplyAmount).sum();
        Integer waitPayAmt = refundByOrderIncomeIdList.stream().filter(i -> i.getStatus().equals(RefundRecordPayStatusEnum.REFUND_RECORD_3.getCode())).mapToInt(HeOrderRefundEntity::getApplyAmount).sum();

        int leftAmount = paidAmount - depositRefund - depositRefundApplyAmt - waitPayAmt;
        queryDepositAmountRes.setDepositAmount(AmountChangeUtil.changeF2Y(leftAmount));
        queryDepositAmountRes.setCurrency(storeCurrencyCode);
        //押金金额设置0元
        return Result.success(queryDepositAmountRes);
    }

    @Override
    public Result<List<LastOrderInfoVO>> getLastOrderInfoByBasicUid(List<Integer> basicUidList, List<Integer> orderType) {


        List<LastOrderInfoVO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(basicUidList)) {

            List<HeOrderEntity> infoByByBasicUid = orderRepository.getInfoByByBasicUid(basicUidList, orderType);
            if (CollectionUtils.isNotEmpty(infoByByBasicUid)) {
                List<Integer> storeIdList = infoByByBasicUid.stream().map(HeOrderEntity::getStoreId).collect(Collectors.toList());
                //门店列表
                List<CfgStoreEntity> cfgStoreEntities = storeRepository.queryCfgStoreByIdList(storeIdList);

                Map<Integer, List<HeOrderEntity>> groupByBasicUid = infoByByBasicUid.stream().collect(Collectors.groupingBy(HeOrderEntity::getBasicUid));

                groupByBasicUid.keySet().forEach(basicUid -> {
                    HeOrderEntity orderEntity = groupByBasicUid.get(basicUid).get(0);
                    LastOrderInfoVO lastOrderInfoVO = orderConvert.entity2OrderInfoVO(orderEntity, cfgStoreEntities);
                    result.add(lastOrderInfoVO);
                });
            }
        }

        return Result.success(result);
    }

    @Override
    public Result<Integer> queryOrderInfo(Integer orderId) {
        HeOrderEntity orderEntity = orderRepository.getByOrderId(orderId);


        return Result.success(orderEntity.fetchServiceRoomDays());
    }

    private GoodInfoVO heOrderGoodsEntity2GoodInfoVO(HeOrderEntity orderEntity, HeOrderGoodsEntity heOrderGoodsEntity) {
        GoodInfoVO goodInfoVO = new GoodInfoVO();
        goodInfoVO.setId(heOrderGoodsEntity.getId());
        goodInfoVO.setGoodsName(heOrderGoodsEntity.getGoodsName());
        goodInfoVO.setGoodsId(heOrderGoodsEntity.getGoodsId());
        goodInfoVO.setSkuId(heOrderGoodsEntity.getSkuId());
        goodInfoVO.setSkuName(heOrderGoodsEntity.getSkuName());
        goodInfoVO.setNum(heOrderGoodsEntity.getGoodsNum());
        goodInfoVO.setPrice(RMBUtils.bigDecimalF2Y(heOrderGoodsEntity.getGoodsPriceOrgin()));
        goodInfoVO.setCostPrice(RMBUtils.bigDecimalF2Y(heOrderGoodsEntity.getGoodsCost()));
        goodInfoVO.setAssetType(new Integer(heOrderGoodsEntity.getAssetType()));
        goodInfoVO.setGoodsType(heOrderGoodsEntity.getGoodsType());
        goodInfoVO.setBusinessType(orderEntity.getOrderType());
        goodInfoVO.setType(heOrderGoodsEntity.getType());
        goodInfoVO.setSubList(new ArrayList<>());
        goodInfoVO.setParentId(heOrderGoodsEntity.getParentId());
        goodInfoVO.setServiceType(heOrderGoodsEntity.getServiceType());
        return goodInfoVO;
    }

    @Override
    public Result<Date> getCheckIn(String orderSn) {
        ListCheckInDateReq listCheckInDateReq = new ListCheckInDateReq();
        listCheckInDateReq.setOrderNos(Arrays.asList(orderSn));
        List<RoomCheckInDateVO> roomCheckInDateVOS = roomExternalQuery.listCheckInDate(listCheckInDateReq).stream().filter(r -> Arrays.asList(2, 3).contains(r.getCheckInStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(roomCheckInDateVOS)) {
            return Result.success(null);
        }
        return Result.success(roomCheckInDateVOS.get(0).getCheckInDate());
    }

    @Override
    public Result<Integer> getOrderRealAmount(Integer orderId) {

        Integer sum = 0, refund = 0;

        List<HeIncomeRecordEntity> allRecordListByOrderId = incomeRecordRepository.getAllRecordListByOrderId(orderId);
        List<HeIncomeProofRecordEntity> incomeProofRecordByOrderId = incomeProofRecordRepository.getIncomeProofRecordByOrderId(orderId);


        for (HeIncomeRecordEntity incomeRecordEntity : allRecordListByOrderId) {
            if (incomeRecordEntity.getPayType() == 3) {
                Optional<HeIncomeProofRecordEntity> first = incomeProofRecordByOrderId.stream().filter(i -> i.getIncomeId().equals(incomeRecordEntity.getId())).findFirst();
                if (first.isPresent()) {
                    if (first.get().getStatus() == 1) {
                        sum += incomeRecordEntity.getIncome();
                    }
                }
            } else {
                if (incomeRecordEntity.getStatus() == 1) {
                    sum += incomeRecordEntity.getIncome();
                }
            }

        }

        List<HeOrderRefundEntity> refundByOrderId = orderRefundRepository.getRefundByOrderId(orderId);
        if (CollectionUtils.isNotEmpty(refundByOrderId)) {
            refundByOrderId = refundByOrderId.stream().filter(r -> r.getType() == 1).collect(Collectors.toList());
            for (HeOrderRefundEntity heOrderRefundEntity : refundByOrderId) {
                if (Arrays.asList(1, 3, 4).contains(heOrderRefundEntity.getStatus())) {
                    refund += heOrderRefundEntity.getApplyAmount();
                }
            }
        }


        return Result.success(sum - refund);
    }

    @Override
    public List<OrderRealAmountVO> getOrderRealAmountByOrderList(List<Integer> orderIdList) {

        List<HeIncomeRecordEntity> allRecordListByOrderId = incomeRecordRepository.queryAllRecordListByOrderIds(orderIdList);
        List<HeIncomeProofRecordEntity> incomeProofRecordByOrderId = incomeProofRecordRepository.getIncomeProofRecordByOrderIdList(orderIdList);
        List<HeOrderRefundEntity> refundByOrderId = orderRefundRepository.getRefundByOrderIdList(orderIdList);

        Map<Integer, List<HeIncomeRecordEntity>> incomeByOrderIdMap = allRecordListByOrderId.stream().collect(Collectors.groupingBy(HeIncomeRecordEntity::getOrderId));
        Map<Integer, List<HeIncomeProofRecordEntity>> incomeProofByOrderIdMap = incomeProofRecordByOrderId.stream().collect(Collectors.groupingBy(HeIncomeProofRecordEntity::getOrderId));
        Map<Integer, List<HeOrderRefundEntity>> refundByOrderIdMap = refundByOrderId.stream().collect(Collectors.groupingBy(HeOrderRefundEntity::getOrderId));

        List<OrderRealAmountVO> result = new ArrayList<>();

        for (Integer orderId : orderIdList) {
            OrderRealAmountVO orderRealAmountVO = new OrderRealAmountVO();
            orderRealAmountVO.setOrderId(orderId);
            orderRealAmountVO.setRealAmount(0L);
            Long sum = 0L, refund = 0L;
            List<HeIncomeRecordEntity> heIncomeRecordEntities = incomeByOrderIdMap.get(orderId);
            List<HeIncomeProofRecordEntity> heIncomeProofRecordEntities = incomeProofByOrderIdMap.get(orderId);
            List<HeOrderRefundEntity> heOrderRefundEntities = refundByOrderIdMap.get(orderId);
            if (CollectionUtils.isNotEmpty(heIncomeRecordEntities)) {
                for (HeIncomeRecordEntity incomeRecordEntity : heIncomeRecordEntities) {
                    if (incomeRecordEntity.getPayType() == 3) {
                        if (CollectionUtils.isNotEmpty(heIncomeProofRecordEntities)) {
                            Optional<HeIncomeProofRecordEntity> first = heIncomeProofRecordEntities.stream().filter(i -> i.getIncomeId().equals(incomeRecordEntity.getId())).findFirst();
                            if (first.isPresent()) {
                                if (first.get().getStatus() == 1) {
                                    sum += incomeRecordEntity.getIncome();
                                }
                            }
                        }
                    } else {
                        if (incomeRecordEntity.getStatus() == 1) {
                            sum += incomeRecordEntity.getIncome();
                        }
                    }

                }
            }
            if (CollectionUtils.isNotEmpty(heOrderRefundEntities)) {
                heOrderRefundEntities = heOrderRefundEntities.stream().filter(r -> r.getType() == 1).collect(Collectors.toList());
                for (HeOrderRefundEntity heOrderRefundEntity : heOrderRefundEntities) {
                    if (Arrays.asList(1, 3, 4).contains(heOrderRefundEntity.getStatus())) {
                        refund += heOrderRefundEntity.getApplyAmount();
                    }
                }
            }
            Long realAmount = sum - refund;
            orderRealAmountVO.setRealAmount(realAmount < 0 ? 0L : realAmount);
            result.add(orderRealAmountVO);
        }

        return result;
    }

    @Override
    public Result<OrderInfoAndGoodsInfoVO> queryInfoAndGoodsInfo(String orderSn) {

        HeOrderEntity orderEntity = orderRepository.getByOrderSn(orderSn);

        if (ObjectUtil.isNull(orderEntity)) {
            throw new BusinessException(ErrorCodeEnum.BIZ_ERROR.code() + "", "订单号不存在，请重新输入");
        }

        Integer orderId = orderEntity.getOrderId();

        HeOrderUserSnapshotEntity orderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(orderId);

        List<HeOrderGoodsEntity> allItermByOrderId = orderGoodsRepository.getAllItermByOrderId(orderId);

        OrderInfoAndGoodsInfoVO result = getOrderInfoAndGoodsInfoVO(orderEntity, orderUserSnapshotEntity, allItermByOrderId);

        return Result.success(result);
    }

    private OrderInfoAndGoodsInfoVO getOrderInfoAndGoodsInfoVO(HeOrderEntity orderEntity, HeOrderUserSnapshotEntity orderUserSnapshotEntity, List<HeOrderGoodsEntity> allItermByOrderId) {
        OrderInfoAndGoodsInfoVO orderInfoAndGoodsInfoVO = new OrderInfoAndGoodsInfoVO();
        orderInfoAndGoodsInfoVO.setOrderId(orderEntity.getOrderId());
        orderInfoAndGoodsInfoVO.setOrderSn(orderEntity.getOrderSn());
        orderInfoAndGoodsInfoVO.setOrderType(orderEntity.getOrderType());
        orderInfoAndGoodsInfoVO.setClientUid(orderEntity.getClientUid());
        orderInfoAndGoodsInfoVO.setBasicUid(orderEntity.getBasicUid());
        orderInfoAndGoodsInfoVO.setStoreId(orderEntity.getStoreId());
        orderInfoAndGoodsInfoVO.setPayAmount(RMBUtils.bigDecimalF2Y(orderEntity.getPayAmount()));
        orderInfoAndGoodsInfoVO.setOrderAmount(RMBUtils.bigDecimalF2Y(orderEntity.getOrderAmount()));
        Integer percentFirstTime = orderEntity.getPercentFirstTime();
        if (Objects.nonNull(percentFirstTime)) {
            orderInfoAndGoodsInfoVO.setPerformanceEffectiveDate(new Date(percentFirstTime * 1000L));
        }
        orderInfoAndGoodsInfoVO.setOrderStatus(orderEntity.getOrderStatus());
        orderInfoAndGoodsInfoVO.setCurrency(orderEntity.getCurrency());
        orderInfoAndGoodsInfoVO.setPayStatus(orderEntity.getPayStatus());
        orderInfoAndGoodsInfoVO.setRefundStatus(orderEntity.getRefundStatus());
        orderInfoAndGoodsInfoVO.setPredictBornDate(orderUserSnapshotEntity.getPredictBornDate());
        orderInfoAndGoodsInfoVO.setServiceDays(orderEntity.fetchServiceRoomDays());
        orderInfoAndGoodsInfoVO.setOrderDiscount(orderEntity.getDiscountMargin());
        orderInfoAndGoodsInfoVO.setSignDiscount(orderEntity.getSignOrderDiscountMargin());
        orderInfoAndGoodsInfoVO.setOrderScene(orderEntity.getScene());

        List<GoodInfoVO> goodInfoList = new ArrayList<>();

        List<HeOrderGoodsEntity> combinationList = allItermByOrderId.stream().filter(a -> a.getGoodsType().equals(GoodsTypeEnum.COMBINATION.code())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(combinationList)) {
            for (HeOrderGoodsEntity heOrderGoodsEntity : combinationList) {
                GoodInfoVO goodInfoVO = heOrderGoodsEntity2GoodInfoVO(orderEntity, heOrderGoodsEntity);
                List<HeOrderGoodsEntity> childGoods = allItermByOrderId.stream().filter(a -> ObjectUtil.isNotEmpty(a.getParentCombineSn()) && a.getParentCombineSn().equals(heOrderGoodsEntity.getOrderGoodsSn())).collect(Collectors.toList());
                List<GoodInfoVO> goodInfoVOList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(childGoods)) {
                    for (HeOrderGoodsEntity childGood : childGoods) {
                        goodInfoVOList.add(heOrderGoodsEntity2GoodInfoVO(orderEntity, childGood));
                    }
                }
                goodInfoVO.setSubList(goodInfoVOList);
                goodInfoList.add(goodInfoVO);
            }
        }

        List<HeOrderGoodsEntity> notCombinationList = allItermByOrderId.stream().filter(a -> !a.getGoodsType().equals(GoodsTypeEnum.COMBINATION.code()) && ObjectUtil.isEmpty(a.getParentCombineSn())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(notCombinationList)) {
            for (HeOrderGoodsEntity heOrderGoodsEntity : notCombinationList) {
                goodInfoList.add(heOrderGoodsEntity2GoodInfoVO(orderEntity, heOrderGoodsEntity));
            }
        }

        orderInfoAndGoodsInfoVO.setGoodInfoVOList(goodInfoList);

        return orderInfoAndGoodsInfoVO;
    }
}

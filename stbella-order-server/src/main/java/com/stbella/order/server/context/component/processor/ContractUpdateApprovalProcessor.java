package com.stbella.order.server.context.component.processor;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.base.server.ding.response.CreateOrderApproveRecordVO;
import com.stbella.core.result.Result;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.server.order.month.req.MonthDingReq;
import com.stbella.order.server.order.month.service.MonthDingService;
import com.stbella.platform.order.api.reduction.req.DecreaseReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * @Author: wangchuang
 * @CreateTime: 2024-05-28  13:55
 * @Description: 折扣审批组件组装
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "合同修改审批组件组装")
public class ContractUpdateApprovalProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private MonthDingService monthDingService;

    @Override
    public void run(FlowContext bizContext) {
        OrderReductionEntity orderReductionEntity = bizContext.getAttribute(OrderReductionEntity.class);
        Boolean approved = orderReductionEntity.getModifyContractApprove();
        DecreaseReq decreaseReq = bizContext.getAttribute(DecreaseReq.class);
        if (!Boolean.TRUE.equals(approved)) {
            return;
        }
        HeOrderEntity heOrderEntity = bizContext.getAttribute(HeOrderEntity.class);
        Integer orderType = heOrderEntity.getOrderType();
        //月子
        if (ObjectUtil.equals(orderType, OmniOrderTypeEnum.MONTH_ORDER.getCode())
                || ObjectUtil.equals(orderType, OmniOrderTypeEnum.SMALL_MONTH_ORDER.getCode())
                || ObjectUtil.equals(orderType, OmniOrderTypeEnum.PRODUCTION_ORDER.getCode())
                || OmniOrderTypeEnum.getHomeOrderTypeCodeList().contains(orderType)) {
            MonthDingReq monthDingReq = new MonthDingReq();
            monthDingReq.setOrderId(heOrderEntity.getOrderId());
            monthDingReq.setOperator(decreaseReq.getOperator());
            monthDingReq.setIsReduceDiscount(Boolean.TRUE);
            long beforeAmount = heOrderEntity.getPayAmount() - heOrderEntity.fetchTotalReductionAmount();
            Integer decreaseAmount = AmountChangeUtil.changeY2FFoInt(decreaseReq.getDecreaseAmount());
            long afterAmount = beforeAmount - decreaseAmount.longValue();

            Integer leftPayAmount = heOrderEntity.leftPayAmount();
            BigDecimal bigDecimal = AmountChangeUtil.changeF2Y(leftPayAmount - AmountChangeUtil.changeY2FFoInt(decreaseReq.getDecreaseAmount()));
            String content = "1.签单金额由" + AmountChangeUtil.changeF2Y(beforeAmount) + "改为" + AmountChangeUtil.changeF2Y(afterAmount) + "\n"
                    + "2.剩余应收金额由" + AmountChangeUtil.changeF2Y(leftPayAmount) + "改为" + bigDecimal;
            Result<CreateOrderApproveRecordVO> contractModificationApproval = monthDingService.createContractModificationApproval(monthDingReq, content);
            CreateOrderApproveRecordVO createOrderApproveRecordVO = contractModificationApproval.getData();
            bizContext.addAttribute(CreateOrderApproveRecordVO.class, createOrderApproveRecordVO);
        }
    }
}

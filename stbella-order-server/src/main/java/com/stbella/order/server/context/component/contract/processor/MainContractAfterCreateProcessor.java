package com.stbella.order.server.context.component.contract.processor;

import cn.hutool.core.collection.CollectionUtil;
import com.stbella.contract.model.res.ContractSignRecordVO2;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.MonthOrderParamHistoryRepository;
import com.stbella.order.server.config.OrderHistoryTypeConfig;
import com.stbella.order.server.convert.AppMonthContractSignRecordConverter;
import com.stbella.order.server.order.month.enums.OrderHistoryTypeEnum;
import com.stbella.order.server.order.month.res.MonthOrderParamHistoryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 合同创建
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "主合同创建后处理")
public class MainContractAfterCreateProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private MonthOrderParamHistoryRepository monthOrderParamHistoryRepository;
    @Resource
    private AppMonthContractSignRecordConverter appMonthContractSignRecordConverter;

    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        Integer orderId = orderEntity.getOrderId();
        ContractSignRecordVO2 contractSignRecordVO2 = bizContext.getAttribute(ContractSignRecordVO2.class);
        Integer templateContractType = contractSignRecordVO2.getTemplateContractType();
        if (OrderHistoryTypeConfig.SPECIAL_TYPE_MAP.containsKey(templateContractType)) {
            templateContractType = OrderHistoryTypeConfig.SPECIAL_TYPE_MAP.get(templateContractType);
        }
        List<MonthOrderParamHistoryVO> paramHistories = list(orderId.longValue(), 0L, 1, Arrays.asList(templateContractType));
        if (CollectionUtil.isNotEmpty(paramHistories)) {
            for (MonthOrderParamHistoryVO paramHistory : paramHistories) {
                paramHistory.setContractId(contractSignRecordVO2.getId());
            }
            monthOrderParamHistoryRepository.updateBatchByParamHistoryId(paramHistories);
        }
    }

    List<MonthOrderParamHistoryVO> list(Long orderId, Long history, Integer valid, List<Integer> type) {
        return appMonthContractSignRecordConverter.entity2MonthOrderParamHistoryVoList(monthOrderParamHistoryRepository.list(orderId, history, valid, type));
    }

    @Override
    public boolean condition(FlowContext bizContext) {
        ContractSignRecordVO2 contractSignRecordVO2 = bizContext.getAttribute(ContractSignRecordVO2.class);
        Integer templateContractType = contractSignRecordVO2.getTemplateContractType();
        return Arrays.asList(TemplateContractTypeEnum.YZ_SAINTBELLA.code(),
                TemplateContractTypeEnum.YZ_NUBSE_OUTSIDE.code(),
                TemplateContractTypeEnum.YZ_SMALL.code(),
                TemplateContractTypeEnum.RELEASE.code()).contains(templateContractType);

    }


}

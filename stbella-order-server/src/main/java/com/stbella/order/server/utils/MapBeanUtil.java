package com.stbella.order.server.utils;

/**
 * @Classname MapBeanUtil
 * @Description map转bean，bean转map
 * @Date 2022/7/21 14:11
 * @Created by <PERSON>y
 */

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.order.server.order.cts.dto.RefundDingParamDTO;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.HashMap;
import java.util.Map;


public class MapBeanUtil {

    /**
     * 实体对象转成Map
     *
     * @param obj 实体对象
     */
    public static Map<String, Object> object2Map(Object obj) {
        Map<String, Object> map = new HashMap<>();
        if (obj == null) {
            return map;
        }
        Class clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();
        try {
            for (Field field : fields) {
                field.setAccessible(true);
                map.put(field.getName(), field.get(obj));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return map;
    }


    /**
     * 实体对象转成Map，为钉钉审批参数对象转化
     *
     * @param obj 实体对象
     */
    public static HashMap<String, String> object2MapForDingParam(Object obj) {
        HashMap<String, String> map = new HashMap<>();
        if (obj == null) {
            return map;
        }
        Class clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();
        try {
            for (Field field : fields) {
                field.setAccessible(true);
                ApiModelProperty aa = field.getAnnotation(ApiModelProperty.class);
                if (ObjectUtil.isNotNull(aa)) {
                    String key = aa.value();
                    String value = (String) field.get(obj);
                    if (StringUtils.isNotBlank(value)) {
                        map.put(key, value);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return map;
    }

    /**
     * Map转成实体对象
     *
     * @param map   实体对象包含属性
     * @param clazz 实体对象类型
     */
    public static Object map2Object(Map<String, Object> map, Class<?> clazz) {
        if (map == null) {
            return null;
        }
        Object obj = null;
        try {
            obj = clazz.newInstance();

            Field[] fields = obj.getClass().getDeclaredFields();
            for (Field field : fields) {
                int mod = field.getModifiers();
                if (Modifier.isStatic(mod) || Modifier.isFinal(mod)) {
                    continue;
                }
                field.setAccessible(true);
                field.set(obj, map.get(field.getName()));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return obj;
    }


    public static void main(String[] args) {
        RefundDingParamDTO refundDingParamDTO = new RefundDingParamDTO();
        refundDingParamDTO.setOrderTypeString("育婴师订单");
        Map<String, String> paramMap = MapBeanUtil.object2MapForDingParam(refundDingParamDTO);
        System.out.println(JSONUtil.toJsonStr(paramMap));
    }
}


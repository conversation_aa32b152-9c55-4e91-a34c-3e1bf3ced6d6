package com.stbella.order.server.context.component.validator;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.order.common.enums.core.OrderRefundNatureEnum;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Component
@RefreshScope
@SnowballComponent(name = "退货退款金额处理器")
public class OrderRefundAndReturnAmountAssembler implements IExecutableAtom<FlowContext> {


    @Override
    public void run(FlowContext bizContext) {
        CreateRefundReq createRefundReq = bizContext.getAttribute(CreateRefundReq.class);
        List<CreateRefundReq.GoodsInfo> goodsInfoList = createRefundReq.getGoodsInfoList();

        if (CollectionUtil.isEmpty(goodsInfoList)) {
            log.warn("退货退款处理器：商品列表为空，orderId={}", createRefundReq.getOrderId());
            return;
        }

        log.info("退货退款金额分摊开始，orderId={}, 商品数量={}, 总退款金额={}", createRefundReq.getOrderId(), goodsInfoList.size(), createRefundReq.getRefundAmount());

        // 按照各个商品可退现金分摊订单总金额
        BigDecimal refundAmountByGoods = getRefundAmountByGoods(goodsInfoList);
        createRefundReq.setRefundAmount(refundAmountByGoods);

        log.info("退货退款，orderId={},请求参数为 {}", createRefundReq.getOrderId(), JSONObject.toJSONString(createRefundReq));
        bizContext.setAttribute(CreateRefundReq.class, createRefundReq);
    }

    private BigDecimal getRefundAmountByGoods(List<CreateRefundReq.GoodsInfo> goodsInfoList) {
        BigDecimal amount = BigDecimal.ZERO;
        for (CreateRefundReq.GoodsInfo goodsInfo : goodsInfoList) {
            List<CreateRefundReq.GoodsRefundAmountInfo> refundGoodsAmountInfoList = goodsInfo.getRefundGoodsAmountInfoList();
            if (CollectionUtil.isNotEmpty(refundGoodsAmountInfoList)) {
                for (CreateRefundReq.GoodsRefundAmountInfo goodsRefundAmountInfo : refundGoodsAmountInfoList) {
                    amount = amount.add(goodsRefundAmountInfo.getAmount());
                }
            }
        }
        return amount;
    }


    @Override
    public boolean condition(FlowContext context) {
//        CreateRefundReq createRefundReq = context.getAttribute(CreateRefundReq.class);
//        Integer refundType = createRefundReq.getGoodsRefundType();
        return Boolean.FALSE;
    }
}

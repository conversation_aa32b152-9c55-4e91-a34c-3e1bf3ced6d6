package com.stbella.order.server.order.month.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.repository.OrderRefundRepository;
import com.stbella.order.infrastructure.repository.converter.OrderRefundConverter;
import com.stbella.order.server.order.month.res.OrderRefundVO;
import com.stbella.order.server.order.month.service.OrderRefundMangerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@DubboService
@Slf4j
public class OrderRefundMangerServiceImpl implements OrderRefundMangerService {

    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private OrderRefundConverter orderRefundConverter;

    @Override
    public void resetApprovalStatus(Integer id, Integer status) {
        HeOrderRefundEntity oneById = orderRefundRepository.getOneById(id);
        oneById.setStatus(status);
        orderRefundRepository.updateOneById(oneById);
    }

    @Override
    public OrderRefundVO getOneByRefundOrderSn(String refundOrderSn) {
        HeOrderRefundEntity oneByRefundOrderSn = orderRefundRepository.getOneByRefundOrderSn(refundOrderSn);
        if (ObjectUtil.isNotEmpty(oneByRefundOrderSn)) {
            OrderRefundVO orderRefundVO = orderRefundConverter.entity2VO(oneByRefundOrderSn);
            String parentRefundOrderSn = oneByRefundOrderSn.getParentRefundOrderSn();
            if (StringUtils.isNotEmpty(parentRefundOrderSn)) {
                HeOrderRefundEntity parentRefundOrderSnEntity = orderRefundRepository.getOneByRefundOrderSn(parentRefundOrderSn);
                if (ObjectUtil.isEmpty(parentRefundOrderSnEntity)) {
                    throw new BusinessException(ResultEnum.NOT_EXIST, "退款不存在");
                }
                orderRefundVO.setApproveRefundId(parentRefundOrderSnEntity.getId());
            } else {
                orderRefundVO.setApproveRefundId(orderRefundVO.getId());
            }
            return orderRefundVO;
        }
        throw new BusinessException(ResultEnum.NOT_EXIST, "退款不存在");
    }

}

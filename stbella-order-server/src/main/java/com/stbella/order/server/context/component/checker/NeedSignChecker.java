package com.stbella.order.server.context.component.checker;

import cn.hutool.core.collection.CollectionUtil;
import com.stbella.order.common.utils.PropertyValueHelper;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import com.stbella.store.goodz.res.PropertyUserDefinedVO;
import com.stbella.store.goodz.res.PropertyValueListVO;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.stbella.order.common.enums.order.OrderAdditionalKeyEnum.NEED_SIGN;

/**
 * <AUTHOR>
 * @date 2020/03/03
 * 是否需要签署合同
 */
@Component
public class NeedSignChecker {
    public boolean check(SkuDetailInfo sku, Map<Long, PropertyUserDefinedVO> categoryPropertyMap){

        if (CollectionUtil.isEmpty(categoryPropertyMap)){
            return false;
        }
        if (Objects.isNull(sku.getBackCategoryId())){
            return false;
        }
        Long backCategoryId = sku.getBackCategoryId().longValue();
        if (!categoryPropertyMap.containsKey(backCategoryId)){
            return false;
        }
        PropertyUserDefinedVO backCategoryCustomProperty = categoryPropertyMap.get(backCategoryId);
        Optional<PropertyValueListVO> first = backCategoryCustomProperty.getPropertyValueListVOList().stream().filter(property -> Objects.nonNull(property.getCode())).filter(Objects.requireNonNull(propertyValueListVO -> propertyValueListVO.getCode().equals(NEED_SIGN.code()))).findFirst();
        if (!first.isPresent()){
            return false;
        }
        PropertyValueListVO propertyValueListVO = first.get();
        if (PropertyValueHelper.getBooleanPropertyValue(propertyValueListVO.getPropertyValueId().get(0))){
            return true;
        }
        return false;
    }
}

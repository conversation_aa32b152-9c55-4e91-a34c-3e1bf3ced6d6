package com.stbella.order.server.context.component.validator;

import com.stbella.core.exception.BusinessException;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.platform.order.api.reduction.req.DecreaseReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

/**
 * @Author: wangchuang
 * @CreateTime: 2024-05-28  13:55
 * @Description: 订单减免校验
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "订单减免金额校验")
public class DecreaseAmountValidator implements IExecutableAtom<FlowContext> {


    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity heOrderEntity = bizContext.getAttribute(HeOrderEntity.class);
        DecreaseReq req = bizContext.getAttribute(DecreaseReq.class);
        //剩余待支付金额
        Integer decreaseAmount = AmountChangeUtil.changeY2FFoInt(req.getDecreaseAmount());
        Integer leftPayAmount = heOrderEntity.leftPayAmount();
        log.info("订单减免金额校验,订单ID:{},减免金额:{},剩余待支付金额:{}", heOrderEntity.getOrderId(), decreaseAmount, leftPayAmount);
        if (decreaseAmount > leftPayAmount) {
            throw new BusinessException(ErrorCodeEnum.DECREASE_AMOUNT_ERROR.code() + "", ErrorCodeEnum.DECREASE_AMOUNT_ERROR.desc());
        }

    }
}

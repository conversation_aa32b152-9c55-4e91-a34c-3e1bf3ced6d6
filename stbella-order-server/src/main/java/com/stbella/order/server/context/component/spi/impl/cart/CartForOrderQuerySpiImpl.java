package com.stbella.order.server.context.component.spi.impl.cart;

import com.stbella.order.common.enums.core.CartSceneEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.context.component.spi.CartRelatedBusinessEntityQuerySpi;
import com.stbella.platform.order.api.req.RestoreCartReq;
import com.stbella.platform.order.api.res.CartRes;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.annotation.FuncPointSpiImpl;
import top.primecare.snowball.extpoint.definition.FuncSpiConfig;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-05-30  14:01
 * @Description: 从订单获取价格信息
 */
@Component
@FuncPointSpiImpl(name = "获取订单价格信息扩展点实现")
public class CartForOrderQuerySpiImpl implements CartRelatedBusinessEntityQuerySpi {

    @Resource
    OrderRepository orderRepository;

    @Override
    public boolean condition(RestoreCartReq param) {
        //订单和随心拼场景都是一样的处理逻辑
        if (Arrays.asList(CartSceneEnum.ORDER.getCode(), CartSceneEnum.GIFT_ORDER.getCode(), CartSceneEnum.CUSTOMIZE_AS_YOU_WISH.getCode()).contains(param.getScene())){
            return true;
        }
        return false;
    }

    @Override
    public CartRes invoke(RestoreCartReq param) {

        HeOrderEntity order = orderRepository.getByOrderId(param.getBusinessId());
        CartRes cartRes = new CartRes();
        cartRes.setTotalAmount(AmountChangeUtil.changeF2Y(order.getOrderAmount()));
        cartRes.setPayAmount(AmountChangeUtil.changeF2Y(order.getPayAmount()));

        return cartRes;
    }

    @Override
    public FuncSpiConfig config() {
        //默认优先级高
        return new FuncSpiConfig(true, Integer.MIN_VALUE);
    }
}

package com.stbella.order.server.context.component.assembler;

import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.order.service.OrderRefundDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

@Component
@Slf4j
@SnowballComponent(name = "退款都成功后发送短信", desc = "退款都成功后发送短信")
public class OrderRefundAfterSMSAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderRefundDomainService orderRefundDomainService;

    @Override
    public boolean condition(FlowContext context) {
        //非押金订单发送短信
        Boolean deposit = (Boolean) context.getAttribute(OtherConstant.DEPOSIT);
        return (boolean) context.getAttribute(OtherConstant.CONTINUE) && !deposit;
    }

    @Override
    public void run(FlowContext bizContext) {
        //从企微审批表中获取
        HeOrderRefundEntity refundEntity = bizContext.getAttribute(HeOrderRefundEntity.class);
        try {
            orderRefundDomainService.sendRefundAfter(refundEntity.getOrderId(), refundEntity.getId());
        } catch (Exception e) {
            log.error("短信发送失败", e);
        }

    }


}

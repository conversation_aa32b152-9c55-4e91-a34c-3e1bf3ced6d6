package com.stbella.order.server.order.repair.service;

import com.stbella.order.server.context.component.imports.InStoreDataRepairImportDTO;

import java.util.List;

/**
 * 馆内数据修复处理器接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface InStoreDataRepairProcessor {

    /**
     * 处理馆内数据修复
     * 
     * @param dataList 需要修复的数据列表
     * @return 处理结果
     */
    boolean processDataRepair(List<InStoreDataRepairImportDTO> dataList);
}
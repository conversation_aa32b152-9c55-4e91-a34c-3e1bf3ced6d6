package com.stbella.order.server.strategy.approval;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.order.domain.order.entity.ExternalProductionOrderEntity;
import com.stbella.order.domain.repository.ExternalProductionOrderRepository;
import com.stbella.order.server.order.month.component.NoticeAssembler;
import com.stbella.order.server.order.month.enums.ApprovalDiscountStatusEnum;
import com.stbella.order.server.order.month.request.approval.ApprovalStatusInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 产康订单审批
 */
@Component
@Slf4j
public class ExternalCKOrderApprovalStrategy implements ApprovalStrategy {
    @Resource
    private ExternalProductionOrderRepository externalProductionOrderRepository;
    @Autowired
    private NoticeAssembler noticeAssembler;

    @Override
    public void handleApproval(OrderApproveRecordTypeEnum type, String params, String messageId, ApprovalStatusInfo approvalStatusInfo, String processId) {
        JSONObject jsonObject = JSONUtil.parseObj(params);
        String orderSn = jsonObject.getStr("orderSn");
        ExternalProductionOrderEntity byOrderSn = externalProductionOrderRepository.getByOrderSn(orderSn);
        if (ObjectUtil.isEmpty(byOrderSn)) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "产康订单审批审批失败,找不到订单,订单ID=" + byOrderSn.getOrderSn());
        }
        boolean agree = approvalStatusInfo.isAgree();
        boolean finish = approvalStatusInfo.isFinish();
        boolean refuse = approvalStatusInfo.isRefuse();
        boolean terminate = approvalStatusInfo.isTerminate();
        Integer approvalDiscountStatus = null;
        if (finish && agree) {
            approvalDiscountStatus = ApprovalDiscountStatusEnum.APPROVED.getCode();
        } else if (finish && refuse) {
            approvalDiscountStatus = ApprovalDiscountStatusEnum.APPROVAL_FAILED.ordinal();
        } else if (terminate) {
            approvalDiscountStatus = ApprovalDiscountStatusEnum.APPROVAL_FAILED.ordinal();
        }
        log.info("approvalDiscountStatus={}", approvalDiscountStatus);
        byOrderSn.setStatus(approvalDiscountStatus);

        if (ApprovalDiscountStatusEnum.APPROVED.getCode().equals(approvalDiscountStatus)) {
            byOrderSn.setPercentFirstTime(byOrderSn.getCreatedAt().getTime() / 1000);
            byOrderSn.setIsNotice(2);
            //审批通过，报单处理
            String message = noticeAssembler.getExternalCKOrderMsg(byOrderSn);
            noticeAssembler.pushProductMsg(message, byOrderSn.getStoreId());
        }

        externalProductionOrderRepository.update(byOrderSn);

    }

    @Override
    public OrderApproveRecordTypeEnum getOrderApproveRecordTypeEnum() {
        return OrderApproveRecordTypeEnum.EXTERNAL_CK_APPROVE;
    }
}

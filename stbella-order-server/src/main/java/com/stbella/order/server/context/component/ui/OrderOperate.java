package com.stbella.order.server.context.component.ui;

import com.stbella.order.common.enums.month.OrderButtonEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.server.order.month.res.OrderOperateButton;

/**
 * 订单操作
 * @author: JJ
 */
public interface OrderOperate extends IPriority{
    /**
     * 生成订单操作按钮
     * @param order
     * @return
     */
    OrderOperateButton generateButton(OrderOperateContext context);

    /**
     * 获取按钮枚举
     * @return
     */
    OrderButtonEnum  getButtonEnum();
}

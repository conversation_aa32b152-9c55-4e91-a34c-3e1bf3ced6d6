package com.stbella.order.server.context.component.spi.impl;

import com.stbella.base.server.ding.response.CreateOrderApproveRecordVO;
import com.stbella.core.enums.BusinessEnum;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.server.context.component.spi.CustomerQuerySpi;
import com.stbella.order.server.context.component.spi.InitiateDiscountApprovalSpi;
import com.stbella.platform.order.api.req.CreateOrderReq;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.annotation.FuncPointSpiImpl;
import top.primecare.snowball.extpoint.definition.FuncSpiConfig;

/**
 * @Author: jijun<PERSON>an
 * @CreateTime: 2024-05-30  14:01
 * @Description: 产康订单折扣审批发起实现
 */
@Component
@FuncPointSpiImpl(name = "产康订单折折扣审批发起扩展点实现", desc = "")
public class ProductionOrderInitiateDiscountApprovalSpiImpl implements InitiateDiscountApprovalSpi {
    @Override
    public boolean condition(HeOrderEntity param) {
        return false;
        // 这里只根据BU判断
//        if (CompareUtil.integerEqual(param.getOrderType() , OmniOrderTypeEnum.PRODUCTION_ORDER.getCode())){
//            return true;
//        }
//        return false;
    }

    @Override
    public CreateOrderApproveRecordVO invoke(HeOrderEntity param) {
        throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code()+"", "产康订单未实现折扣审批，后续实现");
    }

    @Override
    public FuncSpiConfig config() {
        return new FuncSpiConfig(true, 1);
    }
}

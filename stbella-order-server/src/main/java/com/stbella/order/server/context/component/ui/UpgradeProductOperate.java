package com.stbella.order.server.context.component.ui;

import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.month.OrderButtonEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.server.order.month.res.OrderOperateButton;
import com.stbella.order.server.utils.OrderProcessCheckUtil;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 升级套餐按钮
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class UpgradeProductOperate implements OrderOperate {


    @Resource
    private OrderProcessCheckUtil orderProcessCheckUtil;

    @Override
    public void setPriority(int priority) {

    }

    @Override
    public int getPriority() {
        return 4;
    }

    /**
     * 生成订单操作按钮
     *
     * @param context
     * @return
     */
    @Override
    public OrderOperateButton generateButton(OrderOperateContext context) {

        HeOrderEntity order = context.getOrder();

        Integer orderType = order.getOrderType();
        if (!OmniOrderTypeEnum.MONTH_ORDER.code().equals(orderType)) {
            return null;
        }

        OrderOperateButton button = OrderOperateButton.builder().code(getButtonEnum().getCode()).value(getButtonEnum().getValue()).build();

        boolean realAmount = order.getRealAmount() + order.getProductionAmountPay() > 0;
        if ((!realAmount)) {
            log.info("订单未付款，不显示按钮");
            return null;
        }

        return hasRefundRecord(button, context);

    }

    private OrderOperateButton hasRefundRecord(OrderOperateButton button, OrderOperateContext context) {
        //如果订单有付钱，但是没有核销或者入住，就显示按钮
        if (orderProcessCheckUtil.hasUnfinishedRefund(context.getOrder().getOrderId())) {
            button.setText("当前订单存在退款审批中流程，请等待审批完成后再次尝试！");
        }
        return button;
    }

    /**
     * 获取按钮枚举
     *
     * @return
     */
    @Override
    public OrderButtonEnum getButtonEnum() {
        return OrderButtonEnum.UPGRADE_PRODUCT;
    }

    @Override
    public int compareTo(@NotNull IPriority o) {
        return this.getPriority() - o.getPriority();
    }
}

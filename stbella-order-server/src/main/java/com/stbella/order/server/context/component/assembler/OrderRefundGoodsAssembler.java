package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.domain.client.StoreGoodsClient;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.production.GoodsEntity;
import com.stbella.order.domain.repository.GoodsRepository;
import com.stbella.order.domain.repository.OrderGoodsRepository;
import com.stbella.order.server.convert.OrderGoodsConverter;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.platform.order.api.refund.res.QueryOrderRefundInfoRes;
import com.stbella.platform.order.api.refund.res.QueryRefundGoodsRes;
import com.stbella.store.goodz.res.PropertyDetailVO;
import com.stbella.store.goodz.res.PropertyValueVO;
import com.stbella.store.property.api.PropertyQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Component
@Slf4j
@SnowballComponent(name = "订单退款商品列表", desc = "获取当前订单的商品信息")
public class OrderRefundGoodsAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderGoodsRepository orderGoodsRepository;
    @Resource
    private OrderGoodsConverter orderGoodsConverter;
    @Resource
    private GoodsRepository goodsRepository;
    @Resource
    private StoreGoodsClient storeGoodsClient;

    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);
        //获取当前商品的列表
        List<HeOrderGoodsEntity> allOrderGoods = orderGoodsRepository.getByOrderIdList(Collections.singletonList(order.getOrderId()));
        //获取商品单位
        PropertyDetailVO goodsUnit = storeGoodsClient.getGoodsUnit();
        //设置单位和数量（排除押金、多胞胎、节假日）
        checkAndSetUnit(allOrderGoods.stream().filter(a -> a.getGoodsId() != 0 && a.getSkuId() != 0).collect(Collectors.toList()), goodsUnit);

        Map<String, List<HeOrderGoodsEntity>> goodsMap = allOrderGoods.stream()
                .collect(Collectors.groupingBy(g -> StringUtils.defaultString(g.getParentCombineSn(), "top")));

        List<HeOrderGoodsEntity> topGoods = goodsMap.get("top");

        List<QueryRefundGoodsRes> queryRefundGoodsRes = topGoods.stream()
                .map(topGood -> {
                    //非附加项商品
                    List<HeOrderGoodsEntity> children = goodsMap.getOrDefault(topGood.getOrderGoodsSn(), Collections.emptyList())
                            .stream()
                            .filter(g -> !g.isAddition())
                            .collect(Collectors.toList());
                    //附加项商品
                    List<HeOrderGoodsEntity> additions = goodsMap.getOrDefault(topGood.getOrderGoodsSn(), Collections.emptyList())
                            .stream()
                            .filter(HeOrderGoodsEntity::isAddition)
                            .collect(Collectors.toList());

                    return orderGoodsConverter.toQueryRefundGoodsResWithChildren(topGood, children, additions, goodsMap);
                })
                .collect(Collectors.toList());

        QueryOrderRefundInfoRes result = buildQueryOrderRefundInfoRes(queryRefundGoodsRes, order);
        log.info("订单退款商品列表：{}", JSONObject.toJSONString(result));

        bizContext.setAttribute(QueryOrderRefundInfoRes.class, result);
    }

    private QueryOrderRefundInfoRes buildQueryOrderRefundInfoRes(List<QueryRefundGoodsRes> queryRefundGoodsRes, HeOrderEntity order) {
        QueryOrderRefundInfoRes result = new QueryOrderRefundInfoRes();
        result.setQueryRefundGoodsResList(queryRefundGoodsRes);
        result.setOrderStatus(order.isWaitPay() ? 0 : order.isWaitSendGoods() ? 1 : 2);
        result.setPayAmount(RMBUtils.bigDecimalF2Y(order.getPayAmount()));
        result.setPaidAmount(RMBUtils.bigDecimalF2Y(order.getPaidAmount()));
        result.setFreezeAmount(RMBUtils.bigDecimalF2Y(order.getFreezeAmount()));
        //是否为押金订单
        result.setDeposit(order.isDepositOrder());
        result.setCurrency(order.getCurrency());
        result.setPayStatus(order.getPayStatus());
        return result;
    }


    private void checkAndSetUnit(List<HeOrderGoodsEntity> allOrderGoods, PropertyDetailVO propertyDetailVO) {
        if (CollectionUtil.isEmpty(allOrderGoods)) {
            return;
        }
        //普通商品
        List<GoodsEntity> goodsEntityList = new ArrayList<>();

        List<HeOrderGoodsEntity> goodsList = allOrderGoods.stream()
                .filter(a -> a.getType().equals(CombineTypeEnum.SIMPLE.code()) || a.getType().equals(CombineTypeEnum.COMBINE_SUB.code()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(goodsList)) {
            //TODO 查询商品不直接走数据库
            goodsEntityList = goodsRepository.queryByIdForDelete(goodsList.stream().map(HeOrderGoodsEntity::getGoodsId).collect(Collectors.toList()));
        }

        List<GoodsEntity> finalGoodsEntityList = goodsEntityList;
        allOrderGoods.stream().filter(a -> StringUtils.isEmpty(a.getParentCombineSn())).forEach(parent -> {
            List<HeOrderGoodsEntity> child = allOrderGoods.stream()
                    .filter(a -> StringUtils.isNotEmpty(a.getParentCombineSn()) && a.getParentCombineSn().equals(parent.getOrderGoodsSn()))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(child)) {
                //组合子商品
                for (HeOrderGoodsEntity orderGoodsEntity : child) {
                    //单个商品
                    setUnit(orderGoodsEntity, finalGoodsEntityList, propertyDetailVO, true);
                }
            } else {
                //单个商品
                setUnit(parent, finalGoodsEntityList, propertyDetailVO, false);
            }
        });
    }

    private void setUnit(HeOrderGoodsEntity orderGoodsEntity, List<GoodsEntity> goodsEntityList, PropertyDetailVO propertyDetailVO, Boolean combination) {
        Optional<GoodsEntity> goodsFirst = goodsEntityList.stream().filter(g -> g.getId().equals(orderGoodsEntity.getGoodsId())).findFirst();
        if (goodsFirst.isPresent()) {
            GoodsEntity goodsEntity = goodsFirst.get();

            Optional<PropertyValueVO> unitFirst = propertyDetailVO.getValueList().stream().filter(p -> p.getCode().equals(goodsEntity.getGoodsUnit() + "")).findFirst();

            String unitValue = "";

            if (unitFirst.isPresent() && !"无".equals(unitFirst.get().getValue())) {
                unitValue = unitFirst.get().getValue();
            }

            if (combination) {
                if (unitFirst.isPresent()) {
                    orderGoodsEntity.setGoodsName(orderGoodsEntity.getGoodsName() + "*" + orderGoodsEntity.getGoodsNum() + unitValue);
                    orderGoodsEntity.setSkuName(orderGoodsEntity.getSkuName() + "*" + orderGoodsEntity.getGoodsNum() + unitValue);
                } else {
                    orderGoodsEntity.setGoodsName(orderGoodsEntity.getGoodsName() + "*" + orderGoodsEntity.getGoodsNum());
                    orderGoodsEntity.setSkuName(orderGoodsEntity.getSkuName() + "*" + orderGoodsEntity.getGoodsNum());
                }
            } else {
                if (unitFirst.isPresent()) {
                    orderGoodsEntity.setSkuName(orderGoodsEntity.getSkuName() + "*" + orderGoodsEntity.getGoodsNum() + unitValue);
                } else {
                    orderGoodsEntity.setSkuName(orderGoodsEntity.getSkuName() + "*" + orderGoodsEntity.getGoodsNum());
                }
            }
        }
    }


    @Override
    public boolean condition(FlowContext context) {
        return Boolean.TRUE;
    }
}

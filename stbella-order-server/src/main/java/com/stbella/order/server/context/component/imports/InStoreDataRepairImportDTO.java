package com.stbella.order.server.context.component.imports;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 馆内数据修复导入DTO
 * 根据实际Excel表头生成
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Data
public class InStoreDataRepairImportDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "拆分方法")
    @ApiModelProperty("拆分方法")
    private String splitMethod;

    @ExcelProperty(value = "门店名称")
    @ApiModelProperty("门店名称")
    private String storeName;

    @ExcelProperty(value = "客户姓名")
    @ApiModelProperty("客户姓名")
    private String customerName;

    @ExcelProperty(value = "订单号")
    @ApiModelProperty("订单号")
    private String orderSn;

    @ExcelProperty(value = "商品名称")
    @ApiModelProperty("商品名称")
    private String goodsName;

    @ExcelProperty(value = "订单总金额")
    @ApiModelProperty("订单总金额")
    private BigDecimal totalAmount;

    @ExcelProperty(value = "在馆天数")
    @ApiModelProperty("在馆天数")
    private Integer inStoreDays;

    @ExcelProperty(value = "医院外派天数")
    @ApiModelProperty("医院外派天数")
    private Integer hospitalOutsourcingDays;

    @ExcelProperty(value = "到家外派天数")
    @ApiModelProperty("到家外派天数")
    private Integer homeOutsourcingDays;

    @ExcelProperty(value = "在馆分摊金额")
    @ApiModelProperty("在馆分摊金额")
    private BigDecimal inStoreAllocationAmount;

    @ExcelProperty(value = "医院外派分摊金额")
    @ApiModelProperty("医院外派分摊金额")
    private BigDecimal hospitalOutsourcingAllocationAmount;

    @ExcelProperty(value = "到家外派分摊金额")
    @ApiModelProperty("到家外派分摊金额")
    private BigDecimal homeOutsourcingAllocationAmount;

    @ExcelProperty(value = "产康金")
    @ApiModelProperty("产康金")
    private BigDecimal productionAmount;

    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String remark;


    @ExcelProperty("服务类/产后康复服务/舒缓按摩")
    private BigDecimal repairAmount;

    @ExcelProperty(value = "老订单拆资产类型、数量、分摊金额")
    @ApiModelProperty("老订单拆资产类型、数量、分摊金额")
    private String oldOrderAssetInfo;
}
package com.stbella.order.server.context.component.complaints.processor;

import com.stbella.core.base.Operator;
import com.stbella.core.result.Result;
import com.stbella.order.common.enums.core.CartSceneEnum;
import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.HeCustomerComplaintsRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.platform.order.api.OrderCreateService;
import com.stbella.platform.order.api.cart.CartQueryService;
import com.stbella.platform.order.api.req.CreateOrderReq;
import com.stbella.platform.order.api.req.QueryCartReq;
import com.stbella.platform.order.api.res.CartRes;
import com.stbella.platform.order.api.res.CreateOrderRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

@Component
@Slf4j
@SnowballComponent(name = "客诉审批通过订单处理", desc = "")
public class CustomerComplaintsAgreeOrderProcessor implements IExecutableAtom<FlowContext> {


    @Resource
    CartQueryService cartQueryService;

    @Resource
    OrderCreateService orderCreateService;

    @Resource
    OrderRepository orderRepository;

    @Resource
    HeCustomerComplaintsRepository customerComplaintsRepository;


    @Override
    public void run(FlowContext bizContext) {


        HeCustomerComplaintsEntity customerComplaintsEntity = bizContext.getAttribute(HeCustomerComplaintsEntity.class);

        if (Objects.isNull(customerComplaintsEntity.getCompensationOrderId())){

            QueryCartReq req = new QueryCartReq();
            req.setScene(CartSceneEnum.CUSTOMER_COMPLAINTS.code());
            req.setCartId(customerComplaintsEntity.getCartId());
            req.setOrderType(customerComplaintsEntity.getOrderType());
            Result<CartRes> cartResResult = cartQueryService.queryCart(req);
            CartRes data = cartResResult.getData();

            CreateOrderReq req1 = new CreateOrderReq();
            req1.setCartId(customerComplaintsEntity.getCartId());
            req1.setSkuList(data.getSkuList());
            req1.setOrderType(data.getOrderType());
            req1.setScene(CartSceneEnum.CUSTOMER_COMPLAINTS.code());
            req1.setOriginalPrice(data.getOriginalPrice());
            req1.setPayAmount(BigDecimal.ZERO);
            req1.setStaffId(customerComplaintsEntity.getCreator());
            req1.setStoreId(data.getStoreId());
            req1.setClientUid(data.getClientUid());
            req1.setExtraInfo(data.getExtraInfo());
            req1.setBasicUid(data.getBasicUid());
            req1.setStaffPhone(data.getStaffPhone());
            req1.setOriginalPrice(data.getTotalAmount());
            req1.setBu(0);
            Operator operator = new Operator();
            operator.setOperatorGuid(customerComplaintsEntity.getCreator() + "");
            req1.setOperator(operator);

            Result<CreateOrderRes> orderRes = orderCreateService.createOrder(req1);
            //更新成全额支付
            CreateOrderRes data1 = orderRes.getData();
            Integer orderId = data1.getOrderId();
            customerComplaintsEntity.setCompensationOrderId(orderId.longValue());
            customerComplaintsRepository.updateCompensationOrderId(customerComplaintsEntity);
        }

        HeOrderEntity order = orderRepository.getByOrderId(customerComplaintsEntity.getCompensationOrderId().intValue());
        log.info("订单全额支付，订单id:{}，sn:{}", order.getOrderId(), order.getOrderSn());
        order.setPaidAmount(0);
        order.setRealAmount(0);
        order.setProductionAmountPay(0);
        long percentFirstTime = System.currentTimeMillis() / 1000;
        order.setPercentFirstTime((int) percentFirstTime);
        order.setIsPreviouslyFullyPaid(Boolean.FALSE);
        order.setJustEffect(Boolean.TRUE);
        order.fullPayment(null);

        orderRepository.updateOrderMonthByOrderId(order);
        bizContext.setAttribute(HeOrderEntity.class, order);

    }

    public boolean condition(FlowContext bizContext) {
        HeCustomerComplaintsEntity bizContextAttribute = bizContext.getAttribute(HeCustomerComplaintsEntity.class);
        Integer cartId = bizContextAttribute.getCartId();
        if (Objects.nonNull(cartId)) {
            return true;
        }
        return false;
    }
}

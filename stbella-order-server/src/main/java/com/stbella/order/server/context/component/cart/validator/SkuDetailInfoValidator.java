package com.stbella.order.server.context.component.cart.validator;

import com.stbella.core.exception.BusinessException;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import com.stbella.store.common.enums.core.GoodsSaleStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import java.util.List;

import static com.stbella.order.common.enums.ErrorCodeEnum.GOODS_OFFLINE;


/**
 * 礼赠金校验
 */
@Component
@Slf4j
@SnowballComponent(name = "商品状态校验", desc = "")
public class SkuDetailInfoValidator implements IExecutableAtom<FlowContext> {


    @Override
    public void run(FlowContext bizContext) {

        List<SkuDetailInfo> skuDetailInfos = bizContext.getListAttribute(SkuDetailInfo.class);
        if (CollectionUtils.isEmpty(skuDetailInfos)) {
            return;
        }
        for (SkuDetailInfo skuDetailInfo : skuDetailInfos) {
            Integer skuSaleState = skuDetailInfo.getSkuSaleState();
            if (GoodsSaleStateEnum.OFFLINE.code().equals(skuSaleState)) {
                throw new BusinessException(GOODS_OFFLINE.code() + "", GOODS_OFFLINE.desc());
            }
            List<SkuDetailInfo> subList = skuDetailInfo.getSubList();
            if (CollectionUtils.isNotEmpty(subList)) {
                for (SkuDetailInfo subSkuDetailInfo : subList) {
                    Integer subSkuSaleState = subSkuDetailInfo.getSkuSaleState();
                    if (GoodsSaleStateEnum.OFFLINE.code().equals(subSkuSaleState)) {
                        throw new BusinessException(GOODS_OFFLINE.code() + "", GOODS_OFFLINE.desc());
                    }
                }
            }
        }

    }
}

package com.stbella.order.server.context.component.assembler;

import com.stbella.order.common.enums.core.CartSceneEnum;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.server.context.component.DiscountCalculator;
import com.stbella.order.server.order.DiscountFactReq;
import com.stbella.order.server.order.OrderIndex;
import com.stbella.order.server.order.month.enums.DiscountRuleResultEnum;
import com.stbella.platform.order.api.req.DiscountReq;
import com.stbella.platform.order.api.res.DiscountRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-05-29  15:20
 * @Description: 折扣组装器
 * 判断是否发起折扣审批
 */
@Slf4j
@Component
@SnowballComponent(name = "折扣审批", desc = "判断是否发起折扣审批")
public class DiscountApprovalAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private DiscountCalculator discountCalculator;

    @Override
    public void run(FlowContext bizContext) {

        OrderIndex orderIndex = bizContext.getAttribute(OrderIndex.class);
        CfgStoreEntity cfgStore = bizContext.getAttribute(CfgStoreEntity.class);

        DiscountReq discountReq = bizContext.getAttribute(DiscountReq.class);
        Integer scene = discountReq.getScene();
        log.info("折扣审批判断，订单类型：{}，场景：{}", discountReq.getOrderType(), scene);
        //非随心配订单，需要判断折扣审批
        //配置折扣对象
        DiscountFactReq discountFactReq = DiscountFactReq.builder()
                .orderType(discountReq.getOrderType())
                .grossMargin(orderIndex.getGrossMargin())
                .discountMargin(orderIndex.getDiscountMargin())
                .signOrderDiscountMargin(orderIndex.getSignOrderDiscountMargin())
                .netMargin(orderIndex.getNetMargin())
                .storeBrand(cfgStore.getType())
                .orderScene(scene)
                .warArea(cfgStore.getWarzone())
                .storeId(cfgStore.getStoreId())
                .isNewOrder(Boolean.TRUE)
                .build();
        Integer discountApproval = DiscountRuleResultEnum.NO_APPROVAL_REQUIRED.getCode();
        if (!Arrays.asList(CartSceneEnum.CUSTOMER_COMPLAINTS.code(), CartSceneEnum.GIFT_ORDER.code()).contains(discountReq.getScene())) {
            discountApproval = discountCalculator.isDiscountApprovalNew(discountFactReq);
        }

        DiscountRes discountRes = bizContext.getAttribute(DiscountRes.class);
        if (discountRes == null) {
            discountRes = new DiscountRes()
                    .setDiscountMargin(orderIndex.getDiscountMargin())
                    .setNetMargin(orderIndex.getNetMargin())
                    .setGrossMargin(orderIndex.getGrossMargin());
        }
        discountRes.setDiscountRuleResult(discountApproval);
        bizContext.setAttribute(DiscountRes.class, discountRes);

    }

}

package com.stbella.order.server.listener;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.mail.MailUtils;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.domain.repository.StoreRepository;
import com.stbella.order.server.listener.event.ExportMonthOrderItermEvent;
import com.stbella.order.server.order.cts.excel.SimpleMonthOrderItermExcel;
import com.stbella.order.server.order.order.req.OrderExportReq;
import com.stbella.order.infrastructure.oss.OSSFactory;
import com.stbella.order.server.order.production.res.StoreVO;
import com.stbella.order.server.oss.response.OssUploadResponse;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 导出月订单明细
 * 支付金额大于1
 * 所有订单类型
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ExportMonthOrderItermListener {

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private MailUtils mailUtils;

    @Resource
    RuleLinkClient ruleLinkClient;
    @Resource
    StoreRepository storeRepository;



    @Async
    @EventListener
    public void exportEvent(ExportMonthOrderItermEvent event) {

        log.info("收到导出母婴当月订单明细事件{}", JSONUtil.toJsonStr(event));

        String fileName = "monthOrder-"+ DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
        String path = "stbella/order/excel/" + fileName + ".xlsx";

        OrderExportReq orderExportReq = new OrderExportReq();
        orderExportReq.setStartTime(event.getStartTime());
        orderExportReq.setEndTime(event.getEndTime());

        List<HeOrderEntity> heOrderEntities = orderRepository.queryCurrMonthOrderIterm(orderExportReq);

        Map<Integer, String> storeInfoVOS = storeRepository.queryAllStore().stream().collect(Collectors.toMap(CfgStoreEntity::getStoreId, CfgStoreEntity::getStoreName));

        List<SimpleMonthOrderItermExcel> dataList = new ArrayList<>();
        heOrderEntities.forEach(o->{
            SimpleMonthOrderItermExcel data = BeanMapper.map(o, SimpleMonthOrderItermExcel.class);
            data.setPercentFirstTime(DateUtil.format(new Date(o.getPercentFirstTime()*1000L), "yyyy-MM-dd HH:mm:ss"));
            String storeName = storeInfoVOS.get(o.getStoreId());
            data.setStoreName(storeName);
            dataList.add(data);
        });


        log.info("一共{}条，开始上传腾讯云服务器", heOrderEntities.size());
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        EasyExcel.write(byteArrayOutputStream, SimpleMonthOrderItermExcel.class).sheet("sheet1").doWrite(dataList);

        InputStream inputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());

        OssUploadResponse ossUploadResponse = OSSFactory.build().upload(inputStream, path);
        if (!ossUploadResponse.isSuccess()) {
            log.info("上传腾讯云服务器失败,msg={}", ossUploadResponse.getMsg());
            throw new BusinessException(ResultEnum.FILE_ERROR.getCode(), "上传腾讯服务器失败");
        }

        log.info("上传腾讯云服务器成功, 开始发送邮件,path={}", ossUploadResponse.getUrl());
        Map<String, String> factMap = new HashMap<>();
        factMap.put("configCode","month_order_receiver");
        ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req("systemConfig", factMap);

        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
        // simpleRuleValue 按逗号分成数组
        String[] to = hitRuleVo.getSimpleRuleValue().split(",");
        String subject = "母婴订单业绩明细";
        String content = "请点击链接下载文件：<a href='"+ossUploadResponse.getUrl()+"'>" + ossUploadResponse.getUrl() + "</a>";

        mailUtils.sendSimpleMail(to, subject, content);


    }

}

package com.stbella.order.server.context.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 门店财务信息
 */
@Data
public class StoreFinanceDetailDto implements Serializable {


	private static final long serialVersionUID = -7191801943065509375L;

	@ApiModelProperty(value = "id", required = true)
	private Long id;

	@ApiModelProperty(value = "门店id", required = true)
	private Long storeId;

	@ApiModelProperty(value = "公司主体id", required = true)
	private Long companySubjectId;

	@ApiModelProperty(value = "公司主体名称", required = true)
	private String companySubjectName;

	@ApiModelProperty(value = "客户合同银行账户", required = true)
	private String accountNo;

	@ApiModelProperty(value = "结算主体id", required = true)
	private Long settleCompanySubjectId;

	@ApiModelProperty(value = "招行支付主体id")
	private Long merchantsBankPayId;

	@ApiModelProperty(value = "通联Pos支付主体id")
	private Long tongLianPosPayId;

	@ApiModelProperty(value = "房品性质")
	private Integer storeRoomQualityProperty;

	@ApiModelProperty(value = "房品供应商id")
	private Long storeRoomQualitySupplierId;

	@ApiModelProperty(value = "押金 单位:分")
	private Integer deposit;


	@ApiModelProperty(value = "备房天数")
	private Integer standbyDay;

	@ApiModelProperty(value = "餐品性质")
	private Integer storeFoodQualityProperty;

	@ApiModelProperty(value = "餐品供应商id")
	private Long storeFoodQualitySupplierId;

	@ApiModelProperty(value = "节日费用价格/天 单位:分")
	private Integer holidayPrice;

	@ApiModelProperty(value = "节日费用成本/天 单位:分")
	private Integer holidayCost;

	@ApiModelProperty(value = "多胎费用价格/胎/天 单位:分")
	private Integer multipleKidsPrice;

	@ApiModelProperty(value = "多胎费用成本/胎/天 单位:分")
	private Integer multipleKidsCost;
	@ApiModelProperty(value = "银行账户列表")
	private List<BankAccountDto> bankAccountList;

}

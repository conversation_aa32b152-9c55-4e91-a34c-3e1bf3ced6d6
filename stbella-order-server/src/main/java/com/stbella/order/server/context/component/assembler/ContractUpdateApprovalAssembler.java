package com.stbella.order.server.context.component.assembler;

import com.stbella.contract.api.ContractAuthenticationService;
import com.stbella.contract.api.ContractQueryService;
import com.stbella.core.result.Result;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

/**
 * @Author: wangchuang
 * @CreateTime: 2024-05-28  13:55
 * @Description: 折扣审批组件
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "合同修改审批组件")
public class ContractUpdateApprovalAssembler implements IExecutableAtom<FlowContext> {

    @DubboReference
    private ContractAuthenticationService contractAuthenticationService;

    @DubboReference
    private ContractQueryService contractQueryService;


    @Override
    public void run(FlowContext bizContext) {

        HeOrderEntity heOrderEntity = bizContext.getAttribute(HeOrderEntity.class);

        OrderReductionEntity orderReductionEntity = bizContext.getAttribute(OrderReductionEntity.class);

        Result<Boolean> booleanResult = contractAuthenticationService.signMasterContract(heOrderEntity.getOrderId().longValue());

        orderReductionEntity.setModifyContractApprove(booleanResult.getData());

        bizContext.setAttribute(OrderReductionEntity.class, orderReductionEntity);
    }
}

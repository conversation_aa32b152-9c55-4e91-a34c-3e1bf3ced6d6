package com.stbella.order.server.context.component.assembler;

import cn.hutool.json.JSONUtil;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.domain.order.month.entity.TabClientEntity;
import com.stbella.order.domain.repository.ClientRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.Objects;

@Component
@Slf4j
@SnowballComponent(name = "客户信息查询", desc = "客户信息查询")
public class TabClientAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private ClientRepository clientRepository;

    @Override
    public void run(FlowContext bizContext) {

        Integer clientUid = (Integer) bizContext.getAttribute(BizConstant.ExtraKey.clientUid);
        if (Objects.isNull(clientUid)){
            return;
        }
        TabClientEntity tabClientById = clientRepository.getTabClientById(clientUid);
        log.info("客户信息查询tabClient:{}", JSONUtil.toJsonStr(tabClientById));
        bizContext.addAttribute(TabClientEntity.class, tabClientById);
    }
}

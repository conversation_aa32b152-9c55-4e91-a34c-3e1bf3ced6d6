package com.stbella.order.server.order.production.component;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.Week;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.stbella.core.base.Operator;
import com.stbella.core.base.PageVO;
import com.stbella.core.result.Result;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.core.IsNanEnum;
import com.stbella.order.common.enums.production.*;
import com.stbella.order.common.exception.ApplicationException;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.domain.client.ClientWechatService;
import com.stbella.order.domain.client.StoreGoodsClient;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.production.GoodsSkuEntity;
import com.stbella.order.domain.order.production.OrderProductionAppointmentEntity;
import com.stbella.order.domain.order.production.OrderProductionAppointmentOptLogEntity;
import com.stbella.order.domain.order.production.dto.ExportProductionBookDTO;
import com.stbella.order.domain.repository.GoodsSkuRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.domain.repository.ProductionAppointmentOptLogRepository;
import com.stbella.order.domain.repository.ProductionAppointmentRepository;
import com.stbella.order.server.convert.AppProductionAppointmentConverter;
import com.stbella.order.server.convert.AppProductionAppointmentOptLogConverter;
import com.stbella.order.server.order.month.constant.BaseConstant;
import com.stbella.order.server.order.production.req.*;
import com.stbella.order.server.order.production.res.*;
import com.stbella.sso.therapist.api.TherapistQueryService;
import com.stbella.sso.therapist.req.TherapistScheduleCalendarQueryReq;
import com.stbella.sso.therapist.res.TherapistScheduleGanttVO;
import com.stbella.store.goodz.res.GoodsImageAndServiceTypeVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.stbella.order.common.constant.BizConstant.*;
import static com.stbella.order.common.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <p>
 * 产康预约面板组装数据
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-10 10:18
 */
@Component
@Slf4j
public class ProductionAppointmentAssembler {

    @Resource
    ProductionAppointmentRepository appointmentRepository;
    @Resource
    ProductionAppointmentOptLogRepository appointmentOptLogRepository;

    @Resource
    AppProductionAppointmentConverter appointmentConverter;
    @Resource
    AppProductionAppointmentOptLogConverter appointmentOptLogConverter;

    @Resource
    GoodsSkuRepository goodsSkuRepository;

    @DubboReference
    private TherapistQueryService therapistQueryService;

    @Resource
    ClientWechatService clientWechatService;
    @Resource
    private OrderRepository orderRepository;

    @Resource
    private StoreGoodsClient storeGoodsClient;

    /**
     * 面板组装
     *
     * @param query 查询
     * @return {@link ProductionBoardAppointmentVo}
     * @throws Exception 异常
     */
    public ProductionBoardAppointmentVo boardAssembler(ProductionAppointmentQuery query) throws Exception {

        final List<OrderProductionAppointmentEntity> orderProductionAppointmentEntities = appointmentRepository.querySearchAppointmentList(query);
        final ProductionBoardAppointmentVo appointmentVo = new ProductionBoardAppointmentVo();
        final List<ProductionBoardAppointmentVo.Body> bodyList = Lists.newArrayList();
        //组装body 部分 && php api 用户微信头像获取
        List<WechatUserVo> wechatList = clientWechatService.getWechatList(orderProductionAppointmentEntities.stream().map(OrderProductionAppointmentEntity::getClientPhone).distinct().collect(Collectors.toList()));
        log.info("预约订单看板获取微信用户列表查询{}", JSONUtil.toJsonStr(wechatList));
        final Map<String, WechatUserVo> wechatUserVoMap = wechatList.stream().collect(Collectors.toMap(WechatUserVo::getPhoneNumber, Function.identity(), (item1, item2) -> item1));
        orderProductionAppointmentEntities.forEach(item -> {
            final ProductionBoardAppointmentVo.Body body = appointmentConverter.entity2VoForProductionBoard(item);
            if(!wechatUserVoMap.isEmpty() && wechatUserVoMap.containsKey(item.getClientPhone())){
                final WechatUserVo mapItem = wechatUserVoMap.get(item.getClientPhone());
                body.setAvatarUrl(mapItem.getAvatarUrl());
            }
            bodyList.add(body);
        });

        final LinkedHashMap<String, List<ProductionBoardAppointmentVo.Body>> bodyMap = bodyList.stream().sorted(Comparator.comparing(ProductionBoardAppointmentVo.Body::getDay)).collect(Collectors.groupingBy(ProductionBoardAppointmentVo.Body::getDay,LinkedHashMap::new, Collectors.toList()));
        appointmentVo.setBody(bodyMap);

        String serveStart = Objects.nonNull(query.getServeStart()) ? DateUtil.formatDate(DateUtil.date(query.getServeStart())) : null;
        String serveEnd = Objects.nonNull(query.getServeEnd()) ? DateUtil.formatDate(DateUtil.date(query.getServeEnd())) : null;

        final List<ProductionBoardAppointmentVo.Header> headerList = headerList(serveStart, serveEnd);
        // todo 处理 头部状态值
        headerList.forEach(item -> {
            item.setStatus(bodyList.stream().anyMatch(o -> item.getDay().equals(o.getDay())) ? IsNanEnum.YES.code() : IsNanEnum.NO.code());
        });
        appointmentVo.setHeader(headerList);
        //todo 处理 setting
        Map<String, String> date = getDate(serveStart, serveEnd);
        TherapistScheduleCalendarQueryReq therapistScheduleCalendarQueryReq = new TherapistScheduleCalendarQueryReq();
        log.info("预约订单看板列表查询{}", JSONUtil.toJsonStr(query));
        therapistScheduleCalendarQueryReq.setId(query.getTherapistId());
        therapistScheduleCalendarQueryReq.setQueryStartDate(date.get("beginDate"));
        therapistScheduleCalendarQueryReq.setQueryEndDate(date.get("endDate"));
        final Result<List<TherapistScheduleGanttVO>> listResult = therapistQueryService.queryTherapistScheduleGantt(therapistScheduleCalendarQueryReq);
        if (!listResult.getSuccess()) {
            throw new ApplicationException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code(), "产康师服务时间异常，请检查");
        }
        //先过滤enable=0 不可预约
        listResult.getData().forEach(i -> {
            i.setSchedules(i.getSchedules().stream().filter(x -> Objects.equals(IsNanEnum.NO.code(), x.getEnable())).collect(Collectors.toList()));
        });
        //处理
        LinkedHashMap<String, List<ProductionBoardAppointmentVo.Setting>> setting = new LinkedHashMap<>();
        listResult.getData().forEach(i -> {
            setting.put(i.getDate(), i.getSchedules().stream().map(m -> {
                ProductionBoardAppointmentVo.Setting objSetting = new ProductionBoardAppointmentVo.Setting();
                objSetting.setServeStart(m.getStartTime());
                objSetting.setServeEnd(m.getEndTime());
                Long serverTime = DateUtil.between(DateUtil.parse(i.getDate() + " " + m.getStartTime()), DateUtil.parse(i.getDate() + " " + m.getEndTime()), DateUnit.MINUTE);
                objSetting.setServeTime(serverTime.intValue());
                return objSetting;
            }).collect(Collectors.toList()));
        });
        appointmentVo.setSetting(setting);
        appointmentVo.setNowDay(DateUtil.today());

        return appointmentVo;
    }

    /**
     * 标题列表
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link List}<{@link ProductionBoardAppointmentVo.Header}>
     * @throws Exception 异常
     */
    public List<ProductionBoardAppointmentVo.Header> headerList(String beginDate, String endDate) throws Exception {

        List<String> betweenDate = new ArrayList<>();

        Map<String, String> date = getDate(beginDate, endDate);
        betweenDate = DateUtils.getBetweenDate(date.get("beginDate"), date.get("endDate"));

        List<ProductionBoardAppointmentVo.Header> headerList = new ArrayList<>();
        String now = DateUtil.today();
        for (String s : betweenDate) {
            ProductionBoardAppointmentVo.Header header = new ProductionBoardAppointmentVo.Header();
            Week week = DateUtil.dayOfWeekEnum(DateUtil.parse(s));
            header.setDay(s);
            header.setWeek(week.toChinese(""));
            header.setIsToday(s.equals(now) ? 1 : 0);
            headerList.add(header);
        }

        return headerList;
    }


    /**
     * 获取处理后的日期
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link Map}<{@link String}, {@link String}>
     */
    private Map<String, String> getDate(String beginDate, String endDate) {

        Map<String, String> map = new HashMap<>();
        if (StringUtils.isBlank(beginDate) && StringUtils.isBlank(endDate)) {
            DateTime beginDateTime = DateUtil.offsetDay(DateUtil.date(), -7);
            DateTime endDateTime = DateUtil.offsetDay(DateUtil.date(), 13);
            map.put("beginDate", beginDateTime.toDateStr());
            map.put("endDate", endDateTime.toDateStr());
        } else {
            map.put("beginDate", beginDate);
            map.put("endDate", endDate);
        }

        return map;
    }

    public ProductionAppointmentInfoBackVo infoAssembler(ProductionAppointmentInfoBackQuery query) {
        OrderProductionAppointmentEntity orderProductionAppointmentEntity = appointmentRepository.queryAppointmentInfo(query);
        if (orderProductionAppointmentEntity == null) {
            throw new ApplicationException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code(), "记录不存在，请检查参数");
        }
        ProductionAppointmentInfoBackVo productionAppointmentInfoBackVo = appointmentConverter.entity2VoForProductionAppointmentInfoBack(orderProductionAppointmentEntity);

        // 核销记录中包含核销人，核销时间，核销项，取消人，取消时间
        List<OrderProductionAppointmentOptLogEntity> orderProductionAppointmentOptLogEntityList = appointmentOptLogRepository.queryListByAppointmentId(orderProductionAppointmentEntity.getId(), Arrays.asList(OptTypeEnum.WRITE_OFF.code(), OptTypeEnum.CANCEL.code()));
        Map<Integer, OrderProductionAppointmentOptLogEntity> optLogMap = orderProductionAppointmentOptLogEntityList.stream().collect(Collectors.toMap(OrderProductionAppointmentOptLogEntity::getOptType, orderProductionAppointmentOptLogEntity -> orderProductionAppointmentOptLogEntity));
        if (optLogMap.size() > 0) {
            // 核销操作日志
            OrderProductionAppointmentOptLogEntity writeOffOptLog = optLogMap.getOrDefault(OptTypeEnum.WRITE_OFF.code(), null);
            productionAppointmentInfoBackVo.setOptName(writeOffOptLog == null ? "" : writeOffOptLog.getOptName());
            productionAppointmentInfoBackVo.setOptId(writeOffOptLog == null ? 0 : writeOffOptLog.getOptId().intValue());
            productionAppointmentInfoBackVo.setOptGmtCreate(writeOffOptLog == null ? null : writeOffOptLog.getGmtCreate());

            // 取消操作日志
            OrderProductionAppointmentOptLogEntity cancelOptLog = optLogMap.getOrDefault(OptTypeEnum.CANCEL.code(), null);
            productionAppointmentInfoBackVo.setCancelName(cancelOptLog == null ? "" : cancelOptLog.getOptName());
            productionAppointmentInfoBackVo.setCancelTime(cancelOptLog == null ? null : cancelOptLog.getGmtCreate());
        }
//        // 核销记录
//        OrderProductionAppointmentOptLogEntity orderProductionAppointmentOptLogEntity = appointmentOptLogRepository.queryByAppointmentId(orderProductionAppointmentEntity.getId(), OptTypeEnum.WRITE_OFF.code());
//        ProductionAppointmentOptVo productionAppointmentOptVo = appointmentOptLogConverter.entity2VoProductionAppointmentOptLog(orderProductionAppointmentOptLogEntity);
//        if (productionAppointmentOptVo == null) {
//            productionAppointmentInfoBackVo.setOptName("");
//            productionAppointmentInfoBackVo.setOptId(0);
//            productionAppointmentInfoBackVo.setOptGmtCreate(null);
//        }else {
//            productionAppointmentInfoBackVo.setOptName(productionAppointmentOptVo.getOptName());
//            productionAppointmentInfoBackVo.setOptId(productionAppointmentOptVo.getOptId());
//            productionAppointmentInfoBackVo.setOptGmtCreate(productionAppointmentOptVo.getGmtCreate());
//        }
        // 需要获取商品核销项
        GoodsSkuEntity goodsSkuEntity = goodsSkuRepository.selectById(orderProductionAppointmentEntity.getProductionSkuId());
        productionAppointmentInfoBackVo.setGoodsOptionsVerification(goodsSkuEntity == null ? "" : goodsSkuEntity.getOptionsVerification());
        return productionAppointmentInfoBackVo;
    }

    public List<ProductionAppointmentProductionScheduleVo> scheduleAssembler(AppointmentProductionScheduleQuery query) {
        List<OrderProductionAppointmentEntity> productionScheduleEntityList = appointmentRepository.queryProductionSchedule(query);
        Map<String, WechatUserVo> wechatUserVoMap = new HashMap<>();
        Map<Integer, String> goodsImageMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(productionScheduleEntityList)) {
            //组装body 部分 && php api 用户微信头像获取
            List<WechatUserVo> wechatList = clientWechatService.getWechatList(productionScheduleEntityList.stream().map(OrderProductionAppointmentEntity::getClientPhone).distinct().collect(Collectors.toList()));
            log.info("产康日程获取微信用户列表查询{}", JSONUtil.toJsonStr(wechatList));
            wechatUserVoMap = wechatList.stream().collect(Collectors.toMap(WechatUserVo::getPhoneNumber, Function.identity(), (item1, item2) -> item1));

            List<GoodsImageAndServiceTypeVO> goodsImageList = storeGoodsClient.goodsImageAndServiceType(productionScheduleEntityList.stream().map(OrderProductionAppointmentEntity::getProductionGoodsId).distinct().collect(Collectors.toList()));
            if (CollectionUtil.isNotEmpty(goodsImageList)) {
                goodsImageMap = goodsImageList.stream().filter(i -> Objects.nonNull(i.getImage())).collect(Collectors.toMap(GoodsImageAndServiceTypeVO::getId, GoodsImageAndServiceTypeVO::getImage, (item1, item2) -> item1));
            }
        }

        ArrayList<ProductionAppointmentProductionScheduleVo> scheduleVoList = new ArrayList<>();
        Map<String, WechatUserVo> finalWechatUserVoMap = wechatUserVoMap;
        Map<Integer, String> finalGoodsImageMap = goodsImageMap;
        productionScheduleEntityList.forEach(item -> {
            ProductionAppointmentProductionScheduleVo scheduleVo = appointmentConverter.entity2VoForProductionAppointmentScheduleVo(item);
            if(finalWechatUserVoMap.containsKey(item.getClientPhone())){
                WechatUserVo mapItem = finalWechatUserVoMap.get(item.getClientPhone());
                scheduleVo.setAvatarUrl(mapItem.getAvatarUrl());
            }
            scheduleVo.setVerificationStateName(VerificationStatusEnum.getDescByTerminalType(query.getTerminalType(), scheduleVo.getVerificationState()));
            String goodsImage = finalGoodsImageMap.get(item.getProductionGoodsId());

            scheduleVo.setGoodsImage((StringUtils.isNotBlank(goodsImage) ? goodsImage : BaseConstant.PRODUCTION_GOODS_IMAGE_DEFAULT));
            scheduleVoList.add(scheduleVo);
        });
        return scheduleVoList;
    }

    public List<ExportProductionBookDTO> fromDto(List<OrderProductionAppointmentEntity> productionBookList, List<OrderProductionAppointmentOptLogEntity> optLogEntities){
        List<ExportProductionBookDTO> exportProductionBookDTOList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(productionBookList)){
            List<Integer> orderIds = productionBookList.stream()
                    .map(OrderProductionAppointmentEntity::getOrderId)
                    .distinct()
                    .collect(Collectors.toList());
            List<HeOrderEntity> heOrderEntities = orderRepository.listOrderNoByIds(orderIds);
            Map<Integer, HeOrderEntity> orderMap = heOrderEntities.stream().collect(Collectors.toMap(HeOrderEntity::getOrderId, Function.identity(), (item1, item2) -> item1));

            for (OrderProductionAppointmentEntity appointmentEntity : productionBookList){
                ExportProductionBookDTO exportProductionBookDTO = new ExportProductionBookDTO();
                exportProductionBookDTO.setStoreName(appointmentEntity.getStoreName());
                exportProductionBookDTO.setOrderNumber(appointmentEntity.getOrderSn());
                exportProductionBookDTO.setBasicId(appointmentEntity.getBasicId());
                exportProductionBookDTO.setClientName(appointmentEntity.getClientName());
                // exportProductionBookDTO.setClientPhone(appointmentEntity.getClientPhone());
                exportProductionBookDTO.setTherapistName(appointmentEntity.getTherapistName());
                exportProductionBookDTO.setServeFee(AmountChangeUtil.changeF2Y(String.valueOf(appointmentEntity.getServeFee())));
                exportProductionBookDTO.setVerificationState(VerificationStatusEnum.fromCode(appointmentEntity.getVerificationState()));
                exportProductionBookDTO.setServeTime(DateUtils.getSpecialDateToString(appointmentEntity.getServeStart(), appointmentEntity.getServeEnd()));
                exportProductionBookDTO.setAppointmentType(ProductionAppointmentTypeEnum.fromCode(appointmentEntity.getAppointmentType()));
                HeOrderEntity heOrderEntity = orderMap.get(appointmentEntity.getOrderId());
                if (Objects.nonNull(heOrderEntity)) {
                    exportProductionBookDTO.setProductionOrderNo(heOrderEntity.getOrderSn());
                }
                exportProductionBookDTO.setGoodsName(appointmentEntity.getProductionGoodsName());
                exportProductionBookDTO.setSpecificationsName(appointmentEntity.getProductionSkuName());
                exportProductionBookDTO.setProductionType(OrderProductionItemEnum.fromCode(appointmentEntity.getItemType()));
                doHandlerProductionName(exportProductionBookDTO, appointmentEntity);
                exportProductionBookDTO.setProjectType(ProductionServeTypeEnum.fromCode(appointmentEntity.getServeType()));
                exportProductionBookDTO.setBookType(ProductionBookTypeEnum.fromCode(appointmentEntity.getBookType()));
                exportProductionBookDTO.setCreateOrderName(appointmentEntity.getCreatorName());
                exportProductionBookDTO.setCreateOrderTime(DateUtils.format(appointmentEntity.getGmtCreate(), YYYY_MM_DD_HH_MM_SS));
                optLogEntities.stream().filter(opt -> Objects.equals(appointmentEntity.getId(), opt.getAppointmentId())).findFirst().ifPresent(i -> {
                    if (OptTypeEnum.CANCEL.code().equals(i.getOptType())){
                        exportProductionBookDTO.setCancelName(i.getOptName());
                        exportProductionBookDTO.setCancelTime(DateUtils.format(i.getGmtCreate(), YYYY_MM_DD_HH_MM_SS));
                    }
                    if (OptTypeEnum.WRITE_OFF.code().equals(i.getOptType())){
                        exportProductionBookDTO.setOffName(i.getOptName());
                        exportProductionBookDTO.setOffTime(DateUtils.format(i.getGmtCreate(), YYYY_MM_DD_HH_MM_SS));
                        exportProductionBookDTO.setOffProject(appointmentEntity.getOptionsVerification());
                    }
                });
                exportProductionBookDTO.setUpdateName(appointmentEntity.getUpdateName());
                exportProductionBookDTO.setUpdateTime(DateUtils.format(appointmentEntity.getGmtModified(), YYYY_MM_DD_HH_MM_SS));
                exportProductionBookDTO.setTherapistId(appointmentEntity.getTherapistId());
                exportProductionBookDTOList.add(exportProductionBookDTO);
            }
        }
        return exportProductionBookDTOList;
    }

    public OrderProductionAppointmentEntity assembleAppointmentEntity(AppointmentCreateReq req) {
        OrderProductionAppointmentEntity entity = new OrderProductionAppointmentEntity();
        BeanUtils.copyProperties(req, entity);

        String serveStart = req.getServeStart();
        String serveEnd = req.getServeEnd();
        entity.genServeRange(serveStart, serveEnd);

        Operator operator = req.getOperator();
        entity.setCreatorId(Long.valueOf(operator.getOperatorGuid()));
        entity.setCreatorName(operator.getOperatorName());
        entity.setUpdateId(Long.valueOf(operator.getOperatorGuid()));
        entity.setUpdateName(operator.getOperatorName());
        entity.setSource(req.getSource());

        return entity;
    }

    private void doHandlerProductionName(ExportProductionBookDTO exportProductionBookDTO, OrderProductionAppointmentEntity appointmentEntity){
        switch (appointmentEntity.getItemType()){
            case SECONDARY_CARD:
                // 次卡
                exportProductionBookDTO.setProductionName(appointmentEntity.getGroupGoodsName());
                break;
            case PASS_CARD:
                // 通卡
                exportProductionBookDTO.setProductionName(appointmentEntity.getGroupGoodsName() + appointmentEntity.getGroupName());
                exportProductionBookDTO.setCombinationName(appointmentEntity.getGroupName());
                break;
            case INDIVIDUAL_SERVICE:
                // 单项服务
                exportProductionBookDTO.setProductionName(appointmentEntity.getProductionGoodsName());
                break;
            default:
                break;
        }
    }

    /**
     * 产康师预约单 数据组装
     *
     * @param query 查询
     * @return {@link List}<{@link ProductionAppointmentTherapistVo}>
     */
    public ProductionAppointmentTherapistVo therapistAndStoreAssembler(ProductionAppointmentInfoQuery query) {
        List<OrderProductionAppointmentEntity> productionScheduleEntityList = appointmentRepository.queryProductionByTherapistId(query);

        final ProductionAppointmentTherapistVo therapistVo = new ProductionAppointmentTherapistVo();
        List<ProductionAppointmentTherapistVo.Store> storeList = productionScheduleEntityList.stream().map(item -> new ProductionAppointmentTherapistVo.Store(item.getStoreId(), item.getStoreName())).collect(Collectors.toList());
        LinkedHashSet<ProductionAppointmentTherapistVo.Store> setStore = new LinkedHashSet<ProductionAppointmentTherapistVo.Store>(storeList.size());
        setStore.addAll(storeList);
        storeList.clear();
        storeList.addAll(setStore);

        final List<ProductionAppointmentTherapistVo.Therapist> therapistList = productionScheduleEntityList.stream().map(item -> new ProductionAppointmentTherapistVo.Therapist(item.getTherapistId(), item.getTherapistName())).distinct().collect(Collectors.toList());
        LinkedHashSet<ProductionAppointmentTherapistVo.Therapist> setTherapist = new LinkedHashSet<ProductionAppointmentTherapistVo.Therapist>(therapistList.size());
        setTherapist.addAll(therapistList);
        therapistList.clear();
        therapistList.addAll(setTherapist);

        therapistVo.setTherapistList(therapistList);
        therapistVo.setStoreList(storeList);

        return therapistVo;
    }

    /**
     * 组装预约单 列表头像
     *
     * @param query
     * @return {@link PageVO}<{@link ProductionAppointmentMiniVo}>
     */
    public PageVO<ProductionAppointmentMiniVo> appointmentMiniPageAssembler(ProductionAppointmentBackQuery query) {
        Page<OrderProductionAppointmentEntity> appointmentEntityPage = appointmentRepository.querySearchAppointmentListBack(query);
        List<ProductionAppointmentMiniVo> appointmentBackVos = appointmentConverter.entity2VoForListProductionAppointmentMini(appointmentEntityPage.getRecords());
        if (CollectionUtil.isNotEmpty(appointmentEntityPage.getRecords())) {
            //组装body 部分 && php api 用户微信头像获取
            try {
                List<WechatUserVo> wechatList = clientWechatService.getWechatList(appointmentEntityPage.getRecords().stream().map(OrderProductionAppointmentEntity::getClientPhone).distinct().collect(Collectors.toList()));
                log.info("预约单列表获取微信用户列表查询{}", JSONUtil.toJsonStr(wechatList));
                final Map<String, WechatUserVo> wechatUserVoMap = wechatList.stream().collect(Collectors.toMap(WechatUserVo::getPhoneNumber, Function.identity(), (item1, item2) -> item1));

                // 获取产康师信息 组装等级
//                TherapistQueryReq therapistQueryReq = new TherapistQueryReq();
//                Result<List<TherapistVo>> result = therapistQueryService.queryTherapists(therapistQueryReq);
//                if (!result.getSuccess()) {
//                    throw new ApplicationException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code(), "产康师服务有误，请检查");
//                }
//                List<TherapistVo> therapistList = result.getData();
//
//                log.info("调用用sso返回数据为：{}", JSONUtil.toJsonStr(therapistList));

                appointmentBackVos.forEach(item -> {
                    if (!wechatUserVoMap.isEmpty() && wechatUserVoMap.containsKey(item.getClientPhone())) {
                        item.setAvatarUrl(wechatUserVoMap.get(item.getClientPhone()).getAvatarUrl());
                    }

//                    therapistList.stream().filter(i->Objects.equals(i.getId(),item.getTherapistId())).findFirst().ifPresent(t->{
//                        item.setGradeId(t.getGrade());
//                    });
                });

            } catch (Exception e) {
                log.error("预约单列表查询：" + e.getMessage(), e);
            }
        }

        if (CollectionUtil.isNotEmpty(appointmentBackVos)) {
            List<Integer> orderIdList = appointmentBackVos.stream().map(ProductionAppointmentMiniVo::getOrderId).filter(Objects::nonNull).collect(Collectors.toList());
            List<HeOrderEntity> byOrderList = orderRepository.getByOrderList(orderIdList);
            if (CollectionUtil.isNotEmpty(byOrderList)){
                appointmentBackVos.forEach(i->{
                    HeOrderEntity heOrderEntity = byOrderList.stream().filter(o -> Objects.equals(o.getOrderId(), i.getOrderId())).findFirst().orElse(null);
                    i.setOrderNo(Objects.nonNull(heOrderEntity) ? heOrderEntity.getOrderSn() : null);
                });
            }
        }

        PageVO pageVO = new PageVO(appointmentBackVos, (int) appointmentEntityPage.getTotal(), query.getPageSize(), query.getPageNum());

        return pageVO;
    }

}

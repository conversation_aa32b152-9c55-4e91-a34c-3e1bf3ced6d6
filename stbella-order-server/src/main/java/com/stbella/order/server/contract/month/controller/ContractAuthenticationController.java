package com.stbella.order.server.contract.month.controller;

import com.stbella.core.annotations.PreventDuplication;
import com.stbella.core.result.Result;
import com.stbella.order.server.contract.dto.PersonAccountDTO;
import com.stbella.order.server.contract.provider.month.ESignProvider;
import com.stbella.order.server.contract.req.MonthContractQuery;
import com.stbella.order.server.contract.req.MonthContractSignQuery;
import com.stbella.order.server.contract.req.SignContractQuery;
import com.stbella.order.server.contract.res.*;
import com.stbella.order.server.contract.service.month.MonthContractAuthenticationService;

import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;


@RestController
@Api(tags = "【废弃】-已迁移合同stbella-contract服务-月子e签宝合同签署及相关认证")
@RequestMapping("/order/contract/month")
public class ContractAuthenticationController {

    @Resource
    private MonthContractAuthenticationService contractAuthenticationService;

    @Resource
    private ESignProvider eSignProvider;

    @ApiOperation(value = "签署合同(主合同/附件合同/补充协议) && 获取e签宝签署地址")
    @PostMapping("/sign/address")
    @PreventDuplication
    public Result<ContractSignAddressVO> getContractSignAddress(@Valid @RequestBody SignContractQuery signContractQuery) {
        return contractAuthenticationService.getContractSignAddress(signContractQuery);
    }

    @ApiOperation(value = "查看&下载e签宝合同的地址")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "templateType", value = "合同类型 1=>主合同，2=>附属类，3=>补充协议", required = true, paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "contractId", required = true, value = "合同id 查看主合同时传订单orderId，查看附属合同时传附属合同主键id", paramType = "query", dataType = "Long")
    })
    @GetMapping("/download/url")
    public Result<ContractAddressVO> getCheckContractUrl(@RequestParam Long contractId, @RequestParam Integer templateType) {
        return contractAuthenticationService.getCheckContractUrl(contractId, templateType);
    }

    /**
     * 接收thirdPartyUserId
     */
    @ApiOperation(value = "查询e签宝账号信息")
    @ApiIgnore
    @GetMapping("/getByThirdId")
    public Result<PersonAccountDTO> getByThirdId(@RequestParam String thirdPartyUserId) {
        return Result.success(eSignProvider.getByThirdId(thirdPartyUserId));
    }

    @ApiOperation(value = "判断是否签订了主合同 false未签订")
    @GetMapping("/remind/orderId")
    public Result<Boolean> remindByOrderId(@RequestParam Long orderId) {
        return contractAuthenticationService.signMasterContract(orderId);
    }

    @ApiOperation(value = "合同提醒数量总计")
    @GetMapping("/remind/count")
    public Result<ContractRemindCountVO> remindCount(@RequestParam Integer operatorGuid) {
        return contractAuthenticationService.remindDetailCount(operatorGuid);
    }

    @ApiOperation(value = "合同提醒 等待签订合同列表")
    @GetMapping("/remind/detail")
    public Result<ContractRemindVO> remindDetail(@RequestParam Integer operatorGuid) {
        return contractAuthenticationService.remindDetail(operatorGuid);
    }

    @ApiOperation(value = "订单下签合同列表/合同解除协议")
    @GetMapping("/order/list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderId", value = "订单id", required = true, paramType = "query", dataType = "Long"),
            @ApiImplicitParam(name = "templateContractType", value = "24 查询月子标准订单的解除协议", paramType = "query", dataType = "Integer")
    })
    public Result<ContractOrderVO> orderList(@RequestParam Long orderId, @RequestParam(name = "templateContractType", defaultValue = "") Integer templateContractType) {
        return contractAuthenticationService.oldOrNewContractType(orderId, templateContractType);
    }

    @ApiOperation(value = "合同签订状态查询 (主合同/预约协议/附属合同)")
    @PostMapping("/sign/detail")
    public Result<ContractSignStatusVO> querySignDetailV2(@Valid @RequestBody MonthContractQuery contractQuery) {
        return contractAuthenticationService.querySignDetailV2(contractQuery);
    }

    @ApiOperation(value = "合同状态筛选合同列表")
    @PostMapping("/sign/list")
    public Result<List<ContractSignVO>> querySignList(@Valid @RequestBody MonthContractSignQuery signContractQuery) {
        return contractAuthenticationService.querySignList(signContractQuery);
    }

    @ApiOperation(value = "当前订单已签订的合同&&套餐附件列表")
    @PostMapping("/sign/querySignAndAttachmentList")
    public Result<List<ContractAttachmentVO>> querySignAndAttachmentList(@Valid @RequestBody MonthContractSignQuery signContractQuery) {
        return contractAuthenticationService.querySignAndAttachmentList(signContractQuery);
    }

}

package com.stbella.order.server.order.nutrition.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.server.order.nutrition.dto.NutritionExportDTO;
import com.stbella.order.server.order.nutrition.entity.OrderNutritionMonthFoodPO;
import com.stbella.order.server.order.nutrition.mapper.OrderNutritionMonthFoodMapper;
import com.stbella.order.server.order.nutrition.request.QueryManagerOrderListRequest;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 广禾月子餐订单 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2022-03-10
 */
@Service
public class OrderNutritionMonthFoodServiceImpl extends ServiceImpl<OrderNutritionMonthFoodMapper, OrderNutritionMonthFoodPO> implements OrderNutritionMonthFoodService {

    @Override
    public OrderNutritionMonthFoodPO getOrderInfoByOrderNo(String orderNo) {
        LambdaQueryWrapper<OrderNutritionMonthFoodPO> queryWrapper = new LambdaQueryWrapper<OrderNutritionMonthFoodPO>()
                .eq(OrderNutritionMonthFoodPO::getOrderNo, orderNo);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<NutritionExportDTO> getExportList(QueryManagerOrderListRequest managerOrderListRequest) {
        return this.baseMapper.getExportList(managerOrderListRequest);
    }

    @Override
    public List<String> getOrderNoByCustomerName(String customerName, String customerPhone) {
        if (StringUtils.isBlank(customerName) && StringUtils.isBlank(customerPhone)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<OrderNutritionMonthFoodPO> queryWrapper = new LambdaQueryWrapper<OrderNutritionMonthFoodPO>()
                .like(StringUtils.isNotBlank(customerName), OrderNutritionMonthFoodPO::getCustomName, customerName)
                .like(StringUtils.isNotBlank(customerPhone), OrderNutritionMonthFoodPO::getCustomPhone, customerPhone);
        List<OrderNutritionMonthFoodPO> orderNutritionMonthFoods = baseMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(orderNutritionMonthFoods)) {
            return Collections.singletonList("0");
        }
        return orderNutritionMonthFoods.stream().map(OrderNutritionMonthFoodPO::getOrderNo).collect(Collectors.toList());
    }

    @Override
    public OrderNutritionMonthFoodPO getOrderNutritionMonthFoodByOrderNo(String orderNo) {
        OrderNutritionMonthFoodPO orderNutritionMonthFoodPO = this.getBaseMapper().selectOne(
                new LambdaQueryWrapper<OrderNutritionMonthFoodPO>()
                        .eq(OrderNutritionMonthFoodPO::getOrderNo, orderNo)
        );

        if (ObjectUtil.isNull(orderNutritionMonthFoodPO)) {
            throw new BusinessException(ResultEnum.NOT_EXIST, "订单不存在");
        }
        return orderNutritionMonthFoodPO;
    }
}

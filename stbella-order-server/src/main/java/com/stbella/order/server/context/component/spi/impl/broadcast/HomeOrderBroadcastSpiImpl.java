package com.stbella.order.server.context.component.spi.impl.broadcast;

import com.stbella.core.enums.BusinessEnum;
import com.stbella.core.result.Result;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.server.context.component.spi.OrderBroadcastSpi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.annotation.FuncPointSpiImpl;
import top.primecare.snowball.extpoint.definition.FuncSpiConfig;


/**
 * @Author: ji<PERSON><PERSON>an
 * @CreateTime: 2024-05-30  14:01
 * @Description: 予家订单报单扩展点实现
 */
@Component
@Slf4j
@FuncPointSpiImpl(name = "予家订单报单扩展点实现")
public class HomeOrderBroadcastSpiImpl implements OrderBroadcastSpi {

    @Override
    public boolean condition(HeOrderEntity param) {
        if (CompareUtil.integerEqual(param.getBu() , BusinessEnum.CARE_FOR_HOME.getCode())){
            return true;
        }
        return false;
    }

    @Override
    public Result invoke(HeOrderEntity param) {
        log.info("执行予家订单报单扩展点");
        return Result.success();
    }

    @Override
    public FuncSpiConfig config() {
        //默认优先级高
        return new FuncSpiConfig(true, 100);
    }
}

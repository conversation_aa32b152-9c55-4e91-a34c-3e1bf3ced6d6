package com.stbella.order.server.context.component.spi.impl.broadcast;

import cn.hutool.core.date.DateUtil;
import com.stbella.core.enums.BusinessEnum;
import com.stbella.core.result.Result;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.month.PayStatusV2Enum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.month.entity.UserEntity;
import com.stbella.order.domain.order.service.OrderGoodsDomainService;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.domain.repository.UserRepository;
import com.stbella.order.server.context.component.spi.OrderBroadcastSpi;
import com.stbella.order.server.order.cts.constant.CtsOrderConstant;
import com.stbella.order.server.order.cts.dto.CtsPerformanceDTO;
import com.stbella.order.server.order.month.component.NoticeAssembler;
import com.stbella.order.server.order.month.req.OrderQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.annotation.FuncPointSpiImpl;
import top.primecare.snowball.extpoint.definition.FuncSpiConfig;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.List;
import java.util.Objects;


/**
 * @Author: jijunjian
 * @CreateTime: 2024-05-30  14:01
 * @Description: 予家订单报单扩展点实现
 */
@Component
@Slf4j
@FuncPointSpiImpl(name = "予家订单报单扩展点实现")
public class HomeOrderBroadcastSpiImpl implements OrderBroadcastSpi {

    @Resource
    private NoticeAssembler noticeAssembler;

    @Resource
    private UserRepository userRepository;

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private OrderGoodsDomainService orderGoodsDomainService;

    @Resource
    private IncomeRecordRepository incomeRecordRepository;


    @Override
    public boolean condition(HeOrderEntity param) {
        if (CompareUtil.integerEqual(param.getBu() , BusinessEnum.CARE_FOR_HOME.getCode())){
            return true;
        }
        return false;
    }

    @Override
    public Result invoke(HeOrderEntity param) {

        log.info("执行予家订单报单扩展点");
        CtsPerformanceDTO ctsPerformanceDTO = new CtsPerformanceDTO();
        String template = CtsOrderConstant.CTS_EMPLOYER_NOTICE_COMMON;
        if (OmniOrderTypeEnum.getHomeOrderTypeCodeList().contains(param.getOrderType())) {
            log.info("执行予家订单-雇主订单模块报单");
            List<HeOrderEntity> heOrderEntitieList = getOrderListByMonth();
            this.fillCtsPerformanceDTO(param, ctsPerformanceDTO, heOrderEntitieList);
            String msg = MessageFormat.format(template, ctsPerformanceDTO.getSellName(), ctsPerformanceDTO.getComboName(),
                    ctsPerformanceDTO.getSignAmount(), ctsPerformanceDTO.getSignCount(), ctsPerformanceDTO.getSellActuallyAmount(),
                    ctsPerformanceDTO.getActuallyAmount());
            noticeAssembler.pushMsg(msg);
        }
        return Result.success();
    }

    private void fillCtsPerformanceDTO(HeOrderEntity heOrderEntity, CtsPerformanceDTO ctsPerformanceDTO, List<HeOrderEntity> heOrderEntitieList) {

        ctsPerformanceDTO.setSellName(getSellName(heOrderEntity.getStaffId()));
        ctsPerformanceDTO.setComboName(getComboName(heOrderEntity.getOrderId()));
        ctsPerformanceDTO.setSignAmount(AmountChangeUtil.changeF2Y(heOrderEntity.getPayAmount()).toString());
        ctsPerformanceDTO.setSignCount(getSignCount(heOrderEntitieList, heOrderEntity));
        ctsPerformanceDTO.setSellActuallyAmount(getSignAmount(heOrderEntity));
        ctsPerformanceDTO.setActuallyAmount(orderRepository.getActuallyAmount(DateUtil.beginOfMonth(DateUtil.date()), DateUtil.endOfMonth(DateUtil.date())).toString());
    }

    private Integer getActuallyAmount(List<HeOrderEntity> heOrderEntitieList){

        if (CollectionUtils.isEmpty(heOrderEntitieList)){
            return 0;
        }
        return heOrderEntitieList.stream().mapToInt(item -> {
            if (Objects.isNull(item.getRealAmount())) {
                item.setRealAmount(0);
            }
            if (Objects.isNull(item.getCashReviewingAmount())) {
                item.setCashReviewingAmount(0);
            }
            return item.getRealAmount() + item.getCashReviewingAmount();
        }).sum();
    }

    private String getSignAmount(HeOrderEntity heOrderEntity){

        List<HeIncomeRecordEntity> allRecordListByOrderId = incomeRecordRepository.getAllRecordListByOrderId(heOrderEntity.getOrderId());
        if (CollectionUtils.isEmpty(allRecordListByOrderId)){
            return "0";
        }
        return AmountChangeUtil.changeF2Y(allRecordListByOrderId.get(0).getIncome()).toString();
    }

    private String getSignCount(List<HeOrderEntity> heOrderEntitieList, HeOrderEntity heOrderEntity){

        if (CollectionUtils.isEmpty(heOrderEntitieList)){
            return "1";
        }
        long count = heOrderEntitieList.stream().filter(order -> Objects.nonNull(order.getPayFirstTime()) && order.getPayFirstTime() > 0 && order.getStaffId().equals(heOrderEntity.getStaffId()) && ((Objects.nonNull(order.getRealAmount()) && order.getRealAmount() > 0) ||
                (Objects.nonNull(heOrderEntity.getCashReviewingAmount()) && heOrderEntity.getCashReviewingAmount() > 0))).count();
        return String.valueOf(count);
    }

    private String getSellName(Integer id){

        UserEntity userEntity = userRepository.queryById(id);
        if (Objects.isNull(userEntity)){
            return StringUtils.EMPTY;
        }
        return userEntity.getName();
    }

    private String getComboName(Integer orderId){

        List<HeOrderGoodsEntity> goodsEntityList = orderGoodsDomainService.queryOrderGoodsWithGroup(orderId);
        if (CollectionUtils.isEmpty(goodsEntityList)){
            return StringUtils.EMPTY;
        }
        StringBuilder comboName = new StringBuilder();
        for (HeOrderGoodsEntity heOrderGoodsEntity : goodsEntityList) {
            if (comboName.length() > 0){
                comboName.append(",");
            }
            if (StringUtils.isNotEmpty(heOrderGoodsEntity.getGoodsName())){
                comboName.append(heOrderGoodsEntity.getGoodsName());
            }
        }
        return comboName.toString();
    }

    private List<HeOrderEntity> getOrderListByMonth(){

        OrderQuery query = new OrderQuery();
        query.setOrderTypeList(OmniOrderTypeEnum.getHomeOrderTypeCodeList());
        query.setPayStatusList(PayStatusV2Enum.getAllNormalCode());
        query.setMaxCreatedAt(DateUtil.endOfMonth(DateUtil.date()).getTime() / 1000L);
        query.setMinCreatedAt(DateUtil.beginOfMonth(DateUtil.date()).getTime() / 1000L);
        return orderRepository.queryByCondition(query);

    }

    @Override
    public FuncSpiConfig config() {
        //默认优先级高
        return new FuncSpiConfig(true, 100);
    }
}

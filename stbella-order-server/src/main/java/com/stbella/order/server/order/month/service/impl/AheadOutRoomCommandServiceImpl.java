package com.stbella.order.server.order.month.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.domain.order.month.entity.AheadOutRoomEntity;
import com.stbella.order.domain.repository.AheadOutRoomRepository;
import com.stbella.order.server.convert.AppAheadOutRoomConverter;
import com.stbella.order.server.order.month.component.OrderIncomeAssembler;
import com.stbella.order.server.order.month.req.ClientRoomQuery;
import com.stbella.order.server.order.month.req.CreateAheadOutRoomReq;
import com.stbella.order.server.order.month.req.SaveAheadOutRoomReq;
import com.stbella.order.server.order.month.res.MonthIncomeRefundVO;
import com.stbella.order.server.order.month.res.RoomStateQueryVO;
import com.stbella.order.server.order.month.service.AheadOutRoomCommandService;
import com.stbella.order.server.order.month.service.AheadOutRoomQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-12 11:41
 */
@Service
@Slf4j
public class AheadOutRoomCommandServiceImpl implements AheadOutRoomCommandService {
    @Resource
    private AppAheadOutRoomConverter appAheadOutRoomConverter;
    @Resource
    private AheadOutRoomRepository aheadOutRoomRepository;
    @Resource
    private OrderIncomeAssembler orderIncomeAssembler;
    @Resource
    private AheadOutRoomQueryService aheadOutRoomQueryService;

    @Override
    public Result<Integer> createAheadOutRoom(CreateAheadOutRoomReq req) {
        log.info("创建提前离管{}", JSONUtil.toJsonStr(req));
        //设置提前离馆可退金额
        AheadOutRoomEntity aheadOutRoomEntity = appAheadOutRoomConverter.req2AheadOutRoomEntity(req);
        aheadOutRoomEntity.setRemainingRefundableAmount(aheadOutRoomEntity.getRefundedAmount());

        ClientRoomQuery clientRoomQuery = new ClientRoomQuery();
        clientRoomQuery.setOrderId(req.getOrderId());
        Result<RoomStateQueryVO> roomStateQueryVOResult = aheadOutRoomQueryService.queryClientRoomStateByOrderId(clientRoomQuery);
        RoomStateQueryVO data = roomStateQueryVOResult.getData();
        if (ObjectUtil.isNotEmpty(data)) {
            aheadOutRoomEntity.setRoomCheckInDate(DateUtil.parseDate(data.getCheckInDate()));
            aheadOutRoomEntity.setRoomCheckOutDate(DateUtil.parseDate(data.getCheckOutDate()));
        }
        if (ObjectUtil.isNotEmpty(req.getOperator().getOperatorGuid())) {
            aheadOutRoomEntity.setCreateBy(new Long(req.getOperator().getOperatorGuid()));
        }
        aheadOutRoomEntity.setCreateByName(req.getOperator().getOperatorName());
        Integer integer = aheadOutRoomRepository.saveOutRoom(aheadOutRoomEntity);
        return Result.success(integer);
    }

    @Override
    public Result<Boolean> updateAheadOutRoom(SaveAheadOutRoomReq req) {
        log.info("更新提前离管{}", JSONUtil.toJsonStr(req));
        MonthIncomeRefundVO monthIncomeRefundVO = orderIncomeAssembler.queryApplyRefundAssembler(req.getOrderId());
        log.info("订单可退金额查询{}", JSONUtil.toJsonStr(monthIncomeRefundVO));
        //设置提前离馆可退金额
        BigDecimal sub = NumberUtil.sub(orderIncomeAssembler.queryApplyRefundAssembler(req.getOrderId()).getOrderRefundAmountCurrentPrice(), req.getRefundedAmount());
        if (sub.compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(),"应退金额要小于等于最大可退金额");
        }
        AheadOutRoomEntity aheadOutRoomEntity = appAheadOutRoomConverter.req2AheadOutRoomEntity(req);
        aheadOutRoomEntity.setRemainingRefundableAmount(aheadOutRoomEntity.getRefundedAmount());
        Boolean aBoolean = aheadOutRoomRepository.updateOutRoom(aheadOutRoomEntity);
        return Result.success(aBoolean);
    }

}

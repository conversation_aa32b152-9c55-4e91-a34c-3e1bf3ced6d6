package com.stbella.order.server.context.component.processor;

import com.stbella.base.server.ding.response.CreateOrderApproveRecordVO;
import com.stbella.core.base.Operator;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.server.context.component.spi.InitiateDiscountApprovalSpi;
import com.stbella.order.server.order.month.enums.DiscountRuleResultEnum;
import com.stbella.platform.order.api.res.DiscountRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.SnowballExtensionInvoker;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import java.util.Objects;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-07-02  11:09
 * @Description: 发起折扣审批
 */
@Component
@Slf4j
@SnowballComponent(name = "发起折扣审批", desc = "")
public class InitiateDiscountApprovalProcessor implements IExecutableAtom<FlowContext> {


    @Override
    public void run(FlowContext bizContext) {
        DiscountRes discountRes = bizContext.getAttribute(DiscountRes.class);
        if (Objects.isNull(discountRes) || !DiscountRuleResultEnum.APPROVAL_REQUIRED.getCode().equals(discountRes.getDiscountRuleResult())) {
            log.info("无须发起折扣");
            return;
        }
        HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);
        order.setDiscountMargin(discountRes.getDiscountMargin());
        order.setGrossMargin(discountRes.getGrossMargin());
        order.setNetMargin(discountRes.getNetMargin());
        order.setSignOrderDiscountMargin(discountRes.getSignOrderDiscountMargin());
        OrderReductionEntity orderReductionEntity = bizContext.getAttribute(OrderReductionEntity.class);
        if (Objects.nonNull(orderReductionEntity)) {
            order.setDisCountAmount(orderReductionEntity.getDecreaseAmount().intValue());
            order.setIsReduceDiscount(Boolean.TRUE);
        } else {
            order.setDisCountAmount(0);
            order.setIsReduceDiscount(Boolean.FALSE);
        }
        // 设置折扣发起人号码
        Operator operator = bizContext.getAttribute(Operator.class);
        if (Objects.nonNull(operator)) {
            order.setDiscountApprovePhone(operator.getOperatorPhone());
        }
        CreateOrderApproveRecordVO approveResult = SnowballExtensionInvoker.invoke(InitiateDiscountApprovalSpi.class, order);
        if (Objects.nonNull(approveResult)) {
            bizContext.setAttribute(CreateOrderApproveRecordVO.class, approveResult);
        }
    }
}

package com.stbella.order.server.convert;

import com.stbella.contract.model.res.ContractSignRecordVO2;
import com.stbella.core.utils.BigDecimalUtil;
import com.stbella.month.server.request.ContractModificationApprovalRequest;
import com.stbella.month.server.request.DepositRefundApprovalRequest;
import com.stbella.month.server.request.PaperContractApprovalRequest;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.order.month.entity.MonthContractSignRecordEntity;
import com.stbella.order.server.order.month.req.OrderMonthClientReq;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.math.BigDecimal;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;

/**
 * <p>
 * 钉钉转换
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-14 16:27
 */
@Mapper(componentModel = "spring", uses = AppImageFormater.class,imports = {DateUtil.class, BigDecimal.class,BigDecimalUtil.class, ObjectUtil.class})
public interface MonthDingConvert {

    @Mappings({
            @Mapping(target = "storeName", source = "cfgStoreEntity.storeName"),
            @Mapping(target = "customerName", source = "monthClientReq.name"),
            @Mapping(target = "customerPhone", source = "monthClientReq.phone"),
            @Mapping(target = "contractName", source = "entity.contractName"),
//            @Mapping(target = "contractContentsList", source = "entity.img", qualifiedByName = "str2List"),//图片地址
            @Mapping(target = "contractContentsList", source = "entity.img"),//图片地址
            @Mapping(target = "reasonSigning", source = "entity.reason"),
            @Mapping(target = "recordId", source = "entity.id"),
            @Mapping(target = "masterAddress", expression = "java(ObjectUtil.isNotEmpty(monthContractSignRecordEntity)?monthContractSignRecordEntity.getContractShortUrl():\"\")"),
    })
    PaperContractApprovalRequest paperWithClient2Approval(ContractSignRecordVO2 entity, OrderMonthClientReq monthClientReq, CfgStoreEntity cfgStoreEntity, ContractSignRecordVO2 monthContractSignRecordEntity);

    @Mappings({
            @Mapping(target = "storeName", source = "cfgStoreEntity.storeName"),
            @Mapping(target = "customerName", source = "monthClientReq.name"),
            @Mapping(target = "customerPhone", source = "monthClientReq.phone"),
            @Mapping(target = "contractName", source = "monthContractSignRecordEntity.contractName"),
            @Mapping(target = "masterAddress", source = "monthContractSignRecordEntity.contractShortUrl"),
    })
    ContractModificationApprovalRequest contractWithClient2Approval(OrderMonthClientReq monthClientReq, CfgStoreEntity cfgStoreEntity, ContractSignRecordVO2 monthContractSignRecordEntity);

    @Mappings({
            @Mapping(target = "storeName", source = "cfgStoreEntity.storeName"),
            @Mapping(target = "customerName", source = "monthClientReq.name"),
            @Mapping(target = "customerPhone", source = "monthClientReq.phone"),
            @Mapping(target = "amountRefundApplied", expression = "java(BigDecimalUtil.divide(new BigDecimal(heOrderRefundEntity.getApplyAmount().toString()),new BigDecimal(100)))"),
            @Mapping(target = "refundReason", source = "heOrderRefundEntity.remark"),
            @Mapping(target = "orderRefundId", source = "heOrderRefundEntity.id"),
    })
    DepositRefundApprovalRequest refundWithClient2Approval(OrderMonthClientReq monthClientReq, CfgStoreEntity cfgStoreEntity, HeOrderRefundEntity heOrderRefundEntity);
}

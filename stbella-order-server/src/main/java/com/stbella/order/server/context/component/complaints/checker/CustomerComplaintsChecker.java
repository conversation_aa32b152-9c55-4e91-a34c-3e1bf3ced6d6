package com.stbella.order.server.context.component.complaints.checker;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsEntity;
import com.stbella.order.domain.repository.HeCustomerComplaintsRepository;
import com.stbella.order.server.order.month.enums.CustomerComplaintsStatusEnum;
import com.stbella.platform.order.api.req.CustomerComplaintsCreateReq;
import com.stbella.platform.order.api.res.CustomerComplaintsCheckRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@RefreshScope
@SnowballComponent(name = "客诉校验")
public class CustomerComplaintsChecker implements IExecutableAtom<FlowContext> {

    @Resource
    HeCustomerComplaintsRepository heCustomerComplaintsRepository;

    @Override
    public void run(FlowContext bizContext) {

        CustomerComplaintsCheckRes customerComplaintsCheckRes = new CustomerComplaintsCheckRes();
        customerComplaintsCheckRes.setShowApproval(Boolean.TRUE);
        CustomerComplaintsCreateReq complaintsCreateReq = bizContext.getAttribute(CustomerComplaintsCreateReq.class);
        List<HeCustomerComplaintsEntity> customerComplaintsEntities = heCustomerComplaintsRepository.getListByOrderId(complaintsCreateReq.getOrderId());
        if (CollectionUtils.isNotEmpty(customerComplaintsEntities)) {
            List<Integer> complainStatusList = customerComplaintsEntities.stream().map(HeCustomerComplaintsEntity::getComplaintStatus).collect(Collectors.toList());
            if (complainStatusList.contains(CustomerComplaintsStatusEnum.SUCCESS.getCode())) {
                int count = (int) complainStatusList.stream().filter(status -> ObjectUtil.isNotEmpty(status) && status.equals(CustomerComplaintsStatusEnum.SUCCESS.getCode())).count();
                customerComplaintsCheckRes.setMsg("当前订单已存在【"+count+"】单处理成功的客诉工单，建议点击【存草稿】确认非重复客诉工单后再次发起审批。确认发起审批?");
            }else {
                customerComplaintsCheckRes.setMsg("客诉审批通过后系统将自动处理订单退款意见及额外赔偿商品，若存在线下汇款将通知财务处理，确认发起审批?");
            }
        } else {
            customerComplaintsCheckRes.setMsg("客诉审批通过后系统将自动处理订单退款意见及额外赔偿商品，若存在线下汇款将通知财务处理，确认发起审批?");
        }
        bizContext.setAttribute(CustomerComplaintsCheckRes.class, customerComplaintsCheckRes);

    }


    @Override
    public boolean condition(FlowContext bizContext) {
        CustomerComplaintsCreateReq complaintsCreateReq = bizContext.getAttribute(CustomerComplaintsCreateReq.class);
        return Boolean.FALSE.equals(complaintsCreateReq.getIsDraft());
    }
}

package com.stbella.order.server.context.service.reduction;

import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.domain.repository.OrderReductionRepository;
import com.stbella.order.server.listener.event.OrderDecreaseSuccessEvent;
import com.stbella.platform.order.api.reduction.api.OrderReductionService;
import com.stbella.platform.order.api.reduction.req.OrderReductionApprovalDealReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Slf4j
@Service
public class OrderReductionServiceImpl implements OrderReductionService {

    @Resource
    private OrderReductionRepository orderReductionRepository;

    @Resource
    private ApplicationContext applicationContext;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reductionApprovalCallback(OrderReductionApprovalDealReq req) {
        OrderReductionEntity orderReductionEntity = orderReductionRepository.selectByLocalProcessId(req.getLocalProcessId());
        if (orderReductionEntity == null || OrderReductionEntity.SUCCESS_AUTH_STATE.contains(orderReductionEntity.getAuthState())) {
            log.info("无需进行减免审批处理,找不到对应的减免审批记录,localProcessId={}", req.getLocalProcessId());
            return;
        }
        orderReductionEntity.setAuthState(req.getAuthState());
        orderReductionRepository.updateOne(orderReductionEntity);
        if (orderReductionEntity.isSuccessAuthState()) {
            //减免审批成功
            publishOrderDecreaseSuccessEvent(orderReductionEntity.getOrderId().intValue(), orderReductionEntity.getId(), req.getType().getCode());
        }
    }

    private void publishOrderDecreaseSuccessEvent(Integer orderId, Long reductionId, Integer successType) {
        OrderDecreaseSuccessEvent event = new OrderDecreaseSuccessEvent();
        event.setOrderId(orderId);
        event.setReductionId(reductionId);
        event.setSuccessType(successType);
        applicationContext.publishEvent(event);
    }
}

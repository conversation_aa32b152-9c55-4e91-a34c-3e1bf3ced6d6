package com.stbella.order.server.contract.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.stbella.order.server.contract.entity.ContractSignRecordPO;
import com.stbella.order.server.contract.service.ContractSignRecordService;
import com.stbella.order.server.order.cts.entity.OrderCtsPO;
import com.stbella.order.server.order.cts.enums.ContractSignStatusEnum;
import com.stbella.order.server.order.cts.enums.YesOrNoEnum;
import com.stbella.order.server.order.cts.service.OrderCtsService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("order/callback/contract")
public class CallbackController {

    @Resource
    private ContractSignRecordService contractSignRecordService;
    @Resource
    private OrderCtsService orderCtsService;


    private static final String SUCCESS_CODE = "200";
    private static final String FLAG = "true";
    //流程结束
    private static final String FINISH_FLOW = "SIGN_FLOW_FINISH";
    //签署人签署完成
    private static final String FINISH_SIGN = "SIGN_FLOW_UPDATE";
    private static final String COMPLETE_CODE = "2";


    @Data
    static class EsignCallbackVO {

        private String code;

        private String msg;
    }

    @ApiIgnore
    @PostMapping("/esign/notify")
    public EsignCallbackVO esignCallback(@RequestBody JSONObject jsonObject) {
        String action = jsonObject.getString("action");
        log.info("esignCallback=====>:{}", jsonObject.toJSONString());
        EsignCallbackVO esignCallback = new EsignCallbackVO();
        esignCallback.setCode(SUCCESS_CODE);
        esignCallback.setMsg("success");
        if (FINISH_FLOW.equals(action)) {
            String flowId = jsonObject.getString("flowId");
            String flowStatus = jsonObject.getString("flowStatus");
            String statusDescription = jsonObject.getString("statusDescription");
            log.info("esignCallback=====>statusDescription:{}", statusDescription);

            //签署完成事件通知，并更新数据库
            if (COMPLETE_CODE.equals(flowStatus)) {
                List<ContractSignRecordPO> contractSignRecordPOs = contractSignRecordService
                        .list(new LambdaQueryWrapper<ContractSignRecordPO>().eq(ContractSignRecordPO::getESignFlowId, flowId));
                if (!contractSignRecordPOs.isEmpty()) {
                    ContractSignRecordPO contractSignRecordPO = contractSignRecordPOs.stream().findFirst().get();
                    contractSignRecordService.update(new LambdaUpdateWrapper<ContractSignRecordPO>()
                            .eq(ContractSignRecordPO::getESignFlowId, flowId)
                            .set(ContractSignRecordPO::getStatus, ContractSignStatusEnum.SIGN_FINISH.getCode()));

                    orderCtsService.update((new LambdaUpdateWrapper<OrderCtsPO>()
                            .eq(OrderCtsPO::getOrderNo, contractSignRecordPO.getOrderNo())
                            .set(OrderCtsPO::getIsSignContract, YesOrNoEnum.YES.getCode())));
                }


                log.info("esignCallback=====>{}", "已经更新主合同签署状态，正在返回状态码给e签宝");
                return esignCallback;
            }
        } else if (FINISH_SIGN.equals(action)) {
            Date signTime = jsonObject.getDate("signTime");
            String flowId = jsonObject.getString("flowId");
            String signResult = jsonObject.getString("signResult");
            if (COMPLETE_CODE.equals(signResult)) {
                contractSignRecordService.update(new LambdaUpdateWrapper<ContractSignRecordPO>()
                        .eq(ContractSignRecordPO::getESignFlowId, flowId)
                        .set(ContractSignRecordPO::getSignTime, signTime));
                return esignCallback;
            }
        }
        return null;
    }
}

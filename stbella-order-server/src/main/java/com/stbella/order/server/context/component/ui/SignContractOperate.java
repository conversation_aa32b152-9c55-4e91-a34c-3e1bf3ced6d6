package com.stbella.order.server.context.component.ui;

import com.stbella.order.common.enums.month.OrderButtonEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.server.order.month.res.OrderOperateButton;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @Author: ji<PERSON><PERSON>an
 * @CreateTime: 2024-08-12  16:28
 * @Description: 签定合同操作
 */
@Component
@Slf4j
public class SignContractOperate implements OrderOperate{
    @Override
    public void setPriority(int priority) {

    }

    @Override
    public int getPriority() {
        return 1;
    }

    /**
     * 生成订单操作按钮
     *
     * @param order
     * @return
     */
    @Override
    public OrderOperateButton generateButton(OrderOperateContext context) {

//        if (Objects.nonNull(context.getOrder().getNeedSign()) && context.getOrder().getNeedSign() != 1) {
//            return null;
//        }
        if (context.getOrder().getOrderStatus() == 0 || context.getOrder().getOrderStatus() == 1 || context.getOrder().getOrderStatus() == 2) {
            return OrderOperateButton.builder().code(getButtonEnum().getCode()).value(getButtonEnum().getValue()).build();
        }

        return null;
    }

    /**
     * 获取按钮枚举
     *
     * @return
     */
    @Override
    public OrderButtonEnum getButtonEnum() {
        return OrderButtonEnum.SIGN_CONTRACT;
    }

    @Override
    public int compareTo(@NotNull IPriority o) {
        return this.getPriority() - o.getPriority();
    }
}

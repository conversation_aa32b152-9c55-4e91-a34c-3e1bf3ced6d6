package com.stbella.order.server.context.component.export;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-01-23  17:33
 * @Description: 订单中产康金使用情况
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductionCoinUsageDto implements Serializable {

    /**
     *  没有价格的产康金的 使用的产康金数量
     */
    private Long useFreeCoin = 0L;

    /**
     *  有价格的产康金 使用的产康金数量。
     */
    private Long useCoin = 0L;


    /**
     *  有价格的产康金支付金额对应的实付金额
     */
    private BigDecimal realAmount = new BigDecimal(0);
}

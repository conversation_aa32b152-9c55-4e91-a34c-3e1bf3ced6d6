package com.stbella.order.server.context.component.export;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Sets;
import com.stbella.asset.api.res.AccountDetailDto;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.utils.SpringContextHolder;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.month.entity.IncomePaidAllocationEntity;
import com.stbella.order.domain.repository.HeCartRepository;
import com.stbella.order.domain.repository.IncomePaidAllocationRepository;
import com.stbella.order.infrastructure.gateway.asset.AssetRemoteService;
import com.stbella.order.server.order.ClientContractSignMainFact;
import com.stbella.order.server.order.month.req.ClientContractSignMainReq;
import com.stbella.order.server.order.month.res.ClientContractSignMainVO;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单使用金币支付的数据处理器
 */
@Slf4j
public class ProductionCoinUsageProcessor  {

    Set<String> productionPaidOrderIds = Sets.newHashSet();

    // 订单号与使用金币的明细列表的映射关系
    Map<String, List<AccountDetailDto>> productionCoinUsageMap = new HashMap<>();

    // 订单号与使用金币统计
    Map<String, ProductionCoinUsageDto> orderCoinUsageMap = new HashMap<>();

    private AssetRemoteService assetRemoteService;
    private IncomePaidAllocationRepository incomePaidAllocationRepository;

    public ProductionCoinUsageProcessor(){
        assetRemoteService = SpringContextHolder.getBean(AssetRemoteService.class);
        incomePaidAllocationRepository = SpringContextHolder.getBean(IncomePaidAllocationRepository.class);

    }

    public void init(Set<String> productionPaidOrderIds) {
        this.productionPaidOrderIds = productionPaidOrderIds;
        // 循环查询订单使用金币的明细
        for (String orderNo : productionPaidOrderIds) {
            List<AccountDetailDto> accountDetailDtos = assetRemoteService.listPaidAccountDetailByOrderNo(orderNo);
            productionCoinUsageMap.put(orderNo, accountDetailDtos);
        }
    }

    /**
     * 统计使用特殊的产康金资产的金额
     * sourceBizId=2
     * @param orderNo
     * @return
     */
    public Long getProductionCoinSpecialUsage(String orderNo) {
        List<AccountDetailDto> accountDetailDtos = productionCoinUsageMap.get(orderNo);
        if (accountDetailDtos == null) {
            return 0L;
        }
        return accountDetailDtos.stream().filter( a-> Objects.nonNull(a.getSourceBizId()) && a.getSourceBizId().equals("2")).mapToLong(AccountDetailDto::getUsedAmount).sum()/100;
    }

    /**
     * 获取订单使用的产康金的情况
     * 多少是免费的产康金，多少是收费的产康金，收费的产康金的金额是多少。
     *
     *
     * @param orderNo
     * @return
     */
    public  ProductionCoinUsageDto getProductionCoinUsage(String orderNo) {
        List<AccountDetailDto> accountDetailDtos = productionCoinUsageMap.get(orderNo);
        if (accountDetailDtos == null) {
            return new ProductionCoinUsageDto();
        }
        // 资产明细中，assetId 不是以 OMNI 开头的，是免费的产康金。
        //  以OMNI  需要根据 incomePaidAllocation 判断。如果存在表示是付费的，否则表示不是。
        long freeProductionCoinPart1 = accountDetailDtos.stream().filter(a -> !a.getAssetId().startsWith("OMNI")).mapToLong(AccountDetailDto::getUsedAmount).sum();

        //新订单产生的产康金，根据这个查询分摊情况
        Set<String> paidProductionCoinAssetIds = accountDetailDtos.stream().filter(a -> a.getAssetId().startsWith("OMNI")).map(AccountDetailDto::getAssetId).collect(Collectors.toSet());

        if (paidProductionCoinAssetIds.isEmpty()) {
            ProductionCoinUsageDto build = ProductionCoinUsageDto.builder().useCoin(0L).useFreeCoin(freeProductionCoinPart1/100).realAmount(new BigDecimal(0)).build();
            return build;
        }
        if (orderCoinUsageMap.containsKey(orderNo)) {
            return orderCoinUsageMap.get(orderNo);
        }

        //每个资产的使用产康金情况
        Map<String, Long> assetPaidMap = new HashMap<>();
        //每个资产的总量
        Map<String, Long> assettotalMap = new HashMap<>();
        Map<String, List<AccountDetailDto>> accountDetailGroupMap = accountDetailDtos.stream().filter(item -> StringUtils.isNotEmpty(item.getAssetId()) && Objects.nonNull(item.getUsedAmount()) && Objects.nonNull(item.getAmount())).collect(Collectors.groupingBy(AccountDetailDto::getAssetId));
        accountDetailGroupMap.forEach((key,valueList)->{
            long usedAmountSum = valueList.stream().mapToLong(AccountDetailDto::getUsedAmount).sum();
            assetPaidMap.put(key, usedAmountSum);
            long amountSum = valueList.stream().mapToLong(AccountDetailDto::getAmount).sum();
            assettotalMap.put(key, amountSum);
        });


        List<IncomePaidAllocationEntity> allocationEntities = incomePaidAllocationRepository.queryListByOrderGoodsSnList(new ArrayList<>(paidProductionCoinAssetIds));
        // 按orderGoodsSn分组 统计goodsSn 对应的价值
        Map<String, Long> assetValueMap = allocationEntities.stream().filter(a->a.getPaidAmount() > 0).collect(Collectors.groupingBy(IncomePaidAllocationEntity::getOrderGoodsSn, Collectors.summingLong(IncomePaidAllocationEntity::getPaidAmount)));

        // 新订单送的产康金，没有在分摊表中，也是免费的。
        long freeProductionCoinPart2 = accountDetailDtos.stream().filter(a -> a.getAssetId().startsWith("OMNI")).filter(a -> !assetValueMap.containsKey(a.getAssetId())).mapToLong(AccountDetailDto::getUsedAmount).sum();

        long freeProductionCoin = freeProductionCoinPart1 + freeProductionCoinPart2;

        ProductionCoinUsageDto build = ProductionCoinUsageDto.builder().useCoin(0L).useFreeCoin(freeProductionCoin/100).realAmount(new BigDecimal(0)).build();

        Long useCoin = 0L;
        BigDecimal realAmount = new BigDecimal(0);

        // 计算每个有价值的资产的使用情况及金额
        for (Map.Entry<String, Long> entry : assetValueMap.entrySet()) {
            String assetId = entry.getKey();
            Long value = entry.getValue();
            Long paidAmount = assetPaidMap.get(assetId);
            Long assetTotal = assettotalMap.get(assetId);
            if (paidAmount == null || assetTotal == null || assetTotal == 0) {
                continue;
            }
            BigDecimal paidValue = new BigDecimal(paidAmount).multiply(new BigDecimal(value)).divide(new BigDecimal(assetTotal), 2, BigDecimal.ROUND_DOWN);

            useCoin +=paidAmount;
            realAmount = realAmount.add(paidValue);
        }
        build.setUseCoin(useCoin/100);
        build.setRealAmount(realAmount.divide(new BigDecimal(100), 2, BigDecimal.ROUND_DOWN));

        orderCoinUsageMap.put(orderNo, build);

        return build;
    }

}

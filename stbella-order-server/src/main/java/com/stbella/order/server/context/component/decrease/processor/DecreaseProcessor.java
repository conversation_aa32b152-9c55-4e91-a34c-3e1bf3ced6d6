package com.stbella.order.server.context.component.decrease.processor;

import com.stbella.base.server.ding.response.CreateOrderApproveRecordVO;
import com.stbella.order.common.enums.month.OrderDecreaseStatusEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.domain.repository.OrderReductionRepository;
import com.stbella.order.server.listener.event.OrderDecreaseSuccessEvent;
import com.stbella.order.server.order.cts.enums.DecreaseTypeEnum;
import com.stbella.order.server.order.month.enums.CreateApproveStatusEnum;
import com.stbella.platform.order.api.reduction.req.DecreaseReq;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @Author: wangchuang
 * @CreateTime: 2024-05-28  13:55
 * @Description: 订单减免组件
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "订单减免组件")
public class DecreaseProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderReductionRepository orderReductionRepository;

    @Resource
    private ApplicationContext applicationContext;

    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity heOrderEntity = bizContext.getAttribute(HeOrderEntity.class);
        DecreaseReq decreaseReq = bizContext.getAttribute(DecreaseReq.class);

        OrderReductionEntity processOrderEntity = bizContext.getAttribute(OrderReductionEntity.class);

        setOrderReductionEntity(heOrderEntity, decreaseReq, processOrderEntity);

        if (processOrderEntity.getShouldApproval()) {
            CreateOrderApproveRecordVO createOrderApproveRecordVO = bizContext.getAttribute(CreateOrderApproveRecordVO.class);
            if (createOrderApproveRecordVO != null) {
                Integer status = createOrderApproveRecordVO.getStatus();
                if (CreateApproveStatusEnum.SUCCESS.getCode().equals(status)) {
                    processOrderEntity.setAuthState(OrderDecreaseStatusEnum.APPROVING.getCode());
                } else if (CreateApproveStatusEnum.FAILED.getCode().equals(status)) {
                    processOrderEntity.setAuthState(OrderDecreaseStatusEnum.INITIATION_FAILED.getCode());
                }
                processOrderEntity.setLocalProcessId(createOrderApproveRecordVO.getId());
            } else {
                processOrderEntity.setAuthState(OrderDecreaseStatusEnum.INITIATION_FAILED.getCode());
            }
        } else {
            processOrderEntity.setAuthState(OrderDecreaseStatusEnum.NO_APPROVAL_NEEDED.getCode());
        }

        Long reductionId = orderReductionRepository.addOne(processOrderEntity);
        processOrderEntity.setId(reductionId);
        //无需审批的情况进行处理
        if (processOrderEntity.isSuccessAuthState()) {
            publishOrderDecreaseSuccessEvent(heOrderEntity, processOrderEntity);
        }

        bizContext.setAttribute(OrderReductionEntity.class, processOrderEntity);
    }

    private void publishOrderDecreaseSuccessEvent(HeOrderEntity heOrderEntity, OrderReductionEntity processOrderEntity) {
        OrderDecreaseSuccessEvent event = new OrderDecreaseSuccessEvent();
        event.setOrderId(heOrderEntity.getOrderId());
        event.setReductionId(processOrderEntity.getId());
        event.setSuccessType(DecreaseTypeEnum.NO_APPROVAL_NEEDED.getCode());
        applicationContext.publishEvent(event);
    }

    @NotNull
    private static OrderReductionEntity setOrderReductionEntity(HeOrderEntity heOrderEntity, DecreaseReq decreaseReq, OrderReductionEntity orderReductionEntity) {
        orderReductionEntity.setOrderId(heOrderEntity.getOrderId().longValue());
        orderReductionEntity.setGmtCreate(new Date());
        orderReductionEntity.setGmtModified(new Date());
        orderReductionEntity.setDecreaseDate(new Date());
        orderReductionEntity.setStoreId(heOrderEntity.getStoreId().longValue());
        orderReductionEntity.setBasicId(heOrderEntity.getBasicUid().longValue());
        orderReductionEntity.setOperator(decreaseReq.getOperator().getOperatorName());
        orderReductionEntity.setOperatorId(decreaseReq.getOperator().getOperatorGuid());
        orderReductionEntity.setBeforeAmount(heOrderEntity.getPayAmount()-heOrderEntity.fetchTotalReductionAmount());
        Integer decreaseAmount = AmountChangeUtil.changeY2FFoInt(decreaseReq.getDecreaseAmount());
        orderReductionEntity.setDecreaseAmount(decreaseAmount.longValue());
        orderReductionEntity.setAfterAmount(orderReductionEntity.getBeforeAmount() - decreaseAmount);
        orderReductionEntity.setReason(decreaseReq.getReason());
        return orderReductionEntity;
    }
}

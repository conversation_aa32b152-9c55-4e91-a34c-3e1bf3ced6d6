package com.stbella.order.server.context.component.processor.coin;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.core.base.Operator;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.common.enums.production.OrderProductionTypeEnum;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeUserProductionAmountListEntity;
import com.stbella.order.domain.repository.HeUserProductionAmountListRepository;
import com.stbella.order.server.order.month.service.impl.OrderAssetTradeService;
import com.stbella.platform.order.api.req.CreateOrderReq;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;
import com.stbella.store.common.enums.core.GoodsTypeEnum;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: JJ
 * @CreateTime: 2024-05-28  13:55
 * @Description: 发放冻结产康金组件
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "发放冻结产康金组件")
public class GiveFreezingProductionCoinProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderAssetTradeService orderAssetTradeService;
    @Resource
    private HeUserProductionAmountListRepository heUserProductionAmountListRepository;

    @Override
    public void run(FlowContext bizContext) {

        //判断商品中是否有产康金商品（assetType=22），分两种场景发放：商品中包含的，额外送的。(只处理单品和组合中的单品)
        List<SkuDetailInfo> skuList = bizContext.getListAttribute(SkuDetailInfo.class);
        HeOrderEntity heOrder = bizContext.getAttribute(HeOrderEntity.class);
        CreateOrderReq req = bizContext.getAttribute(CreateOrderReq.class);
        List<SkuDetailInfo> productionCoinList = new ArrayList<>();

        skuList.forEach(sku -> {
            //处理组合商品
            if (CompareUtil.integerEqual(CombineTypeEnum.COMBINE.code(), sku.getType())) {
                if (CollectionUtil.isNotEmpty(sku.getSubList())){
                    sku.getSubList().forEach(childSku -> {
                        SkuDetailInfo productionCoin = filterProductionCoin(childSku);
                        if (ObjectUtil.isNotNull(productionCoin)){
                            productionCoinList.add(productionCoin);
                        }
                    });
                }
            }else {
                //单品
                SkuDetailInfo productionCoin = filterProductionCoin(sku);
                if (ObjectUtil.isNotNull(productionCoin)){
                    productionCoinList.add(productionCoin);
                }
            }
        });
        // 分组：商品中包含的 or 额外送的
        {
            BigDecimal fixedTotal = productionCoinList.stream().filter(sku-> !sku.getGift()).map(SkuDetailInfo::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            //转成分
            Long fixedTotalLong = fixedTotal.multiply(new BigDecimal(100)).longValue();;
            give(heOrder, req.getOperator(), OrderProductionTypeEnum.BUY, fixedTotalLong);
            log.info("发放产康金，固定部分，订单号：{}，产康金：{}", heOrder.getOrderId(), fixedTotalLong);
        }
        {
            BigDecimal giftTotal = productionCoinList.stream().filter(sku-> sku.getGift()).map(SkuDetailInfo::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            //转成分
            Long giftTotalLong = giftTotal.multiply(new BigDecimal(100)).longValue();;
            give(heOrder, req.getOperator(), OrderProductionTypeEnum.GIFT, giftTotalLong);
            log.info("发放产康金，礼赠部分，订单号：{}，产康金：{}", heOrder.getOrderId(), giftTotalLong);
        }


    }

    /**
     * 判断是否产康金
     * @param sku
     * @return
     */
    protected SkuDetailInfo filterProductionCoin(SkuDetailInfo sku){
        if (CompareUtil.integerEqual(GoodsTypeEnum.Production_Coin.code(), sku.getType())){
            return sku;
        }
        return null;
    }

    /**
     * 发放产康金，本地记录+ 资产中心记录
     * @param order
     * @param operator
     * @param productionTypeEnum
     * @param productionAmount
     */
    protected void give(HeOrderEntity order, Operator operator, OrderProductionTypeEnum productionTypeEnum, Long productionAmount){
        HeUserProductionAmountListEntity heUserProductionAmountListEntity = new HeUserProductionAmountListEntity();
        heUserProductionAmountListEntity.setBasicId(order.getBasicUid());
        heUserProductionAmountListEntity.setTotalAmount(Long.valueOf(productionAmount));
        heUserProductionAmountListEntity.setSource(productionTypeEnum.code());
        heUserProductionAmountListEntity.setClientType(7);
        heUserProductionAmountListEntity.setOperatorId(Integer.valueOf(operator.getOperatorGuid()));
        heUserProductionAmountListEntity.setOperator(operator.getOperatorName());
        heUserProductionAmountListEntity.setOrderId(Long.valueOf(order.getOrderId()));
        heUserProductionAmountListEntity.setCreatedAt(Math.toIntExact(System.currentTimeMillis() / 1000));
        log.info("新增产康金参数={}", JSONUtil.toJsonStr(heUserProductionAmountListEntity));
        heUserProductionAmountListRepository.saveOne(heUserProductionAmountListEntity);
        orderAssetTradeService.saveCkj(order.getOrderId() + "" + productionTypeEnum.code() + "-save", order.getBasicUid() + "", order.getOrderId(), productionAmount.longValue(), productionTypeEnum.code(), orderAssetTradeService.getSellOperator(Integer.valueOf(operator.getOperatorGuid())));
    }
}

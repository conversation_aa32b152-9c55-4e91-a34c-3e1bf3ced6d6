package com.stbella.order.server.context.component.refund.processor;

import cn.hutool.json.JSONUtil;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.month.RefundRecordPayStatusEnum;
import com.stbella.order.domain.order.service.OrderRefundDomainService;
import com.stbella.order.server.order.cts.enums.OrderRefundResultEnum;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import com.stbella.platform.order.api.res.CreateApproveRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;


@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "创建退款审批失败逻辑")
public class OrderRefundCreateFailProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderRefundDomainService orderRefundDomainService;

    @Override
    public boolean condition(FlowContext context) {
        return true;
    }

    @Override
    public void run(FlowContext bizContext) {
        boolean approveStatus = (boolean) bizContext.getAttribute(OtherConstant.CONTINUE);
        CreateApproveRes createApproveRes =  bizContext.getAttribute(CreateApproveRes.class);
        log.info("创建退款审批失败逻辑approveStatus:{}, createApproveRes:{}", approveStatus, JSONUtil.toJsonStr(createApproveRes));
        if (!approveStatus && createApproveRes.getType().equals(OrderRefundResultEnum.RESULT_3.getCode())) {
            CreateRefundReq attribute = bizContext.getAttribute(CreateRefundReq.class);
            //发起失败，全部回滚
            //本次退款Id
            Integer refundId = (Integer) bizContext.getAttribute(OtherConstant.PARENT_REFUND_ORDER_ID);
            //回滚
            orderRefundDomainService.approveFailOrRefuse(refundId, RefundRecordPayStatusEnum.REFUND_RECORD_0);
        }
    }

}

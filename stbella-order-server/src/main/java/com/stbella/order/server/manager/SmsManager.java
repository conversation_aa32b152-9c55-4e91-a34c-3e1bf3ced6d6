package com.stbella.order.server.manager;

import com.stbella.base.server.sms.Sms;
import com.stbella.base.server.sms.SmsService;
import com.stbella.base.server.sms.enums.SmsAppEnum;
import com.stbella.base.server.sms.enums.SmsSignEnum;
import com.stbella.base.server.sms.enums.SmsTemplateV2Enum;
import com.stbella.base.server.sms.request.SmsRequest;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 短信服务
 * @date 2023/1/6 16:42
 */
@Component
public class SmsManager {
    @DubboReference
    private SmsService iSmsService;

    public boolean sendMessage(String phone, SmsAppEnum smsAppEnum, SmsSignEnum smsSignEnum, SmsTemplateV2Enum smsTemplateV2Enum, String[] params) {
        Sms sms = Sms.start(phone)
                //选择应用
                .setMessageApp().babyBella()
                //选择签名
                .setMessageSign().piAida()
                //选择短信模板并设置内容
                .setMessageTemplate().setSmsTemplateV2Enum(smsTemplateV2Enum).setContent(params);

        return iSmsService.sendMessage(sms);
    }

    public boolean sendMessage(String phone, SmsTemplateV2Enum smsTemplateV2Enum, String[] params) {
        SmsRequest smsRequest = new SmsRequest();
        smsRequest.setPhone(phone);
        smsRequest.setSmsAppEnum(SmsAppEnum.BABY_BELLA);
        smsRequest.setSmsSignEnum(SmsSignEnum.PI_AIDA);
        smsRequest.setSmsTemplateV2Enum(smsTemplateV2Enum);
        smsRequest.setParams(params);
        return iSmsService.sendMessage(smsRequest);
    }
}

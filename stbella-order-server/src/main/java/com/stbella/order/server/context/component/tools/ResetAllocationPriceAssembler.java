package com.stbella.order.server.context.component.tools;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.common.enums.order.OrderAdditionalKeyEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.service.OrderGoodsDomainService;
import com.stbella.order.domain.repository.OrderGoodsRepository;
import com.stbella.order.server.context.component.StoreCurrencyContainer;
import com.stbella.order.server.convert.OrderGoodsConverter;
import com.stbella.order.server.order.OrderIndex;
import com.stbella.order.server.order.month.enums.ApprovalDiscountStatusEnum;
import com.stbella.order.server.order.month.res.OrderDiscountsCacheVO;
import com.stbella.platform.order.api.req.CreateOrderReq;
import com.stbella.platform.order.api.req.CustomAttribute;
import com.stbella.platform.order.api.req.ExtraInfo;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import com.stbella.platform.order.api.res.DiscountRes;
import com.stbella.platform.order.api.res.SkuAdditionalInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-03-26  13:55
 * @Description: 重置分摊价格
 * 根据组合商品的价格 重新计算分摊价格
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "重置分摊价格")
public class ResetAllocationPriceAssembler implements IExecutableAtom<FlowContext> {

    @Autowired
    OrderGoodsDomainService orderGoodsDomainService;
    @Resource
    OrderGoodsRepository orderGoodsRepository;

    @Override
    public void run(FlowContext bizContext) {

        HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);
        if (!order.isNewOrder()){
            log.warn("未新订单不处理，不需要重置分摊价格");
            return;
        }
        log.warn("订单处理{} ing", order.getOrderSn());
        List<HeOrderGoodsEntity> goodsEntityList = orderGoodsDomainService.queryOrderGoodsWithGroup(order.getOrderId());

        for (HeOrderGoodsEntity sku : goodsEntityList) {
            sku.setTotalAllocationOriginPrice(sku.getGoodsPriceOrgin() * sku.getGoodsNum());
            {
                HeOrderGoodsEntity updateEntity = new HeOrderGoodsEntity();
                updateEntity.setId(sku.getId());
                updateEntity.setTotalAllocationOriginPrice(sku.getTotalAllocationOriginPrice());
                orderGoodsRepository.update(updateEntity);
            }

            if (CombineTypeEnum.COMBINE.code().intValue() != sku.getType()) {
                continue;
            }
            if (CollectionUtil.isNotEmpty(sku.getSubList())) {
                int index = 0;
                BigDecimal sumAllocationAmount = new BigDecimal(0);
                //计算组合商品总价（元）
                BigDecimal totalPrice = sku.getSubList().stream().map(a -> AmountChangeUtil.changeF2Y(a.getGoodsPriceOrgin())).reduce(BigDecimal.ZERO, BigDecimal::add);
                //升序，这样最后一个商品做尾差处理。不会分到0的情况
                sku.getSubList().sort(Comparator.comparing(HeOrderGoodsEntity::getGoodsPriceOrgin));
                for (HeOrderGoodsEntity childSku : sku.getSubList()) {
                    //最后一个商品，做尾差处理，allocationOriginPrice = 组合价  * (price*num)/sum(price*num)
                    BigDecimal allocationAmount = new BigDecimal(0);
                    BigDecimal goodsTotalAllocationAmount = new BigDecimal(0);
                    BigDecimal num = BigDecimal.valueOf(childSku.getGoodsNum());
                    BigDecimal totalNum = BigDecimal.valueOf(childSku.getGoodsNum());
                    BigDecimal skuPrice = AmountChangeUtil.changeF2Y(sku.getGoodsPriceOrgin());
                    if (index == sku.getSubList().size() - 1) {
                        goodsTotalAllocationAmount = skuPrice.subtract(sumAllocationAmount);
                        allocationAmount = skuPrice.subtract(sumAllocationAmount).divide(totalNum, 2, BigDecimal.ROUND_DOWN);
                    } else {
                        // 组合里的有多个的商品，价格是多个商品的总价
                        BigDecimal price = AmountChangeUtil.changeF2Y(childSku.getGoodsPriceOrgin());
                        goodsTotalAllocationAmount = skuPrice.multiply(price).divide(totalPrice, 2, BigDecimal.ROUND_DOWN);
                        allocationAmount = skuPrice.multiply(price).divide(num, 2, BigDecimal.ROUND_DOWN).divide(totalPrice, 2, BigDecimal.ROUND_DOWN);
                        sumAllocationAmount = sumAllocationAmount.add(allocationAmount.multiply(totalNum));
                    }
                    log.info("skuPrice is {}, sumAllocationAmount is {}, totalPrice is {} ,num is {}, orderGoodsId={},allocationAmount={}", skuPrice, sumAllocationAmount, totalPrice, num, childSku.getId(),allocationAmount);
                    childSku.setAllocationOriginPrice(AmountChangeUtil.changeY2FFoInt(allocationAmount));
                    childSku.setTotalAllocationOriginPrice(AmountChangeUtil.changeY2FFoInt(goodsTotalAllocationAmount));
                    index++;
                }

                BigDecimal newTotalPrice = sku.getSubList().stream().map(a -> AmountChangeUtil.changeF2Y(a.getAllocationOriginPrice()).multiply(new BigDecimal(a.getGoodsNum()))).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (sku.getGoodsPriceOrgin() - AmountChangeUtil.changeY2FFoInt(newTotalPrice) < 100) {
                    sku.getSubList().forEach(goodsEntity -> {
                        HeOrderGoodsEntity updateEntity = new HeOrderGoodsEntity();
                        updateEntity.setId(goodsEntity.getId());
                        updateEntity.setAllocationOriginPrice(goodsEntity.getAllocationOriginPrice());
                        updateEntity.setTotalAllocationOriginPrice(goodsEntity.getTotalAllocationOriginPrice());
                        orderGoodsRepository.update(updateEntity);
                    });

                }else {
                    log.error("组合商品价格分摊异常，原总价：{}，新总价：{}", totalPrice, newTotalPrice);
                    throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR.code()+"", "订单分摊错误:" + order.getOrderSn());
                }
            }
        }

    }

}


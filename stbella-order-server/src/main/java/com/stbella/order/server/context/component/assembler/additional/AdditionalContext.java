package com.stbella.order.server.context.component.assembler.additional;

import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import com.stbella.store.goodz.res.PropertyUserDefinedVO;
import com.stbella.store.goodz.res.PropertyValueListVO;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: ji<PERSON><PERSON><PERSON>
 * @CreateTime: 2024-06-19  11:25
 * @Description: 额外项上下文
 */
@Data
@Builder
public class AdditionalContext {

    /**
     * 门店
     */
    private CfgStoreEntity store;

    /**
     * 关联商品信息
     */
    private SkuDetailInfo sku;

    /**
     * 商品分类扩展属性信息
     */
    private Map<Long, PropertyUserDefinedVO> categoryPropertyMap;

    /**
     * 履约属性信息 如预计入住时间，胎数等
     */
    private Map<String, String> fulfillExtraMap;

    /**
     * 节假日列表
     */
    private List<LocalDate> holidayList;

    /**
     * 前一个商品的离店日期。第二天为下一个商品的入住日期
     */
    LocalDate lastCheckOutDate;

}

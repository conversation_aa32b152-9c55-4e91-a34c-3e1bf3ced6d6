package com.stbella.order.server.context.component.spi.impl;

import com.stbella.core.enums.BusinessEnum;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.server.context.component.spi.CustomerQuerySpi;
import com.stbella.platform.order.api.req.CreateOrderReq;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.annotation.FuncPointSpiImpl;
import top.primecare.snowball.extpoint.definition.FuncSpiConfig;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-05-30  14:01
 * @Description: 予家客户信息组装扩展点实现
 */
@Component
@FuncPointSpiImpl(name = "予家客户信息组装扩展点实现", desc = "予家客户信息，本次不实现")
public class HomeCustomerQuerySpiImpl implements CustomerQuerySpi {
    @Override
    public boolean condition(CreateOrderReq param) {
        // 这里只根据BU判断
        if (CompareUtil.integerEqual(param.getBu() , BusinessEnum.CARE_FOR_HOME.getCode())){
            return true;
        }
        return false;
    }

    @Override
    public HeOrderUserSnapshotEntity invoke(CreateOrderReq param) {
        HeOrderUserSnapshotEntity userSnapshotEntity = new HeOrderUserSnapshotEntity();
        userSnapshotEntity.setClientUid(param.getClientUid());
        userSnapshotEntity.setStoreId(param.getStoreId());
        userSnapshotEntity.setBasicUid(param.getBasicUid());

        return userSnapshotEntity;
    }

    @Override
    public FuncSpiConfig config() {
        return new FuncSpiConfig(true, 1);
    }
}

package com.stbella.order.server.context.component.cart.processor;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.client.IdGenerator;
import com.stbella.order.domain.order.entity.HeCartEntity;
import com.stbella.order.domain.order.entity.HeCartGoodsEntity;
import com.stbella.order.domain.order.remark.HeCartRemark;
import com.stbella.order.domain.repository.HeCartGoodsRepository;
import com.stbella.order.domain.repository.HeCartRepository;
import com.stbella.platform.order.api.req.QueryCartReq;
import com.stbella.platform.order.api.req.RestoreCartReq;
import com.stbella.platform.order.api.res.CartRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
@SnowballComponent(name = "复制购物车实体", desc = "")
public class CopyCartEntityProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    IdGenerator idGenerator;
    @Resource
    private HeCartRepository heCartRepository;
    @Resource
    private HeCartGoodsRepository heCartGoodsRepository;

    @Override
    public void run(FlowContext bizContext) {

        RestoreCartReq req = bizContext.getAttribute(RestoreCartReq.class);
        CartRes cartRes = bizContext.getAttribute(CartRes.class);

        QueryCartReq queryCartReq = QueryCartReq.builder().cartId(req.getBusinessId()).scene(req.getScene()).build();
        HeCartEntity cartEntity = heCartRepository.queryOne(queryCartReq);
        if (cartEntity == null) {
            log.error("未查询到购物车信息req:{}", JSONUtil.toJsonStr(queryCartReq));
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code() + "", "购物车数据异常");
        }
        HeCartRemark heCartRemark = new HeCartRemark();
        heCartRemark.setOldOrderId(cartEntity.getOrderId());
        heCartRemark.setOldCartId(cartEntity.getCartId());
        heCartRemark.setScene(req.getScene());
        cartEntity.setRemark(JSONUtil.toJsonStr(heCartRemark));

        List<HeCartGoodsEntity> goodsEntityList = heCartGoodsRepository.queryListByCartId(cartEntity.getCartId());
        List<HeCartGoodsEntity> cartGoodsList = goodsEntityList.stream().peek(sku -> {
            sku.setItermSn(idGenerator.geneStringIdForLocal(""));
            sku.setParentSn("");
            sku.setAddTime(null);
            sku.setUpdateTime(null);
            sku.setId(null);
            if (CollectionUtil.isNotEmpty(sku.getSubGoodsList())) {
                sku.getSubGoodsList().forEach(subSku -> {
                    subSku.setParentSn(sku.getItermSn());
                    subSku.setItermSn(idGenerator.geneStringIdForLocal(""));
                    subSku.setType(CombineTypeEnum.SIMPLE.code());
                    subSku.setAddTime(null);
                    subSku.setUpdateTime(null);
                    subSku.setId(null);
                });
                sku.setType(CombineTypeEnum.COMBINE.code());
            } else {
                sku.setType(CombineTypeEnum.SIMPLE.code());
            }
        }).collect(Collectors.toList());
        cartEntity.setGoodsList(cartGoodsList);
        cartEntity.setCartSn(idGenerator.geneStringIdForLocal("Cart"));
        cartEntity.setScene(req.getScene());
        cartEntity.setCartId(null);
        cartEntity.setOrderId(null);
        cartEntity.setSubmitted(false);
        cartEntity.setAddTime(new Date());
        cartEntity.setUpdateTime(cartEntity.getAddTime());
        cartEntity.setPayAmount(AmountChangeUtil.changeY2FFoInt(cartRes.getPayAmount()));
        cartEntity.saveOrUpdateCart();
        bizContext.setAttribute(HeCartEntity.class, cartEntity);
    }
}

package com.stbella.order.server.context.component.imports;

import com.google.common.base.Strings;
import com.stbella.order.common.enums.production.OrderProductionItemEnum;
import com.stbella.order.common.enums.production.OrderProductionTypeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.infrastructure.repository.mapper.saas.OrderGiftExtendMapper;
import com.stbella.order.infrastructure.repository.mapper.saas.OrderProductionCardExtendMapper;
import com.stbella.order.infrastructure.repository.mapper.saas.OrderProductionExtendMapper;
import com.stbella.order.infrastructure.repository.po.OrderGiftExtend;
import com.stbella.order.infrastructure.repository.po.OrderProductionCardExtend;
import com.stbella.order.infrastructure.repository.po.OrderProductionExtend;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: ji<PERSON><PERSON><PERSON>
 * @CreateTime: 2023-08-24  16:12
 * @Description: 恢复手工核销的产康订资产
 */
@Slf4j
@Component
public class RestoreProductionAssetProcessor {

    @Resource
    OrderGiftExtendMapper giftExtendMapper;
    @Resource
    OrderProductionExtendMapper extendMapper;
    @Resource
    OrderProductionCardExtendMapper cardExtendMapper;


    /**
     * 功能描述: 恢复手工核销的产康订资产
     * 背景：由于一些原因，后台核销了用户的产康资产，次月需要恢复。
     * 核销的数据来源是两个表： gift_extend 和 order_production_extend
     * order_production_extend 是非单项服务，其明细是放在order_production_card_extend
     * 恢复的逻辑如下：
     * 1. ThirdPartyProductImportDTO.orderSellType 如果是购买，那么根据orderProductionId 查询order_production_extend
     * 1.1 如果 是单项服务，也就是type=2， 那么直接重新生成一条数据
     * 1.2 如果 不是单项服务，那么根据orderProductionId 查询order_production_card_extend，重新生成同样一条数据。
     *
     * 2，ThirdPartyProductImportDTO.orderSellType 如果是非购买，那么根据orderProductionId 查询gift_extend，重新生成同样一条数据。
     *
     *
     * @param orderList
     * @return
     */
    public Integer run(List<ThirdPartyProductImportDTO> orderList){

        //根据获取门店名获取门店id

        List<HeOrderEntity> heOrderList = new ArrayList<>();

        //格式化数据，金额里有逗号的直接替换
        int index = 0;
        for (ThirdPartyProductImportDTO importDTO:orderList){
            if (importDTO.getOrderSellType().equals(OrderProductionTypeEnum.BUY.desc())){

                OrderProductionExtend extend = extendMapper.selectByPrimaryKey(Integer.valueOf(importDTO.getOrderProductionId()));
                if (extend.getType().intValue() == OrderProductionItemEnum.TYPE_GROUP.code()){
                    //如果是通卡，那么就不用管了
                    log.info("通卡不处理 orderId={}：原Id={},商品名={}",extend.getOrderId(),importDTO.getOrderProductionId(),extend.getGoodsName());
                    continue;
                }
                extend.setOriginalId(extend.getId());
                extend.setDataSource(1);
                extend.setGmtCreate(new Date());
                extend.setGmtModified(new Date());
                extend.setStatus(1);
                extend.setVerificationStatus(0);
                extend.setId(null);

                extendMapper.insert(extend);

                //添加cartExtend
                if (extend.getType().intValue() == OrderProductionItemEnum.TYPE_COUNT.code()){
                    OrderProductionCardExtend cardExtend = cardExtendMapper.selectByPrimaryKey(Integer.valueOf(importDTO.getCardExtendId()));
                    cardExtend.setOriginalId(cardExtend.getId());
                    cardExtend.setGmtCreate(new Date());
                    cardExtend.setGmtModified(new Date());
                    cardExtend.setStatus(1);
                    cardExtend.setAppointmentNum(0);
                    cardExtend.setUsedNum(0);

                    int addCount = 0;
                    String fixCountStr = importDTO.getFixCount();
                    if (Strings.isNullOrEmpty(fixCountStr) || fixCountStr.trim().equals("0")){
                        //如果没有修正数量，那么就用未核销数量
                        addCount = Integer.valueOf(importDTO.getUnVerificationCount());
                    }else {
                        addCount = Integer.valueOf(fixCountStr);
                    }
                    cardExtend.setNum(addCount);
                    cardExtend.setId(null);
                    cardExtend.setOrderProductionId(extend.getId());
                    cardExtendMapper.insert(cardExtend);
                }

                log.info("购买商品添加完成 orderId={}：原Id={},商品名={}",extend.getOrderId(),importDTO.getOrderProductionId(),extend.getGoodsName());


            }else {
                OrderGiftExtend orderGiftExtend = giftExtendMapper.selectByPrimaryKey(Integer.valueOf(importDTO.getOrderProductionId()));
                orderGiftExtend.setOriginalId(orderGiftExtend.getId());
                orderGiftExtend.setDataSource(1);
                orderGiftExtend.setStatus(1);
                orderGiftExtend.setVerificationStatus(0);
                orderGiftExtend.setGmtCreate(new Date());
                orderGiftExtend.setGmtModified(new Date());
                orderGiftExtend.setId(null);

                giftExtendMapper.insert(orderGiftExtend);

                log.info("礼赠商品添加完成 orderId={}：原Id={},商品名={}",orderGiftExtend.getOrderId(),importDTO.getOrderProductionId(),orderGiftExtend.getGoodsName());
            }
            index ++;
        }
        log.info("总共添加了{}条数据",index);


        return heOrderList.size();
    }


}

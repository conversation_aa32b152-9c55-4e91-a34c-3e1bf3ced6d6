package com.stbella.order.server.strategy.approval;

import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.order.server.order.month.request.approval.ApprovalStatusInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 审批处理策略
 * @date 2024/3/18 17:35
 */
@Component
@Slf4j
public class ApprovalStrategyFactory {
    private final Map<OrderApproveRecordTypeEnum, ApprovalStrategy> strategyMap = new HashMap<>();

    @Autowired
    public ApprovalStrategyFactory(List<ApprovalStrategy> strategies) {
        for (ApprovalStrategy strategy : strategies) {
            // 获取策略对应的枚举值
            OrderApproveRecordTypeEnum orderApproveRecordTypeEnum = strategy.getOrderApproveRecordTypeEnum();
            strategyMap.put(orderApproveRecordTypeEnum, strategy);
            if (strategy instanceof DiscountApprovalStrategy) {
                strategyMap.put(OrderApproveRecordTypeEnum.NEW_DISCOUNT_APPROVAL, strategy);
            }
        }
    }

    public void processApproval(OrderApproveRecordTypeEnum type, String params, String messageId, ApprovalStatusInfo approvalStatusInfo, String processId) {
        ApprovalStrategy strategy = strategyMap.get(type);
        if (strategy == null) {
            log.error("无法找到对应审批类型的策略处理器: {}", type);
        } else {
            strategy.handleApproval(type, params, messageId, approvalStatusInfo, processId);
        }
    }
}

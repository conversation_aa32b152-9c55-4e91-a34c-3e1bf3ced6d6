package com.stbella.order.server.context.component.contract.stage;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import com.stbella.contract.common.constant.BizConstant;
import com.stbella.contract.model.enums.TemplateContractTypeEnum;
import com.stbella.contract.model.res.ContractTemplateRes;
import com.stbella.contract.model.res.OrganizationVO;
import com.stbella.contract.model.res.TemplateParamVO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.utils.GeneratorUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.MonthOrderParamHistoryEntity;
import com.stbella.order.domain.repository.MonthOrderParamHistoryRepository;
import com.stbella.order.server.config.OrderHistoryTypeConfig;
import com.stbella.order.server.convert.AppMonthContractSignRecordConverter;
import com.stbella.order.server.order.month.enums.OrderHistoryTypeEnum;
import com.stbella.order.server.order.month.res.MonthOrderParamHistoryVO;
import com.stbella.platform.order.api.contract.req.ContractBaseReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import top.primecare.snowball.flow.annotation.SnowballComponent;

import javax.annotation.Resource;
import java.util.*;

/**
 * 查询合同是否已创建
 * 创建则返回
 *
 * <AUTHOR>
 * @since 2023-11-22 08:28
 */

@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "母婴主合同月子标准")
public class MainContractFillStrategy extends ContractFillStrategy {


    @Resource
    private MonthOrderParamHistoryRepository monthOrderParamHistoryRepository;
    @Resource
    private AppMonthContractSignRecordConverter appMonthContractSignRecordConverter;


    public static final Map<Integer, Integer> SPECIAL_TYPE_MAP;

    static {
        SPECIAL_TYPE_MAP = new HashMap<Integer, Integer>() {
            {
                put(TemplateContractTypeEnum.YZ_SAINTBELLA.code(), 1);
                put(TemplateContractTypeEnum.SUPPLIMENT.code(), 2);
            }
        };
    }

    @Override
    protected Map<String, String> buildParam(ContractParamFillContext contractParamFillContext) {
        HeOrderEntity orderEntity = contractParamFillContext.getOrderEntity();
        ContractBaseReq req = contractParamFillContext.getRequest();
        //查询创建流程需要的参数
        Map<String, String> templateData = new HashMap<>();

        //判断新老订单，老订单，直接去params里面去查
        if (!orderEntity.isNewOrder()) {
            Integer templateContractType = req.getTemplateContractType();
            if (Arrays.asList(TemplateContractTypeEnum.YZ_SAINTBELLA.code(), TemplateContractTypeEnum.YZ_NURSE.code(), TemplateContractTypeEnum.YZ_SMALL.code()).contains(templateContractType)) {
                if (OrderHistoryTypeConfig.SPECIAL_TYPE_MAP.containsKey(templateContractType)) {
                    templateContractType = OrderHistoryTypeConfig.SPECIAL_TYPE_MAP.get(templateContractType);
                }
                List<MonthOrderParamHistoryEntity> entities = monthOrderParamHistoryRepository.list(orderEntity.getOrderId().longValue(), 0L, 1, Collections.singletonList(templateContractType));
                if (CollectionUtil.isNotEmpty(entities)) {
                    entities.forEach(entity -> {
                        templateData.put(entity.getMark(), entity.getParamValue());
                    });
                    templateData.put("sign_time_str2", DateTime.now().toDateStr());
                    templateData.put("sign_time_str", DateTime.now().toDateStr());
                    return templateData;
                } else {
                    if (TemplateContractTypeEnum.YZ_NURSE.code().equals(templateContractType)) {
                        throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "订单orderId：" + req.getOrderId() + "主合同填充参数不存在");
                    }
                }
            }
        }

        ContractTemplateRes contractTemplateRes = contractParamFillContext.getContractTemplateRes();
        List<TemplateParamVO> templateParamVOS = contractTemplateRes.getTemplateParamVOS();
        if (CollectionUtils.isEmpty(templateParamVOS)) {
            log.error("合同模板参数为空");
            return templateData;
        }
        Integer orderId = contractParamFillContext.getOrderId();

        //参数填充, 得有个映射逻辑。
        Map<String, String> orderParam = getOrderParam(orderEntity);
        String markStr = "";
        String extraGift = "";

        /**
         * 转换的特殊字段处理
         */
        Map<String, String> organization2Map = organization2Map(contractTemplateRes.getOrganizationVO());

        log.info("合同创建参数[{}]", JSONObject.toJSONString(organization2Map));

        List<MonthOrderParamHistoryVO> monthOrderParamHistoryEntities = new ArrayList<>();
        for (TemplateParamVO templateParamVO : templateParamVOS) {
            MonthOrderParamHistoryVO orderParamHistory = new MonthOrderParamHistoryVO();
            String mark = templateParamVO.getMark();
            String name = templateParamVO.getName();
            String value = orderParam.get(mark);
            //特殊参数处理，公司名称取配置的公司名称
            if (StringUtils.isEmpty(value)) {
                value = organization2Map.get(mark);
                if (StringUtils.isEmpty(value)) {
                    continue;
                }
            }
            templateData.put(mark, value);
            // 截取备注与额外礼赠内容
            mark = "remark".equals(name) ? value : mark;
            extraGift = "extra_gift".equals(name) ? value : extraGift;

            orderParamHistory.setName(name);
            orderParamHistory.setParamValue(value);
            orderParamHistory.setItem(templateParamVO.getItem());
            orderParamHistory.setMark(templateParamVO.getMark());
            orderParamHistory.setTerms(templateParamVO.getTerms());
            orderParamHistory.setOrderId(Long.valueOf(orderId));
            orderParamHistory.setValid(1);
            orderParamHistory.setType(1);
            orderParamHistory.setHistory(0L);
            monthOrderParamHistoryEntities.add(orderParamHistory);
        }
        // 拼接额外礼赠与备注 并替换
        String value = StringUtils.isNotBlank(extraGift) ? BizConstant.GIFT + "：\n\n" + extraGift + "\n\n" + BizConstant.REMARK + "：\n\n" + markStr : BizConstant.REMARK + "：\n\n" + markStr;
        monthOrderParamHistoryEntities.forEach(orderParamHistory -> {
            if ("remark".equals(orderParamHistory.getMark())) {
                orderParamHistory.setParamValue(value);
            }
        });


        //先查前面有没有记录
        List<MonthOrderParamHistoryVO> paramHistories = list(orderId.longValue(), 0L, 1, Arrays.asList(OrderHistoryTypeEnum.MAIN.getCode(), OrderHistoryTypeEnum.AGREEMENT.getCode()));
        //如果前面有记录,则作废这些记录
        if (CollectionUtil.isNotEmpty(paramHistories)) {
            for (MonthOrderParamHistoryVO paramHistory : paramHistories) {
                paramHistory.setHistory(paramHistory.getGmtCreate().getTime());
                paramHistory.setValid(0);
                paramHistory.setContractId(0L);
            }
            monthOrderParamHistoryRepository.updateBatchByParamHistoryId(paramHistories);
        }
        monthOrderParamHistoryRepository.saveBatchByParamHistoryId(monthOrderParamHistoryEntities);
        templateData.put("sign_time_str2", DateTime.now().toDateStr());

        String contractNo = GeneratorUtil.contractNo();
        templateData.put("contract_no", contractNo);
        //如果有合同,根据新参数重新生成合同
        return templateData;
    }

    List<MonthOrderParamHistoryVO> list(Long orderId, Long history, Integer valid, List<Integer> type) {
        return appMonthContractSignRecordConverter.entity2MonthOrderParamHistoryVoList(monthOrderParamHistoryRepository.list(orderId, history, valid, type));
    }

    @Override
    protected String getContractName(ContractParamFillContext contractParamFillContext) {
        HeOrderEntity orderEntity = contractParamFillContext.getOrderEntity();
        if (orderEntity.isNewOrder()) {
            return "服务合同";
        }
        return "母婴护理服务合同书";
    }


    @Override
    public boolean filter(Integer templateContractType) {
        return Arrays.asList(TemplateContractTypeEnum.YZ_SAINTBELLA.getCode(), TemplateContractTypeEnum.YZ_NURSE.getCode(), TemplateContractTypeEnum.YZ_SMALL.getCode()).contains(templateContractType);
    }

    private Map<String, String> organization2Map(OrganizationVO organizationEntity) {
        HashMap<String, String> map = MapUtil.newHashMap();
        if (organizationEntity == null) {
            return map;
        }
        map.put("company_name", organizationEntity.getName());
        map.put("companyName", organizationEntity.getName());
        map.put("bank", organizationEntity.getBankOpenAccountBank());
        map.put("bank_card", organizationEntity.getBankAccount());
        map.put("bank_name", organizationEntity.getBankOpenAccountName());
        return map;
    }

}

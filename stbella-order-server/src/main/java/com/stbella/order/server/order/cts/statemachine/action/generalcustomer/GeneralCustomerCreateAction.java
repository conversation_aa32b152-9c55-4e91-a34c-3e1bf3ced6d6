package com.stbella.order.server.order.cts.statemachine.action.generalcustomer;

import com.stbella.core.base.UserTokenInfoDTO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.cts.server.entity.HomeGeneralProductPO;
import com.stbella.order.server.order.cts.constant.CtsOrderConstant;
import com.stbella.order.server.order.cts.entity.OrderCtsPO;
import com.stbella.order.server.order.cts.enums.OrderStatusStateMachineStatusEnum;
import com.stbella.order.server.order.cts.request.order.GeneralCustomerOrderCreateRequest;
import com.stbella.order.server.order.cts.statemachine.OrderContext;
import com.stbella.order.server.order.cts.statemachine.OrderStateEvent;
import com.stbella.order.server.order.cts.statemachine.action.common.CommonAction;
import com.stbella.order.server.order.month.enums.OrderTypeEnum;
import com.stbella.order.server.utils.BigDecimalUtil;
import com.stbella.store.server.cts.entity.CtsSitePO;

import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.action.Action;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 管理费订单创建
 *
 * <AUTHOR>
 * @date 2022-03-10 11:41
 * @sine 1.0.0
 */
@Slf4j
@Component
public class GeneralCustomerCreateAction implements Action<OrderStatusStateMachineStatusEnum, OrderStateEvent> {

    @Resource
    private CommonAction commonAction;


    @Override
    public void execute(StateContext<OrderStatusStateMachineStatusEnum, OrderStateEvent> stateContext) {
        log.info("执行雇主-通用订单创建" + stateContext.toString());
        OrderContext context = (OrderContext) stateContext.getMessageHeader(OrderContext.HEADER_KEY);

        final GeneralCustomerOrderCreateRequest model = (GeneralCustomerOrderCreateRequest) context.getModel();
        final UserTokenInfoDTO userTokenInfoDTO = model.getUserTokenInfoDTO();
        if(model.getNumber() == null){
            model.setNumber(1);
        }
        if(model.getNumber() > 10000){
            throw new BusinessException(ResultEnum.PARAM_ERROR,"订购条数超过10000");
        }
        // 权限校验
        final CtsSitePO ctsSitePO = commonAction.checkCreateAuth(userTokenInfoDTO);
        //通用商品校验
        final HomeGeneralProductPO productPO = commonAction.checkGeneralProduct(model.getProductId());

        final OrderCtsPO orderPO = new OrderCtsPO();
        orderPO.setProductId(model.getProductId());
        orderPO.setCustomId(model.getCustomerId());

        BigDecimal payableAmount = null;
        BigDecimal orderAmount = productPO.getProductPrice();
        //自定义价格的通用商品
        if (ObjectUtil.isNull(orderAmount)){
            payableAmount = model.getDiyPrice();
        }else {
            //设置折扣
            payableAmount = productPO.getProductPrice()
                    .multiply(new BigDecimal(model.getNumber()));
            commonAction.checkDiscount(model.getDiscountAmount(), model.getDiscount(), payableAmount);
            if (ObjectUtil.isNotNull(model.getDiscountAmount())) {
                payableAmount = orderAmount.subtract(model.getDiscountAmount());
                model.setDiscount(BigDecimalUtil.divide(payableAmount, orderAmount));
            }else if(model.getDiscount() != null){
                payableAmount = orderAmount.multiply(model.getDiscount());
                model.setDiscountAmount(orderAmount.subtract(payableAmount));
            }
        }
        // 设置应收金额
        payableAmount = payableAmount.setScale(2, RoundingMode.HALF_UP);

        orderPO.setPayableAmount(payableAmount);
        orderPO.setDiscount(model.getDiscount());
        orderPO.setNumber(model.getNumber());
        orderPO.setRenewTag(model.getRenewTag());
        orderPO.setDiscountAmount(model.getDiscountAmount());
        orderPO.setPerformance(productPO.getPerformance());
        //分站
        Long ctsSiteId  = ctsSitePO.getId();
        if (ObjectUtil.isNotNull(model.getCtsSiteId())){
            ctsSiteId = model.getCtsSiteId();
        }
        commonAction.fillAndSaveOrder(OrderTypeEnum.CUSTOMER_GENERAL.getCode(), ctsSiteId, model.getRemark(), orderPO, userTokenInfoDTO);


        // 填充orderNo
        stateContext.getStateMachine().getExtendedState().getVariables().put(CtsOrderConstant.MACHINE_VARIABLES_ORDER_NO, orderPO.getOrderNo());
        log.info("执行雇主-通用商品创建订单创建成功 orderNo:{}", orderPO.getOrderNo());
    }
}

package com.stbella.order.server.strategy.approval;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.base.server.sms.enums.SmsTemplateV2Enum;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.month.server.request.DepositRefundApprovalRequest;
import com.stbella.month.server.request.RefundApprovalOfflineRemittanceRequest;
import com.stbella.order.common.enums.month.RefundRecordPayStatusEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.JsonUtil;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.order.month.entity.TabClientEntity;
import com.stbella.order.domain.repository.ClientRepository;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.domain.repository.OrderRefundRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.manager.SmsManager;
import com.stbella.order.server.order.month.request.approval.ApprovalStatusInfo;
import com.stbella.order.server.order.month.service.OrderPayV2Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class OldDepositRefundApprovalStrateg implements ApprovalStrategy {

    @Lazy
    @Resource
    private OrderPayV2Service orderPayV2Service;
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @Resource
    private SmsManager smsManager;

    @Resource
    private ClientRepository clientRepository;

    @Override
    public void handleApproval(OrderApproveRecordTypeEnum type, String params, String messageId, ApprovalStatusInfo approvalStatusInfo, String processId) {

        DepositRefundApprovalRequest depositRefundApprovalRequest = JSONObject.parseObject(params, DepositRefundApprovalRequest.class);

        if (Objects.isNull(depositRefundApprovalRequest) || Objects.isNull(depositRefundApprovalRequest.getOrderRefundId())) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "退款记录Id不能为空");
        }
        HeOrderRefundEntity orderRefundEntity = orderRefundRepository.getOneById(depositRefundApprovalRequest.getOrderRefundId());
        if (ObjectUtil.isEmpty(orderRefundEntity)) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "钉钉审批完成退款失败,找不到对应的退款记录,退款ID=" + depositRefundApprovalRequest.getOrderRefundId());
        }
        boolean isSendRefund = false;
        boolean refuse = approvalStatusInfo.isRefuse();
        boolean finish = approvalStatusInfo.isFinish();
        boolean terminate = approvalStatusInfo.isTerminate();
        boolean agree = approvalStatusInfo.isAgree();
        Integer refundAmount = AmountChangeUtil.changeY2FFoInt(depositRefundApprovalRequest.getAmountRefundApplied());
        String originatorPhone = depositRefundApprovalRequest.getPhone();
        if (finish && agree) {
            isSendRefund = true;
            orderRefundEntity.setActualAmount(refundAmount);
            orderRefundEntity.setAgreeAt(System.currentTimeMillis() / 1000);
            orderRefundEntity.setFinishAt(orderRefundEntity.getAgreeAt());
            orderRefundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_3.getCode());
            try {
                log.info("审批通过发送短信通知（意向金）originatorPhone:{},refundMethod:{}" + originatorPhone, orderRefundEntity.getRefundMethod());
                smsManager.sendMessage(originatorPhone, null, null, SmsTemplateV2Enum.ORDER1_REFUND_SUCCESS, new String[]{depositRefundApprovalRequest.getCustomerName(), "退款至意向金", String.valueOf(AmountChangeUtil.f2YScale2(orderRefundEntity.getApplyAmount()))});
            } catch (Exception e) {
                log.error("审批通过发送短信通知（意向金）异常：{}，手机号：{}，内容：{}", e.getMessage(), originatorPhone, new String[]{depositRefundApprovalRequest.getCustomerName(), "退款至意向金", String.valueOf(AmountChangeUtil.f2YScale2(orderRefundEntity.getApplyAmount()))});
            }
        }
        if (terminate) {
            orderRefundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_2.getCode());
        }
        if (finish && refuse) {
            orderRefundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_2.getCode());
            try {
                smsManager.sendMessage(originatorPhone, null, null, SmsTemplateV2Enum.ORDER1_REFUND_REFUSE, new String[]{depositRefundApprovalRequest.getCustomerName()});
            }catch (Exception e){
                log.error("审批拒绝发送短信-发生异常msg:{}, refundOrderSn:{}", e.getMessage(), orderRefundEntity.getRefundOrderSn());
            }
        }
        HeIncomeRecordEntity heIncomeRecordEntity = incomeRecordRepository.getRecordByIncomeSn(orderRefundEntity.getIncomeSn());
        heIncomeRecordEntity.setFreezeAmount(heIncomeRecordEntity.getFreezeAmount() - refundAmount);
        if (isSendRefund) {
            try {
                heIncomeRecordEntity.setAlreadyRefundAmount(Objects.isNull(heIncomeRecordEntity.getAlreadyRefundAmount()) ? orderRefundEntity.getActualAmount() : heIncomeRecordEntity.getAlreadyRefundAmount() + orderRefundEntity.getActualAmount());
                TabClientEntity tabClientById = clientRepository.getTabClientById(heIncomeRecordEntity.getClientUid());
                List<Long> refundBalance = orderPayV2Service.refundDepositBalance(orderRefundEntity.getId(), tabClientById.getBasicUid(), orderRefundEntity.getRefundOrderSn());
                orderRefundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode());
                orderRefundEntity.setPaymentResult(JSONUtil.toJsonStr(refundBalance));
                try {
                    smsManager.sendMessage(originatorPhone, SmsTemplateV2Enum.ORDER1_REFUND_BALANCE_CREATOR, new String[]{orderRefundEntity.getRefundOrderSn(), depositRefundApprovalRequest.getAmountRefundApplied().toString(), depositRefundApprovalRequest.getCustomerName()});
                    smsManager.sendMessage(depositRefundApprovalRequest.getCustomerPhone(), SmsTemplateV2Enum.ORDER1_REFUND_BALANCE_CUSTOMER, new String[]{orderRefundEntity.getRefundOrderSn(), depositRefundApprovalRequest.getAmountRefundApplied().toString()});
                }catch (Exception e){
                    log.error("退款成功向客户发送通知短信-发生异常msg:{}, refundOrderSn:{}", e.getMessage(), orderRefundEntity.getRefundOrderSn());
                }
            } catch (Exception e){
                log.error("押金发起退款-至意向金账户发生异常msg:{}", e.getMessage());
                orderRefundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_5.getCode());
            }
        }
        //释放支付记录冻结金额
        incomeRecordRepository.updateRecord(heIncomeRecordEntity);
        orderRefundRepository.updateOneById(orderRefundEntity);
    }

    @Override
    public OrderApproveRecordTypeEnum getOrderApproveRecordTypeEnum() {
        return OrderApproveRecordTypeEnum.APPROVAL_OLD_DEPOSIT_REFUND;
    }
}

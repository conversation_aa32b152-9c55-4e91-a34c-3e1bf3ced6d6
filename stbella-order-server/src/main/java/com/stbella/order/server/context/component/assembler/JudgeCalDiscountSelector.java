package com.stbella.order.server.context.component.assembler;

import com.stbella.core.enums.BusinessEnum;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.FlowIdentity;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

/**
 * 判断是合同修改审批还是折扣审批
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "合同修改审批组件")
public class JudgeCalDiscountSelector implements IExecutableAtom<FlowContext> {


    @Override
    public void run(FlowContext bizContext) {
        OrderReductionEntity orderReductionEntity = bizContext.getAttribute(OrderReductionEntity.class);
        //如果合同修改审批通过，不发起折扣审批
        if (Boolean.TRUE.equals(orderReductionEntity.getModifyContractApprove())) {
            return;
        }
        FlowIdentity identity = FlowIdentity.builder()
                .bizActivity(BizActivityEnum.DECREASES.code())
                .idSlice(BusinessEnum.CARE_CENTER.name())
                .idSlice("decrease_discount_assembler")
                .delimiter(":")
                .build();
        SnowballFlowLauncher.fire(identity, bizContext);
    }
}

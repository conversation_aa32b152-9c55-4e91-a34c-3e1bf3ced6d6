package com.stbella.order.server.strategy.pay;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.server.order.month.enums.PayRecordEnum;
import com.stbella.order.server.order.month.request.pay.PayNotityRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 支付通知处理器
 * @date 2024/3/15 16:03
 */
@Component
@Slf4j
public class PayNotificationStrategyFactory {

    private final Map<PayRecordEnum, PayNotificationStrategy> payStrategyMap = new HashMap<>();

    @Autowired
    public PayNotificationStrategyFactory(List<PayNotificationStrategy> strategies) {
        for (PayNotificationStrategy strategy : strategies) {
            // 获取策略对应的枚举值
            PayRecordEnum payRecordEnum = strategy.getPayRecordEnum();
            payStrategyMap.put(payRecordEnum, strategy);
        }
    }

    public boolean processPayment(PayRecordEnum accountTypeByLocalTransactionalNo, PayNotityRequest payNotityRequest) {
        PayNotificationStrategy strategy = payStrategyMap.get(accountTypeByLocalTransactionalNo);
        if (ObjectUtil.isNull(strategy)) {
            throw new BusinessException(ResultEnum.NOT_EXIST, "无法找到对应支付通知处理策略");
        }
        return strategy.handlePayNotification(payNotityRequest);
    }
}

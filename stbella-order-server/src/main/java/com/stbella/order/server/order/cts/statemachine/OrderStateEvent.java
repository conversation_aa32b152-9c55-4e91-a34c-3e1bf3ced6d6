package com.stbella.order.server.order.cts.statemachine;

import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.server.order.cts.constant.CtsOrderConstant;
import com.stbella.order.server.order.month.enums.OrderTypeEnum;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * OrderStateEvent 订单事件
 *
 * <AUTHOR>
 * @date 2022-03-10 11:42
 * @sine 1.0.0
 */
public enum OrderStateEvent {

    SERVE_FEE_EVENT_CREATE(0, CtsOrderConstant.EVENT_TYPE_SERVE_FEE, "创建"),
    SERVE_FEE_EVENT_CANCEL(1, CtsOrderConstant.EVENT_TYPE_SERVE_FEE, "取消"),
    SERVE_FEE_EVENT_PAY_SUCCESS(2, CtsOrderConstant.EVENT_TYPE_SERVE_FEE, "付款完成"),
    SERVE_FEE_EVENT_APPLY_FOR_REFUND(3, CtsOrderConstant.EVENT_TYPE_SERVE_FEE, "申请退款"),
    SERVE_FEE_EVENT_REFUND_END(4, CtsOrderConstant.EVENT_TYPE_SERVE_FEE, "退款结束"),

    COURSE_EVENT_CREATE(10, CtsOrderConstant.EVENT_TYPE_COURSE, "创建"),
    COURSE_EVENT_CANCEL(11, CtsOrderConstant.EVENT_TYPE_COURSE, "取消"),
    COURSE_EVENT_PAY_SUCCESS(12, CtsOrderConstant.EVENT_TYPE_COURSE, "付款完成"),
    COURSE_EVENT_APPLY_FOR_REFUND(13, CtsOrderConstant.EVENT_TYPE_COURSE, "申请退款"),
    COURSE_EVENT_REFUND_END(14, CtsOrderConstant.EVENT_TYPE_COURSE, "退款结束"),

    MARGIN_EVENT_CREATE(20, CtsOrderConstant.EVENT_TYPE_MARGIN, "创建"),
    MARGIN_EVENT_CANCEL(21, CtsOrderConstant.EVENT_TYPE_MARGIN, "取消"),
    MARGIN_EVENT_PAY_SUCCESS(22, CtsOrderConstant.EVENT_TYPE_MARGIN, "付款完成"),
    MARGIN_EVENT_APPLY_FOR_REFUND(23, CtsOrderConstant.EVENT_TYPE_MARGIN, "申请退款"),
    MARGIN_EVENT_REFUND_END(24, CtsOrderConstant.EVENT_TYPE_MARGIN, "退款结束"),

    AUNT_SITTER_EVENT_CREATE(30, CtsOrderConstant.EVENT_TYPE_AUNT_SITTER, "创建"),
    AUNT_SITTER_EVENT_CANCEL(31, CtsOrderConstant.EVENT_TYPE_AUNT_SITTER, "取消"),
    AUNT_SITTER_EVENT_PAY_SUCCESS(32, CtsOrderConstant.EVENT_TYPE_AUNT_SITTER, "付款完成"),
    AUNT_SITTER_EVENT_APPLY_FOR_REFUND(33, CtsOrderConstant.EVENT_TYPE_AUNT_SITTER, "申请退款"),
    AUNT_SITTER_EVENT_REFUND_END(34, CtsOrderConstant.EVENT_TYPE_AUNT_SITTER, "退款结束"),

    BABY_SITTER_EVENT_CREATE(40, CtsOrderConstant.EVENT_TYPE_BABY_SITTER, "创建"),
    BABY_SITTER_EVENT_CANCEL(41, CtsOrderConstant.EVENT_TYPE_BABY_SITTER, "取消"),
    BABY_SITTER_EVENT_PAY_SUCCESS(42, CtsOrderConstant.EVENT_TYPE_BABY_SITTER, "付款完成"),
    BABY_SITTER_EVENT_APPLY_FOR_REFUND(43, CtsOrderConstant.EVENT_TYPE_BABY_SITTER, "申请退款"),
    BABY_SITTER_EVENT_REFUND_END(44, CtsOrderConstant.EVENT_TYPE_BABY_SITTER, "退款结束"),

    GENERAL_SITTER_EVENT_CREATE(50, CtsOrderConstant.EVENT_TYPE_GENERAL_SITTER, "创建"),
    GENERAL_SITTER_EVENT_CANCEL(51, CtsOrderConstant.EVENT_TYPE_GENERAL_SITTER, "取消"),
    GENERAL_SITTER_EVENT_PAY_SUCCESS(52, CtsOrderConstant.EVENT_TYPE_GENERAL_SITTER, "付款完成"),
    GENERAL_SITTER_EVENT_APPLY_FOR_REFUND(53, CtsOrderConstant.EVENT_TYPE_GENERAL_SITTER, "申请退款"),
    GENERAL_SITTER_EVENT_REFUND_END(54, CtsOrderConstant.EVENT_TYPE_GENERAL_SITTER, "退款结束"),


    GENERAL_CUSTOMER_EVENT_CREATE(60, CtsOrderConstant.EVENT_TYPE_GENERAL_CUSTOMER, "创建"),
    GENERAL_CUSTOMER_EVENT_CANCEL(61, CtsOrderConstant.EVENT_TYPE_GENERAL_CUSTOMER, "取消"),
    GENERAL_CUSTOMER_EVENT_PAY_SUCCESS(62, CtsOrderConstant.EVENT_TYPE_GENERAL_CUSTOMER, "付款完成"),
    GENERAL_CUSTOMER_EVENT_APPLY_FOR_REFUND(63, CtsOrderConstant.EVENT_TYPE_GENERAL_CUSTOMER, "申请退款"),
    GENERAL_CUSTOMER_EVENT_REFUND_END(64, CtsOrderConstant.EVENT_TYPE_GENERAL_CUSTOMER, "退款结束"),
    ;

    private final static Map<Integer, OrderStateEvent> ORDER_REFUND_END_MAPPING = new HashMap<Integer, OrderStateEvent>() {{
        put(OrderTypeEnum.BABY_SITTER.getCode(), OrderStateEvent.BABY_SITTER_EVENT_REFUND_END);
        put(OrderTypeEnum.AUNT_SITTER.getCode(), OrderStateEvent.AUNT_SITTER_EVENT_REFUND_END);
        put(OrderTypeEnum.COURSE.getCode(), OrderStateEvent.COURSE_EVENT_REFUND_END);
        put(OrderTypeEnum.MARGIN.getCode(), OrderStateEvent.MARGIN_EVENT_REFUND_END);
        put(OrderTypeEnum.SERVE_FEE.getCode(), OrderStateEvent.SERVE_FEE_EVENT_REFUND_END);
        put(OrderTypeEnum.SITTER_GENERAL.getCode(), OrderStateEvent.GENERAL_SITTER_EVENT_REFUND_END);
        put(OrderTypeEnum.CUSTOMER_GENERAL.getCode(), OrderStateEvent.GENERAL_CUSTOMER_EVENT_REFUND_END);
    }};

    private final static Map<Integer, OrderStateEvent> ORDER_REFUND_MAPPING = new HashMap<Integer, OrderStateEvent>() {{
        put(OrderTypeEnum.BABY_SITTER.getCode(), OrderStateEvent.BABY_SITTER_EVENT_APPLY_FOR_REFUND);
        put(OrderTypeEnum.AUNT_SITTER.getCode(), OrderStateEvent.AUNT_SITTER_EVENT_APPLY_FOR_REFUND);
        put(OrderTypeEnum.COURSE.getCode(), OrderStateEvent.COURSE_EVENT_APPLY_FOR_REFUND);
        put(OrderTypeEnum.MARGIN.getCode(), OrderStateEvent.MARGIN_EVENT_APPLY_FOR_REFUND);
        put(OrderTypeEnum.SERVE_FEE.getCode(), OrderStateEvent.SERVE_FEE_EVENT_APPLY_FOR_REFUND);
        put(OrderTypeEnum.SITTER_GENERAL.getCode(), OrderStateEvent.GENERAL_SITTER_EVENT_APPLY_FOR_REFUND);
        put(OrderTypeEnum.CUSTOMER_GENERAL.getCode(), OrderStateEvent.GENERAL_CUSTOMER_EVENT_APPLY_FOR_REFUND);
    }};

    private final static Map<Integer, OrderStateEvent> ORDER_PAY_NOTIFY_MAPPING = new HashMap<Integer, OrderStateEvent>() {{
        put(OrderTypeEnum.BABY_SITTER.getCode(), OrderStateEvent.BABY_SITTER_EVENT_PAY_SUCCESS);
        put(OrderTypeEnum.AUNT_SITTER.getCode(), OrderStateEvent.AUNT_SITTER_EVENT_PAY_SUCCESS);
        put(OrderTypeEnum.COURSE.getCode(), OrderStateEvent.COURSE_EVENT_PAY_SUCCESS);
        put(OrderTypeEnum.MARGIN.getCode(), OrderStateEvent.MARGIN_EVENT_PAY_SUCCESS);
        put(OrderTypeEnum.SERVE_FEE.getCode(), OrderStateEvent.SERVE_FEE_EVENT_PAY_SUCCESS);
        put(OrderTypeEnum.SITTER_GENERAL.getCode(), OrderStateEvent.GENERAL_SITTER_EVENT_PAY_SUCCESS);
        put(OrderTypeEnum.CUSTOMER_GENERAL.getCode(), OrderStateEvent.GENERAL_CUSTOMER_EVENT_PAY_SUCCESS);
    }};

    private final static Map<Integer, OrderStateEvent> ORDER_CANCEL_MAPPING = new HashMap<Integer, OrderStateEvent>() {{
        put(OrderTypeEnum.BABY_SITTER.getCode(), OrderStateEvent.BABY_SITTER_EVENT_CANCEL);
        put(OrderTypeEnum.AUNT_SITTER.getCode(), OrderStateEvent.AUNT_SITTER_EVENT_CANCEL);
        put(OrderTypeEnum.COURSE.getCode(), OrderStateEvent.COURSE_EVENT_CANCEL);
        put(OrderTypeEnum.MARGIN.getCode(), OrderStateEvent.MARGIN_EVENT_CANCEL);
        put(OrderTypeEnum.SERVE_FEE.getCode(), OrderStateEvent.SERVE_FEE_EVENT_CANCEL);
        put(OrderTypeEnum.SITTER_GENERAL.getCode(), OrderStateEvent.GENERAL_CUSTOMER_EVENT_CANCEL);
        put(OrderTypeEnum.CUSTOMER_GENERAL.getCode(), OrderStateEvent.GENERAL_CUSTOMER_EVENT_CANCEL);
    }};

    private final int code;
    private final int source;
    private final String desc;

    OrderStateEvent(int code, int source, String desc) {
        this.code = code;
        this.source = source;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public int getSource() {
        return source;
    }

    public String getDesc() {
        return desc;
    }

    public static OrderStateEvent getRefundEndEventByOrderType(Integer orderType) {
        final OrderStateEvent orderStateEvent = ORDER_REFUND_END_MAPPING.get(orderType);
        if (Objects.isNull(orderStateEvent)) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR);
        }
        return orderStateEvent;
    }

    public static OrderStateEvent getRefundEventByOrderType(Integer orderType) {
        final OrderStateEvent orderStateEvent = ORDER_REFUND_MAPPING.get(orderType);
        if (Objects.isNull(orderStateEvent)) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR);
        }
        return orderStateEvent;
    }

    public static OrderStateEvent getPayNotifyEventByOrderType(Integer orderType) {
        final OrderStateEvent orderStateEvent = ORDER_PAY_NOTIFY_MAPPING.get(orderType);
        if (Objects.isNull(orderStateEvent)) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR);
        }
        return orderStateEvent;
    }

    public static OrderStateEvent getCancelEventByOrderType(Integer orderType) {
        final OrderStateEvent orderStateEvent = ORDER_CANCEL_MAPPING.get(orderType);
        if (Objects.isNull(orderStateEvent)) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR);
        }
        return orderStateEvent;
    }

    @Override
    public String toString() {
        return "OrderStateEvent{" +
                "code=" + code +
                ", source=" + source +
                ", desc='" + desc + '\'' +
                '}';
    }

    public static OrderStateEvent valueOfCode(int code) {
        for (OrderStateEvent value : getValues()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }

    public static String descOfCode(int code) {
        for (OrderStateEvent value : getValues()) {
            if (value.code == code) {
                return value.desc;
            }
        }
        return "";
    }

    private static OrderStateEvent[] getValues() {
        return OrderStateEvent.values();
    }
}

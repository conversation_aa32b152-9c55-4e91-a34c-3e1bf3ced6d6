package com.stbella.order.server.context.component.processor.pay;

import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.domain.utils.PayTypeConvertUtil;
import com.stbella.order.server.fact.OrderFact;
import com.stbella.order.server.manager.PayRuleComponent;
import com.stbella.order.server.order.month.req.PayReqV2;
import com.stbella.order.server.order.month.request.pay.PayNotityRequest;
import com.stbella.pay.server.pay.service.res.PayAccountVo;
import com.stbella.rule.api.req.ExecuteRuleReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-05-29  15:20
 * @Description: 支付完成回调
 */
@Slf4j
@Component
@SnowballComponent(name = "支付完成", desc = "支付完成回调")
public class SucceedOrderTransactionProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private IncomeRecordRepository incomeRecordRepository;

    @Override
    public void run(FlowContext bizContext) {

        PayNotityRequest payNotityRequest = bizContext.getAttribute(PayNotityRequest.class);

        String outTradeNo = payNotityRequest.getOutTradeNo();
        HeIncomeRecordEntity heIncomeRecordEntity = incomeRecordRepository.getRecordByIncomeSn(outTradeNo);
        heIncomeRecordEntity.setParams(payNotityRequest.getRequestBody());
        heIncomeRecordEntity.setStatus(payNotityRequest.getPayStatus());
        heIncomeRecordEntity.setPayTime(DateUtils.getTenBitTimestamp(payNotityRequest.getPayTime()));

        //线下enum 乱了，线下场景这里就不处理了。
        Integer payType = payNotityRequest.getPayType();
        if (payType == null || OmniPayTypeEnum.OFFLINE.getCode().intValue() != payType){
            heIncomeRecordEntity.setPayType(payType);
            //线下不用存金额
        }else {
            heIncomeRecordEntity.setIncome(payNotityRequest.getPayAmount().multiply(new BigDecimal(100)).intValue());
        }

        heIncomeRecordEntity.setTransactionId(payNotityRequest.getTransactionalNo());
        heIncomeRecordEntity.setUpdatedAt(DateUtils.getTenBitTimestamp(new Date()).longValue());
        incomeRecordRepository.updateRecord(heIncomeRecordEntity);

        bizContext.setAttribute(HeIncomeRecordEntity.class, heIncomeRecordEntity);
    }


}

package com.stbella.order.server.order.job;

import cn.hutool.core.lang.Assert;
import com.google.common.collect.Lists;
import com.stbella.asset.api.enums.TradeType;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.server.manager.AssetManager;
import com.stbella.order.server.manager.OssManager;
import com.stbella.order.server.order.month.component.IncomeTotalAssembler;
import com.stbella.order.server.utils.DateUtils;
import com.stbella.platform.order.api.res.InnerRankRes;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import com.stbella.sso.server.dingding.entity.DdEmployeePO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 系统分发礼赠金处理器
 */
@Slf4j
@Component
public class SystemDistributeGiftGoldHandler {

    private static final String ruleParam = "configCode";
    private static final String sceneCodeSystem = "systemConfig";
    private static final String sceneCodeArea = "area_gift_coin_award";
    private static final String ruleCode = "warzone_distribution_reward_ratio_configuration_item";
    private static final String keyFlag = "warArea";

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private RuleLinkClient ruleLinkClient;

    @Resource
    private IncomeTotalAssembler incomeTotalAssembler;

    @Resource
    private AssetManager assetManager;

    @Resource
    private OssManager ossManager;

    @XxlJob("systemDistributeGiftGoldHandler")
    public ReturnT<?> systemDistributeGiftGoldHandler() {
        long start = System.currentTimeMillis();
        log.info("系统分发礼赠金任务开始处理，time:{}, param:{}", start, XxlJobHelper.getJobParam());
        String key = "Order:WarZone:Distribution:GiftGold";
        XxlJobHelper.getJobParam();
        RLock lockName = redissonClient.getLock(key);
        try {
            boolean tryLock = lockName.tryLock(0, 30, TimeUnit.MINUTES);
            Assert.isTrue(tryLock, "系统分发礼赠金任务正在处理，禁止重复提交");

            this.distributeGiftGoldExec(XxlJobHelper.getJobParam());
            log.info("系统分发礼赠金任务任务处理完成, key:{}, time:{}", key, System.currentTimeMillis() - start);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("系统分发礼赠金任务处理发生异常:msg:{}", e.getMessage());
            return ReturnT.FAIL;
        } finally {
            if (Objects.nonNull(lockName)) {
                lockName.unlock();
            }
        }
    }

    public void distributeGiftGoldExec(String jobParam){

        Date lastMonthStart = DateUtils.getLastMonthStart();
        int year = DateUtils.getYear(lastMonthStart);
        int month = DateUtils.getMonth(lastMonthStart);
        List<InnerRankRes> rankData = incomeTotalAssembler.getRankData(year, month);
        List<String> jobNumberList = Lists.newArrayList();
        rankData.forEach(item -> {
            String giftCoinManager = getGiftCoinManager(item.getStoreId());
            if (StringUtils.isNotEmpty(giftCoinManager)){
                jobNumberList.add(giftCoinManager);
                item.setJobNumber(giftCoinManager);
            }
        });
        List<DdEmployeePO> ddEmployeePOList = ossManager.queryUserByJobNumberList(jobNumberList);
        Map<String, Long> basicUidMap = ddEmployeePOList.stream().filter(item -> Objects.nonNull(item.getJobNumber()) && Objects.nonNull(item.getBasicUid())).collect(Collectors.toMap(DdEmployeePO::getJobNumber, DdEmployeePO::getBasicUid, (k1, k2) -> k1));

        BigDecimal distributeScale = this.getDistributeScale(jobParam);
        for (InnerRankRes item : rankData) {
            if (Objects.isNull(item.getJobNumber())){
                continue;
            }
            Long basicUid = basicUidMap.get(item.getJobNumber());
            if (Objects.isNull(basicUid)){
                continue;
            }
            BigDecimal giftGold = new BigDecimal(item.getTotalValue()).multiply(distributeScale).setScale(0, RoundingMode.UP);
            log.info("资产交易: uniqueId:{}, userId:{}, amount:{}, title:{}", getUniqueId(year, month, item.getStoreId(), jobParam), basicUidMap.get(item.getJobNumber()).toString(), giftGold.longValue(), String.join("-", TradeType.AWARD_GIFT_COIN.getName(), item.getName()));
            //assetManager.warAreaDistributionGiftGold(getUniqueId(year, month, item.getStoreId(), jobParam), basicUidMap.get(item.getJobNumber()).toString(), giftGold.longValue(), String.join("-", TradeType.AWARD_GIFT_COIN.getName(), item.getName()));

            assetManager.warAreaDistributionGiftGold(getUniqueId(year, month, item.getStoreId(), jobParam), "708380", giftGold.longValue(), String.join("-", TradeType.AWARD_GIFT_COIN.getName(), item.getName()));

        }
    }

    private static String getUniqueId(Integer year, Integer month, Integer warArea, String jobParam){
        if (StringUtils.isEmpty(jobParam)){
            return keyFlag + year.toString() + month.toString() +warArea.toString();
        }
        log.info("执行的规则参数：jobParam:{}", jobParam);
        return keyFlag + year.toString() + month.toString() +warArea.toString() + DateUtils.getDay(new Date());
    }

    private BigDecimal getDistributeScale(String jobParam){

        if (StringUtils.isNotEmpty(jobParam)){
            // 正则表达式匹配小数，整数，负小数，负整数
            String regex = "^-?\\d+(\\.\\d+)?$";
            Assert.isTrue(jobParam.matches(regex), "参数格式错误，必须为小数或整数，如：0.1 或 -0.1 或 1 或 -1");
            return new BigDecimal(jobParam);
        }
        Map<String, String> factMap = new HashMap<>();
        factMap.put(ruleParam, ruleCode);
        ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req(sceneCodeSystem, factMap);
        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
        Assert.isTrue(Objects.nonNull(hitRuleVo) && StringUtils.isNotEmpty(hitRuleVo.getSimpleRuleValue()));
        return new BigDecimal(hitRuleVo.getSimpleRuleValue());
    }

    private String getGiftCoinManager(Integer storeId){

        Map<String, Object> factMap = new HashMap<>(1);
        factMap.put(keyFlag, storeId);
        ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req(sceneCodeArea, factMap);
        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
        if (Objects.isNull(hitRuleVo)){
            return null;
        }
        return hitRuleVo.getSimpleRuleValue();
    }


}

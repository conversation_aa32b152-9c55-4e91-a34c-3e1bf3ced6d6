package com.stbella.order.server.context.component.checker;

import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.core.OrderProcessTypeEnum;
import com.stbella.order.common.enums.month.OrderStatusV2Enum;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.platform.order.api.req.CheckOrderProcessReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 订单流程检查上下文
 */
@Slf4j
@Component
public class OrderProcessCheckerContext {

    @Resource
    List<OrderProcessChecker> processCheckers;

    public Result check(HeOrderEntity order, OrderProcessTypeEnum processTypeEnum) {

        if (CompareUtil.integerEqual(OrderStatusV2Enum.CLOSE.getCode(), order.getOrderStatus())) {
            throw new BusinessException(ErrorCodeEnum.ORDER_CLOSE.code() + "", ErrorCodeEnum.ORDER_CLOSE.desc());
        }

        CheckOrderProcessReq req = new CheckOrderProcessReq();
        req.setOrderId(order.getOrderId());
        req.setProcessType(processTypeEnum.code());

        Optional<OrderProcessChecker> first = processCheckers.stream().filter(checker -> checker.filter(req)).collect(Collectors.toList()).stream().findFirst();
        first.ifPresent(orderProcessChecker -> orderProcessChecker.run(order));
        return Result.success();
    }
}

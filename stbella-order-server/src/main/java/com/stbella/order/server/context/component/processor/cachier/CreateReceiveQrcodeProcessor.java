package com.stbella.order.server.context.component.processor.cachier;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.result.Result;
import com.stbella.order.common.constant.FlowContextConstant;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OrderProcessTypeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.infrastructure.gateway.QrcodeGateway;
import com.stbella.order.infrastructure.gateway.req.QrcodeConfig;
import com.stbella.order.server.context.component.checker.OrderProcessCheckerContext;
import com.stbella.order.server.order.month.req.MonthDingReq;
import com.stbella.order.server.order.month.req.OrderProductionDiscountApprovalReq;
import com.stbella.order.server.order.month.request.standard.QrCodeRequest;
import com.stbella.order.server.order.month.service.MonthDingService;
import com.stbella.pay.server.alipay.enums.AccountTypeEnum;
import com.stbella.platform.order.api.req.CheckOrderProcessReq;
import com.stbella.platform.order.api.res.ReceiveQrcodeRes;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @Author: JJ
 * @Description: 创建订单收款码
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "创建订单收款码")
public class CreateReceiveQrcodeProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    QrcodeGateway qrcodeGateway;

    @Resource
    OrderProcessCheckerContext orderProcessCheckerContext;

    @Value("${qrCode.month-url-v2}")
    private String monthUrlV2;

    @Value("${qrCode.width}")
    private Integer width;

    @Value("${qrCode.height}")
    private Integer height;

    @Value("${qrCode.margin}")
    private Integer margin;

    @Override
    public void run(FlowContext bizContext) {

        QrCodeRequest request = bizContext.getAttribute(QrCodeRequest.class);
        HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);

        Result check = orderProcessCheckerContext.check(order, OrderProcessTypeEnum.PAY);

        String redirectUrl = "";
        if (ObjectUtil.isEmpty(request.getAccountType()) || request.getAccountType().equals(AccountTypeEnum.BEI_KANG.getCode())) {
            redirectUrl = monthUrlV2;
        }
        Map<String, Object> stringObjectMap = BeanUtil.beanToMap(request);
        stringObjectMap.put("v", order.getOldOrNew());
        stringObjectMap.put("t", System.currentTimeMillis());

        String content = String.format("%s?%s", redirectUrl, MapUtil.join(stringObjectMap, "&", "="));
        log.info("createQrCodeNV2参数url{}", content);

        QrcodeConfig config = QrcodeConfig.builder().width(width)
                .height(height)
                .margin(margin)
                .content(content).build();
        String qrcodeUrl = qrcodeGateway.createDefaultQrcode(config);

        ReceiveQrcodeRes qrcodeRes = ReceiveQrcodeRes.builder().url(qrcodeUrl).build();

        bizContext.setAttribute(ReceiveQrcodeRes.class, qrcodeRes);
    }

}

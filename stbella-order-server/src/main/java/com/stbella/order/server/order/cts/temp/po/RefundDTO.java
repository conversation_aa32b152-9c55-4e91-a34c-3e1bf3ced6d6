package com.stbella.order.server.order.cts.temp.po;

import lombok.Data;

@Data
public class RefundDTO {

    private String orderNumber; // 订单编号
    private String orderType; // 订单类型
    private String region; // 区域
    private String sellerName; // 销售名字
    private String employerName; // 雇主名字
    private String nannyName; // 育婴师名字
    private String refundTime; // 实际退款时间
    private String refundAmount; // 退款金额
    private String refundMethod; // 退款方式
    private String attribute; // 属性
    private String firstPaymentTime; // 订单首次支付时间

    public RefundDTO(String orderNumber, String orderType, String region, String sellerName, String employerName, String nannyName, String refundTime, String refundAmount, String refundMethod, String attribute, String firstPaymentTime) {
        this.orderNumber = orderNumber;
        this.orderType = orderType;
        this.region = region;
        this.sellerName = sellerName;
        this.employerName = employerName;
        this.nannyName = nannyName;
        this.refundTime = refundTime;
        this.refundAmount = refundAmount;
        this.refundMethod = refundMethod;
        this.attribute = attribute;
        this.firstPaymentTime = firstPaymentTime;
    }

    public RefundDTO() {
    }
}

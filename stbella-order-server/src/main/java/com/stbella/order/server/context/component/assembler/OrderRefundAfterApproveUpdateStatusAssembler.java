package com.stbella.order.server.context.component.assembler;

import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.core.OmniRefundApproveEnum;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.repository.OrderRefundRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 审批通过的时候，把审批状态改成退款中
 */
@Component
@Slf4j
@SnowballComponent(name = "审批结束，修改退款状态", desc = "审批结束，修改退款状态")
public class OrderRefundAfterApproveUpdateStatusAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderRefundRepository orderRefundRepository;

    @Override
    public boolean condition(FlowContext context) {
        Boolean continueBoolean = (Boolean) context.getAttribute(OtherConstant.CONTINUE);
        Boolean isRefundSuccess = (Boolean) context.getAttribute(OtherConstant.IS_REFUND);
        return continueBoolean && isRefundSuccess;
    }

    @Override
    public void run(FlowContext bizContext) {
        //主退款
        HeOrderRefundEntity refundEntity = bizContext.getAttribute(HeOrderRefundEntity.class);
        //获取子退款
        List<HeOrderRefundEntity> refundByParentSn = bizContext.getListAttribute(HeOrderRefundEntity.class);

        updateRefundStatus(refundByParentSn);
        updateRefundStatus(Collections.singletonList(refundEntity));

        //更新状态
        updateRefundStatus(refundByParentSn);
        log.info("主退款refundSn为 {} 退款状态改成退款中", refundEntity.getRefundOrderSn());
    }

    private void updateRefundStatus(List<HeOrderRefundEntity> allRefund) {
        for (HeOrderRefundEntity orderRefundEntity : allRefund) {
            orderRefundEntity.setAgreeAt(System.currentTimeMillis() / 1000);
            orderRefundEntity.setStatus(OmniRefundApproveEnum.REFUND_RECORD_3.getCode());
        }
        orderRefundRepository.batchUpdateById(allRefund);
    }
}

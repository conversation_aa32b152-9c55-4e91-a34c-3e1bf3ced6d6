package com.stbella.order.server.context.component.validator;

import com.stbella.month.server.request.RefundApprovalRequest;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.month.RefundRecordPayStatusEnum;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.repository.OrderRefundRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

@Slf4j
@Component
@RefreshScope
@SnowballComponent(name = "退款审批成功后校验")
public class OrderRefundAfterApproveValidator implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderRefundRepository orderRefundRepository;

    /**
     * 退款审批成功后校验
     *
     * @param bizContext
     */
    @Override
    public void run(FlowContext bizContext) {
        log.info("退款审批后校验...");
        //审批发起
        RefundApprovalRequest refundApprovalOriginalRoadRequest = bizContext.getAttribute(RefundApprovalRequest.class);
        //获取审批状态
        HeOrderRefundEntity heOrderRefundEntity = orderRefundRepository.getOneById(refundApprovalOriginalRoadRequest.getOrderRefundId());
        Integer status = heOrderRefundEntity.getStatus();
        if (status.equals(RefundRecordPayStatusEnum.REFUND_RECORD_1.getCode())) {
            return;
        }
        log.info("{} 审批状态不在审批中，状态为{} ", refundApprovalOriginalRoadRequest.getOrderRefundId(), status);
        //流程结束，接下来的流程不会走
        bizContext.setAttribute(OtherConstant.CONTINUE, false);
    }

    @Override
    public boolean condition(FlowContext context) {
        return (boolean) context.getAttribute(OtherConstant.CONTINUE);
    }
}

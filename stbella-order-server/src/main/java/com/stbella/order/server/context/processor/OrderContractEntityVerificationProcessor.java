package com.stbella.order.server.context.processor;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.contract.model.req.ContractQueryV4;
import com.stbella.customer.server.ecp.entity.TabClientPO;
import com.stbella.order.common.enums.month.ContractStatusEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.manager.ContractManager;
import com.stbella.order.server.manager.StoreManager;
import com.stbella.order.server.manager.TabClientManager;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单合同主体验证处理器
 * 用于判断订单的合同主体是否需要变更为杭州贝康科技有限公司
 */
@Component
@Slf4j
public class OrderContractEntityVerificationProcessor {

    /**
     * 杭州贝康科技有限公司
     */
    private static final String TARGET_COMPANY_NAME = "杭州贝康健康科技集团有限公司";

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private ContractManager contractManager;

    @Resource
    private TabClientManager tabClientManager;

    /**
     * 处理订单合同主体验证
     *
     * @param orderId 订单ID
     * @return 处理结果
     */
    public boolean needGenerateEntityChangeAgreement(Integer orderId) {
        log.info("开始处理订单合同主体验证, orderId: {}", orderId);

        // 1. 根据orderId查询basicUid
        Integer basicUid = queryBasicUidByOrderId(orderId);
        if (basicUid == null) {
            log.warn("根据订单ID未找到basicUid, orderId: {}", orderId);
            return false;
        }

        // 2. 根据basicUid查找当前用户所有的月子订单
        List<HeOrderEntity> monthOrders = queryMonthOrdersByBasicUid(basicUid);
        if (CollectionUtil.isEmpty(monthOrders)) {
            log.warn("用户没有月子订单, basicUid: {}", basicUid);
            return false;
        }

        // 3. 根据basicUid查询客户信息，然后查询该用户最近签署成功的一份主合同
        OrderContractSignRecordVO latestMainContract = queryLatestSuccessMainContract(basicUid);
        if (latestMainContract == null) {
            log.warn("用户没有签署成功的主合同, basicUid: {}", basicUid);
            return false;
        }

        // 4. 判断该合同的主体是否是杭州贝康科技有限公司
        boolean isTargetCompany = isTargetCompanyContract(latestMainContract);

        // 5. 如果不是该公司，则给这份订单生成一个主体变更协议
        if (!isTargetCompany) {
            //TODO 这里需要判断是否已经生成了补充协议，如果生成了且已经签署了，就不用管了，如果未生成或者未签署，则返回true
            log.info("合同主体不是{}，需要生成主体变更协议, orderId: {}, contractId: {}",
                    TARGET_COMPANY_NAME, orderId, latestMainContract.getId());
            return true;
        }

        log.info("合同主体已经是{}, 无需处理, orderId: {}, contractId: {}",
                TARGET_COMPANY_NAME, orderId, latestMainContract.getId());

        return false;

    }

    /**
     * 根据订单ID查询basicUid
     */
    private Integer queryBasicUidByOrderId(Integer orderId) {
        HeOrderEntity order = orderRepository.queryOrderById(orderId);
        return order != null ? order.getBasicUid() : null;
    }

    /**
     * 根据basicUid查询所有月子订单
     */
    private List<HeOrderEntity> queryMonthOrdersByBasicUid(Integer basicUid) {
        // 月子订单类型：0-普通月子套餐订单，1-小月子订单
        List<Integer> monthOrderTypes = Arrays.asList(0, 1);
        return orderRepository.getInfoByByBasicUid(Arrays.asList(basicUid), monthOrderTypes);
    }

    /**
     * 查询最近签署成功的主合同
     * 使用项目中现有的方式通过ContractManager查询
     */
    private OrderContractSignRecordVO queryLatestSuccessMainContract(Integer basicUid) {
        try {
            // 1. 通过basicUid查询客户信息
            List<TabClientPO> tabClientPOS = tabClientManager.listByBasicUid(basicUid);
            log.info("查询客户信息：{}", JSONObject.toJSONString(tabClientPOS));

            if (CollectionUtil.isEmpty(tabClientPOS)) {
                log.warn("根据basicUid未找到客户信息, basicUid: {}", basicUid);
                return null;
            }

            // 2. 构建合同查询条件
            ContractQueryV4 contractQueryV4 = new ContractQueryV4();
            contractQueryV4.setContractStatus(Collections.singletonList(ContractStatusEnum.SIGNED.code()));
            contractQueryV4.setClientIdList(tabClientPOS.stream()
                    .map(tabClientPO -> tabClientPO.getId().intValue())
                    .collect(Collectors.toList()));

            // 3. 分别查询圣贝拉和小贝拉的主合同模板
            List<OrderContractSignRecordVO> allContracts = contractManager.queryByV4(contractQueryV4);


            if (CollectionUtil.isEmpty(allContracts)) {
                log.warn("未找到已签署的主合同, basicUid: {}, clientIds: {}",
                        basicUid, contractQueryV4.getClientIdList());
                return null;
            }

            // 4. 按签署时间排序，取最新的一份
            return allContracts.stream()
                    .filter(contract -> contract.getSignTime() != null)
                    .max(Comparator.comparing(OrderContractSignRecordVO::getSignTime))
                    .orElse(allContracts.get(0));

        } catch (Exception e) {
            log.error("查询最近签署成功的主合同异常, basicUid: {}", basicUid, e);
            return null;
        }
    }


    /**
     * 判断合同主体是否是目标公司
     * 通过合同关联的门店信息来判断合同主体
     */
    private boolean isTargetCompanyContract(OrderContractSignRecordVO contract) {
        try {
            String actualEntityName = "";
            //TODO ,从合同里面取

            return TARGET_COMPANY_NAME.equals(actualEntityName);

        } catch (Exception e) {
            log.error("判断合同主体异常, contractId: {}", contract.getId(), e);
            return false;
        }
    }


}
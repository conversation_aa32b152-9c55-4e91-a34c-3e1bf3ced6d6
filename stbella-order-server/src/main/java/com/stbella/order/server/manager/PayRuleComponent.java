package com.stbella.order.server.manager;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.exception.ApplicationException;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.server.fact.OrderFact;
import com.stbella.order.server.order.cts.entity.OrderCtsPO;
import com.stbella.order.server.order.cts.service.OrderCtsService;
import com.stbella.order.server.order.month.enums.PayRecordEnum;
import com.stbella.order.server.order.month.enums.CtsPayTypeEnum;
import com.stbella.order.server.order.month.req.PayDepositReq;
import com.stbella.order.server.order.month.req.PayReqV2;
import com.stbella.order.server.order.month.request.pay.PayRequest;
import com.stbella.pay.server.pay.service.PayAccountQueryService;
import com.stbella.pay.server.pay.service.req.PayAccountQuery;
import com.stbella.pay.server.pay.service.res.PayAccountVo;
import com.stbella.rule.api.req.ExecuteRuleReq;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Author: jijunjian
 * @CreateTime: 2023-04-20  17:19
 * @Description: 支付规则组件
 */
@Component
@Slf4j
public class PayRuleComponent {

    @Resource
    RuleLinkClient ruleLinkClient;
    @Resource
    private OrderCtsService orderCtsService;
    @DubboReference(timeout = 30000)
    private PayAccountQueryService payAccountQueryService;


    /**
     * 根据事实查询支付规则，返回第一个支付账号
     * 下游根据这个判断走哪个支付方式
     * @param payRequest
     * @return
     */
    public PayAccountVo queryPayRule(PayRequest payRequest){
        log.info("根据事实查询支付规则，{}", JSONUtil.toJsonStr(payRequest));
        //支付规则场景固定1
        ExecuteRuleReq req = new ExecuteRuleReq();
        req.setSceneCode(1);

        final OrderCtsPO orderCtsPO = orderCtsService.getByOrderNo(payRequest.getOrderNo());
        if (Objects.isNull(orderCtsPO)) {
            throw new BusinessException(ResultEnum.NOT_EXIST);
        }

        /**
         * 支付环境（当前在的环境）这个与母婴保持一致（母婴与予家是反的）
         * 1：微信
         * 2：支付宝
         */
        Integer payEvn= 0;
        if (CtsPayTypeEnum.ZFB.getCode().equals(payRequest.getPayType())) {
            payEvn = 2;
        } else {
            payEvn = 1;
        }
        OrderFact orderFact = new OrderFact();
        orderFact.setBu(PayRecordEnum.CTS.getCode());
        orderFact.setOrderType(orderCtsPO.getOrderType());
        orderFact.setPayEvn(payEvn);
        orderFact.setCtsSiteId(orderCtsPO.getCtsSiteId());
        orderFact.setOrderSn(payRequest.getOrderNo());
        req.setFactObj(orderFact);
        PayAccountVo payAccountVo = getPayAccountVo(req);
        return payAccountVo;

    }

    /**
     * 获取母婴
     * 根据事实查询支付规则，返回第一个支付账号
     * 下游根据这个判断走哪个支付方式
     *
     * @param payRequest
     * @return
     */
    public PayAccountVo queryBkPayRule(PayReqV2 payRequest) {
        log.info("根据事实查询支付规则，{}", JSONUtil.toJsonStr(payRequest));
        //支付规则场景固定1
        ExecuteRuleReq req = new ExecuteRuleReq();
        req.setSceneCode(1);

        /**
         * 支付环境（当前在的环境）这个与母婴保持一致（母婴与予家是反的）
         * 1：微信
         * 2：支付宝
         */
        Integer payEvn = payRequest.getPayType();
        OrderFact orderFact = new OrderFact();
        orderFact.setStoreId(payRequest.getStoreId());
        orderFact.setBu(PayRecordEnum.BK.getCode());
        orderFact.setPayEvn(payEvn);

        req.setFactObj(orderFact);
        PayAccountVo payAccountVo = getPayAccountVo(req);
        return payAccountVo;

    }

    /**
     * 获取押金
     * 根据事实查询支付规则，返回第一个支付账号
     * 下游根据这个判断走哪个支付方式
     *
     * @param payRequest
     * @return
     */
    public PayAccountVo queryDepositPayRule(PayDepositReq payRequest) {
        log.info("根据事实查询支付规则，{}", JSONUtil.toJsonStr(payRequest));
        //支付规则场景固定1
        ExecuteRuleReq req = new ExecuteRuleReq();
        req.setSceneCode(1);

        /**
         * 支付环境（当前在的环境）这个与母婴保持一致（母婴与予家是反的）
         * 1：微信
         * 2：支付宝
         */
        Integer payEvn = payRequest.getPayType();
        OrderFact orderFact = new OrderFact();
        //押金以前都走总部,现在要求走各自门店  2024.4.16修改
        orderFact.setStoreId(payRequest.getStoreId());
        orderFact.setBu(PayRecordEnum.BK.getCode());
        orderFact.setPayEvn(payEvn);

        req.setFactObj(orderFact);
        PayAccountVo payAccountVo = getPayAccountVo(req);
        return payAccountVo;

    }


    public PayAccountVo getPayAccountVo(ExecuteRuleReq req) {

        ExecuteRuleV2Req executeRuleV2Req = BeanMapper.map(req, ExecuteRuleV2Req.class);
        executeRuleV2Req.setSceneCode(req.getSceneCode()+"");
        executeRuleV2Req.setFactObj(req.getFactObj());

        HitRuleVo ruleResult = ruleLinkClient.hitOneRule(executeRuleV2Req);
        if (Objects.isNull(ruleResult)) {
            log.error("未查到支付规则，请检查配置，{}", JSONUtil.toJsonStr(req));
            throw new ApplicationException(ErrorCodeEnum.PAY_RULE_NOT_CONFIG.code(), "未查到支付规则，请检查配置");
        }

        // 根据返回的 simpleRuleValue，查询对应的支付渠道，然后调用支付接口
        PayAccountQuery payAccountQuery = new PayAccountQuery();
        payAccountQuery.setPayAccountIds(Lists.newArrayList(Long.parseLong(ruleResult.getSimpleRuleValue())));
        Result<List<PayAccountVo>> accountResult = payAccountQueryService.queryPayAccountList(payAccountQuery);
        if (CollectionUtils.isEmpty(accountResult.getData())) {
            log.error("未查询支付账号，请检查配置，{}", JSONUtil.toJsonStr(payAccountQuery));
            throw new ApplicationException(ErrorCodeEnum.PAY_RULE_NOT_CONFIG.code(), "未查到支付规则，请检查配置");
        }

        PayAccountVo payAccountVo = accountResult.getData().get(0);
        log.info("根据事实查询支付规则，{}", JSONUtil.toJsonStr(payAccountVo));
        return payAccountVo;
    }

}

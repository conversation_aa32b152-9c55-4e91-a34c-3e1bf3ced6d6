package com.stbella.order.server.context.component.validator;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.enums.core.CartSceneEnum;
import com.stbella.order.server.utils.OrderProcessCheckUtil;
import com.stbella.platform.order.api.cart.CartQueryService;
import com.stbella.platform.order.api.req.CreateOrderReq;
import com.stbella.platform.order.api.req.QueryCartReq;
import com.stbella.platform.order.api.res.CartRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

@Component
@Slf4j
@SnowballComponent(name = "升级订单校验", desc = "升级订单校验")
public class UpgradeOrderValidator implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderProcessCheckUtil orderProcessCheckUtil;
    @Resource
    private CartQueryService cartQueryService;

    @Override
    public void run(FlowContext bizContext) {

        CreateOrderReq createOrderReq = bizContext.getAttribute(CreateOrderReq.class);

        Integer cartId = createOrderReq.getCartId();

        if (ObjectUtil.isEmpty(cartId)) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "升级订单必须传入购物车ID");
        }

        QueryCartReq req = new QueryCartReq();
        req.setCartId(cartId);
        req.setScene(createOrderReq.getScene());
        Result<CartRes> cartResResult = cartQueryService.queryCart(req);
        CartRes data = cartResResult.getData();
        if (ObjectUtil.isEmpty(data)) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "购物车不存在");
        }

        Integer originalOrderId = data.getOriginalOrderId();
        boolean hasUnfinishedRefund = orderProcessCheckUtil.hasUnfinishedRefund(originalOrderId);
        log.info("originalOrderId={},hasUnfinishedRefund={}", originalOrderId, hasUnfinishedRefund);
        if (hasUnfinishedRefund) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "原订单有退款审批中流程，请等待审批完成后再次尝试！");
        }
    }

    @Override
    public boolean condition(FlowContext context) {
        CreateOrderReq createOrderReq = context.getAttribute(CreateOrderReq.class);
        return CartSceneEnum.UPGRADE_ORDER.getCode().equals(createOrderReq.getScene());
    }
}

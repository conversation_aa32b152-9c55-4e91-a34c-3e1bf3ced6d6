package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.repository.OrderRefundRepository;
import com.stbella.platform.order.api.refund.api.OrderRefundService;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
@SnowballComponent(name = "处理income表冻结+退款主表+退款子表", desc = "处理income表冻结+退款主表+退款子表")
public class OrderRefundCreateProcessAchievementAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private OrderRefundService orderRefundService;

    @Override
    public void run(FlowContext bizContext) {
        //主退款
        HeOrderRefundEntity parentRefund = bizContext.getAttribute(HeOrderRefundEntity.class);
        CreateRefundReq attribute = bizContext.getAttribute(CreateRefundReq.class);
        if (ObjectUtil.isNotEmpty(attribute.getComplaintId())) {
            log.info("客诉退款不记录退款业绩");
            return;
        }
        List<HeOrderRefundEntity> refundEntityList = (List<HeOrderRefundEntity>) bizContext.getAttribute(OtherConstant.ORDER_REFUND_CHILD);
        //设置退款业绩
        updateRefundAchievement(parentRefund, refundEntityList);

    }

    private void updateRefundAchievement(HeOrderRefundEntity parentRefund, List<HeOrderRefundEntity> refundEntityList) {
        Integer refundAchievement = orderRefundService.calAchievement(parentRefund.getId()).getData();

        parentRefund.setRefundAchievement(refundAchievement);
        List<HeOrderRefundEntity> orderRefundEntities = refundEntityList.stream().filter(o -> !OmniPayTypeEnum.REDUCTION.getCode().equals(o.getRefundType()) && !OmniPayTypeEnum.PRODUCTION_COIN.getCode().equals(o.getRefundType())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(orderRefundEntities)) {
            HeOrderRefundEntity heOrderRefundEntity = orderRefundEntities.get(0);
            heOrderRefundEntity.setRefundAchievement(refundAchievement);
            orderRefundRepository.updateOneById(heOrderRefundEntity);
        }
        orderRefundRepository.updateOneById(parentRefund);

    }


    @Override
    public boolean condition(FlowContext context) {
        return (boolean) context.getAttribute(OtherConstant.CONTINUE);
    }
}

package com.stbella.order.server.context.service.cashier;

import com.stbella.core.result.Result;
import com.stbella.order.server.context.component.settlement.OrderSettlementAssembler;
import com.stbella.order.server.order.month.request.standard.OrderSettlementQuery;
import com.stbella.order.server.order.month.res.OrderSettlementVO;
import com.stbella.platform.order.api.cashier.CashierQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.core.context.FlowContext;

import javax.annotation.Resource;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-08-02  17:03
 * @Description: 结算信息查询
 */
@Slf4j
@Service
@DubboService
public class CashierQueryServiceImpl implements CashierQueryService {

    @Resource
    OrderSettlementAssembler orderSettlementAssembler;

    /**
     * 查询订单收款情况
     *
     * @param settlementQuery
     * @return
     */
    @Override
    public Result<OrderSettlementVO> querySettlement(OrderSettlementQuery settlementQuery) {

        FlowContext context = new FlowContext();
        context.setAttribute(OrderSettlementQuery.class, settlementQuery);
        SnowballFlowLauncher.fire(context, orderSettlementAssembler);
        OrderSettlementVO settlementVO = context.getAttribute(OrderSettlementVO.class);

        return Result.success(settlementVO);

    }
}

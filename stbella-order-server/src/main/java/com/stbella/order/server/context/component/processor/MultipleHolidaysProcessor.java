package com.stbella.order.server.context.component.processor;

import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.repository.StoreRepository;
import com.stbella.order.server.order.StoreFinanceConfigFact;
import com.stbella.order.server.order.month.req.MultipleHolidaysReq;
import com.stbella.order.server.order.month.res.StoreFinanceVO;
import com.stbella.rule.api.req.ExecuteRuleV2Req;

import com.stbella.rule.api.res.HitRuleVo;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

/**
 * @ProjectName stbella-order
 * @PackageName com.stbella.order.server.context.component.processor
 * @ClassName MultipleHolidaysProcessor
 * @Description 多胞胎费用组件
 * <AUTHOR>
 * @CreateDate 2024-04-11 16:01
 * @Version 1.0
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "多胞胎节假日费用处理器")
public class MultipleHolidaysProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    RuleLinkClient ruleLinkClient;

    @Resource
    private StoreRepository storeRepository;

    @Override
    public void run(FlowContext bizContext) {
        MultipleHolidaysReq multipleHolidaysReq = bizContext.getResultAttribute(MultipleHolidaysReq.class);

        log.info("多胞胎节假日费用处理器 ==={}", JSONUtil.toJsonStr(multipleHolidaysReq));
        //region 规则
        ExecuteRuleV2Req req = new ExecuteRuleV2Req(BizConstant.RuleLinkScene.Store_Finance_Config);
        StoreFinanceConfigFact fact = new StoreFinanceConfigFact();
        CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(multipleHolidaysReq.getStoreId());

        if(ObjectUtil.isEmpty(cfgStoreEntity)){
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(),"门店信息不存在");
        }
        fact.setBu(multipleHolidaysReq.getBu());
        fact.setStoreId(multipleHolidaysReq.getStoreId());
        fact.setStoreType(cfgStoreEntity.getType());
        fact.setChildStoreType(cfgStoreEntity.getChildType());

        req.setFactObj(fact);

        HitRuleVo vo = ruleLinkClient.hitOneRuleWithException(req);

        StoreFinanceVO storeFinanceVO =  JSONUtil.toBean(vo.getSimpleRuleValue(), StoreFinanceVO.class);

        bizContext.addResultAttribute(StoreFinanceVO.class, storeFinanceVO);
    }
}
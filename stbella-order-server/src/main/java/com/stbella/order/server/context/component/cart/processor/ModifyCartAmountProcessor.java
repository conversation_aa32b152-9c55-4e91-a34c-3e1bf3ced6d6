package com.stbella.order.server.context.component.cart.processor;

import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.entity.HeCartEntity;
import com.stbella.order.domain.repository.HeCartRepository;
import com.stbella.order.server.order.OrderPrice;
import com.stbella.platform.order.api.req.QueryCartReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-05-29  15:20
 * @Description: 更新购物车金额
 */
@Component
@Slf4j
@SnowballComponent(name = "更新购物车金额", desc = "")
public class ModifyCartAmountProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private HeCartRepository heCartRepository;

    @Override
    public void run(FlowContext bizContext) {
        HeCartEntity cartEntity = bizContext.getAttribute(HeCartEntity.class);
        OrderPrice orderPrice = bizContext.getAttribute(OrderPrice.class);
        Integer orderAmount = AmountChangeUtil.changeY2FFoInt(orderPrice.getTotalAmount());
        Integer goodsAmount = AmountChangeUtil.changeY2FFoInt(orderPrice.getGoodsAmount());
        Integer payAmount = AmountChangeUtil.changeY2FFoInt(orderPrice.getPayAmount());
        if (Objects.nonNull(cartEntity.getCartId())) {
            //商品未变更，折扣金额不需要变化
            HeCartEntity heCartEntity = heCartRepository.queryOne(QueryCartReq.builder().cartId(cartEntity.getCartId()).build());
            if (Objects.nonNull(heCartEntity) && Objects.nonNull(heCartEntity.getOrderAmount()) && Objects.nonNull(heCartEntity.getTotalAmount())) {
                if (heCartEntity.getOrderAmount().equals(orderAmount) && heCartEntity.getTotalAmount().equals(goodsAmount) && Objects.nonNull(heCartEntity.getPayAmount())) {
                    payAmount = heCartEntity.getPayAmount();
                }
            }
        }
        // 创建购物车时，签单金额与订单金额一致
        cartEntity.modifyAmount(orderAmount, goodsAmount, payAmount);

    }
}

package com.stbella.order.server.context.component.ui;

import com.stbella.order.common.enums.month.OrderButtonEnum;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.server.order.month.res.OrderOperateButton;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-08-12  16:28
 * @Description: 查看支付记录
 */
@Component
@Slf4j
public class ViewSettlementOperate implements OrderOperate{


    @Override
    public void setPriority(int priority) {

    }

    @Override
    public int getPriority() {
        return 8;
    }

    /**
     * 生成订单操作按钮
     *
     * @param order
     * @return
     */
    @Override
    public OrderOperateButton generateButton(OrderOperateContext context) {
        // 收款记录
        List<HeIncomeRecordEntity> incomeList = context.incomeList;
        if (CollectionUtils.isNotEmpty(incomeList)) {
            return OrderOperateButton.builder().code(getButtonEnum().getCode()).value(getButtonEnum().getValue()).build();
        }
        return null;
    }

    /**
     * 获取按钮枚举
     *
     * @return
     */
    @Override
    public OrderButtonEnum getButtonEnum() {
        return OrderButtonEnum.COLLECTION_RECORD;
    }

    @Override
    public int compareTo(@NotNull IPriority o) {
        return this.getPriority() - o.getPriority();
    }
}

package com.stbella.order.server.context.component.export;

import cn.hutool.json.JSONUtil;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.domain.order.production.OrderProductionCardExtendEntity;
import com.stbella.order.domain.repository.OrderProductionCardExtendRepository;
import com.stbella.order.server.order.OrderProductionCardContentItermDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: ji<PERSON><PERSON><PERSON>
 * @CreateTime: 2023-08-24  16:12
 * @Description: 产康通卡初始化器
 */
@Slf4j
@Component
public class CardResetProcessor {

    @Resource
    private OrderProductionCardExtendRepository cardExtendRepository;

    /**
     * 产康通卡初始化
     * @param cardDataList
     * @return
     */
    public Integer run(List<ProductionOrderExportDTO> cardDataList){

        // datalist 按 orderSn 分组
        Map<String, List<ProductionOrderExportDTO>> orderSnMap = cardDataList.stream().collect(Collectors.groupingBy(ProductionOrderExportDTO::getOrderSn));

        // 遍历 orderSnMap
        for (Map.Entry<String, List<ProductionOrderExportDTO>> entry : orderSnMap.entrySet()) {
            String orderSn = entry.getKey();
            List<ProductionOrderExportDTO> cardList = entry.getValue();
            resetOneOrder(orderSn, cardList);
        }


        return null;
    }

    /**
     * 重置一个订单的产康通卡
     * @param orderSn
     * @param cardList
     */
    protected void resetOneOrder(String orderSn, List<ProductionOrderExportDTO> cardList){

        List<OrderProductionCardExtendEntity> orderProductionCardExtendEntities = cardExtendRepository.queryProductionOrderCardByOrderSn(orderSn);

        // 按groupId 分组, 生成每个组合的商品明细
        Map<Integer, List<ProductionOrderExportDTO>> groupIdMap = cardList.stream().filter(a -> Objects.nonNull(a.getGroupId()) && a.getGroupId() > 0).collect(Collectors.groupingBy(ProductionOrderExportDTO::getGroupId));
        // 遍历 orderSnMap
        for (Map.Entry<Integer, List<ProductionOrderExportDTO>> entry : groupIdMap.entrySet()) {
            Integer groupId = entry.getKey();
            List<ProductionOrderExportDTO> cardListByGroup = entry.getValue();

            Optional<OrderProductionCardExtendEntity> first = orderProductionCardExtendEntities.stream().filter(a -> a.getGroupId().equals(groupId)).findFirst();
            if (!first.isPresent()){
                log.error("订单{}的组合{}不存在", orderSn, groupId);
                continue;
            }
            OrderProductionCardExtendEntity cardExtend = first.get();
            if (CompareUtil.integerEqual(1, cardExtend.getReset())){
                log.info("订单{}-{}已经重置，忽略", orderSn, groupId);
                continue;
            }

            resetOneGroup(orderSn, first.get(), cardListByGroup);
        }
    }

    /**
     * 重围单个组合
     * @description:
     * @author: jijunjian
     * @date: 8/24/23 17:18
     * @param:
     * @return:
     **/
    protected void resetOneGroup(String orderSn, OrderProductionCardExtendEntity cardExtendEntity, List<ProductionOrderExportDTO> goodsList){

        log.info("开始处理订单{}-分组{}", orderSn, cardExtendEntity.getGroupId());
        // 通过 goodsList 重置 cardExtendEntity.content;
        List<OrderProductionCardContentItermDTO> itermDTOS = JSONUtil.toList(cardExtendEntity.getContent(), OrderProductionCardContentItermDTO.class);
        // itermDTOS 按 goods_name+/+sku_name  生成map
        Map<String, OrderProductionCardContentItermDTO> itermDTOMap = new HashMap<>();
        itermDTOS.forEach(iterm->{
            itermDTOMap.put(iterm.getGoods_name()+"/"+iterm.getSku_name(), iterm);
        });


        // 原则上 goodsList < itermDTOS. 根据goodsList 找到聚合的iterm，最后生成json

        List<OrderProductionCardContentItermDTO> newItermDTOS = new ArrayList<>();
        goodsList.forEach(goods->{

            // getProductionChildName =  getProductionName+/+skuName;
            OrderProductionCardContentItermDTO tempIterm = itermDTOMap.get(goods.getProductionChildName());
            if (Objects.nonNull(tempIterm)){
                int serviceCount = goods.getTotalCheckoutNum() + Integer.parseInt(goods.getResidueNum());
                if (serviceCount > 0){
                    tempIterm.setService_count(serviceCount);
                    newItermDTOS.add(tempIterm);
                }else {
                    log.warn("订单{}-分组{}-商品{}-服务次数为0", orderSn, cardExtendEntity.getGroupId(), goods.getChildSkuId());
                }

            }
        });
        if (newItermDTOS.size() > 0){
            String newContent = JSONUtil.toJsonStr(newItermDTOS);
            // 更新cardExtendEntity newContent + reset
            OrderProductionCardExtendEntity updateEntity = new OrderProductionCardExtendEntity();
            updateEntity.setId(cardExtendEntity.getId());
            updateEntity.setReset(1);
            updateEntity.setContent(newContent);
            cardExtendRepository.updateByPrimaryKeySelective(updateEntity);
            log.info("更新完成-订单{}-分组{}", orderSn, cardExtendEntity.getGroupId());

        }else {
            log.warn("无明细-订单{}-分组{}", orderSn, cardExtendEntity.getGroupId());
        }
    }
}

package com.stbella.order.server.context.component.contract.stage.handle;


import cn.hutool.core.date.DateTime;
import com.stbella.contract.model.req.OrderParamHistoryChangeValuePushDTO;
import com.stbella.core.utils.BigDecimalUtil;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.domain.order.service.OrderParamHistoryService;
import com.stbella.order.domain.repository.OrderReductionRepository;
import com.stbella.order.server.context.component.contract.stage.template.AgreementParamsFillTemplate;
import com.stbella.order.server.contract.req.OrderParamHistoryPushDTO;
import com.stbella.order.server.contract.req.OrderParamHistoryValuePushDTO;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

@Slf4j
public abstract class AgreementParamsFillAbstractService implements AgreementParamsFillTemplate {

    @Resource
    private OrderReductionRepository orderReductionRepository;

    @Resource
    OrderParamHistoryService orderParamHistoryService;

    public Long getFetchTotalReductionAmount(OrderContractSignRecordVO currentSignRecord, Long fetchTotalReductionAmount){

        if (Objects.nonNull(currentSignRecord) && Objects.nonNull(currentSignRecord.getId())) {
            OrderReductionEntity orderReductionEntity = orderReductionRepository.selectByContractId(currentSignRecord.getId());
            OrderReductionEntity firstOrderReductionEntity = orderReductionRepository.selectFirstReductionEntity(orderReductionEntity.getOrderId());
            fetchTotalReductionAmount = firstOrderReductionEntity.getBeforeAmount() - orderReductionEntity.getAfterAmount();
        }
        return fetchTotalReductionAmount;
    }

    public HashMap<String, String> generateTemplateData(OrderContractSignRecordVO signRecord, List<OrderParamHistoryChangeValuePushDTO> orderParamHistoryChangeValuePushDTOS, HeOrderEntity orderEntity) {

        //所有文字段落的数组,这里将每段先放到数组中,全部生成完成后再拼接,切割分页
        List<String> textList = new ArrayList<>();

        String signTimeStr3 = "";
        //如果没有关联合同,也没有修改协议  则直接抛出异常,不需要生成补充协议
        //不是-1说明修改内容中涉及合同  生成合同相关描述
        DateTime dateTime = new DateTime(signRecord.getUpdatedAt() * 1000);
        int year = dateTime.year();
        int month = dateTime.monthBaseOne();
        int day = dateTime.dayOfMonth();
        String contractNo = signRecord.getContractNo();
        String firsText = "一、鉴于，甲、乙双方于【" + year + "年" + month + "月" + day + "日】签订了编号为【" + contractNo + "】的《服务合同》，现经双方友好协商，就调整原合同部分内容的相关事项，签署补充协议如下：\n";
        textList.add(firsText);
        signTimeStr3 = dateTime.toDateStr();
        //如果map有数据说明有参数变更了
        //索引,每次数据变更都+1
        Integer index = 0;
        for (OrderParamHistoryChangeValuePushDTO orderParamHistoryChangeValuePushDTO : orderParamHistoryChangeValuePushDTOS) {
            //合同名称
            String oldValue = orderParamHistoryChangeValuePushDTO.getOldValue();
            String value = orderParamHistoryChangeValuePushDTO.getNewValue();
            String paramName = orderParamHistoryChangeValuePushDTO.getName() + ": ";
            index++;
            String contentText = index + ".双方协商一致同意就《服务合同》中【" + orderParamHistoryChangeValuePushDTO.getParam1() + "】的" + orderParamHistoryChangeValuePushDTO.getParam2() + "项进行调整，《服务合同》【" + paramName + oldValue + "】变更为【" + paramName + value + "】。\n";
            if (StringUtils.isEmpty(orderParamHistoryChangeValuePushDTO.getParam1())){
                contentText = index + ".双方协商一致同意就《服务合同》中的" + orderParamHistoryChangeValuePushDTO.getParam2() + "项进行调整，《服务合同》【" + paramName + oldValue + "】变更为【" + paramName + value + "】。\n";
            }
            textList.add(contentText);
        }

        String template5 = "二、除本协议明确所作修改、变更及补充的内容外，原合同的其他条款均继续有效。本协议签订生效后，即成为原合同不可分割的部分，与原合同具有同等法律效力。\n";
        String template6 = "三、原合同与本协议约定不一致的，以本协议约定为准；本协议尚未约定或约定不明的，以原合同约定的为准。\n";
        String template7 = "四、本协议于双方法定代表人或授权代表签字（或盖章）并加盖公章或合同专用章后生效，本协议一式两份，甲方持一份，乙方执一份，每份具有同等法律效力。\n";
        String template8 = "\n\n\n甲方：\n" + "法定代表人或授权代表（签章）：\n\n\n\n\n\n\n" + "乙方：\n" + "法定代表人或授权代表（签章）：\n\n" + "签订日期：" + DateTime.now().year() + "年" + DateTime.now().monthBaseOne() + "月" + DateTime.now().dayOfMonth() + "日";

        textList.add(template5);
        textList.add(template6);
        textList.add(template7);

        String positionA = "270,";
        String positionB = "270,";
        //基础高度 第一页默认430,第二页670
        int height = 380;
        //行高,计算常量
        int rowHeight = 18;
        //这里写定模版的高度行,现在模版一共四页
        List<Integer> tabHigh = new ArrayList<>();
        tabHigh.add(32);
        tabHigh.add(41);
        tabHigh.add(41);
        tabHigh.add(41);
        int tabHighIndex = 0;
        int textIndex = 0;

        // 正文 根据textStrList的数据来具体生成文本
        List<StringBuffer> textStrList = new ArrayList<>();
        textStrList.add(new StringBuffer());
        textStrList.add(new StringBuffer());
        textStrList.add(new StringBuffer());
        textStrList.add(new StringBuffer());
        int rows = 0;
        StringBuilder builder = new StringBuilder();
        int length;
        for (String s : textList) {
            length = 0;
            for (char aChar : s.toCharArray()) {
                builder.append(aChar);
                if (!"\n".equals(String.valueOf(aChar))) {
                    length += String.valueOf(aChar).getBytes(StandardCharsets.UTF_8).length > 1 ? 2 : 1;
                    if (length >= 69) {
                        length = 0;
                        builder.append("\n");
                    }
                }
            }
        }
        String[] splits = builder.toString().split("\n");
        for (String split : splits) {
            if (StringUtils.hasText(split)) {
                StringBuffer stringBuffer = textStrList.get(textIndex);
                stringBuffer.append(split).append("\n");
                rows++;
                if (tabHigh.get(tabHighIndex) == rows) {
                    rows = 0;
                    textIndex++;
                    tabHighIndex++;
                    height = 560;
                }
            }
        }
        if (tabHigh.get(tabHighIndex) < (rows + 15)) {
            rows = 15;
            textIndex++;
            height = 560;
        } else {
            rows += 15;
        }
        textStrList.get(textIndex).append(template8);

        height -= (rows * rowHeight);

        HashMap<String, String> contentData = new HashMap<>();
        //合同信息,这个是为了拿到甲方,乙方等信息
        OrderParamHistoryPushDTO orderParamHistoryPushDTO = orderParamHistoryService.buildOrderParamHistory(orderEntity);
        List<OrderParamHistoryValuePushDTO> templateData = orderParamHistoryPushDTO.getParamList();
        for (OrderParamHistoryValuePushDTO orderParamHistory : templateData) {
            contentData.put(orderParamHistory.getName(), orderParamHistory.getValue());
        }
        /*
          计算签章位置
         */
        //pageNum :签章位置页码 position_a:甲方盖章坐标,x,y格式 position_b 乙方盖章坐标,x,y格式
        int pageNum = textIndex + 1;
        positionA = positionA + (height + 400);
        positionB = positionB + (height + 280);
        for (int i = 1; i < 5; i++) {
            //给每页的文本域填写对应的数值
            contentData.put("tab_" + i, textStrList.get(i - 1).toString());
        }
        contentData.put("pageNum", String.valueOf(pageNum));
        contentData.put("positionA", positionA);
        contentData.put("positionB", positionB);
        contentData.put("sign_time_str3", signTimeStr3);

        return contentData;
    }

    public void addBalancePayment(HeOrderEntity order, Long fetchTotalReductionAmount, List<OrderParamHistoryChangeValuePushDTO> changeValuePushDTOS) {
        Integer calPayable = order.getPayAmount() - fetchTotalReductionAmount.intValue();
        BigDecimal newDecreaseAmount = BigDecimalUtil.divide(AmountChangeUtil.changeF2Y(calPayable), new BigDecimal(2));
        BigDecimal oldDecreaseAmount = BigDecimalUtil.divide(AmountChangeUtil.changeF2Y(order.getPayAmount()), new BigDecimal(2));
        addChangeDTO("尾款", oldDecreaseAmount, newDecreaseAmount, changeValuePushDTOS, "尾款大写");
    }

    public void addReservation(HeOrderEntity order, Long fetchTotalReductionAmount, List<OrderParamHistoryChangeValuePushDTO> changeValuePushDTOS) {
        Integer calPayable = order.getPayAmount() - fetchTotalReductionAmount.intValue();
        BigDecimal newDecreaseAmount = BigDecimalUtil.divide(AmountChangeUtil.changeF2Y(calPayable), new BigDecimal(2));
        BigDecimal oldDecreaseAmount = BigDecimalUtil.divide(AmountChangeUtil.changeF2Y(order.getPayAmount()), new BigDecimal(2));

        addChangeDTO("预定金", oldDecreaseAmount, newDecreaseAmount, changeValuePushDTOS, "预定金大写");
    }

    public void addDiscount(HeOrderEntity order, Long fetchTotalReductionAmount, List<OrderParamHistoryChangeValuePushDTO> changeValuePushDTOS) {
        BigDecimal newDecreaseAmount = AmountChangeUtil.changeF2Y(order.fetchFirstDiscountAmount() + fetchTotalReductionAmount);
        BigDecimal oldDecreaseAmount = AmountChangeUtil.changeF2Y(order.fetchFirstDiscountAmount());

        addChangeDTO("折扣减免", oldDecreaseAmount, newDecreaseAmount, changeValuePushDTOS, "折扣减免大写");


    }

    public static void addPayAmount(HeOrderEntity order, Long fetchTotalReductionAmount, List<OrderParamHistoryChangeValuePushDTO> changeValuePushDTOS) {
        Integer calPayable = order.getPayAmount() - fetchTotalReductionAmount.intValue();
        BigDecimal newPayAmount = AmountChangeUtil.changeF2Y(calPayable);
        BigDecimal payAmount = AmountChangeUtil.changeF2Y(order.getPayAmount());
        addChangeDTO("签单金额", payAmount, newPayAmount, changeValuePushDTOS, "签单金额大写");
    }

    private static void addChangeDTO(String name1, BigDecimal oldDecreaseAmount, BigDecimal newDecreaseAmount, List<OrderParamHistoryChangeValuePushDTO> changeValuePushDTOS, String name2) {
        OrderParamHistoryChangeValuePushDTO orderParamHistoryChangeValuePushDTO = new OrderParamHistoryChangeValuePushDTO();
        orderParamHistoryChangeValuePushDTO.setName(name1);
        orderParamHistoryChangeValuePushDTO.setOldValue(RMBUtils.formatToseparaDecimals(oldDecreaseAmount));
        orderParamHistoryChangeValuePushDTO.setNewValue(RMBUtils.formatToseparaDecimals(newDecreaseAmount));
        changeValuePushDTOS.add(orderParamHistoryChangeValuePushDTO);


        OrderParamHistoryChangeValuePushDTO chineseShow = new OrderParamHistoryChangeValuePushDTO();
        chineseShow.setName(name2);
        chineseShow.setOldValue(RMBUtils.numToRMBStr(oldDecreaseAmount.doubleValue()));
        chineseShow.setNewValue(RMBUtils.numToRMBStr(newDecreaseAmount.doubleValue()));
        changeValuePushDTOS.add(chineseShow);
    }

}

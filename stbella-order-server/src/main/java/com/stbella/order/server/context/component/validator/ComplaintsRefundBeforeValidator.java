package com.stbella.order.server.context.component.validator;

import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

@Slf4j
@Component
@RefreshScope
@SnowballComponent(name = "客诉闭环退款校验")
public class ComplaintsRefundBeforeValidator implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderRepository orderRepository;

    @Override
    public void run(FlowContext bizContext) {
        HeCustomerComplaintsEntity customerComplaintsEntity = bizContext.getAttribute(HeCustomerComplaintsEntity.class);
        HeOrderEntity orderEntity = orderRepository.getByOrderId(customerComplaintsEntity.getOrderId().intValue());
        bizContext.setAttribute(HeOrderEntity.class, orderEntity);
    }


    @Override
    public boolean condition(FlowContext context) {
        return (boolean) context.getAttribute(OtherConstant.CONTINUE);
    }
}

package com.stbella.order.server.context.component.spi;

import cn.hutool.json.JSONObject;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.server.context.component.processor.pay.OrderPayProcessor;
import top.primecare.snowball.extpoint.annotation.FuncPointSpi;
import top.primecare.snowball.extpoint.definition.IFuncSpiSupport;

/**
 * <AUTHOR>
 * 支付扩展点
 * 1，聚合支付
 * 2，余额支付等
 */
@FuncPointSpi(name = "支付扩展点", attachToComponent = OrderPayProcessor.class)
public interface PaySpi extends IFuncSpiSupport<HeIncomeRecordEntity, JSONObject> {

}

package com.stbella.order.server.context.component.temp;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.order.common.enums.core.OrderNoticeEnum;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.IncomePaidAllocationEntity;
import com.stbella.order.domain.repository.IncomePaidAllocationRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.context.component.spi.OrderBroadcastSpi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.SnowballExtensionInvoker;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: wangchuang
 * @CreateTime: 2024-05-28  13:55
 * @Description: 报单组件
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "修复分摊数据")
public class RepairSplitDataProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    IncomePaidAllocationRepository allocationRepository;

    @Override
    public boolean condition(FlowContext bizContext) {
        return true;
    }

    @Override
    public void run(FlowContext bizContext) {

        HeIncomeRecordEntity incomeRecord = bizContext.getAttribute(HeIncomeRecordEntity.class);

        List<IncomePaidAllocationEntity> allocationEntities = allocationRepository.queryListByIncomeIds(Lists.newArrayList(incomeRecord.getId()));
        //错误的分摊
        List<IncomePaidAllocationEntity> errorList = allocationEntities.stream().filter(a -> a.getPaidAmount() < 100 && a.getPaidAmount() > 0).collect(Collectors.toList());

        if (allocationEntities.size() == 1 ){
            throw new BusinessException("分摊数据异常 "+ incomeRecord.getId());
        }

        if (CollectionUtil.isEmpty(errorList)){
            log.info("无需修复分摊数据{}", incomeRecord.getId());
            return;
        }
        if (errorList.size() > 1){
            log.info("无需修复分摊数据{}", incomeRecord.getId());
            throw new BusinessException("分摊数据异常 "+ incomeRecord.getId());
        }

        // 逻辑如下： errorList 表示分摊有问题的数据，把 paidAmount 设置成0， 并加到金额最大的那个分摊上。
        Integer paidAmount = errorList.get(0).getPaidAmount();

        // 金额最大的那个分摊
        IncomePaidAllocationEntity maxAllocation = allocationEntities.stream().max((a1, a2) -> a1.getPaidAmount().compareTo(a2.getPaidAmount())).get();
        maxAllocation.setPaidAmount(maxAllocation.getPaidAmount() + paidAmount);

        IncomePaidAllocationEntity errorAllocation = errorList.get(0);
        errorAllocation.setPaidAmount(0);
        allocationRepository.updatePaidAmount(errorAllocation);

        allocationRepository.updatePaidAmount(maxAllocation);

        log.info("更新数据完成 {}", maxAllocation.getPaidAmount());

    }
}

package com.stbella.order.server.context.service;

import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.month.MonthAdditionalRevenueEnum;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.convert.OrderGoodsDomainConverter;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.production.OrderGiftExtendEntity;
import com.stbella.order.domain.order.service.OrderIncomeDomainService;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.context.component.processor.performance.OrderAmountProcessor;
import com.stbella.order.server.order.month.req.TagReq;
import com.stbella.order.server.order.month.res.TagVO;
import com.stbella.order.server.order.month.service.MonthOrderWxCommandService;
import com.stbella.order.server.utils.IdGenUtils;
import com.stbella.platform.order.api.OldOrderTransService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.primecare.snowball.flow.core.context.FlowContext;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 老订单转化成新订单
 */
@Service
@Slf4j
public class OldOrderTransServiceImpl implements OldOrderTransService {

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private OrderGoodsRepository orderGoodsRepository;

    @Resource
    private IdGenUtils idGenUtils;

    @Resource
    private MonthOrderWxCommandService monthOrderWxCommandService;

    @Resource
    private IncomeRecordRepository incomeRecordRepository;

    @Resource
    private OrderGiftExtendRepository orderGiftExtendRepository;

    @Resource
    private OrderGoodsDomainConverter orderGoodsDomainConverter;

    @Resource
    private OrderUserSnapshotRepository userSnapshotRepository;

    @Resource
    private TagsRepository tagsRepository;

    @Resource
    private OrderAdditionalRevenueRepository orderAdditionalRevenueRepository;

    @Resource
    private OrderRefundRepository orderRefundRepository;

    @Resource
    OrderIncomeDomainService incomeDomainService;


    @Resource
    OrderAmountProcessor orderAmountProcessor;


    /**
     * 1. 修改数据库，old_or_new 1 - 3
     * version  1.00 - 3.00
     * 订单标签为普通订单，
     * 后台查看商品清单报错
     * 原因 order_goods 里面gift字段为空，设置成购买  0
     * 小程序发起退单报错
     * order_goods 缺少type， 填充为 2，单件商品
     * 退单显示可退金额为0
     * 尝试先把分摊流水这些修复掉
     * 修复流水的时候发现，缺少orderGoodsSn
     * 增加逻辑
     *
     * @param orderId
     */
    @Override
    @Transactional
    public void transOldOrder2NewOrder(Integer orderId) {
        log.info("transfer order id is {}", orderId);
        HeOrderEntity orderEntity = orderRepository.getByOrderId(orderId);
        updateOrderInfo(orderEntity);
        updateUserSnapshot(orderEntity);
        updateOrderGoodsInfo(orderEntity);
        setAdditionalInfo(orderEntity);
        updateIncome(orderEntity);
        monthOrderWxCommandService.fixAllocationPayment(Collections.singletonList(orderId), true);
        //处理礼赠商品，将礼赠商品加入到普通商品中
        addOrderGiftExtend2Goods(orderEntity);
        //退款记录处理
        updateOrderRefund(orderEntity);

    }

    @Override
    public void fixAmount(Integer orderId) {
        HeOrderEntity byOrderId = orderRepository.getByOrderId(orderId);
        FlowContext bizContext = new FlowContext();
        bizContext.setAttribute(HeOrderEntity.class, byOrderId);
        orderAmountProcessor.run(bizContext);
    }

    @Override
    public void fixGoodsAmount(Boolean fixAll) {
        List<HeOrderGoodsEntity> goodsToFix = orderGoodsRepository.getGoodsWithSameOrderSnAndParentSn();

        if (CollectionUtils.isEmpty(goodsToFix)) {
            log.info("没有找到需要修复的订单商品数据");
            return;
        }

        log.info("找到{}条需要修复的订单商品数据", goodsToFix.size());

        for (HeOrderGoodsEntity goods : goodsToFix) {
            String newOrderGoodsSn = idGenUtils.genOmniSn(BizConstant.OrderAppKey.OMNI_SN_PRE_KEY, null);
            goods.setOrderGoodsSn(newOrderGoodsSn);
            log.info("更新订单商品ID: {}, 原order_goods_sn: {}, 新order_goods_sn: {}",
                    goods.getId(), goods.getParentCombineSn(), newOrderGoodsSn);
        }
        if (fixAll) {
            orderGoodsRepository.batchUpdate(goodsToFix);
            log.info("批量更新完成，共更新{}条记录", goodsToFix.size());
        }

    }

    private void updateOrderRefund(HeOrderEntity orderEntity) {
        Integer orderId = orderEntity.getOrderId();
        List<HeOrderRefundEntity> newRefund = new ArrayList<>();
        List<HeOrderRefundEntity> refundByOrderId = orderRefundRepository.getRefundByOrderId(orderId);
        if (CollectionUtils.isEmpty(refundByOrderId)) {
            return;
        }
        for (HeOrderRefundEntity heOrderRefundEntity : refundByOrderId) {
            //深拷贝 heOrderRefundEntity
            HeOrderRefundEntity newRefundEntity = new HeOrderRefundEntity();
            BeanMapper.copy(heOrderRefundEntity, newRefundEntity);
            newRefundEntity.setId(null);
            newRefundEntity.setCurrency("￥");
            newRefundEntity.setRefundNature(2);
            newRefundEntity.setParentRefundOrderSn(heOrderRefundEntity.getRefundOrderSn());
            newRefundEntity.setRefundOrderSn(IdGenUtils.createRefundTransactionalNo(null, new Date()));
            newRefund.add(newRefundEntity);

            heOrderRefundEntity.setCurrency("￥");
            heOrderRefundEntity.setRefundNature(2);
        }
        if (CollectionUtils.isNotEmpty(refundByOrderId)) {
            orderRefundRepository.saveOneList(newRefund);
        }
        orderRefundRepository.batchUpdateById(refundByOrderId);
    }

    private void setAdditionalInfo(HeOrderEntity orderEntity) {

        //加收项数据
        List<HeOrderAdditionalRevenueEntity> additionalRevenueEntityList = orderAdditionalRevenueRepository.getByOrderId(orderEntity.getOrderId());
        if (CollectionUtils.isEmpty(additionalRevenueEntityList)) {
            return;
        }
        List<HeOrderGoodsEntity> orderGoodsEntities = new ArrayList<>();
        List<HeOrderAdditionalRevenueEntity> holidayAdditional = additionalRevenueEntityList.stream().filter(a -> MonthAdditionalRevenueEnum.HOLIDAY.getCode().equals(a.getType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(holidayAdditional)) {
            //节假日
            HeOrderAdditionalRevenueEntity heOrderAdditionalRevenueEntity = holidayAdditional.get(0);
            HeOrderGoodsEntity heOrderGoodsEntity = new HeOrderGoodsEntity();
            heOrderGoodsEntity.setOrderId(orderEntity.getOrderId());
            heOrderGoodsEntity.setGoodsCost(heOrderAdditionalRevenueEntity.getCost());
            Integer days = heOrderAdditionalRevenueEntity.getDays();
            if (Objects.nonNull(days) && days > 0) {
                heOrderGoodsEntity.setGoodsPriceOrgin(heOrderAdditionalRevenueEntity.getPrice() / days);
            } else {
                heOrderGoodsEntity.setGoodsPriceOrgin(0);
            }
            heOrderGoodsEntity.setGoodsNum(heOrderAdditionalRevenueEntity.getDays());
            heOrderGoodsEntity.setGoodsName("节日费用");
            heOrderGoodsEntity.setGift(0);
            heOrderGoodsEntity.setType(2);
            heOrderGoodsEntity.setAllocationOriginPrice(0);
            heOrderGoodsEntity.setTotalAllocationOriginPrice(0);
            heOrderGoodsEntity.setAssetType("23");

            heOrderGoodsEntity.setParentId(0);
            heOrderGoodsEntity.setBasicUid(orderEntity.getBasicUid());
            heOrderGoodsEntity.setClientUid(orderEntity.getClientUid());
            heOrderGoodsEntity.setClientType(orderEntity.getClientType());
            heOrderGoodsEntity.setStaffId(orderEntity.getStaffId());
            heOrderGoodsEntity.setStoreId(orderEntity.getStoreId());
            heOrderGoodsEntity.setPayStatus(0);
            heOrderGoodsEntity.setGoodsType(23);
            heOrderGoodsEntity.setSkuId(0);
            heOrderGoodsEntity.setGoodsId(0);

            heOrderGoodsEntity.setOrderGoodsSn(idGenUtils.genOmniSn(BizConstant.OrderAppKey.OMNI_SN_PRE_KEY, null));
            orderGoodsEntities.add(heOrderGoodsEntity);
        }
        //多胎费用
        List<HeOrderAdditionalRevenueEntity> multipleBirthList = additionalRevenueEntityList.stream().filter(a -> MonthAdditionalRevenueEnum.COST_MULTIPLE_BIRTHS.getCode().equals(a.getType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(multipleBirthList)) {
            //多胞胎
            HeOrderAdditionalRevenueEntity heOrderAdditionalRevenueEntity = multipleBirthList.get(0);
            HeOrderGoodsEntity heOrderGoodsEntity = new HeOrderGoodsEntity();
            heOrderGoodsEntity.setOrderId(orderEntity.getOrderId());
            heOrderGoodsEntity.setGoodsCost(heOrderAdditionalRevenueEntity.getCost());
            Integer embryoNumber = heOrderAdditionalRevenueEntity.getEmbryoNumber();
            log.info("多胞胎胎数:{}", embryoNumber);
            heOrderGoodsEntity.setGoodsPriceOrgin(heOrderAdditionalRevenueEntity.getPrice() / embryoNumber);
            heOrderGoodsEntity.setGoodsNum(embryoNumber);
            heOrderGoodsEntity.setGoodsName("多胞胎费用");
            heOrderGoodsEntity.setOrderGoodsSn(idGenUtils.genOmniSn(BizConstant.OrderAppKey.OMNI_SN_PRE_KEY, null));
            heOrderGoodsEntity.setGift(0);
            heOrderGoodsEntity.setType(2);
            heOrderGoodsEntity.setAllocationOriginPrice(0);
            heOrderGoodsEntity.setTotalAllocationOriginPrice(0);
            heOrderGoodsEntity.setAssetType("24");

            heOrderGoodsEntity.setParentId(0);
            heOrderGoodsEntity.setBasicUid(orderEntity.getBasicUid());
            heOrderGoodsEntity.setClientUid(orderEntity.getClientUid());
            heOrderGoodsEntity.setClientType(orderEntity.getClientType());
            heOrderGoodsEntity.setStaffId(orderEntity.getStaffId());
            heOrderGoodsEntity.setStoreId(orderEntity.getStoreId());
            heOrderGoodsEntity.setPayStatus(0);
            heOrderGoodsEntity.setGoodsType(24);
            heOrderGoodsEntity.setSkuId(0);
            heOrderGoodsEntity.setGoodsId(0);

            orderGoodsEntities.add(heOrderGoodsEntity);
        }

        //续住费用
        List<HeOrderAdditionalRevenueEntity> stayCostList = additionalRevenueEntityList.stream().filter(a -> MonthAdditionalRevenueEnum.STAY_COST.getCode().equals(a.getType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(stayCostList)) {
            //续住费用
            HeOrderAdditionalRevenueEntity heOrderAdditionalRevenueEntity = stayCostList.get(0);
            HeOrderGoodsEntity heOrderGoodsEntity = new HeOrderGoodsEntity();
            heOrderGoodsEntity.setOrderId(orderEntity.getOrderId());
            heOrderGoodsEntity.setGoodsCost(heOrderAdditionalRevenueEntity.getCost());
            Integer days = heOrderAdditionalRevenueEntity.getDays();
            log.info("续住天数:{}", days);
            heOrderGoodsEntity.setGoodsPriceOrgin(heOrderAdditionalRevenueEntity.getPrice() / days);
            heOrderGoodsEntity.setGoodsNum(days);
            heOrderGoodsEntity.setGoodsName("续住费用");
            heOrderGoodsEntity.setOrderGoodsSn(idGenUtils.genOmniSn(BizConstant.OrderAppKey.OMNI_SN_PRE_KEY, null));
            heOrderGoodsEntity.setGift(0);
            heOrderGoodsEntity.setType(2);
            heOrderGoodsEntity.setAssetType("25");
            heOrderGoodsEntity.setAllocationOriginPrice(0);
            heOrderGoodsEntity.setTotalAllocationOriginPrice(0);

            heOrderGoodsEntity.setParentId(0);
            heOrderGoodsEntity.setBasicUid(orderEntity.getBasicUid());
            heOrderGoodsEntity.setClientUid(orderEntity.getClientUid());
            heOrderGoodsEntity.setClientType(orderEntity.getClientType());
            heOrderGoodsEntity.setStaffId(orderEntity.getStaffId());
            heOrderGoodsEntity.setStoreId(orderEntity.getStoreId());
            heOrderGoodsEntity.setPayStatus(0);
            heOrderGoodsEntity.setGoodsType(25);
            heOrderGoodsEntity.setSkuId(0);
            heOrderGoodsEntity.setGoodsId(0);

            orderGoodsEntities.add(heOrderGoodsEntity);
        }

        //房型变更
        List<HeOrderAdditionalRevenueEntity> roomChangesList = additionalRevenueEntityList.stream().filter(a -> MonthAdditionalRevenueEnum.ROOM_CHANGES.getCode().equals(a.getType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(roomChangesList)) {
            //房型变更
            HeOrderAdditionalRevenueEntity heOrderAdditionalRevenueEntity = roomChangesList.get(0);
            HeOrderGoodsEntity heOrderGoodsEntity = new HeOrderGoodsEntity();
            heOrderGoodsEntity.setOrderId(orderEntity.getOrderId());
            heOrderGoodsEntity.setGoodsCost(heOrderAdditionalRevenueEntity.getCost());
            Integer days = heOrderAdditionalRevenueEntity.getDays() != null ? heOrderAdditionalRevenueEntity.getDays() : 1;
            heOrderGoodsEntity.setGoodsPriceOrgin(heOrderAdditionalRevenueEntity.getPrice() / days);
            heOrderGoodsEntity.setGoodsNum(days);
            heOrderGoodsEntity.setGoodsName("房型变更");
            heOrderGoodsEntity.setOrderGoodsSn(idGenUtils.genOmniSn(BizConstant.OrderAppKey.OMNI_SN_PRE_KEY, null));
            heOrderGoodsEntity.setGift(0);
            heOrderGoodsEntity.setType(2);
            heOrderGoodsEntity.setAssetType("26");
            heOrderGoodsEntity.setAllocationOriginPrice(0);
            heOrderGoodsEntity.setTotalAllocationOriginPrice(0);

            heOrderGoodsEntity.setParentId(0);
            heOrderGoodsEntity.setBasicUid(orderEntity.getBasicUid());
            heOrderGoodsEntity.setClientUid(orderEntity.getClientUid());
            heOrderGoodsEntity.setClientType(orderEntity.getClientType());
            heOrderGoodsEntity.setStaffId(orderEntity.getStaffId());
            heOrderGoodsEntity.setStoreId(orderEntity.getStoreId());
            heOrderGoodsEntity.setPayStatus(0);
            heOrderGoodsEntity.setGoodsType(26);
            heOrderGoodsEntity.setSkuId(0);
            heOrderGoodsEntity.setGoodsId(0);

            // 设置房型信息（如果有）
            if (heOrderAdditionalRevenueEntity.getRoomId() != null) {
                heOrderGoodsEntity.setRoomId(heOrderAdditionalRevenueEntity.getRoomId());
                heOrderGoodsEntity.setRoomName(heOrderAdditionalRevenueEntity.getRoomName());
            }

            orderGoodsEntities.add(heOrderGoodsEntity);
        }

        if (CollectionUtils.isNotEmpty(additionalRevenueEntityList)) {
            orderGoodsRepository.batchInsert(orderGoodsEntities);
        }


    }

    private void updateUserSnapshot(HeOrderEntity orderEntity) {
        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = userSnapshotRepository.queryByOrderId(orderEntity.getOrderId());
        if (heOrderUserSnapshotEntity == null) {
            return;
        }
        Integer fetusNum = heOrderUserSnapshotEntity.getFetusNum();
        if (Objects.nonNull(fetusNum) && fetusNum == 0) {
            heOrderUserSnapshotEntity.setFetusNum(1);
            userSnapshotRepository.updateOne(heOrderUserSnapshotEntity);
        }
    }

    private void addOrderGiftExtend2Goods(HeOrderEntity orderEntity) {
        Integer orderId = orderEntity.getOrderId();
        List<OrderGiftExtendEntity> giftList = orderGiftExtendRepository.getByOrderId(orderId);
        if (CollectionUtils.isEmpty(giftList)) {
            return;
        }

        List<HeOrderGoodsEntity> orderGoodsEntities = new ArrayList<>();
        for (OrderGiftExtendEntity giftExtend : giftList) {
            HeOrderGoodsEntity orderGoods = orderGoodsDomainConverter.giftExtend2Entity(giftExtend);
            orderGoods.setAllocationOriginPrice(0);
            orderGoods.setTotalAllocationOriginPrice(0);
            orderGoods.setOrderGoodsSn(idGenUtils.genOmniSn(BizConstant.OrderAppKey.OMNI_SN_PRE_KEY, null));
            orderGoods.setBasicUid(orderEntity.getBasicUid());
            if (StringUtils.isEmpty(orderGoods.getGoodsName())) {
                orderGoods.setGoodsName("产康金");
                orderGoods.setGoodsPriceOrgin(100);
            }
            // 设置其他必要字段
            orderGoodsEntities.add(orderGoods);
        }

        if (CollectionUtils.isNotEmpty(orderGoodsEntities)) {
            orderGoodsRepository.batchInsert(orderGoodsEntities);
        }


    }

    private void updateIncome(HeOrderEntity byOrderId) {
        List<HeIncomeRecordEntity> allRecordListByOrderId = incomeRecordRepository.getAllRecordListByOrderId(byOrderId.getOrderId());
        if (CollectionUtils.isEmpty(allRecordListByOrderId)) {
            return;
        }
        for (HeIncomeRecordEntity heIncomeRecordEntity : allRecordListByOrderId) {
            heIncomeRecordEntity.setCurrency("￥");
            heIncomeRecordEntity.setBasicUid(byOrderId.getBasicUid());
        }
        incomeRecordRepository.batchUpdateRecordList(allRecordListByOrderId);
    }

    private void updateOrderGoodsInfo(HeOrderEntity byOrderId) {
        HeOrderGoodsEntity orderGoodsEntity = orderGoodsRepository.getByOrderId(byOrderId.getOrderId());
        orderGoodsEntity.setOrderGoodsSn(idGenUtils.genOmniSn(BizConstant.OrderAppKey.OMNI_SN_PRE_KEY, null));
        orderGoodsEntity.setType(2);
        orderGoodsEntity.setGift(0);

        orderGoodsRepository.update(orderGoodsEntity);

    }

    private void updateOrderInfo(HeOrderEntity byOrderId) {
        byOrderId.setOldOrNew(3);
        byOrderId.setVersion(BigDecimal.valueOf(3.00));
        //        byOrderId.setOrderTag();
        //TODO ,增加标记，是本次转换的订单
        byOrderId.setTransferOrder(Boolean.TRUE);
        byOrderId.setScene(0);
        byOrderId.setNeedSign(1);

        Integer orderTag = byOrderId.getOrderTag();
        if (Objects.nonNull(orderTag)) {
            String tagName = getTagName(orderTag);
            byOrderId.setOrderTagName(tagName);
        }
        //realAmount 修改
        List<HeIncomeRecordEntity> heIncomeRecordEntities = incomeDomainService.queryEffectiveRecord(byOrderId);
        if (CollectionUtils.isNotEmpty(heIncomeRecordEntities)) {

            Integer sumPaidForPerformance = heIncomeRecordEntities.stream()
                    .filter(HeIncomeRecordEntity::isActualSuccess).mapToInt(HeIncomeRecordEntity::getIncome).sum();
            byOrderId.setRealAmount(sumPaidForPerformance);
        }


        orderRepository.updateOne(byOrderId);
    }


    private String getTagName(Integer orderTag) {

        TagReq tagReq = new TagReq();
        tagReq.setId(orderTag);
        TagVO tagVO = tagsRepository.queryTagByReq(tagReq);
        return Objects.isNull(tagVO) ? StringUtils.EMPTY : tagVO.getTagName();
    }
}

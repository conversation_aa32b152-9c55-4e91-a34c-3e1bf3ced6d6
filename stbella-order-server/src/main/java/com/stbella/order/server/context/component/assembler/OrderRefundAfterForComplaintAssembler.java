package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsEntity;
import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsRefundDraftEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.repository.HeCustomerComplaintsRefundDraftRepository;
import com.stbella.order.domain.repository.HeCustomerComplaintsRepository;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

@Component
@Slf4j
@SnowballComponent(name = "退款结束后对客诉的修改", desc = "退款结束后对客诉的修改")
public class OrderRefundAfterForComplaintAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private HeCustomerComplaintsRefundDraftRepository customerComplaintsRefundDraftRepository;

    @Resource
    private HeCustomerComplaintsRepository heCustomerComplaintsRepository;

    @Override
    public boolean condition(FlowContext context) {
        CreateRefundReq attribute = context.getAttribute(CreateRefundReq.class);
        //客诉才走
        return ObjectUtil.isNotEmpty(attribute.getComplaintId());
    }

    @Override
    public void run(FlowContext bizContext) {
        //主退款
        HeOrderRefundEntity refundEntity = bizContext.getAttribute(HeOrderRefundEntity.class);
        CreateRefundReq attribute = bizContext.getAttribute(CreateRefundReq.class);
        HeCustomerComplaintsEntity customerComplaintsEntity = heCustomerComplaintsRepository.selectById(attribute.getComplaintId());
        if (ObjectUtil.isNotEmpty(customerComplaintsEntity)) {
            customerComplaintsEntity.setRefundOrderId(refundEntity.getId().longValue());
            heCustomerComplaintsRepository.update(customerComplaintsEntity);
            HeCustomerComplaintsRefundDraftEntity complaintsRefundDraftEntity = customerComplaintsRefundDraftRepository.queryByComplaintsId(attribute.getComplaintId());
            if (ObjectUtil.isNotEmpty(complaintsRefundDraftEntity)) {
                complaintsRefundDraftEntity.setRefundEnum(OrderApproveRecordTypeEnum.ORDER_BALANCE_REFUND.getPlatformId().toString());
                customerComplaintsRefundDraftRepository.saveOrUpdate(complaintsRefundDraftEntity);
            }
        }
    }


}

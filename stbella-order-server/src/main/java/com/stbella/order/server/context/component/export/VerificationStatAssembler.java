package com.stbella.order.server.context.component.export;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.core.base.DataItermDto;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.common.enums.order.PartDiscountEnum;
import com.stbella.order.common.enums.order.ProduceAmountDeductionEnum;
import com.stbella.order.common.enums.production.OrderProductionItemEnum;
import com.stbella.order.common.enums.production.OrderProductionTypeEnum;
import com.stbella.order.common.enums.production.ProductionServeTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.IncomePaidAllocationEntity;
import com.stbella.order.domain.order.month.entity.TabClientEntity;
import com.stbella.order.domain.order.production.GoodsSkuEntity;
import com.stbella.order.domain.order.production.OrderProductionCardExtendEntity;
import com.stbella.order.domain.order.production.OrderProductionVerificationLogEntity;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.cache.GoodsSupplierCache;
import com.stbella.order.server.convert.OrderGoodsConverter;
import com.stbella.order.server.order.*;
import com.stbella.order.server.order.order.req.OrderExportReq;
import com.stbella.order.server.order.production.component.OrderAmountSplitComponent;
import com.stbella.order.server.order.production.req.ProductionVerificationStatReq;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.pulsar.shade.com.google.common.collect.Lists;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: jijunjian
 * @CreateTime: 2023-07-25  14:39
 * @Description: 产康师核销统计
 */
@Component
@Slf4j
public class VerificationStatAssembler {

    @Resource
    OrderProductionExtendRepository extendRepository;
    @Resource
    OrderGiftExtendRepository giftExtendRepository;
    @Resource
    OrderProductionCardExtendRepository cardExtendRepository;
    @Resource
    OrderProductionVerificationLogRepository verificationLogRepository;
    @Resource
    StoreRepository storeRepository;
    @Resource
    GoodsRepository goodsRepository;
    @Resource
    GoodsSkuRepository goodsSkuRepository;
    @Resource
    OrderGoodsConverter orderGoodsConverter;
    @Resource
    ClientRepository clientRepository;
    @Resource
    OrderAmountSplitComponent orderAmountSplitComponent;
    @Resource
    RuleLinkClient ruleLinkClient;
    @Resource
    IncomePaidAllocationRepository incomePaidAllocationRepository;


    /**
     * 组装产康订单导出数据
     *
     * @param req
     * @return
     */
    @SneakyThrows
    public List<ProductionOrderExportDTO> assemble(OrderExportReq req) {

        //查询产康订单明细
        // 根据爆单时间获取出全部的产康商品的明细，并附加上关联的部分订单信息
        List<ProductionOrderGoodsModel> productionOrderGoodsList = extendRepository.queryProductionOrderGoodsList(req);
        log.info("查询产康订单商品{}", productionOrderGoodsList.size());

        if (log.isDebugEnabled()) {
            log.debug("查询产康订单商品{}", JSONUtil.toJsonStr(productionOrderGoodsList));
        }

        // 生成以订单号为key的map
        Map<Integer, ProductionOrderGoodsModel> orderGoodsMap = new HashMap<>(1024);
        productionOrderGoodsList.forEach(goods -> {
            if (!orderGoodsMap.containsKey(goods.getOrderId())) {
                Integer paidAmount = goods.getPayAmount() - goods.getProductionAmountPay();
                goods.setPaidAmount(paidAmount);
                orderGoodsMap.put(goods.getOrderId(), goods);
            }
            if (OrderProductionItemEnum.TYPE_PRODUCTION.code().equals(goods.getType())) {
                // 有些sku，是多个sku打包的，价格要除以数量。
                Integer price = (goods.getPrice() / (goods.getGoodsNum() * goods.getSkuNum()));
                goods.setPrice(price);
            }
        });

        // 核销信息
        OrderExportReq verificationReq = new OrderExportReq();
        verificationReq.setStartTime(DateUtils.monthStart(req.getEndTime()));
        verificationReq.setEndTime(req.getEndTime());
        verificationReq.setStartTime(req.getWriteOffStartTime());
        verificationReq.setWriteOffEndTime(req.getWriteOffEndTime());


        // 所有门店
        List<CfgStoreEntity> allStore = storeRepository.queryAllStore();
        Map<Integer, CfgStoreEntity> storeMap = allStore.stream().collect(Collectors.toMap(CfgStoreEntity::getStoreId, a -> a));
        // 获取产康商品
        List<ProductionGoodsSkuModel> productionGoodsSkuModels = goodsRepository.queryProductionGoods(null);

        log.info("查询商品{}", productionGoodsSkuModels.size());
        //按sku id 生成map
        Map<Integer, ProductionGoodsSkuModel> productionGoodsSkuModelMap = productionGoodsSkuModels.stream().collect(Collectors.toMap(ProductionGoodsSkuModel::getSkuId, Function.identity(), (item1, item2) -> item1));

        // 释放内存
        productionGoodsSkuModels = null;
        List<ProductionOrderExportDTO> productionOrderExportDTOS = Lists.newArrayList();
        //单项服务
        List<ProductionOrderGoodsModel> singleServiceList = productionOrderGoodsList.stream().filter(a -> OrderProductionItemEnum.TYPE_PRODUCTION.code().equals(a.getType())).collect(Collectors.toList());
        List<ProductionOrderExportDTO> singleServiceExportList = orderGoodsConverter.goodsSku2ExportList(singleServiceList);
        singleServiceExportList.forEach(sku -> {
            sku.setOrderSellType(OrderProductionTypeEnum.BUY.desc());
            sku.setServiceType(OrderProductionItemEnum.TYPE_PRODUCTION.desc());
            sku.setOrderTypeName(OmniOrderTypeEnum.PRODUCTION_ORDER.getDesc());
            sku.setChildSkuPrice(sku.getSkuPrice());
            sku.setChildSkuId(sku.getSkuId());
            sku.setChildGoodsId(sku.getGoodsId());

            if (productionGoodsSkuModelMap.containsKey(sku.getChildSkuId())) {
                ProductionGoodsSkuModel productionGoodsSkuModel = productionGoodsSkuModelMap.get(sku.getChildSkuId());
                sku.setChildServiceType(productionGoodsSkuModel.getServiceType());
                sku.setScale(productionGoodsSkuModel.getScale());
                sku.setSupplierName(productionGoodsSkuModel.getSupplierName());
                sku.setSupplierId(productionGoodsSkuModel.getSupplierId());
                sku.setSettlementRatio(productionGoodsSkuModel.getSettlementRatio());
                sku.setTripartiteSettlementPrice(AmountChangeUtil.changeF2Y(productionGoodsSkuModel.getTripartiteSettlementPrice()).toString());
                sku.setTripartiteSettlementRatio(productionGoodsSkuModel.getTripartiteSettlementRatio());
            } else {
                // 可能不是产康商品，单独查询价格，
                GoodsSkuEntity goodsSkuEntity = goodsSkuRepository.selectForDeletedById(sku.getChildSkuId());
                Integer serviceType = goodsSkuEntity.getServiceType();
                if (Objects.isNull(serviceType)) {
                    serviceType = ProductionServeTypeEnum.APPOINTMENT_TYPE_PROPRIETARY.code();
                }
                sku.setChildServiceType(serviceType);
                sku.setScale(goodsSkuEntity.getScale());

            }
            sku.setSelfSupportCode(sku.getChildServiceType());

        });
        log.info("单项商品处理完成");

        productionOrderExportDTOS.addAll(singleServiceExportList);

        if (log.isDebugEnabled()) {
            log.debug("单项商品后的内容{}", JSONUtil.toJsonStr(productionOrderExportDTOS));
        }
        //卡项服务
        List<ProductionOrderGoodsModel> cardServiceList = productionOrderGoodsList.stream()
                .filter(a -> a.getType().equals(OrderProductionItemEnum.TYPE_GROUP.code()) || a.getType().equals(OrderProductionItemEnum.TYPE_COUNT.code()))
                .collect(Collectors.toList());

        List<ProductionOrderExportDTO> cardExportData = parseCardData(cardServiceList, productionGoodsSkuModelMap, req, allStore);
        productionOrderExportDTOS.addAll(cardExportData);
        log.info("卡项商品处理完成");

        if (log.isDebugEnabled()) {
            log.debug("卡项商品后的内容{}", JSONUtil.toJsonStr(productionOrderExportDTOS));
        }

        //查询产康赠品(下单)
        List<ProductionOrderGoodsModel> giftList = giftExtendRepository.queryProductionOrderGiftList(req);
        List<ProductionOrderExportDTO> productionOrderGiftExport = parseGiftData(giftList, productionGoodsSkuModelMap, OrderProductionTypeEnum.GIFT);
        productionOrderExportDTOS.addAll(productionOrderGiftExport);
        log.info("赠品处理完成{}", productionOrderGiftExport.size());

        if (log.isDebugEnabled()) {
            log.debug("赠送商品后的内容{}", JSONUtil.toJsonStr(productionOrderExportDTOS));
        }
        // 核销信息, 门店，客户
        productionOrderExportDTOS.forEach(productionOrderExportDTO -> {
            productionOrderExportDTO.setSelfSupport(ProductionServeTypeEnum.fromCode(productionOrderExportDTO.getSelfSupportCode()));
            if (productionGoodsSkuModelMap.containsKey(productionOrderExportDTO.getChildSkuId())) {
                ProductionGoodsSkuModel productionGoodsSkuModel = productionGoodsSkuModelMap.get(productionOrderExportDTO.getChildSkuId());
                productionOrderExportDTO.setSelfSupportCode(ProductionServeTypeEnum.APPOINTMENT_TYPE_PROPRIETARY.code());
                if (!ProductionServeTypeEnum.APPOINTMENT_TYPE_PROPRIETARY.code().equals(productionGoodsSkuModel.getServiceType())) {
                    productionOrderExportDTO.setSelfSupportCode(productionGoodsSkuModel.getServiceType());
                }
                productionOrderExportDTO.setSelfSupport(ProductionServeTypeEnum.fromCode(productionOrderExportDTO.getSelfSupportCode()));
            }
        });

        // productionOrderExportDTOS 按订单号分组
        Map<Integer, List<ProductionOrderExportDTO>> orderMap = productionOrderExportDTOS.stream().collect(Collectors.groupingBy(ProductionOrderExportDTO::getOrderId));

        // 按订单计算分摊金额
        log.info("按订单计算分摊金额");
        orderMap.forEach((orderId, orderList) -> {
            List<ProductionOrderGoodsModel> goods = productionOrderGoodsList.stream().filter(a -> a.getOrderId().equals(orderId)).collect(Collectors.toList());
            List<ProductionOrderGoodsModel> orderGiftList = giftList.stream().filter(a -> a.getOrderId().equals(orderId)).collect(Collectors.toList());
            OrderGoodsForSplitValueModel orderGoodsForSplitValueModel = splitAmount(orderList, goods, orderGiftList, productionGoodsSkuModelMap);
            fillSplitResult(orderGoodsForSplitValueModel, orderList);
        });

        //看看是否可能加速回收
        orderMap = null;

        //产康后台赠品
        List<ProductionOrderGoodsModel> adminProductionOrderGiftList = giftExtendRepository.queryProductionOrderAdminGiftList(req);
        List<ProductionOrderExportDTO> adminProductionOrderGiftExport = parseGiftData(adminProductionOrderGiftList, productionGoodsSkuModelMap, OrderProductionTypeEnum.GIFT_ADMIN);
        productionOrderExportDTOS.addAll(adminProductionOrderGiftExport);
        log.info("产康后台赠品{}", adminProductionOrderGiftExport.size());

        adminProductionOrderGiftList = null;

        //查询月子赠品
        List<ProductionOrderGoodsModel> monthOrderGiftList = giftExtendRepository.queryMonthOrderGiftList(req);
        List<ProductionOrderExportDTO> monthOrderGiftExport = parseGiftData(monthOrderGiftList, productionGoodsSkuModelMap, null);

        // 处理新订单的分摊。根据分摊记录查询。
        Map<Integer, List<ProductionOrderExportDTO>> monthOrderGiftMap = monthOrderGiftExport.stream().collect(Collectors.groupingBy(ProductionOrderExportDTO::getOrderId));
        monthOrderGiftMap.forEach((orderId, orderList) -> {
            List<IncomePaidAllocationEntity> allocationEntities = incomePaidAllocationRepository.queryListByOrderId(orderId.longValue());
            for (ProductionOrderExportDTO item: orderList){
                //获取此商品的累计分摊金额
                int skuCount = allocationEntities.stream().filter(a -> a.getSkuId().equals(item.getSkuId())).collect(Collectors.toList()).size();
                if (skuCount == 0){
                    item.setChildSkuPayAmount("0");
                    continue;
                }
                long sum = allocationEntities.stream().filter(a -> a.getSkuId().equals(item.getSkuId())).collect(Collectors.toList()).stream().mapToLong(IncomePaidAllocationEntity::getPaidAmount).sum();
                BigDecimal singlePay = new BigDecimal(sum).divide(new BigDecimal(skuCount), 0, RoundingMode.HALF_UP);
                item.setChildSkuPayAmount(singlePay.toString());
            }

        });
        productionOrderExportDTOS.addAll(monthOrderGiftExport);
        log.info("月子赠品处理完成{}", monthOrderGiftExport.size());

        monthOrderGiftList = null;

        //查询月子中的产康
        OrderExportReq monthOrderReq = BeanMapper.map(req, OrderExportReq.class);
        monthOrderReq.setOrderType(OmniOrderTypeEnum.MONTH_ORDER.getCode());

        List<ProductionOrderGoodsModel> monthOrderProducntionList = extendRepository.queryProductionOrderGoodsList(monthOrderReq);
        List<ProductionOrderExportDTO> monthOrderProducntionExportList = orderGoodsConverter.goodsSku2ExportList(monthOrderProducntionList);
        // 处理新订单的分摊。根据分摊记录查询。
        Map<Integer, List<ProductionOrderExportDTO>> monthOrderProducntionMap = monthOrderProducntionExportList.stream().collect(Collectors.groupingBy(ProductionOrderExportDTO::getOrderId));
        monthOrderProducntionMap.forEach((orderId, orderList) -> {
            List<IncomePaidAllocationEntity> allocationEntities = incomePaidAllocationRepository.queryListByOrderId(orderId.longValue());
            for (ProductionOrderExportDTO item: orderList){
                //获取此商品的累计分摊金额
                int skuCount = allocationEntities.stream().filter(a -> a.getSkuId().equals(item.getSkuId())).collect(Collectors.toList()).size();
                if (skuCount == 0){
                    item.setChildSkuPayAmount("0");
                    continue;
                }
                long sum = allocationEntities.stream().filter(a -> a.getSkuId().equals(item.getSkuId())).collect(Collectors.toList()).stream().mapToLong(IncomePaidAllocationEntity::getPaidAmount).sum();
                // 分摊金额 = 累计分摊金额 / 商品数量
                BigDecimal singlePay = new BigDecimal(sum).divide(new BigDecimal(skuCount), 0, RoundingMode.HALF_UP);
                item.setChildSkuPayAmount(singlePay.toString());
            }

        });

        new ProductionOrderExportAssembler().initSingle(monthOrderProducntionExportList, productionGoodsSkuModelMap, "月子中的产康", OrderProductionTypeEnum.BUY);

        productionOrderExportDTOS.addAll(monthOrderProducntionExportList);
        log.info("月子中的产康处理完成{}", monthOrderProducntionList.size());

        monthOrderProducntionList = null;


        //根据对象 productionOrderExportDTOS 找到所有 clientUid 生成set
        Set<Integer> clientUidSet = productionOrderExportDTOS.stream().map(ProductionOrderExportDTO::getClientUid).collect(Collectors.toSet());
        // 生成以clientUid为key的map
        List<TabClientEntity> tabClientByIdList = clientRepository.getTabClientByIdList(clientUidSet.stream().collect(Collectors.toList()));
        // 生成以clientUid为key的map
        Map<Integer, TabClientEntity> clientMap = tabClientByIdList.stream().collect(Collectors.toMap(TabClientEntity::getId, a -> a));
        log.info(" 客户处理处理完成{}", tabClientByIdList.size());

        //统计核销了删除或者未生效的订单的资产
        List<ProductionOrderExportDTO> exportDTOS = new ArrayList<>();


        ProductionVerificationStatReq verificationStatReq = new ProductionVerificationStatReq();
        verificationStatReq.setStartDate(new Date(req.getWriteOffStartTime() * 1000));
        verificationStatReq.setEndDate(new Date(req.getWriteOffEndTime() * 1000));
        List<OrderProductionVerificationLogEntity> verificationLogs = verificationLogRepository.queryUserVerificationLogList(verificationStatReq);
        log.info(" 核销数据 {}", verificationLogs.size());

        // 1735556400 是 2024-12-30 19:00:00 给客户转送产康金的时间
//        Set<String> productionPaidOrderIds = productionOrderExportDTOS.stream()
//                .filter(a -> a.getProductionAmount() != null && !a.getProductionAmount().equals("0"))
//                .filter(a->a.getCreatedAt() != null && a.getCreatedAt() > 1735556400)
//                .map(ProductionOrderExportDTO::getOrderSn)
//                .collect(Collectors.toSet());
//
//        log.info("产康金支付的订单数{}", productionPaidOrderIds.size());
//        ProductionCoinUsageProcessor usageProcessor =  new ProductionCoinUsageProcessor();
//        usageProcessor.init(productionPaidOrderIds);
//
//        log.info("产康金支付的明细查询完成{}", productionPaidOrderIds.size());
//
//        for (ProductionOrderExportDTO productionOrderExportDTO : productionOrderExportDTOS) {
//            List<ProductionOrderExportDTO> verificationList = fillVerificationData(productionOrderExportDTO, verificationLogs);
//            exportDTOS.addAll(verificationList);
//        }
        productionOrderExportDTOS = null;
        verificationLogs = null;

        //格式化
        formatFiled(exportDTOS);

//        //使用资产置换的产康金
//        exportDTOS.forEach(exportDTO -> {
//
//            Long productionCoinSpecialUsage = usageProcessor.getProductionCoinSpecialUsage(exportDTO.getOrderSn());
//            exportDTO.setSpecialProductionAmount(productionCoinSpecialUsage.intValue());
//        });


        // 门店，客户
        exportDTOS.forEach(exportDTO -> {
            fillStoreData(exportDTO, storeMap);
            if (clientMap.containsKey(exportDTO.getClientUid())) {
                exportDTO.setClientName(clientMap.get(exportDTO.getClientUid()).getName());
            }
            //如果没有生效时间，使用支付时间
            if (Strings.isBlank(exportDTO.getAssetStartTime())) {
                exportDTO.setAssetStartTime(exportDTO.getPercentFirstTime());
            }

        });
        log.info("客户门店信息处理完成");

        // 核销了删除或者未生效的订单的资产
        List<ProductionOrderPo> productionOrderForInvalidOrder = verificationLogRepository.queryProductionOrderVerificationLogForInvalidList(verificationReq);
        log.info("核销了删除或者未生效的订单的资产{}", productionOrderForInvalidOrder.size());
        List<ProductionOrderExportDTO> productionOrderForInvalidOrderExport = BeanMapper.mapList(productionOrderForInvalidOrder, ProductionOrderExportDTO.class);
        productionOrderForInvalidOrderExport.forEach(exportDTO -> {
            exportDTO.setNum("1");
            exportDTO.setOrderSellType("已经删除");
            exportDTO.setLastNum("1");
            exportDTO.setTotalCheckoutNum(1);
            fillStoreData(exportDTO, storeMap);
            if (clientMap.containsKey(exportDTO.getClientUid())) {
                exportDTO.setClientName(clientMap.get(exportDTO.getClientUid()).getName());
            } else {
                //如果没找到，重新查
                List<TabClientEntity> clientList = clientRepository.getTabClientByIdList(Lists.newArrayList(exportDTO.getClientUid()));
                if (CollectionUtil.isNotEmpty(clientList)) {
                    exportDTO.setClientName(clientList.get(0).getName());
                    exportDTO.setClientPhone(clientList.get(0).getPhone());
                }
            }
        });

        exportDTOS.addAll(productionOrderForInvalidOrderExport);

        log.info("数据处理完成");

        return exportDTOS;
    }


    /**
     * productionOrderExportDTOS 格式化金额时间等字段
     */
    protected void formatFiled(List<ProductionOrderExportDTO> productionOrderExportDTOS) {
        String noTimeTag = "1970-01-01 08:00:00";
        productionOrderExportDTOS.forEach(item -> {
            // 金额从分变成元
            item.setTotalPayAmount(AmountChangeUtil.changeF2Y(item.getTotalPayAmount()));
            item.setPayAmount(AmountChangeUtil.changeF2Y(item.getPayAmount()));
            item.setPaidAmount(AmountChangeUtil.changeF2Y(item.getPaidAmount()));
            item.setProductionAmount(AmountChangeUtil.changeF2Y(item.getProductionAmount()));
            item.setSkuPrice(AmountChangeUtil.changeF2Y(item.getSkuPrice()));
            item.setSkuSellingPrice(AmountChangeUtil.changeF2Y(item.getSkuSellingPrice()));
            item.setSkuProductionAmount(AmountChangeUtil.changeF2Y(item.getSkuProductionAmount()));
            item.setSkuPayAmount(AmountChangeUtil.changeF2Y(item.getSkuPayAmount()));

            item.setChildSkuPrice(AmountChangeUtil.changeF2Y(item.getChildSkuPrice()));
            item.setChildSkuSellingPrice(AmountChangeUtil.changeF2Y(item.getChildSkuSellingPrice()));
            item.setChildSkuProductionAmount(AmountChangeUtil.changeF2Y(item.getChildSkuProductionAmount()));
            item.setChildSkuPayAmount(AmountChangeUtil.changeF2Y(item.getChildSkuPayAmount()));

            if (noTimeTag.equals(item.getAssetStartTime())) {
                item.setAssetStartTime("");
            }
        });
    }

    /**
     * 解析退款数据
     *
     * @param refundList
     * @return
     */
    protected List<ProductionOrderExportDTO> parseRefundData(List<ProductionOrderGoodsModel> refundList) {
        List<ProductionOrderExportDTO> productionOrderExportDTOS = Lists.newArrayList();
        refundList.forEach(gift -> {
            gift.setValidEndTime(0L);
            gift.setGoodsNum(1);
            gift.setSkuNum(1);
            gift.setPrice(0);
            ProductionOrderExportDTO sku = orderGoodsConverter.goodsSku2Export(gift);
            sku.setPaidAmount("-" + sku.getPaidAmount());
            sku.setOrderTypeName("退款");
            sku.setProductionAmount("");
            productionOrderExportDTOS.add(sku);
        });
        return productionOrderExportDTOS;
    }

    /**
     * 解析卡项数据 为多条记录
     *
     * @return
     */
    protected List<ProductionOrderExportDTO> parseCardData(List<ProductionOrderGoodsModel> cardServiceList, Map<Integer, ProductionGoodsSkuModel> productionGoodsSkuModelMap, OrderExportReq req, List<CfgStoreEntity> allStore) {

        List<ProductionOrderExportDTO> productionOrderExportDTOS = Lists.newArrayList();
        // 补充卡项商品信息（卡项要去）, 还要查询价格
        List<OrderProductionCardExtendEntity> orderProductionCardExtendEntities = cardExtendRepository.queryProductionOrderCardList(req);

        cardServiceList.forEach(card -> {

            List<OrderProductionCardExtendEntity> cardExtendEntityList = orderProductionCardExtendEntities.stream().filter(a -> a.getOrderId().equals(card.getOrderId()) && a.getOrderProductionId().equals(card.getId())).collect(Collectors.toList());
            // 卡项，拆分成多个商品加入到导出列表
            cardExtendEntityList.forEach(cardExtend -> {
                List<OrderProductionCardContentItermDTO> contentItermDTOS = new ArrayList<>();
                if (OrderProductionItemEnum.TYPE_COUNT.code().equals(card.getType())) {
                    // 次卡是 json对象
                    OrderProductionCardContentItermDTO orderProductionCardContentItermDTO = JSONUtil.toBean(cardExtend.getContent(), OrderProductionCardContentItermDTO.class);
                    contentItermDTOS.add(orderProductionCardContentItermDTO);
                } else {
                    //通卡是 json数组
                    contentItermDTOS = JSONUtil.toList(cardExtend.getContent(), OrderProductionCardContentItermDTO.class);
                }
                //根据具体商品生成一条记录
                contentItermDTOS.forEach(itermDTO -> {
                    ProductionOrderExportDTO sku = orderGoodsConverter.goodsSku2Export(card);
                    sku.setOrderSellType(OrderProductionTypeEnum.BUY.desc());

                    sku.setServiceType(OrderProductionItemEnum.fromCode(card.getType()));
                    sku.setServiceTypeCode(card.getType());
                    sku.setProductionGroupName(cardExtend.getName());
                    sku.setProductionChildName(itermDTO.getGoods_name() + "/" + itermDTO.getSku_name());
                    sku.setNum(cardExtend.getNum() + "");
                    sku.setUsedNum(cardExtend.getUsedNum() + "");
                    sku.setResidueNum((cardExtend.getNum() - cardExtend.getUsedNum()) + "");
                    sku.setCardExtendId(cardExtend.getId());

                    //先看卡项，再用商品表
                    Long endTime = cardExtend.getValidEndTime();
                    if (Objects.isNull(endTime) || endTime.intValue() <= 0) {
                        endTime = card.getValidEndTime();
                    }
                    sku.setValidEndTime(DateUtil.formatDateTime(new Date(endTime * 1000L)));


                    sku.setChildSkuId(itermDTO.getSku_id());
                    sku.setChildGoodsId(itermDTO.getGoods_id());
                    if (productionGoodsSkuModelMap.containsKey(itermDTO.getSku_id())) {
                        ProductionGoodsSkuModel productionGoodsSkuModel = productionGoodsSkuModelMap.get(itermDTO.getSku_id());
                        sku.setChildSkuPrice(productionGoodsSkuModel.getGoodsPrice() + "");
                        sku.setChildServiceType(productionGoodsSkuModel.getServiceType());
                        sku.setChildProduceAmountDeduction(productionGoodsSkuModel.getProduceAmountDeduction());
                        sku.setScale(productionGoodsSkuModel.getScale());
                        sku.setSupplierName(productionGoodsSkuModel.getSupplierName());
                        sku.setSupplierId(productionGoodsSkuModel.getSupplierId());
                        sku.setSettlementRatio(productionGoodsSkuModel.getSettlementRatio());
                        sku.setTripartiteSettlementPrice(AmountChangeUtil.changeF2Y(productionGoodsSkuModel.getTripartiteSettlementPrice()).toString());
                        sku.setTripartiteSettlementRatio(productionGoodsSkuModel.getTripartiteSettlementRatio());
                    } else {
                        // 可能不是产康商品，单独查询价格，
                        GoodsSkuEntity goodsSkuEntity = goodsSkuRepository.selectForDeletedById(itermDTO.getSku_id());
                        sku.setChildSkuPrice(goodsSkuEntity.getGoodsPrice() + "");
                        sku.setScale(goodsSkuEntity.getScale());
                        Integer serviceType = goodsSkuEntity.getServiceType();
                        if (Objects.isNull(serviceType)) {
                            serviceType = ProductionServeTypeEnum.APPOINTMENT_TYPE_PROPRIETARY.code();
                        }
                        sku.setChildServiceType(serviceType);
                        sku.setChildProduceAmountDeduction(ProduceAmountDeductionEnum.NO.code());
                    }
                    sku.setSelfSupportCode(sku.getChildServiceType());
                    sku.setSelfSupport(ProductionServeTypeEnum.fromCode(sku.getSelfSupportCode()));
                    sku.setGroupId(cardExtend.getGroupId());
                    sku.setGroupShareCount(cardExtend.getNum());
                    sku.setOrderTypeName(OmniOrderTypeEnum.PRODUCTION_ORDER.getDesc());

                    //三方商品需要去查询阶梯定价
                    if (ProductionServeTypeEnum.APPOINTMENT_TYPE_TRIPARTITE.code().equals(sku.getChildServiceType())) {
                        CfgStoreEntity cfgStoreEntity = allStore.stream().filter(a -> a.getStoreId().equals(card.getStoreId())).findFirst().orElse(null);

                        if (Objects.nonNull(cfgStoreEntity)) {
                            // 三方的商品，查询配置的阶梯定价
                            Map<String, Object> factMap = new HashMap<>();
                            factMap.put("orderDate", card.getPercentFirstTime());
                            factMap.put("storeType", cfgStoreEntity.getType());
                            factMap.put("quantity", cardExtend.getNum());

                            ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req("price_sku_" + sku.getChildSkuId(), factMap);
                            try {
                                HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
                                if (Objects.nonNull(hitRuleVo)) {
                                    log.info("{} -规则引擎返回结果：{}", sku.getChildSkuId(), hitRuleVo.getSimpleRuleValue());
                                    //转成分
                                    Integer price = Integer.parseInt(hitRuleVo.getSimpleRuleValue()) * 100;
                                    sku.setChildSkuPrice(price + "");
                                }
                            } catch (Exception e) {
                                log.error("规则引擎返回结果发生异常", e);
                            }
                        }

                    }
                    productionOrderExportDTOS.add(sku);
                });
            });
        });

        return productionOrderExportDTOS;
    }

    /**
     * 解析赠品数据
     *
     * @param giftList
     * @return
     */
    protected List<ProductionOrderExportDTO> parseGiftData(List<ProductionOrderGoodsModel> giftList, Map<Integer, ProductionGoodsSkuModel> productionGoodsSkuModelMap, OrderProductionTypeEnum typeEnum) {
        List<ProductionOrderExportDTO> productionOrderExportDTOS = Lists.newArrayList();

        giftList.forEach(gift -> {
            ProductionOrderExportDTO sku = orderGoodsConverter.goodsSku2Export(gift);
            if (Objects.nonNull(typeEnum)) {
                sku.setOrderSellType(typeEnum.desc());
            } else {
                //根据source 判断，1表示是订单送的，其他表示是后台
                if (Objects.equals(gift.getSource(), 1)) {
                    sku.setOrderSellType(OrderProductionTypeEnum.GIFT.desc());
                } else {
                    sku.setOrderSellType(OrderProductionTypeEnum.GIFT_ADMIN.desc());
                }
            }

            sku.setServiceType(OrderProductionItemEnum.TYPE_PRODUCTION.desc());
            sku.setOrderProductionId(gift.getId());
            sku.setOrderTypeName(OmniOrderTypeEnum.getValueByCode(gift.getOrderType()));
            sku.setChildSkuPrice(gift.getPrice() + "");
            sku.setChildSkuSellingPrice("0");
            sku.setProductionAmount("0");
            sku.setPayAmount("0");
            sku.setSkuPrice("0");
            sku.setChildSkuProductionAmount("0");
            sku.setChildSkuPayAmount("0");
            sku.setValidEndTime(DateUtil.formatDateTime(new Date(gift.getValidEndTime() * 1000)));

            if (productionGoodsSkuModelMap.containsKey(sku.getSkuId())) {
                ProductionGoodsSkuModel productionGoodsSkuModel = productionGoodsSkuModelMap.get(sku.getSkuId());
                sku.setChildServiceType(productionGoodsSkuModel.getServiceType());
                sku.setSelfSupport(ProductionServeTypeEnum.fromCode(productionGoodsSkuModel.getServiceType()));
            }


            productionOrderExportDTOS.add(sku);
        });
        return productionOrderExportDTOS;
    }

    /**
     * 填充核销信息
     *
     * @param sku
     * @param req
     * @return
     */
    protected List<ProductionOrderExportDTO> fillVerificationData(ProductionOrderExportDTO sku, List<OrderProductionVerificationLogEntity> verificationLogEntities) {

        log.info("核销信息,orderSn:{}", sku.getOrderSn());
        /**
         * 单项商品，没有子商品，子商品id就是自己
         */
        if (Objects.isNull(sku.getChildSkuId())) {
            sku.setChildSkuId(sku.getSkuId());
        }
        if (Objects.isNull(sku.getCardExtendId())) {
            sku.setCardExtendId(0);
        }

        List<OrderProductionVerificationLogEntity> monthVerificationRecordList = verificationLogEntities.stream()
                .filter(a -> a.getOrderId().intValue() == sku.getOrderId().intValue()
                        && a.getProductionId().equals(sku.getOrderProductionId())
                        && a.getCardId().equals(sku.getCardExtendId())
                        && a.getSkuId().equals(sku.getChildSkuId()))
                .collect(Collectors.toList());

        if (monthVerificationRecordList.size() <= 0) {
            return new ArrayList<>();
        }
        sku.setResidueNum("0");
        sku.setTotalCheckoutNum(0);
        log.info("核销信息,orderSn:{}-{}次", sku.getOrderSn(), monthVerificationRecordList.size());

        if (CollectionUtil.isEmpty(monthVerificationRecordList)) {
            return new ArrayList<>();
        }
        List<ProductionOrderExportDTO> verificationList = new ArrayList<>();

        monthVerificationRecordList.forEach(item->{
            ProductionOrderExportDTO verificationSku = BeanMapper.map(sku, ProductionOrderExportDTO.class);
            verificationSku.setLastNum(1 + "");
            verificationSku.setCheckoutTime(DateUtil.formatDateTime(item.getGmtCreate()));
            verificationSku.setCheckoutUserName(item.getOperatorName());
            verificationSku.setCheckoutStoreId(item.getOperatorStoreId());

            verificationList.add(verificationSku);
        });

        return verificationList;
    }

    /**
     * 填充订单门店与核销门店
     */
    protected ProductionOrderExportDTO fillStoreData(ProductionOrderExportDTO sku, Map<Integer, CfgStoreEntity> storeMap) {
        if (storeMap.containsKey(sku.getStoreId())) {
            sku.setStoreName(storeMap.get(sku.getStoreId()).getStoreName());
        }
        if (storeMap.containsKey(sku.getCheckoutStoreId())) {
            sku.setCheckoutStoreName(storeMap.get(sku.getCheckoutStoreId()).getStoreName());
        }
        return sku;
    }

    /**
     * 单个订单商品分摊金额
     *
     * @param productionOrderExportDTOS
     */
    protected OrderGoodsForSplitValueModel splitAmount(List<ProductionOrderExportDTO> productionOrderExportDTOS, List<ProductionOrderGoodsModel> orderGoods
            , List<ProductionOrderGoodsModel> giftList, Map<Integer, ProductionGoodsSkuModel> goodsSkuMap
    ) {

        String speProductName = "黄金三件套体验卡";
        String speGroupName = "JAMU全身护理尊享1天";


        // 订单列 转化成 OrderGoodsForSplitValueModel
        OrderGoodsForSplitValueModel orderGoodsForSplitValueModel = new OrderGoodsForSplitValueModel();
        if (CollectionUtil.isEmpty(productionOrderExportDTOS)) {
            return null;
        }
        orderGoodsForSplitValueModel.setOrderId(productionOrderExportDTOS.get(0).getOrderId());
        orderGoodsForSplitValueModel.setPayAmount(Integer.valueOf(productionOrderExportDTOS.get(0).getPayAmount()));
        orderGoodsForSplitValueModel.setProductionAmountPay(Integer.valueOf(productionOrderExportDTOS.get(0).getProductionAmount()));

        // 设置订单商品
        List<ProductionGoodsSkuModel> orderGoodsList = new ArrayList<>();
        orderGoods.forEach(item -> {
            // 处理单项与卡项
            ProductionGoodsSkuModel productionGoodsSkuModel = new ProductionGoodsSkuModel();
            productionGoodsSkuModel.setSkuId(item.getSkuId());
            productionGoodsSkuModel.setGoodsPrice(item.getPrice());
            productionGoodsSkuModel.setCombineType(item.getType());
            productionGoodsSkuModel.setOrderProductionId(item.getId());


            if (!item.getType().equals(OrderProductionItemEnum.TYPE_PRODUCTION.code())) {
                List<ProductionGoodsSkuModel> childSkuList = new ArrayList<>();
                // 卡项 - 要处理子项目
                List<ProductionOrderExportDTO> childList = productionOrderExportDTOS.stream().filter(a -> a.getOrderProductionId().equals(item.getId())).collect(Collectors.toList());
                childList.forEach(child -> {
                    ProductionGoodsSkuModel childSku = new ProductionGoodsSkuModel();
                    childSku.setSkuId(child.getChildSkuId());
                    if (Strings.isBlank(child.getChildSkuPrice())) {
                        child.setChildSkuPrice("0");
                    }
                    childSku.setGoodsPrice(Integer.valueOf(child.getChildSkuPrice()));
                    if (child.getProductionName().equals(speProductName) && child.getProductionGroupName().equals(speGroupName)) {
                        //赠送的，价格为0；
                        childSku.setGoodsPrice(0);
                    }
                    childSku.setCombineType(OrderProductionItemEnum.TYPE_PRODUCTION.code());
                    childSku.setGroupId(child.getGroupId());
                    childSku.setGroupShareCount(child.getGroupShareCount());
                    childSku.setIsFullMoonType(child.getChildServiceType());
                    childSku.setServiceType(child.getChildServiceType());

                    childSkuList.add(childSku);
                });
                productionGoodsSkuModel.setIsFullMoonType(PartDiscountEnum.NO.code());
                productionGoodsSkuModel.setChildSkuList(childSkuList);

            } else {
                productionGoodsSkuModel.setIsFullMoonType(PartDiscountEnum.YES.code());
            }

            productionGoodsSkuModel.setOrderSellType(OrderProductionTypeEnum.BUY.desc());
            orderGoodsList.add(productionGoodsSkuModel);
        });

        // 设置赠品
        giftList.forEach(gift -> {
            ProductionGoodsSkuModel productionGoodsSkuModel = new ProductionGoodsSkuModel();
            productionGoodsSkuModel.setOrderProductionId(gift.getId());
            productionGoodsSkuModel.setSkuId(gift.getSkuId());
            productionGoodsSkuModel.setGoodsPrice(gift.getPrice());
            productionGoodsSkuModel.setCombineType(OrderProductionItemEnum.TYPE_PRODUCTION.code());
            productionGoodsSkuModel.setOrderSellType(OrderProductionTypeEnum.GIFT.desc());
            productionGoodsSkuModel.setIsFullMoonType(PartDiscountEnum.YES.code());

            orderGoodsList.add(productionGoodsSkuModel);
        });


        orderGoodsList.forEach(goods -> {
            goods.setProduceAmountDeduction(ProduceAmountDeductionEnum.NO.code());
            if (goodsSkuMap.containsKey(goods.getSkuId())) {
                ProductionGoodsSkuModel productionGoodsSkuModel = goodsSkuMap.get(goods.getSkuId());
                goods.setProduceAmountDeduction(productionGoodsSkuModel.getProduceAmountDeduction());
                goods.setServiceType(productionGoodsSkuModel.getServiceType());
            }
            if (CollectionUtil.isNotEmpty(goods.getChildSkuList())) {
                goods.getChildSkuList().forEach(childSku -> {
                    if (goodsSkuMap.containsKey(childSku.getSkuId())) {
                        ProductionGoodsSkuModel productionGoodsSkuModel = goodsSkuMap.get(childSku.getSkuId());
                        childSku.setProduceAmountDeduction(productionGoodsSkuModel.getProduceAmountDeduction());
                        childSku.setServiceType(productionGoodsSkuModel.getServiceType());
                    }
                });
            }
        });

        orderGoodsForSplitValueModel.setSkuList(orderGoodsList);
        orderAmountSplitComponent.split(orderGoodsForSplitValueModel);

        if (log.isDebugEnabled()) {
            log.debug("订单分摊{}：{}", productionOrderExportDTOS.get(0).getOrderSn(), JSONUtil.toJsonStr(orderGoodsForSplitValueModel));
        }
        return orderGoodsForSplitValueModel;

    }


    /**
     * 填充分担结果
     * 根据不同类型，不同id， 填充不同商品和子项
     *
     * @param productionOrderExportDTOS
     */
    protected void fillSplitResult(OrderGoodsForSplitValueModel splitResult, List<ProductionOrderExportDTO> productionOrderExportDTOS) {

        productionOrderExportDTOS.forEach(item -> {
            List<ProductionGoodsSkuModel> skuList = splitResult.getSkuList().stream().filter(
                    sku -> sku.getOrderProductionId().equals(item.getOrderProductionId())
                            && sku.getOrderSellType().equals(item.getOrderSellType())
                            && sku.getSkuId().equals(item.getSkuId())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(skuList)) {
                item.setSkuSellingPrice(skuList.get(0).getSellingPrice() + "");
                item.setSkuProductionAmount(skuList.get(0).getProductionAmount() + "");
                item.setSkuPayAmount(skuList.get(0).getPayAmount() + "");

                //所有子项都显示价格
                item.setChildSkuSellingPrice(skuList.get(0).getSellingPrice() + "");
                item.setChildSkuProductionAmount(skuList.get(0).getProductionAmount() + "");
                item.setChildSkuPayAmount(skuList.get(0).getPayAmount() + "");

                //log.info("订单分摊{}：{}设置sku价格",productionOrderExportDTOS.get(0).getOrderSn(), JSONUtil.toJsonStr(skuList.get(0)) );

                // 子项
                if (Objects.nonNull(item.getChildSkuId())) {
                    if (CollectionUtil.isNotEmpty(skuList.get(0).getChildSkuList())) {
                        Optional<ProductionGoodsSkuModel> childOpt = skuList.get(0).getChildSkuList().stream().filter(a -> a.getSkuId().equals(item.getChildSkuId()) && a.getGroupId().equals(item.getGroupId())).findFirst();
                        if (childOpt.isPresent()) {
                            item.setChildSkuSellingPrice(childOpt.get().getSellingPrice() + "");
                            item.setChildSkuProductionAmount(childOpt.get().getProductionAmount() + "");
                            item.setChildSkuPayAmount(childOpt.get().getPayAmount() + "");

                            //log.info("订单分摊{}：{}设置child sku价格",productionOrderExportDTOS.get(0).getOrderSn(), childOpt.get());
                        }
                    }
                }
            }
        });
    }

}

package com.stbella.order.server.context.component.processor;

import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.service.QuotationCreateDomainService;
import com.stbella.platform.order.api.req.CreateOrderReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "创建报价单")
public class CreateQuotationProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private QuotationCreateDomainService quotationCreateDomainService;

    @Override
    public void run(FlowContext bizContext) {

        CreateOrderReq req = bizContext.getAttribute(CreateOrderReq.class);
        HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);
        order.setOrderId(quotationCreateDomainService.createQuotation(order, req.getOperator()));
        bizContext.setAttribute(HeOrderEntity.class, order);
        log.info("报价单保存成功，报价单号：{}", order.getOrderSn());
    }
}

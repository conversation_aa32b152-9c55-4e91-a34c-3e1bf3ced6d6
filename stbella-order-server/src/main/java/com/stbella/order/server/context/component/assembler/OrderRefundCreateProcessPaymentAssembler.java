package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.core.OrderRefundNatureEnum;
import com.stbella.order.common.enums.month.RefundRecordPayStatusEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.HeIncomeProofRecordEntity;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.repository.IncomeProofRecordRepository;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.domain.repository.OrderRefundRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.order.cts.enums.OrderRefundAuditStatusEnum;
import com.stbella.order.server.order.month.enums.PayMethodEnum;
import com.stbella.order.server.order.month.enums.PayStatusEnum;
import com.stbella.order.server.order.month.enums.RefundTypeEnum;
import com.stbella.order.server.utils.IdGenUtils;
import com.stbella.platform.order.api.refund.api.OrderRefundService;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
@SnowballComponent(name = "处理income表冻结+退款主表+退款子表", desc = "处理income表冻结+退款主表+退款子表")
public class OrderRefundCreateProcessPaymentAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @Resource
    private IncomeProofRecordRepository incomeProofRecordRepository;
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private OrderRepository orderRepository;

    @Override
    public void run(FlowContext bizContext) {
        // 1. 获取并处理基本信息
        CreateRefundReq createRefundReq = getCreateRefundReq(bizContext);
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);

        // 2. 获取并过滤收入记录
        List<HeIncomeRecordEntity> filteredRecords = getFilteredIncomeRecords(createRefundReq.getOrderId());

        // 3. 对收入记录进行排序
        List<HeIncomeRecordEntity> sortedRecords = sortIncomeRecords(filteredRecords);

        // 4. 处理退款
        String parentRefundOrderSn = IdGenUtils.createRefundTransactionalNo(null, new Date());
        List<HeOrderRefundEntity> refundEntityList = processRefunds(sortedRecords, createRefundReq, parentRefundOrderSn, orderEntity);

        // 处理仅退款数量的情况
        Object attribute = bizContext.getAttribute(OtherConstant.ONLY_REFUND_NUM);
        if (attribute != null && (boolean) attribute) {
            HeOrderRefundEntity refundEntity = buildRefundEntity(parentRefundOrderSn, null, 0,
                    createRefundReq, orderEntity.getCurrency());
            refundEntityList.add(refundEntity);
        }


        // 5. 更新收入记录
        updateIncomeRecords(sortedRecords);

        // 6. 处理主退款
        HeOrderRefundEntity parentRefund = processParentRefund(createRefundReq, parentRefundOrderSn, orderEntity);

        // 7. 保存退款记录
        saveRefundRecords(refundEntityList, parentRefund);

        // 8. 更新订单冻结金额
        updateOrderFreezeAmount(createRefundReq, orderEntity);

        // 9. 设置上下文属性
        setContextAttributes(bizContext, parentRefund, parentRefundOrderSn, refundEntityList);
    }



    private CreateRefundReq getCreateRefundReq(FlowContext bizContext) {
        return bizContext.getAttribute(CreateRefundReq.class);
    }


    private List<HeIncomeRecordEntity> getFilteredIncomeRecords(Integer orderId) {
        List<HeIncomeProofRecordEntity> incomeProofRecords = incomeProofRecordRepository.getIncomeProofRecordByOrderId(orderId);
        return incomeRecordRepository.getAllRecordListByOrderId(orderId).stream()
                .filter(this::isValidIncomeRecord)
                .filter(record -> isValidProofRecord(record, incomeProofRecords))
                .collect(Collectors.toList());
    }

    private boolean isValidIncomeRecord(HeIncomeRecordEntity record) {
        return record.getStatus().equals(PayStatusEnum.PAY_STATUS_SUCCESS.getCode())
                && record.getRefundIncome() > 0;
    }

    private boolean isValidProofRecord(HeIncomeRecordEntity record, List<HeIncomeProofRecordEntity> proofRecords) {
        if (!record.getPayType().equals(OmniPayTypeEnum.OFFLINE.getCode())) {
            return true;
        }
        return proofRecords.stream()
                .anyMatch(proof -> proof.getIncomeId().equals(record.getId())
                        && proof.getStatus().equals(OrderRefundAuditStatusEnum.SUCCESS_AUDIT.getCode()));
    }

    private List<HeIncomeRecordEntity> sortIncomeRecords(List<HeIncomeRecordEntity> records) {
        return records.stream()
                .sorted(Comparator.comparingInt(this::getPayTypePriority)
                        .thenComparing(this::getAdjustedPayTime, Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());
    }

    private int getPayTypePriority(HeIncomeRecordEntity record) {
        OmniPayTypeEnum typeEnum = OmniPayTypeEnum.getByCode(record.getPayType());
        switch (typeEnum) {
            case WECHAT:
            case ALIPAY:
                return 1;
            case ONLINE_POS:
                return 3;
            case BALANCE:
                return 4;
            case OFFLINE:
                return 5;
            default:
                return 6;
        }
    }

    private Long getAdjustedPayTime(HeIncomeRecordEntity record) {
        if (record.getPayType().equals(OmniPayTypeEnum.ALIPAY.getCode())) {
            return record.getPayTime() + 15552000L;
        }
        return record.getPayType().equals(OmniPayTypeEnum.WECHAT.getCode()) ? record.getPayTime().longValue() : 0L;
    }


    private void updateIncomeRecords(List<HeIncomeRecordEntity> records) {
        incomeRecordRepository.batchUpdateRecordList(records);
    }

    private HeOrderRefundEntity processParentRefund(CreateRefundReq createRefundReq, String parentRefundOrderSn,
                                                    HeOrderEntity orderEntity) {
        HeOrderRefundEntity parentRefund = buildParentRefundEntity(createRefundReq, parentRefundOrderSn, orderEntity.getCurrency());
        Integer refundId = orderRefundRepository.saveOne(parentRefund);
        parentRefund.setId(refundId);
        return parentRefund;
    }

    private List<HeOrderRefundEntity> processRefunds(
            List<HeIncomeRecordEntity> sortedRecords,
            CreateRefundReq createRefundReq,
            String parentRefundOrderSn,
            HeOrderEntity orderEntity) {
        List<HeOrderRefundEntity> refundEntityList = new ArrayList<>();

        List<CreateRefundReq.GoodsRefundAmountInfo> refundAmountInfoList = createRefundReq.getRefundAmountInfoList();

        // 创建子退款记录，
        for (CreateRefundReq.GoodsRefundAmountInfo goodsRefundAmountInfo : refundAmountInfoList) {
            Integer remainingRefundAmount = AmountChangeUtil.changeY2FFoInt(goodsRefundAmountInfo.getAmount());

            if (Objects.isNull(remainingRefundAmount) || remainingRefundAmount <= 0) continue;
            //类型收入
            List<HeIncomeRecordEntity> typeIncome = sortedRecords.stream().filter(s -> PayMethodEnum.PayType2Currency(s.getPayType()).getCode().equals(goodsRefundAmountInfo.getAmountType().toString())).collect(Collectors.toList());

            for (HeIncomeRecordEntity record : typeIncome) {

                Integer refundIncome = record.getRefundIncome();

                int refundAmount = Math.min(remainingRefundAmount, refundIncome);
                // 更新收入记录的冻结金额
                updateIncomeFreezeAmount(record, refundAmount);

                remainingRefundAmount -= refundAmount;

                HeOrderRefundEntity refundEntity = buildRefundEntity(parentRefundOrderSn, record, refundAmount,
                        createRefundReq, orderEntity.getCurrency());
                refundEntityList.add(refundEntity);
            }
        }

        return refundEntityList;
    }

    private void updateIncomeFreezeAmount(HeIncomeRecordEntity record, int refundAmount) {
        record.setFreezeAmount(record.getFreezeAmount() + refundAmount);
    }

    private HeOrderRefundEntity buildRefundEntity(String parentRefundOrderSn, HeIncomeRecordEntity incomeRecord,
                                                  int refundAmount, CreateRefundReq createRefundReq, String currency) {
        HeOrderRefundEntity refundEntity = new HeOrderRefundEntity();
        refundEntity.setParentRefundOrderSn(parentRefundOrderSn);
        refundEntity.setOrderId(createRefundReq.getOrderId());
        refundEntity.setRefundOrderSn(IdGenUtils.createRefundTransactionalNo(null, new Date()));
        refundEntity.setApplyAmount(refundAmount);
        refundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_1.getCode());
        refundEntity.setCreatedAt(System.currentTimeMillis() / 1000);
        refundEntity.setApplyId(Integer.valueOf(createRefundReq.getOperator().getOperatorGuid()));
        refundEntity.setApplyName(createRefundReq.getOperator().getOperatorName());
        refundEntity.setApplyPhone(createRefundReq.getOperator().getOperatorPhone());
        refundEntity.setCurrency(currency);
        refundEntity.setRefundNature(createRefundReq.getGoodsRefundType());
        refundEntity.setRemark(createRefundReq.getRemark());
        refundEntity.setRefundReasonType(createRefundReq.getRefundReasonType());
        refundEntity.setRefundReason(createRefundReq.getRefundReason());
        refundEntity.setHasLiquidatedDamages(0);
        refundEntity.setGiftExtendDisabled(0);
        if (ObjectUtil.isNotEmpty(incomeRecord)) {
            refundEntity.setOrderGoodId(incomeRecord.getId());
            refundEntity.setIncomeSn(incomeRecord.getIncomeSn());
            refundEntity.setRefundType(determineRefundType(createRefundReq.getRefundType(), incomeRecord.getPayType()));
        } else {
            refundEntity.setRefundType(OmniPayTypeEnum.OTHER.getCode());
        }
        refundEntity.setRefundMethod(createRefundReq.getRefundType());
        refundEntity.setType(1);
        refundEntity.setRefundInfo(createRefundInfo(createRefundReq));
        return refundEntity;
    }

    private Integer determineRefundType(Integer refundType, Integer payType) {
        OmniPayTypeEnum payTypeEnum = OmniPayTypeEnum.getByCode(payType);
        if (payTypeEnum.equals(OmniPayTypeEnum.PRODUCTION_COIN)) {
            return OmniPayTypeEnum.PRODUCTION_COIN.getCode();
        } else if (payTypeEnum.equals(OmniPayTypeEnum.REDUCTION)){
            return OmniPayTypeEnum.REDUCTION.getCode();
        }else {
            RefundTypeEnum refundTypeEnum = RefundTypeEnum.getEnumByCode(refundType);
            switch (refundTypeEnum) {
                case BACK_TRACK:
                    return payType;
                case OFFLINE_REMITTANCE:
                    return OmniPayTypeEnum.OFFLINE.getCode();
                case BACK_ACCOUNT:
                    return OmniPayTypeEnum.BALANCE.getCode();
                default:
                    return payType;
            }
        }
    }

    private String createRefundInfo(CreateRefundReq createRefundReq) {
        Map<String, Object> refundInfo = new HashMap<>();
        refundInfo.put("images", createRefundReq.getAuditRefundProof());
        if (createRefundReq.getRefundType().equals(RefundTypeEnum.OFFLINE_REMITTANCE.getCode())) {
            refundInfo.put("name", createRefundReq.getAccountName());
            refundInfo.put("bank_number", createRefundReq.getBankNo());
            refundInfo.put("bank_name", createRefundReq.getAccountBank());
        }
        return JSONUtil.toJsonStr(refundInfo);
    }

    private HeOrderRefundEntity buildParentRefundEntity(CreateRefundReq createRefundReq, String parentRefundOrderSn, String currency) {
        HeOrderRefundEntity parentRefund = new HeOrderRefundEntity();
        parentRefund.setParentRefundOrderSn(null);
        parentRefund.setOrderId(createRefundReq.getOrderId());
        parentRefund.setRefundOrderSn(parentRefundOrderSn);

        List<CreateRefundReq.GoodsRefundAmountInfo> refundAmountInfoList = createRefundReq.getRefundAmountInfoList();
        Optional<CreateRefundReq.GoodsRefundAmountInfo> first = refundAmountInfoList.stream().filter(r -> r.getAmountType().toString().equals(PayMethodEnum.CASH.getCode())).findFirst();
        if (first.isPresent()) {
            parentRefund.setApplyAmount(AmountChangeUtil.changeY2FFoInt(first.get().getAmount()));
        } else {
            parentRefund.setApplyAmount(0);
        }
        parentRefund.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_1.getCode());
        parentRefund.setCreatedAt(System.currentTimeMillis() / 1000);
        parentRefund.setRefundMethod(createRefundReq.getRefundType());
        parentRefund.setApplyId(Integer.valueOf(createRefundReq.getOperator().getOperatorGuid()));
        parentRefund.setApplyName(createRefundReq.getOperator().getOperatorName());
        parentRefund.setApplyPhone(createRefundReq.getOperator().getOperatorPhone());
        parentRefund.setCurrency(currency);
        parentRefund.setRefundNature(createRefundReq.getGoodsRefundType());
        parentRefund.setRemark(createRefundReq.getRemark());
        parentRefund.setRefundReasonType(createRefundReq.getRefundReasonType());
        parentRefund.setRefundReason(createRefundReq.getRefundReason());
        parentRefund.setHasLiquidatedDamages(0);
        parentRefund.setGiftExtendDisabled(0);
        parentRefund.setOrderGoodId(0);
        parentRefund.setIncomeSn("");
        parentRefund.setRefundType(OmniPayTypeEnum.OTHER.getCode());
        parentRefund.setType(0);
        parentRefund.setRefundInfo(createRefundInfo(createRefundReq));

        return parentRefund;
    }

    private void saveRefundRecords(List<HeOrderRefundEntity> refundEntityList, HeOrderRefundEntity parentRefund) {
        if (!refundEntityList.isEmpty()) {
            orderRefundRepository.saveOneList(refundEntityList);
        }
    }

    private void updateOrderFreezeAmount(CreateRefundReq createRefundReq, HeOrderEntity orderEntity) {
        if (createRefundReq.getGoodsRefundType().equals(OrderRefundNatureEnum.TEMP_REFUND.code())
                && createRefundReq.getRefundAmount().compareTo(BigDecimal.ZERO) > 0) {

            List<CreateRefundReq.GoodsRefundAmountInfo> refundAmountInfoList = createRefundReq.getRefundAmountInfoList().stream().filter(c -> c.getAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());


            if (CollectionUtil.isNotEmpty(refundAmountInfoList)) {
                BigDecimal totalSum = refundAmountInfoList.stream()
                        .map(CreateRefundReq.GoodsRefundAmountInfo::getAmount) // 提取 BigDecimal 字段
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                orderEntity.setFreezeAmount(orderEntity.getFreezeAmount() + AmountChangeUtil.changeY2FFoInt(totalSum));
                orderRepository.updateOne(orderEntity);
            }
            /*Optional<CreateRefundReq.GoodsRefundAmountInfo> first = refundAmountInfoList.stream().filter(r -> r.getAmountType().equals(new Integer(PayMethodEnum.CASH.getCode()))).findFirst();
            if (first.isPresent()) {
                //有现金才更新
                orderEntity.setFreezeAmount(orderEntity.getFreezeAmount() + AmountChangeUtil.changeY2FFoInt(first.get().getAmount()));
                orderRepository.updateOne(orderEntity);
            }*/
        }
    }

    private void setContextAttributes(FlowContext bizContext, HeOrderRefundEntity parentRefund, String parentRefundOrderSn, List<HeOrderRefundEntity> childRefundEntityList) {
        bizContext.setAttribute(OtherConstant.PARENT_REFUND_ORDER_ID, parentRefund.getId());
        bizContext.setAttribute(OtherConstant.PARENT_REFUND_ORDER_SN, parentRefundOrderSn);
        bizContext.setAttribute(HeOrderRefundEntity.class, parentRefund);
        bizContext.setAttribute(OtherConstant.ORDER_REFUND_CHILD, childRefundEntityList);
    }

    @Override
    public boolean condition(FlowContext context) {
        return (boolean) context.getAttribute(OtherConstant.CONTINUE);
    }
}

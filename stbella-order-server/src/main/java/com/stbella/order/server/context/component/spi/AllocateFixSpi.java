package com.stbella.order.server.context.component.spi;

import cn.hutool.json.JSONObject;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.server.context.component.processor.pay.AllocationPaymentProcessor;
import top.primecare.snowball.extpoint.annotation.FuncPointSpi;
import top.primecare.snowball.extpoint.definition.IFuncSpiSupport;

/**
 * <AUTHOR>
 *
 * 支付分摊SPI
 */
@FuncPointSpi(name = "支付扩展点", attachToComponent = AllocationPaymentProcessor.class)
public interface AllocateFixSpi extends IFuncSpiSupport<HeIncomeRecordEntity, JSONObject> {

}

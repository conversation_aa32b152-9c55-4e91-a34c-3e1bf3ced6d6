package com.stbella.order.server.order.production.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.stbella.care.server.care.vo.SatisfactionExportVO;
import com.stbella.base.server.common.request.RealPaidClientUploadReq;
import com.stbella.base.server.common.request.RealPaidSummaryUploadReq;
import com.stbella.base.server.common.request.RealPaidUploadReq;
import com.stbella.core.base.LoginUserDTO;
import com.stbella.core.base.PageVO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.core.utils.CopyBeanUtil;
import com.stbella.core.utils.ExcelUtils;
import com.stbella.message.scene.req.SceneTriggerReq;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.production.*;
import com.stbella.order.common.exception.ApplicationException;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.BPCheckUtil;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.domain.client.BaseClient;
import com.stbella.order.domain.client.ClientWechatService;
import com.stbella.order.domain.client.MessageClient;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.UserEntity;
import com.stbella.order.domain.order.production.*;
import com.stbella.order.domain.order.production.dto.*;
import com.stbella.order.domain.order.service.ProductionAppointmentDomainService;
import com.stbella.order.domain.repository.*;
import com.stbella.order.infrastructure.config.BizConfig;
import com.stbella.order.infrastructure.repository.converter.ProductionAppointmentConverter;
import com.stbella.order.infrastructure.repository.mapper.ecp.CfgStoreMapper;
import com.stbella.order.infrastructure.repository.mapper.ecp.TabClientMapper;
import com.stbella.order.infrastructure.repository.mapper.saas.HeOrderMapper;
import com.stbella.order.infrastructure.repository.mapper.saas.OrderGiftExtendMapper;
import com.stbella.order.infrastructure.repository.mapper.saas.OrderProductionExtendMapper;
import com.stbella.order.infrastructure.repository.po.CfgStorePO;
import com.stbella.order.infrastructure.repository.mapper.saas.TherapistMapper;
import com.stbella.order.infrastructure.repository.po.HeOrderPO;
import com.stbella.order.infrastructure.repository.mapper.saas.*;
import com.stbella.order.infrastructure.repository.po.*;
import com.stbella.order.infrastructure.repository.mapper.saas.*;
import com.stbella.order.infrastructure.repository.po.*;
import com.stbella.order.infrastructure.repository.mapper.saas.HeOrderMapper;
import com.stbella.order.infrastructure.repository.mapper.saas.OrderGiftExtendMapper;
import com.stbella.order.infrastructure.repository.mapper.saas.OrderProductionExtendMapper;
import com.stbella.order.infrastructure.repository.po.CfgStorePO;
import com.stbella.order.infrastructure.repository.po.HeOrderPO;
import com.stbella.order.infrastructure.repository.po.OrderProductionAppointment;
import com.stbella.order.domain.repository.GoodsSkuRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.domain.repository.ProductionAppointmentOptLogRepository;
import com.stbella.order.domain.repository.ProductionAppointmentRepository;
import com.stbella.order.infrastructure.repository.po.OrderGiftExtend;
import com.stbella.order.domain.repository.GoodsSkuRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.domain.repository.ProductionAppointmentOptLogRepository;
import com.stbella.order.domain.repository.ProductionAppointmentRepository;
import com.stbella.order.domain.repository.GoodsSkuRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.domain.repository.ProductionAppointmentOptLogRepository;
import com.stbella.order.domain.repository.ProductionAppointmentRepository;
import com.stbella.order.infrastructure.repository.po.Therapist;
import com.stbella.order.infrastructure.repository.po.TabClientPO;
import com.stbella.order.server.convert.AppProductionAppointmentConverter;
import com.stbella.order.server.order.ProductionClientRealPaidModel;
import com.stbella.order.server.order.ProductionTherapistRealPaidModel;
import com.stbella.order.server.order.order.req.BatchProductionExportReq;
import com.stbella.order.server.order.order.req.ExportProductionRealPaidReq;
import com.stbella.order.server.order.order.req.ProductionRealPaidReq;
import com.stbella.order.server.order.order.req.ProductionReq;
import com.stbella.order.server.order.production.api.ProductionAppointmentQueryService;
import com.stbella.order.server.order.production.component.ProductionAppointmentAssembler;
import com.stbella.order.server.order.production.req.*;
import com.stbella.order.server.order.production.res.*;
import com.stbella.order.server.utils.BigDecimalUtil;
import com.stbella.sso.base.Operator;
import com.stbella.sso.server.dingding.entity.DdEmployeePO;
import com.stbella.sso.server.dingding.service.DdEmployeeService;
import com.stbella.sso.therapist.api.TherapistQueryService;
import com.stbella.sso.therapist.req.TherapistDetailReq;
import com.stbella.sso.therapist.req.TherapistQueryReq;
import com.stbella.sso.therapist.res.TherapistSelectOptionVo;
import com.stbella.sso.therapist.res.TherapistVo;
import jodd.util.StringUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.stbella.order.common.constant.BizConstant.EXPORT_FILE_NAME;
import static com.stbella.order.common.utils.DateUtils.*;


/**
 * 查询服务impl生产约会
 *
 * <AUTHOR>
 * @date 2022/09/30
 */
@Service
@DubboService
@Slf4j
public class ProductionAppointmentQueryServiceImpl implements ProductionAppointmentQueryService {

    @Resource
    private ProductionAppointmentRepository appointmentRepository;
    @Resource
    private AppProductionAppointmentConverter productionAppointmentConverter;
    @Resource
    private ProductionAppointmentDomainService appointmentDomainService;
    @Resource
    private ProductionAppointmentAssembler appointmentAssembler;
    @Resource
    private ProductionAppointmentOptLogRepository optLogRepository;
    @Resource
    private GoodsSkuRepository goodsSkuRepository;
    @Resource
    private ProductionAppointmentDomainService productionAppointmentDomainService;
    @Resource
    private ClientWechatService clientWechatService;
    @Resource
    private OrderProductionExtendRepository orderProductionExtendRepository;
    @Resource
    private OrderProductionCardExtendRepository orderProductionCardExtendRepository;
    @Resource
    private OrderGiftExtendRepository orderGiftExtendRepository;
    @Resource
    private HeOrderMapper heOrderMapper;
    @Resource
    private BizConfig bizConfig;
    @Resource
    private OrderProductionExtendMapper orderProductionExtendMapper;
    @Resource
    private ProductionAppointmentOptLogRepository productionAppointmentOptLogRepository;
    @Resource
    private CfgStoreMapper cfgStoreMapper;
    @Resource
    private TabClientMapper tabClientMapper;
    @Resource
    private OrderGiftExtendMapper orderGiftExtendMapper;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private TherapistStoreMapper therapistStoreMapper;
    @Resource
    private TherapistMapper therapistMapper;
    @Resource
    private ProductionAppointmentConverter appointmentConverter;
    @Resource
    private OrderProductionAppointmentMapper orderProductionAppointmentMapper;
    @Resource
    private UserRepository userRepository;
    @Resource
    private BaseClient baseClient;
    @Resource
    private MessageClient messageClient;

    @DubboReference
    private TherapistQueryService therapistQueryService;
    @DubboReference
    private DdEmployeeService ddEmployeeService;


    /**
     * 查询搜索预约列表
     *
     * @param query 查询
     * @return {@link Result}<{@link List}<{@link ProductionAppointmentVo}>>
     */
    @Override
    public Result<PageVO<ProductionAppointmentVo>> querySearchAppointmentList(ProductionAppointmentQuery query) {
        log.info("预约订单列表查询{}", JSONUtil.toJsonStr(query));
        BPCheckUtil.checkEmptyInBean(new String[]{"pageNum", "pageSize", "operator"}, query, false);
        Long appointmentCount = appointmentRepository.querySearchAppointmentCount(query);
        List<ProductionAppointmentVo> appointmentVos = new ArrayList<>();
        if (0 < appointmentCount) {
            List<OrderProductionAppointmentEntity> orderProductionAppointmentEntities = appointmentRepository.querySearchAppointmentList(query);
            //todo 查询分组skuIds
            List<Integer> skuIds = appointmentDomainService.distinctSkuIds(orderProductionAppointmentEntities);
            if (CollectionUtils.isNotEmpty(skuIds)) {
                query.setSkuIds(skuIds);
                orderProductionAppointmentEntities = appointmentRepository.querySearchAppointmentList(query);
            }
            appointmentVos = productionAppointmentConverter.entity2VoForListProductionAppointment(orderProductionAppointmentEntities);
        }
        // 返回最后结果
        PageVO pageVO = new PageVO(appointmentVos, appointmentCount.intValue(), query.getPageSize(), query.getPageNum());

        return Result.success(pageVO);
    }

    @Override
    public Result<PageVO<ProductionAppointmentBackVo>> queryAppointmentBackPage(ProductionAppointmentBackQuery query) {
        log.info("预约订单列表查询{}", JSONUtil.toJsonStr(query));
        BPCheckUtil.checkEmptyInBean(new String[]{"pageNum", "pageSize", "operator"}, query, false);
        // 如果有产康师筛选，则dubbo掉用产康师服务返回产康师ID
        List<Long> therapistIdList = null;

        if (Objects.nonNull(query.getTherapistName())) {
            TherapistQueryReq therapistQueryReq = new TherapistQueryReq();
            therapistQueryReq.setName(query.getTherapistName());
            Result<List<TherapistVo>> result = therapistQueryService.queryTherapists(therapistQueryReq);
            if (!result.getSuccess()) {
                throw new ApplicationException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code(), "产康师服务有误，请检查");
            }
            List<TherapistVo> therapistList = result.getData();

            log.info("调用用sso返回数据为：{}", JSONUtil.toJsonStr(therapistList));
            if (CollectionUtils.isEmpty(therapistList)) {
                therapistIdList = Collections.singletonList(-1L);
            } else {
                therapistIdList = therapistList.stream().map(TherapistVo::getId).collect(Collectors.toList());
            }
        }
        log.info("最终拿到的数据为：{}", therapistIdList);
        query.setTherapistIdList(therapistIdList);
        Page<OrderProductionAppointmentEntity> appointmentEntityPage = appointmentRepository.querySearchAppointmentListBack(query);

        List<OrderProductionAppointmentEntity> records = appointmentEntityPage.getRecords();
        List<ProductionAppointmentBackVo> appointmentBackVos = productionAppointmentConverter.entity2VoForListProductionAppointmentBack(records);

        // 填充核销时间
        if (CollectionUtils.isNotEmpty(appointmentBackVos)) {
            List<ProductionAppointmentBackVo> writeOffAppointments = appointmentBackVos.stream()
                    .filter(o -> Objects.equals(o.getVerificationState(), VerificationStatusEnum.DONE_WRITE_OFF.code()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(writeOffAppointments)) {
                List<OrderProductionAppointmentOptLogEntity> writeOffLogs = optLogRepository.queryByAppointmentIds(
                        writeOffAppointments.stream().map(ProductionAppointmentBackVo::getId).collect(Collectors.toList()),
                        OptTypeEnum.WRITE_OFF.code()
                );
                writeOffAppointments.forEach(o -> {
                    Optional<OrderProductionAppointmentOptLogEntity> first = writeOffLogs.stream()
                            .filter(log -> Objects.equals(log.getAppointmentId(), o.getId()))
                            .findFirst();
                    first.ifPresent(orderProductionAppointmentOptLogEntity -> o.setWriteOffDate(orderProductionAppointmentOptLogEntity.getGmtCreate()));
                });
            }
        }

        PageVO pageVO = new PageVO(appointmentBackVos, (int) appointmentEntityPage.getTotal(), (int) appointmentEntityPage.getSize(), (int) appointmentEntityPage.getCurrent());
        return Result.success(pageVO);
    }

    /**
     * 产康师核销金额汇总
     */
    @Override
    public void therapistRealPaidExport(ProductionAppointmentBackQuery query, HttpServletResponse response) {
        List<OrderProductionAppointmentEntity> productionBookList = appointmentRepository.getProductionBookList(query);

        productionBookList = productionBookList.stream().filter(o -> !Objects.equals(o.getStoreId(), 1005)).collect(Collectors.toList());

        List<String> orderSnList = productionBookList.stream().map(OrderProductionAppointmentEntity::getOrderSn).distinct().collect(Collectors.toList());
        List<OrderProductionAppointmentEntity> orderProductionAppointmentEntities = appointmentRepository.listByOrderSn(orderSnList);

        List<Long> therapistIds = productionBookList.stream().map(OrderProductionAppointmentEntity::getTherapistId).distinct().collect(Collectors.toList());

        List<Therapist> therapists = therapistMapper.selectList(
                new LambdaQueryWrapper<Therapist>().in(Therapist::getId, therapistIds)
        );

        // 填充实付金额
        Map<String, BigDecimal> realPaidMap = this.getRealPaidMap(orderProductionAppointmentEntities);
        List<ProductionJudgeVO> judges = this.judgeAppointment(orderProductionAppointmentEntities);

        List<TherapistRealPaidDTO> re = new ArrayList<>();

        productionBookList = productionBookList.stream().filter(o -> Objects.nonNull(o.getTherapistId())).collect(Collectors.toList());
        Map<Long, List<OrderProductionAppointmentEntity>> therapistMap = productionBookList.stream().collect(Collectors.groupingBy(OrderProductionAppointmentEntity::getTherapistId));

        therapistMap.forEach((therapistId, list) -> {
            TherapistRealPaidDTO dto = new TherapistRealPaidDTO();
            dto.setTherapistId(therapistId);

            Optional<Therapist> therapistOpt = therapists.stream().filter(o -> Objects.equals(o.getId(), therapistId)).findFirst();
            therapistOpt.ifPresent(o -> {
                dto.setTherapistName(o.getName());
                dto.setTherapistMobile(o.getMobile());
            });

            BigDecimal summary = BigDecimal.ZERO;
            for (OrderProductionAppointmentEntity orderProductionAppointmentEntity : list) {
                BigDecimal realPaid = realPaidMap.get(orderProductionAppointmentEntity.getOrderSn());
                if (Objects.isNull(realPaid) || realPaid.compareTo(BigDecimal.ZERO) < 0) {
                    realPaid = BigDecimal.ZERO;
                }

                Optional<ProductionJudgeVO> judgeOpt = judges.stream().filter(j -> Objects.equals(orderProductionAppointmentEntity.getOrderSn(), j.getAppointmentSn())).findFirst();
                if (judgeOpt.isPresent() && judgeOpt.get().isHit()) {
                    summary = BigDecimalUtil.add(summary, realPaid);
                }
            }
            BigDecimal multiplication = com.stbella.core.utils.BigDecimalUtil.multiplication(summary, BigDecimal.valueOf(100));
            dto.setRealPaidSummary(AmountChangeUtil.changeF2Y(String.valueOf(multiplication.intValue())));
            re.add(dto);
        });

        String fileName = "产康师核销金额" + DateUtils.format(new Date(), CURRENT_TIME_FORMAT);
        try {
            ExcelUtils.exportExcel(response, fileName, re, TherapistRealPaidDTO.class);
        } catch (IOException e) {
            throw new ApplicationException(ErrorCodeEnum.SYSTEM_ERROR.code(), "产康师核销金额导出异常");
        }
    }

    @Override
    @SneakyThrows
    public void doExportProductionBookList(ProductionAppointmentBackQuery query, HttpServletResponse response) {
        if (Objects.isNull(query.getWriteOffStart()) || Objects.isNull(query.getWriteOffEnd())){
            throw new BusinessException(ResultEnum.PARAM_IS_NULL.getCode(), "核销日期筛选不能为空");
        }
        Date serveStart = query.getWriteOffStart();
        Date serveEnd = query.getWriteOffEnd();
        List<Date> intervalMonth = getIntervalMonth(serveStart, serveEnd);
        Integer appointmentMaxMonthSize = bizConfig.getAppointmentMaxMonthSize();
        if (intervalMonth.size() > appointmentMaxMonthSize){
            throw new BusinessException(ResultEnum.PARAM_IS_NULL.getCode(), "核销日期间隔不能大于"+appointmentMaxMonthSize+"个月");
        }

        String fileName = EXPORT_FILE_NAME + DateUtils.format(new Date(), CURRENT_TIME_FORMAT);
        ServletOutputStream outputStream = response.getOutputStream();
        ZipOutputStream zipOutputStream = new ZipOutputStream(outputStream);
        try {
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName+".zip", "UTF-8"));
            for (int i = 1; i <= intervalMonth.size(); i++) {
                Date date = intervalMonth.get(i - 1);
                if (i == 1) {
                    query.setWriteOffStart(serveStart);
                } else {
                    query.setWriteOffStart(DateUtils.getMonthStart(date));
                }
                if (i == intervalMonth.size()) {
                    query.setWriteOffEnd(serveEnd);
                } else {
                    query.setWriteOffEnd(DateUtils.getMonthEnd(date));
                }

                List<OrderProductionAppointmentEntity> productionBookList = appointmentRepository.getProductionBookList(query);
                List<OrderProductionAppointmentOptLogEntity> optLogEntities = optLogRepository
                        .batchOperateLog(productionBookList.stream().map(OrderProductionAppointmentEntity::getId).collect(Collectors.toList()));
                List<ExportProductionBookDTO> exportProductionBookDTOList = appointmentAssembler.fromDto(productionBookList, optLogEntities);


                // 导出时获取核销金额
                List<String> orderSnList = productionBookList.stream().map(OrderProductionAppointmentEntity::getOrderSn).distinct().collect(Collectors.toList());
                List<OrderProductionAppointmentEntity> orderProductionAppointmentEntities = appointmentRepository.listByOrderSn(orderSnList);

                // 填充实付金额
                Map<String, BigDecimal> realPaidMap = this.getRealPaidMap(orderProductionAppointmentEntities);
                List<ProductionJudgeVO> judges = this.judgeAppointment(orderProductionAppointmentEntities);

                exportProductionBookDTOList.forEach(o -> {
                    BigDecimal realPaid = realPaidMap.get(o.getOrderNumber());
                    if (Objects.isNull(realPaid) || realPaid.compareTo(BigDecimal.ZERO) < 0) {
                        realPaid = BigDecimal.ZERO;
                    }
                    BigDecimal multiplication = com.stbella.core.utils.BigDecimalUtil.multiplication(realPaid, BigDecimal.valueOf(100));
                    o.setRealPaid(AmountChangeUtil.changeF2Y(String.valueOf(multiplication.intValue())));

                    Optional<ProductionJudgeVO> judgeOpt = judges.stream().filter(j -> Objects.equals(o.getOrderNumber(), j.getAppointmentSn())).findFirst();
                    if (judgeOpt.isPresent()) {
                        o.setSettleFlg(judgeOpt.get().isHit() ? "未结算" : "已结算");
                    } else {
                        o.setSettleFlg("已结算");
                    }
                });
                ExcelWriter excelWriter = EasyExcel.write().excelType(ExcelTypeEnum.XLS).build();
                WriteSheet writeSheet = EasyExcel.writerSheet("sheet1").build();
                WriteTable writeTable0 = EasyExcel.writerTable(0).head(ExportProductionBookDTO.class).needHead(Boolean.TRUE).build();
                excelWriter.write(exportProductionBookDTOList, writeSheet, writeTable0);
                ZipEntry zipEntry = new ZipEntry(DateUtils.format(date, "yyyy-MM") + ".xls");
                zipOutputStream.putNextEntry(zipEntry);
                Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();
                workbook.write(zipOutputStream);
            }
            zipOutputStream.closeEntry();
            zipOutputStream.flush();
        } catch (Exception e) {
            log.error("doExportProductionBookList 异常", e);
        } finally {
            zipOutputStream.close();
            outputStream.close();
        }
    }

    @Override
    public Result<ProductionAppointmentInfoBackVo> queryAppointmentBackDetail(ProductionAppointmentInfoBackQuery query) {
        log.info("预约订单详情查询{}", JSONUtil.toJsonStr(query));
        BPCheckUtil.checkEmptyInBean(new String[]{"operator"}, query, false);
        return Result.success(appointmentAssembler.infoAssembler(query));
    }

    @Override
    public Result<ProductionAppointmentInfoBackVo> queryAppointmentBackDetail(Integer propertyId, Integer itemType, Integer orderProductionType) {
        List<OrderProductionAppointmentEntity> orderProductionAppointmentEntities = appointmentRepository.queryByConditions(propertyId, itemType, orderProductionType);
        if (CollectionUtil.isEmpty(orderProductionAppointmentEntities)) {
            return Result.success(null);
        }
        OrderProductionAppointmentEntity orderProductionAppointmentEntity = orderProductionAppointmentEntities.get(0);
        ProductionAppointmentInfoBackQuery query = new ProductionAppointmentInfoBackQuery();
        query.setId(orderProductionAppointmentEntity.getId());
        return Result.success(appointmentAssembler.infoAssembler(query));
    }

    /**
     * 查询搜索产康预约面板
     *
     * @param query 查询
     * @return {@link Result}<{@link List}<{@link ProductionAppointmentVo}>>
     */
    @Override
    public Result<ProductionBoardAppointmentVo> querySearchBoardAppointmentList(ProductionAppointmentQuery query) throws Exception {
        log.info("预约订单看板列表查询{}", JSONUtil.toJsonStr(query));
        BPCheckUtil.checkEmptyInBean(new String[]{"operator"}, query, false);
        return Result.success(appointmentAssembler.boardAssembler(query));
    }

    @Override
    public Result<ProductionAppointmentBoardWorkmanshipVo> queryWorkmanship(AppointmentBoardTherapistQuery query) {
        log.info("预约单产康师手工费查询{}", JSONUtil.toJsonStr(query));
        TherapistDetailReq therapistDetailReq = new TherapistDetailReq();
        therapistDetailReq.setOperator(Operator.of(""));
        therapistDetailReq.setId(query.getTherapistId());
        Result<TherapistSelectOptionVo> result = therapistQueryService.queryTherapistDetail(therapistDetailReq);
        if (!result.getSuccess()) {
            throw new ApplicationException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code(), "产康师服务有误，请检查");
        }
        TherapistSelectOptionVo therapistVo = result.getData();
        if (therapistVo == null) {
            throw new ApplicationException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code(), "产康师信息不存在！");
        }

        if (Objects.nonNull(query.getWriteOffStart()) || Objects.nonNull(query.getWriteOffEnd())) {
            List<OrderProductionAppointmentOptLogEntity> optLogEntities = productionAppointmentOptLogRepository.queryByWriteOffDate(query.getWriteOffStart(), query.getWriteOffEnd());
            List<Long> appointmentIds = optLogEntities.stream()
                    .map(OrderProductionAppointmentOptLogEntity::getAppointmentId)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(appointmentIds)) {
                appointmentIds.add(-1L);
            }
            query.setAppointmentIds(appointmentIds);
        }

        List<OrderProductionAppointment> serveFeeEntities = orderProductionAppointmentMapper.selectList(
                new LambdaQueryWrapper<OrderProductionAppointment>()
                        .eq(OrderProductionAppointment::getTherapistId, query.getTherapistId())
                        .in(OrderProductionAppointment::getId, query.getAppointmentIds())
                        .eq(OrderProductionAppointment::getVerificationState, OrderProductionVerificationStateEnum.WRITTEN_OFF.code())
        );

        List<OrderProductionAppointment> pendingServeFeeEntities = orderProductionAppointmentMapper.selectList(
                new LambdaQueryWrapper<OrderProductionAppointment>()
                        .eq(OrderProductionAppointment::getTherapistId, query.getTherapistId())
                        .eq(OrderProductionAppointment::getVerificationState, OrderProductionVerificationStateEnum.RESERVED.code())
                        .select(OrderProductionAppointment::getServeFee)
        );

        ProductionAppointmentBoardWorkmanshipVo re = new ProductionAppointmentBoardWorkmanshipVo();

        // 已核销手工费
        int writeOffServeFee = serveFeeEntities.stream().map(OrderProductionAppointment::getServeFee).filter(Objects::nonNull).mapToInt(o -> o).sum();
        re.setWriteOffServeFee(AmountChangeUtil.changeF2Y(String.valueOf(writeOffServeFee)));

        // 历史预约待核销手工费
        int pendServeFee = pendingServeFeeEntities.stream().map(OrderProductionAppointment::getServeFee).filter(Objects::nonNull).mapToInt(o -> o).sum();
        re.setAllPendingServeFee(AmountChangeUtil.changeF2Y(String.valueOf(pendServeFee)));

        // 核销金额 自营part
        List<OrderProductionAppointmentEntity> stbellaAppointmentEntities = appointmentConverter.po2EntityForListProductionAppointment(
                serveFeeEntities.stream()
                        .filter(o -> Objects.equals(o.getServeType(), 0))
                        .collect(Collectors.toList())
        );

        // 三方part
        List<OrderProductionAppointmentEntity> thirdAppointmentEntities = Lists.newArrayList();
        UserEntity userEntity = userRepository.queryByPhone(therapistVo.getMobile());
        if (Objects.nonNull(userEntity)) {
            ProductionReq productionReq = new ProductionReq();
            productionReq.setProductionGoodsIds(bizConfig.getAppointmentJudgeSpuIds());
            productionReq.setOrderCreatedAt(bizConfig.getTransJudgeDate().getTime() / 1000);
            productionReq.setWriteOffStartDate(query.getWriteOffStart());
            productionReq.setWriteOffEndDate(query.getWriteOffEnd());
            productionReq.setUserIds(Collections.singletonList(userEntity.getId()));
            thirdAppointmentEntities.addAll(orderProductionExtendMapper.getTherapistRealPaidThird(productionReq));
        } else {
            log.info("queryWorkmanship no userEntity found, phone {}", therapistVo.getMobile());
        }

        // 核销金额 = 自营 + 三方下单
        List<OrderProductionAppointmentEntity> orderProductionAppointmentEntities = new ArrayList<>(stbellaAppointmentEntities);
        orderProductionAppointmentEntities.addAll(thirdAppointmentEntities);
        Map<String, BigDecimal> realPaidMap = this.getRealPaidMap(orderProductionAppointmentEntities);

        // 自营
        ProductionRealPaidVO stbellaRealPaid = this.getRealPaid(stbellaAppointmentEntities, realPaidMap);
        // 三方下单
        ProductionRealPaidVO thirdRealPaid = this.getRealPaid(thirdAppointmentEntities, realPaidMap);

        log.info("therapistId:{}, stbellaRealPaid:{}, thirdRealPaid:{}", therapistVo.getId(), stbellaRealPaid, thirdRealPaid);

        BigDecimal add = BigDecimalUtil.add(stbellaRealPaid.getWriteOffRealPaidAmount(), thirdRealPaid.getWriteOffRealPaidAmount());
        re.setWriteOffRealPaid(ProductionRealPaidVO.changeF2Y(add));

        // 产康师信息
        re.setTherapistId(therapistVo.getId());
        re.setTherapistName(therapistVo.getName());

        return Result.success(re);
    }

    /**
     * 【MINI端 pi/c端】查询预约单列表
     *
     * @param query 筛选
     * @return Result<PageVO < List < ProductionAppointmentMiniVo>>>
     */
    @Override
    public Result<PageVO<ProductionAppointmentMiniVo>> queryAppointmentMiniPage(ProductionAppointmentBackQuery query) {
        log.info("pi/c端 预约订单列表查询{}", JSONUtil.toJsonStr(query));
        BPCheckUtil.checkEmptyInBean(new String[]{"pageNum", "pageSize", "operator"}, query, false);

        Integer realPaidServeType = query.getRealPaidServeType();
        if (Objects.equals(realPaidServeType, 1)) {
            BPCheckUtil.checkEmptyInBean(new String[]{"therapistId"}, query, false);
            Therapist therapist = therapistMapper.selectById(query.getTherapistId());

            if (Objects.isNull(therapist)) {
                throw new ApplicationException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code(), "产康师信息不存在 id:" + query.getTherapistId());
            }

            UserEntity userEntity = userRepository.queryByPhone(therapist.getMobile());
            if (Objects.isNull(userEntity)) {
                log.info("queryAppointmentMiniPage no userEntity found, phone {}", therapist.getMobile());
                return Result.success(new PageVO<>(Collections.emptyList(), 0, query.getPageSize(), query.getPageNum()));
            }

            ProductionReq productionReq = new ProductionReq();
            productionReq.setStoreId(query.getStoreId());
            productionReq.setServeStartDate(query.getServeStart());
            productionReq.setServeEndDate(query.getServeEnd());
            productionReq.setUserIds(Collections.singletonList(userEntity.getId()));
            List<OrderProductionAppointmentEntity> therapistRealPaidThird = orderProductionExtendMapper.getTherapistRealPaidThird(productionReq);

            if (CollectionUtil.isEmpty(therapistRealPaidThird)) {
                return Result.success(new PageVO<>(Collections.emptyList(), 0, query.getPageSize(), query.getPageNum()));
            }

            query.clear().setAppointmentIds(
                    therapistRealPaidThird.stream().map(OrderProductionAppointmentEntity::getId).collect(Collectors.toList())
            );
        }

        PageVO<ProductionAppointmentMiniVo> re = appointmentAssembler.appointmentMiniPageAssembler(query);
        List<ProductionAppointmentMiniVo> list = re.getList();

        if (CollectionUtil.isEmpty(list)) return Result.success(re);

        // 填充实付金额
        List<String> appointmentOrderSns = list.stream().map(ProductionAppointmentMiniVo::getOrderSn).collect(Collectors.toList());
        List<OrderProductionAppointmentEntity> orderProductionAppointmentEntities = appointmentRepository.listByOrderSn(appointmentOrderSns);
        Map<String, BigDecimal> realPaidMap = this.getRealPaidMap(orderProductionAppointmentEntities);
        List<ProductionJudgeVO> judges = this.judgeAppointment(orderProductionAppointmentEntities);

        list.forEach(o -> {
            BigDecimal realPaid = realPaidMap.get(o.getOrderSn());
            if (Objects.isNull(realPaid) || realPaid.compareTo(BigDecimal.ZERO) < 0) {
                realPaid = BigDecimal.ZERO;
            }
            BigDecimal multiplication = com.stbella.core.utils.BigDecimalUtil.multiplication(realPaid, BigDecimal.valueOf(100));
            o.setRealPaidAmount(AmountChangeUtil.changeF2Y(String.valueOf(multiplication.intValue())));

            Optional<ProductionJudgeVO> judgeOpt = judges.stream().filter(j -> Objects.equals(o.getOrderSn(), j.getAppointmentSn())).findFirst();
            if (judgeOpt.isPresent()) {
                o.setSettleFlg(judgeOpt.get().isHit() ? 0 : 1);
            } else {
                o.setSettleFlg(1);
            }
        });

        return Result.success(re);
    }

    @Override
    public Result<List<ProductionAppointmentProductionScheduleVo>> queryProductionSchedule(AppointmentProductionScheduleQuery query) {
        log.info("PI端/c端】查看产康日程{}", JSONUtil.toJsonStr(query));
        BPCheckUtil.checkEmptyInBean(new String[]{"operator"}, query, false);
        return Result.success(appointmentAssembler.scheduleAssembler(query));
    }

    /**
     * 约单详情
     *
     * @param id
     * @return com.stbella.core.result.Result<com.stbella.order.server.order.production.res.ProductionAppointmentDetailVO>
     * @throws
     * <AUTHOR>
     * @date 2022/10/14 00:03
     * @since 1.0.0
     */
    @Override
    public Result<ProductionAppointmentDetailVO> queryProductionById(Long id) {
        OrderProductionAppointmentEntity entity = appointmentRepository.queryByIdWithLogs(id);
        ProductionAppointmentDetailVO result = CopyBeanUtil.copy(entity, ProductionAppointmentDetailVO.class);

        result.setServeStart(DateUtils.format(entity.getServeStart(), TIME_FORMAT));
        result.setServeEnd(DateUtils.format(entity.getServeEnd(), TIME_FORMAT));
        result.setServeStartValue(entity.getServeStart().getTime());
        result.setServeEndValue(entity.getServeEnd().getTime());
        result.setServeFeeShow(Objects.nonNull(result.getServeFee()) ? AmountChangeUtil.changeF2Y(result.getServeFee().toString()) : "0.00");
        result.setItemTypeName(OrderProductionItemEnum.fromCode(result.getItemType()));
        if (OrderProductionItemEnum.TYPE_PRODUCTION.code().equals(result.getItemType())) {
            result.setGroupGoodsName(result.getProductionGoodsName());
        }

        try {
            List<WechatUserVo> wechatList = clientWechatService.getWechatList(Collections.singletonList(entity.getClientPhone()));
            if (CollectionUtils.isNotEmpty(wechatList)) {
                Optional<WechatUserVo> first = wechatList.stream()
                        .filter(o -> Objects.equals(o.getPhoneNumber(), entity.getClientPhone()))
                        .findFirst();
                first.ifPresent(wechatUserVo -> result.setAvatarUrl(wechatUserVo.getAvatarUrl()));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        List<OrderProductionAppointmentOptLogEntity> logEntities = entity.getLogEntities();
        if (CollectionUtils.isNotEmpty(logEntities)) {
            List<ProductionAppointmentDetailVO.AppointmentLog> collect = logEntities.stream().map(logEntity -> new ProductionAppointmentDetailVO.AppointmentLog(
                    logEntity.getOptName(),
                    DateUtils.format(logEntity.getGmtCreate(), YYYY_MM_DD_HH_MM_SS),
                    logEntity.getOptType()
            )).collect(Collectors.toList());

            result.setAppointmentLogs(collect);
        }
        if (Objects.nonNull(result.getOrderId())) {
            HeOrderEntity heOrderEntity = orderRepository.queryOrderById(result.getOrderId());
            result.setOrderNo(Objects.nonNull(heOrderEntity) ? heOrderEntity.getOrderSn() : null);
        }
        return Result.success(result);
    }

    @Override
    public Result<ProductionAppointmentGoodsSkuInfoVo> behalfGoodsInfo(AppointmentBehalfConsumerQuery query) {
        log.info("代客预约获取商品信息{}", JSONUtil.toJsonStr(query));
        BPCheckUtil.checkEmptyInBean(new String[]{"propertyId", "clientId", "propertySource", "skuId", "serviceType"}, query, false);
        return productionAppointmentDomainService.behalfGoodsInfo(query);
    }

    @Override
    public Result<List<ProductionBehalfTimeShaftVo>> behalfTimeInfo(AppointmentBehalfTImeQuery query) {
        log.info("代客预约获取可预约时间{}", JSONUtil.toJsonStr(query));
        BPCheckUtil.checkEmptyInBean(new String[]{"therapistId", "clientId", "serviceTime", "productionInstrId", "storeId", "startDate", "endDate", "bookType"}, query, false);
        return productionAppointmentDomainService.behalfTimeInfo(query);
    }

    @Override
    public Result<ProductionAppointmentServiceFeeVo> getServiceFeeByTherapistIdAndSkuID(AppointmentServiceFeeByTherapistIdAndSkuIDQuery query) {
        log.info("代客预约获取服务费{}", JSONUtil.toJsonStr(query));
        BPCheckUtil.checkEmptyInBean(new String[]{"id", "SKUID"}, query, false);

        TherapistDetailReq req = new TherapistDetailReq();
        req.setId(query.getId());
        req.setOperator(Objects.isNull(req.getOperator()) ? new Operator() : req.getOperator());
        Result<TherapistVo> oneTherapist = therapistQueryService.getOneTherapist(req);
        if (!oneTherapist.getSuccess()) {
            throw new ApplicationException(ErrorCodeEnum.BIZ_ERROR.code(), "产康师不存在");
        }
        TherapistVo data = oneTherapist.getData();
        if (Objects.isNull(data.getGrade())) {
            throw new ApplicationException(ErrorCodeEnum.BIZ_ERROR.code(), "获取手工费失败：未知产康师等级");
        }
        query.setGradeId(data.getGrade());
        return productionAppointmentDomainService.getServiceFeeByTherapistIdAndSkuID(query);
    }

    @Override
    public Result<ProductionAppointmentTherapistVo> queryProductionByTherapistId(ProductionAppointmentInfoQuery query) {
        log.info("【PI端/c端】查看预约单所有记录{}", JSONUtil.toJsonStr(query));
        BPCheckUtil.checkEmptyInBean(new String[]{"operator"}, query, false);
        return Result.success(appointmentAssembler.therapistAndStoreAssembler(query));
    }

    @Override
    public List<ProductionTherapistInfoVO> queryTherapistInfoByOrderSn(List<String> orderSnList) {
        if (CollectionUtil.isEmpty(orderSnList)) {
            return new ArrayList<>();
        }
        List<OrderProductionAppointmentEntity> entityList = appointmentRepository.queryTherapistInfoByOrderSn(orderSnList);
        if (CollectionUtil.isEmpty(entityList)) {
            return new ArrayList<>();
        }
        return entityList.stream().map(i -> {
            ProductionTherapistInfoVO productionTherapistInfoVO = new ProductionTherapistInfoVO();
            productionTherapistInfoVO.setTherapistId(i.getTherapistId());
            productionTherapistInfoVO.setTherapistName(i.getTherapistName());
            productionTherapistInfoVO.setServeType(i.getServeType());
            productionTherapistInfoVO.setOrderSn(i.getOrderSn());
            return productionTherapistInfoVO;
        }).collect(Collectors.toList());
    }

    @Override
    public Result<ProductionRealPaidSummaryVO> queryRealPaidSummary(ProductionRealPaidReq req) {
        ProductionRealPaidSummaryVO re = new ProductionRealPaidSummaryVO();
        re.setStoreId(req.getStoreId());

        List<CfgStorePO> stores = cfgStoreMapper.selectList(new LambdaQueryWrapper<CfgStorePO>().eq(CfgStorePO::getStoreId, req.getStoreId()));
        if (CollectionUtil.isNotEmpty(stores)) {
            re.setStoreName(stores.get(0).getStoreName());
        }

        ProductionReq summaryReq = CopyBeanUtil.copy(req, ProductionReq.class);
        summaryReq.setProductionGoodsIds(bizConfig.getAppointmentJudgeSpuIds());
        summaryReq.setOrderCreatedAt(bizConfig.getTransJudgeDate().getTime() / 1000);
        summaryReq.setVerificationState(VerificationStatusEnum.DONE_WRITE_OFF.code());

        // 礼赠累计
        BigDecimal giftRealPaid = orderProductionExtendMapper.getGiftRealPaid(summaryReq);
        // 单项累计
        BigDecimal productionRealPaid = orderProductionExtendMapper.getProductionRealPaid(summaryReq);
        // 通次卡累计
        BigDecimal productionCardRealPaid = orderProductionExtendMapper.getProductionCardRealPaid(summaryReq);
        log.info("累计核销金额(自营)：礼赠累计：{}，单项累计：{}，通次卡累计：{}", giftRealPaid, productionRealPaid, productionCardRealPaid);

        // 礼赠累计(三方)
        BigDecimal giftRealPaidThird = orderProductionExtendMapper.getGiftRealPaidThird(summaryReq);
        // 单项累计(三方)
        BigDecimal productionRealPaidThird = orderProductionExtendMapper.getProductionRealPaidThird(summaryReq);
        // 通次卡累计(三方)
        BigDecimal productionCardRealPaidThird = orderProductionExtendMapper.getProductionCardRealPaidThird(summaryReq);
        log.info("累计核销金额(三方)：礼赠累计：{}，单项累计：{}，通次卡累计：{}", giftRealPaidThird, productionRealPaidThird, productionCardRealPaidThird);

        BigDecimal summaryPaidSelf = BigDecimalUtil.add(giftRealPaid, productionRealPaid, productionCardRealPaid);
        BigDecimal summaryPaidThird = BigDecimalUtil.add(giftRealPaidThird, productionRealPaidThird, productionCardRealPaidThird);

        // 累计核销金额(自营)
        re.setWriteOffSummaryPaidSelf(AmountChangeUtil.format2Y(summaryPaidSelf));
        // 累计核销金额(三方)
        re.setWriteOffSummaryPaidThird(AmountChangeUtil.format2Y(summaryPaidThird));
        // 累计核销金额
        re.setWriteOffSummaryPaid(AmountChangeUtil.format2Y(BigDecimalUtil.add(summaryPaidSelf, summaryPaidThird)));

        ProductionReq productionReq = CopyBeanUtil.copy(summaryReq, ProductionReq.class);
        if (Objects.nonNull(req.getWriteOffStartDate()) || Objects.nonNull(req.getWriteOffEndDate())) {
            List<OrderProductionAppointmentOptLogEntity> optLogEntities = productionAppointmentOptLogRepository.queryByWriteOffDate(req.getWriteOffStartDate(), req.getWriteOffEndDate());
            List<Long> appointmentIds = optLogEntities.stream()
                    .map(OrderProductionAppointmentOptLogEntity::getAppointmentId)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(appointmentIds)) {
                appointmentIds.add(-1L);
            }
            productionReq.setAppointmentIds(appointmentIds);
        }

        // 礼赠
        BigDecimal doneGiftRealPaid = orderProductionExtendMapper.getGiftRealPaid(productionReq);
        // 单项
        BigDecimal doneProductionRealPaid = orderProductionExtendMapper.getProductionRealPaid(productionReq);
        // 通次卡
        BigDecimal doneProductionCardRealPaid = orderProductionExtendMapper.getProductionCardRealPaid(productionReq);
        log.info("已核销金额(自营)：礼赠累计：{}，单项累计：{}，通次卡累计：{}", doneGiftRealPaid, doneProductionRealPaid, doneProductionCardRealPaid);

        // 礼赠(三方)
        BigDecimal doneGiftRealPaidThird = orderProductionExtendMapper.getGiftRealPaidThird(productionReq);
        // 单项(三方)
        BigDecimal doneProductionRealPaidThird = orderProductionExtendMapper.getProductionRealPaidThird(productionReq);
        // 通次卡(三方)
        BigDecimal doneProductionCardRealPaidThird = orderProductionExtendMapper.getProductionCardRealPaidThird(productionReq);
        log.info("已核销金额(三方)：礼赠累计：{}，单项累计：{}，通次卡累计：{}", doneGiftRealPaidThird, doneProductionRealPaidThird, doneProductionCardRealPaidThird);


        BigDecimal donePaidSelf = BigDecimalUtil.add(doneGiftRealPaid, doneProductionRealPaid, doneProductionCardRealPaid);
        BigDecimal donePaidThird = BigDecimalUtil.add(doneGiftRealPaidThird, doneProductionRealPaidThird, doneProductionCardRealPaidThird);

        // 核销金额(自营)
        re.setWriteOffPaidSelf(AmountChangeUtil.format2Y(donePaidSelf));
        // 核销金额(三方)
        re.setWriteOffPaidThird(AmountChangeUtil.format2Y(donePaidThird));
        // 核销金额
        re.setWriteOffPaid(AmountChangeUtil.format2Y(BigDecimalUtil.add(donePaidSelf, donePaidThird)));

        // 未核销金额
        BigDecimal unwrittenSummary = orderProductionExtendMapper.getUnwrittenSummary(summaryReq);
        re.setUnwrittenSummaryPaid(
                AmountChangeUtil.changeF2Y(
                        String.valueOf(BigDecimalUtil.multiplication(
                                unwrittenSummary,
                                BigDecimal.valueOf(100)
                        ).intValue())
                )
        );
        return Result.success(re.adjust());
    }

    /**
     * 产康师实付导出
     */
    @Override
    public void exportTherapistRealPaid(ProductionRealPaidReq req, HttpServletResponse response) {
        req.setPageNum(1);
        req.setPageSize(10000);
        Result<PageVO<ProductionTherapistRealPaidVO>> pageResult = this.queryTherapistRealPaid(req);

        PageVO<ProductionTherapistRealPaidVO> data = pageResult.getData();
        List<ProductionTherapistRealPaidVO> list = data.getList();

        try {
            Date writeOffStartDate = req.getWriteOffStartDate();
            Date writeOffEndDate = req.getWriteOffEndDate();

            List<TherapistRealPaidExportDTO> copy = CopyBeanUtil.copy(list, TherapistRealPaidExportDTO.class);
            ExcelUtils.exportExcel(
                    response,
                    "产康核销业绩" + DateUtils.format(writeOffStartDate, YYYY_MM_DD) + "-" + DateUtils.format(writeOffEndDate, YYYY_MM_DD),
                    copy,
                    TherapistRealPaidExportDTO.class
            );
        } catch (IOException e) {
            throw new ApplicationException(ErrorCodeEnum.SYSTEM_ERROR.code(), "导出失败");
        }
    }

    /**
     * 产康师实付导出 全部门店
     */
    @Override
    public void exportTherapistRealPaidSummary(ProductionRealPaidReq req, HttpServletResponse response) {
        // 获取所有月子门店
        List<CfgStorePO> stores = cfgStoreMapper.selectList(
                new LambdaQueryWrapper<CfgStorePO>()
                        .in(CfgStorePO::getType, Arrays.asList(0, 1, 100))
                        .notIn(CfgStorePO::getStoreId, Arrays.asList(1011, 1005))
                        .eq(CfgStorePO::getActive, 1)
                        .orderByAsc(CfgStorePO::getStoreName)
        );

        Date writeOffStartDate = req.getWriteOffStartDate();
        Date writeOffEndDate = req.getWriteOffEndDate();

        List<TherapistRealPaidExportSummaryDTO> re = Lists.newArrayList();
        stores.forEach(store -> {
            req.setPageNum(1);
            req.setPageSize(10000);
            req.setStoreId(store.getStoreId());
            Result<PageVO<ProductionTherapistRealPaidVO>> pageResult = this.queryTherapistRealPaid(req);

            PageVO<ProductionTherapistRealPaidVO> data = pageResult.getData();
            List<ProductionTherapistRealPaidVO> list = data.getList();

            List<TherapistRealPaidExportSummaryDTO> copy = CopyBeanUtil.copy(list, TherapistRealPaidExportSummaryDTO.class);
            copy.forEach(item -> item.setStoreName(store.getStoreName()));
            re.addAll(copy);
        });

        try {
            ExcelUtils.exportExcel(
                    response,
                    "产康师核销业绩" + DateUtils.format(writeOffStartDate, YYYY_MM_DD) + "-" + DateUtils.format(writeOffEndDate, YYYY_MM_DD),
                    re,
                    TherapistRealPaidExportSummaryDTO.class
            );
        } catch (IOException e) {
            throw new ApplicationException(ErrorCodeEnum.SYSTEM_ERROR.code(), "导出失败");
        }
    }

    /**
     * 批量导出客户实付
     */
    @Override
    public String batchExportClientRealPaid(BatchProductionExportReq req) {
        List<Integer> storeIds = req.getStoreIds();
        List<CfgStorePO> stores = cfgStoreMapper.selectList(new LambdaQueryWrapper<CfgStorePO>().in(CfgStorePO::getStoreId, storeIds));

        LoginUserDTO loginUserDTO = req.getLoginUserDTO();
        DdEmployeePO employee = ddEmployeeService.getById(loginUserDTO.getUserId());
        if (Objects.isNull(employee) || StringUtil.isBlank(employee.getEmail())) {
            throw new BusinessException(ResultEnum.PARAM_ERROR, "操作失败:当前操作人的邮箱信息为空");
        }

        ThreadUtil.execute(() -> {
            try {
                List<RealPaidClientUploadReq> re = Lists.newArrayList();
                stores.forEach(store -> {
                    int pageNum = 1;
                    int totalNum = -1;

                    ProductionRealPaidReq productionRealPaidReq = new ProductionRealPaidReq();
                    productionRealPaidReq.setStoreId(store.getStoreId());
                    productionRealPaidReq.setWriteOffStartDate(req.getWriteOffStartDate());
                    productionRealPaidReq.setWriteOffEndDate(req.getWriteOffEndDate());
                    productionRealPaidReq.setPageSize(100);

                    while (pageNum <= totalNum || totalNum == -1) {
                        productionRealPaidReq.setPageNum(pageNum++);
                        Result<PageVO<ProductionClientRealPaidVO>> pageVOResult = this.queryClientRealPaid(productionRealPaidReq);
                        PageVO<ProductionClientRealPaidVO> pageData = pageVOResult.getData();
                        if (totalNum == -1) {
                            totalNum = pageData.getTotalPage();
                        }
                        List<ProductionClientRealPaidVO> realPaidList = pageData.getList();

                        realPaidList.removeIf(o -> {
                            // 如果三个值都为0 则移除调
                            String writeOffPaid = o.getWriteOffPaid();
                            String writeOffSummaryPaid = o.getWriteOffSummaryPaid();
                            String unwrittenSummaryPaid = o.getUnwrittenSummaryPaid();
                            return (new BigDecimal(writeOffPaid).compareTo(BigDecimal.ZERO) == 0)
                                    && (new BigDecimal(writeOffSummaryPaid).compareTo(BigDecimal.ZERO) == 0)
                                    && (new BigDecimal(unwrittenSummaryPaid).compareTo(BigDecimal.ZERO) == 0);
                        });

                        List<RealPaidClientUploadReq> copy = CopyBeanUtil.copy(realPaidList, RealPaidClientUploadReq.class);
                        copy.forEach(o -> o.setStoreName(store.getStoreName()));

                        re.addAll(copy);
                    }
                });

                String start = format(req.getWriteOffStartDate(), "yyyy-MM-dd");
                String end = format(req.getWriteOffEndDate(), "yyyy-MM-dd");

                String uploadResult = baseClient.uploadClientRealPaidUpload(
                        start,
                        end,
                        re
                );

                Map<String, String> contextData = new HashMap<>();
                contextData.put("fileName", "门店客户核销金额_" + start + "至" + end + ".xlsx");
                contextData.put("fileUrl", uploadResult);
                contextData.put("startDate", start);
                contextData.put("endDate", end);

                SceneTriggerReq sceneTriggerReq = new SceneTriggerReq();
                sceneTriggerReq.setSceneId(100195L);
                sceneTriggerReq.setRequestId(IdWorker.get32UUID());
                sceneTriggerReq.setTargetList(Collections.singletonList(employee.getEmail()));
                sceneTriggerReq.setContextData(contextData);
                messageClient.triggerScene(sceneTriggerReq);
            } catch (Exception e) {
                log.error("batchExportClientRealPaid error", e);
            }
        });
        return employee.getEmail();
    }

    /**
     * 批量导出汇总实付
     */
    public String batchExportSummaryRealPaid(BatchProductionExportReq req) {
        List<Integer> storeIds = req.getStoreIds();
        List<CfgStorePO> stores = cfgStoreMapper.selectList(new LambdaQueryWrapper<CfgStorePO>().in(CfgStorePO::getStoreId, storeIds));

        LoginUserDTO loginUserDTO = req.getLoginUserDTO();
        DdEmployeePO employee = ddEmployeeService.getById(loginUserDTO.getUserId());
        if (Objects.isNull(employee) || StringUtil.isBlank(employee.getEmail())) {
            throw new BusinessException(ResultEnum.PARAM_ERROR, "操作失败:当前操作人的邮箱信息为空");
        }

        ThreadUtil.execute(() -> {
            try {
                List<RealPaidSummaryUploadReq> re = Lists.newArrayList();
                stores.forEach(store -> {
                    ProductionRealPaidReq productionRealPaidReq = new ProductionRealPaidReq();
                    productionRealPaidReq.setStoreId(store.getStoreId());
                    productionRealPaidReq.setWriteOffStartDate(req.getWriteOffStartDate());
                    productionRealPaidReq.setWriteOffEndDate(req.getWriteOffEndDate());

                    Result<ProductionRealPaidSummaryVO> productionRealPaidSummaryVOResult = this.queryRealPaidSummary(productionRealPaidReq);

                    ProductionRealPaidSummaryVO data = productionRealPaidSummaryVOResult.getData();
                    re.add(CopyBeanUtil.copy(data, RealPaidSummaryUploadReq.class));
                });

                String start = format(req.getWriteOffStartDate(), "yyyy-MM-dd");
                String end = format(req.getWriteOffEndDate(), "yyyy-MM-dd");

                String uploadResult = baseClient.uploadRealPaidSummaryUpload(
                        start,
                        end,
                        re
                );

                Map<String, String> contextData = new HashMap<>();
                contextData.put("fileName", "门店汇总核销金额_" + start + "至" + end + ".xlsx");
                contextData.put("fileUrl", uploadResult);
                contextData.put("startDate", start);
                contextData.put("endDate", end);

                SceneTriggerReq sceneTriggerReq = new SceneTriggerReq();
                sceneTriggerReq.setSceneId(100193L);
                sceneTriggerReq.setRequestId(IdWorker.get32UUID());
                sceneTriggerReq.setTargetList(Collections.singletonList(employee.getEmail()));
                sceneTriggerReq.setContextData(contextData);
                messageClient.triggerScene(sceneTriggerReq);
            } catch (Exception e) {
                log.error("batchExportSummaryRealPaid error", e);
            }
        });
        return employee.getEmail();
    }

    /**
     * 批量导出产康师实付
     */
    public String batchExportTherapistRealPaid(BatchProductionExportReq req) {
        List<Integer> storeIds = req.getStoreIds();
        List<CfgStorePO> stores = cfgStoreMapper.selectList(new LambdaQueryWrapper<CfgStorePO>().in(CfgStorePO::getStoreId, storeIds));

        String email = req.getEmail();
        if (StringUtil.isEmpty(email)) {
            LoginUserDTO loginUserDTO = req.getLoginUserDTO();
            DdEmployeePO employee = ddEmployeeService.getById(loginUserDTO.getUserId());
            if (Objects.isNull(employee) || StringUtil.isBlank(employee.getEmail())) {
                throw new BusinessException(ResultEnum.PARAM_ERROR, "操作失败:当前操作人的邮箱信息为空");
            }
            email = employee.getEmail();
        }

        String finalEmail = email;
        ThreadUtil.execute(() -> {
            try {
                List<RealPaidUploadReq> re = Lists.newArrayList();
                stores.forEach(store -> {
                    ProductionRealPaidReq productionRealPaidReq = new ProductionRealPaidReq();
                    productionRealPaidReq.setStoreId(store.getStoreId());
                    productionRealPaidReq.setWriteOffStartDate(req.getWriteOffStartDate());
                    productionRealPaidReq.setWriteOffEndDate(req.getWriteOffEndDate());
                    productionRealPaidReq.setPageNum(1);
                    productionRealPaidReq.setPageSize(1000);

                    Result<PageVO<ProductionTherapistRealPaidVO>> pageResult = this.queryTherapistRealPaid(productionRealPaidReq);

                    PageVO<ProductionTherapistRealPaidVO> pageData = pageResult.getData();
                    List<ProductionTherapistRealPaidVO> list = pageData.getList();

                    List<RealPaidUploadReq> copy = CopyBeanUtil.copy(list, RealPaidUploadReq.class);
                    copy.forEach(o -> o.setStoreName(store.getStoreName()));
                    re.addAll(copy);
                });

                String start = format(req.getWriteOffStartDate(), "yyyy-MM-dd");
                String end = format(req.getWriteOffEndDate(), "yyyy-MM-dd");

                String uploadResult = baseClient.uploadRealPaidUpload(
                        start,
                        end,
                        re
                );

                Map<String, String> contextData = new HashMap<>();
                contextData.put("fileName", "产康师核销金额_" + start + "至" + end + ".xlsx");
                contextData.put("fileUrl", uploadResult);
                contextData.put("startDate", start);
                contextData.put("endDate", end);

                SceneTriggerReq sceneTriggerReq = new SceneTriggerReq();
                sceneTriggerReq.setSceneId(100194L);
                sceneTriggerReq.setRequestId(IdWorker.get32UUID());
                sceneTriggerReq.setTargetList(Collections.singletonList(finalEmail));
                sceneTriggerReq.setContextData(contextData);
                messageClient.triggerScene(sceneTriggerReq);
            } catch (Exception e) {
                log.error("batchExportTherapistRealPaid error", e);
            }
        });
        return email;
    }

    /**
     * 查询产康师实付
     */
    @Override
    public Result<PageVO<ProductionTherapistRealPaidVO>> queryTherapistRealPaid(ProductionRealPaidReq req) {
        List<TherapistStore> therapistStores = therapistStoreMapper.selectList(
                new LambdaQueryWrapper<TherapistStore>()
                        .eq(TherapistStore::getStoreId, req.getStoreId())
                        .eq(TherapistStore::getBindType, 1)
                        .eq(TherapistStore::getDeleted, 0)
                        .select(TherapistStore::getTherapistId)
        );

        Set<Long> therapistIds = therapistStores.stream().map(TherapistStore::getTherapistId).collect(Collectors.toSet());

        // 查询门店该门店核销过的产康师
        List<Long> writeOffAppointmentIds = Lists.newArrayList();
        if (Objects.nonNull(req.getWriteOffStartDate()) || Objects.nonNull(req.getWriteOffEndDate())) {
            List<OrderProductionAppointmentOptLogEntity> optLogEntities = productionAppointmentOptLogRepository.queryByWriteOffDate(req.getWriteOffStartDate(), req.getWriteOffEndDate());
            writeOffAppointmentIds = optLogEntities.stream()
                    .map(OrderProductionAppointmentOptLogEntity::getAppointmentId)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(writeOffAppointmentIds)) {
                writeOffAppointmentIds.add(-1L);
            } else {
                Set<Long> tmpTherapistIds = orderProductionAppointmentMapper.selectList(
                        new LambdaQueryWrapper<OrderProductionAppointment>()
                                .eq(OrderProductionAppointment::getStoreId, req.getStoreId())
                                .in(OrderProductionAppointment::getId, writeOffAppointmentIds)
                                .select(OrderProductionAppointment::getTherapistId)
                ).stream().map(OrderProductionAppointment::getTherapistId).collect(Collectors.toSet());

                therapistIds.addAll(tmpTherapistIds);
            }
        }

        if (CollectionUtil.isEmpty(therapistIds)) {
            return Result.success(new PageVO<>(Lists.newArrayList(), 0, req.getPageSize(), req.getPageNum()));
        }

        Page<Therapist> therapistPage = therapistMapper.selectPage(
                new Page<>(req.getPageNum(), req.getPageSize()),
                new LambdaQueryWrapper<Therapist>()
                        .in(Therapist::getId, therapistIds)
                        .eq(Therapist::getNatureType, 0) // 只获取自营三方产康师
                        .and(StringUtil.isNotEmpty(req.getKeyword()),
                                queryWrapper -> queryWrapper.like(Therapist::getName, req.getKeyword())
                                        .or()
                                        .like(Therapist::getMobile, req.getKeyword())
                        )
                        .eq(Therapist::getDeleted, 0)
                        .orderByAsc(Therapist::getState)
                        .orderByDesc(Therapist::getGmtCreate)
        );
        List<Therapist> records = therapistPage.getRecords();
        if (CollectionUtil.isEmpty(records)) {
            return Result.success(new PageVO<>(Lists.newArrayList(), 0, req.getPageSize(), req.getPageNum()));
        }

        ProductionReq summaryReq = CopyBeanUtil.copy(req, ProductionReq.class);
        summaryReq.setProductionGoodsIds(bizConfig.getAppointmentJudgeSpuIds());
        summaryReq.setOrderCreatedAt(bizConfig.getTransJudgeDate().getTime() / 1000);
        summaryReq.setVerificationState(VerificationStatusEnum.DONE_WRITE_OFF.code());
        summaryReq.setTherapistIds(records.stream().map(Therapist::getId).collect(Collectors.toList()));

        if (Objects.nonNull(req.getWriteOffStartDate()) || Objects.nonNull(req.getWriteOffEndDate())) {
            summaryReq.setAppointmentIds(writeOffAppointmentIds);

            summaryReq.setWriteOffStartDate(req.getWriteOffStartDate());
            summaryReq.setWriteOffEndDate(req.getWriteOffEndDate());
        }

        List<ProductionTherapistRealPaidModel> therapistRealPaid = orderProductionExtendMapper.getTherapistRealPaid(summaryReq);

        // 三方核销
        List<String> therapistMobiles = records.stream().map(Therapist::getMobile).filter(StringUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<UserEntity> userEntities = userRepository.listByPhones(therapistMobiles);
        List<OrderProductionAppointmentEntity> therapistRealPaidThirds;

        if (CollectionUtil.isNotEmpty(userEntities)) {
            summaryReq.setUserIds(userEntities.stream().map(UserEntity::getId).collect(Collectors.toList()));
            therapistRealPaidThirds = orderProductionExtendMapper.getTherapistRealPaidThird(summaryReq);
        } else {
            therapistRealPaidThirds = Lists.newArrayList();
        }

        Map<String, BigDecimal> realPaidMap = this.getRealPaidMap(therapistRealPaidThirds);

        List<ProductionTherapistRealPaidVO> collect = records.stream().map(o -> {
            ProductionTherapistRealPaidVO vo = new ProductionTherapistRealPaidVO();
            vo.setId(o.getId());
            vo.setMobile(o.getMobile());
            vo.setName(o.getName());
            vo.setGrade(o.getGrade());
            vo.setGradeTitle(TherapistGradeEnum.fromCode(o.getGrade()));
            vo.setNatureType(o.getNatureType());
            vo.setNatureTypeTitle(TherapistTypeEnum.fromCode(o.getNatureType()));
            vo.setState(o.getState());
            vo.setStateTitle(TherapistStateEnum.fromCode(o.getState()));

            Optional<ProductionTherapistRealPaidModel> realPaidOpt = therapistRealPaid.stream()
                    .filter(t -> Objects.equals(t.getTherapistId(), o.getId()))
                    .findFirst();

            // 获取三方核销金额
            Optional<UserEntity> usrOpt = userEntities.stream().filter(usr -> Objects.equals(usr.getPhone(), o.getMobile())).findFirst();
            // 获取当前产康师下单的产康订单-预约单
            List<OrderProductionAppointmentEntity> thirdTmpAppointments = usrOpt.map(userEntity -> therapistRealPaidThirds.stream()
                    .filter(app -> Objects.equals(app.getCreateBy(), userEntity.getId()))
                    .collect(Collectors.toList())).orElseGet(Lists::newArrayList);

            BigDecimal realPaidThird = BigDecimal.ZERO;
            if (CollectionUtil.isNotEmpty(thirdTmpAppointments)) {
                for (OrderProductionAppointmentEntity thirdTmpAppointment : thirdTmpAppointments) {
                    BigDecimal bigDecimal = realPaidMap.get(thirdTmpAppointment.getOrderSn());
                    realPaidThird = BigDecimalUtil.add(realPaidThird, Objects.isNull(bigDecimal) ? BigDecimal.ZERO : bigDecimal);
                }
            }

            BigDecimal realPaidSelf = realPaidOpt.isPresent() ? realPaidOpt.get().getVal() : BigDecimal.ZERO;
            // 自营核销金额
            vo.setRealPaidSelf(AmountChangeUtil.format2Y(realPaidSelf));
            // 三方核销金额
            vo.setRealPaidThird(AmountChangeUtil.format2Y(realPaidThird));
            // 核销金额
            vo.setRealPaid(AmountChangeUtil.format2Y(BigDecimalUtil.add(realPaidSelf, realPaidThird)));
            return vo;
        }).collect(Collectors.toList());

        collect.forEach(ProductionTherapistRealPaidVO::adjust);

        return Result.success(new PageVO<>(collect, (int) therapistPage.getTotal(), (int) therapistPage.getSize(), (int) therapistPage.getCurrent()));
    }

    /**
     * 查询客户核销金额
     */
    @Override
    public Result<PageVO<ProductionClientRealPaidVO>> queryClientRealPaid(ProductionRealPaidReq req) {
        Page<TabClientPO> page = tabClientMapper.selectPage(
                new Page<>(req.getPageNum(), req.getPageSize()),
                new LambdaQueryWrapper<TabClientPO>()
                        .eq(TabClientPO::getStoreId, req.getStoreId())
                        .and(
                                StringUtil.isNotBlank(req.getKeyword()),
                                wrapper -> wrapper.like(TabClientPO::getName, req.getKeyword())
                                        .or()
                                        .like(TabClientPO::getPhone, req.getKeyword())
                        )
                        .orderByDesc(TabClientPO::getRecordTime)
        );

        List<TabClientPO> records = page.getRecords();
        if (CollectionUtil.isEmpty(records)) return Result.success(new PageVO<>(Lists.newArrayList(), (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent()));

        List<Integer> clientIds = records.stream().map(TabClientPO::getId).collect(Collectors.toList());
        List<Integer> basicIds = records.stream().map(TabClientPO::getBasicUid).collect(Collectors.toList());

        ProductionReq productionReq = CopyBeanUtil.copy(req, ProductionReq.class);
        productionReq.setStoreId(req.getStoreId());
        productionReq.setProductionGoodsIds(bizConfig.getAppointmentJudgeSpuIds());
        productionReq.setOrderCreatedAt(bizConfig.getTransJudgeDate().getTime() / 1000);
        productionReq.setVerificationState(VerificationStatusEnum.DONE_WRITE_OFF.code());
        productionReq.setClientIds(clientIds);

        // 客户累计已核销
        List<ProductionClientRealPaidModel> clientSummaryRealPaid = orderProductionExtendMapper.getClientRealPaid(productionReq);

        // 客户本月已核销
        if (Objects.nonNull(req.getWriteOffStartDate()) || Objects.nonNull(req.getWriteOffEndDate())) {
            List<OrderProductionAppointmentOptLogEntity> optLogEntities = productionAppointmentOptLogRepository.queryByWriteOffDate(req.getWriteOffStartDate(), req.getWriteOffEndDate());
            List<Long> appointmentIds = optLogEntities.stream()
                    .map(OrderProductionAppointmentOptLogEntity::getAppointmentId)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(appointmentIds)) {
                appointmentIds.add(-1L);
            }
            productionReq.setAppointmentIds(appointmentIds);
        }
        List<ProductionClientRealPaidModel> clientRealPaid = orderProductionExtendMapper.getClientRealPaid(productionReq);

        // 客户核销金额(三方)
        productionReq.setWriteOffStartDate(null);
        productionReq.setWriteOffEndDate(null);
        List<OrderProductionAppointmentEntity> clientRealPaidThird = orderProductionExtendMapper.getClientRealPaidThird(productionReq);
        Map<String, BigDecimal> realPaidMap = this.getRealPaidMap(clientRealPaidThird);

        // 客户未核销金额
        productionReq.setBasicIds(basicIds);
        List<ProductionClientRealPaidModel> clientUnwrittenSummary = orderProductionExtendMapper.getClientUnwrittenSummary(productionReq);

        List<Long> appointmentIds = productionReq.getAppointmentIds();

        List<ProductionClientRealPaidVO> re = records.stream().map(r -> {
            Integer clientId = r.getId();
            Integer basicId = r.getBasicUid();

            ProductionClientRealPaidVO vo = new ProductionClientRealPaidVO();
            vo.setClientId(clientId);
            vo.setBasicId(basicId);
            vo.setMobile(r.getPhone());
            vo.setName(r.getName());

            // 客户累计已核销(三方)
            List<OrderProductionAppointmentEntity> clientThirdAppointments = clientRealPaidThird.stream()
                    .filter(client -> Objects.equals(client.getClientId(), clientId))
                    .collect(Collectors.toList());
            BigDecimal realPaidThird = BigDecimal.ZERO;
            BigDecimal realPaidMonthThird = BigDecimal.ZERO;
            if (CollectionUtil.isNotEmpty(clientThirdAppointments)) {
                for (OrderProductionAppointmentEntity thirdTmpAppointment : clientThirdAppointments) {
                    BigDecimal bigDecimal = realPaidMap.get(thirdTmpAppointment.getOrderSn());
                    bigDecimal = Objects.isNull(bigDecimal) ? BigDecimal.ZERO : bigDecimal;
                    realPaidThird = BigDecimalUtil.add(realPaidThird, bigDecimal);
                    if (CollectionUtil.isNotEmpty(appointmentIds) && appointmentIds.contains(thirdTmpAppointment.getId())) {
                        realPaidMonthThird = BigDecimalUtil.add(realPaidMonthThird, bigDecimal);
                    }
                }
            }

            // 客户累计已核销
            Optional<ProductionClientRealPaidModel> summaryOpt = clientSummaryRealPaid.stream().filter(o -> Objects.equals(o.getClientId(), clientId)).findFirst();
            vo.setWriteOffSummaryPaid(
                    AmountChangeUtil.changeF2Y(
                            String.valueOf(BigDecimalUtil.multiplication(
                                    BigDecimalUtil.add(summaryOpt.isPresent() ? summaryOpt.get().getVal() : BigDecimal.ZERO, realPaidThird),
                                    BigDecimal.valueOf(100)
                            ).intValue())
                    )
            );

            // 客户本月已核销
            Optional<ProductionClientRealPaidModel> paidOpt = clientRealPaid.stream().filter(o -> Objects.equals(o.getClientId(), clientId)).findFirst();
            vo.setWriteOffPaid(
                    AmountChangeUtil.changeF2Y(
                            String.valueOf(BigDecimalUtil.multiplication(
                                    BigDecimalUtil.add(paidOpt.isPresent() ? paidOpt.get().getVal() : BigDecimal.ZERO, realPaidMonthThird),
                                    BigDecimal.valueOf(100)
                            ).intValue())
                    )
            );

            Optional<ProductionClientRealPaidModel> unwrittenSummaryOpt = clientUnwrittenSummary.stream().filter(o -> Objects.equals(o.getClientId(), basicId)).findFirst();
            if (unwrittenSummaryOpt.isPresent()) {
                ProductionClientRealPaidModel productionClientRealPaidModel = unwrittenSummaryOpt.get();
                vo.setUnwrittenSummaryPaid(
                        AmountChangeUtil.changeF2Y(
                                String.valueOf(BigDecimalUtil.multiplication(
                                        BigDecimalUtil.add(productionClientRealPaidModel.getVal(), BigDecimal.valueOf(0)),
                                        BigDecimal.valueOf(100)
                                ).intValue())
                        )
                );
            } else {
                vo.setUnwrittenSummaryPaid("0.00");
            }
            return vo;
        }).collect(Collectors.toList());

        re.forEach(ProductionClientRealPaidVO::adjust);

        return Result.success(new PageVO<>(re, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent()));
    }

    /**
     * 查询预约单核销金额
     */
    @Override
    public Result<PageVO<ProductionAppointmentRealPaidVO>> queryAppointmentRealPaid(ProductionRealPaidReq req) {
        BPCheckUtil.checkEmptyInBean(new String[]{"therapistId", "realPaidServeType"}, req, false);

        ProductionAppointmentBackQuery backQuery = new ProductionAppointmentBackQuery();
        com.stbella.core.base.Operator operator = new com.stbella.core.base.Operator();
        operator.setOperatorGuid("-1");
        operator.setOperatorName("queryAppointmentRealPaid");
        backQuery.setOperator(operator);
        backQuery.setPageSize(req.getPageSize());
        backQuery.setPageNum(req.getPageNum());
        backQuery.setStoreAuth(false);

        if (Objects.equals(req.getRealPaidServeType(), ProductionServeTypeEnum.APPOINTMENT_TYPE_TRIPARTITE.code())) {
            // 三方详情
            Therapist therapist = therapistMapper.selectById(req.getTherapistId());
            if (Objects.isNull(therapist)) {
                return Result.success(new PageVO<>(Lists.newArrayList(), 0, req.getPageSize(), req.getPageNum()));
            }

            UserEntity userEntity = userRepository.queryByPhone(therapist.getMobile());
            if (Objects.isNull(userEntity)) {
                log.info("queryAppointmentRealPaid no userEntity found, therapistId:{}", req.getTherapistId());
                return Result.success(new PageVO<>(Lists.newArrayList(), 0, req.getPageSize(), req.getPageNum()));
            }

            ProductionReq productionReq = new ProductionReq();
            productionReq.setStoreId(req.getStoreId());
            productionReq.setUserIds(Collections.singletonList(userEntity.getId()));
            productionReq.setWriteOffStartDate(req.getWriteOffStartDate());
            productionReq.setWriteOffEndDate(req.getWriteOffEndDate());
            List<OrderProductionAppointmentEntity> therapistRealPaidThird = orderProductionExtendMapper.getTherapistRealPaidThird(productionReq);

            if (CollectionUtil.isEmpty(therapistRealPaidThird)) {
                return Result.success(new PageVO<>(Lists.newArrayList(), 0, req.getPageSize(), req.getPageNum()));
            }
            backQuery.setAppointmentIds(therapistRealPaidThird.stream().map(OrderProductionAppointmentEntity::getId).collect(Collectors.toList()));
            backQuery.setServeType(ProductionServeTypeEnum.APPOINTMENT_TYPE_TRIPARTITE.code());
            backQuery.setKeyword(req.getKeyword());
        } else {
            backQuery.setStoreId(req.getStoreId());
            backQuery.setKeyword(req.getKeyword());
            backQuery.setWriteOffStart(req.getWriteOffStartDate());
            backQuery.setWriteOffEnd(req.getWriteOffEndDate());
            backQuery.setTherapistId(req.getTherapistId());
            backQuery.setServeType(ProductionServeTypeEnum.APPOINTMENT_TYPE_PROPRIETARY.code());
        }

        Result<PageVO<ProductionAppointmentBackVo>> page = this.queryAppointmentBackPage(backQuery);

        PageVO<ProductionAppointmentBackVo> data = page.getData();
        List<ProductionAppointmentBackVo> list = data.getList();
        if (CollectionUtil.isEmpty(list)) {
            return Result.success(new PageVO<>(Lists.newArrayList(), data.getTotalCount(), data.getPageSize(), data.getPageNo()));
        }

        // 填充实付金额
        List<String> appointmentOrderSns = list.stream().map(ProductionAppointmentBackVo::getOrderSn).collect(Collectors.toList());
        List<OrderProductionAppointmentEntity> orderProductionAppointmentEntities = appointmentRepository.listByOrderSn(appointmentOrderSns);
        Map<String, BigDecimal> realPaidMap = this.getRealPaidMap(orderProductionAppointmentEntities);
        List<ProductionJudgeVO> judges = this.judgeAppointment(orderProductionAppointmentEntities);

        List<ProductionAppointmentRealPaidVO> re = list.stream().map(o -> {
            ProductionAppointmentRealPaidVO vo = CopyBeanUtil.copy(o, ProductionAppointmentRealPaidVO.class);

            BigDecimal realPaid = realPaidMap.get(o.getOrderSn());
            if (Objects.isNull(realPaid) || realPaid.compareTo(BigDecimal.ZERO) < 0) {
                realPaid = BigDecimal.ZERO;
            }
            BigDecimal multiplication = com.stbella.core.utils.BigDecimalUtil.multiplication(realPaid, BigDecimal.valueOf(100));
            vo.setRealPaidAmount(AmountChangeUtil.changeF2Y(String.valueOf(multiplication.intValue())));

            Optional<ProductionJudgeVO> judgeOpt = judges.stream().filter(j -> Objects.equals(o.getOrderSn(), j.getAppointmentSn())).findFirst();
            if (judgeOpt.isPresent()) {
                vo.setSettleFlg(judgeOpt.get().isHit() ? 0 : 1);
            } else {
                vo.setSettleFlg(1);
            }
            return vo;
        }).collect(Collectors.toList());

        re.forEach(ProductionAppointmentRealPaidVO::adjust);

        return Result.success(new PageVO<>(re, data.getTotalCount(), data.getPageSize(), data.getPageNo()));
    }

    /**
     * 导出核销金额汇总
     */
    @Override
    public void exportTherapistSummary(ExportProductionRealPaidReq req, HttpServletResponse response) {
        // 获取所有月子门店
        List<CfgStorePO> stores = cfgStoreMapper.selectList(
                new LambdaQueryWrapper<CfgStorePO>()
                        .in(CfgStorePO::getType, Arrays.asList(0, 1, 100))
                        .notIn(CfgStorePO::getStoreId, Arrays.asList(1011, 1005))
                        .eq(CfgStorePO::getActive, 1)
                        .orderByAsc(CfgStorePO::getStoreName)
        );
        ProductionRealPaidReq realPaidReq = new ProductionRealPaidReq();
        realPaidReq.setWriteOffStartDate(req.getWriteOffStartDate());
        realPaidReq.setWriteOffEndDate(req.getWriteOffEndDate());
        realPaidReq.setPageNum(1);
        realPaidReq.setPageSize(100);

        List<TherapistRealPaidSummaryExportDTO> dtoList = Lists.newArrayList();

        stores.parallelStream().forEach(store -> {
            realPaidReq.setStoreId(store.getStoreId());
            Result<ProductionRealPaidSummaryVO> result = this.queryRealPaidSummary(realPaidReq);

            ProductionRealPaidSummaryVO vo = result.getData();
            TherapistRealPaidSummaryExportDTO dto = new TherapistRealPaidSummaryExportDTO();
            dto.setStoreName(store.getStoreName());
            dto.setWriteOffPaid(vo.getWriteOffPaid());
            dto.setWriteOffSummaryPaid(vo.getWriteOffSummaryPaid());
            dto.setUnwrittenSummaryPaid(vo.getUnwrittenSummaryPaid());
            dtoList.add(dto);
        });

        try {
            ExcelUtils.exportExcel(
                    response,
                    "产康核销业绩" + DateUtils.format(req.getWriteOffStartDate(), YYYY_MM_DD) + "-" + DateUtils.format(req.getWriteOffEndDate(), YYYY_MM_DD),
                    dtoList,
                    TherapistRealPaidSummaryExportDTO.class
            );
        } catch (IOException e) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, "导出失败,请联系创新中心处理");
        }
    }

    private Map<String, BigDecimal> getRealPaidMap(List<OrderProductionAppointmentEntity> appointmentEntities) {
        if (CollectionUtil.isEmpty(appointmentEntities)) return Maps.newHashMap();

        List<Integer> giftIds = Lists.newArrayList();
        List<Integer> orderProductionCardIds = Lists.newArrayList();

        appointmentEntities.forEach(app -> {
            // 订单类型 1: 购买 2：额外礼赠
            Integer orderProductionType = app.getOrderProductionType();
            Integer orderProductionCardId = app.getOrderProductionCardId();

            OrderProductionTypeEnum orderProductionTypeEnum = OrderProductionTypeEnum.of(orderProductionType);
            switch (orderProductionTypeEnum) {
                case GIFT:
                    giftIds.add(orderProductionCardId);
                    break;
                case BUY:
                    orderProductionCardIds.add(orderProductionCardId);
                    break;
                default:
                    log.info("订单类型设置错误:{}", app.getOrderSn());
            }
        });

        // 礼赠
        List<OrderGiftExtendEntity> orderGiftExtendEntities = orderGiftExtendRepository.listByGiftIds(giftIds);

        // extend
        List<OrderProductionExtendEntity> orderProductionExtendEntities = orderProductionExtendRepository.listByExtendIds(orderProductionCardIds);

        // 获取通卡次卡
        Set<Integer> tmpExtendIds = orderProductionExtendEntities.stream()
                .filter(o -> Objects.equals(o.getType(), OrderProductionItemEnum.TYPE_COUNT.code()) || Objects.equals(o.getType(), OrderProductionItemEnum.TYPE_GROUP.code()))
                .map(OrderProductionExtendEntity::getId)
                .collect(Collectors.toSet());
        List<OrderProductionCardExtendEntity> orderProductionCardExtendEntities = orderProductionCardExtendRepository.queryByProductionIdList(tmpExtendIds);

        Map<String, BigDecimal> re = new HashMap<>();

        appointmentEntities.forEach(app -> {
            // 订单类型 1: 购买 2：额外礼赠
            Integer orderProductionType = app.getOrderProductionType();
            Integer orderProductionCardId = app.getOrderProductionCardId();

            OrderProductionTypeEnum orderProductionTypeEnum = OrderProductionTypeEnum.of(orderProductionType);
            switch (orderProductionTypeEnum) {
                case GIFT:
                    Optional<OrderGiftExtendEntity> giftOpt = orderGiftExtendEntities.stream().filter(o -> Objects.equals(o.getId(), orderProductionCardId)).findFirst();
                    giftOpt.ifPresent(o -> re.put(app.getOrderSn(), o.getRealPaid()));
                    break;
                case BUY:
                    Optional<OrderProductionExtendEntity> extendOpt = orderProductionExtendEntities.stream().filter(o -> Objects.equals(o.getId(), orderProductionCardId)).findFirst();
                    extendOpt.ifPresent(extend -> {
                        Integer type = extend.getType();
                        OrderProductionItemEnum orderProductionItemEnum = OrderProductionItemEnum.of(type);
                        switch (orderProductionItemEnum) {
                            // 单项
                            case TYPE_PRODUCTION:
                                re.put(app.getOrderSn(), extend.getRealPaid());
                                break;
                            // 通卡
                            case TYPE_GROUP:
                            // 次卡
                            case TYPE_COUNT:
                                Optional<OrderProductionCardExtendEntity> careExtendOpt = orderProductionCardExtendEntities.stream()
                                        .filter(o -> Objects.equals(o.getOrderProductionId(), orderProductionCardId))
                                        .filter(o -> Objects.equals(o.getId(), app.getOrderProductionCardExtendId()))
                                        .findFirst();
                                careExtendOpt.ifPresent(careExtend -> {
                                    BigDecimal realPaid = careExtend.getRealPaid();
                                    if (Objects.nonNull(realPaid)) {
                                        re.put(app.getOrderSn(), realPaid);
                                    }
                                });
                                break;
                        }
                    });
                    break;
            }
        });
        return re;
    }

    /**
     * 判断是否参与统计
     */
    private List<ProductionJudgeVO> judgeAppointment(List<OrderProductionAppointmentEntity> appointmentEntities) {
        if (CollectionUtil.isEmpty(appointmentEntities)) return Lists.newArrayList();

        List<Integer> orderIds = appointmentEntities.stream().map(OrderProductionAppointmentEntity::getOrderId).collect(Collectors.toList());

        List<HeOrderPO> orders = heOrderMapper.selectList(
                new LambdaQueryWrapper<HeOrderPO>()
                        .in(HeOrderPO::getOrderId, orderIds)
                        .ne(HeOrderPO::getCreatedAt, 0L)
                        .isNotNull(HeOrderPO::getCreatedAt)
                        .select(HeOrderPO::getOrderId, HeOrderPO::getCreatedAt)
        );

        return appointmentEntities.stream().map(app -> {
            ProductionJudgeVO vo = new ProductionJudgeVO();

            Optional<HeOrderPO> orderOpt = orders.stream().filter(o -> Objects.equals(o.getOrderId(), app.getOrderId())).findFirst();
            if (!orderOpt.isPresent()) {
                vo.setNonHitGoods(Boolean.FALSE);
                vo.setNonExpired(Boolean.FALSE);
                return vo;
            }

            // 商品名单内的不参与统计 | 2023-08-01之前创建的订单不参与统计
            HeOrderPO order = orderOpt.get();
            vo.setOrderId(order.getOrderId());
            try {
                Date judgeTime = DateUtils.parse(bizConfig.getJudgeDate(), YYYY_MM_DD);
                vo.setNonExpired(order.getCreatedAt() * 1000 >= judgeTime.getTime());
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            vo.setNonHitGoods(CollectionUtil.isEmpty(bizConfig.getAppointmentJudgeSpuIds()) || !bizConfig.getAppointmentJudgeSpuIds().contains(app.getProductionGoodsId()));

            vo.setCreateAt(order.getCreatedAt());
            vo.setAppointmentSn(app.getOrderSn());
            return vo;
        }).collect(Collectors.toList());
    }

    private ProductionRealPaidVO getRealPaid(List<OrderProductionAppointmentEntity> appointmentEntities, Map<String, BigDecimal> realPaidMap) {
        if (CollectionUtil.isEmpty(appointmentEntities) || CollectionUtil.isEmpty(realPaidMap)) {
            return ProductionRealPaidVO.defaultZeroRealPaid();
        }

        // 判断是否参与统计
        List<ProductionJudgeVO> judges = this.judgeAppointment(appointmentEntities);

        // 已核销实付
        BigDecimal writeOffRealPaidAmount = BigDecimal.ZERO;
        // 预约实付
        BigDecimal appointmentRealPaidAmount = BigDecimal.ZERO;

        for (OrderProductionAppointmentEntity app : appointmentEntities) {
            String orderSn = app.getOrderSn();
            Optional<ProductionJudgeVO> judgeOpt = judges.stream().filter(o -> Objects.equals(o.getAppointmentSn(), orderSn)).findFirst();

            BigDecimal tmpRealPaid = realPaidMap.get(orderSn);
            // 如果实付小于0 则不参与统计
            if (Objects.isNull(tmpRealPaid) || tmpRealPaid.compareTo(BigDecimal.ZERO) < 0) continue;

            if (judgeOpt.isPresent()) {
                ProductionJudgeVO judge = judgeOpt.get();
                if (judge.isHit()) {
                    Integer verificationState = app.getVerificationState();
                    VerificationStatusEnum verificationStatusEnum = VerificationStatusEnum.of(verificationState);
                    switch (verificationStatusEnum) {
                        case DONE_WRITE_OFF:
                            writeOffRealPaidAmount = BigDecimalUtil.add(writeOffRealPaidAmount, tmpRealPaid);
                            break;
                        case BOOKED:
                            appointmentRealPaidAmount = BigDecimalUtil.add(appointmentRealPaidAmount, tmpRealPaid);
                            break;
                    }
                }
            }
        }
        return new ProductionRealPaidVO(writeOffRealPaidAmount, appointmentRealPaidAmount);
    }

//    @Override
//    public List<ProductionTherapistInfoVO> queryTherapistInfoByOrderSn(List<String> orderSnList) {
//        if (CollectionUtil.isEmpty(orderSnList)) {
//            return new ArrayList<>();
//        }
//        List<OrderProductionAppointmentEntity> entityList = appointmentRepository.queryTherapistInfoByOrderSn(orderSnList);
//        if (CollectionUtil.isEmpty(entityList)) {
//            return new ArrayList<>();
//        }
//        return entityList.stream().map(i -> {
//            ProductionTherapistInfoVO productionTherapistInfoVO = new ProductionTherapistInfoVO();
//            productionTherapistInfoVO.setTherapistId(i.getTherapistId());
//            productionTherapistInfoVO.setTherapistName(i.getTherapistName());
//            productionTherapistInfoVO.setServeType(i.getServeType());
//            productionTherapistInfoVO.setOrderSn(i.getOrderSn());
//            return productionTherapistInfoVO;
//        }).collect(Collectors.toList());
//    }

    @Override
    public List<ProductionAppointmentVo> queryAppointmentListByServeDate(List<String> serveDateList) {

        List<OrderProductionAppointmentEntity> entityList = appointmentRepository.listByServeDate(serveDateList);
        if (CollectionUtil.isEmpty(entityList)){
            return new ArrayList<>();
        }
        return BeanMapper.mapList(entityList,ProductionAppointmentVo.class);
    }
}

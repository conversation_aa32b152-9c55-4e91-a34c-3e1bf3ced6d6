package com.stbella.order.server.contract.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.stbella.contract.model.req.ContractProcessReq;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.cts.server.entity.BaseSitterPO;
import com.stbella.cts.server.entity.HomeContractTemplatePO;
import com.stbella.cts.server.entity.HomeCustomerProductPO;
import com.stbella.cts.server.enums.CTSContractTemplateStatusEnum;
import com.stbella.cts.server.enums.CTSContractTypeEnum;
import com.stbella.cts.server.enums.CTSProductCustomerServiceTypeEnum;
import com.stbella.cts.server.query.HomeContractTemplateBaseQuery;
import com.stbella.cts.server.query.HomeCustomerProductBaseQuery;
import com.stbella.cts.server.query.HomeSitterProductQuery;
import com.stbella.cts.server.response.HomeSitterProductVO;
import com.stbella.cts.service.BaseSitterService;
import com.stbella.cts.service.HomeContractTemplateService;
import com.stbella.cts.service.HomeCustomerProductService;
import com.stbella.cts.service.HomeSitterProductService;
import com.stbella.customer.server.cts.service.CustomerInfoCtsService;
import com.stbella.customer.server.cts.vo.CustomerInfoCtsDetailVO;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.service.OrderParamHistoryService;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.config.ContractContextConfig;
import com.stbella.order.server.config.ESignPropertiesConfig;
import com.stbella.order.server.context.component.processor.contract.PushContractParamsProcessor;
import com.stbella.order.server.contract.biz.config.ContractSignConfig;
import com.stbella.order.server.contract.constant.RegexConstant;
import com.stbella.order.server.contract.dto.*;
import com.stbella.order.server.contract.entity.ContractSignRecordPO;
import com.stbella.order.server.contract.entity.OrderContractConfigPO;
import com.stbella.order.server.contract.entity.UserESignPO;
import com.stbella.order.server.contract.enums.IdTypeEnum;
import com.stbella.order.server.contract.enums.SignModeEnum;
import com.stbella.order.server.contract.param.BabysittingServiceAgreementParam;
import com.stbella.order.server.contract.param.PersonAuthParam;
import com.stbella.order.server.contract.repository.ContractBuildRepository;
import com.stbella.order.server.contract.req.OrderParamHistoryPushDTO;
import com.stbella.order.server.contract.req.OrderParamHistoryValuePushDTO;
import com.stbella.order.server.contract.service.ContractService;
import com.stbella.order.server.contract.service.ContractSignRecordService;
import com.stbella.order.server.contract.service.ESignService;
import com.stbella.order.server.contract.service.UserESignService;
import com.stbella.order.server.contract.vo.*;
import com.stbella.order.server.order.cts.entity.OrderCtsPO;
import com.stbella.order.server.order.cts.enums.ContractSignStatusEnum;
import com.stbella.order.server.order.cts.service.OrderCtsService;
import com.stbella.order.server.order.month.constant.BaseConstant;
import com.stbella.order.server.order.month.enums.OrderTypeEnum;
import com.stbella.redisson.DistributedLocker;
import com.stbella.store.server.store.service.StoreAddressService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.core.context.FlowContext;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@DubboService
public class ContractServiceImpl implements ContractService {
    @Resource
    private ESignService eSignService;
    @Resource
    private OrderCtsService orderCtsService;
    @DubboReference
    private HomeContractTemplateService homeContractTemplateService;
    @Resource
    private ContractSignRecordService contractSignRecordService;
    @Resource
    private UserESignService userESignService;
    @DubboReference
    private CustomerInfoCtsService customerInfoCtsService;
    @DubboReference
    private HomeCustomerProductService homeCustomerProductService;
    @DubboReference
    private HomeSitterProductService homeSitterProductService;
    @DubboReference
    private BaseSitterService baseSitterService;
    @DubboReference
    private StoreAddressService storeAddressService;
    @Resource
    private ContractBuildRepository contractBuildRepository;
    @Resource
    private ContractSignConfig contractSignConfig;

    @Resource
    private DistributedLocker locker;

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private PushContractParamsProcessor pushContractParamsProcessor;

    @Resource
    OrderParamHistoryService orderParamHistoryService;

    private static final String AUTH_KEY = "person:auth:";
    private static final String APPLY_KEY = "person:apply:";

    private static final HashMap<Integer, Integer> MAPPER = new HashMap<>();

    static {
        MAPPER.put(OrderTypeEnum.AUNT_SITTER.getCode(), CTSContractTypeEnum.EMPLOYER_SERVICE_CONTRACT.getCode());
        MAPPER.put(OrderTypeEnum.BABY_SITTER.getCode(), CTSContractTypeEnum.EMPLOYER_SERVICE_CONTRACT.getCode());
        MAPPER.put(OrderTypeEnum.COURSE.getCode(), CTSContractTypeEnum.BABYSITTER_TRAINING_AGREEMENT.getCode());
        MAPPER.put(OrderTypeEnum.MARGIN.getCode(), CTSContractTypeEnum.TO_HOME_CONFIRMATION.getCode());
        MAPPER.put(OrderTypeEnum.SERVE_FEE.getCode(), CTSContractTypeEnum.BABYSITTING_SERVICE_AGREEMENT.getCode());
    }

    private static final HashMap<Integer, Integer> SERVICE_TYPE_MAPPER = new HashMap<>();

    static {
        SERVICE_TYPE_MAPPER.put(OrderTypeEnum.AUNT_SITTER.getCode(), CTSProductCustomerServiceTypeEnum.MATERNAL_AND_CHILD_CARE.getCode());
        SERVICE_TYPE_MAPPER.put(OrderTypeEnum.BABY_SITTER.getCode(), CTSProductCustomerServiceTypeEnum.SITTER.getCode());
    }

    private static final HashMap<Integer, String> PREFIX_MAPPER = new HashMap<>();

    static {
        //雇主母婴订单
        PREFIX_MAPPER.put(OrderTypeEnum.AUNT_SITTER.getCode(), "MYDD");
        //雇主育婴师订单
        PREFIX_MAPPER.put(OrderTypeEnum.BABY_SITTER.getCode(), "YYDD");
        //育婴师培训课程订单
        PREFIX_MAPPER.put(OrderTypeEnum.COURSE.getCode(), "YPDD");
        //育婴师平台管理费
        PREFIX_MAPPER.put(OrderTypeEnum.SERVE_FEE.getCode(), "YGDD");
    }

    /**
     * 坐标先写这里吧
     */
    private static final HashMap<Integer, Position> POSITION_MAPPER = new HashMap<>();

    static {
        //雇主母婴订单
        POSITION_MAPPER.put(OrderTypeEnum.AUNT_SITTER.getCode(), new Position(10, 197, 703, 10, 324, 644));
        //雇主育婴师订单 ok
        POSITION_MAPPER.put(OrderTypeEnum.BABY_SITTER.getCode(), new Position(11, 197, 707, 11, 319, 647));

        //育婴师平台管理费
        POSITION_MAPPER.put(OrderTypeEnum.SERVE_FEE.getCode(), new Position(11, 439, 186, 11, 229, 184));
        //育婴师培训订单
        POSITION_MAPPER.put(OrderTypeEnum.COURSE.getCode(), new Position(8,199,176,8,330,280));

    }

    @AllArgsConstructor
    @Data
    static class Position {
        private Integer customerPage;

        private Integer customerX;

        private Integer customerY;

        private Integer companyPage;

        private Integer companyX;

        private Integer companyY;
    }


    @Override
    public AuthDetailVO authDetail(Long customerId) {
        CustomerInfoCtsDetailVO pcCtsCustomerDetail = customerInfoCtsService.getPcCtsCustomerDetail(customerId);
        if (pcCtsCustomerDetail == null) {
            throw new BusinessException(ResultEnum.NOT_EXIST, "客户详情不能为空");
        }
        AuthDetailVO authDetailVO = new AuthDetailVO();
        UserESignPO userESignPO = userESignService.getOne(new LambdaQueryWrapper<UserESignPO>()
                .eq(UserESignPO::getPhone, pcCtsCustomerDetail.getPhoneNumber())
                .last("limit 1"));
        if (userESignPO != null) {
            if (userESignPO.getStatus() == 1) {
                authDetailVO.setESignUserId(userESignPO.getId());
                authDetailVO.setAuth(true);
            }
        }
        return authDetailVO;
    }


    @Override
    public PersonBaseVO compare(PersonAuthParam personAuthParam) {
        PersonBaseVO personBase = new PersonBaseVO();
        // 用户如果使用港澳台通行证或者护照，直接默认认证成功，但需校验港澳台通行证格式因为e签宝的个人信息比对只支持大陆身份证
        if (IdTypeEnum.CRED_PSN_CH_HONGKONG.getCode().equals(personAuthParam.getCerType())
                || IdTypeEnum.CRED_PSN_CH_MACAO.getCode().equals(personAuthParam.getCerType())) {
            if (RegexConstant.p1.matcher(personAuthParam.getCerNo()).matches()) {
                personBase.setAuthenticationMsg(3);
                return personBase;
            }
            log.info("==========================res:{}", "港澳通行证证件格式异常");
            personBase.setAuthenticationMsg(2);
            return personBase;
        }
        // 台湾来往大陆通行证正则校验
        if (IdTypeEnum.CRED_PSN_CH_TWCARD.getCode().equals(personAuthParam.getCerType())) {
            if (RegexConstant.p2.matcher(personAuthParam.getCerNo()).matches()) {
                personBase.setAuthenticationMsg(3);
                return personBase;
            }
            log.info("==========================res:{}", "台湾通行证证件格式异常");
            personBase.setAuthenticationMsg(2);
            return personBase;
        }
        // 护照正则校验
        if (IdTypeEnum.CRED_PSN_PASSPORT.getCode().equals(personAuthParam.getCerType())) {
            if (RegexConstant.p3.matcher(personAuthParam.getCerNo()).matches()) {
                personBase.setAuthenticationMsg(3);
                return personBase;
            }
            log.info("==========================res:{}", "护照证件格式异常");
            personBase.setAuthenticationMsg(2);
            return personBase;
        }
        // 新用户校验
        boolean c = eSignService.doPersonIdentityComparison(personAuthParam.getName(), personAuthParam.getCerNo());
        if (c) {
            personBase.setAuthenticationMsg(1);
            return personBase;
        }
        personBase.setAuthenticationMsg(2);
        return personBase;
    }

    @Override
    public AuthDetailVO personAuth(PersonAuthParam personAuthParam) {
        PersonBaseVO compare = this.compare(personAuthParam);
        if (compare.getAuthenticationMsg() != 1&&compare.getAuthenticationMsg() != 3) {
            throw new BusinessException(ResultEnum.NOT_EXIST, "证件号码格式有误");
        }
        RLock lock = locker.lock(AUTH_KEY + personAuthParam.getCerNo());
        try {
            AuthDetailVO authDetailVO = new AuthDetailVO();
            UserESignPO userESignPO = userESignService.getOne(new LambdaQueryWrapper<UserESignPO>()
                    .eq(UserESignPO::getCerNo, personAuthParam.getCerNo())
                    .last("limit 1"));

            if (userESignPO == null) {
                PersonAccountDTO personAccount = new PersonAccountDTO();
                personAccount.setThirdPartyUserId(personAuthParam.getCerNo());
                personAccount.setIdNumber(personAuthParam.getCerNo());
                personAccount.setIdType(IdTypeEnum.getIdType(personAuthParam.getCerType()));
                personAccount.setMobile(personAuthParam.getPhone());
                personAccount.setName(personAuthParam.getName());
                String accountId = eSignService.createPersonSignAccount(personAccount);
                if (StringUtils.isBlank(accountId)) {
                    throw new BusinessException(ResultEnum.NOT_EXIST, "认证异常");
                } else {
                    UserESignPO userESign = new UserESignPO();
                    userESign.setESignUserId(accountId);
                    userESign.setPhone(personAuthParam.getPhone());
                    userESign.setName(personAuthParam.getName());
                    userESign.setCerNo(personAuthParam.getCerNo());
                    userESign.setCerType(personAuthParam.getCerType());
                    userESign.setStatus(1);
                    boolean flag = userESignService.save(userESign);
                    if (!flag) {
                        //注销e签宝创建个人签署账户信息
                        eSignService.cancellationAccount(accountId);
                        throw new BusinessException(ResultEnum.NOT_EXIST, "认证异常");
                    } else {
                        authDetailVO.setAuth(true);
                        authDetailVO.setESignUserId(userESign.getId());
                        return authDetailVO;
                    }
                }
            } else {
                //判断name、mobile没有改变
                if (userESignPO.getName().equals(personAuthParam.getName())
                        && userESignPO.getPhone().equals(personAuthParam.getPhone())) {
                    authDetailVO.setAuth(true);
                    authDetailVO.setESignUserId(userESignPO.getId());
                    return authDetailVO;
                }
                //name、mobile有改变,调用E签宝的更新接口
                UpdateAccountResquestDTO updateAccountResquestDTO = new UpdateAccountResquestDTO();
                updateAccountResquestDTO.setAccountId(userESignPO.getESignUserId());
                updateAccountResquestDTO.setName(personAuthParam.getName());
                updateAccountResquestDTO.setPhone(personAuthParam.getPhone());
                String accountId = eSignService.updatePersonSignAccount(updateAccountResquestDTO);
                if (StringUtils.isBlank(accountId)) {
                    throw new BusinessException(ResultEnum.NOT_EXIST, "认证异常");
                } else {
                    //更新我们自己数据库
                    boolean flag = userESignService.update(new LambdaUpdateWrapper<UserESignPO>()
                            .eq(UserESignPO::getCerNo, userESignPO.getCerNo())
                            .set(UserESignPO::getName, personAuthParam.getName())
                            .set(UserESignPO::getPhone, personAuthParam.getPhone()));
                    if (flag) {
                        authDetailVO.setAuth(true);
                        authDetailVO.setESignUserId(userESignPO.getId());
                        return authDetailVO;
                    } else {
                        //e 签宝更新个人签署信息 回退
                        UpdateAccountResquestDTO updateAccountInfo = new UpdateAccountResquestDTO();
                        updateAccountInfo.setAccountId(userESignPO.getESignUserId());
                        updateAccountInfo.setName(userESignPO.getName());
                        updateAccountInfo.setPhone(userESignPO.getPhone());
                        eSignService.updatePersonSignAccount(updateAccountInfo);
                        throw new BusinessException(ResultEnum.NOT_EXIST, "认证异常");
                    }
                }
            }
        } finally {
            lock.unlock();
        }
    }


    @SneakyThrows
    @Override
    public SignContractVO apply(BabysittingServiceAgreementParam babysittingServiceAgreementParam) {
        String orderNo = babysittingServiceAgreementParam.getOrderNo();
        Integer signMode = babysittingServiceAgreementParam.getSignMode();

        RLock lock = locker.lock(APPLY_KEY + orderNo);
        try {
            List<ContractSignRecordPO> contractSignRecordPOS = contractSignRecordService
                    .list(new LambdaQueryWrapper<ContractSignRecordPO>()
                            .eq(ContractSignRecordPO::getOrderNo, orderNo));
            if (contractSignRecordPOS.stream().anyMatch(i -> ContractSignStatusEnum.SIGN_FINISH.getCode().equals(i.getStatus()))) {
                throw new BusinessException(ResultEnum.DATABASE_DATA_EXIST, "该订单已经签订过合同");
            }
//            contractSignRecordPOS.forEach(i -> eSignService.cancelSignFlow(i.getESignFlowId()));
            OrderCtsPO orderCtsPO = orderCtsService.getByOrderNo(orderNo);
            if (orderCtsPO == null) {
                throw new BusinessException(ResultEnum.NOT_EXIST, "订单不存在");
            }
            //获取模版

            HomeContractTemplateBaseQuery homeContractTemplateBaseQuery = new HomeContractTemplateBaseQuery();
            homeContractTemplateBaseQuery.setStatus(CTSContractTemplateStatusEnum.OPEN.getCode());
            homeContractTemplateBaseQuery.setContractType(MAPPER.get(orderCtsPO.getOrderType()));
            if (SERVICE_TYPE_MAPPER.get(orderCtsPO.getOrderType()) != null) {
                homeContractTemplateBaseQuery.setProductServiceType(SERVICE_TYPE_MAPPER.get(orderCtsPO.getOrderType()));
            }
            List<HomeContractTemplatePO> homeContractTemplatePOS = homeContractTemplateService.queryHomeContractTemplateList(homeContractTemplateBaseQuery);
            if (homeContractTemplatePOS == null || homeContractTemplatePOS.isEmpty()) {
                throw new BusinessException(ResultEnum.NOT_EXIST, "模板不存在");
            }
            HomeContractTemplatePO homeContractTemplatePO = homeContractTemplatePOS.stream().findFirst().get();

            UserESignPO userESignPO = userESignService.getById(orderCtsPO.getOrderCustomCertificationId());
            if (userESignPO == null) {
                throw new BusinessException(ResultEnum.NOT_EXIST, "认证信息不存在");
            }
            String eSignUserId = userESignPO.getESignUserId();

            HashMap<String, String> data;
            String contractNo = PREFIX_MAPPER.get(orderCtsPO.getOrderType()) + DateTime.now().toString("yyyyMMdd") + RandomUtil.randomNumbers(BaseConstant.CTS_CONTRACT_NO);
            Map<Integer, String> addressMap = storeAddressService.queryAllAddressMap();

            if (OrderTypeEnum.COURSE.getCode().equals(orderCtsPO.getOrderType())||OrderTypeEnum.SERVE_FEE.getCode().equals(orderCtsPO.getOrderType())) {

                BaseSitterPO baseSitterPO = baseSitterService.getById(orderCtsPO.getSitterId());
                if (OrderTypeEnum.SERVE_FEE.getCode().equals(orderCtsPO.getOrderType())) {
                    SitterPlatformServeFeeAgreementDTO sitterPlatformServeFeeAgreementDTO = contractBuildRepository.buildSitterPlatformServeFeeAgreementDTO(orderCtsPO, userESignPO, addressMap,baseSitterPO, contractNo);
                    data = JSONObject.parseObject(JSONObject.toJSONString(sitterPlatformServeFeeAgreementDTO), HashMap.class);
                }else{
                    HomeSitterProductQuery homeSitterProductQuery = new HomeSitterProductQuery();
                    homeSitterProductQuery.setId(orderCtsPO.getProductId());
                    List<HomeSitterProductVO> list = homeSitterProductService.list(homeSitterProductQuery);
                    if (list == null || list.isEmpty()) {
                        throw new BusinessException(ResultEnum.NOT_EXIST, "商品不存在");
                    }
                    HomeSitterProductVO homeSitterProductVO = list.stream().findFirst().get();
                    SitterTrainFeeAgreementDTO sitterTrainFeeAgreementDTO = contractBuildRepository.buildSitterTrainFeeAgreementDTO(orderCtsPO, userESignPO, addressMap, baseSitterPO, homeSitterProductVO, contractNo);
                    data = JSONObject.parseObject(JSONObject.toJSONString(sitterTrainFeeAgreementDTO), HashMap.class);
                }
            } else if (OrderTypeEnum.BABY_SITTER.getCode().equals(orderCtsPO.getOrderType()) || OrderTypeEnum.AUNT_SITTER.getCode().equals(orderCtsPO.getOrderType())) {
                HomeCustomerProductBaseQuery homeCustomerProductBaseQuery = new HomeCustomerProductBaseQuery();
                homeCustomerProductBaseQuery.setId(orderCtsPO.getProductId());
                List<HomeCustomerProductPO> homeCustomerProductPOS = homeCustomerProductService.queryHomeCustomerProductList(homeCustomerProductBaseQuery);
                if (homeCustomerProductPOS == null || homeCustomerProductPOS.isEmpty()) {
                    throw new BusinessException(ResultEnum.NOT_EXIST, "商品不存在");
                }
                CustomerInfoCtsDetailVO pcCtsCustomerDetail = customerInfoCtsService.getPcCtsCustomerDetail(orderCtsPO.getCustomId());
                HomeCustomerProductPO homeCustomerProductPO = homeCustomerProductPOS.stream().findFirst().get();
                if (OrderTypeEnum.BABY_SITTER.getCode().equals(orderCtsPO.getOrderType())) {
                    BabysittingServiceAgreementDTO babysittingServiceAgreementDTO = contractBuildRepository.buildBabySitting(orderCtsPO, userESignPO, homeCustomerProductPO, addressMap, contractNo, pcCtsCustomerDetail);
                    data = JSONObject.parseObject(JSONObject.toJSONString(babysittingServiceAgreementDTO), HashMap.class);
                } else {
                    MaternalChildNursingServiceAgreementDTO maternalChildNursingServiceAgreementDTO = contractBuildRepository.buildMaternalChildNuring(orderCtsPO, userESignPO, homeCustomerProductPO, addressMap, contractNo,pcCtsCustomerDetail);
                    data = JSONObject.parseObject(JSONObject.toJSONString(maternalChildNursingServiceAgreementDTO), HashMap.class);
                }
            } else {
                throw new BusinessException(ResultEnum.NOT_EXIST, "该订单类型,无需签订合同");
            }


            //对上传的合同模板文件填充相关合同内容
            //此处有顺序问题 不可随意改动！
            if(OrderTypeEnum.COURSE.getCode().equals(orderCtsPO.getOrderType())){
                ContractContextConfig.setValue(true);
            }
            OrderContractConfigPO config = eSignService.getConfig();
            HashMap<Long,String> hashMap = JSONObject.parseObject(homeContractTemplatePO.getESignTemplateId(), HashMap.class);
            ContractParamFillRequestDTO contractParamFillRequestDTO = new ContractParamFillRequestDTO();
            contractParamFillRequestDTO.setName(homeContractTemplatePO.getTemplateName());
            //模块id
            if(OrderTypeEnum.COURSE.getCode().equals(orderCtsPO.getOrderType())){
                //别纠正为-1L 会空
                contractParamFillRequestDTO.setTemplateId(hashMap.get(0));
            }else {
                contractParamFillRequestDTO.setTemplateId(hashMap.get(config.getCtsSiteId()));
            }

            contractParamFillRequestDTO.setContent(data);
            ContractParamFillResponseDTO contractParamFillResponseDTO = eSignService.doFillContentTemplate(contractParamFillRequestDTO);

            //创建合同记录
            ContractSignRecordPO contractSignRecordPO = new ContractSignRecordPO();
            contractSignRecordPO.setContractNo(contractNo);
            contractSignRecordPO.setOrderNo(orderNo);
            contractSignRecordPO.setTemplateId(homeContractTemplatePO.getTemplateId());
            contractSignRecordPO.setCustomerId(orderCtsPO.getCustomId());
            contractSignRecordPO.setStaffId(orderCtsPO.getSellId());
            contractSignRecordPO.setParam(JSONObject.toJSONString(data));
            contractSignRecordPO.setStatus(ContractSignStatusEnum.SIGNING.getCode());
            contractSignRecordPO.setESignFileId(contractParamFillResponseDTO.getFileId());
            contractSignRecordPO.setESignFileName(contractParamFillResponseDTO.getFileName());
            long id = IdWorker.getId(contractSignRecordPO);
            contractSignRecordPO.setId(id);
            contractSignRecordService.save(contractSignRecordPO);
            //
            ContractSignStartRequestDTO contractSignStartRequest = new ContractSignStartRequestDTO();
            // 启动流程基本信息
            ContractSignStartRequestDTO.InnerFlowInfo innerFlowInfo = new ContractSignStartRequestDTO.InnerFlowInfo();
            innerFlowInfo.setBusinessScene("合同签订流程发起");
            ContractSignStartRequestDTO.InnerFlowInfo.InnerFlowConfigInfo flowConfigInfo = new
                    ContractSignStartRequestDTO.InnerFlowInfo.InnerFlowConfigInfo();

            // 在此业务场景下不需要发短信或邮件通知客户，所以此字段直接默认"",必须要填e签宝文档规定
            flowConfigInfo.setNoticeType("");
            flowConfigInfo.setBatchDropSeal(false);
            flowConfigInfo.setNoticeDeveloperUrl(config.getPrimaryCallbackUrl());
            flowConfigInfo.setSignPlatform("1");
            flowConfigInfo.setRedirectUrl("wechat://back");
            List<String> list = Lists.newArrayList();
            List<String> newList = Lists.newArrayList();
            //个人认证和意愿认证 个人认证是初次使用时指定 意愿认证是后面已经有签订历史了可以指定的方式
            if (SignModeEnum.FACE_SIGN.getCode().equals(signMode)) {
                if(IdTypeEnum.CRED_PSN_CH_IDCARD.getCode().equals(userESignPO.getCerType())){
                    // 个人刷脸认证
                    list.add("PSN_FACEAUTH_BYURL");
                    // 个人运营商三要素认证
                    list.add("PSN_TELECOM_AUTHCODE");
                    // 个人银行卡四要素认证
                    list.add("PSN_BANK4_AUTHCODE");
                    // 指定意愿微信小程序刷脸
                    newList.add("FACE_WE_CHAT_FACE");
                    // 指定意愿短信验证
                    newList.add("CODE_SMS");
                }else{
                    // 个人银行卡四要素认证
                    list.add("PSN_BANK4_AUTHCODE");
                    // 指定意愿短信验证
                    newList.add("CODE_SMS");
                }
                // 指定个人页面显示的认证方式
                flowConfigInfo.setPersonAvailableAuthTypes(list);
                flowConfigInfo.setWillTypes(newList);
            } else {
                if(IdTypeEnum.CRED_PSN_CH_IDCARD.getCode().equals(userESignPO.getCerType())){
                    // 个人刷脸认证
                    list.add("PSN_FACEAUTH_BYURL");
                    // 个人运营商三要素认证
                    list.add("PSN_TELECOM_AUTHCODE");
                    // 个人银行卡四要素认证
                    list.add("PSN_BANK4_AUTHCODE");

                    // 指定意愿腾讯云刷脸
                    newList.add("FACE_TECENT_CLOUD_H5");
                    // 指定意愿支付宝刷脸
                    newList.add("FACE_ZHIMA_XY");
                    // 指定意愿短信验证
                    newList.add("CODE_SMS");
                }else{
                    // 个人银行卡四要素认证
                    list.add("PSN_BANK4_AUTHCODE");
                    // 指定意愿短信验证
                    newList.add("CODE_SMS");
                }
                // 指定个人页面显示的认证方式
                flowConfigInfo.setPersonAvailableAuthTypes(list);
                flowConfigInfo.setWillTypes(newList);
            }
            innerFlowInfo.setFlowConfigInfo(flowConfigInfo);
            contractSignStartRequest.setFlowInfo(innerFlowInfo);


            Position position = POSITION_MAPPER.get(orderCtsPO.getOrderType());
            // 乙方企业签署方信息
            List<ContractSignStartRequestDTO.InnerSigners> signersList = new ArrayList<>();
            ContractSignStartRequestDTO.InnerSigners signers = new ContractSignStartRequestDTO.InnerSigners();
            ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount signerAccount = new
                    ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount();
            // 乙方企业账号在合同合规初版只使用总部账号，各门店账号暂不用，所以此处写死
            signerAccount.setAuthorizedAccountId(config.getCompanyAccountId());
            signers.setSignerAccount(signerAccount);
            signers.setPlatformSign(true);
            List<ContractSignStartRequestDTO.InnerSigners.InnerSignfields> innerSignfieldsList = new ArrayList<>();
            ContractSignStartRequestDTO.InnerSigners.InnerSignfields innerSignfields = new
                    ContractSignStartRequestDTO.InnerSigners.InnerSignfields();
            innerSignfields.setAutoExecute(true);
            innerSignfields.setActorIndentityType(2);

            innerSignfields.setFileId(contractSignRecordPO.getESignFileId());

            ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean innerPosBean = new
                    ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean();
            // 这里坐标写死是因为主合同签章位置和页数都是固定的 公司签名
            innerPosBean.setPosPage(position.getCompanyPage());
            innerPosBean.setPosX(position.getCompanyX());
            innerPosBean.setPosY(position.getCompanyY());
            innerSignfields.setPosBean(innerPosBean);
            innerSignfieldsList.add(innerSignfields);
            signers.setSignfields(innerSignfieldsList);
            signersList.add(signers);

            // 甲方个人签署信息
            ContractSignStartRequestDTO.InnerSigners personSigners = new ContractSignStartRequestDTO.InnerSigners();
            ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount personSignerAccount = new
                    ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount();
            personSignerAccount.setSignerAccount(eSignUserId);
            personSigners.setSignerAccount(personSignerAccount);
            List<ContractSignStartRequestDTO.InnerSigners.InnerSignfields> personInnerSignfieldsList = new ArrayList<>();
            ContractSignStartRequestDTO.InnerSigners.InnerSignfields personInnerSignfields = new
                    ContractSignStartRequestDTO.InnerSigners.InnerSignfields();
            personInnerSignfields.setAutoExecute(false);
            personInnerSignfields.setFileId(contractSignRecordPO.getESignFileId());
            //手绘签名
            personInnerSignfields.setSealType("0");
            personSigners.setPlatformSign(false);
            ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean personInnerPosBean = new
                    ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean();
            //支持动态配置吧 改起来方便
            personInnerPosBean.setPosPage(position.getCustomerPage());
            personInnerPosBean.setPosX(position.getCustomerX());
            personInnerPosBean.setPosY(position.getCustomerY());
            personInnerSignfields.setPosBean(personInnerPosBean);
            personInnerSignfieldsList.add(personInnerSignfields);
            personSigners.setSignfields(personInnerSignfieldsList);
            signersList.add(personSigners);
            contractSignStartRequest.setSigners(signersList);
            // 待签署文件信息
            List<ContractSignStartRequestDTO.InnerDocs> docsList = new ArrayList<>();
            ContractSignStartRequestDTO.InnerDocs innerDocs = new ContractSignStartRequestDTO.InnerDocs();
            innerDocs.setFileId(contractSignRecordPO.getESignFileId());
            innerDocs.setFileName(contractSignRecordPO.getESignFileName());
            docsList.add(innerDocs);
            contractSignStartRequest.setDocs(docsList);
            // 获取主合同签订启动流程id
            String signContractFlowId = eSignService.doInitiateSigningProcess(contractSignStartRequest);
            // 并保存至数据库，如果合同流程启动，却因其他原因未签署合同，想再次签署合同时直接使用这个流程id就可以获取签署链接
            // 不必再重新发起流程，重新再发起流程会重复扣合同费用
            contractSignRecordService.update(new LambdaUpdateWrapper<ContractSignRecordPO>()
                    .eq(ContractSignRecordPO::getId, id)
                    .set(ContractSignRecordPO::getESignFlowId, signContractFlowId));

            SignAddressResponseDTO signAddress = eSignService.getSignAddress(eSignUserId, signContractFlowId);


            contractSignRecordService.update(new LambdaUpdateWrapper<ContractSignRecordPO>()
                    .eq(ContractSignRecordPO::getId, id)
                    .set(ContractSignRecordPO::getESignLongUrl, signAddress.getLongUrl())
                    .set(ContractSignRecordPO::getESignShortUrl, signAddress.getShortUrl()));

            SignContractVO signContractVO = new SignContractVO();
            if (SignModeEnum.FACE_SIGN.getCode().equals(signMode)) {
                signContractVO.setSignAddress(signAddress.getLongUrl());
                return signContractVO;
            }
            signContractVO.setSignAddress(signAddress.getShortUrl());
            return signContractVO;
        } finally {
            lock.unlock();
        }
    }

    @Override
    public List<ContractVO> contractListH5(String orderNo) {
        OrderCtsPO orderCtsPO = orderCtsService.getByOrderNo(orderNo);
        if (orderCtsPO == null) {
            throw new BusinessException(ResultEnum.NOT_EXIST, "订单不存在");
        }
        ArrayList<ContractVO> res = new ArrayList<>();
        if (contractSignConfig.getMapper() != null && contractSignConfig.getMapper().get(orderCtsPO.getOrderType())) {
            HomeContractTemplateBaseQuery homeContractTemplateBaseQuery = new HomeContractTemplateBaseQuery();
            homeContractTemplateBaseQuery.setStatus(CTSContractTemplateStatusEnum.OPEN.getCode());
            homeContractTemplateBaseQuery.setContractType(MAPPER.get(orderCtsPO.getOrderType()));
            homeContractTemplateBaseQuery.setProductServiceType(SERVICE_TYPE_MAPPER.get(orderCtsPO.getOrderType()));
            List<HomeContractTemplatePO> homeContractTemplatePOS = homeContractTemplateService.queryHomeContractTemplateList(homeContractTemplateBaseQuery);
            if (homeContractTemplatePOS == null || homeContractTemplatePOS.isEmpty()) {
                throw new BusinessException(ResultEnum.NOT_EXIST, "模板不存在");
            }
            HomeContractTemplatePO homeContractTemplatePO = homeContractTemplatePOS.stream().findFirst().get();

            ContractVO contractVO = new ContractVO();

            contractVO.setName(homeContractTemplatePO.getTemplateName());
            contractVO.setStatus(orderCtsPO.getIsSignContract());
            res.add(contractVO);
        }
        return res;
    }

    @Override
    public ContractDetailVO contractDetail(String orderNo) {
        OrderCtsPO orderCtsPO = orderCtsService.getByOrderNo(orderNo);
        if (orderCtsPO == null) {
            throw new BusinessException(ResultEnum.NOT_EXIST, "订单不存在");
        }
        HomeContractTemplateBaseQuery homeContractTemplateBaseQuery = new HomeContractTemplateBaseQuery();
        homeContractTemplateBaseQuery.setStatus(CTSContractTemplateStatusEnum.OPEN.getCode());
        homeContractTemplateBaseQuery.setContractType(MAPPER.get(orderCtsPO.getOrderType()));
        homeContractTemplateBaseQuery.setProductServiceType(SERVICE_TYPE_MAPPER.get(orderCtsPO.getOrderType()));
        List<HomeContractTemplatePO> homeContractTemplatePOS = homeContractTemplateService.queryHomeContractTemplateList(homeContractTemplateBaseQuery);
        if (homeContractTemplatePOS == null || homeContractTemplatePOS.isEmpty()) {
            throw new BusinessException(ResultEnum.NOT_EXIST, "模板不存在");
        }
        HomeContractTemplatePO homeContractTemplatePO = homeContractTemplatePOS.stream().findFirst().get();

        List<ContractSignRecordPO> list = contractSignRecordService.list(new LambdaQueryWrapper<ContractSignRecordPO>()
                .eq(ContractSignRecordPO::getStatus,ContractSignStatusEnum.SIGN_FINISH.getCode())
                .eq(ContractSignRecordPO::getOrderNo, orderNo));

        ContractDetailVO contractDetailVO = new ContractDetailVO();
        contractDetailVO.setName(homeContractTemplatePO.getTemplateName());
        contractDetailVO.setStatus(orderCtsPO.getIsSignContract());
        if (!list.isEmpty()) {
            ContractSignRecordPO contractSignRecordPO = list.stream().findFirst().get();
            contractDetailVO.setContractUrl(contractSignRecordPO.getESignLongUrl());
        }
        return contractDetailVO;
    }

    @Override
    public PushContractParamsVO pushContractParams(Integer orderId) {

        PushContractParamsVO vo = new PushContractParamsVO();
        vo.setResult(Boolean.TRUE);
        try {
            Assert.isTrue(Objects.nonNull(orderId), "订单Id不能为空");
            HeOrderEntity heOrderEntity = orderRepository.queryOrderById(orderId);
            FlowContext flowContext = new FlowContext();
            flowContext.setAttribute(HeOrderEntity.class, heOrderEntity);
            SnowballFlowLauncher.fire(flowContext, pushContractParamsProcessor);
        } catch (Exception e){
            log.error("构造合同参数发生异常 e:{}", e);
            vo.setResult(Boolean.FALSE);
            vo.setFailMsg(e.getMessage());
        }
        return vo;
    }

    @Override
    public Map<String, String> buildContractParams(Integer orderId) {

        Map<String, String> resultMap = Maps.newHashMap();
        log.info("订单1.0构建折扣保密协议参数开始处理orderId:{}", orderId);
        try {
            if (Objects.isNull(orderId)){
                return resultMap;
            }
            HeOrderEntity heOrderEntity = orderRepository.queryOrderById(orderId);
            if (Objects.isNull(heOrderEntity)){
                return resultMap;
            }
            OrderParamHistoryPushDTO orderParamHistoryPushDTO = orderParamHistoryService.buildOrderParamHistory(heOrderEntity);
            if (Objects.isNull(orderParamHistoryPushDTO) || CollectionUtils.isEmpty(orderParamHistoryPushDTO.getParamList())){
                return resultMap;
            }
            return orderParamHistoryPushDTO.getParamList().stream().collect(Collectors.toMap(OrderParamHistoryValuePushDTO :: getName, OrderParamHistoryValuePushDTO :: getValue));
        } catch (Exception e){
            log.error("订单1.0构建折扣保密协议参数发生异常msg:{}", e.getMessage(), e);
            return resultMap;
        }
    }


}

package com.stbella.order.server.context.component.assembler.additional;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.order.OrderAdditionalKeyEnum;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.common.utils.PropertyValueHelper;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import com.stbella.platform.order.api.res.SkuAdditionalInfo;
import com.stbella.store.common.enums.core.GoodsTypeEnum;
import com.stbella.store.goodz.res.PropertyUserDefinedVO;
import com.stbella.store.goodz.res.PropertyValueListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.stbella.order.common.enums.order.OrderAdditionalKeyEnum.HOLIDAY_SERVICE_COST;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-06-19  11:16
 * @Description: 节假日服务费组装器
 */
@Component
@Slf4j
public class HolidayServiceFeeAdditionalItermAssembler implements AdditionalItermAssembler {

    /**
     * 过滤器， 判断sku所在分类的属性中 是否包含节假日服务费属性
     *
     * @param context
     * @return
     */
    @Override
    public boolean filter(AdditionalContext context) {
        if (CollectionUtil.isEmpty(context.getCategoryPropertyMap())) {
            return false;
        }
        if (Objects.isNull(context.getSku().getBackCategoryId())) {
            return false;
        }
        Long backCategoryId = context.getSku().getBackCategoryId().longValue();
        if (!context.getCategoryPropertyMap().containsKey(backCategoryId)) {
            return false;
        }
        PropertyUserDefinedVO backCategoryCustomProperty = context.getCategoryPropertyMap().get(backCategoryId);
        Optional<PropertyValueListVO> first = backCategoryCustomProperty.getPropertyValueListVOList().stream().filter(property -> Objects.nonNull(property.getCode())).filter(Objects.requireNonNull(propertyValueListVO -> propertyValueListVO.getCode().equals(HOLIDAY_SERVICE_COST.code()))).findFirst();
        if (!first.isPresent()) {
            return false;
        }
        PropertyValueListVO propertyValueListVO = first.get();
        if (PropertyValueHelper.getBooleanPropertyValue(propertyValueListVO.getPropertyValueId().get(0))) {
            return true;
        }
        return false;
    }

    /**
     * 生成额外收费信息
     */
    @Override
    public SkuAdditionalInfo run(AdditionalContext bizContext) {

        CfgStoreEntity store = bizContext.getStore();
        SkuAdditionalInfo additional = new SkuAdditionalInfo()
                .setPropertyId(0L)
                .setGoodsType(GoodsTypeEnum.HOLIDAY_SERVICE.code() + "")
                .setCode(HOLIDAY_SERVICE_COST.code())
                .setName(HOLIDAY_SERVICE_COST.desc())
                .setPrice(new BigDecimal(store.getHolidayPrice() / 100))
                .setCost(new BigDecimal(store.getHolidayCost() / 100));

        //计算天数： 1. 获取预计入住日期，2，根据假日日期列表，计算入住过程中有多少天节假日

        // 预入住日期
        LocalDate wantInDate = null;
        String wantInSecondStr = bizContext.getFulfillExtraMap().get(OrderAdditionalKeyEnum.WANT_IN_DATE.code());
        if (StringUtils.isEmpty(wantInSecondStr)) {
            return null;
        }
        wantInDate = DateUtils.getLocalDateByUnixTimestamp(Long.parseLong(wantInSecondStr));
        if (wantInDate.getYear() >= BizConstant.OrderAppKey.PreparationForPregnancyYearTag) {
            //备孕中，不生成
            return null;
        }

        if (CollectionUtil.isEmpty(bizContext.getHolidayList())) {
            return null;
        }
        //开始时间要根据 前一个商品的离开日期来计算
        LocalDate startDate = bizContext.getLastCheckOutDate() == null ? wantInDate : bizContext.getLastCheckOutDate().plusDays(1);

        SkuDetailInfo skuDetailInfo = bizContext.getSku();
        LocalDate endDate = startDate.plusDays((long) skuDetailInfo.getNum() * skuDetailInfo.getSkuNum()* skuDetailInfo.getParentNum() - 1);

        //设置本商品离开日期
        bizContext.lastCheckOutDate = endDate;

        List<LocalDate> holidays = bizContext.getHolidayList().stream().filter(holiday -> !holiday.isBefore(startDate) && !holiday.isAfter(endDate)).collect(Collectors.toList());
        if (holidays.size() == 0) {
            return null;
        }
        List<String> dateList = holidays.stream().map(a -> LocalDateTimeUtil.format(a, "yyyy-MM-dd")).collect(Collectors.toList());

        additional.setContent(JSONUtil.toJsonStr(dateList));
        additional.setNum(holidays.size());
        log.info("节假日天数：{}，明细={}", additional.getNum(), JSONUtil.toJsonStr(additional));

        return additional;
    }
}

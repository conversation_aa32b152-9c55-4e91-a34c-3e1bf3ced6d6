package com.stbella.order.server.context.component.complaints.processor;

import cn.hutool.json.JSONUtil;
import com.stbella.order.common.enums.order.CustomerComplaintsRefundEnum;
import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.server.convert.HeCustomerComplaintsServerConverter;
import com.stbella.order.server.order.month.enums.CustomerComplaintsStatusEnum;
import com.stbella.order.server.order.month.req.OfflineRemittanceRequest;
import com.stbella.order.server.order.month.res.SelectRespVO;
import com.stbella.order.server.utils.IdGenUtils;
import com.stbella.platform.order.api.req.CustomerComplaintsCreateReq;
import com.stbella.platform.order.api.req.ExtraRefundTypeReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
@SnowballComponent(name = "客诉创建处理", desc = "")
public class CustomerComplaintsCreateProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private HeCustomerComplaintsServerConverter heCustomerComplaintsServerConverter;


    @Override
    public void run(FlowContext bizContext) {

        CustomerComplaintsCreateReq complaintsCreateReq = bizContext.getAttribute(CustomerComplaintsCreateReq.class);
        Long id = complaintsCreateReq.getId();
        if (Objects.nonNull(id)) {
            log.info("客诉创建处理，id:{}", id);
        }
        //基础信息处理
        HeCustomerComplaintsEntity heCustomerComplaintsEntity = heCustomerComplaintsServerConverter.customerComplaintsCreateReqToEntity(complaintsCreateReq);
        if (complaintsCreateReq.getIsDraft()) {
            heCustomerComplaintsEntity.setComplaintStatus(CustomerComplaintsStatusEnum.DRAFT.getCode());
        }
        if (Objects.nonNull(complaintsCreateReq.getCreateRefundReq())){
            heCustomerComplaintsEntity.setCartId(complaintsCreateReq.getCreateRefundReq().getCartId());
            heCustomerComplaintsEntity.setHasExtraCompensationOpinion(complaintsCreateReq.getCreateRefundReq().getHasExtraCompensationOpinion());
            if (CollectionUtils.isNotEmpty(complaintsCreateReq.getCreateRefundReq().getExtraRefundTypeList())){
                heCustomerComplaintsEntity.setExtraRefundInfo(JSONUtil.toJsonStr(complaintsCreateReq.getCreateRefundReq().getExtraRefundTypeList()));
            }
            List<SelectRespVO> otherCompensationAmountList = getOtherCompensationAmountList(complaintsCreateReq.getCreateRefundReq().getExtraRefundTypeList());
            if (CollectionUtils.isNotEmpty(otherCompensationAmountList)){
                heCustomerComplaintsEntity.setOtherCompensationAmount(JSONUtil.toJsonStr(otherCompensationAmountList));
            }
        }
        if (Objects.nonNull(heCustomerComplaintsEntity.getId())) {
            log.info("客诉更新处理，id:{}", heCustomerComplaintsEntity.getId());
            heCustomerComplaintsEntity.update();
        } else {
            heCustomerComplaintsEntity.add(complaintsCreateReq.getOperator());
        }

        bizContext.setAttribute(HeCustomerComplaintsEntity.class, heCustomerComplaintsEntity);


    }

    private List<SelectRespVO> getOtherCompensationAmountList(List<ExtraRefundTypeReq> extraRefundTypeList) {

        List<SelectRespVO> otherCompensationAmountList = new ArrayList<>();
        if (CollectionUtils.isEmpty(extraRefundTypeList)){
            return otherCompensationAmountList;
        }

        Map<String, ExtraRefundTypeReq> extraRefundTypeMap = extraRefundTypeList.stream().collect(Collectors.toMap(ExtraRefundTypeReq::getExtraCode, v -> v, (k1, k2) -> k1));
        for (CustomerComplaintsRefundEnum item : CustomerComplaintsRefundEnum.values()){

            SelectRespVO selectRespVO = new SelectRespVO(item.getDesc(), BigDecimal.ZERO.toEngineeringString());
            ExtraRefundTypeReq extraRefundType = extraRefundTypeMap.get(item.getCode());
            if (Objects.nonNull(extraRefundType)){
                selectRespVO.setLabel(getRoundingMode(extraRefundType.getExtraRefundAmount()).toEngineeringString());
            }
            otherCompensationAmountList.add(selectRespVO);
        }
        return otherCompensationAmountList;
    }

    private BigDecimal getRoundingMode(BigDecimal amount) {

        if (Objects.isNull(amount) || amount.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO.divide(BigDecimal.ONE, 2, RoundingMode.UP);
        }
        return amount.divide(BigDecimal.ONE, 2, RoundingMode.UP);
    }
}

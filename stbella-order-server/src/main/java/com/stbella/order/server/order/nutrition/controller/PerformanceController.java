package com.stbella.order.server.order.nutrition.controller;

import com.stbella.core.result.Result;
import com.stbella.mvc.base.BaseController;
import com.stbella.order.server.order.nutrition.response.OrderNutritionAllPerformanceVO;
import com.stbella.order.server.order.nutrition.service.OrderNutritionObjectivesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;


@Validated
@Api(tags = "广禾堂业绩获取")
@RestController
@RequestMapping("/order/nutrition/performance")
@Slf4j
public class PerformanceController extends BaseController {

    @Resource
    private OrderNutritionObjectivesService orderNutritionObjectivesService;

    @ApiOperation(value = "根据日期获取某个月份或某年的总业绩")
    @GetMapping("/allPerformance")
    public Result<OrderNutritionAllPerformanceVO> allPerformance(
            @NotNull(message = "年份不能为空") @Max(value = 2999, message = "年份最大为2999年") @Min(value = 1970, message = "年份最小为1970年") Integer year,
            @NotNull(message = "月份不能为空") @Max(value = 12, message = "月份最大为12月") @Min(value = 0, message = "月份最小为0月") Integer month
    ) {
        return Result.success(orderNutritionObjectivesService.allPerformance(year, month));
    }

}

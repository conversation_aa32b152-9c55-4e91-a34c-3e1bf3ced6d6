package com.stbella.order.server.context.component.assembler.additional;

import cn.hutool.core.collection.CollectionUtil;
import com.stbella.order.common.enums.order.OrderAdditionalKeyEnum;
import com.stbella.order.common.utils.PropertyValueHelper;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import com.stbella.platform.order.api.res.SkuAdditionalInfo;
import com.stbella.store.common.enums.core.GoodsTypeEnum;
import com.stbella.store.goodz.res.PropertyUserDefinedVO;
import com.stbella.store.goodz.res.PropertyValueListVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-06-19  11:16
 * @Description: 多胞胎服务费组装器
 * 数量=商品数量
 * 价格=门店配置价格 * （胎数-1）
 * 成本=门店配置成本 * （胎数-1）
 */
@Component
@Slf4j
public class MultipleBirthsFeeAdditionalItermAssembler implements AdditionalItermAssembler {

    /**
     * 过滤器， 判断sku所在分类的属性中 是否包含节假日服务费属性
     *
     * @param context
     * @return
     */
    @Override
    public boolean filter(AdditionalContext context) {
        if (CollectionUtil.isEmpty(context.getCategoryPropertyMap())) {
            return false;
        }
        if (Objects.isNull(context.getSku().getBackCategoryId())) {
            return false;
        }
        Long backCategoryId = context.getSku().getBackCategoryId().longValue();
        if (!context.getCategoryPropertyMap().containsKey(backCategoryId)) {
            return false;
        }
        PropertyUserDefinedVO backCategoryCustomProperty = context.getCategoryPropertyMap().get(backCategoryId);
        Optional<PropertyValueListVO> first = backCategoryCustomProperty.getPropertyValueListVOList().stream().filter(propertyValueListVO -> OrderAdditionalKeyEnum.MULTIPLE_BIRTHS.code().equals(propertyValueListVO.getCode())).findFirst();
        if (!first.isPresent()) {
            return false;
        }
        PropertyValueListVO propertyValueListVO = first.get();
        return PropertyValueHelper.getBooleanPropertyValue(propertyValueListVO.getPropertyValueId().get(0));
    }

    /**
     * 生成额外收费信息
     */
    @Override
    public SkuAdditionalInfo run(AdditionalContext bizContext) {

        CfgStoreEntity store = bizContext.getStore();
        SkuAdditionalInfo additional = new SkuAdditionalInfo()
                .setPropertyId(0L)
                .setGoodsType(GoodsTypeEnum.MULTIPLE_BIRTHS.code() + "")
                .setCode(OrderAdditionalKeyEnum.MULTIPLE_BIRTHS.code())
                .setName(OrderAdditionalKeyEnum.MULTIPLE_BIRTHS.desc())
                .setPrice(new BigDecimal(store.getMultipleBirthsPrice() / 100))
                .setCost(new BigDecimal(store.getMultipleBirthsCost() / 100));

        //计算价格： 最终的价格 = （fetusNum -1）* 配置的价格
        String fetusNumStr = bizContext.getFulfillExtraMap().getOrDefault(OrderAdditionalKeyEnum.FETUS_NUM.code(), "1");
        int fetusNum = Integer.parseInt(fetusNumStr);
        if (fetusNum <= 1) {
            //一个娃 不单独收费
            return null;
        }
        //额外收费胎数（总的-1）
        int feeFetusNum = fetusNum - 1;

        additional.setPrice(additional.getPrice());
        additional.setCost(additional.getCost());

        SkuDetailInfo skuDetailInfo = bizContext.getSku();
        int totalNum = skuDetailInfo.getNum() * skuDetailInfo.getSkuNum() * skuDetailInfo.getParentNum();
        additional.setNum(totalNum * feeFetusNum);


        log.info("多胎服务费：总胎数:{} ,收费胎数据: {}，总数量={}", fetusNumStr, feeFetusNum, totalNum);

        return additional;
    }
}

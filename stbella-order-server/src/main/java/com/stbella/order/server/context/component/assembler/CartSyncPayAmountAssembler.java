package com.stbella.order.server.context.component.assembler;

import cn.hutool.json.JSONUtil;
import com.stbella.order.domain.order.entity.HeCartEntity;
import com.stbella.order.domain.repository.HeCartRepository;
import com.stbella.order.server.order.CartSyncFact;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.platform.order.api.req.QueryCartReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import top.primecare.snowball.flow.annotation.SnowballComponent;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;


@Slf4j
@Component
@SnowballComponent(name = "将签单金额同步购物车", desc = "将签单金额同步购物车")
public class CartSyncPayAmountAssembler {


    @Resource
    private HeCartRepository heCartRepository;

    public void run(CartSyncFact cartSyncFact) {

        log.info("将签单金额同步购物车对应数据开始处理req:{}", JSONUtil.toJsonStr(cartSyncFact));
        HeCartEntity heCartEntity = heCartRepository.queryOne(QueryCartReq.builder()
                .cartId(cartSyncFact.getCartId())
                .orderType(cartSyncFact.getOrderType())
                .scene(cartSyncFact.getScene()).build());
        Assert.isTrue(Objects.nonNull(heCartEntity), "当前数据在购物车不存在，处理失败");
        heCartEntity.setPayAmount(RMBUtils.YTFInt(cartSyncFact.getPayAmount()));
        heCartEntity.setUpdateTime(new Date());
        heCartRepository.update(heCartEntity);
    }
}

package com.stbella.order.server.order.nutrition.service.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.stbella.core.base.Operator;
import com.stbella.core.base.PageVO;
import com.stbella.core.result.Result;
import com.stbella.customer.server.customer.enums.service.CustomerServiceContextEnum;
import com.stbella.customer.server.customer.enums.service.CustomerServiceTypeEnum;
import com.stbella.customer.server.customer.request.service.CustomerServicePushReq;
import com.stbella.customer.server.customer.service.CustomerServiceService;
import com.stbella.message.scene.req.SceneTriggerReq;
import com.stbella.order.common.constant.MsgConstant;
import com.stbella.order.common.enums.production.VerificationStatusEnum;
import com.stbella.order.domain.client.MessageClient;
import com.stbella.order.domain.client.StoreGoodsClient;
import com.stbella.order.infrastructure.config.BizConfig;
import com.stbella.order.infrastructure.repository.mapper.ecp.CfgStoreMapper;
import com.stbella.order.infrastructure.repository.po.CfgStorePO;
import com.stbella.order.server.order.month.enums.StoreTypeEnum;
import com.stbella.order.server.order.nutrition.config.ShopNoConf;
import com.stbella.order.server.order.nutrition.constant.NutritionDashboardConstant;
import com.stbella.order.server.order.nutrition.enums.OrderNutritionShopEnum;
import com.stbella.order.server.order.nutrition.enums.OrderNutritionSourceEnum;
import com.stbella.order.server.order.nutrition.service.OrderNutritionManagementKanbanService;
import com.stbella.order.server.order.nutrition.service.OrderNutritionObjectivesService;
import com.stbella.order.server.order.nutrition.service.WdtSpuService;
import com.stbella.order.server.order.nutrition.service.WdtSuiteService;
import com.stbella.order.server.order.order.req.BatchProductionExportReq;
import com.stbella.order.server.order.production.api.ProductionAppointmentCommandService;
import com.stbella.order.server.order.production.api.ProductionAppointmentQueryService;
import com.stbella.order.server.order.production.req.ProductionAppointmentBackQuery;
import com.stbella.order.server.order.production.res.ProductionAppointmentBackVo;
import com.stbella.order.server.utils.DateUtils;
import com.stbella.redis.service.RedisService;
import com.stbella.redisson.RedissonDistributedLocker;
import com.stbella.store.goodz.res.GoodsImageAndServiceTypeVO;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: JobHandler
 * @date 2022/2/16 2:07 下午
 */
@Component
@Slf4j
public class JobHandler {

    @Resource
    private RedissonDistributedLocker locker;
    @Resource
    private RedisService redisService;
    @Resource
    private OrderNutritionObjectivesService orderNutritionObjectivesService;
    @Resource
    private OrderNutritionManagementKanbanService orderNutritionManagementKanbanService;
    @Resource
    private WdtSpuService wdtSpuService;
    @Resource
    private WdtSuiteService wdtSuiteService;
    @Resource
    private ProductionAppointmentCommandService productionAppointmentCommandService;
    @Resource
    private ProductionAppointmentQueryService productionAppointmentQueryService;
    @Resource
    private CfgStoreMapper cfgStoreMapper;
    @Resource
    private StoreGoodsClient storeGoodsClient;
    @Resource
    private BizConfig bizConfig;

    @DubboReference(timeout = 6000)
    private CustomerServiceService customerServiceService;

    @Resource
    private MessageClient messageClient;

    @Value("${yz.enable}")
    private Integer enable;

    @Value("${nutrition.refresh}")
    private Integer refresh;

    private static String LOCK_KEY = "order:nutrition:bao";
    private static String YZ_LOCK = "order:yz:order";
    private static String JD_LOCK = "order:jd:order";

    /**
     * 定时营养报单
     * cron 表达式，每天11点 执行 0 0 11 * * ?
     */
    @XxlJob("doTask")
    public void doTask() {
        try {
            boolean b = locker.tryLock(LOCK_KEY, TimeUnit.SECONDS, 30, 30);
            if (b) {
                String day = DateUtil.today();
                String redisKey = LOCK_KEY + ":" + day;
                log.info("redisKey={}", redisKey);
                XxlJobHelper.log("redisKey={}", redisKey);
                String cacheObject = redisService.getCacheObject(redisKey);
                if (ObjectUtil.isEmpty(cacheObject)) {
                    log.info("定时营养报单开始" + DateUtil.now());
                    XxlJobHelper.log("定时营养报单开始={}", DateUtil.now());
                    //调用营养业务钉钉报单
                    orderNutritionObjectivesService.getPerformanceObjectives();
                    log.info("定时营养报单结束" + DateUtil.now());
                    XxlJobHelper.log("定时营养报单结束={}", DateUtil.now());
                    redisService.setCacheObject(redisKey, day);
                } else {
                    return;
                }
            }
        } catch (Exception e) {
            log.info("定时营养报单执行异常,e={}", e.getMessage());
            XxlJobHelper.log("定时营养报单执行异常,e={}", e.getMessage());
        } finally {
            locker.unlock(LOCK_KEY);
        }
    }

    /**
     * 有赞获取订单（每天8点开始）
     */
//    @XxlJob("geYzOrder")
    public void geYzOrder() {
        try {

            if (ObjectUtil.isNotNull(enable) && enable == 1) {
                boolean b = locker.tryLock(YZ_LOCK, TimeUnit.SECONDS, 30, 30);

                if (b) {
                    String day = DateUtil.today();
                    String redisKey = YZ_LOCK + ":" + day;
                    log.info("redisKey={}", redisKey);
                    XxlJobHelper.log("redisKey={}", redisKey);
                    String cacheObject = redisService.getCacheObject(redisKey);
                    if (ObjectUtil.isEmpty(cacheObject)) {
                        log.info("获取有赞订单开始" + DateUtil.now());
                        XxlJobHelper.log("获取有赞订单开始" + DateUtil.now());
                        //调用营养业务钉钉报单
                        orderNutritionObjectivesService.getYzOrder(DateUtils.getYesterdayStart(), DateUtils.getTodayStart());
                        log.info("获取有赞订单结束" + DateUtil.now());
                        XxlJobHelper.log("获取有赞订单结束" + DateUtil.now());
                        redisService.setCacheObject(redisKey, day);
                    } else {
                        return;
                    }
                }
            } else {
                log.info("未开启获取有赞订单数据");
                XxlJobHelper.log("未开启获取有赞订单数据");
            }

        } catch (Exception e) {
            log.info("获取有赞订单异常,e={}", e.getMessage());
            XxlJobHelper.log("获取有赞订单异常,e={}", e.getMessage());
        } finally {
            locker.unlock(YZ_LOCK);
        }
    }

    /**
     * 刷新-营养数据看板（每天11点开始）
     */
    @XxlJob("refreshGetConsumption")
    public void refreshGetConsumption() {
        try {
            if (ObjectUtil.isNotNull(refresh) && refresh == 1) {

                boolean b = locker.tryLock(NutritionDashboardConstant.DASHBOARD_LOCK, TimeUnit.SECONDS, 30, 30);
                if (b) {
                    log.info("刷新-营养数据看板开始");
                    XxlJobHelper.log("刷新-营养数据看板开始");
                    redisService.deleteByPre(NutritionDashboardConstant.CUSTOMER_LIFE_TIME_KEY);
                    for (OrderNutritionSourceEnum o : OrderNutritionSourceEnum.values()) {
                        //自动获取一次
                        orderNutritionManagementKanbanService.getAllNewCustomer(o.getCode());
                    }
                    log.info("刷新-营养数据看板结束");
                    XxlJobHelper.log("刷新-营养数据看板结束");
                }
            }
        } catch (Exception e) {
            log.info("定时任务-刷新-营养数据看板异常,e={}", e.getMessage());
            XxlJobHelper.log("定时任务-刷新-营养数据看板异常,e={}", e.getMessage());
        } finally {
            locker.unlock(NutritionDashboardConstant.DASHBOARD_LOCK);
        }
    }

    @Resource
    private ShopNoConf shopNoConf;

    /**
     * 获取京东（获取旺店通订单）
     */
    @XxlJob("getJDOrder")
    public void getJDOrder() {
        String msg = "旺店通获取订单";
        try {

            if (ObjectUtil.isNotNull(enable) && enable == 1) {

                String day = DateUtil.today();
                String redisKey = JD_LOCK + ":" + day;
                log.info("redisKey={}", redisKey);
                XxlJobHelper.log("redisKey={}", redisKey);
                String cacheObject = redisService.getCacheObject(redisKey);
                if (ObjectUtil.isEmpty(cacheObject)) {
                    log.info(msg + "开始" + DateUtil.now());
                    XxlJobHelper.log(msg + "开始" + DateUtil.now());
                    String[] jd = shopNoConf.getJd();
                    for (OrderNutritionShopEnum value : OrderNutritionShopEnum.values()) {
                        orderNutritionObjectivesService.getJdOrder(DateUtils.getYesterdayStart(), DateUtils.getTodayStart(), value);
                    }
                    log.info(msg + "结束" + DateUtil.now());
                    XxlJobHelper.log(msg + "结束" + DateUtil.now());
                    redisService.setCacheObject(redisKey, day);
                } else {
                    return;
                }
            } else {
                log.info("未开启" + msg + "数据");
                XxlJobHelper.log("未开启" + msg + "数据");
            }

        } catch (Exception e) {
            log.info(msg + "异常,e={}", e.getMessage());
            XxlJobHelper.log(msg + "异常,e={}", e.getMessage());
        }
    }

    /**
     * 获取旺店通商品的数据
     */
    @XxlJob("getGoodsList")
    public void getGoodsList() {
        String msg = "获取旺店通商品的数据";
        try {
            Date end = new Date();
            //获取当前时间-30天的数据
            wdtSpuService.getSpuList(new Date(DateUtil.offsetDay(end, -30).getTime()), end);
        } catch (Exception e) {
            log.info(msg + "异常,e={}", e.getMessage());
            XxlJobHelper.log(msg + "异常,e={}", e.getMessage());
        }
    }

    /**
     * 获取旺店通组合装的数据
     */
    @XxlJob("getSuiteList")
    public void getSuiteList() {
        String msg = "获取旺店通组合装的数据";
        try {
            Date end = new Date();
            //获取当前时间-30天的数据
            wdtSuiteService.getSuiteList(new Date(DateUtil.offsetDay(end, -30).getTime()), end);
        } catch (Exception e) {
            log.info(msg + "异常,e={}", e.getMessage());
            XxlJobHelper.log(msg + "异常,e={}", e.getMessage());
        }
    }

    /**
     * 定时失效用户的产康资产
     */
    @XxlJob("expireUserProductionData")
    public void expireUserProductionData() {
        String msg = "定时失效用户的产康资产";
        try {
            productionAppointmentCommandService.expireUserProductionData();
        } catch (Exception e) {
            log.info(msg + "异常,e={}", e.getMessage());
            XxlJobHelper.log(msg + "异常,e={}", e.getMessage());
        }

    }

    /**
     * 推送产康客户服务记录
     */
    @XxlJob("pushProductionCustomerServices")
    public void pushProductionCustomerServices() {
        Date targetDate = null;
        try {
            String jobParam = XxlJobHelper.getJobParam();
            log.info("pushProductionCustomerServices param:{}", jobParam);
            if (StringUtils.isNotBlank(jobParam)) {
                targetDate = DateUtils.parse(jobParam, DateUtils.YYYY_MM_DD);
            }
        } catch (Exception e) {
            log.error("pushProductionCustomerServices param error", e);
        }

        if (Objects.isNull(targetDate)) targetDate = DateUtils.getTodayStart();

        // 扫描的开始结束日期
        Date startDate = DateUtils.offsetDay(targetDate, -1);
        Date endDate = targetDate;

        this.pushProductionCustomerServices(startDate, endDate);
    }


    public void pushProductionCustomerServices(Date startDate, Date endDate) {
        // 获取已核销且在核销时间范围内的预约单数据
        ProductionAppointmentBackQuery backQuery = new ProductionAppointmentBackQuery();
        backQuery.setWriteOffStart(startDate);
        backQuery.setWriteOffEnd(endDate);
        backQuery.setVerificationState(VerificationStatusEnum.DONE_WRITE_OFF.code());
        backQuery.setPageSize(100000);
        backQuery.setPageNum(1);
        backQuery.setStoreAuth(false);
        Operator operator = new Operator();
        operator.setOperatorGuid("-1");
        operator.setOperatorName("sys");
        backQuery.setOperator(operator);

        Result<PageVO<ProductionAppointmentBackVo>> pageResult = productionAppointmentQueryService.queryAppointmentBackPage(backQuery);
        PageVO<ProductionAppointmentBackVo> pageVO = pageResult.getData();

        // 待同步的核销单数据
        List<ProductionAppointmentBackVo> appointments = pageVO.getList();
        log.info("pushProductionCustomerServices appointments size:{}", appointments.size());
        if (CollectionUtil.isEmpty(appointments)) return;

        List<CfgStorePO> stores = cfgStoreMapper.selectList(new LambdaQueryWrapper<CfgStorePO>().select(CfgStorePO::getStoreId, CfgStorePO::getStoreName, CfgStorePO::getType));

        List<Integer> goodsIds = appointments.stream().map(ProductionAppointmentBackVo::getProductionGoodsId).distinct().collect(Collectors.toList());
        List<GoodsImageAndServiceTypeVO> goodsImageAndServiceTypeVOS = storeGoodsClient.goodsImageAndServiceType(goodsIds);

        // 分割集合
        List<List<ProductionAppointmentBackVo>> splitList = ListUtil.split(appointments, 10);
        splitList.forEach(list -> {
            List<CustomerServicePushReq> pushReqList = list.stream().map(appointment -> {
                CustomerServicePushReq push = new CustomerServicePushReq();
                push.setStoreId(appointment.getStoreId().longValue());
                push.setClientId(appointment.getClientId().longValue());
                push.setTypeSign(CustomerServiceTypeEnum.CK.getCode());
                push.setServiceDate(DateUtils.parseDate(appointment.getServeDate()));
                push.setBelongDate(DateUtils.parseDate(appointment.getServeDate()));
                push.setBizId(appointment.getOrderSn());

                // 门店信息
                Optional<CfgStorePO> first = stores.stream().filter(o -> Objects.equals(o.getStoreId(), appointment.getStoreId())).findFirst();
                String storeName = first.isPresent() ? first.get().getStoreName() : "unknown";

                Optional<GoodsImageAndServiceTypeVO> goodsOpt = goodsImageAndServiceTypeVOS.stream().filter(o -> Objects.equals(o.getId(), appointment.getProductionGoodsId())).findFirst();
                String avatar = goodsOpt.isPresent() ? goodsOpt.get().getImage() : "";

                push.setContexts(Arrays.asList(
                        // 预约单号
                        new CustomerServicePushReq.Context(CustomerServiceContextEnum.CK001.getCode(), appointment.getOrderSn()),
                        // 项目名称
                        new CustomerServicePushReq.Context(CustomerServiceContextEnum.CK002.getCode(), appointment.getProductionGoodsName()),
                        // 门店
                        new CustomerServicePushReq.Context(CustomerServiceContextEnum.CK003.getCode(), storeName),
                        // 预约方式
                        new CustomerServicePushReq.Context(CustomerServiceContextEnum.CK004.getCode(), appointment.getBookTypeName()),
                        // 核销时间
                        new CustomerServicePushReq.Context(CustomerServiceContextEnum.CK005.getCode(), DateUtils.format(appointment.getWriteOffDate(), DateUtils.format)),
                        // 预约人
                        new CustomerServicePushReq.Context(CustomerServiceContextEnum.CK006.getCode(), appointment.getTherapistName()),
                        // goodsId
                        new CustomerServicePushReq.Context(CustomerServiceContextEnum.CK007.getCode(), appointment.getProductionGoodsId().toString()),
                        // 产康师id
                        new CustomerServicePushReq.Context(CustomerServiceContextEnum.CK008.getCode(), appointment.getTherapistId().toString()),
                        // 商品类型 三方/自营
                        new CustomerServicePushReq.Context(CustomerServiceContextEnum.CK009.getCode(), avatar),
                        // 商品主图
                        new CustomerServicePushReq.Context(CustomerServiceContextEnum.CK010.getCode(), appointment.getServeType().toString())
                ));
                return push;
            }).collect(Collectors.toList());

            customerServiceService.push(pushReqList);
        });
    }

    @XxlJob("therapistRealPaidNotice")
    public void therapistRealPaidNotice() {
        Date[] lastMonthFirstDayAndLastDay = DateUtils.getLastMonthFirstDayAndLastDay(LocalDate.now());

        List<CfgStorePO> stores = cfgStoreMapper.selectList(
                new LambdaQueryWrapper<CfgStorePO>()
                        .in(CfgStorePO::getType, Arrays.asList(0, 1, 100))
                        .notIn(CfgStorePO::getStoreId, Arrays.asList(1011, 1005))
                        .eq(CfgStorePO::getActive, 1)
                        .orderByAsc(CfgStorePO::getStoreName)
        );

        BatchProductionExportReq req = new BatchProductionExportReq();
        req.setStoreIds(stores.stream().map(CfgStorePO::getStoreId).collect(Collectors.toList()));
        req.setWriteOffStartDate(lastMonthFirstDayAndLastDay[0]);
        req.setWriteOffEndDate(lastMonthFirstDayAndLastDay[1]);
        req.setEmail(bizConfig.getTherapistRealPaidNoticeEmail());

        productionAppointmentQueryService.batchExportTherapistRealPaid(req);
    }
}

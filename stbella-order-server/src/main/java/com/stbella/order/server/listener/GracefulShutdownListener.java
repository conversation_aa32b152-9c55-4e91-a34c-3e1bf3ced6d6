package com.stbella.order.server.listener;

import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.PropertyKeyConst;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Properties;


/**
 * @Classname GracefulShutdownListener
 * @Description 服务优雅下线（设置权重为0）， 消除发布过程的服务不可用的情况
 * @Date 2022/10/11 17:49
 * @Created by Jacky
 */
@Component
@Slf4j
public class GracefulShutdownListener implements ApplicationListener<ContextClosedEvent> {

    @Resource
    private NacosDiscoveryProperties nacosDiscoveryProperties;

    @Override
    public void onApplicationEvent(ContextClosedEvent contextClosedEvent) {

        log.info("收到容器关闭事件，开始设置实例 权重");
        Properties properties = new Properties();
        properties.put(PropertyKeyConst.NAMESPACE, nacosDiscoveryProperties.getNamespace());
        properties.put(PropertyKeyConst.SERVER_ADDR, nacosDiscoveryProperties.getServerAddr());
        String serviceName = nacosDiscoveryProperties.getService();
        NamingService namingService = null;
        try {
            namingService = NacosFactory.createNamingService(properties);
            List<Instance> instanceList = namingService.getAllInstances(serviceName);
            for (Instance instance : instanceList) {
                log.info(instance.toString());
                if (instance.getIp().equals(nacosDiscoveryProperties.getIp())) {
                    namingService.deregisterInstance(serviceName, instance);
                }
            }
        } catch (NacosException e) {
            log.error("服务优雅下线异常", e);
        }

        log.info("收到容器关闭事件 Nacos 服务权重为 0");
    }
}

package com.stbella.order.server.strategy.approval;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.base.server.sms.enums.SmsTemplateV2Enum;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.month.server.request.PaperContractApprovalRequest;
import com.stbella.month.server.request.RefundApprovalOfflineRemittanceRequest;
import com.stbella.order.common.enums.month.AheadOutRoomEnum;
import com.stbella.order.common.enums.month.RefundRecordPayStatusEnum;
import com.stbella.order.common.utils.JsonUtil;
import com.stbella.order.domain.order.month.entity.AheadOutRoomEntity;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.repository.AheadOutRoomRepository;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.domain.repository.OrderRefundRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.async.AsyncOrder;
import com.stbella.order.server.manager.SmsManager;
import com.stbella.order.server.order.month.req.AheadOutRoomQuery;
import com.stbella.order.server.order.month.request.approval.ApprovalStatusInfo;
import com.stbella.order.server.order.month.service.HeOrderService;
import com.stbella.order.server.order.month.service.MonthOrderWxCommandService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 线下退款
 * @date 2024/3/18 17:45
 */
@Component
@Slf4j
public class OfflineRemittanceRefundApprovalStrategy implements ApprovalStrategy {
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private SmsManager smsManager;
    @Resource
    private AheadOutRoomRepository aheadOutRoomRepository;
    @Resource
    private AsyncOrder asyncOrder;
    @Resource
    @Lazy
    private MonthOrderWxCommandService monthOrderWxCommandService;

    @Resource
    private HeOrderService heOrderService;

    @Override
    public void handleApproval(OrderApproveRecordTypeEnum type, String params, String messageId, ApprovalStatusInfo approvalStatusInfo, String processId) {
        RefundApprovalOfflineRemittanceRequest refundApprovalOfflineRemittanceRequest = JSONObject.parseObject(params, RefundApprovalOfflineRemittanceRequest.class);
        //修改退款审批状态
        HeOrderRefundEntity heOrderRefundEntity2 = orderRefundRepository.getOneById(refundApprovalOfflineRemittanceRequest.getOrderRefundId());
        Integer applyAmount = heOrderRefundEntity2.getApplyAmount();
        if (ObjectUtil.isEmpty(heOrderRefundEntity2)) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "钉钉审批完成退款失败,找不到对应的退款记录,退款ID=" + refundApprovalOfflineRemittanceRequest.getOrderRefundId());
        }
        HeIncomeRecordEntity incomeRecordEntity = incomeRecordRepository.getOneById(heOrderRefundEntity2.getOrderGoodId());
        boolean agree = approvalStatusInfo.isAgree();
        boolean finish = approvalStatusInfo.isFinish();
        boolean refuse = approvalStatusInfo.isRefuse();
        boolean terminate = approvalStatusInfo.isTerminate();
        boolean isRefundBoolean = false;
        //发起人手机号
        String originatorPhone = refundApprovalOfflineRemittanceRequest.getPhone();
        if (finish && agree) {
            heOrderRefundEntity2.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_3.getCode());
            isRefundBoolean = true;
            try {
                log.info("审批同意发送短信通知（线下汇款）:" + originatorPhone);
                smsManager.sendMessage(originatorPhone, null, null, SmsTemplateV2Enum.HELPER_REFUND_OFFLINE_APPLY_PASS_USER, new String[]{refundApprovalOfflineRemittanceRequest.getCustomerName()});
            } catch (Exception e) {
                log.error("审批同意发送短信通知（线下汇款）异常：{}，手机号：{}，内容：{}", e.getMessage(), originatorPhone, new String[]{refundApprovalOfflineRemittanceRequest.getCustomerName()});
            }
        } else if (finish && refuse) {
            heOrderRefundEntity2.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_2.getCode());
            try {
                log.info("审批拒绝发送短信通知（线下汇款）:" + originatorPhone);
                smsManager.sendMessage(originatorPhone, null, null, SmsTemplateV2Enum.HELPER_REFUND_APPLY_REFUSE_USER, new String[]{refundApprovalOfflineRemittanceRequest.getCustomerName()});
            } catch (Exception e) {
                log.error("审批拒绝发送短信通知（线下汇款）异常：{}，手机号：{}，内容：{}", e.getMessage(), originatorPhone, new String[]{refundApprovalOfflineRemittanceRequest.getCustomerName()});
            }
        } else if (terminate) {
            heOrderRefundEntity2.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_2.getCode());
        }
        HeOrderEntity heOrder1 = orderRepository.getByOrderId(heOrderRefundEntity2.getOrderId());
        heOrder1.setRefundStatus(heOrderService.getRefundStatusByActualAmount(heOrder1.getOrderId(), applyAmount, isRefundBoolean, false));
        orderRepository.updateOrderMonthByOrderId(heOrder1);
        orderRefundRepository.updateOneById(heOrderRefundEntity2);
        if (!isRefundBoolean) {
            incomeRecordEntity.setFreezeAmount(incomeRecordEntity.getFreezeAmount() - applyAmount);
            //释放支付记录冻结金额
            incomeRecordRepository.updateRecord(incomeRecordEntity);
            //修改提前离馆可退金额
            AheadOutRoomQuery query = new AheadOutRoomQuery();
            query.setOrderId(heOrderRefundEntity2.getOrderId());
            query.setState(AheadOutRoomEnum.STATE_DISENABLE.code());
            AheadOutRoomEntity aheadOutRoomEntity = aheadOutRoomRepository.queryByOrderId(query);
            //如果提前离馆同意时间早于退款申请时间则要修改提前离馆可退金额
            if (ObjectUtil.isNotEmpty(aheadOutRoomEntity) && aheadOutRoomEntity.getAgreeAt().before(DateUtil.date(heOrderRefundEntity2.getCreatedAt() * 1000))) {
                aheadOutRoomEntity.setRemainingRefundableAmount(aheadOutRoomEntity.getRemainingRefundableAmount() + applyAmount);
                aheadOutRoomRepository.updateOutRoom(aheadOutRoomEntity);
            }

        }
        //异步退款-积分处理
        HeOrderRefundEntity orderRefund1 = orderRefundRepository.getOneById(heOrderRefundEntity2.getId());
        log.info("退款审批（线下汇款）-积分处理,订单参数={},退款参数={}", heOrder1, orderRefund1);
        asyncOrder.refundDeductIntegral(heOrder1, orderRefund1);
    }

    @Override
    public OrderApproveRecordTypeEnum getOrderApproveRecordTypeEnum() {
        return OrderApproveRecordTypeEnum.OFFLINE_REMITTANCE_FOR_REFUND_APPROVAL;
    }
}

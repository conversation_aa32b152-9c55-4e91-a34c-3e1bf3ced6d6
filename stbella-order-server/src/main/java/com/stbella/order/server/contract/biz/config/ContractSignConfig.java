package com.stbella.order.server.contract.biz.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "cts.order.contract.sign")
public class ContractSignConfig {

    private Map<Integer, Boolean> mapper = new HashMap<>();
}

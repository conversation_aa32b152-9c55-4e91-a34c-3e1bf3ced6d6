package com.stbella.order.server.manager;

import com.alibaba.fastjson.JSONObject;
import com.stbella.base.server.approval.ApprovalService;
import com.stbella.base.server.ding.request.ApprovalCreateReq;
import com.stbella.base.server.ding.request.Old2NewOrderRequest;
import com.stbella.base.server.ding.response.CreateOrderApproveRecordVO;
import com.stbella.base.server.test.ToolsService;
import com.stbella.base.server.test.req.SwapPhoneReq;
import com.stbella.core.result.Result;
import com.stbella.notice.server.OaProcessIdRelationService;
import com.stbella.order.server.contract.dto.Old2NewOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 门店调用
 * @date 2021/12/6 3:06 下午
 */
@Component
@Slf4j
public class BaseManager {

    @DubboReference
    private ToolsService toolsService;
    @DubboReference
    private OaProcessIdRelationService oaProcessIdRelationService;

    @DubboReference
    private ApprovalService approvalService;


    public Result<String> swapPhone(SwapPhoneReq req) {
        Result<String> stringResult = toolsService.swapPhone(req);
        return stringResult;
    }

    public boolean old2New(List<Old2NewOrderDTO> processIdList, Integer orderId) {
        List<Old2NewOrderRequest> requestList = new ArrayList<>();
        for (Old2NewOrderDTO dto : processIdList) {
            Old2NewOrderRequest request = new Old2NewOrderRequest();
            request.setProcessId(dto.getDdInstanceId());
            request.setType(dto.getType());
            request.setOrderId(orderId);
            request.setId(dto.getId());
            request.setOrderRefundId(dto.getOrderRefundId());
            requestList.add(request);
        }
        Boolean aBoolean = oaProcessIdRelationService.old2NewOrder(requestList);
        return aBoolean;

    }

    public void createApproval(ApprovalCreateReq approvalCreateReq) {
        log.info("create approval req {}", JSONObject.toJSONString(approvalCreateReq));
        Result<CreateOrderApproveRecordVO> approvalByCy = approvalService.createApprovalByCy(approvalCreateReq);
        log.info("create approval res {}", JSONObject.toJSONString(approvalByCy));
        return;
    }




}

package com.stbella.order.server.manager;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.core.result.Result;
import com.stbella.customer.server.scrm.dto.OrderReductionRecordDTO;
import com.stbella.customer.server.scrm.dto.ScrmCustomerDTO;
import com.stbella.customer.server.scrm.service.ScrmCustomerOrderService;
import com.stbella.customer.server.scrm.service.ScrmCustomerService;
import com.stbella.customer.server.scrm.service.ScrmOrderService;
import com.stbella.order.common.constant.PhpApiConstant;
import com.stbella.order.server.convert.TabClientConvert;
import com.stbella.order.server.order.month.req.OrderMonthClientReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: ScrmManager
 * @date 2023/4/17 13:59
 */
@Component
@Slf4j
public class ScrmManager {
    @Resource
    private TabClientConvert tabClientConvert;
    @Resource
    private TabClientManager tabClientManager;
    @DubboReference(timeout = 180000)
    private ScrmCustomerService scrmCustomerService;
    @DubboReference(timeout = 180000)
    private ScrmCustomerOrderService scrmCustomerOrderService;
    @DubboReference
    private ScrmOrderService scrmOrderService;


    /**
     * 客户信息推送SCRM 废弃
     */
    @Async
    public Long pushScrmCustomer(Integer clientUid) {
        OrderMonthClientReq clientInfoById = tabClientManager.getClientInfoById(clientUid);
        ScrmCustomerDTO scrmCustomerDTO = tabClientConvert.orderMonthClientReq2ScrmCustomerDTO(clientInfoById);
        log.info("scrmCustomerDTO 信息=> {}", JSONUtil.toJsonStr(scrmCustomerDTO));
        if (ObjectUtil.isNotEmpty(scrmCustomerDTO.getScrmCustomerId())) {
            Result<Long> longResult = scrmCustomerService.php2Custom2Scrm(scrmCustomerDTO);
            return longResult.getData();
        }
        return null;
    }

    /**
     * 只有订单修改时,并且是业绩生效时才推送
     * 订单信息推送SCRM
     */
    @Async
    public void pushScrmCustomerOrder(Integer orderId) {
        String url = PhpApiConstant.phpApiUrl + "/" + PhpApiConstant.SYNC_ORDER_SCRM + "/" + orderId;
        log.info("修改订单,订单信息推送SCRM,url={}", url);
        String s = HttpUtil.get(url);
        log.info("修改订单,订单信息推送SCRM,结果={}", JSONUtil.parse(s));
    }

    @Async
    public void phpPushScrmCustomer(Integer clientUid) {
        String url = PhpApiConstant.phpApiUrl + "/" + PhpApiConstant.SYNC_CUSTOMER_SCRM + "/" + clientUid + "/1";
        log.info("创建/修改订单,客户信息推送SCRM,url={}", url);
        String s = HttpUtil.get(url);
        log.info("创建/订单,客户信息推送SCRM,结果={}", JSONUtil.parse(s));
    }

    /**
     * 同步减免信息
     *
     * @param recordDTO
     */
    @Async
    public void omniOrderReductionRecordSyncScrm(OrderReductionRecordDTO recordDTO) {
        scrmOrderService.ominOrderReducionRecordSyncScrm(recordDTO);
    }
}

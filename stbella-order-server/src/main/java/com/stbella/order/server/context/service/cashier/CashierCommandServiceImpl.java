package com.stbella.order.server.context.service.cashier;

import cn.hutool.json.JSONObject;
import com.stbella.core.enums.BusinessEnum;
import com.stbella.core.result.Result;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.common.utils.BPCheckUtil;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.context.component.processor.cachier.CreatePosPayQrcodeProcessor;
import com.stbella.order.server.order.month.req.PayReqV2;
import com.stbella.order.server.order.month.request.standard.QrCodeRequest;
import com.stbella.platform.order.api.cashier.CashierCommandService;
import com.stbella.platform.order.api.req.OmniOfflinePayRequest;
import com.stbella.platform.order.api.req.PosPayQrCodeRequest;
import com.stbella.platform.order.api.res.ReceiveQrcodeRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.FlowIdentity;

import javax.annotation.Resource;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-07-05  13:19
 * @Description: 收银台命令服务
 */
@Slf4j
@Service
@DubboService
public class CashierCommandServiceImpl implements CashierCommandService {

    @Resource
    CreatePosPayQrcodeProcessor createPosPayQrcodeProcessor;
    @Resource
    OrderRepository orderRepository;

    /**
     * 创建收银台聚合收款二维码
     *
     * @param request
     * @return
     */
    @Override
    public Result<ReceiveQrcodeRes> createReceiveQrcode(QrCodeRequest request) {


        HeOrderEntity orderEntity = orderRepository.getByOrderId(Integer.valueOf(request.getOrderNo()));

        FlowIdentity identity = FlowIdentity.builder().bizActivity(BizActivityEnum.CREATE_ORDER_RECEIVE_QRCODE.code())
                .idSlice(BizActivityEnum.CREATE_ORDER_RECEIVE_QRCODE.name())
                .build();

        FlowContext context = new FlowContext();
        context.setAttribute(QrCodeRequest.class, request);
        context.setAttribute(HeOrderEntity.class, orderEntity);
        SnowballFlowLauncher.fire(identity, context);
        ReceiveQrcodeRes qrcodeRes = context.getAttribute(ReceiveQrcodeRes.class);

        return Result.success(qrcodeRes);
    }

    /**
     * 创建pos支付二维码
     * pos 机扫此码，用户刷卡。
     *
     * @param request
     * @return
     */
    @Override
    public Result<ReceiveQrcodeRes> createPosPayQrcode(PosPayQrCodeRequest request) {
        BPCheckUtil.checkEmptyInBean(new String[]{"incomeSn", "bizType", "operator"}, request, false);

        FlowContext context = new FlowContext();
        context.setAttribute(PosPayQrCodeRequest.class, request);
        SnowballFlowLauncher.fire(context, createPosPayQrcodeProcessor);
        ReceiveQrcodeRes qrcodeRes = context.getAttribute(ReceiveQrcodeRes.class);

        return Result.success(qrcodeRes);
    }

    /**
     * 在线支付
     *
     * @param request
     * @return
     */
    @Override
    public Result<JSONObject> onlinePay(PayReqV2 request) {

        BPCheckUtil.checkEmptyInBean(new String[]{"orderId", "accountType"}, request, false);

        HeOrderEntity orderEntity = orderRepository.getByOrderId(request.getOrderId());
        BusinessEnum bu = BusinessEnum.getEnumByCode(orderEntity.getBu());
        OmniPayTypeEnum payTypeEnum = OmniPayTypeEnum.getByCode(request.getPayType());

        assert payTypeEnum != null;
        FlowIdentity identity = FlowIdentity.builder().bizActivity(BizActivityEnum.PAY_ORDER.code())
                .idSlice(bu.name())
                .idSlice(payTypeEnum.name())
                .build();

        FlowContext context = new FlowContext();
        context.setAttribute(PayReqV2.class, request);
        context.setAttribute(HeOrderEntity.class, orderEntity);
        SnowballFlowLauncher.fire(identity, context);

        JSONObject attribute = context.getAttribute(JSONObject.class);

        return Result.success(attribute);
    }

    /**
     * 线下支付支付
     * flow:offline_pay_order
     * @see "order_offline_pay.xml"
     * @param request
     * @return
     */
    @Override
    public Result<Integer> offlinePay(OmniOfflinePayRequest request) {

        BPCheckUtil.checkEmptyInBean(new String[]{"orderId", "payProof", "operator"}, request, false);

        HeOrderEntity orderEntity = orderRepository.getByOrderId(request.getOrderId());
        BusinessEnum bu = BusinessEnum.getEnumByCode(orderEntity.getBu());
        OmniPayTypeEnum payTypeEnum = OmniPayTypeEnum.getByCode(request.getPayType());

        FlowIdentity identity = FlowIdentity.builder().bizActivity(BizActivityEnum.PAY_ORDER.code())
                .idSlice(bu.name())
                .idSlice(payTypeEnum.name())
                .build();

        FlowContext context = new FlowContext();

        //支付记录生成依赖此对象
        PayReqV2 basePayReq = BeanMapper.map(request, PayReqV2.class);
        context.setAttribute(PayReqV2.class, basePayReq);
        context.setAttribute(OmniOfflinePayRequest.class, request);
        context.setAttribute(HeOrderEntity.class, orderEntity);
        SnowballFlowLauncher.fire(identity, context);

        HeIncomeRecordEntity incomeRecord = context.getAttribute(HeIncomeRecordEntity.class);

        return Result.success(incomeRecord.getId());
    }
}

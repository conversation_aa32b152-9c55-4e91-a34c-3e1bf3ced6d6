package com.stbella.order.server.context.component.complaints.processor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.core.result.Result;
import com.stbella.order.common.constant.CustomerComplaintConstant;
import com.stbella.order.common.enums.core.OmniRefundTypeEnum;
import com.stbella.order.common.enums.order.CustomerComplaintsRefundEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.repository.HeCustomerComplaintsRepository;
import com.stbella.order.domain.repository.OrderRefundRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.infrastructure.repository.converter.OrderRefundConverter;
import com.stbella.order.server.order.month.req.OfflineRemittanceRequest;
import com.stbella.order.server.order.month.req.SubmitRefundApplyV2Request;
import com.stbella.order.server.order.month.service.OrderPayV2Service;
import com.stbella.order.server.utils.IdGenUtils;
import com.stbella.platform.order.api.refund.api.OrderRefundService;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import com.stbella.platform.order.api.req.CustomerComplaintsCreateRefundReq;
import com.stbella.platform.order.api.req.CustomerComplaintsCreateReq;
import com.stbella.platform.order.api.req.ExtraRefundTypeReq;
import com.stbella.platform.order.api.res.CreateApproveRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
@SnowballComponent(name = "创建新旧退款", desc = "创建新旧退款")
public class ComplaintRefundCreateProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderRefundService orderRefundService;
    @Resource
    private OrderPayV2Service orderPayV2Service;
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private OrderRefundConverter orderRefundConverter;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    HeCustomerComplaintsRepository heCustomerComplaintsRepository;
    @Resource
    private OrderRefundService refundService;

    @Override
    public void run(FlowContext bizContext) {
        CustomerComplaintsCreateReq attribute = bizContext.getAttribute(CustomerComplaintsCreateReq.class);

        CustomerComplaintsCreateRefundReq createRefundReq = attribute.getCreateRefundReq();

        HeCustomerComplaintsEntity customerComplaintsEntity = bizContext.getAttribute(HeCustomerComplaintsEntity.class);

        if (ObjectUtil.isNotEmpty(customerComplaintsEntity) && ObjectUtil.isNotEmpty(customerComplaintsEntity.getCompensationOrderId())) {
            HeOrderEntity compensationOrder = orderRepository.getByOrderId(customerComplaintsEntity.getCompensationOrderId().intValue());
            bizContext.setAttribute(CustomerComplaintConstant.COMPLAINT_ZERO_ORDER, compensationOrder);
        }

        HeOrderEntity orderEntity = orderRepository.getByOrderId(attribute.getOrderId().intValue());

        boolean newOrder = ObjectUtil.isNotEmpty(orderEntity.getVersion()) && orderEntity.getVersion().compareTo(new BigDecimal(3.0)) >= 0;

        final BigDecimal[] refundAmount = {BigDecimal.ZERO};

        Integer refundId = null;

        Integer refundType = 0;

        if (newOrder) {
            //新退款
            CreateRefundReq newRefund = createRefundReq.getNewCreateRefundReq();
            if (Objects.nonNull(newRefund) && Objects.nonNull(newRefund.getOrderId())) {
                newRefund.setComplaintId(customerComplaintsEntity.getId());
                newRefund.setOperator(attribute.getOperator());
                Result<CreateApproveRes> createApproveResResult = orderRefundService.create(newRefund);
                refundId = createApproveResResult.getData().getRefundId();
                refundType = newRefund.getRefundType();
            }
        } else {
            //旧退款
            SubmitRefundApplyV2Request oldRefund = createRefundReq.getOldCreateRefundReq();
            if (Objects.nonNull(oldRefund) && Objects.nonNull(oldRefund.getOrderId())) {
                oldRefund.setComplaintId(customerComplaintsEntity.getId());
                oldRefund.setOperator(attribute.getOperator());
                refundId = orderPayV2Service.submitRefundApply(oldRefund);
                refundAmount[0] = oldRefund.getRefundAmount();
            }
        }

        if (Objects.nonNull(refundId)){
            List<HeOrderRefundEntity> orderRefundList = orderRefundRepository.getRefundByOrderId(orderEntity.getOrderId());
            if (Objects.nonNull(orderRefundList)){
                Integer finalRefundId = refundId;
                //非意向金客诉计入业绩
                if (orderEntity.isNewOrder() && !Integer.valueOf(3).equals(refundType)){
                    Result<Integer> integerResult = refundService.calRefundAchievement(refundId);
                    refundAmount[0] = AmountChangeUtil.changeF2Y(integerResult.getData());
                } else {
                    orderRefundList.stream().filter(item -> finalRefundId.equals(item.getId()) && item.getType() == 2).findFirst().ifPresent(present -> {
                        refundAmount[0] = BigDecimal.ZERO;
                    });
                }
            }
        }

        customerComplaintsEntity = heCustomerComplaintsRepository.selectById(customerComplaintsEntity.getId());
        customerComplaintsEntity.setSubmissionAt(new Date());
        customerComplaintsEntity.setSubmitter(Integer.parseInt(attribute.getOperator().getOperatorGuid()));
        //发起的时候清空这两个字段
        customerComplaintsEntity.setApproveFinish(null);
        customerComplaintsEntity.setResolveFinish(null);


        if (ObjectUtil.isEmpty(createRefundReq.getRefundAmount()) || createRefundReq.getRefundAmount().compareTo(BigDecimal.ZERO) == 0) {
            createRefundReq.setRefundAmount(BigDecimal.ZERO);
        }

        //现金赔偿线下退款
        createOfflineRefundComplaint(createRefundReq, customerComplaintsEntity.getId(), refundAmount[0], customerComplaintsEntity);

        //其他退款
        otherRefundComplaintExec(createRefundReq, customerComplaintsEntity);

        bizContext.setAttribute(CustomerComplaintConstant.COMPLAINT_ORDER, orderEntity);
        if (ObjectUtil.isNotEmpty(refundId)) {
            bizContext.setAttribute(CustomerComplaintConstant.COMPLAINT_REFUND_ID, refundId);
        }
        heCustomerComplaintsRepository.update(customerComplaintsEntity);
    }

    private void otherRefundComplaintExec(CustomerComplaintsCreateRefundReq createRefundReq, HeCustomerComplaintsEntity customerComplaintsEntity) {


        if (CollectionUtils.isEmpty(createRefundReq.getExtraRefundTypeList())) {
            return;
        }
        Map<String, Integer> otherCompensationIdMap = new HashMap<>();
        for (CustomerComplaintsRefundEnum item : CustomerComplaintsRefundEnum.values()){
            Map<String, ExtraRefundTypeReq> extraRefundTypeMap = createRefundReq.getExtraRefundTypeList().stream().collect(Collectors.toMap(ExtraRefundTypeReq::getExtraCode, v -> v, (k1, k2) -> k1));
            ExtraRefundTypeReq extraRefundTypeVO = extraRefundTypeMap.get(item.getCode());
            if (Objects.isNull(extraRefundTypeVO)){
                continue;
            }
            OfflineRemittanceRequest offlineRemittanceRequest = new OfflineRemittanceRequest();
            offlineRemittanceRequest.setBankNo(extraRefundTypeVO.getBankNo());
            offlineRemittanceRequest.setAccountName(extraRefundTypeVO.getAccountName());
            offlineRemittanceRequest.setAccountBank(extraRefundTypeVO.getAccountBank());
            offlineRemittanceRequest.setRefundAmount(extraRefundTypeVO.getExtraRefundAmount());
            offlineRemittanceRequest.setComplaintId(customerComplaintsEntity.getId());
            offlineRemittanceRequest.setRefundAchievement(BigDecimal.ZERO);
            HeOrderRefundEntity orderRefundEntity = orderRefundConverter.offlineRemittanceRequest2OrderRefundEntity(offlineRemittanceRequest, IdGenUtils.createRefundTransactionalNo(null, new Date()));
            Integer refundId = orderRefundRepository.saveOne(orderRefundEntity);
            otherCompensationIdMap.put(item.getCode(), refundId);
        }
        customerComplaintsEntity.setOtherCompensationId(JSONUtil.toJsonStr(otherCompensationIdMap));
    }

    private void createOfflineRefundComplaint(CustomerComplaintsCreateRefundReq createRefundReq, Long complaintId, BigDecimal refundAmount, HeCustomerComplaintsEntity customerComplaintsEntity) {
        OfflineRemittanceRequest offlineRemittanceRequest = new OfflineRemittanceRequest();
        offlineRemittanceRequest.setRefundAmount(createRefundReq.getRefundAmount());
        offlineRemittanceRequest.setAccountBank(createRefundReq.getAccountBank());
        offlineRemittanceRequest.setAccountName(createRefundReq.getAccountName());
        offlineRemittanceRequest.setBankNo(createRefundReq.getBankNo());
        offlineRemittanceRequest.setComplaintId(complaintId);
        // 退款业绩 = 退款金额
        //todo 退款业绩计算需要优化
        offlineRemittanceRequest.setRefundAchievement(refundAmount);
        HeOrderRefundEntity orderRefundEntity = orderRefundConverter.offlineRemittanceRequest2OrderRefundEntity(offlineRemittanceRequest, IdGenUtils.createRefundTransactionalNo(null, new Date()));
        Integer refundId = orderRefundRepository.saveOne(orderRefundEntity);
        customerComplaintsEntity.setCashRefundId(refundId);
        customerComplaintsEntity.setHasExtraCompensationOpinion(createRefundReq.getHasExtraCompensationOpinion());
        customerComplaintsEntity.setCompensationAmount(createRefundReq.getRefundAmount());
    }


    @Override
    public boolean condition(FlowContext context) {
        CustomerComplaintsCreateReq req = context.getAttribute(CustomerComplaintsCreateReq.class);
        return !req.getIsDraft();
    }
}

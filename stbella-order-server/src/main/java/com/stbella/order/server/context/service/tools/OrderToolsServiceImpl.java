package com.stbella.order.server.context.service.tools;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stbella.core.result.Result;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.server.context.component.spi.AllocateSpi;
import com.stbella.order.server.context.component.temp.RepairSplitDataProcessor;
import com.stbella.platform.order.api.OrderAggregateRoot;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.context.component.tools.ResetAllocationPriceAssembler;
import com.stbella.order.server.order.month.req.OrderPageReq;
import com.stbella.platform.order.api.tools.OrderToolsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import top.primecare.snowball.extpoint.SnowballExtensionInvoker;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.core.context.FlowContext;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description: OrderCommandServiceImpl
 * @date 2024/5/29 14:51
 */
@Service
@DubboService
@Slf4j
public class OrderToolsServiceImpl implements OrderToolsService {

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private ResetAllocationPriceAssembler resetAllocationPriceAssembler;
    @Resource
    OrderAggregateRoot orderAggregateRoot;
    @Resource
    IncomeRecordRepository incomeRecordRepository;
    @Resource
    RepairSplitDataProcessor repairSplitDataProcessor;


    /**
     * 订单减免-审批校验
     *
     * @param req
     */
    @Override
    public Result<String> resetAllocationPrice(OrderPageReq req){

        Page<HeOrderEntity> pageList = orderRepository.queryList(req);

        pageList.getRecords().forEach(order -> {

            FlowContext context = new FlowContext();
            context.setAttribute(HeOrderEntity.class, order);
            SnowballFlowLauncher.fire(context, resetAllocationPriceAssembler);
        });

        return Result.success();
    }

    /**
     * 修复分摊错误
     *
     * @param incomeIds
     */
    @Override
    public Result<String> repairAllocationPaidAmount(List<Integer> incomeIds) {

        incomeIds.forEach(id -> {
            HeIncomeRecordEntity incomeRecord = incomeRecordRepository.getOneById(id);
            FlowContext context = new FlowContext();
            context.setAttribute(HeIncomeRecordEntity.class, incomeRecord);
            SnowballFlowLauncher.fire(context, repairSplitDataProcessor);
        });

        return Result.success();
    }

    /**
     * 重新分摊
     *
     * @param incomeIds
     */
    @Override
    public Result<String> retryAllocation(List<Integer> incomeIds) {
        incomeIds.forEach(id -> {
            HeIncomeRecordEntity incomeRecord = incomeRecordRepository.getOneById(id);
            SnowballExtensionInvoker.invoke(AllocateSpi.class, incomeRecord);
        });

        return Result.success();
    }


}

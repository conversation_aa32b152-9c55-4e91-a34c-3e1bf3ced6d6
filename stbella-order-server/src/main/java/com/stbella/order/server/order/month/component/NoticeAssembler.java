package com.stbella.order.server.order.month.component;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.stbella.base.server.ding.request.WechatNailNailRobotDeclarationRequest;
import com.stbella.core.enums.BrandTypeEnum;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.customer.server.ecp.vo.ClientInfoVO;
import com.stbella.financial.res.MainSupplierQueryVO;
import com.stbella.message.scene.req.SceneTriggerReq;
import com.stbella.notice.enums.NoticeTypeEnum;
import com.stbella.notice.server.NoticeService;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.constant.NoticeConstant;
import com.stbella.order.common.enums.core.*;
import com.stbella.order.common.enums.month.*;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.domain.client.MessageClient;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.domain.order.entity.ExternalProductionOrderEntity;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.repository.*;
import com.stbella.order.infrastructure.config.BizConfig;
import com.stbella.order.server.context.component.StoreCurrencyContainer;
import com.stbella.order.server.context.component.assembler.GoodsNamesForBroadcastAssembler;
import com.stbella.order.server.context.dto.OrderBroadcastDto;
import com.stbella.order.server.manager.BasicManager;
import com.stbella.order.server.manager.FinanceManager;
import com.stbella.order.server.manager.TabClientManager;
import com.stbella.order.server.order.month.biz.config.MonthDingTalkRobotConfig;
import com.stbella.order.server.order.month.enums.OrderQianwanNoticeEnum;
import com.stbella.order.server.order.month.enums.StoreTypeEnum;
import com.stbella.order.server.order.month.req.CfgStoreReq;
import com.stbella.order.server.order.month.req.OrderNoticeReq;
import com.stbella.order.server.order.month.req.OrderQuery;
import com.stbella.order.server.order.month.req.OrderRefundQuery;
import com.stbella.order.server.order.month.res.AmountSumVo;
import com.stbella.order.server.order.month.res.StoreAchievementResultVO;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.order.server.utils.BigDecimalUtil;
import com.stbella.platform.order.api.req.CustomAttribute;
import com.stbella.platform.order.api.req.ExtraInfo;
import com.stbella.platform.order.api.res.GoodsInfo;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import com.stbella.store.common.enums.core.GoodsTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.core.context.FlowContext;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-09 16:35
 */
@Component
@Slf4j
public class NoticeAssembler {


    @Resource
    private OrderRepository orderRepository;

    @Resource
    private OrderUserSnapshotRepository userSnapshotRepository;

    @Resource
    private MonthDingTalkRobotConfig monthDingTalkRobotConfig;

    @Resource
    private StoreRepository storeRepository;

    @Resource
    private SettingStoreTargetRepository storeTargetRepository;

    @Resource
    private OrderRefundRepository orderRefundRepository;

    @Resource
    private IncomeTotalAssembler incomeTotalAssembler;

    @DubboReference
    private NoticeService noticeService;

    @Resource
    RuleLinkClient ruleLinkClient;

    @Resource
    UserRepository userRepository;
    @Resource
    OrderReductionRepository orderReductionRepository;

    @Resource
    private StoreCurrencyContainer storeCurrencyContainer;
    @Resource
    GoodsNamesForBroadcastAssembler goodsNamesForBroadcastAssembler;
    @Resource
    IncomeRecordRepository incomeRecordRepository;
    @Resource
    private IncomeProofRecordRepository incomeProofRecordRepository;

    @Resource
    private OrderGoodsRepository orderGoodsRepository;

    @Resource
    private FinanceManager financeManager;
    @Resource
    private ClientRepository clientRepository;
    @Resource
    private BasicManager basicManager;
    @Resource
    private MessageClient messageClient;
    @Resource
    private BizConfig bizConfig;

    public String queryNoticeAssembler(OrderNoticeReq req) {
        HeOrderEntity orderEntity = orderRepository.getByOrderId(req.getOrderId());

        OrderBroadcastDto broadcastDto = initBroadcastBody(orderEntity);

        CfgStoreEntity cfgStore = storeRepository.queryCfgStoreById(orderEntity.getStoreId());
        Optional.ofNullable(cfgStore).orElseThrow(() -> new BusinessException(ResultEnum.PARAM_ERROR, "门店不存在"));


        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = userSnapshotRepository.queryByOrderId(req.getOrderId());
        Optional.ofNullable(heOrderUserSnapshotEntity).orElseThrow(() -> new BusinessException(ResultEnum.PARAM_ERROR, "订单快照不存在"));

        String storeName = cfgStore.getStoreName();
        // 这里storeName 需要增加 销售名称。
        UserEntity userEntity = userRepository.queryById(orderEntity.getStaffId());
        if (Objects.nonNull(userEntity)) {
            storeName = storeName + "-" + userEntity.getName() + "";
        }

        SettingStoreTargetEntity settingStoreTargetEntity = storeTargetRepository.queryByCondition(orderEntity.getStoreId(), DateUtil.format(DateUtil.date(), DatePattern.NORM_MONTH_PATTERN), EcpStoreTargetEnum.YZ_TOTAL.code());
        log.info("settingStoreTargetEntity{}", JSONUtil.toJsonStr(settingStoreTargetEntity));
        //套餐原价保留2为小数单位 w
        String goodsPriceOrgin = BigDecimalUtil.divide(new BigDecimal(orderEntity.getOrderAmount().toString()), new BigDecimal(1000000)).toString();
        //客户来源
        String formTypeName = FromTypeEnum.fromCode(heOrderUserSnapshotEntity.getFromType());
        OrderQuery query = new OrderQuery();

        // 统计所有月子订单
        query.setOrderTypeList(Arrays.asList(OmniOrderTypeEnum.MONTH_ORDER.getCode()
                , OmniOrderTypeEnum.SMALL_MONTH_ORDER.getCode()
                , OmniOrderTypeEnum.NURSE_OUTSIDE_ORDER.getCode()
                , OmniOrderTypeEnum.OTHER_MONTH_ORDER.getCode()
                , OmniOrderTypeEnum.PRODUCTION_ORDER.getCode()
                , OmniOrderTypeEnum.SBRA_ORDER.getCode()));
        //必须大于1元钱
        query.setPaidAmount(100);
        //本月开始结束时间
        query.setStartPercentFirstTime(DateUtil.beginOfMonth(DateUtil.date()).getTime() / 1000L);
        query.setEndPercentFirstTime(DateUtil.endOfMonth(DateUtil.date()).getTime() / 1000L);
        query.setStoreId(cfgStore.getStoreId());
        query.setIsNotice(OrderNoticeEnum.NOTICE_YES.code());
        List<HeOrderEntity> heOrderEntities = orderRepository.queryByCondition(query);
        Set<Integer> orderIds = heOrderEntities.stream().map(HeOrderEntity::getOrderId).collect(Collectors.toSet());
        //如果 如果订单，不为空，且不在当日订单中，增加到列表中
        if (!orderIds.contains(orderEntity.getOrderId())) {
            log.info("当前订单不在日订单中{}", orderEntity.getOrderId());
            heOrderEntities.add(orderEntity);
        }
        Long reductionSum = orderReductionRepository.reductionSum(query);
        //有效签单数 - 月了订单
        Integer num = heOrderEntities.stream().filter(a -> a.getOrderType().equals(OmniOrderTypeEnum.MONTH_ORDER.getCode())).collect(Collectors.toList()).size();
        //门店本月总业绩
        Long sumPayAmount = heOrderEntities.stream().mapToLong(HeOrderEntity::getPayAmount).sum();
        //排除减免
        sumPayAmount = sumPayAmount - reductionSum;

        //产康金抵扣金额
        Long sumProductionAmount = heOrderEntities.stream().mapToLong(HeOrderEntity::getProductionAmountPay).sum();
        sumPayAmount = sumPayAmount - sumProductionAmount;
        String totalPayAmount = BigDecimalUtil.divide(new BigDecimal(sumPayAmount.toString()), new BigDecimal(1000000)).toString();

//        Long paidPayAmount = heOrderEntities.stream().mapToLong(HeOrderEntity::getPaidAmount).sum();
        //本月 净业绩指的是总业绩去掉退款之后的业绩
        OrderRefundQuery refundQuery = new OrderRefundQuery();
        refundQuery.setStoreId(Arrays.asList(orderEntity.getStoreId()))
                .setOrderType(OmniOrderTypeEnum.allList())
                .setIncomeReceiptType(Arrays.asList(
                        IncomeReceiptTypeEnum.RECORD_RECEIPT_TYPE_NONE.code(),
                        IncomeReceiptTypeEnum.RECORD_RECEIPT_TYPE_INTENTION_TO_GOLD.code(),
                        IncomeReceiptTypeEnum.RECORD_RECEIPT_TYPE_ADVANCE_PAYMENT.code(),
                        IncomeReceiptTypeEnum.RECORD_RECEIPT_TYPE_BALANCE_PAYMENT.code()))
                .setStartPercentFirstTime(DateUtil.beginOfMonth(DateUtil.date()).getTime() / 1000L)
                .setEndPercentFirstTime(DateUtil.endOfMonth(DateUtil.date()).getTime() / 1000L);

        final Long totalRefundAchievement = orderRefundRepository.queryTotalRefundAchievement(refundQuery);
        //本月净业绩=本月
        String netTotalRealAmount = BigDecimalUtil.divide(BigDecimalUtil.subtract(new BigDecimal(sumPayAmount.toString()), new BigDecimal(totalRefundAchievement)), new BigDecimal(1000000)).toString();
        //达成率 百分比精确2位小数
        //达成率 如果未设置目标或目标为0，则达成率按100%算
        //月度目标金额单位数元 转成分
        String netRateTarget = "100";
        String rateTarget = "100";
        if (Objects.isNull(settingStoreTargetEntity) || Objects.isNull(settingStoreTargetEntity.getAmount()) || settingStoreTargetEntity.getAmount() <= 0) {
            netRateTarget = "100";
        } else {
            netRateTarget = BigDecimalUtil.divideScaleForPercentage(new BigDecimal(netTotalRealAmount), new BigDecimal(settingStoreTargetEntity.getAmount() / 10000)).toString();
            ;
            rateTarget = BigDecimalUtil.divideScaleForPercentage(new BigDecimal(sumPayAmount), new BigDecimal(settingStoreTargetEntity.getAmount() * 100)).toString();
            ;
        }


        String messageStr = "";
        //查询门店 货币汇率
        CfgStoreReq cfgStoreReq = new CfgStoreReq();
        cfgStoreReq.setStoreId(orderEntity.getStoreId());
        BigDecimal storeRate = StoreCurrencyContainer.getStoreRate(orderEntity.getStoreId(), DateUtils.getTenBitTimestamp());
        // 汇率计算

        goodsPriceOrgin = BigDecimalUtil.multiplication(new BigDecimal(goodsPriceOrgin), storeRate).toString();
        totalPayAmount = BigDecimalUtil.multiplication(new BigDecimal(totalPayAmount), storeRate).toString();
        netTotalRealAmount = BigDecimalUtil.multiplication(new BigDecimal(netTotalRealAmount), storeRate).toString();


        StoreAchievementResultVO yuziTotalAmount = incomeTotalAssembler.query(DateUtil.thisYear(), DateUtil.thisMonth() + 1, StoreAchievementTypeEnum.YUEZI_TOTAL_AMOUNT.getCode());
        StoreAchievementResultVO yuziNetAmount = incomeTotalAssembler.query(DateUtil.thisYear(), DateUtil.thisMonth() + 1, StoreAchievementTypeEnum.YUEZI_NET_AMOUNT.getCode());

        //判断今天是不是本月最后一天
        if (DateUtil.date().toDateStr().equalsIgnoreCase(DateUtil.endOfMonth(DateUtil.date()).toDateStr())) {
            AmountSumVo amountSumVo = incomeTotalAssembler.queryIncomeTotal(DateUtil.thisYear(), DateUtil.thisMonth() + 1);
            messageStr = MessageFormat.format(NoticeConstant.MONTH_LAST_DAY_NOTICE_TEMPLATE,
                    storeName,
                    broadcastDto.getGoodsName(),
                    goodsPriceOrgin,
                    formTypeName,
                    num,
                    rateTarget,
                    yuziTotalAmount.getTotalRate(),
                    yuziNetAmount.getTotalRate(),
                    amountSumVo.getAchievement()
            );
        } else {
            messageStr = MessageFormat.format(NoticeConstant.DAY_NOTICE_TEMPLATE,
                    storeName,
                    broadcastDto.getGoodsName(),
                    goodsPriceOrgin,
                    formTypeName,
                    num,
                    rateTarget,
                    yuziTotalAmount.getTotalRate(),
                    yuziNetAmount.getTotalRate());
        }
        messageStr += buildDailyNoticePart(orderEntity);

        return messageStr;
    }


    public String queryProductNoticeAssembler(OrderNoticeReq req) {
        HeOrderEntity orderEntity = orderRepository.getByOrderId(req.getOrderId());

        OrderBroadcastDto broadcastDto = initBroadcastBody(orderEntity);

        CfgStoreEntity cfgStore = storeRepository.queryCfgStoreById(orderEntity.getStoreId());
        Optional.ofNullable(cfgStore).orElseThrow(() -> new BusinessException(ResultEnum.PARAM_ERROR, "门店不存在"));


        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = userSnapshotRepository.queryByOrderId(req.getOrderId());
        Optional.ofNullable(heOrderUserSnapshotEntity).orElseThrow(() -> new BusinessException(ResultEnum.PARAM_ERROR, "订单快照不存在"));

        String storeName = cfgStore.getStoreName();

        SettingStoreTargetEntity settingStoreTargetEntity = storeTargetRepository.queryByCondition(orderEntity.getStoreId(), DateUtil.format(DateUtil.date(), DatePattern.NORM_MONTH_PATTERN), EcpStoreTargetEnum.YZ_TOTAL.code());
        log.info("settingStoreTargetEntity{}", JSONUtil.toJsonStr(settingStoreTargetEntity));
        //套餐原价保留2为小数单位 w
        String goodsPriceOrgin = BigDecimalUtil.divide(new BigDecimal(orderEntity.getOrderAmount().toString()), new BigDecimal(1000000)).toString();

        List<HeIncomeRecordEntity> allRecordListByOrderId = incomeRecordRepository.getAllRecordListByOrderId(orderEntity.getOrderId());
        List<HeIncomeProofRecordEntity> incomeProofRecordByOrderId = incomeProofRecordRepository.getIncomeProofRecordByOrderId(orderEntity.getOrderId());


        AtomicReference<Long> processing = new AtomicReference<>(0L);

        //实付金额
        Long paidAmount = allRecordListByOrderId.stream().filter(o -> !OmniPayTypeEnum.PRODUCTION_COIN.getCode().equals(o.getPayType())).filter(o -> {
            boolean equals = IncomeRecordPayStatusEnum.COMPLETE.getCode().equals(o.getStatus());
            if (o.getPayType().equals(OmniPayTypeEnum.OFFLINE.getCode())) {
                Optional<HeIncomeProofRecordEntity> first = incomeProofRecordByOrderId.stream().filter(i -> i.getIncomeId().equals(o.getId())).findFirst();
                if (first.isPresent()) {
                    HeIncomeProofRecordEntity heIncomeProofRecordEntity = first.get();

                    if (heIncomeProofRecordEntity.getStatus().equals(OfflineAuditStatusV2Enum.UNDER_REVIEW.getCode())) {
                        processing.updateAndGet(v -> v + heIncomeProofRecordEntity.getIncomeProof());
                    }

                    equals = equals && ObjectUtil.isNotEmpty(heIncomeProofRecordEntity.getStatus()) && heIncomeProofRecordEntity.getStatus().equals(OfflineAuditStatusV2Enum.EXAMINATION_PASSED.getCode());
                }
            }
            return equals;
        }).mapToLong(HeIncomeRecordEntity::getIncome).sum();
        Long productCoinPayAmount = allRecordListByOrderId.stream().filter(o -> OmniPayTypeEnum.PRODUCTION_COIN.getCode().equals(o.getPayType()) && IncomeRecordPayStatusEnum.COMPLETE.getCode().equals(o.getStatus())).mapToLong(HeIncomeRecordEntity::getIncome).sum();


        List<HeOrderGoodsEntity> allItermByOrderId = orderGoodsRepository.getAllItermByOrderId(orderEntity.getOrderId());

        //成交项目
        StringBuffer submitProject = new StringBuffer();

        //成交金额
        StringBuffer tradingVolume = new StringBuffer();

        //组合商品
        List<HeOrderGoodsEntity> combination = allItermByOrderId.stream().filter(a -> a.getGoodsType().equals(GoodsTypeEnum.COMBINATION.code())).collect(Collectors.toList());
        //普通商品合集
        List<HeOrderGoodsEntity> commonGoodsInfo = allItermByOrderId.stream().filter(a -> a.getType().equals(2)).collect(Collectors.toList());

        List<Long> commonGoodsMainSupplerList = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(combination)) {
            //组合的处理
            for (HeOrderGoodsEntity heOrderGoodsEntity : combination) {
                tradingVolume
                        .append(AmountChangeUtil.changeF2Y(heOrderGoodsEntity.getTotalAllocationOriginPrice()))
                        .append(heOrderGoodsEntity.getGoodsName())
                        .append("*").append(heOrderGoodsEntity.getGoodsNum()).append("+");
                submitProject.append(heOrderGoodsEntity.getGoodsName()).append("、");
            }
        }
        if (CollectionUtil.isNotEmpty(commonGoodsInfo)) {
            //普通商品显示：5999腰腹塑性*8+688母乳指导*3

            //组合只展示组合名字，非组合展示明细
            for (HeOrderGoodsEntity heOrderGoodsEntity : commonGoodsInfo) {
                //分摊原价
                Integer totalAllocationOriginPrice = heOrderGoodsEntity.getTotalAllocationOriginPrice();
                //商品名
                String goodsName = heOrderGoodsEntity.getGoodsName();
                //商品数量
                Integer goodsNum = heOrderGoodsEntity.getGoodsNum();
                tradingVolume
                        .append(AmountChangeUtil.changeF2Y(totalAllocationOriginPrice))
                        .append(goodsName)
                        .append("*").append(goodsNum).append("+")
                ;

                GoodsInfo goodsInfo = heOrderGoodsEntity.getGoodsInfo();
                if (ObjectUtil.isNotEmpty(goodsInfo)) {
                    Long mainSupplierId = goodsInfo.getMainSupplierId();
                    if (ObjectUtil.isNotEmpty(mainSupplierId)) {
                        commonGoodsMainSupplerList.add(mainSupplierId);
                        heOrderGoodsEntity.setMainSupplerId(mainSupplierId);
                    }
                }
            }

            Map<Long, String> mainSupplerMap = new HashMap<>();

            if (CollectionUtil.isNotEmpty(commonGoodsMainSupplerList)) {
                List<MainSupplierQueryVO> mainSupplierQueryVOS = financeManager.queryMainSupplier(commonGoodsMainSupplerList);
                mainSupplerMap = mainSupplierQueryVOS.stream().collect(Collectors.toMap(MainSupplierQueryVO::getSupplierId, MainSupplierQueryVO::getName));
            }

            for (HeOrderGoodsEntity heOrderGoodsEntity : commonGoodsInfo) {
                String goodsName = heOrderGoodsEntity.getGoodsName();
                submitProject.append(goodsName);
                if (ObjectUtil.isNotEmpty(heOrderGoodsEntity)) {
                    String mainSupplerName = mainSupplerMap.get(heOrderGoodsEntity.getMainSupplerId());
                    if (StringUtils.isNotEmpty(mainSupplerName)) {
                        submitProject.append("-").append(mainSupplerName);
                    }
                }
                submitProject.append("、");
            }
        }

        //三方产康师
        String tripartiteHealthExperts = "";
        ExtraInfo extraInfo = orderEntity.getExtraInfo();
        if (ObjectUtil.isNotEmpty(extraInfo)) {
            List<CustomAttribute> fulfillExtraList = extraInfo.getFulfillExtraList();
            if (CollectionUtil.isNotEmpty(fulfillExtraList)) {
                Optional<CustomAttribute> tripartiteHealthExpertsFirst = fulfillExtraList.stream().filter(f -> f.getCode().equals("tripartiteHealthExperts")).findFirst();
                if (tripartiteHealthExpertsFirst.isPresent()) {
                    tripartiteHealthExperts = tripartiteHealthExpertsFirst.get().getShowStr();
                }
            }
        }

        //订单所有人
        String staffName = "";
        Integer staffId = orderEntity.getStaffId();
        if (ObjectUtil.isNotEmpty(staffId)) {
            UserEntity userEntity = userRepository.queryById(staffId);
            if (ObjectUtil.isNotEmpty(userEntity)) {
                staffName = userEntity.getName();
            }
        }

        Integer percentFirstTime = orderEntity.getPercentFirstTime();
        String dateAndStoreName = DateUtil.format(new Date(percentFirstTime * 1000L), "yyyy年MM月dd日");

        String messageStr = MessageFormat.format(NoticeConstant.PRODUCT_NOTICE_TEMPLATE,
                dateAndStoreName + storeName,
                StringUtils.isNotEmpty(submitProject) ? submitProject.toString().subSequence(0, submitProject.toString().length() - 1) : "",
                heOrderUserSnapshotEntity.getName(),
                StringUtils.isNotEmpty(tradingVolume) ? tradingVolume.toString().subSequence(0, tradingVolume.toString().length() - 1) : "",
                AmountChangeUtil.changeF2Y(productCoinPayAmount),
                RMBUtils.formatToseparaDecimals(AmountChangeUtil.changeF2Y(paidAmount)) + (processing.get() > 0L ? "（" + RMBUtils.formatToseparaDecimals(AmountChangeUtil.changeF2Y(processing.get())) + "线下审阅中）" : ""),
                staffName,
                tripartiteHealthExperts,
                BrandTypeEnum.getValueByCode(cfgStore.getType())
        );
        return messageStr;
    }

    //更新业绩
    public void savePercent(HeOrderEntity orderEntity, Integer percentFirstTime) {
        orderEntity.setPercentFirstTime(percentFirstTime);
        if (orderEntity.getOrderStatus() == 0) {
            //改成待入住
            orderEntity.setOrderStatus(OrderStatusV2Enum.TO_STAY_IN.getCode());
        }
        orderRepository.updateOrderMonthByOrderId(orderEntity);
    }


    //发送钉钉消息
    @Async
    public void pushMsg(String msg, Integer originalStoreId) {
        try {

            log.info("月子报单推送内容{},token{}", msg, monthDingTalkRobotConfig.toString());
            WechatNailNailRobotDeclarationRequest wechatNailNailRobotDeclarationRequest = new WechatNailNailRobotDeclarationRequest();
            wechatNailNailRobotDeclarationRequest.setType(1);
            wechatNailNailRobotDeclarationRequest.setBizType(NoticeTypeEnum.PICP_PERFORMANCE_NOTICE.getBizType());
            wechatNailNailRobotDeclarationRequest.setStoreId(0);
            wechatNailNailRobotDeclarationRequest.setContent(msg);


            //独立报单
            Map<String, String> factMap = new HashMap<>();
            factMap.put("configCode", "separate_notice_store_ids");
            ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req("systemConfig", factMap);


            HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
            if (Objects.nonNull(hitRuleVo)) {
                // simpleRuleValue 按逗号分成数组
                // 非统一报单的，都到此门店对应的机器人，这样运营可以自助配置。
                Integer defaultStoreId = 1005;
                Set<String> storeIds = Arrays.stream(hitRuleVo.getSimpleRuleValue().split(",")).collect(Collectors.toSet());
                if (storeIds.contains(originalStoreId.toString())) {
                    wechatNailNailRobotDeclarationRequest.setStoreId(defaultStoreId);
                }
            }


            Result<Boolean> notice = noticeService.notice(wechatNailNailRobotDeclarationRequest);
            log.info("月子报单结果：{}", JSONUtil.toJsonStr(notice));
        } catch (Exception e) {
            log.error("月子报单失败:{}", e);
        }
    }


    /**
     * 产康报单
     *
     * @param msg
     * @param originalStoreId
     */
    @Async
    public void pushProductMsg(String msg, Integer originalStoreId) {
        try {

            log.info("产康报单推送内容{},token{}", msg, monthDingTalkRobotConfig.toString());
            WechatNailNailRobotDeclarationRequest wechatNailNailRobotDeclarationRequest = new WechatNailNailRobotDeclarationRequest();
            wechatNailNailRobotDeclarationRequest.setType(1);
            wechatNailNailRobotDeclarationRequest.setBizType(90000);
            wechatNailNailRobotDeclarationRequest.setStoreId(0);
            wechatNailNailRobotDeclarationRequest.setContent(msg);


            //独立报单
            Map<String, String> factMap = new HashMap<>();
            factMap.put("configCode", "separate_notice_store_ids");
            ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req("systemConfig", factMap);


            HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
            if (Objects.nonNull(hitRuleVo)) {
                // simpleRuleValue 按逗号分成数组
                // 非统一报单的，都到此门店对应的机器人，这样运营可以自助配置。
                Integer defaultStoreId = 1005;
                Set<String> storeIds = Arrays.stream(hitRuleVo.getSimpleRuleValue().split(",")).collect(Collectors.toSet());
                if (storeIds.contains(originalStoreId.toString())) {
                    wechatNailNailRobotDeclarationRequest.setStoreId(defaultStoreId);
                }
            }


            Result<Boolean> notice = noticeService.notice(wechatNailNailRobotDeclarationRequest);
            log.info("月子报单结果：{}", JSONUtil.toJsonStr(notice));
        } catch (Exception e) {
            log.error("月子报单失败:", e);

        }
    }

    @Async
    public void pushQianwanNotice(Integer type, String preview, String text) {
        //业务类型 （10000 "月子订单业绩报单"；10001 月子千万报单提醒 10002 小助手添加或编辑订单同步picp失败告警 10003 月子订单那同步ecp告警 10004 php支付异常信息预警 10005 月子收到一条反馈 10006 大众点评预约客资(门店维度) 10007 产康订单报单群(门店维度 10008 Sbar订单报单群(门店维度））
        try {
            WechatNailNailRobotDeclarationRequest wechatNailNailRobotDeclarationRequest = new WechatNailNailRobotDeclarationRequest();
            wechatNailNailRobotDeclarationRequest.setType(2);
            wechatNailNailRobotDeclarationRequest.setBizType(NoticeTypeEnum.MONTH_10_MILLION_DECLARATION_REMINDER.getBizType());
            wechatNailNailRobotDeclarationRequest.setStoreId(0);
            wechatNailNailRobotDeclarationRequest.setPreview(preview);
            wechatNailNailRobotDeclarationRequest.setText(text);
            wechatNailNailRobotDeclarationRequest.setTitle("捷报:" + OrderQianwanNoticeEnum.getValueByCode(type));
            Result<Boolean> notice = noticeService.notice(wechatNailNailRobotDeclarationRequest);
            log.info("月子千万报单结果：{}", JSONUtil.toJsonStr(notice));
        } catch (Exception e) {
            log.error("月子千万报单失败:{}", e.getMessage());
        }
    }

    /**
     * 报单中日统计部分内容
     *
     * @param brandOrderNumMap
     * @return
     */
    protected String buildDailyNoticePart(HeOrderEntity orderEntity) {

        Map<Integer, String> brandOrderNumMap = statDailyOrderNum(orderEntity);
        String dailyNoticePart = MessageFormat.format(NoticeConstant.MONTH_DAILY_ORDER_NOTICE_PART,
                AmountChangeUtil.nullToZero(brandOrderNumMap.get(BizConstant.ALL_ORDER_TAG)),
                AmountChangeUtil.nullToZero(brandOrderNumMap.get(StoreTypeEnum.SAINT_BELLA.getCode())),
                AmountChangeUtil.nullToZero(brandOrderNumMap.get(StoreTypeEnum.BABY_BELLA.getCode())),
                AmountChangeUtil.nullToZero(brandOrderNumMap.get(StoreTypeEnum.ISLA.getCode())),
                AmountChangeUtil.nullToZero(brandOrderNumMap.get(BizConstant.ALL_ORDER_AMOUNT_TAG)));

        return dailyNoticePart;
    }

    /**
     * 统计当日订单量，返回各品牌订单量
     * key=0 表示总订单量
     */
    protected Map<Integer, String> statDailyOrderNum(HeOrderEntity orderEntity) {

        OrderQuery query = new OrderQuery();
        query.setOrderType(OmniOrderTypeEnum.MONTH_ORDER.getCode());
        //必须大于1元钱
        query.setPaidAmount(100);
        query.setIsNotice(OrderNoticeEnum.NOTICE_YES.code());
        //本月开始结束时间
        query.setStartPercentFirstTime(DateUtil.beginOfMonth(DateUtil.date()).getTime() / 1000L);
        query.setEndPercentFirstTime(DateUtil.endOfMonth(DateUtil.date()).getTime() / 1000L);
        List<HeOrderEntity> heOrderEntities = orderRepository.queryByCondition(query);
        Set<Integer> orderIds = heOrderEntities.stream().map(HeOrderEntity::getOrderId).collect(Collectors.toSet());
        //如果 如果订单，不为空，且不在当日订单中，增加到列表中
        if (Objects.nonNull(orderEntity) && !orderIds.contains(orderEntity.getOrderId())) {
            log.info("当前订单不在日订单中{}", orderEntity.getOrderId());
            heOrderEntities.add(orderEntity);
        }

        log.info("当日订单量{}", JSONUtil.toJsonStr(heOrderEntities));

        //过滤当日订单。
        List<HeOrderEntity> todayOrders = heOrderEntities.stream().filter(heOrderEntity -> {
            Long todayStartTime = DateUtil.beginOfDay(new Date()).getTime() / 1000;
            Long todayEndTime = DateUtil.endOfDay(new Date()).getTime() / 1000;
            //过滤当日订单
            return heOrderEntity.getPercentFirstTime() >= todayStartTime && heOrderEntity.getPercentFirstTime() <= todayEndTime;
        }).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(todayOrders)) {
            return new HashMap<>();
        }

        log.info("当日订单量{}", JSONUtil.toJsonStr(todayOrders));

        // 查询当日的门店
        List<Integer> storeIds = todayOrders.stream().map(HeOrderEntity::getStoreId).collect(Collectors.toList());
        List<CfgStoreEntity> cfgStoreEntities = storeRepository.queryCfgStoreByIdList(storeIds);
        Map<Integer, Integer> stopMap = cfgStoreEntities.stream().collect(Collectors.toMap(CfgStoreEntity::getStoreId, CfgStoreEntity::getType, (item1, item2) -> item1));

        // 订单中的品牌赋值
        todayOrders.forEach(heOrderEntity -> {
            Integer storeId = heOrderEntity.getStoreId();
            Integer type = stopMap.get(storeId);
            heOrderEntity.setIsDelivery(type);

            BigDecimal storeRate = StoreCurrencyContainer.getStoreRate(orderEntity.getStoreId(), DateUtils.getTenBitTimestamp());
            // payAmount 乘以汇率
            heOrderEntity.setPayAmount(BigDecimalUtil.multiplication(new BigDecimal(heOrderEntity.getPayAmount()), storeRate).intValue());

        });
        // 按类型分组
        Map<Integer, List<HeOrderEntity>> typeMap = todayOrders.stream().collect(Collectors.groupingBy(HeOrderEntity::getIsDelivery));
        // 按品牌计算订单量
        Map<Integer, String> dailyOrderNumMap = new HashMap<>();
        typeMap.forEach((key, value) -> {
            Integer num = value.size();
            dailyOrderNumMap.put(key, num + "");
        });

        //计算总量
        dailyOrderNumMap.put(BizConstant.ALL_ORDER_TAG, todayOrders.size() + "");

        //统计 payAmount的总和
        Long sumPayAmount = todayOrders.stream().mapToLong(HeOrderEntity::getPayAmount).sum();
        String totalPayAmount = BigDecimalUtil.divide(new BigDecimal(sumPayAmount.toString()), new BigDecimal(1000000)).toString();
        dailyOrderNumMap.put(BizConstant.ALL_ORDER_AMOUNT_TAG, totalPayAmount);

        return dailyOrderNumMap;
    }


    /**
     * 组装订单报单
     *
     * @param orderEntity
     * @return
     */
    protected OrderBroadcastDto initBroadcastBody(HeOrderEntity orderEntity) {
        FlowContext bizContext = new FlowContext();
        bizContext.setAttribute(HeOrderEntity.class, orderEntity);
        goodsNamesForBroadcastAssembler.run(bizContext);
        OrderBroadcastDto broadcastDto = bizContext.getAttribute(OrderBroadcastDto.class);
        return broadcastDto;
    }

    public String getExternalCKOrderMsg(ExternalProductionOrderEntity byOrderSn) {

        Integer storeId = byOrderSn.getStoreId();
        CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(storeId);
        String dateAndStoreName = DateUtil.format(byOrderSn.getCreatedAt(), "yyyy年MM月dd日");
        Integer clientUid = byOrderSn.getClientUid();
        TabClientEntity tabClientById = clientRepository.getTabClientById(clientUid);

        Long createdBy = byOrderSn.getCreatedBy();
        UserEntity userEntity = userRepository.queryById(createdBy.intValue());

        String messageStr = MessageFormat.format(NoticeConstant.EXTERNAL_PRODUCT_NOTICE_TEMPLATE,
                dateAndStoreName + cfgStoreEntity.getStoreName(),
                byOrderSn.getSupplierName(),
                byOrderSn.getProjectType(),
                tabClientById.getName(),
                "￥" + AmountChangeUtil.changeF2Y(byOrderSn.getAmount()),
                userEntity.getName(),
                byOrderSn.getExpertName(),
                BrandTypeEnum.getValueByCode(cfgStoreEntity.getType())
        );
        return messageStr;
    }

    public Boolean isSTBNewLead(Integer basicId) {
        if (Objects.isNull(basicId)) {
            return false;
        }
        Date productionNewLeadTimeByBasicId = basicManager.getProductionNewLeadTimeByBasicId(basicId.longValue());
        log.info("客户的stb商机时间: {}", productionNewLeadTimeByBasicId);
        return Optional.ofNullable(productionNewLeadTimeByBasicId).map(i -> new Date().after(productionNewLeadTimeByBasicId)).orElse(false);
    }

    /**
     * STB馆外商机报单
     *
     * @param req
     * @param originalStoreId
     */
    @Async
    public void pushSTBNewLeadMsg(OrderNoticeReq req, Integer originalStoreId) {
        try {

            log.info("产康报单推送内容{},storeId{}", req, originalStoreId);

            SceneTriggerReq triggerReq = new SceneTriggerReq();
            triggerReq.setRequestId("order_notice" + req.getOrderId());
            triggerReq.setContextData(genMessageContext(req, originalStoreId));
            triggerReq.setSceneId(102001L);
            triggerReq.setTargetList(Lists.newArrayList(bizConfig.getStbLeadNoticeRobotUrl()));
            messageClient.triggerScene(triggerReq);

        } catch (Exception e) {
            log.error("馆外STB商机报单失败:", e);
        }
    }

    private Map<String, String> genMessageContext(OrderNoticeReq req, Integer storeId) {
        HeOrderEntity orderEntity = orderRepository.getByOrderId(req.getOrderId());

        CfgStoreEntity cfgStore = storeRepository.queryCfgStoreById(orderEntity.getStoreId());
        Optional.ofNullable(cfgStore).orElseThrow(() -> new BusinessException(ResultEnum.PARAM_ERROR, "门店不存在"));


        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = userSnapshotRepository.queryByOrderId(req.getOrderId());
        Optional.ofNullable(heOrderUserSnapshotEntity).orElseThrow(() -> new BusinessException(ResultEnum.PARAM_ERROR, "订单快照不存在"));

        String storeName = cfgStore.getStoreName();

        SettingStoreTargetEntity settingStoreTargetEntity = storeTargetRepository.queryByCondition(orderEntity.getStoreId(), DateUtil.format(DateUtil.date(), DatePattern.NORM_MONTH_PATTERN), EcpStoreTargetEnum.YZ_TOTAL.code());
        log.info("settingStoreTargetEntity{}", JSONUtil.toJsonStr(settingStoreTargetEntity));

        List<HeIncomeRecordEntity> allRecordListByOrderId = incomeRecordRepository.getAllRecordListByOrderId(orderEntity.getOrderId());
        List<HeIncomeProofRecordEntity> incomeProofRecordByOrderId = incomeProofRecordRepository.getIncomeProofRecordByOrderId(orderEntity.getOrderId());


        AtomicReference<Long> processing = new AtomicReference<>(0L);

        //实付金额
        Long paidAmount = allRecordListByOrderId.stream().filter(o -> !OmniPayTypeEnum.PRODUCTION_COIN.getCode().equals(o.getPayType())).filter(o -> {
            boolean equals = IncomeRecordPayStatusEnum.COMPLETE.getCode().equals(o.getStatus());
            if (o.getPayType().equals(OmniPayTypeEnum.OFFLINE.getCode())) {
                Optional<HeIncomeProofRecordEntity> first = incomeProofRecordByOrderId.stream().filter(i -> i.getIncomeId().equals(o.getId())).findFirst();
                if (first.isPresent()) {
                    HeIncomeProofRecordEntity heIncomeProofRecordEntity = first.get();

                    if (heIncomeProofRecordEntity.getStatus().equals(OfflineAuditStatusV2Enum.UNDER_REVIEW.getCode())) {
                        processing.updateAndGet(v -> v + heIncomeProofRecordEntity.getIncomeProof());
                    }

                    equals = equals && ObjectUtil.isNotEmpty(heIncomeProofRecordEntity.getStatus()) && heIncomeProofRecordEntity.getStatus().equals(OfflineAuditStatusV2Enum.EXAMINATION_PASSED.getCode());
                }
            }
            return equals;
        }).mapToLong(HeIncomeRecordEntity::getIncome).sum();
        Long productCoinPayAmount = allRecordListByOrderId.stream().filter(o -> OmniPayTypeEnum.PRODUCTION_COIN.getCode().equals(o.getPayType()) && IncomeRecordPayStatusEnum.COMPLETE.getCode().equals(o.getStatus())).mapToLong(HeIncomeRecordEntity::getIncome).sum();


        List<HeOrderGoodsEntity> allItermByOrderId = orderGoodsRepository.getAllItermByOrderId(orderEntity.getOrderId());

        //成交项目
        StringBuffer submitProject = new StringBuffer();

        //成交金额
        StringBuffer tradingVolume = new StringBuffer();

        //组合商品
        List<HeOrderGoodsEntity> combination = allItermByOrderId.stream().filter(a -> a.getGoodsType().equals(GoodsTypeEnum.COMBINATION.code())).collect(Collectors.toList());
        //普通商品合集
        List<HeOrderGoodsEntity> commonGoodsInfo = allItermByOrderId.stream().filter(a -> a.getType().equals(2)).collect(Collectors.toList());

        List<Long> commonGoodsMainSupplerList = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(combination)) {
            //组合的处理
            for (HeOrderGoodsEntity heOrderGoodsEntity : combination) {
                tradingVolume
                        .append(AmountChangeUtil.changeF2Y(heOrderGoodsEntity.getTotalAllocationOriginPrice()))
                        .append(heOrderGoodsEntity.getGoodsName())
                        .append("*").append(heOrderGoodsEntity.getGoodsNum()).append("+");
                submitProject.append(heOrderGoodsEntity.getGoodsName()).append("、");
            }
        }
        if (CollectionUtil.isNotEmpty(commonGoodsInfo)) {
            //普通商品显示：5999腰腹塑性*8+688母乳指导*3

            //组合只展示组合名字，非组合展示明细
            for (HeOrderGoodsEntity heOrderGoodsEntity : commonGoodsInfo) {
                //分摊原价
                Integer totalAllocationOriginPrice = heOrderGoodsEntity.getTotalAllocationOriginPrice();
                //商品名
                String goodsName = heOrderGoodsEntity.getGoodsName();
                //商品数量
                Integer goodsNum = heOrderGoodsEntity.getGoodsNum();
                tradingVolume
                        .append(AmountChangeUtil.changeF2Y(totalAllocationOriginPrice))
                        .append(goodsName)
                        .append("*").append(goodsNum).append("+")
                ;

                GoodsInfo goodsInfo = heOrderGoodsEntity.getGoodsInfo();
                if (ObjectUtil.isNotEmpty(goodsInfo)) {
                    Long mainSupplierId = goodsInfo.getMainSupplierId();
                    if (ObjectUtil.isNotEmpty(mainSupplierId)) {
                        commonGoodsMainSupplerList.add(mainSupplierId);
                        heOrderGoodsEntity.setMainSupplerId(mainSupplierId);
                    }
                }
            }

            Map<Long, String> mainSupplerMap = new HashMap<>();

            if (CollectionUtil.isNotEmpty(commonGoodsMainSupplerList)) {
                List<MainSupplierQueryVO> mainSupplierQueryVOS = financeManager.queryMainSupplier(commonGoodsMainSupplerList);
                mainSupplerMap = mainSupplierQueryVOS.stream().collect(Collectors.toMap(MainSupplierQueryVO::getSupplierId, MainSupplierQueryVO::getName));
            }

            for (HeOrderGoodsEntity heOrderGoodsEntity : commonGoodsInfo) {
                String goodsName = heOrderGoodsEntity.getGoodsName();
                submitProject.append(goodsName);
                if (ObjectUtil.isNotEmpty(heOrderGoodsEntity)) {
                    String mainSupplerName = mainSupplerMap.get(heOrderGoodsEntity.getMainSupplerId());
                    if (StringUtils.isNotEmpty(mainSupplerName)) {
                        submitProject.append("-").append(mainSupplerName);
                    }
                }
                submitProject.append("、");
            }
        }

        //三方产康师
        String tripartiteHealthExperts = "";
        ExtraInfo extraInfo = orderEntity.getExtraInfo();
        if (ObjectUtil.isNotEmpty(extraInfo)) {
            List<CustomAttribute> fulfillExtraList = extraInfo.getFulfillExtraList();
            if (CollectionUtil.isNotEmpty(fulfillExtraList)) {
                Optional<CustomAttribute> tripartiteHealthExpertsFirst = fulfillExtraList.stream().filter(f -> f.getCode().equals("tripartiteHealthExperts")).findFirst();
                if (tripartiteHealthExpertsFirst.isPresent()) {
                    tripartiteHealthExperts = tripartiteHealthExpertsFirst.get().getShowStr();
                }
            }
        }

        //订单所有人
        String staffName = "";
        Integer staffId = orderEntity.getStaffId();
        if (ObjectUtil.isNotEmpty(staffId)) {
            UserEntity userEntity = userRepository.queryById(staffId);
            if (ObjectUtil.isNotEmpty(userEntity)) {
                staffName = userEntity.getName();
            }
        }

        Integer percentFirstTime = orderEntity.getPercentFirstTime();
        String dateAndStoreName = DateUtil.format(new Date(percentFirstTime * 1000L), "yyyy年MM月dd日");

        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("time", dateAndStoreName);
        dataMap.put("storeName", storeName);
        CharSequence charSequence = submitProject.toString().subSequence(0, submitProject.toString().length() - 1);
        dataMap.put("project", (StringUtils.isNotEmpty(submitProject) ? (String) charSequence : ""));
        dataMap.put("customer", heOrderUserSnapshotEntity.getName());
        dataMap.put("tradingVolume", StringUtils.isNotEmpty(tradingVolume) ? (String)tradingVolume.toString().subSequence(0, tradingVolume.toString().length() - 1) : "");
        dataMap.put("productAmount",  AmountChangeUtil.format2Y(AmountChangeUtil.changeF2Y(productCoinPayAmount)));
        dataMap.put("amount",  RMBUtils.formatToseparaDecimals(AmountChangeUtil.changeF2Y(paidAmount)) + (processing.get() > 0L ? "（" + RMBUtils.formatToseparaDecimals(AmountChangeUtil.changeF2Y(processing.get())) + "线下审阅中）" : ""));
        dataMap.put("staff",  staffName);
        dataMap.put("thirdHealthExperts",  tripartiteHealthExperts);
        dataMap.put("brand", BrandTypeEnum.getValueByCode(cfgStore.getType()));
        return dataMap;
    }
}

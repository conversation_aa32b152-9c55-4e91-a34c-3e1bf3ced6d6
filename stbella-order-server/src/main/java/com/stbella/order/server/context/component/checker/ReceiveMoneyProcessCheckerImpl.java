package com.stbella.order.server.context.component.checker;

import cn.hutool.json.JSONObject;
import com.stbella.contract.model.res.ContractSignRecordVO2;
import com.stbella.core.exception.BusinessException;
import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.notice.entity.OaProcessIdRelationPO;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OrderProcessTypeEnum;
import com.stbella.order.common.enums.month.ContractStatusEnum;
import com.stbella.order.common.enums.month.OrderSignTypeEnum;
import com.stbella.order.common.enums.month.PayStatusV2Enum;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.domain.client.OaApproveGateway;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.domain.order.service.ContractOpponentAuthDomainService;
import com.stbella.order.infrastructure.gateway.ContractGateway;
import com.stbella.order.server.order.month.enums.ApprovalDiscountStatusEnum;
import com.stbella.platform.order.api.req.CheckOrderProcessReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Set;


/**
 * @Author: jijunjian
 * @CreateTime: 2024-06-19  11:16
 * @Description: 收款流程检查器
 */
@Component
@Slf4j
public class ReceiveMoneyProcessCheckerImpl implements OrderProcessChecker {

    @Resource
    ContractGateway contractGateway;
    @Resource
    OaApproveGateway oaApproveGateway;
    @Resource
    OrderEntityForRemarkChecker orderEntityForRemarkChecker;
    @Resource
    RuleLinkClient ruleLinkClient;

    /**
     * 过滤器， 判断sku所在分类的属性中 是否包含节假日服务费属性
     *
     * @param req
     * @return
     */
    @Override
    public boolean filter(CheckOrderProcessReq req) {
        if (OrderProcessTypeEnum.PAY.code().equalsIgnoreCase(req.getProcessType())) {
            return true;
        }
        return false;
    }

    /**
     * 收款校验
     * - 优先验证合同主体的认证状态，若[认证状态]=[未认证]则不可签合同/收款，错误提示：请先完成合同主体认证
     * - 验证订单的折扣审批状态，若[审批状态]=[审批中]则不可签合同/收款，警示提示：您发起的订单折扣审批当前仍在审批中，审批通过方可签署合同/收款
     * - 验证订单的折扣审批状态，若[审批状态]=[审批拒绝]则不可签合同/收款，警示提示：您发起的订单折扣审批未通过，可点击重新发起后再次尝试！
     * - 验证《母婴护理服务合同书》的签署状态(若有)，若[签署状态]=[未签订]则不可签合同/收款，错误提示：请先签署订单主合同
     */
    @Override
    public void run(HeOrderEntity order) {

        if (order.getPayStatus().intValue() == PayStatusV2Enum.PAY_OFF.getCode()) {
            throw new BusinessException(ErrorCodeEnum.ORDER_PAID.code() + "", ErrorCodeEnum.ORDER_PAID.desc());
        }

        //先判断是不是减免审批
        OrderReductionEntity lastApprovingOrderReduction = order.fetchLastOrderReduction();
        if (Objects.nonNull(lastApprovingOrderReduction)) {
            if (lastApprovingOrderReduction.isApproving()) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.putOnce(BizConstant.ExtraKey.approveIdKey, lastApprovingOrderReduction.getLocalProcessId());
                throw new BusinessException(ErrorCodeEnum.DECREASE_APPROVAL_ERROR.code() + "", ErrorCodeEnum.DECREASE_APPROVAL_ERROR.desc(), jsonObject);
            }
            checkContract(order);
            return;
        }

        if (CompareUtil.integerEqual(ApprovalDiscountStatusEnum.APPROVING.getCode(), order.getApprovalDiscountStatus())) {
            JSONObject jsonObject = getLastApproveRecord(order.getOrderId());
            throw new BusinessException(ErrorCodeEnum.APPROVE_PROCESSING.code() + "", ErrorCodeEnum.APPROVE_PROCESSING.desc(), jsonObject);
        }

        if (CompareUtil.integerEqual(ApprovalDiscountStatusEnum.INITIATION_FAILED.getCode(), order.getApprovalDiscountStatus())) {
            JSONObject jsonObject = getLastApproveRecord(order.getOrderId());
            throw new BusinessException(ErrorCodeEnum.APPROVE_REJECT.code() + "", ErrorCodeEnum.APPROVE_REJECT.desc(), jsonObject);
        }

        if (CompareUtil.integerEqual(ApprovalDiscountStatusEnum.APPROVAL_FAILED.getCode(), order.getApprovalDiscountStatus())) {
            JSONObject jsonObject = getLastApproveRecord(order.getOrderId());
            throw new BusinessException(ErrorCodeEnum.APPROVE_REJECT.code() + "", ErrorCodeEnum.APPROVE_REJECT.desc(), jsonObject);
        }

        checkContract(order);

    }

    protected JSONObject getLastApproveRecord(Integer orderId) {
        OaProcessIdRelationPO approveRecord = oaApproveGateway.getLastRecordByBizIdAndType(orderId.longValue(), OrderApproveRecordTypeEnum.NEW_DISCOUNT_APPROVAL);
        JSONObject jsonObject = new JSONObject();
        jsonObject.putOnce(BizConstant.ExtraKey.approveIdKey, approveRecord.getLocalProcessId());
        return jsonObject;
    }

    /**
     * 合同校验
     *
     * @param order
     */
    protected void checkContract(HeOrderEntity order) {

        if (!order.isNeedSign()) {
            return;
        }

        // 先判断委托书
        if (order.getSignType().intValue() == OrderSignTypeEnum.SIGN_TYPE_BAILOR.code()) {
            ContractSignRecordVO2 contract = contractGateway.queryOrderContractByType(order.getOrderId(), TemplateContractTypeEnum.ENTRUST);
            if (contract == null || !CompareUtil.integerEqual(ContractStatusEnum.SIGNED.code(), contract.getContractStatus())) {
                throw new BusinessException(ErrorCodeEnum.ENTRUST_CONTRACT_NOT_SIGN.code() + "", ErrorCodeEnum.ENTRUST_CONTRACT_NOT_SIGN.desc());
            }
        }

        if (OmniOrderTypeEnum.getHomeOrderTypeCodeList().contains(order.getOrderType())){
            this.checkMainContract(order);
            return;
        }

        //看下门店合同是否必签
        Set<Integer> noNeedSignStoreIds = ruleLinkClient.getNoNeedSignStoreIds();
        if (noNeedSignStoreIds.contains(order.getStoreId())) {
            return;
        }

        //判断这个订单有没有老订单，老订单是不是签过合同
        boolean remarkCheckerResult = orderEntityForRemarkChecker.check(order);
        log.info("订单备注处理结果：{}", remarkCheckerResult);
        if (remarkCheckerResult) {
            return;
        }

        if (!OmniOrderTypeEnum.getHomeOrderTypeCodeList().equals(order.getOrderType())){
            this.checkMainContract(order);
        }
    }

    public void checkMainContract(HeOrderEntity order) {

        //判断查询合同签署状态
        ContractSignRecordVO2 contract = contractGateway.queryOrderMainContract(order.getOrderId());
        if (contract == null || !CompareUtil.integerEqual(ContractStatusEnum.SIGNED.code(), contract.getContractStatus())) {
            throw new BusinessException(ErrorCodeEnum.CONTRACT_NOT_SIGN.code() + "", ErrorCodeEnum.CONTRACT_NOT_SIGN.desc());
        }
    }

}

package com.stbella.order.server.context.component.processor;

import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.common.enums.order.OrderGiftStatusEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGiftExtendEntity;
import com.stbella.order.domain.repository.HeOrderGiftInfoRepository;
import com.stbella.order.domain.repository.OrderRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.FlowIdentity;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

@Component
@Slf4j
@SnowballComponent(name = "礼赠订单-关联订单首次完款进行资产发放处理", desc = "关联订单已进行资产发放则终止流程")
public class GiftOrderAssetGrantProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private HeOrderGiftInfoRepository heOrderGiftInfoRepository;

    @Resource
    private OrderRepository orderRepository;

    @Override
    public boolean condition(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        return Objects.nonNull(orderEntity) && orderEntity.isJustFullPayment();
    }

    @Override
    public void run(FlowContext bizContext) {

        log.info("检测当前订单是否关联赠送订单，且赠送订单相关资产未进行发放处理");
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        HeOrderGiftExtendEntity heOrderGiftExtendEntity = heOrderGiftInfoRepository.selectByOrderId(orderEntity.getOrderId());
        if (Objects.isNull(heOrderGiftExtendEntity)){
            log.info("当前订单不需要对关联的赠送订单进行资产发放处理");
            return;
        }
        if (StringUtils.isEmpty(heOrderGiftExtendEntity.getGiftOrderSn())){
            log.warn("赠送订单对应赠送订单编码不存在，不需要对其进行资产发放处理");
            return;
        }
        HeOrderEntity order = orderRepository.getByOrderSn(heOrderGiftExtendEntity.getGiftOrderSn());
        order.setJustEffect(Boolean.TRUE);
        order.setIsPreviouslyFullyPaid(Boolean.FALSE);
        order.fullPayment(null);
        FlowContext context = new FlowContext();
        context.setAttribute(HeOrderEntity.class, order);
        FlowIdentity identity = FlowIdentity.builder()
                .bizActivity(BizActivityEnum.GIFT_ORDER_ASSET_GRANT.code())
                .idSlice("GiftOrder")
                .idSlice("AssetGrant")
                .build();
        SnowballFlowLauncher.fire(identity, context);
        heOrderGiftExtendEntity.setStatus(OrderGiftStatusEnum.APPROVAL_SEND.getCode());
        heOrderGiftExtendEntity.setReleaseTime(new Date());
        heOrderGiftInfoRepository.update(heOrderGiftExtendEntity);
    }
}

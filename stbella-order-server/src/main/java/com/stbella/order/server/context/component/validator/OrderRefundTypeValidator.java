package com.stbella.order.server.context.component.validator;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import java.util.List;

@Slf4j
@Component
@RefreshScope
@SnowballComponent(name = "订单退款前校验")
public class OrderRefundTypeValidator implements IExecutableAtom<FlowContext> {

    @Override
    public void run(FlowContext bizContext) {
        CreateRefundReq createRefundReq = bizContext.getAttribute(CreateRefundReq.class);

        List<CreateRefundReq.GoodsInfo> goodsInfoList = createRefundReq.getGoodsInfoList();
        if (CollUtil.isEmpty(goodsInfoList)) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "请选择退款商品！");
        }

    }

    @Override
    public boolean condition(FlowContext context) {
        CreateRefundReq createRefundReq = context.getAttribute(CreateRefundReq.class);
        HeOrderEntity orderEntity = context.getAttribute(HeOrderEntity.class);
        return ObjectUtil.isNotEmpty(createRefundReq) && ObjectUtil.isNotEmpty(createRefundReq.getOrderId()) && orderEntity.isNewOrder();
    }
}

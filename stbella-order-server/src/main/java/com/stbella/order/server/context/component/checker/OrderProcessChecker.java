package com.stbella.order.server.context.component.checker;

import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.server.context.component.assembler.additional.AdditionalContext;
import com.stbella.platform.order.api.req.CheckOrderProcessReq;
import com.stbella.platform.order.api.res.SkuAdditionalInfo;

/**
 * 订单流程校验
 * <AUTHOR>
 */
public interface OrderProcessChecker {

    /**
     * 过滤器
     * @param req
     * @return
     */
    boolean filter(CheckOrderProcessReq req);

    /**
     *  run
     */
    void run(HeOrderEntity req);
}

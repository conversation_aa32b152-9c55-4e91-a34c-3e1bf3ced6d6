package com.stbella.order.server.context.component.cancel.assembler;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.production.OrderGiftExtendEntity;
import com.stbella.order.domain.order.production.OrderProductionExtendEntity;
import com.stbella.order.domain.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@SnowballComponent(name = "客诉工单取消审批-获取相关业务信息")
public class ComplaintCancelAssemblerProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private OrderRefundRepository orderRefundRepository;

    @Resource
    private HeOrderRefundGoodsRepository orderRefundGoodsRepository;

    @Resource
    private HeOrderRefundDisabledProductRepository orderRefundDisabledProductRepository;

    @Resource
    private ProductionAmountPayRepository productionAmountPayRepository;

    @Resource
    private ProductionAmountRefundRepository productionAmountRefundRepository;

    @Resource
    private OrderGiftExtendRepository orderGiftExtendRepository;

    @Resource
    private OrderProductionExtendRepository orderProductionExtendRepository;

    @Override
    public void run(FlowContext bizContext) {

        log.info("客诉工单发起人撤回-获取相关信息操作");
        HeCustomerComplaintsEntity heCustomerComplaintsEntity = bizContext.getAttribute(HeCustomerComplaintsEntity.class);

        if (Objects.isNull(heCustomerComplaintsEntity.getOrderId()) || (Objects.isNull(heCustomerComplaintsEntity.getCashRefundId()) && Objects.isNull(heCustomerComplaintsEntity.getRefundOrderId()))){
            log.warn("处理流程终止");
            return;
        }
        HeOrderEntity heOrderEntity = orderRepository.getByOrderId(heCustomerComplaintsEntity.getOrderId().intValue());
        if (Objects.isNull(heOrderEntity)){
            return;
        }
        bizContext.setAttribute(HeOrderEntity.class, heOrderEntity);
        boolean newOrder = heOrderEntity.isNewOrder();

        List<HeOrderRefundEntity> heOrderRefundList = getHeOrderRefundList(heCustomerComplaintsEntity);
        if (CollectionUtils.isNotEmpty(heOrderRefundList)){
            bizContext.setListAttribute(HeOrderRefundEntity.class, heOrderRefundList);
        }

        if (Objects.nonNull(heCustomerComplaintsEntity.getRefundOrderId()) && newOrder){
            List<HeOrderRefundDisabledProductEntity> orderRefundDisabledProductEntityList = orderRefundDisabledProductRepository.queryByRefundId(heCustomerComplaintsEntity.getRefundOrderId());
            if (CollectionUtils.isNotEmpty(orderRefundDisabledProductEntityList)){
                bizContext.setListAttribute(HeOrderRefundDisabledProductEntity.class, orderRefundDisabledProductEntityList);

                List<Integer> buyIds = orderRefundDisabledProductEntityList.stream().filter(o -> o.getProductType() == 0).map(HeOrderRefundDisabledProductEntity::getProductId).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(buyIds)) {
                    List<OrderProductionExtendEntity> orderProductionExtendEntities = orderProductionExtendRepository.listByExtendIds(buyIds);
                    bizContext.setListAttribute(OrderProductionExtendEntity.class, orderProductionExtendEntities);
                }

                List<Integer> giftIds = orderRefundDisabledProductEntityList.stream().filter(o -> o.getProductType() == 1).map(HeOrderRefundDisabledProductEntity::getProductId).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(giftIds)) {
                    List<OrderGiftExtendEntity> orderGiftExtendEntities = orderGiftExtendRepository.listByGiftIds(giftIds);
                    bizContext.setListAttribute(OrderGiftExtendEntity.class, orderGiftExtendEntities);
                }
            }

            HeOrderRefundEntity heOrderRefundEntity = heOrderRefundList.stream().filter(item -> heCustomerComplaintsEntity.getRefundOrderId().equals(item.getId().longValue())).findFirst().orElse(null);
            //退款商品列表
            if (Objects.nonNull(heOrderRefundEntity)){
                List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList = orderRefundGoodsRepository.queryByRefundOrderSn(heOrderRefundEntity.getRefundOrderSn());
                if (CollectionUtils.isNotEmpty(orderRefundGoodsEntityList)){
                    bizContext.setListAttribute(HeOrderRefundGoodsEntity.class, orderRefundGoodsEntityList);
                }
            }
        }

        if (newOrder){
            return;
        }
        //老订单可能会出现产康金支付的情况
        heOrderRefundList.stream().filter(item -> heCustomerComplaintsEntity.getRefundOrderId().equals(item.getId().longValue()) && Objects.nonNull(item.getType()) && item.getType() == 2 ).findFirst().ifPresent(heOrderRefund -> {

            if (Objects.nonNull(heOrderRefund.getProjectId())){
                HeUserProductionAmountPayLogEntity heUserProductionAmountPayLogEntity = productionAmountPayRepository.getOneById(heOrderRefund.getProjectId().intValue());
                if (Objects.nonNull(heUserProductionAmountPayLogEntity)){
                    bizContext.setAttribute(HeUserProductionAmountPayLogEntity.class, heUserProductionAmountPayLogEntity);
                    List<HeUserProductionAmountRefundLogEntity> heUserProductionAmountRefundLogEntities = productionAmountRefundRepository.queryRecordListByOrderId(heOrderRefund.getOrderId());
                    if (CollectionUtils.isNotEmpty(heUserProductionAmountRefundLogEntities)){
                        HeUserProductionAmountRefundLogEntity productionAmountRefundLogEntity = heUserProductionAmountRefundLogEntities.stream().filter(item -> item.getPayId().equals(heOrderRefund.getProjectId().intValue()) && heOrderRefund.getApplyAmount().equals(item.getApplyAmount())).findFirst().orElse(null);
                        bizContext.setAttribute(HeUserProductionAmountRefundLogEntity.class, productionAmountRefundLogEntity);
                    }
                }
            }
        });
    }

    private List<HeOrderRefundEntity> getHeOrderRefundList(HeCustomerComplaintsEntity heCustomerComplaintsEntity){

        List<HeOrderRefundEntity> refundAllByOrderId = new ArrayList<>();
        List<Integer> orderRefundIds = new ArrayList<>();
        if (Objects.nonNull(heCustomerComplaintsEntity.getCashRefundId())) {
            orderRefundIds.add(heCustomerComplaintsEntity.getCashRefundId());
        }

        if (StringUtils.isNotEmpty(heCustomerComplaintsEntity.getOtherCompensationId())){
            Map<String, Integer> otherCompensationIdMap = JSONObject.parseObject(heCustomerComplaintsEntity.getOtherCompensationId(), Map.class);
            if (!otherCompensationIdMap.isEmpty()){
                orderRefundIds.addAll(otherCompensationIdMap.values());
            }
        }
        List<HeOrderRefundEntity> orderRefundEntities = orderRefundRepository.batchQueryEntityByIds(orderRefundIds);
        if (CollectionUtils.isNotEmpty(orderRefundEntities)) {
            refundAllByOrderId.addAll(orderRefundEntities);
        }

        if (Objects.isNull(heCustomerComplaintsEntity.getRefundOrderId())){
            return refundAllByOrderId;
        }

        List<HeOrderRefundEntity> heOrderRefundList = orderRefundRepository.getRefundByOrderId(heCustomerComplaintsEntity.getOrderId().intValue());

        HeOrderRefundEntity heOrderRefundEntity = heOrderRefundList.stream().filter(item -> heCustomerComplaintsEntity.getRefundOrderId().equals(item.getId().longValue())).findFirst().orElse(null);

        if (Objects.isNull(heOrderRefundEntity)){
            return refundAllByOrderId;
        }

        refundAllByOrderId.add(heOrderRefundEntity);

        List<HeOrderRefundEntity> subOrderRefundList = heOrderRefundList.stream().filter(item -> heOrderRefundEntity.getRefundOrderSn().equals(item.getParentRefundOrderSn())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(subOrderRefundList)){
            refundAllByOrderId.addAll(subOrderRefundList);
        }
        return refundAllByOrderId;
    }
}

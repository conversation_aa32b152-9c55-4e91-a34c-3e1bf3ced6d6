package com.stbella.order.server.context.component.contract.stage.template;

import com.stbella.order.server.context.component.contract.stage.ContractParamFillContext;

import java.util.Map;

public interface AgreementParamsFillTemplate {

    /**
     * 模板执行处理方法
     * @return
     */
    Map<String, String> buildParam(ContractParamFillContext contractParamFillContext);

    /**
     * 区分业务处理Handle
     * @return
     */
    Integer bizHandleType();
}

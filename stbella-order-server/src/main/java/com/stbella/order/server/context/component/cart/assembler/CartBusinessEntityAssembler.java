package com.stbella.order.server.context.component.cart.assembler;

import com.stbella.order.server.context.component.spi.CartRelatedBusinessEntityQuerySpi;
import com.stbella.platform.order.api.req.RestoreCartReq;
import com.stbella.platform.order.api.res.CartRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.SnowballExtensionInvoker;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

/**
 * @Author: ji<PERSON><PERSON><PERSON>
 * @CreateTime: 2024-05-29  15:20
 * @Description: 购物车关联业务实体组装器
 */
@Component
@Slf4j
@SnowballComponent(name = "购物车关联业务实体组装器")
public class CartBusinessEntityAssembler implements IExecutableAtom<FlowContext> {
    @Override
    public void run(FlowContext bizContext) {
        RestoreCartReq req = bizContext.getAttribute(RestoreCartReq.class);
        CartRes cartRes = SnowballExtensionInvoker.invoke(CartRelatedBusinessEntityQuerySpi.class, req);

        bizContext.setAttribute(CartRes.class, cartRes);

    }
}

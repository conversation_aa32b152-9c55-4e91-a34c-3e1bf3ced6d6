package com.stbella.order.server.context.component.refund.processor;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.order.common.enums.month.RefundRecordPayStatusEnum;
import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.repository.OrderRefundRepository;
import com.stbella.order.server.order.month.request.approval.ApprovalStatusInfo;
import com.stbella.order.server.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.*;


@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "客诉闭环退款审批后现金退款操作")
public class ComplaintApproveCashProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderRefundRepository orderRefundRepository;

    @Override
    public boolean condition(FlowContext context) {
        return true;
    }

    @Override
    public void run(FlowContext bizContext) {
        log.info("客诉闭环退款审批后现金退款操作");
        HeCustomerComplaintsEntity customerComplaintsEntity = bizContext.getAttribute(HeCustomerComplaintsEntity.class);
        log.info("客诉闭环退款审批后现金退款操作：customerComplaintsEntity:{}", JsonUtil.write(customerComplaintsEntity));
        ApprovalStatusInfo approvalStatusInfo = bizContext.getAttribute(ApprovalStatusInfo.class);
        log.info("客诉闭环退款审批后现金退款操作：approvalStatusInfo:{}", JsonUtil.write(approvalStatusInfo));

        Integer cashRefundId = customerComplaintsEntity.getCashRefundId();

        log.info("客诉闭环退款审批后现金退款操作：cashRefundId:{}", JsonUtil.write(cashRefundId));

        Long opTime = System.currentTimeMillis();
        if (ObjectUtil.isNotEmpty(cashRefundId)) {
            HeOrderRefundEntity orderRefundEntity = orderRefundRepository.getOneById(cashRefundId);

            log.info("客诉闭环退款审批后现金退款操作：orderRefundEntity:{}", JsonUtil.write(orderRefundEntity));

            if (ObjectUtil.isNotEmpty(orderRefundEntity)) {
                orderRefundEntity.setUpdatedAt(opTime / 1000);
                if (approvalStatusInfo.isAgree()) {
                    orderRefundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode());
                    orderRefundEntity.setFinishAt(opTime / 1000);
                    orderRefundEntity.setAgreeAt(opTime / 1000);
                } else {
                    orderRefundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_2.getCode());
                }
            }

            log.info("客诉闭环退款审批后现金退款操作END：orderRefundEntity:{}", JsonUtil.write(orderRefundEntity));
            orderRefundRepository.updateOneById(orderRefundEntity);
        }

        if (StringUtils.isNotEmpty(customerComplaintsEntity.getOtherCompensationId())){

            Map<String, Integer> otherCompensationIdMap = new HashMap<>();;
            try {
                otherCompensationIdMap =JSONObject.parseObject(customerComplaintsEntity.getOtherCompensationId(), Map.class);
            } catch (Exception e) {
                log.error("解析其他补偿Id失败：{}", e.getMessage());
            }
            if (!otherCompensationIdMap.isEmpty()){
                List<HeOrderRefundEntity> orderRefundList = orderRefundRepository.batchQueryEntityByIds(new ArrayList<>(otherCompensationIdMap.values()));
                if (CollectionUtils.isNotEmpty(orderRefundList)){
                    List<HeOrderRefundEntity> heOrderRefundEntityList = new ArrayList<>();
                    for (HeOrderRefundEntity orderRefundEntity : orderRefundList) {
                        orderRefundEntity.setUpdatedAt(opTime / 1000);
                        if (approvalStatusInfo.isAgree()) {
                            orderRefundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode());
                            orderRefundEntity.setFinishAt(orderRefundEntity.getUpdatedAt());
                            orderRefundEntity.setAgreeAt(orderRefundEntity.getUpdatedAt());
                        } else {
                            orderRefundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_2.getCode());
                        }
                        heOrderRefundEntityList.add(orderRefundEntity);
                    }
                    orderRefundRepository.batchUpdateById(heOrderRefundEntityList);
                }
            }
        }

        if (ObjectUtil.isEmpty(customerComplaintsEntity.getRefundOrderId()) && approvalStatusInfo.isAgree()) {
            //如果没有退款，就成功
            customerComplaintsEntity.processSuccess();
        }

    }

}

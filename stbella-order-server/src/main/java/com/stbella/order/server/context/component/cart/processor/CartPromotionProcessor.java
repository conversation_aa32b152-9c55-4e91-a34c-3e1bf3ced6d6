package com.stbella.order.server.context.component.cart.processor;

import com.alibaba.fastjson.JSONObject;
import com.stbella.marketing.api.res.goods.ActivityMinProgramDetailVO;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.domain.order.entity.HeCartEntity;
import com.stbella.order.domain.order.entity.HeCartGoodsEntity;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.repository.HeCartGoodsRepository;
import com.stbella.order.server.manager.MarketingManager;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import com.stbella.platform.order.api.res.PromotionInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 活动变更校验，判断前端提交的活动是否更新了
 */
@Component
@Slf4j
@SnowballComponent(name = "购物车活动信息处理", desc = "")
public class CartPromotionProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private MarketingManager marketingManager;

    @Resource
    private HeCartGoodsRepository heCartGoodsRepository;


    @Override
    public void run(FlowContext bizContext) {

        Integer clientUid = (Integer) bizContext.getAttribute(BizConstant.ExtraKey.clientUid);
        if (clientUid == -1) {
            log.info("客户都没传，不需要校验活动");
            return;
        }

        HeCartEntity cartEntity = bizContext.getAttribute(HeCartEntity.class);
        CfgStoreEntity cfgStoreEntity = bizContext.getAttribute(CfgStoreEntity.class);

        List<HeCartGoodsEntity> goodsEntityList = heCartGoodsRepository.queryListByCartId(cartEntity.getCartId());
        log.info("goodsEntityList 为：{}", JSONObject.toJSONString(goodsEntityList));
        List<HeCartGoodsEntity> commonGoods = goodsEntityList.stream().filter(o -> !o.isGift() && Objects.isNull(o.getPromotionInfo())).collect(Collectors.toList());
        List<SkuDetailInfo> skuDetailInfos = new ArrayList<>();
        for (HeCartGoodsEntity commonGood : commonGoods) {
            SkuDetailInfo skuDetailInfo = new SkuDetailInfo();
            skuDetailInfo.setGoodsId(commonGood.getGoodsId());
            skuDetailInfo.setSkuId(commonGood.getSkuId());
            skuDetailInfo.setGoodsType(commonGood.getGoodsType());
            List<HeCartGoodsEntity> subGoodsList = commonGood.getSubGoodsList();
            if (CollectionUtils.isNotEmpty(subGoodsList)) {
                List<SkuDetailInfo> subSkuDetailInfos = new ArrayList<>();
                skuDetailInfo.setSubList(subSkuDetailInfos);
                for (HeCartGoodsEntity heCartGoodsEntity : subGoodsList) {
                    SkuDetailInfo skuDetailInfo1 = new SkuDetailInfo();
                    skuDetailInfo1.setSkuId(heCartGoodsEntity.getSkuId());
                    skuDetailInfo1.setGoodsId(heCartGoodsEntity.getGoodsId());
                    skuDetailInfo1.setParentId(commonGood.getGoodsId());
                    skuDetailInfo1.setNum(heCartGoodsEntity.getNum());
                    skuDetailInfo1.setSkuNum(heCartGoodsEntity.getSkuNum());
                    skuDetailInfo1.setGoodsType(heCartGoodsEntity.getGoodsType());
                    subSkuDetailInfos.add(skuDetailInfo1);
                }
            }
            skuDetailInfo.setNum(commonGood.getNum());
            skuDetailInfo.setSkuNum(commonGood.getSkuNum());
            skuDetailInfos.add(skuDetailInfo);
        }

        List<ActivityMinProgramDetailVO> activityMinProgramDetailVOS = marketingManager.queryStoreActivity(clientUid, cfgStoreEntity.getStoreId(), skuDetailInfos, cartEntity.getScene());
        log.info("命中活动的数量为{}", activityMinProgramDetailVOS.size());
        Map<Integer, Long> activityMap = activityMinProgramDetailVOS.stream().collect(Collectors.toMap(ActivityMinProgramDetailVO::getActivityId, ActivityMinProgramDetailVO::getGmtModified));
        //判断提交上来的活动跟查询出来的活动是否一致
        List<Integer> heCartGoodIdList = new ArrayList<>();
        for (HeCartGoodsEntity heCartGoodsEntity : goodsEntityList) {
            PromotionInfo promotionInfo = heCartGoodsEntity.getPromotionInfo();
            log.info("cart goods id is{},promotion is{}", heCartGoodsEntity.getCartId(), promotionInfo);
            if (Objects.nonNull(promotionInfo) && Objects.nonNull(promotionInfo.getPromotionId())) {
                Long gmtModified = activityMap.get(promotionInfo.getPromotionId());
                if (Objects.isNull(gmtModified) || Long.valueOf(promotionInfo.getUpdateAt()).compareTo(gmtModified) != 0) {
                    heCartGoodIdList.add(heCartGoodsEntity.getId());
                }
            }
        }
        if (!heCartGoodIdList.isEmpty()) {
            log.info("查询购物车的时候变更活动，清空购物车中的活动商品，商品ID={}", heCartGoodIdList);
            heCartGoodsRepository.deleteByIds(heCartGoodIdList);
        }
    }


}

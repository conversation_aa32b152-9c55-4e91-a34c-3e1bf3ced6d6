package com.stbella.order.server.manager;

import com.stbella.core.result.Result;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.repository.StoreRepository;
import com.stbella.store.goodz.api.GoodsCombinationQueryService;
import com.stbella.store.goodz.api.category.BackCategoryQueryService;
import com.stbella.store.goodz.res.PropertyDetailVO;
import com.stbella.store.property.api.PropertyQueryService;
import com.stbella.store.server.ecp.entity.CfgStore;
import com.stbella.store.server.ecp.service.CfgStoreService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 门店调用
 * @date 2021/12/6 3:06 下午
 */
@Slf4j
@Component
public class StoreManager {


    @Resource
    StoreRepository storeRepository;

    @DubboReference
    private BackCategoryQueryService backCategoryQueryService;

    @DubboReference
    private PropertyQueryService propertyQueryService;

    @DubboReference
    private CfgStoreService cfgStoreService;

    @DubboReference
    private GoodsCombinationQueryService goodsCombinationQueryService;


    public List<CfgStore> queryStoreInfoByStoreIdList(List<Integer> storeIdList) {
        return cfgStoreService.queryStoreInfoByStoreIdList(storeIdList);
    }

    /**
     * 获取全类目路径
     *
     * @param backId
     * @return
     */
    public String getAllBackName(Integer backId) {
        return backCategoryQueryService.getAllBackName(backId);
    }

    /**
     * ruleLike配置信息
     *
     * @param code
     * @return
     */
    public PropertyDetailVO queryPropertyDetail(String code) {

        try {
            log.info("获取ruleLike配置信息code:{}", code);
            Result<PropertyDetailVO> goodsUnit = propertyQueryService.queryDetailByCode(code);
            return goodsUnit.getData();
        } catch (Exception e) {
            log.error("获取ruleLike配置信息发生异常msg:{}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * @param storeId
     * @return
     * @description: 获取门店信息
     */
    public CfgStoreEntity queryByStoreId(Integer storeId) {
        CfgStoreEntity store = storeRepository.queryCfgStoreById(storeId);
        return store;
    }


    public List<Integer> upgradeGoodsIdList(Integer storeId) {
        return goodsCombinationQueryService.queryUpgradeGoods(4, storeId).getData();
    }

}

package com.stbella.order.server.context.component.processor.contract;

import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.service.OrderParamHistoryService;
import com.stbella.order.server.context.component.processor.ClientContractSignMainProcessor;
import com.stbella.order.server.contract.req.OrderParamHistoryPushDTO;
import com.stbella.order.server.manager.ContractManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

/**
 * @Author: JJ
 * @Description: 推送合同参数
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "推送合同参数")
public class PushContractParamsProcessor implements IExecutableAtom<FlowContext> {


    @Resource
    OrderParamHistoryService orderParamHistoryService;

    @Resource
    private ContractManager contractManager;


    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);
        if (order.isNeedSign()){
            OrderParamHistoryPushDTO pushDTO = orderParamHistoryService.buildOrderParamHistory(order);
            contractManager.pushOrderParamHistory(pushDTO);
        }
    }
}

package com.stbella.order.server.context.component.contract.processor;

import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.HeOrderBailorSnapshotEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.server.context.component.contract.checker.AbstractContractCreateValidator;
import com.stbella.order.server.context.component.contract.checker.ContractCreateValidateContext;
import com.stbella.order.server.context.component.contract.checker.ContractValidationContext;
import com.stbella.platform.order.api.contract.req.ContractBaseReq;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import com.stbella.store.server.ecp.entity.CfgStore;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.List;

/**
 * 合同创建前参数校验
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "合同创建前参数校验")
public class ContractCreateValidatorProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private ContractCreateValidateContext contractCreateValidateContext;

    @Override
    public void run(FlowContext bizContext) {
        ContractValidationContext contractValidationContext = bulidContactValidationContext(bizContext);

        AbstractContractCreateValidator validator = contractCreateValidateContext.getValidator(contractValidationContext);
        validator.validate(contractValidationContext);

    }

    private static @NotNull ContractValidationContext bulidContactValidationContext(FlowContext bizContext) {
        HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);
        ContractBaseReq req = bizContext.getAttribute(ContractBaseReq.class);
        CfgStoreEntity cfgStore = bizContext.getAttribute(CfgStoreEntity.class);
        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = bizContext.getAttribute(HeOrderUserSnapshotEntity.class);
        HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity = bizContext.getAttribute(HeOrderBailorSnapshotEntity.class);
        List<OrderContractSignRecordVO> orderContractSignRecordVOS = bizContext.getListAttribute(OrderContractSignRecordVO.class);

        ContractValidationContext contractValidationContext = new ContractValidationContext();

        contractValidationContext.setOrderEntity(order);
        contractValidationContext.setRequest(req);
        contractValidationContext.setCfgStore(cfgStore);
        contractValidationContext.setHeOrderUserSnapshotEntity(heOrderUserSnapshotEntity);
        contractValidationContext.setHeOrderBailorSnapshotEntity(heOrderBailorSnapshotEntity);
        contractValidationContext.setSignRecords(orderContractSignRecordVOS);
        return contractValidationContext;
    }


}

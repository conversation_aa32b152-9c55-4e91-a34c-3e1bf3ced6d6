package com.stbella.order.server.contract.constant;

import java.util.regex.Pattern;

public interface RegexConstant {

    /**
     * 港澳来往大陆通行证证件号码正则表达式 香港H开头  澳门M开头
     */
    Pattern p1 = Pattern.compile("^[HM]{1}([0-9]{10}|[0-9]{8})$");

    /**
     * 台湾来往大陆通行证证件号码正则表达式
     */
    Pattern p2 = Pattern.compile("^[0-9]{8}|[0-9]{10}$");

    /**
     * 护照正则表达式
     */
    Pattern p3 = Pattern.compile("^[a-zA-Z0-9]{5,17}$");
}

package com.stbella.order.server.order.nutrition.controller;

import com.stbella.core.result.Result;
import com.stbella.order.server.order.nutrition.request.UpdateLogisticsRequest;
import com.stbella.order.server.order.nutrition.response.NutritionLogisticsExpensesVO;
import com.stbella.order.server.order.nutrition.service.NutritionLogisticsExpensesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Validated
@Api(tags = "费用管理-物流费用")
@RestController
@RequestMapping("/order/nutrition/logisticsExpenses")
public class NutritionLogisticsExpensesController {

    @Resource
    private NutritionLogisticsExpensesService nutritionLogisticsExpensesService;

    @ApiOperation(value = "查询物流费用")
    @GetMapping("/get/logisticsExpenses")
    public Result<List<NutritionLogisticsExpensesVO>> getLogisticsExpenses(Integer year) {
        return Result.success(nutritionLogisticsExpensesService.getLogisticsExpenses(year));
    }

    @ApiOperation(value = "编辑物流费用")
    @PostMapping("/update/logisticsExpenses")
    public Result updateLogisticsExpenses(@RequestBody UpdateLogisticsRequest updateLogisticsRequest) {
        nutritionLogisticsExpensesService.updateLogisticsExpenses(updateLogisticsRequest.getAllData().get(0));
        return Result.success();
    }


}

package com.stbella.order.server.order.cts.statemachine.action.servefee;

import com.stbella.order.server.order.cts.constant.CtsOrderConstant;
import com.stbella.order.server.order.cts.dto.RefundNotifyMqDTO;
import com.stbella.order.server.order.cts.enums.OrderStatusStateMachineStatusEnum;
import com.stbella.order.server.order.cts.statemachine.OrderContext;
import com.stbella.order.server.order.cts.statemachine.OrderStateEvent;
import com.stbella.order.server.order.cts.statemachine.action.common.CommonAction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.action.Action;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 管理费订单退款结束
 *
 * <AUTHOR>
 * @date 2022-03-10 11:41
 * @sine 1.0.0
 */
@Slf4j
@Component
public class ServeFeeRefundEndAction implements Action<OrderStatusStateMachineStatusEnum, OrderStateEvent> {

    @Resource
    private CommonAction commonAction;

    @Override
    public void execute(StateContext<OrderStatusStateMachineStatusEnum, OrderStateEvent> stateContext) {
        log.info("执行育婴师-管理费退款回调" + stateContext.toString());
        OrderContext context = (OrderContext) stateContext.getMessageHeader(OrderContext.HEADER_KEY);

        final RefundNotifyMqDTO refundNotifyMqDTO = (RefundNotifyMqDTO) context.getModel();

        //退款成功回调
        commonAction.refundNotify(refundNotifyMqDTO, context.getOrderCtsPO());

        // 填充orderNo
        stateContext.getStateMachine().getExtendedState().getVariables().put(CtsOrderConstant.MACHINE_VARIABLES_ORDER_NO, context.getOrderCtsPO().getOrderNo());
        log.info("执行育婴师-管理费退款回调成功 orderNo:{}", context.getOrderCtsPO().getOrderNo());
    }
}

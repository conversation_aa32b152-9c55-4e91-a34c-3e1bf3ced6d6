package com.stbella.order.server.context.component.spi.impl.pay;

import cn.hutool.json.JSONObject;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.context.component.spi.PaySpi;
import com.stbella.order.server.listener.event.OrderDecreasePaySuccessEvent;
import com.stbella.order.server.listener.event.OrderDecreaseSuccessEvent;
import com.stbella.order.server.order.month.enums.PayStatusEnum;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.annotation.FuncPointSpiImpl;
import top.primecare.snowball.extpoint.definition.FuncSpiConfig;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * 减免支付
 */
@Component
@FuncPointSpiImpl(name = "减免支付")
public class ReducePaySpiImpl implements PaySpi {


    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private OrderRepository orderRepository;

    @Override
    public boolean condition(HeIncomeRecordEntity param) {
        return CompareUtil.integerEqual(param.getPayType(), OmniPayTypeEnum.REDUCTION.getCode());
    }

    @Override
    public JSONObject invoke(HeIncomeRecordEntity param) {

        HeOrderEntity heOrderEntity = orderRepository.queryOrderById(param.getOrderId());


        JSONObject payResult = new JSONObject();
        payResult.putOnce("result", payResult);

        String transactionalNo =  "";
        OrderDecreasePaySuccessEvent paySuccessEvent = OrderDecreasePaySuccessEvent.builder()
                .orderNo(param.getOrderSn())
                .payStatus(PayStatusEnum.PAY_STATUS_SUCCESS.getCode())
                .outTradeNo(param.getIncomeSn())
                .orderId(heOrderEntity.getOrderId())
                .payType(OmniPayTypeEnum.REDUCTION.getCode())
                .payAmount(AmountChangeUtil.changeF2Y(param.getIncome().longValue()))
                .payTime(new Date()).build();

        applicationContext.publishEvent(paySuccessEvent);

        return new JSONObject();
    }

    @Override
    public FuncSpiConfig config() {
        //默认优先级高
        return new FuncSpiConfig(true, Integer.MIN_VALUE);
    }

    public static Long getAmount(BigDecimal fxRate, Integer amount) {
        return fxRate.multiply(new BigDecimal(amount)).divide(BigDecimal.ONE, 0, RoundingMode.HALF_UP).longValue();
    }
}

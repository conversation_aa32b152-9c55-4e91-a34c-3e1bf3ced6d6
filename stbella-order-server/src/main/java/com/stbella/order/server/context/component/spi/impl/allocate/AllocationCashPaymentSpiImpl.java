package com.stbella.order.server.context.component.spi.impl.allocate;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.core.OrderRefundNatureEnum;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.service.OrderGoodsDomainService;
import com.stbella.order.domain.order.service.OrderRefundDomainService;
import com.stbella.order.domain.repository.IncomePaidAllocationRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.context.component.spi.AllocateSpi;
import com.stbella.order.server.context.dto.OrderAmountDto;
import com.stbella.order.server.context.dto.OrderGoodsAllocationDto;
import com.stbella.order.server.convert.PaidAllocationConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.annotation.FuncPointSpiImpl;
import top.primecare.snowball.extpoint.definition.FuncSpiConfig;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: JJ
 * @Description: 分摊实付
 * <p>
 * 前提：组合子商品，下单时根据商品在组合的比重计算出每个商品的分摊原价
 * <p>
 * O 表示订单
 * A 表示A商品
 * P 表示本次支付金额
 * O已付 所有收款
 * <p>
 * O应付 = O签单 - 减免
 * O待付 = O应付  + O退回重付 - O已付
 * 总原价  =  Sum( 原数量 * 原单价)
 * <p>
 * A应付 = O应付 * (A原数量 * A原单价 / 总原价)
 * A待付 = A应付 - A已付  + A退回重付
 * A分摊实付 = P * (A待付 / O待付 )
 * <p>
 * 举个例子： 有A，B两个商品。当前支付P
 * <p>
 * P =  A分 + B分 = P * A待/O待 + P* B待/O待
 * = P * ((A待付  +B待付 ) / O待付)
 * = p* ( (A应 + B应  + O退回重  - O已付 ) / O待付)
 * = p* ( (O应付  + O退回重  - O已付 ) / O待付)
 * = p
 * <p>
 * 注：如果有除不尽的情况，最后一个商品做尾差处理： 总价 - 其他商品
 **/


@Slf4j
@Component
@FuncPointSpiImpl(name = "现金支付分摊")
public class AllocationCashPaymentSpiImpl implements AllocateSpi {

    @Resource
    OrderRefundDomainService refundDomainService;
    @Resource
    IncomePaidAllocationRepository allocationRepository;
    @Resource
    OrderGoodsDomainService orderGoodsDomainService;
    @Resource
    PaidAllocationConverter allocationConverter;
    @Resource
    OrderRepository orderRepository;

    @Override
    public JSONObject invoke(HeIncomeRecordEntity incomeRecordEntity) {

        HeOrderEntity order = orderRepository.queryOrderById(incomeRecordEntity.getOrderId());

        //每个商品退回重付
        List<HeOrderRefundGoodsEntity> orderGoodsRefundList = refundDomainService.querySuccessfulOrderGoodsAllocationRefund(order.getOrderId())
                .stream().filter(a -> Objects.nonNull(a.getRefundNature()))
                .filter(refund -> OrderRefundNatureEnum.TEMP_REFUND.code().intValue() == refund.getRefundNature())
                .collect(Collectors.toList());
        //计算总退回重复退款金额
        Integer tempRefund = orderGoodsRefundList.stream()
                .filter(refund -> OrderRefundNatureEnum.TEMP_REFUND.code().intValue() == refund.getRefundNature())
                .mapToInt(HeOrderRefundGoodsEntity::getRefundAmount)
                .sum();

        // 获取所有成功退款的商品（包括退回重付和普通退款）
        List<HeOrderRefundGoodsEntity> allSuccessfulRefunds = refundDomainService.querySuccessfulOrderGoodsAllocationRefund(order.getOrderId());
        // 计算每个商品的已退款数量
        Map<String, Integer> orderGoodsRefundNumMap = getOrderGoodsRefundNumMap(allSuccessfulRefunds);

        // 计算每个商品的普通退款金额（非退回重付）
        List<HeOrderRefundGoodsEntity> normalRefunds = allSuccessfulRefunds.stream()
                .filter(refund -> refund.getRefundNature() == null ||
                        !OrderRefundNatureEnum.TEMP_REFUND.code().equals(refund.getRefundNature()))
                .collect(Collectors.toList());
        Map<String, Integer> orderGoodsNormalRefundMap = getOrderGoodsNormalRefundMap(normalRefunds);

        // 每个支付金额
        Map<String, Integer> orderGoodsPaidMap = getOrderGoodsPaidMap(order.getOrderId());
        int totalPaid = orderGoodsPaidMap.values().stream().mapToInt(a -> a).sum();

        OrderAmountDto orderAmountDto = new OrderAmountDto()
                .setTotalAmount(AmountChangeUtil.changeF2Y(order.getOrderAmount().longValue()))
//                .setPayAmount(AmountChangeUtil.changeF2Y(order.calPayable().longValue()))
                .setPaid(AmountChangeUtil.changeF2Y(totalPaid))
                .setTempRefund(AmountChangeUtil.changeF2Y(tempRefund))
                .setCurrentIncomeId(incomeRecordEntity.getId())
                .setCurrentPay(AmountChangeUtil.changeF2Y(incomeRecordEntity.getIncome().longValue()));

        //本次分摊记录
        List<OrderGoodsAllocationDto> allocationDtos = initAllocationAndPaid(order.getOrderId(), orderGoodsPaidMap, orderGoodsRefundNumMap, orderGoodsNormalRefundMap);

        //过滤掉数量为空的
        allocationDtos = allocationDtos.stream().filter(o->o.getGoodsNum() > 0).collect(Collectors.toList());

        log.info("allocationDtos: {}", JSONUtil.toJsonStr(allocationDtos));

        //退回重付
        Map<String, Integer> orderGoodsTempRefundListMap = getOrderGoodsTempRefundMap(orderGoodsRefundList);

        setAllocationTempRefund(allocationDtos, orderGoodsTempRefundListMap);

        // 计算正确的总待付金额
        BigDecimal totalToBePaid = calculateTotalToBePaid(allocationDtos);
        orderAmountDto.setToBePaid(totalToBePaid);

        log.info("计算总待付金额完成，orderId={}, 总待付金额={}", order.getOrderId(), totalToBePaid);

        allocate(orderAmountDto, allocationDtos);

        //过滤 allocation 为 0的情况
        List<OrderGoodsAllocationDto> allocationList = allocationDtos.stream().filter(al -> al.getAllocation() > 0).collect(Collectors.toList());

        List<IncomePaidAllocationEntity> allocationEntities = allocationConverter.dto2EntityList(allocationList);

        fillInEntityBaseInfo(allocationEntities, incomeRecordEntity);

        List<HeOrderGoodsEntity> orderGoodsEntities = orderGoodsDomainService.queryOrderGoodsWithExpand(order.getOrderId());

        fillInEntityProductInfo(allocationEntities, orderGoodsEntities);

        allocationRepository.saveOrUpdateBatch(allocationEntities);

        return null;
    }

    /**
     * 填充实体商品信息
     *
     * @param allocationEntities
     * @param orderGoodsEntities
     */
    private void fillInEntityProductInfo(List<IncomePaidAllocationEntity> allocationEntities, List<HeOrderGoodsEntity> orderGoodsEntities) {

        Map<Integer, HeOrderGoodsEntity> orderGoodsMap = orderGoodsEntities.stream().collect(Collectors.toMap(HeOrderGoodsEntity::getId, a -> a));

        allocationEntities.forEach(entity -> {
            HeOrderGoodsEntity orderGoodsEntity = orderGoodsMap.get(entity.getOrderGoodsId().intValue());
            entity.setOrderGoodsSn(orderGoodsEntity.getOrderGoodsSn());
            entity.setAssetType(orderGoodsEntity.getGoodsType() + "");
            entity.setBusinessType(orderGoodsEntity.getBusinessType());
            entity.setBackCategoryId(orderGoodsEntity.getBackCategoryId());
            entity.setSkuName(orderGoodsEntity.getGoodsName());
            entity.setSkuId(orderGoodsEntity.getSkuId());
            entity.setGoodsId(orderGoodsEntity.getGoodsId());
        });
    }

    /**
     * 填充分摊实体基础信息
     *
     * @param allocationEntities
     * @param income
     */
    private void fillInEntityBaseInfo(List<IncomePaidAllocationEntity> allocationEntities, HeIncomeRecordEntity income) {
        allocationEntities.forEach(entity -> {
            entity.setBasicId(income.getBasicUid());
            entity.setIncomeId(income.getId());
            entity.setOrderId(income.getOrderId());
            entity.setStoreId(income.getStoreId());
            entity.setPaymentMethod(income.getPayType() + "");
        });
    }

    /**
     * 计算总待付金额
     * 根据各个商品的待付金额求和得出订单总待付金额
     * 公式：总待付 = Σ(商品应付 - 商品已付 - 商品普通退款 + 商品退回重付)
     *
     * @param allocationDtos 商品分摊列表
     * @return 总待付金额
     */
    private BigDecimal calculateTotalToBePaid(List<OrderGoodsAllocationDto> allocationDtos) {
        BigDecimal totalToBePaid = BigDecimal.ZERO;

        for (OrderGoodsAllocationDto allocation : allocationDtos) {
            // A应付 = O应付 * (A原数量 * A原单价 / 总原价)
            BigDecimal payable = AmountChangeUtil.changeF2Y(allocation.getTotalAmount());
            //A待付 = A应付 - A已付 - A普通退款 + A退回重付
            BigDecimal toBePaid = payable.subtract(AmountChangeUtil.changeF2Y(allocation.getPaid()))
                    .subtract(AmountChangeUtil.changeF2Y(allocation.getNormalRefund().longValue()))
                    .add(AmountChangeUtil.changeF2Y(allocation.getTempRefund().longValue()));

            // 只统计待付金额大于0的商品
            if (toBePaid.compareTo(BigDecimal.ZERO) > 0) {
                totalToBePaid = totalToBePaid.add(toBePaid);
            }

            log.info("商品待付金额计算：orderGoodsSn={}, 应付={}, 已付={}, 普通退款={}, 退回重付={}, 待付={}",
                    allocation.getOrderGoodsSn(), payable,
                    AmountChangeUtil.changeF2Y(allocation.getPaid()),
                    AmountChangeUtil.changeF2Y(allocation.getNormalRefund().longValue()),
                    AmountChangeUtil.changeF2Y(allocation.getTempRefund().longValue()),
                    toBePaid);
        }

        log.info("总待付金额计算完成：{}", totalToBePaid);
        return totalToBePaid;
    }

    public void allocate(OrderAmountDto order, List<OrderGoodsAllocationDto> allocationDtos) {
        int index = 0;

        //按 goodsPriceOrigin 升序排序 allocationDtos （尾差分到单间最高的）
        allocationDtos.sort(Comparator.comparing(OrderGoodsAllocationDto::getTotalAmount));

        log.info("分摊前：{}", JSONUtil.toJsonStr(allocationDtos));
        log.info("分摊前order：{}", JSONUtil.toJsonStr(order));

        BigDecimal sumAllocationAmount = new BigDecimal(0);
        BigDecimal currentPay = order.getCurrentPay();

        for (OrderGoodsAllocationDto allocation : allocationDtos) {

            log.info("开始一轮分摊状态：{}", JSONUtil.toJsonStr(allocation));

            BigDecimal allocationAmount;
            // A应付 = O应付 * (A原数量 * A原单价 / 总原价)
            BigDecimal payable = AmountChangeUtil.changeF2Y(allocation.getTotalAmount());
            //A待付 = A应付 - A已付 - A普通退款 + A退回重付
            BigDecimal toBePaid = payable.subtract(AmountChangeUtil.changeF2Y(allocation.getPaid()))
                    .subtract(AmountChangeUtil.changeF2Y(allocation.getNormalRefund().longValue()))
                    .add(AmountChangeUtil.changeF2Y(allocation.getTempRefund().longValue()));
            log.info("应付：{} , 待付：{}", payable, toBePaid);

            if (toBePaid.compareTo(BigDecimal.ZERO) <= 0) {
                index++;
                continue;
            }

            if (index == allocationDtos.size() - 1) {
                // 对于最后一个商品：
                // 1. 先按比例计算应得金额
                BigDecimal calculatedAmount = currentPay.multiply(toBePaid)
                        .divide(order.getToBePaid(), 2, RoundingMode.HALF_UP);
                // 2. 计算剩余金额
                BigDecimal remainingAmount = currentPay.subtract(sumAllocationAmount);

                allocationAmount = remainingAmount;

                if (allocationAmount.compareTo(BigDecimal.ZERO) < 0) {
                    throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR.code() + "",
                            "分摊异常, 金额小于0，请检查金额是否有修改 支付记录id：" + order.getCurrentIncomeId());
                }
                log.info("最后一个商品 - 计算金额：{}，剩余金额：{}，最终分摊：{}",
                        calculatedAmount, remainingAmount, allocationAmount);
            } else {
                // A分摊实付 = P * (A待付 / O待付 )
                allocationAmount = currentPay.multiply(toBePaid)
                        .divide(order.getToBePaid(), 2, BigDecimal.ROUND_HALF_UP);
                log.info("分摊金额：{},应付：{} , 待付：{}", allocationAmount, payable, toBePaid);
                sumAllocationAmount = sumAllocationAmount.add(allocationAmount);
            }

            allocation.setIncomeId(order.getCurrentIncomeId());
            allocation.setAllocation(AmountChangeUtil.changeY2FFoInt(allocationAmount));
            index++;
        }
    }

    /**
     * 初始化分摊记录 及每个记录已付
     *
     * @param orderId
     * @param orderGoodsPaidMap
     * @param orderGoodsRefundNumMap
     * @param orderGoodsNormalRefundMap
     * @return
     */
    protected List<OrderGoodsAllocationDto> initAllocationAndPaid(Integer orderId, Map<String, Integer> orderGoodsPaidMap,
                                                                 Map<String, Integer> orderGoodsRefundNumMap,
                                                                 Map<String, Integer> orderGoodsNormalRefundMap) {
        List<OrderGoodsAllocationDto> allocationDtos = new ArrayList<>();
        List<HeOrderGoodsEntity> groupedGoodsEntityList = orderGoodsDomainService.queryOrderGoodsWithGroup(orderId);
        for (HeOrderGoodsEntity goodsEntity : groupedGoodsEntityList) {
            CombineTypeEnum combineTypeEnum = CombineTypeEnum.from(goodsEntity.getType());
            switch (combineTypeEnum) {
                case COMBINE:
                    goodsEntity.getSubList().forEach(sub -> {
                        OrderGoodsAllocationDto orderGoodsAllocationDto = initAllocationDto(sub, orderGoodsRefundNumMap);
                        orderGoodsAllocationDto = setOrderGoodsPaid(orderGoodsAllocationDto, orderGoodsPaidMap);
                        orderGoodsAllocationDto = setOrderGoodsNormalRefund(orderGoodsAllocationDto, orderGoodsNormalRefundMap);
                        // 只有可分摊数量大于0的商品才参与分摊
                        if (orderGoodsAllocationDto.getGoodsNum() > 0) {
                            allocationDtos.add(orderGoodsAllocationDto);
                        }
                    });
                    break;
                case SIMPLE:
                    OrderGoodsAllocationDto orderGoodsAllocationDto = initAllocationDto(goodsEntity, orderGoodsRefundNumMap);
                    orderGoodsAllocationDto = setOrderGoodsPaid(orderGoodsAllocationDto, orderGoodsPaidMap);
                    orderGoodsAllocationDto = setOrderGoodsNormalRefund(orderGoodsAllocationDto, orderGoodsNormalRefundMap);
                    // 只有可分摊数量大于0的商品才参与分摊
                    if (orderGoodsAllocationDto.getGoodsNum() > 0) {
                        allocationDtos.add(orderGoodsAllocationDto);
                    }
                    break;
                default:
                    throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code() + "", "订单商品类型异常");
            }
        }

        return allocationDtos;
    }

    /**
     * 设置分摊记录中的退回重付
     *
     * @param allocationDtos
     * @param orderGoodsTempRefundListMap
     * @return
     */
    protected void setAllocationTempRefund(List<OrderGoodsAllocationDto> allocationDtos, Map<String, Integer> orderGoodsTempRefundListMap) {
        allocationDtos.forEach(allocationDto -> {
            allocationDto.setTempRefund(orderGoodsTempRefundListMap.getOrDefault(allocationDto.getOrderGoodsSn(), 0));
        });
    }

    /**
     * 已付map
     *
     * @param orderId
     * @return
     */
    private Map<String, Integer> getOrderGoodsPaidMap(Integer orderId) {
        //已经分摊的金额
        List<IncomePaidAllocationEntity> incomePaidAllocationEntities = allocationRepository.queryListByOrderId(orderId.longValue());
        Map<String, List<IncomePaidAllocationEntity>> orderGoodsPaidListMap = incomePaidAllocationEntities.stream().collect(Collectors.groupingBy(IncomePaidAllocationEntity::getOrderGoodsSn));
        // 每个支付金额
        Map<String, Integer> orderGoodsPaidMap = orderGoodsPaidListMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue()
                        .stream()
                        .mapToInt(IncomePaidAllocationEntity::getPaidAmount)
                        .sum()));
        return orderGoodsPaidMap;
    }

    /**
     * 退回重支付 map
     *
     * @param orderGoodsTempRefundList
     * @return
     */
    private Map<String, Integer> getOrderGoodsTempRefundMap(List<HeOrderRefundGoodsEntity> orderGoodsTempRefundList) {
        Map<String, List<HeOrderRefundGoodsEntity>> orderGoodsRefundListMap = orderGoodsTempRefundList.stream().collect(Collectors.groupingBy(HeOrderRefundGoodsEntity::getOrderGoodsSn));
        // 每个商品支退款金额
        Map<String, Integer> orderGoodsRefudMap = new HashMap<>();
        // orderGoodsRefundListMap 遍历 计算退款金额
        orderGoodsRefundListMap.forEach((orderGoodsSn, refundList) -> {
            int refundAmount = refundList.stream().mapToInt(a -> a.getRefundAmount()).sum();
            orderGoodsRefudMap.put(orderGoodsSn, refundAmount);
        });
        return orderGoodsRefudMap;
    }

    /**
     * 普通退款金额 map
     *
     * @param normalRefunds
     * @return
     */
    private Map<String, Integer> getOrderGoodsNormalRefundMap(List<HeOrderRefundGoodsEntity> normalRefunds) {
        Map<String, List<HeOrderRefundGoodsEntity>> orderGoodsRefundListMap = normalRefunds.stream()
                .collect(Collectors.groupingBy(HeOrderRefundGoodsEntity::getOrderGoodsSn));

        Map<String, Integer> orderGoodsNormalRefundMap = new HashMap<>();
        orderGoodsRefundListMap.forEach((orderGoodsSn, refundList) -> {
            int refundAmount = refundList.stream().mapToInt(HeOrderRefundGoodsEntity::getRefundAmount).sum();
            orderGoodsNormalRefundMap.put(orderGoodsSn, refundAmount);
        });
        return orderGoodsNormalRefundMap;
    }

    /**
     * 计算每个商品的已退款数量
     *
     * @param allSuccessfulRefunds
     * @return
     */
    private Map<String, Integer> getOrderGoodsRefundNumMap(List<HeOrderRefundGoodsEntity> allSuccessfulRefunds) {
        Map<String, List<HeOrderRefundGoodsEntity>> orderGoodsRefundListMap = allSuccessfulRefunds.stream()
                .collect(Collectors.groupingBy(HeOrderRefundGoodsEntity::getOrderGoodsSn));

        Map<String, Integer> orderGoodsRefundNumMap = new HashMap<>();
        orderGoodsRefundListMap.forEach((orderGoodsSn, refundList) -> {
            int totalRefundNum = refundList.stream().mapToInt(HeOrderRefundGoodsEntity::getRefundNum).sum();
            orderGoodsRefundNumMap.put(orderGoodsSn, totalRefundNum);
        });

        return orderGoodsRefundNumMap;
    }

    private OrderGoodsAllocationDto setOrderGoodsPaid(OrderGoodsAllocationDto orderGoodsAllocationDto, Map<String, Integer> orderGoodsPaidMap) {
        orderGoodsAllocationDto.setPaid(0);
        if (orderGoodsPaidMap.containsKey(orderGoodsAllocationDto.getOrderGoodsSn())) {
            orderGoodsAllocationDto.setPaid(orderGoodsPaidMap.get(orderGoodsAllocationDto.getOrderGoodsSn()));
        }
        return orderGoodsAllocationDto;
    }

    private OrderGoodsAllocationDto setOrderGoodsNormalRefund(OrderGoodsAllocationDto orderGoodsAllocationDto, Map<String, Integer> orderGoodsNormalRefundMap) {
        orderGoodsAllocationDto.setNormalRefund(0);
        if (orderGoodsNormalRefundMap.containsKey(orderGoodsAllocationDto.getOrderGoodsSn())) {
            orderGoodsAllocationDto.setNormalRefund(orderGoodsNormalRefundMap.get(orderGoodsAllocationDto.getOrderGoodsSn()));
        }
        return orderGoodsAllocationDto;
    }

    private OrderGoodsAllocationDto initAllocationDto(HeOrderGoodsEntity goodsEntity, Map<String, Integer> orderGoodsRefundNumMap) {
        // 计算可分摊数量：原购买数量 - 已退款数量
        Integer originalNum = goodsEntity.getGoodsNum();
        Integer refundedNum = orderGoodsRefundNumMap.getOrDefault(goodsEntity.getOrderGoodsSn(), 0);
        Integer availableNum = originalNum - refundedNum;

        // 确保可分摊数量不为负数
        availableNum = Math.max(0, availableNum);

        // 按比例计算可分摊的总金额：(可分摊数量 / 原数量) * 原总金额
        int availableTotalAmount = 0;
        if (originalNum > 0) {
            // 使用long避免Integer溢出
            long totalPrice = (long) goodsEntity.getTotalAllocationOriginPrice() * availableNum;
            availableTotalAmount = (int) (totalPrice / originalNum);
        }

        OrderGoodsAllocationDto orderGoodsAllocationDto = new OrderGoodsAllocationDto()
                .setOrderGoodsId(goodsEntity.getId())
                .setOrderGoodsSn(goodsEntity.getOrderGoodsSn())
                .setGoodsPriceOrigin(goodsEntity.getAllocationOriginPrice())
                .setTotalAmount(availableTotalAmount)
                .setGoodsNum(availableNum);
        log.info("初始化分摊记录：{}", JSONUtil.toJsonStr(orderGoodsAllocationDto));
        return orderGoodsAllocationDto;
    }

    @Override
    public boolean condition(HeIncomeRecordEntity param) {
        log.info("分摊实付条件判断测试");
        return !OmniPayTypeEnum.PRODUCTION_COIN.getCode().equals(param.getPayType()) && !OmniPayTypeEnum.REDUCTION.getCode().equals(param.getPayType());
    }


    @Override
    public FuncSpiConfig config() {
        return null;
    }
}

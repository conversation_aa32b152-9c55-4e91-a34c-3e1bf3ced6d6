package com.stbella.order.server.manager;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.base.PageVO;
import com.stbella.customer.server.ecp.entity.HeUserBasicPO;
import com.stbella.customer.server.ecp.request.UserBasicQueryRequest;
import com.stbella.customer.server.ecp.service.HeUserBasicService;
import com.stbella.order.server.convert.BasicConvert;
import com.stbella.order.server.order.month.req.BasicQueryRequest;
import com.stbella.order.server.order.month.res.BasicSimpInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class BasicManager {

    @DubboReference
    private HeUserBasicService heUserBasicService;
    @Resource
    private BasicConvert basicConvert;

    /**
     * 获取客户信息
     *
     * @param request
     * @return
     */
    public PageVO<BasicSimpInfoVO> getByCustomerNameOrPhone(BasicQueryRequest request) {
        UserBasicQueryRequest req = new UserBasicQueryRequest();
        req.setKeyword(request.getKeyword());
        req.setPageSize(request.getPageSize());
        req.setPageNum(request.getPageNum());
        PageVO<HeUserBasicPO> heUserBasicPOPageVO = heUserBasicService.queryByCustomerNameOrPhone(req);
        PageVO<BasicSimpInfoVO> basicSimpInfoVOPageVO = basicConvert.pagePO2VO(heUserBasicPOPageVO);
        return basicSimpInfoVOPageVO;
    }

    /**
     * 获取客户信息
     *
     * @param phone
     * @return
     */
    public BasicSimpInfoVO getByCustomerPhone(String phone) {
        HeUserBasicPO heUserBasicPO = heUserBasicService.queryUserBasicInfoByPhone(phone);
        BasicSimpInfoVO basicSimpInfoVO = basicConvert.po2vo(heUserBasicPO);
        if (ObjectUtil.isNotEmpty(heUserBasicPO)) {
            basicSimpInfoVO.setNamePhone(heUserBasicPO.getName() + "-" + heUserBasicPO.getPhone());
        }
        return basicSimpInfoVO;
    }

    /**
     * 获取客户信息
     *
     * @param basicUidList
     * @return
     */
    public List<BasicSimpInfoVO> getByBasicUidList(List<Long> basicUidList) {
        List<HeUserBasicPO> heUserBasicPOList = heUserBasicService.queryUserBasicInfoByIdList(basicUidList);
        List<BasicSimpInfoVO> basicSimpInfoVOList = basicConvert.heUserBasicPOListToBasicSimpInfoVOList(heUserBasicPOList);
        return basicSimpInfoVOList;
    }


    public Date getProductionNewLeadTimeByBasicId(Long basicUId) {
        HeUserBasicPO userBasicPO = heUserBasicService.queryUserBasicInfoById(basicUId);
        return Optional.ofNullable(userBasicPO).map(HeUserBasicPO::getProductionNewleadTime).orElse(null);
    }
}

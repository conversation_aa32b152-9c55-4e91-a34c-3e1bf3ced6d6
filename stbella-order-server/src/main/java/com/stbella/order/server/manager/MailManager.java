package com.stbella.order.server.manager;

import com.stbella.mail.MailUtils;
import com.stbella.order.server.order.month.constant.BaseConstant;
import com.stbella.order.server.order.month.request.mail.SendMailToFinanceRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.MessageFormat;

/**
 * <AUTHOR>
 * 邮箱服务
 * @date 2023/4/24 14:05
 */
@Component
@Slf4j
@RefreshScope
public class MailManager {
    @Resource
    private MailUtils mailUtils;
    @Value("${mail.orderAmountChange.to}")
    private String to;
    @Value("${mail.orderAmountChange.subject}")
    private String subject;

    /***
     * 订单业绩生效s修改订单金额,给财务发送邮件
     */
    @Async
    public void sendMailToFinance(SendMailToFinanceRequest request) {
        //发送纯文本邮件
        String content = MessageFormat.format(BaseConstant.SEND_MAIL_TO_FINANCE, request.getClientName(), request.getPhone(), request.getStoreName(), request.getOldPayAmount(), request.getNewPayAmount(), request.getOrderSn());
        log.info("订单变更发送财务邮件通知,to={},subject={},content={}", to, subject, content);
        mailUtils.sendSimpleMail(to, subject, content);
    }
}

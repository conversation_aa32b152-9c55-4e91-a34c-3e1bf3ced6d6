package com.stbella.order.server.order.cts.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.core.utils.ExcelUtils;
import com.stbella.order.server.contract.service.UserESignService;
import com.stbella.order.server.order.cts.entity.OrderCtsApplyRefundPO;
import com.stbella.order.server.order.cts.entity.OrderCtsPO;
import com.stbella.order.server.order.cts.enums.DingApproveStatusEnum;
import com.stbella.order.server.order.cts.enums.OrderRefundAuditStatusEnum;
import com.stbella.order.server.order.cts.enums.OrderRefundStatusEnum;
import com.stbella.order.server.order.cts.excel.ApplyRefundRecordExcel;
import com.stbella.order.server.order.cts.mapper.OrderCtsApplyRefundMapper;
import com.stbella.order.server.order.cts.request.order.CustomerPayApplyRefundAdminSearchRequest;
import com.stbella.order.server.order.cts.response.order.AdminApplyRefundResponse;
import com.stbella.order.server.order.cts.response.order.AdminCustomerVO;
import com.stbella.order.server.order.cts.service.OrderCtsApplyRefundService;
import com.stbella.order.server.order.cts.service.OrderCtsService;
import com.stbella.order.server.order.cts.service.OrderFacade;
import com.stbella.order.server.order.month.enums.OrderTypeEnum;
import com.stbella.order.server.order.month.enums.CtsPayTypeEnum;
import com.stbella.store.server.cts.service.CtsSiteService;
import lombok.extern.slf4j.Slf4j;

import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 到家订单申请退款表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2022-03-24
 */
@Service
@Slf4j
public class OrderCtsApplyRefundServiceImpl extends ServiceImpl<OrderCtsApplyRefundMapper, OrderCtsApplyRefundPO> implements OrderCtsApplyRefundService {

    @DubboReference
    private CtsSiteService ctsSiteService;
    @Resource
    private OrderCtsService orderCtsService;
    @Resource
    private UserESignService userESignService;
    @Resource
    private OrderFacade orderFacade;
    //线下支付类型
    private final static List<Integer> offlinePayTypes = Arrays.asList(CtsPayTypeEnum.CASH.getCode(), CtsPayTypeEnum.OTHER.getCode(), CtsPayTypeEnum.REMITTANCE.getCode());


    private static final HashMap<Integer,List<Integer>> REFUND_TO_PAY_TYPE_MAPPER = new HashMap<Integer, List<Integer>>(){{
        put(1, Collections.singletonList(CtsPayTypeEnum.ZFB.getCode()));
        put(2, Collections.singletonList(CtsPayTypeEnum.WX.getCode()));
        put(3,Arrays.asList(CtsPayTypeEnum.CASH.getCode(), CtsPayTypeEnum.OTHER.getCode(), CtsPayTypeEnum.REMITTANCE.getCode()));
    }};

    Function<AdminApplyRefundResponse, ApplyRefundRecordExcel> applyRefundRecordExcelFunction = item->{
        ApplyRefundRecordExcel applyRefundRecordExcel = new ApplyRefundRecordExcel();
        applyRefundRecordExcel.setLocalTransactionalNo(item.getLocalTransactionalNo());
        applyRefundRecordExcel.setRefundAmount(item.getRefundAmount());
        applyRefundRecordExcel.setPayTypeName(CtsPayTypeEnum.getValueByCode(item.getPayType()));
        applyRefundRecordExcel.setRefundOption(item.getRefundOption());
        applyRefundRecordExcel.setRefundRemark(item.getRefundRemark());
        String statusName = "";
        switch (item.getRefundStatus()){
            case 1:
                statusName = "支付成功";
                break;
            case 3:
                statusName = "支付中";
                break;
            case 4:
                statusName = "支付失败";
                break;
            default:
                break;
        }
        applyRefundRecordExcel.setRefundStatusName(statusName);
        applyRefundRecordExcel.setDingApproveStatus(OrderRefundAuditStatusEnum.getValueByCode(item.getAuditStatus()));
        applyRefundRecordExcel.setCreateByName(item.getCreateByName());
        if (item.getGmtCreate() != null) {
            applyRefundRecordExcel.setGmtCreate(DateUtil.format(item.getGmtCreate(), DatePattern.NORM_DATETIME_PATTERN));
        }
        applyRefundRecordExcel.setAuditName(item.getAuditName());
        if (item.getAuditTime() != null) {
            applyRefundRecordExcel.setAuditTime(DateUtil.format(item.getAuditTime(), DatePattern.NORM_DATETIME_PATTERN));
        }
        applyRefundRecordExcel.setOrderNo(item.getOrderNo());
        applyRefundRecordExcel.setOrderTypeName(OrderTypeEnum.getValueByCode(item.getOrderType()));
        applyRefundRecordExcel.setCustomerName(item.getCustomerName());
        applyRefundRecordExcel.setCustomerPhoneNum(item.getMobile());
        applyRefundRecordExcel.setSignSiteName(item.getCtsSiteName());
        return applyRefundRecordExcel;
    };



    @SneakyThrows
    @Override
    public void exportExcel(CustomerPayApplyRefundAdminSearchRequest query, HttpServletResponse httpServletResponse){
        query.setPageSize(Integer.MAX_VALUE);
        Page<AdminApplyRefundResponse> adminApplyRefundResponsePage = this.pageAdminApplyRefundList(query);
        List<AdminApplyRefundResponse> records = adminApplyRefundResponsePage.getRecords();
        List<ApplyRefundRecordExcel> collect = records.stream().map(applyRefundRecordExcelFunction).collect(Collectors.toList());
        try {
            ExcelUtils.exportExcel(httpServletResponse,"退款订单",collect,ApplyRefundRecordExcel.class);
        } catch (IOException e) {
            throw new BusinessException(ResultEnum.FILE_ERROR.getCode(), "退款订单列表导出异常");
        }
    }

    @Override
    public Page<AdminApplyRefundResponse> pageAdminApplyRefundList(CustomerPayApplyRefundAdminSearchRequest query) {
        //订单编号搜索
        if (StringUtils.isNotBlank(query.getOrderNo())) {
            OrderCtsPO orderCtsPO = orderCtsService.getByOrderNo(query.getOrderNo());
            if (orderCtsPO == null) {
                return new Page<AdminApplyRefundResponse>();
            }
        }
        //搜索条件：客户姓名或者手机号
        Set<String> customerNos = new HashSet<>();
        if (StringUtils.isNotBlank(query.getCustomerName()) || StringUtils.isNotBlank(query.getMobile())){
            customerNos = orderFacade.getOrderNosByCustomer(query.getCustomerName(), query.getMobile());
            if (CollectionUtil.isEmpty(customerNos)){
                return new Page<AdminApplyRefundResponse>();
            }
        }
        //搜索条件：分站
        Set<String> siteNos = new HashSet<>();
        if (CollectionUtil.isNotEmpty(query.getSiteList())){
            siteNos = orderFacade.getOrderNosBySiteIds(query.getSiteList());
            if (CollectionUtil.isEmpty(siteNos)){
                return new Page<AdminApplyRefundResponse>();
            }
        }
        //处理审核状态搜索
        if (ObjectUtil.isNotNull(query.getAuditStatus())){
            query.setRefundStatus(query.getAuditStatus());
        }
        if(query.getRefundType()!=null){
            List<Integer> refundType = query.getRefundType();
            ArrayList<Integer> res = new ArrayList<>();
            for (Integer integer : refundType) {
                res.addAll(REFUND_TO_PAY_TYPE_MAPPER.get(integer));
            }
            query.setRefundType(res);
        }

        LambdaQueryWrapper<OrderCtsApplyRefundPO> wrapper = new LambdaQueryWrapper<OrderCtsApplyRefundPO>()
                .in(query.getRefundType()!=null&&!query.getRefundType().isEmpty(),OrderCtsApplyRefundPO::getPayType,query.getRefundType())
                .eq(query.getOrderNo() != null, OrderCtsApplyRefundPO::getOrderNo, query.getOrderNo())
                .eq(query.getRefundStatus() != null, OrderCtsApplyRefundPO::getRefundStatus, query.getRefundStatus())
                .eq(query.getLocalTransactionalNo() != null, OrderCtsApplyRefundPO::getLocalTransactionalNo, query.getLocalTransactionalNo())
                .eq(query.getOrderType() != null, OrderCtsApplyRefundPO::getOrderType, query.getOrderType())
                .like(StringUtils.isNotBlank(query.getRefundRemark()),OrderCtsApplyRefundPO::getRefundRemark,query.getRefundRemark())
                .like(StringUtils.isNotBlank(query.getRefundOption()),OrderCtsApplyRefundPO::getRefundOption,query.getRefundOption())
                .like(StringUtils.isNotBlank(query.getAuditName()),OrderCtsApplyRefundPO::getAuditName,query.getAuditName())
                .between(query.getAuditStartTime()!=null&&query.getAuditEndTime()!=null,OrderCtsApplyRefundPO::getAuditTime,query.getAuditStartTime(),query.getAuditEndTime())
                .between(ObjectUtil.isNotNull(query.getBegin()), OrderCtsApplyRefundPO::getGmtCreate, query.getBegin(), query.getEnd())
                .in((StringUtils.isNotBlank(query.getCustomerName()) || StringUtils.isNotBlank(query.getMobile())), OrderCtsApplyRefundPO::getOrderNo, customerNos.toArray())
                .in((CollectionUtil.isNotEmpty(query.getSiteList())), OrderCtsApplyRefundPO::getOrderNo, siteNos.toArray())
                .like(StringUtils.isNotBlank(query.getCreateByName()), OrderCtsApplyRefundPO::getCreateByName, query.getCreateByName())
                .orderByDesc(OrderCtsApplyRefundPO::getGmtCreate);

        //查询申请记录表
        Page<OrderCtsApplyRefundPO> page = this.baseMapper.selectPage(new Page(query.getPageNum(), query.getPageSize()), wrapper);
        //获取订单编号
        if (CollectionUtil.isEmpty(page.getRecords())){
            return new Page<AdminApplyRefundResponse>();
        }

        //查询不同类型的订单客户和分站信息
        List<AdminApplyRefundResponse> dataList = new ArrayList<>();
        List<String> orderIds = page.getRecords().stream().map(OrderCtsApplyRefundPO::getOrderNo).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(orderIds)){
            return new Page<AdminApplyRefundResponse>();
        }
        List<OrderCtsPO> orderCtsPOS = orderCtsService.list(new LambdaQueryWrapper<OrderCtsPO>().in(OrderCtsPO::getOrderNo, orderIds));
        //查询不同订单类型对应的客户信息
        Map<String, AdminCustomerVO> sitterByOrderIds = orderFacade.sitterByOrderIds(orderCtsPOS);
        HashMap<String, AdminCustomerVO> customerByOrders = orderFacade.customerByOrders(orderCtsPOS);
        sitterByOrderIds.putAll(customerByOrders);
        HashMap<String, String> orderToSiteName = orderFacade.ctsSiteByOrders(orderCtsPOS);
        for (OrderCtsApplyRefundPO item : page.getRecords()) {
            AdminApplyRefundResponse response = new AdminApplyRefundResponse();
            BeanUtils.copyProperties(item,response);
            if (item.getRefundStatus() == OrderRefundStatusEnum.WAIT_AUDIT.getCode()){
                response.setAuditStatus(OrderRefundAuditStatusEnum.WAIT_AUDIT.getCode());
                //线下支付申请，审核状态和钉钉审批状态结合判断是否审核成功
                if (offlinePayTypes.contains(item.getPayType())){
                    if (ObjectUtil.equals(DingApproveStatusEnum.DING_SUCCESS.getCode(),item.getDingApproveStatus())){
                        response.setAuditStatus(OrderRefundAuditStatusEnum.SUCCESS_AUDIT.getCode());
                    }
                }
            }else if (item.getRefundStatus() == OrderRefundStatusEnum.SUCCESS_AUDIT.getCode()){
                response.setAuditStatus(OrderRefundAuditStatusEnum.SUCCESS_AUDIT.getCode());
            }else if (item.getRefundStatus() == OrderRefundStatusEnum.FAILED_AUDIT.getCode()){
                response.setAuditStatus(OrderRefundAuditStatusEnum.FAILED_AUDIT.getCode());
            }else {
                response.setAuditStatus(OrderRefundAuditStatusEnum.SUCCESS_AUDIT.getCode());
            }

            if(orderToSiteName.containsKey(item.getOrderNo())){
                response.setCtsSiteName(orderToSiteName.get(item.getOrderNo()));
            }
            AdminCustomerVO adminCustomerVO = sitterByOrderIds.get(item.getOrderNo());
            if(adminCustomerVO!=null){
                response.setCustomerName(adminCustomerVO.getName());
                response.setMobile(adminCustomerVO.getMobile());
            }
            dataList.add(response);
        }
        Page<AdminApplyRefundResponse> retPage = new Page<AdminApplyRefundResponse>();
        retPage.setSize(page.getSize());
        retPage.setCurrent(page.getCurrent());
        retPage.setTotal(page.getTotal());
        retPage.setRecords(dataList);
        return retPage;
    }
}

package com.stbella.order.server.order.month.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Strings;
import com.stbella.base.server.security.ISysRoleService;
import com.stbella.core.base.PageVO;
import com.stbella.core.enums.BusinessEnum;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.customer.server.ecp.entity.HeUserCardPO;
import com.stbella.customer.server.ecp.service.HeInviteRelationService;
import com.stbella.customer.server.ecp.vo.InviteInfoVO;
import com.stbella.notice.entity.OaProcessIdRelationPO;
import com.stbella.notice.server.OaProcessIdRelationService;
import com.stbella.order.common.enums.core.IncomeRecordPayStatusEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.core.OrderRefundStatusEnum;
import com.stbella.order.common.enums.month.*;
import com.stbella.order.common.enums.production.OrderGiftExtendTypeEnum;
import com.stbella.order.common.enums.production.OrderProductionVerificationStateEnum;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.production.GoodsEntity;
import com.stbella.order.domain.order.production.OrderGiftExtendEntity;
import com.stbella.order.domain.repository.*;
import com.stbella.order.infrastructure.repository.converter.IncomeRecordConverter;
import com.stbella.order.infrastructure.repository.converter.OrderRefundConverter;
import com.stbella.order.server.context.component.DiscountCalculator;
import com.stbella.order.server.context.component.StoreCurrencyContainer;
import com.stbella.order.server.context.component.processor.MultipleHolidaysProcessor;
import com.stbella.order.server.convert.OrderConvert;
import com.stbella.order.server.manager.GoodsManager;
import com.stbella.order.server.manager.UserCardManager;
import com.stbella.order.server.order.DiscountFact;
import com.stbella.order.server.order.OrderIndex;
import com.stbella.order.server.order.OrderIndexFact;
import com.stbella.order.server.order.StoreGoodsSkuModel;
import com.stbella.order.server.order.month.component.OrderIncomeAssembler;
import com.stbella.order.server.order.month.enums.ApprovalDiscountStatusEnum;
import com.stbella.order.server.order.month.enums.BirthTypeEnum;
import com.stbella.order.server.order.month.enums.PayAmountEnum;
import com.stbella.order.server.order.month.req.*;
import com.stbella.order.server.order.month.request.standard.OrderSettlementQuery;
import com.stbella.order.server.order.month.res.*;
import com.stbella.order.server.order.month.service.MonthOrderWxCommandService;
import com.stbella.order.server.order.month.service.MonthOrderWxQueryService;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.order.server.utils.BigDecimalUtil;
import com.stbella.order.server.utils.JsonUtil;
import com.stbella.order.server.utils.ListUtils;
import com.stbella.order.server.utils.SensitiveInformationUtil;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import com.stbella.store.goodz.api.GoodsQueryService;
import com.stbella.store.goodz.req.QueryGoodsPropertyReq;
import com.stbella.store.goodz.res.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.core.context.FlowContext;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 月子餐订单服务 查询服务实现类
 */
@Service
@DubboService
@Slf4j
public class MonthOrderWxQueryServiceImpl implements MonthOrderWxQueryService {

    @Resource
    private GoodsRepository goodsRepository;
    @Resource
    private GoodsManager goodsManager;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderGoodsRepository orderGoodsRepository;
    @Resource
    private StoreRepository storeRepository;
    @Resource
    private MonthOrderWxQueryService monthOrderWxQueryService;
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @Resource
    private MonthContractSignRecordRepository monthContractSignRecordRepository;
    @Resource
    private MonthContractSignAgreementRepository monthContractSignAgreementRepository;
    @Resource
    private ContractSignRecordPaperRepository contractSignRecordPaperRepository;
    @Resource
    private TagsRepository tagsRepository;
    @Resource
    private ClientRepository clientRepository;
    @Resource
    private OrderGiftExtendRepository orderGiftExtendRepository;
    @Resource
    private OrderAdditionalRevenueRepository orderAdditionalRevenueRepository;
    @Resource
    private OrderVoucherRepository orderVoucherRepository;
    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;
    @Resource
    private OrderBailorSnapshotRepository orderBailorSnapshotRepository;
    @Resource
    private OrderOtherRepository orderOtherRepository;

    @Resource
    private OrderProductionExtendRepository orderProductionExtendRepository;
    @Resource
    private MonthOrderWxCommandService monthOrderWxCommandService;
    @DubboReference
    private HeInviteRelationService heInviteRelationService;
    @Resource
    private OrderContinueLiveRecordRepository orderContinueLiveRecordRepository;
    @Resource
    private StayinRepository stayinRepository;
    @Resource
    private AheadOutRoomRepository aheadOutRoomRepository;

    @Resource
    private OrderIncomeAssembler orderIncomeAssembler;
    @DubboReference
    private OaProcessIdRelationService oaProcessIdRelationService;
    @Resource
    private OrderConvert orderConvert;
    @Resource
    private OrderRoomTypeChangeRecordRepository orderRoomTypeChangeRecordRepository;
    @Resource
    private UserCardManager userCardManager;
    @Resource
    private DiscountCalculator discountCalculator;
    @DubboReference
    private GoodsQueryService goodsQueryService;

    @Resource
    private MultipleHolidaysProcessor multipleHolidaysProcessor;

    @Resource
    private IncomeRecordConverter incomeRecordConverter;

    @Resource
    private OrderRefundConverter orderRefundConverter;
    @DubboReference
    ISysRoleService sysRoleService;
    @Resource
    RuleLinkClient ruleLinkClient;


    private static List<Integer> commonOrderTypeList = Arrays.asList(OmniOrderTypeEnum.MONTH_ORDER.code(), OmniOrderTypeEnum.SMALL_MONTH_ORDER.code(), OmniOrderTypeEnum.NURSE_OUTSIDE_ORDER.code(), OmniOrderTypeEnum.SBRA_ORDER.code());

    @Override
    public Result<OrderMonthGoodsCacheVO> getOrderGoodsCache(OrderGoodsCacheReq req) {
        OrderMonthGoodsCacheVO result = new OrderMonthGoodsCacheVO();

        OrderGoodsCacheReq orderGoodsCache;
        if (req.isFromCache()) {
            orderGoodsCache = orderRepository.getOrderGoodsCache(req);
        } else {
            orderGoodsCache = req;
        }
        if (ObjectUtil.isEmpty(orderGoodsCache)) {
            return Result.success(new OrderMonthGoodsCacheVO());
        }

        //获取套餐的详情
        Integer goodsId = orderGoodsCache.getGoodsId();
        Integer skuId = orderGoodsCache.getSkuId();

        //获取套餐的数据
        RoomByGoodsIdVo oldRoomInfoById = goodsManager.getRoomInfoByGoodsId(goodsId);
        GoodsAttachmentVo goodsAttachmentVo = goodsManager.goodsAttachmentVo(goodsId);

        log.info("goodsId:" + goodsId);
        log.info("skuId:" + skuId);
        PackageInformationVo simpleGoodsInfoByGoodsIdAndSkuId = goodsManager.getSimpleGoodsInfoByGoodsIdAndSkuId(goodsId, skuId);
        log.info("simpleGoodsInfoByGoodsIdAndSkuId:" + simpleGoodsInfoByGoodsIdAndSkuId);
        if (ObjectUtil.isEmpty(simpleGoodsInfoByGoodsIdAndSkuId)) {
            return Result.success(new OrderMonthGoodsCacheVO());
        }
        GoodsEntity goodsEntity = goodsRepository.selectById(goodsId);
        result.setEcpRoomType(goodsEntity.getEcpRoomType());
        result.setSkuId(skuId);
        result.setGoodsId(simpleGoodsInfoByGoodsIdAndSkuId.getGoodsId());
        result.setGoodsName(simpleGoodsInfoByGoodsIdAndSkuId.getParentName() + simpleGoodsInfoByGoodsIdAndSkuId.getTemplateSkuProp());

        if (StringUtils.isEmpty(result.getGoodsName())) {
            List<SpuVo> goodsListWithIds = goodsManager.getGoodsListWithIds(Arrays.asList(simpleGoodsInfoByGoodsIdAndSkuId.getParentId()));
            log.info("goodsListWithIds:" + goodsListWithIds);
            if (CollectionUtils.isNotEmpty(goodsListWithIds)) {
                result.setGoodsName(goodsListWithIds.get(0).getGoodsName() + simpleGoodsInfoByGoodsIdAndSkuId.getServiceDays() + "天");
            } else {
                result.setGoodsName(simpleGoodsInfoByGoodsIdAndSkuId.getSkuName());
            }
        }

        result.setGoodsReceivableAmount(ObjectUtil.isEmpty(orderGoodsCache.getGoodsReceivableAmount()) ? null : BigDecimalUtil.formatBigDecimalToBigDecimal(orderGoodsCache.getGoodsReceivableAmount()));
        result.setGoodsPriceOrgin(simpleGoodsInfoByGoodsIdAndSkuId.getGoodsPriceOrgin());
        result.setGoodsPriceCost(simpleGoodsInfoByGoodsIdAndSkuId.getGoodsPriceCost());
        result.setServiceDays(simpleGoodsInfoByGoodsIdAndSkuId.getServiceDays());

        if (ObjectUtil.isNotEmpty(oldRoomInfoById)) {
            result.setRoomId(oldRoomInfoById.getId());
            result.setRoomName(oldRoomInfoById.getRoomName());
            result.setRoomPrice(new BigDecimal(oldRoomInfoById.getRoomPrice() / 100));
        }

        result.setParentId(orderGoodsCache.getParentId());

        if (ObjectUtil.isNotEmpty(goodsAttachmentVo)) {
            result.setAttachmentName(goodsAttachmentVo.getName());
            result.setAttachmentUrl(goodsAttachmentVo.getUrl());
        }
        try {
            QueryGoodsPropertyReq queryGoodsPropertyReq = new QueryGoodsPropertyReq();
            queryGoodsPropertyReq.setSkuId(skuId);
            queryGoodsPropertyReq.setIsStore(1);
            Result<List<GoodsPropertyVO>> listResult = goodsQueryService.queryGoodsProperty(queryGoodsPropertyReq);
            log.info("listResult:{}", listResult);
            result.setDefinedProperty(JsonUtil.write(listResult.getData()));
        } catch (Exception e) {
            log.error("创建/修改商品的时候获取商品属性失败,{}", e.getMessage());
        }

        return Result.success(result);
    }


    @Override
    public Result<List<OrderAdditionalRevenueCacheVO>> getOrderMonthAdditionalRevenueCache(OrderMonthAdditionalRevenueCacheReq req, Boolean refuse) {

        List<OrderAdditionalRevenueCacheVO> result = new ArrayList<>();

        List<OrderMonthAdditionalRevenueCacheReq> orderMonthAdditionalRevenueCache = orderRepository.getOrderMonthAdditionalRevenueCache(req);

        if (CollectionUtils.isEmpty(orderMonthAdditionalRevenueCache)) {
            orderMonthAdditionalRevenueCache = new ArrayList<>();
            return Result.success(result);
        }

//        List<OrderMonthAdditionalRevenueCacheReq> costMultipleBirths = orderMonthAdditionalRevenueCache.stream().filter(o -> MonthAdditionalRevenueEnum.COST_MULTIPLE_BIRTHS.getCode().equals(o.getType())).collect(Collectors.toList());
        List<OrderMonthAdditionalRevenueCacheReq> stayCost = orderMonthAdditionalRevenueCache.stream().filter(o -> MonthAdditionalRevenueEnum.STAY_COST.getCode().equals(o.getType())).collect(Collectors.toList());
//        List<OrderMonthAdditionalRevenueCacheReq> roomChanges = orderMonthAdditionalRevenueCache.stream().filter(o -> MonthAdditionalRevenueEnum.ROOM_CHANGES.getCode().equals(o.getType())).collect(Collectors.toList());
//        List<OrderMonthAdditionalRevenueCacheReq> holiday = orderMonthAdditionalRevenueCache.stream().filter(o -> MonthAdditionalRevenueEnum.HOLIDAY.getCode().equals(o.getType())).collect(Collectors.toList());

        //编辑订单的时候才会排序
        /*if (ObjectUtil.isNotEmpty(req.getOrderId())) {
            //排序：节假日-多胞胎-续住-房型变更
            orderMonthAdditionalRevenueCache = new ArrayList<>();
            orderMonthAdditionalRevenueCache.addAll(holiday);
            orderMonthAdditionalRevenueCache.addAll(costMultipleBirths);
            orderMonthAdditionalRevenueCache.addAll(stayCost);
            orderMonthAdditionalRevenueCache.addAll(roomChanges);
        }
*/
        if (CollectionUtils.isNotEmpty(orderMonthAdditionalRevenueCache)) {

            //取套餐缓存的数据
            OrderGoodsCacheReq orderGoodsCacheReq = new OrderGoodsCacheReq();
            orderGoodsCacheReq.setOperator(req.getOperator());
            orderGoodsCacheReq.setClientUid(req.getClientUid());
            orderGoodsCacheReq.setStoreId(req.getStoreId());
            orderGoodsCacheReq.setOrderType(req.getOrderType());
            orderGoodsCacheReq.setOrderId(req.getOrderId());
            OrderMonthGoodsCacheVO data = getOrderGoodsCache(orderGoodsCacheReq).getData();
            if (ObjectUtil.isEmpty(data)) {
                throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "未选择套餐");
            }
            data.setGoodsReceivableAmount(BigDecimalUtil.check(data.getGoodsReceivableAmount()));

            //region 获取多胞胎节假日费用
            FlowContext flowContext = new FlowContext();
            MultipleHolidaysReq multipleHolidaysReq = new MultipleHolidaysReq();
            multipleHolidaysReq.setStoreId(req.getStoreId());
            flowContext.setResultAttribute(MultipleHolidaysReq.class, multipleHolidaysReq);

            SnowballFlowLauncher.fire(flowContext, multipleHolidaysProcessor);
            StoreFinanceVO multipleHolidayVO = flowContext.getResultAttribute(StoreFinanceVO.class);
            //endregion

            //获取门店类型 废弃
//            OrderMonthStoreTypeCostEnum orderMonthStoreTypeCostEnum = getStoreType(req.getStoreId());

            //获取套餐的具体房型
            Integer goodsId = req.getGoodsId();

            if (ObjectUtil.isEmpty(goodsId)) {
                //无套餐
                return Result.success(result);
            }

            //原房型数据
            RoomByGoodsIdVo oldRoomInfoById = goodsManager.getRoomInfoByGoodsId(goodsId);

            //获取套餐的数据
            StorePackageInfo storePackageInfo = goodsManager.getStorePackageInfo(goodsId);

            int serviceDays = storePackageInfo.getServiceDays();
            int allDays = serviceDays;

            //获取有没有续住天数
            if (CollectionUtils.isNotEmpty(stayCost)) {
                for (OrderMonthAdditionalRevenueCacheReq orderMonthAdditionalRevenueCacheReq : stayCost) {
                    if (orderMonthAdditionalRevenueCacheReq.getIntegrality() == 1) {
                        allDays = allDays + orderMonthAdditionalRevenueCacheReq.getDays();
                    }
                }
            }

            for (OrderMonthAdditionalRevenueCacheReq orderMonthAdditionalRevenueCacheReq : orderMonthAdditionalRevenueCache) {
                Integer type = orderMonthAdditionalRevenueCacheReq.getType();
                OrderAdditionalRevenueCacheVO orderAdditionalRevenueCacheVO = new OrderAdditionalRevenueCacheVO();
                orderAdditionalRevenueCacheVO.setGoodsId(orderGoodsCacheReq.getGoodsId());
                orderAdditionalRevenueCacheVO.setIntegrality(orderMonthAdditionalRevenueCacheReq.getIntegrality());
                orderAdditionalRevenueCacheVO.setId(orderMonthAdditionalRevenueCacheReq.getId());
                orderAdditionalRevenueCacheVO.setStoreId(req.getStoreId());
                orderAdditionalRevenueCacheVO.setType(type);
                orderAdditionalRevenueCacheVO.setPrice(orderMonthAdditionalRevenueCacheReq.getPrice());
                MonthAdditionalRevenueEnum enumByCode = MonthAdditionalRevenueEnum.getEnumByCode(type);
                orderAdditionalRevenueCacheVO.setGoodsName(enumByCode.getDesc());
                switch (enumByCode) {
                    case COST_MULTIPLE_BIRTHS:
                        Integer embryoNumber = orderMonthAdditionalRevenueCacheReq.getEmbryoNumber();
                        //多胞胎数量
                        orderAdditionalRevenueCacheVO.setEmbryoNumber(embryoNumber);
                        orderAdditionalRevenueCacheVO.setCost(BigDecimal.ZERO);
                        orderAdditionalRevenueCacheVO.setCostPrice(BigDecimal.ZERO);
                        if (ObjectUtil.isNotEmpty(embryoNumber)) {
                            //多胞胎原价（多胞胎数量-1）* 2000
//                            orderAdditionalRevenueCacheVO.setCost(BigDecimalUtil.multiplication(new BigDecimal(embryoNumber - 1), orderMonthStoreTypeCostEnum.getMultipleBirthsOriginal()).multiply(new BigDecimal(allDays)));
                            orderAdditionalRevenueCacheVO.setCost(BigDecimalUtil.multiplication(new BigDecimal(embryoNumber - 1), multipleHolidayVO.getMultipleBirthsOriginal()).multiply(new BigDecimal(allDays)));
                            //多胞胎成本：圣贝拉：12200/28=435元 每胎每天；小贝拉：14000/28=500元 每胎每天；Deluxe：11300/28=403元 每胎每天
//                            orderAdditionalRevenueCacheVO.setCostPrice(BigDecimalUtil.multiplication(new BigDecimal(embryoNumber - 1), orderMonthStoreTypeCostEnum.getMultipleBirths()).multiply(new BigDecimal(allDays)));
                            orderAdditionalRevenueCacheVO.setCostPrice(BigDecimalUtil.multiplication(new BigDecimal(embryoNumber - 1), multipleHolidayVO.getMultipleBirths()).multiply(new BigDecimal(allDays)));
                        }
                        break;
                    case STAY_COST:
                        //续住天数
                        Integer days = orderMonthAdditionalRevenueCacheReq.getDays();
                        orderAdditionalRevenueCacheVO.setDays(days);
                        Integer roomId = orderMonthAdditionalRevenueCacheReq.getRoomId();
                        if (ObjectUtil.isNotEmpty(roomId)) {
                            //续住房型
                            RoomByGoodsIdVo newRoomInfoById = goodsManager.getRoomInfoById(orderMonthAdditionalRevenueCacheReq.getRoomId());
                            //房间ID名字
                            orderAdditionalRevenueCacheVO.setRoomId(newRoomInfoById.getId());
                            orderAdditionalRevenueCacheVO.setRoomName(newRoomInfoById.getRoomName());
                            //续住原价 =（套餐应付金额/套餐天数+单天续住房型与原套餐房型差价）* 续住时长天数
                            if (ObjectUtil.isNotEmpty(days)) {
                                BigDecimal cost = (BigDecimalUtil.divide(data.getGoodsReceivableAmount(), new BigDecimal(serviceDays)).add(new BigDecimal(newRoomInfoById.getRoomPrice() / 100 - oldRoomInfoById.getRoomPrice() / 100))).multiply(new BigDecimal(days));
                                orderAdditionalRevenueCacheVO.setCost(cost);
                                //原价小于0，应付 = 原价 且不能修改
                                if (cost.compareTo(BigDecimal.ZERO) < 0) {
                                    orderAdditionalRevenueCacheVO.setPrice(cost);
                                }
                                //续住成本 =（套餐成本/套餐天数+单天续住房型成本（房型成本不是续住成本）差价）*续住时长天数
                                BigDecimal costPrice = (BigDecimalUtil.divide(new BigDecimal(storePackageInfo.getPackageCost()), new BigDecimal(serviceDays)).add(new BigDecimal(newRoomInfoById.getRoomCost() / 100 - oldRoomInfoById.getRoomCost() / 100))).multiply(new BigDecimal(days));
                                orderAdditionalRevenueCacheVO.setCostPrice(costPrice);
                            }
                        }
                        //具体时间
                        orderAdditionalRevenueCacheVO.setDaysList(orderMonthAdditionalRevenueCacheReq.getDaysList());


                        //更正状态
                        if (ObjectUtil.isAllNotEmpty(orderAdditionalRevenueCacheVO.getPrice(), orderMonthAdditionalRevenueCacheReq.getDays(), orderMonthAdditionalRevenueCacheReq.getRoomId())) {
                            orderAdditionalRevenueCacheVO.setIntegrality(1);
                        }

                        break;
                    case ROOM_CHANGES:
                        //续住天数
                        days = orderMonthAdditionalRevenueCacheReq.getDays();
                        orderAdditionalRevenueCacheVO.setDays(days);

                        Integer cacheReqRoomId = orderMonthAdditionalRevenueCacheReq.getRoomId();
                        //变更后的房型
                        RoomByGoodsIdVo roomByGoodsIdVo = goodsManager.getRoomInfoById(cacheReqRoomId);

                        if (ObjectUtil.isNotEmpty(roomByGoodsIdVo) && ObjectUtil.isNotEmpty(data.getGoodsId())) {
                            //变更后的房型ID名字
                            orderAdditionalRevenueCacheVO.setRoomId(roomByGoodsIdVo.getId());
                            orderAdditionalRevenueCacheVO.setRoomName(roomByGoodsIdVo.getRoomName());

                            //原房型每天费用
                            BigDecimal oldRoomPriceEveryDay = data.getGoodsPriceOrgin().divide(new BigDecimal(storePackageInfo.getServiceDays()), 2, RoundingMode.HALF_UP);
                            //原房型和现房型差价
                            BigDecimal subtract = new BigDecimal(roomByGoodsIdVo.getRoomPrice() / 100 - oldRoomInfoById.getRoomPrice() / 100);

                            if (ObjectUtil.isNotEmpty(days) && days > 0) {
                                //房型变更差价
                                BigDecimal changeCost = subtract.multiply(new BigDecimal(days));
                                if (changeCost.compareTo(BigDecimal.ZERO) < 0) {
                                    //房型变更的原价如果负的，应付 = 原价 且不能修改
                                    orderAdditionalRevenueCacheVO.setPrice(changeCost);
                                } else {
                                    //原先是负的，现在是正的，应收金额 = 原价
                                    if (ObjectUtil.isNotEmpty(orderAdditionalRevenueCacheVO.getPrice()) && orderAdditionalRevenueCacheVO.getPrice().compareTo(BigDecimal.ZERO) < 0) {
                                        orderAdditionalRevenueCacheVO.setPrice(changeCost);
                                    }
                                }
                                //变更后的房型原价
                                orderAdditionalRevenueCacheVO.setCost(changeCost);
                                //房型变更成本=房型成本差价*变更房型周期天数
                                orderAdditionalRevenueCacheVO.setCostPrice(BigDecimalUtil.multiplication(new BigDecimal(roomByGoodsIdVo.getRoomCost() / 100 - oldRoomInfoById.getRoomCost() / 100), new BigDecimal(days)));

                            }
                        }

                        //更正状态
                        if (ObjectUtil.isAllNotEmpty(orderAdditionalRevenueCacheVO.getPrice(), orderMonthAdditionalRevenueCacheReq.getDaysList(), orderMonthAdditionalRevenueCacheReq.getRoomId())) {
                            orderAdditionalRevenueCacheVO.setIntegrality(1);
                        }

                        //具体时间
                        orderAdditionalRevenueCacheVO.setDaysList(orderMonthAdditionalRevenueCacheReq.getDaysList());
                        break;
                    case HOLIDAY:
                        //节假日天数
                        days = orderMonthAdditionalRevenueCacheReq.getDays();
                        if (ObjectUtil.isNotEmpty(days)) {
                            //原价
                            //2024-01-17产品逻辑 成本取数据库门店设置的节假日费用
                            CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(req.getStoreId());
                            orderAdditionalRevenueCacheVO.setCost(BigDecimalUtil.multiplication(new BigDecimal(cfgStoreEntity.getHolidayPrice() / 100), new BigDecimal(days)));
                            //成本
                            //2024-01-17产品逻辑 成本取数据库门店设置的节假日成本
                            orderAdditionalRevenueCacheVO.setCostPrice(BigDecimalUtil.multiplication(new BigDecimal(cfgStoreEntity.getHolidayCost() / 100), new BigDecimal(days)));
                        }
                        orderAdditionalRevenueCacheVO.setIsFestival(orderMonthAdditionalRevenueCacheReq.getIsFestival());
                        orderAdditionalRevenueCacheVO.setDays(orderMonthAdditionalRevenueCacheReq.getDays());
                        break;
                    default:
                }
                result.add(orderAdditionalRevenueCacheVO);
            }
        }

        //强制刷新刷新缓存请求获取加收项的时候
        if (ObjectUtil.isNotEmpty(refuse) && refuse) {
            List<OrderMonthAdditionalRevenueCacheReq> orderMonthAdditionalRevenueCacheReqs = orderAdditionalRevenueRepository.voListToReq(result, req);
            for (OrderMonthAdditionalRevenueCacheReq orderMonthAdditionalRevenueCacheReq : orderMonthAdditionalRevenueCacheReqs) {
                orderMonthAdditionalRevenueCacheReq.setOrderId(req.getOrderId());
            }
            if (CollectionUtils.isNotEmpty(orderMonthAdditionalRevenueCacheReqs)) {
                monthOrderWxCommandService.orderMonthAdditionalRevenueCache(orderMonthAdditionalRevenueCacheReqs);
            }
        }

        return Result.success(result);
    }

    @Override
    public Result<OrderGiftByStoreIdVO> getOrderGiftByStoreId(OrderGiftByStoreReq req) {

        //判断是否有缓存
        Integer orderId = req.getOrderId();
        if (ObjectUtil.isNotEmpty(orderId)) {
            //判断订单是否有缓存
            RemoveAllOrderCacheReq removeAllOrderCacheReq = new RemoveAllOrderCacheReq();
            removeAllOrderCacheReq.setOrderId(orderId);
            Result<RemoveAllOrderCacheVO> removeAllOrderCacheVOResult = monthOrderWxCommandService.checkCacheByOrderId(removeAllOrderCacheReq);
            if (!removeAllOrderCacheVOResult.getData().getExist()) {
                //单独创建缓存
                monthOrderWxCommandService.createGiftExtendSkuCache(orderId);
            }
        }

        OrderGiftByStoreIdVO goodsGiftStorePageList = goodsManager.getGoodsGiftStorePageList(req);
        OrderDiscountsCacheReq orderDiscountsCacheReq = new OrderDiscountsCacheReq();
        orderDiscountsCacheReq.setGoodsId(req.getGoodsId());
        orderDiscountsCacheReq.setOrderType(req.getOrderType());
        orderDiscountsCacheReq.setClientUid(req.getClientUid());
        orderDiscountsCacheReq.setOperator(req.getOperator());
        orderDiscountsCacheReq.setStoreId(req.getStoreId());
        orderDiscountsCacheReq.setOrderId(orderId);
        Result<OrderDiscountsCacheVO> orderDiscounts = monthOrderWxQueryService.getOrderDiscounts(orderDiscountsCacheReq);
        OrderDiscountsCacheVO data = orderDiscounts.getData();
        goodsGiftStorePageList.setNetDiscountRate(data.getNetDiscountRate());

        BigDecimal taxRate = data.getTaxRate();
        if (ObjectUtil.isNull(taxRate)) {
            taxRate = new BigDecimal(0);
        }
        goodsGiftStorePageList.setTaxRate(taxRate);
        goodsGiftStorePageList.setNetDiscountRate(data.getNetDiscountRate());
        goodsGiftStorePageList.setGrossProfitMargin(data.getGrossProfitMargin());
        return Result.success(goodsGiftStorePageList);
    }

    /**
     * 下单的时候获取用户加收项
     *
     * @param req
     * @return
     */
    @Override
    public List<OrderAdditionalRevenueCacheVO> getOrderAdditionalRevenueCache(OrderAddtionalRevenueCacheReq req) {
        OrderMonthAdditionalRevenueCacheReq orderMonthAdditionalRevenueCacheReq = new OrderMonthAdditionalRevenueCacheReq();
        orderMonthAdditionalRevenueCacheReq.setOrderType(req.getOrderType());
        orderMonthAdditionalRevenueCacheReq.setClientUid(req.getClientUid());
        orderMonthAdditionalRevenueCacheReq.setStoreId(req.getStoreId());
        orderMonthAdditionalRevenueCacheReq.setOrderType(req.getOrderType());
        orderMonthAdditionalRevenueCacheReq.setOperator(req.getOperator());
        orderMonthAdditionalRevenueCacheReq.setGoodsId(req.getGoodsId());
        orderMonthAdditionalRevenueCacheReq.setOrderId(req.getOrderId());
        return this.getOrderMonthAdditionalRevenueCache(orderMonthAdditionalRevenueCacheReq, null).getData();
    }

    /**
     * @param req
     * @return
     */
    @Override
    public List<OrderGiftCacheByUserVO> getOrderGiftExtentCache(OrderAdvanceOrderGiftReq req) {

        List<OrderGiftCacheByUserVO> result = new ArrayList<>();

        OrderGiftByStoreReq orderGiftByStoreReq = new OrderGiftByStoreReq();
        orderGiftByStoreReq.setStoreId(req.getStoreId());
        orderGiftByStoreReq.setOrderType(req.getOrderType());
        orderGiftByStoreReq.setClientUid(req.getClientUid());
        orderGiftByStoreReq.setGoodsId(req.getGoodsId());
        orderGiftByStoreReq.setOperator(req.getOperator());
        orderGiftByStoreReq.setOrderId(req.getOrderId());

        String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(req.getStoreId());


        OrderGiftByStoreIdVO giftByStoreIdVO = goodsManager.getGoodsGiftStorePageList(orderGiftByStoreReq);

        if (giftByStoreIdVO.getProductionGoldQuantity() > 0) {
            OrderGiftCacheByUserVO orderGiftCacheByUserVO = new OrderGiftCacheByUserVO();
            //产康金
            orderGiftCacheByUserVO.setCategoryName("产康金");
            //类型
            orderGiftCacheByUserVO.setType(OrderGiftExtendTypeEnum.SANKANGJIN.code());
            //价格
            orderGiftCacheByUserVO.setPrice(new BigDecimal(giftByStoreIdVO.getProductionGoldQuantity()));
            orderGiftCacheByUserVO.setAllPrice(new BigDecimal(giftByStoreIdVO.getProductionGoldQuantity()));
            //产康金成本=产康金*0.2
            //成本=赠送产康金/税率*40% 2024-1-30逻辑
            OrderIndexFact fact = new OrderIndexFact();
            fact.setStoreId(req.getStoreId());
            fact.setEffectDate(System.currentTimeMillis() / 1000);
            BigDecimal taxRate = discountCalculator.getTaxRate(fact);
            BigDecimal allCostPrice = new BigDecimal(giftByStoreIdVO.getProductionGoldQuantity()).divide((new BigDecimal(1).add(taxRate)), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(0.4)).setScale(2, RoundingMode.HALF_UP);
            orderGiftCacheByUserVO.setCostPrice(allCostPrice);
            orderGiftCacheByUserVO.setAllCostPrice(allCostPrice);
            orderGiftCacheByUserVO.setStoreId(req.getStoreId());
            orderGiftCacheByUserVO.setGoodsNum(giftByStoreIdVO.getProductionGoldQuantity());
            orderGiftCacheByUserVO.setCurrency(storeCurrencyCode);
            result.add(orderGiftCacheByUserVO);
        }

        List<OrderGiftByStoreCategoryVO> goodsGiftStorePageList = giftByStoreIdVO.getOrderGiftByStoreCategoryVOList();
        for (OrderGiftByStoreCategoryVO orderGiftByStoreCategoryVO : goodsGiftStorePageList) {
            List<OrderGiftByStoreGoodsInfoVO> orderGiftByStoreGoodsInfoVOList = orderGiftByStoreCategoryVO.getOrderGiftByStoreGoodsInfoVOList();
            if (CollectionUtils.isNotEmpty(orderGiftByStoreGoodsInfoVOList)) {
                for (OrderGiftByStoreGoodsInfoVO orderGiftByStoreGoodsInfoVO : orderGiftByStoreGoodsInfoVOList) {
                    List<OrderGiftByStoreGoodsSkuInfoVO> orderGiftByStoreGoodsInfoSkuList = orderGiftByStoreGoodsInfoVO.getOrderGiftByStoreGoodsInfoSkuList();
                    if (CollectionUtils.isNotEmpty(orderGiftByStoreGoodsInfoSkuList)) {
                        List<OrderGiftByStoreGoodsSkuInfoVO> collect = orderGiftByStoreGoodsInfoSkuList.stream().filter(o -> o.getQuantity() > 0).collect(Collectors.toList());
                        for (OrderGiftByStoreGoodsSkuInfoVO orderGiftByStoreGoodsSkuInfoVO : collect) {
                            //有效的选择
                            OrderGiftCacheByUserVO orderGiftCacheByUserVO = new OrderGiftCacheByUserVO();
                            //分组ID
                            Integer categoryFront = orderGiftByStoreCategoryVO.getCategoryFront();
                            String categoryName = orderGiftByStoreCategoryVO.getCategoryName();
                            if (categoryFront == -1) {
                                //家属房
                                orderGiftCacheByUserVO.setType(OrderGiftExtendTypeEnum.FAMILY_ROOM.code());
                            } else {
                                //产康服务
                                orderGiftCacheByUserVO.setType(OrderGiftExtendTypeEnum.INDUSTRIAL_HEALTH_SERVICE.code());
                            }

                            orderGiftCacheByUserVO.setPrice(new BigDecimal(orderGiftByStoreGoodsSkuInfoVO.getGoodsPrice()));
                            orderGiftCacheByUserVO.setCostPrice(new BigDecimal(orderGiftByStoreGoodsSkuInfoVO.getCostPrice()));
                            orderGiftCacheByUserVO.setAllCostPrice(BigDecimalUtil.multiplication(new BigDecimal(orderGiftByStoreGoodsSkuInfoVO.getQuantity()), new BigDecimal(orderGiftByStoreGoodsSkuInfoVO.getCostPrice())));

                            orderGiftCacheByUserVO.setCategoryId(categoryFront);
                            orderGiftCacheByUserVO.setCategoryName(categoryName);
                            orderGiftCacheByUserVO.setGoodsId(orderGiftByStoreGoodsInfoVO.getGoodsId());
                            orderGiftCacheByUserVO.setGoodsName(orderGiftByStoreGoodsInfoVO.getGoodsName());
                            orderGiftCacheByUserVO.setSkuId(orderGiftByStoreGoodsSkuInfoVO.getSkuId());
                            orderGiftCacheByUserVO.setSkuName(orderGiftByStoreGoodsSkuInfoVO.getSkuName());
                            orderGiftCacheByUserVO.setStoreId(req.getStoreId());
                            orderGiftCacheByUserVO.setGoodsNum(orderGiftByStoreGoodsSkuInfoVO.getQuantity());
                            orderGiftCacheByUserVO.setAllPrice(BigDecimalUtil.multiplication(new BigDecimal(orderGiftByStoreGoodsSkuInfoVO.getQuantity()), new BigDecimal(orderGiftByStoreGoodsSkuInfoVO.getGoodsPrice())));
                            orderGiftCacheByUserVO.setCurrency(storeCurrencyCode);
                            result.add(orderGiftCacheByUserVO);
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * 下单前获取订单折扣
     *
     * @param req
     * @return
     */
    @Override
    public Result<OrderDiscountsCacheVO> getOrderDiscounts(OrderDiscountsCacheReq req) {
        HeOrderEntity orderInfo = null;

        String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(req.getStoreId());

        Integer orderId = req.getOrderId();
        if (ObjectUtil.isNotEmpty(orderId)) {
            orderInfo = orderRepository.getByOrderId(orderId);
            if (ObjectUtil.isEmpty(orderInfo)) {
                throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "订单不存在：" + orderId);
            }
            //判断订单是否有缓存
            RemoveAllOrderCacheReq removeAllOrderCacheReq = new RemoveAllOrderCacheReq();
            removeAllOrderCacheReq.setOrderId(orderId);
            Result<RemoveAllOrderCacheVO> removeAllOrderCacheVOResult = monthOrderWxCommandService.checkCacheByOrderId(removeAllOrderCacheReq);
            if (!removeAllOrderCacheVOResult.getData().getExist() && ObjectUtil.isNotEmpty(orderInfo.getDiscountDetails())) {
                //获取订单主表数据
                String discountDetails = orderInfo.getDiscountDetails();
                OrderDiscountsCacheVO read = JsonUtil.read(discountDetails, OrderDiscountsCacheVO.class);
                //什么都没改无需发起审批
                read.setApproval(false);
                return Result.success(read);
            }
        }
        //获取套餐缓存
        OrderGoodsCacheReq orderGoodsCacheReq = new OrderGoodsCacheReq();
        orderGoodsCacheReq.setClientUid(req.getClientUid());
        orderGoodsCacheReq.setStoreId(req.getStoreId());
        orderGoodsCacheReq.setOrderType(req.getOrderType());
        orderGoodsCacheReq.setOperator(req.getOperator());
        orderGoodsCacheReq.setOrderId(req.getOrderId());

        OrderMonthGoodsCacheVO orderMonthGoodsCacheVO = getOrderGoodsCache(orderGoodsCacheReq).getData();

        if (ObjectUtil.isEmpty(orderMonthGoodsCacheVO) || ObjectUtil.isEmpty(orderMonthGoodsCacheVO.getGoodsId())) {
            //没有套餐，不计算
            return Result.success(new OrderDiscountsCacheVO());
        }

        orderMonthGoodsCacheVO.setRoomPrice(BigDecimalUtil.check(orderMonthGoodsCacheVO.getRoomPrice()));
        orderMonthGoodsCacheVO.setGoodsReceivableAmount(BigDecimalUtil.check(orderMonthGoodsCacheVO.getGoodsReceivableAmount()));
        orderMonthGoodsCacheVO.setGoodsPriceOrgin(BigDecimalUtil.check(orderMonthGoodsCacheVO.getGoodsPriceOrgin()));
        orderMonthGoodsCacheVO.setGoodsPriceCost(BigDecimalUtil.check(orderMonthGoodsCacheVO.getGoodsPriceCost()));


        //获取套餐额外礼赠数据
        OrderAdvanceOrderGiftReq orderAdvanceOrderGiftReq = new OrderAdvanceOrderGiftReq();
        orderAdvanceOrderGiftReq.setClientUid(req.getClientUid());
        orderAdvanceOrderGiftReq.setStoreId(req.getStoreId());
        orderAdvanceOrderGiftReq.setOrderType(req.getOrderType());
        orderAdvanceOrderGiftReq.setOperator(req.getOperator());
        orderAdvanceOrderGiftReq.setGoodsId(req.getGoodsId());
        orderAdvanceOrderGiftReq.setOrderId(req.getOrderId());

        List<OrderGiftCacheByUserVO> orderGiftExtentCache = getOrderGiftExtentCache(orderAdvanceOrderGiftReq);
        //获取加收项明细
        OrderAddtionalRevenueCacheReq orderAddtionalRevenueCacheReq = new OrderAddtionalRevenueCacheReq();
        orderAddtionalRevenueCacheReq.setClientUid(req.getClientUid());
        orderAddtionalRevenueCacheReq.setStoreId(req.getStoreId());
        orderAddtionalRevenueCacheReq.setOrderType(req.getOrderType());
        orderAddtionalRevenueCacheReq.setOperator(req.getOperator());
        orderAddtionalRevenueCacheReq.setGoodsId(req.getGoodsId());
        orderAddtionalRevenueCacheReq.setOrderId(req.getOrderId());

        List<OrderAdditionalRevenueCacheVO> orderAdditionalRevenueCache = getOrderAdditionalRevenueCache(orderAddtionalRevenueCacheReq);
        //只有填写完整的时候参与计算
        orderAdditionalRevenueCache = orderAdditionalRevenueCache.stream().filter(o ->
                //填写完整的
                o.getIntegrality() == 1).collect(Collectors.toList());

        Optional<OrderAdditionalRevenueCacheVO> first = orderAdditionalRevenueCache.stream().filter(o -> o.getType() == 4).findFirst();
        if (first.isPresent()) {
            if (ObjectUtil.isEmpty(first.get().getIsFestival()) || first.get().getIsFestival() == 0) {
                orderAdditionalRevenueCache.remove(first.get());
            }
        }


        //套餐成本金额
        BigDecimal goodsCostPrice = orderMonthGoodsCacheVO.getGoodsPriceCost();
        goodsCostPrice = null == goodsCostPrice ? BigDecimal.ZERO : goodsCostPrice;
        //套餐原价金额
        BigDecimal goodsOriginalPrice = orderMonthGoodsCacheVO.getGoodsPriceOrgin();
        goodsOriginalPrice = null == goodsOriginalPrice ? BigDecimal.ZERO : goodsOriginalPrice;
        //套餐应收金额
        BigDecimal goodsReceivableCurrentPrice = orderMonthGoodsCacheVO.getGoodsReceivableAmount();
        goodsReceivableCurrentPrice = null == goodsReceivableCurrentPrice ? BigDecimal.ZERO : goodsReceivableCurrentPrice;


        //加收项成本金额之和
        log.info("加收项数据：" + orderAdditionalRevenueCache);
        BigDecimal additionalRevenueOriginalCostPrice = orderAdditionalRevenueCache.stream().map(OrderAdditionalRevenueCacheVO::getCostPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        additionalRevenueOriginalCostPrice = null == additionalRevenueOriginalCostPrice ? BigDecimal.ZERO : additionalRevenueOriginalCostPrice;
        //加收项原价金额之和
        BigDecimal additionalRevenueOriginalPrice = orderAdditionalRevenueCache.stream().map(OrderAdditionalRevenueCacheVO::getCost).reduce(BigDecimal.ZERO, BigDecimal::add);
        additionalRevenueOriginalPrice = null == additionalRevenueOriginalPrice ? BigDecimal.ZERO : additionalRevenueOriginalPrice;
        //加收项应收之和
        BigDecimal allAnItemCurrentPrice = orderAdditionalRevenueCache.stream().map(OrderAdditionalRevenueCacheVO::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        allAnItemCurrentPrice = null == allAnItemCurrentPrice ? BigDecimal.ZERO : allAnItemCurrentPrice;
        //额外礼赠成本
        BigDecimal giftExtentCostPrice = orderGiftExtentCache.stream().map(OrderGiftCacheByUserVO::getAllCostPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        giftExtentCostPrice = null == giftExtentCostPrice ? BigDecimal.ZERO : giftExtentCostPrice;
        //额外礼赠原价
        BigDecimal giftExtentOriginalPrice = orderGiftExtentCache.stream().map(OrderGiftCacheByUserVO::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        giftExtentOriginalPrice = null == giftExtentOriginalPrice ? BigDecimal.ZERO : giftExtentOriginalPrice;

        //订单应收=套餐应收金额+加收项应收金额之和
        BigDecimal ordersAccountsReceivableAmount = BigDecimalUtil.add(goodsReceivableCurrentPrice, allAnItemCurrentPrice);
        //订单原价=套餐原价金额+加收项原价金额之和
        BigDecimal ordersAccountsReceivableOriginalAmount = BigDecimalUtil.add(goodsOriginalPrice, additionalRevenueOriginalPrice);
        // 开始配置事实
        OrderIndexFact indexFact = OrderIndexFact.builder().bu(BusinessEnum.CARE_CENTER.getCode())
                .orderAmount(ordersAccountsReceivableOriginalAmount)
                .payAmount(ordersAccountsReceivableAmount).goodsOriginalPrice(goodsOriginalPrice)
                .goodsCostPrice(goodsCostPrice).giftOriginalPrice(giftExtentOriginalPrice)
                .selectedAmount(ordersAccountsReceivableOriginalAmount)
                .giftCostPrice(giftExtentCostPrice).additionalOriginalPrice(additionalRevenueOriginalPrice).additionalCostPrice(additionalRevenueOriginalCostPrice).effectDate(System.currentTimeMillis() / 1000).storeId(req.getStoreId()).orderType(String.valueOf(req.getOrderType())).build();

        // 计算订单折扣
        FlowContext discountContext = new FlowContext();
        discountContext.setAttribute(OrderIndexFact.class, indexFact);
        OrderIndex orderIndex = discountCalculator.run(discountContext);
        indexFact = discountContext.getAttribute(OrderIndexFact.class);

        //订单指标从ruleLink 配置规则
        BigDecimal orderDiscount = orderIndex.getDiscountMargin();
        BigDecimal netDiscountRate = orderIndex.getNetMargin();
        BigDecimal grossProfitMargin = orderIndex.getGrossMargin();
        Integer storeId = orderGoodsCacheReq.getStoreId();
        CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(storeId);

        DecimalFormat decimalFormat = new DecimalFormat("0.00#");

        List<OrderAdditionalRevenueCacheVO> holidayCollect = orderAdditionalRevenueCache.stream().filter(o -> o.getType().equals(MonthAdditionalRevenueEnum.HOLIDAY.getCode())).collect(Collectors.toList());
        List<OrderAdditionalRevenueCacheVO> costMultipleBirthsCollect = orderAdditionalRevenueCache.stream().filter(o -> o.getType().equals(MonthAdditionalRevenueEnum.COST_MULTIPLE_BIRTHS.getCode())).collect(Collectors.toList());
        List<OrderAdditionalRevenueCacheVO> stayCostCollect = orderAdditionalRevenueCache.stream().filter(o -> o.getType().equals(MonthAdditionalRevenueEnum.STAY_COST.getCode())).collect(Collectors.toList());
        List<OrderAdditionalRevenueCacheVO> roomChangesCollect = orderAdditionalRevenueCache.stream().filter(o -> o.getType().equals(MonthAdditionalRevenueEnum.ROOM_CHANGES.getCode())).collect(Collectors.toList());


        OrderDiscountsCacheVO init = OrderDiscountsCacheVO.init();
        init.setCurrency(storeCurrencyCode);
        init.setOrderDiscount(orderDiscount).setNetDiscountRate(netDiscountRate).setGrossProfitMargin(grossProfitMargin).setAmountReceivablePackageOriginalPrice(ObjectUtil.isEmpty(orderMonthGoodsCacheVO.getGoodsId()) ? BigDecimal.ZERO : new BigDecimal(decimalFormat.format(goodsOriginalPrice))).setAmountReceivablePackageCurrentPrice(ObjectUtil.isEmpty(orderMonthGoodsCacheVO.getGoodsId()) ? BigDecimal.ZERO : new BigDecimal(decimalFormat.format(goodsReceivableCurrentPrice)))

                .setAmountReceivableHolidaysOriginalPrice(CollectionUtils.isEmpty(holidayCollect) ? BigDecimal.ZERO : new BigDecimal(decimalFormat.format(holidayCollect.stream().map(OrderAdditionalRevenueCacheVO::getCost).reduce(BigDecimal.ZERO, BigDecimal::add)))).setAmountReceivableHolidaysCurrentPrice(CollectionUtils.isEmpty(holidayCollect) ? BigDecimal.ZERO : new BigDecimal(decimalFormat.format(holidayCollect.stream().map(OrderAdditionalRevenueCacheVO::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add))))

                .setAmountReceivableMultipleBirthsOriginalPrice(CollectionUtils.isEmpty(costMultipleBirthsCollect) ? BigDecimal.ZERO : new BigDecimal(decimalFormat.format(costMultipleBirthsCollect.stream().map(OrderAdditionalRevenueCacheVO::getCost).reduce(BigDecimal.ZERO, BigDecimal::add)))).setAmountReceivableMultipleBirthsCurrentPrice(CollectionUtils.isEmpty(costMultipleBirthsCollect) ? BigDecimal.ZERO : new BigDecimal(decimalFormat.format(costMultipleBirthsCollect.stream().map(OrderAdditionalRevenueCacheVO::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add))))

                .setAmountReceivableContinuedResidenceOriginalPrice(CollectionUtils.isEmpty(stayCostCollect) ? BigDecimal.ZERO : new BigDecimal(decimalFormat.format(stayCostCollect.stream().map(OrderAdditionalRevenueCacheVO::getCost).reduce(BigDecimal.ZERO, BigDecimal::add)))).setAmountReceivableContinuedResidenceCurrentPrice(CollectionUtils.isEmpty(stayCostCollect) ? BigDecimal.ZERO : new BigDecimal(decimalFormat.format(stayCostCollect.stream().map(OrderAdditionalRevenueCacheVO::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add))))

                .setChangeRoomTypeReceivableAmountOriginalPrice(CollectionUtils.isEmpty(roomChangesCollect) ? BigDecimal.ZERO : new BigDecimal(decimalFormat.format(roomChangesCollect.stream().map(OrderAdditionalRevenueCacheVO::getCost).reduce(BigDecimal.ZERO, BigDecimal::add)))).setChangeRoomTypeReceivableAmountCurrentPrice(CollectionUtils.isEmpty(roomChangesCollect) ? BigDecimal.ZERO : new BigDecimal(decimalFormat.format(roomChangesCollect.stream().map(OrderAdditionalRevenueCacheVO::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add))))

                .setOrderReceivableAmountOriginalPrice(new BigDecimal(decimalFormat.format(BigDecimalUtil.add(goodsOriginalPrice, additionalRevenueOriginalPrice)))).setOrderReceivableAmountCurrentPrice(new BigDecimal(decimalFormat.format(BigDecimalUtil.add(goodsReceivableCurrentPrice, allAnItemCurrentPrice)))).setTaxRate(indexFact.getTxtRate()).setDeposit(new BigDecimal(decimalFormat.format(new BigDecimal(cfgStoreEntity.getDeposit() / 100))));
        //配置折扣对象
        DiscountFact discountFact = DiscountFact.builder().orderType(req.getOrderType()).grossMargin(grossProfitMargin).build();
        //是否需要创建折扣审批
//        Boolean approval = this.getApproval(orderDiscount, req.getStoreId());
        Boolean approval = discountCalculator.isDiscountApproval(discountFact);
        if (ObjectUtil.isNotEmpty(orderInfo) && approval) {
            //原无需审批或者审批通过,需判断现折扣是否小于原折扣
            if (orderInfo.getApprovalDiscountStatus().equals(ApprovalDiscountStatusEnum.NO_APPROVAL_NEEDED.getCode()) || orderInfo.getApprovalDiscountStatus().equals(ApprovalDiscountStatusEnum.APPROVED.getCode())) {
                String discountDetails = orderInfo.getDiscountDetails();
                OrderDiscountsCacheVO read = JsonUtil.read(discountDetails, OrderDiscountsCacheVO.class);
                BigDecimal oldGrossProfitMargin = read.getGrossProfitMargin();
                log.info("是否需要创建折扣审批grossProfitMargin={},oldGrossProfitMargin={}", grossProfitMargin, oldGrossProfitMargin);
                //如果新的折扣率小于修改前的
                init.setApproval(grossProfitMargin.compareTo(oldGrossProfitMargin) < 0);
            } else {
                init.setApproval(approval);
            }
        } else {
            init.setApproval(approval);
        }
        init.setShowApproveText(approval);
        return Result.success(init);
    }

    /**
     * 根据订单ID获取订单折扣 [数据清洗在用]
     *
     * @param orderId
     * @return
     */
    @Override
    public OrderDiscountsCacheVO getOrderDiscountsByOrderId(Integer orderId) {
        HeOrderEntity orderInfo = orderRepository.getByOrderId(orderId);
        final Integer clientUid = orderInfo.getClientUid();
        final Integer storeId = orderInfo.getStoreId();
        final Integer orderType = orderInfo.getOrderType();
        if (ObjectUtil.isEmpty(orderInfo)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "订单不存在：" + orderId);
        }
        HeOrderGoodsEntity heOrderGoodsEntity = orderGoodsRepository.getByOrderId(orderId);
        final Integer goodsId = heOrderGoodsEntity.getGoodsId();
        List<HeOrderAdditionalRevenueEntity> additionalRevenueEntityList = orderAdditionalRevenueRepository.getByOrderId(orderId);

        //获取套餐缓存
        OrderGoodsCacheReq orderGoodsCacheReq = new OrderGoodsCacheReq();
        orderGoodsCacheReq.setParentId(heOrderGoodsEntity.getParentId());
        orderGoodsCacheReq.setGoodsId(goodsId);
        orderGoodsCacheReq.setSkuId(heOrderGoodsEntity.getSkuId());
        orderGoodsCacheReq.setGoodsReceivableAmount(new BigDecimal(heOrderGoodsEntity.getPayAmount() / 100));
        orderGoodsCacheReq.setClientUid(clientUid);
        orderGoodsCacheReq.setStoreId(storeId);
        orderGoodsCacheReq.setOrderType(orderType);
        orderGoodsCacheReq.setOrderId(orderId);
        orderGoodsCacheReq.setFromCache(false);

        OrderMonthGoodsCacheVO orderMonthGoodsCacheVO = getOrderGoodsCache(orderGoodsCacheReq).getData();

        if (ObjectUtil.isEmpty(orderMonthGoodsCacheVO) || ObjectUtil.isEmpty(orderMonthGoodsCacheVO.getGoodsId())) {
            //没有套餐，不计算
            return new OrderDiscountsCacheVO();
        }

        orderMonthGoodsCacheVO.setRoomPrice(BigDecimalUtil.check(orderMonthGoodsCacheVO.getRoomPrice()));
        orderMonthGoodsCacheVO.setGoodsReceivableAmount(BigDecimalUtil.check(orderMonthGoodsCacheVO.getGoodsReceivableAmount()));
        orderMonthGoodsCacheVO.setGoodsPriceOrgin(BigDecimalUtil.check(orderMonthGoodsCacheVO.getGoodsPriceOrgin()));
        orderMonthGoodsCacheVO.setGoodsPriceCost(BigDecimalUtil.check(orderMonthGoodsCacheVO.getGoodsPriceCost()));


        //获取套餐额外礼赠数据
        OrderAdvanceOrderGiftReq orderAdvanceOrderGiftReq = new OrderAdvanceOrderGiftReq();
        orderAdvanceOrderGiftReq.setClientUid(clientUid);
        orderAdvanceOrderGiftReq.setStoreId(storeId);
        orderAdvanceOrderGiftReq.setOrderType(orderType);
        orderAdvanceOrderGiftReq.setGoodsId(goodsId);
        orderAdvanceOrderGiftReq.setOrderId(orderId);

        List<OrderGiftCacheByUserVO> orderGiftExtentCache = getOrderGiftExtentCache(orderAdvanceOrderGiftReq);
        //获取加收项明细
        OrderAddtionalRevenueCacheReq orderAddtionalRevenueCacheReq = new OrderAddtionalRevenueCacheReq();
        orderAddtionalRevenueCacheReq.setClientUid(clientUid);
        orderAddtionalRevenueCacheReq.setStoreId(storeId);
        orderAddtionalRevenueCacheReq.setOrderType(orderType);
        orderAddtionalRevenueCacheReq.setGoodsId(goodsId);
        orderAddtionalRevenueCacheReq.setOrderId(orderId);

        List<OrderAdditionalRevenueCacheVO> orderAdditionalRevenueCache = getOrderAdditionalRevenueCache(orderAddtionalRevenueCacheReq);
        //只有填写完整的时候参与计算
        orderAdditionalRevenueCache = orderAdditionalRevenueCache.stream().filter(o ->
                //填写完整的
                o.getIntegrality() == 1).collect(Collectors.toList());

        Optional<OrderAdditionalRevenueCacheVO> first = orderAdditionalRevenueCache.stream().filter(o -> o.getType() == 4).findFirst();
        if (first.isPresent()) {
            if (ObjectUtil.isEmpty(first.get().getIsFestival()) || first.get().getIsFestival() == 0) {
                orderAdditionalRevenueCache.remove(first.get());
            }
        }


        //套餐成本金额
        BigDecimal goodsCostPrice = orderMonthGoodsCacheVO.getGoodsPriceCost();
        goodsCostPrice = null == goodsCostPrice ? BigDecimal.ZERO : goodsCostPrice;
        //套餐原价金额
        BigDecimal goodsOriginalPrice = orderMonthGoodsCacheVO.getGoodsPriceOrgin();
        goodsOriginalPrice = null == goodsOriginalPrice ? BigDecimal.ZERO : goodsOriginalPrice;
        //套餐应收金额
        BigDecimal goodsReceivableCurrentPrice = orderMonthGoodsCacheVO.getGoodsReceivableAmount();
        goodsReceivableCurrentPrice = null == goodsReceivableCurrentPrice ? BigDecimal.ZERO : goodsReceivableCurrentPrice;


        //加收项成本金额之和
        log.info("加收项数据：" + orderAdditionalRevenueCache);
        BigDecimal additionalRevenueOriginalCostPrice = orderAdditionalRevenueCache.stream().map(OrderAdditionalRevenueCacheVO::getCostPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        additionalRevenueOriginalCostPrice = null == additionalRevenueOriginalCostPrice ? BigDecimal.ZERO : additionalRevenueOriginalCostPrice;
        //加收项原价金额之和
        BigDecimal additionalRevenueOriginalPrice = orderAdditionalRevenueCache.stream().map(OrderAdditionalRevenueCacheVO::getCost).reduce(BigDecimal.ZERO, BigDecimal::add);
        additionalRevenueOriginalPrice = null == additionalRevenueOriginalPrice ? BigDecimal.ZERO : additionalRevenueOriginalPrice;
        //加收项应收之和
        BigDecimal allAnItemCurrentPrice = orderAdditionalRevenueCache.stream().map(OrderAdditionalRevenueCacheVO::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        allAnItemCurrentPrice = null == allAnItemCurrentPrice ? BigDecimal.ZERO : allAnItemCurrentPrice;
        //额外礼赠成本
        BigDecimal giftExtentCostPrice = orderGiftExtentCache.stream().map(OrderGiftCacheByUserVO::getAllCostPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        giftExtentCostPrice = null == giftExtentCostPrice ? BigDecimal.ZERO : giftExtentCostPrice;
        //额外礼赠原价
        BigDecimal giftExtentOriginalPrice = orderGiftExtentCache.stream().map(OrderGiftCacheByUserVO::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        giftExtentOriginalPrice = null == giftExtentOriginalPrice ? BigDecimal.ZERO : giftExtentOriginalPrice;

        //订单应收=套餐应收金额+加收项应收金额之和
        BigDecimal ordersAccountsReceivableAmount = BigDecimalUtil.add(goodsReceivableCurrentPrice, allAnItemCurrentPrice);
        //订单原价=套餐原价金额+加收项原价金额之和
        BigDecimal ordersAccountsReceivableOriginalAmount = BigDecimalUtil.add(goodsOriginalPrice, additionalRevenueOriginalPrice);


        // 这里只需要查询订单里的折扣就可以了,

        BigDecimal orderDiscount = orderInfo.getDiscountMargin();
        BigDecimal netDiscountRate = orderInfo.getNetMargin();
        BigDecimal grossProfitMargin = orderInfo.getGrossMargin();

        CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(storeId);


        DecimalFormat decimalFormat = new DecimalFormat("0.00#");


        List<OrderAdditionalRevenueCacheVO> holidayCollect = orderAdditionalRevenueCache.stream().filter(o -> o.getType().equals(MonthAdditionalRevenueEnum.HOLIDAY.getCode())).collect(Collectors.toList());
        List<OrderAdditionalRevenueCacheVO> costMultipleBirthsCollect = orderAdditionalRevenueCache.stream().filter(o -> o.getType().equals(MonthAdditionalRevenueEnum.COST_MULTIPLE_BIRTHS.getCode())).collect(Collectors.toList());
        List<OrderAdditionalRevenueCacheVO> stayCostCollect = orderAdditionalRevenueCache.stream().filter(o -> o.getType().equals(MonthAdditionalRevenueEnum.STAY_COST.getCode())).collect(Collectors.toList());
        List<OrderAdditionalRevenueCacheVO> roomChangesCollect = orderAdditionalRevenueCache.stream().filter(o -> o.getType().equals(MonthAdditionalRevenueEnum.ROOM_CHANGES.getCode())).collect(Collectors.toList());


        OrderDiscountsCacheVO init = OrderDiscountsCacheVO.init();
        init.setOrderDiscount(orderDiscount).setNetDiscountRate(netDiscountRate).setGrossProfitMargin(grossProfitMargin).setAmountReceivablePackageOriginalPrice(ObjectUtil.isEmpty(orderMonthGoodsCacheVO.getGoodsId()) ? BigDecimal.ZERO : new BigDecimal(decimalFormat.format(goodsOriginalPrice))).setAmountReceivablePackageCurrentPrice(ObjectUtil.isEmpty(orderMonthGoodsCacheVO.getGoodsId()) ? BigDecimal.ZERO : new BigDecimal(decimalFormat.format(goodsReceivableCurrentPrice)))

                .setAmountReceivableHolidaysOriginalPrice(CollectionUtils.isEmpty(holidayCollect) ? BigDecimal.ZERO : new BigDecimal(decimalFormat.format(holidayCollect.stream().map(OrderAdditionalRevenueCacheVO::getCost).reduce(BigDecimal.ZERO, BigDecimal::add)))).setAmountReceivableHolidaysCurrentPrice(CollectionUtils.isEmpty(holidayCollect) ? BigDecimal.ZERO : new BigDecimal(decimalFormat.format(holidayCollect.stream().map(OrderAdditionalRevenueCacheVO::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add))))

                .setAmountReceivableMultipleBirthsOriginalPrice(CollectionUtils.isEmpty(costMultipleBirthsCollect) ? BigDecimal.ZERO : new BigDecimal(decimalFormat.format(costMultipleBirthsCollect.stream().map(OrderAdditionalRevenueCacheVO::getCost).reduce(BigDecimal.ZERO, BigDecimal::add)))).setAmountReceivableMultipleBirthsCurrentPrice(CollectionUtils.isEmpty(costMultipleBirthsCollect) ? BigDecimal.ZERO : new BigDecimal(decimalFormat.format(costMultipleBirthsCollect.stream().map(OrderAdditionalRevenueCacheVO::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add))))

                .setAmountReceivableContinuedResidenceOriginalPrice(CollectionUtils.isEmpty(stayCostCollect) ? BigDecimal.ZERO : new BigDecimal(decimalFormat.format(stayCostCollect.stream().map(OrderAdditionalRevenueCacheVO::getCost).reduce(BigDecimal.ZERO, BigDecimal::add)))).setAmountReceivableContinuedResidenceCurrentPrice(CollectionUtils.isEmpty(stayCostCollect) ? BigDecimal.ZERO : new BigDecimal(decimalFormat.format(stayCostCollect.stream().map(OrderAdditionalRevenueCacheVO::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add))))

                .setChangeRoomTypeReceivableAmountOriginalPrice(CollectionUtils.isEmpty(roomChangesCollect) ? BigDecimal.ZERO : new BigDecimal(decimalFormat.format(roomChangesCollect.stream().map(OrderAdditionalRevenueCacheVO::getCost).reduce(BigDecimal.ZERO, BigDecimal::add)))).setChangeRoomTypeReceivableAmountCurrentPrice(CollectionUtils.isEmpty(roomChangesCollect) ? BigDecimal.ZERO : new BigDecimal(decimalFormat.format(roomChangesCollect.stream().map(OrderAdditionalRevenueCacheVO::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add))))

                .setOrderReceivableAmountOriginalPrice(new BigDecimal(decimalFormat.format(BigDecimalUtil.add(goodsOriginalPrice, additionalRevenueOriginalPrice)))).setOrderReceivableAmountCurrentPrice(new BigDecimal(decimalFormat.format(BigDecimalUtil.add(goodsReceivableCurrentPrice, allAnItemCurrentPrice))))

                .setDeposit(new BigDecimal(decimalFormat.format(new BigDecimal(cfgStoreEntity.getDeposit() / 100))));
        //配置折扣对象
        DiscountFact discountFact = DiscountFact.builder().orderType(orderInfo.getOrderType()).grossMargin(orderInfo.getGrossMargin().multiply(new BigDecimal(100))).build();

        //是否需要创建折扣审批
//        Boolean approval = this.getApproval(orderDiscount, storeId);
        Boolean approval = discountCalculator.isDiscountApproval(discountFact);
        init.setApproval(approval);
        if (ObjectUtil.isNotEmpty(orderInfo) && approval) {
            String discountDetails = orderInfo.getDiscountDetails();
            if (ObjectUtil.isNotEmpty(discountDetails)) {
                OrderDiscountsCacheVO read = JsonUtil.read(discountDetails, OrderDiscountsCacheVO.class);
                if (ObjectUtil.isNotEmpty(read)) {
                    BigDecimal oldOrderDiscount = read.getOrderDiscount();
                    if (ObjectUtil.isNotEmpty(oldOrderDiscount)) {
                        //如果新的折扣率小于修改前的
                        init.setApproval(orderDiscount.compareTo(oldOrderDiscount) < 0);
                    }
                }
            }
        }
        init.setShowApproveText(approval);
        return init;
    }


    @Override
    public Result<CalculateVO> calculate(CalculateReq calculateReq) {
        //获取门店类型
        CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(calculateReq.getStoreId());

        //门店的多胞胎写死，节假日从数据库获取 废弃
//        OrderMonthStoreTypeCostEnum orderMonthStoreTypeCostEnum = getStoreType(calculateReq.getStoreId());
        //region 获取多胞胎节假日费用
        FlowContext flowContext = new FlowContext();
        MultipleHolidaysReq multipleHolidaysReq = new MultipleHolidaysReq();
        multipleHolidaysReq.setStoreId(calculateReq.getStoreId());
        flowContext.setResultAttribute(MultipleHolidaysReq.class, multipleHolidaysReq);

        SnowballFlowLauncher.fire(flowContext, multipleHolidaysProcessor);
        StoreFinanceVO multipleHolidayVO = flowContext.getResultAttribute(StoreFinanceVO.class);
        //endregion


        CalculateVO calculateVO = new CalculateVO();
        calculateVO.setHoliday(new BigDecimal(cfgStoreEntity.getHolidayPrice() / 100));
        calculateVO.setMultipleBirths(multipleHolidayVO.getMultipleBirthsOriginal());
        return Result.success(calculateVO);
    }

    @Override
    public Result<BigDecimal> continueObtainOriginalPrice(ContinueObtainOriginalPriceReq continueObtainOriginalPriceReq) {
        OrderGoodsCacheReq orderGoodsCacheReq = new OrderGoodsCacheReq();
        orderGoodsCacheReq.setOperator(continueObtainOriginalPriceReq.getOperator());
        orderGoodsCacheReq.setClientUid(continueObtainOriginalPriceReq.getClientUid());
        orderGoodsCacheReq.setStoreId(continueObtainOriginalPriceReq.getStoreId());
        orderGoodsCacheReq.setOrderType(continueObtainOriginalPriceReq.getOrderType());
        OrderMonthGoodsCacheVO data = getOrderGoodsCache(orderGoodsCacheReq).getData();
        if (ObjectUtil.isEmpty(data)) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "未选择套餐");
        }

        Integer goodsId = continueObtainOriginalPriceReq.getGoodsId();

        //原房型数据
        RoomByGoodsIdVo oldRoomInfoById = goodsManager.getRoomInfoByGoodsId(goodsId);
        //获取套餐的数据
        StorePackageInfo storePackageInfo = goodsManager.getStorePackageInfo(goodsId);

        //获取续住房型的数据
        RoomByGoodsIdVo newRoomInfoById = goodsManager.getRoomInfoById(continueObtainOriginalPriceReq.getRoomId());

        //续住原价 =（套餐应付金额/套餐天数+）* 续住时长天单天续住房型与套餐房型差价数
        BigDecimal cost = (BigDecimalUtil.divide(data.getGoodsReceivableAmount(), new BigDecimal(storePackageInfo.getServiceDays())).add(new BigDecimal(newRoomInfoById.getRoomPrice() / 100 - oldRoomInfoById.getRoomPrice() / 100))).multiply(new BigDecimal(continueObtainOriginalPriceReq.getDays()));
        return Result.success(cost);
    }

    /**
     * 客户详情-订单列表
     *
     * @param req
     * @return
     */
    @Override
    public Result<PageVO<WechatMyOrderVO>> customerOrder(ClientOrderListReq req) {

        PageVO<WechatMyOrderVO> PageData = orderRepository.getCustomerStoreOrderList(req);
        if (ObjectUtil.isNotNull(PageData) && CollectionUtil.isNotEmpty(PageData.getList())) {

            PageData.getList().forEach(o -> {
                String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(o.getStoreId());
                o.setCurrency(storeCurrencyCode);
            });

        }
        return Result.success(PageData);
    }

    /**
     * 订单列表中按钮列表
     *
     * @param orderId
     * @return
     */
    @Override
    public Result<List<OrderOperateButton>> queryCustomerOrderBtn(Integer orderId) {
        WechatMyOrderVO orderEntity = orderRepository.getWechatOrderVOByOrderId(orderId);
        if (ObjectUtil.isNull(orderEntity)) {
            return Result.failed(ResultEnum.NOT_EXIST.getCode(), "订单不存在");
        }

        if (ObjectUtil.isNotNull(orderEntity.getOldOrNew()) && orderEntity.getOldOrNew().equals(1)) {
            // 退款记录
            List<HeOrderRefundEntity> refundList = orderRefundRepository.getRefundByOrderId(orderId);
            // 钉钉审批记录
            List<OaProcessIdRelationPO> approveList = oaProcessIdRelationService.getByOrderId(orderId.toString());
            // 收款记录
            List<HeIncomeRecordEntity> incomeList = incomeRecordRepository.getSuccessfulRecordListByOrderId(orderId);
            // 签约记录表
            List<MonthContractSignRecordEntity> mainContractList = monthContractSignRecordRepository.getListByOrderId(orderId.longValue());
            // 合同补充协议
            List<MonthContractSignAgreementEntity> agreementContractList = monthContractSignAgreementRepository.list(orderId.longValue(), 2);
            // 纸质合同
            List<ContractSignRecordPaperEntity> paperList = contractSignRecordPaperRepository.getByOrderId(orderId);
            //提前离馆列表
            List<AheadOutRoomEntity> aheadOutRoomEntities = aheadOutRoomRepository.queryByOrderIds(Arrays.asList(orderId));
            //查询合同解除协议
            List<MonthContractSignRecordEntity> byOrderIdListAndContractStatus = monthContractSignRecordRepository.getByOrderIdListAndContractStatus(Arrays.asList(orderId), TemplateContractTypeEnum.RELEASE.code(), ContractStatusEnum.SIGNED.code());
            //判断是否签署离馆协议
            if (ObjectUtil.isNotEmpty(byOrderIdListAndContractStatus)) {
                List<MonthContractSignRecordEntity> collect = byOrderIdListAndContractStatus.stream().filter(x -> x.getGuideId().equals(Long.valueOf(orderId))).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(collect)) {
                    orderEntity.setHasSignContractReleaseAgreement(collect.get(0).getContractStatus());
                }
            }
            //设置订单应该展示的按钮
            List<OrderOperateButton> wechatMyOrderButtons = setButton(orderEntity, refundList, approveList, incomeList, mainContractList, agreementContractList, paperList, aheadOutRoomEntities);
            return Result.success(wechatMyOrderButtons);
        } else {
            // 旧订单
            return Result.failed(ResultEnum.NOT_EXIST.getCode(), "不支持旧订单查询");
        }
    }

    @Override
    public OrderMonthClientVO getOrderCustomerInfoByOrderId(Integer orderId) {
        HeOrderEntity orderInfo = orderRepository.getByOrderId(orderId);
        //获取客户
        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(orderId);
        OrderMonthClientVO orderMonthClientVO = clientRepository.clientEntityToClientVO(heOrderUserSnapshotEntity);
        //如果快照没有,则从tabClient中取
        if (ObjectUtil.isEmpty(orderMonthClientVO)) {
            TabClientEntity tabClientById = clientRepository.getTabClientById(orderInfo.getClientUid());
            orderMonthClientVO = clientRepository.tabClientEntityToClientVO(tabClientById);
            orderMonthClientVO.setClientUid(tabClientById.getId());
        }

        if (ObjectUtil.isNotEmpty(orderMonthClientVO)) {
            orderMonthClientVO.setHidePhone(SensitiveInformationUtil.conductPhoneOrIdCardNo(orderMonthClientVO.getPhone()));
            orderMonthClientVO.setIsConfirm(1);
            Result<InviteInfoVO> inviteInfoVOResult = heInviteRelationService.queryUserInviteInfoByBasicId(orderMonthClientVO.getBasicUid().longValue());
            InviteInfoVO data = inviteInfoVOResult.getData();
            if (ObjectUtil.isNotEmpty(data)) {
                com.stbella.order.server.order.month.res.InviteInfoVO build = com.stbella.order.server.order.month.res.InviteInfoVO.builder().isBind(data.getIsBind()).parentBasicId(data.getParentBasicId()).parentName(data.getParentName()).parentPhone(data.getParentPhone()).parentQrCode(data.getParentQrCode()).parentRealPhone(data.getParentRealPhone()).basicId(data.getBasicId()).isBind(data.getIsBind()).build();
                orderMonthClientVO.setInviteInfo(build);
                orderMonthClientVO.setIsHaveQrCode(BooleanUtils.toInteger(ObjectUtil.isNotEmpty(data.getParentQrCode())));
            } else {
                orderMonthClientVO.setIsHaveQrCode(BooleanUtils.toInteger(ObjectUtil.isNotEmpty(orderMonthClientVO.getQrCode())));
            }
            orderMonthClientVO.setSignType(orderInfo.getSignType());
            orderMonthClientVO.setInviteAddIntegralType(orderInfo.getInviteAddIntegralType());

            Integer storeId = orderMonthClientVO.getStoreId();
            CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(storeId);
            orderMonthClientVO.setStoreName(cfgStoreEntity.getStoreName());

            orderMonthClientVO.setSignMasterContract(SignMasterContract(orderMonthClientVO.getBasicUid()));
        } else {

        }
        return orderMonthClientVO;
    }

    /**
     * 判断是否签定后正式的合同（通过是否有完整的证件信息来判断）
     * 根据 会员id 查询证件信息。如果证件为空可以编辑
     *
     * @param basicUid
     * @return
     */
    protected boolean SignMasterContract(Integer basicUid) {
        HeUserCardPO heUserCardPO = userCardManager.queryUserCard(basicUid);
        if (ObjectUtil.isEmpty(heUserCardPO)) {
            return false;
        }
        if (Strings.isNullOrEmpty(heUserCardPO.getIdCardBack()) || Strings.isNullOrEmpty(heUserCardPO.getIdCardFront())) {
            return false;
        }
        if (Strings.isNullOrEmpty(heUserCardPO.getIdCard())) {
            return false;
        }
        if (Objects.nonNull(heUserCardPO.getContractSign()) && heUserCardPO.getContractSign() != 1) {
            return false;
        }

        return true;
    }

    @Override
    public OrderMonthClientBailorVO getOrderBailorInfoByOrderId(Integer orderId) {
        //获取客户委托人
        HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity = orderBailorSnapshotRepository.queryByOrderId(orderId);
        OrderMonthClientBailorVO orderMonthClientBailorVO = clientRepository.clientBailorEntityToClientBailorVO(heOrderBailorSnapshotEntity);
        if (ObjectUtil.isNotEmpty(orderMonthClientBailorVO)) {
            orderMonthClientBailorVO.setHidePhone(SensitiveInformationUtil.conductPhoneOrIdCardNo(orderMonthClientBailorVO.getPhone()));
            orderMonthClientBailorVO.setIsConfirm(1);

            //判断订单是否签订了授权委托书/主合同/委托协议之一

            // 签约记录表
            List<MonthContractSignRecordEntity> mainContractList = monthContractSignRecordRepository.getListByOrderId(orderId.longValue());
            // 纸质合同
            List<ContractSignRecordPaperEntity> paperList = contractSignRecordPaperRepository.getByOrderId(orderId);

            mainContractList = mainContractList.stream().filter(m -> (ContractStatusEnum.SIGNED.code().equals(m.getTemplateContractType()) && m.getContractType().equals(8)) || (TemplateContractTypeV2Enum.APPOINTMENT.code().equals(m.getTemplateContractType()) && ContractStatusEnum.SIGNED.code().equals(m.getContractStatus())) || (TemplateContractTypeV2Enum.ENTRUST.code().equals(m.getTemplateContractType()) && ContractStatusEnum.SIGNED.code().equals(m.getContractStatus()))).collect(Collectors.toList());
            paperList = paperList.stream().filter(m -> (ContractStatusEnum.SIGNED.code().equals(m.getContractStatus()) && m.getContractType().equals(8)) || (TemplateContractTypeV2Enum.APPOINTMENT.code().equals(m.getContractType()) && ContractStatusEnum.SIGNED.code().equals(m.getContractStatus())) || (TemplateContractTypeV2Enum.ENTRUST.code().equals(m.getContractType()) && ContractStatusEnum.SIGNED.code().equals(m.getContractStatus()))).collect(Collectors.toList());

            orderMonthClientBailorVO.setIsDelete(!(CollectionUtils.isNotEmpty(mainContractList) || CollectionUtils.isNotEmpty(paperList)));
        }


        return orderMonthClientBailorVO;
    }

    @Override
    public Result getOrderCostMultipleBirthsRecord(List<Integer> orderList) {
        Result result = Result.success(Arrays.asList());
        if (CollectionUtils.isEmpty(orderList)) {
            return result;
        }
        List<Integer> orderIdList = orderList.stream().map(o -> new Integer(o.toString())).collect(Collectors.toList());
        List<HeOrderContinueLiveRecordEntity> orderCostMultipleBirthsRecord = orderContinueLiveRecordRepository.getOrderCostMultipleBirthsRecord(orderIdList);
        Map<Integer, List<HeOrderContinueLiveRecordEntity>> collect = orderCostMultipleBirthsRecord.stream().collect(Collectors.groupingBy(HeOrderContinueLiveRecordEntity::getOrderId));

        for (Integer orderId : collect.keySet()) {
            result = getListResult(orderId, collect.get(orderId));
        }
        return result;
    }


    private Result<List<OrderCostMultipleBirthsRecordVO>> getListResult(Integer orderId, List<HeOrderContinueLiveRecordEntity> orderCostMultipleBirthsRecord) {
        Map<Long, List<HeOrderContinueLiveRecordEntity>> collect = orderCostMultipleBirthsRecord.stream().collect(Collectors.groupingBy(HeOrderContinueLiveRecordEntity::getAdditionalRevenueId));

        HeOrderEntity orderInfo = orderRepository.getByOrderId(orderId);
        String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(orderInfo.getStoreId());


        List<OrderCostMultipleBirthsRecordVO> result = new ArrayList<>();

        List<Long> list = new ArrayList<>(collect.keySet());

        Collections.sort(list);

        int packIndex = 0;
        for (Long id : list) {
            List<HeOrderContinueLiveRecordEntity> orderContinueLiveRecordEntityList = collect.get(id);
            //最后一条记录
            HeOrderContinueLiveRecordEntity lastRecord = orderContinueLiveRecordEntityList.get(orderContinueLiveRecordEntityList.size() - 1);
            int index = 0;
            for (HeOrderContinueLiveRecordEntity heOrderContinueLiveRecordEntity : orderContinueLiveRecordEntityList) {
                OrderCostMultipleBirthsRecordVO orderCostMultipleBirthsRecordVO = new OrderCostMultipleBirthsRecordVO();
                orderCostMultipleBirthsRecordVO.setFinalDurationOfStay(lastRecord.getFinalDays().toString());
                orderCostMultipleBirthsRecordVO.setFinalContinuousHousingType(lastRecord.getRoomName());

                if (lastRecord.getFinalOriginalPrice().compareTo(BigDecimal.ZERO) >= 0) {
                    orderCostMultipleBirthsRecordVO.setFinalAmountOfRenewal(storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(lastRecord.getFinalOriginalPrice()));
                } else {
                    orderCostMultipleBirthsRecordVO.setFinalAmountOfRenewal("-" + storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(lastRecord.getFinalOriginalPrice()).replaceAll("-", ""));
                }

                if (lastRecord.getFinalPrice().compareTo(BigDecimal.ZERO) >= 0) {
                    orderCostMultipleBirthsRecordVO.setFinalRenewTheContractAmount(storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(lastRecord.getFinalPrice()));
                } else {
                    orderCostMultipleBirthsRecordVO.setFinalRenewTheContractAmount("-" + storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(lastRecord.getFinalPrice()).replaceAll("-", ""));
                }

                Integer days = heOrderContinueLiveRecordEntity.getDays();
                orderCostMultipleBirthsRecordVO.setChangeOfStayDays(days > 0 ? "+" + days : days.toString());
                orderCostMultipleBirthsRecordVO.setContinuousHousingType(heOrderContinueLiveRecordEntity.getRoomName());
                BigDecimal changeOriginalPrice = heOrderContinueLiveRecordEntity.getChangeOriginalPrice();
                String changeOriginalPriceStr = RMBUtils.formatToseparaDecimalsNew(changeOriginalPrice);

                if (changeOriginalPrice.compareTo(BigDecimal.ZERO) > 0) {
                    orderCostMultipleBirthsRecordVO.setChangeOfRenewalAmount("+" + storeCurrencyCode + changeOriginalPriceStr);
                } else if (changeOriginalPrice.compareTo(BigDecimal.ZERO) == 0) {
                    orderCostMultipleBirthsRecordVO.setChangeOfRenewalAmount(storeCurrencyCode + changeOriginalPriceStr);
                } else {
                    orderCostMultipleBirthsRecordVO.setChangeOfRenewalAmount("-" + storeCurrencyCode + changeOriginalPriceStr.replaceAll("-", ""));
                }

                BigDecimal changePrice = heOrderContinueLiveRecordEntity.getChangePrice();
                String changePriceStr = RMBUtils.formatToseparaDecimalsNew(changePrice);

                if (changePrice.compareTo(BigDecimal.ZERO) > 0) {
                    orderCostMultipleBirthsRecordVO.setChangeOfContractAmountForRenewal("+" + storeCurrencyCode + changePriceStr);
                } else if (changePrice.compareTo(BigDecimal.ZERO) == 0) {
                    orderCostMultipleBirthsRecordVO.setChangeOfContractAmountForRenewal(storeCurrencyCode + changePriceStr);
                } else {
                    orderCostMultipleBirthsRecordVO.setChangeOfContractAmountForRenewal("-" + storeCurrencyCode + changePriceStr.replaceAll("-", ""));
                }

                orderCostMultipleBirthsRecordVO.setTimeOfChange(DateUtil.formatDateTime(heOrderContinueLiveRecordEntity.getGmtModified()));
                orderCostMultipleBirthsRecordVO.setIndex(index);
                orderCostMultipleBirthsRecordVO.setPackIndex(packIndex);
                orderCostMultipleBirthsRecordVO.setLength(orderContinueLiveRecordEntityList.size());
                orderCostMultipleBirthsRecordVO.setChangeOfPerson(heOrderContinueLiveRecordEntity.getOptName());
                orderCostMultipleBirthsRecordVO.setOrderId(orderId);
                orderCostMultipleBirthsRecordVO.setAdditionalRevenueId(heOrderContinueLiveRecordEntity.getAdditionalRevenueId());
                result.add(orderCostMultipleBirthsRecordVO);
                index++;
            }
            packIndex++;
        }
        return Result.success(result);
    }

    @Override
    public Result<OrderSettlementVO> paymentCollectionPageByOrderId(OrderSettlementQuery paymentCollectionPageRequest) {

        OrderSettlementVO result = new OrderSettlementVO();
        result.setOrderId(paymentCollectionPageRequest.getOrderId());
        result.setClientUid(paymentCollectionPageRequest.getClientUid());
        result.setType(paymentCollectionPageRequest.getType());


        //套餐应付金额
        Integer payAmount;
        //套餐已支付金额
        Integer paidAmount;
        //门店id
        Integer storeId;
        //门店信息
        CfgStoreEntity cfgStoreEntity;
        if (paymentCollectionPageRequest.getType() == 0) {
            final Integer orderId = paymentCollectionPageRequest.getOrderId();
            List<HeIncomeRecordEntity> offlineRefuseRecordListByOrderId = incomeRecordRepository.getOfflineRefuseRecordListByOrderId(orderId);
            //获取订单主表数据
            HeOrderEntity orderInfo = orderRepository.getByOrderId(orderId);
            String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(orderInfo.getStoreId());
            result.setCurrency(storeCurrencyCode);
            //获取订单套餐数据
            HeOrderGoodsEntity orderGoodsInfo = orderGoodsRepository.getByOrderId(orderId);
            result.setGoodsId(orderGoodsInfo.getGoodsId());
            result.setSkuId(orderGoodsInfo.getSkuId());
            result.setPayName(orderGoodsInfo.getGoodsName());
            result.setHasIncomeRecords(ObjectUtil.isEmpty(offlineRefuseRecordListByOrderId) ? 0 : 1);
            result.setBasicUid(orderInfo.getBasicUid());
            //套餐应付金额
            payAmount = orderInfo.calPayable();
            //套餐已支付金额（注：20230208需求变更，改为实际已付金额=累计支付金额-退款成功金额）
            paidAmount = orderInfo.getRealAmount() + orderInfo.getProductionAmountPay();

            //门店ID
            storeId = orderInfo.getStoreId();
            cfgStoreEntity = storeRepository.queryCfgStoreById(storeId);

            //待支付金额
            // 2025.01.07需求变更，待支付金额=应付金额-已支付金额-未确认金额
            int unpaidAmount = orderInfo.leftPayAmount();
            if (unpaidAmount <= 0) {
                unpaidAmount = 0;
                result.setIsFullAmount(true);
            } else {
                result.setIsFullAmount(false);
            }
            result.setPayAmount(BigDecimalUtil.divide(new BigDecimal(payAmount), new BigDecimal(100)));
            result.setPaidAmount(BigDecimalUtil.divide(new BigDecimal(paidAmount), new BigDecimal(100)));
            result.setUnpaidAmount(BigDecimalUtil.divide(new BigDecimal(unpaidAmount), new BigDecimal(100)));
            result.setPercentagePayment(BigDecimalUtil.divideRoundingModeAndScale(result.getPaidAmount(), result.getPayAmount(), BigDecimal.ROUND_DOWN, 2).multiply(new BigDecimal(100)).intValue());
            result.setStoreId(storeId);
            result.setStoreName(cfgStoreEntity.getStoreName());
            result.setStoreType(cfgStoreEntity.getType());
            result.setOrderSn(orderInfo.getOrderSn());
        } else {
            result.setGoodsId(null);
            result.setSkuId(null);
            result.setPayName(PayAmountEnum.ANTECEDENT_MONEY.getValue());
            Integer clientUid = paymentCollectionPageRequest.getClientUid();
            OrderMonthIncomeQuery query = new OrderMonthIncomeQuery();
            query.setClientId(clientUid);
            query.setReceiptType(PayAmountEnum.ANTECEDENT_MONEY.getCode());
            DepositSettlementVO statisticsIncomeVO = orderIncomeAssembler.queryDepositAssembler(query);

            String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(statisticsIncomeVO.getStoreId());
            result.setCurrency(storeCurrencyCode);
            result.setBasicUid(statisticsIncomeVO.getBasicUid());
            //押金默认不存在线下支付
            result.setHasIncomeRecords(0);
            result.setPayAmount(statisticsIncomeVO.getDeposit());
            //门店押金已付金额
            result.setPaidAmount(BigDecimalUtil.subtract(statisticsIncomeVO.getTotalIncome(), statisticsIncomeVO.getDepositRefund()));
            result.setUnpaidAmount(statisticsIncomeVO.getReceivableAmount());
            result.setIsFullAmount(statisticsIncomeVO.getIsFullAmount());
            result.setPercentagePayment(statisticsIncomeVO.getPercentagePayment());
            result.setStoreId(statisticsIncomeVO.getStoreId());
            result.setStoreName(statisticsIncomeVO.getStoreName());
            result.setStoreType(statisticsIncomeVO.getStoreType());
        }
        return Result.success(result);
    }

    /**
     * C端我的订单列表
     *
     * @param customerMyOrderQuery
     * @return
     */
    @Override
    public Result<PageVO<CustomerMyOrderVO>> customerMyOrder(CustomerMyOrderQuery customerMyOrderQuery) {
        if (StringUtils.isNotBlank(customerMyOrderQuery.getPhone())) {
            List<TabClientEntity> tabClientList = clientRepository.getTabClientByPhone(customerMyOrderQuery.getPhone());
            List<Integer> clientIdList = CollectionUtils.isNotEmpty(tabClientList) ? tabClientList.stream().map(TabClientEntity::getId).collect(Collectors.toList()) : ListUtil.toList(-1);
            customerMyOrderQuery.setClientIdList(clientIdList);
        }

        Page<CustomerMyOrderVO> customerOrderList = orderRepository.getCustomerOrderList(customerMyOrderQuery);
        List<CustomerMyOrderVO> list = customerOrderList.getRecords();
        for (CustomerMyOrderVO customerMyOrderVO : list) {
            Integer orderId = customerMyOrderVO.getOrderId();

            String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(customerMyOrderVO.getStoreId());
            customerMyOrderVO.setCurrency(storeCurrencyCode);

            //查出所有orderType 0,1,50,70 月子标准订单,小月子订单,护士外派,S-BAR
            if (commonOrderTypeList.contains(customerMyOrderVO.getOrderType())) {
                HeOrderGoodsEntity heOrderGoodsEntity = orderGoodsRepository.getByOrderId(orderId);
                if (ObjectUtil.isNotEmpty(heOrderGoodsEntity)) {
                    customerMyOrderVO.setGoodsName(heOrderGoodsEntity.getGoodsName());
                }
            } else if (OmniOrderTypeEnum.PRODUCTION_ORDER.code().equals(customerMyOrderVO.getOrderType())) {
                //查出所有orderTyoe 60 产康定订单
                customerMyOrderVO.setGoodsName(orderProductionExtendRepository.getGoodsNameByOrderId(orderId));
            } else if (OmniOrderTypeEnum.OTHER_MONTH_ORDER.code().equals(customerMyOrderVO.getOrderType())) {
                //查出所有orderType 30 其他订单
                HeOrderOtherEntity heOrderOtherEntity = orderOtherRepository.getByOrderId(orderId);
                if (ObjectUtil.isNotEmpty(heOrderOtherEntity)) {
                    customerMyOrderVO.setGoodsName(heOrderOtherEntity.getGoodsName());
                }
            }
            WechatMyOrderVO orderEntity = orderRepository.getWechatOrderVOByOrderId(orderId);
            if (ObjectUtil.isNull(orderEntity)) {
                return Result.failed(ResultEnum.NOT_EXIST.getCode(), "订单不存在");
            }

            // 纸质合同
            List<ContractSignRecordPaperEntity> paperList = contractSignRecordPaperRepository.getByOrderId(orderId, ContractStatusEnum.SIGNED.code());
            // 签约记录表
            List<MonthContractSignRecordEntity> mainContractList = monthContractSignRecordRepository.getListByOrderId(Long.valueOf(orderId), ContractStatusEnum.SIGNED.code());
            //提前离馆列表
            List<AheadOutRoomEntity> aheadOutRoomEntities = aheadOutRoomRepository.queryByOrderIds(Arrays.asList(orderId));
            //查询合同解除协议
            List<MonthContractSignRecordEntity> byOrderIdListAndContractStatus = monthContractSignRecordRepository.getByOrderIdListAndContractStatus(Arrays.asList(orderId), TemplateContractTypeEnum.RELEASE.code(), ContractStatusEnum.SIGNED.code());
            //判断是否签署离馆协议
            if (ObjectUtil.isNotEmpty(byOrderIdListAndContractStatus)) {
                List<MonthContractSignRecordEntity> collect = byOrderIdListAndContractStatus.stream().filter(x -> x.getGuideId().equals(Long.valueOf(orderId))).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(collect)) {
                    orderEntity.setHasSignContractReleaseAgreement(collect.get(0).getContractStatus());
                }
            }
            //设置订单应该展示的按钮
            List<OrderOperateButton> wechatMyOrderButtons = setCustomerButton();
            customerMyOrderVO.setButtonList(wechatMyOrderButtons);
            customerMyOrderVO.setPayButton(setPayButton(orderEntity, mainContractList, paperList, aheadOutRoomEntities));
        }
        return Result.success(new PageVO(customerOrderList));
    }

    @Override
    public Result getOrderRoomTypeChangeRecord(Integer orderId) {
        List<HeOrderRoomTypeChangeRecordEntity> roomTypeChangeRecordEntityList = orderRoomTypeChangeRecordRepository.getByOrderId(orderId);
        HeOrderEntity orderInfo = orderRepository.getByOrderId(orderId);
        String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(orderInfo.getStoreId());

        Map<Long, List<HeOrderRoomTypeChangeRecordEntity>> collect = roomTypeChangeRecordEntityList.stream().collect(Collectors.groupingBy(HeOrderRoomTypeChangeRecordEntity::getAdditionalRevenueId));

        List<OrderRoomTypeChangeRecordVO> result = new ArrayList<>();


        List<Long> list = new ArrayList<>(collect.keySet());

        Collections.sort(list);

        int packIndex = 0;
        for (Long id : list) {
            List<HeOrderRoomTypeChangeRecordEntity> orderRoomTypeChangeRecordEntityList = collect.get(id);

            HeOrderRoomTypeChangeRecordEntity firstRecord = orderRoomTypeChangeRecordEntityList.get(0);
            HeOrderRoomTypeChangeRecordEntity lastRecord = orderRoomTypeChangeRecordEntityList.get(orderRoomTypeChangeRecordEntityList.size() - 1);

            int index = 0;
            for (HeOrderRoomTypeChangeRecordEntity heOrderRoomTypeChangeRecordEntity : orderRoomTypeChangeRecordEntityList) {
                OrderRoomTypeChangeRecordVO orderRoomTypeChangeRecordVO = new OrderRoomTypeChangeRecordVO();
                orderRoomTypeChangeRecordVO.setIndex(index);
                orderRoomTypeChangeRecordVO.setPackIndex(packIndex);
                orderRoomTypeChangeRecordVO.setLength(orderRoomTypeChangeRecordEntityList.size());
                String finalRoomTypeChangeCycle = lastRecord.getFinalRoomTypeChangeCycle();
                if (StringUtils.isEmpty(finalRoomTypeChangeCycle) || "[]".equals(finalRoomTypeChangeCycle)) {
                    orderRoomTypeChangeRecordVO.setFinalDurationOfStayChanged("/");
                } else {
                    List<Integer> read = JsonUtil.read(finalRoomTypeChangeCycle, List.class);
                    List<String> strings = ListUtils.rangeList(read);
                    orderRoomTypeChangeRecordVO.setFinalDurationOfStayChanged(strings.toString().replace("[", "").replace("]", ""));
                }
                orderRoomTypeChangeRecordVO.setFinalOriginalRoomType(firstRecord.getOriginalRoomName());
                orderRoomTypeChangeRecordVO.setFinalNewRoomType(lastRecord.getNewRoomName());
                orderRoomTypeChangeRecordVO.setFinalNumberOfDaysChanged(lastRecord.getFinalDays().toString());


                if (lastRecord.getFinalChangeOriginalPrice().compareTo(BigDecimal.ZERO) >= 0) {
                    orderRoomTypeChangeRecordVO.setFinalAmountChanged(storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(lastRecord.getFinalChangeOriginalPrice()));
                } else {
                    orderRoomTypeChangeRecordVO.setFinalAmountChanged("-" + storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(lastRecord.getFinalChangeOriginalPrice()).replaceAll("-", ""));
                }

                if (lastRecord.getFinalChangePrice().compareTo(BigDecimal.ZERO) >= 0) {
                    orderRoomTypeChangeRecordVO.setFinalSignedAmountChanged(storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(lastRecord.getFinalChangePrice()));
                } else {
                    orderRoomTypeChangeRecordVO.setFinalSignedAmountChanged("-" + storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(lastRecord.getFinalChangePrice()).replaceAll("-", ""));
                }

                String roomTypeChangeCycle = heOrderRoomTypeChangeRecordEntity.getRoomTypeChangeCycle();
                if (StringUtils.isEmpty(roomTypeChangeCycle) || "[]".equals(roomTypeChangeCycle)) {
                    orderRoomTypeChangeRecordVO.setDurationOfStayChanged("/");
                } else {
                    List<Integer> read = JsonUtil.read(roomTypeChangeCycle, List.class);
                    List<String> strings = ListUtils.rangeList(read);
                    orderRoomTypeChangeRecordVO.setDurationOfStayChanged(strings.toString().replace("[", "").replace("]", ""));
                }
                orderRoomTypeChangeRecordVO.setOriginalRoomType(heOrderRoomTypeChangeRecordEntity.getOriginalRoomName());
                orderRoomTypeChangeRecordVO.setNewRoomType(heOrderRoomTypeChangeRecordEntity.getNewRoomName());
                Integer days = heOrderRoomTypeChangeRecordEntity.getDays();
                orderRoomTypeChangeRecordVO.setNumberOfDaysChanged(days > 0 ? "+" + days : days.toString());
                BigDecimal changeOriginalPrice = heOrderRoomTypeChangeRecordEntity.getChangeOriginalPrice();
                String changeOriginalPriceStr = RMBUtils.formatToseparaDecimalsNew(changeOriginalPrice);

                if (changeOriginalPrice.compareTo(BigDecimal.ZERO) > 0) {
                    orderRoomTypeChangeRecordVO.setAmountChanged("+" + storeCurrencyCode + changeOriginalPriceStr);
                } else if (changeOriginalPrice.compareTo(BigDecimal.ZERO) == 0) {
                    orderRoomTypeChangeRecordVO.setAmountChanged(storeCurrencyCode + changeOriginalPriceStr);
                } else {
                    orderRoomTypeChangeRecordVO.setAmountChanged("-" + storeCurrencyCode + changeOriginalPriceStr.replaceAll("-", ""));
                }

                BigDecimal changePrice = heOrderRoomTypeChangeRecordEntity.getChangePrice();
                String changePriceStr = RMBUtils.formatToseparaDecimalsNew(changePrice);

                if (changePrice.compareTo(BigDecimal.ZERO) > 0) {
                    orderRoomTypeChangeRecordVO.setSignedAmountChanged("+" + storeCurrencyCode + changePriceStr);
                } else if (changePrice.compareTo(BigDecimal.ZERO) == 0) {
                    orderRoomTypeChangeRecordVO.setSignedAmountChanged(storeCurrencyCode + changePriceStr);
                } else {
                    orderRoomTypeChangeRecordVO.setSignedAmountChanged("-" + storeCurrencyCode + changePriceStr.replaceAll("-", ""));
                }

                orderRoomTypeChangeRecordVO.setChangeTime(DateUtil.formatDateTime(heOrderRoomTypeChangeRecordEntity.getGmtCreate()));
                orderRoomTypeChangeRecordVO.setChanger(heOrderRoomTypeChangeRecordEntity.getOptName());
                orderRoomTypeChangeRecordVO.setOrderId(heOrderRoomTypeChangeRecordEntity.getOrderId().intValue());
                orderRoomTypeChangeRecordVO.setAdditionalRevenueId(heOrderRoomTypeChangeRecordEntity.getAdditionalRevenueId());
                result.add(orderRoomTypeChangeRecordVO);
                index++;
            }
            packIndex++;
        }
        return Result.success(result);
    }

    /**
     * 获取订单折扣审批状态
     *
     * @param orderId
     * @return
     */
    @Override
    public Result<Integer> getDiscountStatusByOrderId(Integer orderId) {
        HeOrderEntity orderInfo = orderRepository.getByOrderId(orderId);
        if (ObjectUtil.isEmpty(orderInfo)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "订单不存在,订单ID=" + orderId);
        }
        return Result.success(orderInfo.getApprovalDiscountStatus());
    }

    @Override
    public PageVO<DepositOrderRecordVO> pageDepositOrderRecord(DepositOrderQuery req) {

        PageVO pageVO = new PageVO<>();

        List<DepositOrderRecordVO> recordVOS = new ArrayList<>();

        Integer clientId = Integer.valueOf(req.getClientUid());
        TabClientEntity tabClientById = clientRepository.getTabClientById(clientId);
        if (ObjectUtil.isEmpty(tabClientById)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "客户不存在");
        }
        Integer storeId = tabClientById.getStoreId();

        //门店信息
        CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(storeId);
        if (ObjectUtil.isEmpty(cfgStoreEntity)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "门店不存在");
        }
        List<HeIncomeRecordEntity> recordListByClientUid = incomeRecordRepository.getRecordListByClientUid(clientId, IncomeReceiptTypeEnum.RECORD_RECEIPT_TYPE_DEPOSIT.code());
        recordVOS.addAll(incomeRecordConverter.entityList2DepositOrderRecordVO(recordListByClientUid));

        //押金已退金额
        List<HeOrderRefundEntity> refundByOrderIncomeIdList = orderRefundRepository.getRefundSuccessByOrderIncomeIdList(recordListByClientUid.stream().map(HeIncomeRecordEntity::getId).collect(Collectors.toList()));
        //判断list中存在拒绝退款的记录，增加一条发起审批的退款记录
        List<HeOrderRefundEntity> refundByOrderIncomeIdList1 = refundByOrderIncomeIdList.stream().filter(item -> item.getStatus() == 2 || item.getStatus() == 5).collect(Collectors.toList());
        List<HeOrderRefundEntity> heOrderRefundEntities = BeanMapper.mapList(refundByOrderIncomeIdList1, HeOrderRefundEntity.class);
        if (CollectionUtils.isNotEmpty(heOrderRefundEntities)) {
            heOrderRefundEntities.forEach(item -> {
                item.setStatus(1);
            });
            recordVOS.addAll(orderRefundConverter.entityList2DepositOrderRecordVO(heOrderRefundEntities));
        }
        recordVOS.addAll(orderRefundConverter.entityList2DepositOrderRecordVO(refundByOrderIncomeIdList));

        //根据创建时间正序排列
        recordVOS.sort(Comparator.comparing(DepositOrderRecordVO::getGmtCreate));

        //根据加减正序算出来余额
        BigDecimal balance = BigDecimal.ZERO;
        for (DepositOrderRecordVO recordVO : recordVOS) {
            if ("+".equals(recordVO.getTradeAffect())) {
                balance = balance.add(recordVO.getAmount());
            } else {
                balance = balance.subtract(recordVO.getAmount());
            }
            recordVO.setBalanceAfter(RMBUtils.formatToseparaDecimals(balance));
        }
        //根据创建时间倒序排列
        recordVOS.sort(Comparator.comparing(DepositOrderRecordVO::getGmtCreate).reversed());
        recordVOS = recordVOS.stream().filter(item -> req.getTradeTypes().contains(item.getTradeType())).collect(Collectors.toList());
        pageVO.setPageNo(1);
        pageVO.setList(recordVOS);
        pageVO.setPageSize(recordVOS.size());
        pageVO.setTotalPage(recordVOS.size());
        return pageVO;
    }

    @Override
    public Result<List<OrderLabelVO>> getOrderLabelByRole() {
//        Result<List<OrderLabelVO>> orderLabel = getOrderLabel(scene);
//        //过滤掉特殊标签
//        ExecuteRuleV2Req req = new ExecuteRuleV2Req();
//        DiscountTagFact discountTagFact = new DiscountTagFact();
//        discountTagFact.setConfigCode("tag_flag_info");
//        req.setSceneCode(RuleLinkClient.ORDER_DISCOUNT_CONFIG);
//        req.setFactObj(discountTagFact);
//        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(req);
//        if (ObjectUtil.isEmpty(hitRuleVo)) {
//            return Result.success(orderLabel.getData());
//        }
//        String simpleRuleValue = hitRuleVo.getSimpleRuleValue();
//        //特殊标签集合
//        List<Long> specialTagList = new ArrayList<>();
//        if (StringUtils.isNotBlank(simpleRuleValue)) {
//            specialTagList = Arrays.stream(simpleRuleValue.split(",")).map(Long::valueOf).collect(Collectors.toList());
//        }
//        log.info("特殊标签集合:{}", specialTagList);
//
//        List<OrderLabelVO> data = orderLabel.getData();
//        //获取当前用户角色
//        final UserTokenInfoDTO jwtTokenUserInfo = JwtUtil.getJwtTokenUserInfo();
//        List<Long> roleIds = jwtTokenUserInfo.getRoleIds();
//        if (CollectionUtils.isEmpty(roleIds)) {
//            return Result.success(null);
//        }
//        List<SysRolePO> sysRolePOS = sysRoleService.listByIds(roleIds);
//        //转换成Integer类型idList
//        List<Integer> roleIdList = sysRolePOS.stream().map(o -> o.getId().intValue()).collect(Collectors.toList());
//        BatchExecuteRuleReq req1 = new BatchExecuteRuleReq();
//        List<ExecuteRuleV2Req> iterm = new ArrayList<>();
//        for (Integer roleId : roleIdList) {
//            ExecuteRuleV2Req roleIdLabel = new ExecuteRuleV2Req();
//            DiscountTagFact roleIdLabelFact = new DiscountTagFact();
//            roleIdLabelFact.setRoleId(roleId);
//            roleIdLabel.setSceneCode(RuleLinkClient.ORDER_DISCOUNT_CONFIG);
//            roleIdLabel.setFactObj(roleIdLabelFact);
//            iterm.add(roleIdLabel);
//        }
//        req1.setIterm(iterm);
//        List<HitRuleVo> hitRuleVos = ruleLinkClient.batchHitRule(req1);
//
//        //这个角色拥有的特殊标签集合
//        Set<Long> roleIdLabelList = new HashSet<>();
//        if (CollectionUtils.isNotEmpty(hitRuleVos)) {
//            List<String> simpleValues = hitRuleVos.stream().filter(Objects::nonNull).map(HitRuleVo::getSimpleRuleValue).filter(Objects::nonNull).collect(Collectors.toList());
//            for (String simpleValue : simpleValues) {
//                if (StringUtils.isNotBlank(simpleValue)) {
//                    roleIdLabelList.addAll(Arrays.stream(simpleValue.split(",")).map(Long::valueOf).collect(Collectors.toList()));
//                }
//            }
//        }
//        log.info("角色id列表:{},角色拥有的特殊标签集合:{}", roleIdList, roleIdLabelList);
//        List<OrderLabelVO> orderLabelResult = new ArrayList<>();
//        //过滤掉特殊标签
//        for (OrderLabelVO datum : data) {
//            Long id = datum.getId().longValue();
//            if (specialTagList.contains(id)) {
//                if (roleIdLabelList.contains(id)) {
//                    orderLabelResult.add(datum);
//                }
//                continue;
//            }
//            orderLabelResult.add(datum);
//        }
//        return Result.success(orderLabelResult);
        return null;
    }

    /**
     * 已经废弃
     *
     * 获取门店类型
     *
     * @param storeId
     * @return
     */
    /*private OrderMonthStoreTypeCostEnum getStoreType(Integer storeId) {
        //获取门店类型
        CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(storeId);

        //判断门店品牌
        Integer storeType = cfgStoreEntity.getType();

        OrderMonthStoreTypeCostEnum orderMonthStoreTypeCostEnum = null;

        if (storeType == 0) {
            orderMonthStoreTypeCostEnum = OrderMonthStoreTypeCostEnum.SAINT_BELLA;
        } else if (storeType == 1) {
            Integer childType = cfgStoreEntity.getChildType();
            if (ObjectUtil.isNotEmpty(childType) && childType == 0) {
                orderMonthStoreTypeCostEnum = OrderMonthStoreTypeCostEnum.DELUXE;
            } else {
                orderMonthStoreTypeCostEnum = OrderMonthStoreTypeCostEnum.BABY_BELLA;
            }
        }
        return orderMonthStoreTypeCostEnum;
    }*/

    /**
     * 我的订单列表
     *
     * @param query
     * @return
     */
    @Override
    public Result<PageVO<WechatMyOrderVO>> getMyOrder(WechatMyOrderQuery query) {

        if (StringUtils.isNotBlank(query.getKeyword())) {
            List<TabClientEntity> tabClientList = clientRepository.getTabClientByNameAndPhone(query.getKeyword());
            List<Integer> clientIdList = CollectionUtils.isNotEmpty(tabClientList) ? tabClientList.stream().map(TabClientEntity::getId).collect(Collectors.toList()) : ListUtil.toList(-1);
            query.setClientIdList(clientIdList);
        }

        Page<WechatMyOrderVO> orderPage = orderRepository.getMyOrder(query);
        if (CollectionUtils.isEmpty(orderPage.getRecords())) {
            return Result.success(new PageVO(orderPage));
        }

        List<Integer> orderIdList = orderPage.getRecords().stream().map(WechatMyOrderVO::getOrderId).collect(Collectors.toList());

        List<HeOrderGoodsEntity> byOrderIdList = orderGoodsRepository.getByOrderIdList(orderIdList);

        for (WechatMyOrderVO record : orderPage.getRecords()) {
            List<HeOrderGoodsEntity> collect = byOrderIdList.stream().filter(o -> o.getOrderId().equals(record.getOrderId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect) && ObjectUtil.isNotEmpty(collect.get(0).getGoodsName())) {
                record.setGoodsName(collect.get(0).getGoodsName());
            }
        }

        List<String> orderIdListStr = orderPage.getRecords().stream().map(m -> m.getOrderId().toString()).collect(Collectors.toList());

        // 退款记录
        List<HeOrderRefundEntity> refundByOrderIdList = orderRefundRepository.getRefundByOrderIdList(orderIdList);

        // 钉钉审批记录
        List<OaProcessIdRelationPO> approveRecordListByOrderIdList = oaProcessIdRelationService.getByOrderIdList(orderIdListStr);

        // 收款记录
        List<HeIncomeRecordEntity> incomeRecordByOrderIdList = incomeRecordRepository.getRecordListByOrderIdList(orderIdList, null, null);

        // 签约记录表
        List<MonthContractSignRecordEntity> contractSignRecordList = monthContractSignRecordRepository.getByOrderIdList(orderIdList.stream().map(Long::valueOf).collect(Collectors.toList()));
        // 合同补充协议
        List<MonthContractSignAgreementEntity> signAgreementList = monthContractSignAgreementRepository.getByOrderIdList(orderIdList.stream().map(Long::valueOf).collect(Collectors.toList()));
        // 纸质合同
        List<ContractSignRecordPaperEntity> SignRecordPaperList = contractSignRecordPaperRepository.getByOrderIdList(orderIdList);
        //提前退管列表
        List<AheadOutRoomEntity> aheadOutRoomEntities = aheadOutRoomRepository.queryByOrderIds(orderIdList);
        //查询合同解除协议
        List<MonthContractSignRecordEntity> byOrderIdListAndContractStatus = monthContractSignRecordRepository.getByOrderIdListAndContractStatus(orderIdList, TemplateContractTypeEnum.RELEASE.code(), ContractStatusEnum.SIGNED.code());
        Map<Integer, List<HeOrderRefundEntity>> refundMap = refundByOrderIdList.stream().collect(Collectors.groupingBy(HeOrderRefundEntity::getOrderId));
        Map<String, List<OaProcessIdRelationPO>> approveMap = approveRecordListByOrderIdList.stream().collect(Collectors.groupingBy(OaProcessIdRelationPO::getOrderId));
        Map<Integer, List<HeIncomeRecordEntity>> incomeRecordMap = incomeRecordByOrderIdList.stream().collect(Collectors.groupingBy(HeIncomeRecordEntity::getOrderId));
        Map<Long, List<MonthContractSignRecordEntity>> contractSignRecordMap = contractSignRecordList.stream().collect(Collectors.groupingBy(MonthContractSignRecordEntity::getGuideId));
        Map<Long, List<MonthContractSignAgreementEntity>> signAgreementMap = signAgreementList.stream().collect(Collectors.groupingBy(MonthContractSignAgreementEntity::getOrderId));
        Map<Integer, List<ContractSignRecordPaperEntity>> SignRecordPaperMap = SignRecordPaperList.stream().collect(Collectors.groupingBy(ContractSignRecordPaperEntity::getOrderId));
        Map<Integer, List<AheadOutRoomEntity>> integerListMap = aheadOutRoomEntities.stream().collect(Collectors.groupingBy(AheadOutRoomEntity::getOrderId));


        for (WechatMyOrderVO record : orderPage.getRecords()) {

            String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(record.getStoreId());
            record.setCurrency(storeCurrencyCode);

            //判断是否签署离馆协议
            if (ObjectUtil.isNotEmpty(byOrderIdListAndContractStatus)) {
                List<MonthContractSignRecordEntity> collect = byOrderIdListAndContractStatus.stream().filter(x -> x.getGuideId().equals(Long.valueOf(record.getOrderId()))).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(collect)) {
                    record.setHasSignContractReleaseAgreement(collect.get(0).getContractStatus());
                }
            }
            List<OrderOperateButton> wechatMyOrderButtons = setButton(record, refundMap.get(record.getOrderId()), approveMap.get(record.getOrderId().toString()), incomeRecordMap.get(record.getOrderId()), contractSignRecordMap.get(record.getOrderId().longValue()), signAgreementMap.get(record.getOrderId().longValue()), SignRecordPaperMap.get(record.getOrderId()), integerListMap.get(record.getOrderId()));
            record.setButtonList(wechatMyOrderButtons);
        }
        return Result.success(new PageVO(orderPage));
    }

    private ClientOrderDetailVO.Label getLabel(String label, String value, List<ClientOrderDetailVO.Label> list) {
        ClientOrderDetailVO.Label result = new ClientOrderDetailVO.Label();
        result.setKey(label);
        result.setValue(value);
        result.setList(list);
        return result;
    }

    /**
     * 我的订单详情
     *
     * @param orderId
     * @return
     */
    @Override
    public Result<ClientOrderDetailVO> queryOrderDetailsByClient(Integer orderId) {
        ClientOrderDetailVO result = new ClientOrderDetailVO();

        // 订单基本信息
        HeOrderEntity heOrder = orderRepository.getByOrderId(orderId);
        if (Objects.isNull(heOrder)) {
            return Result.success(result);
        }
        result.setExtraInfo(heOrder.getExtraInfo());
        String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(heOrder.getStoreId());
        result.setCurrency(storeCurrencyCode);
        CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(heOrder.getStoreId());
        HeOrderGoodsEntity heOrderGoodsEntity = orderGoodsRepository.getByOrderId(heOrder.getOrderId());
        ClientOrderDetailVO.OrderInfo orderInfo = new ClientOrderDetailVO.OrderInfo();
        orderInfo.setOrderId(heOrder.getOrderId());
        orderInfo.setOrderSn(heOrder.getOrderSn());
        orderInfo.setClientUid(heOrder.getClientUid());
        orderInfo.setBasicUid(heOrder.getBasicUid());
        orderInfo.setStoreId(heOrder.getStoreId());
        orderInfo.setCurrency(storeCurrencyCode);
        orderInfo.setPaidAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(heOrder.getRealAmount())));
        orderInfo.setUnpaidAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(heOrder.getPayAmount() - heOrder.getRealAmount())));
        orderInfo.setOrderAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(heOrder.getOrderAmount())));
        orderInfo.setPayAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(heOrder.getPayAmount())));
        orderInfo.setPayStatus(PayStatusV2Enum.getName(heOrder.getPayStatus()));
        orderInfo.setStoreName(Objects.nonNull(cfgStoreEntity) ? cfgStoreEntity.getStoreName() : null);

        List<StoreGoodsSkuModel> orderGoodsName = goodsRepository.getOrderGoodsName(new ArrayList<>(orderId));
        if (CollectionUtils.isNotEmpty(orderGoodsName) && orderGoodsName.get(0).checkGoodsAndSku()) {
            orderInfo.setGoodsName(orderGoodsName.get(0).getAllGoodsName());
        }
        if (StringUtils.isEmpty(orderInfo.getGoodsName())) {
            orderInfo.setGoodsName(Objects.nonNull(heOrderGoodsEntity) ? heOrderGoodsEntity.getGoodsName() : null);
        }

        //获取订单是否有签合同
        // 签约记录表
        List<MonthContractSignRecordEntity> contractSignRecordList = monthContractSignRecordRepository.getByOrderIdList(Arrays.asList(heOrder.getOrderId().longValue()));
        // 合同补充协议
        List<MonthContractSignAgreementEntity> signAgreementList = monthContractSignAgreementRepository.getByOrderIdList(Arrays.asList(heOrder.getOrderId().longValue()));
        // 纸质合同
        List<ContractSignRecordPaperEntity> signRecordPaperList = contractSignRecordPaperRepository.getByOrderIdList(Arrays.asList(heOrder.getOrderId()));
        orderInfo.setIsContract(BooleanUtils.toInteger(CollectionUtils.isNotEmpty(contractSignRecordList) || CollectionUtils.isNotEmpty(signAgreementList) || CollectionUtils.isNotEmpty(signRecordPaperList)));
        //获取订单是否签署合同解除协议


        result.setOrderInfo(orderInfo);


        // 折扣数据
        ClientOrderDetailVO.DiscountsDetail discountsDetail = new ClientOrderDetailVO.DiscountsDetail();
        String disCountDetail = heOrder.getDiscountDetails();
        if (StringUtils.isNotBlank(disCountDetail)) {
            OrderDiscountsCacheVO discountsCache = JSONObject.toJavaObject(JSON.parseObject(disCountDetail), OrderDiscountsCacheVO.class);

            List<ClientOrderDetailVO.Label> discountsDetailList = new ArrayList<>();
            discountsDetailList.add(getLabel("套餐费用原价", RMBUtils.formatToseparaDecimals(discountsCache.getAmountReceivablePackageOriginalPrice()), null));

            BigDecimal specialTotal = BigDecimalUtil.add(discountsCache.getAmountReceivableHolidaysCurrentPrice(), discountsCache.getAmountReceivableMultipleBirthsCurrentPrice(), discountsCache.getAmountReceivableContinuedResidenceCurrentPrice(), discountsCache.getChangeRoomTypeReceivableAmountCurrentPrice());

            BigDecimal originalPriceTotal = BigDecimalUtil.add(discountsCache.getAmountReceivableMultipleBirthsOriginalPrice(), discountsCache.getAmountReceivableHolidaysOriginalPrice(), discountsCache.getAmountReceivableContinuedResidenceOriginalPrice(), discountsCache.getChangeRoomTypeReceivableAmountOriginalPrice());

            List<ClientOrderDetailVO.Label> specialList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(discountsCache.getAmountReceivableMultipleBirthsOriginalPrice()) && discountsCache.getAmountReceivableMultipleBirthsOriginalPrice().compareTo(BigDecimal.ZERO) > 0) {
                specialList.add(getLabel("多胞胎费用原价", RMBUtils.formatToseparaDecimals(discountsCache.getAmountReceivableMultipleBirthsOriginalPrice()), null));
            }
            if (ObjectUtil.isNotEmpty(discountsCache.getAmountReceivableHolidaysOriginalPrice()) && discountsCache.getAmountReceivableHolidaysOriginalPrice().compareTo(BigDecimal.ZERO) > 0) {
                specialList.add(getLabel("节日费原价", RMBUtils.formatToseparaDecimals(discountsCache.getAmountReceivableHolidaysOriginalPrice()), null));
            }
            if (ObjectUtil.isNotEmpty(discountsCache.getAmountReceivableContinuedResidenceOriginalPrice()) && discountsCache.getAmountReceivableContinuedResidenceOriginalPrice().compareTo(BigDecimal.ZERO) > 0) {
                specialList.add(getLabel("续住费用原价", RMBUtils.formatToseparaDecimals(discountsCache.getAmountReceivableContinuedResidenceOriginalPrice()), null));
            }
            if (ObjectUtil.isNotEmpty(discountsCache.getChangeRoomTypeReceivableAmountOriginalPrice()) /*&& discountsCache.getChangeRoomTypeReceivableAmountOriginalPrice().compareTo(BigDecimal.ZERO) > 0*/) {
                specialList.add(getLabel("变更房型费用原价", RMBUtils.formatToseparaDecimals(discountsCache.getChangeRoomTypeReceivableAmountOriginalPrice()), null));
            }
            if (ObjectUtil.isNotEmpty(originalPriceTotal) /*&& originalPriceTotal.compareTo(BigDecimal.ZERO) > 0*/) {
                discountsDetailList.add(getLabel("特殊费用原价", RMBUtils.formatToseparaDecimals(originalPriceTotal), specialList));
            }

            BigDecimal receTotal = BigDecimalUtil.add(discountsCache.getAmountReceivablePackageOriginalPrice(), discountsCache.getAmountReceivableHolidaysOriginalPrice(), discountsCache.getAmountReceivableMultipleBirthsOriginalPrice(), discountsCache.getAmountReceivableContinuedResidenceOriginalPrice(), discountsCache.getChangeRoomTypeReceivableAmountOriginalPrice());
            BigDecimal realTotal = BigDecimalUtil.add(specialTotal, discountsCache.getAmountReceivablePackageCurrentPrice());
            discountsDetailList.add(getLabel("折扣减免", RMBUtils.formatToseparaDecimals(receTotal.subtract(realTotal)), null));

            result.setDiscountsDetail(discountsDetailList);

        }
        String deposit = Objects.nonNull(cfgStoreEntity) && Objects.nonNull(cfgStoreEntity.getDeposit()) ? RMBUtils.formatToseparaDecimals(new BigDecimal(cfgStoreEntity.getDeposit()).divide(new BigDecimal(100))) : "0.00";
        result.setDeposit(deposit);


        // 收款信息
        List<HeIncomeRecordEntity> incomeRecordEntities = incomeRecordRepository.getSuccessfulRecordListByOrderId(heOrder.getOrderId());
        List<ClientOrderDetailVO.IncomeRecord> incomeRecordList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(incomeRecordEntities)) {
            for (HeIncomeRecordEntity recordEntity : incomeRecordEntities) {
                ClientOrderDetailVO.IncomeRecord incomeRecord = new ClientOrderDetailVO.IncomeRecord();
                incomeRecord.setIncomeSn(recordEntity.getIncomeSn());
                incomeRecord.setCurrency(storeCurrencyCode);
                incomeRecord.setIncome(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(recordEntity.getIncome())));
                incomeRecord.setCreateDate(Objects.nonNull(recordEntity.getCreatedAt()) ? new Date(recordEntity.getCreatedAt() * 1000) : null);
                incomeRecord.setPayTypeName(OmniPayTypeEnum.getName(recordEntity.getPayType()));
                incomeRecord.setStatusName(IncomeRecordPayStatusEnum.getName(recordEntity.getStatus()));
                incomeRecord.setPayType(recordEntity.getPayType());
                incomeRecord.setStatus(recordEntity.getStatus());
                incomeRecordList.add(incomeRecord);
            }
        }
        result.setIncomeRecordList(incomeRecordList.stream().sorted(Comparator.comparing(ClientOrderDetailVO.IncomeRecord::getCreateDate).reversed()).collect(Collectors.toList()));


        // 退款记录
        List<HeOrderRefundEntity> refundByOrderIdList = orderRefundRepository.getRefundByOrderIdList(ListUtil.toList(heOrder.getOrderId()));
        List<ClientOrderDetailVO.IncomeRecord> refundList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(refundByOrderIdList)) {
            for (HeOrderRefundEntity recordEntity : refundByOrderIdList) {
                ClientOrderDetailVO.IncomeRecord incomeRecord = new ClientOrderDetailVO.IncomeRecord();
                incomeRecord.setIncomeSn(recordEntity.getRefundOrderSn());
                incomeRecord.setIncome(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(recordEntity.getApplyAmount())));
                incomeRecord.setCreateDate(Objects.nonNull(recordEntity.getCreatedAt()) ? new Date(recordEntity.getCreatedAt() * 1000) : null);
                incomeRecord.setPayTypeName(OmniPayTypeEnum.getName(recordEntity.getRefundType()));
                //非线下支付且状态是审批通过
                if (!OmniPayTypeEnum.OFFLINE.getCode().equals(recordEntity.getRefundType()) && RefundRecordPayStatusEnum.REFUND_RECORD_3.getCode().equals(recordEntity.getStatus())) {
                    incomeRecord.setStatusName("退款中...");
                } else {
                    incomeRecord.setStatusName(RefundRecordPayStatusEnum.getName(recordEntity.getStatus()));
                }
                incomeRecord.setPayType(recordEntity.getRefundType());
                incomeRecord.setStatus(recordEntity.getStatus());
                incomeRecord.setCurrency(storeCurrencyCode);
                refundList.add(incomeRecord);
            }
        }
        result.setRefundList(refundList.stream().sorted(Comparator.comparing(ClientOrderDetailVO.IncomeRecord::getCreateDate).reversed()).collect(Collectors.toList()));


        // 额外礼赠明细
        List<OrderGiftExtendEntity> giftExtendList = orderGiftExtendRepository.getByOrderId(heOrder.getOrderId());
        if (CollectionUtils.isNotEmpty(giftExtendList)) {
            List<ClientOrderDetailVO.OrderGiftExtend> detailGiftList = new ArrayList<>();
            int sum = 0;
            for (OrderGiftExtendEntity extend : giftExtendList) {
                ClientOrderDetailVO.OrderGiftExtend orderGiftExtend = new ClientOrderDetailVO.OrderGiftExtend();
                if (extend.getType().equals(OrderGiftExtendTypeEnum.SANKANGJIN.code())) {
                    orderGiftExtend.setGoodsName("产康金");
                    orderGiftExtend.setGoodsNum(extend.getGoodsNum());
                    orderGiftExtend.setGoodsNum(1);
                    sum += extend.getPrice();
                } else {
                    orderGiftExtend.setGoodsName(extend.getGoodsName());
                    orderGiftExtend.setGoodsNum(extend.getGoodsNum());
                    sum += (extend.getPrice() * extend.getGoodsNum());
                }
                orderGiftExtend.setPrice(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(extend.getPrice())));
                orderGiftExtend.setCurrency(storeCurrencyCode);
                detailGiftList.add(orderGiftExtend);
            }
            result.setGiftExtendList(detailGiftList);
//            int sum = giftExtendList.stream().filter(item -> Objects.nonNull(item.getPrice())).mapToInt(OrderGiftExtendEntity::getPrice).sum();
            result.setGiftExtendPriceSum(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(sum)));
        }

        HeStayinEntity stayinEntity = stayinRepository.getByOrderId(orderId);
        if (ObjectUtil.isNotEmpty(stayinEntity)) {
            //订单入住信息
            ClientOrderStayinDetailVO clientOrderStayinDetailVO = BeanMapper.map(stayinEntity, ClientOrderStayinDetailVO.class);
            clientOrderStayinDetailVO.setCall(stayinEntity.getSalutation());
            clientOrderStayinDetailVO.setBirthTypeStr(BirthTypeEnum.getValueByCode(clientOrderStayinDetailVO.getBirthType()));
            result.setStayInfo(clientOrderStayinDetailVO);
            //订单宝宝信息
            List<ClientOrderBabyDetailVO> clientOrderBabyDetailVOList = new ArrayList<>();
            String babyList = stayinEntity.getBabyList();
            if (StringUtils.isNotEmpty(babyList)) {
                List<LinkedHashMap> read = com.stbella.order.common.utils.JsonUtil.read(babyList, List.class);
                for (LinkedHashMap linkedHashMap : read) {
                    ClientOrderBabyDetailVO clientOrderBabyDetailVO = new ClientOrderBabyDetailVO();
                    clientOrderBabyDetailVO.setName((String) linkedHashMap.get("name"));
                    Integer baby_gender = (Integer) linkedHashMap.get("baby_gender");
                    clientOrderBabyDetailVO.setBabyGender(baby_gender == 0 ? "女孩" : "男孩");
                    clientOrderBabyDetailVO.setBornWeight(linkedHashMap.get("born_weight") + "克");
                    clientOrderBabyDetailVO.setJaundiceValue(linkedHashMap.get("jaundice_value") + "mg/dl");
                    clientOrderBabyDetailVOList.add(clientOrderBabyDetailVO);
                }
                result.setBabyInfo(clientOrderBabyDetailVOList);
            }
        }
        //提前离馆信息
        result.setSimpleAheadOutRoomVO(getSimpleAheadOutRoomVO(orderId));
        //订单后台-订单详情-基本信息-合同信息
        List<STMOOrderBasicContractInfoVO> orderBasicContractInfoVOList = convertSTMOOrderBasicContractInfoVOList(signRecordPaperList, contractSignRecordList, signAgreementList);
        result.setOrderBasicContractInfoVOList(orderBasicContractInfoVOList);
        return Result.success(result);
    }

    private SimpleAheadOutRoomVO getSimpleAheadOutRoomVO(Integer orderId) {
        AheadOutRoomQuery query = new AheadOutRoomQuery();
        query.setOrderId(orderId);
        SimpleAheadOutRoomVO simpleAheadOutRoomVO = aheadOutRoomRepository.entity2SimpleAheadOutRoomVO(aheadOutRoomRepository.queryByOrderId(query));
        return simpleAheadOutRoomVO;
    }

    @Override
    public Result<List<OrderLabelVO>> getOrderLabel(Integer scene) {
        List<OrderLabelVO> result = new ArrayList<>();
        List<HeOrderTagsEntity> allOrderTags = tagsRepository.getAllOrderTags();
        if (CollectionUtils.isEmpty(allOrderTags)) {
            return Result.success(new ArrayList<>());
        }
        Map<String, String> factMap = new HashMap<>();
        factMap.put("configCode", "tag_flag_info");
        ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req("order_discount_config", factMap);
        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
        for (HeOrderTagsEntity allOrderTag : allOrderTags) {
            OrderLabelVO vo = new OrderLabelVO();
            vo.setId(allOrderTag.getId());
            vo.setName(allOrderTag.getTagName());
            if (Objects.nonNull(hitRuleVo) && StringUtils.isNotEmpty(hitRuleVo.getSimpleRuleValue())){
                vo.setMustFill(hitRuleVo.getSimpleRuleValue().contains(allOrderTag.getId().toString()) ? Boolean.TRUE : Boolean.FALSE);
            }
            result.add(vo);
        }

        //过滤特殊场景标签显示
        if (Objects.nonNull(scene)) {
            Map<String, Integer> factAsYouWishMap = new HashMap<>();
            factAsYouWishMap.put("scene", scene);
            ExecuteRuleV2Req factAsYouWishRuleV2Req = new ExecuteRuleV2Req("order_discount_config", factAsYouWishMap);
            HitRuleVo hitRuleVo1 = ruleLinkClient.hitOneRule(factAsYouWishRuleV2Req);
            if (Objects.nonNull(hitRuleVo1) && StringUtils.isNotEmpty(hitRuleVo1.getSimpleRuleValue())) {
                List<Integer> specialTagList = Arrays.stream(hitRuleVo1.getSimpleRuleValue().split(",")).map(Integer::valueOf).collect(Collectors.toList());
                result = result.stream().filter(item -> specialTagList.contains(item.getId())).collect(Collectors.toList());
            }
        }

        return Result.success(result);
    }

    @Override
    public Result<OrderOtherInfoCacheVO> getOrderOtherInfoCache(OrderCacheBaseReq req) {
        return Result.success(orderRepository.getOrderOtherInfoCache(req));
    }

    @Override
    public OrderInfoByOrderVO getOrderInfoByOrderId(Integer orderId) {
        return getOrderInfoByOrderIdV2(orderId, OrderProductionVerificationStateEnum.PENDING_WRITE_OFF);
    }

    @Override
    public OrderInfoByOrderVO getOrderInfoByOrderIdV2(Integer orderId, OrderProductionVerificationStateEnum orderProductionVerificationStateEnum) {
        OrderInfoByOrderVO result = new OrderInfoByOrderVO();
        //获取订单主表数据
        HeOrderEntity orderInfo = orderRepository.getByOrderId(orderId);

        result.setExtraInfo(orderInfo.getExtraInfo());

        String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(orderInfo.getStoreId());
        result.setCurrency(storeCurrencyCode);

        OrderMonthClientVO orderMonthClientVO = getOrderCustomerInfoByOrderId(orderId);
        OrderMonthClientBailorVO orderMonthClientBailorVO = getOrderBailorInfoByOrderId(orderId);
        //订单入住状态
        result.setOrderRoomStatus(orderInfo.getOrderStatus());
        //客户信息
        result.setOrderMonthClientVO(orderMonthClientVO);
        //委托人信息
        result.setOrderMonthClientBailorVO(orderMonthClientBailorVO);
        //获取订单套餐数据
        HeOrderGoodsEntity orderGoodsInfo = orderGoodsRepository.getByOrderId(orderId);
        //获取订单额外礼赠数据（获取未核销的）
        List<OrderGiftExtendEntity> orderGiftInfoList = orderGiftExtendRepository.getByOrderId(orderId, ObjectUtil.isEmpty(orderProductionVerificationStateEnum) ? null : orderProductionVerificationStateEnum.code());
        //获取订单加收项数据
        List<HeOrderAdditionalRevenueEntity> orderAdditionalRevenueInfoList = orderAdditionalRevenueRepository.getByOrderId(orderId);
        //获取凭证
        List<HeOrderVoucherEntity> byOrderId = orderVoucherRepository.getByOrderId(orderId);


        //额外礼赠数据合并
        List<OrderGiftExtendEntity> afterMergeSkuOrderGiftInfoList = new ArrayList<>();

        Map<Integer, List<OrderGiftExtendEntity>> collect1 = orderGiftInfoList.stream().collect(Collectors.groupingBy(OrderGiftExtendEntity::getSkuId));

        for (Integer skuId : collect1.keySet()) {
            List<OrderGiftExtendEntity> orderGiftExtendEntities = collect1.get(skuId);

            if (orderGiftExtendEntities.size() == 1 && orderGiftExtendEntities.get(0).getType().equals(1)) {
                afterMergeSkuOrderGiftInfoList.addAll(orderGiftExtendEntities);
            } else {
                //非产康金需要根据规格数量进行合并
                Integer skuNum = orderGiftExtendEntities.get(0).getSkuNum();
                skuNum = ObjectUtil.isEmpty(skuNum) ? 1 : skuNum;
                //最终下单数量，老数据默认是1
                Integer goodsNum = orderGiftExtendEntities.size() / skuNum;
                afterMergeSkuOrderGiftInfoList.addAll(orderGiftExtendEntities.subList(0, goodsNum));
            }
        }
        orderGiftInfoList = afterMergeSkuOrderGiftInfoList;


        //额外礼赠数据合并
        List<OrderGiftExtendEntity> afterMergeOrderGiftInfoList = new ArrayList<>();

        for (OrderGiftExtendEntity o : orderGiftInfoList) {
            Integer type = o.getType();
            switch (OrderGiftExtendTypeEnum.of(type)) {
                case INDUSTRIAL_HEALTH_SERVICE:
                    Integer skuId = o.getSkuId();
                    Optional<OrderGiftExtendEntity> first = afterMergeOrderGiftInfoList.stream().filter(a -> Objects.equals(a.getSkuId(), skuId)).findFirst();
                    if (!first.isPresent()) {
                        List<OrderGiftExtendEntity> collect = orderGiftInfoList.stream().filter(a -> Objects.equals(a.getSkuId(), skuId)).collect(Collectors.toList());
                        o.setGoodsNum(collect.size());
                        afterMergeOrderGiftInfoList.add(o);
                    }
                    break;
                case SANKANGJIN:
                    o.setGoodsId(null);
                    o.setSkuId(null);
                    afterMergeOrderGiftInfoList.add(o);
                    break;
                default:
                    afterMergeOrderGiftInfoList.add(o);
                    break;
            }
        }
        OrderInfoByGoodsVO orderInfoByGoodsVO = new OrderInfoByGoodsVO();
        //判断如果是产康或者其他订单
        if (orderInfo.getOrderType().equals(OmniOrderTypeEnum.OTHER_MONTH_ORDER.getCode()) || orderInfo.getOrderType().equals(OmniOrderTypeEnum.PRODUCTION_ORDER.getCode())) {

            orderInfoByGoodsVO.setGoodsName(orderRepository.getGoodsNameByOrder(orderInfo));
        } else {
            //生成订单套餐数据
            GoodsAttachmentVo goodsAttachmentVo = goodsManager.goodsAttachmentVo(orderGoodsInfo.getGoodsId());
            RoomByGoodsIdVo roomInfoByGoodsId = goodsManager.getRoomInfoByGoodsId(orderGoodsInfo.getGoodsId());

            orderInfoByGoodsVO.setRoomId(ObjectUtil.isNotEmpty(roomInfoByGoodsId) ? roomInfoByGoodsId.getId() : null).setAttachmentName(ObjectUtil.isEmpty(goodsAttachmentVo) ? null : goodsAttachmentVo.getName()).setAttachmentUrl(ObjectUtil.isEmpty(goodsAttachmentVo) ? null : goodsAttachmentVo.getUrl()).setParentId(orderGoodsInfo.getParentId()).setId(orderGoodsInfo.getId()).setOrderId(orderId).setGoodsId(orderGoodsInfo.getGoodsId()).setGoodsName(orderGoodsInfo.getGoodsName()).setSkuId(orderGoodsInfo.getSkuId()).setServiceDays(orderGoodsInfo.getServiceDays()).setGoodsPriceOrgin(BigDecimalUtil.divide(new BigDecimal(orderGoodsInfo.getGoodsPriceOrgin()), new BigDecimal(100))).setGoodsReceivableAmount(BigDecimalUtil.divide(new BigDecimal(orderGoodsInfo.getPayAmount()), new BigDecimal(100))).setOrderType(orderInfo.getOrderType());
        }


        //生成订单额外信息数据
        OrderInfoByOtherInfoVO orderInfoByOtherInfoVO = new OrderInfoByOtherInfoVO();
        orderInfoByOtherInfoVO.setOrderTag(orderInfo.getOrderTag());
        orderInfoByOtherInfoVO.setOrderTagName(orderInfo.getOrderTagName());
        orderInfoByOtherInfoVO.setRemark(orderInfo.getRemark());
        orderInfoByOtherInfoVO.setVoucherUrlList(byOrderId.stream().map(HeOrderVoucherEntity::getUrl).collect(Collectors.toList()));

        //生成订单额外礼赠数据
        //总价格
        int totalPrice = 0;
        //总数量
        int totalNum = 0;
        OrderInfoByGiftExtendVO orderInfoByGiftExtendVO = new OrderInfoByGiftExtendVO();
        List<OrderInfoByGiftExtendSkuVO> orderInfoByGiftExtendSkuVOList = new ArrayList<>();

        for (OrderGiftExtendEntity o : afterMergeOrderGiftInfoList) {
            Integer type = o.getType();
            if (type == 1) {
                totalNum += 1;
                totalPrice += o.getGoodsNum() * 100;
            } else {
                totalNum += o.getGoodsNum();
                totalPrice += (o.getPrice() * o.getGoodsNum());
            }
            OrderInfoByGiftExtendSkuVO orderInfoByGiftExtendSkuVO = new OrderInfoByGiftExtendSkuVO().setGoodsId(o.getGoodsId()).setSkuId(o.getSkuId()).setNatureTypeTitle(o.getNatureTypeTitle()).setGoodsTypeTitle(o.getGoodsTypeTitle()).setCategoryBackTitle(o.getCategoryBackTitle()).setQuantity(o.getGoodsNum()).setCategoryFront(type == 1 ? null : 99999).setSkuNum(o.getSkuNum()).setGoodsName(o.getGoodsName()).setSkuName(o.getSkuName()).setType(o.getType()).setCategoryName(o.getCategoryName());

            if (o.getType() == 1) {
                //产康金特殊处理

                Long unitCost = 0L;
                Long totalCost = 0L;

                if (ObjectUtil.isNotEmpty(o.getCost())) {
                    unitCost = o.getCost().longValue() / o.getGoodsNum();
                    totalCost = o.getCost().longValue();
                } else {
                    //成本=赠送产康金/税率*40% 2024-1-30逻辑
                    OrderIndexFact fact = new OrderIndexFact();
                    fact.setStoreId(orderInfo.getStoreId());
                    fact.setEffectDate(System.currentTimeMillis() / 1000);
                    BigDecimal taxRate = discountCalculator.getTaxRate(fact);
                    //单个成本 分
                    BigDecimal unitCostPrice = new BigDecimal(100).divide((new BigDecimal(1).add(taxRate)), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(0.4)).setScale(2, RoundingMode.HALF_UP);
                    //总成本 分
                    BigDecimal allCostPrice = new BigDecimal(o.getGoodsNum() * 100).divide((new BigDecimal(1).add(taxRate)), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(0.4)).setScale(2, RoundingMode.HALF_UP);
                    unitCost = unitCostPrice.longValue();
                    totalCost = allCostPrice.longValue();

                }
                orderInfoByGiftExtendSkuVO.setUnitPrice(RMBUtils.changeF2Y(100L)).setTotalPrice(RMBUtils.changeF2Y(100L * o.getGoodsNum())).setUnitCost(RMBUtils.changeF2Y(unitCost)).setTotalCost(RMBUtils.changeF2Y(totalCost));
            } else {
                orderInfoByGiftExtendSkuVO.setUnitPrice(RMBUtils.changeF2Y(o.getPrice().longValue())).setTotalPrice(RMBUtils.changeF2Y(o.getPrice().longValue() * o.getGoodsNum())).setUnitCost(RMBUtils.changeF2Y(o.getCost().longValue())).setTotalCost(RMBUtils.changeF2Y(o.getCost().longValue() * o.getGoodsNum()));
            }
            orderInfoByGiftExtendSkuVOList.add(orderInfoByGiftExtendSkuVO);
        }
        orderInfoByGiftExtendVO.setChooseQuantity(totalNum);
        orderInfoByGiftExtendVO.setChooseTotalAmount(BigDecimalUtil.divide(new BigDecimal(totalPrice), new BigDecimal(100)));
        orderInfoByGiftExtendVO.setOrderInfoByGiftExtendSkuVOList(orderInfoByGiftExtendSkuVOList);

        //生成订单加收项数据
        List<OrderInfoByAdditionalRevenueVO> orderInfoByAdditionalRevenueVOList = new ArrayList<>();

        //如果订单有加收项但是加收项中没有节假日的情况，必须手动加
        if (CollectionUtils.isNotEmpty(orderAdditionalRevenueInfoList) && !orderAdditionalRevenueInfoList.stream().anyMatch(o -> o.getType() == 4)) {
            OrderInfoByAdditionalRevenueVO orderInfoByAdditionalRevenueVO = new OrderInfoByAdditionalRevenueVO();
            orderInfoByAdditionalRevenueVO.setType(MonthAdditionalRevenueEnum.HOLIDAY.getCode());
            orderInfoByAdditionalRevenueVO.setGoodsId(orderGoodsInfo.getGoodsId());
            orderInfoByAdditionalRevenueVO.setIsFestival(0);
            orderInfoByAdditionalRevenueVO.setDaysList(new ArrayList());
            orderInfoByAdditionalRevenueVO.setPrice(null);
            orderInfoByAdditionalRevenueVO.setCost(BigDecimal.ZERO);
            orderInfoByAdditionalRevenueVO.setOriginPrice(BigDecimal.ZERO);
            orderInfoByAdditionalRevenueVOList.add(orderInfoByAdditionalRevenueVO);
        } else {
            if (CollectionUtils.isNotEmpty(orderAdditionalRevenueInfoList)) {
                HeOrderAdditionalRevenueEntity heOrderAdditionalRevenueEntity = orderAdditionalRevenueInfoList.get(0);
                if (!heOrderAdditionalRevenueEntity.getType().equals(MonthAdditionalRevenueEnum.HOLIDAY.getCode())) {
                    List<HeOrderAdditionalRevenueEntity> newOrderAdditionalRevenueInfoList = new ArrayList<>();
                    newOrderAdditionalRevenueInfoList.addAll(orderAdditionalRevenueInfoList.stream().filter(o -> o.getType().equals(MonthAdditionalRevenueEnum.HOLIDAY.getCode())).collect(Collectors.toList()));
                    newOrderAdditionalRevenueInfoList.addAll(orderAdditionalRevenueInfoList.stream().filter(o -> !o.getType().equals(MonthAdditionalRevenueEnum.HOLIDAY.getCode())).collect(Collectors.toList()));
                    orderAdditionalRevenueInfoList = newOrderAdditionalRevenueInfoList;
                }
            }
        }


        for (HeOrderAdditionalRevenueEntity o : orderAdditionalRevenueInfoList) {
            OrderInfoByAdditionalRevenueVO orderInfoByAdditionalRevenueVO = new OrderInfoByAdditionalRevenueVO();
            Integer type = o.getType();
            orderInfoByAdditionalRevenueVO.setId(o.getId().intValue());
            orderInfoByAdditionalRevenueVO.setType(type);
            orderInfoByAdditionalRevenueVO.setGoodsId(orderGoodsInfo.getGoodsId());
            orderInfoByAdditionalRevenueVO.setOriginPrice(BigDecimalUtil.divide(new BigDecimal(o.getCost()), new BigDecimal(100)));
            orderInfoByAdditionalRevenueVO.setCost(BigDecimalUtil.divide(new BigDecimal(o.getCost()), new BigDecimal(100)));
            orderInfoByAdditionalRevenueVO.setPrice(BigDecimalUtil.divide(new BigDecimal(o.getPrice()), new BigDecimal(100)));
            orderInfoByAdditionalRevenueVO.setIsFestival(o.getIsFestival());
            switch (MonthAdditionalRevenueEnum.getEnumByCode(type)) {
                case COST_MULTIPLE_BIRTHS://多胞胎费用
                    orderInfoByAdditionalRevenueVO.setEmbryoNumber(o.getEmbryoNumber());
                    break;
                case STAY_COST://续住费用
                case ROOM_CHANGES://房型变更
                    orderInfoByAdditionalRevenueVO.setRoomId(o.getRoomId());
                    orderInfoByAdditionalRevenueVO.setRoomName(o.getRoomName());
                    orderInfoByAdditionalRevenueVO.setDays(o.getDays());
                    orderInfoByAdditionalRevenueVO.setDaysList(o.getDaysList());
                    break;
                case HOLIDAY:
                    if (o.getIsFestival() == 0 && orderInfoByAdditionalRevenueVO.getPrice().compareTo(BigDecimal.ZERO) == 0) {
                        orderInfoByAdditionalRevenueVO.setPrice(null);
                    }
                    orderInfoByAdditionalRevenueVO.setDays(o.getDays());
                    break;
                default:
            }
            orderInfoByAdditionalRevenueVOList.add(orderInfoByAdditionalRevenueVO);
        }

        //加收项进行排序
        List<OrderInfoByAdditionalRevenueVO> costMultipleBirths = orderInfoByAdditionalRevenueVOList.stream().filter(o -> MonthAdditionalRevenueEnum.COST_MULTIPLE_BIRTHS.getCode().equals(o.getType())).collect(Collectors.toList());
        List<OrderInfoByAdditionalRevenueVO> stayCost = orderInfoByAdditionalRevenueVOList.stream().filter(o -> MonthAdditionalRevenueEnum.STAY_COST.getCode().equals(o.getType())).collect(Collectors.toList());
        List<OrderInfoByAdditionalRevenueVO> roomChanges = orderInfoByAdditionalRevenueVOList.stream().filter(o -> MonthAdditionalRevenueEnum.ROOM_CHANGES.getCode().equals(o.getType())).collect(Collectors.toList());
        List<OrderInfoByAdditionalRevenueVO> holiday = orderInfoByAdditionalRevenueVOList.stream().filter(o -> MonthAdditionalRevenueEnum.HOLIDAY.getCode().equals(o.getType())).collect(Collectors.toList());

        //排序：节假日-多胞胎-续住-房型变更
        orderInfoByAdditionalRevenueVOList = new ArrayList<>();
        orderInfoByAdditionalRevenueVOList.addAll(holiday);
        orderInfoByAdditionalRevenueVOList.addAll(costMultipleBirths);
        orderInfoByAdditionalRevenueVOList.addAll(stayCost);
        orderInfoByAdditionalRevenueVOList.addAll(roomChanges);


        result.setOrderInfoByGoodsVO(orderInfoByGoodsVO);
        result.setOrderInfoByGiftExtendVO(orderInfoByGiftExtendVO);
        result.setOrderInfoByAdditionalRevenueVOList(orderInfoByAdditionalRevenueVOList);
        result.setOrderInfoByOtherInfoVO(orderInfoByOtherInfoVO);

        OrderDiscountsCacheReq orderDiscountsCacheReq = new OrderDiscountsCacheReq();
        orderDiscountsCacheReq.setOrderId(orderId);
        if (ObjectUtil.isNotEmpty(orderGoodsInfo)) {
            orderDiscountsCacheReq.setGoodsId(orderGoodsInfo.getGoodsId());
        }
        orderDiscountsCacheReq.setOrderType(orderInfo.getOrderType());
        orderDiscountsCacheReq.setStoreId(orderInfo.getStoreId());
        orderDiscountsCacheReq.setClientUid(orderMonthClientVO.getClientUid());
        result.setOrderDiscountsCacheVO(null);
        String discountDetails = orderInfo.getDiscountDetails();
        if (StringUtils.isNotEmpty(discountDetails)) {
            OrderDiscountsCacheVO read = JsonUtil.read(discountDetails, OrderDiscountsCacheVO.class);
            //是否需要展示底部折扣审批
            //配置折扣对象
            DiscountFact discountFact = DiscountFact.builder().orderType(orderInfo.getOrderType()).grossMargin(orderInfo.getGrossMargin().multiply(new BigDecimal(100))).build();

            Boolean approval = discountCalculator.isDiscountApproval(discountFact);
            read.setShowApproveText(approval);
            result.setOrderDiscountsCacheVO(read);
        }
        log.info("下单后获取订单信息={}", JSONUtil.toJsonStr(result));
        return result;
    }

    @Override
    public List<OrderInfoByGiftExtendSkuVO> getOrderInfoByGiftExtendVO(Integer orderId) {
        //获取订单额外礼赠数据（获取未核销的）
        List<OrderGiftExtendEntity> orderGiftInfoList = orderGiftExtendRepository.getByOrderId(orderId, OrderProductionVerificationStateEnum.PENDING_WRITE_OFF.code());

        //额外礼赠数据合并
        List<OrderGiftExtendEntity> afterMergeOrderGiftInfoList = new ArrayList<>();
        orderGiftInfoList.stream().forEach(o -> {
            Integer type = o.getType();
            switch (OrderGiftExtendTypeEnum.of(type)) {
                case INDUSTRIAL_HEALTH_SERVICE:
                    Integer skuId = o.getSkuId();
                    Optional<OrderGiftExtendEntity> first = afterMergeOrderGiftInfoList.stream().filter(a -> Objects.equals(a.getSkuId(), skuId)).findFirst();
                    if (!first.isPresent()) {
                        List<OrderGiftExtendEntity> collect = orderGiftInfoList.stream().filter(a -> Objects.equals(a.getSkuId(), skuId)).collect(Collectors.toList());
                        o.setGoodsNum(collect.size());
                        afterMergeOrderGiftInfoList.add(o);
                    }
                    break;
                case SANKANGJIN:
                    o.setGoodsId(null);
                    o.setSkuId(null);
                    afterMergeOrderGiftInfoList.add(o);
                    break;
                default:
                    afterMergeOrderGiftInfoList.add(o);
                    break;
            }
        });
        //额外礼赠数据合并

        //总价格
        int totalPrice = afterMergeOrderGiftInfoList.stream().mapToInt(OrderGiftExtendEntity::getPrice).sum();
        //总数量
        int totalNum = afterMergeOrderGiftInfoList.stream().mapToInt(OrderGiftExtendEntity::getGoodsNum).sum();
        OrderInfoByGiftExtendVO orderInfoByGiftExtendVO = new OrderInfoByGiftExtendVO();
        orderInfoByGiftExtendVO.setChooseQuantity(totalNum);
        orderInfoByGiftExtendVO.setChooseTotalAmount(new BigDecimal(totalPrice / 100));
        List<OrderInfoByGiftExtendSkuVO> orderInfoByGiftExtendSkuVOList = new ArrayList<>();
        afterMergeOrderGiftInfoList.stream().forEach(o -> {
            Integer type = o.getType();
            OrderInfoByGiftExtendSkuVO orderInfoByGiftExtendSkuVO = new OrderInfoByGiftExtendSkuVO().setGoodsId(o.getGoodsId()).setSkuId(o.getSkuId()).setQuantity(o.getGoodsNum()).setCategoryFront(type == 1 ? null : 99999).setGoodsName(o.getGoodsName()).setSkuName(o.getSkuName());
            orderInfoByGiftExtendSkuVOList.add(orderInfoByGiftExtendSkuVO);
        });
        orderInfoByGiftExtendVO.setOrderInfoByGiftExtendSkuVOList(orderInfoByGiftExtendSkuVOList);

        return orderInfoByGiftExtendVO.getOrderInfoByGiftExtendSkuVOList();
    }


    private List<OrderOperateButton> setButton(WechatMyOrderVO order, List<HeOrderRefundEntity> heOrderRefundEntities, List<OaProcessIdRelationPO> approveList, List<HeIncomeRecordEntity> incomeRecordList, List<MonthContractSignRecordEntity> contractSignRecordList, List<MonthContractSignAgreementEntity> signAgreementList, List<ContractSignRecordPaperEntity> signRecordPaperList, List<AheadOutRoomEntity> aheadOutRoomEntityList) {

        List<OrderOperateButton> buttonList = new ArrayList<>();
        for (OrderButtonEnum buttonEnum : OrderButtonEnum.values()) {
            boolean flag;
            switch (buttonEnum) {
                case SIGN_CONTRACT://签合同    所有订单均可展示该按钮
                    buttonList.add(OrderOperateButton.builder().code(buttonEnum.getCode()).value(buttonEnum.getValue()).build());
                    break;
                case VIEW_CONTRACT://查看合同	只要有签订线上合同，或签署纸质协议，均需展示该按钮
                    flag = CollectionUtils.isNotEmpty(contractSignRecordList) || CollectionUtils.isNotEmpty(signAgreementList) || CollectionUtils.isNotEmpty(signRecordPaperList);
                    if (flag) {
                        buttonList.add(getButton(buttonEnum));
                    }
                    break;

                case CHANGE_ORDER://订单变更	订单状态不是“全部退款”，没有提前离馆即可进行订单变更。需要修改合同的情况，包括套餐、客户基本信息等
                    if (!(CollectionUtils.isNotEmpty(aheadOutRoomEntityList) && ContractStatusEnum.SIGNED.code().equals(order.getHasSignContractReleaseAgreement()))) {
                        flag = !Objects.equals(order.getRefundStatus(), OrderRefundStatusEnum.FULL_REFUND.getCode()) && !Objects.equals(order.getOrderStatus(), OrderStatusV2Enum.ADVANCE_OUT_OF_STORE.getCode());
                        if (flag) {
                            OrderOperateButton button = getButton(buttonEnum);
                            //2024-01-15订单折扣审批前置逻辑,审批中不允许修改订单
                            if (ObjectUtil.isNotEmpty(order.getApprovalDiscountStatus()) && order.getApprovalDiscountStatus().equals(ApprovalDiscountStatusEnum.APPROVING.getCode())) {
                                button.setText("订单优惠审批过程中不可修改合同");
                            }
                            buttonList.add(button);
                        }
                    }
                    break;
                case COLLECTION://收款-一旦发起提起离馆且签署合同解除协议，则该按钮隐藏
                    if (!(CollectionUtils.isNotEmpty(aheadOutRoomEntityList) && ContractStatusEnum.SIGNED.code().equals(order.getHasSignContractReleaseAgreement()))) {
                        OrderOperateButton button = getButton(buttonEnum);
                        //判断订单签订类型
                        Integer signType = order.getSignType();
                        if (ObjectUtil.isNotEmpty(signType)) {

                            //委托协议书
                            List<MonthContractSignRecordEntity> powerAttorneyList = CollectionUtil.isEmpty(contractSignRecordList) ? new ArrayList<>() : contractSignRecordList.stream().filter(c -> c.getTemplateContractType().equals(TemplateContractTypeEnum.ENTRUST.code()) && ContractStatusEnum.SIGNED.code().equals(c.getContractStatus())).collect(Collectors.toList());
                            //订单类合同
                            List<MonthContractSignRecordEntity> orderContract = CollectionUtil.isEmpty(contractSignRecordList) ? new ArrayList<>() : contractSignRecordList.stream().filter(c -> c.getTemplateType() == 1 && ContractStatusEnum.SIGNED.code().equals(c.getContractStatus())).collect(Collectors.toList());
                            //纸质合同
                            List<ContractSignRecordPaperEntity> paperEntityList = CollectionUtil.isEmpty(signRecordPaperList) ? new ArrayList<>() : signRecordPaperList.stream().filter(c -> (TemplateContractTypeEnum.APPOINTMENT.code().equals(c.getContractType()) || TemplateContractTypeEnum.YZ_SAINTBELLA.code().equals(c.getContractType())) && ContractStatusEnum.SIGNED.code().equals(c.getContractStatus())).collect(Collectors.toList());
                            //查看是否是老合同
                            MonthContractSignRecordEntity byOrderId = monthContractSignRecordRepository.getByOrderId(order.getOrderId(), TemplateContractTypeEnum.YZ_SAINTBELLA.code());
                            if (ObjectUtil.isEmpty(byOrderId)) {
                                byOrderId = new MonthContractSignRecordEntity();
                                byOrderId.setContractType(null);
                            }
                            if (signType.equals(OrderSignTypeEnum.SIGN_TYPE_BAILOR.code()) && ObjectUtil.notEqual(byOrderId.getContractType(), ContractTypeEnum.OLD_TYPE.code())) {
                                //委托人的方式签订合同，首先判断是否签订授权委托书
                                if (CollectionUtils.isEmpty(contractSignRecordList)) {
                                    button.setText("请先签订授权委托书");
                                } else {
                                    if (CollectionUtils.isEmpty(powerAttorneyList)) {
                                        button.setText("请先签订授权委托书");
                                    } else {
                                        //签订授权委托书判断是否签订订单类合同
                                        if (CollectionUtils.isEmpty(orderContract) && CollectionUtils.isEmpty(paperEntityList)) {
                                            button.setText("请先签订一个订单类合同");
                                        }
                                    }
                                }
                            } else {
                                if (CollectionUtils.isEmpty(contractSignRecordList) || (CollectionUtils.isEmpty(orderContract) && CollectionUtils.isEmpty(paperEntityList))) {
                                    button.setText("请先签订一个订单类合同");
                                }
                            }
                        }
                        buttonList.add(button);
                    }
                    break;
                case SEND_CHILDBIRTH_PROSPERITY://发送分娩喜报	未生效、待入住可发送 且有支付记录
                    if (order.getOrderStatus().equals(OrderStatusV2Enum.NONE.getCode()) || order.getOrderStatus().equals(OrderStatusV2Enum.TO_STAY_IN.getCode())) {
                        String realAmount = order.getRealAmount();
                        if (StringUtils.isNotEmpty(realAmount)) {
                            realAmount = realAmount.replace(",", "");
                            if (new BigDecimal(realAmount).compareTo(BigDecimal.ZERO) > 0) {
                                buttonList.add(getButton(buttonEnum));
                            }
                        }
                    }
                    break;
                case APPROVAL_PROGRESS://审批进度	有审批流程发起过，没有就不显示该按钮
                    if (CollectionUtils.isNotEmpty(approveList)) {
                        buttonList.add(getButton(buttonEnum));
                    }
                    break;

                case INITIATING_REFUND://发起退款	提交了提前离馆表单且签署了，不展示，已付金额>0就展示
                    /*if (!order.checkedInOrWrittenOff()) {
                        BigDecimal paidAmount = BigDecimal.ZERO;
                        if (StringUtils.isNotEmpty(order.getPaidAmount())) {
                            paidAmount = new BigDecimal(order.getPaidAmount().replace(",", ""));
                        }
                        if (paidAmount.compareTo(BigDecimal.ZERO) > 0) {
                            buttonList.add(getButton(buttonEnum));
                        }
                    }*/
                    if (!order.checkedInOrWrittenOff()) {
                        buttonList.add(getButton(buttonEnum));
                    }
                    break;
                case LIST_REFUND://退款列表	有1笔或者以上退款记录
                    if (CollectionUtils.isNotEmpty(heOrderRefundEntities)) {
                        buttonList.add(getButton(buttonEnum));
                    }
                    break;

                case COLLECTION_RECORD://收款记录	有1笔或以上收款记录
                    if (CollectionUtils.isNotEmpty(incomeRecordList)) {
                        buttonList.add(getButton(buttonEnum));
                    }
                    break;

                case ORDER_DETAIL://订单详情	所有订单均有订单详情
                    buttonList.add(getButton(buttonEnum));
                    break;

                case ADVANCE_LEAVE://提前离馆	(订单支付完成或超额) 并且 (订单状态是入住中或提前离馆)
                    flag = (Objects.equals(order.getPayStatus(), PayStatusV2Enum.PAY_OFF.getCode()) || Objects.equals(order.getPayStatus(), PayStatusV2Enum.EXCESS_PAY.getCode())) && (Objects.equals(order.getOrderStatus(), OrderStatusV2Enum.STAY_IN.getCode()) || Objects.equals(order.getOrderStatus(), OrderStatusV2Enum.ADVANCE_OUT_OF_STORE.getCode()));
                    if (flag) {
                        buttonList.add(getButton(buttonEnum));
                    }
                    break;

                case CLOSE_ORDER://订单关闭	所有没有收款、退款流水的订单，且没有签订过任何合同的，有订单关闭按钮。关闭后，Pi和C端小程序均看不到这笔订单，但订单后台有这笔订单且显示订单状态是“订单关闭”
                    flag = CollectionUtils.isEmpty(incomeRecordList) && CollectionUtils.isEmpty(heOrderRefundEntities);

                    //查询已签订的
                    List<MonthContractSignRecordEntity> signedContractSignRecord = CollectionUtils.isEmpty(contractSignRecordList) ? new ArrayList<>() : contractSignRecordList.stream().filter(c -> ContractStatusEnum.SIGNED.code().equals(c.getContractStatus())).collect(Collectors.toList());
                    List<MonthContractSignAgreementEntity> signedSignAgreementRecord = CollectionUtils.isEmpty(signAgreementList) ? new ArrayList<>() : signAgreementList.stream().filter(c -> c.getState() == 2).collect(Collectors.toList());
                    List<ContractSignRecordPaperEntity> signedSignRecordPaper = CollectionUtils.isEmpty(signRecordPaperList) ? new ArrayList<>() : signRecordPaperList.stream().filter(c -> ContractStatusEnum.SIGNED.code().equals(c.getContractStatus())).collect(Collectors.toList());

                    boolean contractFlag = CollectionUtils.isEmpty(signedContractSignRecord) && CollectionUtils.isEmpty(signedSignAgreementRecord) && CollectionUtils.isEmpty(signedSignRecordPaper);

                    if (flag && contractFlag) {
                        buttonList.add(getButton(buttonEnum));
                    }
                    break;

//                case ORDER_COMPLAINTS:
//                    if (order.checkedInOrWrittenOff()) {
//                        buttonList.add(getButton(buttonEnum));
//                    }
//                    break;
                case REFUND_TO_BALANCE:
                    BigDecimal paidAmount = BigDecimal.ZERO;
                    if (StringUtils.isNotEmpty(order.getPaidAmount())) {
                        paidAmount = new BigDecimal(order.getPaidAmount().replace(",", ""));
                    }
                    if (paidAmount.compareTo(BigDecimal.ZERO) > 0) {
                        buttonList.add(getButton(buttonEnum));
                    }
                default:
                    break;
            }
        }
        return buttonList;
    }

    private List<OrderOperateButton> setCustomerButton() {

        List<OrderOperateButton> buttonList = new ArrayList<>();
        for (OrderButtonEnum buttonEnum : OrderButtonEnum.values()) {
            switch (buttonEnum) {
                case ORDER_DETAIL://订单详情	所有订单均有订单详情
                    buttonList.add(getButton(buttonEnum));
                    break;
                default:
                    break;
            }
        }
        return buttonList;
    }

    private OrderOperateButton setPayButton(WechatMyOrderVO order, List<MonthContractSignRecordEntity> contractSignRecordList, List<ContractSignRecordPaperEntity> signRecordPaperList, List<AheadOutRoomEntity> aheadOutRoomEntityList) {

        OrderOperateButton button = null;
        for (OrderButtonEnum buttonEnum : OrderButtonEnum.values()) {
            switch (buttonEnum) {
                case COLLECTION:
                    //新订单收款-实际已付金额<签单金额;一旦发起提起离馆且签署合同解除协议，则该按钮隐藏
                    if (ObjectUtil.isNotEmpty(order.getOldOrNew()) && order.getOldOrNew().equals(1)) {
                        if (Integer.parseInt(order.getRealAmount()) < Integer.parseInt(order.getPayAmount()) && !(CollectionUtils.isNotEmpty(aheadOutRoomEntityList) && ContractStatusEnum.SIGNED.code().equals(order.getHasSignContractReleaseAgreement()))) {
                            button = getButton(buttonEnum);
                            //判断订单签订类型
                            Integer signType = order.getSignType();
                            if (ObjectUtil.isNotEmpty(signType)) {

                                //委托协议书
                                List<MonthContractSignRecordEntity> powerAttorneyList = CollectionUtil.isEmpty(contractSignRecordList) ? new ArrayList<>() : contractSignRecordList.stream().filter(c -> c.getTemplateContractType().equals(TemplateContractTypeEnum.ENTRUST.code()) && ContractStatusEnum.SIGNED.code().equals(c.getContractStatus())).collect(Collectors.toList());
                                //订单类合同
                                List<MonthContractSignRecordEntity> orderContract = CollectionUtil.isEmpty(contractSignRecordList) ? new ArrayList<>() : contractSignRecordList.stream().filter(c -> c.getTemplateType() == 1 && ContractStatusEnum.SIGNED.code().equals(c.getContractStatus())).collect(Collectors.toList());
                                //纸质合同
                                List<ContractSignRecordPaperEntity> paperEntityList = CollectionUtil.isEmpty(signRecordPaperList) ? new ArrayList<>() : signRecordPaperList.stream().filter(c -> (TemplateContractTypeEnum.APPOINTMENT.code().equals(c.getContractType()) || TemplateContractTypeEnum.YZ_SAINTBELLA.code().equals(c.getContractType())) && ContractStatusEnum.SIGNED.code().equals(c.getContractStatus())).collect(Collectors.toList());
                                //查看是否是老合同
                                MonthContractSignRecordEntity byOrderId = monthContractSignRecordRepository.getByOrderId(order.getOrderId(), TemplateContractTypeEnum.YZ_SAINTBELLA.code());
                                if (ObjectUtil.isEmpty(byOrderId)) {
                                    byOrderId = new MonthContractSignRecordEntity();
                                    byOrderId.setContractType(null);
                                }
                                if (signType.equals(OrderSignTypeEnum.SIGN_TYPE_BAILOR.code()) && ObjectUtil.notEqual(byOrderId.getContractType(), ContractTypeEnum.OLD_TYPE.code())) {
                                    //委托人的方式签订合同，首先判断是否签订授权委托书
                                    if (CollectionUtils.isEmpty(contractSignRecordList)) {
                                        button.setText("请先签订授权委托书");
                                    } else {
                                        if (CollectionUtils.isEmpty(powerAttorneyList)) {
                                            button.setText("请先签订授权委托书");
                                        } else {
                                            //签订授权委托书判断是否签订订单类合同
                                            if (CollectionUtils.isEmpty(orderContract) && CollectionUtils.isEmpty(paperEntityList)) {
                                                button.setText("请先签订一个订单类合同");
                                            }
                                        }
                                    }
                                } else {
                                    if (CollectionUtils.isEmpty(contractSignRecordList) || (CollectionUtils.isEmpty(orderContract) && CollectionUtils.isEmpty(paperEntityList))) {
                                        button.setText("请先签订一个订单类合同");
                                    }
                                }
                            }
                        }
                    } else {
                        //C端老订单列表，当签单金额-累计支付金额<=0，则隐藏支付按钮即可
                        if (Integer.parseInt(order.getPaidAmount()) < Integer.parseInt(order.getPayAmount())) {
                            button = getButton(buttonEnum);
                        }
                    }
                    break;
                default:
                    break;
            }
        }
        return button;
    }

    private OrderOperateButton getButton(OrderButtonEnum buttonEnum) {
        return OrderOperateButton.builder().code(buttonEnum.getCode()).value(buttonEnum.getValue()).build();
    }

    private List<STMOOrderBasicContractInfoVO> convertSTMOOrderBasicContractInfoVOList(List<ContractSignRecordPaperEntity> contractSignRecordPaperEntityList, List<MonthContractSignRecordEntity> monthContractSignRecordEntityList, List<MonthContractSignAgreementEntity> signAgreementList) {
        List<STMOOrderBasicContractInfoVO> voList = new ArrayList<>();
        List<STMOOrderBasicContractInfoVO> voList1 = orderConvert.contractSignRecordPaperEntity2VOList(contractSignRecordPaperEntityList);
        if (ObjectUtil.isNotEmpty(voList1)) {
            voList1.forEach(x -> {
                x.setContractSignType(ContractTypeEnum.PAPER_TYPE.code());
                x.setContractSignTypeStr(ContractTypeEnum.PAPER_TYPE.desc());
            });
        }
        List<STMOOrderBasicContractInfoVO> voList2 = orderConvert.monthContractSignRecordEntity2VOList(monthContractSignRecordEntityList);
        if (ObjectUtil.isNotEmpty(voList2)) {
            voList2.forEach(x -> {
                x.setContractSignType(ContractTypeEnum.ESIGN_TYPE.code());
                x.setContractSignTypeStr(ContractTypeEnum.ESIGN_TYPE.desc());
            });
        }
        voList.addAll(voList1);
        voList.addAll(voList2);
        voList.forEach(x -> {
            x.setTemplateTypeStr(TemplateContractTypeEnum.fromCode(x.getTemplateType()));
            x.setContractStatusStr(ContractStatusEnum.fromCode(x.getContractStatus()));
        });
        return voList;
    }

}

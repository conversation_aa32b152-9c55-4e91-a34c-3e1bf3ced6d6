package com.stbella.order.server.context.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.core.base.Operator;
import com.stbella.core.base.UserTokenInfoDTO;
import com.stbella.core.enums.BusinessEnum;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.core.utils.JwtUtil;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.BPCheckUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderPerformanceOperationEntity;
import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.domain.order.production.OrderProductionVerificationLogEntity;
import com.stbella.order.domain.repository.HeOrderPerformanceOperationRepository;
import com.stbella.order.domain.repository.OrderGoodsRepository;
import com.stbella.order.domain.repository.OrderProductionVerificationLogRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.order.cts.enums.YesOrNoEnum;
import com.stbella.order.server.order.month.enums.ApprovalDiscountStatusEnum;
import com.stbella.order.server.order.month.service.impl.OrderAssetTradeService;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.platform.order.api.OrderAggregateRoot;
import com.stbella.platform.order.api.OrderCommandService;
import com.stbella.platform.order.api.OrderQueryService;
import com.stbella.platform.order.api.reduction.req.DecreaseCheckReq;
import com.stbella.platform.order.api.reduction.req.DecreaseReq;
import com.stbella.platform.order.api.refund.req.OrderDivisionPerformanceReq;
import com.stbella.platform.order.api.req.CloseCommand;
import com.stbella.platform.order.api.res.DecreaseApprovalRes;
import com.stbella.platform.order.api.res.DecreaseCheckRes;
import com.stbella.store.common.enums.core.GoodsTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.FlowIdentity;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: OrderCommandServiceImpl
 * @date 2024/5/29 14:51
 */
@Service
@DubboService
@Slf4j
public class OrderCommandServiceImpl implements OrderCommandService {

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderGoodsRepository orderGoodsRepository;
    @Resource
    OrderAggregateRoot orderAggregateRoot;
    @Resource
    private OrderProductionVerificationLogRepository orderProductionVerificationLogRepository;
    @Resource
    private HeOrderPerformanceOperationRepository orderPerformanceOperationRepository;
    @Resource
    private OrderQueryService orderQueryService;
    @Resource
    private OrderAssetTradeService orderAssetTradeService;


    /**
     * 订单减免-审批校验
     *
     * @param req
     */
    @Override
    public Result<DecreaseApprovalRes> submitCheck(DecreaseReq req) {
        log.info("订单减免-审批校验入参:{}", JSONUtil.toJsonStr(req));
        HeOrderEntity heOrderEntity = orderRepository.queryOrderById(req.getOrderId().intValue());


        List<HeOrderGoodsEntity> goodsEntities = orderGoodsRepository.getByOrderIdList(Arrays.asList(req.getOrderId().intValue()));
        heOrderEntity.setGoodsList(goodsEntities);

        FlowIdentity identity = FlowIdentity.builder().bizActivity(BizActivityEnum.DECREASES.code()).idSlice(BusinessEnum.CARE_CENTER.name()).idSlice("decrease_submit_check").delimiter(":").build();

        FlowContext context = new FlowContext();

        context.setAttribute(BizConstant.ExtraKey.storeId, heOrderEntity.getStoreId());

        OrderReductionEntity orderReductionEntity = new OrderReductionEntity();
        orderReductionEntity.setOrderId(heOrderEntity.getOrderId().longValue());
        context.setAttribute(OrderReductionEntity.class, orderReductionEntity);
        context.setAttribute(HeOrderEntity.class, heOrderEntity);
        context.setAttribute(DecreaseReq.class, req);
        SnowballFlowLauncher.fire(identity, context);

        return Result.success(context.getAttribute(DecreaseApprovalRes.class));
    }

    /**
     * 订单减免
     * 订单减免校验通过后，调用此接口进行订单减免
     * 1.订单减免
     * 2.订单减免审批
     *
     * @param req
     */
    @Override
    public Result reduction(DecreaseReq req) {
        log.info("订单减免入参:{}", JSONUtil.toJsonStr(req));
        HeOrderEntity heOrderEntity = orderRepository.queryOrderById(req.getOrderId().intValue());
        heOrderEntity.setStaffPhone(req.getOperator().getOperatorPhone());
        List<HeOrderGoodsEntity> goodsEntities = orderGoodsRepository.getByOrderIdList(Arrays.asList(req.getOrderId().intValue()));
        heOrderEntity.setGoodsList(goodsEntities);

        FlowIdentity identity = FlowIdentity.builder().bizActivity(BizActivityEnum.DECREASES.code()).idSlice(BusinessEnum.CARE_CENTER.name()).idSlice("decrease").delimiter(":").build();

        FlowContext context = new FlowContext();

        OrderReductionEntity orderReductionEntity = new OrderReductionEntity();
        orderReductionEntity.setOrderId(heOrderEntity.getOrderId().longValue());
        orderReductionEntity.setDecreaseAmount(AmountChangeUtil.changeY2FFoInt(req.getDecreaseAmount()).longValue());
        context.setAttribute(OrderReductionEntity.class, orderReductionEntity);
        context.setAttribute(BizConstant.ExtraKey.storeId, heOrderEntity.getStoreId());
        context.setAttribute(Operator.class, req.getOperator());

        context.setAttribute(HeOrderEntity.class, heOrderEntity);
        context.setAttribute(DecreaseReq.class, req);
        SnowballFlowLauncher.fire(identity, context);

        OrderReductionEntity orderReductionEntity1 = context.getAttribute(OrderReductionEntity.class);
        if (ApprovalDiscountStatusEnum.INITIATION_FAILED.getCode().equals(orderReductionEntity1.getAuthState())) {
            return Result.failed("订单减免发起失败");
        }

        return Result.success();
    }

    @Override
    public Result<DecreaseCheckRes> reductionCheck(DecreaseCheckReq req) {
        log.info("订单减免点击校验入参:{}", JSONUtil.toJsonStr(req));
        HeOrderEntity heOrderEntity = orderRepository.queryOrderById(req.getOrderId().intValue());

        FlowIdentity identity = FlowIdentity.builder().bizActivity(BizActivityEnum.DECREASES.code()).idSlice(BusinessEnum.CARE_CENTER.name()).idSlice("decrease_click_check").delimiter(":").build();

        FlowContext context = new FlowContext();
        context.setAttribute(HeOrderEntity.class, heOrderEntity);

        SnowballFlowLauncher.fire(identity, context);

        DecreaseCheckRes decreaseCheckRes = context.getAttribute(DecreaseCheckRes.class);

        return Result.success(decreaseCheckRes);
    }


    /**
     * 关闭订单
     *
     * @param command
     * @return
     */
    @Override
    public Result<Void> close(CloseCommand command) {

        BPCheckUtil.checkEmptyInBean(new String[]{"orderId", "operator"}, command, false);
        orderAggregateRoot.close(command.getOperator(), command.getOrderId());
        return Result.success();
    }

    @Override
    public Result<Integer> batchUpdateVerification() {
        List<OrderProductionVerificationLogEntity> orderProductionVerificationLogEntities = orderProductionVerificationLogRepository.queryAllOrderId();
        Map<Long, List<OrderProductionVerificationLogEntity>> groupByOrderId = orderProductionVerificationLogEntities.stream().collect(Collectors.groupingBy(OrderProductionVerificationLogEntity::getOrderId));
        List<Integer> orderIdList = groupByOrderId.keySet().stream().map(Long::intValue).collect(Collectors.toList());
        List<HeOrderEntity> orderList = orderRepository.getByOrderList(orderIdList);
        if (CollectionUtil.isNotEmpty(orderList)) {
            for (HeOrderEntity orderEntity : orderList) {
                List<OrderProductionVerificationLogEntity> verificationList = groupByOrderId.get(orderEntity.getOrderId().longValue());
                if (CollectionUtil.isNotEmpty(verificationList)) {
                    orderEntity.setVerificationStatus(YesOrNoEnum.YES.getCode());
                    orderEntity.setVerificationId(verificationList.get(verificationList.size() - 1).getId());
                }
            }
            orderRepository.batchUpdateById(orderList);
        }
        return Result.success(orderList.size());
    }

    @Override
    public Result orderDivisionPerformance(OrderDivisionPerformanceReq req) {

        UserTokenInfoDTO userInfo = JwtUtil.getJwtTokenUserInfo();

        Integer orderId = req.getOrderId();

        HeOrderEntity orderEntity = orderRepository.getByOrderId(orderId);


        Integer percentFirstTime = orderEntity.getPercentFirstTime();
        if (ObjectUtil.isEmpty(percentFirstTime) || percentFirstTime == 0) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, "该订单无实付或业绩未生效");
        }

        List<HeOrderPerformanceOperationEntity> heOrderPerformanceOperationEntities = orderPerformanceOperationRepository.queryByOrderSn(orderEntity.getOrderSn());
        if (CollectionUtil.isNotEmpty(heOrderPerformanceOperationEntities)) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, "请先删除划分记录表中的调整记录后再操作");
        }

        //订单签单金额
        Integer payAmount = orderEntity.calPayable();
        //订单产康金支付金额
        Integer productionAmountPay = orderEntity.getProductionAmountPay();
        //真正的签单
        payAmount = payAmount - productionAmountPay;
        BigDecimal payAmountBigDecimal = RMBUtils.bigDecimalF2Y(payAmount);

        if (payAmount <= 0) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, "该订单无实付或业绩未生效");
        }

        Result<Integer> orderRealAmount = orderQueryService.getOrderRealAmount(orderId);
        Integer realAmount = orderRealAmount.getData();
        if (realAmount <= 0) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, "该订单无实付或业绩未生效");
        }


        if (!req.getPerformance().equals(payAmountBigDecimal)) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, "签单金额发生变更，请刷新页面！");
        }

        // 使用BigDecimal进行精确计算
        BigDecimal percentage = req.getRetainedPerformance()
                .divide(payAmountBigDecimal, 4, RoundingMode.HALF_UP)  // 保留4位小数
                .multiply(new BigDecimal(100))
                .setScale(2, RoundingMode.HALF_UP);  // 保留2位小数

        HeOrderPerformanceOperationEntity orderPerformanceOperationEntity = buildPerformanceOperation(req, orderEntity, payAmount, percentage, userInfo);
        orderPerformanceOperationRepository.saveOne(orderPerformanceOperationEntity);

        return Result.success();
    }

    @Override
    public Result orderDivisionPerformanceDelete(OrderDivisionPerformanceReq req) {
        if (ObjectUtil.isNotEmpty(req.getOperationId())) {
            orderPerformanceOperationRepository.delOneById(req.getOperationId());
        }
        return Result.success();
    }

    @Override
    public void checkInDecreaseCKJ(Integer orderId) {
        HeOrderEntity orderEntity = orderRepository.getByOrderId(orderId);
        List<HeOrderGoodsEntity> orderGoodsEntities = orderGoodsRepository.getByOrderIdList(Collections.singletonList(orderId));
        //过滤资产类型为22的商品
        List<HeOrderGoodsEntity> productionCoinList = orderGoodsEntities.stream().filter(o -> o.getGoodsType().equals(GoodsTypeEnum.Production_Coin.code())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(productionCoinList)) {
            log.info("订单中没有产康金商品，无需处理");
            return;
        }

        // 根据可用阶段分组：孕期和非孕期
        Map<Boolean, List<HeOrderGoodsEntity>> groupedByAvailableStage = productionCoinList.stream().collect(Collectors.groupingBy(HeOrderGoodsEntity::isPregnancyStageGoods));
        //孕期发放的资产
        List<HeOrderGoodsEntity> pregnancyGoods = groupedByAvailableStage.get(Boolean.TRUE);
        if (CollectionUtil.isNotEmpty(pregnancyGoods)) {
            for (HeOrderGoodsEntity pregnancyGood : pregnancyGoods) {
                //扣除的产康金
                orderAssetTradeService.checkInDeductUsableCkj(
                        orderEntity.getOrderId() + "" + System.currentTimeMillis() + "-save",
                        orderEntity.getBasicUid() + "",
                        orderEntity.getOrderId(),
                        -pregnancyGood.getGoodsNum().longValue(),
                        pregnancyGood.getOrderGoodsSn(),
                        Operator.system(),
                        pregnancyGood.getGoodsName()
                );
            }
        }
    }

    private HeOrderPerformanceOperationEntity buildPerformanceOperation(OrderDivisionPerformanceReq req, HeOrderEntity orderEntity, Integer payAmount, BigDecimal percentage, UserTokenInfoDTO userInfo) {
        HeOrderPerformanceOperationEntity orderPerformanceOperationEntity = new HeOrderPerformanceOperationEntity();
        orderPerformanceOperationEntity.setSourceStoreId(orderEntity.getStoreId());
        orderPerformanceOperationEntity.setOrderId(orderEntity.getOrderId());
        orderPerformanceOperationEntity.setOrderSn(orderEntity.getOrderSn());
        orderPerformanceOperationEntity.setOrderPercentFirstTime(orderEntity.getPercentFirstTime());
        orderPerformanceOperationEntity.setOrderTotalAmount(payAmount);
        orderPerformanceOperationEntity.setSourceStoreAmount(RMBUtils.YTFInt(req.getTargetPerformance()));
        orderPerformanceOperationEntity.setOrderRetainedPerformance(RMBUtils.YTFInt(req.getRetainedPerformance()));
        orderPerformanceOperationEntity.setPercentage(percentage);
        orderPerformanceOperationEntity.setTargetStoreId(req.getTargetStoreId());
        orderPerformanceOperationEntity.setTargetStoreAmount(RMBUtils.YTFInt(req.getTargetPerformance()));
        orderPerformanceOperationEntity.setGmtCreate(new Date());
        orderPerformanceOperationEntity.setCreateId(userInfo.getUserId());
        orderPerformanceOperationEntity.setCreateName(userInfo.getUserName());
        orderPerformanceOperationEntity.setGmtModified(new Date());
        orderPerformanceOperationEntity.setModifyId(userInfo.getUserId());
        orderPerformanceOperationEntity.setModifyName(userInfo.getUserName());
        orderPerformanceOperationEntity.setStaffId(orderEntity.getStaffId());
        orderPerformanceOperationEntity.setOrderType(orderEntity.getOrderType());
        return orderPerformanceOperationEntity;
    }

}

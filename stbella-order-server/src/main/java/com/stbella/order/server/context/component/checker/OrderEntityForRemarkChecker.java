package com.stbella.order.server.context.component.checker;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.contract.model.enums.TemplateTypeEnum;
import com.stbella.contract.model.req.ContractQueryV4;
import com.stbella.contract.model.res.ContractSignRecordVO2;
import com.stbella.contract.model.res.ContractTemplateRes;
import com.stbella.customer.server.ecp.entity.TabClientPO;
import com.stbella.order.common.enums.month.ContractStatusEnum;
import com.stbella.order.common.enums.month.ContractTypeEnum;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.fact.ContractFact;
import com.stbella.order.server.manager.ContractManager;
import com.stbella.order.server.manager.StoreManager;
import com.stbella.order.server.manager.TabClientManager;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import com.stbella.store.server.ecp.entity.CfgStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-03-26  13:55
 * @Description: 添加通用订单
 */
@Component
@Slf4j
@RefreshScope
public class OrderEntityForRemarkChecker {

    @Value("${oldOrder.matchPattern:(N?HEM\\d+|HEN\\d+)}")
    private String matchPattern;


    @Resource
    private OrderRepository orderRepository;
    @Resource
    private ContractManager contractManager;
    @Resource
    private RuleLinkClient ruleLinkClient;
    @Resource
    private StoreManager storeManager;
    @Resource
    private TabClientManager tabClientManager;

    public boolean check(HeOrderEntity orderEntity) {
        String remark = orderEntity.getRemark();
        log.info("订单备注处理开始，订单号：{}，备注：{}", orderEntity.getOrderSn(), remark);
        //正则匹配是否包含老订单号
        List<String> result = extractOrderNumbers(remark);
        if (CollectionUtil.isNotEmpty(result)) {
            log.info("订单备注处理结束，订单号：{}，备注：{}，匹配到老订单号：{}", orderEntity.getOrderSn(), remark, result);
            String oldOrderSn = result.get(0);
            HeOrderEntity oldOrderEntity = orderRepository.getByOrderSn(oldOrderSn);
            //匹配到了老订单
            if (Objects.nonNull(oldOrderEntity) && !oldOrderEntity.isNewOrder() && orderEntity.getBasicUid().equals(oldOrderEntity.getBasicUid())) {
                log.info("订单备注处理结束，订单号：{}，备注：{}，匹配到订单号：{}", orderEntity.getOrderSn(), remark, oldOrderSn);
                List<ContractSignRecordVO2> contractSignRecordVO2s = contractManager.getContractSignRecordListByOrderId(oldOrderEntity.getOrderId().longValue());
                contractSignRecordVO2s = contractSignRecordVO2s.stream().filter(o -> Arrays.asList(com.stbella.contract.model.enums.TemplateContractTypeEnum.YZ_SAINTBELLA.getCode(), com.stbella.contract.model.enums.TemplateContractTypeEnum.YZ_NURSE.getCode(), com.stbella.contract.model.enums.TemplateContractTypeEnum.YZ_SMALL.getCode()).contains(o.getTemplateContractType()) && ContractStatusEnum.SIGNED.code().equals(o.getContractStatus())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(contractSignRecordVO2s)) {
                    log.info("订单备注处理结束，订单号：{}，备注：{}，主合同已签署", orderEntity.getOrderSn(), remark);
                    return true;
                }
            }
        }
        //查找这个用户签过对应的服务合同没有
        if (orderEntity.isNewOrder()) {
            boolean checkNewOrder = checkNewOrder(orderEntity);
            log.info("订单备注处理结束，订单号：{}，备注：{}，新订单是否签署：{}", orderEntity.getOrderSn(), remark, checkNewOrder);
            return checkNewOrder;
        }
        return false;
    }

    public boolean checkNewOrder(HeOrderEntity orderEntity) {
        Long templateId = getTemplateId(orderEntity);
        if (Objects.isNull(templateId)) {
            log.info("订单备注处理结束，订单号：{}，备注：{}，未找到对应的合同模板", orderEntity.getOrderSn(), orderEntity.getRemark());
            return false;
        }
        List<TabClientPO> tabClientPOS = tabClientManager.listByBasicUid(orderEntity.getBasicUid());
        log.info("订单备注处理结束，客户信息：{}", JSONObject.toJSONString(tabClientPOS));
        ContractQueryV4 contractQueryV4 = new ContractQueryV4();
        contractQueryV4.setContractStatus(Collections.singletonList(ContractStatusEnum.SIGNED.code()));
        contractQueryV4.setClientIdList(tabClientPOS.stream().map(tabClientPO -> tabClientPO.getId().intValue()).collect(Collectors.toList()));
        ContractTemplateRes contractTemplateParam = contractManager.getContractTemplateParam(templateId);
        contractQueryV4.setTemplateFileId(contractTemplateParam.getEsignFileId());
        List<OrderContractSignRecordVO> orderContractSignRecordVOS = contractManager.queryByV4(contractQueryV4);
        if (CollectionUtil.isNotEmpty(orderContractSignRecordVOS)) {
            log.info("订单备注处理结束，订单号：{}，备注：{}，新订单是否签署：{}", orderEntity.getOrderSn(), orderEntity.getRemark(), true);
            return true;
        }
        return false;
    }

    protected Long getTemplateId(HeOrderEntity heOrder) {

        ContractFact contractFact = setContractFact(heOrder);
        contractFact = buildFactExtra(contractFact);
        ExecuteRuleV2Req req = new ExecuteRuleV2Req();
        req.setSceneCode(RuleLinkClient.SCENE_TEMPLATE_ID);
        req.setFactObj(contractFact);
        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(req);
        if (Objects.isNull(hitRuleVo)) {
            return null;
        }
        return Long.valueOf(hitRuleVo.getSimpleRuleValue());
    }

    protected ContractFact buildFactExtra(ContractFact contractFact) {
        return contractFact;
    }

    private ContractFact setContractFact(HeOrderEntity order) {
        Integer storeId = order.getStoreId();
        CfgStoreEntity cfgStore = storeManager.queryByStoreId(storeId);
        ContractFact contractFact = new ContractFact();
        Integer templateContractType = TemplateContractTypeEnum.YZ_SAINTBELLA.code();
        contractFact.setSecondSort(templateContractType);
        contractFact.setTemplateContractType(templateContractType);
        contractFact.setContractType(ContractTypeEnum.ESIGN_TYPE.code());
        contractFact.setStoreId(cfgStore.getStoreId());
        contractFact.setStoreType(cfgStore.getType());
        contractFact.setChildType(cfgStore.getChildType());
        contractFact.setOrderType(order.getOrderType());
        contractFact.setOrderId(order.getOrderId());
        contractFact.setOrderSn(order.getOrderSn());
        contractFact.setClientUid(order.getClientUid());
        contractFact.setTemplateType(TemplateTypeEnum.MAIN_TYPE.code());
        contractFact.setFirstSort(TemplateTypeEnum.MAIN_TYPE.code());
        contractFact.setStandardOrderV1(order.isNewOrder());
        return contractFact;
    }


    /**
     * 从输入字符串中提取符合条件的订单号。
     *
     * @param input 输入的包含订单类型及订单号的字符串
     * @return 提取到的订单号列表
     */
    public List<String> extractOrderNumbers(String input) {
        // 定义正则表达式
        Pattern pattern = Pattern.compile(matchPattern);
        Matcher matcher = pattern.matcher(input);
        // 用于存储匹配结果的列表
        List<String> orderNumbers = new ArrayList<>();
        // 查找所有匹配项并添加到列表中
        while (matcher.find()) {
            orderNumbers.add(matcher.group());
        }
        return orderNumbers;
    }


}


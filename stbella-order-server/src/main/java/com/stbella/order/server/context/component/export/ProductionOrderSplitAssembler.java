package com.stbella.order.server.context.component.export;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.common.enums.order.PartDiscountEnum;
import com.stbella.order.common.enums.order.ProduceAmountDeductionEnum;
import com.stbella.order.common.enums.production.OrderProductionItemEnum;
import com.stbella.order.common.enums.production.OrderProductionTypeEnum;
import com.stbella.order.common.enums.production.ProductionServeTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.production.GoodsSkuEntity;
import com.stbella.order.domain.order.production.OrderProductionCardExtendEntity;
import com.stbella.order.domain.repository.*;
import com.stbella.order.infrastructure.repository.mapper.saas.OrderGiftExtendMapper;
import com.stbella.order.infrastructure.repository.mapper.saas.OrderProductionCardExtendMapper;
import com.stbella.order.infrastructure.repository.mapper.saas.OrderProductionExtendMapper;
import com.stbella.order.infrastructure.repository.po.OrderGiftExtend;
import com.stbella.order.infrastructure.repository.po.OrderProductionCardExtend;
import com.stbella.order.infrastructure.repository.po.OrderProductionExtend;
import com.stbella.order.server.convert.OrderGoodsConverter;
import com.stbella.order.server.order.*;
import com.stbella.order.server.order.order.req.OrderExportReq;
import com.stbella.order.server.order.production.component.OrderAmountSplitComponent;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.apache.pulsar.shade.com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: jijunjian
 * @CreateTime: 2023-07-25  14:39
 * @Description: 产康订单分摊，并写入db
 */
@Component
@Slf4j
public class ProductionOrderSplitAssembler {

    @Resource
    OrderProductionExtendRepository extendRepository;
    @Resource
    OrderGiftExtendRepository giftExtendRepository;
    @Resource
    OrderProductionCardExtendRepository cardExtendRepository;
    @Resource
    StoreRepository storeRepository;
    @Resource
    GoodsRepository goodsRepository;
    @Resource
    GoodsSkuRepository goodsSkuRepository;
    @Resource
    OrderGoodsConverter orderGoodsConverter;
    @Resource
    OrderAmountSplitComponent orderAmountSplitComponent;
    @Resource
    RuleLinkClient ruleLinkClient;
    @Resource
    OrderProductionExtendMapper orderProductionExtendMapper;
    @Resource
    OrderGiftExtendMapper giftExtendMapper;
    @Resource
    OrderProductionCardExtendMapper cardExtendMapper;


    /**
     * 组装产康订单导出数据
     *
     * @param req
     * @return
     */
    @SneakyThrows
    public List<ProductionOrderExportDTO> assemble(OrderExportReq req) {

        //查询产康订单明细
        List<ProductionOrderGoodsModel> productionOrderGoodsList = extendRepository.queryProductionOrderGoodsList(req);
        log.info("查询产康订单商品{}", productionOrderGoodsList.size());


        // 生成以订单号为key的map
        Map<Integer, ProductionOrderGoodsModel> orderGoodsMap = new HashMap<>(1024);
        productionOrderGoodsList.forEach(goods -> {
            if (!orderGoodsMap.containsKey(goods.getOrderId())) {
                Integer paidAmount = goods.getPayAmount() - goods.getProductionAmountPay();
                goods.setPaidAmount(paidAmount);
                orderGoodsMap.put(goods.getOrderId(), goods);
            }
            if (OrderProductionItemEnum.TYPE_PRODUCTION.code().equals(goods.getType())) {
                // 有些sku，是多个sku打包的，价格要除以数量。
                Integer price = (goods.getPrice() / (goods.getGoodsNum() * goods.getSkuNum()));
                goods.setPrice(price);
            }
        });


        // 所有门店
        List<CfgStoreEntity> allStore = storeRepository.queryAllStore();


        List<ProductionOrderExportDTO> productionOrderExportDTOS = Lists.newArrayList();
        //单项服务
        List<ProductionOrderGoodsModel> singleServiceList = productionOrderGoodsList.stream().filter(a -> OrderProductionItemEnum.TYPE_PRODUCTION.code().equals(a.getType())).collect(Collectors.toList());
        List<ProductionOrderExportDTO> singleServiceExportList = orderGoodsConverter.goodsSku2ExportList(singleServiceList);
        singleServiceExportList.forEach(sku -> {
            sku.setOrderSellType(OrderProductionTypeEnum.BUY.desc());
            sku.setServiceTypeCode(OrderProductionItemEnum.TYPE_PRODUCTION.code());
            sku.setServiceType(OrderProductionItemEnum.TYPE_PRODUCTION.desc());
            sku.setOrderTypeName(OmniOrderTypeEnum.PRODUCTION_ORDER.getDesc());
            sku.setChildSkuPrice(sku.getSkuPrice());
            sku.setChildSkuId(sku.getSkuId());
            sku.setChildGoodsId(sku.getGoodsId());
        });
        log.info("单项商品处理完成");

        productionOrderExportDTOS.addAll(singleServiceExportList);

        //卡项服务
        List<ProductionOrderGoodsModel> cardServiceList = productionOrderGoodsList.stream()
                .filter(a -> a.getType().equals(OrderProductionItemEnum.TYPE_GROUP.code()) || a.getType().equals(OrderProductionItemEnum.TYPE_COUNT.code()))
                .collect(Collectors.toList());

        List<ProductionOrderExportDTO> cardExportData = parseCardData(cardServiceList, req, allStore);
        productionOrderExportDTOS.addAll(cardExportData);
        log.info("卡项商品处理完成");


        //查询产康赠品(下单)
        List<ProductionOrderGoodsModel> giftList = giftExtendRepository.queryProductionOrderGiftList(req);
        List<ProductionOrderExportDTO> productionOrderGiftExport = parseGiftData(giftList, OrderProductionTypeEnum.GIFT);
        productionOrderExportDTOS.addAll(productionOrderGiftExport);
        log.info("赠品处理完成{}", productionOrderGiftExport.size());

        // productionOrderExportDTOS 按订单号分组
        Map<Integer, List<ProductionOrderExportDTO>> orderMap = productionOrderExportDTOS.stream().collect(Collectors.groupingBy(ProductionOrderExportDTO::getOrderId));

        // 按订单计算分摊金额
        log.info("按订单计算分摊金额");
        orderMap.forEach((orderId, orderList) -> {
            fillProductInfo(orderList, allStore);
            List<ProductionOrderGoodsModel> orderGoods = productionOrderGoodsList.stream().filter(a -> a.getOrderId().equals(orderId)).collect(Collectors.toList());
            List<ProductionOrderGoodsModel> orderGiftList = giftList.stream().filter(a -> a.getOrderId().equals(orderId)).collect(Collectors.toList());
            OrderGoodsForSplitValueModel orderGoodsForSplitValueModel = splitAmount(orderList, orderGoods, orderGiftList);
            fillSplitResult(orderGoodsForSplitValueModel, orderList);
        });
        //加速回收
        orderMap = null;

        //格式化
        formatFiled(productionOrderExportDTOS);
        log.info("数据处理完成, 开始写分摊金额");
        // 写入分单项，卡项。

        productionOrderExportDTOS.forEach(item -> {

            log.info("开始写分摊金额 orderSn={}, skuId={}, realP={}, getChildSkuPayAmount={},getServiceTypeCode={}", item.getOrderSn(), item.getChildSkuId(), item.getRealPaid(), item.getChildSkuPayAmount(), item.getServiceTypeCode());

            CombineTypeEnum compineTypeEnum = CombineTypeEnum.from(item.getServiceTypeCode());

            BigDecimal realPaid = new BigDecimal(item.getChildSkuPayAmount());

            switch (compineTypeEnum) {
                case SIMPLE:
                    // 区分礼与购
                    if (OrderProductionTypeEnum.BUY.desc().equals(item.getOrderSellType())) {
                        OrderProductionExtend orderProductionExtend = new OrderProductionExtend();
                        orderProductionExtend.setId(item.getOrderProductionId());
                        orderProductionExtend.setRealPaid(realPaid);
                        orderProductionExtendMapper.updateByPrimaryKeySelective(orderProductionExtend);
                    } else {
                        OrderGiftExtend orderGiftExtend = new OrderGiftExtend();
                        orderGiftExtend.setId(item.getOrderProductionId());
                        orderGiftExtend.setRealPaid(realPaid);
                        giftExtendMapper.updateByPrimaryKeySelective(orderGiftExtend);
                    }
                    break;
                case COUNT_CARD:
                    OrderProductionCardExtend orderProductionCardExtend = new OrderProductionCardExtend();
                    orderProductionCardExtend.setId(item.getCardExtendId());
                    orderProductionCardExtend.setRealPaid(realPaid);
                    cardExtendMapper.updateByPrimaryKeySelective(orderProductionCardExtend);

                    OrderProductionExtend orderProductionExtend = new OrderProductionExtend();
                    orderProductionExtend.setId(item.getOrderProductionId());
                    orderProductionExtend.setRealPaid(new BigDecimal(0));
                    orderProductionExtendMapper.updateByPrimaryKeySelective(orderProductionExtend);

                    break;
                case GROUP_CARD:
                    OrderProductionCardExtendEntity cardExtend = cardExtendRepository.queryById(item.getCardExtendId());
                    List<OrderProductionCardContentItermDTO> contentItermDTOS = JSONUtil.toList(cardExtend.getContent(), OrderProductionCardContentItermDTO.class);
                    List<BigDecimal> realPaidList = new ArrayList<>();
                    if (CollectionUtil.isNotEmpty(contentItermDTOS)) {
                        realPaidList.add(realPaid);
                        contentItermDTOS.forEach(contentItermDTO -> {
                            if (contentItermDTO.getSku_id().equals(item.getChildSkuId())) {
                                contentItermDTO.setRealPaid(realPaid);
                            }
                            if (Objects.nonNull(contentItermDTO.getRealPaid())) {
                                realPaidList.add(contentItermDTO.getRealPaid());
                            }

                        });
                        // 获取 realPaidList 中最大值
                        BigDecimal maxRealPaid = new BigDecimal(0);
                        final Optional<BigDecimal> bigDecimalMax = realPaidList.stream().max(BigDecimal::compareTo);
                        if (bigDecimalMax.isPresent()) {
                            maxRealPaid = bigDecimalMax.get();
                        }

                        OrderProductionCardExtend groupCardExtend = new OrderProductionCardExtend();
                        groupCardExtend.setId(item.getCardExtendId());
                        groupCardExtend.setRealPaid(maxRealPaid);
                        groupCardExtend.setContent(JSONUtil.toJsonStr(contentItermDTOS));
                        cardExtendMapper.updateByPrimaryKeySelective(groupCardExtend);
                    }

                    OrderProductionExtend extend = new OrderProductionExtend();
                    extend.setId(item.getOrderProductionId());
                    extend.setRealPaid(new BigDecimal(0));
                    orderProductionExtendMapper.updateByPrimaryKeySelective(extend);

                    break;
                default:
                    log.info("卡项类型错误 开始写分摊金额 orderSn={}, skuId={}, realP={}, getChildSkuPayAmount={},getServiceTypeCode={}", item.getOrderSn(), item.getChildSkuId(), item.getRealPaid(), item.getChildSkuPayAmount(), item.getServiceTypeCode());
                    break;
            }
        });

        log.info("写分摊完成");

        return productionOrderExportDTOS;
    }


    /**
     * productionOrderExportDTOS 格式化金额时间等字段
     */
    protected void formatFiled(List<ProductionOrderExportDTO> productionOrderExportDTOS) {
        String noTimeTag = "1970-01-01 08:00:00";
        productionOrderExportDTOS.forEach(item -> {
            // 金额从分变成元
            item.setTotalPayAmount(AmountChangeUtil.changeF2Y(item.getTotalPayAmount()));
            item.setPayAmount(AmountChangeUtil.changeF2Y(item.getPayAmount()));
            item.setPaidAmount(AmountChangeUtil.changeF2Y(item.getPaidAmount()));
            item.setProductionAmount(AmountChangeUtil.changeF2Y(item.getProductionAmount()));
            item.setSkuPrice(AmountChangeUtil.changeF2Y(item.getSkuPrice()));
            item.setSkuSellingPrice(AmountChangeUtil.changeF2Y(item.getSkuSellingPrice()));
            item.setSkuProductionAmount(AmountChangeUtil.changeF2Y(item.getSkuProductionAmount()));
            item.setSkuPayAmount(AmountChangeUtil.changeF2Y(item.getSkuPayAmount()));

            item.setChildSkuPrice(AmountChangeUtil.changeF2Y(item.getChildSkuPrice()));
            item.setChildSkuSellingPrice(AmountChangeUtil.changeF2Y(item.getChildSkuSellingPrice()));
            item.setChildSkuProductionAmount(AmountChangeUtil.changeF2Y(item.getChildSkuProductionAmount()));
            item.setChildSkuPayAmount(AmountChangeUtil.changeF2Y(item.getChildSkuPayAmount()));

            if (noTimeTag.equals(item.getAssetStartTime())) {
                item.setAssetStartTime("");
            }
        });
    }


    /**
     * 解析卡项数据 为多条记录
     *
     * @return
     */
    protected List<ProductionOrderExportDTO> parseCardData(List<ProductionOrderGoodsModel> cardServiceList, OrderExportReq req, List<CfgStoreEntity> allStore) {

        List<ProductionOrderExportDTO> productionOrderExportDTOS = Lists.newArrayList();
        // 补充卡项商品信息（卡项要去）, 还要查询价格
        List<OrderProductionCardExtendEntity> orderProductionCardExtendEntities = cardExtendRepository.queryProductionOrderCardList(req);

        cardServiceList.forEach(card -> {

            List<OrderProductionCardExtendEntity> cardExtendEntityList = orderProductionCardExtendEntities.stream().filter(a -> a.getOrderId().equals(card.getOrderId()) && a.getOrderProductionId().equals(card.getId())).collect(Collectors.toList());

            log.info("{}卡项商品处理完成 getOrderProductionId={}, size={}",card.getOrderSn(),card.getId(), cardExtendEntityList.size());

            // 卡项，拆分成多个商品加入到导出列表
            cardExtendEntityList.forEach(cardExtend -> {
                List<OrderProductionCardContentItermDTO> contentItermDTOS = new ArrayList<>();
                if (OrderProductionItemEnum.TYPE_COUNT.code().equals(card.getType())) {
                    // 次卡是 json对象
                    OrderProductionCardContentItermDTO orderProductionCardContentItermDTO = JSONUtil.toBean(cardExtend.getContent(), OrderProductionCardContentItermDTO.class);
                    contentItermDTOS.add(orderProductionCardContentItermDTO);
                } else {
                    //通卡是 json数组
                    contentItermDTOS = JSONUtil.toList(cardExtend.getContent(), OrderProductionCardContentItermDTO.class);
                }
                //根据具体商品生成一条记录
                contentItermDTOS.forEach(itermDTO -> {
                    ProductionOrderExportDTO sku = orderGoodsConverter.goodsSku2Export(card);
                    sku.setOrderSellType(OrderProductionTypeEnum.BUY.desc());
                    sku.setCardContent(cardExtend.getContent());
                    sku.setServiceType(OrderProductionItemEnum.fromCode(card.getType()));
                    sku.setServiceTypeCode(card.getType());
                    sku.setProductionGroupName(cardExtend.getName());
                    sku.setProductionChildName(itermDTO.getGoods_name() + "/" + itermDTO.getSku_name());
                    sku.setNum(cardExtend.getNum() + "");
                    sku.setUsedNum(cardExtend.getUsedNum() + "");
                    sku.setResidueNum((cardExtend.getNum() - cardExtend.getUsedNum()) + "");
                    sku.setCardExtendId(cardExtend.getId());

                    //先看卡项，再用商品表
                    Long endTime = cardExtend.getValidEndTime();
                    if (Objects.isNull(endTime) || endTime.intValue() <= 0) {
                        endTime = card.getValidEndTime();
                    }
                    sku.setValidEndTime(DateUtil.formatDateTime(new Date(endTime * 1000L)));
                    sku.setChildSkuId(itermDTO.getSku_id());
                    sku.setChildGoodsId(itermDTO.getGoods_id());
                    sku.setGroupId(cardExtend.getGroupId());
                    sku.setGroupShareCount(cardExtend.getNum());
                    sku.setOrderTypeName(OmniOrderTypeEnum.PRODUCTION_ORDER.getDesc());


                    productionOrderExportDTOS.add(sku);
                });
            });
        });

        return productionOrderExportDTOS;
    }

    /**
     * 解析赠品数据
     *
     * @param giftList
     * @return
     */
    protected List<ProductionOrderExportDTO> parseGiftData(List<ProductionOrderGoodsModel> giftList, OrderProductionTypeEnum typeEnum) {
        List<ProductionOrderExportDTO> productionOrderExportDTOS = Lists.newArrayList();

        giftList.forEach(gift -> {
            ProductionOrderExportDTO sku = orderGoodsConverter.goodsSku2Export(gift);
            if (Objects.nonNull(typeEnum)) {
                sku.setOrderSellType(typeEnum.desc());
            } else {
                //根据source 判断，1表示是订单送的，其他表示是后台
                if (Objects.equals(gift.getSource(), 1)) {
                    sku.setOrderSellType(OrderProductionTypeEnum.GIFT.desc());
                } else {
                    sku.setOrderSellType(OrderProductionTypeEnum.GIFT_ADMIN.desc());
                }
            }

            sku.setServiceType(OrderProductionItemEnum.TYPE_PRODUCTION.desc());
            sku.setServiceTypeCode(OrderProductionItemEnum.TYPE_PRODUCTION.code());
            sku.setOrderProductionId(gift.getId());
            sku.setOrderTypeName(OmniOrderTypeEnum.getValueByCode(gift.getOrderType()));
            sku.setChildSkuPrice(gift.getPrice() + "");
            sku.setChildSkuSellingPrice("0");
            sku.setProductionAmount("0");
            sku.setPayAmount("0");
            sku.setSkuPrice("0");
            sku.setChildSkuProductionAmount("0");
            sku.setChildSkuPayAmount("0");
            sku.setValidEndTime(DateUtil.formatDateTime(new Date(gift.getValidEndTime() * 1000)));
            sku.setOrderProductionId(gift.getId());


            productionOrderExportDTOS.add(sku);
        });
        return productionOrderExportDTOS;
    }


    /**
     * 单个订单商品分摊金额
     *
     * @param productionOrderExportDTOS
     */
    protected OrderGoodsForSplitValueModel splitAmount(List<ProductionOrderExportDTO> productionOrderExportDTOS, List<ProductionOrderGoodsModel> orderGoods
            , List<ProductionOrderGoodsModel> giftList) {

        String speProductName = "黄金三件套体验卡";
        String speGroupName = "JAMU全身护理尊享1天";


        // 订单列 转化成 OrderGoodsForSplitValueModel
        OrderGoodsForSplitValueModel orderGoodsForSplitValueModel = new OrderGoodsForSplitValueModel();
        if (CollectionUtil.isEmpty(productionOrderExportDTOS)) {
            return null;
        }
        orderGoodsForSplitValueModel.setOrderId(productionOrderExportDTOS.get(0).getOrderId());
        orderGoodsForSplitValueModel.setPayAmount(Integer.valueOf(productionOrderExportDTOS.get(0).getPayAmount()));
        orderGoodsForSplitValueModel.setProductionAmountPay(Integer.valueOf(productionOrderExportDTOS.get(0).getProductionAmount()));

        // 设置订单商品
        List<ProductionGoodsSkuModel> orderGoodsList = new ArrayList<>();
        orderGoods.forEach(item -> {
            // 处理单项与卡项
            ProductionGoodsSkuModel productionGoodsSkuModel = new ProductionGoodsSkuModel();
            productionGoodsSkuModel.setSkuId(item.getSkuId());
            productionGoodsSkuModel.setGoodsPrice(item.getPrice());
            productionGoodsSkuModel.setCombineType(item.getType());
            productionGoodsSkuModel.setOrderProductionId(item.getId());


            if (!item.getType().equals(OrderProductionItemEnum.TYPE_PRODUCTION.code())) {
                List<ProductionGoodsSkuModel> childSkuList = new ArrayList<>();
                // 卡项 - 要处理子项目
                List<ProductionOrderExportDTO> childList = productionOrderExportDTOS.stream().filter(a -> a.getOrderProductionId().equals(item.getId())).collect(Collectors.toList());
                childList.forEach(child -> {
                    ProductionGoodsSkuModel childSku = new ProductionGoodsSkuModel();
                    childSku.setSkuId(child.getChildSkuId());
                    if (Strings.isBlank(child.getChildSkuPrice())) {
                        child.setChildSkuPrice("0");
                    }
                    childSku.setGoodsPrice(Integer.valueOf(child.getChildSkuPrice()));
                    if (child.getProductionName().equals(speProductName) && child.getProductionGroupName().equals(speGroupName)) {
                        //赠送的，价格为0；
                        childSku.setGoodsPrice(0);
                    }
                    childSku.setCombineType(OrderProductionItemEnum.TYPE_PRODUCTION.code());
                    childSku.setGroupId(child.getGroupId());
                    childSku.setGroupShareCount(child.getGroupShareCount());
                    childSku.setIsFullMoonType(child.getChildServiceType());
                    childSku.setServiceType(child.getChildServiceType());

                    childSkuList.add(childSku);
                });
                productionGoodsSkuModel.setIsFullMoonType(PartDiscountEnum.NO.code());
                productionGoodsSkuModel.setChildSkuList(childSkuList);

            } else {
                productionGoodsSkuModel.setIsFullMoonType(PartDiscountEnum.YES.code());
            }

            productionGoodsSkuModel.setOrderSellType(OrderProductionTypeEnum.BUY.desc());
            orderGoodsList.add(productionGoodsSkuModel);
        });

        // 设置赠品
        giftList.forEach(gift -> {
            ProductionGoodsSkuModel productionGoodsSkuModel = new ProductionGoodsSkuModel();
            productionGoodsSkuModel.setOrderProductionId(gift.getId());
            productionGoodsSkuModel.setSkuId(gift.getSkuId());
            productionGoodsSkuModel.setGoodsPrice(gift.getPrice());
            productionGoodsSkuModel.setCombineType(OrderProductionItemEnum.TYPE_PRODUCTION.code());
            productionGoodsSkuModel.setOrderSellType(OrderProductionTypeEnum.GIFT.desc());
            productionGoodsSkuModel.setIsFullMoonType(PartDiscountEnum.YES.code());

            orderGoodsList.add(productionGoodsSkuModel);
        });


        orderGoodsForSplitValueModel.setSkuList(orderGoodsList);
        orderAmountSplitComponent.split(orderGoodsForSplitValueModel);

        log.info("订单分摊{}：{}", productionOrderExportDTOS.get(0).getOrderSn(), JSONUtil.toJsonStr(orderGoodsForSplitValueModel));

        return orderGoodsForSplitValueModel;

    }


    /**
     * 填充分担结果
     * 根据不同类型，不同id， 填充不同商品和子项
     *
     * @param productionOrderExportDTOS
     */
    protected void fillSplitResult(OrderGoodsForSplitValueModel splitResult, List<ProductionOrderExportDTO> productionOrderExportDTOS) {

        productionOrderExportDTOS.forEach(item -> {
            List<ProductionGoodsSkuModel> skuList = splitResult.getSkuList().stream().filter(
                    sku -> sku.getOrderProductionId().equals(item.getOrderProductionId())
                            && sku.getOrderSellType().equals(item.getOrderSellType())
                            && sku.getSkuId().equals(item.getSkuId())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(skuList)) {
                item.setSkuSellingPrice(skuList.get(0).getSellingPrice() + "");
                item.setSkuProductionAmount(skuList.get(0).getProductionAmount() + "");
                item.setSkuPayAmount(skuList.get(0).getPayAmount() + "");

                //所有子项都显示价格
                item.setChildSkuSellingPrice(skuList.get(0).getSellingPrice() + "");
                item.setChildSkuProductionAmount(skuList.get(0).getProductionAmount() + "");
                item.setChildSkuPayAmount(skuList.get(0).getPayAmount() + "");

                //log.info("订单分摊{}：{}设置sku价格",productionOrderExportDTOS.get(0).getOrderSn(), JSONUtil.toJsonStr(skuList.get(0)) );

                // 子项
                if (Objects.nonNull(item.getChildSkuId())) {
                    if (CollectionUtil.isNotEmpty(skuList.get(0).getChildSkuList())) {
                        Optional<ProductionGoodsSkuModel> childOpt = skuList.get(0).getChildSkuList().stream().filter(a -> a.getSkuId().equals(item.getChildSkuId()) && a.getGroupId().equals(item.getGroupId())).findFirst();
                        if (childOpt.isPresent()) {
                            item.setChildSkuSellingPrice(childOpt.get().getSellingPrice() + "");
                            item.setChildSkuProductionAmount(childOpt.get().getProductionAmount() + "");
                            item.setChildSkuPayAmount(childOpt.get().getPayAmount() + "");

                            //log.info("订单分摊{}：{}设置child sku价格",productionOrderExportDTOS.get(0).getOrderSn(), childOpt.get());
                        }
                    }
                }
            }
        });
    }

    /**
     * 订单商品信息填充
     *
     * @param itemList
     */
    protected void fillProductInfo(List<ProductionOrderExportDTO> itemList, List<CfgStoreEntity> allStore) {

        // 获取产康商品
        Set<Integer> skuIds = itemList.stream().map(ProductionOrderExportDTO::getChildSkuId).collect(Collectors.toSet());
        List<ProductionGoodsSkuModel> productionGoodsSkuModels = goodsRepository.queryProductionGoods(skuIds.stream().collect(Collectors.toList()));
        //按sku id 生成map
        Map<Integer, ProductionGoodsSkuModel> productionGoodsSkuModelMap = productionGoodsSkuModels.stream().collect(Collectors.toMap(ProductionGoodsSkuModel::getSkuId, Function.identity(), (item1, item2) -> item1));

        itemList.forEach(sku -> {
            sku.setChildServiceType(ProductionServeTypeEnum.APPOINTMENT_TYPE_PROPRIETARY.code());
            sku.setChildProduceAmountDeduction(ProduceAmountDeductionEnum.NO.code());
            if (productionGoodsSkuModelMap.containsKey(sku.getChildSkuId())) {
                ProductionGoodsSkuModel productionGoodsSkuModel = productionGoodsSkuModelMap.get(sku.getChildSkuId());
                sku.setChildServiceType(productionGoodsSkuModel.getServiceType());
                sku.setScale(productionGoodsSkuModel.getScale());
                sku.setSupplierName(productionGoodsSkuModel.getSupplierName());
                sku.setSupplierId(productionGoodsSkuModel.getSupplierId());
                sku.setSettlementRatio(productionGoodsSkuModel.getSettlementRatio());
                sku.setTripartiteSettlementPrice(AmountChangeUtil.changeF2Y(productionGoodsSkuModel.getTripartiteSettlementPrice()).toString());
                sku.setTripartiteSettlementRatio(productionGoodsSkuModel.getTripartiteSettlementRatio());
                sku.setChildProduceAmountDeduction(productionGoodsSkuModel.getProduceAmountDeduction());

                // 卡项要的子项要设置价格

                sku.setChildSkuPrice(productionGoodsSkuModel.getGoodsPrice() + "");

                //三方商品需要去查询阶梯定价
                if (ProductionServeTypeEnum.APPOINTMENT_TYPE_TRIPARTITE.code().equals(sku.getChildServiceType())) {
                    CfgStoreEntity cfgStoreEntity = allStore.stream().filter(a -> a.getStoreId().equals(sku.getStoreId())).findFirst().orElse(null);
                    if (Objects.nonNull(cfgStoreEntity)) {
                        // 三方的商品，查询配置的阶梯定价

                        Integer percentFirstTime = 0;
                        try {
                            Date percentFirstTimeDate = DateUtils.parse(sku.getPercentFirstTime(), DateUtils.YYYY_MM_DD_HH_MM_SS);
                            percentFirstTime = DateUtils.getTenBitTimestamp(percentFirstTimeDate);
                        } catch (Exception e) {
                            log.error("解析时间发生异常", e);
                        }

                        Map<String, Object> factMap = new HashMap<>();
                        factMap.put("orderDate", percentFirstTime);
                        factMap.put("storeType", cfgStoreEntity.getType());
                        factMap.put("quantity", Integer.parseInt(sku.getNum()));

                        ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req("price_sku_" + sku.getChildSkuId(), factMap);
                        try {
                            HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
                            if (Objects.nonNull(hitRuleVo)) {
                                log.info("{} -规则引擎返回结果：{}", sku.getChildSkuId(), hitRuleVo.getSimpleRuleValue());
                                //转成分
                                Integer price = Integer.parseInt(hitRuleVo.getSimpleRuleValue()) * 100;
                                sku.setChildSkuPrice(price + "");
                            }
                        } catch (Exception e) {
                            log.error("规则引擎返回结果发生异常", e);
                        }
                    }

                }


            }
            sku.setSelfSupport(ProductionServeTypeEnum.fromCode(sku.getSelfSupportCode()));

        });

    }


}

package com.stbella.order.server.context.component.giftOrder.processor;

import com.stbella.base.server.ding.response.CreateOrderApproveRecordVO;
import com.stbella.notice.enums.AuditTypeEnum;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.order.OrderGiftReasonTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.HeOrderGiftExtendEntity;
import com.stbella.order.server.manager.MonthDingManager;
import com.stbella.order.server.order.month.req.HeOrderGiftInfoGoodsAddReq;
import com.stbella.order.server.order.month.req.HeOrderGiftInfoGoodsReq;
import com.stbella.order.server.order.month.res.HeOrderGiftInfoGoodsPicpVO;
import com.stbella.order.server.order.month.service.HeOrderGiftInfoService;
import com.stbella.order.server.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Component
@SnowballComponent(name = "创建礼赠订单审批", desc = "礼赠订单创建审批流程")
public class GiftOrderGoodsApproveProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private MonthDingManager monthDingManager;

    @Resource
    private HeOrderGiftInfoService heOrderGiftInfoService;

    @Override
    public void run(FlowContext bizContext) {

        log.info("礼赠订单创建审批开始");
        HeOrderGiftInfoGoodsAddReq heOrderGiftInfoGoodsAddReq = bizContext.getAttribute(HeOrderGiftInfoGoodsAddReq.class);
        HeOrderGiftExtendEntity heOrderGiftExtendEntity = bizContext.getAttribute(HeOrderGiftExtendEntity.class);
        HeOrderGiftInfoGoodsPicpVO heOrderGiftInfoGoodsPicpVO = heOrderGiftInfoService.queryGiftOrderInfoPicp(HeOrderGiftInfoGoodsReq.builder().id(heOrderGiftExtendEntity.getId()).build());
        HashMap<String, String> param = this.getApproveParam(heOrderGiftExtendEntity, heOrderGiftInfoGoodsPicpVO);
        CreateOrderApproveRecordVO approval = monthDingManager.createApproval(param, heOrderGiftInfoGoodsAddReq.getOperator().getOperatorPhone(), AuditTypeEnum.GIFT_ORDER_APPROVE.getCode());
        bizContext.setAttribute(BizConstant.ExtraKey.approveResult, Objects.nonNull(approval) && Objects.nonNull(approval.getStatus()) && approval.getStatus() == 1);
        if (Objects.nonNull(approval) && Objects.nonNull(approval.getId())){
            bizContext.setAttribute(BizConstant.ExtraKey.approveIdKey, approval.getId());
        }
    }

    private HashMap<String, String> getApproveParam(HeOrderGiftExtendEntity heOrderGiftExtendEntity, HeOrderGiftInfoGoodsPicpVO heOrderGiftInfoGoodsPicpVO) {

        HashMap<String, String> param = new HashMap<>();
        param.put("门店名称", heOrderGiftInfoGoodsPicpVO.getStoreName());
        param.put("品牌", heOrderGiftInfoGoodsPicpVO.getBrandName());
        param.put("战区", heOrderGiftInfoGoodsPicpVO.getWarZoneDesc());
        param.put("客户姓名", heOrderGiftInfoGoodsPicpVO.getCustomerName());
        param.put("创建时间", DateUtils.formatDateTime(heOrderGiftExtendEntity.getCreatedAt()));
        param.put("创建人", heOrderGiftInfoGoodsPicpVO.getCreateByStr());
        param.put("赠送记录编码", heOrderGiftExtendEntity.getGiftSn());
        param.put("赠送原因类型", OrderGiftReasonTypeEnum.getDesc(heOrderGiftExtendEntity.getGiftReasonType()));
        param.put("关联订单号", setStr(heOrderGiftExtendEntity.getOrderSn()));
        param.put("业绩生效日期", DateUtils.formatDateTime(heOrderGiftInfoGoodsPicpVO.getPerformanceEffectiveDate()));
        param.put("订单其他备注", heOrderGiftExtendEntity.getRemark());
        param.put("发布链接", heOrderGiftExtendEntity.getLinkUrl());
        param.put("具体原因说明", heOrderGiftExtendEntity.getGiftSpecificReason());
        param.put("证明截图", heOrderGiftExtendEntity.getEvidence());
        param.put("赠送商品价值", AmountChangeUtil.format2Y(heOrderGiftInfoGoodsPicpVO.getGiftGoodsPrice()));
        param.put("赠送商品明细", getGoodsRecordList(heOrderGiftInfoGoodsPicpVO.getGoodsRecordList()));
        return param;
    }

    private String getGoodsRecordList(List<HeOrderGiftInfoGoodsPicpVO.GoodsRecord> goodsRecordList){

        if(CollectionUtils.isEmpty(goodsRecordList)){
            return StringUtils.EMPTY;
        }

        StringBuilder goodsInfo = new StringBuilder();
        goodsRecordList.forEach(good -> {
            goodsInfo.append(good.getGoodsName()).append("/").append(good.getGoodsPriceOrgin()).append("/").append(good.getGoodsNum()).append("；");
        });
        return goodsInfo.toString();
    }

    private String setStr(String str) {
        if (StringUtils.isEmpty(str)) {
            return StringUtils.EMPTY;
        }
        return str;
    }
}

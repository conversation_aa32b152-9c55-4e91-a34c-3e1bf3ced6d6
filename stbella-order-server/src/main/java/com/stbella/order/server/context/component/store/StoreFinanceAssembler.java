package com.stbella.order.server.context.component.store;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.financial.res.BankAccountVo;
import com.stbella.financial.res.CompanyInfoVO;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.repository.StoreRepository;
import com.stbella.order.infrastructure.gateway.FinanceGateway;
import com.stbella.order.server.context.dto.BankAccountDto;
import com.stbella.order.server.context.dto.StoreFinanceDetailDto;
import com.stbella.store.core.vo.res.finance.StoreFinanceDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-05-29  15:20
 * @Description: 门店财务信息组装
 */
@Component
@Slf4j
@SnowballComponent(name = "门店财务信息组装", desc = "通过门店id组装相关信息")
public class StoreFinanceAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    StoreRepository storeRepository;
    @Resource
    FinanceGateway financeGateway;


    @Override
    public void run(FlowContext bizContext) {
        Integer storeId = (Integer) bizContext.getAttribute(BizConstant.ExtraKey.storeId);
        StoreFinanceDetailVO storeFinanceDetailVO = storeRepository.queryStoreFinance(storeId);

        StoreFinanceDetailDto storeFinanceDetailDto = BeanMapper.map(storeFinanceDetailVO, StoreFinanceDetailDto.class);
        CompanyInfoVO companyInfoVO = financeGateway.queryCompanyInfo(storeFinanceDetailVO.getCompanySubjectId());
        if (Objects.isNull(companyInfoVO)) {
            log.error("门店财务信息组装失败，未找到门店对应的公司信息，storeId:{}", storeId);
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "当前门店暂不支持e签宝链接签合同");
        }
        storeFinanceDetailDto.setCompanySubjectName(companyInfoVO.getCompanyName());

        if (CollectionUtil.isNotEmpty(companyInfoVO.getBankAccountList())) {
            List<BankAccountVo> usedBankAccountVos = companyInfoVO.getBankAccountList().stream().filter(a -> a.getAccountNo().equals(storeFinanceDetailVO.getAccountNo())).collect(Collectors.toList());
            List<BankAccountDto> bankAccountDtos = BeanMapper.mapList(usedBankAccountVos, BankAccountDto.class);
            storeFinanceDetailDto.setBankAccountList(bankAccountDtos);
        } else {
            log.error("门店财务信息组装失败，未找到门店对应的银行账户信息，storeId:{}", storeId);
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code() + "", companyInfoVO.getCompanyName() + "无对应银行账户信息");
        }
        log.info("门店财务信息组装成功，storeId:{}, data={}", storeId, JSONUtil.toJsonStr(storeFinanceDetailDto));

        bizContext.addAttribute(StoreFinanceDetailDto.class, storeFinanceDetailDto);
    }
}

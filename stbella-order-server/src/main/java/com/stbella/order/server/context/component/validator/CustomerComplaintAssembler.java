package com.stbella.order.server.context.component.validator;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.platform.order.api.req.CustomerComplaintsCreateReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

@Slf4j
@Component
@RefreshScope
@SnowballComponent(name = "客诉闭环校验")
public class CustomerComplaintAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderRepository orderRepository;


    @Override
    public void run(FlowContext bizContext) {
        CustomerComplaintsCreateReq complaintsCreateReq = bizContext.getAttribute(CustomerComplaintsCreateReq.class);
        Long orderId = complaintsCreateReq.getOrderId();
        if (ObjectUtil.isEmpty(orderId)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "订单不存在");
        }
        HeOrderEntity orderEntity = orderRepository.getByOrderId(orderId.intValue());
        bizContext.setAttribute(HeOrderEntity.class, orderEntity);
    }


    @Override
    public boolean condition(FlowContext context) {
        return true;
    }
}

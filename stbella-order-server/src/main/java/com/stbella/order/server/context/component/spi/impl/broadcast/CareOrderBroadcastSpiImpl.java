package com.stbella.order.server.context.component.spi.impl.broadcast;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.stbella.core.enums.BusinessEnum;
import com.stbella.core.result.Result;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.month.OrderStatusV2Enum;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.context.component.assembler.GoodsNamesForBroadcastAssembler;
import com.stbella.order.server.context.component.spi.OrderBroadcastSpi;
import com.stbella.order.server.context.dto.OrderBroadcastDto;
import com.stbella.order.server.order.month.component.NoticeAssembler;
import com.stbella.order.server.order.month.req.OrderNoticeReq;
import com.stbella.order.server.order.month.req.OrderQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.annotation.FuncPointSpiImpl;
import top.primecare.snowball.extpoint.definition.FuncSpiConfig;
import top.primecare.snowball.flow.core.context.FlowContext;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-05-30  14:01
 * @Description: 月子订单报单扩展点实现
 */
@Component
@Slf4j
@FuncPointSpiImpl(name = "月子订单报单扩展点实现")
public class CareOrderBroadcastSpiImpl implements OrderBroadcastSpi {

    @Resource
    private NoticeAssembler noticeAssembler;
    @Resource
    GoodsNamesForBroadcastAssembler goodsNamesForBroadcastAssembler;
    @Resource
    OrderRepository orderRepository;


    @Override
    public boolean condition(HeOrderEntity param) {
        if (CompareUtil.integerEqual(param.getBu(), BusinessEnum.CARE_CENTER.getCode())
                || ( CompareUtil.integerEqual(param.getOrderType(), OmniOrderTypeEnum.MONTH_ORDER.getCode())
                && CompareUtil.integerEqual(param.getOrderType(), OmniOrderTypeEnum.SMALL_MONTH_ORDER.getCode()))
        ) {
            return true;
        }
        return false;
    }

    @Override
    public Result invoke(HeOrderEntity order) {

        FlowContext bizContext = new FlowContext();
        bizContext.setAttribute(HeOrderEntity.class, order);
        goodsNamesForBroadcastAssembler.run(bizContext);
        OrderBroadcastDto broadcastDto = bizContext.getAttribute(OrderBroadcastDto.class);
        if (broadcastDto.getBroadcast()) {
            log.info("月子订单报单扩展点实现");

            if(!isPreparePregnancy(order)) {
                //非备孕订单才判断是否是续住订单
                //如果是续住订单，不用报单。 续住订单逻辑：如果当前有订单在入住中，则无需保单
                OrderQuery orderQuery = new OrderQuery()
                        .setBasicUid(order.getBasicUid())
                        .setOrderType(OmniOrderTypeEnum.MONTH_ORDER.getCode())
                        .setOrderStatusList(Lists.newArrayList(OrderStatusV2Enum.STAY_IN.getCode()))
                        .setIsNotice(2);
                List<HeOrderEntity> processingOrders = orderRepository.queryByCondition(orderQuery);
                if (CollectionUtil.isNotEmpty(processingOrders) && !order.getSpecificOrderItems()) {
                    log.info("续住订单不报单{}", order.getOrderSn());
                    return Result.failed("续住订单不报单");
                }
            }


            if (order.fetchServiceRoomDays() < 7 && !order.getSpecificOrderItems()) {
                log.info("订单套餐天数<7，不报单{}", order.getOrderSn());
                return Result.failed("订单套餐天数<7，不报单");
            }

            order.noticed();
            order.effectPerformance();

            OrderNoticeReq req = new OrderNoticeReq()
                    .setOrderId(order.getOrderId())
                    .setBasicUid(order.getBasicUid())
                    .setStaffId(order.getStaffId())
                    .setPercentFirstTime(order.getPercentFirstTime());
            final String ddMsg = noticeAssembler.queryNoticeAssembler(req);
            noticeAssembler.pushMsg(ddMsg, order.getStoreId());
            return Result.success();
        } else {
            log.info("此订单不报单{}", order.getOrderSn());
            return Result.failed("此订单不能报单");
        }
    }

    @Override
    public FuncSpiConfig config() {
        //默认优先级高
        return new FuncSpiConfig(true, 100);
    }

    /***
     * 是否备孕单
     * @param order
     * @return
     */
    private Boolean isPreparePregnancy(HeOrderEntity order) {
        // 若标记为备孕或者期望入住时间在一年之后的
        return Objects.equals(order.getIsPreparePregnancy(), 1) || order.getWantIn() > DateUtil.offsetMonth(new Date(), 12).getTime()/1000;
    }
}

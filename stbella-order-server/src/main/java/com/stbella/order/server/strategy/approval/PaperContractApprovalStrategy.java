package com.stbella.order.server.strategy.approval;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.contract.api.ContractSignRecordService;
import com.stbella.contract.model.res.ContractSignPaperVO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.month.server.request.PaperContractApprovalRequest;
import com.stbella.order.common.enums.month.*;
import com.stbella.order.common.utils.JsonUtil;
import com.stbella.order.domain.order.month.entity.AheadOutRoomEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.MonthContractSignRecordEntity;
import com.stbella.order.domain.repository.AheadOutRoomRepository;
import com.stbella.order.domain.repository.MonthContractSignRecordRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.manager.ContractManager;
import com.stbella.order.server.order.month.req.AheadOutRoomQuery;
import com.stbella.order.server.order.month.request.approval.ApprovalStatusInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * 纸质合同
 * @date 2024/3/18 17:45
 */
@Component
@Slf4j
public class PaperContractApprovalStrategy implements ApprovalStrategy {
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private ContractManager contractManager;
    @Resource
    private AheadOutRoomRepository aheadOutRoomRepository;

    @Resource
    private MonthContractSignRecordRepository monthContractSignRecordRepository;

    @Resource
    private ContractSignRecordService contractSignRecordService;

    @Override
    public void handleApproval(OrderApproveRecordTypeEnum type, String params, String messageId, ApprovalStatusInfo approvalStatusInfo, String processId) {
        PaperContractApprovalRequest paperContractApprovalRequest = JSONObject.parseObject(params, PaperContractApprovalRequest.class);
        ContractSignPaperVO contractSignPaperVO = contractManager.queryById(paperContractApprovalRequest.getRecordId());
        if (ObjectUtil.isEmpty(contractSignPaperVO)) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "钉钉审批完成纸质合同失败,找不到对应的纸质合同记录,合同ID=" + paperContractApprovalRequest.getRecordId());
        }
        boolean agree = approvalStatusInfo.isAgree();
        boolean finish = approvalStatusInfo.isFinish();
        boolean refuse = approvalStatusInfo.isRefuse();
        boolean terminate = approvalStatusInfo.isTerminate();
        //审批通过 纸质合同更新生效
        if (finish && agree) {
            List<MonthContractSignRecordEntity> mainContractList = monthContractSignRecordRepository.getListByOrderId(contractSignPaperVO.getOrderId());
            MonthContractSignRecordEntity recordEntity = mainContractList.stream().filter(item -> contractSignPaperVO.getTemplateContractType().equals(item.getTemplateContractType()) && ContractTypeEnum.ESIGN_TYPE.code().equals(item.getContractType())
                    && ContractStatusEnum.SIGNED.code().equals(item.getContractStatus())).findFirst().orElse(null);
            if (Objects.nonNull(recordEntity)){
                contractSignRecordService.deleteById(contractSignPaperVO.getId());
                return;
            } else {
                MonthContractSignRecordEntity recordEntityESign = mainContractList.stream().filter(item -> contractSignPaperVO.getTemplateContractType().equals(item.getTemplateContractType()) && ContractTypeEnum.ESIGN_TYPE.code().equals(item.getContractType())
                        && Arrays.asList(ContractStatusEnum.WAITING.code(), ContractStatusEnum.NOT_SIGNED.code()).contains(item.getContractStatus())).findFirst().orElse(null);
                if (Objects.nonNull(recordEntityESign)){
                    if (ContractStatusEnum.WAITING.code().equals(recordEntityESign.getContractStatus())){
                        contractSignRecordService.cancelSignFlowV3(recordEntityESign.getEsignFlowId());
                    }
                    contractSignRecordService.deleteById(recordEntityESign.getId());
                }
            }
            contractManager.updateStatusById(paperContractApprovalRequest.getRecordId(), ContractStatusEnum.SIGNED.code());
            //提前离管解除更新状态
            Optional.ofNullable(contractSignPaperVO).filter(i -> Objects.equals(TemplateContractTypeV2Enum.RELEASE.code(), i.getContractType())).ifPresent(
                    record -> {
                        AheadOutRoomQuery roomQuery = new AheadOutRoomQuery();
                        roomQuery.setOrderId(Math.toIntExact(record.getOrderId()));
                        AheadOutRoomEntity aheadOutRoomEntity = aheadOutRoomRepository.queryByOrderId(roomQuery);
                        Optional.ofNullable(aheadOutRoomEntity).ifPresent(aheadOutRoom -> {
                            aheadOutRoom.setState(AheadOutRoomEnum.STATE_DISENABLE.code());
                            aheadOutRoom.setAgreeAt(DateUtil.date());
                            aheadOutRoomRepository.updateOutRoom(aheadOutRoom);
                        });

                        //更新订单状态为 提前离管 状态
                        HeOrderEntity byOrderId = orderRepository.getByOrderId(Math.toIntExact(record.getOrderId()));
                        Optional.ofNullable(byOrderId).ifPresent(orderEntity -> {
                            orderEntity.setOrderStatus(OrderStatusV2Enum.ADVANCE_OUT_OF_STORE.getCode());
                            orderRepository.updateOrderMonthByOrderId(orderEntity);
                        });
                    }
            );
        }
    }

//    public static void main(String[] args) {
//        String json = "{\"orderId\":112238,\"phone\":\"15068116773\",\"storeName\":\"ISLA-深圳万豪行政小贝拉\",\"customerName\":\"冯佳奇\",\"customerPhone\":\"15068116773\",\"masterAddress\":\"\",\"supplementalAgreementAddressList\":[],\"contractName\":\"服务合同\",\"contractContentsList\":[\"https://cos.primecare.top/static/helper/6a3cd588d8a681cdf75c3a89b6ac5417.png\"],\"contractContentsListFileIdList\":null,\"reasonSigning\":\"6\",\"recordId\":105464}";
//        PaperContractApprovalRequest paperContractApprovalRequest = JSONObject.parseObject(json, PaperContractApprovalRequest.class);
//        System.out.println(paperContractApprovalRequest);
//    }

    @Override
    public OrderApproveRecordTypeEnum getOrderApproveRecordTypeEnum() {
        return OrderApproveRecordTypeEnum.PAPER_CONTRACT_APPROVAL;
    }
}

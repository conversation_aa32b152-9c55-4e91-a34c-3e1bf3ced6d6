package com.stbella.order.server.order.month.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.stbella.care.api.appointment.req.ListCheckInDateReq;
import com.stbella.care.api.appointment.res.RoomCheckInDateVO;
import com.stbella.care.server.care.service.RoomExternalQuery;
import com.stbella.core.base.PageVO;
import com.stbella.core.base.StoreInfoDTO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.core.utils.PageUtil;
import com.stbella.core.utils.PrivacyUtil;
import com.stbella.core.utils.sso.EmployeeTokenHelper;
import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.notice.entity.OaProcessIdRelationPO;
import com.stbella.notice.enums.AuditTypeEnum;
import com.stbella.notice.server.OaProcessIdRelationService;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.core.OrderRefundNatureEnum;
import com.stbella.order.common.enums.core.OrderRefundStatusEnum;
import com.stbella.order.common.enums.month.*;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.common.enums.production.OrderProductionTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.common.utils.ExcelUtil;
import com.stbella.order.domain.client.BaseClient;
import com.stbella.order.domain.client.BrandClient;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.domain.client.StoreGoodsClient;
import com.stbella.order.domain.order.month.dto.STMOrderExportDTO;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.production.GoodsCategoryBackEntity;
import com.stbella.order.domain.order.production.GoodsEntity;
import com.stbella.order.domain.order.production.OrderGiftExtendEntity;
import com.stbella.order.domain.order.production.OrderProductionExtendEntity;
import com.stbella.order.domain.order.service.OrderIncomeDomainService;
import com.stbella.order.domain.order.service.OrderRefundDomainService;
import com.stbella.order.domain.repository.*;
import com.stbella.order.domain.repository.condition.StoreQueryCondition;
import com.stbella.order.domain.utils.AsyncService;
import com.stbella.order.domain.utils.dto.AsyncResultDTO;
import com.stbella.order.infrastructure.oss.OSSFactory;
import com.stbella.order.server.config.DynamicConfig;
import com.stbella.order.server.context.component.StoreCurrencyContainer;
import com.stbella.order.server.contract.enums.IdTypeEnum;
import com.stbella.order.server.convert.OrderConvert;
import com.stbella.order.server.manager.BasicManager;
import com.stbella.order.server.manager.StoreManager;
import com.stbella.order.server.manager.TabClientManager;
import com.stbella.order.server.order.GoodsSimpleInfoModel;
import com.stbella.order.server.order.StoreGoodsSkuModel;
import com.stbella.order.server.order.cts.enums.RegionalEnum;
import com.stbella.order.server.order.cts.enums.YesOrNoEnum;
import com.stbella.order.server.order.month.component.MonthOrderExportDataAssembler;
import com.stbella.order.server.order.month.dto.PageDTO;
import com.stbella.order.server.order.month.enums.*;
import com.stbella.order.server.order.month.excel.STMOrderExport;
import com.stbella.order.server.order.month.excel.STMOrderRoomChangeExport;
import com.stbella.order.server.order.month.excel.STMOrderStayOverExport;
import com.stbella.order.server.order.month.req.*;
import com.stbella.order.server.order.month.res.*;
import com.stbella.order.server.order.month.service.AddressQueryService;
import com.stbella.order.server.order.month.service.MonthOrderAdminQueryService;
import com.stbella.order.server.order.month.service.MonthOrderWxQueryService;
import com.stbella.order.server.order.month.service.TagsService;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.order.server.order.order.res.OrderExport;
import com.stbella.order.server.oss.response.OssUploadResponse;
import com.stbella.order.server.utils.*;
import com.stbella.platform.order.api.OrderQueryService;
import com.stbella.platform.order.api.refund.res.QueryOrderRefundGoodsInfoRes;
import com.stbella.platform.order.api.req.CustomAttribute;
import com.stbella.platform.order.api.req.ExtraInfo;
import com.stbella.platform.order.api.res.PromotionInfo;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import com.stbella.scrm.enums.WeWorkAppEnum;
import com.stbella.store.common.enums.core.GoodsTypeEnum;
import com.stbella.store.core.enums.BrandTypeEnum;
import com.stbella.store.core.vo.res.store.BrandVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.openxmlformats.schemas.spreadsheetml.x2006.main.CTMergeCell;
import org.openxmlformats.schemas.spreadsheetml.x2006.main.CTMergeCells;
import org.openxmlformats.schemas.spreadsheetml.x2006.main.CTWorksheet;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 月子订单服务 后台查询服务实现类
 */
@Service
@Slf4j
@DubboService
public class MonthOrderAdminQueryServiceImpl implements MonthOrderAdminQueryService {

    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private OrderConvert orderConvert;
    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;
    @Resource
    private OrderBailorSnapshotRepository orderBailorSnapshotRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private ContractSignRecordPaperRepository contractSignRecordPaperRepository;
    @Resource
    private MonthContractSignRecordRepository monthContractSignRecordRepository;
    @Resource
    private OrderAdditionalRevenueRepository orderAdditionalRevenueRepository;
    @Resource
    private AsyncService asyncService;
    @Resource
    private StoreRepository storeRepository;
    @DubboReference
    private OaProcessIdRelationService oaProcessIdRelationService;
    @Resource
    private TagsService tagsService;
    @Resource
    private OrderVoucherRepository orderVoucherRepository;
    @Resource
    private OrderGoodsRepository orderGoodsRepository;
    @Resource
    private OrderContinueLiveRecordRepository orderContinueLiveRecordRepository;
    @Resource
    private OrderRoomTypeChangeRecordRepository orderRoomTypeChangeRecordRepository;
    @Resource
    private AheadOutRoomRepository aheadOutRoomRepository;
    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @Resource
    private UserRepository userRepository;
    @Resource
    private IncomeProofRecordRepository incomeProofRecordRepository;
    @Resource
    private MonthOrderWxQueryService monthOrderWxQueryService;
    @Resource
    private StayinRepository stayinRepository;
    @Resource
    private OrderGiftExtendRepository orderGiftExtendRepository;
    @Resource
    private TagsRepository tagsRepository;
    @Resource
    private GoodsRepository goodsRepository;
    @Resource
    private ClientRepository clientRepository;
    @Resource
    private AddressQueryService addressQueryService;
    @Resource
    MonthOrderExportDataAssembler monthOrderExportDataAssembler;
    @Resource
    private TabClientManager tabClientManager;
    @Resource
    private StoreGoodsClient storeClient;
    @Resource
    private OrderProductionExtendRepository orderProductionExtendRepository;
    @Resource
    private BasicManager basicManager;

    @Resource
    private GoodsCategoryBackRepository goodsCategoryBackRepository;

    @Resource
    private DynamicConfig dynamicConfig;

    @Resource
    private OrderIncomeDomainService orderIncomeDomainService;

    @Resource
    private BrandClient brandClient;

    @Resource
    private StoreManager storeManager;
    @Resource
    private OrderRefundDomainService orderRefundDomainService;
    @Resource
    private HeOrderRefundGoodsRepository orderRefundGoodsRepository;
    @Resource
    private OrderReductionRepository orderReductionRepository;


    @Resource
    private ThreadPoolTaskExecutor exportTaskThreadPool;

    @Resource
    private BaseClient baseClient;

    @DubboReference
    private RoomExternalQuery roomExternalQuery;

    @Resource
    private RuleLinkClient ruleLinkClient;

    @Resource
    private StoreRepository cfgStoreRepository;

    @Resource
    private HeOrderPerformanceOperationRepository orderPerformanceOperationRepository;

    @Resource
    private OrderQueryService orderQueryService;

    @Resource
    private ProductionAmountRefundRepository productionAmountRefundRepository;

    @Override
    public Page<QueryOrderPageVO> queryPage(QueryOrderPageReq queryOrderPageReq) {
        //客户的id列表
        List<Integer> clientList = null;
        List<Integer> basicList = null;

        //判断是否有用户手机号/姓名字段查询/来源，如果有去用户快照表/client里查询
        List<HeOrderUserSnapshotEntity> heOrderUserSnapshotEntities = new ArrayList<>();
        List<TabClientEntity> tabClientEntityList = new ArrayList<>();

        if (StringUtils.isNotEmpty(queryOrderPageReq.getCustomerName()) || StringUtils.isNotEmpty(queryOrderPageReq.getCustomerMobile()) || CollectionUtils.isNotEmpty(queryOrderPageReq.getCustomerSource())

        ) {

            clientList = new ArrayList<>();
            basicList = new ArrayList<>();

            //新订单从快照表中取
//            heOrderUserSnapshotEntities = orderUserSnapshotRepository.queryByUserIdOrNameOrSource(queryOrderPageReq.getCustomerName(), queryOrderPageReq.getCustomerMobile(), queryOrderPageReq.getCustomerSource());
//            clientList.addAll(heOrderUserSnapshotEntities.stream().map(HeOrderUserSnapshotEntity::getClientUid).collect(Collectors.toList()));
//            basicList.addAll(heOrderUserSnapshotEntities.stream().map(HeOrderUserSnapshotEntity::getBasicUid).collect(Collectors.toList()));

            //新老订单均从client中取
            tabClientEntityList = clientRepository.queryByUserIdOrNameOrSource(queryOrderPageReq.getCustomerName(), queryOrderPageReq.getCustomerMobile(), queryOrderPageReq.getCustomerSource());
            clientList.addAll(tabClientEntityList.stream().map(TabClientEntity::getId).collect(Collectors.toList()));
            basicList.addAll(tabClientEntityList.stream().map(TabClientEntity::getBasicUid).collect(Collectors.toList()));
        }

        //有续住加收项的订单列表
        List<Integer> orderIds = null;

        //判断查询条件中是否有续住字段，如果有去加收项中去查询有续住的订单的id集合（只有新订单有续住）
        if (ObjectUtil.isNotEmpty(queryOrderPageReq.getStayOver())) {
            orderIds = orderAdditionalRevenueRepository.queryAdditionalOrderIdByType(MonthAdditionalRevenueEnum.STAY_COST.getCode(), queryOrderPageReq.getStayOver());
        }

        //审批折扣审批的订单id列表
        List<String> approveOrderIds = null;

        //判断查询条件中是否有折扣审批的字段
        if (ObjectUtil.isNotEmpty(queryOrderPageReq.getDiscountApprovalStatus()) && queryOrderPageReq.getDiscountApprovalStatus() != 0) {
            //根据审批类型和状态，获取最后一条对应的审批
            approveOrderIds = oaProcessIdRelationService.getLastRecordOrderByTypeAndStatus(AuditTypeEnum.DISCOUNT_APPROVAL.getCode(), queryOrderPageReq.getDiscountApprovalStatus());
        }

        Set<Integer> typeStore = null;

        //筛选所有符合的门店
        if (CollectionUtils.isNotEmpty(queryOrderPageReq.getType())) {
            typeStore = storeRepository.queryCfgStoreByType(queryOrderPageReq.getType()).stream().map(CfgStoreEntity::getStoreId).collect(Collectors.toSet());
        }


        Page heOrderEntities = orderRepository.queryAdminPage(new Page(queryOrderPageReq.getPageNum(), queryOrderPageReq.getPageSize()), clientList, basicList, orderIds, queryOrderPageReq, approveOrderIds, typeStore);

        List<HeOrderEntity> records = heOrderEntities.getRecords();

        if (CollectionUtils.isNotEmpty(records)) {
            List<Integer> orderIdList = records.stream().map(HeOrderEntity::getOrderId).collect(Collectors.toList());

            List<HeOrderGoodsEntity> orderGoodsEntityList = orderGoodsRepository.getByOrderIdList(orderIdList);
            //套餐商品的信息
            List<GoodsEntity> goodsEntities = new ArrayList<>();
            //如果是产康或其他订单
            List<Integer> cwOrderIdList = records.stream().filter(x -> x.getOrderType().equals(OmniOrderTypeEnum.PRODUCTION_ORDER.getCode()) || x.getOrderType().equals(OmniOrderTypeEnum.OTHER_MONTH_ORDER.getCode())).map(HeOrderEntity::getOrderId).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(cwOrderIdList)) {
                List<OrderProductionExtendEntity> orderProductionExtendEntities = orderProductionExtendRepository.queryByOrderIdList(cwOrderIdList);
                if (ObjectUtil.isNotEmpty(orderProductionExtendEntities)) {
                    List<Integer> collect = orderProductionExtendEntities.stream().map(OrderProductionExtendEntity::getGoodsId).collect(Collectors.toList());
                    goodsEntities = goodsRepository.selectByIdList(collect);
                    goodsEntities.addAll(goodsRepository.selectByIdList(goodsEntities.stream().map(GoodsEntity::getParentId).collect(Collectors.toList())));
                }
            }
            if (CollectionUtils.isNotEmpty(orderGoodsEntityList)) {
                List<Integer> collect = orderGoodsEntityList.stream().map(HeOrderGoodsEntity::getGoodsId).collect(Collectors.toList());
                goodsEntities = goodsRepository.selectByIdList(collect);
                goodsEntities.addAll(goodsRepository.selectByIdList(goodsEntities.stream().map(GoodsEntity::getParentId).collect(Collectors.toList())));
            }
            //订单商品名字信息
            List<StoreGoodsSkuModel> orderGoodsName = goodsRepository.getOrderGoodsName(orderIdList);
            //添加商品名字
            for (Integer orderId : cwOrderIdList) {
                List<HeOrderEntity> collect = records.stream().filter(x -> x.getOrderId().equals(orderId)).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(collect)) {
                    HeOrderEntity heOrderEntity = collect.get(0);
                    String goodsNameByOrder = orderRepository.getGoodsNameByOrder(heOrderEntity);
                    StoreGoodsSkuModel storeGoodsSkuModel = new StoreGoodsSkuModel();
                    storeGoodsSkuModel.setGoodsName(goodsNameByOrder);
                    storeGoodsSkuModel.setOrderId(orderId);
                    orderGoodsName.add(storeGoodsSkuModel);

                }


            }

            //门店信息
            List<CfgStoreEntity> storeEntityList = storeRepository.queryCfgStoreByIdList(records.stream().map(HeOrderEntity::getStoreId).collect(Collectors.toList()));
            //加收项数据
            List<HeOrderAdditionalRevenueEntity> additionalRevenueEntityList = orderAdditionalRevenueRepository.getByOrderIdList(orderIdList);

            List<QueryOrderPageVO> result = new ArrayList<>();

            List<HeOrderTagsEntity> allOrderTags = tagsRepository.getAllOrderTags();

            List<Integer> staffIdList = records.stream().map(HeOrderEntity::getStaffId).collect(Collectors.toList());
            staffIdList.addAll(records.stream().map(HeOrderEntity::getUpdateStaffId).collect(Collectors.toList()));
            List<UserEntity> userEntityList = userRepository.queryUserByIdList(staffIdList);

            List<HeOrderRefundEntity> refundByOrderIdList = orderRefundRepository.getRefundByOrderIdList(orderIdList);

            tabClientEntityList.addAll(clientRepository.getTabClientByIdList(records.stream().map(HeOrderEntity::getClientUid).collect(Collectors.toList())));

            for (HeOrderEntity record : records) {
                result.add(getQueryOrderPageVO(record, orderGoodsEntityList, storeEntityList, additionalRevenueEntityList, allOrderTags, userEntityList, goodsEntities, tabClientEntityList, refundByOrderIdList, orderGoodsName));
            }

            //获取提前离馆
            List<Integer> collect = result.stream().map(QueryOrderPageVO::getOrderId).collect(Collectors.toList());
            List<AheadOutRoomEntity> aheadOutRoomEntities = this.aheadOutRoomRepository.queryByOrderIdsAndStatus(collect);

            for (QueryOrderPageVO queryOrderPageVO : result) {

                Optional<AheadOutRoomEntity> first = aheadOutRoomEntities.stream().filter(f -> f.getOrderId().equals(queryOrderPageVO.getOrderId())).findFirst();
                if (first.isPresent()) {
                    AheadOutRoomEntity aheadOutRoomEntity = first.get();
                    //入住总天数=套餐天数+续住天数-提前离馆天数
                    queryOrderPageVO.setCheckInTotalDays(queryOrderPageVO.getCheckInTotalDays() - aheadOutRoomEntity.getCheckOutDays());
                    //订单金额=订单金额-提前离馆应退原价金额
                    queryOrderPageVO.setOrderAmount(queryOrderPageVO.getOrderAmount().subtract(new BigDecimal(aheadOutRoomEntity.getOriginalAmount()).divide(new BigDecimal(100))));
                    //签约金额=订单签约金额-提前离馆应退金额
                    queryOrderPageVO.setPayableAmount(queryOrderPageVO.getPayableAmount().subtract(new BigDecimal(aheadOutRoomEntity.getRefundedAmount()).divide(new BigDecimal(100))));
                }
            }

            heOrderEntities.setRecords(result);
        }

        return heOrderEntities;
    }


    private QueryOrderPageVO getQueryOrderPageVO(HeOrderEntity heOrderEntity, List<HeOrderGoodsEntity> goodsEntityList, List<CfgStoreEntity> storeEntityList, List<HeOrderAdditionalRevenueEntity> additionalRevenueEntityList, List<HeOrderTagsEntity> allOrderTags, List<UserEntity> userEntityList, List<GoodsEntity> goodsEntities, List<TabClientEntity> tabClientEntityList, List<HeOrderRefundEntity> refundByOrderIdList, List<StoreGoodsSkuModel> orderGoodsName) {

        QueryOrderPageVO queryOrderPageVO = new QueryOrderPageVO();
        queryOrderPageVO.setPerformanceEffective(new Date(heOrderEntity.getPercentFirstTime().longValue() * 1000));
        queryOrderPageVO.setClientUid(heOrderEntity.getClientUid());
        queryOrderPageVO.setOrderId(heOrderEntity.getOrderId());
        queryOrderPageVO.setStoreId(heOrderEntity.getStoreId());
        Integer oldOrNew = heOrderEntity.getOldOrNew();


        //新老订单来源渠道 姓名 手机号都从客户中取
        Optional<TabClientEntity> clientFirst = tabClientEntityList.stream().filter(t -> t.getId().equals(heOrderEntity.getClientUid())).findFirst();
        if (clientFirst.isPresent()) {
            queryOrderPageVO.setCustomerSource(clientFirst.get().getFromType());
            queryOrderPageVO.setCustomerSourceStr(CustomerFromTypeEnum.getOrderListValueByCode(clientFirst.get().getFromType()));
            queryOrderPageVO.setCustomerName(clientFirst.get().getName());
            queryOrderPageVO.setCustomerMobile(clientFirst.get().getPhone());
        }

        queryOrderPageVO.setCustomerMobile(PrivacyUtil.encryptPhoneForFix(queryOrderPageVO.getCustomerMobile()));

        Integer allDays = 0;

        Optional<HeOrderGoodsEntity> goodsFirst = goodsEntityList.stream().filter(g -> g.getOrderId().equals(heOrderEntity.getOrderId())).findFirst();
        if (goodsFirst.isPresent()) {
            HeOrderGoodsEntity orderGoodsEntity = goodsFirst.get();
            Integer serviceDays = 0;
            List<GoodsEntity> childGoods = goodsEntities.stream().filter(g -> g.getId().equals(orderGoodsEntity.getGoodsId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(childGoods)) {

                GoodsEntity goodsEntity = childGoods.get(0);
                serviceDays = childGoods.get(0).getServiceDays();

                List<StoreGoodsSkuModel> collect = orderGoodsName.stream().filter(o -> o.getOrderId().equals(heOrderEntity.getOrderId())).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(collect) && collect.get(0).checkGoodsAndSku()) {
                    queryOrderPageVO.setGoodsName(collect.get(0).getAllGoodsName());
                } else {
                    List<GoodsEntity> parentGoods = goodsEntities.stream().filter(g -> g.getId().equals(goodsEntity.getParentId())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(parentGoods)) {
                        //商品名称格式  莫某套餐/14天
                        queryOrderPageVO.setGoodsName(parentGoods.get(0).getGoodsName() + "/" + serviceDays + "天");
                    } else {
                        queryOrderPageVO.setGoodsName(goodsEntity.getGoodsName());
                    }
                }

                queryOrderPageVO.setGoodsAmount(RMBUtils.bigDecimalF2Y(goodsFirst.get().getGoodsPriceOrgin()).setScale(2));
                queryOrderPageVO.setGoodsId(goodsEntity.getId() + "");
                queryOrderPageVO.setBasicId(heOrderEntity.getBasicUid() + "");
            }
            allDays += ObjectUtil.isEmpty(serviceDays) ? 0 : serviceDays;
        }

        //其他订单或产康订单商品名称
        if (heOrderEntity.getOrderType().equals(OmniOrderTypeEnum.OTHER_MONTH_ORDER.getCode()) || heOrderEntity.getOrderType().equals(OmniOrderTypeEnum.PRODUCTION_ORDER.getCode())) {
            Optional<StoreGoodsSkuModel> first = orderGoodsName.stream().filter(x -> x.getOrderId().equals(heOrderEntity.getOrderId())).findFirst();
            if (first.isPresent()) {
                queryOrderPageVO.setGoodsName(first.get().getGoodsName());
            }
        }

        Optional<CfgStoreEntity> storeFirst = storeEntityList.stream().filter(s -> s.getStoreId().equals(heOrderEntity.getStoreId())).findFirst();
        if (storeFirst.isPresent()) {
            CfgStoreEntity store = storeFirst.get();
            queryOrderPageVO.setStoreName(store.getStoreName());

            Integer type = store.getType();
            Integer childType = store.getChildType();

            if (StoreTypeEnum.SAINT_BELLA.getCode().equals(type)) {
                if (StoreChildTypeEnum.BELLA_VILLA.getCode().equals(childType)) {
                    queryOrderPageVO.setTypeName(StoreChildTypeEnum.BELLA_VILLA.getValue());
                } else {
                    queryOrderPageVO.setTypeName("圣贝拉");
                }
            } else {
                queryOrderPageVO.setTypeName(StoreTypeEnum.getValueByCode(type));
            }
        }

        List<HeOrderAdditionalRevenueEntity> orderAdditionalRevenueEntityList = additionalRevenueEntityList.stream().filter(f -> f.getOrderId().equals(heOrderEntity.getOrderId())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(orderAdditionalRevenueEntityList)) {
            List<HeOrderAdditionalRevenueEntity> allStayCost = orderAdditionalRevenueEntityList.stream().filter(o -> MonthAdditionalRevenueEnum.STAY_COST.getCode().equals(o.getType())).collect(Collectors.toList());
            int sum = allStayCost.stream().mapToInt(HeOrderAdditionalRevenueEntity::getDays).sum();
            queryOrderPageVO.setDurationOfStay(sum);
            Integer serviceDays = goodsFirst.get().getServiceDays();
            allDays += sum;
        } else {
            queryOrderPageVO.setDurationOfStay(0);
        }

        queryOrderPageVO.setOrderSn(heOrderEntity.getOrderSn());
        queryOrderPageVO.setCheckInTotalDays(allDays);

        queryOrderPageVO.setOrderAmount(RMBUtils.bigDecimalF2Y(heOrderEntity.getOrderAmount()));
        queryOrderPageVO.setPayableAmount(RMBUtils.bigDecimalF2Y(heOrderEntity.getPayAmount()));


        if (oldOrNew == 1) {
            OrderDiscountsCacheVO discountsCache = JSONObject.toJavaObject(JSON.parseObject(heOrderEntity.getDiscountDetails()), OrderDiscountsCacheVO.class);
            if (ObjectUtil.isNotEmpty(discountsCache)) {
                queryOrderPageVO.setOrderDiscount(discountsCache.getOrderDiscount().toString() + "%");
                queryOrderPageVO.setNetDiscountRate(discountsCache.getNetDiscountRate().toString() + "%");
                queryOrderPageVO.setGrossProfitMargin(discountsCache.getGrossProfitMargin().toString() + "%");
            }
        } else {


            Optional<HeOrderGoodsEntity> first = goodsEntityList.stream().filter(o -> o.getOrderId().equals(heOrderEntity.getOrderId())).findFirst();
            if (first.isPresent()) {
                HeOrderGoodsEntity orderGoodsEntity = first.get();
                Integer goodsPriceOrgin = orderGoodsEntity.getGoodsPriceOrgin();
                Integer payAmount = orderGoodsEntity.getPayAmount();
                Integer holidayAmount = heOrderEntity.getHolidayAmount();

                BigDecimal bigDecimal = BigDecimalUtil.divideScaleForPercentageCheckZeroDEfaultZero(new BigDecimal(payAmount - (null == holidayAmount ? 0 : holidayAmount)), new BigDecimal(goodsPriceOrgin)).setScale(0, RoundingMode.DOWN).setScale(2);
                queryOrderPageVO.setOrderDiscount(bigDecimal + "%");
            } else {
                queryOrderPageVO.setOrderDiscount(heOrderEntity.getDiscountMargin().multiply(new BigDecimal(100)) + "%");
            }


            queryOrderPageVO.setNetDiscountRate(heOrderEntity.getNetMargin().multiply(new BigDecimal(100)) + "%");
            queryOrderPageVO.setGrossProfitMargin(heOrderEntity.getGrossMargin().multiply(new BigDecimal(100)) + "%");
        }

        queryOrderPageVO.setPaidAmount(RMBUtils.bigDecimalF2Y(heOrderEntity.getPaidAmount()));
        queryOrderPageVO.setProductionAmountPay(RMBUtils.bigDecimalF2Y(heOrderEntity.getProductionAmountPay()));


        if (oldOrNew == 1) {
            queryOrderPageVO.setRealAmount(RMBUtils.bigDecimalF2Y(heOrderEntity.getRealAmount()));
        } else {
            List<HeOrderRefundEntity> collect = refundByOrderIdList.stream().filter(r -> r.getOrderId().equals(heOrderEntity.getOrderId()) && (RefundRecordPayStatusEnum.REFUND_RECORD_1.getCode().equals(r.getStatus()) || RefundRecordPayStatusEnum.REFUND_RECORD_3.getCode().equals(r.getStatus()) || RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode().equals(r.getStatus()))).collect(Collectors.toList());
            int allRefund = collect.stream().mapToInt(HeOrderRefundEntity::getActualAmount).sum();
            queryOrderPageVO.setRealAmount(RMBUtils.bigDecimalF2Y(heOrderEntity.getPaidAmount() - allRefund));
        }

        BigDecimal remainingAmount = RMBUtils.bigDecimalF2Y(heOrderEntity.getPayAmount()).subtract(queryOrderPageVO.getRealAmount());

        queryOrderPageVO.setRemainingAmount(remainingAmount.compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : remainingAmount);

        if (ObjectUtil.isNotEmpty(heOrderEntity.getOrderTag()) && heOrderEntity.getOrderTag() > 0) {
            queryOrderPageVO.setOrderTagId(heOrderEntity.getOrderTag());
            Optional<HeOrderTagsEntity> first = allOrderTags.stream().filter(a -> a.getId().equals(heOrderEntity.getOrderTag())).findFirst();
            if (first.isPresent()) {
                queryOrderPageVO.setOrderTagName(first.get().getTagName());
            }
        }

        if (ObjectUtil.isNotEmpty(heOrderEntity.getCreatedAt())) {
            queryOrderPageVO.setCreatedAt(DateUtil.formatDateTime(new Date(heOrderEntity.getCreatedAt() * 1000)));
        }
        if (ObjectUtil.isNotEmpty(heOrderEntity.getUpdatedAt())) {
            if (heOrderEntity.getUpdatedAt() == 0L) {
                queryOrderPageVO.setUpdatedAt(DateUtil.formatDate(new Date(heOrderEntity.getCreatedAt() * 1000)));
            } else {
                queryOrderPageVO.setUpdatedAt(DateUtil.formatDate(new Date(heOrderEntity.getUpdatedAt() * 1000)));
            }

        }
        if (ObjectUtil.isNotEmpty(heOrderEntity.getPayFirstTime()) && heOrderEntity.getPayFirstTime() > 0) {
            queryOrderPageVO.setPayFirstTime(DateUtil.formatDateTime(new Date(heOrderEntity.getPayFirstTime().longValue() * 1000)));
        }

        Integer staffId = heOrderEntity.getStaffId();
        if (ObjectUtil.isNotEmpty(staffId)) {
            queryOrderPageVO.setCreateBy(staffId.longValue());
            Optional<UserEntity> userFirst = userEntityList.stream().filter(f -> f.getId().equals(staffId)).findFirst();
            if (userFirst.isPresent()) {
                queryOrderPageVO.setCreateByName(userFirst.get().getName());
            }
        }

        Integer updateStaffId = heOrderEntity.getUpdateStaffId();
        if (ObjectUtil.isNotEmpty(updateStaffId)) {
            queryOrderPageVO.setUpdateBy(updateStaffId.longValue());
            Optional<UserEntity> updateUserFirst = userEntityList.stream().filter(f -> f.getId().equals(updateStaffId)).findFirst();
            if (updateUserFirst.isPresent()) {
                queryOrderPageVO.setUpdateByName(updateUserFirst.get().getName());
            }
        }


        if (heOrderEntity.getPercentFirstTime() > 0) {
            queryOrderPageVO.setPerformanceEffectiveDate(DateUtil.formatDateTime(new Date(heOrderEntity.getPercentFirstTime().longValue() * 1000)));
        }

        if (oldOrNew == 1) {
            //获取这个订单的审批
            //当前审批状态：0:进行中；1同意,2拒绝,3撤销"
            OaProcessIdRelationPO lastRecordOrderByTypeAndOrderId = oaProcessIdRelationService.getLastRecordOrderByTypeAndOrderId(heOrderEntity.getOrderId(), OrderApproveRecordTypeEnum.DISCOUNT_APPROVAL.getPlatformId());
            OldOrderApproveStatusEnum oldOrderApproveStatusEnum = convertOldOrderApproveStatusEnum(lastRecordOrderByTypeAndOrderId);
            queryOrderPageVO.setApprovalDiscountStatus(oldOrderApproveStatusEnum.getCode());
            queryOrderPageVO.setApprovalDiscountStatusStr(oldOrderApproveStatusEnum.getValue());
        } else {
            Integer approvalDiscountStatus = heOrderEntity.getApprovalDiscountStatus();
            queryOrderPageVO.setApprovalDiscountStatus(approvalDiscountStatus);
            queryOrderPageVO.setApprovalDiscountStatusStr(OldOrderApproveStatusEnum.getValueByCode(approvalDiscountStatus));
        }

        queryOrderPageVO.setOrderStatus(heOrderEntity.getOrderStatus());
        queryOrderPageVO.setBasicUid(heOrderEntity.getBasicUid());
        queryOrderPageVO.setOrderStatusStr(OrderStatusV2Enum.getByCode(heOrderEntity.getOrderStatus()).getValue());
        queryOrderPageVO.setPayStatus(heOrderEntity.getPayStatus());
        queryOrderPageVO.setPayStatusStr(PayStatusV2Enum.getName(heOrderEntity.getPayStatus()));
        queryOrderPageVO.setRefundStatus(heOrderEntity.getRefundStatus());
        queryOrderPageVO.setRefundStatusStr(OrderRefundStatusEnum.getName(heOrderEntity.getRefundStatus()));

        //如果支付金额>0且没有首次支付金额超过一半的时间
        if (heOrderEntity.getRealAmount() > 0) {
            queryOrderPageVO.setReportAccountPerformance(ObjectUtil.isEmpty(heOrderEntity.getPercentFirstTime()) ? 1 : 0);
        } else {
            queryOrderPageVO.setReportAccountPerformance(0);
        }

        //业绩正常就可剔除，已剔除可恢复
        queryOrderPageVO.setOperationType(heOrderEntity.getOperationType() == 0 ? 0 : 1);

        queryOrderPageVO.setOldOrNew(ObjectUtil.isEmpty(oldOrNew) ? 0 : oldOrNew);

        queryOrderPageVO.setTaskId(heOrderEntity.getTaskId());

        if (ObjectUtil.isNotEmpty(heOrderEntity.getPaidAmount()) && heOrderEntity.getPaidAmount() > 0 && ObjectUtil.isNotEmpty(heOrderEntity.getPercentFirstTime()) && heOrderEntity.getPercentFirstTime() == 0 && ObjectUtil.isNotEmpty(heOrderEntity.getIsNotice()) && heOrderEntity.getIsNotice() == 1) {
            queryOrderPageVO.setIsPerformanceNotice(0);
        } else {
            queryOrderPageVO.setIsPerformanceNotice(2);
        }

        return queryOrderPageVO;
    }

    /***
     * 根据订单号查询-订单详情-基本信息
     * @param orderId
     */
    @Override
    public Result<STMOOrderBasicInfoVO> queryOrderBasicInfoByOrderNo(Integer orderId) {

        //多线程获取-获取基本信息-获取纸质合同信息-获取线上合同信息-获取客户信息
        AsyncResultDTO<HeOrderEntity, List<ContractSignRecordPaperEntity>, List<MonthContractSignRecordEntity>, HeOrderUserSnapshotEntity> asyncResultDTO = multiThreadGetOrderInfo(orderId);
        //获取委托人信息
        HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity = orderBailorSnapshotRepository.queryByOrderId(orderId);
        return Result.success(convertSTMOOrderBasicInfoVO(asyncResultDTO.getResult1(), asyncResultDTO.getResult2(), asyncResultDTO.getResult3(), asyncResultDTO.getResult4(), heOrderBailorSnapshotEntity));
    }

    /**
     * 通过订单号查询订单套餐信息
     */
    @Override
    public Result<STMOrderInfoMonthInfoVO> queryOrderMonth(String orderNo) {
        //订单信息
        HeOrderEntity byOrderId = orderRepository.getByOrderSn(orderNo);
        if (ObjectUtil.isEmpty(byOrderId)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "订单不存在orderNo:" + orderNo);
        }

        STMOrderInfoMonthInfoVO vo = this.getSTMOrderInfoMonthInfoVOByOrderId(byOrderId);
        return Result.success(vo);
    }

    @Override
    public Result<STMOrderInfoVO> queryByOrderIdOrderInfo(Integer orderId) {

        //订单信息
        HeOrderEntity byOrderId = orderRepository.getByOrderId(orderId);
        if (ObjectUtil.isEmpty(byOrderId)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "订单不存在：" + orderId);
        }

        //获取订单用户快照
        HeOrderUserSnapshotEntity userSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(orderId);

        //加收项数据
        List<HeOrderAdditionalRevenueEntity> additionalRevenueEntityList = orderAdditionalRevenueRepository.getByOrderId(orderId);

        //获取订单信息
        STMOrderInfoOrderInfoVO stmOrderInfoOrderInfoVO = this.getSTMOrderInfoOrderInfoVOByOrderId(byOrderId);

        //获取月子套餐信息
        STMOrderInfoMonthInfoVO orderInfoMonthInfoVO = this.getSTMOrderInfoMonthInfoVOByOrderId(byOrderId);

        //获取续住信息
        STMOrderInfoStayOverInfoVO orderInfoStayOverInfoVO = this.getSTMOrderInfoStayOverInfoVOByOrderIdV2(byOrderId, stmOrderInfoOrderInfoVO, orderInfoMonthInfoVO, additionalRevenueEntityList);

        //获取房型变更信息
        STMOrderInfoRoomChangeInfoVO orderInfoRoomChangeInfoVO = this.getSTMOrderInfoRoomChangeInfoVOByOrderIdV2(byOrderId, additionalRevenueEntityList, orderInfoMonthInfoVO);

        //获取多胞胎费用
        STMOrderInfoMultipleBirthsInfoVO orderInfoMultipleBirthsInfoVO = this.getSTMOrderInfoMultipleBirthsInfoVOByOrderId(byOrderId, additionalRevenueEntityList, userSnapshotEntity);

        //获取节日费用信息
        STMOrderInfoHolidaysInfoVO orderInfoHolidaysInfoVO = this.getSTMOrderInfoHolidaysInfoVOByOrderId(byOrderId, additionalRevenueEntityList);

        //获取提前离馆信息
        STMOrderInfoLeaveEarlyInfoVO orderInfoLeaveEarlyInfoVO = this.getSTMOrderInfoLeaveEarlyInfoVOByOrderId(byOrderId, stmOrderInfoOrderInfoVO);

        return Result.success(new STMOrderInfoVO(stmOrderInfoOrderInfoVO, orderInfoMonthInfoVO, orderInfoStayOverInfoVO, orderInfoRoomChangeInfoVO, orderInfoMultipleBirthsInfoVO, orderInfoHolidaysInfoVO, orderInfoLeaveEarlyInfoVO));
    }

    @Override
    public Result<STMOrderInfoFinancialVO> queryByOrderIdInancialInformation(Integer orderId) {

        STMOrderInfoFinancialVO stmOrderInfoFinancialVO = new STMOrderInfoFinancialVO();

        HeOrderEntity byOrderId = orderRepository.getByOrderId(orderId);
        if (ObjectUtil.isEmpty(byOrderId)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "订单不存在：" + orderId);
        }

        String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(byOrderId.getStoreId());

        STMOrderInfoPaymentVO orderInfoPaymentVO = new STMOrderInfoPaymentVO();
        orderInfoPaymentVO.setPayAmount(storeCurrencyCode + RMBUtils.changeF2Y(byOrderId.getPayAmount().longValue()));
        orderInfoPaymentVO.setPaidAmount(storeCurrencyCode + RMBUtils.changeF2Y(byOrderId.getRealAmount().longValue()));
        orderInfoPaymentVO.setRemainingAmount(storeCurrencyCode + RMBUtils.changeF2Y(byOrderId.getPayAmount() - byOrderId.getRealAmount() - 0L));

        List<HeIncomeRecordEntity> recordListByOrderId = incomeRecordRepository.getAllRecordListByOrderId(orderId);

        //需要过滤pos发起但没有支付成功
        recordListByOrderId = recordListByOrderId.stream().filter(r -> !OmniPayTypeEnum.ONLINE_POS.getCode().equals(r.getPayType()) || (OmniPayTypeEnum.ONLINE_POS.getCode().equals(r.getPayType()) && r.getStatus() == 1)).collect(Collectors.toList());
        List<HeIncomeProofRecordEntity> incomeProofRecordByIncomeIdList = incomeProofRecordRepository.getIncomeProofRecordByIncomeIdList(recordListByOrderId.stream().filter(r -> r.getPayType() == 3).map(HeIncomeRecordEntity::getId).collect(Collectors.toList()));
        STMOrderInfoProceedsVO stmOrderInfoProceedsVO = new STMOrderInfoProceedsVO();
        List<STMOrderInfoProceedsRecordVO> stmOrderInfoProceedsRecordVOList = new ArrayList<>();
        STMOrderInfoRefundVO stmOrderInfoRefundVO = new STMOrderInfoRefundVO();
        List<STMOrderInfoRefundRecordVO> orderInfoRefundVOList = new ArrayList();
        Integer totalPayAmount = 0;
        Integer totalRefundAmount = 0;


        if (CollectionUtils.isNotEmpty(recordListByOrderId)) {

            List<String> incomeSnList = recordListByOrderId.stream().map(HeIncomeRecordEntity::getIncomeSn).collect(Collectors.toList());
            List<HeIncomeProofRecordEntity> listByIncomeSnList = incomeProofRecordRepository.getListByIncomeSnList(incomeSnList);


            for (HeIncomeRecordEntity heIncomeRecordEntity : recordListByOrderId) {
                STMOrderInfoProceedsRecordVO stmOrderInfoProceedsRecordVO = new STMOrderInfoProceedsRecordVO();
                Optional<HeIncomeProofRecordEntity> first = incomeProofRecordByIncomeIdList.stream().filter(i -> i.getIncomeId().equals(heIncomeRecordEntity.getId())).findFirst();
                if (first.isPresent()) {
                    stmOrderInfoProceedsRecordVO.setOptName(first.get().getCreatedName());
                }
                stmOrderInfoProceedsRecordVO.setIncomeId(heIncomeRecordEntity.getId().toString());
                stmOrderInfoProceedsRecordVO.setIncomeTransactionalNo(heIncomeRecordEntity.getIncomeSn());
                stmOrderInfoProceedsRecordVO.setAccountTime(DateUtil.formatDateTime(new Date(heIncomeRecordEntity.getPayTime().longValue() * 1000)));
                stmOrderInfoProceedsRecordVO.setGatheringAmount(RMBUtils.changeF2Y(heIncomeRecordEntity.getIncome().longValue()));
                if (Objects.equals(heIncomeRecordEntity.getChannelType(), PayChannelTypeEnum.CMB.getCode())) {
                    stmOrderInfoProceedsRecordVO.setIncomeType("招商银行-" + OmniPayTypeEnum.getName(heIncomeRecordEntity.getPayType()));
                } else {
                    stmOrderInfoProceedsRecordVO.setIncomeType(OmniPayTypeEnum.getName(heIncomeRecordEntity.getPayType()));
                }

                if (OmniPayTypeEnum.OFFLINE.getCode().equals(heIncomeRecordEntity.getPayType())) {
                    //线下支付获取凭证以及备注
                    List<HeIncomeProofRecordEntity> collect1 = listByIncomeSnList.stream().filter(l -> l.getIncomeSn().equals(heIncomeRecordEntity.getIncomeSn())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect1)) {
                        HeIncomeProofRecordEntity heIncomeProofRecordEntity = collect1.get(collect1.size() - 1);
                        stmOrderInfoProceedsRecordVO.setPayProof(Arrays.asList(heIncomeProofRecordEntity.getPayProof()));
                        stmOrderInfoProceedsRecordVO.setRemark(heIncomeProofRecordEntity.getRemark());

                        Integer status = heIncomeProofRecordEntity.getStatus();
                        if (status == 0) {
                            stmOrderInfoProceedsRecordVO.setStatus("未确认");
                            totalPayAmount += heIncomeRecordEntity.getIncome();
                        } else if (status == 1) {
                            stmOrderInfoProceedsRecordVO.setStatus("已确认");
                            totalPayAmount += heIncomeRecordEntity.getIncome();
                        } else if (status == 2) {
                            stmOrderInfoProceedsRecordVO.setStatus("已拒绝");
                        }
                    }
                } else {
                    Integer status = heIncomeRecordEntity.getStatus();
                    stmOrderInfoProceedsRecordVO.setStatus(status == 1 ? "已到账" : "未到账");
                    totalPayAmount += heIncomeRecordEntity.getIncome();
                }

                Integer optId = heIncomeRecordEntity.getOptId();
                UserEntity userEntity = userRepository.queryById(optId);
                if (ObjectUtil.isNotEmpty(userEntity)) {
                    stmOrderInfoProceedsRecordVO.setOptName(userEntity.getName());
                }
                stmOrderInfoProceedsRecordVOList.add(stmOrderInfoProceedsRecordVO);
            }
            stmOrderInfoProceedsVO.setTotalPayAmount(RMBUtils.changeF2Y(totalPayAmount.longValue()));
            stmOrderInfoProceedsVO.setStmOrderInfoProceedsRecordVOList(stmOrderInfoProceedsRecordVOList);

        }


        List<HeOrderRefundEntity> refundByOrderId = orderRefundRepository.getRefundByOrderId(orderId);
        List<UserEntity> list = userRepository.queryByBasicUidList(refundByOrderId.stream().map(HeOrderRefundEntity::getConfirmUid).collect(Collectors.toList()).stream().map(Long::intValue).collect(Collectors.toList()));

        if (CollectionUtils.isNotEmpty(refundByOrderId)) {

            refundByOrderId = refundByOrderId.stream().sorted(Comparator.comparing(HeOrderRefundEntity::getCreatedAt).reversed()).collect(Collectors.toList());
            List<String> collect = refundByOrderId.stream().map(HeOrderRefundEntity::getIncomeSn).collect(Collectors.toList());

            List<HeIncomeRecordEntity> recordListByIncomeSnList = incomeRecordRepository.getRecordListByIncomeSnList(collect);


            for (HeOrderRefundEntity heOrderRefundEntity : refundByOrderId) {
                STMOrderInfoRefundRecordVO stmOrderInfoRefundRecordVO = new STMOrderInfoRefundRecordVO();
                stmOrderInfoRefundRecordVO.setGiftExtendDisabled(heOrderRefundEntity.getGiftExtendDisabled() == 0 ? "不失效" : "失效");
                stmOrderInfoRefundRecordVO.setRefundId(heOrderRefundEntity.getId().toString());
                stmOrderInfoRefundRecordVO.setRefundTransactionalNo(heOrderRefundEntity.getRefundOrderSn());

                Optional<HeIncomeRecordEntity> first = recordListByIncomeSnList.stream().filter(l -> l.getIncomeSn().equals(heOrderRefundEntity.getIncomeSn())).findFirst();
                if (first.isPresent()) {
                    stmOrderInfoRefundRecordVO.setIncomeId(first.get().getId() + "");
                }

                stmOrderInfoRefundRecordVO.setIncomeTransactionalNo(heOrderRefundEntity.getIncomeSn());
                stmOrderInfoRefundRecordVO.setRefundApplyDate(DateUtil.formatDateTime(new Date(heOrderRefundEntity.getCreatedAt() * 1000)));
                stmOrderInfoRefundRecordVO.setApplyName(heOrderRefundEntity.getApplyName());
                if (ObjectUtil.isNotEmpty(heOrderRefundEntity.getFinishAt()) && heOrderRefundEntity.getFinishAt() > 0) {
                    stmOrderInfoRefundRecordVO.setRefundAccountTime(DateUtil.formatDateTime(new Date(heOrderRefundEntity.getFinishAt() * 1000)));
                }
                if (RefundRecordPayStatusEnum.REFUND_RECORD_1.getCode().equals(heOrderRefundEntity.getStatus()) || RefundRecordPayStatusEnum.REFUND_RECORD_3.getCode().equals(heOrderRefundEntity.getStatus()) || RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode().equals(heOrderRefundEntity.getStatus())) {
                    totalRefundAmount += heOrderRefundEntity.getApplyAmount();
                }
                stmOrderInfoRefundRecordVO.setRefundAmount(RMBUtils.changeF2Y(heOrderRefundEntity.getApplyAmount().longValue()));
                stmOrderInfoRefundRecordVO.setIncomeType(OmniPayTypeEnum.getName(heOrderRefundEntity.getRefundType()));
                stmOrderInfoRefundRecordVO.setRefundReasonType(heOrderRefundEntity.getRefundReasonType() == 1 ? "正常退款" : "⾮正常退款（客诉）");

                /**
                 审批中：钉钉审核流审核中；
                 审批失败：审批被拒绝或者发起审批失败；
                 审核成功：审核通过；
                 待确认：如果是线下汇款方式退款，审批通过后需要财务介入；
                 */
                Integer approvalStatus = heOrderRefundEntity.getStatus();
                Integer refundType = heOrderRefundEntity.getRefundType();

                /**
                 等待打款：用于线下汇款，等待财务线下汇款；
                 退款已到账：退款成功
                 退款失败：退款失败，通常是原路返回的报错；
                 */

                if (RefundRecordPayStatusEnum.REFUND_RECORD_0.getCode().equals(approvalStatus)) {
                    stmOrderInfoRefundRecordVO.setRefundApprovalStatus(RefundRecordPayStatusEnum.REFUND_RECORD_0.getValue());
                } else if (RefundRecordPayStatusEnum.REFUND_RECORD_1.getCode().equals(approvalStatus)) {
                    stmOrderInfoRefundRecordVO.setRefundApprovalStatus(RefundRecordPayStatusEnum.REFUND_RECORD_1.getValue());
                } else if (RefundRecordPayStatusEnum.REFUND_RECORD_2.getCode().equals(approvalStatus)) {
                    stmOrderInfoRefundRecordVO.setRefundApprovalStatus(RefundRecordPayStatusEnum.REFUND_RECORD_2.getValue());
                } else if (RefundRecordPayStatusEnum.REFUND_RECORD_3.getCode().equals(approvalStatus)) {
                    stmOrderInfoRefundRecordVO.setRefundApprovalStatus(RefundRecordPayStatusEnum.REFUND_RECORD_3.getOnlineValue());
                    if (refundType == 3) {
                        //线下汇款
                        stmOrderInfoRefundRecordVO.setStatus("等待打款");
                    } else {
                        stmOrderInfoRefundRecordVO.setStatus("退款中");
                        stmOrderInfoRefundRecordVO.setOptName("系统");
                    }
                } else if (RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode().equals(approvalStatus)) {
                    stmOrderInfoRefundRecordVO.setRefundApprovalStatus(RefundRecordPayStatusEnum.REFUND_RECORD_3.getOnlineValue());
                    stmOrderInfoRefundRecordVO.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_4.getOnlineValue());
                } else if (RefundRecordPayStatusEnum.REFUND_RECORD_5.getCode().equals(approvalStatus)) {
                    stmOrderInfoRefundRecordVO.setRefundApprovalStatus(RefundRecordPayStatusEnum.REFUND_RECORD_3.getOnlineValue());
                    stmOrderInfoRefundRecordVO.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_5.getOnlineValue());
                }


                stmOrderInfoRefundRecordVO.setRefundReason(RefundReasonEnum.getName(heOrderRefundEntity.getRefundReason()));
                stmOrderInfoRefundRecordVO.setRemark(heOrderRefundEntity.getRemark());
                stmOrderInfoRefundRecordVO.setHasLiquidatedDamages(heOrderRefundEntity.getHasLiquidatedDamages() == 1 ? "是" : "否");

                if (OmniPayTypeEnum.OFFLINE.getCode().equals(heOrderRefundEntity.getRefundType())) {
                    Long confirmUid = heOrderRefundEntity.getConfirmUid();
                    if (ObjectUtil.isNotEmpty(confirmUid) && confirmUid != 0) {
                        Optional<UserEntity> confirm = list.stream().filter(l -> l.getBasicUid().equals(confirmUid.intValue())).findFirst();
                        if (confirm.isPresent()) {
                            stmOrderInfoRefundRecordVO.setOptName(confirm.get().getName());
                        }
                    }
                    stmOrderInfoRefundRecordVO.setRefundType("指定银行账户退款");
                } else if (OmniPayTypeEnum.WECHAT.getCode().equals(heOrderRefundEntity.getRefundType())) {
                    stmOrderInfoRefundRecordVO.setRefundType("原路退回（微信)");
                } else if (OmniPayTypeEnum.ALIPAY.getCode().equals(heOrderRefundEntity.getRefundType())) {
                    stmOrderInfoRefundRecordVO.setRefundType("原路退回（支付宝）");
                } else if (OmniPayTypeEnum.ONLINE_POS.getCode().equals(heOrderRefundEntity.getRefundType())) {
                    stmOrderInfoRefundRecordVO.setRefundType("原路退回(POS机)");
                } else {
                    stmOrderInfoRefundRecordVO.setRefundType(OmniPayTypeEnum.getName(heOrderRefundEntity.getRefundType()));
                }

                if (Objects.nonNull(heOrderRefundEntity.getRefundInfo())) {
                    Map<String, Object> map = JSONUtil.toBean(heOrderRefundEntity.getRefundInfo(), Map.class);
                    stmOrderInfoRefundRecordVO.setFiles(map.containsKey("images") ? JSONUtil.toList(map.get("images").toString(), String.class) : null);
                    map.remove("images");
                    stmOrderInfoRefundRecordVO.setRefundInfo(map);
                }
                orderInfoRefundVOList.add(stmOrderInfoRefundRecordVO);
            }
            stmOrderInfoRefundVO.setStmOrderInfoRefundRecordVOList(orderInfoRefundVOList);
            stmOrderInfoRefundVO.setTotalRefundAmount(RMBUtils.changeF2Y(totalRefundAmount.longValue()));
        }

        stmOrderInfoFinancialVO.setOrderInfoPaymentVO(orderInfoPaymentVO);
        stmOrderInfoFinancialVO.setOrderInfoProceedsVO(stmOrderInfoProceedsVO);
        stmOrderInfoFinancialVO.setOrderInfoRefundVO(stmOrderInfoRefundVO);

        return Result.success(stmOrderInfoFinancialVO);
    }

    @Override
    public Result<STMOrderInfoGiftVO> queryByOrderIdGift(Integer orderId) {
        HeOrderEntity byOrderId = orderRepository.getByOrderId(orderId);
        if (ObjectUtil.isEmpty(byOrderId)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "订单不存在：" + orderId);
        }
        String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(byOrderId.getStoreId());
        OrderInfoByOrderVO orderInfoByOrderId = monthOrderWxQueryService.getOrderInfoByOrderIdV2(orderId, null);
        OrderInfoByGiftExtendVO orderInfoByGiftExtendVO = orderInfoByOrderId.getOrderInfoByGiftExtendVO();

        STMOrderInfoGiftVO stmOrderInfoGiftVO = new STMOrderInfoGiftVO();
        if (ObjectUtil.isNotEmpty(orderInfoByGiftExtendVO)) {
            stmOrderInfoGiftVO.setTotalQuantity(orderInfoByGiftExtendVO.getChooseQuantity().toString());
            stmOrderInfoGiftVO.setGlobalValue(storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(orderInfoByGiftExtendVO.getChooseTotalAmount()));
            List<STMOrderInfoGiftListVO> orderInfoGiftListVOList = new ArrayList();

            List<OrderInfoByGiftExtendSkuVO> orderInfoByGiftExtendSkuVOList = orderInfoByGiftExtendVO.getOrderInfoByGiftExtendSkuVOList();

            BigDecimal orderInfoPaymentVO = BigDecimal.ZERO;


            List<OrderInfoByGiftExtendSkuVO> newOrderInfoByGiftExtendSkuVOList = new ArrayList<>();
            newOrderInfoByGiftExtendSkuVOList.addAll(orderInfoByGiftExtendSkuVOList.stream().filter(o -> o.getType() != 7).collect(Collectors.toList()));
            List<OrderInfoByGiftExtendSkuVO> collect = orderInfoByGiftExtendSkuVOList.stream().filter(o -> o.getType() == 7).collect(Collectors.toList());
            //合并
            Map<String, OrderInfoByGiftExtendSkuVO> orderInfoByGiftExtendSkuVOMap = new HashMap<>();
            for (OrderInfoByGiftExtendSkuVO orderInfoByGiftExtendSkuV : collect) {
                String key = orderInfoByGiftExtendSkuV.getGoodsId() + "-" + orderInfoByGiftExtendSkuV.getSkuId();
                if (null != orderInfoByGiftExtendSkuVOMap.get(key)) {
                    OrderInfoByGiftExtendSkuVO orderInfoByGiftExtendSkuVO = orderInfoByGiftExtendSkuVOMap.get(key);
                    orderInfoByGiftExtendSkuVO.setQuantity(orderInfoByGiftExtendSkuVO.getQuantity() + orderInfoByGiftExtendSkuV.getQuantity());
                    orderInfoByGiftExtendSkuVO.setTotalCost(RMBUtils.changeF2Y(new BigDecimal(orderInfoByGiftExtendSkuVO.getTotalCost().replace(",", "")).add(new BigDecimal(orderInfoByGiftExtendSkuV.getTotalCost().replace(",", ""))).multiply(new BigDecimal(100)).longValue()));
                    orderInfoByGiftExtendSkuVO.setTotalPrice(RMBUtils.changeF2Y(new BigDecimal(orderInfoByGiftExtendSkuVO.getTotalPrice().replace(",", "")).add(new BigDecimal(orderInfoByGiftExtendSkuV.getTotalPrice().replace(",", ""))).multiply(new BigDecimal(100)).longValue()));
                } else {
                    orderInfoByGiftExtendSkuVOMap.put(key, orderInfoByGiftExtendSkuV);
                }
            }
            for (String key : orderInfoByGiftExtendSkuVOMap.keySet()) {
                newOrderInfoByGiftExtendSkuVOList.add(orderInfoByGiftExtendSkuVOMap.get(key));
            }

            List<Integer> goodsIdList = newOrderInfoByGiftExtendSkuVOList.stream().map(OrderInfoByGiftExtendSkuVO::getGoodsId).collect(Collectors.toList());
            List<GoodsSimpleInfoModel> goodsSimpleInfoModels = goodsRepository.queryGoodsSimpleInfoByGoodsIdList(goodsIdList);


            for (int i = 1; i <= newOrderInfoByGiftExtendSkuVOList.size(); i++) {
                OrderInfoByGiftExtendSkuVO orderInfoByGiftExtendSkuVO = newOrderInfoByGiftExtendSkuVOList.get(i - 1);
                STMOrderInfoGiftListVO stmOrderInfoGiftListVO = new STMOrderInfoGiftListVO();
                stmOrderInfoGiftListVO.setNo(i + "");

                if (orderInfoByGiftExtendSkuVO.getType() == 1) {
                    stmOrderInfoGiftListVO.setItemName("产康金");
                } else if (orderInfoByGiftExtendSkuVO.getType() == 5) {
                    stmOrderInfoGiftListVO.setItemName(orderInfoByGiftExtendSkuVO.getGoodsName());
                } else {
                    Optional<GoodsSimpleInfoModel> first = goodsSimpleInfoModels.stream().filter(g -> g.getGoodsId().equals(orderInfoByGiftExtendSkuVO.getGoodsId())).findFirst();
                    if (first.isPresent()) {
                        //除了产康金和家属房，别的取全的
                        stmOrderInfoGiftListVO.setItemGroup(first.get().getFrontStr());
                        stmOrderInfoGiftListVO.setItemType(GoodsTypeEnum.allDescFromCode(first.get().getGoodsType()));
                        stmOrderInfoGiftListVO.setItemCategory(first.get().getBackStr());

                        if (!orderInfoByGiftExtendSkuVO.getGoodsName().equals(orderInfoByGiftExtendSkuVO.getSkuName())) {
                            stmOrderInfoGiftListVO.setItemName(orderInfoByGiftExtendSkuVO.getGoodsName() + (StringUtils.isNotEmpty(orderInfoByGiftExtendSkuVO.getSkuName()) ? "/" + orderInfoByGiftExtendSkuVO.getSkuName() : ""));
                        } else {
                            stmOrderInfoGiftListVO.setItemName(orderInfoByGiftExtendSkuVO.getGoodsName());
                        }
                    }
                }
                stmOrderInfoGiftListVO.setQuantity(orderInfoByGiftExtendSkuVO.getQuantity().toString());
                Integer quantity = orderInfoByGiftExtendSkuVO.getQuantity();
                Integer skuNum = orderInfoByGiftExtendSkuVO.getSkuNum();
                skuNum = null == skuNum ? 1 : skuNum;
                //数量（出库数量=规格数量*购买数量）
                if (orderInfoByGiftExtendSkuVO.getType() == 1) {
                    //产康金数量写死1
                    stmOrderInfoGiftListVO.setQuantity("1");
                    stmOrderInfoGiftListVO.setSkuNum("1");
                    stmOrderInfoGiftListVO.setUnitPrice(storeCurrencyCode + orderInfoByGiftExtendSkuVO.getTotalPrice());
                    stmOrderInfoGiftListVO.setTotalPrice(storeCurrencyCode + orderInfoByGiftExtendSkuVO.getTotalPrice());
                    stmOrderInfoGiftListVO.setUnitCost(storeCurrencyCode + orderInfoByGiftExtendSkuVO.getTotalCost());
                    stmOrderInfoGiftListVO.setTotalCost(storeCurrencyCode + orderInfoByGiftExtendSkuVO.getTotalCost());
                } else {
                    stmOrderInfoGiftListVO.setQuantity(quantity + "");
                    stmOrderInfoGiftListVO.setSkuNum(skuNum + "");
                    stmOrderInfoGiftListVO.setUnitPrice(storeCurrencyCode + orderInfoByGiftExtendSkuVO.getUnitPrice());
                    stmOrderInfoGiftListVO.setTotalPrice(storeCurrencyCode + orderInfoByGiftExtendSkuVO.getTotalPrice());
                    stmOrderInfoGiftListVO.setUnitCost(storeCurrencyCode + orderInfoByGiftExtendSkuVO.getUnitCost());
                    stmOrderInfoGiftListVO.setTotalCost(storeCurrencyCode + orderInfoByGiftExtendSkuVO.getTotalCost());
                }


                orderInfoPaymentVO = orderInfoPaymentVO.add(new BigDecimal(orderInfoByGiftExtendSkuVO.getTotalCost().replace(",", "")));
                orderInfoGiftListVOList.add(stmOrderInfoGiftListVO);
            }
            stmOrderInfoGiftVO.setOrderInfoGiftListVOList(orderInfoGiftListVOList);
            stmOrderInfoGiftVO.setAllCost(storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(orderInfoPaymentVO));
        }
        return Result.success(stmOrderInfoGiftVO);
    }

    @Override
    public Result<STMOrderInfoLaborVO> queryByOrderIdGoodNewsChildbirth(String orderNo) {
        HeOrderEntity byOrderSn = orderRepository.getByOrderSn(orderNo);
        if (Objects.isNull(byOrderSn)) return Result.success(null);
        return this.queryByOrderIdGoodNewsChildbirth(byOrderSn.getOrderId());
    }

    @Override
    public Result<STMOrderInfoLaborVO> queryByOrderIdGoodNewsChildbirth(Integer orderId) {
        STMOrderInfoLaborVO stmOrderInfoLaborVO = new STMOrderInfoLaborVO();

        HeStayinEntity stayinEntity = stayinRepository.getByOrderId(orderId);

        STMOrderInfoLaborMotherVO stmOrderInfoLaborMotherVO = new STMOrderInfoLaborMotherVO();
        List<STMOrderInfoLaborBabyVO> orderInfoLaborBabyVOList = new ArrayList<>();

        if (ObjectUtil.isNotEmpty(stayinEntity)) {

            stmOrderInfoLaborMotherVO.setStayinTime(stayinEntity.getStayinTime());
            stmOrderInfoLaborMotherVO.setProductionMode(stayinEntity.getProductionMode() == 0 ? "自然分娩" : "剖腹产");
            stmOrderInfoLaborMotherVO.setProductionModeStr(StayinProductionModeEnum.fromCode(stayinEntity.getProductionMode()));
            stmOrderInfoLaborMotherVO.setBirthType(stayinEntity.getBirthType());
            stmOrderInfoLaborMotherVO.setBirthTypeStr(StayinProductionModeEnum.fromCode(stayinEntity.getBirthType()));
            stmOrderInfoLaborMotherVO.setProductionDate(stayinEntity.getProductionDate());
            stmOrderInfoLaborMotherVO.setGestationWeek(stayinEntity.getGestationWeek().toString());
            stmOrderInfoLaborMotherVO.setGestationDays(stayinEntity.getGestationDay().toString());
            stmOrderInfoLaborMotherVO.setAllergy(stayinEntity.getAllergy());
            stmOrderInfoLaborMotherVO.setBirths(stayinEntity.getBirths().toString());
            stmOrderInfoLaborMotherVO.setNation(stayinEntity.getNational());
            stmOrderInfoLaborMotherVO.setFeedMode(stayinEntity.getFeedMode() == 1 ? "母乳喂养" : stayinEntity.getFeedMode() == 2 ? "配方奶喂养" : "混合喂养");
            stmOrderInfoLaborMotherVO.setFeedModeStr(stayinEntity.getFeedMode());
            stmOrderInfoLaborMotherVO.setDiabetes(stayinEntity.getIsDiabetes() == 0 ? "否" : "是");
            stmOrderInfoLaborMotherVO.setHypertension(stayinEntity.getIsHypertension() == 0 ? "否" : "是");
            stmOrderInfoLaborMotherVO.setInfectiousDiseases(stayinEntity.getInfectiousDiseases());
            stmOrderInfoLaborMotherVO.setPhysicalTaboo(stayinEntity.getDietaryTaboos());
            stmOrderInfoLaborMotherVO.setHeight(stayinEntity.getHeight());
            stmOrderInfoLaborMotherVO.setWeight(stayinEntity.getWeight());
            stmOrderInfoLaborMotherVO.setPickCar(stayinEntity.getIsCar() == 0 ? "否" : "是");
            stmOrderInfoLaborMotherVO.setHospitalAddress(stayinEntity.getHospitalAddress());
            String isAllergyFlower = "";
            if (stayinEntity.getIsAllergyFlower() == 2) {
                isAllergyFlower = "-";
            } else {
                isAllergyFlower = stayinEntity.getIsAllergyFlower() == 0 ? "否" : "是";
            }
            stmOrderInfoLaborMotherVO.setAllergyFlower(isAllergyFlower);
            stmOrderInfoLaborMotherVO.setCall(stayinEntity.getSalutation());
            String isAllergyOther = "";
            if (ObjectUtil.isEmpty(stayinEntity.getIsAllergyOther())) {
                isAllergyOther = "-";
            } else {
                isAllergyOther = stayinEntity.getIsAllergyOther() == 0 ? "否" : "是";
            }
            stmOrderInfoLaborMotherVO.setAllergyOther(isAllergyOther);
            stmOrderInfoLaborMotherVO.setAllergyRemark(stayinEntity.getAllergyRemark());
            stmOrderInfoLaborMotherVO.setGestationHospital(stayinEntity.getProductionHospital());

            stmOrderInfoLaborMotherVO.setLactationClothingSize(StayinClothingSizeEnum.fromCode(stayinEntity.getLactationClothingSize()));

            String babyList = stayinEntity.getBabyList();

            if (org.apache.commons.lang3.StringUtils.isNotEmpty(babyList)) {
                List<LinkedHashMap> read = com.stbella.order.common.utils.JsonUtil.read(babyList, List.class);
                for (LinkedHashMap linkedHashMap : read) {
                    STMOrderInfoLaborBabyVO stmOrderInfoLaborBabyVO = new STMOrderInfoLaborBabyVO();
                    Integer baby_gender = (Integer) linkedHashMap.get("baby_gender");
                    stmOrderInfoLaborBabyVO.setBabyName((String) linkedHashMap.get("name"));
                    stmOrderInfoLaborBabyVO.setBabyGender(baby_gender == 0 ? "女孩" : "男孩");
                    stmOrderInfoLaborBabyVO.setBornWeight(linkedHashMap.get("born_weight") + "克");
                    stmOrderInfoLaborBabyVO.setJaundiceValue(linkedHashMap.get("jaundice_value") + "mg/dl");
                    orderInfoLaborBabyVOList.add(stmOrderInfoLaborBabyVO);
                }
            }
        }
        stmOrderInfoLaborVO.setOrderInfoLaborMotherVO(stmOrderInfoLaborMotherVO);
        stmOrderInfoLaborVO.setOrderInfoLaborBabyVOList(orderInfoLaborBabyVOList);
        return Result.success(stmOrderInfoLaborVO);
    }

    private static void addMergedReigon(CTWorksheet sheetX, CellRangeAddress cellRangeAddress) {
        CTMergeCells ctMergeCells;
        if (sheetX.isSetMergeCells()) {
            ctMergeCells = sheetX.getMergeCells();
        } else {
            ctMergeCells = sheetX.addNewMergeCells();
        }

        CTMergeCell ctMergeCell = ctMergeCells.addNewMergeCell();
        ctMergeCell.setRef(cellRangeAddress.formatAsString());
    }

    @Override
    public void stmOrderExport(QueryOrderPageReq queryOrderPageReq, HttpServletResponse response, Boolean includeMobile) {

        queryOrderPageReq.setPageNum(1);

        int maxPageSize = 5000;
        //导出不限制条数
        queryOrderPageReq.setPageSize(maxPageSize);


        // 订单号 时间，门店 必须要有一个参数
        if (ObjectUtil.isEmpty(queryOrderPageReq.getOrderNo()) && ObjectUtil.isEmpty(queryOrderPageReq.getDateStart()) && ObjectUtil.isEmpty(queryOrderPageReq.getDateEnd()) && ObjectUtil.isEmpty(queryOrderPageReq.getStoreIds())) {
            throw new BusinessException("订单号,时间，门店 必须选择一个条件");
        }

        //门店权限控制，有所有门店的不处理，没有的话，只能查询自己有权限的门店
        StoreInfoDTO storeInfoDTO = EmployeeTokenHelper.getCurrentEmployeeStoreInfo();
        if (!storeInfoDTO.getHasAllStoreInfo()) {
            List<Integer> storeIdList = storeInfoDTO.getStoreIdList();
            if (CollectionUtil.isEmpty(storeIdList)) {
                throw new BusinessException("没有门店权限");
            }
            if (ObjectUtil.isEmpty(queryOrderPageReq.getStoreIds())) {
                queryOrderPageReq.setStoreIds(storeIdList);
            }
        }


        if (ObjectUtil.isNotEmpty(queryOrderPageReq.getDateStart())) {
            queryOrderPageReq.setDateStart(queryOrderPageReq.getDateStart() / 1000);
        }

        if (ObjectUtil.isNotEmpty(queryOrderPageReq.getDateEnd())) {
            queryOrderPageReq.setDateEnd(queryOrderPageReq.getDateEnd() / 1000);
        }

        //查询订单
        long queryStart = System.currentTimeMillis();
        Page<QueryOrderPageVO> queryOrderPageVOPage = queryPage(queryOrderPageReq);
        log.info("查询数据时间：{}", System.currentTimeMillis() - queryStart);

        List<QueryOrderPageVO> records = queryOrderPageVOPage.getRecords();
        if (CollectionUtil.isNotEmpty(records) && records.size() == maxPageSize) {
            // 这里极端情况，数据刚好 maxPageSize 时，也有这样的告警是个bug
            throw new BusinessException("订单数据超过" + maxPageSize + "条，请缩小查询范围");
        }


        SXSSFWorkbook workbook = new SXSSFWorkbook();

        // 创建字体对象并设置加粗样式
        Font font22 = workbook.createFont();
        font22.setFontName("宋体");
        font22.setBold(true);
        font22.setFontHeightInPoints((short) 22);

        Font font12 = workbook.createFont();
        font12.setFontName("宋体");
        font12.setBold(true);
        font12.setFontHeightInPoints((short) 12);

        //居中样式
        CellStyle styleCenter = workbook.createCellStyle();
        styleCenter.setVerticalAlignment(VerticalAlignment.CENTER);
        styleCenter.setAlignment(HorizontalAlignment.CENTER);

        // 创建样式对象并设置字体
        CellStyle styleHeader = workbook.createCellStyle();
        styleHeader.setVerticalAlignment(VerticalAlignment.CENTER);
        styleHeader.setAlignment(HorizontalAlignment.CENTER);
        styleHeader.setFont(font22);


        // 创建样式对象并设置字体
        CellStyle styleContext = workbook.createCellStyle();


        //订单基本信息
        List<STMOrderExportDTO> stmOrderExportDTOS = setOrderInfo(queryOrderPageReq, includeMobile, records, workbook, styleCenter, styleContext, styleHeader);
        //续住
        setSayOver(records, workbook, styleCenter, styleContext, styleHeader, stmOrderExportDTOS);
        //房型变更
        setRoomChange(records, workbook, styleCenter, styleContext, styleHeader, stmOrderExportDTOS);

        try {
            //输出Excel文件
            OutputStream output = response.getOutputStream();
            response.reset();
            response.setHeader("Content-disposition", "attachment; filename=" + java.net.URLEncoder.encode("月子标准订单" + DateUtil.formatDate(new Date()) + ".xlsx", "UTF-8"));
            response.setContentType("application/msexcel");
            workbook.write(output);
            output.flush();
            output.close();
        } catch (Exception e) {
            log.info(e.getMessage());
        }

    }

    private List<STMOrderExportDTO> setOrderInfo(QueryOrderPageReq queryOrderPageReq, Boolean includeMobile, List<QueryOrderPageVO> records, SXSSFWorkbook workbook, CellStyle styleCenter, CellStyle styleContext, CellStyle styleHeader) {
        SXSSFSheet order = workbook.createSheet("订单");

        CTWorksheet sheetX = order.getWorkbook().getXSSFWorkbook().getSheetAt(0).getCTWorksheet();

        List<STMOrderExportDTO> stmOrderExportDTOS = new ArrayList<>();

        stmOrderExportDTOS.add(new STMOrderExportDTO(STMOrderExport.getHeader()));

        List<Integer> orderIdList = records.stream()./*filter(r -> r.getOldOrNew() == 1).*/map(QueryOrderPageVO::getOrderId).collect(Collectors.toList());

        //获取所有新订单的续住、礼赠、房型变更数据

        List<HeOrderAdditionalRevenueEntity> byOrderIdList = orderAdditionalRevenueRepository.getByOrderIdList(orderIdList);
        List<HeOrderAdditionalRevenueEntity> additionalRevenueStayCostEntityList = byOrderIdList.stream().filter(i -> Objects.equals(i.getType(), MonthAdditionalRevenueEnum.STAY_COST.getCode())).collect(Collectors.toList());
        List<HeOrderAdditionalRevenueEntity> additionalRevenueRoomChangeEntityList = byOrderIdList.stream().filter(i -> Objects.equals(i.getType(), MonthAdditionalRevenueEnum.ROOM_CHANGES.getCode())).collect(Collectors.toList());
        List<OrderGiftExtendEntity> giftExtendEntityList = orderGiftExtendRepository.getByOrderIdList(orderIdList, false);


        List<HeOrderEntity> orderList = orderRepository.getByOrderList(orderIdList);
        List<Integer> storeList = orderList.stream().map(HeOrderEntity::getStoreId).collect(Collectors.toList());
        List<CfgStoreEntity> storeEntityList = storeRepository.queryCfgStoreByIdList(storeList);

        List<HeOrderUserSnapshotEntity> heOrderUserSnapshotEntities = orderUserSnapshotRepository.queryByOrderIdList(orderIdList);

        List<HeOrderGoodsEntity> orderGoodsEntityList = orderGoodsRepository.getByOrderIdList(orderIdList);

        List<HeOrderAdditionalRevenueEntity> additionalRevenueEntityList = orderAdditionalRevenueRepository.getByOrderIdList(orderIdList);

        List<UserEntity> userEntities = userRepository.queryUserByIdList(orderList.stream().map(HeOrderEntity::getStaffId).collect(Collectors.toList()));

        List<TabClientEntity> tabClientByIdList = clientRepository.getTabClientByIdList(orderList.stream().map(HeOrderEntity::getClientUid).collect(Collectors.toList()));

        List<HeOrderContinueLiveRecordEntity> orderContinueLiveRecordEntityList = orderContinueLiveRecordRepository.getByOrderIdList(orderIdList);
        List<HeOrderRoomTypeChangeRecordEntity> orderRoomTypeChangeRecordEntityList = orderRoomTypeChangeRecordRepository.getByOrderIdList(orderIdList);

        List<GoodsEntity> goodsEntities = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(orderGoodsEntityList)) {
            List<Integer> collect = orderGoodsEntityList.stream().map(HeOrderGoodsEntity::getGoodsId).collect(Collectors.toList());
            collect.addAll(goodsEntities.stream().map(GoodsEntity::getParentId).collect(Collectors.toList()));
            goodsEntities = goodsRepository.selectByIdList(collect);
        }


        //获取所有导出的数据
        if (CollectionUtils.isNotEmpty(records)) {
            //订单商品名字的信息
            List<StoreGoodsSkuModel> orderGoodsName = goodsRepository.getOrderGoodsName(records.stream().map(QueryOrderPageVO::getOrderId).collect(Collectors.toList()));

            long processingDataStart = System.currentTimeMillis();
            for (int i = 0; i < records.size(); i++) {
                stmOrderExportDTOS.addAll(monthOrderExportDataAssembler.run(records.get(i), i + 1, additionalRevenueStayCostEntityList, additionalRevenueRoomChangeEntityList, giftExtendEntityList, orderList, storeEntityList, heOrderUserSnapshotEntities, orderGoodsEntityList, additionalRevenueEntityList, userEntities, includeMobile, tabClientByIdList, orderContinueLiveRecordEntityList, orderRoomTypeChangeRecordEntityList, goodsEntities, queryOrderPageReq.getGiftOneRow(), orderGoodsName));
            }
            log.info("处理数据时间：{}", System.currentTimeMillis() - processingDataStart);
        }


        Row row = order.createRow(0);
        row.setHeight((short) 800);
        Cell cell = row.createCell(0);
        order.addMergedRegion(new CellRangeAddress(0, 0, 0, 52));
        cell.setCellValue("月子标准订单");
        cell.setCellStyle(styleHeader);

        if (CollectionUtils.isNotEmpty(stmOrderExportDTOS)) {
            long exStart = System.currentTimeMillis();
            int rowNumber = 1;

            for (int i = 0; i < stmOrderExportDTOS.size(); i++) {
                row = order.createRow(rowNumber++);
                STMOrderExportDTO stmOrderExportDTO = stmOrderExportDTOS.get(i);
                STMOrderExport stmOrderExport = stmOrderExportDTO.getStmOrderExport();
                int columnNumber = 0;

                for (int j = 1; j <= 62; j++) {
                    cell = row.createCell(columnNumber++);
                    cell.setCellValue(stmOrderExport.getByIndex(j));
                    if (i == 0) {
                        cell.setCellStyle(styleContext);
                    } else {
                        cell.setCellStyle(styleCenter);
                    }
                }
            }

            Set<String> orderSet = new HashSet<>();

            if (stmOrderExportDTOS.size() > 1) {
                stmOrderExportDTOS = stmOrderExportDTOS.subList(1, stmOrderExportDTOS.size());
            }

            int margeRowNumber = 1;
            for (STMOrderExportDTO stmOrderExportDTO : stmOrderExportDTOS) {
                String orderSn = stmOrderExportDTO.getStmOrderExport().getOrderSn();
                if (orderSet.contains(orderSn)) {
                    continue;
                }

                int start = margeRowNumber + 1;

                List<STMOrderExportDTO> collect = stmOrderExportDTOS.stream().filter(s -> orderSn.equals(s.getStmOrderExport().getOrderSn())).collect(Collectors.toList());
                if (collect.size() > 1) {
                    margeRowNumber += collect.size();
                } else {
                    margeRowNumber++;
                }

                List<Integer> notMarge = Arrays.asList(14, 15, 16, 17, 18, 19, 20, 21, 22, 48, 49, 50, 51, 52, 53, 54);
                for (int j = 0; j <= 62; j++) {
                    if (!notMarge.contains(j) && margeRowNumber > start) {
                        addMergedReigon(sheetX, new CellRangeAddress(start, margeRowNumber, j, j));
                    }
                    order.setColumnWidth(j, 6000);
                }
                orderSet.add(orderSn);
            }
            log.info("导出时间花费：{}", System.currentTimeMillis() - exStart);
        }
        return stmOrderExportDTOS;
    }

    private void setSayOver(List<QueryOrderPageVO> records, SXSSFWorkbook workbook, CellStyle styleCenter, CellStyle styleContext, CellStyle styleHeader, List<STMOrderExportDTO> stmOrderExportDTOS) {
        SXSSFSheet stayOver = workbook.createSheet("续住详情");
        CTWorksheet sheetX = workbook.getXSSFWorkbook().getSheetAt(1).getCTWorksheet();
        List<STMOrderStayOverExport> stmOrderStayOverExports = new ArrayList();
        List<STMOrderStayOverExport> cover = orderConvert.stmSTMOrderExport2stmOrderStayOverExportsList(stmOrderExportDTOS.stream().map(STMOrderExportDTO::getStmOrderExport).collect(Collectors.toList()));

        Map<String, List<STMOrderStayOverExport>> groupByOrderSn = cover.stream().collect(Collectors.groupingBy(STMOrderStayOverExport::getOrderSn));

        for (String s : groupByOrderSn.keySet()) {
            stmOrderStayOverExports.add(groupByOrderSn.get(s).get(0));
        }

        Row row = stayOver.createRow(0);
        row.setHeight((short) 800);
        Cell cell = row.createCell(0);
        stayOver.addMergedRegion(new CellRangeAddress(0, 0, 0, 24));
        cell.setCellValue("续住列表");
        cell.setCellStyle(styleHeader);

        if (CollectionUtils.isNotEmpty(stmOrderStayOverExports)) {
            int rowNumber = 1;

            List<STMOrderStayOverExport> include = new ArrayList<>();
            //设置表格头
            include.add(STMOrderStayOverExport.createSTMOrderStayOverExportHeader());

            Integer orderNo = 1;
            for (int i = 0; i < stmOrderStayOverExports.size(); i++) {

                STMOrderStayOverExport stmOrderStayOverExport = stmOrderStayOverExports.get(i);

                Result<List<OrderCostMultipleBirthsRecordVO>> orderCostMultipleBirthsRecord = monthOrderWxQueryService.getOrderCostMultipleBirthsRecord(Arrays.asList(stmOrderStayOverExport.getOrderId()));

                List<STMOrderStayOverExport> stayOverInfo = monthOrderExportDataAssembler.getStayOver(stmOrderStayOverExport, orderCostMultipleBirthsRecord.getData());

                if (CollectionUtils.isEmpty(stayOverInfo)) {
                    //没有续住的数据就不会出现在表格中
                    continue;
                }
                for (STMOrderStayOverExport stayOverExport : stayOverInfo) {
                    stayOverExport.setNo(orderNo.toString());
                }
                include.addAll(stayOverInfo);
                orderNo++;
            }

            for (int i = 0; i < include.size(); i++) {
                row = stayOver.createRow(rowNumber++);
                STMOrderStayOverExport stmOrderStayOverExport = include.get(i);
                int columnNumber = 0;
                for (int j = 1; j <= 25; j++) {
                    cell = row.createCell(columnNumber++);
                    cell.setCellValue(stmOrderStayOverExport.getByIndex(j));
                    if (i == 0) {
                        cell.setCellStyle(styleContext);
                    } else {
                        cell.setCellStyle(styleCenter);
                    }
                }
            }

            if (include.size() > 1) {
                //去除头
                include = include.subList(1, include.size());
            } else {
                return;
            }

            Set<String> orderSet = new HashSet<>();

            int margeRowNumber = 1;
            for (STMOrderStayOverExport stmOrderStayOverExport : include) {
                String orderSn = stmOrderStayOverExport.getOrderSn();
                if (orderSet.contains(orderSn)) {
                    continue;
                }

                int start = margeRowNumber + 1;

                List<STMOrderStayOverExport> collect = include.stream().filter(s -> orderSn.equals(s.getOrderSn())).collect(Collectors.toList());
                if (collect.size() > 1) {
                    margeRowNumber += collect.size();
                } else {
                    margeRowNumber++;
                }

                List<Integer> notMarge = Arrays.asList(15, 16, 17, 18, 19, 20, 21, 22, 23, 24);
                for (int j = 0; j <= 14; j++) {
                    if (!notMarge.contains(j) && margeRowNumber > start) {
                        addMergedReigon(sheetX, new CellRangeAddress(start, margeRowNumber, j, j));
                    }
                    stayOver.setColumnWidth(j, 6000);
                }
                orderSet.add(orderSn);
            }

            Set<Long> additionalRevenueIdSet = new HashSet<>();
            margeRowNumber = 1;
            for (STMOrderStayOverExport stmOrderStayOverExport : include) {
                Long additionalRevenueId = stmOrderStayOverExport.getAdditionalRevenueId();
                if (additionalRevenueIdSet.contains(additionalRevenueId)) {
                    continue;
                }

                int start = margeRowNumber + 1;

                List<STMOrderStayOverExport> collect = include.stream().filter(s -> additionalRevenueId.equals(s.getAdditionalRevenueId())).collect(Collectors.toList());
                if (collect.size() > 1) {
                    margeRowNumber += collect.size();
                } else {
                    margeRowNumber++;
                }

                List<Integer> notMarge = Arrays.asList(19, 20, 21, 22, 23, 24);
                for (int j = 15; j <= 18; j++) {
                    if (!notMarge.contains(j) && margeRowNumber > start) {
                        addMergedReigon(sheetX, new CellRangeAddress(start, margeRowNumber, j, j));
                    }
                    stayOver.setColumnWidth(j, 6000);
                }
                additionalRevenueIdSet.add(additionalRevenueId);
            }
        }
    }

    private void setRoomChange(List<QueryOrderPageVO> records, SXSSFWorkbook workbook, CellStyle styleCenter, CellStyle styleContext, CellStyle styleHeader, List<STMOrderExportDTO> stmOrderExportDTOS) {
        SXSSFSheet roomChange = workbook.createSheet("房型变更详情");
        CTWorksheet sheetX = workbook.getXSSFWorkbook().getSheetAt(2).getCTWorksheet();
        List<STMOrderRoomChangeExport> stmOrderRoomChangeExports = new ArrayList();
        List<STMOrderRoomChangeExport> cover = orderConvert.stmSTMOrderExport2STMOrderRoomChangeExport(stmOrderExportDTOS.stream().map(STMOrderExportDTO::getStmOrderExport).collect(Collectors.toList()));

        Map<String, List<STMOrderRoomChangeExport>> groupByOrderSn = cover.stream().collect(Collectors.groupingBy(STMOrderRoomChangeExport::getOrderSn));

        for (String s : groupByOrderSn.keySet()) {
            stmOrderRoomChangeExports.add(groupByOrderSn.get(s).get(0));
        }

        Row row = roomChange.createRow(0);
        row.setHeight((short) 800);
        Cell cell = row.createCell(0);
        roomChange.addMergedRegion(new CellRangeAddress(0, 0, 0, 28));
        cell.setCellValue("房型变更列表");
        cell.setCellStyle(styleHeader);

        if (CollectionUtils.isNotEmpty(stmOrderRoomChangeExports)) {
            int rowNumber = 1;

            List<STMOrderRoomChangeExport> include = new ArrayList<>();
            //设置表格头
            include.add(STMOrderRoomChangeExport.createSTMOrderRoomChangeExportHeader());

            Integer orderNo = 1;
            for (STMOrderRoomChangeExport stmOrderRoomChangeExport : stmOrderRoomChangeExports) {

                Result<List<OrderRoomTypeChangeRecordVO>> orderRoomTypeChangeRecord = monthOrderWxQueryService.getOrderRoomTypeChangeRecord(stmOrderRoomChangeExport.getOrderId());

                List<STMOrderRoomChangeExport> roomChangeInfo = monthOrderExportDataAssembler.getRoomChange(stmOrderRoomChangeExport, orderRoomTypeChangeRecord.getData());

                if (CollectionUtils.isEmpty(roomChangeInfo)) {
                    //没有续住的数据就不会出现在表格中
                    continue;
                }
                for (STMOrderRoomChangeExport roomChangeExport : roomChangeInfo) {
                    roomChangeExport.setNo(orderNo + "");
                }
                include.addAll(roomChangeInfo);
                orderNo++;
            }

            for (int i = 0; i < include.size(); i++) {
                row = roomChange.createRow(rowNumber++);
                STMOrderRoomChangeExport stmOrderRoomChangeExport = include.get(i);
                int columnNumber = 0;
                for (int j = 1; j <= 29; j++) {
                    cell = row.createCell(columnNumber++);
                    cell.setCellValue(stmOrderRoomChangeExport.getByIndex(j));
                    if (i == 0) {
                        cell.setCellStyle(styleContext);
                    } else {
                        cell.setCellStyle(styleCenter);
                    }
                }
            }

            if (include.size() > 1) {
                //去除头
                include = include.subList(1, include.size());
            } else {
                return;
            }

            Set<String> orderSet = new HashSet<>();
            int margeRowNumber = 1;

            for (STMOrderRoomChangeExport stmOrderRoomChangeExport : include) {
                String orderSn = stmOrderRoomChangeExport.getOrderSn();
                if (orderSet.contains(orderSn)) {
                    continue;
                }

                int start = margeRowNumber + 1;

                List<STMOrderRoomChangeExport> collect = include.stream().filter(s -> orderSn.equals(s.getOrderSn())).collect(Collectors.toList());
                if (collect.size() > 1) {
                    margeRowNumber += collect.size();
                } else {
                    margeRowNumber++;
                }

                List<Integer> notMarge = Arrays.asList(15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28);
                for (int j = 0; j <= 14; j++) {
                    if (!notMarge.contains(j) && margeRowNumber > start) {
                        addMergedReigon(sheetX, new CellRangeAddress(start, margeRowNumber, j, j));
                    }
                    roomChange.setColumnWidth(j, 6000);
                }
                orderSet.add(orderSn);
            }

            Set<Long> additionalRevenueIdSet = new HashSet<>();
            margeRowNumber = 1;

            for (STMOrderRoomChangeExport stmOrderRoomChangeExport : include) {
                Long additionalRevenueId = stmOrderRoomChangeExport.getAdditionalRevenueId();
                if (additionalRevenueIdSet.contains(additionalRevenueId)) {
                    continue;
                }

                int start = margeRowNumber + 1;

                List<STMOrderRoomChangeExport> collect = include.stream().filter(s -> additionalRevenueId.equals(s.getAdditionalRevenueId())).collect(Collectors.toList());
                if (collect.size() > 1) {
                    margeRowNumber += collect.size();
                } else {
                    margeRowNumber++;
                }

                List<Integer> notMarge = Arrays.asList(21, 22, 23, 24, 25, 26, 27, 28);
                for (int j = 15; j <= 20; j++) {
                    if (!notMarge.contains(j) && margeRowNumber > start) {
                        addMergedReigon(sheetX, new CellRangeAddress(start, margeRowNumber, j, j));
                    }
                    roomChange.setColumnWidth(j, 6000);
                }
                additionalRevenueIdSet.add(additionalRevenueId);
            }

        }
    }

    @Override
    public Result queryPageVerify(QueryOrderPageReq queryOrderPageReq) {
        queryOrderPageReq.setPageNum(1);
        //导出不限制条数
        queryOrderPageReq.setPageSize(Integer.MAX_VALUE);

        if (ObjectUtil.isNotEmpty(queryOrderPageReq.getDateStart())) {
            queryOrderPageReq.setDateStart(queryOrderPageReq.getDateStart() / 1000);
        }

        if (ObjectUtil.isNotEmpty(queryOrderPageReq.getDateEnd())) {
            queryOrderPageReq.setDateEnd(queryOrderPageReq.getDateEnd() / 1000);
        }
        //查询订单
        Page<QueryOrderPageVO> queryOrderPageVOPage = queryPage(queryOrderPageReq);
        List<QueryOrderPageVO> records = queryOrderPageVOPage.getRecords();

        if (records.size() > 2000) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR, "每次最多导出2000条，请重新选择！");
        }

        return Result.success();
    }

    private STMOrderInfoLeaveEarlyInfoVO getSTMOrderInfoLeaveEarlyInfoVOByOrderId(HeOrderEntity byOrderId, STMOrderInfoOrderInfoVO stmOrderInfoOrderInfoVO) {
        String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(byOrderId.getStoreId());
        AheadOutRoomQuery aheadOutRoomQuery = new AheadOutRoomQuery();
        aheadOutRoomQuery.setOrderId(byOrderId.getOrderId());
        AheadOutRoomEntity aheadOutRoomEntity = aheadOutRoomRepository.queryByOrderId(aheadOutRoomQuery);
        if (ObjectUtil.isNotEmpty(aheadOutRoomEntity)) {
            Integer checkOutDays = ObjectUtil.isEmpty(aheadOutRoomEntity) ? 0 : aheadOutRoomEntity.getCheckOutDays();

            Integer originalAmount = ObjectUtil.isEmpty(aheadOutRoomEntity) ? 0 : aheadOutRoomEntity.getOriginalAmount();

            Integer refundedAmount = ObjectUtil.isEmpty(aheadOutRoomEntity) ? 0 : aheadOutRoomEntity.getRefundedAmount();

            //入住总天数=套餐天数+续住天数-提前离馆天数
            stmOrderInfoOrderInfoVO.setCheckInTotalDays(stmOrderInfoOrderInfoVO.getCheckInTotalDays() - checkOutDays);
            //订单金额=订单金额-提前离馆应退原价金额
            stmOrderInfoOrderInfoVO.setOrderAmount(storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(stmOrderInfoOrderInfoVO.getOrderAmountBigDecimal().subtract(new BigDecimal(originalAmount).divide(new BigDecimal(100))).setScale(2)));
            //签约金额=订单签约金额-提前离馆应退金额
            stmOrderInfoOrderInfoVO.setPayableAmount(storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(stmOrderInfoOrderInfoVO.getPayableAmountBigDecimal().subtract(new BigDecimal(refundedAmount).divide(new BigDecimal(100))).setScale(2)));

            if (ObjectUtil.isNotEmpty(aheadOutRoomEntity)) {
                STMOrderInfoLeaveEarlyInfoVO stmOrderInfoLeaveEarlyInfoVO = aheadOutRoomEntity2STMOrderInfoLeaveEarlyInfoVO(storeCurrencyCode, aheadOutRoomEntity);
                return stmOrderInfoLeaveEarlyInfoVO;
            }
        }
        return new STMOrderInfoLeaveEarlyInfoVO();
    }


    private STMOrderInfoLeaveEarlyInfoVO aheadOutRoomEntity2STMOrderInfoLeaveEarlyInfoVO(String storeCurrencyCode, AheadOutRoomEntity aheadOutRoomEntity) {
        STMOrderInfoLeaveEarlyInfoVO stmOrderInfoLeaveEarlyInfoVO = new STMOrderInfoLeaveEarlyInfoVO();

        Integer refundedAmount = aheadOutRoomEntity.getRefundedAmount();
        Integer remainingRefundableAmount = aheadOutRoomEntity.getRemainingRefundableAmount();

        stmOrderInfoLeaveEarlyInfoVO.setCheckInDate(DateUtil.format(aheadOutRoomEntity.getCheckInRoomDate(), "yyyy年MM月dd日"));

        //原离馆时间
        stmOrderInfoLeaveEarlyInfoVO.setOriginalDepartureDate(DateUtil.formatDate(aheadOutRoomEntity.getRoomCheckOutDate()));
        stmOrderInfoLeaveEarlyInfoVO.setOptName(aheadOutRoomEntity.getCreateByName());
        stmOrderInfoLeaveEarlyInfoVO.setEarlyDepartureDate(DateUtil.format(aheadOutRoomEntity.getCheckOutDate(), "yyyy年MM月dd日"));
        stmOrderInfoLeaveEarlyInfoVO.setEarlyDepartureDays(aheadOutRoomEntity.getCheckOutDays());

        stmOrderInfoLeaveEarlyInfoVO.setRefundOriginalAmountBigDecimal(new BigDecimal(aheadOutRoomEntity.getOriginalAmount()).divide(new BigDecimal(100)).setScale(2));
        stmOrderInfoLeaveEarlyInfoVO.setRefundAmountBigDecimal(new BigDecimal(aheadOutRoomEntity.getRefundedAmount()).divide(new BigDecimal(100)).setScale(2));

        stmOrderInfoLeaveEarlyInfoVO.setRefundOriginalAmount(storeCurrencyCode + RMBUtils.changeF2Y(aheadOutRoomEntity.getOriginalAmount().longValue()));
        stmOrderInfoLeaveEarlyInfoVO.setRefundAmount(storeCurrencyCode + RMBUtils.changeF2Y(refundedAmount.longValue()));

        if (remainingRefundableAmount <= 0) {
            stmOrderInfoLeaveEarlyInfoVO.setRefundStatus("已退款");
        } else if (remainingRefundableAmount < refundedAmount) {
            stmOrderInfoLeaveEarlyInfoVO.setRefundStatus("部分退款");
        } else {
            stmOrderInfoLeaveEarlyInfoVO.setRefundStatus("未退款");
        }

        stmOrderInfoLeaveEarlyInfoVO.setOptTime(DateUtil.format(aheadOutRoomEntity.getGmtCreate(), "yyyy-MM-dd HH:mm:ss"));
        return stmOrderInfoLeaveEarlyInfoVO;
    }


    private STMOrderInfoHolidaysInfoVO getSTMOrderInfoHolidaysInfoVOByOrderId(HeOrderEntity byOrderId, List<HeOrderAdditionalRevenueEntity> additionalRevenueEntityList) {
        STMOrderInfoHolidaysInfoVO stmOrderInfoMultipleBirthsInfoVO = new STMOrderInfoHolidaysInfoVO();
        String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(byOrderId.getStoreId());
        List<HeOrderAdditionalRevenueEntity> collect = additionalRevenueEntityList.stream().filter(a -> MonthAdditionalRevenueEnum.HOLIDAY.getCode().equals(a.getType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            HeOrderAdditionalRevenueEntity heOrderAdditionalRevenueEntity = collect.get(0);
            stmOrderInfoMultipleBirthsInfoVO.setHolidayAmount(storeCurrencyCode + RMBUtils.changeF2Y(heOrderAdditionalRevenueEntity.getCost().longValue()));
            stmOrderInfoMultipleBirthsInfoVO.setHolidayContractAmount(storeCurrencyCode + RMBUtils.changeF2Y(heOrderAdditionalRevenueEntity.getPrice().longValue()));
            Integer cost = heOrderAdditionalRevenueEntity.getCost();
            Integer price = heOrderAdditionalRevenueEntity.getPrice();
            if (ObjectUtil.isNotEmpty(heOrderAdditionalRevenueEntity.getPrice()) && heOrderAdditionalRevenueEntity.getPrice() == 0) {
                stmOrderInfoMultipleBirthsInfoVO.setHolidayDiscount("0%");
            } else {
                stmOrderInfoMultipleBirthsInfoVO.setHolidayDiscount(BigDecimalUtil.divideScaleForPercentageCheckZeroDEfaultZero(new BigDecimal(price == 0 ? cost : price), new BigDecimal(cost)).setScale(0, RoundingMode.HALF_UP) + "%");
            }
        }
        return stmOrderInfoMultipleBirthsInfoVO;
    }

    private STMOrderInfoMultipleBirthsInfoVO getSTMOrderInfoMultipleBirthsInfoVOByOrderId(HeOrderEntity byOrderId, List<HeOrderAdditionalRevenueEntity> additionalRevenueEntityList, HeOrderUserSnapshotEntity userSnapshotEntity) {
        STMOrderInfoMultipleBirthsInfoVO stmOrderInfoMultipleBirthsInfoVO = new STMOrderInfoMultipleBirthsInfoVO();
        String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(byOrderId.getStoreId());
        List<HeOrderAdditionalRevenueEntity> collect = additionalRevenueEntityList.stream().filter(a -> MonthAdditionalRevenueEnum.COST_MULTIPLE_BIRTHS.getCode().equals(a.getType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            Integer bornNum = userSnapshotEntity.getBornNum();
            HeOrderAdditionalRevenueEntity heOrderAdditionalRevenueEntity = collect.get(0);
            stmOrderInfoMultipleBirthsInfoVO.setBabyAmount(storeCurrencyCode + RMBUtils.changeF2Y(heOrderAdditionalRevenueEntity.getCost().longValue()));
            stmOrderInfoMultipleBirthsInfoVO.setBabyContractAmount(storeCurrencyCode + RMBUtils.changeF2Y(heOrderAdditionalRevenueEntity.getPrice().longValue()));
            Integer cost = heOrderAdditionalRevenueEntity.getCost();
            Integer price = heOrderAdditionalRevenueEntity.getPrice();
            if (ObjectUtil.isNotEmpty(heOrderAdditionalRevenueEntity.getPrice()) && heOrderAdditionalRevenueEntity.getPrice() == 0) {
                stmOrderInfoMultipleBirthsInfoVO.setBabyDiscount("0%");
            } else {
                stmOrderInfoMultipleBirthsInfoVO.setBabyDiscount(BigDecimalUtil.divideScaleForPercentageCheckZeroDEfaultZero(new BigDecimal(price == 0 ? cost : price), new BigDecimal(cost)).setScale(0, RoundingMode.HALF_UP) + "%");
            }
            stmOrderInfoMultipleBirthsInfoVO.setFetusNum(heOrderAdditionalRevenueEntity.getEmbryoNumber());
            stmOrderInfoMultipleBirthsInfoVO.setBornNum(bornNum == 1 ? "首胎" : bornNum == 2 ? "二胎" : "三胎及以上");
        }
        return stmOrderInfoMultipleBirthsInfoVO;
    }

    private STMOrderInfoRoomChangeInfoVO getSTMOrderInfoRoomChangeInfoVOByOrderId(HeOrderEntity byOrderId, List<HeOrderAdditionalRevenueEntity> additionalRevenueEntityList) {

        STMOrderInfoRoomChangeInfoVO stmOrderInfoRoomChangeInfoVO = new STMOrderInfoRoomChangeInfoVO();
        String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(byOrderId.getStoreId());
        List<STMOrderInfoRoomChangeInfoVO.RoomChange> roomChangeList = new ArrayList();

        List<HeOrderRoomTypeChangeRecordEntity> orderRoomTypeChangeRecordEntityList = orderRoomTypeChangeRecordRepository.getByOrderId(byOrderId.getOrderId());

        List<Long> addIdsList = additionalRevenueEntityList.stream().map(HeOrderAdditionalRevenueEntity::getId).collect(Collectors.toList());

        orderRoomTypeChangeRecordEntityList = orderRoomTypeChangeRecordEntityList.stream().filter(o -> addIdsList.contains(o.getAdditionalRevenueId())).collect(Collectors.toList());

        LinkedHashMap<Long, List<HeOrderRoomTypeChangeRecordEntity>> collect = orderRoomTypeChangeRecordEntityList.stream().collect(Collectors.groupingBy(HeOrderRoomTypeChangeRecordEntity::getAdditionalRevenueId, LinkedHashMap::new, Collectors.toList()));

        BigDecimal allChangeCostAmount = BigDecimal.ZERO;
        BigDecimal allChangeCostSignContractAmount = BigDecimal.ZERO;

        for (Long c : collect.keySet()) {
            List<HeOrderRoomTypeChangeRecordEntity> orderRoomTypeChangeRecordEntities = collect.get(c);
            //获取最后一条
            HeOrderRoomTypeChangeRecordEntity heOrderRoomTypeChangeRecordEntity = orderRoomTypeChangeRecordEntities.get(orderRoomTypeChangeRecordEntities.size() - 1);

            STMOrderInfoRoomChangeInfoVO.RoomChange roomChange = new STMOrderInfoRoomChangeInfoVO.RoomChange();

            String finalRoomTypeChangeCycle = heOrderRoomTypeChangeRecordEntity.getFinalRoomTypeChangeCycle();
            if (StringUtils.isNotEmpty(finalRoomTypeChangeCycle)) {
                List<Integer> read = JsonUtil.read(finalRoomTypeChangeCycle, List.class);
                List<String> strings = ListUtils.rangeList(read);
                roomChange.setRoomTypeChangeCycle(strings.toString().replace("[", "").replace("]", ""));
            } else {
                roomChange.setRoomTypeChangeCycle("");
            }


            roomChange.setOriginalRoomName(heOrderRoomTypeChangeRecordEntity.getOriginalRoomName());
            roomChange.setNewRoomName(heOrderRoomTypeChangeRecordEntity.getNewRoomName());
            roomChange.setDays(heOrderRoomTypeChangeRecordEntity.getFinalDays());
            if (heOrderRoomTypeChangeRecordEntity.getFinalChangeOriginalPrice().compareTo(BigDecimal.ZERO) >= 0) {
                roomChange.setChangeOriginalPrice(storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(heOrderRoomTypeChangeRecordEntity.getFinalChangeOriginalPrice()));
            } else {
                roomChange.setChangeOriginalPrice("-" + storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(heOrderRoomTypeChangeRecordEntity.getFinalChangeOriginalPrice()).replaceAll("-", ""));
            }

            if (heOrderRoomTypeChangeRecordEntity.getFinalChangePrice().compareTo(BigDecimal.ZERO) >= 0) {
                roomChange.setChangePrice(storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(heOrderRoomTypeChangeRecordEntity.getFinalChangePrice()));
            } else {
                roomChange.setChangePrice("-" + storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(heOrderRoomTypeChangeRecordEntity.getFinalChangePrice()).replaceAll("-", ""));
            }

            allChangeCostAmount = allChangeCostAmount.add(heOrderRoomTypeChangeRecordEntity.getFinalChangeOriginalPrice());
            allChangeCostSignContractAmount = allChangeCostSignContractAmount.add(heOrderRoomTypeChangeRecordEntity.getFinalChangePrice());

            roomChangeList.add(roomChange);
        }

        if (allChangeCostAmount.compareTo(BigDecimal.ZERO) >= 0) {
            stmOrderInfoRoomChangeInfoVO.setChangeCostAmount(storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(allChangeCostAmount));
        } else {
            stmOrderInfoRoomChangeInfoVO.setChangeCostAmount("-" + storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(allChangeCostAmount).replaceAll("-", ""));
        }

        if (allChangeCostSignContractAmount.compareTo(BigDecimal.ZERO) >= 0) {
            stmOrderInfoRoomChangeInfoVO.setChangeCostSignContractAmount(storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(allChangeCostSignContractAmount));
        } else {
            stmOrderInfoRoomChangeInfoVO.setChangeCostSignContractAmount("-" + storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(allChangeCostSignContractAmount).replaceAll("-", ""));
        }
        stmOrderInfoRoomChangeInfoVO.setRoomChangeList(roomChangeList);
        return stmOrderInfoRoomChangeInfoVO;
    }

    private STMOrderInfoStayOverInfoVO getSTMOrderInfoStayOverInfoVOByOrderIdV2(HeOrderEntity byOrderId, STMOrderInfoOrderInfoVO stmOrderInfoOrderInfoVO, STMOrderInfoMonthInfoVO orderInfoMonthInfoVO, List<HeOrderAdditionalRevenueEntity> additionalRevenueEntityList) {

        STMOrderInfoStayOverInfoVO stmOrderInfoStayOverInfoVO = new STMOrderInfoStayOverInfoVO();

        List<STMOrderInfoStayOverInfoVO.StayOverList> stayOverList = new ArrayList<>();

        List<HeOrderAdditionalRevenueEntity> stayCostList = additionalRevenueEntityList.stream().filter(a -> MonthAdditionalRevenueEnum.STAY_COST.getCode().equals(a.getType())).collect(Collectors.toList());


        Integer allDay = 0;
        Long allOriginalPrice = 0L, allPrice = 0L;

        if (CollectionUtils.isNotEmpty(stayCostList)) {
            for (HeOrderAdditionalRevenueEntity heOrderAdditionalRevenueEntity : stayCostList) {
                allDay += heOrderAdditionalRevenueEntity.getDays();
                allOriginalPrice += heOrderAdditionalRevenueEntity.getCost();
                allPrice += heOrderAdditionalRevenueEntity.getPrice();

                STMOrderInfoStayOverInfoVO.StayOverList stayOver = new STMOrderInfoStayOverInfoVO.StayOverList();
                stayOver.setDurationOfStay(heOrderAdditionalRevenueEntity.getDays().toString());
                stayOver.setAmountOfRenewal(RMBUtils.changeF2Y(heOrderAdditionalRevenueEntity.getCost().longValue()));
                stayOver.setContinuousHousingType(heOrderAdditionalRevenueEntity.getRoomName());
                stayOver.setContinuousHousingTypeId(heOrderAdditionalRevenueEntity.getRoomId());
                stayOver.setRenewTheContractAmount(RMBUtils.changeF2Y(heOrderAdditionalRevenueEntity.getPrice().longValue()));
                stayOverList.add(stayOver);
            }
        }


        stmOrderInfoStayOverInfoVO.setStayDays(allDay);
        stmOrderInfoStayOverInfoVO.setStayAmount(RMBUtils.changeF2Y(allOriginalPrice));
        stmOrderInfoStayOverInfoVO.setStayPayableAmount(RMBUtils.changeF2Y(allPrice));
        stmOrderInfoStayOverInfoVO.setStayOverList(stayOverList);
        //入住总天数
        Integer goodsServiceDays = orderInfoMonthInfoVO.getServiceDays();
        stmOrderInfoOrderInfoVO.setCheckInTotalDays((ObjectUtil.isNotEmpty(goodsServiceDays) ? goodsServiceDays : 0) + allDay);
        return stmOrderInfoStayOverInfoVO;
    }

    private STMOrderInfoRoomChangeInfoVO getSTMOrderInfoRoomChangeInfoVOByOrderIdV2(HeOrderEntity byOrderId, List<HeOrderAdditionalRevenueEntity> additionalRevenueEntityList, STMOrderInfoMonthInfoVO orderInfoMonthInfoVO) {

        STMOrderInfoRoomChangeInfoVO stmOrderInfoRoomChangeInfoVO = new STMOrderInfoRoomChangeInfoVO();

        List<STMOrderInfoRoomChangeInfoVO.RoomChange> roomChangeList = new ArrayList();

        List<HeOrderAdditionalRevenueEntity> stayCostList = additionalRevenueEntityList.stream().filter(a -> MonthAdditionalRevenueEnum.ROOM_CHANGES.getCode().equals(a.getType())).collect(Collectors.toList());

        Long allChangeCostAmount = 0L, allChangeCostSignContractAmount = 0L;


        if (CollectionUtils.isNotEmpty(stayCostList)) {
            for (HeOrderAdditionalRevenueEntity heOrderAdditionalRevenueEntity : stayCostList) {
                allChangeCostAmount += heOrderAdditionalRevenueEntity.getCost();
                allChangeCostSignContractAmount += heOrderAdditionalRevenueEntity.getPrice();

                STMOrderInfoRoomChangeInfoVO.RoomChange roomChange = new STMOrderInfoRoomChangeInfoVO.RoomChange();
                roomChange.setChangePrice(RMBUtils.changeF2Y(heOrderAdditionalRevenueEntity.getPrice().longValue()));
                roomChange.setChangeOriginalPrice(RMBUtils.changeF2Y(heOrderAdditionalRevenueEntity.getCost().longValue()));

                if (CollectionUtils.isNotEmpty(heOrderAdditionalRevenueEntity.getDaysList())) {
                    List<Integer> daysListInteger = heOrderAdditionalRevenueEntity.getDaysList().stream().map(Integer::valueOf).collect(Collectors.toList());
                    List<String> strings = ListUtils.rangeList(daysListInteger);
                    roomChange.setRoomTypeChangeCycle(strings.toString().replace("[", "").replace("]", ""));
                }

                roomChange.setDays(heOrderAdditionalRevenueEntity.getDays());
                roomChange.setDaysList(heOrderAdditionalRevenueEntity.getDaysList());
                roomChange.setOriginalRoomId(orderInfoMonthInfoVO.getRoomId());
                roomChange.setOriginalRoomName(orderInfoMonthInfoVO.getRoomName());
                roomChange.setNewRoomId(heOrderAdditionalRevenueEntity.getRoomId());
                roomChange.setNewRoomName(heOrderAdditionalRevenueEntity.getRoomName());
                roomChangeList.add(roomChange);

            }
        }

        stmOrderInfoRoomChangeInfoVO.setChangeCostAmount(RMBUtils.changeF2Y(allChangeCostAmount));
        stmOrderInfoRoomChangeInfoVO.setChangeCostSignContractAmount(RMBUtils.changeF2Y(allChangeCostSignContractAmount));
        stmOrderInfoRoomChangeInfoVO.setRoomChangeList(roomChangeList);

        return stmOrderInfoRoomChangeInfoVO;
    }

    private STMOrderInfoStayOverInfoVO getSTMOrderInfoStayOverInfoVOByOrderId(HeOrderEntity byOrderId, STMOrderInfoOrderInfoVO stmOrderInfoOrderInfoVO, STMOrderInfoMonthInfoVO orderInfoMonthInfoVO, List<HeOrderAdditionalRevenueEntity> additionalRevenueEntityList) {

        STMOrderInfoStayOverInfoVO stmOrderInfoStayOverInfoVO = new STMOrderInfoStayOverInfoVO();
        String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(byOrderId.getStoreId());
        List<STMOrderInfoStayOverInfoVO.StayOverList> stayOverList = new ArrayList<>();

        List<HeOrderContinueLiveRecordEntity> orderContinueLiveRecordEntityList = orderContinueLiveRecordRepository.getByOrderId(byOrderId.getOrderId());

        List<Long> addIdList = additionalRevenueEntityList.stream().map(HeOrderAdditionalRevenueEntity::getId).collect(Collectors.toList());

        orderContinueLiveRecordEntityList = orderContinueLiveRecordEntityList.stream().filter(o -> addIdList.contains(o.getAdditionalRevenueId())).collect(Collectors.toList());

        LinkedHashMap<Long, List<HeOrderContinueLiveRecordEntity>> collect = orderContinueLiveRecordEntityList.stream().collect(Collectors.groupingBy(HeOrderContinueLiveRecordEntity::getAdditionalRevenueId, LinkedHashMap::new, Collectors.toList()));

        Integer allDay = 0;
        BigDecimal allOriginalPrice = BigDecimal.ZERO;
        BigDecimal allPrice = BigDecimal.ZERO;

        for (Long c : collect.keySet()) {
            List<HeOrderContinueLiveRecordEntity> orderContinueLiveRecordEntities = collect.get(c);
            //获取最后的记录
            HeOrderContinueLiveRecordEntity heOrderContinueLiveRecordEntity = orderContinueLiveRecordEntities.get(orderContinueLiveRecordEntities.size() - 1);
            allDay += heOrderContinueLiveRecordEntity.getFinalDays();
            allOriginalPrice = allOriginalPrice.add(heOrderContinueLiveRecordEntity.getFinalOriginalPrice());
            allPrice = allPrice.add(heOrderContinueLiveRecordEntity.getFinalPrice());
            STMOrderInfoStayOverInfoVO.StayOverList stay = new STMOrderInfoStayOverInfoVO.StayOverList();
            stay.setDurationOfStay(heOrderContinueLiveRecordEntity.getFinalDays().toString());
            stay.setContinuousHousingTypeId(heOrderContinueLiveRecordEntity.getRoomId());
            stay.setContinuousHousingType(heOrderContinueLiveRecordEntity.getRoomName());

            if (heOrderContinueLiveRecordEntity.getFinalOriginalPrice().compareTo(BigDecimal.ZERO) >= 0) {
                stay.setAmountOfRenewal(storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(heOrderContinueLiveRecordEntity.getFinalOriginalPrice()));
            } else {
                stay.setAmountOfRenewal("-" + storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(heOrderContinueLiveRecordEntity.getFinalOriginalPrice()).replaceAll("-", ""));
            }

            if (heOrderContinueLiveRecordEntity.getFinalPrice().compareTo(BigDecimal.ZERO) >= 0) {
                stay.setRenewTheContractAmount(storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(heOrderContinueLiveRecordEntity.getFinalPrice()));
            } else {
                stay.setRenewTheContractAmount("-" + storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(heOrderContinueLiveRecordEntity.getFinalPrice()).replaceAll("-", ""));
            }

            stayOverList.add(stay);
        }
        //入住总天数=套餐天数+续住总天数
        Integer goodsServiceDays = orderInfoMonthInfoVO.getServiceDays();
        stmOrderInfoOrderInfoVO.setCheckInTotalDays((ObjectUtil.isNotEmpty(goodsServiceDays) ? goodsServiceDays : 0) + allDay);

        if (allOriginalPrice.compareTo(BigDecimal.ZERO) >= 0) {
            stmOrderInfoStayOverInfoVO.setStayAmount(storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(allOriginalPrice));
        } else {
            stmOrderInfoStayOverInfoVO.setStayAmount("-" + storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(allOriginalPrice).replaceAll("-", ""));
        }

        stmOrderInfoStayOverInfoVO.setStayDays(allDay);

        if (allPrice.compareTo(BigDecimal.ZERO) >= 0) {
            stmOrderInfoStayOverInfoVO.setStayPayableAmount(storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(allPrice));
        } else {
            stmOrderInfoStayOverInfoVO.setStayPayableAmount("-" + storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(allPrice).replaceAll("-", ""));
        }

        stmOrderInfoStayOverInfoVO.setStayOverList(stayOverList);
        return stmOrderInfoStayOverInfoVO;
    }

    @Override
    public Result<STMOrderInfoMonthInfoVO> queryOrderGoodsByOrderId(Integer orderId) {
        HeOrderGoodsEntity orderGoods = orderGoodsRepository.getByOrderId(orderId);
        STMOrderInfoMonthInfoVO stmOrderInfoMonthInfoVO = new STMOrderInfoMonthInfoVO();

        stmOrderInfoMonthInfoVO.setGoodsName(orderGoods.getGoodsName());

        stmOrderInfoMonthInfoVO.setServiceDays(orderGoods.getServiceDays());
        stmOrderInfoMonthInfoVO.setRoomName(orderGoods.getRoomName());
        stmOrderInfoMonthInfoVO.setGoodsAmount("￥" + RMBUtils.formatToseparaDecimalsNew(new BigDecimal(orderGoods.getGoodsPriceOrgin()).divide(new BigDecimal(100)).setScale(2)));
        stmOrderInfoMonthInfoVO.setPayableAmount("￥" + RMBUtils.formatToseparaDecimalsNew(new BigDecimal(orderGoods.getPayAmount()).divide(new BigDecimal(100)).setScale(2)));
        stmOrderInfoMonthInfoVO.setPayAmount(orderGoods.getPayAmount());
        Integer goodsPriceOrgin = orderGoods.getGoodsPriceOrgin();
        Integer payAmount = orderGoods.getPayAmount();

        stmOrderInfoMonthInfoVO.setGoodsDiscount(BigDecimalUtil.divideScaleForPercentageCheckZeroDEfaultZero(new BigDecimal(payAmount == 0 ? goodsPriceOrgin : payAmount), new BigDecimal(goodsPriceOrgin)).setScale(0, RoundingMode.HALF_UP) + "%");
        stmOrderInfoMonthInfoVO.setOrderId(orderId);
        return Result.success(stmOrderInfoMonthInfoVO);
    }

    @Override
    public Result<List<STMOrderInfoMonthInfoVO>> queryOrderGoodsByOrderIdList(List<Integer> orderIdList) {
        List<STMOrderInfoMonthInfoVO> list = new ArrayList<>();
        for (Integer orderId : orderIdList) {
            HeOrderGoodsEntity orderGoods = orderGoodsRepository.getByOrderId(orderId);
            if (ObjectUtil.isEmpty(orderGoods)) {
                continue;
            }
            STMOrderInfoMonthInfoVO stmOrderInfoMonthInfoVO = new STMOrderInfoMonthInfoVO();


            stmOrderInfoMonthInfoVO.setGoodsName(orderGoods.getGoodsName());

            stmOrderInfoMonthInfoVO.setServiceDays(orderGoods.getServiceDays());
            stmOrderInfoMonthInfoVO.setRoomName(orderGoods.getRoomName());
            stmOrderInfoMonthInfoVO.setGoodsAmount("￥" + RMBUtils.formatToseparaDecimalsNew(new BigDecimal(orderGoods.getGoodsPriceOrgin()).divide(new BigDecimal(100)).setScale(2)));
            stmOrderInfoMonthInfoVO.setPayableAmount("￥" + RMBUtils.formatToseparaDecimalsNew(new BigDecimal(orderGoods.getPayAmount()).divide(new BigDecimal(100)).setScale(2)));

            Integer goodsPriceOrgin = orderGoods.getGoodsPriceOrgin();
            Integer payAmount = orderGoods.getPayAmount();

            stmOrderInfoMonthInfoVO.setGoodsDiscount(BigDecimalUtil.divideScaleForPercentageCheckZeroDEfaultZero(new BigDecimal(payAmount == 0 ? goodsPriceOrgin : payAmount), new BigDecimal(goodsPriceOrgin)).setScale(0, RoundingMode.HALF_UP) + "%");
            stmOrderInfoMonthInfoVO.setOrderId(orderId);
            list.add(stmOrderInfoMonthInfoVO);
        }
        return Result.success(list);
    }

    private STMOrderInfoMonthInfoVO getSTMOrderInfoMonthInfoVOByOrderId(HeOrderEntity byOrderId) {
        STMOrderInfoMonthInfoVO stmOrderInfoMonthInfoVO = new STMOrderInfoMonthInfoVO();
        stmOrderInfoMonthInfoVO.setOrderType(byOrderId.getOrderType());
        //判断如果是产康或者其他订单
        if (byOrderId.getOrderType().equals(OmniOrderTypeEnum.OTHER_MONTH_ORDER.getCode()) || byOrderId.getOrderType().equals(OmniOrderTypeEnum.PRODUCTION_ORDER.getCode())) {
            stmOrderInfoMonthInfoVO.setGoodsName(orderRepository.getGoodsNameByOrder(byOrderId));

        } else {
            HeOrderGoodsEntity orderGoods = orderGoodsRepository.getByOrderId(byOrderId.getOrderId());

            String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(byOrderId.getStoreId());
            //获取goods的数据
            Integer goodsId = orderGoods.getGoodsId();
            //goods信息
            GoodsEntity goodsEntity = goodsRepository.selectById(goodsId);

            List<StoreGoodsSkuModel> orderGoodsName = goodsRepository.getOrderGoodsName(Arrays.asList(orderGoods.getOrderId()));

            //sku信息
            log.info("goodsEntity：", goodsEntity);
            log.info("orderGoodsName：", orderGoodsName);

            if (CollectionUtils.isNotEmpty(orderGoodsName) && orderGoodsName.get(0).checkGoodsAndSku()) {
                stmOrderInfoMonthInfoVO.setGoodsName(orderGoodsName.get(0).getAllGoodsName());
            }
            stmOrderInfoMonthInfoVO.setGoodsName(orderGoods.getGoodsName());
            stmOrderInfoMonthInfoVO.setEcpRoomType(orderGoods.getEcpRoomType());
            stmOrderInfoMonthInfoVO.setServiceDays(orderGoods.getServiceDays());
            if (byOrderId.isNewOrder()) {

                stmOrderInfoMonthInfoVO.setServiceDays(byOrderId.fetchServiceRoomDays());
            }


            stmOrderInfoMonthInfoVO.setRoomId(orderGoods.getRoomId());
            stmOrderInfoMonthInfoVO.setRoomName(orderGoods.getRoomName());
            stmOrderInfoMonthInfoVO.setGoodsAmount(storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(new BigDecimal(orderGoods.getGoodsPriceOrgin()).divide(new BigDecimal(100)).setScale(2)));
            stmOrderInfoMonthInfoVO.setPayableAmount(storeCurrencyCode + RMBUtils.formatToseparaDecimalsNew(new BigDecimal(orderGoods.getPayAmount()).divide(new BigDecimal(100)).setScale(2)));
            stmOrderInfoMonthInfoVO.setDefinedProperty(orderGoods.getDefinedProperty());
            Integer goodsPriceOrgin = orderGoods.getGoodsPriceOrgin();
            Integer payAmount = orderGoods.getPayAmount();

            stmOrderInfoMonthInfoVO.setGoodsDiscount(BigDecimalUtil.divideScaleForPercentageCheckZeroDEfaultZero(new BigDecimal(payAmount == 0 ? goodsPriceOrgin : payAmount), new BigDecimal(goodsPriceOrgin)).setScale(0, RoundingMode.HALF_UP) + "%");
        }


        return stmOrderInfoMonthInfoVO;
    }

    private STMOrderInfoOrderInfoVO getSTMOrderInfoOrderInfoVOByOrderId(HeOrderEntity byOrderId) {
        String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(byOrderId.getStoreId());
        STMOrderInfoOrderInfoVO stmOrderInfoOrderInfoVO = new STMOrderInfoOrderInfoVO();
        stmOrderInfoOrderInfoVO.setOrderAmount(storeCurrencyCode + RMBUtils.changeF2Y(byOrderId.getOrderAmount().longValue()));
        stmOrderInfoOrderInfoVO.setOrderAmountBigDecimal(new BigDecimal(byOrderId.getOrderAmount()).divide(new BigDecimal(100)));
        stmOrderInfoOrderInfoVO.setPayableAmount(storeCurrencyCode + RMBUtils.changeF2Y(byOrderId.getPayAmount().longValue()));
        stmOrderInfoOrderInfoVO.setPayableAmountBigDecimal(new BigDecimal(byOrderId.getPayAmount()).divide(new BigDecimal(100)));
        if (org.apache.commons.lang3.StringUtils.isNotBlank(byOrderId.getDiscountDetails())) {
            OrderDiscountsCacheVO discountsCache = JSONObject.toJavaObject(JSON.parseObject(byOrderId.getDiscountDetails()), OrderDiscountsCacheVO.class);
            if (ObjectUtil.isNotEmpty(discountsCache)) {
                if (ObjectUtil.isNotEmpty(discountsCache.getOrderDiscount())) {
                    stmOrderInfoOrderInfoVO.setOrderDiscount(discountsCache.getOrderDiscount().setScale(0, RoundingMode.HALF_UP) + "%");
                }
                if (ObjectUtil.isNotEmpty(discountsCache.getNetDiscountRate())) {
                    stmOrderInfoOrderInfoVO.setNetDiscountRate(discountsCache.getNetDiscountRate().setScale(0, RoundingMode.HALF_UP) + "%");
                }
                if (ObjectUtil.isNotEmpty(discountsCache.getGrossProfitMargin())) {
                    stmOrderInfoOrderInfoVO.setGrossProfitMargin(discountsCache.getGrossProfitMargin().setScale(0, RoundingMode.HALF_UP) + "%");
                }
            }
        }
        TagReq tagReq = new TagReq();
        tagReq.setId(byOrderId.getOrderTag());
        TagVO tagVO = tagsService.queryTagByReq(tagReq);
        if (ObjectUtil.isNotEmpty(tagVO)) {
            stmOrderInfoOrderInfoVO.setOrderTagName(tagVO.getTagName());
        }
        List<HeOrderVoucherEntity> voucherEntities = orderVoucherRepository.getByOrderId(byOrderId.getOrderId());
        stmOrderInfoOrderInfoVO.setVoucherUrlList(voucherEntities.stream().map(HeOrderVoucherEntity::getUrl).collect(Collectors.toList()));
        stmOrderInfoOrderInfoVO.setRemark(byOrderId.getRemark());

        return stmOrderInfoOrderInfoVO;
    }

    private AsyncResultDTO<HeOrderEntity, List<ContractSignRecordPaperEntity>, List<MonthContractSignRecordEntity>, HeOrderUserSnapshotEntity> multiThreadGetOrderInfo(Integer orderId) {
        return asyncService.asyncCallResult(
                //获取基本信息
                () -> orderRepository.getByOrderId(orderId),
                //获取已签署纸质合同
                () -> contractSignRecordPaperRepository.getByOrderId(orderId, ContractStatusEnum.SIGNED.code()),
                //获取已签署线上合同
                () -> monthContractSignRecordRepository.getListByOrderId(Long.valueOf(orderId), ContractStatusEnum.SIGNED.code()),
                //获取客户信息
                () -> orderUserSnapshotRepository.queryByOrderId(orderId));
    }

    private STMOOrderBasicInfoVO convertSTMOOrderBasicInfoVO(HeOrderEntity heOrderEntity, List<ContractSignRecordPaperEntity> contractSignRecordPaperEntityList, List<MonthContractSignRecordEntity> monthContractSignRecordEntityList, HeOrderUserSnapshotEntity heOrderUserSnapshotEntity, HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity) {
        STMOOrderBasicInfoVO stmoOrderBasicInfoVO = new STMOOrderBasicInfoVO();

        if (Objects.isNull(heOrderUserSnapshotEntity)) {
            OrderMonthClientReq clientInfoById = tabClientManager.getClientInfoById(heOrderEntity.getClientUid());
            heOrderUserSnapshotEntity = orderUserSnapshotRepository.orderMonthClientReq2Entity(clientInfoById, heOrderEntity.getOrderId());
            heOrderUserSnapshotEntity.setSellerId(heOrderEntity.getStaffId());
        }

        //订单后台-订单详情-基本信息-基本信息
        STMOOrderBasicGeneraInfoVO orderBasicGeneraInfoVO = convertSTMOOrderBasicGeneraInfoVO(heOrderEntity, heOrderUserSnapshotEntity);

        //订单后台-订单详情-基本信息-客户信息
        STMOOrderBasicCustomerInfoVO orderBasicCustomerInfoVO = convertSTMOOrderBasicCustomerInfoVO(heOrderUserSnapshotEntity);

        //订单后台-订单详情-基本信息-委托人信息
        STMOOrderBasicBailorInfoVO orderBasicBailorInfoVO = convertSTMOOrderBasicBailorInfoVO(heOrderBailorSnapshotEntity);

        //订单后台-订单详情-基本信息-合同信息
//        List<STMOOrderBasicContractInfoVO> orderBasicContractInfoVOList = convertSTMOOrderBasicContractInfoVOList(contractSignRecordPaperEntityList, monthContractSignRecordEntityList);

        orderBasicCustomerInfoVO.setPhone(PrivacyUtil.encryptPhoneForFix(orderBasicCustomerInfoVO.getPhone()));
        orderBasicBailorInfoVO.setPhone(PrivacyUtil.encryptPhoneForFix(orderBasicBailorInfoVO.getPhone()));

        stmoOrderBasicInfoVO.setOrderBasicGeneraInfoVO(orderBasicGeneraInfoVO);
        stmoOrderBasicInfoVO.setOrderBasicCustomerInfoVO(orderBasicCustomerInfoVO);
        stmoOrderBasicInfoVO.setOrderBasicBailorInfoVO(orderBasicBailorInfoVO);
//        stmoOrderBasicInfoVO.setOrderBasicContractInfoVOList(orderBasicContractInfoVOList);
        return stmoOrderBasicInfoVO;
    }

    private STMOOrderBasicGeneraInfoVO convertSTMOOrderBasicGeneraInfoVO(HeOrderEntity heOrderEntity, HeOrderUserSnapshotEntity heOrderUserSnapshotEntity) {
        CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(heOrderEntity.getStoreId());
//        //E签宝合同与纸质合同列表
//        List<MonthContractSignRecordEntity> monthContractSignRecordRepositoryListByOrderId = monthContractSignRecordRepository.getListByOrderId(Long.valueOf(heOrderEntity.getOrderId()));
//        List<ContractSignRecordPaperEntity> contractSignRecordPaperRepositoryByOrderId = contractSignRecordPaperRepository.getByOrderId(heOrderEntity.getOrderId());
        STMOOrderBasicGeneraInfoVO sTMOOrderBasicGeneraInfoVO = new STMOOrderBasicGeneraInfoVO();
        sTMOOrderBasicGeneraInfoVO.setOrderNo(heOrderEntity.getOrderSn());
        Integer regional;
        if (ObjectUtil.isEmpty(cfgStoreEntity) || ObjectUtil.isEmpty(cfgStoreEntity.getRegional())) {
            regional = RegionalEnum.REGIONAL_NONE.getCode();
        } else {
            regional = cfgStoreEntity.getRegional();
        }
        sTMOOrderBasicGeneraInfoVO.setRegional(regional);
        sTMOOrderBasicGeneraInfoVO.setRegionalStr(RegionalEnum.getValueByCode(regional));
        sTMOOrderBasicGeneraInfoVO.setWarzone(cfgStoreEntity.getWarzone());
        sTMOOrderBasicGeneraInfoVO.setWarzoneStr(storeClient.getAreaName(cfgStoreEntity.getWarzone()));
        sTMOOrderBasicGeneraInfoVO.setStoreId(heOrderEntity.getStoreId());
        sTMOOrderBasicGeneraInfoVO.setStoreType(cfgStoreEntity.getType());
        sTMOOrderBasicGeneraInfoVO.setStoreTypeStr(StoreTypeEnum.getValueByCode(cfgStoreEntity.getType()));
        sTMOOrderBasicGeneraInfoVO.setStoreName(ObjectUtil.isEmpty(cfgStoreEntity) ? "" : cfgStoreEntity.getStoreName());
//        if (ObjectUtil.isNotEmpty(monthContractSignRecordRepositoryListByOrderId)) {
//            List<MonthContractSignRecordEntity> monthContractSignRecordEntities = monthContractSignRecordRepositoryListByOrderId.stream().filter(x -> x.getTemplateContractType().equals(TemplateContractTypeEnum.DISCOUNT.code())).collect(Collectors.toList());
//            if (ObjectUtil.isNotEmpty(monthContractSignRecordEntities)) {
//
//            }
//        }

        OaProcessIdRelationPO lastRecordOrderByTypeAndOrderId = oaProcessIdRelationService.getLastRecordOrderByTypeAndOrderId(heOrderEntity.getOrderId(), AuditTypeEnum.DISCOUNT_APPROVAL.getCode());

        OldOrderApproveStatusEnum oldOrderApproveStatusEnum = convertOldOrderApproveStatusEnum(lastRecordOrderByTypeAndOrderId);
        sTMOOrderBasicGeneraInfoVO.setDiscountApprovalStatus(oldOrderApproveStatusEnum.getCode());
        sTMOOrderBasicGeneraInfoVO.setDiscountApprovalStatusStr(oldOrderApproveStatusEnum.getValue());
        sTMOOrderBasicGeneraInfoVO.setOrderStatus(heOrderEntity.getOrderStatus());
        sTMOOrderBasicGeneraInfoVO.setOrderStatusStr(OrderStatusV2Enum.getByCode(heOrderEntity.getOrderStatus()).getValue());
        sTMOOrderBasicGeneraInfoVO.setPayStatus(heOrderEntity.getPayStatus());
        sTMOOrderBasicGeneraInfoVO.setPayStatusStr(PayStatusV2Enum.getName(heOrderEntity.getPayStatus()));
        sTMOOrderBasicGeneraInfoVO.setRefundStatus(heOrderEntity.getRefundStatus());
        sTMOOrderBasicGeneraInfoVO.setRefundStatusStr(OrderRefundStatusEnum.getName(heOrderEntity.getRefundStatus()));
        if (ObjectUtil.notEqual(heOrderEntity.getPercentFirstTime(), 0) && ObjectUtil.notEqual(heOrderEntity.getPercentFirstTime(), -1)) {
            sTMOOrderBasicGeneraInfoVO.setPerformanceEffectiveDate(DateUtil.formatDateTime(new Date(new Long(heOrderEntity.getPercentFirstTime()) * 1000)));
        }
        sTMOOrderBasicGeneraInfoVO.setCreatedAt(DateUtil.formatDateTime(new Date(heOrderEntity.getCreatedAt() * 1000)));
        sTMOOrderBasicGeneraInfoVO.setUpdatedAt(DateUtil.formatDateTime(new Date(heOrderEntity.getUpdatedAt() * 1000)));

        if (Objects.nonNull(heOrderUserSnapshotEntity)) {
            sTMOOrderBasicGeneraInfoVO.setCreatedId(heOrderUserSnapshotEntity.getSellerId());
            sTMOOrderBasicGeneraInfoVO.setCreateByName(heOrderUserSnapshotEntity.getSellerName());
        }

        //设置修改人
        Integer updateStaffId = heOrderEntity.getUpdateStaffId();
        if (ObjectUtil.isNotEmpty(updateStaffId)) {
            UserEntity userEntity = userRepository.queryById(updateStaffId);
            if (ObjectUtil.isNotEmpty(userEntity)) {
                sTMOOrderBasicGeneraInfoVO.setUpdateById(updateStaffId);
                sTMOOrderBasicGeneraInfoVO.setUpdateByName(userEntity.getName());
            }
        }
        //业务特殊要求如果子品牌是BELLA VILLA 品牌为子品牌
        if (StoreChildTypeEnum.BELLA_VILLA.getCode().equals(cfgStoreEntity.getChildType())) {
            sTMOOrderBasicGeneraInfoVO.setStoreBrand(StoreChildTypeEnum.getValueByCode(cfgStoreEntity.getChildType()));
        } else {
            sTMOOrderBasicGeneraInfoVO.setStoreBrand(StoreTypeEnum.getValueByCode(cfgStoreEntity.getType()));
        }


        return sTMOOrderBasicGeneraInfoVO;

    }

    private STMOOrderBasicCustomerInfoVO convertSTMOOrderBasicCustomerInfoVO(HeOrderUserSnapshotEntity heOrderUserSnapshotEntity) {
        STMOOrderBasicCustomerInfoVO sTMOOrderBasicCustomerInfoVO = new STMOOrderBasicCustomerInfoVO();
        sTMOOrderBasicCustomerInfoVO.setClientUid(heOrderUserSnapshotEntity.getClientUid());
        sTMOOrderBasicCustomerInfoVO.setBasicUid(heOrderUserSnapshotEntity.getBasicUid());
        sTMOOrderBasicCustomerInfoVO.setName(heOrderUserSnapshotEntity.getName());
        sTMOOrderBasicCustomerInfoVO.setPhone(heOrderUserSnapshotEntity.getPhone());
        sTMOOrderBasicCustomerInfoVO.setPredictBornDate(heOrderUserSnapshotEntity.getPredictBornDate());
        sTMOOrderBasicCustomerInfoVO.setCertType(heOrderUserSnapshotEntity.getCertType());
        sTMOOrderBasicCustomerInfoVO.setCertTypeStr(IdTypeEnum.getValue(heOrderUserSnapshotEntity.getCertType()));
        sTMOOrderBasicCustomerInfoVO.setIdCard(heOrderUserSnapshotEntity.getIdCard());
        sTMOOrderBasicCustomerInfoVO.setIdCardFront(heOrderUserSnapshotEntity.getIdCardFront());
        sTMOOrderBasicCustomerInfoVO.setIdCardBack(heOrderUserSnapshotEntity.getIdCardBack());
        sTMOOrderBasicCustomerInfoVO.setConstellationType(heOrderUserSnapshotEntity.getConstellationType());
        sTMOOrderBasicCustomerInfoVO.setConstellationTypeStr(heOrderUserSnapshotEntity.getConstellation());
        sTMOOrderBasicCustomerInfoVO.setProvince(heOrderUserSnapshotEntity.getProvince());
        sTMOOrderBasicCustomerInfoVO.setCity(heOrderUserSnapshotEntity.getCity());
        sTMOOrderBasicCustomerInfoVO.setRegion(heOrderUserSnapshotEntity.getRegion());
        sTMOOrderBasicCustomerInfoVO.setAddress(heOrderUserSnapshotEntity.getAddress());
        sTMOOrderBasicCustomerInfoVO.setPhoneCiphertext(heOrderUserSnapshotEntity.getPhoneCiphertext());
        sTMOOrderBasicCustomerInfoVO.setUrgentPhoneCiphertext(heOrderUserSnapshotEntity.getUrgentPhoneCiphertext());
        sTMOOrderBasicCustomerInfoVO.setIdCardFrontCiphertext(heOrderUserSnapshotEntity.getIdCardFrontCiphertext());
        sTMOOrderBasicCustomerInfoVO.setIdCardBackCiphertext(heOrderUserSnapshotEntity.getIdCardBackCiphertext());
        sTMOOrderBasicCustomerInfoVO.setIdCardCiphertext(heOrderUserSnapshotEntity.getIdCardCiphertext());


        //组装省市区
        String provincesRegions = addressQueryService.getProvincesRegions(heOrderUserSnapshotEntity.getProvince(), heOrderUserSnapshotEntity.getCity(), heOrderUserSnapshotEntity.getRegion());
        sTMOOrderBasicCustomerInfoVO.setAllAddress(provincesRegions + heOrderUserSnapshotEntity.getAddress());
        sTMOOrderBasicCustomerInfoVO.setBornNum(heOrderUserSnapshotEntity.getBornNum());
        sTMOOrderBasicCustomerInfoVO.setBornNumStr(BornNumEnum.getValueByCode(heOrderUserSnapshotEntity.getBornNum()));
        sTMOOrderBasicCustomerInfoVO.setFetusNum(heOrderUserSnapshotEntity.getFetusNum());
        if (ObjectUtil.isNotEmpty(heOrderUserSnapshotEntity.getFetusNum()) && heOrderUserSnapshotEntity.getFetusNum() > 0) {
            sTMOOrderBasicCustomerInfoVO.setFetusNumStr(BirthTypeEnum.getValueByCode(heOrderUserSnapshotEntity.getFetusNum()));
        }
        //如果是备孕中,wantIn不传
        sTMOOrderBasicCustomerInfoVO.setWantIn(heOrderUserSnapshotEntity.getWantIn());
        sTMOOrderBasicCustomerInfoVO.setHospital(heOrderUserSnapshotEntity.getHospital());
        sTMOOrderBasicCustomerInfoVO.setUrgentName(heOrderUserSnapshotEntity.getUrgentName());
        sTMOOrderBasicCustomerInfoVO.setUrgentPhone(heOrderUserSnapshotEntity.getUrgentPhone());
        sTMOOrderBasicCustomerInfoVO.setProfession(heOrderUserSnapshotEntity.getProfession());
        sTMOOrderBasicCustomerInfoVO.setAge(heOrderUserSnapshotEntity.getAge());
        sTMOOrderBasicCustomerInfoVO.setGestationWeekNow(heOrderUserSnapshotEntity.getGestationWeekNow());
        sTMOOrderBasicCustomerInfoVO.setBloodType(heOrderUserSnapshotEntity.getBloodType());
        sTMOOrderBasicCustomerInfoVO.setBloodTypeStr(BloodTypeEnum.getValueByCode(heOrderUserSnapshotEntity.getBloodType()));
        sTMOOrderBasicCustomerInfoVO.setRelationWithClient(heOrderUserSnapshotEntity.getRelationWithClient());
        sTMOOrderBasicCustomerInfoVO.setRelationWithClientStr(RelationshipWithEnum.getValueByCode(heOrderUserSnapshotEntity.getRelationWithClient()));
        sTMOOrderBasicCustomerInfoVO.setIsPhoneVerify(heOrderUserSnapshotEntity.getIsPhoneVerify());
        sTMOOrderBasicCustomerInfoVO.setIsPhoneVerifyStr(PhoneVerifyEnum.getValueByCode(heOrderUserSnapshotEntity.getIsPhoneVerify()));
        sTMOOrderBasicCustomerInfoVO.setIsHaveQrCode(ObjectUtil.isEmpty(heOrderUserSnapshotEntity.getQrCode()) ? 0 : 1);
        sTMOOrderBasicCustomerInfoVO.setQrCode(heOrderUserSnapshotEntity.getQrCode());
        sTMOOrderBasicCustomerInfoVO.setFromType(heOrderUserSnapshotEntity.getFromType());
        sTMOOrderBasicCustomerInfoVO.setFromTypeStr(FromTypeEnum.fromCode(heOrderUserSnapshotEntity.getFromType()));
        sTMOOrderBasicCustomerInfoVO.setIsCardVerify(heOrderUserSnapshotEntity.getIsCardVerify());
        sTMOOrderBasicCustomerInfoVO.setIsCardVerifyStr(CardVerifyEnum.getValueByCode(heOrderUserSnapshotEntity.getIsCardVerify()));
        return sTMOOrderBasicCustomerInfoVO;
    }

    private STMOOrderBasicBailorInfoVO convertSTMOOrderBasicBailorInfoVO(HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity) {
        STMOOrderBasicBailorInfoVO sTMOOrderBasicBailorInfoVO = new STMOOrderBasicBailorInfoVO();
        if (ObjectUtil.isNotEmpty(heOrderBailorSnapshotEntity)) {
            sTMOOrderBasicBailorInfoVO.setId(heOrderBailorSnapshotEntity.getId());
            sTMOOrderBasicBailorInfoVO.setClientUid(heOrderBailorSnapshotEntity.getClientUid());
            sTMOOrderBasicBailorInfoVO.setName(heOrderBailorSnapshotEntity.getName());
            sTMOOrderBasicBailorInfoVO.setPhone(heOrderBailorSnapshotEntity.getPhone());
            sTMOOrderBasicBailorInfoVO.setPhoneType(heOrderBailorSnapshotEntity.getPhoneType());
            sTMOOrderBasicBailorInfoVO.setCertType(heOrderBailorSnapshotEntity.getCertType());
            sTMOOrderBasicBailorInfoVO.setIdCard(heOrderBailorSnapshotEntity.getIdCard());
            sTMOOrderBasicBailorInfoVO.setIdCardFront(heOrderBailorSnapshotEntity.getIdCardFront());
            sTMOOrderBasicBailorInfoVO.setIdCardBack(heOrderBailorSnapshotEntity.getIdCardBack());
            sTMOOrderBasicBailorInfoVO.setIsPhoneVerify(heOrderBailorSnapshotEntity.getIsPhoneVerify());
            sTMOOrderBasicBailorInfoVO.setIsCardVerify(heOrderBailorSnapshotEntity.getIsCardVerify());
            //TODO 与委托人关系暂无
            sTMOOrderBasicBailorInfoVO.setRelationshipPrincipal(-1);
            sTMOOrderBasicBailorInfoVO.setRelationshipPrincipalStr("暂无");
        }

        return sTMOOrderBasicBailorInfoVO;
    }

    private List<STMOOrderBasicContractInfoVO> convertSTMOOrderBasicContractInfoVOList(List<ContractSignRecordPaperEntity> contractSignRecordPaperEntityList, List<MonthContractSignRecordEntity> monthContractSignRecordEntityList) {
        List<STMOOrderBasicContractInfoVO> voList = new ArrayList<>();
        List<STMOOrderBasicContractInfoVO> voList1 = orderConvert.contractSignRecordPaperEntity2VOList(contractSignRecordPaperEntityList);
        if (ObjectUtil.isNotEmpty(voList1)) {
            voList1.forEach(x -> {
                if (ObjectUtil.isNotEmpty(x.getUpdatedAt())) {
                    x.setUpdatedAt(new Date(x.getUpdatedAt().getTime() * 1000));
                }
                x.setContractSignType(ContractTypeEnum.PAPER_TYPE.code());
                x.setContractSignTypeStr(ContractTypeEnum.PAPER_TYPE.desc());
            });
        }
        List<STMOOrderBasicContractInfoVO> voList2 = orderConvert.monthContractSignRecordEntity2VOList(monthContractSignRecordEntityList);
        if (ObjectUtil.isNotEmpty(voList2)) {
            voList2.forEach(x -> {
                if (ObjectUtil.isNotEmpty(x.getUpdatedAt())) {
                    x.setUpdatedAt(new Date(x.getUpdatedAt().getTime() * 1000));
                }
                x.setContractSignType(ContractTypeEnum.ESIGN_TYPE.code());
                x.setContractSignTypeStr(ContractTypeEnum.ESIGN_TYPE.desc());
            });
        }
        voList.addAll(voList1);
        voList.addAll(voList2);
        voList.forEach(x -> {
            x.setTemplateTypeStr(TemplateContractTypeEnum.fromCode(x.getTemplateType()));
            x.setContractStatusStr(ContractStatusEnum.fromCode(x.getContractStatus()));
        });
        return voList;
    }


    /**
     * 房型变更
     *
     * @param collect
     * @return
     */
    private List<STMOrderExportDTO.RoomTypeChange> orderRoomTypeChangeRecordEntity2RoomTypeChangeList(List<HeOrderAdditionalRevenueEntity> collect, List<HeOrderRoomTypeChangeRecordEntity> orderRoomTypeChangeRecordEntityList) {
        List<STMOrderExportDTO.RoomTypeChange> result = new ArrayList<>();
        for (HeOrderAdditionalRevenueEntity heOrderRoomTypeChangeRecordEntity : collect) {
            STMOrderExportDTO.RoomTypeChange roomTypeChange = new STMOrderExportDTO.RoomTypeChange();

            List<HeOrderRoomTypeChangeRecordEntity> roomTypeChangeRecordEntityList = orderRoomTypeChangeRecordEntityList.stream().filter(o -> o.getAdditionalRevenueId().equals(heOrderRoomTypeChangeRecordEntity.getId())).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(roomTypeChangeRecordEntityList)) {
                HeOrderRoomTypeChangeRecordEntity first = roomTypeChangeRecordEntityList.get(0);
                HeOrderRoomTypeChangeRecordEntity last = roomTypeChangeRecordEntityList.get(roomTypeChangeRecordEntityList.size() - 1);
                roomTypeChange.setOriginalRoomName(first.getOriginalRoomName());
                roomTypeChange.setNewRoomName(last.getNewRoomName());
            }

            roomTypeChange.setChangeOriginalPrice(RMBUtils.formatToseparaDecimalsNew(new BigDecimal(heOrderRoomTypeChangeRecordEntity.getCost() / 100)));
            roomTypeChange.setFinalChangeOriginalPrice(RMBUtils.formatToseparaDecimalsNew(new BigDecimal(heOrderRoomTypeChangeRecordEntity.getPrice() / 100)));
            roomTypeChange.setDays(heOrderRoomTypeChangeRecordEntity.getDays().toString());
            List<String> daysList = heOrderRoomTypeChangeRecordEntity.getDaysList();
            if (CollectionUtils.isNotEmpty(daysList)) {

                List<Integer> daysListInteger = daysList.stream().map(Integer::valueOf).collect(Collectors.toList());
                List<String> strings = ListUtils.rangeList(daysListInteger);
                roomTypeChange.setRoomTypeChangeCycle(strings.toString().replace("[", "").replace("]", ""));
            }
            result.add(roomTypeChange);
        }
        return result;
    }

    /**
     * 续住
     *
     * @param collect
     * @return
     */
    private List<STMOrderExportDTO.ResumingStay> orderContinueLiveRecordEntity2ResumingStayList(List<HeOrderAdditionalRevenueEntity> collect) {
        List<STMOrderExportDTO.ResumingStay> result = new ArrayList<>();
        for (HeOrderAdditionalRevenueEntity heOrderContinueLiveRecordEntity : collect) {
            STMOrderExportDTO.ResumingStay resumingStay = new STMOrderExportDTO.ResumingStay();
            resumingStay.setRehousingType(heOrderContinueLiveRecordEntity.getRoomName());
            resumingStay.setAmountRenewal(RMBUtils.formatToseparaDecimalsNew(new BigDecimal(heOrderContinueLiveRecordEntity.getCost() / 100)));
            resumingStay.setAmountContractRenewal(RMBUtils.formatToseparaDecimalsNew(new BigDecimal(heOrderContinueLiveRecordEntity.getPrice() / 100)));
            resumingStay.setDurationOfStay(heOrderContinueLiveRecordEntity.getDays().toString());
            result.add(resumingStay);
        }
        return result;
    }


    /**
     * 折扣审批状态转换
     */
    @Override
    public OldOrderApproveStatusEnum convertOldOrderApproveStatusEnum(OaProcessIdRelationPO oaProcessIdRelationPO) {
        //当前审批状态：0:进行中；1同意,2拒绝,3撤销
        //折扣审批状态 0=无需审批 1=审批中 2=审批通过 3=审批失败 4=发起失败
        Integer code = null;
        if (ObjectUtil.isEmpty(oaProcessIdRelationPO)) {
            code = 0;
        } else {
            if (ObjectUtil.isEmpty(oaProcessIdRelationPO.getApproveStatus())) {
                code = 4;
            } else {
                code = oaProcessIdRelationPO.getApproveStatus() + 1;
            }
        }
        return OldOrderApproveStatusEnum.getEnumByCode(code);
    }

    @Override
    public List<OrderInfoVO> queryByOrderSn(List<String> orderSnList) {
        List<HeOrderEntity> byOrderSnList = orderRepository.getByOrderSnList(orderSnList);

        List<HeOrderGoodsEntity> byOrderIdList = orderGoodsRepository.getByOrderIdList(byOrderSnList.stream().map(HeOrderEntity::getOrderId).collect(Collectors.toList()));
        Map<Integer, List<HeOrderGoodsEntity>> goodsEntityListMap = byOrderIdList.stream().collect(Collectors.groupingBy(HeOrderGoodsEntity::getOrderId));

        List<OrderInfoVO> orderInfoVOS = CollectionUtil.isEmpty(byOrderSnList) ? new ArrayList<>() : BeanMapper.mapList(byOrderSnList, OrderInfoVO.class);
        for (OrderInfoVO heOrderEntity : orderInfoVOS) {
            List<HeOrderGoodsEntity> orderGoodsEntities = goodsEntityListMap.get(heOrderEntity.getOrderId());
            if (CollectionUtils.isEmpty(orderGoodsEntities)) {
                heOrderEntity.setIfSmallMonthAgeOrder(Boolean.FALSE);
            }
            heOrderEntity.setIfSmallMonthAgeOrder(judgeIsSmallMonthAgeProduct(orderGoodsEntities));
        }
        log.info("查询是否为小月龄订单返回值为 {}",JSONObject.toJSONString(orderInfoVOS));

        return orderInfoVOS;
    }


    /**
     * 判断是否小月龄商品
     * @param orderGoodsEntityList
     * @return
     */
    private boolean judgeIsSmallMonthAgeProduct(List<HeOrderGoodsEntity> orderGoodsEntityList) {
        if (org.springframework.util.CollectionUtils.isEmpty(orderGoodsEntityList)) {
            return false;
        }
        for (HeOrderGoodsEntity orderGoodsEntity : orderGoodsEntityList) {
            String goodsName = orderGoodsEntity.getGoodsName();
            if (StringUtils.isNotEmpty(goodsName) && goodsName.contains("小月龄")) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param orderIds
     * @return
     */
    @Override
    public List<OrderInfoVO> queryByOrderId(List<Integer> orderIds) {
        List<HeOrderEntity> byOrderSnList = orderRepository.getByOrderList(orderIds);
        return CollectionUtil.isEmpty(byOrderSnList) ? new ArrayList<>() : BeanMapper.mapList(byOrderSnList, OrderInfoVO.class);
    }

    /**
     * 根据支付时间查询-客户信息
     *
     * @param req
     */
    @Override
    public Result<List<BasicSimpInfoVO>> queryOrderForPayFirstRecord(PayFirstRecordReq req) {
        List<HeOrderEntity> orderEntities = orderRepository.queryOrderForPayFirstRecord(req);
        if (ObjectUtil.isNotEmpty(orderEntities)) {
            List<Long> basicUidList = orderEntities.stream().map(x -> x.getBasicUid().longValue()).distinct().collect(Collectors.toList());
            List<BasicSimpInfoVO> byBasicUidList = basicManager.getByBasicUidList(basicUidList);
            return Result.success(byBasicUidList);
        }
        return Result.success(new ArrayList<>());
    }

    @Override
    public PageDTO<HeOrderVO> page(OrderPageReq req) {

        if (ObjectUtil.isNotEmpty(req.getDateType()) &&
                req.getDateType() == 3 &&
                ObjectUtil.isNotEmpty(req.getDateStart()) &&
                ObjectUtil.isNotEmpty(req.getDateEnd()) &&
                (req.getDateEnd() - req.getDateStart() > 60 * 60 * 24 * 90)) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "根据入住日期查询最大范围为3个月，导出无限制。");
        }
        fildOrderPageReq(req);
        Page<HeOrderEntity> pageList = orderRepository.queryList(req);
        if (CollectionUtils.isEmpty(pageList.getRecords())) {
            return PageUtils.convert2PageDTO(pageList, null);
        }
        List<HeOrderEntity> heOrderEntityList = pageList.getRecords();

        CompletableFuture<Map<Integer, TabClientEntity>> tabClientMap = CompletableFuture.supplyAsync(() -> {
            List<Integer> clientUidList = heOrderEntityList.stream().map(HeOrderEntity::getClientUid).collect(Collectors.toList());
            List<TabClientEntity> tabClientByIdList = clientRepository.getTabClientByIdList(clientUidList);
            return tabClientByIdList.stream().collect(Collectors.toMap(TabClientEntity::getId, entity -> entity, (v1, v2) -> v1));
        });

        CompletableFuture<Map<Integer, CfgStoreEntity>> storeMapFuture = CompletableFuture.supplyAsync(() -> {
            List<Integer> storeIdList = heOrderEntityList.stream().map(HeOrderEntity::getStoreId).collect(Collectors.toList());
            List<CfgStoreEntity> storeByIdList = storeRepository.queryCfgStoreByIdList(storeIdList);
            return storeByIdList.stream().collect(Collectors.toMap(CfgStoreEntity::getStoreId, entity -> entity, (v1, v2) -> v1));
        });

        CompletableFuture<Map<Integer, String>> orderTagMapFuture = CompletableFuture.supplyAsync(() -> {
            List<HeOrderTagsEntity> allOrderTags = tagsRepository.getAllOrderTags();
            return allOrderTags.stream().collect(Collectors.toMap(HeOrderTagsEntity::getId, HeOrderTagsEntity::getTagName));
        });

        CompletableFuture<Map<Integer, String>> staffIdMapFuture = CompletableFuture.supplyAsync(() -> {
            List<Integer> staffIdList = pageList.getRecords().stream().flatMap(e -> Stream.of(e.getStaffId(), e.getUpdateStaffId(), e.getCreateBy().intValue())).collect(Collectors.toList());
            List<UserEntity> userEntityList = userRepository.queryUserByIdList(staffIdList);
            return userEntityList.stream().collect(Collectors.toMap(UserEntity::getId, UserEntity::getName));
        });

        List<Integer> orderIdList = heOrderEntityList.stream().map(HeOrderEntity::getOrderId).distinct().collect(Collectors.toList());

        CompletableFuture<Map<String, String>> warZoneMapFuture = CompletableFuture.supplyAsync(() -> {
            List<SelectRespVO> attribute = storeClient.getAttribute(dynamicConfig.getWarZone());
            return attribute.stream().collect(Collectors.toMap(SelectRespVO::getValue, SelectRespVO::getLabel, (v1, v2) -> v1));
        });

        CompletableFuture<Map<Integer, List<HeIncomeRecordEntity>>> orderIdIncomeMapFuture = CompletableFuture.supplyAsync(() -> {
            List<HeIncomeRecordEntity> recordListByOrderIdList = incomeRecordRepository.getRecordListByOrderIdList(orderIdList, null, null);
            return recordListByOrderIdList.stream().collect(Collectors.groupingBy(HeIncomeRecordEntity::getOrderId));
        });

        CompletableFuture<Map<Integer, List<HeIncomeProofRecordEntity>>> orderIdProofRecordMapFuture = CompletableFuture.supplyAsync(() -> {
            List<HeIncomeProofRecordEntity> incomeProofRecordByOrderIdList = incomeProofRecordRepository.getIncomeProofRecordByOrderIdList(orderIdList);
            return incomeProofRecordByOrderIdList.stream().collect(Collectors.groupingBy(HeIncomeProofRecordEntity::getOrderId));
        });

        CompletableFuture<Map<Long, List<OrderReductionEntity>>> orderReductionRecordMapFuture = CompletableFuture.supplyAsync(() -> {
            List<OrderReductionEntity> incomeProofRecordByOrderIdList = orderReductionRepository.listByOrderIdList(orderIdList, Lists.newArrayList(OrderDecreaseStatusEnum.APPROVED.getCode(), OrderDecreaseStatusEnum.NO_APPROVAL_NEEDED.getCode()));
            return incomeProofRecordByOrderIdList.stream().collect(Collectors.groupingBy(OrderReductionEntity::getOrderId));
        });




        CompletableFuture<Map<Integer, List<HeOrderGoodsEntity>>> orderGoodsEntityFuture = CompletableFuture.supplyAsync(() -> {
            List<HeOrderGoodsEntity> orderGoodsList = orderGoodsRepository.getByOrderIdList(orderIdList);
            return orderGoodsList.stream().collect(Collectors.groupingBy(HeOrderGoodsEntity::getOrderId));
        });

        CompletableFuture<Map<Integer, List<HeOrderRefundEntity>>> orderRefundFuture = CompletableFuture.supplyAsync(() -> {
            List<HeOrderRefundEntity> orderRefundList = orderRefundRepository.getRefundByOrderIdList(orderIdList);
            return orderRefundList.stream().collect(Collectors.groupingBy(HeOrderRefundEntity::getOrderId));
        });

        CompletableFuture<Map<Integer, List<HeUserProductionAmountRefundLogEntity>>> heUserProductionAmountRefundLogFuture = CompletableFuture.supplyAsync(() -> {
            List<HeUserProductionAmountRefundLogEntity> heUserProductionAmountRefundLogEntities = productionAmountRefundRepository.queryAllRecordByOrderIds(orderIdList);
            return heUserProductionAmountRefundLogEntities.stream().collect(Collectors.groupingBy(HeUserProductionAmountRefundLogEntity::getOrderId));
        });

        // 等待所有任务完成
        CompletableFuture.allOf(tabClientMap, storeMapFuture, orderTagMapFuture, staffIdMapFuture, warZoneMapFuture, orderIdIncomeMapFuture, orderIdProofRecordMapFuture, orderGoodsEntityFuture).join();

        // 获取每个结果
        try {
            Map<Integer, TabClientEntity> abClientByIdMap = tabClientMap.get();
            Map<Integer, CfgStoreEntity> storeByIdMap = storeMapFuture.get();
            Map<Integer, String> orderTagMap = orderTagMapFuture.get();
            Map<Integer, String> staffIdMap = staffIdMapFuture.get();
            Map<String, String> warZoneMap = warZoneMapFuture.get();
            Map<Integer, List<HeIncomeRecordEntity>> orderIdIncomeMap = orderIdIncomeMapFuture.get();
//            Map<Integer, List<HeIncomeProofRecordEntity>> tabClientByIdList = orderIdProofRecordMapFuture.get();
            Map<Long, List<OrderReductionEntity>> orderReductionMap = orderReductionRecordMapFuture.get();
            Map<Integer, List<HeOrderGoodsEntity>> orderGoodsMap = orderGoodsEntityFuture.get();
            Map<Integer, List<HeOrderRefundEntity>> heOrderRefundMap = orderRefundFuture.get();
            Map<Integer, List<HeUserProductionAmountRefundLogEntity>> heUserProductionAmountRefundMap = heUserProductionAmountRefundLogFuture.get();

            List<String> orderSnList = pageList.getRecords().stream().map(HeOrderEntity::getOrderSn).collect(Collectors.toList());
            List<RoomCheckInDateVO> roomCheckInDateVOS = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(orderSnList)) {
                ListCheckInDateReq listCheckInDateReq = new ListCheckInDateReq();
                listCheckInDateReq.setOrderNos(orderSnList);
                roomCheckInDateVOS.addAll(roomExternalQuery.listCheckInDate(listCheckInDateReq).stream().filter(r -> Arrays.asList(2, 3).contains(r.getCheckInStatus())).collect(Collectors.toList()));
            }

            return PageUtils.convert2PageDTO(pageList, temp -> {
                HeOrderVO orderVO = orderConvert.entity2OrderVO(temp);
                HeOrderVO orderVO1 = heOrderModel2Req(orderVO, temp, abClientByIdMap, storeByIdMap, orderTagMap, staffIdMap, warZoneMap, orderGoodsMap);
                setCallPayable(orderVO1, orderReductionMap);
                setAmountbyIncome(orderVO1, temp, orderIdIncomeMap);
                orderVO.setRemainingAmount(AmountChangeUtil.changeF2Y(temp.leftPayAmount()));
                setRefundAmount(orderVO1, temp, heOrderRefundMap, heUserProductionAmountRefundMap);
                Optional<RoomCheckInDateVO> first = roomCheckInDateVOS.stream().filter(r -> r.getOrderNo().equals(orderVO1.getOrderSn())).findFirst();
                if (first.isPresent()) {
                    orderVO1.setCheckInDate(DateUtil.formatDate(first.get().getCheckInDate()));
                }
                orderVO1.setDiscountAmount((orderVO1.getOrderAmount().subtract(orderVO1.getCalPayableAmount())).divide(BigDecimal.ONE, 2, BigDecimal.ROUND_UP));
                return orderVO1;
            });
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }

    }

    private void setRefundAmount(HeOrderVO orderVO1, HeOrderEntity order, Map<Integer, List<HeOrderRefundEntity>> heOrderRefundMap, Map<Integer, List<HeUserProductionAmountRefundLogEntity>> heUserProductionAmountRefundMap) {
        List<HeOrderRefundEntity> heOrderRefundList = heOrderRefundMap.get(orderVO1.getOrderId());
        List<HeUserProductionAmountRefundLogEntity> heUserProductionAmountRefundList = heUserProductionAmountRefundMap.get(orderVO1.getOrderId());
        Integer totalRefreshPayAmount = 0;
        Integer totalOnlyRefundAmount = 0;
        if (CollectionUtils.isEmpty(heOrderRefundList) && CollectionUtils.isEmpty(heUserProductionAmountRefundList)){
            orderVO1.setTotalRefreshPayAmount(AmountChangeUtil.f2YScale2(totalRefreshPayAmount));
            orderVO1.setTotalOnlyRefundAmount(orderVO1.getTotalRefreshPayAmount());
        } else {
            if (order.isNewOrder()){
                if(CollectionUtils.isEmpty(heOrderRefundList)){
                    orderVO1.setTotalRefreshPayAmount(AmountChangeUtil.f2YScale2(totalRefreshPayAmount));
                    orderVO1.setTotalOnlyRefundAmount(AmountChangeUtil.f2YScale2(totalOnlyRefundAmount));
                    return;
                }
                List<HeOrderRefundEntity> parentRefundList = heOrderRefundList.stream().filter(item -> StringUtils.isEmpty(item.getParentRefundOrderSn()) && Objects.nonNull(item.getType()) && item.getType() != 2).collect(Collectors.toList());
                totalRefreshPayAmount = parentRefundList.stream().map(parent -> {
                    List<HeOrderRefundEntity> child = heOrderRefundList.stream().filter(item -> OrderRefundNatureEnum.TEMP_REFUND.code().equals(item.getRefundNature()) && StringUtils.isNotEmpty(item.getParentRefundOrderSn()) && RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode().equals(item.getStatus()) && item.getParentRefundOrderSn().equals(parent.getRefundOrderSn())).collect(Collectors.toList());
                    return child.stream().mapToInt(HeOrderRefundEntity::getApplyAmount).sum();
                }).reduce(Integer::sum).orElse(0);
                totalOnlyRefundAmount = parentRefundList.stream().map(parent -> {
                    List<HeOrderRefundEntity> child = heOrderRefundList.stream().filter(item -> OrderRefundNatureEnum.ONLY_REFUND.code().equals(item.getRefundNature()) && StringUtils.isNotEmpty(item.getParentRefundOrderSn()) && RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode().equals(item.getStatus()) && item.getParentRefundOrderSn().equals(parent.getRefundOrderSn())).collect(Collectors.toList());
                    return child.stream().mapToInt(HeOrderRefundEntity::getApplyAmount).sum();
                }).reduce(Integer::sum).orElse(0);

            }
            orderVO1.setTotalRefreshPayAmount(AmountChangeUtil.f2YScale2(totalRefreshPayAmount));
            orderVO1.setTotalOnlyRefundAmount(AmountChangeUtil.f2YScale2(totalOnlyRefundAmount));
        }
    }



    private void setCallPayable(HeOrderVO orderVO1, Map<Long, List<OrderReductionEntity>> orderReductionMap) {
        List<OrderReductionEntity> orderReductionEntities = orderReductionMap.get(orderVO1.getOrderId().longValue());
        if (CollectionUtils.isEmpty(orderReductionEntities)) {
            orderVO1.setCalPayable(AmountChangeUtil.f2YScale2(orderVO1.getPayAmount()));
            orderVO1.setCalPayableIntAmount(orderVO1.getPayAmount());
            orderVO1.setTotalReductionAmount(AmountChangeUtil.f2YScale2(BigDecimal.ZERO.intValue()));
            return;
        }
        Long totalReductionAmount = orderReductionEntities.stream().map(OrderReductionEntity::getDecreaseAmount).reduce(Long::sum).orElse(0L);
        int amount = orderVO1.getPayAmount() - totalReductionAmount.intValue();
        orderVO1.setTotalReductionAmount(AmountChangeUtil.f2YScale2(totalReductionAmount));
        orderVO1.setCalPayableIntAmount(amount);
        orderVO1.setCalPayable(AmountChangeUtil.f2YScale2(amount));
    }

    @Override
    public List<OrderExport> orderExport(OrderPageReq req) {

        req.setPageSize(5000);
        PageDTO<HeOrderVO> page = this.page(req);
        Assert.isTrue(page.getTotal() <= req.getPageSize(), "订单数据超过5000条，请缩小查询范围");
        List<OrderExport> exportList = Lists.newArrayList();
        List<HeOrderVO> orderVOList = page.getList();

        List<Integer> storeIdList = new ArrayList<>();
        List<CfgStoreEntity> cfgStoreEntities = cfgStoreRepository.queryAllStore();
        log.info("全部门店列表:{}", JsonUtil.write(cfgStoreEntities));
        List<CfgStoreEntity> allTestStore = cfgStoreEntities.stream().filter(c -> ObjectUtil.isNotEmpty(c.getWarzone()) && c.getWarzone() == 11).collect(Collectors.toList());
        log.info("测试门店列表:{}", JsonUtil.write(allTestStore));
        if (CollectionUtils.isNotEmpty(allTestStore)) {
            storeIdList.addAll(allTestStore.stream().map(CfgStoreEntity::getStoreId).collect(Collectors.toList()));
        }
        //导出过滤测试门店
        orderVOList = orderVOList.stream().filter(o -> !storeIdList.contains(o.getStoreId())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(orderVOList)) {
            return exportList;
        }
        List<Integer> orderIdList = orderVOList.stream().map(HeOrderVO::getOrderId).collect(Collectors.toList());
        List<HeOrderGoodsEntity> orderGoodsList = orderGoodsRepository.getByOrderIdList(orderIdList);
        Map<Integer, List<HeOrderGoodsEntity>> orderGoodsMap = orderGoodsList.stream().collect(Collectors.groupingBy(HeOrderGoodsEntity::getOrderId));

        List<GoodsEntity> goodsEntities = goodsRepository.queryByIdForDelete(orderGoodsList.stream().map(HeOrderGoodsEntity::getGoodsId).collect(Collectors.toList()));
        log.info("查出来的商品数据:{}", com.stbella.order.common.utils.JsonUtil.write(goodsEntities));
        Map<Integer, GoodsEntity> goodsEntityMap = goodsEntities.stream().collect(Collectors.toMap(GoodsEntity::getId, a -> a));

        List<HeOrderUserSnapshotEntity> heOrderUserSnapshotList = orderUserSnapshotRepository.queryByOrderIdList(orderIdList);
        Map<Integer, HeOrderUserSnapshotEntity> HeOrderUserSnapshotMap = heOrderUserSnapshotList.stream().collect(Collectors.toMap(HeOrderUserSnapshotEntity::getOrderId, entity -> entity, (v1, v2) -> v1));
        final Integer[] i = {1};
        Map<Integer, String> categoryNameMap = new HashMap<>();
        Set<Long> backendCategoryIdList = orderGoodsList.stream().map(HeOrderGoodsEntity::getBackCategoryId).collect(Collectors.toSet());
        log.info("订单导出-查询类目名称，数量={}", backendCategoryIdList.size());
        for (Long cateGoryId : backendCategoryIdList) {
            if (Objects.isNull(cateGoryId)) {
                continue;
            }
            String allBackName = storeManager.getAllBackName(cateGoryId.intValue());
            categoryNameMap.put(cateGoryId.intValue(), "无类目".equals(allBackName) ? StringUtils.EMPTY : allBackName);
        }

        //TODO 后面有产康金支付时，这里计算逻辑要修改 by JJ
        Map<Long, QueryOrderRefundGoodsInfoRes> refundGoodsMap = orderRefundDomainService.queryOrderGoodsInfoMapByOrderIdList(orderIdList, orderGoodsList);

        for (HeOrderVO order : orderVOList) {
            List<HeOrderGoodsEntity> heOrderGoodsList = orderGoodsMap.get(order.getOrderId());
            OrderExport orderExport = getOrderExport(order, HeOrderUserSnapshotMap, i[0]);
            if (Objects.isNull(heOrderGoodsList)) {
                exportList.add(orderExport);
                continue;
            }

            //计算商品应付金额（按公允价格分摊），实收金额
            // 统计所有商品的  allocationOriginPrice * goodsNum 之和

            //组合
            heOrderGoodsList.stream().filter(a -> CombineTypeEnum.COMBINE.code().intValue() == a.getType()).forEach(a -> {
                OrderExport export = BeanMapper.map(orderExport, OrderExport.class);
                fillOrderExport(export, a, categoryNameMap);
                export.setGoodsPay("");
                export.setGoodsPaid("");

                Integer goodsPriceOrignFen = a.getGoodsPriceOrgin();
                Integer goodsNum = a.getGoodsNum();
                goodsNum = ObjectUtil.isEmpty(goodsNum) ? 1 : goodsNum;
                BigDecimal goodsPriceOrign = AmountChangeUtil.f2YScale2(goodsPriceOrignFen * goodsNum);
                export.setGoodsPriceOrgin(Objects.isNull(goodsPriceOrign) ? StringUtils.EMPTY : goodsPriceOrign.toString());


                exportList.add(export);
            });

            //单项目&子项
            List<HeOrderGoodsEntity> singleAndSumList = heOrderGoodsList.stream().filter(a -> CombineTypeEnum.COMBINE.code().intValue() != a.getType()).collect(Collectors.toList());
            //价格升序
            singleAndSumList.sort(Comparator.comparing(HeOrderGoodsEntity::getGoodsPriceOrgin));
            int index = 0;
            for (HeOrderGoodsEntity orderGoods : singleAndSumList) {
                OrderExport export = BeanMapper.map(orderExport, OrderExport.class);
                fillOrderExport(export, orderGoods, categoryNameMap);
                QueryOrderRefundGoodsInfoRes goodsRefund = refundGoodsMap.get(orderGoods.getId().longValue());
                BigDecimal paid = goodsRefund.getPaidAmount().subtract(goodsRefund.getAlreadyRefundAmount());
                export.setGoodsPaid(paid.toString());

                Integer totalAllocationOriginPrice = orderGoods.getTotalAllocationOriginPrice();
                totalAllocationOriginPrice = null == totalAllocationOriginPrice ? 0 : totalAllocationOriginPrice;
                export.setGoodsPay(AmountChangeUtil.changeF2Y(totalAllocationOriginPrice).toString());

                if (orderGoods.getType().equals(CombineTypeEnum.COMBINE_SUB.code().intValue())) {
                    //组合子商品不显示原价，组合的原价统计是按照组合主商品来的
                    export.setGoodsPriceOrgin("");
                }

                exportList.add(export);
            }

            i[0]++;
        }
        return exportList;
    }


    @Override
    public Result<Integer> queryOrderEffective(String orderNo, Integer basicId) {
        HeOrderEntity orderInfo = orderRepository.getByOrderSn(orderNo);
        if (Objects.isNull(orderInfo)) {
            return Result.success(YesOrNoEnum.NO.getCode());
        }
        if (!Objects.equals(basicId, orderInfo.getBasicUid())) {
            return Result.success(YesOrNoEnum.NO.getCode());
        }
        if (Objects.equals(orderInfo.getRefundStatus(), OrderRefundStatusEnum.FULL_REFUND.getCode()) || Objects.equals(orderInfo.getOrderStatus(), OrderStatusV2Enum.CLOSE.getCode())) {
            return Result.success(YesOrNoEnum.NO.getCode());
        }
        return Result.success(YesOrNoEnum.YES.getCode());
    }

    @Override
    public Result<String> execOrderExport(OrderPageReq req) {
        try {
            exportTaskThreadPool.submit(() -> processTask(req));
            return Result.failed("X10001", "导出请求提交成功，正在处理中，处理完成之后我们会通过企业微信通知您，请耐心等待。");
        } catch (Exception e) {
            log.error("导出订单发生异常msg:{}", e.getMessage());
            return Result.failed(e.getMessage());
        }
    }

    /**
     * 获取订单客户名称
     */
    @Override
    public String queryOrderClientName(String orderNo) {
        List<HeOrderEntity> byOrderSnList = orderRepository.getByOrderSnList(Collections.singletonList(orderNo));
        if (CollectionUtil.isEmpty(byOrderSnList)) {
            return "unknown";
        }
        HeOrderEntity heOrderEntity = byOrderSnList.get(0);
        try {
            TabClientEntity tabClientById = clientRepository.getTabClientById(heOrderEntity.getClientUid());
            return Objects.isNull(tabClientById) ? "unknown" : tabClientById.getName();
        } catch (Exception e) {
            log.error("getTabClientById error", e);
            return "unknown";
        }
    }

    @Override
    public String queryOrderRemark(String orderNo) {
        List<HeOrderEntity> byOrderSnList = orderRepository.getByOrderSnList(Collections.singletonList(orderNo));
        if (CollectionUtil.isEmpty(byOrderSnList)) {
            return "";
        }
        return byOrderSnList.get(0).getRemark();
    }

    @Override
    public Integer queryOrderBasicUid(String orderNo) {
        List<HeOrderEntity> byOrderSnList = orderRepository.getByOrderSnList(Collections.singletonList(orderNo));
        return CollectionUtil.isEmpty(byOrderSnList) ? null : byOrderSnList.get(0).getBasicUid();
    }

    @Override
    public Map<String, Integer> queryOrderBasicUid(List<String> orderNos) {
        Map<String, Integer> re = Maps.newHashMap();
        if (CollectionUtil.isEmpty(orderNos)) {
            return re;
        }
        List<HeOrderEntity> byOrderSnList = orderRepository.getByOrderSnList(orderNos);
        if (CollectionUtil.isEmpty(byOrderSnList)) {
            return re;
        }
        byOrderSnList.forEach(h -> re.put(h.getOrderSn(), h.getBasicUid()));
        return re;
    }

    @Override
    public PageVO<OrderGoodsInfoAndUserInfoVO> queryOrderGoodsInfoByPhoneOrName(QueryOrderGoodsInfoByPhoneOrNameReq req) {

        if (StringUtils.isNotEmpty(req.getKeyword()) && ObjectUtil.isEmpty(req.getStoreId())) {
            return new PageVO<>();
        }

        List<Integer> clientIdList = new ArrayList<>();

        if (StringUtils.isNotEmpty(req.getKeyword()) && StringUtils.isEmpty(req.getOrderSn())) {
            List<TabClientEntity> tabClientEntities = clientRepository.getClientInfoByKeyWord(req.getKeyword(), req.getStoreId());
            if (CollectionUtils.isNotEmpty(tabClientEntities)) {
                clientIdList.addAll(tabClientEntities.stream().map(TabClientEntity::getId).collect(Collectors.toList()));
            } else {
                return new PageVO<>();
            }
        }

        log.info("client:{}", JsonUtil.write(clientIdList));

        List<Integer> orderIdList = orderRepository.queryByClientIdsAndAssetType(clientIdList, Arrays.asList(2, 7), req.getStoreId(), req.getOrderSn());

        log.info("orderIdList:{}", JsonUtil.write(orderIdList));

        if (CollectionUtils.isEmpty(orderIdList)) {
            return new PageVO();
        }

        Page heOrderEntityPage = orderRepository.pageByOrderIdListAndPercentPay(
                req.getPageNum(),
                req.getPageSize(),
                orderIdList,
                StringUtils.isEmpty(req.getOrderSn())
        );

        List<HeOrderEntity> records = heOrderEntityPage.getRecords();

        if (CollectionUtils.isEmpty(records)) {
            return new PageVO();
        }

        List<HeOrderGoodsEntity> orderGoodsByOrderIdList = orderGoodsRepository.getByOrderIdList(orderIdList);
        if (CollectionUtils.isEmpty(orderGoodsByOrderIdList)) {
            return new PageVO();
        }

        List<Integer> clientId = records.stream().map(HeOrderEntity::getClientUid).collect(Collectors.toList());
        List<TabClientEntity> tabClientByIdList = clientRepository.getTabClientByIdList(clientId);

        List<OrderGoodsInfoAndUserInfoVO> orderGoodsInfoAndUserInfoVOList = new ArrayList<>();
        records.forEach(order -> {
            List<HeOrderGoodsEntity> tmpOrderGoodsList = orderGoodsByOrderIdList.stream()
                    .filter(o -> Objects.equals(o.getOrderId(), order.getOrderId()))
                    .collect(Collectors.toList());

            OrderGoodsInfoAndUserInfoVO orderGoodsInfoAndUserInfoVO = new OrderGoodsInfoAndUserInfoVO();
            orderGoodsInfoAndUserInfoVO.setOrderId(order.getOrderId());
            orderGoodsInfoAndUserInfoVO.setOrderSn(order.getOrderSn());
            orderGoodsInfoAndUserInfoVO.setOrderTypeStr(OmniOrderTypeEnum.getValueByCode(order.getOrderType()));
            orderGoodsInfoAndUserInfoVO.setExtraInfo(order.getExtraInfo());
            Optional<TabClientEntity> first = tabClientByIdList.stream().filter(t -> t.getId().equals(order.getClientUid())).findFirst();
            if (first.isPresent()) {
                orderGoodsInfoAndUserInfoVO.setPhone(first.get().getPhone());
                orderGoodsInfoAndUserInfoVO.setName(first.get().getName());
            }

            List<Integer> goodsCategoryIdList = tmpOrderGoodsList.stream()
                    .filter(item -> Objects.nonNull(item.getBackCategoryId()))
                    .map(a -> a.getBackCategoryId().intValue())
                    .collect(Collectors.toList());

            List<GoodsCategoryBackEntity> goodsCategoryList = goodsCategoryBackRepository.queryCategoryListByIds(goodsCategoryIdList);
            Map<Long, String> backCategoryMap = CollectionUtils.isEmpty(goodsCategoryList) ? Maps.newHashMap() : goodsCategoryList.stream().collect(Collectors.toMap(a -> Long.valueOf(a.getId()), GoodsCategoryBackEntity::getName));
            List<GoodsNewVO> resultList = new ArrayList<>();
            for (HeOrderGoodsEntity orderGoods : tmpOrderGoodsList) {

                if (ObjectUtil.isEmpty(orderGoods.getGift())) {
                    orderGoods.setGift(0);
                }

                GoodsNewVO goodsNewVO = BeanMapper.map(orderGoods, GoodsNewVO.class);
                goodsNewVO.setGoodsPriceOrgin(order.getCurrency() + AmountChangeUtil.f2YDown(orderGoods.getGoodsPriceOrgin()));
                goodsNewVO.setGiftStr(orderGoods.getGift() == 0 ? "购买" : "礼赠");

                PromotionInfo promotionInfo = orderGoods.getPromotionInfo();
                String giftTypeStr = orderGoods.getGift() == 0 ? "" : Objects.isNull(promotionInfo) ? "定制礼赠" : ("活动礼赠-" + promotionInfo.getPromotionName());
                goodsNewVO.setGiftTypeStr(giftTypeStr);

                goodsNewVO.setCurrency(order.getCurrency());
                goodsNewVO.setCategoryBack(orderGoods.getBackCategoryId());
                goodsNewVO.setCategoryBackStr(Objects.isNull(orderGoods.getBackCategoryId()) ? StringUtils.EMPTY : backCategoryMap.get(orderGoods.getBackCategoryId()));
                resultList.add(goodsNewVO);
            }
            orderGoodsInfoAndUserInfoVO.setGoodsNewVOList(resultList);
            orderGoodsInfoAndUserInfoVOList.add(orderGoodsInfoAndUserInfoVO);
        });
        heOrderEntityPage.setRecords(orderGoodsInfoAndUserInfoVOList);

        return new PageVO<>(heOrderEntityPage);
    }

    @Override
    public Result<List<OrderPerformanceVO>> performanceSearchOneOrder(OrderPerformanceReq req, Integer type) {
        if (ObjectUtil.isNotEmpty(req.getDateType()) &&
                req.getDateType() == 3 &&
                ObjectUtil.isNotEmpty(req.getDateStart()) &&
                ObjectUtil.isNotEmpty(req.getDateEnd()) &&
                (req.getDateEnd() - req.getDateStart() > 60 * 60 * 24 * 90)) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "根据入住日期查询最大范围为3个月，导出无限制。");
        }

        if (CollectionUtils.isNotEmpty(req.getStoreBrandList())) {
            List<Integer> storeIdByStoreBrandList = getStoreIdByStoreBrand(req.getStoreBrandList());
            req.setStoreIdListByBrand(CollectionUtils.isEmpty(storeIdByStoreBrandList) ? Collections.singletonList(-100) : storeIdByStoreBrandList);
        }

        if (CollectionUtils.isNotEmpty(req.getWarZoneList())) {
            StoreQueryCondition storeQueryCondition = StoreQueryCondition.builder().warZones(req.getWarZoneList()).build();
            List<CfgStoreEntity> cfgStoreEntitieList = storeRepository.queryStoreBaseByCondition(storeQueryCondition);
            req.setStoreIdList(CollectionUtils.isEmpty(cfgStoreEntitieList) ? Collections.singletonList(-100) : cfgStoreEntitieList.stream().map(CfgStoreEntity::getStoreId).collect(Collectors.toList()));
        }

        if (StringUtils.isNotEmpty(req.getClientName()) || StringUtils.isNotEmpty(req.getPhone()) || CollectionUtils.isNotEmpty(req.getCustomerSourceList())) {
            List<TabClientEntity> tabClientEntities = clientRepository.getClientInfoByPhone(req.getClientName(), req.getPhone(), req.getCustomerSourceList());
            req.setClientIds(CollectionUtils.isEmpty(tabClientEntities) ? Collections.singletonList(-100) : tabClientEntities.stream().map(TabClientEntity::getId).collect(Collectors.toList()));
        }

        Page<HeOrderEntity> pageList = orderRepository.queryList(req);

        List<HeOrderEntity> heOrderEntityList = pageList.getRecords();

        List<HeOrderEntity> finalHeOrderEntityList = heOrderEntityList;
        CompletableFuture<Map<Integer, TabClientEntity>> tabClientMap = CompletableFuture.supplyAsync(() -> {
            List<Integer> clientUidList = finalHeOrderEntityList.stream().map(HeOrderEntity::getClientUid).collect(Collectors.toList());
            List<TabClientEntity> tabClientByIdList = clientRepository.getTabClientByIdList(clientUidList);
            return tabClientByIdList.stream().collect(Collectors.toMap(TabClientEntity::getId, entity -> entity, (v1, v2) -> v1));
        });

        CompletableFuture<Map<Integer, CfgStoreEntity>> storeMapFuture = CompletableFuture.supplyAsync(() -> {
            List<Integer> storeIdList = finalHeOrderEntityList.stream().map(HeOrderEntity::getStoreId).collect(Collectors.toList());
            List<CfgStoreEntity> storeByIdList = storeRepository.queryCfgStoreByIdList(storeIdList);
            return storeByIdList.stream().collect(Collectors.toMap(CfgStoreEntity::getStoreId, entity -> entity, (v1, v2) -> v1));
        });

        CompletableFuture<Map<Integer, String>> orderTagMapFuture = CompletableFuture.supplyAsync(() -> {
            List<HeOrderTagsEntity> allOrderTags = tagsRepository.getAllOrderTags();
            return allOrderTags.stream().collect(Collectors.toMap(HeOrderTagsEntity::getId, HeOrderTagsEntity::getTagName));
        });

        CompletableFuture<Map<Integer, String>> staffIdMapFuture = CompletableFuture.supplyAsync(() -> {
            List<Integer> staffIdList = pageList.getRecords().stream().flatMap(e -> Stream.of(e.getStaffId(), e.getUpdateStaffId(), ObjectUtil.isEmpty(e.getCreateBy()) ? null : e.getCreateBy().intValue())).collect(Collectors.toList());
            List<UserEntity> userEntityList = userRepository.queryUserByIdList(staffIdList);
            return userEntityList.stream().collect(Collectors.toMap(UserEntity::getId, UserEntity::getName));
        });

        CompletableFuture<Map<String, String>> warZoneMapFuture = CompletableFuture.supplyAsync(() -> {
            List<SelectRespVO> attribute = storeClient.getAttribute(dynamicConfig.getWarZone());
            return attribute.stream().collect(Collectors.toMap(SelectRespVO::getValue, SelectRespVO::getLabel, (v1, v2) -> v1));
        });

        CompletableFuture<Map<Integer, List<HeOrderRefundEntity>>> orderIdRefundMapFuture = CompletableFuture.supplyAsync(() -> {
            List<HeOrderRefundEntity> refundByOrderIdList = orderRefundRepository.getRefundByOrderIdList(heOrderEntityList.stream().map(HeOrderEntity::getOrderId).collect(Collectors.toList()));
            return refundByOrderIdList.stream().collect(Collectors.groupingBy(HeOrderRefundEntity::getOrderId));
        });


        // 等待所有任务完成
        CompletableFuture.allOf(tabClientMap, storeMapFuture, orderTagMapFuture, staffIdMapFuture, warZoneMapFuture, orderIdRefundMapFuture).join();


        List<OrderPerformanceVO> result = new ArrayList<>();


        try {
            Map<Integer, TabClientEntity> abClientByIdMap = tabClientMap.get();
            Map<Integer, CfgStoreEntity> storeByIdMap = storeMapFuture.get();
            Map<Integer, String> orderTagMap = orderTagMapFuture.get();
            Map<Integer, String> staffIdMap = staffIdMapFuture.get();
            Map<String, String> warZoneMap = warZoneMapFuture.get();
            Map<Integer, List<HeOrderRefundEntity>> refundListMapFuture = orderIdRefundMapFuture.get();

            List<OrderRealAmountVO> orderRealAmountByOrderList = orderQueryService.getOrderRealAmountByOrderList(heOrderEntityList.stream().map(HeOrderEntity::getOrderId).collect(Collectors.toList()));

            for (HeOrderEntity orderEntity : heOrderEntityList) {
                OrderPerformanceVO orderPerformanceVO = new OrderPerformanceVO();
                CfgStoreEntity cfgStoreEntity = storeByIdMap.get(orderEntity.getStoreId());
                if (ObjectUtil.isNotEmpty(cfgStoreEntity)) {
                    orderPerformanceVO.setStoreName(cfgStoreEntity.getStoreName());
                    orderPerformanceVO.setTypeName(this.getTypeName(cfgStoreEntity.getType(), cfgStoreEntity.getChildType()));
                    orderPerformanceVO.setWarZoneDesc(warZoneMap.get(String.valueOf(cfgStoreEntity.getWarzone())));
                    orderPerformanceVO.setWarZone(cfgStoreEntity.getWarzone());
                }

                TabClientEntity tabClientEntity = abClientByIdMap.get(orderEntity.getClientUid());
                if (ObjectUtil.isNotEmpty(tabClientEntity)) {
                    orderPerformanceVO.setCustomerName(tabClientEntity.getName());
                    orderPerformanceVO.setCustomerMobile(tabClientEntity.getPhone());
                }
                orderPerformanceVO.setOrderId(orderEntity.getOrderId());
                orderPerformanceVO.setOrderType(orderEntity.getOrderType());
                orderPerformanceVO.setStoreId(orderEntity.getStoreId());

                orderPerformanceVO.setStaffId(orderEntity.getStaffId());
                orderPerformanceVO.setOrderSn(orderEntity.getOrderSn());
                orderPerformanceVO.setBasicId(orderEntity.getBasicUid().toString());
                orderPerformanceVO.setClientUid(orderEntity.getClientUid().toString());

                orderPerformanceVO.setOrderTypeStr(OmniOrderTypeEnum.getValueByCode(orderEntity.getOrderType()));
                orderPerformanceVO.setStaffName(staffIdMap.get(orderEntity.getStaffId()));
                Integer percentFirstTime = orderEntity.getPercentFirstTime();
                if (ObjectUtil.isNotEmpty(percentFirstTime) && percentFirstTime > 0) {
                    orderPerformanceVO.setPerformanceEffectiveDate(DateUtil.formatDateTime(new Date(percentFirstTime * 1000L)));
                }

                Long createdAt = orderEntity.getCreatedAt();
                if (ObjectUtil.isNotEmpty(createdAt) && createdAt > 0) {
                    orderPerformanceVO.setCreatedAt(DateUtil.formatDateTime(new Date(createdAt * 1000L)));
                }
                orderPerformanceVO.setOrderAmount(com.stbella.core.utils.RMBUtils.changeF2Y(orderEntity.getOrderAmount().longValue()));

                Integer productionAmountPay = orderEntity.getProductionAmountPay();
                productionAmountPay = null == productionAmountPay ? 0 : productionAmountPay;
                orderPerformanceVO.setPayAmount(com.stbella.core.utils.RMBUtils.changeF2Y(orderEntity.calPayable() - productionAmountPay - 0L));


                orderPerformanceVO.setRealAmount(RMBUtils.changeF2Y(0L));
                Optional<OrderRealAmountVO> first = orderRealAmountByOrderList.stream().filter(o -> o.getOrderId().equals(orderEntity.getOrderId())).findFirst();
                if (first.isPresent()) {
                    orderPerformanceVO.setRealAmount(RMBUtils.changeF2Y(first.get().getRealAmount()));
                }
                result.add(orderPerformanceVO);
            }
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "订单查询失败！");
        }

        //排除非大陆的所有门店
        Map<String, String> factMap = new HashMap<>();
        factMap.put("configCode", "non_mainland_stores");
        ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req("systemConfig", factMap);
        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
        Set<String> storeIds = Arrays.stream(hitRuleVo.getSimpleRuleValue().split(",")).collect(Collectors.toSet());
        //排除非大陆门店

        Integer totalSize = result.size();
        result = result.stream().filter(l -> !storeIds.contains(l.getStoreId().toString())).collect(Collectors.toList());
        if (totalSize > result.size() && result.size() == 0) {
            if (type == 0) {
                //单独搜索需要报这个，否则返回空
                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "非大陆地区的门店不支持修改订单的门店业绩");
            } else {
                return Result.success(new ArrayList());
            }
        }
        return Result.success(result);
    }

    @Override
    public Result<PageDTO<OrderPerformanceVO>> performanceSearchUpdateOrder(OrderPerformanceReq req) {
        Integer pageNum = req.getPageNum();
        Integer pageSize = req.getPageSize();

        List<HeOrderPerformanceOperationEntity> heOrderPerformanceOperationEntities = orderPerformanceOperationRepository.listAll();
        List<Integer> orderIdList = heOrderPerformanceOperationEntities.stream().map(HeOrderPerformanceOperationEntity::getOrderId).collect(Collectors.toList());
        req.setOrderIdList(orderIdList);

        if (CollectionUtils.isEmpty(orderIdList)) {
            return Result.success(new PageDTO());
        }

        req.setPageNum(1);
        req.setPageSize(Integer.MAX_VALUE);
        //搜索全部
        Result<List<OrderPerformanceVO>> listResult = performanceSearchOneOrder(req, 1);
        List<OrderPerformanceVO> data = listResult.getData();

        List<Integer> operationOrderIdList = heOrderPerformanceOperationEntities.stream().map(HeOrderPerformanceOperationEntity::getOrderId).collect(Collectors.toList());

        data = data.stream().filter(d -> operationOrderIdList.contains(d.getOrderId())).collect(Collectors.toList());

        List<CfgStoreEntity> cfgStoreEntities = storeRepository.queryAllStore();

        data.forEach(d -> {
            Optional<HeOrderPerformanceOperationEntity> first = heOrderPerformanceOperationEntities.stream().filter(h -> h.getOrderId().equals(d.getOrderId())).findFirst();
            if (first.isPresent()) {
                HeOrderPerformanceOperationEntity orderPerformanceOperationEntity = first.get();

                StringBuffer stringBuffer = new StringBuffer();

                Integer sourceStoreId = orderPerformanceOperationEntity.getSourceStoreId();
                Integer targetStoreId = orderPerformanceOperationEntity.getTargetStoreId();


                Optional<CfgStoreEntity> sourceStore = cfgStoreEntities.stream().filter(c -> c.getStoreId().equals(sourceStoreId)).findFirst();

                if (sourceStore.isPresent()) {
                    stringBuffer.append(sourceStore.get().getStoreName()).append("-").append("￥").append(RMBUtils.changeF2Y(orderPerformanceOperationEntity.getOrderRetainedPerformance().longValue())).append("/").append(orderPerformanceOperationEntity.getPercentage()).append("%").append("\n");
                }
                Optional<CfgStoreEntity> targetStore = cfgStoreEntities.stream().filter(c -> c.getStoreId().equals(targetStoreId)).findFirst();
                if (targetStore.isPresent()) {
                    stringBuffer.append(targetStore.get().getStoreName()).append("-").append("￥").append(RMBUtils.changeF2Y(orderPerformanceOperationEntity.getTargetStoreAmount().longValue())).append("/").append(new BigDecimal(100).subtract(orderPerformanceOperationEntity.getPercentage())).append("%");
                }
                d.setOperationId(orderPerformanceOperationEntity.getId());
                d.setOperationPerformance(stringBuffer.toString());
                d.setOperationName(orderPerformanceOperationEntity.getModifyName());
                d.setOperationTime(DateUtil.formatDateTime(orderPerformanceOperationEntity.getGmtModified()));
                d.setOperationDate(orderPerformanceOperationEntity.getGmtModified().getTime());
            }
        });

        List<OrderPerformanceVO> sort = data.stream()
                .sorted(Comparator.comparing(OrderPerformanceVO::getOperationDate).reversed())
                .collect(Collectors.toList());

        List list = PageUtil.startPage(sort, pageNum, pageSize);
        PageDTO page = new PageDTO();
        page.setTotal(sort.size());
        page.setPageNo(pageNum);
        page.setPageSize(pageSize);
        page.setList(list);
        return Result.success(page);
    }

    @Override
    public Result<List<OrderPerformanceVO>> searchOneOrder(OrderPerformanceReq req) {
        if (StringUtils.isEmpty(req.getOrderNo())) {
            return Result.success();
        }
        return this.performanceSearchOneOrder(req, 0);
    }

    @SneakyThrows
    private void processTask(OrderPageReq req) {
        Assert.isTrue(Objects.nonNull(req.getOperator()), "操作人不能为空");
        List<OrderExport> exportList = this.orderExport(req);
        Workbook sheets = ExcelUtil.exportData(new OrderExport(), exportList);
        byte[] bytes = this.getByteArray(sheets).toByteArray();
        OssUploadResponse ossUploadResponse = OSSFactory.build().upload(bytes, String.format("/picp/%s/%s.xlsx", System.currentTimeMillis(), DateUtils.format(new Date(), DateUtils.YYYY_MM_DD)));
        log.info("订单导出处理完成，ossUploadResponse：{}", JSONUtil.toJsonStr(ossUploadResponse));
        String content = Objects.isNull(ossUploadResponse) || !ossUploadResponse.isSuccess() ? ossUploadResponse.getMsg() : "您好：" + req.getOperator().getOperatorName() + "，你操作的月子订单导出处理完成请点击链接下载文件：".concat("\n").concat(ossUploadResponse.getUrl());
        baseClient.sendMessage(req.getOperator().getOperatorPhone(), content, req.getOperator().getOperatorName(), WeWorkAppEnum.SERVICE_NOTIFY.getCode());
    }

    private OrderExport getOrderExport(HeOrderVO order, Map<Integer, HeOrderUserSnapshotEntity> HeOrderUserSnapshotMap, Integer id) {

        OrderExport orderExport = BeanMapper.map(order, OrderExport.class);
        orderExport.setId(id);
        orderExport.setPayableAmount(order.getCalPayable());
        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = HeOrderUserSnapshotMap.get(order.getOrderId());
        if (Objects.nonNull(heOrderUserSnapshotEntity)) {
            orderExport.setWantInTime(Objects.isNull(heOrderUserSnapshotEntity.getWantIn()) ? StringUtils.EMPTY : DateUtils.format(heOrderUserSnapshotEntity.getWantIn(), DateUtils.YYYY_MM_DD));
            orderExport.setBornNum(heOrderUserSnapshotEntity.getFetusNum());
            if (Objects.nonNull(heOrderUserSnapshotEntity.getPredictBornDate())){
                orderExport.setPredictBornDate(DateUtils.format(heOrderUserSnapshotEntity.getPredictBornDate(), DateUtils.YYYY_MM_DD));
                orderExport.setPredictExpatriateDate(orderExport.getPredictBornDate());
            }
        }
        return orderExport;
    }

    private void fillOrderExport(OrderExport orderExport, HeOrderGoodsEntity orderGoods, Map<Integer, String> categoryNameMap) {

        orderExport.setGoodsId(orderGoods.getSkuId());
        orderExport.setGoodsName(orderGoods.getGoodsName());
        orderExport.setSkuName(orderGoods.getSkuName());
        Long backCategoryId = orderGoods.getBackCategoryId();
        if (Objects.nonNull(backCategoryId)) {
            orderExport.setBackCategoryName(categoryNameMap.get(backCategoryId.intValue()));
        }


        Integer goodsPriceOrginFen = orderGoods.getGoodsPriceOrgin();
        Integer goodsNum = orderGoods.getGoodsNum();
        goodsNum = ObjectUtil.isEmpty(goodsNum) ? 1 : goodsNum;
        BigDecimal goodsPriceOrgin = AmountChangeUtil.f2YScale2(goodsPriceOrginFen * goodsNum);
        orderExport.setGoodsPriceOrgin(Objects.isNull(goodsPriceOrgin) ? StringUtils.EMPTY : goodsPriceOrgin.toString());
        orderExport.setGoodsNum(orderGoods.getGoodsNum());
        PromotionInfo promotionInfo = orderGoods.getPromotionInfo();
        String goodsTypeStr = orderGoods.getGift() == 0 ? OrderProductionTypeEnum.BUY.desc() : "礼赠";
        String giftTypeStr = orderGoods.getGift() == 0 ? "" : Objects.isNull(promotionInfo) ? "定制礼赠" : ("活动礼赠-" + promotionInfo.getPromotionName());
        orderExport.setGiftTypeStr(giftTypeStr);
        orderExport.setGoodsTypeStr(goodsTypeStr);
        orderExport.setAssetTypeStr(GoodsTypeEnum.allDescFromCode(orderGoods.getGoodsType()));
        orderExport.setCombineTypeStr(CombineTypeEnum.fromCode(orderGoods.getType()));
        orderExport.setRoomName(orderGoods.getRoomName());
        orderExport.setPropertyName(getPropertyName(orderGoods.getDefinedProperty(), orderGoods.getId()));
        if (CombineTypeEnum.COMBINE.code().equals(orderGoods.getType())) {
            orderExport.setGoodsId(orderGoods.getGoodsId());
        }


    }

    private String getPropertyName(String definedProperty, Integer orderGoodsId) {

        try {
            if (StringUtils.isEmpty(definedProperty)) {
                return StringUtils.EMPTY;
            }
            JSONObject jsonObject = JSON.parseArray(definedProperty, JSONObject.class).stream().findFirst().orElse(null);
            if (Objects.isNull(jsonObject)) {
                return StringUtils.EMPTY;
            }
            JSONObject goodsPropertyDetailsVOS = JSON.parseArray(jsonObject.getString("goodsPropertyDetailsVOS"), JSONObject.class).stream().findFirst().orElse(null);
            if (Objects.isNull(goodsPropertyDetailsVOS)) {
                return StringUtils.EMPTY;
            }
            return goodsPropertyDetailsVOS.getString("label");
        } catch (Exception e) {
            log.error("获取订单对应的商品中的护理模式发生异常, orderGoodsId:{}", orderGoodsId, e);
            return StringUtils.EMPTY;
        }
    }

    private void fildOrderPageReq(OrderPageReq req) {

        if (CollectionUtils.isNotEmpty(req.getOrderTypeList()) && req.getOrderTypeList().contains(OmniOrderTypeEnum.MONTH_ORDER.code()+"")) {
            req.getOrderTypeList().addAll(Arrays.asList(OmniOrderTypeEnum.SMALL_MONTH_ORDER.code()+"", OmniOrderTypeEnum.OTHER_MONTH_ORDER.code()+"", OmniOrderTypeEnum.NURSE_OUTSIDE_ORDER.code()+""));
        }
        if (CollectionUtils.isNotEmpty(req.getStoreBrandList())) {
            List<Integer> storeIdByStoreBrandList = getStoreIdByStoreBrand(req.getStoreBrandList());
            req.setStoreIdListByBrand(CollectionUtils.isEmpty(storeIdByStoreBrandList) ? Collections.singletonList(-100) : storeIdByStoreBrandList);
        }
        if (CollectionUtils.isNotEmpty(req.getGoodsCategoryIdList())) {
            List<GoodsCategoryBackEntity> goodsCategoryList = goodsCategoryBackRepository.queryCategoryListByIds(req.getGoodsCategoryIdList());
            List<Integer> categoryIdList = goodsCategoryList.stream().map(GoodsCategoryBackEntity::getId).collect(Collectors.toList());
            List<HeOrderGoodsEntity> listByCategoryIds = orderGoodsRepository.getListByCategoryIds(categoryIdList, null, false);
            req.setOrderIdByCategory(CollectionUtils.isEmpty(listByCategoryIds) ? Collections.singletonList(-100) : listByCategoryIds.stream().map(HeOrderGoodsEntity::getOrderId).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(req.getWarZoneList())) {
            StoreQueryCondition storeQueryCondition = StoreQueryCondition.builder().warZones(req.getWarZoneList()).build();
            List<CfgStoreEntity> cfgStoreEntitieList = storeRepository.queryStoreBaseByCondition(storeQueryCondition);
            req.setStoreIdList(CollectionUtils.isEmpty(cfgStoreEntitieList) ? Collections.singletonList(-100) : cfgStoreEntitieList.stream().map(CfgStoreEntity::getStoreId).collect(Collectors.toList()));
        }

        if (StringUtils.isNotEmpty(req.getClientName()) || StringUtils.isNotEmpty(req.getPhone()) || CollectionUtils.isNotEmpty(req.getCustomerSourceList())) {
            List<TabClientEntity> tabClientEntities = clientRepository.getClientInfoByPhone(req.getClientName(), req.getPhone(), req.getCustomerSourceList());
            req.setClientIds(CollectionUtils.isEmpty(tabClientEntities) ? Collections.singletonList(-100) : tabClientEntities.stream().map(TabClientEntity::getId).collect(Collectors.toList()));
        }
        if (Objects.nonNull(req.getOrderStatus())) {
            List<Integer> list = Arrays.asList(0, 1, 2, 3, 4);
            Assert.isTrue(list.contains(req.getOrderStatus()), "订单状态不正确");
            if (req.getOrderStatus() == 0) {
                List<Integer> payStatusList = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(req.getPayStatusList())) {
                    payStatusList = req.getPayStatusList();
                }
                payStatusList.add(PayStatusV2Enum.WAIT_PAY.getCode(), PayStatusV2Enum.NO_PAY_OFF.getCode());
                req.setPayStatusList(payStatusList);
            }
            if (req.getOrderStatus() == 3) {
                req.setOrderStatusList(Arrays.asList(OrderStatusV2Enum.OUT_OF_STORE.getCode(), OrderStatusV2Enum.ADVANCE_OUT_OF_STORE.getCode()));
            } else {
                req.setOrderStatusList(Collections.singletonList(req.getOrderStatus()));
            }
        }
    }

    private List<Integer> getStoreIdByStoreBrand(List<Long> reqList) {

        List<Integer> cfgStoreIdList = Lists.newArrayList();
        List<BrandVO> brandListVOS = brandClient.queryBrandListBy(reqList);
        if (CollectionUtils.isEmpty(brandListVOS)) {
            return cfgStoreIdList;
        }
        List<CfgStoreEntity> cfgStoreEntities = storeRepository.queryStoreBaseByCondition(StoreQueryCondition.builder().build());
        if (CollectionUtils.isEmpty(cfgStoreEntities)) {
            return cfgStoreIdList;
        }
        Map<Long, Integer> brandMap = brandListVOS.stream().filter(item -> BrandTypeEnum.MAIN_BRAND.getCode().equals(item.getBrandType())).collect(Collectors.toMap(BrandVO::getId, BrandVO::getBrandVal));
        brandMap.forEach((key, value) -> {
            List<Integer> idList = cfgStoreEntities.stream().filter(store -> Objects.nonNull(store.getType()) && store.getType().equals(value)).map(CfgStoreEntity::getStoreId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(idList)) {
                cfgStoreIdList.addAll(idList);
            }
        });
        Map<Long, List<BrandVO>> subBrandMap = brandListVOS.stream().filter(item -> BrandTypeEnum.CHILD_BRAND.getCode().equals(item.getBrandType())).collect(Collectors.groupingBy(BrandVO::getParentId));
        List<Long> parentIdList = brandListVOS.stream().filter(item -> BrandTypeEnum.CHILD_BRAND.getCode().equals(item.getBrandType()) && Objects.nonNull(item.getParentId())).map(BrandVO::getParentId).collect(Collectors.toList());
        List<BrandVO> parentBrandListVOS = brandClient.queryBrandListBy(parentIdList);
        if (CollectionUtils.isEmpty(parentBrandListVOS)) {
            return cfgStoreIdList;
        }
        Map<Long, Integer> parentBrandMap = parentBrandListVOS.stream().filter(item -> BrandTypeEnum.MAIN_BRAND.getCode().equals(item.getBrandType())).collect(Collectors.toMap(BrandVO::getId, BrandVO::getBrandVal));
        for (Map.Entry<Long, List<BrandVO>> entry : subBrandMap.entrySet()) {
            Long key = entry.getKey();
            List<BrandVO> valueList = entry.getValue();
            Integer type = parentBrandMap.get(key);
            if (Objects.isNull(type)) {
                continue;
            }
            valueList.forEach(value -> {
                List<Integer> idList = cfgStoreEntities.stream().filter(store -> Objects.nonNull(store.getType()) && Objects.nonNull(store.getChildType()) && store.getType().equals(type) && store.getChildType().equals(value.getBrandVal())).map(CfgStoreEntity::getStoreId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(idList)) {
                    cfgStoreIdList.addAll(idList);
                }
            });
        }
        return cfgStoreIdList;
    }

    private HeOrderVO heOrderModel2Req(HeOrderVO result, HeOrderEntity req, Map<Integer, TabClientEntity> abClientByIdMap, Map<Integer, CfgStoreEntity> storeByIdMap, Map<Integer, String> orderTagMap, Map<Integer, String> staffIdMap, Map<String, String> warZoneMap, Map<Integer, List<HeOrderGoodsEntity>> orderGoodsMap) {

        setClientInfo(result, req, abClientByIdMap);
        setStoreInfo(result, req, storeByIdMap, warZoneMap);
        setExtraInfo(result, req);
        setGoodsInfo(result, req, orderGoodsMap);
        result.setOrderTagName(orderTagMap.get(result.getOrderTagId()));
        result.setStaffName(staffIdMap.get(result.getStaffId()));
        result.setCreateName(staffIdMap.get(result.getCreateBy().intValue()));
        result.setUpdateByName(staffIdMap.get(result.getUpdateStaffId()));
        return result;
    }

    private void setGoodsInfo(HeOrderVO result, HeOrderEntity req, Map<Integer, List<HeOrderGoodsEntity>> orderGoodsMap) {
        List<HeOrderGoodsEntity> heOrderGoodsEntities = orderGoodsMap.get(req.getOrderId());
        Integer buyGoodsPriceSum = 0;
        Integer buyGoodsPayPriceSum = 0;
        if (CollectionUtils.isNotEmpty(heOrderGoodsEntities)) {
            List<HeOrderGoodsEntity> collect = heOrderGoodsEntities.stream().filter(t -> ObjectUtil.isNotEmpty(t.getGift()) && t.getGift() == 0 && ObjectUtil.isNotEmpty(t.getType()) && (t.getType().equals(2) || t.getType().equals(3))).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(collect)) {
                for (HeOrderGoodsEntity heOrderGoodsEntity : collect) {
                    Integer goodsNum = heOrderGoodsEntity.getGoodsNum();
                    goodsNum = null == goodsNum ? 1 : goodsNum;
                    Integer goodsPriceOrgin = heOrderGoodsEntity.getGoodsPriceOrgin();
                    goodsPriceOrgin = null == goodsPriceOrgin ? 0 : goodsPriceOrgin;
                    Integer totalAllocationOriginPrice = heOrderGoodsEntity.getTotalAllocationOriginPrice();
                    totalAllocationOriginPrice = null == totalAllocationOriginPrice ? 0 : totalAllocationOriginPrice;
                    buyGoodsPriceSum += goodsPriceOrgin * goodsNum;
                    buyGoodsPayPriceSum += totalAllocationOriginPrice;
                }
            }
        }
        result.setBuyOrderAmount(RMBUtils.changeF2Y(buyGoodsPriceSum.longValue()));
        result.setBuyPayAmount(RMBUtils.changeF2Y(buyGoodsPayPriceSum.longValue()));

    }

    private void setExtraInfo(HeOrderVO result, HeOrderEntity req) {
        ExtraInfo extraInfo = req.getExtraInfo();
        result.setEstimatedDateAssignment("");
        result.setTripartiteHealthExperts("");
        List<CustomAttribute> fulfillExtraList = extraInfo.getFulfillExtraList();
        if (CollectionUtils.isNotEmpty(fulfillExtraList)) {
            fulfillExtraList.forEach(f -> {
                if (f.getCode().equals("tripartiteHealthExperts")) {
                    result.setTripartiteHealthExperts(f.getShowStr());
                } else if (f.getCode().equals("estimatedDateAssignment")) {
                    result.setEstimatedDateAssignment(f.getShowStr());
                }
            });
        }
    }

    private void setAmountbyIncome(HeOrderVO result, HeOrderEntity temp,Map<Integer, List<HeIncomeRecordEntity>> orderIdIncomeMap) {

        result.setProductionAmountPay("0");
        result.setBalanceAmount("0");
        List<HeIncomeRecordEntity> heIncomeRecordEntities = orderIdIncomeMap.get(result.getOrderId());

        Integer productionAmountPay = temp.getProductionAmountPay();
        if (ObjectUtil.isNotEmpty(productionAmountPay)) {
            result.setProductionAmountPay(RMBUtils.changeF2Y(productionAmountPay.longValue()));
        }

        if (CollectionUtil.isNotEmpty(heIncomeRecordEntities)) {
            Integer balancePay = heIncomeRecordEntities.stream().filter(h -> h.getPayType().equals(OmniPayTypeEnum.BALANCE.getCode()) && h.getStatus().equals(1)).mapToInt(HeIncomeRecordEntity::getIncome).sum();
            if (ObjectUtil.isNotEmpty(balancePay)) {
                result.setBalanceAmount(RMBUtils.changeF2Y(balancePay.longValue()));
            }
        }


        //审阅中的金额
        result.setIncome(AmountChangeUtil.f2YScale2(temp.getCashReviewingAmount()));

    }

    private static void setClientInfo(HeOrderVO result, HeOrderEntity req, Map<Integer, TabClientEntity> abClientByIdMap) {
        TabClientEntity tabClientEntity = abClientByIdMap.get(req.getClientUid());
        if (Objects.nonNull(tabClientEntity)) {
            result.setCustomerName(tabClientEntity.getName());
            result.setCustomerMobile(tabClientEntity.getPhone());
            result.setHidePhone(SensitiveInformationUtil.conductPhoneOrIdCardNo(tabClientEntity.getPhone()));
            result.setCustomerSource(tabClientEntity.getFromType());
            result.setCustomerSourceStr(CustomerFromTypeEnum.getOrderListValueByCode(result.getCustomerSource()));
        }
    }

    private void setStoreInfo(HeOrderVO result, HeOrderEntity req, Map<Integer, CfgStoreEntity> storeByIdMap, Map<String, String> warZoneMap) {
        CfgStoreEntity cfgStoreEntityInfo = storeByIdMap.get(req.getStoreId());
        if (Objects.nonNull(cfgStoreEntityInfo)) {
            result.setStoreName(cfgStoreEntityInfo.getStoreName());
            result.setTypeName(this.getTypeName(cfgStoreEntityInfo.getType(), cfgStoreEntityInfo.getChildType()));
            result.setWarZone(cfgStoreEntityInfo.getWarzone());
            result.setWarZoneDesc(warZoneMap.get(String.valueOf(cfgStoreEntityInfo.getWarzone())));
        }
    }

    private String getTypeName(Integer type, Integer childType) {

        if (StoreTypeEnum.SAINT_BELLA.getCode().equals(type)) {
            if (StoreChildTypeEnum.BELLA_VILLA.getCode().equals(childType)) {
                return StoreChildTypeEnum.BELLA_VILLA.getValue();
            }
            return StoreTypeEnum.SAINT_BELLA.getValue();
        }
        if (StoreTypeEnum.BABY_BELLA.getCode().equals(type)) {
            if (StoreChildTypeEnum.BABY_BELLA_DELUXE.getCode().equals(childType)) {
                return StoreChildTypeEnum.BABY_BELLA_DELUXE.getValue();
            }
            return StoreTypeEnum.BABY_BELLA.getValue();
        }
        return StoreTypeEnum.getValueByCode(type);
    }

    private ByteArrayOutputStream getByteArray(Workbook workbook) throws IOException {
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        if (workbook != null) {
            workbook.write(stream);
        }
        return stream;
    }
}

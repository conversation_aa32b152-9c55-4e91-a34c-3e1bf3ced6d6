package com.stbella.order.server.context.component.spi.impl;

import com.stbella.core.enums.BusinessEnum;
import com.stbella.customer.server.ecp.vo.ClientInfoVO;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.server.context.component.spi.CustomerQuerySpi;
import com.stbella.order.server.convert.TabClientConvert;
import com.stbella.order.server.manager.TabClientManager;
import com.stbella.platform.order.api.req.CreateOrderReq;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.annotation.FuncPointSpiImpl;
import top.primecare.snowball.extpoint.definition.FuncSpiConfig;

import javax.annotation.Resource;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-05-30  14:01
 * @Description: 默认客户信息组装扩展点实现
 */
@Component
@FuncPointSpiImpl(name = "默认客户信息组装扩展点实现", desc = "默认是用tabClient中获取，未来一些场景可能不用获取tabClient")
public class DefaultCustomerQuerySpiImpl implements CustomerQuerySpi {

    @Resource
    TabClientManager tabClientManager;
    @Resource
    TabClientConvert tabClientConvert;

    @Override
    public boolean condition(CreateOrderReq param) {
        // 这里只根据BU判断
        if (CompareUtil.integerEqual(param.getBu() , BusinessEnum.CARE_CENTER.getCode())){
            return true;
        }
        return false;
    }

    @Override
    public HeOrderUserSnapshotEntity invoke(CreateOrderReq param) {

        ClientInfoVO clientInfo = tabClientManager.getClientInfoByClientId(param.getClientUid());
        HeOrderUserSnapshotEntity userSnapshotEntity = tabClientConvert.clientInfo2OrderUserSnapshot(clientInfo);
        return userSnapshotEntity;
    }

    @Override
    public FuncSpiConfig config() {
        //默认优先级高
        return new FuncSpiConfig(true, Integer.MIN_VALUE);
    }
}

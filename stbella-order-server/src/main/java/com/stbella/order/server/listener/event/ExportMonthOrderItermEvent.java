package com.stbella.order.server.listener.event;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2023-09-01  13:39
 * @Description: 导出母婴当月所有生效的订单明细
 */
@Data
public class ExportMonthOrderItermEvent {

    /**
     * 10位时间戳
     */
    private Long eventTime;

    /**
     * 来源
     */
    private String source;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间, 10位时间戳")
    private Long startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间，10位时间戳，当天23:59:59")
    private Long endTime;

}

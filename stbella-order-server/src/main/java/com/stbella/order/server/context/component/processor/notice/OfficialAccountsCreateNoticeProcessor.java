package com.stbella.order.server.context.component.processor.notice;

import com.stbella.order.domain.client.MessageClient;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

/**
 * @Author: JJ
 * @CreateTime: 2024-05-28  13:55
 * @Description: 创建订单公众号通知组件
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "创建订单公众号通知组件")
public class OfficialAccountsCreateNoticeProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private MessageClient messageClient;

    @Override
    public boolean condition(FlowContext bizContext) {
        return true;
    }

    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);
        messageClient.createOrderMessage(order);
    }
}

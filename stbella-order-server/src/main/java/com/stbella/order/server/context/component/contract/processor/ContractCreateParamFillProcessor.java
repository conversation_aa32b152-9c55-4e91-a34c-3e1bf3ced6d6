package com.stbella.order.server.context.component.contract.processor;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.contract.model.req.v3.ContractCreateV3Req;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.enums.month.OrderSignTypeEnum;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.repository.UserEsignRepository;
import com.stbella.order.server.context.component.contract.stage.ContractFillContext;
import com.stbella.order.server.context.component.contract.stage.ContractFillStrategy;
import com.stbella.order.server.context.component.contract.stage.ContractParamFillContext;
import com.stbella.order.server.contract.enums.ContractExceptionEnum;
import com.stbella.order.server.contract.req.MonthUserEsignDTO;
import com.stbella.order.server.convert.TabClientConvert;
import com.stbella.order.server.manager.TabClientManager;
import com.stbella.order.server.order.month.enums.CardVerifyEnum;
import com.stbella.platform.order.api.contract.req.ContractBaseReq;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import com.stbella.store.server.ecp.entity.CfgStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 合同创建参数填充
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "合同创建参数填充")
public class ContractCreateParamFillProcessor implements IExecutableAtom<FlowContext> {


    @Resource
    ContractFillContext contractFillContext;
    @Resource
    TabClientManager tabClientManager;
    @Resource
    TabClientConvert tabClientConvert;
    @Resource
    UserEsignRepository userEsignRepository;

    @Override
    public void run(FlowContext bizContext) {
        ContractParamFillContext contractParamFillContext = buildContractCreateParam(bizContext);
        ContractFillStrategy contractFillStrategy = contractFillContext.getFillStrategy(contractParamFillContext);
        ContractCreateV3Req orderContractCreateReq = contractFillStrategy.handle(contractParamFillContext);
        log.info("合同创建参数填充结果={}", JSONObject.toJSONString(orderContractCreateReq));
        bizContext.setAttribute(ContractCreateV3Req.class, orderContractCreateReq);
    }

    private ContractParamFillContext buildContractCreateParam(FlowContext bizContext) {
        HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);
        CfgStoreEntity cfgStore = bizContext.getAttribute(CfgStoreEntity.class);
        ContractBaseReq contractBaseReq = bizContext.getAttribute(ContractBaseReq.class);
        //根据规则引擎查找模版ID
        ContractParamFillContext contractParamFillContext = new ContractParamFillContext();
        contractParamFillContext.setOrderEntity(order);
        contractParamFillContext.setCfgStore(cfgStore);
        contractParamFillContext.setOrderId(order.getOrderId());
        contractParamFillContext.setRequest(contractBaseReq);

        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = bizContext.getAttribute(HeOrderUserSnapshotEntity.class);
        HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity = bizContext.getAttribute(HeOrderBailorSnapshotEntity.class);
        List<OrderContractSignRecordVO> orderContractSignRecordVOS = bizContext.getListAttribute(OrderContractSignRecordVO.class);

        OrderContractSignRecordVO contractSignRecordVO = bizContext.getAttribute(OrderContractSignRecordVO.class);
        contractParamFillContext.setCurrentSignRecord(contractSignRecordVO);

        contractParamFillContext.setHeOrderUserSnapshotEntity(heOrderUserSnapshotEntity);
        contractParamFillContext.setHeOrderBailorSnapshotEntity(heOrderBailorSnapshotEntity);
        contractParamFillContext.setSignRecords(orderContractSignRecordVOS);
        contractParamFillContext.setUserSignDTO(userSignAssembler(order, contractBaseReq.getTemplateContractType(), heOrderUserSnapshotEntity, heOrderBailorSnapshotEntity));
        return contractParamFillContext;
    }

    public MonthUserEsignDTO userSignAssembler(HeOrderEntity heOrder, Integer templateContractType, HeOrderUserSnapshotEntity heOrderUserSnapshotEntity, HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity) {

        MonthUserEsignDTO userSignDTO = new MonthUserEsignDTO();
        //客户签订/合同类型授权委托书
        Integer signType = heOrder.getSignType();
        if (OrderSignTypeEnum.SIGN_TYPE_CLIENT.code().equals(signType) || TemplateContractTypeEnum.ENTRUST.code().equals(templateContractType)) {

            MonthHeUserEsignEntity monthHeUserEsignEntity = userEsignRepository.queryByConditionOne(heOrderUserSnapshotEntity.getName(), heOrderUserSnapshotEntity.getPhone(), heOrderUserSnapshotEntity.getCertType(), heOrderUserSnapshotEntity.getIdCard());
            if (Objects.isNull(monthHeUserEsignEntity)) {
                if (CardVerifyEnum.successVerify(heOrderUserSnapshotEntity.getIsCardVerify())) {
                    return userSignDTO;
                }
            }

            return tabClientConvert.clientUserEsignEntity2Dto(monthHeUserEsignEntity);
        }

        //委托人
        if (OrderSignTypeEnum.SIGN_TYPE_BAILOR.code().equals(signType)) {
            log.info("委托人信息查询 = {}", JSONUtil.toJsonStr(heOrderBailorSnapshotEntity));
            Optional.ofNullable(heOrderBailorSnapshotEntity).orElseThrow(() -> new BusinessException(ResultEnum.NOT_EXIST.getCode(), "委托人快照不存在"));
            return tabClientManager.queryBailorInfoById(heOrderBailorSnapshotEntity.getBailorId());
        }

        return userSignDTO;
    }


}

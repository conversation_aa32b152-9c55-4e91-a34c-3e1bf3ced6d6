package com.stbella.order.server.context.component.contract.stage;

import cn.hutool.core.collection.CollectionUtil;
import com.stbella.contract.model.res.ContractTemplateRes;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.HeOrderBailorSnapshotEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.server.contract.dto.UserSignDTO;
import com.stbella.order.server.contract.req.MonthUserEsignDTO;
import com.stbella.platform.order.api.contract.req.ContractBaseReq;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import com.stbella.store.server.ecp.entity.CfgStore;
import lombok.Data;

import java.util.List;

@Data
public class ContractParamFillContext {

    /**
     * 请求参数
     */
    private ContractBaseReq request;

    /**
     * 订单信息
     */
    private HeOrderEntity orderEntity;

    /**
     * 用户信息
     */
    private HeOrderUserSnapshotEntity heOrderUserSnapshotEntity;

    /**
     * 委托人信息
     */
    private HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity;

    /**
     * 签订人信息
     */
    private MonthUserEsignDTO userSignDTO;

    /**
     * 门店信息
     */
    private CfgStoreEntity cfgStore;

    /**
     * 所有订单记录
     */
    private List<OrderContractSignRecordVO> signRecords;

    /**
     * 主合同
     */
    private OrderContractSignRecordVO mainSignRecord;

    /**
     * 合同模板类型
     */
    private Integer templateContractType;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 合同模板参数
     */
    private ContractTemplateRes contractTemplateRes;

    /**
     * 当前签约记录
     */
    private OrderContractSignRecordVO currentSignRecord;


    public OrderContractSignRecordVO fetchMainSignRecord() {
        if (CollectionUtil.isNotEmpty(signRecords)) {
            signRecords.stream().filter(o -> TemplateContractTypeEnum.YZ_SAINTBELLA.code().equals(o.getTemplateContractType())).findFirst().ifPresent(o -> mainSignRecord = o);
        }
        return mainSignRecord;
    }

}
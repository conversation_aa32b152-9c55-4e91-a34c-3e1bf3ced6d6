package com.stbella.order.server.context.component.validator;

import com.stbella.asset.api.enums.AccountStatus;
import com.stbella.asset.api.enums.AccountType;
import com.stbella.asset.api.res.AccountDto;
import com.stbella.core.exception.BusinessException;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.core.OrderProcessTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.service.OrderIncomeDomainService;
import com.stbella.order.server.context.component.checker.OrderProcessCheckerContext;
import com.stbella.order.server.manager.AssetManager;
import com.stbella.order.server.order.month.req.PayReqV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 支付校验器
 * <AUTHOR>
 */
@Slf4j
@Component
@RefreshScope
@SnowballComponent(name = "支付校验器")
public class PayOrderValidator implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderProcessCheckerContext processCheckerContext;
    @Resource
    OrderIncomeDomainService incomeDomainService;

    @Resource
    private AssetManager assetManager;

    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);
        PayReqV2 payReq = bizContext.getAttribute(PayReqV2.class);
        //这里为了共用一个组件，多了个上下文判断。
        if (Objects.isNull(payReq)){
            payReq = new PayReqV2();
            payReq.setPayAmount(new BigDecimal(0));
        }

         /*
             验证总数数
             O应付 = O签单 - 减免
             O待付 = O应付  + O退回重付 - O已付
         */
        List<HeIncomeRecordEntity> successfulRecords= incomeDomainService.queryEffectiveRecord(order);
        Integer sumPaidForPerformance = order.getRealAmount();
        if (order.isNewOrder()){
            sumPaidForPerformance = successfulRecords.stream().mapToInt(HeIncomeRecordEntity::getIncome).sum();
        }
        // 未审核的线下支付是要占数量的
        int leftPay = order.calPayable() + order.fetchTempRefund() - sumPaidForPerformance;
        BigDecimal payAmount = payReq.getPayAmount();
        if (OmniPayTypeEnum.REDUCTION.getCode().equals(payReq.getPayType())){
            leftPay = leftPay + AmountChangeUtil.changeY2FFoInt(payAmount);
        }
        if (AmountChangeUtil.changeY2FFoInt(payAmount) > leftPay){
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code()+"", "收款金额不可大于剩余应收金额！");
        }

        processCheckerContext.check(order, OrderProcessTypeEnum.PAY);
        if (Arrays.asList(OmniPayTypeEnum.PRODUCTION_COIN.getCode(), OmniPayTypeEnum.BALANCE.getCode()).contains(payReq.getPayType())){
            log.info("开始校验账户余额basicUid={} payType={}", order.getBasicUid(), payReq.getPayType());
            AccountDto accountDto = assetManager.queryAccount(order.getBasicUid(), payReq.getPayType().equals(OmniPayTypeEnum.PRODUCTION_COIN.getCode()) ? AccountType.CKJ.getCode() : AccountType.BALANCE.getCode());
            if (accountDto == null){
                throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code()+"", "账户不存在！");
            }
            if (AccountStatus.DISABLE.getCode().equals(accountDto.getStatus())){
                throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code()+"", "该账户已冻结，请联系客服处理！");
            }
        }
    }

}

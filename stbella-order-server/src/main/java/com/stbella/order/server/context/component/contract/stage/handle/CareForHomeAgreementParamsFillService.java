package com.stbella.order.server.context.component.contract.stage.handle;

import cn.hutool.core.date.DateTime;
import com.stbella.contract.common.constant.BizConstant;
import com.stbella.contract.model.req.OrderParamHistoryChangeValuePushDTO;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.order.BUEnum;
import com.stbella.order.common.utils.GeneratorUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.server.context.component.contract.stage.ContractParamFillContext;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class CareForHomeAgreementParamsFillService extends AgreementParamsFillAbstractService{

    @Override
    public Map<String, String> buildParam(ContractParamFillContext contractParamFillContext) {

        HeOrderEntity order = contractParamFillContext.getOrderEntity();
        //创建补充协议
        Long fetchTotalReductionAmount = super.getFetchTotalReductionAmount(contractParamFillContext.getCurrentSignRecord(), order.fetchTotalReductionAmount());

        List<OrderParamHistoryChangeValuePushDTO> changeValuePushDTOS = new ArrayList<>();
        //减免折扣
        super.addDiscount(order, fetchTotalReductionAmount, changeValuePushDTOS);
        //签单金额
        addPayAmount(order, fetchTotalReductionAmount, changeValuePushDTOS);

        for (OrderParamHistoryChangeValuePushDTO changeValuePushDTO : changeValuePushDTOS) {

            changeValuePushDTO.setParam1(StringUtils.EMPTY);
            //兜底逻辑
            changeValuePushDTO.setParam2("【费用】");
            if (OmniOrderTypeEnum.DJ_MOTHER_CARE_ORDER.getCode().equals(order.getOrderType())){
                changeValuePushDTO.setParam2("【第六条费用】");
            }
            if (OmniOrderTypeEnum.DJ_INFANT_CARE_ORDER.getCode().equals(order.getOrderType())){
                changeValuePushDTO.setParam2("【第三条服务费用】");
            }
        }
        OrderContractSignRecordVO orderContractSignRecordVO = contractParamFillContext.fetchMainSignRecord();
        Map<String, String> templateContext = generateTemplateData(orderContractSignRecordVO, changeValuePushDTOS, order);
        String contractNo = GeneratorUtil.contractNo();
        templateContext.put("contract_no", contractNo);
        //主合同编号
        templateContext.put("main_contract_no", orderContractSignRecordVO.getContractNo());
        templateContext.put("company_address", BizConstant.COMPANY_ADDRESS);
        DateTime dateTime = new DateTime(orderContractSignRecordVO.getUpdatedAt() * 1000);
        templateContext.put("sign_time_str2", dateTime.toDateStr());
        return templateContext;
    }

    @Override
    public Integer bizHandleType() {
        return BUEnum.CARE_FOR_HOME.getCode();
    }
}

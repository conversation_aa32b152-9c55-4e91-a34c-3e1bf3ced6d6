package com.stbella.order.server.context.component.decrease.processor;

import com.stbella.contract.model.enums.ContractTypeEnum;
import com.stbella.contract.model.enums.TemplateContractTypeEnum;
import com.stbella.contract.model.enums.TemplateTypeEnum;
import com.stbella.core.result.Result;
import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.domain.repository.OrderReductionRepository;
import com.stbella.order.server.manager.ContractManager;
import com.stbella.order.server.order.cts.enums.DecreaseTypeEnum;
import com.stbella.platform.order.api.contract.api.ContractCommandService;
import com.stbella.platform.order.api.contract.req.ContractBaseReq;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

/**
 * 减免合同修改审批通过
 * <p>
 * 创建《母婴护理服务合同书之补充协议X》
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "减免合同修改审批通过")
public class ContractModifyDecreaseSuccessProcessor implements IExecutableAtom<FlowContext> {


    @Resource
    private ContractCommandService contractCommandService;


    @Override
    public void run(FlowContext bizContext) {
        OrderReductionEntity orderReductionEntity = bizContext.getAttribute(OrderReductionEntity.class);
        //先查到主合同，再组装一波参数传到合同中心，去创建补充协议，并通过E签宝模版发送
        Long orderId = orderReductionEntity.getOrderId();
        ContractBaseReq contractBaseReq = new ContractBaseReq();
        contractBaseReq.setOrderId(orderId.intValue());
        contractBaseReq.setTemplateContractType(TemplateContractTypeEnum.SUPPLIMENT.code());
        contractBaseReq.setContractType(ContractTypeEnum.ESIGN_TYPE.code());
        contractBaseReq.setTemplateType(TemplateTypeEnum.MAIN_TYPE.code());
        contractBaseReq.setReductionId(orderReductionEntity.getId());
        Result<OrderContractSignRecordVO> serviceContract =
                contractCommandService.createContract(contractBaseReq);
        OrderContractSignRecordVO data = serviceContract.getData();
        log.info("创建补充协议返回结果={}", data);

    }


    @Override
    public boolean condition(FlowContext context) {
        DecreaseTypeEnum decreaseTypeEnum = context.getAttribute(DecreaseTypeEnum.class);

        //判断是否是修改合同通过，创建补充协议
        if (DecreaseTypeEnum.MODIFY_APPROVAL.getCode().equals(decreaseTypeEnum.getCode())) {
            return true;
        }
        return false;
    }

}

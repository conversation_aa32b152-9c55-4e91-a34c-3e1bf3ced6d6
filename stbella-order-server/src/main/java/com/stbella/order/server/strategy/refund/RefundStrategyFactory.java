package com.stbella.order.server.strategy.refund;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.server.order.month.enums.PayRecordEnum;
import com.stbella.pay.server.entity.mq.RefundNotifyMq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 退款处理器
 * @date 2024/3/15 10:59
 */
@Component
@Slf4j
public class RefundStrategyFactory {
    private final Map<PayRecordEnum, RefundStrategy> refundStrategyMap = new HashMap<>();

    @Autowired
    public RefundStrategyFactory(List<RefundStrategy> strategies) {
        for (RefundStrategy strategy : strategies) {
            // 获取策略对应的枚举值
            PayRecordEnum payRecordEnum = strategy.getPayRecordEnum();
            refundStrategyMap.put(payRecordEnum, strategy);
        }
    }

    public void executeRefundStrategy(PayRecordEnum accountTypeByLocalTransactionalNo, RefundNotifyMq refundNotifyMq) {
        RefundStrategy strategy = refundStrategyMap.get(accountTypeByLocalTransactionalNo);
        if (ObjectUtil.isNull(strategy)) {
            throw new BusinessException(ResultEnum.NOT_EXIST, "无法找到对应退款通知处理策略");
        }
        strategy.processRefund(refundNotifyMq);
    }
}

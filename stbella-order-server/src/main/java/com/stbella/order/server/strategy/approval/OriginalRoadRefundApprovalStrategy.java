package com.stbella.order.server.strategy.approval;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.base.server.sms.enums.SmsTemplateV2Enum;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.month.server.request.RefundApprovalOriginalRoadRequest;
import com.stbella.month.server.request.RefundApprovalRequest;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.month.AheadOutRoomEnum;
import com.stbella.order.common.enums.month.RefundRecordPayStatusEnum;
import com.stbella.order.common.utils.JsonUtil;
import com.stbella.order.domain.order.month.entity.AheadOutRoomEntity;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.repository.AheadOutRoomRepository;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.domain.repository.OrderRefundRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.async.AsyncOrder;
import com.stbella.order.server.manager.SmsManager;
import com.stbella.order.server.order.month.req.AheadOutRoomQuery;
import com.stbella.order.server.order.month.request.approval.ApprovalStatusInfo;
import com.stbella.order.server.order.month.service.HeOrderService;
import com.stbella.order.server.order.month.service.MonthOrderWxCommandService;
import com.stbella.order.server.order.month.service.OrderPayV2Service;
import com.stbella.pay.server.alipay.enums.AccountTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * 原路退回
 * @date 2024/3/18 17:45
 */
@Component
@Slf4j
public class OriginalRoadRefundApprovalStrategy implements ApprovalStrategy {
    @Resource
    @Lazy
    private OrderPayV2Service orderPayV2Service;
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private SmsManager smsManager;
    @Resource
    private AheadOutRoomRepository aheadOutRoomRepository;
    @Resource
    private AsyncOrder asyncOrder;

    @Resource
    private HeOrderService heOrderService;

    @Override
    public void handleApproval(OrderApproveRecordTypeEnum type, String params, String messageId, ApprovalStatusInfo approvalStatusInfo, String processId) {
        RefundApprovalOriginalRoadRequest refundApprovalOriginalRoadRequest =  JSONObject.parseObject(params, RefundApprovalOriginalRoadRequest.class);
        if (Objects.isNull(refundApprovalOriginalRoadRequest)){
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "退款审批完成,找不到对应的参数,params=" + params);
        }
        HeOrderRefundEntity heOrderRefundEntity1 = orderRefundRepository.getOneById(refundApprovalOriginalRoadRequest.getOrderRefundId());
        if (ObjectUtil.isEmpty(heOrderRefundEntity1)) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "钉钉审批完成退款失败,找不到对应的退款记录,退款ID=" + refundApprovalOriginalRoadRequest.getOrderRefundId());
        }
        Integer refundAmount = heOrderRefundEntity1.getApplyAmount();
        HeIncomeRecordEntity recordByIncomeSn = incomeRecordRepository.getOneById(heOrderRefundEntity1.getOrderGoodId());
        boolean agree = approvalStatusInfo.isAgree();
        boolean finish = approvalStatusInfo.isFinish();
        boolean refuse = approvalStatusInfo.isRefuse();
        boolean terminate = approvalStatusInfo.isTerminate();
        boolean isRefund = false;
        //发起人手机号
        String originatorPhone = refundApprovalOriginalRoadRequest.getPhone();

        //修改退款记录表审批人信息
        if (finish && agree) {
            heOrderRefundEntity1.setAgreeAt(System.currentTimeMillis() / 1000);
            heOrderRefundEntity1.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_3.getCode());
            isRefund = true;
            try {
                log.info("审批通过发送短信通知（原路退回）:{},refundType:{}" + originatorPhone, heOrderRefundEntity1.getRefundType());
                if (!OmniPayTypeEnum.ONLINE_POS.getCode().equals(heOrderRefundEntity1.getRefundType())) {
                    smsManager.sendMessage(originatorPhone, null, null, SmsTemplateV2Enum.HELPER_REFUND_ORIGINAL_BACK_USER, new String[]{refundApprovalOriginalRoadRequest.getCustomerName(), refundApprovalOriginalRoadRequest.getRefundAmount().setScale(2).toString()});
                }
            } catch (Exception e) {
                log.error("审批通过发送短信通知（原路退回）异常：{}，手机号：{}，内容：{}", e.getMessage(), originatorPhone, new String[]{refundApprovalOriginalRoadRequest.getCustomerName(), refundApprovalOriginalRoadRequest.getRefundAmount().setScale(2).toString()});
            }
        }
        if (terminate) {
            heOrderRefundEntity1.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_2.getCode());
        }
        if (finish && refuse) {
            heOrderRefundEntity1.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_2.getCode());
            try {
                log.info("审批拒绝发送短信通知（原路退回）:{},refundType:{}" + originatorPhone, heOrderRefundEntity1.getRefundType());
                if (OmniPayTypeEnum.ONLINE_POS.getCode().equals(heOrderRefundEntity1.getRefundType())) {
                    //POS机
                    smsManager.sendMessage(originatorPhone, null, null, SmsTemplateV2Enum.HELPER_REFUND_POS_APPLY_REFUSED_USER, new String[]{refundApprovalOriginalRoadRequest.getCustomerName()});
                } else {
                    //其他
                    smsManager.sendMessage(originatorPhone, null, null, SmsTemplateV2Enum.HELPER_REFUND_APPLY_REFUSE_USER, new String[]{refundApprovalOriginalRoadRequest.getCustomerName()});
                }
            } catch (Exception e) {
                log.error("审批拒绝发送短信通知（原路退回）异常：{}，手机号：{}，内容：{}", e.getMessage(), originatorPhone, new String[]{refundApprovalOriginalRoadRequest.getCustomerName()});
            }
        }

        HeOrderEntity heOrder = orderRepository.getByOrderId(heOrderRefundEntity1.getOrderId());
        heOrder.setRefundStatus(heOrderService.getRefundStatusByActualAmount(heOrder.getOrderId(), refundAmount, isRefund, false));
        orderRepository.updateOrderMonthByOrderId(heOrder);
        orderRefundRepository.updateOneById(heOrderRefundEntity1);

        if (!isRefund) {
            recordByIncomeSn.setFreezeAmount(recordByIncomeSn.getFreezeAmount() - refundAmount);
            //释放支付记录冻结金额
            incomeRecordRepository.updateRecord(recordByIncomeSn);
            //修改提前离馆可退金额
            AheadOutRoomQuery query = new AheadOutRoomQuery();
            query.setOrderId(heOrderRefundEntity1.getOrderId());
            query.setState(AheadOutRoomEnum.STATE_DISENABLE.code());
            AheadOutRoomEntity aheadOutRoomEntity = aheadOutRoomRepository.queryByOrderId(query);
            //如果提前离馆同意时间早于退款申请时间则要修改提前离馆可退金额
            if (ObjectUtil.isNotEmpty(aheadOutRoomEntity) && aheadOutRoomEntity.getAgreeAt().before(DateUtil.date(heOrderRefundEntity1.getCreatedAt() * 1000))) {
                aheadOutRoomEntity.setRemainingRefundableAmount(aheadOutRoomEntity.getRemainingRefundableAmount() + refundAmount);
                aheadOutRoomRepository.updateOutRoom(aheadOutRoomEntity);
            }
        } else {
            //退款
            orderPayV2Service.refund(refundApprovalOriginalRoadRequest.getOrderRefundId(), false, AccountTypeEnum.BEI_KANG.getCode());
        }
        //异步退款-积分处理
        HeOrderRefundEntity orderRefundEntity = orderRefundRepository.getOneById(heOrderRefundEntity1.getId());
        log.info("退款审批（原路退回）-积分处理,订单参数={},退款参数={}", heOrder, orderRefundEntity);
        asyncOrder.refundDeductIntegral(heOrder, orderRefundEntity);
    }

    @Override
    public OrderApproveRecordTypeEnum getOrderApproveRecordTypeEnum() {
        return OrderApproveRecordTypeEnum.REFUND_APPROVAL_OF_THE_ORIGINAL_RETURN;
    }
}

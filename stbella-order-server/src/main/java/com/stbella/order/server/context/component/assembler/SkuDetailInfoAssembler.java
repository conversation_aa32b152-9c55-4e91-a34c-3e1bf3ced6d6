package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.domain.client.CombinationGoodsGateway;
import com.stbella.order.domain.client.PropertyGateway;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.server.context.component.assembler.additional.AdditionalContext;
import com.stbella.order.server.context.component.assembler.additional.AdditionalItermAssembler;
import com.stbella.order.server.context.component.assembler.additional.HolidayServiceFeeAdditionalItermAssembler;
import com.stbella.order.server.context.component.checker.NeedSignChecker;
import com.stbella.order.server.convert.SkuDetailInfoConverter;
import com.stbella.order.server.convert.SkuDetailInfoConverterHelper;
import com.stbella.order.server.manager.GoodsManager;
import com.stbella.platform.order.api.req.CustomAttribute;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import com.stbella.platform.order.api.res.GoodsInfo;
import com.stbella.platform.order.api.res.SkuAdditionalInfo;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import com.stbella.store.common.enums.core.GoodsCostPricingMethodEnum;
import com.stbella.store.common.enums.core.GoodsTypeEnum;
import com.stbella.store.goodz.res.PropertyUserDefinedVO;
import com.stbella.store.goodz.res.SkuVo;
import com.stbella.store.goodz.res.miniProgram.GoodsCombinationChildDetailVO;
import com.stbella.store.goodz.res.miniProgram.GoodsCombinationVo;
import com.stbella.store.server.goods.enums.CombinationTypeEnum;
import com.stbella.store.server.store.enums.PropertyTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 组装sku详情信息，比如价格等，是否有节假日服务费，多爆胎等
 * <p>
 * 依赖如下对象：
 * CustomAttribute.class
 * SkuDetailInfo.class
 * CfgStoreEntity.class
 * </p>
 */
@Slf4j
@Component
@SnowballComponent(name = "sku详情组装器", desc = "组装sku详情信息")
public class SkuDetailInfoAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private GoodsManager goodsManager;
    @Resource
    private RuleLinkClient ruleLinkClient;
    @Resource
    private PropertyGateway propertyGateway;
    @Resource
    private List<AdditionalItermAssembler> additionalItermAssemblerList;
    @Resource
    private CombinationGoodsGateway combinationGoodsGateway;
    @Resource
    private NeedSignChecker needSignChecker;
    @Resource
    private HolidayServiceFeeAdditionalItermAssembler holidayServiceFeeAdditionalItermAssembler;
    @Resource
    private SkuDetailInfoConverter skuDetailInfoConverter;

    @Override
    public void run(FlowContext bizContext) {

        List<SkuDetailInfo> skuList = bizContext.getListAttribute(SkuDetailInfo.class);

        if (CollectionUtils.isEmpty(skuList)) {
            return;
        }

        CfgStoreEntity cfgStore = bizContext.getAttribute(CfgStoreEntity.class);

        // 初始化sku 基本信息
        initAllCartSku(cfgStore, skuList);

        //初始化sku附加项
        initCartSkuAdditionalItermAndSignTag(bizContext);

        bizContext.setListAttribute(SkuDetailInfo.class, skuList);
    }


    /**
     * 初始化购物车所有商品的基本信息（价格等）
     *
     * @param skuList
     */
    protected void initAllCartSku(CfgStoreEntity cfgStore, List<SkuDetailInfo> skuList) {

        //单项商品 + 组合内的明细
        Set<Integer> simpleSkuList = skuList.stream().flatMap(sku -> {
            if (sku.getType().intValue() == CombineTypeEnum.SIMPLE.code()) {
                return Collections.singleton(sku.getSkuId()).stream();
            } else {
                return sku.getSubList().stream().map(SkuDetailInfo::getSkuId).collect(Collectors.toSet()).stream();
            }
        }).collect(Collectors.toSet());
        log.info("商品开始 {}, {}", JSONUtil.toJsonStr(simpleSkuList));
        Map<Integer, SkuVo> skuMap = goodsManager.getSkuByIds(simpleSkuList);
        log.info("商品en");

        BigDecimal taxRate = cfgStore.getTaxRate();

        Map<Integer, GoodsInfo> goodsInfoMap = new HashMap<>();

        //初始化skuList中的商品基础数据
        try {
            List<Integer> allGoodsIdList = new ArrayList<>();
            for (SkuDetailInfo sku : skuList) {
                allGoodsIdList.add(sku.getGoodsId());
                List<SkuDetailInfo> subList = sku.getSubList();
                if (CollectionUtils.isNotEmpty(subList)) {
                    allGoodsIdList.addAll(subList.stream().map(SkuDetailInfo::getGoodsId).collect(Collectors.toList()));
                }
                List<GoodsInfo> goodsInfos = goodsManager.queryGoodsInfoByGoodsIdList(allGoodsIdList);
                goodsInfoMap = goodsInfos.stream().filter(Objects::nonNull)
                        .collect(Collectors.toMap(GoodsInfo::getId, Function.identity(), (a, b) -> a));
            }
        } catch (Exception e) {
            log.info("查询商品主信息失败：{}", e.getMessage());
        }


        //组合商品使用goodsId获取商品信息，但要支持相同goodsId的多个实例
        log.info("combine start");
        Set<Integer> combineGoodsIdSet = skuList.stream().filter(sku -> sku.getType().intValue() == CombineTypeEnum.COMBINE.code()).map(SkuDetailInfo::getGoodsId).collect(Collectors.toSet());
        List<GoodsCombinationVo> combinationGoodsList = combinationGoodsGateway.getCombinationGoodsList(combineGoodsIdSet.stream().collect(Collectors.toList()));
        Map<Integer, GoodsCombinationVo> combinationGoodsMap = combinationGoodsList.stream().collect(Collectors.toMap(GoodsCombinationVo::getGoodsId, Function.identity(), (item1, item2) -> item1));
        log.info("combine end");
        int index = 1;
        for (SkuDetailInfo sku : skuList) {
            sku.setSkuSaleState(0);
            if (Objects.isNull(sku.getId())) {
                sku.setId(index++);
            }

            if (sku.getType().intValue() == CombineTypeEnum.SIMPLE.code()) {
                if (skuMap.containsKey(sku.getSkuId())) {
                    SkuVo sourceSkuVo = skuMap.get(sku.getSkuId());
                    convertSkuVoToSkuDetailInfo(sku, sourceSkuVo, goodsInfoMap);
                    initCartSkuCost(sku, taxRate);
                }
            } else {
                log.info("处理组合商品: skuId={}, goodsId={}, subList.size={}", sku.getSkuId(), sku.getGoodsId(), 
                        sku.getSubList() != null ? sku.getSubList().size() : 0);
                if (combinationGoodsMap.containsKey(sku.getGoodsId())) {
                    GoodsCombinationVo combinationVo = combinationGoodsMap.get(sku.getGoodsId());

                    // 组合商品中有分组，分组内的商品要单独覆盖价格
                    for (SkuDetailInfo subSku : sku.getSubList()) {
                        log.info("处理子商品: parentSkuId={}, subSkuId={}, subGoodsId={}", 
                                sku.getSkuId(), subSku.getSkuId(), subSku.getGoodsId());
                        if (skuMap.containsKey(subSku.getSkuId())) {
                            SkuVo sourceSkuVo = skuMap.get(subSku.getSkuId());
                            if (Objects.isNull(subSku.getId())) {
                                subSku.setId(index++);
                            }
                            convertSkuVoToSkuDetailInfo(subSku, sourceSkuVo, goodsInfoMap);
                            initCartSkuCost(subSku, taxRate);
                            subSku.setParentNum(sku.getNum());
                        }

                    }
                    // 根据组合明细的价格覆盖组合商品的价格
                    initCartCombinationIterm(combinationVo, sku.getSubList());
                    //设置组和和总价
                    initCartCombination(sku, combinationVo);
                } else {
                    log.warn("未找到组合商品配置: goodsId={}", sku.getGoodsId());
                }
            }
        }
    }

    /**
     * 初始化购物车所有商品的附加项 节假日服务费，多胎服务费, 是否要签合同等
     *
     * @param bizContext
     */
    private void initCartSkuAdditionalItermAndSignTag(FlowContext bizContext) {

        List<SkuDetailInfo> skuList = bizContext.getListAttribute(SkuDetailInfo.class);
        CfgStoreEntity cfgStore = bizContext.getAttribute(CfgStoreEntity.class);

        // 客户服务信息
        List<CustomAttribute> customAttributeList = bizContext.getListAttribute(CustomAttribute.class);


        // 首先，处理简单商品和组合商品，设置正确的 parentId
        List<SkuDetailInfo> simpleSkuList = skuList.stream().flatMap(sku -> {
            if (sku.getType().intValue() == CombineTypeEnum.SIMPLE.code()) {
                sku.setParentId(sku.getId());
                return Collections.singleton(sku).stream();
            } else {
                return sku.getSubList().stream().map(subSku -> {
                    subSku.setParentId(sku.getId());
                    return subSku;
                });
            }
        }).collect(Collectors.toList());

        Set<Long> backCategoryIdList = simpleSkuList.stream().filter(sku -> Objects.nonNull(sku.getBackCategoryId())).map(sku -> sku.getBackCategoryId().longValue()).collect(Collectors.toSet());
        log.info("property start");
        Map<Long, PropertyUserDefinedVO> categoryPropertyMap = getPropertyMap(PropertyTypeEnum.BACK.getCode(), backCategoryIdList);
        log.info("property end");
        //履约信息
        Map<String, String> fulfillExtraMap = ObjectUtil.defaultIfNull(customAttributeList, new ArrayList<CustomAttribute>()).stream().collect(Collectors.toMap(CustomAttribute::getCode, CustomAttribute::getValue));
        // 节假日 配置
        log.info("rule start");
        List<LocalDate> holidayListConfig = getHolidayConfig();
        log.info("rule end");

        LocalDate lastCheckOutDate = null;

        simpleSkuList = sortSkuDetailInfos(simpleSkuList, holidayListConfig, fulfillExtraMap, lastCheckOutDate, categoryPropertyMap, cfgStore);
        simpleSkuList = simpleSkuList.stream()
                .sorted(Comparator.comparing(sku -> sku.getPromotionInfo() == null ? 0 : 1))
                .collect(Collectors.toList());
        // 附加费用初始化 & 是否签合同
        for (SkuDetailInfo sku : simpleSkuList) {
            AdditionalContext context = AdditionalContext.builder().holidayList(holidayListConfig).fulfillExtraMap(fulfillExtraMap).sku(sku).lastCheckOutDate(lastCheckOutDate).categoryPropertyMap(categoryPropertyMap).store(cfgStore).build();

            List<SkuAdditionalInfo> additionalList = new ArrayList<>();

            List<AdditionalItermAssembler> handlerList = additionalItermAssemblerList.stream().filter(assembler -> assembler.filter(context)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(handlerList)) {
                for (AdditionalItermAssembler handler : handlerList) {
                    SkuAdditionalInfo additionalInfo = handler.run(context);
                    if (ObjectUtil.isNotNull(additionalInfo)) {
                        additionalList.add(additionalInfo);
                    }
                    lastCheckOutDate = context.getLastCheckOutDate();
                }
                ;
            }
            sku.setAdditionalList(additionalList);

            sku.setNeedSign(needSignChecker.check(sku, categoryPropertyMap));
        }
        ;

        // simpleSkuList 转map 赋值给 skuList
        // 修复bug：使用复合key来区分组合商品中的子商品和外层单个商品
        // 复合key格式：parentId + "_" + id 或 parentId + "_" + skuId
        Map<String, SkuDetailInfo> simplSkuMapByCompositeKey = new HashMap<>();

        log.info("=== 开始构建附加费用映射 ===");
        for (SkuDetailInfo sku : simpleSkuList) {
            String compositeKey;
            if (Objects.nonNull(sku.getId())) {
                compositeKey = sku.getParentId() + "_" + sku.getId();
            } else {
                compositeKey = sku.getParentId() + "_" + sku.getSkuId();
            }

            log.info("构建映射 - 商品名称: {}, id: {}, skuId: {}, parentId: {}, 复合key: {}, 附加费用数量: {}",
                    sku.getSkuName(), sku.getId(), sku.getSkuId(), sku.getParentId(), compositeKey,
                    sku.getAdditionalList() != null ? sku.getAdditionalList().size() : 0);

            if (sku.getAdditionalList() != null && !sku.getAdditionalList().isEmpty()) {
                for (SkuAdditionalInfo additional : sku.getAdditionalList()) {
                    log.info("  - 附加费用: {}, 数量: {}", additional.getName(), additional.getNum());
                }
            }

            simplSkuMapByCompositeKey.put(compositeKey, sku);
        }
        log.info("=== 附加费用映射构建完成，共{}个条目 ===", simplSkuMapByCompositeKey.size());

        log.info("=== 开始分配附加费用到原始商品列表 ===");
        for (SkuDetailInfo sku : skuList) {
            if (sku.getType().intValue() == CombineTypeEnum.SIMPLE.code()) {
                log.info("处理单个商品 - 名称: {}, id: {}, skuId: {}", sku.getSkuName(), sku.getId(), sku.getSkuId());
                initCartSkuAdditionalItermAndSignTag(sku, simplSkuMapByCompositeKey);
            } else {
                log.info("处理组合商品 - 名称: {}, id: {}, 子商品数量: {}", sku.getSkuName(), sku.getId(),
                        sku.getSubList() != null ? sku.getSubList().size() : 0);
                for (SkuDetailInfo subSku : sku.getSubList()) {
                    log.info("  处理子商品 - 名称: {}, id: {}, skuId: {}, parentId: {}",
                            subSku.getSkuName(), subSku.getId(), subSku.getSkuId(), subSku.getParentId());
                    initCartSkuAdditionalItermAndSignTag(subSku, simplSkuMapByCompositeKey);
                }
            }
        }
        log.info("=== 附加费用分配完成 ===");

    }

    @NotNull
    private List<SkuDetailInfo> sortSkuDetailInfos(List<SkuDetailInfo> simpleSkuList, List<LocalDate> holidayListConfig, Map<String, String> fulfillExtraMap, LocalDate lastCheckOutDate, Map<Long, PropertyUserDefinedVO> categoryPropertyMap, CfgStoreEntity cfgStore) {
        //先计算每个SKU需要计算节假日费用的数量，然后进行排序
        for (SkuDetailInfo skuDetailInfo : simpleSkuList) {
            AdditionalContext context = AdditionalContext.builder().holidayList(holidayListConfig).fulfillExtraMap(fulfillExtraMap).sku(skuDetailInfo).lastCheckOutDate(lastCheckOutDate).categoryPropertyMap(categoryPropertyMap).store(cfgStore).build();
            boolean isCalSku = holidayServiceFeeAdditionalItermAssembler.filter(context);
            skuDetailInfo.setCalHoliday(isCalSku);
        }


        // 使用 Map 来统计每个 parentId 需要计算费用的数量
        Map<Integer, Integer> parentIdCalHolidayCountMap = new HashMap<>();

        for (SkuDetailInfo skuDetailInfo : simpleSkuList) {
            if (skuDetailInfo.getCalHoliday()) {
                Integer parentId = skuDetailInfo.getParentId();
                int count = skuDetailInfo.getNum() * skuDetailInfo.getSkuNum() * skuDetailInfo.getParentNum();
                parentIdCalHolidayCountMap.merge(parentId, count, Integer::sum);
            }
        }

        // 对 parentIdCalHolidayCountMap 按值降序排序
        List<Map.Entry<Integer, Integer>> sortedEntries = new ArrayList<>(parentIdCalHolidayCountMap.entrySet());
        sortedEntries.sort((e1, e2) -> e2.getValue().compareTo(e1.getValue()));

        // 创建一个新的列表来存储排序后的结果
        List<SkuDetailInfo> sortedSimpleSkuList = new ArrayList<>();

        // 按照排序后的 parentId 顺序重新组织 simpleSkuList
        for (Map.Entry<Integer, Integer> entry : sortedEntries) {
            Integer parentId = entry.getKey();
            List<SkuDetailInfo> skuGroup = simpleSkuList.stream()
                    .filter(sku -> sku.getParentId().equals(parentId)).sorted((o1, o2) -> Integer.compare(o2.getNum() * o2.getSkuNum() * o2.getParentNum(), o1.getNum() * o1.getSkuNum() * o2.getParentNum()))
                    .collect(Collectors.toList());
            sortedSimpleSkuList.addAll(skuGroup);
        }

        // 添加不需要计算节日费用的商品到列表末尾
        List<SkuDetailInfo> nonCalHolidaySkus = simpleSkuList.stream().filter(sku -> !sku.getCalHoliday()).sorted((o1, o2) -> Integer.compare(o2.getNum() * o2.getSkuNum(), o1.getNum() * o1.getSkuNum())).collect(Collectors.toList());
        sortedSimpleSkuList.addAll(nonCalHolidaySkus);

        // 用排序后的列表替换原有的 simpleSkuList
        simpleSkuList = sortedSimpleSkuList;
        return simpleSkuList;
    }

    /**
     * 组合商品初始化价格
     * 每个分组有自己的商品明细
     *
     * @param combinationVo
     * @param subSkuList
     */
    protected void initCartCombinationIterm(GoodsCombinationVo combinationVo, List<SkuDetailInfo> subSkuList) {

        // 构建map key = groupKey_skuId, value = GoodsCombinationChildDetailVO
        Map<String, GoodsCombinationChildDetailVO> CombinationChildMap = new HashMap<>();
        combinationVo.getCombinationDetailVOList().forEach(combinationDetailVO -> {
            //这里不处理极端情况，不同组有同一个商品，状态还不同。
            combinationDetailVO.getChildDetailList().forEach(sku -> {
                String key = buildGroupSkuKey(combinationDetailVO.getCombinationCardConfigVO().getSort() + "", sku.getSkuId());
                CombinationChildMap.put(key, sku);
            });
        });

        // 遍历组合商品明细, CombinationChildMap 对象覆盖价格等信息
        subSkuList.forEach(subSku -> {
            String key = buildGroupSkuKey(subSku.getGroupKey(), subSku.getSkuId());
            subSku.setSkuSaleState(0);
            if (CombinationChildMap.containsKey(key)) {
                GoodsCombinationChildDetailVO combinationChildDetailVO = CombinationChildMap.get(key);
                initCartSubSku(subSku, combinationChildDetailVO);
            }
        });
    }

    /**
     * 购物车sku初始化附加项
     *
     * @param cartSku 目标商品
     * @param simplSkuMapByCompositeKey 按复合key索引的附加费用map
     */
    protected void initCartSkuAdditionalItermAndSignTag(SkuDetailInfo cartSku,
                                                       Map<String, SkuDetailInfo> simplSkuMapByCompositeKey) {
        // 构建复合key来查找对应的附加费用
        String compositeKey;
        if (Objects.nonNull(cartSku.getId())) {
            compositeKey = cartSku.getParentId() + "_" + cartSku.getId();
        } else {
            compositeKey = cartSku.getParentId() + "_" + cartSku.getSkuId();
        }

        log.info("查找附加费用 - 商品名称: {}, id: {}, skuId: {}, parentId: {}, 复合key: {}",
                cartSku.getSkuName(), cartSku.getId(), cartSku.getSkuId(), cartSku.getParentId(), compositeKey);

        if (simplSkuMapByCompositeKey.containsKey(compositeKey)) {
            SkuDetailInfo sourceSkuWithAdditional = simplSkuMapByCompositeKey.get(compositeKey);
            cartSku.setAdditionalList(sourceSkuWithAdditional.getAdditionalList());

            log.info("找到匹配的附加费用 - 附加费用数量: {}",
                    sourceSkuWithAdditional.getAdditionalList() != null ? sourceSkuWithAdditional.getAdditionalList().size() : 0);

            if (sourceSkuWithAdditional.getAdditionalList() != null && !sourceSkuWithAdditional.getAdditionalList().isEmpty()) {
                for (SkuAdditionalInfo additional : sourceSkuWithAdditional.getAdditionalList()) {
                    log.info("  - 设置附加费用: {}, 数量: {}", additional.getName(), additional.getNum());
                }
            }
        } else {
            log.warn("未找到匹配的附加费用 - 复合key: {}, 可用的keys: {}", compositeKey, simplSkuMapByCompositeKey.keySet());
        }
    }


    /**
     * 使用MapStruct将SkuVo转换为SkuDetailInfo
     *
     * @param cartSku      SKU详情信息（目标对象）
     * @param sku          SKU信息（源对象）
     * @param goodsInfoMap 商品信息映射
     */
    protected void convertSkuVoToSkuDetailInfo(SkuDetailInfo cartSku, SkuVo sku, Map<Integer, GoodsInfo> goodsInfoMap) {
        if (cartSku == null || sku == null) {
            log.warn("转换参数为空，cartSku: {}, sku: {}", cartSku != null, sku != null);
            return;
        }

        try {
            // 使用MapStruct进行转换
            skuDetailInfoConverter.updateSkuDetailInfoFromSkuVo(cartSku, sku, goodsInfoMap);

            // 验证转换结果
            boolean success = SkuDetailInfoConverterHelper.validateConversion(cartSku, sku);
            SkuDetailInfoConverterHelper.logConversion("SkuVo", "SkuDetailInfo", success);

            log.debug("SKU转换完成，skuId: {}, skuName: {}", cartSku.getSkuId(), cartSku.getSkuName());
        } catch (Exception e) {
            log.error("SKU转换失败，skuId: {}", sku.getId(), e);
            // 如果转换失败，使用原来的方法作为备用
            fallbackInitCartSku(cartSku, sku, goodsInfoMap);
        }
    }

    /**
     * 备用的SKU初始化方法（当MapStruct转换失败时使用）
     *
     * @deprecated 仅作为备用方法，优先使用MapStruct转换
     */
    @Deprecated
    private void fallbackInitCartSku(SkuDetailInfo cartSku, SkuVo sku, Map<Integer, GoodsInfo> goodsInfoMap) {
        log.warn("使用备用方法初始化SKU，skuId: {}", sku.getId());

        try {
            cartSku.setSkuParentId(sku.getSkuParentId());
            cartSku.setSkuSaleState(sku.getSkuSaleState());
            cartSku.setSkuName(sku.getSpuName());
            cartSku.setSpecification(sku.getTemplateSkuProp());
            cartSku.setPrice(new BigDecimal(sku.getGoodsPrice()));
            cartSku.setCostPrice(new BigDecimal(sku.getCostPrice()));
            cartSku.setBackCategoryId(sku.getCategoryBack());
            cartSku.setAssetType(sku.getGoodsType());
            cartSku.setCostPricingMethod(sku.getCostPricingMethod());
            cartSku.setCostRatio(sku.getCostRatio());
            cartSku.setRoomType(sku.getRoomType());
            cartSku.setRoomName(sku.getRoomTypeName());
            cartSku.setEcpRoomType(sku.getEcpRoomType());
            cartSku.setUnitStr(sku.getGoodsUnitStr());
            cartSku.setCategoryBackTitle(sku.getCategoryBackTitle());
            cartSku.setProduceAmountDeduction(sku.getProduceAmountDeduction());
            cartSku.setProductionDiscountRuleType(sku.getProductionDiscountRuleType());
            cartSku.setServiceTime(sku.getServiceTime());
            cartSku.setServiceType(sku.getServiceType());

            // 转换属性值
            String propertyValue = SkuDetailInfoConverterHelper.convertPropertyValueToJson(sku);
            cartSku.setPropertyValue(propertyValue);

            // 设置商品信息
            GoodsInfo goodsInfo = goodsInfoMap.getOrDefault(cartSku.getGoodsId(), new GoodsInfo());
            cartSku.setGoodsInfo(goodsInfo);
        } catch (Exception e) {
            log.error("备用SKU初始化方法也失败了，skuId: {}", sku.getId(), e);
        }
    }


    /**
     * 初始化购物车中的sku cost
     * 有些是成本= 按销售价比例的
     *
     * @param cartSku
     * @param taxRate
     */
    protected void initCartSkuCost(SkuDetailInfo cartSku, BigDecimal taxRate) {
        log.info("汇率：{}", taxRate);
        //按销售价比例的
        if (CompareUtil.integerEqual(GoodsCostPricingMethodEnum.PRICING_PROPORTION_SELLING_PRICE.getCode(), cartSku.getCostPricingMethod())) {
            if (Objects.isNull(cartSku.getSkuNum())) {
                cartSku.setSkuNum(1);
            }
            // cost =  单份价格 / sku数量 * 售价比 * 税率；
            BigDecimal cost = cartSku.getPrice().multiply(new BigDecimal(cartSku.getCostRatio())).divide(new BigDecimal(cartSku.getSkuNum()), 2, RoundingMode.DOWN).divide(new BigDecimal(100));
            BigDecimal costTax = cost.multiply(taxRate);
            // 舍去
            BigDecimal costWithTax = cost.add(costTax).setScale(2, RoundingMode.DOWN);
            cartSku.setCostPrice(costWithTax);
        }

    }

    /**
     * 根据sku 初始化购物车中的 组合明细的sku
     *
     * @param cartSku
     * @param sku
     */
    protected void initCartSubSku(SkuDetailInfo cartSku, GoodsCombinationChildDetailVO sku) {
        cartSku.setSkuSaleState(sku.getEnable());
        cartSku.setSkuName(sku.getSkuName());
        cartSku.setPrice(sku.getComboPrice());
        cartSku.setExtraPrice(sku.getAddPrice());
        cartSku.setUnitStr(sku.getUnitStr());
    }

    /**
     * 根据sku 初始化购物车中的 组合商品
     *
     * @param cartSku
     * @param combinationVo
     */
    protected void initCartCombination(SkuDetailInfo cartSku, GoodsCombinationVo combinationVo) {

        //固定搭配 用商品的中的价格，可选用子商品的价格之和
        cartSku.setSkuSaleState(combinationVo.getSaleState());
        cartSku.setSkuName(combinationVo.getGoodsName());
        cartSku.setCostPrice(new BigDecimal(0));
        cartSku.setSkuParentId(combinationVo.getGoodsId());
        cartSku.setPrice(combinationVo.getGoodsPrice());
        if (CombinationTypeEnum.OPTIONAL_COLLOCATION.getCode().intValue() == combinationVo.getCombinationType()) {
            //求和
            BigDecimal total = cartSku.getSubList().stream().map(a -> a.getPrice().multiply(new BigDecimal(a.getNum()))).reduce(BigDecimal.ZERO, BigDecimal::add);
            cartSku.setPrice(total);
        }

        cartSku.setBackCategoryId(combinationVo.getBackId());
        cartSku.setAssetType(GoodsTypeEnum.COMBINATION.code());
        cartSku.setCombinationPricingMethod(combinationVo.getCombinationType());
        cartSku.setCostPricingMethod(0);
        cartSku.setGoodsAttachment(combinationVo.getGoodsAttachment());
        cartSku.setGoodsAttachmentName(combinationVo.getGoodsAttachmentName());
        cartSku.setCategoryBackTitle(combinationVo.getCategoryBackTitle());
    }


    /**
     * 获取节假日配置
     *
     * @return
     */
    private List<LocalDate> getHolidayConfig() {

        Map<String, String> factMap = new HashMap<>();
        factMap.put("configCode", BizConstant.RuleLinkScene.HOLIDAYS_CONFIG);
        ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req("systemConfig", factMap);
        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
        // 查到所有节假日配置
        List<String> holidayListStr = JSONUtil.toList(hitRuleVo.getSimpleRuleValue(), String.class);
        return holidayListStr.stream().map(dateStr -> DateUtil.parseLocalDateTime(dateStr, "yyyy-MM-dd").toLocalDate()).collect(Collectors.toList());
    }

    /**
     * 返回自定义属性
     *
     * @param businessIdList
     * @return
     */
    protected Map<Long, PropertyUserDefinedVO> getPropertyMap(Integer type, Set<Long> businessIdList) {
        List<PropertyUserDefinedVO> customPropertyList = propertyGateway.getCustomPropertyListByIds(type, businessIdList.stream().collect(Collectors.toList()));
        Map<Long, PropertyUserDefinedVO> propertyMap = customPropertyList.stream().collect(Collectors.toMap(PropertyUserDefinedVO::getBusinessId, Function.identity(), (item1, item2) -> item1));
        return propertyMap;
    }

    /**
     * 组合sku的key
     *
     * @param groupKey
     * @param skuId
     * @return
     */
    protected String buildGroupSkuKey(String groupKey, Integer skuId) {
        return groupKey + "_" + skuId;
    }

}

package com.stbella.order.server.config;


import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.provider.AbstractDataSourceProvider;
import com.baomidou.dynamic.datasource.provider.DynamicDataSourceProvider;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DataSourceProperty;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.stbella.order.server.order.month.handler.ListConfigHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.Map;

@ComponentScan
@Configuration
@AutoConfigureBefore({DynamicDataSourceAutoConfiguration.class, SpringBootConfiguration.class})
@MapperScan(basePackages = {
        "com.stbella.order.server.contract.mapper",
        "com.stbella.order.server.order.mapper",
        "com.stbella.order.server.order.cts.mapper",
        "com.stbella.order.server.order.month.mapper",
        "com.stbella.order.server.order.nutrition.mapper",
}, sqlSessionFactoryRef = "orderSqlSessionFactory")
@Slf4j
public class DataSourceConfig {

    private static String DATA_SOURCE_KEY = "master";

    private static String MAPPER_LOCATION = "classpath*:/mapper/*.xml";

    @Resource
    private DynamicDataSourceProperties properties;

    @Value("${sso.datasource.url}")
    public String ssoDatasourceUrl;
    @Value("${sso.datasource.username}")
    public String ssoDatasourceUsername;
    @Value("${sso.datasource.pwd}")
    public String ssoDatasourcePwd;
    @Value("${sso.datasource.driver}")
    public String ssoDatasourceDriver;
    @Value("${sso.datasource.max-active}")
    public Integer datasourceMaxActive;

    @Bean("masterDataSource")
    @Primary
    public DynamicRoutingDataSource masterDataSource() {
        final DruidDataSource dataSource = new DruidDataSource();
        dataSource.setUrl(ssoDatasourceUrl);
        dataSource.setUsername(ssoDatasourceUsername);
        dataSource.setPassword(ssoDatasourcePwd);
        dataSource.setDriverClassName(ssoDatasourceDriver);
        dataSource.setMaxActive(datasourceMaxActive);

        log.info("masterDataSource={}", ssoDatasourceUrl);

        DynamicDataSourceProvider dynamicDataSourceProvider = new AbstractDataSourceProvider() {
            final Map<String, DataSourceProperty> datasourceMap = properties.getDatasource();

            @Override
            public Map<String, DataSource> loadDataSources() {
                Map<String, DataSource> dataSourceMap = createDataSourceMap(datasourceMap);
                dataSourceMap.put(DATA_SOURCE_KEY, dataSource);
                return dataSourceMap;
            }
        };
        DynamicRoutingDataSource DRDataSource = new DynamicRoutingDataSource();
        DRDataSource.setPrimary(DATA_SOURCE_KEY);
        DRDataSource.setStrict(false);
        DRDataSource.setProvider(dynamicDataSourceProvider);
        return DRDataSource;

    }


    @Bean(name = "orderTransactionManager")
    @Primary
    public DataSourceTransactionManager orderTransactionManager() {
        return new DataSourceTransactionManager(masterDataSource());
    }

    @Bean(name = "orderSqlSessionFactory")
    @Primary
    public SqlSessionFactory orderSqlSessionFactory(@Qualifier("masterDataSource") DataSource orderDataSource, @Qualifier("globalConfig") GlobalConfig config)
            throws Exception {
        final MybatisSqlSessionFactoryBean sessionFactory = new MybatisSqlSessionFactoryBean();
        sessionFactory.setDataSource(orderDataSource);
        sessionFactory.setTypeHandlers(new ListConfigHandler(String.class));
        sessionFactory.setMapperLocations(
                new PathMatchingResourcePatternResolver().getResources(MAPPER_LOCATION)
        );

        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        sessionFactory.setPlugins(interceptor);
        sessionFactory.setGlobalConfig(config);
        return sessionFactory.getObject();
    }

    @Bean("orderSqlSessionTemplate")
    @Primary
    public SqlSessionTemplate oneSqlSessionTemplate(
            @Qualifier("orderSqlSessionFactory") SqlSessionFactory sessionFactory) {
        return new SqlSessionTemplate(sessionFactory);
    }

//    @Bean
//    public DataSource dataSource(DynamicDataSourceProvider dynamicDataSourceProvider) {
//        DynamicRoutingDataSource dataSource = new DynamicRoutingDataSource();
//        dataSource.setPrimary(DATA_SOURCE_KEY);
//        dataSource.setStrict(properties.getStrict());
//        dataSource.setStrategy(properties.getStrategy());
//        dataSource.setProvider(dynamicDataSourceProvider);
//        dataSource.setP6spy(properties.getP6spy());
//        return dataSource;
//    }


}



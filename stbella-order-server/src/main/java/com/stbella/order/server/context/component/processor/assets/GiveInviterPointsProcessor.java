package com.stbella.order.server.context.component.processor.assets;

import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.server.async.AsyncOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

/**
 * @Author: JJ
 * @Description: 邀请人赠送积分
 * 业绩生效时
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "邀请人赠送积分")
public class GiveInviterPointsProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private AsyncOrder asyncOrder;

    @Override
    public boolean condition(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        return orderEntity.isJustEffect();
    }

    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        orderEntity.setInviteAddIntegralType(1);
        asyncOrder.addParentOrderIntegral(orderEntity);

    }
}


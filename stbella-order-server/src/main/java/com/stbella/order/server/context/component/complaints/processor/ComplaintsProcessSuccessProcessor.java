package com.stbella.order.server.context.component.complaints.processor;

import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

@Component
@Slf4j
@SnowballComponent(name = "客诉审批通过订单处理", desc = "")
public class ComplaintsProcessSuccessProcessor implements IExecutableAtom<FlowContext> {


    @Override
    public void run(FlowContext bizContext) {
        HeCustomerComplaintsEntity heCustomerComplaintsEntity = bizContext.getAttribute(HeCustomerComplaintsEntity.class);
        log.info("客诉审批通过订单处理:{}", heCustomerComplaintsEntity);
        heCustomerComplaintsEntity.processSuccess();
    }

}

package com.stbella.order.server.consumer;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.stbella.base.server.constant.DingConstant;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.notice.entity.OaProcessIdRelationPO;
import com.stbella.notice.entity.OaProcessRecordPO;
import com.stbella.notice.server.OaProcessIdRelationService;
import com.stbella.notice.server.OaProcessRecordService;
import com.stbella.order.common.utils.JsonUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.order.month.request.approval.ApprovalStatusInfo;
import com.stbella.order.server.strategy.approval.ApprovalStrategyFactory;
import com.stbella.pulsar.consumer.PulsarConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.pulsar.client.api.Message;
import org.apache.pulsar.client.api.SubscriptionType;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class PicpDingNotityConsumer {


    @Resource
    private OrderRepository orderRepository;
    @DubboReference
    private OaProcessIdRelationService oaProcessIdRelationService;
    @DubboReference
    private OaProcessRecordService oaProcessRecordService;
    @Resource
    private ApprovalStrategyFactory approvalsStrategyFactory;
    @Resource
    private RedissonClient redissonClient;


    /**
     * 钉钉审批mq消费
     */
    @PulsarConsumerListener(destination = "persistent://pulsar-44k4paxzz5vg/nameSpace/picp_ding_notify", subscriptionName = "picp_ding_notify", subscriptionType = SubscriptionType.Shared)
    @Transactional(rollbackFor = Exception.class)
    public void getMsg(Message message) {

        String key = "Order:PicpDingNotify:Exec:";
        RLock lockName = redissonClient.getLock(key + this.getProcessInstanceId(message));
        try {
            String messageId = String.valueOf(message.getMessageId());
            log.info("钉钉审批mq消费start message id is{}", messageId);
            boolean tryLock = lockName.tryLock(0, 10, TimeUnit.MINUTES);
            Assert.isTrue(tryLock, "该任务当前正在执行，请勿重复提交");
            String messageData = new String(message.getData());
            JSONObject jsonObject = JSONUtil.parseObj(messageData);
            if (ObjectUtil.isEmpty(jsonObject.get(DingConstant.RESULT)) || jsonObject.get("type").equals("cancel") || jsonObject.get("type").equals("start")) {
                return;
            }
            String processInstanceId = (String) jsonObject.get(DingConstant.PROCESS_INSTANCE_ID);
            String type = (String) jsonObject.get(DingConstant.TYPE);
            String result = (String) jsonObject.get(DingConstant.RESULT);
            log.info("processInstanceId={}", processInstanceId);
            OaProcessIdRelationPO oneByLocalProcessId = oaProcessIdRelationService.getOneByLocalProcessId(processInstanceId);
            if (ObjectUtil.isEmpty(oneByLocalProcessId)) {
                log.error("processInstanceId 为{} oneByLocalProcessId为空", processInstanceId);
                return;
            }
            ApprovalStatusInfo approvalStatusInfo = getApprovalStatusInfo(oneByLocalProcessId, result, type);

            String processId = getProcessId(oneByLocalProcessId);

            log.info("oneByLocalProcessId:{},processId:{}", oneByLocalProcessId, processId);

            OaProcessRecordPO oneByProcessId = oaProcessRecordService.getOneByProcessId(processId);
            log.info("订单钉钉审批回调记录={}", oneByProcessId);

            String params = oneByProcessId.getBody();
            log.info("月子母婴审批的入参{}", params);

            JSONObject read = JSONUtil.parseObj(params);

            String processType = oneByLocalProcessId.getType();

            OrderApproveRecordTypeEnum enumByPlatformId = OrderApproveRecordTypeEnum.getEnumByPlatformId(Integer.valueOf(processType));

            enumByPlatformId = getOrderApproveRecordTypeEnum(enumByPlatformId, read);

            log.info("钉钉审批mq消费start2");
            approvalsStrategyFactory.processApproval(enumByPlatformId, params, messageId, approvalStatusInfo, processInstanceId);

        } catch (Exception e) {
            log.error("钉钉回调失败", e);
            throw new BusinessException(ResultEnum.SYSTEM_EXECUTION_ERROR.getCode(), "钉钉回调失败:" + e.getMessage());
        } finally {
            if (Objects.nonNull(lockName)) {
                lockName.unlock();
            }
        }
    }

    private @NotNull OrderApproveRecordTypeEnum getOrderApproveRecordTypeEnum(OrderApproveRecordTypeEnum enumByPlatformId, JSONObject read) {
        if (enumByPlatformId.equals(OrderApproveRecordTypeEnum.APPROVAL_OF_DEPOSIT_REFUND)) {
            //如果是订单1.0的押金图款审批，按订单退款逻辑走
            Integer orderId = read.getInt("orderId");
            if (ObjectUtil.isNotEmpty(orderId) && orderId > 0) {
                HeOrderEntity orderEntity = orderRepository.getByOrderId(orderId);
                if (ObjectUtil.isNotEmpty(orderEntity)) {
                    if (ObjectUtil.isNotEmpty(orderEntity.getVersion()) && orderEntity.isNewOrder()) {
                        enumByPlatformId = OrderApproveRecordTypeEnum.ORDER_REFUND;
                    }
                }
            }
        }
        return enumByPlatformId;
    }

    private static @NotNull ApprovalStatusInfo getApprovalStatusInfo(OaProcessIdRelationPO oneByLocalProcessId, String result, String type) {
        Boolean finish;
        Boolean terminate;
        Boolean agree;
        Boolean refuse;
        Integer approveStatus = oneByLocalProcessId.getApproveStatus();
        if (StringUtils.isNotEmpty(oneByLocalProcessId.getQwProcessId())) {
            //审批是否结束
            finish = approveStatus == 1 || approveStatus == 2;
            //审批是否是终止
            terminate = approveStatus == 3;
            //最终是否被同意
            agree = finish && result.equalsIgnoreCase("agree");
            //最终是否被拒绝
            refuse = finish && result.equalsIgnoreCase("refuse");
        } else {
            //审批是否结束
            finish = type.equalsIgnoreCase("finish");
            //审批是否是终止
            terminate = type.equalsIgnoreCase("terminate");
            //最终是否被同意
            agree = result.equalsIgnoreCase("agree");
            //最终是否被拒绝
            refuse = result.equalsIgnoreCase("refuse");
        }
        ApprovalStatusInfo approvalStatusInfo = new ApprovalStatusInfo();
        approvalStatusInfo.setFinish(finish);
        approvalStatusInfo.setAgree(agree);
        approvalStatusInfo.setRefuse(refuse);
        approvalStatusInfo.setTerminate(terminate);

        log.info("finish:{},terminate:{},agree:{},refuse:{}", finish, terminate, agree, refuse);
        return approvalStatusInfo;
    }

    /**
     * 返回唯一的id
     *
     * @param oneByLocalProcessId
     * @return
     */
    private String getProcessId(OaProcessIdRelationPO oneByLocalProcessId) {

        Integer opType = oneByLocalProcessId.getOpType();
        if (opType == 1) {
            return oneByLocalProcessId.getQwProcessId();
        } else if (opType == 2) {
            return oneByLocalProcessId.getCyProcessId();
        } else {
            return oneByLocalProcessId.getDingProcessId();
        }
    }

    private String getProcessInstanceId(Message message) {

        String messageId = "processInstanceId";
        try {
            String messageData = new String(message.getData());
            System.out.println(messageData);
            messageId = String.valueOf(message.getMessageId());
            JSONObject jsonObject = JSONUtil.parseObj(messageData);
            log.info("钉钉审批mq消费={}", jsonObject);
            if (ObjectUtil.isEmpty(jsonObject.get(DingConstant.RESULT)) || jsonObject.get("type").equals("cancel") || jsonObject.get("type").equals("start")) {
                return messageData;
            }
            return (String) jsonObject.get(DingConstant.PROCESS_INSTANCE_ID);
        } catch (Exception e) {
            log.error("获取消息唯一标识发生异常", e);
            return messageId;
        }
    }

    public void getMsg(String str) {
        String messageId = null;
        JSONObject jsonObject = JSONUtil.parseObj(str);
        log.info("钉钉审批mq消费={}", jsonObject);
        if (ObjectUtil.isEmpty(jsonObject.get(DingConstant.RESULT)) || jsonObject.get("type").equals("cancel") || jsonObject.get("type").equals("start")) {
            return;
        }
        String processInstanceId = (String) jsonObject.get(DingConstant.PROCESS_INSTANCE_ID);


        //processInstanceId是数据库本地id

        String type = (String) jsonObject.get(DingConstant.TYPE);
        String result = (String) jsonObject.get(DingConstant.RESULT);

        Boolean finish = null, terminate = null, agree = null, refuse = null;

        OaProcessIdRelationPO oneByLocalProcessId = oaProcessIdRelationService.getOneByLocalProcessId(processInstanceId);
        oneByLocalProcessId.setOpType(1);
        oneByLocalProcessId.setApproveStatus(1);
        oaProcessIdRelationService.updateById(oneByLocalProcessId);

        Integer approveStatus = oneByLocalProcessId.getApproveStatus();
        if (ObjectUtil.isNotEmpty(oneByLocalProcessId)) {
            if (StringUtils.isNotEmpty(oneByLocalProcessId.getQwProcessId())) {
                //审批是否结束
                finish = approveStatus == 1 || approveStatus == 2;
                //审批是否是终止
                terminate = approveStatus == 3;
                //最终是否被同意
                agree = finish && result.equalsIgnoreCase("agree");
                //最终是否被拒绝
                refuse = finish && result.equalsIgnoreCase("refuse");
            } else {
                //审批是否结束
                finish = type.equalsIgnoreCase("finish");
                //审批是否是终止
                terminate = type.equalsIgnoreCase("terminate");
                //最终是否被同意
                agree = result.equalsIgnoreCase("agree");
                //最终是否被拒绝
                refuse = result.equalsIgnoreCase("refuse");
            }
        }
        ApprovalStatusInfo approvalStatusInfo = new ApprovalStatusInfo();
        approvalStatusInfo.setFinish(finish);
        approvalStatusInfo.setAgree(agree);
        approvalStatusInfo.setRefuse(refuse);
        approvalStatusInfo.setTerminate(terminate);


        log.info("finish:{},terminate:{},agree:{},refuse:{}", finish, terminate, agree, refuse);
        log.info("oneByLocalProcessId:{}", oneByLocalProcessId);

        String processId = getProcessId(oneByLocalProcessId);
        OaProcessRecordPO oneByProcessId = oaProcessRecordService.getOneByProcessId(processId);
        log.info("订单钉钉审批回调记录={}", oneByProcessId);


        String params = oneByProcessId.getBody();
        log.info("月子母婴审批的入参{}", params);

        //入参转hashMap
        HashMap read = JsonUtil.read(params, HashMap.class);

        //发起人手机号
        String originatorPhone = (String) read.get("phone");

        String processType = oneByLocalProcessId.getType();

        OrderApproveRecordTypeEnum enumByPlatformId = OrderApproveRecordTypeEnum.getEnumByPlatformId(Integer.valueOf(processType));

        if (enumByPlatformId.equals(OrderApproveRecordTypeEnum.APPROVAL_OF_DEPOSIT_REFUND)) {
            //如果是订单1.0的押金图款审批，按订单退款逻辑走
            Integer orderId = (Integer) read.get("orderId");
            if (ObjectUtil.isNotEmpty(orderId) && orderId > 0) {
                HeOrderEntity orderEntity = orderRepository.getByOrderId(orderId);
                if (ObjectUtil.isNotEmpty(orderEntity)) {
                    if (ObjectUtil.isNotEmpty(orderEntity.getVersion()) && orderEntity.getVersion().compareTo(new BigDecimal(3.0)) >= 0) {
                        enumByPlatformId = OrderApproveRecordTypeEnum.ORDER_REFUND;
                    }
                }
            }
        }

        approvalsStrategyFactory.processApproval(enumByPlatformId, params, messageId, approvalStatusInfo, oneByLocalProcessId.getLocalProcessId());

    }

    public void getMsgFail(String str) {
        String messageId = null;
        JSONObject jsonObject = JSONUtil.parseObj(str);
        log.info("钉钉审批mq消费={}", jsonObject);
        if (ObjectUtil.isEmpty(jsonObject.get(DingConstant.RESULT)) || jsonObject.get("type").equals("cancel") || jsonObject.get("type").equals("start")) {
            return;
        }
        String processInstanceId = (String) jsonObject.get(DingConstant.PROCESS_INSTANCE_ID);


        //processInstanceId是数据库本地id

        String type = (String) jsonObject.get(DingConstant.TYPE);
        String result = (String) jsonObject.get(DingConstant.RESULT);

        Boolean finish = null, terminate = null, agree = null, refuse = null;

        OaProcessIdRelationPO oneByLocalProcessId = oaProcessIdRelationService.getOneByLocalProcessId(processInstanceId);
        oneByLocalProcessId.setOpType(1);
        oneByLocalProcessId.setApproveStatus(2);
        oaProcessIdRelationService.updateById(oneByLocalProcessId);

        Integer approveStatus = oneByLocalProcessId.getApproveStatus();
        if (ObjectUtil.isNotEmpty(oneByLocalProcessId)) {
            if (StringUtils.isNotEmpty(oneByLocalProcessId.getQwProcessId())) {
                //审批是否结束
                finish = approveStatus == 1 || approveStatus == 2;
                //审批是否是终止
                terminate = approveStatus == 3;
                //最终是否被同意
                agree = finish && result.equalsIgnoreCase("agree");
                //最终是否被拒绝
                refuse = finish && result.equalsIgnoreCase("refuse");
            } else {
                //审批是否结束
                finish = type.equalsIgnoreCase("finish");
                //审批是否是终止
                terminate = type.equalsIgnoreCase("terminate");
                //最终是否被同意
                agree = result.equalsIgnoreCase("agree");
                //最终是否被拒绝
                refuse = result.equalsIgnoreCase("refuse");
            }
        }
        ApprovalStatusInfo approvalStatusInfo = new ApprovalStatusInfo();
        approvalStatusInfo.setFinish(finish);
        approvalStatusInfo.setAgree(agree);
        approvalStatusInfo.setRefuse(refuse);
        approvalStatusInfo.setTerminate(terminate);

        log.info("finish:{},terminate:{},agree:{},refuse:{}", finish, terminate, agree, refuse);
        log.info("oneByLocalProcessId:{}", oneByLocalProcessId);

        String processId = getProcessId(oneByLocalProcessId);
        OaProcessRecordPO oneByProcessId = oaProcessRecordService.getOneByProcessId(processId);
        log.info("订单钉钉审批回调记录={}", oneByProcessId);


        String params = oneByProcessId.getBody();
        log.info("月子母婴审批的入参{}", params);

        //入参转hashMap
        HashMap read = JsonUtil.read(params, HashMap.class);

        String processType = oneByLocalProcessId.getType();

        OrderApproveRecordTypeEnum enumByPlatformId = OrderApproveRecordTypeEnum.getEnumByPlatformId(Integer.valueOf(processType));

        if (enumByPlatformId.equals(OrderApproveRecordTypeEnum.APPROVAL_OF_DEPOSIT_REFUND)) {
            //如果是订单1.0的押金图款审批，按订单退款逻辑走
            Integer orderId = (Integer) read.get("orderId");
            if (ObjectUtil.isNotEmpty(orderId)) {
                HeOrderEntity orderEntity = orderRepository.getByOrderId(orderId);
                if (ObjectUtil.isNotEmpty(orderEntity)) {
                    if (ObjectUtil.isNotEmpty(orderEntity.getVersion()) && orderEntity.getVersion().compareTo(new BigDecimal(3.0)) >= 0) {
                        enumByPlatformId = OrderApproveRecordTypeEnum.ORDER_REFUND;
                    }
                }
            }
        }

        approvalsStrategyFactory.processApproval(enumByPlatformId, params, messageId, approvalStatusInfo, oneByLocalProcessId.getLocalProcessId());

    }
}

package com.stbella.order.server.contract.month.component;

import com.google.common.collect.Lists;
import com.stbella.order.server.contract.req.ESignFlowRecordReq;
import com.stbella.order.server.contract.res.ESignCancleFlowRecordVO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.enums.month.ContractModeEnum;
import com.stbella.order.common.enums.month.ContractStatusEnum;
import com.stbella.order.common.enums.month.OrderSignTypeEnum;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.config.MonthESignPropertiesConfig;
import com.stbella.order.server.contract.dto.ContractSignStartRequestDTO;
import com.stbella.order.server.contract.dto.SignAddressResponseDTO;
import com.stbella.order.server.contract.enums.ContractExceptionEnum;
import com.stbella.order.server.contract.provider.month.ESignProvider;
import com.stbella.order.server.contract.req.MonthUserEsignDTO;
import com.stbella.order.server.contract.req.SignContractQuery;
import com.stbella.order.server.contract.res.ContractSignAddressVO;
import com.stbella.order.server.contract.service.month.MonthEsignTemplateService;
import com.stbella.order.server.contract.service.month.MonthUserEsignService;
import com.stbella.order.server.convert.TabClientConvert;
import com.stbella.order.server.manager.TabClientManager;
import com.stbella.order.server.order.month.res.MonthContractSignRecordVO;
import com.stbella.order.server.order.month.res.MonthEsignTemplateVO;
import com.stbella.order.server.order.month.res.MonthHeUserEsignVO;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 月子合同组装数据
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-08 15:02
 */
@Component
@Slf4j
public class MonthContractAssembler {

    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;
    @Resource
    private MonthUserEsignService monthUserEsignService;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private TabClientManager tabClientManager;
    @Resource
    private TabClientConvert tabClientConvert;
    @Resource
    private ESignProvider eSignProvider;
    @Resource
    private MonthEsignTemplateService monthEsignTemplateService;
    @Resource
    private MonthESignPropertiesConfig eSignPropertiesConfig;
    @Resource
    private MonthContractSignRecordRepository monthContractSignRecordRepository;
    @Resource
    private OrderBailorSnapshotRepository orderBailorSnapshotRepository;
    @Resource
    private MonthContractSignAgreementRepository monthContractSignAgreementRepository;

    public MonthUserEsignDTO userEsignAssembler(Integer orderId, Integer templateContractType) {

        MonthUserEsignDTO monthUserEsignDTO = new MonthUserEsignDTO();
        HeOrderEntity orderEntity = orderRepository.queryOrderById(orderId);
        Optional.ofNullable(orderEntity).orElseThrow(() -> new BusinessException(ResultEnum.PARAM_ERROR, "订单信息不存在"));

        //客户签订/合同类型授权委托书
        if (OrderSignTypeEnum.SIGN_TYPE_CLIENT.code().equals(orderEntity.getSignType()) || TemplateContractTypeEnum.ENTRUST.code().equals(templateContractType)) {
            HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(orderId);
            Optional.ofNullable(heOrderUserSnapshotEntity).orElseThrow(() -> new BusinessException(ResultEnum.PARAM_ERROR, "订单快照不存在"));
            MonthHeUserEsignVO userEsign = monthUserEsignService.queryByConditionOne(heOrderUserSnapshotEntity.getName(), heOrderUserSnapshotEntity.getPhone(), heOrderUserSnapshotEntity.getCertType(), heOrderUserSnapshotEntity.getIdCard());
            Optional.ofNullable(userEsign).orElseThrow(() -> new BusinessException(ContractExceptionEnum.USER_ESIGN_INFO_NULL.getCode(), ContractExceptionEnum.USER_ESIGN_INFO_NULL.getValue()));
            return tabClientConvert.clientUserEsignVo2Dto(userEsign);
        }

        //委托人
        if (OrderSignTypeEnum.SIGN_TYPE_BAILOR.code().equals(orderEntity.getSignType())) {
            HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity = orderBailorSnapshotRepository.queryByOrderId(orderId);
            log.info("委托人信息查询 = {}", JSONUtil.toJsonStr(heOrderBailorSnapshotEntity));
            Optional.ofNullable(heOrderBailorSnapshotEntity).orElseThrow(() -> new BusinessException(ResultEnum.NOT_EXIST.getCode(), "委托人快照不存在"));
            return tabClientManager.queryBailorInfoById(heOrderBailorSnapshotEntity.getBailorId());
        }

        return monthUserEsignDTO;
    }

    /**
     * @param signContractQuery
     * @param signRecord
     * @return {@link ContractSignAddressVO}
     */
    public ContractSignAddressVO signAddressAssembler(SignContractQuery signContractQuery, MonthContractSignRecordEntity signRecord) {
        ContractSignAddressVO contractSignAddress = new ContractSignAddressVO();
        if (Objects.isNull(signRecord)) {
            throw new BusinessException(ContractExceptionEnum.GENERATE_CONTRACT_NOT_EXIT.getCode(), ContractExceptionEnum.GENERATE_CONTRACT_NOT_EXIT.getValue());
        }
        if (StringUtils.isBlank(signRecord.getEsignFileId())) {
            throw new BusinessException(ContractExceptionEnum.GET_ESIGN_FILEID_NULL.getCode(), ContractExceptionEnum.GET_ESIGN_FILEID_NULL.getValue());
        }
        if (ContractStatusEnum.SIGNED.code().equals(signRecord.getContractStatus())) {
            throw new BusinessException(ContractExceptionEnum.CONTRACT_SIGEN_ERR.getCode(), ContractExceptionEnum.CONTRACT_SIGEN_ERR.getValue());
        }
        if (ContractStatusEnum.NOT_SIGNED.code().equals(signRecord.getContractStatus()) && StringUtils.isNotBlank(signRecord.getContractLongUrl()) && StringUtils.isNotBlank(signRecord.getContractShortUrl())) {
            //当面签用长链接
            if (ContractModeEnum.NOW_FACE_TYPE.code().equals(signContractQuery.getContractMode())) {
                contractSignAddress.setContractSignAddress(signRecord.getContractLongUrl());
                return contractSignAddress;
            }
            //其它签署用短连接
            contractSignAddress.setContractSignAddress(signRecord.getContractShortUrl());
            return contractSignAddress;
        }

        return contractSignAddress;
    }

    /**
     * 授权委托书签署
     *
     * @param signContractQuery
     * @return {@link ContractSignAddressVO}
     * @return {@link MonthContractSignRecordVO}
     */
    public ContractSignAddressVO entrustAssembler(SignContractQuery signContractQuery, MonthContractSignRecordEntity signRecord, MonthUserEsignDTO userEsign) {
        ContractSignAddressVO contractSignAddress = signAddressAssembler(signContractQuery, signRecord);
        // 通过sign record 的模版ID再获取模版信息
        MonthEsignTemplateVO esignTemplate = monthEsignTemplateService.getByTemplateId(signRecord.getTemplateId());
        // 获取授权委托书甲方签章位置
        if (StringUtils.isAnyBlank(esignTemplate.getPersonSignaturePos())) {
            throw new BusinessException(ContractExceptionEnum.CONTRACT_TEMPLATE_CONFIGURATION_ERR.getCode(), ContractExceptionEnum.CONTRACT_TEMPLATE_CONFIGURATION_ERR.getValue());
        }
        String[] personSignaturePos = esignTemplate.getPersonSignaturePos().split(",");
        if (ArrayUtil.isEmpty(personSignaturePos)) {
            throw new BusinessException(ContractExceptionEnum.CONTRACT_TEMPLATE_CONFIGURATION_ERR.getCode(), ContractExceptionEnum.CONTRACT_TEMPLATE_CONFIGURATION_ERR.getValue());
        }
        ContractSignStartRequestDTO contractSignStartRequest = new ContractSignStartRequestDTO();
        // 启动流程基本信息
        ContractSignStartRequestDTO.InnerFlowInfo innerFlowInfo = new ContractSignStartRequestDTO.InnerFlowInfo();
        innerFlowInfo.setBusinessScene("月子" + TemplateContractTypeEnum.ENTRUST.desc() + "附件合同签订流程发起");
        ContractSignStartRequestDTO.InnerFlowInfo.InnerFlowConfigInfo flowConfigInfo = new ContractSignStartRequestDTO.InnerFlowInfo.InnerFlowConfigInfo();

        // 在此业务场景下不需要发短信或邮件通知客户，所以此字段直接默认"",必须要填e签宝文档规定
        flowConfigInfo.setNoticeType("");
        flowConfigInfo.setBatchDropSeal(false);
        flowConfigInfo.setNoticeDeveloperUrl(eSignPropertiesConfig.getMainCallbackUrl());
//        flowConfigInfo.setSignPlatform("1,2");//h5微信和支付宝都兼容
        List<String> list = Lists.newArrayList();
        List<String> newList = Lists.newArrayList();
        //合同签订入口0=未知;1=h5;2=微信;3=支付宝
        Integer signFrom = 0;
        if (10 == signContractQuery.getContractMode()) {
            signFrom = 2;
            // 个人刷脸认证
            list.add("PSN_FACEAUTH_BYURL");
            // 个人运营商三要素认证
            list.add("PSN_TELECOM_AUTHCODE");
            // 个人银行卡四要素认证
            list.add("PSN_BANK4_AUTHCODE");
            // 指定个人页面显示的认证方式
            flowConfigInfo.setPersonAvailableAuthTypes(list);
            flowConfigInfo.setSignPlatform("1");//1,2h5微信和支付宝都兼容
            // 指定意愿微信小程序刷脸
            newList.add("FACE_WE_CHAT_FACE");
            // 指定意愿短信验证
            newList.add("CODE_SMS");
            flowConfigInfo.setWillTypes(newList);
        } else if (ArrayUtil.contains(new int[]{20, 30}, signContractQuery.getContractMode())) {
            signFrom = 2;
            flowConfigInfo.setSignPlatform("1");//1,2h5微信和支付宝都兼容
            //入口支付宝 todo 有可能通过浏览器短信验证签署
            if (30 == signContractQuery.getContractMode()) {
                signFrom = 3;
                flowConfigInfo.setSignPlatform("2");//1,2h5微信和支付宝都兼容
            }
            // 个人刷脸认证
            list.add("PSN_FACEAUTH_BYURL");
            // 个人运营商三要素认证
            list.add("PSN_TELECOM_AUTHCODE");
            // 个人银行卡四要素认证
            list.add("PSN_BANK4_AUTHCODE");
            // 指定个人页面显示的认证方式
            flowConfigInfo.setPersonAvailableAuthTypes(list);
            // 指定意愿腾讯云刷脸
            newList.add("FACE_TECENT_CLOUD_H5");
            // 指定意愿支付宝刷脸
            newList.add("FACE_ZHIMA_XY");
            // 指定意愿短信验证
            newList.add("CODE_SMS");
            flowConfigInfo.setWillTypes(newList);
        }
        innerFlowInfo.setFlowConfigInfo(flowConfigInfo);
        contractSignStartRequest.setFlowInfo(innerFlowInfo);
        List<ContractSignStartRequestDTO.InnerSigners> signersList = new ArrayList<>();
        // 甲方个人签署信息
        ContractSignStartRequestDTO.InnerSigners personSigners = new ContractSignStartRequestDTO.InnerSigners();
        ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount personSignerAccount = new ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount();
        personSignerAccount.setSignerAccount(userEsign.getEsignUserId());
        personSigners.setSignerAccount(personSignerAccount);
        List<ContractSignStartRequestDTO.InnerSigners.InnerSignfields> personInnerSignfieldsList = new ArrayList<>();
        ContractSignStartRequestDTO.InnerSigners.InnerSignfields personInnerSignfields = new ContractSignStartRequestDTO.InnerSigners.InnerSignfields();
        personInnerSignfields.setAutoExecute(false);
        personInnerSignfields.setFileId(signRecord.getEsignFileId());
        personInnerSignfields.setSealType("0");
        personSigners.setPlatformSign(false);
        ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean personInnerPosBean = new ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean();

        // 0420 使用数据库中签名位置
        personInnerPosBean.setPosPage(esignTemplate.getPosPage());
        personInnerPosBean.setPosX(Integer.valueOf(personSignaturePos[0]));
        personInnerPosBean.setPosY(Integer.valueOf(personSignaturePos[1]));

        personInnerSignfields.setPosBean(personInnerPosBean);
        personInnerSignfieldsList.add(personInnerSignfields);
        personSigners.setSignfields(personInnerSignfieldsList);
        signersList.add(personSigners);
        contractSignStartRequest.setSigners(signersList);
        // 待签署文件信息
        List<ContractSignStartRequestDTO.InnerDocs> docsList = new ArrayList<>();
        ContractSignStartRequestDTO.InnerDocs innerDocs = new ContractSignStartRequestDTO.InnerDocs();
        innerDocs.setFileId(signRecord.getEsignFileId());
        innerDocs.setFileName(signRecord.getEsignFileName());
        docsList.add(innerDocs);
        contractSignStartRequest.setDocs(docsList);
        //todo 撤销旧的合同流程
        if (StringUtils.isNotBlank(signRecord.getEsignFlowId())) {
            eSignProvider.cancelSignFlow(signRecord.getEsignFlowId());
        }
        // 获取主合同签订启动流程id
        String signContractFlowId = eSignProvider.doInitiateSigningProcess(contractSignStartRequest);
        log.info("=========================================res:{}", "获取月子" + TemplateContractTypeEnum.ENTRUST.desc() + "合同签订启动流程id" + signContractFlowId);
        // 并保存至数据库，如果合同流程启动，却因其他原因未签署合同，想再次签署合同时直接使用这个流程id就可以获取签署链接
        // 不必再重新发起流程，重新再发起流程会重复扣合同费用
        signRecord.setEsignFileId(signRecord.getEsignFileId());
        signRecord.setEsignFlowId(signContractFlowId);
        signRecord.setSignFrom(signFrom);
        signRecord.setUpdatedAt(DateUtils.getTenBitTimestamp());
        monthContractSignRecordRepository.updateBySignRecordId(signRecord);

        SignAddressResponseDTO signAddress = eSignProvider.getSignAddress(userEsign.getEsignUserId(), signContractFlowId);
        signRecord.setContractLongUrl(signAddress.getLongUrl());
        signRecord.setContractShortUrl(signAddress.getShortUrl());
        signRecord.setUpdatedAt(DateUtils.getTenBitTimestamp());
        monthContractSignRecordRepository.updateBySignRecordId(signRecord);
        log.info("=========================================res:{}", "月子" + TemplateContractTypeEnum.ENTRUST.desc() + "查看地址已更新至数据库");
        if (Objects.equals(ContractModeEnum.NOW_FACE_TYPE.code(), signContractQuery.getContractMode())) {
            contractSignAddress.setContractSignAddress(signAddress.getLongUrl());
            return contractSignAddress;
        }
        contractSignAddress.setContractSignAddress(signAddress.getShortUrl());
        return contractSignAddress;
    }


    /**
     * 订单折扣保密书 甲方是 公司 乙方是 客户
     *
     * @param signContractQuery
     * @param signRecord
     * @param userEsign
     * @return {@link ContractSignAddressVO}
     */
    public ContractSignAddressVO discountAssembler(SignContractQuery signContractQuery, MonthContractSignRecordEntity signRecord, MonthUserEsignDTO userEsign) {
        ContractSignAddressVO contractSignAddress = signAddressAssembler(signContractQuery, signRecord);
        // 通过sign record 的模版ID再获取模版信息
        MonthEsignTemplateVO esignTemplate = monthEsignTemplateService.getByTemplateId(signRecord.getTemplateId());
        // 获取主合同的签章位置
        if (StringUtils.isAnyBlank(esignTemplate.getCompanySealPos(), esignTemplate.getPersonSignaturePos()) || esignTemplate.getPosPage().equals(0)) {
            throw new BusinessException(ContractExceptionEnum.CONTRACT_TEMPLATE_CONFIGURATION_ERR.getCode(), ContractExceptionEnum.CONTRACT_TEMPLATE_CONFIGURATION_ERR.getValue());
        }
        String[] companySealPos = esignTemplate.getCompanySealPos().split(",");
        String[] personSignaturePos = esignTemplate.getPersonSignaturePos().split(",");
        if (ArrayUtil.isEmpty(companySealPos) || ArrayUtil.isEmpty(personSignaturePos)) {
            throw new BusinessException(ContractExceptionEnum.CONTRACT_TEMPLATE_CONFIGURATION_ERR.getCode(), ContractExceptionEnum.CONTRACT_TEMPLATE_CONFIGURATION_ERR.getValue());
        }

        ContractSignStartRequestDTO contractSignStartRequest = new ContractSignStartRequestDTO();
        // 启动流程基本信息
        ContractSignStartRequestDTO.InnerFlowInfo innerFlowInfo = new ContractSignStartRequestDTO.InnerFlowInfo();
        innerFlowInfo.setBusinessScene("月子" + TemplateContractTypeEnum.ENTRUST.desc() + "附件合同签订流程发起");
        ContractSignStartRequestDTO.InnerFlowInfo.InnerFlowConfigInfo flowConfigInfo = new ContractSignStartRequestDTO.InnerFlowInfo.InnerFlowConfigInfo();

        // 在此业务场景下不需要发短信或邮件通知客户，所以此字段直接默认"",必须要填e签宝文档规定
        flowConfigInfo.setNoticeType("");
        flowConfigInfo.setBatchDropSeal(false);
        flowConfigInfo.setNoticeDeveloperUrl(eSignPropertiesConfig.getMainCallbackUrl());
//        flowConfigInfo.setSignPlatform("1,2");//h5微信和支付宝都兼容
        List<String> list = Lists.newArrayList();
        List<String> newList = Lists.newArrayList();
        //合同签订入口0=未知;1=h5;2=微信;3=支付宝
        Integer signFrom = 0;
        if (10 == signContractQuery.getContractMode()) {
            signFrom = 2;
            // 个人刷脸认证
            list.add("PSN_FACEAUTH_BYURL");
            // 个人运营商三要素认证
            list.add("PSN_TELECOM_AUTHCODE");
            // 个人银行卡四要素认证
            list.add("PSN_BANK4_AUTHCODE");
            // 指定个人页面显示的认证方式
            flowConfigInfo.setPersonAvailableAuthTypes(list);
            flowConfigInfo.setSignPlatform("1");//1,2 h5微信和支付宝都兼容
            // 指定意愿微信小程序刷脸
            newList.add("FACE_WE_CHAT_FACE");
            // 指定意愿短信验证
            newList.add("CODE_SMS");
            flowConfigInfo.setWillTypes(newList);
        } else if (ArrayUtil.contains(new int[]{20, 30}, signContractQuery.getContractMode())) {
            signFrom = 2;
            flowConfigInfo.setSignPlatform("1");//1,2 h5微信和支付宝都兼容
            //入口支付宝 todo 有可能通过浏览器短信验证签署
            if (30 == signContractQuery.getContractMode()) {
                signFrom = 3;
                flowConfigInfo.setSignPlatform("2");//1,2 h5微信和支付宝都兼容
            }
            // 个人刷脸认证
            list.add("PSN_FACEAUTH_BYURL");
            // 个人运营商三要素认证
            list.add("PSN_TELECOM_AUTHCODE");
            // 个人银行卡四要素认证
            list.add("PSN_BANK4_AUTHCODE");
            // 指定个人页面显示的认证方式
            flowConfigInfo.setPersonAvailableAuthTypes(list);
            // 指定意愿腾讯云刷脸
            newList.add("FACE_TECENT_CLOUD_H5");
            // 指定意愿支付宝刷脸
            newList.add("FACE_ZHIMA_XY");
            // 指定意愿短信验证
            newList.add("CODE_SMS");
            flowConfigInfo.setWillTypes(newList);
        }
        innerFlowInfo.setFlowConfigInfo(flowConfigInfo);
        contractSignStartRequest.setFlowInfo(innerFlowInfo);

        // 乙方企业签署方信息
        List<ContractSignStartRequestDTO.InnerSigners> signersList = new ArrayList<>();
        ContractSignStartRequestDTO.InnerSigners signers = new ContractSignStartRequestDTO.InnerSigners();
        ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount signerAccount = new ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount();
        // 甲方企业账号在合同合规初版只使用总部账号，各门店账号暂不用，所以此处写死
        signerAccount.setAuthorizedAccountId(eSignPropertiesConfig.getCompanyAccountId());
        signers.setSignerAccount(signerAccount);
        signers.setPlatformSign(true);
        List<ContractSignStartRequestDTO.InnerSigners.InnerSignfields> innerSignfieldsList = new ArrayList<>();
        ContractSignStartRequestDTO.InnerSigners.InnerSignfields innerSignfields = new ContractSignStartRequestDTO.InnerSigners.InnerSignfields();
        innerSignfields.setAutoExecute(true);
        innerSignfields.setActorIndentityType(2);

        innerSignfields.setFileId(signRecord.getEsignFileId());

        ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean innerPosBean = new ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean();

        // 0420 使用数据库中签章位置
        innerPosBean.setPosPage(esignTemplate.getPosPage());
        innerPosBean.setPosX(Integer.valueOf(companySealPos[0]));
        innerPosBean.setPosY(Integer.valueOf(companySealPos[1]));

        innerSignfields.setPosBean(innerPosBean);
        innerSignfieldsList.add(innerSignfields);
        signers.setSignfields(innerSignfieldsList);
        signersList.add(signers);

        // 甲方个人签署信息
        ContractSignStartRequestDTO.InnerSigners personSigners = new ContractSignStartRequestDTO.InnerSigners();
        ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount personSignerAccount = new ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount();
        personSignerAccount.setSignerAccount(userEsign.getEsignUserId());
        personSigners.setSignerAccount(personSignerAccount);
        List<ContractSignStartRequestDTO.InnerSigners.InnerSignfields> personInnerSignfieldsList = new ArrayList<>();
        ContractSignStartRequestDTO.InnerSigners.InnerSignfields personInnerSignfields = new ContractSignStartRequestDTO.InnerSigners.InnerSignfields();
        personInnerSignfields.setAutoExecute(false);
        personInnerSignfields.setFileId(signRecord.getEsignFileId());
        personInnerSignfields.setSealType("0");
        personSigners.setPlatformSign(false);
        ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean personInnerPosBean = new ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean();

        // 0420 使用数据库中签名位置
        personInnerPosBean.setPosPage(esignTemplate.getPosPage());
        personInnerPosBean.setPosX(Integer.valueOf(personSignaturePos[0]));
        personInnerPosBean.setPosY(Integer.valueOf(personSignaturePos[1]));

        personInnerSignfields.setPosBean(personInnerPosBean);
        personInnerSignfieldsList.add(personInnerSignfields);
        personSigners.setSignfields(personInnerSignfieldsList);
        signersList.add(personSigners);
        contractSignStartRequest.setSigners(signersList);
        // 待签署文件信息
        List<ContractSignStartRequestDTO.InnerDocs> docsList = new ArrayList<>();
        ContractSignStartRequestDTO.InnerDocs innerDocs = new ContractSignStartRequestDTO.InnerDocs();
        innerDocs.setFileId(signRecord.getEsignFileId());
        innerDocs.setFileName(signRecord.getEsignFileName());
        docsList.add(innerDocs);
        contractSignStartRequest.setDocs(docsList);
        //todo 撤销旧的合同流程
        if (StringUtils.isNotBlank(signRecord.getEsignFlowId())) {
            eSignProvider.cancelSignFlow(signRecord.getEsignFlowId());
        }
        // 获取主合同签订启动流程id
        String signContractFlowId = eSignProvider.doInitiateSigningProcess(contractSignStartRequest);
        log.info("=========================================res:{}", "获取月子" + TemplateContractTypeEnum.ENTRUST.desc() + "签订启动流程id" + signContractFlowId);
        // 并保存至数据库，如果合同流程启动，却因其他原因未签署合同，想再次签署合同时直接使用这个流程id就可以获取签署链接
        // 不必再重新发起流程，重新再发起流程会重复扣合同费用
        signRecord.setEsignFileId(signRecord.getEsignFileId());
        signRecord.setEsignFlowId(signContractFlowId);
        signRecord.setSignFrom(signFrom);
        signRecord.setUpdatedAt(DateUtils.getTenBitTimestamp());
        monthContractSignRecordRepository.updateBySignRecordId(signRecord);

        SignAddressResponseDTO signAddress = eSignProvider.getSignAddress(userEsign.getEsignUserId(), signContractFlowId);
        signRecord.setContractLongUrl(signAddress.getLongUrl());
        signRecord.setContractShortUrl(signAddress.getShortUrl());
        signRecord.setUpdatedAt(DateUtils.getTenBitTimestamp());
        monthContractSignRecordRepository.updateBySignRecordId(signRecord);
        log.info("=========================================res:{}", "月子" + TemplateContractTypeEnum.DISCOUNT.desc() + "查看地址已更新至数据库");
        if (Objects.equals(ContractModeEnum.NOW_FACE_TYPE.code(), signContractQuery.getContractMode())) {
            contractSignAddress.setContractSignAddress(signAddress.getLongUrl());
            return contractSignAddress;
        }
        contractSignAddress.setContractSignAddress(signAddress.getShortUrl());
        return contractSignAddress;
    }


    /**
     * 提前离管合同解除协议 甲方是 客户 乙方是 公司
     *
     * @param signContractQuery
     * @param signRecord
     * @param userEsign
     * @return {@link ContractSignAddressVO}
     */
    public ContractSignAddressVO releaseAssembler(SignContractQuery signContractQuery, MonthContractSignRecordEntity signRecord, MonthUserEsignDTO userEsign) {
        ContractSignAddressVO contractSignAddress = signAddressAssembler(signContractQuery, signRecord);
        // 通过sign record 的模版ID再获取模版信息
        MonthEsignTemplateVO esignTemplate = monthEsignTemplateService.getByTemplateId(signRecord.getTemplateId());
        // 获取主合同的签章位置
        if (StringUtils.isAnyBlank(esignTemplate.getCompanySealPos(), esignTemplate.getPersonSignaturePos()) || esignTemplate.getPosPage().equals(0)) {
            throw new BusinessException(ContractExceptionEnum.CONTRACT_TEMPLATE_CONFIGURATION_ERR.getCode(), ContractExceptionEnum.CONTRACT_TEMPLATE_CONFIGURATION_ERR.getValue());
        }
        String[] companySealPos = esignTemplate.getCompanySealPos().split(",");
        String[] personSignaturePos = esignTemplate.getPersonSignaturePos().split(",");
        if (ArrayUtil.isEmpty(companySealPos) || ArrayUtil.isEmpty(personSignaturePos)) {
            throw new BusinessException(ContractExceptionEnum.CONTRACT_TEMPLATE_CONFIGURATION_ERR.getCode(), ContractExceptionEnum.CONTRACT_TEMPLATE_CONFIGURATION_ERR.getValue());
        }

        ContractSignStartRequestDTO contractSignStartRequest = new ContractSignStartRequestDTO();
        // 启动流程基本信息
        ContractSignStartRequestDTO.InnerFlowInfo innerFlowInfo = new ContractSignStartRequestDTO.InnerFlowInfo();
        innerFlowInfo.setBusinessScene("月子" + TemplateContractTypeEnum.ENTRUST.desc() + "附件合同签订流程发起");
        ContractSignStartRequestDTO.InnerFlowInfo.InnerFlowConfigInfo flowConfigInfo = new ContractSignStartRequestDTO.InnerFlowInfo.InnerFlowConfigInfo();

        // 在此业务场景下不需要发短信或邮件通知客户，所以此字段直接默认"",必须要填e签宝文档规定
        flowConfigInfo.setNoticeType("");
        flowConfigInfo.setBatchDropSeal(false);
        flowConfigInfo.setNoticeDeveloperUrl(eSignPropertiesConfig.getMainCallbackUrl());
//        flowConfigInfo.setSignPlatform("1,2");//h5微信和支付宝都兼容
        List<String> list = Lists.newArrayList();
        List<String> newList = Lists.newArrayList();
        //合同签订入口0=未知;1=h5;2=微信;3=支付宝
        Integer signFrom = 0;
        if (10 == signContractQuery.getContractMode()) {
            signFrom = 2;
            // 个人刷脸认证
            list.add("PSN_FACEAUTH_BYURL");
            // 个人运营商三要素认证
            list.add("PSN_TELECOM_AUTHCODE");
            // 个人银行卡四要素认证
            list.add("PSN_BANK4_AUTHCODE");
            // 指定个人页面显示的认证方式
            flowConfigInfo.setPersonAvailableAuthTypes(list);
            flowConfigInfo.setSignPlatform("1");//1,2 h5微信和支付宝都兼容
            // 指定意愿微信小程序刷脸
            newList.add("FACE_WE_CHAT_FACE");
            // 指定意愿短信验证
            newList.add("CODE_SMS");
            flowConfigInfo.setWillTypes(newList);
        } else if (ArrayUtil.contains(new int[]{20, 30}, signContractQuery.getContractMode())) {
            signFrom = 2;
            flowConfigInfo.setSignPlatform("1");//1,2 h5微信和支付宝都兼容
            //入口支付宝 todo 有可能通过浏览器短信验证签署
            if (30 == signContractQuery.getContractMode()) {
                signFrom = 3;
                flowConfigInfo.setSignPlatform("2");//1,2 h5微信和支付宝都兼容
            }
            // 个人刷脸认证
            list.add("PSN_FACEAUTH_BYURL");
            // 个人运营商三要素认证
            list.add("PSN_TELECOM_AUTHCODE");
            // 个人银行卡四要素认证
            list.add("PSN_BANK4_AUTHCODE");
            // 指定个人页面显示的认证方式
            flowConfigInfo.setPersonAvailableAuthTypes(list);
            // 指定意愿腾讯云刷脸
            newList.add("FACE_TECENT_CLOUD_H5");
            // 指定意愿支付宝刷脸
            newList.add("FACE_ZHIMA_XY");
            // 指定意愿短信验证
            newList.add("CODE_SMS");
            flowConfigInfo.setWillTypes(newList);
        }
        innerFlowInfo.setFlowConfigInfo(flowConfigInfo);
        contractSignStartRequest.setFlowInfo(innerFlowInfo);

        // 乙方企业签署方信息
        List<ContractSignStartRequestDTO.InnerSigners> signersList = new ArrayList<>();
        ContractSignStartRequestDTO.InnerSigners signers = new ContractSignStartRequestDTO.InnerSigners();
        ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount signerAccount = new ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount();
        // 甲方企业账号在合同合规初版只使用总部账号，各门店账号暂不用，所以此处写死
        signerAccount.setAuthorizedAccountId(eSignPropertiesConfig.getCompanyAccountId());
        signers.setSignerAccount(signerAccount);
        signers.setPlatformSign(true);
        List<ContractSignStartRequestDTO.InnerSigners.InnerSignfields> innerSignfieldsList = new ArrayList<>();
        ContractSignStartRequestDTO.InnerSigners.InnerSignfields innerSignfields = new ContractSignStartRequestDTO.InnerSigners.InnerSignfields();
        innerSignfields.setAutoExecute(true);
        innerSignfields.setActorIndentityType(2);

        innerSignfields.setFileId(signRecord.getEsignFileId());

        ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean innerPosBean = new ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean();

        // 0420 使用数据库中签章位置
        innerPosBean.setPosPage(esignTemplate.getPosPage());
        innerPosBean.setPosX(Integer.valueOf(companySealPos[0]));
        innerPosBean.setPosY(Integer.valueOf(companySealPos[1]));

        innerSignfields.setPosBean(innerPosBean);
        innerSignfieldsList.add(innerSignfields);
        signers.setSignfields(innerSignfieldsList);
        signersList.add(signers);

        // 甲方个人签署信息
        ContractSignStartRequestDTO.InnerSigners personSigners = new ContractSignStartRequestDTO.InnerSigners();
        ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount personSignerAccount = new ContractSignStartRequestDTO.InnerSigners.InnerSignerAccount();
        personSignerAccount.setSignerAccount(userEsign.getEsignUserId());
        personSigners.setSignerAccount(personSignerAccount);
        List<ContractSignStartRequestDTO.InnerSigners.InnerSignfields> personInnerSignfieldsList = new ArrayList<>();
        ContractSignStartRequestDTO.InnerSigners.InnerSignfields personInnerSignfields = new ContractSignStartRequestDTO.InnerSigners.InnerSignfields();
        personInnerSignfields.setAutoExecute(false);
        personInnerSignfields.setFileId(signRecord.getEsignFileId());
        personInnerSignfields.setSealType("0");
        personSigners.setPlatformSign(false);
        ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean personInnerPosBean = new ContractSignStartRequestDTO.InnerSigners.InnerSignfields.InnerPosBean();

        // 0420 使用数据库中签名位置
        personInnerPosBean.setPosPage(esignTemplate.getPosPage());
        personInnerPosBean.setPosX(Integer.valueOf(personSignaturePos[0]));
        personInnerPosBean.setPosY(Integer.valueOf(personSignaturePos[1]));

        personInnerSignfields.setPosBean(personInnerPosBean);
        personInnerSignfieldsList.add(personInnerSignfields);
        personSigners.setSignfields(personInnerSignfieldsList);
        signersList.add(personSigners);
        contractSignStartRequest.setSigners(signersList);
        // 待签署文件信息
        List<ContractSignStartRequestDTO.InnerDocs> docsList = new ArrayList<>();
        ContractSignStartRequestDTO.InnerDocs innerDocs = new ContractSignStartRequestDTO.InnerDocs();
        innerDocs.setFileId(signRecord.getEsignFileId());
        innerDocs.setFileName(signRecord.getEsignFileName());
        docsList.add(innerDocs);
        contractSignStartRequest.setDocs(docsList);
        //todo 撤销旧的合同流程
        if (StringUtils.isNotBlank(signRecord.getEsignFlowId())) {
            eSignProvider.cancelSignFlow(signRecord.getEsignFlowId());
        }
        // 获取主合同签订启动流程id
        String signContractFlowId = eSignProvider.doInitiateSigningProcess(contractSignStartRequest);
        log.info("=========================================res:{}", "获取" + "月子" + TemplateContractTypeEnum.ENTRUST.desc() + "签订启动流程id" + signContractFlowId);
        // 并保存至数据库，如果合同流程启动，却因其他原因未签署合同，想再次签署合同时直接使用这个流程id就可以获取签署链接
        // 不必再重新发起流程，重新再发起流程会重复扣合同费用
        signRecord.setEsignFileId(signRecord.getEsignFileId());
        signRecord.setEsignFlowId(signContractFlowId);
        signRecord.setSignFrom(signFrom);
        signRecord.setUpdatedAt(DateUtils.getTenBitTimestamp());
        monthContractSignRecordRepository.updateBySignRecordId(signRecord);

        SignAddressResponseDTO signAddress = eSignProvider.getSignAddress(userEsign.getEsignUserId(), signContractFlowId);
        signRecord.setContractLongUrl(signAddress.getLongUrl());
        signRecord.setContractShortUrl(signAddress.getShortUrl());
        signRecord.setUpdatedAt(DateUtils.getTenBitTimestamp());
        monthContractSignRecordRepository.updateBySignRecordId(signRecord);
        log.info("=========================================res:{}", "月子" + TemplateContractTypeEnum.ENTRUST.desc() + "查看地址已更新至数据库");
        if (Objects.equals(ContractModeEnum.NOW_FACE_TYPE.code(), signContractQuery.getContractMode())) {
            contractSignAddress.setContractSignAddress(signAddress.getLongUrl());
            return contractSignAddress;
        }
        contractSignAddress.setContractSignAddress(signAddress.getShortUrl());
        return contractSignAddress;
    }


    /**
     * 合同撤销
     *
     * @param eSignFlowRecordReqList
     * @return {@link List}<{@link ESignCancleFlowRecordVO}>
     */
    public List<ESignCancleFlowRecordVO> cancleFlowRecord(List<ESignFlowRecordReq> eSignFlowRecordReqList) {
        log.info("合同撤销 req = {}", JSONUtil.toJsonStr(eSignFlowRecordReqList));
        List<String> esignFlowIds = eSignFlowRecordReqList.stream().map(ESignFlowRecordReq::getEsignFlowId).collect(Collectors.toList());

        List<MonthContractSignRecordEntity> contractSignRecordEntities = monthContractSignRecordRepository.queryByEsignFlowIds(esignFlowIds);

        List<ESignCancleFlowRecordVO> eSignCancleFlowRecordVOList = org.apache.commons.compress.utils.Lists.newArrayList();

        contractSignRecordEntities.forEach(signRecord -> {
            try {
                ESignCancleFlowRecordVO eSignCancleFlowRecordVO = new ESignCancleFlowRecordVO();
                if (StringUtils.isNotBlank(signRecord.getEsignFlowId()) && !signRecord.getContractStatus().equals(ContractStatusEnum.SIGNED.code())) {
                    eSignProvider.cancelSignFlow(signRecord.getEsignFlowId());

                    eSignCancleFlowRecordVO.setEsignFlowId(signRecord.getEsignFlowId());
                    eSignCancleFlowRecordVO.setIsSuccess(true);
                    eSignCancleFlowRecordVO.setMsg("成功");
                    eSignCancleFlowRecordVOList.add(eSignCancleFlowRecordVO);

                    signRecord.setEsignFlowId("");
                    signRecord.setContractStatus(com.stbella.contract.model.enums.ContractStatusEnum.NOT_SIGNED.code());
                    signRecord.setUpdatedAt(System.currentTimeMillis() / 1000);
                    monthContractSignRecordRepository.updateBySignRecordId(signRecord);

                }
            } catch (BusinessException e) {
                ESignCancleFlowRecordVO eSignCancleFlowRecordVO = new ESignCancleFlowRecordVO();
                eSignCancleFlowRecordVO.setEsignFlowId(signRecord.getEsignFlowId());
                eSignCancleFlowRecordVO.setIsSuccess(false);
                eSignCancleFlowRecordVO.setMsg(e.getMessage());
                eSignCancleFlowRecordVOList.add(eSignCancleFlowRecordVO);
            }
        });

        return eSignCancleFlowRecordVOList;
    }

    /**
     * 合同撤销
     *
     * @param eSignFlowRecordReqList
     * @return {@link List}<{@link ESignCancleFlowRecordVO}>
     */
    public List<ESignCancleFlowRecordVO> cancleSignFlowsAgreementTable(List<ESignFlowRecordReq> eSignFlowRecordReqList) {
        log.info("补充协议合同撤销 req = {}", JSONUtil.toJsonStr(eSignFlowRecordReqList));
        List<String> esignFlowIds = eSignFlowRecordReqList.stream().map(ESignFlowRecordReq::getEsignFlowId).collect(Collectors.toList());

        List<MonthContractSignAgreementEntity> monthContractSignAgreementEntities = monthContractSignAgreementRepository.queryByEsignFlowIds(esignFlowIds);

        List<ESignCancleFlowRecordVO> eSignCancleFlowRecordVOList = org.apache.commons.compress.utils.Lists.newArrayList();

        monthContractSignAgreementEntities.forEach(signRecord -> {
            try {
                ESignCancleFlowRecordVO eSignCancleFlowRecordVO = new ESignCancleFlowRecordVO();
                if (StringUtils.isNotBlank(signRecord.getEsignFlowId()) && !signRecord.getState().equals(ContractStatusEnum.SIGNED.code())) {
                    eSignProvider.cancelSignFlow(signRecord.getEsignFlowId());

                    eSignCancleFlowRecordVO.setEsignFlowId(signRecord.getEsignFlowId());
                    eSignCancleFlowRecordVO.setIsSuccess(true);
                    eSignCancleFlowRecordVO.setMsg("成功");
                    eSignCancleFlowRecordVOList.add(eSignCancleFlowRecordVO);

                    signRecord.setEsignFlowId("");
                    signRecord.setState(com.stbella.contract.model.enums.ContractStatusEnum.NOT_SIGNED.code());
                    monthContractSignAgreementRepository.updateMonthContractSignAgreement(signRecord);

                }
            } catch (BusinessException e) {
                ESignCancleFlowRecordVO eSignCancleFlowRecordVO = new ESignCancleFlowRecordVO();
                eSignCancleFlowRecordVO.setEsignFlowId(signRecord.getEsignFlowId());
                eSignCancleFlowRecordVO.setIsSuccess(false);
                eSignCancleFlowRecordVO.setMsg(e.getMessage());
                eSignCancleFlowRecordVOList.add(eSignCancleFlowRecordVO);
            }
        });

        return eSignCancleFlowRecordVOList;
    }
}

package com.stbella.order.server.manager;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.contract.api.ContractSignAgreementV3Service;
import com.stbella.contract.api.ContractSignRecordPaperService;
import com.stbella.contract.api.ContractSignRecordService;
import com.stbella.contract.api.OrderParamHistoryService;
import com.stbella.contract.api.v4.ContractCommandV4Service;
import com.stbella.contract.api.v4.ContractQueryV4Service;
import com.stbella.contract.model.enums.ContractTypeEnum;
import com.stbella.contract.model.req.ContractProcessNewReq;
import com.stbella.contract.model.req.ContractQueryV4;
import com.stbella.contract.model.req.ContractSignRecordReq;
import com.stbella.contract.model.req.v3.ContractCreateV3Req;
import com.stbella.contract.model.res.ContractSignAgreementVO;
import com.stbella.contract.model.res.ContractSignRecordVO2;
import com.stbella.contract.model.res.ContractTemplateRes;
import com.stbella.core.result.Result;
import com.stbella.core.utils.CopyBeanUtil;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.server.contract.req.OrderParamHistoryPushDTO;
import com.stbella.order.server.convert.AppMonthContractSignRecordConverter;
import com.stbella.platform.order.api.contract.req.ContractBaseReq;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 合同调用
 * @date 2023-11-22
 */
@Component
@Slf4j
public class ContractManager {

    @DubboReference
    private ContractSignRecordPaperService contractSignRecordPaperService;

    @DubboReference
    private OrderParamHistoryService monthOrderParamHistoryService;

    @DubboReference
    private ContractSignAgreementV3Service contractSignAgreementV3Service;

    @DubboReference
    private ContractSignRecordService contractSignRecordService;

    @DubboReference
    private ContractQueryV4Service contractQueryV4Service;

    @DubboReference
    private ContractCommandV4Service contractCommandV4Service;

    @Resource
    private AppMonthContractSignRecordConverter appMonthContractSignRecordConverter;




    public static final List<Integer> CONTRACT_TYPE_LIST = ListUtil.toList(ContractTypeEnum.ESIGN_TYPE.code(), ContractTypeEnum.OLD_TYPE.code(), ContractTypeEnum.PAPER_TYPE.code());
    public static final List<Integer> MAIN_CONTRACT = Arrays.asList(com.stbella.contract.model.enums.TemplateContractTypeEnum.YZ_SAINTBELLA.getCode(), com.stbella.contract.model.enums.TemplateContractTypeEnum.YZ_NURSE.getCode(), com.stbella.contract.model.enums.TemplateContractTypeEnum.YZ_SMALL.getCode());;


    /**
     * @description: 获取纸质合同信息
     */
    public com.stbella.contract.model.res.ContractSignPaperVO queryById(Integer id) {
        Result<com.stbella.contract.model.res.ContractSignPaperVO> contractSignPaperVOResult = contractSignRecordPaperService.queryById(id);
        return contractSignPaperVOResult.getData();
    }

    /**
     * @description: 修改纸质合同签署状态
     */
    public void updateStatusById(Integer id, Integer status) {
        contractSignRecordPaperService.updateStatusById(id, status);
    }


    /**
     * 订单推送合同变更记录
     *
     * @param dto
     * @return {@link Boolean}
     */
    public Boolean pushOrderParamHistory(OrderParamHistoryPushDTO dto) {
        try {
            log.info("订单服务推送=>订单创建or修改参数推送记录{}", JSONUtil.toJsonStr(dto));
            com.stbella.contract.model.req.OrderParamHistoryPushDTO orderParamHistoryPushDTO = CopyBeanUtil.copy(dto, com.stbella.contract.model.req.OrderParamHistoryPushDTO.class);
            return monthOrderParamHistoryService.pushOrderParamHistory(orderParamHistoryPushDTO);
        } catch (Exception e) {
            log.error("订单服务推送，保存主要参数信息发生异常, req:{}, msg:{}, e:{}", JSONUtil.toJsonStr(dto), e.getMessage(), e);
            return Boolean.FALSE;
        }
    }




    /**
     * 获取订单下的合同列表
     *
     * @param orderId
     * @return
     */
    public List<ContractSignRecordVO2> getContractSignRecordListByOrderId(Long orderId) {
        ContractQueryV4 contractQueryV4 = new ContractQueryV4();
        contractQueryV4.setGuideIds(Collections.singletonList(orderId.intValue()));
        return contractQueryV4Service.querySignRecordList(contractQueryV4);
    }

    /**
     * 获取所有的补充协议
     *
     * @param orderId
     * @return
     */
    public List<ContractSignAgreementVO> getAgreementByOrderId(Long orderId) {
        return contractQueryV4Service.getAgreementByOrderId(orderId);
    }

    public OrderContractSignRecordVO queryContractSignRecordVo(ContractBaseReq req) {
        ContractSignRecordReq recordReq = appMonthContractSignRecordConverter.baseReq2SignRecordReq(req);
        return appMonthContractSignRecordConverter.signRecord2OrderSign(contractQueryV4Service.queryContractSignRecordVoByReq(recordReq));
    }

    /**
     * 撤销合同
     *
     * @param contractId
     */
    public void cancelContract(Long contractId) {
        contractCommandV4Service.cancelContract(contractId);
    }

    /**
     * 返回对应的合同模板参数
     *
     * @return
     */
    public ContractTemplateRes getContractTemplateParam(Long contractTemplateId) {
        return contractQueryV4Service.getContractTemplateParam(contractTemplateId);
    }

    /**
     * 创建合同
     *
     * @param contractProcessReq
     * @return
     */
    public Result<ContractSignRecordVO2> createContract(ContractCreateV3Req contractProcessReq) {
        return contractCommandV4Service.createContract(contractProcessReq);
    }

    public List<ContractSignRecordVO2> getContractSignRecordListByOrderIdList(List<Long> heOrderList) {
        return contractQueryV4Service.getContractSignRecordListByOrderIdList(heOrderList);
    }

    public List<OrderContractSignRecordVO> getContractSignRecordAndAgreementListByOrderIdList(List<Long> orderIdList) {
        List<ContractSignRecordVO2> contractSignRecordAndAgreementListByOrderIdList = contractQueryV4Service.getContractSignRecordAndAgreementListByOrderIdList(orderIdList);
        return appMonthContractSignRecordConverter.signRecordList2OrderSign(contractSignRecordAndAgreementListByOrderIdList);
    }

    public List<OrderContractSignRecordVO> queryByV4(ContractQueryV4 contractQueryV4) {
        return appMonthContractSignRecordConverter.signRecordList2OrderSign(contractQueryV4Service.querySignRecordList(contractQueryV4));
    }

    public Boolean delectByContractIdList(List<Long> collect) {
        return contractCommandV4Service.deleteContract(collect);
    }

    public OrderContractSignRecordVO getContractSignRecord(Long contractId) {
        ContractSignRecordVO2 contractSignRecordVO2 = contractQueryV4Service.queryContractSignRecordVo(contractId);
        return appMonthContractSignRecordConverter.signRecord2OrderSign(contractSignRecordVO2);
    }

    public OrderContractSignRecordVO getAgreementById(Integer contractId) {
        return appMonthContractSignRecordConverter.signRecord2OrderSign(contractQueryV4Service.queryAgreementById(contractId));
    }

    public void cancelSupplement(Long id) {
        contractCommandV4Service.cancelSupplement(id);
    }

    public ContractSignRecordVO2 getContractPreviewUrl(ContractSignRecordReq contractSignRecordReq) {
        return contractQueryV4Service.getContractPreviewUrl(contractSignRecordReq).getData();
    }
}

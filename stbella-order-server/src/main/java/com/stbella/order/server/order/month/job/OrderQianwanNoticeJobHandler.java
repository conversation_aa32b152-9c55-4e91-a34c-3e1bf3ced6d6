package com.stbella.order.server.order.month.job;

import cn.hutool.core.date.DateUtil;
import com.stbella.core.result.Result;
import com.stbella.order.server.order.month.constant.BaseConstant;
import com.stbella.order.server.order.month.service.MonthOrderAdminCommandService;
import com.stbella.redisson.RedissonDistributedLocker;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * 订单千万报单定时任务
 * @date 2023/4/23 18:13
 */
@Component
@Slf4j
public class OrderQianwanNoticeJobHandler {
    @Resource
    private MonthOrderAdminCommandService monthOrderAdminCommandService;
    @Resource
    private RedissonDistributedLocker locker;

    /**
     * 订单千万报单定时任务
     */
    @XxlJob("orderQianwanNotice")
    public void orderQianwanNotice() {

        try {
            boolean b = locker.tryLock(BaseConstant.ORDER_QIANWAN_NOTICE_KEY, TimeUnit.SECONDS, 60, 60);
            if (b) {
                //获取当前时间年和月
                int year = DateUtil.year(new Date());
                int month = DateUtil.month(new Date()) + 1;
                log.info("订单千万报单定时任务开始,year={},month={}", year, month);
                XxlJobHelper.log("订单千万报单定时任务开始,year={},month={}", year, month);
                Result<String> stringResult = monthOrderAdminCommandService.orderQianwanNotice(year, month);
                log.info("订单千万报单定时任务结束,result={}", stringResult.getData());
                XxlJobHelper.log("订单千万报单定时任务结束,result={}", stringResult.getData());
            }
        } catch (Exception e) {
            log.info("订单千万报单定时任务任务异常,e={}", e.getMessage());
            XxlJobHelper.log("订单千万报单定时任务任务异常,e={}", e.getMessage());
        } finally {
            locker.unlock(BaseConstant.ORDER_QIANWAN_NOTICE_KEY);
        }
    }

}

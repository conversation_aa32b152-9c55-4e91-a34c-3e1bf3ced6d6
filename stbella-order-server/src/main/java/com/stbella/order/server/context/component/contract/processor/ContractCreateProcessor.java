package com.stbella.order.server.context.component.contract.processor;

import com.stbella.contract.model.req.v3.ContractCreateV3Req;
import com.stbella.contract.model.res.ContractSignRecordVO2;
import com.stbella.order.server.convert.AppMonthContractSignRecordConverter;
import com.stbella.order.server.manager.ContractManager;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

/**
 * 合同创建
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "合同创建")
public class ContractCreateProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    ContractManager contractManager;
    @Resource
    private AppMonthContractSignRecordConverter appMonthContractSignRecordConverter;

    @Override
    public void run(FlowContext bizContext) {
        ContractCreateV3Req contractCreateV3Req = bizContext.getAttribute(ContractCreateV3Req.class);
        //查找合同模版对应的参数
        ContractSignRecordVO2 contract = contractManager.createContract(contractCreateV3Req).getData();
        OrderContractSignRecordVO orderContractSignRecordVO = appMonthContractSignRecordConverter.signRecord2OrderSign(contract);
        bizContext.setAttribute(OrderContractSignRecordVO.class, orderContractSignRecordVO);
        bizContext.setAttribute(ContractSignRecordVO2.class, contract);
    }


}

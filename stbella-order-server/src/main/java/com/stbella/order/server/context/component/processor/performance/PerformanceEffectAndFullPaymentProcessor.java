package com.stbella.order.server.context.component.processor.performance;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.month.PayStatusV2Enum;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderNoticeLogEntity;
import com.stbella.order.domain.order.service.OrderIncomeDomainService;
import com.stbella.order.domain.repository.OrderNoticeLogRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.order.month.enums.OperationTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-07-11  10:00
 * @Description: 业绩生效及全额支付
 * 业绩生效规则
 * 1,自动：累计已支付金额大于等于签单金额的 1/2 生效
 * 2，手动：后台手动发起
 * <p>
 * 全额支付规则：
 * 累计支付金额 = O签单 - 减免 + 退回重付
 */
@Component
@Slf4j
@SnowballComponent(name = "业绩生效及全额支付", desc = "根据累计累计支付记录总额，订单业绩生效及订单全额支付")
public class PerformanceEffectAndFullPaymentProcessor implements IExecutableAtom<FlowContext> {

    /**
     * 累计已支付 * rate >= 签单金额
     */
    private static final Integer rate = 2;
    @Resource
    OrderIncomeDomainService incomeDomainService;
    @Resource
    OrderRepository orderRepository;
    @Resource
    OrderNoticeLogRepository orderNoticeLogRepository;


    @Override
    public void run(FlowContext bizContext) {
        log.info("业绩生效");
        HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);

        Integer percentFirstTime = order.getPercentFirstTime();
        if (ObjectUtil.isEmpty(percentFirstTime)) {
            percentFirstTime = 0;
        }
        int totalPayAmount = order.calPayable();
        List<HeIncomeRecordEntity> successfulRecords = incomeDomainService.queryEffectiveRecord(order);
        if (!successfulRecords.isEmpty()) {
            order.setPayFirstTime(successfulRecords.get(successfulRecords.size() - 1).getPayTime());
            order.firstPay();
        }
        Integer sumPaidForPerformance = successfulRecords.stream().mapToInt(HeIncomeRecordEntity::getIncome).sum() - successfulRecords.stream().mapToInt(HeIncomeRecordEntity::getAlreadyRefundAmount).sum();

        if (order.getOperationType().intValue() == OperationTypeEnum.DELETED.getCode() && percentFirstTime <= 0 && sumPaidForPerformance * rate >= totalPayAmount) {
            if (CollectionUtil.isNotEmpty(successfulRecords)) {
                order.setPercentFirstTime(successfulRecords.get(0).getPayTime());
                order.effectPerformance();
                log.info("业绩生效 orderSn={}, 生效了吗：{}", order.getOrderSn(), order.isJustEffect());
            }
        }

        if (order.getOperationType().intValue() == OperationTypeEnum.RECOVER.getCode() && percentFirstTime <= 0 && sumPaidForPerformance * rate >= totalPayAmount) {
            //已经剔除，成功的时间记录到bk里
            HeOrderEntity updateModel = new HeOrderEntity();
            updateModel.setOrderId(order.getOrderId());
            if (CollectionUtil.isNotEmpty(successfulRecords)) {
                updateModel.setBkPercentFirstTime(successfulRecords.get(0).getPayTime());
            } else {
                updateModel.setBkPercentFirstTime(0);
            }
            orderRepository.updateOrderMonthByOrderId(updateModel);
        }

        // 原来业绩生效，线下支付拒绝后，业绩可能不生效了。
        if (percentFirstTime > 0 && sumPaidForPerformance * rate < totalPayAmount) {
            HeOrderNoticeLogEntity byOrderId = orderNoticeLogRepository.getByOrderId(order.getOrderId());
            if (Objects.isNull(byOrderId)) {
                log.info("订单id:{}，sn:{}，业绩生效后，线下支付拒绝，业绩不生效", order.getOrderId(), order.getOrderSn());
                order.invalidPerformance();
            } else {
                log.info("订单id:{}，sn:{}，手动报单过，不执行业绩失效", order.getOrderId(), order.getOrderSn());
            }
        }

        /**
         * 全额支付规则：
         * 累计支付金额 = O签单 - 减免 + 退回重付
         * 真支付
         */
        int sumProductCoinPaid = successfulRecords.stream()
                .filter(HeIncomeRecordEntity::isActualSuccess).filter(o -> OmniPayTypeEnum.PRODUCTION_COIN.getCode().equals(o.getPayType())).mapToInt(HeIncomeRecordEntity::getIncome).sum();

        int sumCashPaid = successfulRecords.stream()
                .filter(HeIncomeRecordEntity::isActualSuccess).filter(o -> !OmniPayTypeEnum.PRODUCTION_COIN.getCode().equals(o.getPayType())).mapToInt(HeIncomeRecordEntity::getIncome).sum();
        int sumRealPaid = sumProductCoinPaid + sumCashPaid;


        if (sumRealPaid >= totalPayAmount) {
            //全额支付
            log.info("订单全额支付，订单id:{}，sn:{}", order.getOrderId(), order.getOrderSn());
            order.setPaidAmount(sumRealPaid);
            order.setRealAmount(sumCashPaid);
            order.setProductionAmountPay(sumProductCoinPaid);
            Long payFinishAt = null;
            if (CollectionUtil.isNotEmpty(successfulRecords)) {
                successfulRecords.sort(Comparator.comparing(HeIncomeRecordEntity::getPayTime).reversed());
                payFinishAt = successfulRecords.get(0).getPayTime().longValue();
            }
            order.fullPayment(payFinishAt);
            order.setPayStatus(PayStatusV2Enum.PAY_OFF.getCode());
        } else if (sumRealPaid > 0) {
            //部分支付
            order.setPaidAmount(sumPaidForPerformance);
            order.setRealAmount(sumCashPaid);
            order.setProductionAmountPay(sumProductCoinPaid);
            order.partPayment();
        } else if (sumRealPaid == 0) {
            // 线下支付拒绝了
            order.setPaidAmount(sumPaidForPerformance);
            order.setRealAmount(sumCashPaid);
            order.setProductionAmountPay(sumProductCoinPaid);

            HeOrderEntity updateModel = new HeOrderEntity();
            updateModel.setOrderId(order.getOrderId());
            updateModel.setPaidAmount(sumPaidForPerformance);
            updateModel.setRealAmount(sumCashPaid);
            updateModel.setProductionAmountPay(sumProductCoinPaid);
            orderRepository.updateOrderMonthByOrderId(updateModel);

        }
        log.info("业绩生效 orerSn={}, 生效了吗：{}", order.getOrderSn(), order.isJustEffect());
        bizContext.setAttribute(HeOrderEntity.class, order);

    }


}

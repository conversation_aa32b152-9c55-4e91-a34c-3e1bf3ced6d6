package com.stbella.order.server.order.month.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.core.base.PageVO;
import com.stbella.core.base.StoreInfoDTO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.utils.sso.EmployeeTokenHelper;
import com.stbella.financial.res.MainSupplierQueryVO;
import com.stbella.financial.res.SupplierPageVO;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.core.CartSceneEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.common.enums.order.CustomerComplaintsRefundEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.repository.*;
import com.stbella.order.domain.repository.condition.StoreQueryCondition;
import com.stbella.order.server.context.component.complaints.processor.CustomerComplaintsAgreeOrderProcessor;
import com.stbella.order.server.convert.HeCustomerComplaintsServerConverter;
import com.stbella.order.server.manager.ContractManager;
import com.stbella.order.server.manager.FinanceManager;
import com.stbella.order.server.order.month.enums.CustomerComplaintsResponsibilityEnum;
import com.stbella.order.server.order.month.enums.CustomerComplaintsStatusEnum;
import com.stbella.order.server.order.month.req.SubmitRefundApplyV2Request;
import com.stbella.order.server.order.month.res.SelectRespVO;
import com.stbella.order.server.order.month.service.OrderCustomerComplaintsService;
import com.stbella.order.server.utils.JsonUtil;
import com.stbella.order.server.utils.SensitiveInformationUtil;
import com.stbella.platform.order.api.cart.CartQueryService;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import com.stbella.platform.order.api.req.*;
import com.stbella.platform.order.api.res.*;
import com.stbella.sso.base.Operator;
import com.stbella.sso.therapist.api.TherapistQueryService;
import com.stbella.sso.therapist.req.TherapistDetailReq;
import com.stbella.sso.therapist.res.TherapistSelectOptionVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.FlowIdentity;
import top.primecare.snowball.flow.core.definition.FlowIdentityBuilder;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.stbella.order.common.enums.ErrorCodeEnum.*;

@Service
@Slf4j
public class OrderCustomerComplaintsServiceImpl implements OrderCustomerComplaintsService {

    @Resource
    private HeCustomerComplaintsTypeRepository heCustomerComplaintsTypeRepository;

    @Resource
    HeCustomerComplaintsServerConverter heCustomerComplaintsServerConverter;

    @Resource
    HeCustomerComplaintsRepository heCustomerComplaintsRepository;

    @Resource
    ClientRepository clientRepository;

    @Resource
    StoreRepository storeRepository;

    @Resource
    UserRepository userRepository;

    @Resource
    OrderRepository orderRepository;

    @Resource
    FinanceManager financeManager;

    @Resource
    CartQueryService cartQueryService;

    @Resource
    private HeCustomerComplaintsRefundDraftRepository customerComplaintsRefundDraftRepository;

    @Resource
    private ContractManager contractManager;

    @Resource
    CustomerComplaintsAgreeOrderProcessor customerComplaintsAgreeOrderProcessor;

    @Resource
    private HeCustomerComplaintsRepository customerComplaintsRepository;

    @Resource
    private OrderRefundRepository orderRefundRepository;

    @DubboReference
    private TherapistQueryService therapistQueryService;

    @Override
    public Result<List<CustomerComplaintsTypeVO>> getCustomerComplaintsTypeList(Long parentId) {
        List<HeCustomerComplaintsTypeEntity> listByParentId = heCustomerComplaintsTypeRepository.getListByParentId(Collections.singletonList(parentId));
        return Result.success(heCustomerComplaintsServerConverter.customerComplaintsTypeEntityToVO(listByParentId));
    }

    @Override
    public Result<PageVO<CustomerComplaintsVO>> page(CustomerComplaintsPageReq pageReq) {

        //查询条件搜索
        String keyWord = pageReq.getKeyWord();
        if (StringUtils.isNotBlank(keyWord)) {
            List<TabClientEntity> tabClientList = clientRepository.getTabClientByNameAndPhone(keyWord);
            List<Integer> clientIdList = CollectionUtils.isNotEmpty(tabClientList) ? tabClientList.stream().map(TabClientEntity::getId).collect(Collectors.toList()) : ListUtil.toList(-1);
            if (CollectionUtils.isNotEmpty(clientIdList)) {
                List<HeCustomerComplaintsEntity> customerComplaintsEntities = customerComplaintsRepository.listByClientUid(clientIdList);
                if (CollectionUtils.isNotEmpty(customerComplaintsEntities)) {
                    pageReq.setClientUidList(clientIdList);
                }
            } else {
                pageReq.setClientUidList(null);
            }

            StoreQueryCondition condition = new StoreQueryCondition();
            condition.setName(keyWord);
            List<CfgStoreEntity> cfgStoreEntities = storeRepository.queryStoreBaseByCondition(condition);
            if (CollectionUtils.isNotEmpty(cfgStoreEntities)) {
                List<Integer> storeIdList = cfgStoreEntities.stream().map(CfgStoreEntity::getStoreId).collect(Collectors.toList());
                pageReq.setStoreIdList(storeIdList);
            }
            if (CollectionUtils.isEmpty(pageReq.getClientUidList()) && CollectionUtils.isEmpty(pageReq.getStoreIdList())) {
                return Result.success(new PageVO<>());
            }
        }

        StoreInfoDTO storeInfoDTO = EmployeeTokenHelper.getCurrentEmployeeStoreInfo();
        log.info("当前登录人门店信息：{}", JSONObject.toJSONString(storeInfoDTO));
        if (!storeInfoDTO.getHasAllStoreInfo()) {
            List<Integer> storeIdList = storeInfoDTO.getStoreIdList();
            if (CollectionUtil.isEmpty(storeIdList)) {
                pageReq.setStoreIdList(Collections.singletonList(-1));
            } else {
                if (CollectionUtils.isEmpty(pageReq.getStoreIdList())) {
                    pageReq.setStoreIdList(storeIdList);
                } else {
                    pageReq.getStoreIdList().retainAll(storeIdList);
                }
            }
        }


        PageVO<HeCustomerComplaintsEntity> page = heCustomerComplaintsRepository.pageList(pageReq);
        //填充客户信息
        List<HeCustomerComplaintsEntity> customerComplaintsEntities = page.getList();
        List<Integer> clientUidList = customerComplaintsEntities.stream().map(HeCustomerComplaintsEntity::getClientUid).collect(Collectors.toList());
        List<TabClientEntity> tabClientByIdList = clientRepository.getTabClientByIdList(clientUidList);
        Map<Integer, TabClientEntity> clientNameByIdMap = tabClientByIdList.stream().collect(Collectors.toMap(TabClientEntity::getId, Function.identity(), (v1, v2) -> v1));
        //填充门店信息
        List<Integer> storeIdList = customerComplaintsEntities.stream().map(HeCustomerComplaintsEntity::getStoreId).collect(Collectors.toList());
        List<CfgStoreEntity> storeByIdList = storeRepository.queryCfgStoreByIdList(storeIdList);
        Map<Integer, CfgStoreEntity> storeInfoMap = storeByIdList.stream().collect(Collectors.toMap(CfgStoreEntity::getStoreId, entity -> entity, (v1, v2) -> v1));

        //填充客诉类型
        List<HeCustomerComplaintsTypeEntity> all = heCustomerComplaintsTypeRepository.getAll();
        Map<Long, HeCustomerComplaintsTypeEntity> complaintsTypeMap = all.stream().collect(Collectors.toMap(HeCustomerComplaintsTypeEntity::getId, Function.identity(), (v1, v2) -> v1));

        List<Integer> creatorIdList = customerComplaintsEntities.stream().map(HeCustomerComplaintsEntity::getCreator).collect(Collectors.toList());
        List<UserEntity> userEntityList = userRepository.queryUserByIdList(creatorIdList);
        Map<Integer, String> userNameMap = userEntityList.stream().collect(Collectors.toMap(UserEntity::getId, UserEntity::getName));

        PageVO<CustomerComplaintsVO> customerComplaintsVOPageVO = heCustomerComplaintsServerConverter.customerComplaintsEntityPageToVOPage(page);

        List<CustomerComplaintsVO> list = customerComplaintsVOPageVO.getList();
        list.forEach(customerComplaintsVO -> {
            TabClientEntity tabClientEntity = clientNameByIdMap.get(customerComplaintsVO.getClientUid());
            if (Objects.nonNull(tabClientEntity)) {
                customerComplaintsVO.setCustomerName(tabClientEntity.getName());
                customerComplaintsVO.setCustomerPhone(SensitiveInformationUtil.conductPhoneOrIdCardNo(tabClientEntity.getPhone()));
            }
            CfgStoreEntity cfgStoreEntity = storeInfoMap.get(customerComplaintsVO.getStoreId());
            if (Objects.nonNull(cfgStoreEntity)) {
                customerComplaintsVO.setStoreName(cfgStoreEntity.getStoreName());
            }
            Integer complaintLevel = customerComplaintsVO.getComplaintLevel();
            if (Objects.nonNull(complaintLevel)) {
                HeCustomerComplaintsTypeEntity complaintsTypeEntity = complaintsTypeMap.get(complaintLevel.longValue());
                if (Objects.nonNull(complaintsTypeEntity)) {
                    customerComplaintsVO.setComplaintReason(complaintsTypeEntity.getName());
                    Integer parentId = complaintsTypeEntity.getParentId();
                    if (Objects.nonNull(parentId)) {
                        HeCustomerComplaintsTypeEntity parent = complaintsTypeMap.get(parentId.longValue());
                        if (Objects.nonNull(parent)) {
                            customerComplaintsVO.setComplaintTypeName(parent.getName());
                        }
                        Integer parentId1 = parent.getParentId();
                        if (Objects.nonNull(parentId1)) {
                            HeCustomerComplaintsTypeEntity parent1 = complaintsTypeMap.get(parentId1.longValue());
                            if (Objects.nonNull(parent1)) {
                                customerComplaintsVO.setComplaintLevelName(parent1.getName());
                            }
                        }
                    }
                }
            }
            String creatorName = userNameMap.get(customerComplaintsVO.getCreator());
            customerComplaintsVO.setCreatorName(creatorName);
        });


        return Result.success(customerComplaintsVOPageVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Long> add(CustomerComplaintsCreateReq customerComplaintsCreateReq) {

        log.info("新增客诉信息：{}", JSONUtil.toJsonStr(customerComplaintsCreateReq));
        FlowIdentity identity = FlowIdentity.builder().bizActivity(BizActivityEnum.CUSTOMER_COMPLAINTS_CREATE.code()).idSlice("Complaints").idSlice("Create").build();

        FlowContext context = new FlowContext();
        context.setAttribute(CustomerComplaintsCreateReq.class, customerComplaintsCreateReq);

        SnowballFlowLauncher.fire(identity, context);

        HeCustomerComplaintsEntity heCustomerComplaintsEntity = context.getAttribute(HeCustomerComplaintsEntity.class);

        return Result.success(heCustomerComplaintsEntity.getId());
    }

    @Override
    public Result<Boolean> delete(Long id) {
        return Result.success(heCustomerComplaintsRepository.deleteById(id));
    }

    @Override
    public Result<CustomerComplaintsDetailVO> detail(Long id) {
        HeCustomerComplaintsEntity heCustomerComplaintsEntity = heCustomerComplaintsRepository.selectById(id);
        if (Objects.isNull(heCustomerComplaintsEntity)) {
            return Result.success();
        }
        CustomerComplaintsDetailVO customerComplaintsDetailVO = heCustomerComplaintsServerConverter.customerComplaintsEntityToDetailVO(heCustomerComplaintsEntity);
        //查订单
        HeOrderEntity orderEntity = orderRepository.getByOrderId(heCustomerComplaintsEntity.getOrderId().intValue());
        Integer orderType = orderEntity.getOrderType();
        if (Objects.nonNull(orderEntity)) {
            customerComplaintsDetailVO.setOrderType(orderType);
            customerComplaintsDetailVO.setBasicUid(orderEntity.getBasicUid());
            customerComplaintsDetailVO.setOrderSn(orderEntity.getOrderSn());
            customerComplaintsDetailVO.setOrderId(orderEntity.getOrderId().longValue());
            customerComplaintsDetailVO.setVersion(orderEntity.getVersion());
            customerComplaintsDetailVO.setOrderCreatedAt(orderEntity.getCreatedAt());
            //是否显示合同
            List<OrderContractSignRecordVO> recordAndAgreementListByOrderIdList = contractManager.getContractSignRecordAndAgreementListByOrderIdList(Collections.singletonList(orderEntity.getOrderId().longValue()));
            if (CollectionUtils.isNotEmpty(recordAndAgreementListByOrderIdList)) {
                customerComplaintsDetailVO.setIsShowContractButton(1);
            } else {
                customerComplaintsDetailVO.setIsShowContractButton(0);
            }
        }
        //查客户
        List<TabClientEntity> tabClientByIdList = clientRepository.getTabClientByIdList(Collections.singletonList(heCustomerComplaintsEntity.getClientUid()));
        if (CollectionUtils.isNotEmpty(tabClientByIdList)) {
            TabClientEntity tabClientEntity = tabClientByIdList.get(0);
            customerComplaintsDetailVO.setCustomerName(tabClientEntity.getName());
            customerComplaintsDetailVO.setCustomerPhone(SensitiveInformationUtil.conductPhoneOrIdCardNo(tabClientEntity.getPhone()));
        }
        //查门店
        List<CfgStoreEntity> storeByIdList = storeRepository.queryCfgStoreByIdList(Collections.singletonList(heCustomerComplaintsEntity.getStoreId()));
        if (CollectionUtils.isNotEmpty(storeByIdList)) {
            customerComplaintsDetailVO.setStoreName(storeByIdList.get(0).getStoreName());
        }
        //主要责任人，次要责任人赋值
        List<Integer> userIdList = new ArrayList<>();
        if (Objects.nonNull(heCustomerComplaintsEntity.getPrimaryResponsiblePerson())) {
            userIdList.add(heCustomerComplaintsEntity.getPrimaryResponsiblePerson());
        }
        List<Integer> secondaryResponsiblePerson = heCustomerComplaintsEntity.getSecondaryResponsiblePerson();
        if (CollectionUtils.isNotEmpty(secondaryResponsiblePerson)) {
            userIdList.addAll(secondaryResponsiblePerson);
        }
        if (CollectionUtils.isNotEmpty(userIdList)) {
            List<UserEntity> userEntityList = userRepository.queryUserByIdList(userIdList);
            Map<Integer, String> userNameMap = userEntityList.stream().collect(Collectors.toMap(UserEntity::getId, UserEntity::getName));
            if (Objects.nonNull(heCustomerComplaintsEntity.getPrimaryResponsiblePerson())) {
                customerComplaintsDetailVO.setPrimaryResponsiblePersonName(userNameMap.get(heCustomerComplaintsEntity.getPrimaryResponsiblePerson()));
            }
            if (CollectionUtils.isNotEmpty(secondaryResponsiblePerson)) {
                List<String> responsiblePersonNameList = new ArrayList<>();
                for (Integer responsiblePerson : secondaryResponsiblePerson) {
                    responsiblePersonNameList.add(userNameMap.get(responsiblePerson));
                }
                customerComplaintsDetailVO.setSecondaryResponsiblePersonName(responsiblePersonNameList);
            }
        }
        //定责类型str
        if (Objects.nonNull(heCustomerComplaintsEntity.getResponsibilityType())) {
            customerComplaintsDetailVO.setResponsibilityTypeStr(CustomerComplaintsResponsibilityEnum.getValueByCode(heCustomerComplaintsEntity.getResponsibilityType()));
        }
        //填充客诉类型
        List<HeCustomerComplaintsTypeEntity> all = heCustomerComplaintsTypeRepository.getAll();
        Map<Long, HeCustomerComplaintsTypeEntity> complaintsTypeMap = all.stream().collect(Collectors.toMap(HeCustomerComplaintsTypeEntity::getId, Function.identity(), (v1, v2) -> v1));

        Integer complaintLevel = customerComplaintsDetailVO.getComplaintLevel();
        if (Objects.nonNull(complaintLevel)) {
            HeCustomerComplaintsTypeEntity complaintsTypeEntity = complaintsTypeMap.get(complaintLevel.longValue());
            customerComplaintsDetailVO.setComplaintReason(complaintsTypeEntity.getName());
            customerComplaintsDetailVO.setProductRelateType(complaintsTypeEntity.getType());
            Integer parentId = complaintsTypeEntity.getParentId();
            if (Objects.nonNull(parentId)) {
                HeCustomerComplaintsTypeEntity parent = complaintsTypeMap.get(parentId.longValue());
                if (Objects.nonNull(parent)) {
                    customerComplaintsDetailVO.setComplaintTypeName(parent.getName());
                }
                Integer parentId1 = parent.getParentId();
                if (Objects.nonNull(parentId1)) {
                    HeCustomerComplaintsTypeEntity parent1 = complaintsTypeMap.get(parentId1.longValue());
                    if (Objects.nonNull(parent1)) {
                        customerComplaintsDetailVO.setComplaintLevelName(parent1.getName());
                    }
                }
            }
            customerComplaintsDetailVO.setComplaintLevelStr(customerComplaintsDetailVO.getComplaintLevelName() + "/" + customerComplaintsDetailVO.getComplaintTypeName() + "/" + customerComplaintsDetailVO.getComplaintReason());
        }

        //三方产康师
        if (Objects.nonNull(heCustomerComplaintsEntity.getTherapistId())) {


            TherapistDetailReq req = new TherapistDetailReq();
            req.setId(heCustomerComplaintsEntity.getTherapistId().longValue());
            req.setOperator(Objects.isNull(req.getOperator()) ? new Operator() : req.getOperator());
            Result<TherapistSelectOptionVo> therapistSelectOptionVoResult = therapistQueryService.queryTherapistDetail(req);
            TherapistSelectOptionVo therapistSelectOptionVo = therapistSelectOptionVoResult.getData();
            if (Objects.nonNull(therapistSelectOptionVo)) {
                customerComplaintsDetailVO.setTherapistName(therapistSelectOptionVo.getName());
                customerComplaintsDetailVO.setTherapistIdStr(therapistSelectOptionVo.getName());
            }
        }

        if (Objects.nonNull(heCustomerComplaintsEntity.getThirdPartyTherapistId())) {
            TherapistDetailReq req = new TherapistDetailReq();
            req.setId(heCustomerComplaintsEntity.getThirdPartyTherapistId().longValue());
            req.setOperator(Objects.isNull(req.getOperator()) ? new Operator() : req.getOperator());
            Result<TherapistSelectOptionVo> therapistSelectOptionVoResult = therapistQueryService.queryTherapistDetail(req);
            TherapistSelectOptionVo therapistSelectOptionVo = therapistSelectOptionVoResult.getData();
            if (Objects.nonNull(therapistSelectOptionVo)) {
                customerComplaintsDetailVO.setThirdPartyTherapistName(therapistSelectOptionVo.getName());
                customerComplaintsDetailVO.setThirdPartyTherapistIdStr(therapistSelectOptionVo.getName());
            }
        }

        //产康供应商名称
        Integer supplierId = heCustomerComplaintsEntity.getMainSupplierId();
        if (Objects.nonNull(supplierId)) {
            List<MainSupplierQueryVO> mainSupplierQueryVOS = financeManager.queryMainSupplier(Collections.singletonList(supplierId.longValue()));
            if (CollectionUtils.isNotEmpty(mainSupplierQueryVOS)) {
                customerComplaintsDetailVO.setMainSupplierIdStr(mainSupplierQueryVOS.get(0).getName());
            }
        }

        Long supplierId1 = heCustomerComplaintsEntity.getSupplierId();
        if (Objects.nonNull(supplierId1)) {
            List<SupplierPageVO> supplierPageVOS = financeManager.queryHLYSupplier(Collections.singletonList(supplierId1));
            if (CollectionUtils.isNotEmpty(supplierPageVOS)) {
                customerComplaintsDetailVO.setSupplierIdStr(supplierPageVOS.get(0).getName());
            }
        }

        if (Objects.nonNull(heCustomerComplaintsEntity.getCartId())) {
            QueryCartReq req = new QueryCartReq();
            req.setCartId(heCustomerComplaintsEntity.getCartId());
            List<Integer> orderTypeList = Arrays.asList(OmniOrderTypeEnum.MONTH_ORDER.code(), OmniOrderTypeEnum.PRODUCTION_ORDER.code());
            if (orderTypeList.contains(orderType)) {
                req.setOrderType(orderType);
            } else {
                req.setOrderType(OmniOrderTypeEnum.MONTH_ORDER.code());
            }
            req.setScene(CartSceneEnum.CUSTOMER_COMPLAINTS.code());
            Result<CartRes> cartResResult = cartQueryService.queryCart(req);
            customerComplaintsDetailVO.setCartRes(cartResResult.getData());
        }


        customerComplaintsDetailVO.setHasExtraCompensationOpinion(heCustomerComplaintsEntity.getHasExtraCompensationOpinion());
        customerComplaintsDetailVO.setCompensationAmount(heCustomerComplaintsEntity.getCompensationAmount());

        HeCustomerComplaintsRefundDraftEntity complaintsRefundDraftEntity = customerComplaintsRefundDraftRepository.queryByComplaintsId(id);
        if (ObjectUtil.isNotEmpty(complaintsRefundDraftEntity)) {
            CustomerComplaintsCreateRefundVO customerComplaintsCreateRefundVO = new CustomerComplaintsCreateRefundVO();
            customerComplaintsCreateRefundVO.setAccountBank(complaintsRefundDraftEntity.getAccountBank());
            customerComplaintsCreateRefundVO.setBankNo(complaintsRefundDraftEntity.getBankNo());
            customerComplaintsCreateRefundVO.setAccountName(complaintsRefundDraftEntity.getAccountName());
            customerComplaintsCreateRefundVO.setHasExtraCompensationOpinion(heCustomerComplaintsEntity.getHasExtraCompensationOpinion());
            customerComplaintsCreateRefundVO.setRefundAmount(complaintsRefundDraftEntity.getRefundAmount());
            String refundJson = complaintsRefundDraftEntity.getRefundInfo();
            if (complaintsRefundDraftEntity.getNewOrOldRefund() == 1) {
                //新
                CreateRefundVO read = JsonUtil.read(refundJson, CreateRefundVO.class);
                customerComplaintsCreateRefundVO.setGoodsRefundTypeStr(read.getRefundTypeStr());
                customerComplaintsCreateRefundVO.setNewCreateRefundReq(read);
            } else {
                //旧
                SubmitRefundApplyV2VO read = JsonUtil.read(refundJson, SubmitRefundApplyV2VO.class);
                customerComplaintsCreateRefundVO.setGoodsRefundTypeStr(read.getRefundModelStr());
                customerComplaintsCreateRefundVO.setOldCreateRefundReq(read);
            }

            BigDecimal otherCompensationAmount = getOtherCompensationAmount(heCustomerComplaintsEntity.getOtherCompensationAmount());
            customerComplaintsCreateRefundVO.setTotalComplaintAmount(getRoundingMode(complaintsRefundDraftEntity.getAmountGoodsClaimed().add(complaintsRefundDraftEntity.getRefundAmount()).add(complaintsRefundDraftEntity.getOrderRefundAmount()).add(otherCompensationAmount)));
            customerComplaintsCreateRefundVO.setTotalBusinessAmount(complaintsRefundDraftEntity.getOrderRefundAmount());
            if (Objects.nonNull(heCustomerComplaintsEntity.getCashRefundId())) {
                HeOrderRefundEntity refundEntity = orderRefundRepository.getOneById(heCustomerComplaintsEntity.getCashRefundId());
                if (Objects.nonNull(refundEntity)) {
                    customerComplaintsCreateRefundVO.setTotalBusinessAmount(AmountChangeUtil.f2YScale2(refundEntity.getRefundAchievement()));
                }
            }
            customerComplaintsCreateRefundVO.setTotalSaleAmount(BigDecimal.ZERO);

            customerComplaintsDetailVO.setCustomerComplaintsCreateRefundVO(customerComplaintsCreateRefundVO);
        }

        if (StringUtils.isNotEmpty(heCustomerComplaintsEntity.getExtraRefundInfo())) {
            List<ExtraRefundTypeRes> otherCompensationAmountList = JSONUtil.toList(heCustomerComplaintsEntity.getExtraRefundInfo(), ExtraRefundTypeRes.class);
            otherCompensationAmountList.forEach(item -> {
                item.setExtraValue(CustomerComplaintsRefundEnum.fromCode(item.getExtraCode()));
            });
            customerComplaintsDetailVO.setOtherCompensationAmountList(otherCompensationAmountList);
        } else {
            customerComplaintsDetailVO.setOtherCompensationAmountList(Collections.emptyList());
        }

        //额外赔偿意见

        return Result.success(customerComplaintsDetailVO);
    }

    @Override
    public Result<CustomerComplaintsCheckRes> check(CustomerComplaintsCreateReq customerComplaintsCreateReq) {

        try {
            log.info("客诉审核入参：{}", JSONObject.toJSONString(customerComplaintsCreateReq));

            FlowIdentity identity = FlowIdentity.builder().bizActivity(BizActivityEnum.CUSTOMER_COMPLAINTS_CHECK.code()).idSlice("Complaints").idSlice("Check").build();

            FlowContext context = new FlowContext();

            context.setAttribute(CustomerComplaintsCreateReq.class, customerComplaintsCreateReq);
            Long orderId = customerComplaintsCreateReq.getOrderId();
            context.setAttribute(OtherConstant.ORDER_ID, orderId);
            HeOrderEntity heOrderEntity = orderRepository.queryOrderById(orderId.intValue());
            context.setAttribute(HeOrderEntity.class, heOrderEntity);
            CustomerComplaintsCreateRefundReq createRefundReq = customerComplaintsCreateReq.getCreateRefundReq();
            if (Objects.nonNull(createRefundReq)) {
                CreateRefundReq newCreateRefundReq = createRefundReq.getNewCreateRefundReq();
                if (Objects.nonNull(newCreateRefundReq)) {
                    context.setAttribute(CreateRefundReq.class, newCreateRefundReq);
                } else {
                    context.setAttribute(CreateRefundReq.class, new CreateRefundReq());
                }
                SubmitRefundApplyV2Request oldCreateRefundReq = createRefundReq.getOldCreateRefundReq();
                if (Objects.nonNull(oldCreateRefundReq)) {
                    context.setAttribute(SubmitRefundApplyV2Request.class, oldCreateRefundReq);
                } else {
                    context.setAttribute(SubmitRefundApplyV2Request.class, new SubmitRefundApplyV2Request());
                }
                Integer cartId = createRefundReq.getCartId();
                if (Objects.nonNull(cartId)) {
                    QueryCartReq req = new QueryCartReq();
                    req.setScene(CartSceneEnum.CUSTOMER_COMPLAINTS.code());
                    req.setCartId(cartId);
                    Integer orderType = heOrderEntity.getOrderType();
                    List<Integer> orderTypeList = Arrays.asList(OmniOrderTypeEnum.MONTH_ORDER.code(), OmniOrderTypeEnum.PRODUCTION_ORDER.code());
                    if (orderTypeList.contains(orderType)) {
                        req.setOrderType(orderType);
                    } else {
                        req.setOrderType(OmniOrderTypeEnum.MONTH_ORDER.code());
                    }
                    Result<CartRes> cartResResult = cartQueryService.queryCart(req);
                    CartRes data = cartResResult.getData();
                    List<SkuDetailInfo> skuList = data.getSkuList();
                    context.setListAttribute(SkuDetailInfo.class, skuList);
                    CfgStoreEntity storeEntity = storeRepository.queryCfgStoreById(data.getStoreId());
                    storeEntity.setTaxRate(heOrderEntity.getFxRate());
                    context.setAttribute(CfgStoreEntity.class, storeEntity);
                    context.setListAttribute(CustomAttribute.class, data.getExtraInfo().getFulfillExtraList());
                }
            }

            SnowballFlowLauncher.fire(identity, context);

            CustomerComplaintsCheckRes result = context.getAttribute(CustomerComplaintsCheckRes.class);
            return Result.success(result);
        } catch (BusinessException e) {
            log.info("客诉审核异常", e);
            CustomerComplaintsCheckRes customerComplaintsCheckRes = new CustomerComplaintsCheckRes();
            customerComplaintsCheckRes.setShowApproval(Boolean.FALSE);
            if ((ALREADY_HAS_APPROVAL.code() + "").equals(e.getCode())) {
                customerComplaintsCheckRes.setMsg(ALREADY_HAS_APPROVAL.desc());
            } else if ((GOODS_OFFLINE.code() + "").equals(e.getCode())) {
                customerComplaintsCheckRes.setMsg(GOODS_OFFLINE.desc());
            } else if ((REFUND_NUM_OR_AMOUNT.code() + "").equals(e.getCode())) {
                customerComplaintsCheckRes.setMsg("当前订单因其他客诉工单审批通过已更新订单商品可退数量或可退金额，请重新填写处理意见后再次尝试。");
            } else {
                customerComplaintsCheckRes.setMsg(e.getMessage());
            }
            return Result.success(customerComplaintsCheckRes);
        } catch (Exception e) {
            log.error("客诉审核异常", e);
            return Result.deleteFailed("温馨提示", "系统异常");
        }
    }

    @Override
    public void createOrder(Long cartId) {
        HeCustomerComplaintsEntity heCustomerComplaintsEntity = new HeCustomerComplaintsEntity();
        heCustomerComplaintsEntity.setCartId(cartId.intValue());
        heCustomerComplaintsEntity.setComplaintStatus(CustomerComplaintsStatusEnum.PROCESS.getCode());
        heCustomerComplaintsEntity.setCreator(5315);

        FlowContext bizContext = new FlowContext();
        bizContext.setAttribute(HeCustomerComplaintsEntity.class, heCustomerComplaintsEntity);
        customerComplaintsAgreeOrderProcessor.run(bizContext);

    }

    @Override
    public Result<Long> relaunch(Long id) {
        FlowContext context = new FlowContext();
        FlowIdentityBuilder flowIdentityBuilder = FlowIdentity.builder().bizActivity(BizActivityEnum.CUSTOMER_COMPLAINTS_RE_CREATE.code()).idSlice("Create").idSlice("Customer").idSlice("Re").idSlice("Complaints");

        HeCustomerComplaintsRefundDraftEntity complaintsRefundDraftEntity = customerComplaintsRefundDraftRepository.queryByComplaintsId(id);
        String reqJson = complaintsRefundDraftEntity.getReqJson();
        CustomerComplaintsCreateReq customerComplaintsCreateReq = JsonUtil.read(reqJson, CustomerComplaintsCreateReq.class);
        context.setAttribute(CustomerComplaintsCreateReq.class, customerComplaintsCreateReq);
        if (Objects.nonNull(customerComplaintsCreateReq) && StringUtils.isEmpty(customerComplaintsCreateReq.getStoreName())) {
            CfgStoreEntity cfgStoreEntity = storeRepository.queryCfgStoreById(customerComplaintsCreateReq.getStoreId());
            customerComplaintsCreateReq.setStoreName(cfgStoreEntity.getStoreName());
        }
        HeCustomerComplaintsEntity customerComplaintsEntity = customerComplaintsRepository.selectById(id);
        context.setAttribute(HeCustomerComplaintsEntity.class, customerComplaintsEntity);
        Integer cashRefundId = customerComplaintsEntity.getCashRefundId();
        orderRefundRepository.delById(cashRefundId);
        SnowballFlowLauncher.fire(flowIdentityBuilder.build(), context);
        return Result.success(id);
    }

    private BigDecimal getOtherCompensationAmount(String otherCompensationAmount) {

        final BigDecimal[] otherAmount = {BigDecimal.ZERO};
        try {
            if (StringUtils.isEmpty(otherCompensationAmount)) {
                return otherAmount[0];
            }
            List<SelectRespVO> respVOList = JSONUtil.toList(otherCompensationAmount, SelectRespVO.class);
            if (CollectionUtils.isEmpty(respVOList)) {
                return otherAmount[0];
            }
            respVOList.forEach(item -> {
                otherAmount[0] = otherAmount[0].add(new BigDecimal(item.getLabel()));
            });
            return otherAmount[0];
        } catch (Exception e) {
            log.error("提交工单获取其他金额总和发生异常", e);
            return otherAmount[0];
        }
    }

    private BigDecimal getRoundingMode(BigDecimal amount) {

        if (Objects.isNull(amount) || amount.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO.divide(BigDecimal.ONE, 2, RoundingMode.UP);
        }
        return amount.divide(BigDecimal.ONE, 2, RoundingMode.UP);
    }
}

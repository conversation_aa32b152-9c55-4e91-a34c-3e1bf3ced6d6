package com.stbella.order.server.contract.month.component;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.customer.server.ecp.dto.HeUserCardDTO;
import com.stbella.order.common.enums.month.*;
import com.stbella.order.domain.order.month.entity.AheadOutRoomEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.domain.order.month.entity.MonthContractSignRecordEntity;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.async.AsyncOrder;
import com.stbella.order.server.contract.req.MonthEsignNotifyReq;
import com.stbella.order.server.contract.vo.EsignCallbackVO;
import com.stbella.order.server.convert.AppMonthContractSignRecordConverter;
import com.stbella.order.server.manager.UserCardManager;
import com.stbella.order.server.order.month.req.AheadOutRoomQuery;
import com.stbella.order.server.order.month.req.MonthContractSignAgreementReq;
import com.stbella.order.server.order.month.req.MonthContractSignRecordReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 * 合同回调
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-24 10:15
 */
@Component
@Slf4j
public class MonthContractNotifyAssembler {
    private static final String SUCCESS_CODE = "200";
    //流程结束
    private static final String EVENT_TYPE = "SIGN_FLOW_FINISH";
    private static final String COMPLETE_CODE = "2";

    @Resource
    private MonthContractSignRecordRepository monthContractSignRecordRepository;
    @Resource
    private MonthContractSignAgreementRepository monthContractSignAgreementRepository;
    @Resource
    private AheadOutRoomRepository aheadOutRoomRepository;
    @Resource
    private UserCardManager userCardManager;
    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;
    @Resource
    private AppMonthContractSignRecordConverter appMonthContractSignRecordConverter;
    @Resource
    private AsyncOrder asyncOrder;
    @Resource
    private OrderRepository orderRepository;
    /**
     * 主合同和附属合同回调
     *
     * @param monthEsignNotifyReq
     * @return {@link EsignCallbackVO}
     * @throws UnsupportedEncodingException
     */
    public EsignCallbackVO mainAccessoryCallback(MonthEsignNotifyReq monthEsignNotifyReq) {
        log.info("esignCallback=====>:{}", JSONUtil.toJsonStr(monthEsignNotifyReq));
        String action = monthEsignNotifyReq.getAction();
        if (EVENT_TYPE.equals(action)) {
            String flowId = monthEsignNotifyReq.getFlowId();
            String flowStatus = monthEsignNotifyReq.getFlowStatus();
            Long timestamp = monthEsignNotifyReq.getTimestamp();
            String statusDescription = monthEsignNotifyReq.getStatusDescription();
            log.info("esignCallback=====>statusDescription:{}", statusDescription);
            //签署完成事件通知，并更新数据库
            if (COMPLETE_CODE.equals(flowStatus)) {
                MonthContractSignRecordReq monthContractSignRecordReq = new MonthContractSignRecordReq();
                monthContractSignRecordReq.setEsignFlowId(flowId);
                monthContractSignRecordReq.setContractStatus(ContractStatusEnum.SIGNED.code());
                monthContractSignRecordReq.setUpdatedAt(timestamp / 1000);
                monthContractSignRecordRepository.updateByCondition(monthContractSignRecordReq);
                log.info("esignCallback=====>{}", "已经更新主合同/附件签署状态，正在返回状态码给e签宝");
                MonthContractSignRecordEntity monthContractSignRecordEntity = monthContractSignRecordRepository.queryByCondition(monthContractSignRecordReq);
                //提前离管解除更新状态
                Optional.ofNullable(monthContractSignRecordEntity).filter(i -> Objects.equals(TemplateContractTypeV2Enum.RELEASE.code(), i.getTemplateContractType())).ifPresent(
                        record -> {
                            AheadOutRoomQuery roomQuery = new AheadOutRoomQuery();
                            roomQuery.setOrderId(record.getGuideId().intValue());
                            AheadOutRoomEntity aheadOutRoomEntity = aheadOutRoomRepository.queryByOrderId(roomQuery);
                            Optional.ofNullable(aheadOutRoomEntity).ifPresent(aheadOutRoom -> {
                                aheadOutRoom.setState(AheadOutRoomEnum.STATE_DISENABLE.code());
                                aheadOutRoom.setAgreeAt(DateUtil.date(timestamp));
                                aheadOutRoomRepository.updateOutRoom(aheadOutRoom);
                            });

                            //更新订单状态为 提前离管 状态
                            HeOrderEntity byOrderId = orderRepository.getByOrderId(record.getGuideId().intValue());
                            Optional.ofNullable(byOrderId).ifPresent(orderEntity -> {
                                orderEntity.setOrderStatus(OrderStatusV2Enum.ADVANCE_OUT_OF_STORE.getCode());
                                orderRepository.updateOrderMonthByOrderId(orderEntity);
                            });
                        }
                );

                //更新主合同签订,算认证成功
                Optional.ofNullable(monthContractSignRecordEntity).filter(i -> Objects.equals(TemplateContractTypeV3Enum.YZ_SAINTBELLA.code(), i.getTemplateContractType())).ifPresent(
                        record -> {
                            HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(record.getGuideId().intValue());
                            Optional.ofNullable(heOrderUserSnapshotEntity).ifPresent(snap -> {
                                HeUserCardDTO userCardDTO = appMonthContractSignRecordConverter.entity2UserCardDTO(snap);
                                userCardDTO.setContractSign(UserCardContractSignStatusEnum.SIGNED.code());
                                userCardManager.saveUserCardInfo(userCardDTO);
                            });
                            HeOrderEntity byOrderId = orderRepository.getByOrderId(record.getGuideId().intValue());
                            //剔除业绩的不需要报单
                            if (!ObjectUtil.equals(byOrderId.getPercentFirstTime(), -1) && !ObjectUtil.equals(byOrderId.getOperationType(), 1)) {
                                // 异步调用报单
                                asyncOrder.asyncClientSendNotice(record.getGuideId().intValue(), (int) (monthEsignNotifyReq.getTimestamp() / 1000));

                            }
                        }
                );

                EsignCallbackVO esignCallback = new EsignCallbackVO();
                esignCallback.setCode(SUCCESS_CODE);
                esignCallback.setMsg("success");
                return esignCallback;
            }
        }

        return null;
    }


    /**
     * 补充协议回调
     *
     * @param monthEsignNotifyReq
     * @return {@link EsignCallbackVO}
     * @throws UnsupportedEncodingException
     */
    public EsignCallbackVO agreementCallback(MonthEsignNotifyReq monthEsignNotifyReq) {
        log.info("agreementCallback=====>:{}", JSONUtil.toJsonStr(monthEsignNotifyReq));
        String action = monthEsignNotifyReq.getAction();
        if (EVENT_TYPE.equals(action)) {
            String flowId = monthEsignNotifyReq.getFlowId();
            String flowStatus = monthEsignNotifyReq.getFlowStatus();
            Date endTime = monthEsignNotifyReq.getEndTime();
            String statusDescription = monthEsignNotifyReq.getStatusDescription();
            log.info("agreementCallback=====>statusDescription:{}", statusDescription);
            if (COMPLETE_CODE.equals(flowStatus)) {
                MonthContractSignAgreementReq monthContractSignAgreementReq = new MonthContractSignAgreementReq();
                monthContractSignAgreementReq.setEsignFlowId(flowId);
                monthContractSignAgreementReq.setSignTime(LocalDateTimeUtil.of(endTime));
                monthContractSignAgreementReq.setState(ContractStatusEnum.SIGNED.code());
                monthContractSignAgreementRepository.updateByCondition(monthContractSignAgreementReq);
                log.info("======================{}", "已经更新补充协议合同签署状态，正在返回状态码给e签宝");
                EsignCallbackVO esignCallback = new EsignCallbackVO();
                esignCallback.setCode(SUCCESS_CODE);
                esignCallback.setMsg("success");
                return esignCallback;
            }
        }
        return null;
    }
}

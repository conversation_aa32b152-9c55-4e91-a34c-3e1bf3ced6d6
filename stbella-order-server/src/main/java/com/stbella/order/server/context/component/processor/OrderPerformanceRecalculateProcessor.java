package com.stbella.order.server.context.component.processor;

import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderPerformanceOperationEntity;
import com.stbella.order.domain.repository.HeOrderPerformanceOperationRepository;
import com.stbella.order.server.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "减免审批通过/产康金支付成功后业绩划分重新计算")
public class OrderPerformanceRecalculateProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private HeOrderPerformanceOperationRepository orderPerformanceOperationRepository;


    @Override
    public void run(FlowContext bizContext) {

        HeOrderEntity heOrderEntity = bizContext.getAttribute(HeOrderEntity.class);

        log.info("减免对业绩转移的影响heOrderEntity.calPayable:{}", JsonUtil.write(heOrderEntity.calPayable()));

        try {
            List<HeOrderPerformanceOperationEntity> heOrderPerformanceOperationEntities = orderPerformanceOperationRepository.queryByOrderSn(heOrderEntity.getOrderSn());
            if (CollectionUtils.isNotEmpty(heOrderPerformanceOperationEntities)) {
                Optional<HeOrderPerformanceOperationEntity> first = heOrderPerformanceOperationEntities.stream().findFirst();
                //签单金额
                Integer payAmount = heOrderEntity.calPayable();
                //产康金支付金额
                Integer productionAmountPay = heOrderEntity.getProductionAmountPay();
                //实际签单金额（减去产康金之后的金额）
                Integer realPayAmount = payAmount - productionAmountPay;

                HeOrderPerformanceOperationEntity orderPerformanceOperationEntity = first.get();

                log.info("订单折扣减免成功，重新分配业绩划分；订单SN:{},原划分对象:{}", heOrderEntity.getOrderSn(), JsonUtil.write(orderPerformanceOperationEntity));

                //本次的签单金额
                orderPerformanceOperationEntity.setOrderTotalAmount(realPayAmount);

                //原比例
                BigDecimal percentage = orderPerformanceOperationEntity.getPercentage();


                BigDecimal divide = new BigDecimal(realPayAmount).multiply(percentage.divide(new BigDecimal(100))).setScale(2, RoundingMode.HALF_UP);

                int orderRetainedPerformance = divide.intValue();
                orderPerformanceOperationEntity.setOrderRetainedPerformance(orderRetainedPerformance);
                orderPerformanceOperationEntity.setSourceStoreAmount(realPayAmount - orderRetainedPerformance);
                orderPerformanceOperationEntity.setTargetStoreAmount(realPayAmount - orderRetainedPerformance);
                orderPerformanceOperationEntity.setModifyId(0L);
                orderPerformanceOperationEntity.setModifyName(orderPerformanceOperationEntity.getModifyName() + "/系统自动");
                orderPerformanceOperationEntity.setGmtModified(new Date());

                log.info("订单折扣减免成功，重新分配业绩划分；订单SN:{},新划分对象:{}", heOrderEntity.getOrderSn(), JsonUtil.write(orderPerformanceOperationEntity));
                orderPerformanceOperationRepository.updateOne(orderPerformanceOperationEntity);
            }

        } catch (Exception e) {
            log.info("业绩重新划分失败:{},{}", heOrderEntity.getOrderSn(), e.getMessage());
        }
    }

}

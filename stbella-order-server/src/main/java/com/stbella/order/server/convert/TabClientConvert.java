package com.stbella.order.server.convert;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.customer.server.ecp.entity.TabClientBailorPO;
import com.stbella.customer.server.ecp.request.OrderMonthClientBailorCacheRequest;
import com.stbella.customer.server.ecp.request.OrderMonthClientCacheRequest;
import com.stbella.customer.server.ecp.request.SaveTabClientBailorRequest;
import com.stbella.customer.server.ecp.request.SaveTabClientRequest;
import com.stbella.customer.server.ecp.vo.ClientInfoVO;
import com.stbella.customer.server.scrm.dto.ScrmCustomerDTO;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.domain.order.month.entity.MonthHeUserEsignEntity;
import com.stbella.order.server.config.DateMapper;
import com.stbella.order.server.config.MappingConfig;
import com.stbella.order.server.contract.req.MonthUserEsignDTO;
import com.stbella.order.server.order.month.req.OrderMonthClientBailorReq;
import com.stbella.order.server.order.month.req.OrderMonthClientReq;
import com.stbella.order.server.order.month.res.MonthHeUserEsignVO;
import com.stbella.order.server.order.month.res.OrderMonthClientBailorVO;
import com.stbella.order.server.order.month.res.OrderMonthClientVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.Date;

/**
 * <AUTHOR>
 * @description: StoreConvert 门店转换类
 * @date 2021/5/17 4:26 下午
 */
@Mapper(config = MappingConfig.class, uses = DateMapper.class, imports = {AmountChangeUtil.class, Date.class, ObjectUtil.class})
public interface TabClientConvert {


    @Mappings({
            @Mapping(target = "scrmCustomerId", expression = "java( ObjectUtil.isEmpty(orderMonthClientReq.getScrmId())?null:Long.valueOf(orderMonthClientReq.getScrmId()))"),
    })
    ScrmCustomerDTO orderMonthClientReq2ScrmCustomerDTO(OrderMonthClientReq orderMonthClientReq);

    OrderMonthClientReq clientInfoVO2Req(ClientInfoVO vo);

    /**
     * client vo -> order user  entity
     * @param vo
     * @return
     */
    HeOrderUserSnapshotEntity clientInfo2OrderUserSnapshot(ClientInfoVO vo);


    //
    @Mappings({
            @Mapping(target = "wantIn", expression = "java( ObjectUtil.isEmpty(req.getWantIn())?null:req.getWantIn().getTime()/1000)"),
    })
    SaveTabClientRequest orderMonthClientReq2Request(OrderMonthClientReq req);

    SaveTabClientBailorRequest orderMonthClientBailorReq2Request(OrderMonthClientBailorReq req);

    /**
     * po -> dto
     *
     * @param po
     * @return {@link MonthUserEsignDTO}
     */
    @Mappings({
            @Mapping(target = "idCardNo", source = "idCard"),
            @Mapping(target = "idCardType", source = "certType"),
    })
    MonthUserEsignDTO clientBailorPo2Dto(TabClientBailorPO po);

    /**
     * vo -> dto
     *
     * @param vo
     * @return {@link MonthUserEsignDTO}
     */
    MonthUserEsignDTO clientUserEsignVo2Dto(MonthHeUserEsignVO vo);

    MonthUserEsignDTO clientUserEsignEntity2Dto(MonthHeUserEsignEntity vo);

    OrderMonthClientCacheRequest heOrderUserSnapshotEntity2OrderMonthClientCacheRequest(OrderMonthClientVO heOrderUserSnapshotEntity);

    OrderMonthClientBailorCacheRequest heOrderBailorSnapshotEntity2OrderMonthClientBailorCacheRequest(OrderMonthClientBailorVO heOrderBailorSnapshotEntity);
}

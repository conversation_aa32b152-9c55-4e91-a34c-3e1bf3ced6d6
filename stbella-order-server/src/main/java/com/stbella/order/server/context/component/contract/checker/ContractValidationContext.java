package com.stbella.order.server.context.component.contract.checker;

import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.HeOrderBailorSnapshotEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.platform.order.api.contract.req.ContractBaseReq;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import com.stbella.store.server.ecp.entity.CfgStore;
import lombok.Data;

import java.util.List;

@Data
public class ContractValidationContext {

    private ContractBaseReq request;

    private HeOrderEntity orderEntity;

    private HeOrderUserSnapshotEntity heOrderUserSnapshotEntity;

    private HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity;

    private CfgStoreEntity cfgStore;

    private List<OrderContractSignRecordVO> signRecords;

}
package com.stbella.order.server.contract.repository;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.base.Joiner;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.cts.server.entity.BaseSitterPO;
import com.stbella.cts.server.entity.HomeCustomerProductPO;
import com.stbella.cts.server.entity.HomeSitterProductPO;
import com.stbella.cts.server.enums.CTSCourseTypeEnum;
import com.stbella.cts.server.enums.CTSMaternalChildCareLevelEnum;
import com.stbella.cts.server.enums.CTSProductCustomerServiceTypeEnum;
import com.stbella.cts.server.enums.CTSSitterLevelEnum;
import com.stbella.customer.server.cts.vo.CustomerInfoCtsDetailVO;
import com.stbella.order.common.utils.ExtraUtil;
import com.stbella.order.server.config.TrainClassValidity;
import com.stbella.order.server.contract.dto.BabysittingServiceAgreementDTO;
import com.stbella.order.server.contract.dto.MaternalChildNursingServiceAgreementDTO;
import com.stbella.order.server.contract.dto.SitterPlatformServeFeeAgreementDTO;
import com.stbella.order.server.contract.dto.SitterTrainFeeAgreementDTO;
import com.stbella.order.server.contract.entity.UserESignPO;
import com.stbella.order.server.contract.enums.IdTypeEnum;
import com.stbella.order.server.order.cts.entity.OrderCtsPO;
import com.stbella.order.server.order.cts.enums.ServeTypeEnum;
import com.stbella.order.server.order.cts.request.order.CourseOrderCreateRequest;
import com.stbella.order.server.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * <AUTHOR>
 */
@Repository
public class ContractBuildRepository {
    @Autowired
    TrainClassValidity trainClassValidity;




    /**
     * 育婴师服务委托协议
     *
     * @param orderCtsPO 订单
     * @param contractNo 合同id
     * @return BabysittingServiceAgreementDTO
     */
    public BabysittingServiceAgreementDTO buildBabySitting(OrderCtsPO orderCtsPO, UserESignPO userESignPO, HomeCustomerProductPO homeCustomerProductPO, Map<Integer, String> addressMap, String contractNo, CustomerInfoCtsDetailVO ctsCustomerDetail) {


        BabysittingServiceAgreementDTO babysittingServiceAgreementDTO = new BabysittingServiceAgreementDTO();
        babysittingServiceAgreementDTO.setCustomerName(userESignPO.getName());
        babysittingServiceAgreementDTO.setCompanyContact(orderCtsPO.getSellName());
        babysittingServiceAgreementDTO.setSignNumber(contractNo);
        babysittingServiceAgreementDTO.setSignAddress("杭州市萧山区信息港六期杭州湾数字健康创新谷6号楼");
        babysittingServiceAgreementDTO.setSignDate(DateUtil.format(new Date(), "yyyy.MM.dd"));
        babysittingServiceAgreementDTO.setIdNumber(userESignPO.getCerNo());
        babysittingServiceAgreementDTO.setCerType(IdTypeEnum.getValueByCode(userESignPO.getCerType()));
        babysittingServiceAgreementDTO.setCustomerMobile(userESignPO.getPhone());
        String homeAddress = Joiner.on("").join(Arrays.asList(
                addressMap.getOrDefault(orderCtsPO.getLiveProvinceAddressId(), ""),
                addressMap.getOrDefault(orderCtsPO.getLiveCityAddressId(), ""),
                addressMap.getOrDefault(orderCtsPO.getLiveAreaAddressId(), ""),
                (StringUtils.isNotBlank(orderCtsPO.getLiveAddress())?orderCtsPO.getLiveAddress():"")));
        babysittingServiceAgreementDTO.setHomeAddress(homeAddress);
        babysittingServiceAgreementDTO.setBusinessAddress("浙江省杭州市萧山区经济技术开发区明星路371号2幢308-1室");
        babysittingServiceAgreementDTO.setBabyCount(String.valueOf(orderCtsPO.getBabyNum()));
        babysittingServiceAgreementDTO.setMomCount("/");
        babysittingServiceAgreementDTO.setServiceBaby("√");
        babysittingServiceAgreementDTO.setSpecialDemand(replace(orderCtsPO.getSpecialRequire()));

        String allName = Joiner.on("").join(Arrays.asList(
                addressMap.getOrDefault(orderCtsPO.getServeProvinceAddressId(),""),
                addressMap.getOrDefault(orderCtsPO.getServeCityAddressId(),""),
                addressMap.getOrDefault(orderCtsPO.getServeAreaAddressId(),""),
                (StringUtils.isNotBlank(orderCtsPO.getServeAddress())?orderCtsPO.getServeAddress():"")));


        babysittingServiceAgreementDTO.setServiceAllAddress(allName);

        if(ctsCustomerDetail != null){
            StringBuilder sb = new StringBuilder();
            if(StringUtils.isNotBlank(ctsCustomerDetail.getEmergencyContactName())){
                sb.append(ctsCustomerDetail.getEmergencyContactName());
            }
            if(StringUtils.isNotBlank(ctsCustomerDetail.getEmergencyContactMobile())){
                sb.append("(").append(ctsCustomerDetail.getEmergencyContactMobile()).append(")");
            }
            babysittingServiceAgreementDTO.setEmergencyContactInfo(sb.toString());
        }
        if(StringUtils.isBlank(babysittingServiceAgreementDTO.getEmergencyContactInfo())){
            babysittingServiceAgreementDTO.setEmergencyContactInfo("/");
        }



        //rzd业务方


        if (orderCtsPO.getServeType() != null) {
            ServeTypeEnum enumByCode = ServeTypeEnum.getEnumByCode(orderCtsPO.getServeType());
            switch (enumByCode) {
                case ALL_DAY:
                    babysittingServiceAgreementDTO.setAllDayInHome("√");
                    break;
                case WORK_DAY:
                    babysittingServiceAgreementDTO.setDayTakeCare("√");
                    break;

                default:
                    babysittingServiceAgreementDTO.setServiceOther("√");
                    babysittingServiceAgreementDTO.setServiceOtherDes(orderCtsPO.getServeTypeRemark());
                    break;
            }
        }


        if (homeCustomerProductPO.getComboLevel() != null) {
            if (CTSProductCustomerServiceTypeEnum.SITTER.getCode().equals(homeCustomerProductPO.getServiceType())) {
                CTSSitterLevelEnum cTSSitterLevelEnum = CTSSitterLevelEnum.getEnumByCode(homeCustomerProductPO.getComboLevel());

                CTSSitterLevelEnum.ServiceComboLevelEnum serviceComboLevelEnum = cTSSitterLevelEnum.getServiceComboLevelEnum();
                if(CTSSitterLevelEnum.ServiceComboLevelEnum.SITTER_SERVICE.getCode().equals(serviceComboLevelEnum.getCode())){
                    babysittingServiceAgreementDTO.setSitterService("√");
                }else if(CTSSitterLevelEnum.ServiceComboLevelEnum.CONSULTATION_SERVICE.getCode().equals(serviceComboLevelEnum.getCode())){
                    babysittingServiceAgreementDTO.setConsultationService("√");
                }else if(CTSSitterLevelEnum.ServiceComboLevelEnum.DOUBLE_EXTRACT_SERVICE.getCode().equals(serviceComboLevelEnum.getCode())){
                    babysittingServiceAgreementDTO.setDoubleExtractService("√");
                }

                switch (cTSSitterLevelEnum) {
                    case CONFINEMENT_CLUB:
                        babysittingServiceAgreementDTO.setConfinementClub("√");
                        break;
                    case CONCENTRATION:
                        babysittingServiceAgreementDTO.setConcentration("√");
                        break;
                    case SPECIAL_CARE:
                        babysittingServiceAgreementDTO.setSpecialCare("√");
                        break;
                    case PREFERRED:
                        babysittingServiceAgreementDTO.setPreferred("√");
                        break;
                    case EXCLUSIVE:
                        babysittingServiceAgreementDTO.setExclusive("√");
                        break;
                    case LE_YU:
                        babysittingServiceAgreementDTO.setLeYu("√");
                        break;
                    case LI_YU:
                        babysittingServiceAgreementDTO.setLiYu("√");
                        break;
                    case YU_YU:
                        babysittingServiceAgreementDTO.setYuYu("√");
                        break;
                    case SHU_YU:
                        babysittingServiceAgreementDTO.setShuYu("√");
                        break;
                    case YANG_CHENG:
                        babysittingServiceAgreementDTO.setYangCheng("√");
                        break;
                    default:
                        break;
                }
            }
        }

        if (homeCustomerProductPO.getComboLevel() != null) {
            if (CTSProductCustomerServiceTypeEnum.SITTER.getCode().equals(homeCustomerProductPO.getServiceType())) {
                CTSSitterLevelEnum cTSSitterLevelEnum = CTSSitterLevelEnum.getEnumByCode(homeCustomerProductPO.getComboLevel());
                CTSSitterLevelEnum.ServiceComboLevelEnum serviceComboLevelEnum = cTSSitterLevelEnum.getServiceComboLevelEnum();
                if(CTSSitterLevelEnum.ServiceComboLevelEnum.SITTER_SERVICE.getCode().equals(serviceComboLevelEnum.getCode())){
                    if (orderCtsPO.getExpectServeStartDate() != null) {
                        String startDate = DateUtil.format(orderCtsPO.getExpectServeStartDate(), "yyyy-MM-dd");
                        String[] split = startDate.split("-");
                        babysittingServiceAgreementDTO.setServiceStartYear(split[0]);
                        babysittingServiceAgreementDTO.setServiceStartMonth(split[1]);
                        babysittingServiceAgreementDTO.setServiceStartDay(split[2]);
                    }
                    if (orderCtsPO.getExpectServeEndDate() != null) {
                        String startDate = DateUtil.format(orderCtsPO.getExpectServeEndDate(), "yyyy-MM-dd");
                        String[] split = startDate.split("-");
                        babysittingServiceAgreementDTO.setServiceEndYear(split[0]);
                        babysittingServiceAgreementDTO.setServiceEndMonth(split[1]);
                        babysittingServiceAgreementDTO.setServiceEndDay(split[2]);
                    }

                    babysittingServiceAgreementDTO.setConsultationServiceStartYear("/");
                    babysittingServiceAgreementDTO.setConsultationServiceStartMonth("/");
                    babysittingServiceAgreementDTO.setConsultationServiceStartDay("/");

                    babysittingServiceAgreementDTO.setConsultationServiceEndYear("/");
                    babysittingServiceAgreementDTO.setConsultationServiceEndMonth("/");
                    babysittingServiceAgreementDTO.setConsultationServiceEndDay("/");
                }else if(CTSSitterLevelEnum.ServiceComboLevelEnum.CONSULTATION_SERVICE.getCode().equals(serviceComboLevelEnum.getCode())){

                    if (orderCtsPO.getExpectServeStartDate() != null) {
                        String startDate = DateUtil.format(orderCtsPO.getExpectServeStartDate(), "yyyy-MM-dd");
                        String[] split = startDate.split("-");
                        babysittingServiceAgreementDTO.setConsultationServiceStartYear(split[0]);
                        babysittingServiceAgreementDTO.setConsultationServiceStartMonth(split[1]);
                        babysittingServiceAgreementDTO.setConsultationServiceStartDay(split[2]);
                    }
                    if (orderCtsPO.getExpectServeEndDate() != null) {
                        String startDate = DateUtil.format(orderCtsPO.getExpectServeEndDate(), "yyyy-MM-dd");
                        String[] split = startDate.split("-");
                        babysittingServiceAgreementDTO.setConsultationServiceEndYear(split[0]);
                        babysittingServiceAgreementDTO.setConsultationServiceEndMonth(split[1]);
                        babysittingServiceAgreementDTO.setConsultationServiceEndDay(split[2]);
                    }
                    babysittingServiceAgreementDTO.setServiceStartYear("/");
                    babysittingServiceAgreementDTO.setServiceStartMonth("/");
                    babysittingServiceAgreementDTO.setServiceStartDay("/");
                    babysittingServiceAgreementDTO.setServiceEndYear("/");
                    babysittingServiceAgreementDTO.setServiceEndMonth("/");
                    babysittingServiceAgreementDTO.setServiceEndDay("/");

                }else if(CTSSitterLevelEnum.ServiceComboLevelEnum.DOUBLE_EXTRACT_SERVICE.getCode().equals(serviceComboLevelEnum.getCode())){

                    if (orderCtsPO.getExpectServeStartDate() != null) {
                        String startDate = DateUtil.format(orderCtsPO.getExpectServeStartDate(), "yyyy-MM-dd");
                        String[] split = startDate.split("-");
                        babysittingServiceAgreementDTO.setServiceStartYear(split[0]);
                        babysittingServiceAgreementDTO.setServiceStartMonth(split[1]);
                        babysittingServiceAgreementDTO.setServiceStartDay(split[2]);

                        babysittingServiceAgreementDTO.setConsultationServiceStartYear(split[0]);
                        babysittingServiceAgreementDTO.setConsultationServiceStartMonth(split[1]);
                        babysittingServiceAgreementDTO.setConsultationServiceStartDay(split[2]);

                    }
                    if (orderCtsPO.getExpectServeEndDate() != null) {
                        String startDate = DateUtil.format(orderCtsPO.getExpectServeEndDate(), "yyyy-MM-dd");
                        String[] split = startDate.split("-");
                        babysittingServiceAgreementDTO.setServiceEndYear(split[0]);
                        babysittingServiceAgreementDTO.setServiceEndMonth(split[1]);
                        babysittingServiceAgreementDTO.setServiceEndDay(split[2]);

                        babysittingServiceAgreementDTO.setConsultationServiceEndYear(split[0]);
                        babysittingServiceAgreementDTO.setConsultationServiceEndMonth(split[1]);
                        babysittingServiceAgreementDTO.setConsultationServiceEndDay(split[2]);

                    }
                }
            }
        }

        Integer number = 1;
        if(orderCtsPO.getNumber() != null){
            number = orderCtsPO.getNumber();
        }
        babysittingServiceAgreementDTO.setServiceDay(String.valueOf(homeCustomerProductPO.getComboDays() != null ? homeCustomerProductPO.getComboDays() * number : 0));


        /*
          产品说不选
         */
        babysittingServiceAgreementDTO.setServiceFee(orderCtsPO.getPayableAmount().toEngineeringString());
        babysittingServiceAgreementDTO.setServiceFeeCapitalize(Convert.digitToChinese(orderCtsPO.getPayableAmount()).replace("元整", ""));

        babysittingServiceAgreementDTO.setServiceFeeDayAverage(orderCtsPO.getDailyPayableAmount().toEngineeringString());
        babysittingServiceAgreementDTO.setServiceFeeDayAverageCapitalize(Convert.digitToChinese(orderCtsPO.getDailyPayableAmount()).replace("元整", ""));

        babysittingServiceAgreementDTO.setCompanyBankOpenAccountName("杭州贝康恩护家政服务有限公司");
        babysittingServiceAgreementDTO.setCompanyBankOpenAccountBank("中信银行杭州萧山支行");
        babysittingServiceAgreementDTO.setCompanyBankAccount("8110801012701957977");

        babysittingServiceAgreementDTO.setCustomerSignMobile(userESignPO.getPhone());
        babysittingServiceAgreementDTO.setStaffSignContact(orderCtsPO.getSellName());

        String now = DateUtil.format(new Date(), "yyyy-MM-dd");
        String[] split = now.split("-");
        babysittingServiceAgreementDTO.setSignYear(split[0]);
        babysittingServiceAgreementDTO.setSignMonth(split[1]);
        babysittingServiceAgreementDTO.setSignDay(split[2]);
        if (StringUtils.isBlank(orderCtsPO.getRemark())) {
            orderCtsPO.setRemark("无");
        }
        babysittingServiceAgreementDTO.setRemark(orderCtsPO.getRemark());

        return babysittingServiceAgreementDTO;

    }


    /**
     * 雇主母婴
     *
     * @param orderCtsPO            订单对象
     * @param userESignPO           易签宝认证用户对象
     * @param homeCustomerProductPO 商品对象
     * @param addressMap            地址map
     * @param contractNo            合同编号
     * @return MaternalChildNursingServiceAgreementDTO
     */
    public MaternalChildNursingServiceAgreementDTO buildMaternalChildNuring(OrderCtsPO orderCtsPO, UserESignPO userESignPO, HomeCustomerProductPO homeCustomerProductPO, Map<Integer, String> addressMap, String contractNo,CustomerInfoCtsDetailVO ctsCustomerDetail) {


        MaternalChildNursingServiceAgreementDTO maternalChildNursingServiceAgreementDTO = new MaternalChildNursingServiceAgreementDTO();

        maternalChildNursingServiceAgreementDTO.setCustomerName(userESignPO.getName());
        maternalChildNursingServiceAgreementDTO.setCompanyContact(orderCtsPO.getSellName());
        maternalChildNursingServiceAgreementDTO.setSignNumber(contractNo);
        maternalChildNursingServiceAgreementDTO.setSignAddress("杭州市萧山区信息港六期杭州湾数字健康创新谷6号楼");
        maternalChildNursingServiceAgreementDTO.setSignDate(DateUtil.format(new Date(), "yyyy.MM.dd"));
        maternalChildNursingServiceAgreementDTO.setCerType(IdTypeEnum.getValueByCode(userESignPO.getCerType()));
        maternalChildNursingServiceAgreementDTO.setIdNumber(userESignPO.getCerNo());
        maternalChildNursingServiceAgreementDTO.setCustomerMobile(userESignPO.getPhone());
        String homeAddress = Joiner.on("").join(Arrays.asList(
                addressMap.getOrDefault(orderCtsPO.getLiveProvinceAddressId(), ""),
                addressMap.getOrDefault(orderCtsPO.getLiveCityAddressId(), ""),
                addressMap.getOrDefault(orderCtsPO.getLiveAreaAddressId(), ""),
                (StringUtils.isNotBlank(orderCtsPO.getLiveAddress())?orderCtsPO.getLiveAddress():"")));
        maternalChildNursingServiceAgreementDTO.setHomeAddress(homeAddress);
        maternalChildNursingServiceAgreementDTO.setBusinessAddress("浙江省杭州市萧山区经济技术开发区明星路371号2幢308-1室");
        if(orderCtsPO.getBabyNum() != null&&orderCtsPO.getBabyNum() > 0){
            maternalChildNursingServiceAgreementDTO.setBabyCountFlag("√");
            maternalChildNursingServiceAgreementDTO.setBabyCount(String.valueOf(orderCtsPO.getBabyNum()));
        }
        maternalChildNursingServiceAgreementDTO.setMomCountFlag("√");
        maternalChildNursingServiceAgreementDTO.setMomCount("1");
        maternalChildNursingServiceAgreementDTO.setSpecialDemand(replace(orderCtsPO.getSpecialRequire()));


        String allName = Joiner.on("").join(Arrays.asList(
                addressMap.getOrDefault(orderCtsPO.getServeProvinceAddressId(),""),
                addressMap.getOrDefault(orderCtsPO.getServeCityAddressId(),""),
                addressMap.getOrDefault(orderCtsPO.getServeAreaAddressId(),""),
                (StringUtils.isNotBlank(orderCtsPO.getServeAddress())?orderCtsPO.getServeAddress():"")));


        maternalChildNursingServiceAgreementDTO.setServiceAllAddress(allName);

        if(ctsCustomerDetail != null){
            StringBuilder stringBuilder = new StringBuilder();
            if(StringUtils.isNotBlank(ctsCustomerDetail.getEmergencyContactName())){
                stringBuilder.append(ctsCustomerDetail.getEmergencyContactName());
            }
            if(StringUtils.isNotBlank(ctsCustomerDetail.getEmergencyContactMobile())){
                stringBuilder.append("(").append(ctsCustomerDetail.getEmergencyContactMobile()).append(")");
            }
            maternalChildNursingServiceAgreementDTO.setEmergencyContactInfo(stringBuilder.toString());
        }

        if(StringUtils.isBlank(maternalChildNursingServiceAgreementDTO.getEmergencyContactInfo())){
            maternalChildNursingServiceAgreementDTO.setEmergencyContactInfo("/");
        }

        if (orderCtsPO.getServeType() != null) {
            ServeTypeEnum enumByCode = ServeTypeEnum.getEnumByCode(orderCtsPO.getServeType());
            switch (enumByCode) {
                case ALL_DAY:
                    maternalChildNursingServiceAgreementDTO.setAllDayInHome("√");
                    break;
                case WORK_DAY:
                    maternalChildNursingServiceAgreementDTO.setDayTakeCare("√");
                    break;
                default:
                    maternalChildNursingServiceAgreementDTO.setServiceOther("√");
                    maternalChildNursingServiceAgreementDTO.setServiceOtherDes(orderCtsPO.getServeTypeRemark());
                    break;
            }
        }


        CTSMaternalChildCareLevelEnum cTSMaternalChildCareLevelEnum = CTSMaternalChildCareLevelEnum.getEnumByCode(homeCustomerProductPO.getComboLevel());
        switch (cTSMaternalChildCareLevelEnum) {
            case CONFINEMENT_CLUB:
                maternalChildNursingServiceAgreementDTO.setConfinementClub("√");
                break;
            case BOUTIQUE:
                maternalChildNursingServiceAgreementDTO.setBoutique("√");
                break;
            case PRIMARY:
                maternalChildNursingServiceAgreementDTO.setPrimary("√");
                break;
            case MIDDLE:
                maternalChildNursingServiceAgreementDTO.setMiddle("√");
                break;
            default:
                maternalChildNursingServiceAgreementDTO.setHigh("√");
                break;
        }


        if (orderCtsPO.getExpectServeStartDate() != null) {
            String startDate = DateUtil.format(orderCtsPO.getExpectServeStartDate(), "yyyy-MM-dd");
            String[] split = startDate.split("-");
            maternalChildNursingServiceAgreementDTO.setServiceStartYear(split[0]);
            maternalChildNursingServiceAgreementDTO.setServiceStartMonth(split[1]);
            maternalChildNursingServiceAgreementDTO.setServiceStartDay(split[2]);
        }
        if (orderCtsPO.getExpectServeEndDate() != null) {
            String startDate = DateUtil.format(orderCtsPO.getExpectServeEndDate(), "yyyy-MM-dd");
            String[] split = startDate.split("-");
            maternalChildNursingServiceAgreementDTO.setServiceEndYear(split[0]);
            maternalChildNursingServiceAgreementDTO.setServiceEndMonth(split[1]);
            maternalChildNursingServiceAgreementDTO.setServiceEndDay(split[2]);
        }
        Integer number = 1;
        if(orderCtsPO.getNumber() != null){
            number = orderCtsPO.getNumber();
        }
        maternalChildNursingServiceAgreementDTO.setServiceDay(String.valueOf(homeCustomerProductPO.getComboDays() != null ? homeCustomerProductPO.getComboDays() * number : 0));

        maternalChildNursingServiceAgreementDTO.setServiceFee(orderCtsPO.getPayableAmount().toEngineeringString());
        maternalChildNursingServiceAgreementDTO.setServiceFeeCapitalize(Convert.digitToChinese(orderCtsPO.getPayableAmount()).replace("元整", ""));
        maternalChildNursingServiceAgreementDTO.setServiceFeeDayAverage(orderCtsPO.getDailyPayableAmount().toEngineeringString());
        maternalChildNursingServiceAgreementDTO.setServiceFeeDayAverageCapitalize(Convert.digitToChinese(orderCtsPO.getDailyPayableAmount()));

        //TODO 名字不匹配
        maternalChildNursingServiceAgreementDTO.setCompanyBankOpenAccountName("杭州贝康恩护家政服务有限公司");
        maternalChildNursingServiceAgreementDTO.setCompanyBankOpenAccountBank("中信银行杭州萧山支行");
        maternalChildNursingServiceAgreementDTO.setCompanyBankAccount("8110801012701957977");


        maternalChildNursingServiceAgreementDTO.setCustomerSignMobile(userESignPO.getPhone());
        maternalChildNursingServiceAgreementDTO.setStaffSignContact(orderCtsPO.getSellName());

        String now = DateUtil.format(new Date(), "yyyy-MM-dd");
        String[] split = now.split("-");
        maternalChildNursingServiceAgreementDTO.setSignYear(split[0]);
        maternalChildNursingServiceAgreementDTO.setSignMonth(split[1]);
        maternalChildNursingServiceAgreementDTO.setSignDay(split[2]);
        if (StringUtils.isBlank(orderCtsPO.getRemark())) {
            orderCtsPO.setRemark("无");
        }
        maternalChildNursingServiceAgreementDTO.setRemark(orderCtsPO.getRemark());

        return maternalChildNursingServiceAgreementDTO;

    }

    /**
     * 平台管理费
     * @param orderCtsPO 订单对象
     * @param userESignPO 易签宝认证对象
     * @param addressMap 地址map
     * @param contractNo 合同编号
     * @return SitterPlatformServeFeeAgreementDTO
     */
    public SitterPlatformServeFeeAgreementDTO buildSitterPlatformServeFeeAgreementDTO(OrderCtsPO orderCtsPO, UserESignPO userESignPO, Map<Integer, String> addressMap,BaseSitterPO baseSitterPO, String contractNo) {
        SitterPlatformServeFeeAgreementDTO sitterPlatformServeFeeAgreementDTO = new SitterPlatformServeFeeAgreementDTO();
        sitterPlatformServeFeeAgreementDTO.setSignNumber(contractNo);
        sitterPlatformServeFeeAgreementDTO.setSignAddress("杭州市萧山区信息港六期杭州湾数字健康创新谷6号楼");
        sitterPlatformServeFeeAgreementDTO.setSignDate(DateUtil.format(new Date(), "yyyy.MM.dd"));
        sitterPlatformServeFeeAgreementDTO.setCompanyContact(orderCtsPO.getSellName());
        sitterPlatformServeFeeAgreementDTO.setBusinessAddress("浙江省杭州市萧山区经济技术开发区明星路371号2幢308-1室");
        sitterPlatformServeFeeAgreementDTO.setSitterName(userESignPO.getName());
        sitterPlatformServeFeeAgreementDTO.setIdNumber(userESignPO.getCerNo());
        sitterPlatformServeFeeAgreementDTO.setSitterMobile(userESignPO.getPhone());
        StringBuilder stringBuffer = new StringBuilder();
        if(StringUtils.isNotBlank(baseSitterPO.getEmergencyContact())){
            stringBuffer.append(baseSitterPO.getEmergencyContact());
        }
        if(StringUtils.isNotBlank(baseSitterPO.getEmergencyContactPhone())){
            stringBuffer.append("(").append(baseSitterPO.getEmergencyContactPhone()).append(")");
        }
        sitterPlatformServeFeeAgreementDTO.setEmergencyMobile(StringUtils.isNotBlank(stringBuffer.toString())?stringBuffer.toString():"/");

        StringBuilder sb = new StringBuilder();
        if (orderCtsPO.getLiveProvinceAddressId() != null) {
            sb.append(addressMap.get(orderCtsPO.getLiveProvinceAddressId()));
        }
        if (orderCtsPO.getLiveCityAddressId() != null) {
            sb.append(addressMap.get(orderCtsPO.getLiveCityAddressId()));
        }
        if (orderCtsPO.getLiveAreaAddressId() != null) {
            sb.append(addressMap.get(orderCtsPO.getLiveAreaAddressId()));
        }
        String homeAddress = sb.toString() + (StringUtils.isNotBlank(orderCtsPO.getLiveAddress())?orderCtsPO.getLiveAddress():"");
        sitterPlatformServeFeeAgreementDTO.setHomeAddress(StringUtils.isNotBlank(homeAddress)?homeAddress:"/");


        if (orderCtsPO.getServeType() != null) {
            ServeTypeEnum enumByCode = ServeTypeEnum.getEnumByCode(orderCtsPO.getServeType());
            switch (enumByCode) {
                case ALL_DAY:
                    sitterPlatformServeFeeAgreementDTO.setAllDayInHome("√");
                    break;
                case WORK_DAY:
                    sitterPlatformServeFeeAgreementDTO.setDayTakeCare("√");
                    break;
                case AUNT_SITTER:
                    sitterPlatformServeFeeAgreementDTO.setAuntSitterCare("√");
                    break;
                case BABY_SITTER:
                    sitterPlatformServeFeeAgreementDTO.setBabySitterCare("√");
                    break;
                case Home_Companion:
                    sitterPlatformServeFeeAgreementDTO.setHomeCompanion("√");
                    break;
                default:
                    sitterPlatformServeFeeAgreementDTO.setServiceOther("√");
                    sitterPlatformServeFeeAgreementDTO.setServiceOtherDes(orderCtsPO.getServeTypeRemark());
                    break;
            }
        }

        if (orderCtsPO.getGmtCreate() != null) {
            String startDate = DateUtil.format(orderCtsPO.getGmtCreate(), "yyyy-MM-dd");
            String[] split = startDate.split("-");
            sitterPlatformServeFeeAgreementDTO.setServiceStartYear(split[0]);
            sitterPlatformServeFeeAgreementDTO.setServiceStartMonth(split[1]);
            sitterPlatformServeFeeAgreementDTO.setServiceStartDay(split[2]);

            String endDate = DateUtil.format(addYear(orderCtsPO.getGmtCreate(),1, -1), "yyyy-MM-dd");
            String[] endSplit = endDate.split("-");
            sitterPlatformServeFeeAgreementDTO.setServiceEndYear(endSplit[0]);
            sitterPlatformServeFeeAgreementDTO.setServiceEndMonth(endSplit[1]);
            sitterPlatformServeFeeAgreementDTO.setServiceEndDay(endSplit[2]);
        }

        sitterPlatformServeFeeAgreementDTO.setFirstYearManageFee(orderCtsPO.getPayableAmount().toEngineeringString());
        sitterPlatformServeFeeAgreementDTO.setFirstYearManageFeeCapitalize(Convert.digitToChinese(orderCtsPO.getPayableAmount()).replace("元整",""));
        //他们说先写死
        sitterPlatformServeFeeAgreementDTO.setPerformanceMarginCapitalize(Convert.digitToChinese(1000).replace("元整",""));
        sitterPlatformServeFeeAgreementDTO.setPerformanceMargin("1000");

        sitterPlatformServeFeeAgreementDTO.setCompanyBankOpenAccountName("杭州贝康恩护家政服务有限公司");
        sitterPlatformServeFeeAgreementDTO.setCompanyBankOpenAccountBank("中信银行杭州萧山支行");
        sitterPlatformServeFeeAgreementDTO.setCompanyBankAccount("8110801012701957977");

        sitterPlatformServeFeeAgreementDTO.setStaffSignName(orderCtsPO.getSellName());
        sitterPlatformServeFeeAgreementDTO.setSitterSignMobile(userESignPO.getPhone());
        sitterPlatformServeFeeAgreementDTO.setSitterSignName(userESignPO.getName());


        String now = DateUtil.format(new Date(), "yyyy-MM-dd");
        String[] split = now.split("-");
        sitterPlatformServeFeeAgreementDTO.setSignYear(split[0]);
        sitterPlatformServeFeeAgreementDTO.setSignMonth(split[1]);
        sitterPlatformServeFeeAgreementDTO.setSignDay(split[2]);
        sitterPlatformServeFeeAgreementDTO.setRemark(StringUtils.isNotBlank(orderCtsPO.getRemark())?orderCtsPO.getRemark():"无");
        return sitterPlatformServeFeeAgreementDTO;

    }


    /**
     * 培训订单
     * @param orderCtsPO 订单
     * @param userESignPO 认证
     * @param addressMap 地址
     * @param baseSitterPO 育婴师
     * @param homeSitterProductPO 育婴师商品
     * @param contractNo 合同编号
     * @return SitterTrainFeeAgreementDTO dto
     */
    public SitterTrainFeeAgreementDTO buildSitterTrainFeeAgreementDTO(OrderCtsPO orderCtsPO, UserESignPO userESignPO, Map<Integer, String> addressMap, BaseSitterPO baseSitterPO, HomeSitterProductPO homeSitterProductPO, String contractNo){
        SitterTrainFeeAgreementDTO sitterTrainFeeAgreementDTO = new SitterTrainFeeAgreementDTO();
        sitterTrainFeeAgreementDTO.setSignNumber(contractNo);
        sitterTrainFeeAgreementDTO.setSitterName(userESignPO.getName());
        sitterTrainFeeAgreementDTO.setIdNumber(userESignPO.getCerNo());
        sitterTrainFeeAgreementDTO.setSitterMobile(userESignPO.getPhone());
        sitterTrainFeeAgreementDTO.setRemark(StringUtils.isNotBlank(orderCtsPO.getRemark())?orderCtsPO.getRemark():"无");

        Map<String, Object> extMap = ExtraUtil.jsonFormat(orderCtsPO.getExt());

        //线上 时间
        sitterTrainFeeAgreementDTO.setTrainStartYear("/");
        sitterTrainFeeAgreementDTO.setTrainStartMonth("/");
        sitterTrainFeeAgreementDTO.setTrainStartDay("/");
        sitterTrainFeeAgreementDTO.setTrainEndYear("/");
        sitterTrainFeeAgreementDTO.setTrainEndMonth("/");
        sitterTrainFeeAgreementDTO.setTrainEndDay("/");

        //线下 时间
        sitterTrainFeeAgreementDTO.setOfflineTrainStartYear("/");
        sitterTrainFeeAgreementDTO.setOfflineTrainStartMonth("/");
        sitterTrainFeeAgreementDTO.setOfflineTrainStartDay("/");
        sitterTrainFeeAgreementDTO.setOfflineTrainEndYear("/");
        sitterTrainFeeAgreementDTO.setOfflineTrainEndMonth("/");
        sitterTrainFeeAgreementDTO.setOfflineTrainEndDay("/");




        sitterTrainFeeAgreementDTO.setOpeningTimeYear("/");
        sitterTrainFeeAgreementDTO.setOpeningTimeMonth("/");
        sitterTrainFeeAgreementDTO.setTrainingAddress("/");

        if (Objects.nonNull(extMap.get(CourseOrderCreateRequest.Fields.trainingAddress))){
            sitterTrainFeeAgreementDTO.setTrainingAddress(extMap.get(CourseOrderCreateRequest.Fields.trainingAddress).toString());
        }
        if (Objects.nonNull(extMap.get(CourseOrderCreateRequest.Fields.openingTime))){
            String openingTimeObj = extMap.get(CourseOrderCreateRequest.Fields.openingTime).toString();
            Date openingTime = new Date( Long.parseLong(openingTimeObj));
            int[] yearMonthDay = DateUtils.getYearMonthDay(openingTime);

            sitterTrainFeeAgreementDTO.setOpeningTimeYear(yearMonthDay[0]+"");
            sitterTrainFeeAgreementDTO.setOpeningTimeMonth(yearMonthDay[1]+"");
        }

        Date trainStart = null;

        if (Objects.nonNull(extMap.get(CourseOrderCreateRequest.Fields.trainStart))){

            String openingTimeObj = extMap.get(CourseOrderCreateRequest.Fields.trainStart).toString();
            trainStart = new Date(Long.parseLong(openingTimeObj));
        }

        StringBuilder sb = new StringBuilder();

        if (ObjectUtil.isNull(baseSitterPO)){
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT, "同主体已变更，请重新创建订单");
        }

        if (baseSitterPO.getLiveProvinceAddressId() != null) {
            sb.append(addressMap.get(baseSitterPO.getLiveProvinceAddressId()));
        }
        if (baseSitterPO.getLiveCityAddressId() != null) {
            sb.append(addressMap.get(baseSitterPO.getLiveCityAddressId()));
        }
        if (baseSitterPO.getLiveAreaAddressId() != null) {
            sb.append(addressMap.get(baseSitterPO.getLiveAreaAddressId()));
        }
        String homeAddress = sb.toString() + (StringUtils.isNotBlank(baseSitterPO.getLiveAddress())?baseSitterPO.getLiveAddress():"");
        sitterTrainFeeAgreementDTO.setHomeAddress(StringUtils.isNotBlank(homeAddress)?homeAddress:"/");

        sitterTrainFeeAgreementDTO.setEmergencyContact(StringUtils.isNotBlank(baseSitterPO.getEmergencyContact())?baseSitterPO.getEmergencyContact():"/");
        sitterTrainFeeAgreementDTO.setEmergencyContactPhone(StringUtils.isNotBlank(baseSitterPO.getEmergencyContactPhone())?baseSitterPO.getEmergencyContactPhone():"/");

        sitterTrainFeeAgreementDTO.setCourseName(homeSitterProductPO.getCourseName());
        sitterTrainFeeAgreementDTO.setCoursePrice(orderCtsPO.getPayableAmount().toEngineeringString());
        sitterTrainFeeAgreementDTO.setCoursePriceCapitalize(Convert.digitToChinese(orderCtsPO.getPayableAmount()).replace("元整",""));


        sitterTrainFeeAgreementDTO.setMixTrainDay("/");
        sitterTrainFeeAgreementDTO.setMixTrainClassHour("/");

        sitterTrainFeeAgreementDTO.setOnlineTrainDay("/");
        sitterTrainFeeAgreementDTO.setOnlineTrainClassHour("/");

        sitterTrainFeeAgreementDTO.setOfflineTrainDay("/");
        sitterTrainFeeAgreementDTO.setOfflineTrainClassHour("/");



        switch (CTSCourseTypeEnum.getEnumByCode(homeSitterProductPO.getCourseType())){
            case MIX:
                sitterTrainFeeAgreementDTO.setMixTrain("√");
                sitterTrainFeeAgreementDTO.setMixTrainDay(homeSitterProductPO.getCourseDays()!=null?homeSitterProductPO.getCourseDays().toEngineeringString():"/");
                sitterTrainFeeAgreementDTO.setMixTrainClassHour(homeSitterProductPO.getCourseHours()!=null?homeSitterProductPO.getCourseHours().toString():"/");

                if (Objects.nonNull(trainStart)){
                    Date endDate = initOlineTrainEndDate(trainStart, homeSitterProductPO);
                    sitterTrainFeeAgreementDTO = fillOnlineTrainTime(sitterTrainFeeAgreementDTO, trainStart, endDate);
                    sitterTrainFeeAgreementDTO = fillOfflineTrainTime(sitterTrainFeeAgreementDTO, trainStart);
                }

                break;

            case ONLINE_COURSE:
                sitterTrainFeeAgreementDTO.setOnlineTrain("√");
                sitterTrainFeeAgreementDTO.setOnlineTrainDay("/");
                sitterTrainFeeAgreementDTO.setOnlineTrainClassHour(homeSitterProductPO.getCourseHours().toString());
                if (Objects.nonNull(trainStart)){
                    Date endDate = initOlineTrainEndDate(trainStart, homeSitterProductPO);
                    sitterTrainFeeAgreementDTO = fillOnlineTrainTime(sitterTrainFeeAgreementDTO, trainStart,endDate);
                }
                break;

            case OFFLINE_COURSE:
                sitterTrainFeeAgreementDTO.setOfflineTrain("√");
                sitterTrainFeeAgreementDTO.setOfflineTrainDay(homeSitterProductPO.getCourseDays().toEngineeringString());
                sitterTrainFeeAgreementDTO.setOfflineTrainClassHour("/");
                if (Objects.nonNull(trainStart)){
                    sitterTrainFeeAgreementDTO = fillOfflineTrainTime(sitterTrainFeeAgreementDTO, trainStart);
                }
                break;
            default:
                break;
        }

        String now = DateUtil.format(new Date(), "yyyy-MM-dd");
        String[] split = now.split("-");
        sitterTrainFeeAgreementDTO.setSignYear(split[0]);
        sitterTrainFeeAgreementDTO.setSignMonth(split[1]);
        sitterTrainFeeAgreementDTO.setSignDay(split[2]);
        return sitterTrainFeeAgreementDTO;
    }

    /**
     * 填充线下培训时间
     * @param data
     * @param trainStart
     * @return
     */
    protected SitterTrainFeeAgreementDTO fillOfflineTrainTime(SitterTrainFeeAgreementDTO data, Date trainStart){

        int[] yearMonthDay = DateUtils.getYearMonthDay(trainStart);
        //线下 时间
        data.setOfflineTrainStartYear(yearMonthDay[0]+"");
        data.setOfflineTrainStartMonth(yearMonthDay[1]+"");
        data.setOfflineTrainStartDay(yearMonthDay[2]+"");

        Date trainEndTime = DateUtils.addMonth(trainStart, trainClassValidity.getOffline());
        int[] timeArray = DateUtils.getYearMonthDay(trainEndTime);
        data.setOfflineTrainEndYear(timeArray[0]+"");
        data.setOfflineTrainEndMonth(timeArray[1]+"");
        data.setOfflineTrainEndDay(timeArray[2]+"");

        return data;
    }

    /**
     * 填充线上培训时间
     * @param data
     * @param trainStart
     * @return
     */
    protected SitterTrainFeeAgreementDTO fillOnlineTrainTime(SitterTrainFeeAgreementDTO data, Date trainStart,  Date trainEnd){

        int[] yearMonthDay = DateUtils.getYearMonthDay(trainStart);
        data.setTrainStartYear(yearMonthDay[0]+"");
        data.setTrainStartMonth(yearMonthDay[1]+"");
        data.setTrainStartDay(yearMonthDay[2]+"");

        Date onlineTrainEndTime = trainEnd;
        int[] timeArray = DateUtils.getYearMonthDay(onlineTrainEndTime);
        data.setTrainEndYear(timeArray[0]+"");
        data.setTrainEndMonth(timeArray[1]+"");
        data.setTrainEndDay(timeArray[2]+"");

        return data;
    }


    private String replace(String src) {
        if (StringUtils.isNotBlank(src)) {
            return src.replaceAll("\n", "")
                    .replaceAll("\t", "");

        }
        return src;
    }

    public static Date addYear(Date date, int val, int day) {
        GregorianCalendar gc = new GregorianCalendar();
        gc.setTime(date);
        gc.add(Calendar.YEAR, val);
        gc.add(Calendar.DATE,day);
        return gc.getTime();
    }

    /**
     * 初始化线上培训结束时间
     * @param start
     * @param homeSitterProductPO
     * @return
     */
    public Date initOlineTrainEndDate(Date start, HomeSitterProductPO homeSitterProductPO) {

        Integer addMonth = homeSitterProductPO.getExpireMonth();
        if (addMonth == null || addMonth <= 0) {
            addMonth = trainClassValidity.getOnline();
        }

        Date trainEndTime = DateUtils.addMonth(start, addMonth);
        return trainEndTime;
    }
}


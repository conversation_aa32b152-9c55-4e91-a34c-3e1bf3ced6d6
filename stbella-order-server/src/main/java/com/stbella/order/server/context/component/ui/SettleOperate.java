package com.stbella.order.server.context.component.ui;

import com.stbella.notice.entity.OaProcessIdRelationPO;
import com.stbella.notice.server.OaProcessIdRelationService;
import com.stbella.order.common.enums.month.ContractStatusEnum;
import com.stbella.order.common.enums.month.OrderButtonEnum;
import com.stbella.order.common.enums.month.OrderStatusV2Enum;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.MonthContractSignRecordEntity;
import com.stbella.order.domain.order.service.ContractOpponentAuthDomainService;
import com.stbella.order.domain.repository.MonthContractSignRecordRepository;
import com.stbella.order.server.order.month.enums.PayStatusEnum;
import com.stbella.order.server.order.month.res.OrderOperateButton;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.stbella.order.common.enums.ErrorCodeEnum.CONTRACT_SUBJECT_NOT_AUTH;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-08-12  16:28
 * @Description: 结算操作
 */
@Component
@Slf4j
public class SettleOperate implements OrderOperate{

    @Resource
    ContractOpponentAuthDomainService contractOpponentAuthDomainService;

    @Override
    public void setPriority(int priority) {

    }

    @Override
    public int getPriority() {
        return 3;
    }

    /**
     * 生成订单操作按钮
     * @return
     */
    @Override
    public OrderOperateButton generateButton(OrderOperateContext context) {

        HeOrderEntity order = context.getOrder();
        if (OrderStatusV2Enum.CLOSE.getCode().intValue() == order.getOrderStatus() || !order.isWaitPay()) {
            return null;
        }
        OrderOperateButton button = OrderOperateButton.builder().code(getButtonEnum().getCode()).value(getButtonEnum().getValue()).build();
//        if (!contractOpponentAuthDomainService.getAuthState(order)) {
//            button.setText(CONTRACT_SUBJECT_NOT_AUTH.desc());
//        }
        return button;
    }

    /**
     * 获取按钮枚举
     *
     * @return
     */
    @Override
    public OrderButtonEnum getButtonEnum() {
        return OrderButtonEnum.COLLECTION;
    }

    @Override
    public int compareTo(@NotNull IPriority o) {
        return this.getPriority() - o.getPriority();
    }
}

package com.stbella.order.server.strategy.approval;

import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.order.server.order.month.request.approval.ApprovalStatusInfo;

/**
 * <AUTHOR>
 * 审批策略
 * @date 2024/3/18 16:33
 */

public interface ApprovalStrategy {

    void handleApproval(OrderApproveRecordTypeEnum type, String params, String messageId, ApprovalStatusInfo approvalStatusInfo, String processId);

    OrderApproveRecordTypeEnum getOrderApproveRecordTypeEnum();
}

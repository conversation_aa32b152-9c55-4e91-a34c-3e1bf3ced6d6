package com.stbella.order.server.context.component.complaints.processor;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.stbella.order.common.constant.CustomerComplaintConstant;
import com.stbella.order.common.enums.core.OrderRefundNatureEnum;
import com.stbella.order.domain.order.entity.HeCartGoodsEntity;
import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsEntity;
import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsRefundDraftEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.production.GoodsEntity;
import com.stbella.order.domain.repository.GoodsRepository;
import com.stbella.order.domain.repository.HeCartGoodsRepository;
import com.stbella.order.domain.repository.HeCustomerComplaintsRefundDraftRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.order.month.enums.RefundTypeEnum;
import com.stbella.order.server.order.month.req.SubmitRefundApplyV2Request;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.order.server.utils.JsonUtil;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import com.stbella.platform.order.api.req.CustomerComplaintsCreateRefundReq;
import com.stbella.platform.order.api.req.CustomerComplaintsCreateReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
@SnowballComponent(name = "草稿新旧退款", desc = "草稿新旧退款")
public class ComplaintRefundDraftProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private HeCustomerComplaintsRefundDraftRepository customerComplaintsRefundDraftRepository;

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private HeCartGoodsRepository heCartGoodsRepository;

    @Resource
    private GoodsRepository goodsRepository;

    @Override
    public void run(FlowContext bizContext) {

        CustomerComplaintsCreateReq req = bizContext.getAttribute(CustomerComplaintsCreateReq.class);

        HeCustomerComplaintsEntity customerComplaintsEntity = bizContext.getAttribute(HeCustomerComplaintsEntity.class);

        Long complaintsId = customerComplaintsEntity.getId();

        if (ObjectUtil.isNotEmpty(customerComplaintsEntity) && ObjectUtil.isNotEmpty(customerComplaintsEntity.getCompensationOrderId())) {
            complaintsId = customerComplaintsEntity.getId();
            HeOrderEntity compensationOrder = orderRepository.getByOrderId(customerComplaintsEntity.getOrderId().intValue());
            bizContext.setAttribute(CustomerComplaintConstant.COMPLAINT_ZERO_ORDER, compensationOrder);
        }

        HeOrderEntity orderEntity = orderRepository.getByOrderId(req.getOrderId().intValue());

        setDraft(orderEntity, complaintsId, req);
        bizContext.setAttribute(CustomerComplaintConstant.COMPLAINT_ORDER, orderEntity);
    }

    private void setDraft(HeOrderEntity orderEntity, Long complaintsId, CustomerComplaintsCreateReq attribute) {
        CustomerComplaintsCreateRefundReq customerComplaintsCreateRefundReq = attribute.getCreateRefundReq();

        String refundInfo = "{}";

        Boolean newRefund = false;

        BigDecimal refundAmount = BigDecimal.ZERO;

        if (ObjectUtil.isNotEmpty(orderEntity.getVersion()) && orderEntity.getVersion().compareTo(new BigDecimal(3.0)) >= 0) {
            //新订单
            CreateRefundReq createRefundReq = customerComplaintsCreateRefundReq.getNewCreateRefundReq();

            if (ObjectUtil.isNotEmpty(createRefundReq) && ObjectUtil.isNotEmpty(createRefundReq.getOrderId())) {
                createRefundReq.setRefundReasonType(2);
                createRefundReq.setComplaintId(complaintsId);
                createRefundReq.setRefundTypeStr(RefundTypeEnum.getValueByCode(createRefundReq.getRefundType()));
                createRefundReq.setGoodsRefundTypeStr(OrderRefundNatureEnum.getValueByCode(createRefundReq.getGoodsRefundType()));
                refundInfo = JsonUtil.write(createRefundReq);

                for (CreateRefundReq.GoodsInfo goodsInfo : createRefundReq.getGoodsInfoList()) {
                    List<CreateRefundReq.GoodsRefundAmountInfo> refundGoodsAmountInfoList = goodsInfo.getRefundGoodsAmountInfoList();
                    refundGoodsAmountInfoList = refundGoodsAmountInfoList.stream().filter(r -> r.getAmountType() != 2 &&
                            r.getAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                    refundAmount = refundAmount.add(refundGoodsAmountInfoList.stream().map(CreateRefundReq.GoodsRefundAmountInfo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                }
            }

            newRefund = true;
        } else {
            //老订单
            SubmitRefundApplyV2Request submitRefundApplyV2Request = customerComplaintsCreateRefundReq.getOldCreateRefundReq();

            if (ObjectUtil.isNotEmpty(submitRefundApplyV2Request) && ObjectUtil.isNotEmpty(submitRefundApplyV2Request.getOrderId())) {
                submitRefundApplyV2Request.setComplaintId(complaintsId);
                submitRefundApplyV2Request.setRefundModelStr(RefundTypeEnum.getValueByCode(submitRefundApplyV2Request.getRefundModel()));
                refundInfo = JsonUtil.write(submitRefundApplyV2Request);
                refundAmount = submitRefundApplyV2Request.getRefundAmount();
            }
        }

        HeCustomerComplaintsRefundDraftEntity complaintsRefundDraftEntity = customerComplaintsRefundDraftRepository.queryByComplaintsId(complaintsId);

        if (ObjectUtil.isEmpty(complaintsRefundDraftEntity)) {
            complaintsRefundDraftEntity = new HeCustomerComplaintsRefundDraftEntity();
        }

        complaintsRefundDraftEntity.setNewOrOldRefund(BooleanUtil.toInt(newRefund));
        complaintsRefundDraftEntity.setComplaintsId(complaintsId);
        complaintsRefundDraftEntity.setRefundInfo(refundInfo);
        complaintsRefundDraftEntity.setOrderId(orderEntity.getOrderId());
        complaintsRefundDraftEntity.setRefundAmount(customerComplaintsCreateRefundReq.getRefundAmount());
        complaintsRefundDraftEntity.setOrderRefundAmount(refundAmount);
        complaintsRefundDraftEntity.setAmountGoodsClaimed(getCartAmount(customerComplaintsCreateRefundReq.getCartId()));
        complaintsRefundDraftEntity.setAccountBank(customerComplaintsCreateRefundReq.getAccountBank());
        complaintsRefundDraftEntity.setAccountName(customerComplaintsCreateRefundReq.getAccountName());
        complaintsRefundDraftEntity.setBankNo(customerComplaintsCreateRefundReq.getBankNo());
        complaintsRefundDraftEntity.setHasExtraCompensationOpinion(customerComplaintsCreateRefundReq.getHasExtraCompensationOpinion());
        complaintsRefundDraftEntity.setReqJson(JsonUtil.write(attribute));

        customerComplaintsRefundDraftRepository.saveOrUpdate(complaintsRefundDraftEntity);
    }


    private BigDecimal getCartAmount(Integer cartId) {

        Integer allGoodsPrice = 0;

        if (ObjectUtil.isNotEmpty(cartId)) {

            List<HeCartGoodsEntity> heCartGoodsEntities = heCartGoodsRepository.queryListByCartId(cartId);

            if (CollectionUtil.isNotEmpty(heCartGoodsEntities)) {
                List<Integer> goodsIdList = heCartGoodsEntities.stream().map(HeCartGoodsEntity::getGoodsId).collect(Collectors.toList());

                List<GoodsEntity> goodsEntities = goodsRepository.selectByIdList(goodsIdList);

                if (CollectionUtil.isNotEmpty(goodsEntities)) {

                    for (HeCartGoodsEntity heOrderGoodsEntity : heCartGoodsEntities) {
                        Optional<GoodsEntity> first = goodsEntities.stream().filter(g -> g.getId().equals(heOrderGoodsEntity.getGoodsId())).findFirst();
                        if (first.isPresent()) {
                            allGoodsPrice += first.get().getGoodsPrice() * heOrderGoodsEntity.getNum();
                        }
                    }
                }
            }
        }

        return RMBUtils.bigDecimalF2Y(allGoodsPrice);
    }


    @Override
    public boolean condition(FlowContext context) {
//        CustomerComplaintsCreateReq req = context.getAttribute(CustomerComplaintsCreateReq.class);
        //无需校验，都存，查询详情的时候直接取，无需转换
        return true;
    }
}

package com.stbella.order.server.context.component.assembler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.core.OrderRefundGoodsStatusEnum;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.production.OrderProductionVerificationLogEntity;
import com.stbella.order.domain.repository.HeOrderRefundGoodsRepository;
import com.stbella.order.domain.repository.IncomePaidAllocationRepository;
import com.stbella.order.domain.repository.OrderGoodsRepository;
import com.stbella.order.domain.repository.OrderProductionVerificationLogRepository;
import com.stbella.order.server.manager.AssetManager;
import com.stbella.order.server.order.month.enums.PayMethodEnum;
import com.stbella.order.server.order.month.service.impl.OrderAssetTradeService;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.platform.order.api.refund.req.QueryRefundGoodsReq;
import com.stbella.platform.order.api.refund.res.QueryOrderRefundInfoRes;
import com.stbella.platform.order.api.refund.res.QueryRefundGoodsRes;
import com.stbella.platform.order.api.refund.res.RefundGoodsAmountInfo;
import com.stbella.store.common.enums.core.GoodsTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Component
@Slf4j
@SnowballComponent(name = "订单商品的支付退款情况", desc = "获取当前订单的支付信息")
public class OrderGoodsRefundPayInfoAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    private IncomePaidAllocationRepository incomePaidAllocationRepository;
    @Resource
    private HeOrderRefundGoodsRepository orderRefundGoodsRepository;
    @Resource
    private AssetManager assetManager;
    @Resource
    private OrderGoodsRepository orderGoodsRepository;
    @Resource
    private OrderAssetTradeService assetTradeService;
    @Resource
    private OrderProductionVerificationLogRepository orderProductionVerificationLogRepository;


    @Override
    public void run(FlowContext bizContext) {
        QueryRefundGoodsReq queryRefundGoodsReq = bizContext.getAttribute(QueryRefundGoodsReq.class);
        QueryOrderRefundInfoRes result = bizContext.getAttribute(QueryOrderRefundInfoRes.class);
        HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);

        Integer orderId = queryRefundGoodsReq.getOrderId();
        List<IncomePaidAllocationEntity> incomePaidAllocationEntities = incomePaidAllocationRepository.queryListByOrderId(orderId.longValue());
        List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList = orderRefundGoodsRepository.queryByOrderId(orderId);
        List<HeOrderRefundGoodsAmountEntity> refundGoodsAmountEntities = new ArrayList<>();

        log.info("订单商品的支付退款情况，orderId={}, incomePaidAllocationEntities={}, orderRefundGoodsEntityList={}",
                orderId,
                JSONObject.toJSONString(incomePaidAllocationEntities),
                JSONObject.toJSONString(orderRefundGoodsEntityList));

        // 步骤1：处理所有商品，设置各种金额信息
        List<QueryRefundGoodsRes> queryRefundGoodsResList = result.getQueryRefundGoodsResList();

        // 定义状态优先级顺序：礼赠组合子-礼赠商品-购买组合子-购买
        Collections.sort(queryRefundGoodsResList, Comparator.comparingInt(o -> {
            Integer gift = o.getGift();
            List<QueryRefundGoodsRes> child = o.getChild();
            if (ObjectUtil.isNotEmpty(gift) && gift == 1 && CollectionUtil.isNotEmpty(child)) {
                return 1;
            } else if (ObjectUtil.isNotEmpty(gift) && gift == 1 && CollectionUtil.isEmpty(child)) {
                return 2;
            } else if (ObjectUtil.isNotEmpty(gift) && gift == 0 && CollectionUtil.isNotEmpty(child)) {
                return 3;
            } else if (ObjectUtil.isNotEmpty(gift) && gift == 0 && CollectionUtil.isEmpty(child)) {
                return 4;
            } else {
                return 5;
            }
        }));
        List<HeOrderGoodsEntity> orderGoodsEntityList = orderGoodsRepository.getByOrderIdList(Collections.singletonList(order.getOrderId()));
        List<HeOrderGoodsEntity> heOrderGoodsEntityList = orderGoodsEntityList.stream().filter(orderGoodsEntity -> !CombineTypeEnum.COMBINE.code().equals(orderGoodsEntity.getType())).collect(Collectors.toList());
        //根据goods和sku进行数量获取
        Map<String, Integer> availableNumMap = assetManager.getCountByGoodsSn(order.getOrderSn(), heOrderGoodsEntityList);

        //拉取所有订单核销的
        List<OrderProductionVerificationLogEntity> orderProductionVerificationLogEntities = orderProductionVerificationLogRepository.queryVerificationByOrderId(orderId);
        //根据goods和sku进行数量获取
        Map<String, Integer> countByGoodsAndSkuId = orderProductionVerificationLogEntities.stream()
                .collect(Collectors.groupingBy(
                        obj -> {
                            // 假设你的对象有 getSkuId() 和 getGoodsId() 方法
                            Integer skuId = obj.getSkuId();
                            Integer goodsId = obj.getGoodsId();
                            return goodsId + "+" + skuId; // 组合成key
                        },
                        Collectors.summingInt(e -> 1) // 计算每个key出现的次数
                ));

        processAllGoods(queryRefundGoodsResList, incomePaidAllocationEntities, orderRefundGoodsEntityList, refundGoodsAmountEntities, availableNumMap, countByGoodsAndSkuId);

        // 步骤2：计算总的已退款金额
        BigDecimal totalAlreadyRefundAmount = calculateTotalAlreadyRefundAmount(queryRefundGoodsResList);

        result.setAlreadyRefundAmount(totalAlreadyRefundAmount);
    }

    private void processAllGoods(List<QueryRefundGoodsRes> goodsList,
                                 List<IncomePaidAllocationEntity> incomePaidAllocationEntities,
                                 List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList,
                                 List<HeOrderRefundGoodsAmountEntity> refundGoodsAmountEntities,
                                 Map<String, Integer> availableNumMap,
                                 Map<String, Integer> countByGoodsAndSkuId) {
        goodsList.forEach(goods -> processGoodsAndChildren(goods, incomePaidAllocationEntities, orderRefundGoodsEntityList, refundGoodsAmountEntities, availableNumMap, countByGoodsAndSkuId));
    }

    private void processGoodsAndChildren(QueryRefundGoodsRes goods,
                                         List<IncomePaidAllocationEntity> incomePaidAllocationEntities,
                                         List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList,
                                         List<HeOrderRefundGoodsAmountEntity> refundGoodsAmountEntities,
                                         Map<String, Integer> availableNumMap,
                                         Map<String, Integer> countByGoodsAndSkuId) {
        // 关键步骤：设置商品的金额信息（冻结金额、已付金额、已退金额等）
        setGoodsAmountInfo(goods, incomePaidAllocationEntities, orderRefundGoodsEntityList, availableNumMap, countByGoodsAndSkuId);

        // 处理子商品
        if (CollectionUtil.isNotEmpty(goods.getChild())) {
            processAllGoods(goods.getChild(), incomePaidAllocationEntities, orderRefundGoodsEntityList, refundGoodsAmountEntities, availableNumMap, countByGoodsAndSkuId);
            updateParentAmount(goods);
        }

        // 处理附加商品
        if (CollectionUtil.isNotEmpty(goods.getAdditionList())) {
            processAllGoods(goods.getAdditionList(), incomePaidAllocationEntities, orderRefundGoodsEntityList, refundGoodsAmountEntities, availableNumMap, countByGoodsAndSkuId);
        }
    }

    private BigDecimal calculateTotalAlreadyRefundAmount(List<QueryRefundGoodsRes> goodsList) {
        return goodsList.stream().map(this::calculateAlreadyRefundAmountForGoodsAndChildren).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal calculateAlreadyRefundAmountForGoodsAndChildren(QueryRefundGoodsRes goods) {
        BigDecimal alreadyRefundAmount = goods.getAlreadyRefundAmount();

        if (goods.getChild() != null) {
            alreadyRefundAmount = alreadyRefundAmount.add(calculateTotalAlreadyRefundAmount(goods.getChild()));
        }

        if (goods.getAdditionList() != null) {
            alreadyRefundAmount = alreadyRefundAmount.add(calculateTotalAlreadyRefundAmount(goods.getAdditionList()));
        }

        return alreadyRefundAmount;
    }

    private void updateParentAmount(QueryRefundGoodsRes parent) {
        List<QueryRefundGoodsRes> parentChild = parent.getChild();
        parent.setRefundGoodsNum(parentChild.stream().mapToInt(QueryRefundGoodsRes::getRefundGoodsNum).sum());
    }


    private void setGoodsAmountInfo(QueryRefundGoodsRes queryRefundGoodsRes,
                                    List<IncomePaidAllocationEntity> incomePaidAllocationEntities,
                                    List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList,
                                    Map<String, Integer> availableNumMap,
                                    Map<String, Integer> countByGoodsAndSkuId) {
        String orderGoodsSn = queryRefundGoodsRes.getOrderGoodsSn();

        //退款列表
        List<HeOrderRefundGoodsEntity> goodsRefundList = filterByOrderGoodsSn(orderRefundGoodsEntityList, orderGoodsSn, HeOrderRefundGoodsEntity::getOrderGoodsSn);

        List<IncomePaidAllocationEntity> goodsPaidList = incomePaidAllocationEntities.stream().filter(i -> i.getOrderGoodsSn().equals(queryRefundGoodsRes.getOrderGoodsSn())).collect(Collectors.toList());


        //当前商品金额列表
        List<RefundGoodsAmountInfo> refundGoodsAmountInfoList = new ArrayList<>();

        //冻结金额
        Map<PayMethodEnum, Integer> freezeAmount = calculateAmount(goodsRefundList, OrderRefundGoodsStatusEnum.REFUNDING);
        //退款成功
        Map<PayMethodEnum, Integer> alreadyRefundAmount = calculateAmount(goodsRefundList, OrderRefundGoodsStatusEnum.SUCCESS);

        // 先计算可退数量，后面设置单价时需要用到
        int alreadyRefundNum = calculateRefundableNum(goodsRefundList);

        Integer goodsNum = queryRefundGoodsRes.getGoodsNum();

        int leftNum = leftNum(queryRefundGoodsRes, incomePaidAllocationEntities, availableNumMap, alreadyRefundNum, countByGoodsAndSkuId);
        //已使用数量
        int useNum = goodsNum - alreadyRefundNum - leftNum;
        log.info("orderGoodsSn={}, 已退数量={}, 可退数量={}, 已使用数量={},总数={}", orderGoodsSn, alreadyRefundNum, leftNum, useNum, goodsNum);
        for (PayMethodEnum payMethodEnum : PayMethodEnum.values()) {
            RefundGoodsAmountInfo refundGoodsAmountInfo = new RefundGoodsAmountInfo();
            refundGoodsAmountInfo.setModel(payMethodEnum.getModel());
            refundGoodsAmountInfo.setLabel(payMethodEnum.getValue());
            refundGoodsAmountInfo.setPaymentMethod(payMethodEnum.getCode());
            setGoodsPaidAndRefund(goodsPaidList, freezeAmount, alreadyRefundAmount, refundGoodsAmountInfo, leftNum, useNum);
            refundGoodsAmountInfoList.add(refundGoodsAmountInfo);
        }
        //设置 这个商品产康金和现金的收退情况
        queryRefundGoodsRes.setRefundGoodsAmountInfoList(refundGoodsAmountInfoList);

        // 设置可退数量（已在上面计算过）
        queryRefundGoodsRes.setRefundGoodsNum(leftNum);

    }

    /**
     * 组合（A*10）
     * 普通A*5
     * 购买组合（A*10）
     * 购买普通A*5
     * <p>
     * 20
     */

    private void setGoodsPaidAndRefund(List<IncomePaidAllocationEntity> incomePaidAllocationEntities, Map<PayMethodEnum, Integer> freezeAmount, Map<PayMethodEnum, Integer> alreadyRefundAmount, RefundGoodsAmountInfo refundGoodsAmountInfo, int refundableNum, int usedNum) {
        String paymentMethod = refundGoodsAmountInfo.getPaymentMethod();
        int paidSum = incomePaidAllocationEntities.stream().filter(i -> PayMethodEnum.PayType2Currency(Integer.valueOf(i.getPaymentMethod())).getCode().equals(paymentMethod)).mapToInt(IncomePaidAllocationEntity::getPaidAmount).sum();

        BigDecimal paidAmount = RMBUtils.bigDecimalF2Y(paidSum);
        BigDecimal refundedAmount = BigDecimal.ZERO;
        BigDecimal freeAmount = BigDecimal.ZERO;

        //已付总金额

        if (CollectionUtil.isNotEmpty(alreadyRefundAmount)) {
            Integer i = alreadyRefundAmount.get(PayMethodEnum.getEnumByCode(paymentMethod));
            if (ObjectUtil.isNotEmpty(i)) {
                refundedAmount = RMBUtils.bigDecimalF2Y(i);
            }
            //已退总金额
        }
        if (CollectionUtil.isNotEmpty(freezeAmount)) {
            Integer i = freezeAmount.get(PayMethodEnum.getEnumByCode(paymentMethod));
            if (ObjectUtil.isNotEmpty(i)) {
                freeAmount = RMBUtils.bigDecimalF2Y(i);
            }
            //冻结总金额
        }

        // 计算可退金额单价：可退款金额除以可退数量
        BigDecimal refundableAmount = paidAmount.subtract(refundedAmount).subtract(freeAmount);
        BigDecimal refundableUnitPrice = BigDecimal.ZERO;
        if (refundableNum > 0 && refundableAmount.compareTo(BigDecimal.ZERO) > 0) {
            refundableUnitPrice = refundableAmount.divide(new BigDecimal(refundableNum + usedNum), 2, BigDecimal.ROUND_HALF_UP);
        }
        BigDecimal usedAmount = refundableUnitPrice.multiply(new BigDecimal(usedNum));
        BigDecimal subtract = refundableAmount.subtract(usedAmount);
        refundGoodsAmountInfo.setRefundAmount(subtract);
        refundGoodsAmountInfo.setRefundableUnitPrice(refundableUnitPrice);
        refundGoodsAmountInfo.setAmount(subtract);
    }

    /**
     * 计算可退数量
     *
     * @param goodsRefundList 商品退款列表
     * @return 可退数量
     */
    private int calculateRefundableNum(List<HeOrderRefundGoodsEntity> goodsRefundList) {
        //冻结数量
        int freezeNum = calculateNum(goodsRefundList, OrderRefundGoodsStatusEnum.REFUNDING);
        //已退数量
        int alreadyRefundNum = calculateNum(goodsRefundList, OrderRefundGoodsStatusEnum.SUCCESS);

        return freezeNum + alreadyRefundNum;

    }


    private int leftNum(QueryRefundGoodsRes queryRefundGoodsRes, List<IncomePaidAllocationEntity> goodsPaidList, Map<String, Integer> availableNumMap, int alreadyRefundNum, Map<String, Integer> countByGoodsAndSkuId) {
        //默认使用这个公式，别的跟资产相关的用资产中心的返回的值
        int leftNum = queryRefundGoodsRes.getGoodsNum() - alreadyRefundNum;
        Integer verificationCount = countByGoodsAndSkuId.get(queryRefundGoodsRes.getGoodsId() + "+" + queryRefundGoodsRes.getSkuId());
        if (ObjectUtil.isNotEmpty(verificationCount)) {
            leftNum = leftNum - verificationCount;
        }
        if (queryRefundGoodsRes.getGoodsType().equals(GoodsTypeEnum.Production_Coin.code())) {
            if (CollectionUtil.isNotEmpty(goodsPaidList)) {

                //如果是产康金，需要查资产中心获取数量
                Integer orderId = goodsPaidList.get(0).getOrderId();
                Long ckjByOrderId = assetTradeService.getCkjByOrderId(orderId, queryRefundGoodsRes.getOrderGoodsSn());
                if (Objects.isNull(ckjByOrderId)) {
                    return leftNum;
                }
                log.info("订单商品的支付退款情况,商品类型是产康金，orderId={}, orderGoodsSn={}, ckjByOrderId={}", orderId, queryRefundGoodsRes.getOrderGoodsSn(), ckjByOrderId);
                return ckjByOrderId.intValue();
            } else {
                return leftNum;
            }
        } else {
            Integer availableNum = availableNumMap.get(queryRefundGoodsRes.getOrderGoodsSn());
            if (Objects.nonNull(availableNum)) {
                if (availableNum > leftNum) {
                    log.error("订单商品的支付退款情况,商品类型不是产康金，但是可用数量大于可退数量，orderGoodsSn={}, availableNum={}, leftNum={}", queryRefundGoodsRes.getOrderGoodsSn(), availableNum, leftNum);
                    throw new RuntimeException("商品可用数量有问题 orderGoodsSn=" + queryRefundGoodsRes.getOrderGoodsSn());
                }
                return availableNum;
            }
        }
        return leftNum;
    }

    private <T> List<T> filterByOrderGoodsSn(List<T> list, String orderGoodsSn, Function<T, String> getOrderGoodsSn) {
        return list.stream().filter(i -> getOrderGoodsSn.apply(i).equals(orderGoodsSn)).collect(Collectors.toList());
    }

    private Map<PayMethodEnum, Integer> calculateAmount(List<HeOrderRefundGoodsEntity> list, OrderRefundGoodsStatusEnum status) {
        Map<PayMethodEnum, Integer> result = new HashMap<>();
        //根据类型区分退款
        Map<PayMethodEnum, List<HeOrderRefundGoodsEntity>> collect = list.stream().collect(Collectors.groupingBy(l -> PayMethodEnum.PayType2Currency(l.getPayType())));

        for (PayMethodEnum payMethodEnum : collect.keySet()) {
            List<HeOrderRefundGoodsEntity> heOrderRefundGoodsEntities = collect.get(payMethodEnum);
            result.put(payMethodEnum, heOrderRefundGoodsEntities.stream().filter(g -> g.getStatus().equals(status.code())).mapToInt(HeOrderRefundGoodsEntity::getRefundAmount).sum());
        }
        return result;
    }

    private int calculateNum(List<HeOrderRefundGoodsEntity> list, OrderRefundGoodsStatusEnum status) {
        return list.stream()
                .filter(g -> g.getStatus().equals(status.code()))
                .collect(Collectors.toMap(
                        HeOrderRefundGoodsEntity::getRefundOrderSn,
                        Function.identity(),
                        (existing, replacement) -> existing // 相同refundOrderSn只保留第一个
                ))
                .values()
                .stream()
                .mapToInt(HeOrderRefundGoodsEntity::getRefundNum)
                .sum();
    }


    @Override
    public boolean condition(FlowContext context) {
        return (boolean) context.getAttribute(OtherConstant.CONTINUE);
    }


}

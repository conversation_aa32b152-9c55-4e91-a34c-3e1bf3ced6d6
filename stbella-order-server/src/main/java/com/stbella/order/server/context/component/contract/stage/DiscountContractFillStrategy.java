package com.stbella.order.server.context.component.contract.stage;

import cn.hutool.core.date.DateTime;
import com.stbella.contract.common.constant.BizConstant;
import com.stbella.contract.common.utils.GeneratorUtil;
import com.stbella.contract.common.utils.RMBUtils;
import com.stbella.contract.model.enums.TemplateContractTypeEnum;
import com.stbella.contract.model.res.ContractTemplateRes;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.core.utils.BigDecimalUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.server.context.dto.StoreFinanceDetailDto;
import com.stbella.order.server.contract.enums.ContractExceptionEnum;
import com.stbella.order.server.contract.req.MonthUserEsignDTO;
import com.stbella.order.server.order.month.res.OrderInfoByGiftExtendSkuVO;
import com.stbella.order.server.order.month.res.OrderInfoByOrderVO;
import com.stbella.order.server.order.month.service.MonthOrderWxCommandService;
import com.stbella.order.server.order.month.service.MonthOrderWxQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.SnowballFlowLauncher;
import top.primecare.snowball.flow.core.context.FlowContext;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-23 15:33
 */
@Component
@Slf4j
public class DiscountContractFillStrategy extends ContractFillStrategy {

    @Resource
    private MonthOrderWxQueryService monthOrderWxQueryService;

    @Resource
    private MonthOrderWxCommandService monthOrderWxCommandService;


    @Override
    protected Map<String, String> buildParam(ContractParamFillContext contractParamFillContext) {
        HeOrderEntity orderEntity = contractParamFillContext.getOrderEntity();
        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = contractParamFillContext.getHeOrderUserSnapshotEntity();
        MonthUserEsignDTO userSignDTO = contractParamFillContext.getUserSignDTO();

        String contractNo = GeneratorUtil.contractNo();
        //获取信息,填充部分合同信息
        Map<String, String> templateData = new HashMap<>();
        //合同参数需要的结束
        if (orderEntity.isNewOrder()) {
            fillNewTemplateData(contractNo, orderEntity, templateData);
        } else {
            checkMonthUserEsignDTO(userSignDTO);
            fillOldTemplateData(contractNo, templateData, heOrderUserSnapshotEntity, orderEntity, userSignDTO);
        }

        return templateData;
    }

    @Override
    protected String getContractName(ContractParamFillContext contractParamFillContext) {
        return "";
    }


    @Override
    public boolean filter(Integer templateContractType) {
        return TemplateContractTypeEnum.DISCOUNT.getCode().equals(templateContractType);
    }

    private void fillOldTemplateData(String contractNo, Map<String, String> templateData, HeOrderUserSnapshotEntity heOrderUserSnapshotEntity, HeOrderEntity orderEntity, MonthUserEsignDTO userSignDTO) {

        //签署人手机号(个人签署需要)
        templateData.put("signPhone", userSignDTO.getPhone());
        //合同参数需要的开始
        templateData.put("client_name", heOrderUserSnapshotEntity.getName());
        templateData.put("contract_no", contractNo);
        templateData.put("address", heOrderUserSnapshotEntity.getAddress());
        templateData.put("phone", heOrderUserSnapshotEntity.getPhone());
        templateData.put("id_card", heOrderUserSnapshotEntity.getIdCard());
        templateData.put("pay_amount_words", RMBUtils.numToRMBStr(BigDecimalUtil.divide(new BigDecimal(orderEntity.getPayAmount()), new BigDecimal(100)).doubleValue()));
        templateData.put("sign_time_str2", DateTime.now().toDateStr());
        //额外礼赠信息
        OrderInfoByOrderVO orderInfoByOrderVO = monthOrderWxQueryService.getOrderInfoByOrderId(orderEntity.getOrderId());
        List<OrderInfoByGiftExtendSkuVO> orderInfoByGiftExtendSkuVOList = orderInfoByOrderVO.getOrderInfoByGiftExtendVO().getOrderInfoByGiftExtendSkuVOList();
        String extraGift = monthOrderWxCommandService.convertOrderGift2String(orderInfoByGiftExtendSkuVOList);
        String remark = StringUtils.isNotBlank(extraGift) ? BizConstant.GIFT + "：\n\n" + extraGift + "\n\n" + BizConstant.REMARK + "：\n\n" + orderEntity.getRemark() : BizConstant.REMARK + "：\n\n" + orderEntity.getRemark();
        templateData.put("remark", remark);
    }

    private void fillNewTemplateData(String contractNo, HeOrderEntity orderEntity, Map<String, String> templateData) {

        List<String> contractList = Arrays.asList("client_name", "address", "phone", "id_card", "order_amount_words", "pay_amount_words");
        Map<String, String> contractParamsMap = getOrderParam(orderEntity);
        String str = contractList.stream().filter(item -> !contractParamsMap.containsKey(item)).findFirst().orElse(null);
        if (StringUtils.isNotEmpty(str)) {
            throw new BusinessException(ResultEnum.PARAM_ERROR, String.format("构建参数%s缺失", str));
        }
        String remark = contractParamsMap.get("remark2");
        if (StringUtils.isNotEmpty(remark)) {
            templateData.put("remark", remark);
        }
        contractParamsMap.forEach((key, value) -> {
            if (contractList.contains(key)) {
                templateData.put(key, value);
            }
        });
        templateData.put("sign_time_str2", DateTime.now().toDateStr());
        templateData.put("contract_no", contractNo);
    }


}
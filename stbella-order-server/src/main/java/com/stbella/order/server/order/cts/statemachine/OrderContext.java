package com.stbella.order.server.order.cts.statemachine;

import com.stbella.order.server.order.cts.entity.OrderCtsPO;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * OrderContext上下文
 *
 * <AUTHOR>
 * @date 2022-03-10 11:41
 * @sine 1.0.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class OrderContext {
    public static final String HEADER_KEY = "orderContext";
    private String orderNo;
    private Object model;
    private OrderCtsPO orderCtsPO;
}

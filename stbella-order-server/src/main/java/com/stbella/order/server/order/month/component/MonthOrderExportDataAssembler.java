package com.stbella.order.server.order.month.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.stbella.asset.api.enums.AssetType;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.month.MonthAdditionalRevenueEnum;
import com.stbella.order.common.enums.production.OrderGiftExtendTypeEnum;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.common.utils.JsonUtil;
import com.stbella.order.domain.client.StoreGoodsClient;
import com.stbella.order.domain.order.month.dto.STMOrderExportDTO;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.production.GoodsEntity;
import com.stbella.order.domain.order.production.OrderGiftExtendEntity;
import com.stbella.order.infrastructure.gateway.asset.AssetRemoteService;
import com.stbella.order.infrastructure.gateway.asset.res.AssetAccountDetailDto;
import com.stbella.order.server.order.StoreGoodsSkuModel;
import com.stbella.order.server.order.month.enums.CustomerFromTypeEnum;
import com.stbella.order.server.order.month.enums.StoreChildTypeEnum;
import com.stbella.order.server.order.month.enums.StoreTypeEnum;
import com.stbella.order.server.order.month.excel.STMOrderExport;
import com.stbella.order.server.order.month.excel.STMOrderRoomChangeExport;
import com.stbella.order.server.order.month.excel.STMOrderStayOverExport;
import com.stbella.order.server.order.month.res.OrderCostMultipleBirthsRecordVO;
import com.stbella.order.server.order.month.res.OrderDiscountsCacheVO;
import com.stbella.order.server.order.month.res.OrderRoomTypeChangeRecordVO;
import com.stbella.order.server.order.month.res.QueryOrderPageVO;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.order.server.utils.BigDecimalUtil;
import com.stbella.order.server.utils.DateUtils;
import com.stbella.order.server.utils.ListUtils;
import com.stbella.order.server.utils.MathUtil;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;

/**
 * 月子订单导出数据组装器
 *
 * <AUTHOR>
 */
@Component
public class MonthOrderExportDataAssembler {


    @Resource
    AssetRemoteService assetRemoteService;
    @Resource
    private StoreGoodsClient storeClient;

    //	房型升级 名称
    private static String ROOM_TYPE_UPGRADE = "房型升级";

    /**
     * 组装导出数据
     * @param queryOrderPageVO
     * @param no
     * @param additionalRevenueCostEntityList
     * @param additionalRevenueRoomChangeEntityList
     * @param giftExtendEntityList
     * @param orderList
     * @param storeEntityList
     * @param heOrderUserSnapshotEntities
     * @param orderGoodsEntityList
     * @param additionalRevenueEntityList
     * @param userEntities
     * @param includeMobile
     * @param tabClientByIdList
     * @param orderContinueLiveRecordEntityList
     * @param orderRoomTypeChangeRecordEntityList
     * @return
     */
    public List<STMOrderExportDTO> run(QueryOrderPageVO queryOrderPageVO,
                                       Integer no,
                                       List<HeOrderAdditionalRevenueEntity> additionalRevenueCostEntityList,
                                       List<HeOrderAdditionalRevenueEntity> additionalRevenueRoomChangeEntityList,
                                       List<OrderGiftExtendEntity> giftExtendEntityList,
                                       List<HeOrderEntity> orderList,
                                       List<CfgStoreEntity> storeEntityList,
                                       List<HeOrderUserSnapshotEntity> heOrderUserSnapshotEntities,
                                       List<HeOrderGoodsEntity> orderGoodsEntityList,
                                       List<HeOrderAdditionalRevenueEntity> additionalRevenueEntityList,
                                       List<UserEntity> userEntities,
                                       Boolean includeMobile,
                                       List<TabClientEntity> tabClientByIdList,
                                       List<HeOrderContinueLiveRecordEntity> orderContinueLiveRecordEntityList,
                                       List<HeOrderRoomTypeChangeRecordEntity> orderRoomTypeChangeRecordEntityList,
                                       List<GoodsEntity> goodsEntities,
                                       Integer giftOneRow,
                                       List<StoreGoodsSkuModel> orderGoodsName
    ) {


        Integer orderId = queryOrderPageVO.getOrderId();
        List<STMOrderExportDTO> result = new ArrayList<>();
        Integer storeId = queryOrderPageVO.getStoreId();

        List<STMOrderExportDTO.ResumingStay> resumingStayList = orderContinueLiveRecordEntity2ResumingStayList(additionalRevenueCostEntityList.stream().filter(o -> o.getOrderId().equals(orderId)).collect(Collectors.toList()));
        List<STMOrderExportDTO.RoomTypeChange> roomTypeChangeList = orderRoomTypeChangeRecordEntity2RoomTypeChangeList(additionalRevenueRoomChangeEntityList.stream().filter(o -> o.getOrderId().equals(orderId)).collect(Collectors.toList()), orderRoomTypeChangeRecordEntityList);


        List<OrderGiftExtendEntity> theOrderGift = giftExtendEntityList.stream().filter(o -> o.getOrderId().equals(orderId)).collect(Collectors.toList());
        // extraGiftList 中type=1 是的，默认名称设置成 产康金
        theOrderGift.forEach(o -> {
            if (OrderGiftExtendTypeEnum.SANKANGJIN.code().equals(o.getType())) {
                o.setGoodsName(OrderGiftExtendTypeEnum.SANKANGJIN.desc());
                o.setSkuName(OrderGiftExtendTypeEnum.SANKANGJIN.desc());
                // 老的
                if (queryOrderPageVO.getOldOrNew() != 1){
                    o.setGoodsNum(o.getPrice()/100);
                }
                //产康金的存储应该是 每份1元。这里设置单价为1元
                o.setPrice(100);
            }
        });

        List<STMOrderExportDTO.ExtraGift> extraGiftList = orderGiftExtendEntity2extraGiftList2(queryOrderPageVO.getOldOrNew(),  theOrderGift);
        Optional<HeOrderEntity> first = orderList.stream().filter(o -> o.getOrderId().equals(orderId)).findFirst();
        HeOrderEntity heOrderEntity = first.get();
        CfgStoreEntity store = null;
        if (CollectionUtils.isNotEmpty(storeEntityList)) {
            Optional<CfgStoreEntity> storeFirst = storeEntityList.stream().filter(f -> f.getStoreId().equals(heOrderEntity.getStoreId())).findFirst();
            if (storeFirst.isPresent()) {
                store = storeFirst.get();
            }
        }


        HeOrderUserSnapshotEntity userSnapshotEntity = null;
        Optional<HeOrderUserSnapshotEntity> userSnapshotFirst = heOrderUserSnapshotEntities.stream().filter(f -> f.getOrderId().equals(orderId)).findFirst();
        if (userSnapshotFirst.isPresent()) {
            userSnapshotEntity = userSnapshotFirst.get();
        }

        HeOrderGoodsEntity orderGoodsEntity = null;
        Optional<HeOrderGoodsEntity> orderGoodsEntityFirst = orderGoodsEntityList.stream().filter(f -> f.getOrderId().equals(orderId)).findFirst();
        if (orderGoodsEntityFirst.isPresent()) {
            orderGoodsEntity = orderGoodsEntityFirst.get();
        }

        List<HeOrderAdditionalRevenueEntity> costMultipleBirths = additionalRevenueEntityList.stream().filter(a -> a.getOrderId().equals(orderId) && MonthAdditionalRevenueEnum.COST_MULTIPLE_BIRTHS.getCode().equals(a.getType())).collect(Collectors.toList());
        List<HeOrderAdditionalRevenueEntity> holiday = additionalRevenueEntityList.stream().filter(a -> a.getOrderId().equals(orderId) && MonthAdditionalRevenueEnum.HOLIDAY.getCode().equals(a.getType())).collect(Collectors.toList());

        Integer staffId = heOrderEntity.getStaffId();
        UserEntity userEntity = null;
        Optional<UserEntity> userEntityFirst = userEntities.stream().filter(u -> u.getId().equals(staffId)).findFirst();
        if (userEntityFirst.isPresent()) {
            userEntity = userEntityFirst.get();
        }

        int max = MathUtil.max(false, resumingStayList.size(), roomTypeChangeList.size(), extraGiftList.size());
        max = max == 0 ? 1 : max;
        boolean isGiftOneRow = false;
        if (giftOneRow != null && giftOneRow == 1) {
            // 礼赠合并一行
            max = 1;
            isGiftOneRow = true;
        }

        Long fixedProductionAmount = queryFixedProductionAmount(heOrderEntity.getOrderSn());
        for (int i = 0; i < max; i++) {
            STMOrderExportDTO stmOrderExportDTO = new STMOrderExportDTO();
            STMOrderExport stmOrderExport = new STMOrderExport();
            Date performanceEffective = queryOrderPageVO.getPerformanceEffective();
            if (ObjectUtil.isNotEmpty(performanceEffective) && performanceEffective.getTime() > 0) {
                Optional<TabClientEntity> clientFirst = tabClientByIdList.stream().filter(t -> t.getId().equals(queryOrderPageVO.getClientUid()) && t.getStoreId().equals(storeId)).findFirst();
                if (clientFirst.isPresent()) {
                    TabClientEntity tabClientEntity = clientFirst.get();
                    Date recordTime = tabClientEntity.getRecordTime();
                    long betweenDays = DateUtil.between(recordTime, performanceEffective, DateUnit.DAY);
                    stmOrderExport.setClientFollowUpPeriod(betweenDays + "天" );
                }
            }
            stmOrderExport.setNo(no.toString());
            stmOrderExport.setOrderType(OmniOrderTypeEnum.getValueByCode(heOrderEntity.getOrderType()));
            stmOrderExport.setOrderSn(queryOrderPageVO.getOrderSn());
            stmOrderExport.setBasicUid(queryOrderPageVO.getBasicId());
            stmOrderExport.setOrderId(queryOrderPageVO.getOrderId());

            if (ObjectUtil.isNotEmpty(store)) {
                stmOrderExport.setAreaName(storeClient.getAreaName(store.getWarzone()));
                Integer type = store.getType();
                Integer childType = store.getChildType();
                if (StoreTypeEnum.SAINT_BELLA.getCode().equals(type)) {
                    if (ObjectUtil.isEmpty(childType) || !StoreChildTypeEnum.BELLA_VILLA.getCode().equals(childType)) {
                        stmOrderExport.setBrandName(StoreTypeEnum.SAINT_BELLA.getValue());
                    } else {
                        stmOrderExport.setBrandName(StoreChildTypeEnum.BELLA_VILLA.getValue());
                    }
                } else if (StoreTypeEnum.ISLA.getCode().equals(type)) {
                    stmOrderExport.setBrandName(StoreTypeEnum.ISLA.getValue());
                } else {
                    stmOrderExport.setBrandName(StoreTypeEnum.BABY_BELLA.getValue());
                }
                stmOrderExport.setStoreName(store.getStoreName());
            }
            stmOrderExport.setCustomerName(queryOrderPageVO.getCustomerName());

            if (includeMobile) {
                stmOrderExport.setCustomerMobile(queryOrderPageVO.getCustomerMobile());
            }

            if (heOrderEntity.getOldOrNew() == 1 && ObjectUtil.isNotEmpty(userSnapshotEntity)) {
                stmOrderExport.setSourceChannel(CustomerFromTypeEnum.getValueByCode(userSnapshotEntity.getFromType()));
                Date predictBornDate = userSnapshotEntity.getPredictBornDate();
                Date wantIn = userSnapshotEntity.getWantIn();
                if (ObjectUtil.isNotEmpty(predictBornDate) && DateUtil.year(predictBornDate) == 2099) {
                    stmOrderExport.setPreCheckInDate("备孕中");
                } else {
                    stmOrderExport.setPreCheckInDate(DateUtil.formatDate(wantIn));
                }
            } else {
                stmOrderExport.setPreCheckInDate(DateUtil.formatDate(new Date(heOrderEntity.getWantIn() * 1000)));
            }

            if (ObjectUtil.isNotEmpty(orderGoodsEntity)) {
                Integer goodsId = orderGoodsEntity.getGoodsId();
                Integer skuId = orderGoodsEntity.getSkuId();
                Optional<GoodsEntity> goodsFirst = goodsEntities.stream().filter(g -> g.getId().equals(goodsId)).findFirst();

                List<StoreGoodsSkuModel> collect = orderGoodsName.stream().filter(o -> o.getOrderId().equals(queryOrderPageVO.getOrderId())).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(collect) && collect.get(0).checkGoodsAndSku()) {
                    stmOrderExport.setGoodsName(collect.get(0).getAllGoodsName());
                } else {
                    stmOrderExport.setGoodsName(orderGoodsEntity.getGoodsName());
                }

                stmOrderExport.setOrderRoom(orderGoodsEntity.getRoomName());

                String definedProperty = orderGoodsEntity.getDefinedProperty();
                if (StringUtils.isNotEmpty(definedProperty)) {

                    List<LinkedHashMap> read = JsonUtil.read(definedProperty, List.class);
                    if (CollectionUtils.isNotEmpty(read)) {
                        LinkedHashMap linkedHashMap = read.get(0);
                        List<LinkedHashMap> goodsPropertyDetailsVOS = (List<LinkedHashMap>) linkedHashMap.get("goodsPropertyDetailsVOS");
                        if (CollectionUtils.isNotEmpty(goodsPropertyDetailsVOS)) {
                            String serviceTypeName = (String) goodsPropertyDetailsVOS.get(0).get("label");
                            stmOrderExport.setServiceType(serviceTypeName);
                        }

                    }
                }

                if (heOrderEntity.getOldOrNew() == 1) {
                    //新订单从he_order_goods取
                    Integer serviceDays = orderGoodsEntity.getServiceDays();
                    serviceDays = ObjectUtil.isNotEmpty(serviceDays) ? serviceDays : 0;
                    stmOrderExport.setServiceDays(serviceDays.toString());
                } else {
                    //老订单从he_goods取
                    if (goodsFirst.isPresent()) {
                        stmOrderExport.setServiceDays(goodsFirst.get().getServiceDays().toString());
                    }
                }

                Integer goodsPriceOrgin = orderGoodsEntity.getGoodsPriceOrgin();
                //原价
                Integer payAmount = orderGoodsEntity.getGoodsPriceOrgin();
                stmOrderExport.setGoodsAmount(RMBUtils.changeF2Y(payAmount.longValue()));
                stmOrderExport.setPackageDiscount(BigDecimalUtil.divideScaleForPercentageCheckZeroDEfaultZero(new BigDecimal(payAmount), new BigDecimal(goodsPriceOrgin == 0 ? payAmount : goodsPriceOrgin)).toEngineeringString() + "%");
            }

            // 入住时间
            stmOrderExport.setFulfillmentStartDate(DateUtils.FormatDate(heOrderEntity.getFulfillmentStartDate()));
            // 获取固定赠送的产康金。 调用 资产查询，这样不太好。
            stmOrderExport.setFixedProductAmount(fixedProductionAmount+"");

            if (CollectionUtils.isNotEmpty(costMultipleBirths)) {
                HeOrderAdditionalRevenueEntity heOrderAdditionalRevenueEntity = costMultipleBirths.get(0);
                stmOrderExport.setFetusNum(heOrderAdditionalRevenueEntity.getEmbryoNumber().toString());
                Integer cost = heOrderAdditionalRevenueEntity.getCost();
                Integer price = heOrderAdditionalRevenueEntity.getPrice();
                stmOrderExport.setBabyAmount(RMBUtils.changeF2Y(cost.longValue()));
                stmOrderExport.setBabyContractAmount(RMBUtils.changeF2Y(price.longValue()));
                stmOrderExport.setBabyDiscount(BigDecimalUtil.divideScaleForPercentageCheckZeroDEfaultZero(new BigDecimal(price), new BigDecimal(cost == 0 ? price : cost)).toEngineeringString() + "%");
            }

            if (CollectionUtils.isNotEmpty(holiday)) {
                HeOrderAdditionalRevenueEntity heOrderAdditionalRevenueEntity = holiday.get(0);
                stmOrderExport.setHolidayAmount(RMBUtils.changeF2Y(heOrderAdditionalRevenueEntity.getCost().longValue()));
                stmOrderExport.setHolidayContractAmount(RMBUtils.changeF2Y(heOrderAdditionalRevenueEntity.getPrice().longValue()));
            }

            stmOrderExport.setOrderAmount(RMBUtils.changeF2Y(heOrderEntity.getOrderAmount().longValue()));
            stmOrderExport.setOrderContractAmount(RMBUtils.changeF2Y(heOrderEntity.getPayAmount().longValue()));
            stmOrderExport.setCheckInTotalDays(queryOrderPageVO.getCheckInTotalDays().toString());
            stmOrderExport.setGoodsId(queryOrderPageVO.getGoodsId());
            stmOrderExport.setBasicId(queryOrderPageVO.getBasicId());

            if (heOrderEntity.getOldOrNew() == 1) {
                //新老订单单独处理
                String discountDetails = heOrderEntity.getDiscountDetails();
                if (StringUtils.isNotEmpty(discountDetails)) {
                    OrderDiscountsCacheVO discountsCache = JSONObject.toJavaObject(JSON.parseObject(heOrderEntity.getDiscountDetails()), OrderDiscountsCacheVO.class);
                    stmOrderExport.setOrderDiscount(discountsCache.getOrderDiscount().toEngineeringString() + "%");
                    stmOrderExport.setNetDiscountRate(discountsCache.getNetDiscountRate().toEngineeringString() + "%");
                    stmOrderExport.setGrossProfitMargin(discountsCache.getGrossProfitMargin().toEngineeringString() + "%");
                }
            } else {
                //老订单的订单折扣=套餐折扣
                stmOrderExport.setOrderDiscount(stmOrderExport.getPackageDiscount());
                stmOrderExport.setNetDiscountRate(heOrderEntity.getNetMargin().multiply(new BigDecimal(100)).toEngineeringString() + "%");
                stmOrderExport.setGrossProfitMargin(heOrderEntity.getGrossMargin().multiply(new BigDecimal(100)).toEngineeringString() + "%");
            }

            stmOrderExport.setPaidAmount(queryOrderPageVO.getRealAmount().toEngineeringString());
            stmOrderExport.setRemainingAmount(queryOrderPageVO.getRemainingAmount().toEngineeringString());
            stmOrderExport.setOrderTagName(queryOrderPageVO.getOrderTagName());
            stmOrderExport.setCreateTime(queryOrderPageVO.getCreatedAt());
            if (ObjectUtil.isNotEmpty(userEntity)) {
                stmOrderExport.setSellerName(userEntity.getName());
            }
            stmOrderExport.setUpdatedAt(queryOrderPageVO.getUpdatedAt());
            stmOrderExport.setPayFirstTime(queryOrderPageVO.getPayFirstTime());
            if (ObjectUtil.isNotEmpty(queryOrderPageVO.getPerformanceEffectiveDate())) {
                stmOrderExport.setPerformanceEffectiveDate(queryOrderPageVO.getPerformanceEffectiveDate());
            }
            Integer operationType = heOrderEntity.getOperationType();
            stmOrderExport.setDeletionPerformance(operationType == 1 ? "是" : "否");
            stmOrderExport.setDiscountApprovalStatus(queryOrderPageVO.getApprovalDiscountStatusStr());
            stmOrderExport.setOrderStatus(queryOrderPageVO.getOrderStatusStr());
            stmOrderExport.setPayStatus(queryOrderPageVO.getPayStatusStr());
            stmOrderExport.setRefundStatus(queryOrderPageVO.getRefundStatusStr());
            stmOrderExport.setRemark(heOrderEntity.getRemark());
            stmOrderExport.setGoodsId(queryOrderPageVO.getGoodsId());
            stmOrderExport.setBasicId(queryOrderPageVO.getBasicId());


            if (resumingStayList.size() > i) {
                //如果是单号不显示赠送信息
                if (!isGiftOneRow) {
                    STMOrderExportDTO.ResumingStay resumingStay = resumingStayList.get(i);
                    stmOrderExport.setDurationOfStay(resumingStay.getDurationOfStay());
                    stmOrderExport.setRehousingType(resumingStay.getRehousingType());
                    stmOrderExport.setAmountRenewal(resumingStay.getAmountRenewal());
                    stmOrderExport.setAmountContractRenewal(resumingStay.getAmountContractRenewal());
                }
            }
            if (roomTypeChangeList.size() > i) {
                //如果是单号不显示赠送信息
                if (!isGiftOneRow) {
                    STMOrderExportDTO.RoomTypeChange roomTypeChange = roomTypeChangeList.get(i);
                    stmOrderExport.setOriginalRoomName(roomTypeChange.getOriginalRoomName());
                    stmOrderExport.setNewRoomName(roomTypeChange.getNewRoomName());
                    stmOrderExport.setRoomTypeChangeCycle(roomTypeChange.getRoomTypeChangeCycle());
                    stmOrderExport.setChangeOriginalPrice(roomTypeChange.getChangeOriginalPrice());
                    stmOrderExport.setFinalChangeOriginalPrice(roomTypeChange.getFinalChangeOriginalPrice());
                }
            }
            stmOrderExport.setTotalGiftExtendAmount("0");
            if (extraGiftList.size() > i) {
                STMOrderExportDTO.ExtraGift extraGift = extraGiftList.get(i);
                //如果是单号不显示赠送信息
                if (!isGiftOneRow){
                    stmOrderExport.setGiftExtendName(extraGift.getGiftExtendName());
                    if (extraGift.getGiftExtendName().equals("产康金")) {
                        stmOrderExport.setGiftExtendNum("1");
                        stmOrderExport.setGiftExtendSkuNum("1");
                    } else {
                        stmOrderExport.setGiftExtendNum(extraGift.getGiftExtendNum());


                        String giftExtendNum = extraGift.getGiftExtendNum();
                        String giftExtendSkuNum = extraGift.getGiftExtendSkuNum();

                        //商品数量=规格数量*购买数量
                        extraGift.setGiftExtendNum((new Integer(giftExtendNum) * new Integer(giftExtendSkuNum)) + "");
                        stmOrderExport.setGiftExtendNum((new Integer(giftExtendNum) * new Integer(giftExtendSkuNum)) + "");
                        //购买数量
                        extraGift.setGiftExtendSkuNum(giftExtendNum);
                        stmOrderExport.setGiftExtendSkuNum(giftExtendNum);

                    }
                    stmOrderExport.setGiftExtendAmount(extraGift.getGiftExtendAmount());
                    stmOrderExport.setGiftExtendSkuName(extraGift.getGiftExtendSkuName());
                }
                // 统计总的赠送金额
                stmOrderExport.setTotalGiftExtendAmount(extraGift.getTotalGiftExtendAmount());


            }

            stmOrderExportDTO.setStmOrderExport(stmOrderExport);
            stmOrderExportDTO.setResumingStayList(resumingStayList);
            stmOrderExportDTO.setRoomTypeChangeList(roomTypeChangeList);

            stmOrderExportDTO.setExtraGiftList(extraGiftList);
            result.add(stmOrderExportDTO);
        }

        return result;

    }

    /**
     * 续住
     *
     * @param collect
     * @return
     */
    private List<STMOrderExportDTO.ResumingStay> orderContinueLiveRecordEntity2ResumingStayList2(List<HeOrderAdditionalRevenueEntity> collect) {
        List<STMOrderExportDTO.ResumingStay> result = new ArrayList<>();
        for (HeOrderAdditionalRevenueEntity heOrderContinueLiveRecordEntity : collect) {
            STMOrderExportDTO.ResumingStay resumingStay = new STMOrderExportDTO.ResumingStay();
            resumingStay.setRehousingType(heOrderContinueLiveRecordEntity.getRoomName());
            resumingStay.setAmountRenewal(RMBUtils.formatToseparaDecimalsNew(new BigDecimal(heOrderContinueLiveRecordEntity.getCost() / 100)));
            resumingStay.setAmountContractRenewal(RMBUtils.formatToseparaDecimalsNew(new BigDecimal(heOrderContinueLiveRecordEntity.getPrice() / 100)));
            resumingStay.setDurationOfStay(heOrderContinueLiveRecordEntity.getDays().toString());
            result.add(resumingStay);
        }
        return result;
    }

    /**
     * 续住
     *
     * @param collect
     * @return
     */
    private List<STMOrderExportDTO.ResumingStay> orderContinueLiveRecordEntity2ResumingStayList(List<HeOrderAdditionalRevenueEntity> collect) {
        List<STMOrderExportDTO.ResumingStay> result = new ArrayList<>();
        for (HeOrderAdditionalRevenueEntity heOrderContinueLiveRecordEntity : collect) {
            STMOrderExportDTO.ResumingStay resumingStay = new STMOrderExportDTO.ResumingStay();
            resumingStay.setRehousingType(heOrderContinueLiveRecordEntity.getRoomName());
            resumingStay.setAmountRenewal(RMBUtils.formatToseparaDecimalsNew(new BigDecimal(heOrderContinueLiveRecordEntity.getCost() / 100)));
            resumingStay.setAmountContractRenewal(RMBUtils.formatToseparaDecimalsNew(new BigDecimal(heOrderContinueLiveRecordEntity.getPrice() / 100)));
            resumingStay.setDurationOfStay(heOrderContinueLiveRecordEntity.getDays().toString());
            result.add(resumingStay);
        }
        return result;
    }

    /**
     * 房型变更
     *
     * @param collect
     * @return
     */
    private List<STMOrderExportDTO.RoomTypeChange> orderRoomTypeChangeRecordEntity2RoomTypeChangeList(List<HeOrderAdditionalRevenueEntity> collect, List<HeOrderRoomTypeChangeRecordEntity> orderRoomTypeChangeRecordEntityList) {
        List<STMOrderExportDTO.RoomTypeChange> result = new ArrayList<>();
        for (HeOrderAdditionalRevenueEntity heOrderRoomTypeChangeRecordEntity : collect) {
            STMOrderExportDTO.RoomTypeChange roomTypeChange = new STMOrderExportDTO.RoomTypeChange();

            List<HeOrderRoomTypeChangeRecordEntity> roomTypeChangeRecordEntityList = orderRoomTypeChangeRecordEntityList.stream().filter(o -> o.getAdditionalRevenueId().equals(heOrderRoomTypeChangeRecordEntity.getId())).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(roomTypeChangeRecordEntityList)) {
                HeOrderRoomTypeChangeRecordEntity first = roomTypeChangeRecordEntityList.get(0);
                HeOrderRoomTypeChangeRecordEntity last = roomTypeChangeRecordEntityList.get(roomTypeChangeRecordEntityList.size() - 1);
                roomTypeChange.setOriginalRoomName(first.getOriginalRoomName());
                roomTypeChange.setNewRoomName(last.getNewRoomName());
            }

            roomTypeChange.setChangeOriginalPrice(RMBUtils.formatToseparaDecimalsNew(new BigDecimal(heOrderRoomTypeChangeRecordEntity.getCost() / 100)));
            roomTypeChange.setFinalChangeOriginalPrice(RMBUtils.formatToseparaDecimalsNew(new BigDecimal(heOrderRoomTypeChangeRecordEntity.getPrice() / 100)));
            roomTypeChange.setDays(heOrderRoomTypeChangeRecordEntity.getDays().toString());
            List<String> daysList = heOrderRoomTypeChangeRecordEntity.getDaysList();
            if (CollectionUtils.isNotEmpty(daysList)) {

                List<Integer> daysListInteger = daysList.stream()
                        .map(Integer::valueOf)
                        .collect(Collectors.toList());
                List<String> strings = ListUtils.rangeList(daysListInteger);
                roomTypeChange.setRoomTypeChangeCycle(strings.toString().replace("[", "").replace("]", ""));
            }
            result.add(roomTypeChange);
        }
        return result;
    }

    /**
     * 额外礼赠 转换
     *
     * @param gift
     * @return
     */
    private List<STMOrderExportDTO.ExtraGift> orderGiftExtendEntity2extraGiftList2(Integer newOrderTag, List<OrderGiftExtendEntity> gift) {
        List<STMOrderExportDTO.ExtraGift> result = new ArrayList<>();

        /**
         * 计算总的赠送金额
         */
        Long totalGiftAmount = 0L;
        if (gift.size()  > 0){
            //计算总的赠送金额 数量*单价
            for (OrderGiftExtendEntity orderGiftExtendEntity : gift) {
                // 房型升级 老订单是保存的 是总价格，新订单保存的是单价。 这是坑；这里处理里，老订单先变成单价
                if (newOrderTag != 1 && orderGiftExtendEntity.getGoodsName().equals(ROOM_TYPE_UPGRADE)) {
                    orderGiftExtendEntity.setPrice(orderGiftExtendEntity.getPrice() / orderGiftExtendEntity.getGoodsNum());
                }
                totalGiftAmount += orderGiftExtendEntity.getGoodsNum() * orderGiftExtendEntity.getPrice();
            }
        }


        //根据规格进行合并

        // gift 按goodsName 分组
        Map<Integer, List<OrderGiftExtendEntity>> giftNameMap = gift.stream().collect(Collectors.groupingBy(OrderGiftExtendEntity::getSkuId));
        // giftNameMap 遍历 生成 extraGift
        Integer totalGiftExtendAmount = 0;
        for (Map.Entry<Integer, List<OrderGiftExtendEntity>> entry : giftNameMap.entrySet()) {
            STMOrderExportDTO.ExtraGift extraGift = new STMOrderExportDTO.ExtraGift();
            List<OrderGiftExtendEntity> value = entry.getValue();
            Integer giftAmount = 0;
            if (value.get(0).getType() == 1) {
                value.get(0).setSkuNum(1);
                totalGiftExtendAmount += value.get(0).getGoodsNum() * 100;
                extraGift.setGiftExtendAmount(RMBUtils.changeF2Y(value.get(0).getGoodsNum().longValue() * 100));
            } else {
                Integer skuNum = value.get(0).getSkuNum();
                skuNum = ObjectUtil.isEmpty(skuNum) ? 1 : skuNum;
                Integer quantity = value.size() / skuNum;
                value.get(0).setSkuNum(skuNum);
                value.get(0).setGoodsNum(quantity);
                totalGiftExtendAmount += value.get(0).getGoodsNum() * value.get(0).getPrice();
                extraGift.setGiftExtendAmount(RMBUtils.changeF2Y(value.get(0).getGoodsNum() * value.get(0).getPrice() * 1L));
            }


            extraGift.setGiftExtendSkuName(value.get(0).getSkuName());
            extraGift.setGiftExtendNum(value.get(0).getGoodsNum().toString());
            extraGift.setGiftExtendName(value.get(0).getGoodsName());
            extraGift.setGiftExtendSkuNum(value.get(0).getSkuNum().toString());
            result.add(extraGift);
        }
        for (STMOrderExportDTO.ExtraGift r : result) {
            r.setTotalGiftExtendAmount(RMBUtils.changeF2Y(totalGiftExtendAmount.longValue()));
        }
        return result;
    }

    /**
     * 查询固定赠送产康金
     * @param orderSn
     * @return
     */
    protected Long queryFixedProductionAmount(String orderSn) {

        List<AssetAccountDetailDto> assetAccountDetailDtos = assetRemoteService.listDetailByOrderNoAndSource(orderSn, "1", AssetType.AVAILABLE_CKJ.getCode().longValue());
        if (CollectionUtils.isEmpty(assetAccountDetailDtos)) {
            return 0L;
        }
        Long amount = assetAccountDetailDtos.get(0).getAmount() / 100;
        return amount;
    }

    public List<STMOrderStayOverExport> getStayOver(STMOrderStayOverExport stmOrderStayOverExport, List<OrderCostMultipleBirthsRecordVO> data) {

        List<STMOrderStayOverExport> stayOverExportList = new ArrayList<>();

        //根据加收项
        Map<Integer, List<OrderCostMultipleBirthsRecordVO>> collect = data.stream().collect(Collectors.groupingBy(OrderCostMultipleBirthsRecordVO::getPackIndex));

        for (int i = 0; i < collect.keySet().size(); i++) {
            //PackIndex
            List<OrderCostMultipleBirthsRecordVO> orderCostMultipleBirthsRecordVOS = collect.get(i);
            for (int j = 0; j < orderCostMultipleBirthsRecordVOS.size(); j++) {
                STMOrderStayOverExport stayOverExport = BeanMapper.map(stmOrderStayOverExport, STMOrderStayOverExport.class);
                OrderCostMultipleBirthsRecordVO orderCostMultipleBirthsRecordVO = orderCostMultipleBirthsRecordVOS.get(j);
                stayOverExport.setFinalDurationOfStay(orderCostMultipleBirthsRecordVO.getFinalDurationOfStay());
                stayOverExport.setFinalContinuousHousingType(orderCostMultipleBirthsRecordVO.getFinalContinuousHousingType());
                stayOverExport.setFinalAmountOfRenewal(orderCostMultipleBirthsRecordVO.getFinalAmountOfRenewal());
                stayOverExport.setFinalRenewTheContractAmount(orderCostMultipleBirthsRecordVO.getFinalRenewTheContractAmount());
                stayOverExport.setChangeOfStayDays(orderCostMultipleBirthsRecordVO.getChangeOfStayDays());
                stayOverExport.setContinuousHousingType(orderCostMultipleBirthsRecordVO.getContinuousHousingType());
                stayOverExport.setChangeOfRenewalAmount(orderCostMultipleBirthsRecordVO.getChangeOfRenewalAmount());
                stayOverExport.setChangeOfContractAmountForRenewal(orderCostMultipleBirthsRecordVO.getChangeOfContractAmountForRenewal());
                stayOverExport.setTimeOfChange(orderCostMultipleBirthsRecordVO.getTimeOfChange());
                stayOverExport.setChangeOfPerson(orderCostMultipleBirthsRecordVO.getChangeOfPerson());
                stayOverExport.setAdditionalRevenueId(orderCostMultipleBirthsRecordVO.getAdditionalRevenueId());
                stayOverExport.setOrderId(orderCostMultipleBirthsRecordVO.getOrderId());
                stayOverExportList.add(stayOverExport);
            }
        }
        return stayOverExportList;
    }

    public List<STMOrderRoomChangeExport> getRoomChange(STMOrderRoomChangeExport stmOrderRoomChangeExport, List<OrderRoomTypeChangeRecordVO> data) {
        List<STMOrderRoomChangeExport> roomChangeExportList = new ArrayList<>();

        //根据加收项
        Map<Integer, List<OrderRoomTypeChangeRecordVO>> collect = data.stream().collect(Collectors.groupingBy(OrderRoomTypeChangeRecordVO::getPackIndex));

        for (int i = 0; i < collect.keySet().size(); i++) {
            //PackIndex
            List<OrderRoomTypeChangeRecordVO> roomTypeChangeRecordVOList = collect.get(i);
            for (int j = 0; j < roomTypeChangeRecordVOList.size(); j++) {
                STMOrderRoomChangeExport roomChangeExport = BeanMapper.map(stmOrderRoomChangeExport, STMOrderRoomChangeExport.class);
                OrderRoomTypeChangeRecordVO orderRoomTypeChangeRecordVO = roomTypeChangeRecordVOList.get(j);
                roomChangeExport.setFinalDurationOfStayChanged(orderRoomTypeChangeRecordVO.getFinalDurationOfStayChanged());
                roomChangeExport.setFinalOriginalRoomType(orderRoomTypeChangeRecordVO.getFinalOriginalRoomType());
                roomChangeExport.setFinalNewRoomType(orderRoomTypeChangeRecordVO.getFinalNewRoomType());
                roomChangeExport.setFinalNumberOfDaysChanged(orderRoomTypeChangeRecordVO.getFinalNumberOfDaysChanged());
                roomChangeExport.setFinalAmountChanged(orderRoomTypeChangeRecordVO.getFinalAmountChanged());
                roomChangeExport.setFinalSignedAmountChanged(orderRoomTypeChangeRecordVO.getFinalSignedAmountChanged());
                roomChangeExport.setDurationOfStayChanged(orderRoomTypeChangeRecordVO.getDurationOfStayChanged());
                roomChangeExport.setOriginalRoomType(orderRoomTypeChangeRecordVO.getOriginalRoomType());
                roomChangeExport.setNewRoomType(orderRoomTypeChangeRecordVO.getNewRoomType());
                roomChangeExport.setNumberOfDaysChanged(orderRoomTypeChangeRecordVO.getNumberOfDaysChanged());
                roomChangeExport.setAmountChanged(orderRoomTypeChangeRecordVO.getAmountChanged());
                roomChangeExport.setSignedAmountChanged(orderRoomTypeChangeRecordVO.getSignedAmountChanged());
                roomChangeExport.setChangeTime(orderRoomTypeChangeRecordVO.getChangeTime());
                roomChangeExport.setChanger(orderRoomTypeChangeRecordVO.getChanger());
                roomChangeExport.setAdditionalRevenueId(orderRoomTypeChangeRecordVO.getAdditionalRevenueId());
                roomChangeExportList.add(roomChangeExport);
            }
        }
        return roomChangeExportList;
    }
}

package com.stbella.order.server.context.component.contract.stage.handle;

import cn.hutool.core.date.DateTime;
import com.stbella.contract.common.constant.BizConstant;
import com.stbella.contract.model.req.OrderParamHistoryChangeValuePushDTO;
import com.stbella.order.common.enums.order.BUEnum;
import com.stbella.order.common.utils.GeneratorUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.domain.repository.OrderReductionRepository;
import com.stbella.order.server.context.component.contract.stage.ContractParamFillContext;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class CareCenterAgreementParamsFillService extends AgreementParamsFillAbstractService {

    @Resource
    private OrderReductionRepository orderReductionRepository;

    @Override
    public Map<String, String> buildParam(ContractParamFillContext contractParamFillContext) {

        HeOrderEntity order = contractParamFillContext.getOrderEntity();
        //创建补充协议
        Long fetchTotalReductionAmount = super.getFetchTotalReductionAmount(contractParamFillContext.getCurrentSignRecord(), order.fetchTotalReductionAmount());

        List<OrderParamHistoryChangeValuePushDTO> changeValuePushDTOS = new ArrayList<>();
        //减免折扣
        super.addDiscount(order, fetchTotalReductionAmount, changeValuePushDTOS);
        //签单金额
        addPayAmount(order, fetchTotalReductionAmount, changeValuePushDTOS);
        //预定金
        super.addReservation(order, fetchTotalReductionAmount, changeValuePushDTOS);
        //尾款
        super.addBalancePayment(order, fetchTotalReductionAmount, changeValuePushDTOS);

        for (OrderParamHistoryChangeValuePushDTO changeValuePushDTO : changeValuePushDTOS) {
            changeValuePushDTO.setParam1("专用条款");
            changeValuePushDTO.setParam2("【附件一：购买清单】");
        }
        OrderContractSignRecordVO orderContractSignRecordVO = contractParamFillContext.fetchMainSignRecord();
        Map<String, String> templateContext = generateTemplateData(orderContractSignRecordVO, changeValuePushDTOS, order);
        String contractNo = GeneratorUtil.contractNo();
        templateContext.put("contract_no", contractNo);
        //主合同编号
        templateContext.put("main_contract_no", orderContractSignRecordVO.getContractNo());
        templateContext.put("company_address", BizConstant.COMPANY_ADDRESS);
        DateTime dateTime = new DateTime(orderContractSignRecordVO.getUpdatedAt() * 1000);
        templateContext.put("sign_time_str2", dateTime.toDateStr());
        return templateContext;
    }

    @Override
    public Integer bizHandleType() {
        return BUEnum.CARE_CENTER.getCode();
    }
}

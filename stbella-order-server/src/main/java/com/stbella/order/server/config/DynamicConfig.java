/**
 * Tencent.com Inc.
 * Copyright (c) 1998-2022 All Rights Reserved.
 */
package com.stbella.order.server.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 动态配置
 *
 * <AUTHOR>
 * @version $Id: ApolloConfig.java, v 0.1 2024年07月17日 1:09 AM
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "dynamic.info")
public class DynamicConfig {

    private Long warZone;

    /**
     * 订单按照人民币汇率计算积分和成长值的时间节点
     */
    public Long timeNodes = 1736496501L;

    private Long sceneId = 200117L;

    /**
     * PICP端-客诉单创建，发送贝康小助手通知，兜底人员的手机号
     * 400客服经理李晨
     */
    private String mobile = "13732287202";

    private Boolean failureAttachment = false;

    /**
     * 是否全量查询订单数据
     */
    private Boolean allQueryOrderData = false;

    /**
     * 当前时间之前的处理时间
     */
    private Integer orderPreTime = 5;

    /**
     * 赠送订单审批申请重试次数
     */
    private Integer retryNum = 10;

    private Integer homeCareAsset = 1000;

    private Integer expatriateNursingAsset = 1000;

}

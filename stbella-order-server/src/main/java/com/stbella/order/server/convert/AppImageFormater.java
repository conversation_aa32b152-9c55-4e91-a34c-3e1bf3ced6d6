package com.stbella.order.server.convert;

import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Named;
import org.springframework.stereotype.Component;

import java.util.List;

import cn.hutool.json.JSONUtil;

/**
 * <p>
 * 图片转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-07 20:24
 */
@Component
@Named("AppImageFormater")
public class AppImageFormater {

    //绑定限定符
    @Named("str2List")
    public List<String> str2List(String img) {
        return StringUtils.isNotBlank(img) ? JSONUtil.toList(JSONUtil.parseArray(img), String.class) : null;
    }
}

package com.stbella.order.server.context.component.cancel.exec;

import cn.hutool.core.collection.CollectionUtil;
import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundDisabledProductEntity;
import com.stbella.order.domain.order.production.OrderGiftExtendEntity;
import com.stbella.order.domain.order.production.OrderProductionExtendEntity;
import com.stbella.order.domain.repository.OrderGiftExtendRepository;
import com.stbella.order.domain.repository.OrderProductionExtendRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
@SnowballComponent(name = "客诉工单取消审批-资产信息处理")
public class ComplaintCancelAssetExecProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private OrderGiftExtendRepository orderGiftExtendRepository;
    @Resource
    private OrderProductionExtendRepository orderProductionExtendRepository;

    @Override
    public void run(FlowContext bizContext) {

        log.info("客诉工单取消审批-资产信息处理");
        HeOrderEntity heOrderEntity = bizContext.getAttribute(HeOrderEntity.class);
        HeCustomerComplaintsEntity heCustomerComplaintsEntity = bizContext.getAttribute(HeCustomerComplaintsEntity.class);
        if (!heOrderEntity.isNewOrder() || Objects.isNull(heCustomerComplaintsEntity.getRefundOrderId())){
            log.info("仅月子订单进行资产回退操作");
            return;
        }
        List<HeOrderRefundDisabledProductEntity> orderRefundDisabledProductEntityList = bizContext.getListAttribute(HeOrderRefundDisabledProductEntity.class);

        List<Integer> buyIds = orderRefundDisabledProductEntityList.stream().filter(o -> o.getProductType() == 0).map(HeOrderRefundDisabledProductEntity::getProductId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(buyIds)) {
            List<OrderProductionExtendEntity> orderProductionExtendEntities = bizContext.getListAttribute(OrderProductionExtendEntity.class);
            for (OrderProductionExtendEntity orderProductionExtendEntity : orderProductionExtendEntities) {
                Optional<HeOrderRefundDisabledProductEntity> first = orderRefundDisabledProductEntityList.stream().filter(buy -> buy.getProductId().equals(orderProductionExtendEntity.getId())).findFirst();
                first.ifPresent(heOrderRefundDisabledProductEntity -> orderProductionExtendEntity.setStatus(heOrderRefundDisabledProductEntity.getSourceStatus()));
            }
            orderProductionExtendRepository.saveOrUpdateBatch(orderProductionExtendEntities);
        }

        List<Integer> giftIds = orderRefundDisabledProductEntityList.stream().filter(o -> o.getProductType() == 1).map(HeOrderRefundDisabledProductEntity::getProductId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(giftIds)) {
            return;
        }
        List<OrderGiftExtendEntity> orderGiftExtendEntities = bizContext.getListAttribute(OrderGiftExtendEntity.class);
        for (OrderGiftExtendEntity orderGiftExtendEntity : orderGiftExtendEntities) {
            Optional<HeOrderRefundDisabledProductEntity> first = orderRefundDisabledProductEntityList.stream().filter(gift -> gift.getProductId().equals(orderGiftExtendEntity.getId())).findFirst();
            first.ifPresent(heOrderRefundDisabledProductEntity -> orderGiftExtendEntity.setStatus(heOrderRefundDisabledProductEntity.getSourceStatus()));
        }
        orderGiftExtendRepository.batchUpdateById(orderGiftExtendEntities);
    }
}

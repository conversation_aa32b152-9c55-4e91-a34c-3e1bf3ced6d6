package com.stbella.order.server.manager;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.stbella.care.server.care.service.RoomStateService;
import com.stbella.care.server.care.vo.room.RoomStateQueryDetailInfoVO;
import com.stbella.contract.common.utils.BeanMapper;
import com.stbella.core.base.Operator;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.customer.server.ecp.entity.TabClientBailorPO;
import com.stbella.customer.server.ecp.entity.TabClientPO;
import com.stbella.customer.server.ecp.entity.UserPO;
import com.stbella.customer.server.ecp.request.*;
import com.stbella.customer.server.ecp.service.TabClientBailorService;
import com.stbella.customer.server.ecp.service.TabClientService;
import com.stbella.customer.server.ecp.service.UserService;
import com.stbella.customer.server.ecp.vo.ClientInfoVO;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.server.contract.req.MonthUserEsignDTO;
import com.stbella.order.server.convert.TabClientConvert;
import com.stbella.order.server.order.month.req.OrderCacheBaseReq;
import com.stbella.order.server.order.month.req.OrderMonthClientBailorReq;
import com.stbella.order.server.order.month.req.OrderMonthClientReq;
import com.stbella.order.server.order.month.res.OrderMonthClientBailorVO;
import com.stbella.order.server.order.month.res.OrderMonthClientVO;
import com.stbella.platform.order.api.contract.req.OrderUserSnapshotReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TabClientManager {

    @DubboReference
    private TabClientService tabClientService;
    @DubboReference
    private TabClientBailorService tabClientBailorService;
    @Resource
    private TabClientConvert tabClientConvert;

    @DubboReference
    private RoomStateService roomStateService;

    @DubboReference
    private UserService userService;

    /**
     * 获取客户信息(返回客户模型)
     *
     * @return
     */
    public ClientInfoVO getClientInfoByClientId(Integer clientUid) {
        ClientSearchByIdRequest request = new ClientSearchByIdRequest();
        request.setId(clientUid);
        Result<ClientInfoVO> clientInfoVOResult = tabClientService.queryClientInfoById(request);

        if (ObjectUtil.isEmpty(clientInfoVOResult.getData())) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "客户不存在,客户ID=" + clientUid);
        }
        return clientInfoVOResult.getData();
    }

    /**
     * 获取客户信息
     *
     * @return
     */
    public OrderMonthClientReq getClientInfoById(Integer clientUid) {
        ClientSearchByIdRequest request = new ClientSearchByIdRequest();
        request.setId(clientUid);
        Result<ClientInfoVO> clientInfoVOResult = tabClientService.queryClientInfoById(request);

        if (ObjectUtil.isEmpty(clientInfoVOResult.getData())) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "客户不存在,客户ID=" + clientUid);
        }
        return tabClientConvert.clientInfoVO2Req(clientInfoVOResult.getData());
    }


    /**
     * 获取客户信息
     *
     * @return
     */
    public ClientInfoVO getClientInfoVOById(Integer clientUid) {
        ClientSearchByIdRequest request = new ClientSearchByIdRequest();
        request.setId(clientUid);
        Result<ClientInfoVO> clientInfoVOResult = tabClientService.queryClientInfoById(request);

        if (ObjectUtil.isEmpty(clientInfoVOResult.getData())) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "客户不存在,客户ID=" + clientUid);
        }
        return clientInfoVOResult.getData();
    }


    /**
     * 先新写一个获取认证的接口，之前的处理证件信息的时候有点问题，为了不影响原有逻辑
     * @param clientUid
     * @return
     */
    public ClientInfoVO getClientInfoVOByIdForAuth(Integer clientUid, Integer authType) {
        ClientSearchByIdRequest request = new ClientSearchByIdRequest();
        request.setId(clientUid);
        request.setAuthType(authType);
        Result<ClientInfoVO> clientInfoVOResult = tabClientService.queryClientInfoByIdForAuth(request);

        if (ObjectUtil.isEmpty(clientInfoVOResult.getData())) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "客户不存在,客户ID=" + clientUid);
        }
        return clientInfoVOResult.getData();
    }



    /**
     * 修改客户信息
     *
     * @param req
     * @return
     */
    public Integer updateClientInfo(OrderMonthClientReq req) {
        SaveTabClientRequest saveTabClientRequest = tabClientConvert.orderMonthClientReq2Request(req);
        try {
            tabClientService.updateClientInfo(saveTabClientRequest);
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), e.getMessage());
        }
        return req.getClientUid();
    }


    /**
     * 新增委托人信息
     *
     * @param req
     * @return
     */
    public Integer createBailor(OrderMonthClientBailorReq req) {
        SaveTabClientBailorRequest request = tabClientConvert.orderMonthClientBailorReq2Request(req);
        Result<Integer> integerResult;
        try {
            integerResult = tabClientBailorService.createBailor(request);
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), e.getMessage());
        }
        return integerResult.getData();
    }

    /**
     * 查询客户对应 委托人 信息
     *
     * @param clientUid
     * @return {@link MonthUserEsignDTO}
     */
    public MonthUserEsignDTO queryBailorInfoByClientId(Integer clientUid) {
        ClientSearchByIdRequest request = new ClientSearchByIdRequest();
        request.setId(clientUid);
        Result<TabClientBailorPO> tabClientBailorPOResult = tabClientBailorService.queryBailorInfoByClientId(request);
        if (ObjectUtil.isEmpty(tabClientBailorPOResult.getData())) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "委托人不存在,客户ID=" + clientUid);
        }
        log.info("委托人信息{}", JSONUtil.toJsonStr(tabClientBailorPOResult.getData()));
        return tabClientConvert.clientBailorPo2Dto(tabClientBailorPOResult.getData());
    }

    public void removeClientInfoCache(OrderCacheBaseReq req) {
        OrderMonthClientCacheRequest orderMonthClientCacheRequest = new OrderMonthClientCacheRequest();
        orderMonthClientCacheRequest.setOrderId(req.getOrderId());
        orderMonthClientCacheRequest.setOrderType(req.getOrderType());
        orderMonthClientCacheRequest.setClientUid(req.getClientUid());
        com.stbella.core.base.Operator operator = new com.stbella.core.base.Operator();
        operator.setOperatorGuid(req.getOperator().getOperatorGuid());
        orderMonthClientCacheRequest.setOperator(operator);
        tabClientService.removeClientInfoCache(orderMonthClientCacheRequest);
    }

    public void removeBailorInfoCache(OrderCacheBaseReq req) {
        OrderMonthClientBailorCacheRequest request = new OrderMonthClientBailorCacheRequest();
        request.setOrderId(req.getOrderId());
        request.setOrderType(req.getOrderType());
        request.setClientUid(req.getClientUid());
        com.stbella.core.base.Operator operator = new com.stbella.core.base.Operator();
        operator.setOperatorGuid(req.getOperator().getOperatorGuid());
        request.setOperator(operator);
        tabClientBailorService.removeBailorInfoCache(request);
    }

    public void clientInfoCache(OrderMonthClientVO orderMonthClientVO, Operator operator, Integer orderId) {
        OrderMonthClientCacheRequest request = tabClientConvert.heOrderUserSnapshotEntity2OrderMonthClientCacheRequest(orderMonthClientVO);
        request.setOrderType(OmniOrderTypeEnum.MONTH_ORDER.getCode());
        request.setOperator(operator);
        request.setOrderId(orderId);
        tabClientService.clientInfoCache(request);
    }


    public void bailorInfoCache(OrderMonthClientBailorVO orderMonthClientBailorVO, Operator operator, Integer orderId) {
        OrderMonthClientBailorCacheRequest request = tabClientConvert.heOrderBailorSnapshotEntity2OrderMonthClientBailorCacheRequest(orderMonthClientBailorVO);
        request.setOrderType(OmniOrderTypeEnum.MONTH_ORDER.getCode());
        request.setOperator(operator);
        request.setOrderId(orderId);
        tabClientBailorService.bailorInfoCache(request);
    }

    public MonthUserEsignDTO queryBailorInfoById(Integer id) {
        ClientBailorSearchByIdRequest request = new ClientBailorSearchByIdRequest();
        request.setId(id);
        Result<TabClientBailorPO> tabClientBailorPOResult = tabClientBailorService.queryBailorInfoById(request);
        if (ObjectUtil.isEmpty(tabClientBailorPOResult.getData())) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "委托人不存在id=" + id);
        }
        log.info("委托人信息{}", JSONUtil.toJsonStr(tabClientBailorPOResult.getData()));
        return tabClientConvert.clientBailorPo2Dto(tabClientBailorPOResult.getData());
    }


    /**
     * 获取入住中房态信息
     * @param orderSn
     * @return {@link RoomStateQueryDetailInfoVO}
     */
    public RoomStateQueryDetailInfoVO queryClientRoomDetail(String orderSn) {
        RoomStateQueryDetailInfoVO roomStateQueryDetailInfoVO = roomStateService.queryStateByOrderNoV2(orderSn);
        log.info("客户入住中房态单条查询结果{}", roomStateQueryDetailInfoVO);
        return roomStateQueryDetailInfoVO;
    }

    /**
     * 更新客户信息
     *
     * @return
     */
    public Boolean updateClientInfoForOmni(OrderUserSnapshotReq req) {

        try {
            log.info("更新客户信息数据请求开始处理req:{}", JSONUtil.toJsonStr(req));
            SaveTabClientForOmniRequest request = BeanMapper.map(req, SaveTabClientForOmniRequest.class);
            Result<Boolean> result = tabClientService.updateClientInfoForOmni(request);
            if (!result.getSuccess()) {
                log.error("更新客户信息发生异常，msg:{}", result.getMsg());
                return Boolean.FALSE;
            }
            log.info("更新客户信息数据请求处理完成");
            return result.getData();
        } catch (Exception e) {
            log.error("调用customer服务更新用户信息发生异常", e);
            return Boolean.FALSE;
        }
    }

    public List<TabClientPO> listByCustomerIdList(List<Integer> customerIdList) {
        if (ObjectUtil.isEmpty(customerIdList)) {
            return null;
        }
        List<Long> clientUidList = customerIdList.stream()
                .map(i -> (long) i)
                .collect(Collectors.toList());
        List<TabClientPO> tabClientPOS = tabClientService.listByCustomerIdList(clientUidList);
        return tabClientPOS;
    }


    /**
     * 根据渠道来源或销售id 查询 basicId
     * @param basicUid
     * @return
     */
    public List<TabClientPO> listByBasicUid(Integer basicUid) {
        return tabClientService.queryClientListByBasicUid(basicUid).getData();
    }


    public UserPO queryUserById(Long staffId) {
        return userService.queryUserById(staffId);
    }

    public List<UserPO> queryUserListByIds(List<Long> staffIdList) {

        try {
            log.info("查询用户列表staffIdList:{}", staffIdList);
            if (CollectionUtils.isEmpty(staffIdList)){
                return Lists.newArrayList();
            }
            List<UserPO> userPOList = userService.queryUserByIds(staffIdList);
            log.info("查询用户列表userPOList:{}", JSONUtil.toJsonStr(userPOList));
            return CollectionUtils.isEmpty(userPOList) ? Lists.newArrayList() : userPOList;
        } catch (Exception e) {
            log.error("查询用户列表发生异常", e);
            return Lists.newArrayList();
        }

    }
}

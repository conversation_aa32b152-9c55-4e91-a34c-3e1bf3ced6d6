package com.stbella.order.server.convert;

import com.stbella.order.server.config.MappingConfig;
import com.stbella.order.server.order.nutrition.response.OrderNutritionConsumptionVO;
import com.stbella.order.server.order.nutrition.response.OrderNutritionReConsumptionPercentVO;
import com.stbella.order.server.order.nutrition.response.OrderNutritionReConsumptionUnitMaoriVO;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @description: OrderNutritionConvert
 * @date 2022/1/12 2:30 下午
 */
@Mapper(config = MappingConfig.class)
public interface OrderNutritionConsumptionConvert {

    OrderNutritionConsumptionVO unitMaoriVO2ConsumptionVO(OrderNutritionReConsumptionUnitMaoriVO orderNutritionReConsumptionUnitMaoriVO);

    OrderNutritionConsumptionVO ercentVO2ConsumptionVO(OrderNutritionReConsumptionPercentVO orderNutritionReConsumptionPercentVO);


}

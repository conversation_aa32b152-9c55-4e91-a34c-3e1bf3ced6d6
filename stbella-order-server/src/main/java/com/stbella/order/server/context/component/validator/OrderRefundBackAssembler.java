package com.stbella.order.server.context.component.validator;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.order.common.enums.core.OrderRefundNatureEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.server.order.month.enums.PayMethodEnum;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
@RefreshScope
@SnowballComponent(name = "退回重付金额处理器")
public class OrderRefundBackAssembler implements IExecutableAtom<FlowContext> {


    @Override
    public void run(FlowContext bizContext) {
        CreateRefundReq createRefundReq = bizContext.getAttribute(CreateRefundReq.class);
        List<CreateRefundReq.GoodsInfo> goodsInfoList = createRefundReq.getGoodsInfoList();

        if (CollectionUtil.isEmpty(goodsInfoList)) {
            log.warn("退回重付处理器：商品列表为空，orderId={}", createRefundReq.getOrderId());
            return;
        }

        log.info("退回重付金额分摊开始，orderId={}, 商品数量={}, 总退款金额={}", createRefundReq.getOrderId(), goodsInfoList.size(), createRefundReq.getRefundAmount());

        // 按照各个商品可退现金分摊订单总金额
        allocateRefundAmountToGoods(createRefundReq, goodsInfoList);

        log.info("退回重付金额分摊完成，orderId={},请求参数为 {}", createRefundReq.getOrderId(), JSONObject.toJSONString(createRefundReq));
        bizContext.setAttribute(CreateRefundReq.class, createRefundReq);
    }


    /**
     * 按照各个商品可退现金分摊订单总金额
     *
     * @param createRefundReq 退款请求
     * @param goodsInfoList   商品列表
     */
    private void allocateRefundAmountToGoods(CreateRefundReq createRefundReq, List<CreateRefundReq.GoodsInfo> goodsInfoList) {
        BigDecimal totalRefundAmount = createRefundReq.getRefundAmount();
        if (ObjectUtil.isEmpty(totalRefundAmount) || totalRefundAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("退回重付处理器：总退款金额无效，orderId={}, amount={}", createRefundReq.getOrderId(), totalRefundAmount);
            return;
        }

        // 计算所有商品的可退现金总额
        BigDecimal totalCashRefundableAmount = calculateTotalCashRefundableAmount(goodsInfoList);
        if (totalCashRefundableAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("退回重付处理器：商品可退现金总额为0，orderId={}", createRefundReq.getOrderId());
            return;
        }

        // 按比例分摊退款金额到各个商品
        allocateAmountProportionally(goodsInfoList, totalRefundAmount, totalCashRefundableAmount);
    }

    /**
     * 计算所有商品的可退现金总额
     *
     * @param goodsInfoList 商品列表
     * @return 可退现金总额
     */
    private BigDecimal calculateTotalCashRefundableAmount(List<CreateRefundReq.GoodsInfo> goodsInfoList) {
        return goodsInfoList.stream().filter(Objects::nonNull).filter(goods -> CollectionUtil.isNotEmpty(goods.getRefundGoodsAmountInfoList())).flatMap(goods -> goods.getRefundGoodsAmountInfoList().stream()).filter(Objects::nonNull).filter(amountInfo -> Integer.valueOf(PayMethodEnum.CASH.getCode()).equals(amountInfo.getAmountType())).map(CreateRefundReq.GoodsRefundAmountInfo::getRefundAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 按比例分摊退款金额到各个商品
     *
     * @param goodsInfoList             商品列表
     * @param totalRefundAmount         总退款金额
     * @param totalCashRefundableAmount 总可退现金金额
     */
    private void allocateAmountProportionally(List<CreateRefundReq.GoodsInfo> goodsInfoList, BigDecimal totalRefundAmount, BigDecimal totalCashRefundableAmount) {
        // 如果总退款金额等于总可退现金金额，直接按原金额分配，避免精度问题
        if (totalRefundAmount.compareTo(totalCashRefundableAmount) == 0) {
            for (CreateRefundReq.GoodsInfo goodsInfo : goodsInfoList) {
                if (CollectionUtil.isEmpty(goodsInfo.getRefundGoodsAmountInfoList())) {
                    continue;
                }
                
                BigDecimal goodsCashAmount = getCashRefundableAmount(goodsInfo);
                if (goodsCashAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                
                // 直接使用原始金额，无需重新计算
                updateGoodsRefundAmount(goodsInfo, goodsCashAmount);
                log.debug("商品金额分摊(直接分配)：orderGoodsSn={}, 分摊后金额={}", goodsInfo.getOrderGoodsSn(), goodsCashAmount);
            }
            return;
        }
        
        // 按比例分配
        BigDecimal allocatedAmount = BigDecimal.ZERO;
        List<CreateRefundReq.GoodsInfo> validGoodsList = goodsInfoList.stream()
            .filter(goods -> CollectionUtil.isNotEmpty(goods.getRefundGoodsAmountInfoList()))
            .filter(goods -> getCashRefundableAmount(goods).compareTo(BigDecimal.ZERO) > 0)
            .collect(java.util.stream.Collectors.toList());

        for (int i = 0; i < validGoodsList.size(); i++) {
            CreateRefundReq.GoodsInfo goodsInfo = validGoodsList.get(i);
            BigDecimal goodsCashAmount = getCashRefundableAmount(goodsInfo);

            BigDecimal allocatedGoodsAmount;
            if (i == validGoodsList.size() - 1) {
                // 最后一个商品，分配剩余金额，避免精度丢失
                allocatedGoodsAmount = totalRefundAmount.subtract(allocatedAmount);
                // 确保分配的金额不会超过该商品的可退现金金额
                if (allocatedGoodsAmount.compareTo(goodsCashAmount) > 0) {
                    allocatedGoodsAmount = goodsCashAmount;
                }
            } else {
                // 按比例计算分配金额
                BigDecimal ratio = goodsCashAmount.divide(totalCashRefundableAmount, 6, RoundingMode.HALF_UP);
                allocatedGoodsAmount = totalRefundAmount.multiply(ratio).setScale(2, RoundingMode.HALF_UP);
                
                // 确保分配的金额不会导致总金额超过原始金额
                BigDecimal remainingAmount = totalRefundAmount.subtract(allocatedAmount);
                if (allocatedGoodsAmount.compareTo(remainingAmount) > 0) {
                    allocatedGoodsAmount = remainingAmount;
                }
                
                // 确保分配的金额不会超过该商品的可退现金金额
                if (allocatedGoodsAmount.compareTo(goodsCashAmount) > 0) {
                    allocatedGoodsAmount = goodsCashAmount;
                }
                
                allocatedAmount = allocatedAmount.add(allocatedGoodsAmount);
            }

            // 更新商品的退款金额信息
            updateGoodsRefundAmount(goodsInfo, allocatedGoodsAmount);

            log.debug("商品金额分摊：orderGoodsSn={}, 原可退现金={}, 分摊后金额={}", goodsInfo.getOrderGoodsSn(), goodsCashAmount, allocatedGoodsAmount);
        }
    }

    /**
     * 获取商品的现金可退金额
     *
     * @param goodsInfo 商品信息
     * @return 现金可退金额
     */
    private BigDecimal getCashRefundableAmount(CreateRefundReq.GoodsInfo goodsInfo) {
        return goodsInfo.getRefundGoodsAmountInfoList().stream().filter(Objects::nonNull).filter(amountInfo -> Integer.valueOf(PayMethodEnum.CASH.getCode()).equals(amountInfo.getAmountType())).map(CreateRefundReq.GoodsRefundAmountInfo::getRefundAmount).filter(Objects::nonNull).findFirst().orElse(BigDecimal.ZERO);
    }

    /**
     * 更新商品的退款金额信息
     *
     * @param goodsInfo       商品信息
     * @param allocatedAmount 分配的金额
     */
    private void updateGoodsRefundAmount(CreateRefundReq.GoodsInfo goodsInfo, BigDecimal allocatedAmount) {
        // 更新商品总退款金额
        goodsInfo.setRefundAmount(allocatedAmount);

        // 更新现金退款金额信息
        goodsInfo.getRefundGoodsAmountInfoList().stream().filter(Objects::nonNull).filter(amountInfo -> Integer.valueOf(PayMethodEnum.CASH.getCode()).equals(amountInfo.getAmountType())).findFirst().ifPresent(amountInfo -> amountInfo.setAmount(allocatedAmount));

        // 将非现金的退款金额设置为0（退回重付只退现金）
        goodsInfo.getRefundGoodsAmountInfoList().stream().filter(Objects::nonNull).filter(amountInfo -> !Integer.valueOf(PayMethodEnum.CASH.getCode()).equals(amountInfo.getAmountType())).forEach(amountInfo -> amountInfo.setAmount(BigDecimal.ZERO));
    }

    @Override
    public boolean condition(FlowContext context) {
        CreateRefundReq createRefundReq = context.getAttribute(CreateRefundReq.class);
        Integer refundType = createRefundReq.getGoodsRefundType();
        log.info("退款类型：{}", refundType);
        HeOrderEntity orderEntity = context.getAttribute(HeOrderEntity.class);
        log.info("是否为押金订单{}", orderEntity.isDepositOrder());
        return OrderRefundNatureEnum.TEMP_REFUND.code().equals(refundType) || orderEntity.isDepositOrder();
    }
}

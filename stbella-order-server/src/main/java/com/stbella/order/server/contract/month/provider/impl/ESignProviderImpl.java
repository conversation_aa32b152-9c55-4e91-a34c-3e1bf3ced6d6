package com.stbella.order.server.contract.month.provider.impl;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.codec.Base64;
import com.google.common.collect.Maps;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.server.config.ContractContextConfig;
import com.stbella.order.server.config.MonthESignPropertiesConfig;
import com.stbella.order.server.contract.dto.*;
import com.stbella.order.server.contract.provider.month.ESignProvider;
import com.stbella.order.server.contract.service.ESignService;
import com.stbella.order.server.utils.ESignUtils;
import com.stbella.order.server.utils.HTTPHelper;
import com.stbella.order.server.utils.HttpUtils;

import com.stbella.order.server.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
@DubboService
public class ESignProviderImpl implements ESignProvider {

    @Resource
    private MonthESignPropertiesConfig eSignPropertiesConfig;
    @Resource
    private ESignService eSignService;

    /**
     * 【手机号认证】运营商3要素核身
     *
     * <AUTHOR>
     * @date 2023/7/10 18:08
     * @since 1.0.0
     * @param name
     * @param idNo
     * @param mobileNo
     * @return boolean
     * @throws
     */
    @Override
    public boolean telecom3Factors(String name, String idNo, String mobileNo) {
        // 设置走默认
        ContractContextConfig.setValue(true);
        return eSignService.telecom3Factors(name, idNo, mobileNo);
    }

    @Override
    public boolean doPersonIdentityComparison(String name, String idNo) {
        // 设置走默认
        log.info("开始认证name:{}, idNo:{}",name, idNo);
        ContractContextConfig.setValue(true);
        return eSignService.doPersonIdentityComparison(name, idNo);
    }

    @Override
    public boolean doPersonIdentityComparison(String name, String idNo, String mobileNo) throws BusinessException {
        // 设置走默认
        log.info("开始认证name:{}, idNo:{}，phone:{}", name, idNo, mobileNo);
        ContractContextConfig.setValue(true);
        return eSignService.doPersonIdentityComparison(name, idNo, mobileNo);
    }

    @Override
    public String createPersonSignAccount(PersonAccountDTO personAccountDTO) {
        // 接口请求地址
        String apiUrl = "/v1/accounts/createByThirdPartyUserId";
        // http请求访问url
        String url = eSignPropertiesConfig.getDomainName() + apiUrl;

        JSONObject bodys = new JSONObject();
        bodys.put("thirdPartyUserId", personAccountDTO.getThirdPartyUserId());
        bodys.put("name", personAccountDTO.getName());
        bodys.put("idType", personAccountDTO.getIdType());
        bodys.put("idNumber", personAccountDTO.getIdNumber());
        bodys.put("mobile", personAccountDTO.getMobile());
        String json = bodys.toString();
        LinkedHashMap<String, String> header = getHeader(apiUrl, json);

        try {
            String response = HTTPHelper.sendPOST(url, json, header, "UTF-8");
            log.info("eSign=====res:{}", response);
            JSONObject jsonObject = JSON.parseObject(response);
            int code = jsonObject.getIntValue("code");
            if (0 == code || ******** == code) {
                JSONObject data = jsonObject.getJSONObject("data");
                log.info("eSign======accountId:{}", data.toJSONString());
                String accountId = data.getString("accountId");
                return accountId;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String updatePersonSignAccount(UpdateAccountResquestDTO updateAccountResquest) {
        // 接口请求地址
        String apiUrl = "/v1/accounts/" + updateAccountResquest.getAccountId();
        // http请求访问url
        String url = eSignPropertiesConfig.getDomainName() + apiUrl;

        JSONObject bodys = new JSONObject();
        bodys.put("name", updateAccountResquest.getName());
        bodys.put("mobile", updateAccountResquest.getPhone());
        String json = bodys.toString();

        // 获取待签名字符串
        String getSignedString = ESignUtils.createPutSignedString(apiUrl, ESignUtils.doContentMD5(json));
        // 获取签名值
        String s = ESignUtils.doSignatureBase64(getSignedString, eSignPropertiesConfig.getAppSecret());
        // 构建请求头
        LinkedHashMap<String, String> header = new LinkedHashMap<String, String>();
        header.put("X-Tsign-Open-App-Id", eSignPropertiesConfig.getAppId());
        header.put("X-Tsign-Open-Auth-Mode", "Signature");
        header.put("X-Tsign-Open-Ca-Timestamp", String.valueOf(System.currentTimeMillis()));
        header.put("Accept", "*/*");
        header.put("Content-Type", "application/json;charset=UTF-8");
        header.put("X-Tsign-Open-Ca-Signature", s);
        header.put("Content-MD5", ESignUtils.doContentMD5(json));

        String response = HttpUtils.doNewPut(url, json, header);
        JSONObject jsonObject = JSON.parseObject(response);
        if (0 == jsonObject.getIntValue("code")) {
            JSONObject data = jsonObject.getJSONObject("data");
            return data.getString("accountId");
        }
        return null;
    }

    @Override
    public TemplateResponseDTO doUploadFileUrl(byte[] fileByte, String fileName) throws NoSuchAlgorithmException {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        byte[] digest = md5.digest(fileByte);

        // 接口请求地址
        String apiUrl = "/v1/docTemplates/createByUploadUrl";
        // http调用地址
        String url = eSignPropertiesConfig.getDomainName() + apiUrl;

        JSONObject bodys = new JSONObject();
        bodys.put("contentMd5", new String(Base64.encodeBase64(digest)));
        bodys.put("contentType", "application/pdf");
        bodys.put("fileName", fileName);
        String json = bodys.toString();

        LinkedHashMap<String, String> header = getHeader(apiUrl, json);

        try {
            String s = HTTPHelper.sendPOST(url, json, header, "UTF-8");
            log.info("================s:{}", s);
            JSONObject jsonObject = JSON.parseObject(s);
            if (0 == jsonObject.getIntValue("code")) {
                JSONObject data = jsonObject.getJSONObject("data");
                TemplateResponseDTO templateResponse = new TemplateResponseDTO();
                templateResponse.setTemplateId(data.getString("templateId"));
                templateResponse.setUploadUrl(data.getString("uploadUrl"));
                return templateResponse;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String uploadFileStream(byte[] fileByte, String fileName) throws NoSuchAlgorithmException {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        byte[] digest = md5.digest(fileByte);


        TemplateResponseDTO templateResponse = doUploadFileUrl(fileByte, fileName);
        // 调用url
        String url = templateResponse.getUploadUrl();

        Map<String, String> headers = Maps.newHashMap();
        headers.put("Content-MD5", new String(Base64.encodeBase64(digest)));
        headers.put("Content-Type", "application/pdf");
        String s = HttpUtils.doPut(url, fileByte, headers);
        log.info("=============================res:{}", s);
        return templateResponse.getTemplateId();

    }

    @Override
    public ContractParamFillResponseDTO doFillContentTemplate(ContractParamFillRequestDTO contractParamFillRequest) {
        // 接口请求地址
        String apiUrl = "/v1/files/createByTemplate";
        // http调用地址
        String url = eSignPropertiesConfig.getDomainName() + apiUrl;

        JSONObject bodys = new JSONObject();
        ContractParamFillRequestDTO.InnerSimpleFormFieIds simpleFormFieIds =
                contractParamFillRequest.getSimpleFormFieIds();
        Map<String, String> content = simpleFormFieIds.getContent();
        String s1 = JSON.toJSONString(content);
        JSONObject object = JSON.parseObject(s1);
        bodys.put("name", contractParamFillRequest.getName());
        bodys.put("templateId", contractParamFillRequest.getTemplateId());
        bodys.put("simpleFormFields", object);
        String json = bodys.toString();
        log.info("================================doFillContentTemplate req:{}", json);
        // 获取待签名字符串
        String postSignedString = ESignUtils.createPostSignedString(apiUrl, ESignUtils.doContentMD5(json));
        // 获取签名值
        String s = ESignUtils.doSignatureBase64(postSignedString, eSignPropertiesConfig.getAppSecret());
        // 构建请求头
        LinkedHashMap<String, String> header = new LinkedHashMap<String, String>();
        header.put("X-Tsign-Open-App-Id", eSignPropertiesConfig.getAppId());
        header.put("X-Tsign-Open-Auth-Mode", "Signature");
        header.put("X-Tsign-Open-Ca-Timestamp", String.valueOf(System.currentTimeMillis()));
        header.put("Accept", "*/*");
        header.put("Content-Type", "application/json;charset=UTF-8");
        header.put("X-Tsign-Open-Ca-Signature", s);
        header.put("Content-MD5", ESignUtils.doContentMD5(json));

        try {
            String response = HTTPHelper.sendPOST(url, json, header, "UTF-8");
            log.info("================================res:{}", response);
            JSONObject jsonObject = JSON.parseObject(response);
            if (0 == jsonObject.getIntValue("code")) {
                JSONObject data = jsonObject.getJSONObject("data");
                ContractParamFillResponseDTO contractParamFillResponse = new ContractParamFillResponseDTO();
                contractParamFillResponse.setFileId(data.getString("fileId"));
                contractParamFillResponse.setFileName(data.getString("fileName"));
                return contractParamFillResponse;
            } else {
                throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), jsonObject.getString("message") + "，请联系创新中心");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), e.getMessage() + "，请联系创新中心");
        }
    }

    @Override
    public String createCompanyAccount(CompanyAccountRequestDTO companyAccountRequestDTO) {
        // 接口请求地址
        String apiUrl = "/v1/organizations/createByThirdPartyUserId";
        // http请求访问url
        String url = eSignPropertiesConfig.getDomainName() + apiUrl;

        JSONObject bodys = new JSONObject();
        bodys.put("thirdPartyUserId", companyAccountRequestDTO.getThirdPartyUserId());
        bodys.put("name", companyAccountRequestDTO.getName());
        bodys.put("idType", companyAccountRequestDTO.getIdType());
        bodys.put("idNumber", companyAccountRequestDTO.getIdNumber());
        //bodys.put("creator", companyAccountRequestDTO.getCreator());
        String json = bodys.toString();

        LinkedHashMap<String, String> header = getHeader(apiUrl, json);

        try {
            String s = HTTPHelper.sendPOST(url, json, header, "UTF-8");
            log.info("==============s:{}", s);
            JSONObject jsonObject = JSON.parseObject(s);
            if (0 == jsonObject.getIntValue("code")) {
                JSONObject data = jsonObject.getJSONObject("data");
                return data.getString("orgId");
            }
            if (******** == jsonObject.getIntValue("code")) {
                JSONObject data = jsonObject.getJSONObject("data");
                return data.getString("orgId");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 查询机构信息，有状态
     *
     * @param orgId
     * @return 账号id
     */
    @Override
    public String queryCompanyAccount(String orgId) {

            // 接口请求地址
            String apiUrl = "/v1/organizations/"+orgId;
            // http请求访问url
            String url = eSignPropertiesConfig.getDomainName() + apiUrl;

            LinkedHashMap<String, String> header = findHeader(apiUrl);

            try {
                String s = HTTPHelper.sendGet(url, header, "UTF-8");
                log.info("==========================res:{}", s);
                return s;
            } catch (Exception e) {
                e.printStackTrace();
            }
            return null;

    }

    /**
     * 创建机构印章
     *
     * @param requestDTO 机构相关参数信息
     * @return 账号id
     */
    @Override
    public String createSealsOfficialTemplate(CompanySealRequestDTO requestDTO) {
        // 接口请求地址
        String apiUrl = "/v1/organizations/" + requestDTO.getOrgId() + "/seals/officialtemplate";
        // http请求访问url
        String url = eSignPropertiesConfig.getDomainName() + apiUrl;

        String json = JSONUtil.toJsonStr(requestDTO);
        log.info("=================================创建机构印章req:{}", json);
        LinkedHashMap<String, String> header = getHeader(apiUrl, json);

        try {
            String s = HTTPHelper.sendPOST(url, json, header, "UTF-8");
            log.info("=========创建机构印章result:{}", s);
            JSONObject jsonObject = JSON.parseObject(s);
            if (0 == jsonObject.getIntValue("code")) {
                JSONObject data = jsonObject.getJSONObject("data");
                return data.getString("sealId");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 查询机构印章
     *
     * @param orgId
     * @return 账号id
     */
    @Override
    public String queryCompanySealList(String orgId) {

        // 接口请求地址
        String apiUrl = "/v1/organizations/"+orgId+"/seals";
        // http请求访问url
        String url = eSignPropertiesConfig.getDomainName() + apiUrl;

        LinkedHashMap<String, String> header = findHeader(apiUrl);

        try {
            String s = HTTPHelper.sendGet(url, header, "UTF-8");
            log.info("==========================res:{}", s);
            return s;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public CompanyAuthenticationDTO doCompanyAuthentication(CompanyAuthenticationRequestDTO companyAuthenticationRequest,
                                                            String orgId) {
        // 接口请求地址
        String apiUrl = "/v2/identity/auth/web/" + orgId + "/orgIdentityUrl";
        // http请求访问url
        String url = eSignPropertiesConfig.getDomainName() + apiUrl;

        JSONObject bodys = new JSONObject();
        bodys.put("agentAccountId", companyAuthenticationRequest.getAgentAccountId());
        bodys.put("authType", companyAuthenticationRequest.getAuthType());
        String json = bodys.toString();

        LinkedHashMap<String, String> header = getHeader(apiUrl, json);

        try {
            String s = HTTPHelper.sendPOST(url, json, header, "UTF-8");
            log.info("=========cas:{}", s);
            JSONObject jsonObject = JSON.parseObject(s);
            if (0 == jsonObject.getIntValue("code")) {
                JSONObject data = jsonObject.getJSONObject("data");
                CompanyAuthenticationDTO companyAuthentication = new CompanyAuthenticationDTO();
                companyAuthentication.setFlowId(data.getString("flowId"));
                companyAuthentication.setShortLink(data.getString("shortLink"));
                companyAuthentication.setUrl(data.getString("url"));
                return companyAuthentication;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Boolean doSetCompanyAutoGrantAuthentication(String orgId) {
        // 接口请求地址
        String apiUrl = "/v1/signAuth/" + orgId;
        // http请求访问url
        String url = eSignPropertiesConfig.getDomainName() + apiUrl;

        LinkedHashMap<String, String> header = findHeader(apiUrl);

        try {
            String s = HttpUtils.doPost(url, null, header);
            log.info("============res:{}", s);
            JSONObject jsonObject = JSON.parseObject(s);
            if (0 == jsonObject.getIntValue("code")) {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }

    @Override
    public CompanyAuthenticationResponseDTO getCompanyAuthenticationState(String flowId) {
        // 接口请求地址
        String apiUrl = "/v2/identity/auth/api/common/" + flowId + "/detail";
        // http请求访问url
        String url = eSignPropertiesConfig.getDomainName() + apiUrl;

        LinkedHashMap<String, String> header = findHeader(apiUrl);

        try {
            String s = HTTPHelper.sendGet(url, header, "UTF-8");
            log.info("==========================res:{}", s);
            JSONObject jsonObject = JSON.parseObject(s);
            if (0 == jsonObject.getIntValue("code")) {
                JSONObject data = jsonObject.getJSONObject("data");
                CompanyAuthenticationResponseDTO companyAuthenticationResponse = new CompanyAuthenticationResponseDTO();
                companyAuthenticationResponse.setFlowId(data.getString("flowId"));
                companyAuthenticationResponse.setStatus(data.getString("status"));
                return companyAuthenticationResponse;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String doInitiateSigningProcess(ContractSignStartRequestDTO contractSignStartRequestDTO) {
        // 接口请求地址
        String apiUrl = "/api/v2/signflows/createFlowOneStep";
        // http请求访问url
        String url = eSignPropertiesConfig.getDomainName() + apiUrl;

        ContractSignStartRequestDTO.InnerFlowInfo flowInfo = contractSignStartRequestDTO.getFlowInfo();
        ContractSignStartRequestDTO.InnerFlowInfo.InnerFlowConfigInfo flowConfigInfo = flowInfo.getFlowConfigInfo();

        JSONObject bodys = new JSONObject();
        JSONObject flowConfigInfos = new JSONObject();
        flowConfigInfos.put("noticeDeveloperUrl", flowConfigInfo.getNoticeDeveloperUrl());
        flowConfigInfos.put("noticeType", flowConfigInfo.getNoticeType());
        flowConfigInfos.put("signPlatform", flowConfigInfo.getSignPlatform());
        flowConfigInfos.put("personAvailableAuthTypes", flowConfigInfo.getPersonAvailableAuthTypes());
        flowConfigInfos.put("willTypes", flowConfigInfo.getWillTypes());
        flowConfigInfos.put("batchDropSeal", flowConfigInfo.getBatchDropSeal());

        JSONObject flowInfos = new JSONObject();
        flowInfos.put("autoArchive", flowInfo.getAutoArchive());
        flowInfos.put("autoInitiate", flowInfo.getAutoInitiate());
        flowInfos.put("businessScene", flowInfo.getBusinessScene());
        flowInfos.put("flowConfigInfo", flowConfigInfos);
        bodys.put("flowInfo", flowInfos);

        List<ContractSignStartRequestDTO.InnerSigners> signer = contractSignStartRequestDTO.getSigners();
        JSONArray signerList = new JSONArray();
        signer.forEach(instance -> {
            JSONObject signers = new JSONObject();
            signers.put("platformSign", instance.getPlatformSign());
            JSONObject signAccounts = new JSONObject();
            signAccounts.put("signerAccountId", instance.getSignerAccount().getSignerAccount());
            signAccounts.put("authorizedAccountId", instance.getSignerAccount().getAuthorizedAccountId());
            signers.put("signerAccount", signAccounts);
            List<ContractSignStartRequestDTO.InnerSigners.InnerSignfields> innerSignfields = instance.getSignfields();
            innerSignfields.forEach(item -> {
                JSONArray signfieldList = new JSONArray();
                JSONObject signfield = new JSONObject();
                signfield.put("autoExecute", item.getAutoExecute());
                signfield.put("actorIndentityType", item.getActorIndentityType());
                signfield.put("fileId", item.getFileId());
                if (Objects.nonNull(item.getSealId())){
                    signfield.put("sealId", item.getSealId());
                }

                signfield.put("sealType", item.getSealType());
                JSONObject innerPosBean = new JSONObject();
                innerPosBean.put("posPage", item.getPosBean().getPosPage());
                innerPosBean.put("posX", item.getPosBean().getPosX());
                innerPosBean.put("posY", item.getPosBean().getPosY());
                signfield.put("posBean", innerPosBean);
                signfieldList.add(signfield);
                signers.put("signfields", signfieldList);
            });
            signerList.add(signers);
            bodys.put("signers", signerList);
        });

        List<ContractSignStartRequestDTO.InnerDocs> docs = contractSignStartRequestDTO.getDocs();
        List<String> fileIds = docs.stream().map(ContractSignStartRequestDTO.InnerDocs::getFileId).collect(Collectors.toList());
        List<String> fileNames = docs.stream().map(ContractSignStartRequestDTO.InnerDocs::getFileName).collect(Collectors.toList());
        JSONArray docList = new JSONArray();
        JSONObject doc = new JSONObject();
        doc.put("fileId", doListToString(fileIds));
        doc.put("fileName", doListToString(fileNames));
        docList.add(doc);

        bodys.put("docs", docList);
        String json = bodys.toString();

        LinkedHashMap<String, String> header = getHeader(apiUrl, json);
        try {
            log.info("=================================获取签署流程req:{}", json);
            String s = HTTPHelper.sendPOST(url, json, header, "UTF-8");
            log.info("=================================获取签署流程res:{}", s);
            JSONObject jsonObject = JSON.parseObject(s);
            if (0 == jsonObject.getIntValue("code")) {
                JSONObject data = jsonObject.getJSONObject("data");
                return data.getString("flowId");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public SignAddressResponseDTO getSignAddress(String accountId, String flowId) {
        // 接口请求地址
        String apiUrl = "/v1/signflows/" + flowId + "/executeUrl";
        // http请求访问url
        String url = eSignPropertiesConfig.getDomainName() + apiUrl + "?accountId=" + accountId;

        LinkedHashMap<String, String> header = findHeader(apiUrl);

        try {
            String s = HTTPHelper.sendGet(url, header, "UTF-8");
            log.info("============================res:{}", s);
            JSONObject jsonObject = JSON.parseObject(s);
            if (0 == jsonObject.getIntValue("code")) {
                JSONObject data = jsonObject.getJSONObject("data");
                SignAddressResponseDTO signAddressResponse = new SignAddressResponseDTO();
                signAddressResponse.setLongUrl(data.getString("url"));
                signAddressResponse.setShortUrl(data.getString("shortUrl"));
                return signAddressResponse;
            } else {
                throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), jsonObject.getString("message"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public SignPositionResponseDTO searchWordsPosition(String keywords, String fileId) {
        // 接口请求地址
        String apiUrl = "/v1/documents/" + fileId + "/searchWordsPosition";
        // http请求访问url
        String url = eSignPropertiesConfig.getDomainName() + apiUrl + "?keywords=" + keywords;

        LinkedHashMap<String, String> header = findHeader(apiUrl);

        try {
            String s = HTTPHelper.sendGet(url, header, "UTF-8");
            log.info("=============================res:{}", s);
            JSONObject jsonObject = JSON.parseObject(s);
            if (0 == jsonObject.getIntValue("code")) {
                JSONObject data = jsonObject.getJSONObject("data");
                SignPositionResponseDTO signPositionResponse = new SignPositionResponseDTO();
                signPositionResponse.setFileId(data.getString("fileId"));
                signPositionResponse.setKeyword(data.getString("keyword"));
                JSONArray positionList = data.getJSONArray("positionList");
                List<SignPositionResponseDTO.InnerPositionList> innerPositionLists =
                        JSONObject.parseArray(positionList.toJSONString(), SignPositionResponseDTO.InnerPositionList.class);
                signPositionResponse.setPositionList(innerPositionLists);
                return signPositionResponse;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public SignCompleteResponseDTO getSignStatus(String flowId) {
        // 接口请求地址
        String apiUrl = "/v1/signflows/" + flowId;
        // http请求访问url
        String url = eSignPropertiesConfig.getDomainName() + apiUrl;

        LinkedHashMap<String, String> header = findHeader(apiUrl);

        try {
            String s = HTTPHelper.sendGet(url, header, "UTF-8");
            log.info("=========================res:{}", s);
            JSONObject jsonObject = JSONObject.parseObject(s);
            if (0 == jsonObject.getIntValue("code")) {
                JSONObject data = jsonObject.getJSONObject("data");
                SignCompleteResponseDTO signCompleteResponse = new SignCompleteResponseDTO();
                signCompleteResponse.setAppId(data.getString("appId"));
                signCompleteResponse.setFlowId(data.getString("flowId"));
                signCompleteResponse.setFlowStatus(data.getIntValue("flowStatus"));
                signCompleteResponse.setFlowDesc(data.getString("flowDesc"));
                return signCompleteResponse;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public DownloadUrlResponseDTO downloadContractFile(String flowId) {
        // 接口请求地址
        String apiUrl = "/v1/signflows/" + flowId + "/documents";
        // http请求访问url
        String url = eSignPropertiesConfig.getDomainName() + apiUrl;

        LinkedHashMap<String, String> header = findHeader(apiUrl);

        try {
            String s = HTTPHelper.sendGet(url, header, "UTF-8");
            log.info("=========================res:{}", s);
            JSONObject jsonObject = JSONObject.parseObject(s);
            if (0 == jsonObject.getIntValue("code")) {
                JSONObject data = jsonObject.getJSONObject("data");
                JSONArray docs = data.getJSONArray("docs");
                DownloadUrlResponseDTO downloadUrlResponse = new DownloadUrlResponseDTO();
                List<DownloadUrlResponseDTO> downloadUrlResponseDTO =
                        JSONObject.parseArray(docs.toJSONString(), DownloadUrlResponseDTO.class);
                // TODO e签宝文档支持一个签署流程签多份文件，目前业务及当前版本只有一个签署文件，故未做list接收
                downloadUrlResponse.setFileId(downloadUrlResponseDTO.get(0).getFileId());
                downloadUrlResponse.setFileName(downloadUrlResponseDTO.get(0).getFileName());
                downloadUrlResponse.setFileUrl(downloadUrlResponseDTO.get(0).getFileUrl());
                return downloadUrlResponse;
            } else {
                throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), jsonObject.getString("message"));
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Boolean cancellationAccount(String accountId) {
        // 接口请求地址
        String apiUrl = "/v1/accounts/" + accountId;
        // http请求访问url
        String url = eSignPropertiesConfig.getDomainName() + apiUrl;

        // get请求或者post请求参数在路径上body无参数时contentMD5为''
        String contentMd5 = "{}";

        // 获取待签名字符串
        String getSignedString = ESignUtils.createDeleteSignedString(apiUrl);
        // 获取签名值
        String s = ESignUtils.doSignatureBase64(getSignedString, eSignPropertiesConfig.getAppSecret());
        // 构建请求头
        LinkedHashMap<String, String> header = new LinkedHashMap<String, String>();
        header.put("X-Tsign-Open-App-Id", eSignPropertiesConfig.getAppId());
        header.put("X-Tsign-Open-Auth-Mode", "Signature");
        header.put("X-Tsign-Open-Ca-Timestamp", String.valueOf(System.currentTimeMillis()));
        header.put("Accept", "*/*");
        header.put("Content-Type", "application/json;charset=UTF-8");
        header.put("X-Tsign-Open-Ca-Signature", s);
        header.put("Content-MD5", contentMd5);

        String response = HttpUtils.doDelete(url, header);
        log.info("===============================res/sign/cancellationAccount:{}", response);
        JSONObject jsonObject = JSON.parseObject(response);
        if (0 == jsonObject.getIntValue("code")) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public Boolean cancelSignFlow(String flowId) {
        // 接口请求地址
        String apiUrl = "/v1/signflows/" + flowId + "/revoke";
        // http请求访问url
        String url = eSignPropertiesConfig.getDomainName() + apiUrl;

        // get请求或者post请求参数在路径上body无参数时contentMD5为''
        String contentMd5 = "{}";

        // 获取待签名字符串
        String getSignedString = ESignUtils.createNewPutSignedString(apiUrl);
        // 获取签名值
        String s = ESignUtils.doSignatureBase64(getSignedString, eSignPropertiesConfig.getAppSecret());
        // 构建请求头
        LinkedHashMap<String, String> header = new LinkedHashMap<String, String>();
        header.put("X-Tsign-Open-App-Id", eSignPropertiesConfig.getAppId());
        header.put("X-Tsign-Open-Auth-Mode", "Signature");
        header.put("X-Tsign-Open-Ca-Timestamp", String.valueOf(System.currentTimeMillis()));
        header.put("Accept", "*/*");
        header.put("Content-Type", "application/json;charset=UTF-8");
        header.put("X-Tsign-Open-Ca-Signature", s);
        header.put("Content-MD5", contentMd5);

        String response = HttpUtils.doNewCancelPut(url, header);
        log.info("===============================res/sign/cancelSignFlow:{}", response);
        JSONObject jsonObject = JSON.parseObject(response);
        if (0 == jsonObject.getIntValue("code")) {
            return true;
        } else {
//            throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), jsonObject.getString("message"));
            return false;
        }
    }

    /**
     * 获取post请求签名鉴权方式的请求头，此种方式无需携带token
     *
     * @param apiUrl 接口请求地址
     * @param bodys  请求参数
     */
    private LinkedHashMap<String, String> getHeader(String apiUrl, String bodys) {
        // 获取待签名字符串
        String postSignedString = ESignUtils.createPostSignedString(apiUrl, ESignUtils.doContentMD5(bodys));
        // 获取签名值
        String s = ESignUtils.doSignatureBase64(postSignedString, eSignPropertiesConfig.getAppSecret());
        // 构建请求头
        LinkedHashMap<String, String> header = new LinkedHashMap<String, String>();
        header.put("X-Tsign-Open-App-Id", eSignPropertiesConfig.getAppId());
        header.put("X-Tsign-Open-Auth-Mode", "Signature");
        header.put("X-Tsign-Open-Ca-Timestamp", String.valueOf(System.currentTimeMillis()));
        header.put("Accept", "*/*");
        header.put("Content-Type", "application/json;charset=UTF-8");
        header.put("X-Tsign-Open-Ca-Signature", s);
        header.put("Content-MD5", ESignUtils.doContentMD5(bodys));

        return header;
    }

    /**
     * 获取get请求或者post请求参数在路径上body无参数时请求签名鉴权方式的请求头，此种方式无需携带token
     *
     * @param apiUrl 接口请求地址
     * @return map
     */
    private LinkedHashMap<String, String> findHeader(String apiUrl) {
        // get请求或者post请求参数在路径上body无参数时contentMD5为''
        String contentMd5 = "{}";

        // 获取待签名字符串
        String getSignedString = ESignUtils.createGetSignedString(apiUrl);
        // 获取签名值
        String s = ESignUtils.doSignatureBase64(getSignedString, eSignPropertiesConfig.getAppSecret());
        // 构建请求头
        LinkedHashMap<String, String> header = new LinkedHashMap<String, String>();
        header.put("X-Tsign-Open-App-Id", eSignPropertiesConfig.getAppId());
        header.put("X-Tsign-Open-Auth-Mode", "Signature");
        header.put("X-Tsign-Open-Ca-Timestamp", String.valueOf(System.currentTimeMillis()));
        header.put("Accept", "*/*");
        header.put("Content-Type", "application/json;charset=UTF-8");
        header.put("X-Tsign-Open-Ca-Signature", s);
        header.put("Content-MD5", contentMd5);

        return header;
    }

    /**
     * 将List<String>转为String
     *
     * @param list
     * @return
     */
    private String doListToString(List<String> list) {
        StringBuilder result = new StringBuilder();
        boolean flag = true;
        for (String string : list) {
            if (flag) {
                flag = false;
            } else {
                result.append(",");
            }
            result.append(string);
        }
        return result.toString();
    }

    /**
     * 查询个人签署账号
     *
     * @param thirdPartyUserId 第三方平台的用户id
     * @return 查询个人签署账号信息
     */
    @Override
    public PersonAccountDTO getByThirdId(String thirdPartyUserId) {
        // 接口请求地址
        String apiUrl = "/v1/accounts/getByThirdId?thirdPartyUserId=" + thirdPartyUserId;
        // http请求访问url
        String url = eSignPropertiesConfig.getDomainName() + apiUrl;

        LinkedHashMap<String, String> header = findHeader(apiUrl);

        try {
            String s = HTTPHelper.sendGet(url, header, "UTF-8");
            log.info("=========================res:{}", s);
            JSONObject jsonObject = JSONObject.parseObject(s);
            if (0 == jsonObject.getIntValue("code")) {
                JSONObject data = jsonObject.getJSONObject("data");
                PersonAccountDTO personAccountDTO = new PersonAccountDTO();
                personAccountDTO.setIdNumber(data.getString("idNumber"));
                personAccountDTO.setIdType(data.getString("idType"));
                personAccountDTO.setName(data.getString("name"));
                personAccountDTO.setThirdPartyUserId(data.getString("idNumber"));
                personAccountDTO.setMobile(data.getString("mobile"));
                personAccountDTO.setAccountId(data.getString("accountId"));
                return personAccountDTO;
            } else {
                throw new BusinessException(ResultEnum.CALL_THIRD_PARTY_SERVICE_ERROR.getCode(), jsonObject.getString("message"));
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}

package com.stbella.order.server.context.component.processor;

import cn.hutool.core.collection.CollectionUtil;
import com.stbella.asset.api.enums.TradeEventEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.repository.OrderGoodsRepository;
import com.stbella.order.server.manager.AssetManager;
import com.stbella.order.server.order.month.service.impl.OrderAssetTradeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@SnowballComponent(name = "资产取消发放", desc = "线下支付驳回-资产取消发放")
public class AssetRevocationProcessor implements IExecutableAtom<FlowContext> {


    @Resource
    private AssetManager assetManager;

    @Resource
    private OrderGoodsRepository orderGoodsRepository;

    @Resource
    private OrderAssetTradeService orderAssetTradeService;


    @Override
    public boolean condition(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        return Objects.nonNull(orderEntity) && orderEntity.isAssetRevocation();
    }

    @Override
    public void run(FlowContext bizContext) {

        log.info("资产取消发放开始处理");
        HeIncomeRecordEntity heIncomeRecordEntity = bizContext.getAttribute(HeIncomeRecordEntity.class);
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        log.info("资产取消订单id:{}", orderEntity.getOrderId());
        String orderSn = orderEntity.getOrderSn();
        if (OmniOrderTypeEnum.DEPOSIT_ORDER.getCode().equals(orderEntity.getOrderType())){
            log.info("订单sn:{}，押金订单不接入资产中心", orderSn);
            return;
        }
        List<HeOrderGoodsEntity> orderGoodsEntityList = orderGoodsRepository.getByOrderIdList(Collections.singletonList(orderEntity.getOrderId()));
        if (CollectionUtil.isEmpty(orderGoodsEntityList)){
            log.error("订单id:{}，sn:{}，没有商品", orderEntity.getOrderId(), orderSn);
            return;
        }
        List<HeOrderGoodsEntity> heOrderGoodsEntityList = orderGoodsEntityList.stream().filter(orderGoodsEntity -> !CombineTypeEnum.COMBINE.code().equals(orderGoodsEntity.getType())).collect(Collectors.toList());

        Map<String, Integer> countByGoodsSnMap = assetManager.getCountByGoodsSn(orderSn, heOrderGoodsEntityList);

        List<HeOrderGoodsEntity> revocationGoodsList = new ArrayList<>();
        for (HeOrderGoodsEntity heOrderGoodsEntity : heOrderGoodsEntityList){
            if (StringUtils.isEmpty(heOrderGoodsEntity.getOrderGoodsSn())){
                log.warn("订单商品的orderGoodsSn为空，订单商品id：{}", heOrderGoodsEntity.getId());
                continue;
            }
            Integer num = countByGoodsSnMap.get(heOrderGoodsEntity.getOrderGoodsSn());
            if (Objects.isNull(num)){
                log.warn("订单商品的商品货号：{}，对应的资产信息可用数量为0", heOrderGoodsEntity.getOrderGoodsSn());
                continue;
            }
            heOrderGoodsEntity.setGoodsNum(num);
            heOrderGoodsEntity.setRevocationNum(num);
            revocationGoodsList.add(heOrderGoodsEntity);
        }
        if (CollectionUtil.isEmpty(revocationGoodsList)){
            log.info("订单id:{}，sn:{}，没有需要取消发放的商品", orderEntity.getOrderId(), orderSn);
            return;
        }
        Boolean result = assetManager.execMultiUserTrade(revocationGoodsList, orderEntity, TradeEventEnum.ORDER_PAYED_REVOCATION, null, heIncomeRecordEntity.getId().toString());
        if (result) {
            orderGoodsRepository.batchUpdate(revocationGoodsList);
        }
    }


}

package com.stbella.order.server.context.component.processor;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.marketing.api.req.ActivityRuleRequest;
import com.stbella.marketing.api.req.goods.ActivityGoodsReq;
import com.stbella.marketing.api.res.goods.ActivityMinProgramDetailVO;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.service.OrderGoodsDomainService;
import com.stbella.order.domain.repository.OrderGoodsRepository;
import com.stbella.order.server.manager.MarketingManager;
import com.stbella.order.server.utils.JsonUtil;
import com.stbella.platform.order.api.res.PromotionInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 记录下单商品匹配到的活动
 */
@Component
@Slf4j
@SnowballComponent(name = "记录下单商品匹配到的活动", desc = "")
public class OrderGoodsActivityProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    OrderGoodsDomainService orderGoodsDomainService;
    @Resource
    OrderGoodsRepository orderGoodsRepository;
    @Resource
    private MarketingManager marketingManager;

    @Override
    public void run(FlowContext bizContext) {

        try {
            HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
            if (orderEntity == null || orderEntity.getOrderId() == null) {
                log.error("订单信息为空，无法进行价格分摊");
                return;
            }

            log.info("记录下单商品匹配到的活动:{}", orderEntity.getOrderId());

            List<HeOrderGoodsEntity> orderGoodsList = orderGoodsDomainService.queryOrderGoodsWithGroup(orderEntity.getOrderId());
            //只取下单的商品，排除礼赠
            orderGoodsList = orderGoodsList.stream().filter(o -> ObjectUtil.isNotEmpty(o.getGift()) && o.getGift() == 0).collect(Collectors.toList());

            if (orderGoodsList.isEmpty()) {
                log.warn("订单{}没有商品信息", orderEntity.getOrderId());
                return;
            }

            List<HeOrderGoodsEntity> allItermByOrderId = orderGoodsRepository.getAllItermByOrderId(orderEntity.getOrderId());

            if (allItermByOrderId.isEmpty()) {
                log.warn("订单{}没有商品信息2", orderEntity.getOrderId());
                return;
            }

            List<Integer> activityListId = new ArrayList<>();

            allItermByOrderId.stream().forEach(a -> {
                PromotionInfo promotionInfo = a.getPromotionInfo();
                if (ObjectUtil.isNotEmpty(promotionInfo)) {
                    Integer promotionId = promotionInfo.getPromotionId();
                    if (ObjectUtil.isNotEmpty(promotionId)) {
                        activityListId.add(promotionId);
                    }
                }
            });

            if (CollectionUtils.isNotEmpty(activityListId)) {
                log.info("记录下单商品匹配到的活动-订单全部商品:{}", JsonUtil.write(allItermByOrderId));

                ActivityRuleRequest request = new ActivityRuleRequest();
                request.setClientId(orderEntity.getClientUid());
                request.setStoreId(orderEntity.getStoreId());
                request.setOrderScene(orderEntity.getScene());

                for (HeOrderGoodsEntity heOrderGoodsEntity : orderGoodsList) {

                    log.info("记录下单商品匹配到的活动-开始匹配商品:{}", JsonUtil.write(heOrderGoodsEntity));

                    //根据组合商品/单向去匹配活动

                    if (heOrderGoodsEntity.getType().equals(CombineTypeEnum.COMBINE.code().intValue())) {
                        //组合商品

                        List<ActivityGoodsReq> goodsList = new ArrayList<>();
                        ActivityGoodsReq activityGoodsReq = new ActivityGoodsReq();
                        activityGoodsReq.setGoodsId(heOrderGoodsEntity.getGoodsId());
                        activityGoodsReq.setSkuId(heOrderGoodsEntity.getSkuId());
                        goodsList.add(activityGoodsReq);
                        List<HeOrderGoodsEntity> subList = heOrderGoodsEntity.getSubList();
                        if (CollectionUtils.isNotEmpty(subList)) {
                            for (HeOrderGoodsEntity orderGoodsEntity : subList) {
                                activityGoodsReq = new ActivityGoodsReq();
                                activityGoodsReq.setGoodsId(orderGoodsEntity.getGoodsId());
                                activityGoodsReq.setSkuId(orderGoodsEntity.getSkuId());
                                activityGoodsReq.setParentId(heOrderGoodsEntity.getGoodsId());
                                goodsList.add(activityGoodsReq);
                            }
                        }
                        request.setGoodsList(goodsList);
                    } else {
                        //单项
                        List<ActivityGoodsReq> goodsList = new ArrayList<>();
                        ActivityGoodsReq activityGoodsReq = new ActivityGoodsReq();
                        activityGoodsReq.setGoodsId(heOrderGoodsEntity.getGoodsId());
                        activityGoodsReq.setSkuId(heOrderGoodsEntity.getSkuId());
                        goodsList.add(activityGoodsReq);
                        request.setGoodsList(goodsList);
                    }
                    List<ActivityMinProgramDetailVO> activityMinProgramDetailVOS = marketingManager.queryStoreActivityByReq(request);
                    log.info("记录下单商品匹配到的活动-开始匹配商品-匹配到的活动:{}", JsonUtil.write(activityMinProgramDetailVOS));
                    activityMinProgramDetailVOS = activityMinProgramDetailVOS.stream().filter(a -> activityListId.contains(a.getActivityId())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(activityMinProgramDetailVOS)) {
                        List<Integer> activityIdList = activityMinProgramDetailVOS.stream().map(ActivityMinProgramDetailVO::getActivityId).collect(Collectors.toList());
                        Optional<HeOrderGoodsEntity> first = allItermByOrderId.stream().filter(a -> a.getId().equals(heOrderGoodsEntity.getId())).findFirst();
                        if (first.isPresent()) {
                            first.get().setBuyGoodsActivity(JsonUtil.write(activityIdList));
                        }
                    }
                }
                orderGoodsRepository.batchUpdate(allItermByOrderId);
            }
        } catch (Exception e) {
            log.error("订单商品匹配活动失败:{}", e.getMessage());
        }


    }

}

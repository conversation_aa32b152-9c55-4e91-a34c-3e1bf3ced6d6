package com.stbella.order.server.context.component.ui;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.month.OrderButtonEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.server.order.month.res.OrderOperateButton;
import com.stbella.order.server.utils.OrderProcessCheckUtil;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 客诉工单
 */
@Component
@Slf4j
public class OrderComplaintsOperate implements OrderOperate {

    @Resource
    private OrderProcessCheckUtil orderProcessCheckUtil;

    @Override
    public void setPriority(int priority) {

    }

    @Override
    public int getPriority() {
        return 5;
    }

    /**
     * 客诉工单
     *
     * @param order
     * @return
     */
    @Override
    public OrderOperateButton generateButton(OrderOperateContext context) {

        HeOrderEntity order = context.getOrder();

        if (ObjectUtil.isAllNotEmpty(order) && ObjectUtil.isNotEmpty(order.getOrderType()) && order.getOrderType().equals(OmniOrderTypeEnum.DEPOSIT_ORDER.getCode())) {
            //押金订单不显示
            return null;
        }

        boolean realAmount = order.getRealAmount() + order.getProductionAmountPay() > 0;
        if ((!realAmount)) {
            log.info("订单未付款，没有退意向金按钮");
            return null;
        }

        //如果这个单子有核销或者有入住时间，就显示
        OrderOperateButton button = OrderOperateButton.builder().code(getButtonEnum().getCode()).value(getButtonEnum().getValue()).build();
        // 检查是否存在未处理完的流程
        if (orderProcessCheckUtil.hasUnfinishedRefund(order.getOrderId())) {
            log.info("订单{}存在未处理完的审批或退款流程，不显示退意向金按钮", order.getOrderSn());
            button.setText("当前订单存在退款审批中流程，请等待审批完成后再次尝试！");
        }
        return button;

    }

    /**
     * 获取按钮枚举
     *
     * @return
     */
    @Override
    public OrderButtonEnum getButtonEnum() {
        return OrderButtonEnum.ORDER_COMPLAINTS;
    }

    @Override
    public int compareTo(@NotNull IPriority o) {
        return this.getPriority() - o.getPriority();
    }
}

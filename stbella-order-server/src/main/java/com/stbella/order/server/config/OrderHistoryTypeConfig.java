package com.stbella.order.server.config;


import com.stbella.contract.model.enums.TemplateContractTypeEnum;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;


/**
 * 合同历史记录type映射
 *
 * <AUTHOR>
 * @date 2023/11/28
 */
@Data
@Component
public class OrderHistoryTypeConfig implements Serializable {


    public static final Map<Integer, Integer> SPECIAL_TYPE_MAP;
    private static final long serialVersionUID = 1L;

    static {
        SPECIAL_TYPE_MAP = new HashMap<Integer, Integer>() {
            {
                put(TemplateContractTypeEnum.YZ_SAINTBELLA.code(), 1);
                put(TemplateContractTypeEnum.SUPPLIMENT.code(), 2);
            }
        };
    }

}

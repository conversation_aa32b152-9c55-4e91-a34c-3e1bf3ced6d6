package com.stbella.order.server.context.service.contract;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.stbella.contract.model.enums.*;
import com.stbella.contract.model.fact.PaperContractRuleFact;
import com.stbella.contract.model.req.ContractQueryV4;
import com.stbella.contract.model.res.ContractSignAgreementVO;
import com.stbella.contract.model.res.ContractSignRecordVO2;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.core.utils.BigDecimalUtil;
import com.stbella.customer.server.ecp.entity.TabClientPO;
import com.stbella.customer.server.ecp.entity.UserPO;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.month.OrderMonthStoreTypeCostEnum;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.repository.AheadOutRoomRepository;
import com.stbella.order.domain.repository.HeOrderAttachmentRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.domain.repository.OrderUserSnapshotRepository;
import com.stbella.order.server.context.component.checker.OrderEntityForRemarkChecker;
import com.stbella.order.server.convert.AppMonthContractSignRecordConverter;
import com.stbella.order.server.manager.ContractManager;
import com.stbella.order.server.manager.StoreManager;
import com.stbella.order.server.manager.TabClientManager;
import com.stbella.order.server.order.month.enums.ApprovalDiscountStatusEnum;
import com.stbella.order.server.order.month.req.AheadOutRoomQuery;
import com.stbella.order.server.order.month.req.OrderQuery;
import com.stbella.order.server.order.month.res.OrderDiscountsCacheVO;
import com.stbella.order.server.order.month.res.STMOrderInfoMonthInfoVO;
import com.stbella.order.server.order.month.service.MonthOrderAdminQueryService;
import com.stbella.platform.order.api.contract.api.ContractQueryService;
import com.stbella.platform.order.api.contract.req.ContractBaseReq;
import com.stbella.platform.order.api.contract.req.ContractSignQuery;
import com.stbella.platform.order.api.contract.res.*;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import com.stbella.store.server.ecp.entity.CfgStore;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ContractQueryServiceImpl implements ContractQueryService {

    private static final List<Integer> djList = Arrays.asList(OmniOrderTypeEnum.DJ_MOTHER_CARE_ORDER.code(), OmniOrderTypeEnum.DJ_INFANT_CARE_ORDER.code());

    @Resource
    private ContractManager contractManager;
    @Resource
    private StoreManager storeManager;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private RuleLinkClient ruleLinkClient;
    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;
    @Resource
    private TabClientManager tabClientManager;
    @Resource
    private MonthOrderAdminQueryService monthOrderAdminQueryService;
    @Resource
    private AheadOutRoomRepository aheadOutRoomRepository;
    @Resource
    private HeOrderAttachmentRepository heOrderAttachmentRepository;
    @Resource
    private AppMonthContractSignRecordConverter appMonthContractSignRecordConverter;
    @Resource
    private OrderEntityForRemarkChecker orderEntityForRemarkChecker;

    public static final String PAPER_STORE_IDS = "paper_store_ids_rule";



    @Override
    public Result<ContractReconstructOrderRes> list(ContractBaseReq contractBaseQuery) {
        log.info("New contract query: {}", JSONUtil.toJsonStr(contractBaseQuery));
        Long orderId = Long.valueOf(contractBaseQuery.getOrderId());

        HeOrderEntity heOrder = orderRepository.queryOrderById(contractBaseQuery.getOrderId());
        if (Objects.isNull(heOrder)) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "订单不存在");
        }

        List<ContractSignRecordVO2> contractSignRecordVOList = contractManager.getContractSignRecordListByOrderId(orderId);

        checkType(contractBaseQuery, contractSignRecordVOList, heOrder);

        ContractReconstructOrderRes reconstructOrderRes = new ContractReconstructOrderRes();
        if (!heOrder.isNeedSign()) {
            if (!contractBaseQuery.getSignType().equals(OrderSignTypeEnum.SIGN_TYPE_CLIENT.code())) {
                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "签署类型不正确");
            }
            reconstructOrderRes.setIsPrincipal(Boolean.FALSE);
            reconstructOrderRes.setOrderInfoList(new ArrayList<>());
            return Result.success(reconstructOrderRes);
        }

        reconstructOrderRes = buildContractReconstructOrderRes(contractBaseQuery, heOrder, contractSignRecordVOList);

        return Result.success(reconstructOrderRes);
    }

    @Override
    public Result<OrderContractRemindCountVO> remindDetailCount(Integer operatorGuid) {
        OrderContractRemindCountVO result = new OrderContractRemindCountVO();
        // 查询所有订单信息
        List<HeOrderEntity> monthOrders = fetchOrders(operatorGuid);
        monthOrders = monthOrders.stream().filter(HeOrderEntity::isNeedSign).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(monthOrders)) {
            return Result.success(result);
        }
        List<Long> orderIds = monthOrders.stream()
                .map(HeOrderEntity::getOrderId).map(Long::valueOf).collect(Collectors.toList());

        List<ContractSignRecordVO2> contractSignRecords = contractManager.getContractSignRecordListByOrderIdList(orderIds);

        if (contractSignRecords.isEmpty()) {
            return Result.success(result);
        }

        contractSignRecords = contractSignRecords.stream()
                .filter(o -> Objects.equals(ContractStatusEnum.NOT_SIGNED.code(), o.getContractStatus())).collect(Collectors.toList());
        Map<Boolean, Long> countMap = contractSignRecords.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.partitioningBy(
                        record -> Arrays.asList(
                                TemplateContractTypeEnum.YZ_SAINTBELLA.code(),
                                TemplateContractTypeEnum.HOMECARE_MIDDLE_AUNT.code()
                        ).contains(record.getTemplateContractType()),
                        Collectors.counting()
                ));

        result.setAppurtenanceInfoSize(countMap.get(false).intValue());
        result.setOrderInfoSize(countMap.get(true).intValue());

        return Result.success(result);
    }

    private Map<Long, OrderContractSignRecordVO> processAppointmentContracts(List<OrderContractSignRecordVO> contractSignRecords) {
        return contractSignRecords.stream()
                .filter(item -> Objects.equals(item.getTemplateContractType(), TemplateContractTypeEnum.APPOINTMENT.code()))
                .collect(Collectors.toMap(OrderContractSignRecordVO::getGuideId, Function.identity(), (k1, k2) -> k1));
    }

    private Map<Long, OrderContractSignRecordVO> processMasterContracts(List<OrderContractSignRecordVO> contractSignRecords) {
        return contractSignRecords.stream()
                .filter(item -> Objects.equals(item.getTemplateContractType(), TemplateContractTypeEnum.YZ_SAINTBELLA.code())
                        || Objects.equals(item.getTemplateContractType(), TemplateContractTypeEnum.HOMECARE_MIDDLE_AUNT.code()))
                .collect(Collectors.toMap(OrderContractSignRecordVO::getGuideId, Function.identity(), (k1, k2) -> k1));
    }

    private List<OrderContractSignRecordVO> processAgreements(List<OrderContractSignRecordVO> contractSignRecords) {
        return contractSignRecords.stream()
                .filter(item -> Objects.equals(item.getTemplateContractType(), TemplateContractTypeEnum.SUPPLIMENT.code()))
                .collect(Collectors.toList());
    }

    private List<OrderContractInfo> generateOrderContractInfo(List<HeOrderEntity> heOrderList,
                                                              Map<Integer, CfgStore> storeMap,
                                                              Map<Long, TabClientPO> clientMap,
                                                              Map<Integer, STMOrderInfoMonthInfoVO> goodMap,
                                                              Map<Long, OrderContractSignRecordVO> appointmentContractMap,
                                                              Map<Long, OrderContractSignRecordVO> masterContractMap) {
        List<OrderContractInfo> orderInfoList = new ArrayList<>();
        for (HeOrderEntity orderEntity : heOrderList) {
            Long orderId = orderEntity.getOrderId().longValue();
            OrderContractSignRecordVO master = masterContractMap.get(orderId);

            if (Objects.nonNull(master) && Objects.equals(master.getContractStatus(), ContractStatusEnum.SIGNED.code())) {
                continue;
            }

            OrderContractSignRecordVO appointmentContract = appointmentContractMap.get(orderId);

            // Add appointment contract info if necessary
            if (Objects.equals(orderEntity.getOldOrNew(), 1) && Objects.nonNull(appointmentContract)
                    && !Objects.equals(appointmentContract.getContractStatus(), ContractStatusEnum.SIGNED.code())) {
                orderInfoList.add(createOrderContractInfo(orderEntity, storeMap, clientMap, goodMap, appointmentContract, true));
            }

            // Add main contract info
            orderInfoList.add(createOrderContractInfo(orderEntity, storeMap, clientMap, goodMap, master, false));
        }

        return orderInfoList;
    }

    private OrderContractInfo createOrderContractInfo(HeOrderEntity orderEntity,
                                                      Map<Integer, CfgStore> storeMap,
                                                      Map<Long, TabClientPO> clientMap,
                                                      Map<Integer, STMOrderInfoMonthInfoVO> goodMap,
                                                      OrderContractSignRecordVO contract,
                                                      boolean isAppointment) {
        OrderContractInfo info = new OrderContractInfo();
        info.setStoreId(orderEntity.getStoreId());
        info.setStoreName(Optional.ofNullable(storeMap.get(orderEntity.getStoreId())).map(CfgStore::getStoreName).orElse(StringUtils.EMPTY));
        info.setClientName(Optional.ofNullable(clientMap.get(orderEntity.getClientUid().longValue())).map(TabClientPO::getName).orElse(StringUtils.EMPTY));
        info.setGoodsName(Optional.ofNullable(goodMap.get(orderEntity.getOrderId())).map(STMOrderInfoMonthInfoVO::getGoodsName).orElse(StringUtils.EMPTY));
        info.setClientUid(orderEntity.getClientUid());
        info.setOrderId(orderEntity.getOrderId().longValue());
        info.setOldOrNew(orderEntity.getOldOrNew());

        if (isAppointment) {
            info.setContractName(contract.getContractName());
            info.setTemplateContractType(contract.getTemplateContractType());
            info.setTemplateContractTypeName(TemplateContractTypeEnum.getValueByCode(contract.getTemplateContractType()));
            info.setCreatedAt(new Date(contract.getCreatedAt() * 1000));
            info.setAppointmentCreatedAt(new Date(contract.getCreatedAt() * 1000));
            info.setAppointmentStatus(ContractStatusEnum.NOT_SIGNED.code());
        } else {
            String tempContractName = OmniOrderTypeEnum.MONTH_ORDER.getCode().equals(orderEntity.getOrderType())
                    ? TemplateContractTypeEnum.YZ_SAINTBELLA.value()
                    : TemplateContractTypeEnum.YZ_SMALL.value();
            info.setContractName(Objects.nonNull(contract) ? contract.getContractName() : tempContractName);
            info.setTemplateContractType(Objects.nonNull(contract) ? contract.getTemplateContractType() : TemplateContractTypeEnum.YZ_SAINTBELLA.code());
            info.setTemplateContractTypeName(TemplateContractTypeEnum.getValueByCode(info.getTemplateContractType()));
            info.setCreatedAt(new Date((Objects.nonNull(contract) ? contract.getCreatedAt() : orderEntity.getCreatedAt()) * 1000));

            if (Objects.equals(orderEntity.getOldOrNew(), 1) && Objects.nonNull(contract) && Objects.equals(contract.getContractStatus(), ContractStatusEnum.SIGNED.code())) {
                setAppointmentInfo(info, contract);
            } else {
                info.setAppointmentCreatedAt(new Date(orderEntity.getCreatedAt() * 1000));
                info.setAppointmentStatus(ContractStatusEnum.NOT_SIGNED.code());
            }
        }

        return info;
    }

    private void setAppointmentInfo(OrderContractInfo info, OrderContractSignRecordVO contract) {
        Date now = new Date();
        Date signDate = new Date(contract.getUpdatedAt() * 1000);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(signDate);
        calendar.add(Calendar.DAY_OF_MONTH, 30);
        Date failDate = calendar.getTime();

        int payDay = (int) DateUtil.between(signDate, now, DateUnit.DAY);
        int failDay = (int) DateUtil.between(failDate, now, DateUnit.DAY) + 1;
        info.setIntentionDay(Math.max(payDay, 0));
        info.setPreferentialDay(Math.max(failDay, 0));
        info.setAppointmentCreatedAt(new Date(contract.getUpdatedAt() * 1000));
        info.setAppointmentStatus(contract.getContractStatus());
    }

    @Override
    public Result<OrderContractRemindVO> remindDetail(Integer operatorGuid) {
        log.info("登录销售信息：{}", operatorGuid);
        OrderContractRemindVO result = new OrderContractRemindVO();
        // 查询所有订单信息
        List<HeOrderEntity> heOrderList = fetchOrders(operatorGuid);
        heOrderList = heOrderList.stream().filter(HeOrderEntity::isNeedSign).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(heOrderList)) {
            return Result.success(result);
        }

        // 查询所有其它需要显示的关联信息
        List<Integer> orderIdList = heOrderList.stream().map(HeOrderEntity::getOrderId).collect(Collectors.toList());
        List<Long> orderIdLongList = orderIdList.stream().map(Long::valueOf).collect(Collectors.toList());
        Map<Integer, CfgStore> storeMap = fetchStoreInfo(heOrderList);
        Map<Long, TabClientPO> clientMap = fetchClientInfo(heOrderList);
        Map<Integer, STMOrderInfoMonthInfoVO> goodMap = fetchGoodInfo(orderIdList);
        List<OrderContractSignRecordVO> contractSignRecords = fetchContractSignRecords(orderIdLongList);

        // 处理相关联的合同信息
        Map<Long, OrderContractSignRecordVO> appointmentContractMap = processAppointmentContracts(contractSignRecords);
        Map<Long, OrderContractSignRecordVO> masterContractMap = processMasterContracts(contractSignRecords);
        List<OrderContractSignRecordVO> agreementList = processAgreements(contractSignRecords);

        // Generate order contract info
        List<OrderContractInfo> orderInfoList = generateOrderContractInfo(heOrderList, storeMap, clientMap, goodMap, appointmentContractMap, masterContractMap);
        result.setOrderInfoList(sortOrderInfoList(orderInfoList));

        // Generate appurtenance info
        List<OrderAppurtenanceInfo> orderAppurtenanceInfoList = generateOrderAppurtenanceInfo(heOrderList, storeMap, clientMap, goodMap, agreementList, contractSignRecords);
        result.setAppurtenanceInfoList(orderAppurtenanceInfoList);

        return Result.success(result);
    }

    private List<OrderAppurtenanceInfo> generateOrderAppurtenanceInfo(List<HeOrderEntity> heOrderList,
                                                                      Map<Integer, CfgStore> storeMap,
                                                                      Map<Long, TabClientPO> clientMap,
                                                                      Map<Integer, STMOrderInfoMonthInfoVO> goodMap,
                                                                      List<OrderContractSignRecordVO> agreementList,
                                                                      List<OrderContractSignRecordVO> contractSignRecords) {
        List<OrderAppurtenanceInfo> orderAppurtenanceInfoList = new ArrayList<>();

        // Process agreements
        List<OrderAppurtenanceInfo> agreementAddList = processAgreementInfo(heOrderList, storeMap, clientMap, goodMap, agreementList);
        orderAppurtenanceInfoList.addAll(agreementAddList);

        // Process discount contracts
        List<OrderAppurtenanceInfo> discountAddList = processDiscountInfo(heOrderList, storeMap, clientMap, goodMap, contractSignRecords);
        orderAppurtenanceInfoList.addAll(discountAddList);

        // Process entrust contracts
        List<OrderAppurtenanceInfo> entrustAddList = processEntrustInfo(heOrderList, storeMap, clientMap, goodMap, contractSignRecords);
        orderAppurtenanceInfoList.addAll(entrustAddList);

        return orderAppurtenanceInfoList;
    }

    private List<OrderContractInfo> sortOrderInfoList(List<OrderContractInfo> orderInfoList) {
        return orderInfoList.stream()
                .sorted(Comparator.comparing(OrderContractInfo::getAppointmentStatus).reversed()
                        .thenComparing(OrderContractInfo::getAppointmentCreatedAt))
                .collect(Collectors.toList());
    }

    private List<OrderAppurtenanceInfo> processAgreementInfo(List<HeOrderEntity> heOrderList,
                                                             Map<Integer, CfgStore> storeMap,
                                                             Map<Long, TabClientPO> clientMap,
                                                             Map<Integer, STMOrderInfoMonthInfoVO> goodMap,
                                                             List<OrderContractSignRecordVO> agreementList) {
        List<Integer> orderIdList = heOrderList.stream().map(HeOrderEntity::getOrderId).collect(Collectors.toList());
        return agreementList.stream()
                .filter(item -> Objects.equals(item.getContractStatus(), ContractStatusEnum.NOT_SIGNED.code())
                        && orderIdList.contains(item.getGuideId().intValue()))
                .map(record -> createOrderAppurtenanceInfo(record, storeMap, clientMap, goodMap, TemplateContractTypeEnum.SUPPLIMENT))
                .sorted(Comparator.comparing(OrderAppurtenanceInfo::getCreatedAt))
                .collect(Collectors.toList());
    }

    private List<OrderAppurtenanceInfo> processDiscountInfo(List<HeOrderEntity> heOrderList,
                                                            Map<Integer, CfgStore> storeMap,
                                                            Map<Long, TabClientPO> clientMap,
                                                            Map<Integer, STMOrderInfoMonthInfoVO> goodMap,
                                                            List<OrderContractSignRecordVO> contractSignRecords) {
        List<HeOrderEntity> approvalList = getApproval(heOrderList, storeMap);
        Map<Long, OrderContractSignRecordVO> discountMap = contractSignRecords.stream()
                .filter(item -> Objects.equals(TemplateContractTypeEnum.DISCOUNT.code(), item.getTemplateContractType()))
                .collect(Collectors.toMap(OrderContractSignRecordVO::getGuideId, Function.identity(), (k1, k2) -> k1));

        return approvalList.stream()
                .filter(heOrder -> {
                    OrderContractSignRecordVO record = discountMap.get(heOrder.getOrderId().longValue());
                    return Objects.isNull(record) || !record.getContractStatus().equals(ContractStatusEnum.SIGNED.code());
                })
                .map(heOrder -> createOrderAppurtenanceInfo(heOrder, discountMap.get(heOrder.getOrderId().longValue()),
                        storeMap, clientMap, goodMap, TemplateContractTypeEnum.DISCOUNT))
                .sorted(Comparator.comparing(OrderAppurtenanceInfo::getCreatedAt))
                .collect(Collectors.toList());
    }

    private List<OrderAppurtenanceInfo> processEntrustInfo(List<HeOrderEntity> heOrderList,
                                                           Map<Integer, CfgStore> storeMap,
                                                           Map<Long, TabClientPO> clientMap,
                                                           Map<Integer, STMOrderInfoMonthInfoVO> goodMap,
                                                           List<OrderContractSignRecordVO> contractSignRecords) {
        List<HeOrderEntity> entrustList = heOrderList.stream()
                .filter(item -> Objects.equals(item.getSignType(), OrderSignTypeEnum.SIGN_TYPE_BAILOR.code()))
                .collect(Collectors.toList());

        Map<Long, OrderContractSignRecordVO> entrustMap = contractSignRecords.stream()
                .filter(item -> Objects.equals(TemplateContractTypeEnum.ENTRUST.code(), item.getTemplateContractType()))
                .collect(Collectors.toMap(OrderContractSignRecordVO::getGuideId, Function.identity(), (k1, k2) -> k1));

        return entrustList.stream()
                .filter(heOrder -> {
                    OrderContractSignRecordVO record = entrustMap.get(heOrder.getOrderId().longValue());
                    return Objects.isNull(record) || !record.getContractStatus().equals(ContractStatusEnum.SIGNED.code());
                })
                .map(heOrder -> createOrderAppurtenanceInfo(heOrder, entrustMap.get(heOrder.getOrderId().longValue()),
                        storeMap, clientMap, goodMap, TemplateContractTypeEnum.ENTRUST))
                .sorted(Comparator.comparing(OrderAppurtenanceInfo::getCreatedAt))
                .collect(Collectors.toList());
    }

    private OrderAppurtenanceInfo createOrderAppurtenanceInfo(OrderContractSignRecordVO record,
                                                              Map<Integer, CfgStore> storeMap,
                                                              Map<Long, TabClientPO> clientMap,
                                                              Map<Integer, STMOrderInfoMonthInfoVO> goodMap,
                                                              TemplateContractTypeEnum contractType) {
        OrderAppurtenanceInfo info = new OrderAppurtenanceInfo();
        info.setStoreId(record.getStoreId().intValue());
        info.setStoreName(Optional.ofNullable(storeMap.get(record.getStoreId().intValue())).map(CfgStore::getStoreName).orElse(StringUtils.EMPTY));
        info.setClientName(Optional.ofNullable(clientMap.get(record.getClientUid().longValue())).map(TabClientPO::getName).orElse(StringUtils.EMPTY));
        info.setGoodsName(Optional.ofNullable(goodMap.get(record.getGuideId().intValue())).map(STMOrderInfoMonthInfoVO::getGoodsName).orElse(StringUtils.EMPTY));
        info.setContractName(record.getContractName());
        info.setTemplateContractType(contractType.code());
        info.setTemplateContractTypeName(contractType.value());
        info.setCreatedAt(new Date(record.getCreatedAt() * 1000));
        info.setClientUid(record.getClientUid());
        info.setOrderId(record.getGuideId());
        return info;
    }

    private OrderAppurtenanceInfo createOrderAppurtenanceInfo(HeOrderEntity heOrder,
                                                              OrderContractSignRecordVO record,
                                                              Map<Integer, CfgStore> storeMap,
                                                              Map<Long, TabClientPO> clientMap,
                                                              Map<Integer, STMOrderInfoMonthInfoVO> goodMap,
                                                              TemplateContractTypeEnum contractType) {
        OrderAppurtenanceInfo info = new OrderAppurtenanceInfo();
        info.setStoreId(heOrder.getStoreId());
        info.setStoreName(storeMap.get(heOrder.getStoreId()).getStoreName());
        info.setClientName(clientMap.get(heOrder.getClientUid().longValue()).getName());
        STMOrderInfoMonthInfoVO stmOrderInfoMonthInfoVO = goodMap.get(heOrder.getOrderId());
        if (Objects.nonNull(stmOrderInfoMonthInfoVO)) {
            info.setGoodsName(stmOrderInfoMonthInfoVO.getGoodsName());
        }
        if (Objects.nonNull(record)) {
            info.setContractName(record.getContractName());
            info.setCreatedAt(new Date(record.getCreatedAt() * 1000));
        } else {
            info.setContractName(contractType.value());
            info.setCreatedAt(new Date());
        }

        info.setTemplateContractType(contractType.code());
        info.setTemplateContractTypeName(contractType.value());
        info.setClientUid(heOrder.getClientUid());
        info.setOrderId(heOrder.getOrderId().longValue());
        info.setOldOrNew(heOrder.getOldOrNew());
        return info;
    }

    private List<OrderContractSignRecordVO> fetchContractSignRecords(List<Long> orderIdLongList) {
        return contractManager.getContractSignRecordAndAgreementListByOrderIdList(orderIdLongList);
    }

    private Map<Integer, STMOrderInfoMonthInfoVO> fetchGoodInfo(List<Integer> orderIdList) {
        List<STMOrderInfoMonthInfoVO> goodList = monthOrderAdminQueryService.queryOrderGoodsByOrderIdList(orderIdList).getData();
        return goodList.stream().collect(Collectors.toMap(STMOrderInfoMonthInfoVO::getOrderId, Function.identity()));
    }

    private Map<Long, TabClientPO> fetchClientInfo(List<HeOrderEntity> heOrderList) {
        // 用户信息
        List<Integer> clientUidList = heOrderList.stream().map(HeOrderEntity::getClientUid).collect(Collectors.toList());
        List<TabClientPO> clientList = tabClientManager.listByCustomerIdList(clientUidList);
        return clientList.stream().collect(Collectors.toMap(TabClientPO::getId, Function.identity()));
    }

    private Map<Integer, CfgStore> fetchStoreInfo(List<HeOrderEntity> heOrderList) {
        // 门店信息
        List<Integer> storeIdList = heOrderList.stream().map(HeOrderEntity::getStoreId).collect(Collectors.toList());
        List<CfgStore> cfgStoreEntities = storeManager.queryStoreInfoByStoreIdList(storeIdList);
        return cfgStoreEntities.stream().collect(Collectors.toMap(CfgStore::getStoreId, Function.identity()));
    }

    private List<HeOrderEntity> fetchOrders(Integer operatorGuid) {
        OrderQuery orderQuery = new OrderQuery();
        orderQuery.setStaffId(operatorGuid);
        orderQuery.setOrderTypeList(Arrays.asList(OmniOrderTypeEnum.MONTH_ORDER.getCode(), OmniOrderTypeEnum.SMALL_MONTH_ORDER.getCode()));
        return orderRepository.queryByCondition(orderQuery);
    }


    @Override
    public Result<OrderContractRemindVO> oldOrNewContractType(Long orderId, Integer templateContractType) {
        //首先判断已签署的合同是新老合同,后续都是用此类型合同
        HeOrderEntity heOrder = orderRepository.getByOrderId(orderId.intValue());
        ContractSignRecordVO2 mainContract = getMainContract(orderId);
        Integer contractType;
        if (ObjectUtil.isEmpty(mainContract) || !ContractStatusEnum.SIGNED.code().equals(mainContract.getContractStatus())) {
            Set<String> oldContractStoreIds = ruleLinkClient.getOldContractStoreIds();
            contractType = ContractTypeEnum.ESIGN_TYPE.code();
            if (oldContractStoreIds.contains(heOrder.getStoreId().toString())) {
                contractType = ContractTypeEnum.OLD_TYPE.code();
            }
            dealUnSignContract(orderId, mainContract, contractType);
        } else {
            contractType = mainContract.getContractType();
        }
        //先得到一个主合同
        boolean isEsignContractType = Objects.equals(contractType, ContractTypeEnum.ESIGN_TYPE.code());

        OrderContractRemindVO result = new OrderContractRemindVO();

        // 附属合同
        List<OrderAppurtenanceInfo> orderAppurtenanceInfoList = new ArrayList<>();
        // 补充协议
        // 签订记录
        List<OrderContractSignRecordVO> signList = contractManager.getContractSignRecordAndAgreementListByOrderIdList(ListUtil.toList(orderId));
        List<OrderContractSignRecordVO> agreementList = signList.stream().filter(item -> Objects.equals(item.getTemplateContractType(), TemplateContractTypeEnum.SUPPLIMENT.code())).collect(Collectors.toList());


        //展示所有的纸质合同
        List<OrderContractSignRecordVO> contractSignRecordEntities = signList.stream().filter(i -> Objects.equals(i.getContractType(), ContractTypeEnum.PAPER_TYPE.code())).collect(Collectors.toList());
        List<OrderContractSignRecordVO> notMainPaperContract = contractSignRecordEntities
                .stream()
                .filter(i -> !TemplateContractTypeEnum.getMonthMainContract().contains(TemplateContractTypeEnum.getEnumByCode(i.getTemplateContractType()))).collect(Collectors.toList());

        AheadOutRoomQuery aheadOutRoomQuery = new AheadOutRoomQuery();
        aheadOutRoomQuery.setOrderId(orderId.intValue());
        AheadOutRoomEntity aheadOutRoomEntity = aheadOutRoomRepository.queryByOrderId(aheadOutRoomQuery);
        // 订单类合同
        List<OrderContractInfo> orderInfoList = new ArrayList<>();
        HeOrderUserSnapshotEntity userEsign = orderUserSnapshotRepository.queryByOrderId(orderId.intValue());
        for (TemplateContractTypeEnum value : TemplateContractTypeEnum.getMonthMainContract()) {
            OrderContractInfo orderContractInfo = new OrderContractInfo();
            if (Objects.equals(value.code(), TemplateContractTypeEnum.RELEASE.code())) {
                if (Objects.isNull(aheadOutRoomEntity)) {
                    continue;
                }
                orderContractInfo.setApiRoute(1);
            } else {
                orderContractInfo.setApiRoute(0);
            }
            //老合同额外逻辑-不返回预约协议
            if (Objects.equals(value.code(), TemplateContractTypeEnum.APPOINTMENT.code())) {
                if (!isEsignContractType) {
                    continue;
                }
            }
            orderContractInfo.setTemplateType(TemplateTypeEnum.MAIN_TYPE.code());
            orderContractInfo.setTemplateContractType(value.code());
            orderContractInfo.setContractName(value.value());
            orderContractInfo.setContractDesc(value.title());
            orderContractInfo.setOrderId(orderId);
            orderContractInfo.setCertType(userEsign.getCertType());
            orderContractInfo.setAmountEarnest(BigDecimalUtil.divide(new BigDecimal(heOrder.getPayAmount() / 2), new BigDecimal(100)).toString());
            List<OrderContractSignRecordVO> signRecordVOS = signList.stream().filter(i -> Objects.equals(i.getTemplateContractType(), value.code())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(signRecordVOS)) {
                //先找出已签订的单子
                //根据状态和创建时间排序
                signRecordVOS.sort(Comparator.comparing(OrderContractSignRecordVO::getContractStatus).thenComparing(OrderContractSignRecordVO::getCreatedAt).reversed());
                OrderContractSignRecordVO signRecordVO = signRecordVOS.get(0);
                orderContractInfo.setContractType(signRecordVO.getContractType());
                orderContractInfo.setSign(Objects.equals(signRecordVO.getContractStatus(), ContractStatusEnum.SIGNED.code()));
                orderContractInfo.setContractId(signRecordVO.getId().intValue());
                if (ContractTypeEnum.PAPER_TYPE.code().equals(signRecordVO.getContractType())) {
                    orderContractInfo.setApiRoute(2);
                }
            } else {
                orderContractInfo.setSign(false);

            }
            if (Objects.isNull(orderContractInfo.getContractType())) {
                if (isEsignContractType) {
                    orderContractInfo.setContractType(ContractTypeEnum.ESIGN_TYPE.code());
                } else {
                    orderContractInfo.setContractType(ContractTypeEnum.OLD_TYPE.code());
                }
            }
            orderInfoList.add(orderContractInfo);
        }
        result.setOrderInfoList(orderInfoList.stream().sorted(Comparator.comparing(OrderContractInfo::getTemplateContractType).reversed()).collect(Collectors.toList()));

        for (OrderContractSignRecordVO contractSignRecordEntity : notMainPaperContract) {
            List<OrderAppurtenanceInfo> needFilterContractOrderAppurtenanceInfoOrderList = orderAppurtenanceInfoList.stream()
                    .filter(o -> Objects.equals(o.getTemplateContractType(), contractSignRecordEntity.getTemplateContractType()) && !o.getSign()).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(needFilterContractOrderAppurtenanceInfoOrderList)) {
                orderAppurtenanceInfoList.removeAll(needFilterContractOrderAppurtenanceInfoOrderList);
            }
            OrderAppurtenanceInfo contractInfo = new OrderAppurtenanceInfo();
            contractInfo.setContractType(ContractTypeEnum.PAPER_TYPE.code());
            //todo 补充协议没有纸质合同
            contractInfo.setTemplateType(TemplateTypeEnum.ACCESSORY_TYPE.code());
            contractInfo.setTemplateContractType(contractSignRecordEntity.getTemplateContractType());
            contractInfo.setContractName(contractSignRecordEntity.getContractName());
            contractInfo.setOrderId(orderId);
            contractInfo.setContractId(contractSignRecordEntity.getId().longValue());
            contractInfo.setContractDesc(Optional.ofNullable(TemplateContractTypeEnum.getEnumByCode(contractSignRecordEntity.getTemplateContractType())).map(TemplateContractTypeEnum::title).orElse(null));
            contractInfo.setSign(Objects.equals(ContractStatusEnum.SIGNED.code(), contractSignRecordEntity.getContractStatus()));
            contractInfo.setApiRoute(2);
            orderAppurtenanceInfoList.add(contractInfo);
        }
        //新合同设置附属合同补充协议
        if (isEsignContractType) {
            // 添加补充协议
            if (CollectionUtil.isNotEmpty(agreementList)) {
                for (OrderContractSignRecordVO agreement : agreementList) {
                    OrderAppurtenanceInfo contractInfo = new OrderAppurtenanceInfo();
                    contractInfo.setContractType(ContractTypeEnum.ESIGN_TYPE.code());
                    contractInfo.setTemplateType(TemplateTypeEnum.AGREEMENT_TYPE.code());
                    contractInfo.setTemplateContractType(TemplateContractTypeEnum.SUPPLIMENT.code());
                    contractInfo.setContractName(agreement.getContractName());
                    contractInfo.setContractDesc(TemplateContractTypeEnum.SUPPLIMENT.title());
                    contractInfo.setOrderId(orderId);
                    contractInfo.setContractId(agreement.getId());
                    contractInfo.setSign(Objects.equals(ContractStatusEnum.SIGNED.code(), agreement.getContractStatus()));
                    contractInfo.setApiRoute(2);
                    contractInfo.setCertType(userEsign.getCertType());
                    orderAppurtenanceInfoList.add(contractInfo);
                }
            }

            if (Objects.equals(heOrder.getSignType(), com.stbella.order.common.enums.month.OrderSignTypeEnum.SIGN_TYPE_BAILOR.code())) {
                OrderAppurtenanceInfo contractInfo = new OrderAppurtenanceInfo();
                contractInfo.setContractType(ContractTypeEnum.ESIGN_TYPE.code());
                contractInfo.setTemplateType(TemplateTypeEnum.ACCESSORY_TYPE.code());
                contractInfo.setTemplateContractType(TemplateContractTypeEnum.ENTRUST.code());
                contractInfo.setContractName(TemplateContractTypeEnum.ENTRUST.value());
                contractInfo.setContractDesc(TemplateContractTypeEnum.ENTRUST.title());
                contractInfo.setOrderId(orderId);
                Optional<OrderContractSignRecordVO> first = signList.stream().filter
                        (item -> Objects.equals(TemplateContractTypeEnum.ENTRUST.code(), item.getTemplateContractType()) && Objects.equals(ContractStatusEnum.SIGNED.code(), item.getContractStatus())).findFirst();
                contractInfo.setContractId(first.isPresent() ? first.get().getId() : -1L);
                contractInfo.setSign(first.isPresent());
                contractInfo.setApiRoute(1);
                contractInfo.setCertType(userEsign.getCertType());
                orderAppurtenanceInfoList.add(contractInfo);
            }

            // 订单折扣保密协议
            Optional<OrderContractSignRecordVO> first = signList.stream().filter
                    (item -> Objects.equals(TemplateContractTypeEnum.DISCOUNT.code(), item.getTemplateContractType()) && Objects.equals(ContractStatusEnum.SIGNED.code(), item.getContractStatus())).findFirst();
            if (StringUtils.isNotBlank(heOrder.getDiscountDetails()) || first.isPresent()) {
                boolean discountFlag = false;
                //2024-01-15折扣审批前置逻辑,只有无需审批或审批通过或已签署需要展示此协议,其他情况隐藏
                if (StringUtils.isNotBlank(heOrder.getDiscountDetails())) {
                    OrderDiscountsCacheVO discountsCache = JSONObject.toJavaObject(JSON.parseObject(heOrder.getDiscountDetails()), OrderDiscountsCacheVO.class);
                    discountFlag = Objects.nonNull(discountsCache.getOrderDiscount())
                            && (
                            heOrder.getApprovalDiscountStatus().equals(ApprovalDiscountStatusEnum.APPROVED.getCode())
                                    || heOrder.getApprovalDiscountStatus().equals(ApprovalDiscountStatusEnum.NO_APPROVAL_NEEDED.getCode())
                    );
                }
                log.info("订单折扣保密协议,discountFlag={},first.isPresent()={}", discountFlag, first.isPresent());
                if (discountFlag || first.isPresent()) {
                    OrderAppurtenanceInfo contractInfo = new OrderAppurtenanceInfo();
                    contractInfo.setContractType(ContractTypeEnum.ESIGN_TYPE.code());
                    contractInfo.setTemplateType(TemplateTypeEnum.ACCESSORY_TYPE.code());
                    contractInfo.setTemplateContractType(TemplateContractTypeEnum.DISCOUNT.code());
                    contractInfo.setContractName(TemplateContractTypeEnum.DISCOUNT.value());
                    contractInfo.setContractDesc(TemplateContractTypeEnum.DISCOUNT.title());
                    contractInfo.setOrderId(orderId);
                    contractInfo.setContractId(first.isPresent() ? first.get().getId() : -1L);
                    contractInfo.setSign(first.isPresent());
                    contractInfo.setApiRoute(1);
                    contractInfo.setCertType(userEsign.getCertType());
                    orderAppurtenanceInfoList.add(contractInfo);
                }
            }
        }


        //单独一栏 其他合同（纸质合同）
        OrderAppurtenanceInfo contractInfo = new OrderAppurtenanceInfo();
        contractInfo.setContractType(ContractTypeEnum.PAPER_TYPE.code());
        contractInfo.setTemplateContractType(TemplateContractTypeEnum.OTHER.getCode());
        contractInfo.setContractName(TemplateContractTypeEnum.OTHER.getValue());
        contractInfo.setOrderId(orderId);
        contractInfo.setContractId(-1L);
        contractInfo.setContractDesc(TemplateContractTypeEnum.OTHER.getTitle());
        contractInfo.setSign(false);
        contractInfo.setApiRoute(2);
        orderAppurtenanceInfoList.add(contractInfo);
        result.setAppurtenanceInfoList(orderAppurtenanceInfoList);

        //只筛选指定合同模板项
        if (Objects.nonNull(templateContractType)) {
            result.setOrderInfoList(result.getOrderInfoList().stream().filter(i -> Objects.equals(i.getTemplateContractType(), templateContractType)).collect(Collectors.toList()));
            result.setAppurtenanceInfoList(result.getAppurtenanceInfoList().stream().filter(i -> Objects.equals(i.getTemplateContractType(), templateContractType)).collect(Collectors.toList()));
        }

        return Result.success(result);
    }

    private ContractSignRecordVO2 getMainContract(Long orderId) {
        List<ContractSignRecordVO2> contractSignRecordVOList = contractManager.getContractSignRecordListByOrderId(orderId);
        contractSignRecordVOList = contractSignRecordVOList.stream()
                .filter(i -> Objects.equals(i.getTemplateContractType(), TemplateContractTypeEnum.YZ_SAINTBELLA.code())
                        || Objects.equals(i.getTemplateContractType(), TemplateContractTypeEnum.YZ_SMALL.code()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(contractSignRecordVOList)) {

            contractSignRecordVOList.sort(Comparator.comparing(ContractSignRecordVO2::getContractStatus).thenComparing(ContractSignRecordVO2::getCreatedAt).reversed());
            return contractSignRecordVOList.get(0);
        }
        return null;
    }

    @Override
    public Result<List<OrderContractSignRecordVO>> querySignAndAttachmentList(ContractSignQuery signContractQuery) {
        log.info("已签订合同列表&&订单附件查询{}", JSONUtil.toJsonStr(signContractQuery));
        Integer orderId = signContractQuery.getOrderId();
        if (Objects.isNull(orderId)) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), MessageFormat.format("参数{0}不能为空", "orderId"));
        }
        //获取快照操作人姓名
        HeOrderEntity heOrderEntity = orderRepository.queryOrderById(orderId);
        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(orderId);
        List<OrderContractSignRecordVO> contractAttachmentVOList = Lists.newArrayList();

        List<OrderContractSignRecordVO> contractSignVOList = getContractSignVOS(signContractQuery);
        contractSignVOList.forEach(x -> {
            x.setOldOrNew(heOrderEntity.getOldOrNew());
        });
        contractAttachmentVOList.addAll(contractSignVOList);

        //把订单附件解析成合同
        List<HeOrderAttachmentEntity> heOrderAttachmentEntities = heOrderAttachmentRepository.queryListByCondition(orderId);
        List<OrderContractSignRecordVO> orderContractSignRecordVOList = appMonthContractSignRecordConverter.orderAttachment2ContractRecord(heOrderAttachmentEntities);
        contractAttachmentVOList.addAll(orderContractSignRecordVOList);
        //创建时间倒序排列
        contractAttachmentVOList.sort(Comparator.comparing(OrderContractSignRecordVO::getGmtCreate, Comparator.nullsLast(Comparator.naturalOrder())).reversed());

        for (OrderContractSignRecordVO contractAttachmentVO : contractAttachmentVOList) {
            //纸质的单独处理 templateType
            Integer templateContractType = contractAttachmentVO.getTemplateContractType();
            if (Objects.isNull(templateContractType)) {
                templateContractType = TemplateContractTypeEnum.OTHER.code();
            }
            TemplateContractTypeEnum contractTypeEnum = TemplateContractTypeEnum.getEnumByCode(templateContractType);
            boolean isPaperType = Objects.equals(contractAttachmentVO.getContractType(), ContractTypeEnum.PAPER_TYPE.code());
            if (isPaperType && (TemplateContractTypeEnum.getMonthMainContract().contains(contractTypeEnum))) {
                contractAttachmentVO.setTemplateType(TemplateTypeEnum.MAIN_TYPE.code());
            } else if (isPaperType && (TemplateContractTypeEnum.getPaperAccessoryTypeContract().contains(contractTypeEnum))) {
                contractAttachmentVO.setTemplateType(TemplateTypeEnum.ACCESSORY_TYPE.code());
            }
            contractAttachmentVO.setTaskId(heOrderEntity.getTaskId());
            contractAttachmentVO.setOrderType(heOrderEntity.getOrderType());
            //特殊处理操作人 离管协议的操作人
            if (Objects.equals(templateContractType, TemplateContractTypeEnum.OUT_ROOM.getCode())) {
                if (ObjectUtil.isEmpty(contractAttachmentVO.getStaffId())) {
                    contractAttachmentVO.setOperatorName("");
                } else {
                    // TODO合同修改
                    UserPO userEntity = tabClientManager.queryUserById(Long.valueOf(contractAttachmentVO.getStaffId()));
                    contractAttachmentVO.setOperatorName(Objects.nonNull(userEntity) ? userEntity.getName() : "");
                }
            } else {
                contractAttachmentVO.setOperatorName(ObjectUtil.isNotEmpty(heOrderUserSnapshotEntity) ? heOrderUserSnapshotEntity.getSellerName() : "");
            }
            contractAttachmentVO.setContractTypeName(ObjectUtil.isNotEmpty(contractAttachmentVO.getContractType()) ? ContractTypeEnum.fromCode(contractAttachmentVO.getContractType()) : "");
            contractAttachmentVO.setTemplateTypeName(ObjectUtil.isNotEmpty(contractAttachmentVO.getTemplateType()) ? TemplateTypeEnum.fromCode(contractAttachmentVO.getTemplateType()) : "");
        }

        //需要过滤的模板类型
        if (CollectionUtil.isNotEmpty(signContractQuery.getFilterTemplateContractTypes())) {
            contractAttachmentVOList = contractAttachmentVOList.stream().filter(c -> !signContractQuery.getFilterTemplateContractTypes().contains(c.getTemplateContractType())).collect(Collectors.toList());
        }

        return Result.success(contractAttachmentVOList);
    }


    private List<OrderContractSignRecordVO> getContractSignVOS(ContractSignQuery signContractQuery) {
        // 合同签订记录
        List<OrderContractSignRecordVO> signList = contractManager.getContractSignRecordAndAgreementListByOrderIdList(Arrays.asList(signContractQuery.getOrderId().longValue()));
        for (OrderContractSignRecordVO contractSignVO : signList) {
            contractSignVO.setTemplateContractTypeStr(TemplateContractTypeEnum.getValueByCode(contractSignVO.getTemplateContractType()));
        }
        return signList;
    }


    private void dealUnSignContract(Long orderId, ContractSignRecordVO2 mainContract, Integer contractType) {


        ContractQueryV4 contractQueryV4 = new ContractQueryV4();
        contractQueryV4.setGuideIds(Collections.singletonList(orderId.intValue()));
        contractQueryV4.setContractStatus(Arrays.asList(ContractStatusEnum.NOT_SIGNED.code(), ContractStatusEnum.WAITING.code()));
        contractQueryV4.setTemplateTypeList(Arrays.asList(TemplateContractTypeEnum.YZ_SAINTBELLA.code(), TemplateContractTypeEnum.RELEASE.code()));
        List<OrderContractSignRecordVO> orderContractSignRecordVOList = contractManager.queryByV4(contractQueryV4);
        if (CollectionUtil.isNotEmpty(orderContractSignRecordVOList)) {
            boolean shouldDelete = !mainContract.getContractType().equals(contractType) && !ContractTypeEnum.PAPER_TYPE.code().equals(mainContract.getContractType());
            List<Long> deleteContractIds = new ArrayList<>();
            if (shouldDelete) {
                log.info("删除合同记录,orderId={},contractType={},mainContract={}", orderId, contractType, mainContract.getId());
                deleteContractIds.add(mainContract.getId());
            }
            List<OrderContractSignRecordVO> signRecordVOS = orderContractSignRecordVOList.stream().filter(i -> !i.getTemplateContractType().equals(TemplateContractTypeEnum.RELEASE.code())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(signRecordVOS)) {
                OrderContractSignRecordVO orderContractSignRecordVO = signRecordVOS.get(0);
                boolean shouldDelete2 = !orderContractSignRecordVO.getContractType().equals(contractType) && !ContractTypeEnum.PAPER_TYPE.code().equals(mainContract.getContractType());
                if (shouldDelete2) {
                    log.info("删除合同记录,orderId={},contractType={},mainContract={}", orderId, contractType, orderContractSignRecordVO.getId());
                    deleteContractIds.add(orderContractSignRecordVO.getId());
                }
            }

            if (CollectionUtil.isNotEmpty(deleteContractIds)) {
                contractManager.delectByContractIdList(deleteContractIds);
            }
        }
    }


    /**
     * 过滤出 存在订单折扣的数据
     *
     * @param orderEntities
     * @return
     */
    private List<HeOrderEntity> getApproval(List<HeOrderEntity> orderEntities, Map<Integer, CfgStore> storeMap) {
        List<HeOrderEntity> result = new ArrayList<>();
        for (HeOrderEntity orderEntity : orderEntities) {
            if (StringUtils.isBlank(orderEntity.getDiscountDetails())) {
                continue;
            }
            OrderDiscountsCacheVO discountsCache = JSONObject.toJavaObject(JSON.parseObject(orderEntity.getDiscountDetails()), OrderDiscountsCacheVO.class);
            BigDecimal orderDiscount = discountsCache.getOrderDiscount();
            if (Objects.isNull(orderDiscount) || orderDiscount.compareTo(BigDecimal.ZERO) < 0) {
                continue;
            }
            CfgStore cfgStoreEntity = storeMap.get(orderEntity.getStoreId());
            if (Objects.isNull(cfgStoreEntity)) {
                continue;
            }
            //判断门店品牌
            Integer storeType = cfgStoreEntity.getType();
            //获取门店类型
            OrderMonthStoreTypeCostEnum orderMonthStoreTypeCostEnum = null;
            if (storeType == 0) {
                orderMonthStoreTypeCostEnum = OrderMonthStoreTypeCostEnum.SAINT_BELLA;
            } else if (storeType == 1) {
                Integer childType = cfgStoreEntity.getChildType();
                if (ObjectUtil.isNotEmpty(childType) && childType == 0) {
                    orderMonthStoreTypeCostEnum = OrderMonthStoreTypeCostEnum.DELUXE;
                } else {
                    orderMonthStoreTypeCostEnum = OrderMonthStoreTypeCostEnum.BABY_BELLA;
                }
            }
            Boolean flag = false;
            if (OrderMonthStoreTypeCostEnum.SAINT_BELLA.equals(orderMonthStoreTypeCostEnum)) {
                //圣贝拉,折扣率低于百分之86%
                if (orderDiscount.compareTo(new BigDecimal(86)) >= 0) {
                    flag = true;
                }
            } else {
                //获取开店时间
                Date openDate = new Date(cfgStoreEntity.getOpenTime() * 1000);

                Calendar calendar = Calendar.getInstance();
                calendar.setTime(new Date());
                calendar.add(Calendar.MONTH, -6);
                //6个月前的今天
                Date time = calendar.getTime();
                //开店时间小于6个月，折扣70%
                if (time.compareTo(openDate) < 0) {
                    if (orderDiscount.compareTo(new BigDecimal(70)) >= 0) {
                        flag = true;
                    }
                } else {
                    //开店时间大于6个月，折扣80%
                    if (orderDiscount.compareTo(new BigDecimal(80)) >= 0) {
                        flag = true;
                    }
                }
            }
            //低于设置折扣
            if (!flag) {
                result.add(orderEntity);
            }
        }

        return result;
    }


    private ContractReconstructOrderRes buildContractReconstructOrderRes(ContractBaseReq contractBaseQuery, HeOrderEntity heOrder, List<ContractSignRecordVO2> contractSignRecordVOList) {
        ContractReconstructOrderRes reconstructOrderRes = new ContractReconstructOrderRes();
        reconstructOrderRes.setIsPrincipal(Boolean.TRUE);
        reconstructOrderRes.setSignType(heOrder.getSignType());
        setPaperContractFlag(reconstructOrderRes, heOrder);
        reconstructOrderRes.setIsApprove(heOrder.isDiscountPass());

        List<OrderContractInfo> orderInfoList = buildOrderInfoList(contractBaseQuery, heOrder, contractSignRecordVOList);
        HeOrderUserSnapshotEntity userEsign = orderUserSnapshotRepository.queryByOrderId(Math.toIntExact(heOrder.getOrderId()));
        orderInfoList.forEach(orderContractInfo -> {
            orderContractInfo.setOrderId(heOrder.getOrderId().longValue());
            orderContractInfo.setCertType(userEsign.getCertType());
        });

        reconstructOrderRes.setOrderInfoList(orderInfoList);
        setIsPrincipalFlag(reconstructOrderRes, orderInfoList);

        return reconstructOrderRes;
    }

    private void setPaperContractFlag(ContractReconstructOrderRes reconstructOrderRes, HeOrderEntity heOrder) {
        ExecuteRuleV2Req req = new ExecuteRuleV2Req();
        req.setSceneCode(PAPER_STORE_IDS);
        PaperContractRuleFact fact = new PaperContractRuleFact();
        fact.setOrderType(heOrder.getOrderType());
        req.setFactObj(fact);
        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(req);
        if (Objects.nonNull(hitRuleVo) && hitRuleVo.getSimpleRuleValue().contains(heOrder.getStoreId().toString())) {
            reconstructOrderRes.setPaperContract(Boolean.TRUE);
        }
    }

    private List<OrderContractInfo> buildOrderInfoList(ContractBaseReq contractBaseQuery, HeOrderEntity heOrder, List<ContractSignRecordVO2> contractSignRecordVOList) {
        List<OrderContractInfo> orderInfoList = new ArrayList<>();

        //如果是委托人的话，先把委托协议加进来
        if (contractBaseQuery.getSignType().equals(OrderSignTypeEnum.SIGN_TYPE_BAILOR.code())) {
            orderInfoList.add(getEntrustContract(contractSignRecordVOList));
        }

        //主合同
        orderInfoList.add(getMainContract(contractSignRecordVOList, heOrder));
        List<OrderContractInfo> allAgreement = getAllAgreement(heOrder.getOrderId().longValue());
        List<OrderContractInfo> changeOrgName = allAgreement.stream().filter(o -> "主体变更补充协议".equals(o.getContractName())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(changeOrgName)) {
            allAgreement.removeAll(changeOrgName);
        }
        orderInfoList.addAll(allAgreement);
        //折扣保密协议
        dealDiscountContract(contractSignRecordVOList, heOrder, orderInfoList);
        orderInfoList.add(getOtherContract());

        orderInfoList.addAll(getOtherPaperList(contractSignRecordVOList));
        orderInfoList.addAll(changeOrgName);

        return orderInfoList;
    }

    private void dealDiscountContract(List<ContractSignRecordVO2> contractSignRecordVOList, HeOrderEntity heOrder, List<OrderContractInfo> orderInfoList) {
        //已签订的订单折扣保密协议
        Optional<ContractSignRecordVO2> discountFirst = contractSignRecordVOList.stream().filter(item -> Objects.equals(TemplateContractTypeEnum.DISCOUNT.code(),
                item.getTemplateContractType())).findFirst();
        boolean present = discountFirst.isPresent();
        if (!heOrder.isDiscountPass() && !present) {
            return;
        }
        OrderContractInfo discountInfo = new OrderContractInfo();
        log.info("订单折扣保密协议,discountFirst.isPresent()={}", present);
        discountInfo.setContractType(ContractTypeEnum.ESIGN_TYPE.code());
        discountInfo.setTemplateType(TemplateTypeEnum.ACCESSORY_TYPE.code());
        discountInfo.setTemplateContractType(TemplateContractTypeEnum.DISCOUNT.code());
        discountInfo.setContractName(TemplateContractTypeEnum.DISCOUNT.value());
        discountInfo.setContractDesc("订单折扣减免低于折扣标准时，需签署此保密协议；");
        if (present) {
            ContractSignRecordVO2 contractSignRecordVO2 = discountFirst.get();
            if (ContractStatusEnum.SIGNED.code().equals(contractSignRecordVO2.getContractStatus())) {
                discountInfo.setSign(Boolean.TRUE);
            } else {
                discountInfo.setSign(Boolean.FALSE);
            }
            discountInfo.setContractType(contractSignRecordVO2.getContractType());
            discountInfo.setContractId(Math.toIntExact(contractSignRecordVO2.getId()));
        } else {
            discountInfo.setSign(Boolean.FALSE);
            discountInfo.setContractId(-1);
        }
        discountInfo.setApiRoute(1);
        orderInfoList.add(discountInfo);
    }

    private void setIsPrincipalFlag(ContractReconstructOrderRes reconstructOrderRes, List<OrderContractInfo> orderInfoList) {
        OrderContractInfo entrustContract = orderInfoList.stream()
                .filter(item -> TemplateContractTypeEnum.ENTRUST.code().equals(item.getTemplateContractType()))
                .findFirst()
                .orElse(null);

        if (Objects.nonNull(entrustContract) && entrustContract.getSign()) {
            reconstructOrderRes.setIsPrincipal(Boolean.FALSE);
        }

        OrderContractInfo myContract = orderInfoList.stream()
                .filter(item -> TemplateContractTypeEnum.YZ_SAINTBELLA.code().equals(item.getTemplateContractType()))
                .findFirst()
                .orElse(null);

        if (Objects.nonNull(myContract) && Objects.isNull(entrustContract) && myContract.getSign()) {
            reconstructOrderRes.setIsPrincipal(Boolean.FALSE);
        }
    }

    private List<OrderContractInfo> getOtherPaperList(List<ContractSignRecordVO2> contractSignRecordVOList) {
        List<OrderContractInfo> orderContractInfoList = new ArrayList<>();
        for (TemplateContractTypeEnum i : TemplateContractTypeEnum.getPaperContractV2()) {
            ContractSignRecordVO2 contractSignRecordEntity = contractSignRecordVOList.stream().filter(x -> (Objects.equals(x.getTemplateContractType(), i.code()) && Objects.equals(x.getContractType(), ContractTypeEnum.PAPER_TYPE.code()))).findFirst().orElse(null);
            if (Objects.nonNull(contractSignRecordEntity)) {
                OrderContractInfo discountInfo = new OrderContractInfo();
                discountInfo.setContractId(contractSignRecordEntity.getId().intValue());
                discountInfo.setContractName(contractSignRecordEntity.getContractName());
                TemplateContractTypeEnum enumByCode = TemplateContractTypeEnum.getEnumByCode(contractSignRecordEntity.getTemplateContractType());
                discountInfo.setContractDesc(enumByCode.getTitle());
                discountInfo.setContractType(contractSignRecordEntity.getContractType());
                discountInfo.setTemplateType(contractSignRecordEntity.getTemplateType());
                discountInfo.setTemplateContractType(contractSignRecordEntity.getTemplateContractType());
                discountInfo.setSign(contractSignRecordEntity.getContractStatus() == 2);
                discountInfo.setApiRoute(2);
                orderContractInfoList.add(discountInfo);
            }
        }
        return orderContractInfoList;
    }

    private List<OrderContractInfo> getAllAgreement(Long orderId) {
        List<OrderContractInfo> orderInfoList = new ArrayList<>();
        //补充协议多个
        List<ContractSignAgreementVO> agreementList = contractManager.getAgreementByOrderId(orderId);
        // 添加补充协议
        if (CollectionUtil.isNotEmpty(agreementList)) {
            CollectionUtil.sort(agreementList, Comparator.comparing(ContractSignAgreementVO::getName));
            for (ContractSignAgreementVO agreement : agreementList) {
                OrderContractInfo agreementOrderContractInfo = new OrderContractInfo();
                agreementOrderContractInfo.setContractType(ContractTypeEnum.ESIGN_TYPE.code());
                agreementOrderContractInfo.setTemplateType(TemplateTypeEnum.AGREEMENT_TYPE.code());
                agreementOrderContractInfo.setTemplateContractType(TemplateContractTypeEnum.SUPPLIMENT.code());
                agreementOrderContractInfo.setContractName(agreement.getName());
                agreementOrderContractInfo.setContractDesc("对《服务合同》的额外补充；");
                boolean signed = Objects.equals(ContractStatusEnum.SIGNED.code(), agreement.getState());
                agreementOrderContractInfo.setSign(signed);
                agreementOrderContractInfo.setSignStatus(!signed);
                agreementOrderContractInfo.setContractId(Math.toIntExact(agreement.getId()));
                agreementOrderContractInfo.setApiRoute(2);
                orderInfoList.add(agreementOrderContractInfo);
            }
        }
        return orderInfoList;
    }

    private OrderContractInfo getOtherContract() {
        OrderContractInfo otherOrderContractInfo = new OrderContractInfo();
        otherOrderContractInfo.setContractType(ContractTypeEnum.PAPER_TYPE.code());
        otherOrderContractInfo.setTemplateType(null);
        otherOrderContractInfo.setTemplateContractType(TemplateContractTypeEnum.OTHER.getCode());
        otherOrderContractInfo.setContractName(TemplateContractTypeEnum.OTHER.getValue());
        otherOrderContractInfo.setContractDesc(TemplateContractTypeEnum.OTHER.getTitle());
        otherOrderContractInfo.setCertType(null);
        otherOrderContractInfo.setContractId(-1);
        otherOrderContractInfo.setSign(false);
        otherOrderContractInfo.setApiRoute(2);
        return otherOrderContractInfo;
    }


    private OrderContractInfo getMainContract(List<ContractSignRecordVO2> contractSignRecordVOList, HeOrderEntity heOrder) {
        //母婴主合同
        OrderContractInfo orderContractInfo = new OrderContractInfo();
        orderContractInfo.setTemplateType(TemplateTypeEnum.MAIN_TYPE.code());
        orderContractInfo.setTemplateContractType(TemplateContractTypeEnum.YZ_SAINTBELLA.code());
        orderContractInfo.setContractName("服务合同");
        orderContractInfo.setContractDesc((djList.contains(heOrder.getOrderType()) ? "予家" : "月子") + "订单的正式合同，所有下单客户均需完成签订，否则订单将不会记入业绩也无法进行报单；必签");
        //主合同存在多个的情况，取签署了的合同
        List<ContractSignRecordVO2> contractSignRecordVO2s = contractSignRecordVOList
                .stream()
                .filter(item -> Objects.equals(TemplateContractTypeEnum.YZ_SAINTBELLA.code(), item.getTemplateContractType())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(contractSignRecordVO2s)) {
            //先找出已签订的单子
            //根据状态和创建时间排序
            contractSignRecordVO2s.sort(Comparator.comparing(ContractSignRecordVO2::getContractStatus).thenComparing(ContractSignRecordVO2::getCreatedAt).reversed());
            ContractSignRecordVO2 contractSignRecordVO3 = contractSignRecordVO2s.get(0);
            boolean isSign = ContractStatusEnum.SIGNED.code().equals(contractSignRecordVO3.getContractStatus());
            orderContractInfo.setSign(isSign);
            orderContractInfo.setContractId(contractSignRecordVO3.getId().intValue());
            orderContractInfo.setContractType(contractSignRecordVO3.getContractType());
            orderContractInfo.setContractName(contractSignRecordVO3.getContractName());
            if (!isSign && !djList.contains(heOrder.getOrderType())) {
                //看下门店合同是否必签
                Set<Integer> noNeedSignStoreIds = ruleLinkClient.getNoNeedSignStoreIds();
                boolean noNeedSign = noNeedSignStoreIds.contains(heOrder.getStoreId());
                //判断订单是否必签
                boolean check = orderEntityForRemarkChecker.check(heOrder);
                if (noNeedSign || check) {
                    orderContractInfo.setSignStatus(Boolean.FALSE);
                } else {
                    orderContractInfo.setSignStatus(Boolean.TRUE);
                }
            } else if (djList.contains(heOrder.getOrderType())) {
                orderContractInfo.setSignStatus(Boolean.TRUE);
            }
        } else {
            orderContractInfo.setContractType(ContractTypeEnum.ESIGN_TYPE.code());
            orderContractInfo.setSign(Boolean.FALSE);
            //判断订单是否必签
            boolean check = orderEntityForRemarkChecker.check(heOrder);
            Set<Integer> noNeedSignStoreIds = ruleLinkClient.getNoNeedSignStoreIds();
            boolean noNeedSign = noNeedSignStoreIds.contains(heOrder.getStoreId());
            if (noNeedSign || check) {
                orderContractInfo.setSignStatus(Boolean.FALSE);
            } else {
                orderContractInfo.setSignStatus(Boolean.TRUE);
            }
            orderContractInfo.setContractId(null);
            if (djList.contains(heOrder.getOrderType())) {
                orderContractInfo.setSignStatus(Boolean.TRUE);
            }
        }
        orderContractInfo.setApiRoute(0);

        return orderContractInfo;
    }

    private OrderContractInfo getEntrustContract(List<ContractSignRecordVO2> contractSignRecordVOList) {
        OrderContractInfo orderContractInfo = new OrderContractInfo();
        orderContractInfo.setContractType(ContractTypeEnum.ESIGN_TYPE.code());
        orderContractInfo.setTemplateType(TemplateTypeEnum.ACCESSORY_TYPE.code());
        orderContractInfo.setTemplateContractType(TemplateContractTypeEnum.ENTRUST.code());
        orderContractInfo.setContractName(TemplateContractTypeEnum.ENTRUST.value());
        orderContractInfo.setContractDesc("适用委托人代客户签合同的场景；必签");
        ContractSignRecordVO2 entrustContract;
        List<ContractSignRecordVO2> enTrustList = contractSignRecordVOList.stream().filter(i -> Objects.equals(TemplateContractTypeEnum.ENTRUST.code(), i.getTemplateContractType())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(enTrustList)) {
            orderContractInfo.setSign(Boolean.FALSE);
            orderContractInfo.setSignStatus(Boolean.TRUE);
            orderContractInfo.setContractId(null);
            return orderContractInfo;
        }
        entrustContract = enTrustList.get(0);
        orderContractInfo.setSign(ContractStatusEnum.SIGNED.code().equals(entrustContract.getContractStatus()));
        orderContractInfo.setContractId(entrustContract.getId().intValue());
        orderContractInfo.setContractType(entrustContract.getContractType());
        orderContractInfo.setApiRoute(1);
        orderContractInfo.setSignStatus(!orderContractInfo.getSign());
        return orderContractInfo;
    }

    private void checkType(ContractBaseReq contractBaseQuery, List<ContractSignRecordVO2> contractSignRecordVOList, HeOrderEntity heOrder) {
        ArrayList<Integer> contractTypeList = ListUtil.toList(ContractTypeEnum.ESIGN_TYPE.code(), ContractTypeEnum.OLD_TYPE.code(), ContractTypeEnum.PAPER_TYPE.code());
        Integer signType = contractBaseQuery.getSignType();
        if (Objects.equals(signType, OrderSignTypeEnum.SIGN_TYPE_CLIENT.code())) {
            Optional<ContractSignRecordVO2> first = contractSignRecordVOList.stream().filter(i -> contractTypeList.contains(i.getContractType()))
                    .filter(item -> ObjectUtil.equals(TemplateContractTypeEnum.ENTRUST.code(), item.getTemplateContractType())
                            && Objects.equals(ContractStatusEnum.SIGNED.code(), item.getContractStatus()))
                    .filter(item -> Objects.nonNull(item.getTemplateContractType())).findFirst();
            if (first.isPresent()) {
                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "合同主体不可切换，即仅显示受托人且隐藏客户本人签署");
            }
        } else {
            Optional<ContractSignRecordVO2> first = contractSignRecordVOList.stream().filter(i -> contractTypeList.contains(i.getContractType()))
                    .filter(item -> ObjectUtil.equals(TemplateContractTypeEnum.YZ_SAINTBELLA.code(), item.getTemplateContractType())
                            && Objects.equals(ContractStatusEnum.SIGNED.code(), item.getContractStatus()))
                    .filter(item -> Objects.nonNull(item.getTemplateContractType())).findFirst();
            if (first.isPresent() && Objects.equals(heOrder.getSignType(), OrderSignTypeEnum.SIGN_TYPE_CLIENT.code())) {
                throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "合同主体不可切换，即仅显示客户本人且隐藏受托人签署");
            }
        }
    }
}

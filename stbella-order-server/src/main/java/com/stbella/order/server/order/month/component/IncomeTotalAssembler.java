package com.stbella.order.server.order.month.component;

import cn.hutool.core.lang.Assert;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.order.common.constant.StbellaHelperApiConstant;
import com.stbella.order.common.enums.month.StoreAchievementTypeEnum;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.server.order.month.res.AmountSumVo;
import com.stbella.order.server.order.month.res.StoreAchievementResultVO;
import com.stbella.platform.order.api.res.InnerRankRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 获取集团总业绩
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13 14:02
 */
@Component
@Slf4j
public class IncomeTotalAssembler {

    public AmountSumVo queryIncomeTotal(Integer year, Integer month) {
        //todo 这个端代码stbella_helper
        String fullUrl = MessageFormat.format("{0}{1}", StbellaHelperApiConstant.apiUrl, StbellaHelperApiConstant.INCOME_TOTAL);
        try {
            Map<String,Object> map= new HashMap<>();
            map.put("year",year);
            map.put("month",month);
            log.info("发起stbella_helper请求参数：{} body json：{}",fullUrl, JSONUtil.toJsonStr(map));
            String execute = HttpRequest.post(fullUrl).body(JSONUtil.toJsonStr(map)).execute().body();
            JSONObject jsonObject = JSONUtil.parseObj(execute);
            log.info("响应stbella_helper请求结果：{}", JSONUtil.toJsonStr(jsonObject));
            if (jsonObject.containsKey(StbellaHelperApiConstant.CODE) && StbellaHelperApiConstant.SUCCESS_CODE.equals(jsonObject.getStr("code"))) {
                JSONObject data = jsonObject.getJSONObject(StbellaHelperApiConstant.DATA);
                return JSONUtil.toBean(data,AmountSumVo.class);
            } else {
                log.info("获取stbella_helper服务错误信息====={}", jsonObject.get("msg"));
                throw new BusinessException("获取stbella_helper服务错误信息" + jsonObject.get("msg"));
            }
        } catch (HttpException e) {
            log.info("获取stbella_helper服务失败====={}", e.getMessage());
            throw new BusinessException("获取stbella_helper服务失败：" + e.getMessage());
        }
    }

    public StoreAchievementResultVO query(Integer year, Integer month, Integer type) {

        log.info("获取{}年{}月{}类型业绩",year,month,type);
        String fullUrl = MessageFormat.format("{0}{1}", StbellaHelperApiConstant.apiUrl, StbellaHelperApiConstant.INCOME_LIST);
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("year", year);
            map.put("month", month);
            map.put("type", type);
            log.info("发起stbella_helper请求参数：{} body json：{}", fullUrl, JSONUtil.toJsonStr(map));
            String execute = HttpRequest.post(fullUrl).body(JSONUtil.toJsonStr(map)).execute().body();
            JSONObject jsonObject = JSONUtil.parseObj(execute);
            log.info("响应stbella_helper请求结果：{}", JSONUtil.toJsonStr(jsonObject));
            if (jsonObject.containsKey(StbellaHelperApiConstant.CODE) && StbellaHelperApiConstant.SUCCESS_CODE.equals(jsonObject.getStr("code"))) {
                JSONObject data = jsonObject.getJSONObject(StbellaHelperApiConstant.DATA);
                return JSONUtil.toBean(data, StoreAchievementResultVO.class);
            } else {
                log.info("获取stbella_helper服务错误信息====={}", jsonObject.get("msg"));
                throw new BusinessException("获取stbella_helper服务错误信息" + jsonObject.get("msg"));
            }
        } catch (HttpException e) {
            log.info("获取stbella_helper服务失败====={}", e.getMessage());
            throw new BusinessException("获取stbella_helper服务失败：" + e.getMessage());
        }
    }

    public List<InnerRankRes> getRankData(Integer year, Integer month) {

        log.info("获取{}年{}月战区业绩", year, month);
        StoreAchievementResultVO storeAchievementResultVO = this.query(year, month, StoreAchievementTypeEnum.AREA_NET_AMOUNT.getCode());
        log.info("获取{}年{}月战区业绩返回体：{}", year, month, JSONUtil.toJsonStr(storeAchievementResultVO));
        Assert.notNull(storeAchievementResultVO, "获取战区业绩返回体为空");
        List<StoreAchievementResultVO.InnerBrandData> children = storeAchievementResultVO.getChildren();
        Assert.notEmpty(children, "获取战区业绩返回体中children为空");
        StoreAchievementResultVO.InnerBrandData innerBrandData = children.stream().findFirst().orElse(null);
        if (innerBrandData == null){
            log.info("获取{}年{}月战区业绩为空", year, month);
            throw new BusinessException(String.format("获取%s年%s月战区业绩为空", year, month));
        }
        return BeanMapper.mapList(innerBrandData.getRankData(), InnerRankRes.class);
    }

}

package com.stbella.order.server.context.component.processor;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.result.Result;
import com.stbella.order.common.enums.core.*;
import com.stbella.order.domain.order.month.entity.HeIncomeProofRecordEntity;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.IncomeProofRecordRepository;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.server.context.component.spi.OrderBroadcastSpi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.SnowballExtensionInvoker;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: wangchuang
 * @CreateTime: 2024-05-28  13:55
 * @Description: 报单组件
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "报单组件")
public class BroadcastProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @Resource
    private IncomeProofRecordRepository incomeProofRecordRepository;

    @Override
    public boolean condition(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        return orderEntity.isJustEffect() && !OmniOrderTypeEnum.getHomeOrderTypeCodeList().contains(orderEntity.getOrderType());
    }

    @Override
    public void run(FlowContext bizContext) {
        try {
            HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);
            if (OrderNoticeEnum.NOTICE_YES.code().intValue() == order.getIsNotice().intValue()) {
                log.info("订单已报单，无需报单{}", order.getOrderSn());
                return;
            }
            if (ObjectUtil.isNull(order.getPercentFirstTime()) || order.getPercentFirstTime() <= 0) {
                log.info("订单未生效，无需报单{}", order.getOrderSn());
                return;
            }

            Boolean checkOnlyProductPay = checkOnlyProductPay(order);
            if (checkOnlyProductPay) {
                log.info("当前订单全部是产康金支付，无需报单{}", order.getOrderSn());
                return;
            }

            Result invokeResult = SnowballExtensionInvoker.invoke(OrderBroadcastSpi.class, order);
            if (ObjectUtil.isNotNull(invokeResult)) {
                bizContext.setAttribute(Result.class, invokeResult);
            }
        } catch (Exception e) {
            log.error("报单失败", e);
            Result invokeResult = new Result();
            invokeResult.setSuccess(false);
            invokeResult.setMsg(e.getMessage());
            bizContext.setAttribute(Result.class, invokeResult);
        }


    }

    private Boolean checkOnlyProductPay(HeOrderEntity order) {

        if (!order.getOrderType().equals(OmniOrderTypeEnum.PRODUCTION_ORDER.code())) {
            return false;
        }

        List<HeIncomeRecordEntity> allRecordListByOrderId = incomeRecordRepository.getAllRecordListByOrderId(order.getOrderId());

        List<HeIncomeProofRecordEntity> incomeProofRecordByOrderId = incomeProofRecordRepository.getIncomeProofRecordByOrderId(order.getOrderId());

        if (CollectionUtil.isNotEmpty(allRecordListByOrderId)) {

            //所有的产康金支付
            List<HeIncomeRecordEntity> productIncomeList = allRecordListByOrderId.stream().filter(a -> a.getPayType().equals(OmniPayTypeEnum.PRODUCTION_COIN.getCode())).collect(Collectors.toList());

            //非产康金支付的记录
            List<HeIncomeRecordEntity> nonProductIncomeList = allRecordListByOrderId.stream().filter(a -> !a.getPayType().equals(OmniPayTypeEnum.PRODUCTION_COIN.getCode())).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(productIncomeList) && productIncomeList.size() == allRecordListByOrderId.size()) {
                return true;
            }

            if (CollectionUtil.isNotEmpty(nonProductIncomeList)) {

                for (HeIncomeRecordEntity heIncomeRecordEntity : nonProductIncomeList) {
                    if (heIncomeRecordEntity.getPayType().equals(OmniPayTypeEnum.OFFLINE.getCode())) {
                        //线下支付需要判断线下的状态
                        Optional<HeIncomeProofRecordEntity> first = incomeProofRecordByOrderId.stream().filter(i -> i.getIncomeId().equals(heIncomeRecordEntity.getId())).findFirst();
                        if (first.isPresent()) {
                            Integer status = first.get().getStatus();
                            if (OfflineAuditStatusV2Enum.EXAMINATION_PASSED.getCode().equals(status)) {
                                return false;
                            }
                        }
                    } else {
                        if (IncomeRecordPayStatusEnum.COMPLETE.getCode().equals(heIncomeRecordEntity.getStatus())) {
                            return false;
                        }
                    }
                }
            }
        }
        return false;
    }
}

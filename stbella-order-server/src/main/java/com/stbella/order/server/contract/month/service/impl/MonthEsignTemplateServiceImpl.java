package com.stbella.order.server.contract.month.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stbella.order.domain.order.month.entity.MonthEsignTemplateEntity;
import com.stbella.order.domain.repository.MonthEsignTemplateRepository;
import com.stbella.order.server.contract.service.month.MonthEsignTemplateService;
import com.stbella.order.server.convert.AppMonthContractSignRecordConverter;
import com.stbella.order.server.order.month.res.MonthEsignTemplateVO;

import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;


/**
 * 合同模版表,新版E签宝 服务实现类
 * <AUTHOR>
 * @date 2022/11/09
 */
@Service
@Slf4j
public class MonthEsignTemplateServiceImpl  implements MonthEsignTemplateService {

    @Resource
    private AppMonthContractSignRecordConverter appMonthContractSignRecordConverter;
    @Resource
    private MonthEsignTemplateRepository monthEsignTemplateRepository;

    /**
     * 查询合同模版列表 默认排序为创建时间倒序
     *
     * @param page 分页信息
     * @return 列表数据
     */
    @Override
    public Page<MonthEsignTemplateVO> getPage(Page page) {
        Page<MonthEsignTemplateEntity> pageEntitys = monthEsignTemplateRepository.getPage(page);
        List<MonthEsignTemplateVO> monthEsignTemplateVOS = appMonthContractSignRecordConverter.entity2MonthEsignTemplateVoList(pageEntitys.getRecords());
        Page<MonthEsignTemplateVO> monthEsignTemplateVOPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal(), true);
        monthEsignTemplateVOPage.setRecords(monthEsignTemplateVOS);
        return monthEsignTemplateVOPage;
    }

    /**
     * 根据类型获取可用的合同模版
     *
     * @param orderType    订单类型
     * @param storeType    门店类型
     * @param templateType 模版类型1:订单 2:附件 3:补充协议
     * @return 合同模版, 如果有多个的话会返回查询到的第一个
     */
    @Override
    public MonthEsignTemplateVO getByTypes(Integer orderType, Integer storeType, Integer templateType,Integer templateContractType) {
        return appMonthContractSignRecordConverter.entity2MonthEsignTemplateVo(monthEsignTemplateRepository.getByTypes(orderType, storeType, templateType,templateContractType));
    }

    /**
     * 根据类型获取可用的合同模版（增加合同页数·）
     *
     * @param orderType    订单类型
     * @param storeType    门店类型
     * @param templateType 模版类型1:订单 2:附件 3:补充协议
     * @param pageNum      合同页数 为0表示 判断不正常
     * @return 合同模版, 如果有多个的话会返回查询到的第一个
     */
    @Override
    public MonthEsignTemplateVO getByTypes(Integer orderType, Integer storeType, Integer templateType, Integer templateContractType,Integer pageNum) {
        return appMonthContractSignRecordConverter.entity2MonthEsignTemplateVo(monthEsignTemplateRepository.getByTypes(orderType, storeType, templateType, templateContractType,pageNum));
    }

    /**
     * 需要特殊处理的门店通过门店ID获取
     *
     * @param storeId 门店ID
     * @return MonthEsignTemplateEntity 模版信息
     */
    @Override
    public MonthEsignTemplateVO specialGetByStoreId(Integer storeId,Integer orderType, Integer storeType, Integer templateType, Integer templateContractType, Integer pageNum) {
        return appMonthContractSignRecordConverter.entity2MonthEsignTemplateVo(monthEsignTemplateRepository.specialGetByStoreId(storeId,orderType,storeType,templateType,templateContractType,pageNum));
    }

    @Override
    public MonthEsignTemplateVO getByTemplateId(Long id) {
        return appMonthContractSignRecordConverter.entity2MonthEsignTemplateVo(monthEsignTemplateRepository.getByTemplateId(id));
    }
}

package com.stbella.order.server.context.component.ui;

import com.stbella.order.common.enums.month.OrderButtonEnum;
import com.stbella.order.common.enums.month.PayStatusV2Enum;
import com.stbella.order.server.order.month.res.OrderOperateButton;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

/**
 * @Author: ji<PERSON><PERSON>an
 * @CreateTime: 2024-08-12  16:28
 * @Description: 查看退款记录
 */
@Component
@Slf4j
public class CloseOrderOperate implements OrderOperate{
    @Override
    public void setPriority(int priority) {

    }

    @Override
    public int getPriority() {
        return 11;
    }

    /**
     * 生成订单操作按钮
     *
     * @param order
     * @return
     */
    @Override
    public OrderOperateButton generateButton(OrderOperateContext context) {
        if (PayStatusV2Enum.WAIT_PAY.getCode().equals(context.getOrder().getPayStatus()) && context.getOrder().getOrderStatus() == 0) {
            return OrderOperateButton.builder().code(getButtonEnum().getCode()).value("关闭订单").build();
        }
        return null;
    }

    /**
     * 获取按钮枚举
     *
     * @return
     */
    @Override
    public OrderButtonEnum getButtonEnum() {
        return OrderButtonEnum.CLOSE_ORDER;
    }

    @Override
    public int compareTo(@NotNull IPriority o) {
        return this.getPriority() - o.getPriority();
    }
}

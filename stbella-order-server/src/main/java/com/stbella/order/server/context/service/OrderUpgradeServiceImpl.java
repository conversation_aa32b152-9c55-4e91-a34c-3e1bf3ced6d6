package com.stbella.order.server.context.service;

import cn.hutool.core.collection.CollectionUtil;
import com.stbella.core.result.Result;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.manager.StoreManager;
import com.stbella.order.server.order.month.enums.PayMethodEnum;
import com.stbella.platform.order.api.refund.api.OrderRefundService;
import com.stbella.platform.order.api.refund.req.QueryRefundGoodsReq;
import com.stbella.platform.order.api.refund.res.QueryOrderRefundInfoRes;
import com.stbella.platform.order.api.refund.res.QueryRefundGoodsRes;
import com.stbella.platform.order.api.refund.res.RefundGoodsAmountInfo;
import com.stbella.platform.order.api.refund.res.UpGradeGoodsRes;
import com.stbella.platform.order.api.upgrade.api.OrderUpgradeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单升级服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class OrderUpgradeServiceImpl implements OrderUpgradeService {

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private StoreManager storeManager;

    @Resource
    private OrderRefundService orderRefundService;

    @Override
    public Result<List<UpGradeGoodsRes>> queryUpgradeableGoods(Integer orderId) {

        HeOrderEntity order = orderRepository.queryOrderById(orderId);
        if (order == null) {
            return Result.failed("订单不存在");
        }

        QueryRefundGoodsReq req = new QueryRefundGoodsReq();
        req.setOrderId(orderId);
        Result<QueryOrderRefundInfoRes> queryOrderRefundInfoResResult = orderRefundService.queryGoods(req);
        QueryOrderRefundInfoRes data = queryOrderRefundInfoResResult.getData();

        List<Integer> goodsIdList = storeManager.upgradeGoodsIdList(order.getStoreId());
        log.info("可升级的商品列表为{}", goodsIdList);

        // 从data中获取所有商品信息，包括子商品
        List<QueryRefundGoodsRes> allGoods = flattenRefundGoods(data.getQueryRefundGoodsResList());
        
        // 过滤出可升级的商品
        List<QueryRefundGoodsRes> upgradeableGoods = allGoods.stream()
                .filter(goods -> goodsIdList.contains(goods.getGoodsId()))
                .filter(goods -> goods.getRefundGoodsNum() != null && goods.getRefundGoodsNum() > 0)
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(upgradeableGoods)) {
            log.info("订单中没有可升级的商品");
            return Result.success(new ArrayList<>());
        }

        List<UpGradeGoodsRes> upgradeGoodsList = new ArrayList<>();
        for (QueryRefundGoodsRes goods : upgradeableGoods) {
            upgradeGoodsList.add(convertToUpgradeGoods(goods));
        }

        return Result.success(upgradeGoodsList);

    }

    /**
     * 将退款商品列表平铺，包括子商品
     */
    private List<QueryRefundGoodsRes> flattenRefundGoods(List<QueryRefundGoodsRes> goodsList) {
        List<QueryRefundGoodsRes> result = new ArrayList<>();
        if (CollectionUtil.isEmpty(goodsList)) {
            return result;
        }
        
        for (QueryRefundGoodsRes goods : goodsList) {
            result.add(goods);
            
            // 添加子商品
            if (CollectionUtil.isNotEmpty(goods.getChild())) {
                result.addAll(flattenRefundGoods(goods.getChild()));
            }
            
            // 添加附加项
            if (CollectionUtil.isNotEmpty(goods.getAdditionList())) {
                result.addAll(flattenRefundGoods(goods.getAdditionList()));
            }
        }
        
        return result;
    }

    /**
     * 将QueryRefundGoodsRes转换为UpGradeGoodsRes
     */
    private UpGradeGoodsRes convertToUpgradeGoods(QueryRefundGoodsRes refundGoods) {
        UpGradeGoodsRes upgradeGoods = new UpGradeGoodsRes();
        BeanUtils.copyProperties(refundGoods, upgradeGoods);
        
        // 设置可退数量
        upgradeGoods.setRefundGoodsNum(refundGoods.getRefundGoodsNum());
        
        // 从RefundGoodsAmountInfo中获取可退金额
        BigDecimal refundAmount = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(refundGoods.getRefundGoodsAmountInfoList())) {
            refundAmount = refundGoods.getRefundGoodsAmountInfoList().stream()
                    .filter(o-> PayMethodEnum.CASH.getCode().equals(o.getPaymentMethod()))
                    .map(RefundGoodsAmountInfo::getAmount)
                    .filter(java.util.Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        upgradeGoods.setPayAmount(refundAmount);
        
        return upgradeGoods;
    }
}
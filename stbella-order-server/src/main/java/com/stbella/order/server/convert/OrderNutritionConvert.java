package com.stbella.order.server.convert;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stbella.order.server.config.DateMapper;
import com.stbella.order.server.config.MappingConfig;
import com.stbella.order.server.order.month.entity.OrderPayRecordPO;
import com.stbella.order.server.order.month.request.pay.PayNotityRequest;
import com.stbella.order.server.order.nutrition.dto.NutritionImportExcelJD;
import com.stbella.order.server.order.nutrition.dto.PayNotifyMqDTO;
import com.stbella.order.server.order.nutrition.entity.NutritionLogisticsExpensesPO;
import com.stbella.order.server.order.nutrition.entity.OrderNutritionCatchCostPO;
import com.stbella.order.server.order.nutrition.entity.OrderNutritionGrossPO;
import com.stbella.order.server.order.nutrition.entity.OrderNutritionPO;
import com.stbella.order.server.order.nutrition.enums.OrderNutritionAreaTypeEnum;
import com.stbella.order.server.order.nutrition.enums.OrderNutritionRenewEnum;
import com.stbella.order.server.order.nutrition.enums.OrderNutritionSourceChannelEnum;
import com.stbella.order.server.order.nutrition.enums.OrderNutritionSourceEnum;
import com.stbella.order.server.order.nutrition.excel.FailNutritionImportExcel;
import com.stbella.order.server.order.nutrition.excel.NutritionConsumptionExcel;
import com.stbella.order.server.order.nutrition.excel.NutritionExportExcel;
import com.stbella.order.server.order.nutrition.excel.NutritionImportExcel;
import com.stbella.order.server.order.nutrition.request.BaseGuestSaveCostRequest;
import com.stbella.order.server.order.nutrition.request.GuestSaveCostRequest;
import com.stbella.order.server.order.nutrition.request.SimpleGuestSaveCostRequest;
import com.stbella.order.server.order.nutrition.request.SumAllOperateRequest;
import com.stbella.order.server.order.nutrition.response.*;
import com.stbella.order.server.utils.DateUtils;
import com.stbella.pay.server.entity.mq.PayNotifyMq;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: OrderNutritionConvert
 * @date 2022/1/12 2:30 下午
 */
@Mapper(config = MappingConfig.class, uses = DateMapper.class, imports = {OrderNutritionSourceEnum.class})
public interface OrderNutritionConvert {
    OrderNutritionConvert INSTANCE = Mappers.getMapper(OrderNutritionConvert.class);

    Page<OrderNutritionVO> pagePO2VO(Page<OrderNutritionPO> page);

    default List<OrderNutritionPO> listExcel2PO(List<NutritionImportExcel> list) {

        if (list == null) {
            return null;
        }
        List<OrderNutritionPO> list1 = new ArrayList<OrderNutritionPO>(list.size());
        for (NutritionImportExcel nutritionImportExcel : list) {
            list1.add(excel2po(nutritionImportExcel));
        }

        return list1;
    }

    default List<OrderNutritionPO> listExcel2POJD(List<NutritionImportExcelJD> list) {

        if (list == null) {
            return null;
        }
        List<OrderNutritionPO> list1 = new ArrayList<OrderNutritionPO>(list.size());
        for (NutritionImportExcelJD nutritionImportExcel : list) {
            list1.add(excel2poJD(nutritionImportExcel));
        }

        return list1;
    }


    default OrderNutritionPO excel2po(NutritionImportExcel nutritionImportExcel) {
        if (nutritionImportExcel == null) {
            return null;
        }

        OrderNutritionPO orderNutritionPO = new OrderNutritionPO();

        if (nutritionImportExcel.getOutOrderNo() != null) {
            orderNutritionPO.setOutOrderNo(nutritionImportExcel.getOutOrderNo());
        }
        if (nutritionImportExcel.getOrderUserId() != null) {
            orderNutritionPO.setOrderUserId(nutritionImportExcel.getOrderUserId());
        }
        if (nutritionImportExcel.getOrderUserPhone() != null) {
            orderNutritionPO.setOrderUserPhone(nutritionImportExcel.getOrderUserPhone());
        }
        if (nutritionImportExcel.getCommodityName() != null) {
            orderNutritionPO.setCommodityName(nutritionImportExcel.getCommodityName());
        }
        if (nutritionImportExcel.getOrderAmount() != null) {
            orderNutritionPO.setOrderAmount(nutritionImportExcel.getOrderAmount());
        }
        if (nutritionImportExcel.getPayAmount() != null) {
            orderNutritionPO.setPayAmount(nutritionImportExcel.getPayAmount());
        }
        if (nutritionImportExcel.getOrderSourceStr() != null) {
            orderNutritionPO.setOrderSource(OrderNutritionSourceEnum.getCodeByValue(nutritionImportExcel.getOrderSourceStr()));
        }
        if (StringUtils.isNotEmpty(nutritionImportExcel.getOrderArea())) {
            orderNutritionPO.setOrderArea(OrderNutritionAreaTypeEnum.getCodeByValue(nutritionImportExcel.getOrderArea()));
        }
        if (nutritionImportExcel.getOrderTime() != null) {
            orderNutritionPO.setOrderTime(nutritionImportExcel.getOrderTime());
        }
        if (nutritionImportExcel.getCsRemark() != null) {
            orderNutritionPO.setCsRemark(nutritionImportExcel.getCsRemark());
        }
        if (nutritionImportExcel.getBuyerMessage() != null) {
            orderNutritionPO.setBuyerMessage(nutritionImportExcel.getBuyerMessage());
        }
        if (nutritionImportExcel.getRenew() != null) {
            orderNutritionPO.setRenew(OrderNutritionRenewEnum.getEumCodeByValue(nutritionImportExcel.getRenew()));
        }
        if (nutritionImportExcel.getChannelSource() != null) {
            orderNutritionPO.setChannelSource(OrderNutritionSourceChannelEnum.getEnumCodeByValue(nutritionImportExcel.getChannelSource()));
        }
        return orderNutritionPO;
    }

    default OrderNutritionPO excel2poJD(NutritionImportExcelJD nutritionImportExcel) {
        if (nutritionImportExcel == null) {
            return null;
        }

        OrderNutritionPO orderNutritionPO = new OrderNutritionPO();

        if (nutritionImportExcel.getOutOrderNo() != null) {
            orderNutritionPO.setOutOrderNo(nutritionImportExcel.getOutOrderNo());
        }
        if (nutritionImportExcel.getOrderUserId() != null) {
            orderNutritionPO.setOrderUserId(nutritionImportExcel.getOrderUserId());
        }
        if (nutritionImportExcel.getOrderUserPhone() != null) {
            orderNutritionPO.setOrderUserPhone(nutritionImportExcel.getOrderUserPhone());
        }
        if (nutritionImportExcel.getCommodityName() != null) {
            orderNutritionPO.setCommodityName(nutritionImportExcel.getCommodityName());
        }
        if (nutritionImportExcel.getOrderAmount() != null) {
            orderNutritionPO.setOrderAmount(nutritionImportExcel.getOrderAmount());
        }
        if (nutritionImportExcel.getPayAmount() != null) {
            orderNutritionPO.setPayAmount(nutritionImportExcel.getPayAmount());
        }
        if (nutritionImportExcel.getOrderSourceStr() != null) {
            orderNutritionPO.setOrderSource(OrderNutritionSourceEnum.getCodeByValue(nutritionImportExcel.getOrderSourceStr()));
        }
        if (StringUtils.isNotEmpty(nutritionImportExcel.getOrderArea())) {
            orderNutritionPO.setOrderArea(OrderNutritionAreaTypeEnum.getCodeByValue(nutritionImportExcel.getOrderArea()));
        }
        if (nutritionImportExcel.getOrderTime() != null) {
            orderNutritionPO.setOrderTime(nutritionImportExcel.getOrderTime());
        }
        if (nutritionImportExcel.getSrcTids() != null) {
            orderNutritionPO.setSrcTids(nutritionImportExcel.getSrcTids());
        }
        if (nutritionImportExcel.getCsRemark() != null) {
            orderNutritionPO.setCsRemark(nutritionImportExcel.getCsRemark());
        }
        if (nutritionImportExcel.getBuyerMessage() != null) {
            orderNutritionPO.setBuyerMessage(nutritionImportExcel.getBuyerMessage());
        }
        if (nutritionImportExcel.getIncome() != null) {
            orderNutritionPO.setIncome(nutritionImportExcel.getIncome());
        }
        return orderNutritionPO;
    }

    List<FailNutritionImportExcel> listExcel2Fail(List<NutritionImportExcel> list);

    FailNutritionImportExcel excel2Fail(NutritionImportExcel nutritionImportExcel);

    List<NutritionExportExcel> OrderNutritionPOList2NutritionExportExcelList(List<OrderNutritionPO> orderNutritionPOList);

    SumAllOperateVO sumAllOperateRequest2SumAllOperateVO(SumAllOperateRequest sumAllOperateRequest);

    PayNotifyMqDTO PayNotifyMq2PayNotifyMqDTO(PayNotifyMq payNotifyMq);

    PayNotifyMqDTO PayNotityRequest2PayNotifyMqDTO(PayNotityRequest payNotityRequest);

    List<SimplePayVO> orderPayRecordPOList2simplePayVOList(List<OrderPayRecordPO> orderPayRecordPOList);

    List<MaoriSaveShopByYearVO> orderNutritionGrossPOList2MaoriSaveShopByYearVOList(List<OrderNutritionGrossPO> orderNutritionGrossPOList);

    List<OrderNutritionCatchCostPO> guestSaveCostRequestList2orderNutritionCatchCostPOList(List<GuestSaveCostRequest> guestSaveCostRequestList);

    List<GuestSaveCostByMonthVO> orderNutritionCatchCostPOList2GuestSaveCostByMonthVOList(List<OrderNutritionCatchCostPO> orderNutritionCatchCostPOList);

    default List<NutritionConsumptionExcel> nutritionConsumptionAmountVOList2nutritionConsumptionExcelList(List<OrderNutritionConsumptionAmountVO> nutritionConsumptionAmountVOList) {
        return null;
//        List<NutritionConsumptionExcel> nutritionConsumptionExcelList = new ArrayList<>();
//
//        nutritionConsumptionAmountVOList.stream().forEach(n -> {
//            NutritionConsumptionExcel nutritionConsumptionExcel = new NutritionConsumptionExcel();
//            nutritionConsumptionExcel.setMonth(n.getMonth() + "月");
//            nutritionConsumptionExcel.setTotalAmountConsumption(ObjectUtil.isEmpty(n.getTotalConsumptionAmount()) ? "-" : n.getTotalConsumptionAmount());
//            nutritionConsumptionExcel.setFirstSinglePurchase(ObjectUtil.isEmpty(n.getFirstBuy()) ? "-" : n.getFirstBuy());
//            nutritionConsumptionExcel.setMonthAfterPurchase(ObjectUtil.isEmpty(n.getThisRePurchase()) ? "-" : n.getThisRePurchase());
//            nutritionConsumptionExcel.setFirst2MonthsAfterPurchase(ObjectUtil.isEmpty(n.getTwoRePurchase()) ? "-" : n.getTwoRePurchase());
//            nutritionConsumptionExcel.setFirst3MonthsAfterPurchase(ObjectUtil.isEmpty(n.getThreeRePurchase()) ? "-" : n.getThreeRePurchase());
//            nutritionConsumptionExcel.setFirst4MonthsAfterPurchase(ObjectUtil.isEmpty(n.getFourRePurchase()) ? "-" : n.getFourRePurchase());
//            nutritionConsumptionExcel.setFirst5MonthsAfterPurchase(ObjectUtil.isEmpty(n.getFiveRePurchase()) ? "-" : n.getFiveRePurchase());
//            nutritionConsumptionExcel.setFirst6MonthsAfterPurchase(ObjectUtil.isEmpty(n.getSixRePurchase()) ? "-" : n.getSixRePurchase());
//            nutritionConsumptionExcel.setFirst7MonthsAfterPurchase(ObjectUtil.isEmpty(n.getSevenRePurchase()) ? "-" : n.getSevenRePurchase());
//            nutritionConsumptionExcel.setFirst8MonthsAfterPurchase(ObjectUtil.isEmpty(n.getEightRePurchase()) ? "-" : n.getEightRePurchase());
//            nutritionConsumptionExcel.setFirst9MonthsAfterPurchase(ObjectUtil.isEmpty(n.getNineRePurchase()) ? "-" : n.getNineRePurchase());
//            nutritionConsumptionExcel.setFirst10MonthsAfterPurchase(ObjectUtil.isEmpty(n.getTenRePurchase()) ? "-" : n.getTenRePurchase());
//            nutritionConsumptionExcel.setFirst11MonthsAfterPurchase(ObjectUtil.isEmpty(n.getElevenRePurchase()) ? "-" : n.getElevenRePurchase());
//            nutritionConsumptionExcel.setFirst12MonthsAfterPurchase(ObjectUtil.isEmpty(n.getTwelveRePurchase()) ? "-" : n.getTwelveRePurchase());
//            nutritionConsumptionExcelList.add(nutritionConsumptionExcel);
//        });
//
//        return nutritionConsumptionExcelList;
//    }
//
//    default List<NutritionConsumptionBuyNumExcel> nutritionConsumptionNumVO2nutritionConsumptionExcelList(List<OrderNutritionConsumptionNumVO> collect) {
//        List<NutritionConsumptionBuyNumExcel> nutritionConsumptionExcelList = new ArrayList<>();
//
//        collect.stream().forEach(n -> {
//            NutritionConsumptionBuyNumExcel nutritionConsumptionExcel = new NutritionConsumptionBuyNumExcel();
//            nutritionConsumptionExcel.setMonth(n.getMonth() + "月");
//            nutritionConsumptionExcel.setTotalNumConsumption(ObjectUtil.isEmpty(n.getTotalConsumptionNum()) ? "-" : n.getTotalConsumptionNum());
//            nutritionConsumptionExcel.setFirstSinglePurchase(ObjectUtil.isEmpty(n.getFirstBuy()) ? "-" : n.getFirstBuy());
//            nutritionConsumptionExcel.setMonthAfterPurchase(ObjectUtil.isEmpty(n.getThisRePurchase()) ? "-" : n.getThisRePurchase());
//            nutritionConsumptionExcel.setFirst2MonthsAfterPurchase(ObjectUtil.isEmpty(n.getTwoRePurchase()) ? "-" : n.getTwoRePurchase());
//            nutritionConsumptionExcel.setFirst3MonthsAfterPurchase(ObjectUtil.isEmpty(n.getThreeRePurchase()) ? "-" : n.getThreeRePurchase());
//            nutritionConsumptionExcel.setFirst4MonthsAfterPurchase(ObjectUtil.isEmpty(n.getFourRePurchase()) ? "-" : n.getFourRePurchase());
//            nutritionConsumptionExcel.setFirst5MonthsAfterPurchase(ObjectUtil.isEmpty(n.getFiveRePurchase()) ? "-" : n.getFiveRePurchase());
//            nutritionConsumptionExcel.setFirst6MonthsAfterPurchase(ObjectUtil.isEmpty(n.getSixRePurchase()) ? "-" : n.getSixRePurchase());
//            nutritionConsumptionExcel.setFirst7MonthsAfterPurchase(ObjectUtil.isEmpty(n.getSevenRePurchase()) ? "-" : n.getSevenRePurchase());
//            nutritionConsumptionExcel.setFirst8MonthsAfterPurchase(ObjectUtil.isEmpty(n.getEightRePurchase()) ? "-" : n.getEightRePurchase());
//            nutritionConsumptionExcel.setFirst9MonthsAfterPurchase(ObjectUtil.isEmpty(n.getNineRePurchase()) ? "-" : n.getNineRePurchase());
//            nutritionConsumptionExcel.setFirst10MonthsAfterPurchase(ObjectUtil.isEmpty(n.getTenRePurchase()) ? "-" : n.getTenRePurchase());
//            nutritionConsumptionExcel.setFirst11MonthsAfterPurchase(ObjectUtil.isEmpty(n.getElevenRePurchase()) ? "-" : n.getElevenRePurchase());
//            nutritionConsumptionExcel.setFirst12MonthsAfterPurchase(ObjectUtil.isEmpty(n.getTwelveRePurchase()) ? "-" : n.getTwelveRePurchase());
//            nutritionConsumptionExcelList.add(nutritionConsumptionExcel);
//        });
//
//        return nutritionConsumptionExcelList;
    }

    default List<SimpleGuestSaveCostRequest> guestSaveCostRequest2SimpleGuestSaveCostRequestList(GuestSaveCostRequest guestSaveCostRequest) {
        List<SimpleGuestSaveCostRequest> simpleGuestSaveCostRequestList = new ArrayList();

        List<BaseGuestSaveCostRequest> baseGuestSaveCostRequestList = guestSaveCostRequest.getBaseGuestSaveCostRequestList();

        if (CollectionUtil.isNotEmpty(baseGuestSaveCostRequestList)) {

            Optional<BaseGuestSaveCostRequest> expensesOptional = baseGuestSaveCostRequestList.stream().filter(b -> "花费（元）".equals(b.getType())).findFirst();

            BaseGuestSaveCostRequest guestSaveCostExpensesRequest = null;

            if (expensesOptional.isPresent()) {
                guestSaveCostExpensesRequest = expensesOptional.get();
            }

            for (int i = 1; i <= 12; i++) {

                BigDecimal expenses = null;

                if (ObjectUtil.isNotEmpty(guestSaveCostExpensesRequest)) {
                    String byMonth = guestSaveCostExpensesRequest.getByMonth(i);
                    if (StringUtils.isNotEmpty(byMonth)) {
                        expenses = new BigDecimal(guestSaveCostExpensesRequest.getByMonth(i));
                    }
                }

                if (ObjectUtil.isNotEmpty(expenses)) {
                    SimpleGuestSaveCostRequest simpleGuestSaveCostRequest = new SimpleGuestSaveCostRequest();
                    simpleGuestSaveCostRequest.setYear(guestSaveCostRequest.getYear());
                    simpleGuestSaveCostRequest.setType(guestSaveCostRequest.getType());
                    simpleGuestSaveCostRequest.setMonth(i);
                    simpleGuestSaveCostRequest.setExpenses(expenses);
                    simpleGuestSaveCostRequestList.add(simpleGuestSaveCostRequest);
                }
            }
        }
        return simpleGuestSaveCostRequestList;
    }

    default ArrayList<String> OrderNutritionConsumptionBaseVO2LinkedHashMap(OrderNutritionConsumptionBaseVO orderNutritionConsumptionBaseVO) {

        ArrayList result = new ArrayList();

        Integer year = orderNutritionConsumptionBaseVO.getYear();
        Integer month = orderNutritionConsumptionBaseVO.getMonth();

        if (ObjectUtil.isEmpty(year)) {
            result.add("均值");
        } else {
            String yyyyMM = DateUtils.addMonthFormat((year + String.format("%02d", month)), 0, DateUtils.formatyyyyMM, "yyyy年MM月");
            //设置年月
            result.add(yyyyMM);
        }

        List<OrderNutritionConsumptionInitVO> orderNutritionConsumptionInitVOS = orderNutritionConsumptionBaseVO.getOrderNutritionConsumptionInitVOS();
        result.addAll(orderNutritionConsumptionInitVOS.stream().sorted(Comparator.comparing(OrderNutritionConsumptionInitVO::getForm)).map(m -> {
            if (StringUtils.isEmpty(m.getResult())) {
                return "-";
            }
            return m.getResult();
        }).collect(Collectors.toList()));
        return result;
    }

    NutritionLogisticsExpensesVO NutritionLogisticsExpensesPO2NutritionLogisticsExpensesVO(NutritionLogisticsExpensesPO nutritionLogisticsExpensesPO);
}

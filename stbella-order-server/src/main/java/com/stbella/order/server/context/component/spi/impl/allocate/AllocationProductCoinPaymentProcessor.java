package com.stbella.order.server.context.component.spi.impl.allocate;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundGoodsEntity;
import com.stbella.order.domain.order.month.entity.IncomePaidAllocationEntity;
import com.stbella.order.domain.order.service.OrderRefundDomainService;
import com.stbella.order.domain.repository.IncomePaidAllocationRepository;
import com.stbella.order.domain.repository.OrderGoodsRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.context.component.settlement.ProductCoinPayAssembler;
import com.stbella.order.server.context.component.spi.AllocateSpi;
import com.stbella.order.server.context.dto.OrderAmountDto;
import com.stbella.order.server.context.dto.OrderGoodsAllocationDto;
import com.stbella.order.server.convert.PaidAllocationConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.extpoint.annotation.FuncPointSpiImpl;
import top.primecare.snowball.extpoint.definition.FuncSpiConfig;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 产康金分摊
 * <p>
 * 首先得出该订单可用产康金支付的总数
 * <p>
 * 再取出各个商品可用产康金支付的金额
 * <p>
 * 然后根据已付金额，分摊到各个商品上
 */
@Slf4j
@Component
@FuncPointSpiImpl(name = "产康金支付分摊")
public class AllocationProductCoinPaymentProcessor implements AllocateSpi {

    @Resource
    ProductCoinPayAssembler productCoinPayAssembler;

    @Resource
    OrderGoodsRepository orderGoodsRepository;

    @Resource
    PaidAllocationConverter allocationConverter;

    @Resource
    IncomePaidAllocationRepository allocationRepository;

    @Resource
    OrderRepository orderRepository;

    @Resource
    OrderRefundDomainService refundDomainService;


    @Override
    public JSONObject invoke(HeIncomeRecordEntity incomeRecordEntity) {

        HeOrderEntity order = orderRepository.queryOrderById(incomeRecordEntity.getOrderId());

        //产康金待支付总金额
        Integer productCoinPayAmount = productCoinPayAssembler.fetchOrderAmount(order);

        // 获取所有成功退款的商品
        List<HeOrderRefundGoodsEntity> allSuccessfulRefunds = refundDomainService.querySuccessfulOrderGoodsAllocationRefund(order.getOrderId());
        // 计算每个商品的已退款数量
        Map<String, Integer> orderGoodsRefundNumMap = getOrderGoodsRefundNumMap(allSuccessfulRefunds);

        //每个商品可用产康金支付金额
        List<HeOrderGoodsEntity> orderGoodsEntityList = orderGoodsRepository.getAllItermByOrderId(order.getOrderId());
        //过滤掉不可用产康金支付的商品
        List<HeOrderGoodsEntity> productCoinPayList = orderGoodsEntityList.stream().filter(o -> !o.isGift() && o.isProductCoinCanPay()).collect(Collectors.toList());
        Map<Integer, HeOrderGoodsEntity> productCoinPayMap = productCoinPayList.stream().collect(Collectors.toMap(HeOrderGoodsEntity::getId, Function.identity()));
        Map<Integer, Integer> productCoinPayIdMap = productCoinPayAssembler.fetchOrderGoodsProductCoinAmount(productCoinPayList, order);
        // 每个支付金额
        Integer income = incomeRecordEntity.getIncome();
        List<OrderGoodsAllocationDto> allocationDtos = new ArrayList<>();
        for (Integer orderGoodId : productCoinPayMap.keySet()) {
            Integer goodsCoinPayAmount = productCoinPayIdMap.get(orderGoodId);
            if (goodsCoinPayAmount == null) {
                log.error("商品id：{}，可用产康金支付金额为空", orderGoodId);
                continue;
            }
            log.info("商品id：{}，可用产康金支付金额：{}", orderGoodId, goodsCoinPayAmount);
            HeOrderGoodsEntity heOrderGoodsEntity = productCoinPayMap.get(orderGoodId);
            OrderGoodsAllocationDto orderGoodsAllocationDto = initAllocationDto(heOrderGoodsEntity, orderGoodsRefundNumMap);
            // 只有可分摊数量大于0的商品才参与分摊
            if (orderGoodsAllocationDto.getGoodsNum() > 0) {
                orderGoodsAllocationDto.setWaitPayAmount(goodsCoinPayAmount);
                allocationDtos.add(orderGoodsAllocationDto);
            }
        }

        OrderAmountDto orderAmountDto = new OrderAmountDto()
                .setCurrentIncomeId(incomeRecordEntity.getId())
                .setCurrentPay(AmountChangeUtil.changeF2Y(income.longValue()))
                .setTotalAmount(AmountChangeUtil.changeF2Y(productCoinPayAmount));

        allocate(orderAmountDto, allocationDtos);

        //过滤 allocation 为 0的情况
        List<OrderGoodsAllocationDto> allocationList = allocationDtos.stream().filter(al -> al.getAllocation() > 0).collect(Collectors.toList());

        List<IncomePaidAllocationEntity> allocationEntities = allocationConverter.dto2EntityList(allocationList);

        fillInEntityBaseInfo(allocationEntities, incomeRecordEntity);

        fillInEntityProductInfo(allocationEntities, orderGoodsEntityList);

        allocationRepository.saveOrUpdateBatch(allocationEntities);

        return null;
    }


    /**
     * 填充实体商品信息
     *
     * @param allocationEntities
     * @param orderGoodsEntities
     */
    private void fillInEntityProductInfo(List<IncomePaidAllocationEntity> allocationEntities, List<HeOrderGoodsEntity> orderGoodsEntities) {

        Map<Integer, HeOrderGoodsEntity> orderGoodsMap = orderGoodsEntities.stream().collect(Collectors.toMap(HeOrderGoodsEntity::getId, a -> a));

        allocationEntities.forEach(entity -> {
            HeOrderGoodsEntity orderGoodsEntity = orderGoodsMap.get(entity.getOrderGoodsId().intValue());
            entity.setOrderGoodsSn(orderGoodsEntity.getOrderGoodsSn());
            entity.setAssetType(orderGoodsEntity.getGoodsType() + "");
            entity.setBusinessType(orderGoodsEntity.getBusinessType());
            entity.setBackCategoryId(orderGoodsEntity.getBackCategoryId());
            entity.setSkuName(orderGoodsEntity.getGoodsName());
            entity.setSkuId(orderGoodsEntity.getSkuId());
            entity.setGoodsId(orderGoodsEntity.getGoodsId());
        });
    }

    /**
     * 填充分摊实体基础信息
     *
     * @param allocationEntities
     * @param income
     */
    private void fillInEntityBaseInfo(List<IncomePaidAllocationEntity> allocationEntities, HeIncomeRecordEntity income) {
        allocationEntities.forEach(entity -> {
            entity.setBasicId(income.getBasicUid());
            entity.setIncomeId(income.getId());
            entity.setOrderId(income.getOrderId());
            entity.setStoreId(income.getStoreId());
            entity.setPaymentMethod(income.getPayType() + "");
        });
    }

    /**
     * 分摊
     *
     * @param order
     * @param allocationDtos
     */
    public void allocate(OrderAmountDto order, List<OrderGoodsAllocationDto> allocationDtos) {
        int index = 0;

        //按 goodsPriceOrigin 升序排序 allocationDtos （尾差分到单间最高的）
        allocationDtos.sort(Comparator.comparing(OrderGoodsAllocationDto::getGoodsPriceOrigin));

        log.info("分摊前：{}", JSONUtil.toJsonStr(allocationDtos));
        log.info("分摊前order：{}", JSONUtil.toJsonStr(order));

        BigDecimal sumAllocationAmount = new BigDecimal(0);
        BigDecimal currentPay = order.getCurrentPay();

        for (OrderGoodsAllocationDto allocation : allocationDtos) {

            log.info("开始一轮分摊状态：{}", JSONUtil.toJsonStr(allocation));

            BigDecimal allocationAmount = new BigDecimal(0);
            if (index == allocationDtos.size() - 1) {
                allocationAmount = currentPay.subtract(sumAllocationAmount);
                if (allocationAmount.compareTo(BigDecimal.ZERO) < 0) {
                    throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR.code() + "", "分摊异常, 金额小于0，请检查金额是否有修改 支付记录id：" + order.getCurrentIncomeId());
                }
                log.info("尾差分摊金额：{}", allocationAmount);
            } else {
                // A分摊实付 = P * (A待付 / O待付 )
                allocationAmount = currentPay.multiply(AmountChangeUtil.changeF2Y(allocation.getWaitPayAmount())).divide(order.getTotalAmount(), 2, RoundingMode.DOWN);
                log.info("分摊金额：{},应付：{} , 待付：{}", allocationAmount, allocation.getWaitPayAmount(), order.getTotalAmount());
                sumAllocationAmount = sumAllocationAmount.add(allocationAmount);
            }

            allocation.setIncomeId(order.getCurrentIncomeId());
            allocation.setAllocation(AmountChangeUtil.changeY2FFoInt(allocationAmount));
            index++;
        }

    }

    private OrderGoodsAllocationDto initAllocationDto(HeOrderGoodsEntity goodsEntity, Map<String, Integer> orderGoodsRefundNumMap) {
        // 计算可分摊数量：原购买数量 - 已退款数量
        Integer originalNum = goodsEntity.getGoodsNum();
        Integer refundedNum = orderGoodsRefundNumMap.getOrDefault(goodsEntity.getOrderGoodsSn(), 0);
        Integer availableNum = originalNum - refundedNum;

        // 确保可分摊数量不为负数
        availableNum = Math.max(0, availableNum);

        // 按比例计算可分摊的总金额：(可分摊数量 / 原数量) * 原总金额
        Integer availableTotalAmount = 0;
        if (originalNum > 0) {
            // 使用long避免Integer溢出
            long totalPrice = (long) goodsEntity.getTotalAllocationOriginPrice() * availableNum;
            availableTotalAmount = (int) (totalPrice / originalNum);
        }

        OrderGoodsAllocationDto orderGoodsAllocationDto = new OrderGoodsAllocationDto()
                .setOrderGoodsId(goodsEntity.getId())
                .setOrderGoodsSn(goodsEntity.getOrderGoodsSn())
                .setGoodsPriceOrigin(goodsEntity.getAllocationOriginPrice())
                .setTotalAmount(availableTotalAmount)
                .setGoodsNum(availableNum);
        return orderGoodsAllocationDto;
    }

    /**
     * 计算每个商品的已退款数量
     *
     * @param allSuccessfulRefunds
     * @return
     */
    private Map<String, Integer> getOrderGoodsRefundNumMap(List<HeOrderRefundGoodsEntity> allSuccessfulRefunds) {
        Map<String, List<HeOrderRefundGoodsEntity>> orderGoodsRefundListMap = allSuccessfulRefunds.stream()
                .collect(Collectors.groupingBy(HeOrderRefundGoodsEntity::getOrderGoodsSn));

        Map<String, Integer> orderGoodsRefundNumMap = new HashMap<>();
        orderGoodsRefundListMap.forEach((orderGoodsSn, refundList) -> {
            int totalRefundNum = refundList.stream().mapToInt(HeOrderRefundGoodsEntity::getRefundNum).sum();
            orderGoodsRefundNumMap.put(orderGoodsSn, totalRefundNum);
        });

        return orderGoodsRefundNumMap;
    }

    @Override
    public boolean condition(HeIncomeRecordEntity param) {
        return OmniPayTypeEnum.PRODUCTION_COIN.getCode().equals(param.getPayType());
    }


    @Override
    public FuncSpiConfig config() {
        return null;
    }
}

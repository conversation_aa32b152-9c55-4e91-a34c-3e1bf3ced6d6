package com.stbella.order.server.context.component;

import cn.hutool.json.JSONUtil;
import com.stbella.base.server.security.ISysRoleService;
import com.stbella.base.server.security.entity.SysRolePO;
import com.stbella.core.base.UserTokenInfoDTO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.utils.JwtUtil;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.server.order.DiscountFact;
import com.stbella.order.server.order.DiscountFactReq;
import com.stbella.order.server.order.OrderIndex;
import com.stbella.order.server.order.OrderIndexFact;
import com.stbella.order.server.order.month.enums.DiscountRuleResultEnum;
import com.stbella.rule.api.req.BatchExecuteRuleReq;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.core.context.FlowContext;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: jijunjian
 * @CreateTime: 2023-11-23  13:49
 * @Description: 折扣计算器
 */
@Slf4j
@Component
public class DiscountCalculator {

    @Resource
    RuleLinkClient ruleLinkClient;
    @DubboReference
    ISysRoleService sysRoleService;
    @Value("${order.discountRule:new_discount_trigger}")
    private String discountRule;

    public OrderIndex run(FlowContext bizContext) {

        OrderIndexFact indexModel = bizContext.getAttribute(OrderIndexFact.class);

        log.info("订单指标开始执行 {}", JSONUtil.toJsonStr(indexModel));

        // 根据业务线，指标类型 请求Rule link 实现计算
        // 同时对毛利等三个指标进行计算
        indexModel.setTxtRate(getTaxRate(indexModel));

        log.info("查询Tax {}", indexModel.getTxtRate());


        ExecuteRuleV2Req taxExecuteRuleV2Req = new ExecuteRuleV2Req();
        taxExecuteRuleV2Req.setSceneCode("currency_text_rate");

        BatchExecuteRuleReq req = new BatchExecuteRuleReq();
        List<ExecuteRuleV2Req> factList = new ArrayList<>();

        String sceneCode = "order_index_config";
        if (Boolean.TRUE.equals(indexModel.getIsNewOrder()) && OmniOrderTypeEnum.PRODUCTION_ORDER.code().equals(Integer.valueOf(indexModel.getOrderType()))) {
            sceneCode = "new_order_index_config";
        }
        Integer roundingMode = BigDecimal.ROUND_DOWN;

        //订单折扣
        {
            OrderIndexFact discountModel = BeanMapper.map(indexModel, OrderIndexFact.class);
            discountModel.setIndexType(1);
            ExecuteRuleV2Req discountExecuteRuleV2Req = new ExecuteRuleV2Req();
            discountExecuteRuleV2Req.setSceneCode(sceneCode);
            discountExecuteRuleV2Req.setFactObj(discountModel);
            discountExecuteRuleV2Req.setRoundingMode(roundingMode);
            factList.add(discountExecuteRuleV2Req);
        }
        //母婴净折扣率
        {
            OrderIndexFact netDiscountModel = BeanMapper.map(indexModel, OrderIndexFact.class);
            netDiscountModel.setIndexType(2);
            ExecuteRuleV2Req netDiscountExecuteRuleV2Req = new ExecuteRuleV2Req();
            netDiscountExecuteRuleV2Req.setSceneCode(sceneCode);
            netDiscountExecuteRuleV2Req.setFactObj(netDiscountModel);
            netDiscountExecuteRuleV2Req.setRoundingMode(roundingMode);
            factList.add(netDiscountExecuteRuleV2Req);
        }
        //母婴订单毛利计算
        {
            OrderIndexFact grossModel = BeanMapper.map(indexModel, OrderIndexFact.class);
            grossModel.setIndexType(3);
            ExecuteRuleV2Req grossExecuteRuleV2Req = new ExecuteRuleV2Req();
            grossExecuteRuleV2Req.setSceneCode(sceneCode);
            grossExecuteRuleV2Req.setFactObj(grossModel);
            grossExecuteRuleV2Req.setRoundingMode(roundingMode);
            factList.add(grossExecuteRuleV2Req);
        }
        //母婴签单折扣率计算
        {
            OrderIndexFact grossModel = BeanMapper.map(indexModel, OrderIndexFact.class);
            grossModel.setIndexType(4);
            ExecuteRuleV2Req selectedExecuteRuleV2Req = new ExecuteRuleV2Req();
            selectedExecuteRuleV2Req.setSceneCode(sceneCode);
            selectedExecuteRuleV2Req.setFactObj(grossModel);
            selectedExecuteRuleV2Req.setRoundingMode(roundingMode);
            factList.add(selectedExecuteRuleV2Req);
        }
        req.setIterm(factList);
        log.info("折扣计算传参={}", JSONUtil.toJsonStr(req));
        List<HitRuleVo> ruleVos = ruleLinkClient.batchHitAndCalculateRule(req);

        //根据顺序返回结果
        OrderIndex orderIndex = new OrderIndex();
        if (CollectionUtils.isEmpty(ruleVos) || ruleVos.size() < factList.size()) {
            orderIndex.setDiscountMargin(new BigDecimal(0));
            orderIndex.setNetMargin(new BigDecimal(0));
            orderIndex.setGrossMargin(new BigDecimal(0));
            orderIndex.setSignOrderDiscountMargin(new BigDecimal(0));
            log.error("订单指标计算异常，未查到规则，{}", ruleVos);
        }

        if (Objects.nonNull(ruleVos.get(0).getSimpleRuleValue())) {

            String value = ruleVos.get(0).getSimpleRuleValue();
            if (value.contains("E")) {
                value = "0";
            }
            orderIndex.setDiscountMargin(new BigDecimal(value));
            // 保留0位小数
            orderIndex.setDiscountMargin(orderIndex.getDiscountMargin().setScale(0, RoundingMode.DOWN));
        }
        if (Objects.nonNull(ruleVos.get(1).getSimpleRuleValue())) {
            String value = ruleVos.get(1).getSimpleRuleValue();
            if (value.contains("E")) {
                value = "0";
            }
            orderIndex.setNetMargin(new BigDecimal(value));
            orderIndex.setNetMargin(orderIndex.getNetMargin().setScale(0, RoundingMode.DOWN));
        }
        if (Objects.nonNull(ruleVos.get(2).getSimpleRuleValue())) {
            String value = ruleVos.get(2).getSimpleRuleValue();
            if (value.contains("E")) {
                value = "0";
            }
            orderIndex.setGrossMargin(new BigDecimal(value));
            orderIndex.setGrossMargin(orderIndex.getGrossMargin().setScale(0, RoundingMode.DOWN));
        }

        if (Objects.nonNull(ruleVos.get(3).getSimpleRuleValue())) {
            String value = ruleVos.get(3).getSimpleRuleValue();
            if (value.contains("E")) {
                value = "0";
            }
            orderIndex.setSignOrderDiscountMargin(new BigDecimal(value).setScale(0, RoundingMode.DOWN));
        }
        return orderIndex;
    }


    public Boolean isDiscountApproval(DiscountFact discountFact) {
        discountFact.setIsNewOrder(Boolean.FALSE);
        ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req("discount_trigger", discountFact);
        log.info("匹配规则-判断是否需要折扣审批,参数={}", JSONUtil.toJsonStr(executeRuleV2Req));
        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
        log.info("匹配规则-判断是否需要折扣审批,结果={}", JSONUtil.toJsonStr(hitRuleVo));
        if (hitRuleVo != null) {
            return Integer.valueOf(hitRuleVo.getSimpleRuleValue()).equals(1);
        }
        //如果没有默认false
        return false;
    }

    /**
     * @description: 判断是否需要折扣审批
     * @author: jijunjian
     * @date: 11/23/23 19:37
     * @param: [fact]
     * @return: java.math.BigDecimal
     **/
    public Integer isDiscountApprovalNew(DiscountFactReq discountFactReq) {

        BatchExecuteRuleReq req1 = buildExecuteRuleReq(discountFactReq);
        log.info("匹配规则-判断是否需要折扣审批,参数={}", JSONUtil.toJsonStr(req1));
        List<HitRuleVo> hitRuleVos = ruleLinkClient.batchHitRule(req1);
        log.info("匹配规则-判断是否需要折扣审批,结果={}", JSONUtil.toJsonStr(hitRuleVos));
        if (CollectionUtils.isEmpty(hitRuleVos)) {
            log.info("匹配规则-判断是否需要折扣审批,结果为空");
            return DiscountRuleResultEnum.NOT_SUBMITTABLE.getCode();
        }

        List<Integer> discountResult = hitRuleVos.stream()
                .filter(Objects::nonNull)
                .map(HitRuleVo::getSimpleRuleValue)
                .filter(Objects::nonNull)
                .map(Integer::valueOf)
                .collect(Collectors.toList());

        if (discountResult.contains(DiscountRuleResultEnum.NO_APPROVAL_REQUIRED.getCode())) {
            log.info("匹配规则-判断是否需要折扣审批,结果={}", false);
            return DiscountRuleResultEnum.NO_APPROVAL_REQUIRED.getCode();
        }
        if (discountResult.contains(DiscountRuleResultEnum.APPROVAL_REQUIRED.getCode())) {
            log.info("匹配规则-判断是否需要折扣审批,结果={}", true);
            return DiscountRuleResultEnum.APPROVAL_REQUIRED.getCode();
        }
        if (discountResult.contains(DiscountRuleResultEnum.NOT_SUBMITTABLE.getCode())) {
            return DiscountRuleResultEnum.NOT_SUBMITTABLE.getCode();
        }
        //TODO 未匹配到规则，不可提交
        return DiscountRuleResultEnum.NOT_SUBMITTABLE.getCode();
    }

    private @NotNull BatchExecuteRuleReq buildExecuteRuleReq(DiscountFactReq discountFactReq) {
        //获取当前用户角色
        final UserTokenInfoDTO jwtTokenUserInfo = JwtUtil.getJwtTokenUserInfo();
        List<Long> roleIds = jwtTokenUserInfo.getRoleIds();
        if (CollectionUtils.isEmpty(roleIds)) {
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code() + "", "用户角色不能为空");
        }
        List<SysRolePO> sysRolePOS = sysRoleService.listByIds(roleIds);
        //转换成Integer类型idList
        List<Integer> roleIdList = sysRolePOS.stream().map(o -> o.getId().intValue()).collect(Collectors.toList());
        BatchExecuteRuleReq req1 = new BatchExecuteRuleReq();
        List<ExecuteRuleV2Req> executeRuleV2Reqs = new ArrayList<>();
        for (Integer roleId : roleIdList) {
            ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req();
            DiscountFact discountFact = new DiscountFact();
            discountFact.setRoleId(roleId);
            discountFact.setDiscountMargin(discountFactReq.getDiscountMargin());
            discountFact.setNetMargin(discountFactReq.getNetMargin());
            discountFact.setOrderType(discountFactReq.getOrderType());
            discountFact.setSignMargin(discountFactReq.getSignOrderDiscountMargin());
            discountFact.setStoreBrand(discountFactReq.getStoreBrand());
            discountFact.setGrossMargin(discountFactReq.getGrossMargin());
            discountFact.setIsNewOrder(discountFactReq.getIsNewOrder());
            discountFact.setOrderScene(discountFactReq.getOrderScene());
            discountFact.setStoreId(discountFactReq.getStoreId());
            discountFact.setWarArea(discountFactReq.getWarArea());
            executeRuleV2Req.setSceneCode(discountRule);
            executeRuleV2Req.setFactObj(discountFact);
            executeRuleV2Reqs.add(executeRuleV2Req);
        }
        req1.setIterm(executeRuleV2Reqs);
        return req1;
    }

    /**
     * @description: 获取税率，如果没有默认0，不抛异常，不返回null，返回0，避免空指针异常
     * @author: jijunjian
     * @date: 11/23/23 19:37
     * @param: [fact]
     * @return: java.math.BigDecimal
     **/
    public BigDecimal getTaxRate(OrderIndexFact fact) {
        log.info("开始获取taxRate={}", JSONUtil.toJsonStr(fact));
        //获取税率
        String storeCurrencyCode = StoreCurrencyContainer.getStoreCurrencyCode(fact.getStoreId());
        //删除可能的空格
        String pureCurrency = storeCurrencyCode.trim();
        Map<String, Object> factMap = new HashMap<>(1);
        factMap.put("currency", pureCurrency);
        factMap.put("startTime", fact.getEffectDate());
        ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req("currency_text_rate", factMap);

        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
        log.info("获取结果hitRuleVo={}", JSONUtil.toJsonStr(hitRuleVo));
        if (hitRuleVo != null) {
            return new BigDecimal(hitRuleVo.getSimpleRuleValue());
        }
        //如果没有默认0
        return new BigDecimal(0);
    }

}

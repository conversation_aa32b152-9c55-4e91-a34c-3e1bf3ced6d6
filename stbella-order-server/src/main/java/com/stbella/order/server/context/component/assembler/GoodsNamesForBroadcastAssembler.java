package com.stbella.order.server.context.component.assembler;

import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.production.GoodsEntity;
import com.stbella.order.domain.order.service.OrderGoodsDomainService;
import com.stbella.order.domain.repository.GoodsRepository;
import com.stbella.order.server.context.dto.OrderBroadcastDto;
import com.stbella.order.server.utils.JsonUtil;
import com.stbella.order.server.utils.wangdian.StringUtils;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-05-29  15:20
 * @Description: 组装订单报单中商品名称
 * 月子护理服务 商品名称组合
 */
@Component
@Slf4j
@SnowballComponent(name = "组装订单报单中商品名称")
public class GoodsNamesForBroadcastAssembler implements IExecutableAtom<FlowContext> {

    @Resource
    OrderGoodsDomainService orderGoodsDomainService;
    @Resource
    RuleLinkClient ruleLinkClient;
    @Resource
    private GoodsRepository goodsRepository;

    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity order = bizContext.getAttribute(HeOrderEntity.class);
        List<HeOrderGoodsEntity> goodsEntityList = orderGoodsDomainService.queryOrderGoodsWithGroup(order.getOrderId());
        String goodsNames = "";
        String productGoodsNames = "";
        List<HeOrderGoodsEntity> santifyGoodsList = new ArrayList<>();

        List<GoodsEntity> goodsEntities = goodsRepository.queryByIdForDelete(goodsEntityList.stream().map(HeOrderGoodsEntity::getGoodsId).collect(Collectors.toList()));

        Map<String, String> factMap = new HashMap<>();
        factMap.put("configCode", "master_edition_middleGoods_id");
        ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req("order-center-config", factMap);
        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
        String simpleRuleValue = hitRuleVo.getSimpleRuleValue();
        //大师商品中台id
        List<Integer> masterEditionGoodsIdList = JsonUtil.readList(simpleRuleValue, Integer.class);
        log.info("大师版组合中台Id:{}", JsonUtil.write(masterEditionGoodsIdList));

        if (order.isNewOrder()) {
            factMap = new HashMap<>();
            factMap.put("configCode", order.getOrderType().equals(OmniOrderTypeEnum.PRODUCTION_ORDER.code()) ? "Broadcast_product_Category_Ids" : "Broadcast_Category_Ids");
            executeRuleV2Req = new ExecuteRuleV2Req("systemConfig", factMap);
            hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);

            Set<String> baseCategoryIds = Arrays.stream(hitRuleVo.getSimpleRuleValue().split(",")).collect(Collectors.toSet());
            for (HeOrderGoodsEntity goodsEntity : goodsEntityList) {
                Optional<GoodsEntity> first = goodsEntities.stream().filter(g -> g.getId().equals(goodsEntity.getGoodsId())).findFirst();
                //如果是月子且下了特定的商品，确认要爆单
                if (first.isPresent() && masterEditionGoodsIdList.contains(first.get().getParentId()) && order.getOrderType().equals(OmniOrderTypeEnum.MONTH_ORDER.code())) {
                    santifyGoodsList.add(goodsEntity);
                    //下了特定的商品
                    order.setSpecificOrderItems(true);
                    continue;
                }
                // 判断是不是组合商品
                if (CombineTypeEnum.COMBINE.code().equals(goodsEntity.getType())) {
                    List<HeOrderGoodsEntity> subList = goodsEntity.getSubList();
                    for (HeOrderGoodsEntity subGoodsEntity : subList) {
                        if (baseCategoryIds.contains(subGoodsEntity.getBackCategoryId() + "")) {
                            santifyGoodsList.add(goodsEntity);
                            break;
                        }
                    }
                    continue;
                }
                if (baseCategoryIds.contains(goodsEntity.getBackCategoryId() + "")) {
                    santifyGoodsList.add(goodsEntity);
                }
            }
        } else {
            santifyGoodsList = goodsEntityList;
        }

        factMap = new HashMap<>();
        factMap.put("configCode", "master_edition");
        executeRuleV2Req = new ExecuteRuleV2Req("order-center-config", factMap);
        hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
        simpleRuleValue = hitRuleVo.getSimpleRuleValue();
        List<Integer> activityIdList = JsonUtil.readList(simpleRuleValue, Integer.class);
        log.info("大师版活动Id:{}", JsonUtil.write(activityIdList));

        for (HeOrderGoodsEntity goodsEntity : santifyGoodsList) {
            goodsNames += goodsEntity.getGoodsName();
            String buyGoodsActivity = goodsEntity.getBuyGoodsActivity();
            if (StringUtils.areNotEmpty(buyGoodsActivity)) {
                List<Integer> read = JsonUtil.read(buyGoodsActivity, List.class);
                log.info("当前商品参与的活动为：{}", JsonUtil.write(read));
                for (Integer activityId : read) {
                    if (activityIdList.contains(activityId)) {
                        goodsNames += "-大师版";
                        break;
                    }
                }
            }
            goodsNames += ",";
            productGoodsNames += (goodsEntity.getGoodsName() + "*" + goodsEntity.getGoodsNum()) + ",";
        }

        log.info("需要报单的商品列表:{}", JsonUtil.write(santifyGoodsList));

        // 删除最后一个逗号
        if (goodsNames.length() > 0) {
            goodsNames = goodsNames.substring(0, goodsNames.length() - 1);
        }
        if (productGoodsNames.length() > 0) {
            productGoodsNames = productGoodsNames.substring(0, goodsNames.length() - 1);
        }

        OrderBroadcastDto orderBroadcastDto = new OrderBroadcastDto().setGoodsName(goodsNames).setGoodsEntityList(santifyGoodsList).setProdcutGoodsName(productGoodsNames);
        orderBroadcastDto.setBroadcast(false);
        if (santifyGoodsList.size() > 0) {
            orderBroadcastDto.setBroadcast(true);
        }
        log.info("组装订单报单中商品名称 业绩生效 orerSn={}, 生效了吗：{}，商品名称{}", order.getOrderSn(), order.isJustEffect(), goodsNames);
        bizContext.setAttribute(OrderBroadcastDto.class, orderBroadcastDto);
    }
}

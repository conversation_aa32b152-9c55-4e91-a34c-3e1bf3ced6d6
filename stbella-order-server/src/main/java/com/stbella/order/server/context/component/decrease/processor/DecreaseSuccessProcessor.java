package com.stbella.order.server.context.component.decrease.processor;

import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.server.order.month.req.PayReqV2;
import com.stbella.platform.order.api.cashier.CashierCommandService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;

import static com.stbella.order.server.order.month.enums.PayAmountEnum.FINAL_PAYMENT;

/**
 * 减免成功更新折扣率
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "订单减免组件")
public class DecreaseSuccessProcessor implements IExecutableAtom<FlowContext> {


    @Resource
    private CashierCommandService cashierCommandService;


    @Override
    public void run(FlowContext bizContext) {
        //更新折扣信息
        HeOrderEntity heOrderEntity = bizContext.getAttribute(HeOrderEntity.class);
        OrderReductionEntity orderReductionEntity = bizContext.getAttribute(OrderReductionEntity.class);

        //减免成功搞成一次支付成功的行为
        PayReqV2 payReqV2 = new PayReqV2();
        payReqV2.setPayAmount(AmountChangeUtil.changeF2Y(orderReductionEntity.getDecreaseAmount()));
        payReqV2.setOrderId(heOrderEntity.getOrderId());
        payReqV2.setPayType(OmniPayTypeEnum.REDUCTION.getCode());
        payReqV2.setOrderNo(heOrderEntity.getOrderSn());
        payReqV2.setAmountType(FINAL_PAYMENT.getCode());
        payReqV2.setAccountType(0);
        payReqV2.setStoreId(heOrderEntity.getStoreId());

        cashierCommandService.onlinePay(payReqV2);


    }

}

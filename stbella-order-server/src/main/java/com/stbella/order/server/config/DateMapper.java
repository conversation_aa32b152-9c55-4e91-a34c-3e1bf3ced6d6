package com.stbella.order.server.config;

import cn.hutool.core.date.DatePattern;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

@Component
public class DateMapper {

    public String asString(Date date) {
        return date != null ? new SimpleDateFormat(DatePattern.NORM_DATETIME_PATTERN)
                .format(date) : null;
    }

    public Date asDate(String date) {
        try {
            return date != null ? new SimpleDateFormat(DatePattern.NORM_DATETIME_PATTERN)
                    .parse(date) : null;
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    public Date asDate(Long date) {
        try {
            return new Date(date);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
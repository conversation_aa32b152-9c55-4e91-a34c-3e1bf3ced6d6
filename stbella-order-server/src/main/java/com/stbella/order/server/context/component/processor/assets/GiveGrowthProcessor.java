package com.stbella.order.server.context.component.processor.assets;

import cn.hutool.json.JSONUtil;
import com.stbella.core.base.Operator;
import com.stbella.customer.server.customer.request.growth.CustomerAssetsFlowReq;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.client.CustomerGrowthClient;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.StoreRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import top.primecare.snowball.flow.annotation.SnowballComponent;
import top.primecare.snowball.flow.core.context.FlowContext;
import top.primecare.snowball.flow.core.definition.IExecutableAtom;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * @Author: JJ
 * @Description: 增加成长值
 */
@Component
@Slf4j
@RefreshScope
@SnowballComponent(name = "增加成长值")
public class GiveGrowthProcessor implements IExecutableAtom<FlowContext> {

    @Resource
    private CustomerGrowthClient customerGrowthClient;
    @Resource
    StoreRepository storeRepository;


    @Override
    public void run(FlowContext bizContext) {
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        HeIncomeRecordEntity incomeRecord = bizContext.getAttribute(HeIncomeRecordEntity.class);
        CfgStoreEntity store = storeRepository.queryCfgStoreById(orderEntity.getStoreId());
        log.info("订单支付信息orderEntity:{}", JSONUtil.toJsonStr(orderEntity));
        CustomerAssetsFlowReq flowReq = new CustomerAssetsFlowReq();
        flowReq.setBasicId(orderEntity.getBasicUid().longValue());
        flowReq.setBrandType(store.getType());
        //这里现在固定了。
        flowReq.setFlowSource(100);
        flowReq.setFlowSourceBizId(incomeRecord.getOrderId().longValue());
        flowReq.setFlowSourceBizNo(incomeRecord.getId() + "");
        Operator system = Operator.system();
        flowReq.setUpdateBy(0L);
        flowReq.setUpdateByName(system.getOperatorName());
        if (Objects.isNull(orderEntity.getFxRate())) {
            orderEntity.setFxRate(BigDecimal.ONE);
        }
        BigDecimal income = orderEntity.getFxRate().multiply(new BigDecimal(incomeRecord.getIncome()));
        flowReq.setAssetsVal(Objects.requireNonNull(AmountChangeUtil.changeF2Y(income)).longValue());
        customerGrowthClient.addGrowth(flowReq);
    }

    @Override
    public boolean condition(FlowContext bizContext) {
        HeIncomeRecordEntity incomeRecord = bizContext.getAttribute(HeIncomeRecordEntity.class);
        HeOrderEntity orderEntity = bizContext.getAttribute(HeOrderEntity.class);
        return !OmniPayTypeEnum.PRODUCTION_COIN.getCode().equals(incomeRecord.getPayType()) && !OmniOrderTypeEnum.getHomeOrderTypeCodeList().contains(orderEntity.getOrderType());
    }
}


<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.contract.mapper.OrderContractConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.contract.entity.OrderContractConfigPO">
        <result column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="company_name" property="companyName"/>
        <result column="status" property="status"/>
        <result column="app_id" property="appId"/>
        <result column="app_secret" property="appSecret"/>
        <result column="e_sign_host" property="eSignHost"/>
        <result column="company_account_id" property="companyAccountId"/>
        <result column="primary_callback_url" property="primaryCallbackUrl"/>
        <result column="track_callback_url" property="trackCallbackUrl"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        gmt_create,
        deleted,
        gmt_modified,
        company_name, status, app_id, app_secret, e_sign_host, company_account_id, primary_callback_url, track_callback_url, create_by, update_by
    </sql>

</mapper>

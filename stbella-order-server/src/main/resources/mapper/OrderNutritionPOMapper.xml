<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.nutrition.mapper.OrderNutritionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.order.nutrition.entity.OrderNutritionPO">
        <result column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="deleted" property="deleted"/>
        <result column="order_no" property="orderNo"/>
        <result column="src_tids" property="srcTids"/>
        <result column="cs_remark" property="csRemark"/>
        <result column="buyer_message" property="buyerMessage"/>
        <result column="out_order_no" property="outOrderNo"/>
        <result column="order_user_id" property="orderUserId"/>
        <result column="order_user_phone" property="orderUserPhone"/>
        <result column="commodity_name" property="commodityName"/>
        <result column="order_amount" property="orderAmount"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="order_source" property="orderSource"/>
        <result column="order_time" property="orderTime"/>
        <result column="income" property="income"/>
        <result column="renew" property="renew"/>
        <result column="channel_source" property="channelSource"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        gmt_create,
        gmt_modified,
        deleted,
        order_no, src_tids, cs_remark, buyer_message, out_order_no, order_user_id, order_user_phone, commodity_name, order_amount, pay_amount, order_source, order_time, income, renew, channel_source
    </sql>

    <select id="getPerformanceObjectives" resultType="java.math.BigDecimal">
        SELECT
        IFNULL(SUM( pay_amount ),0)
        FROM
        order_nutrition
        WHERE
        deleted = 0
        <if test="orderSource != null">
            AND order_source IN
            <foreach collection="orderSource" item="source" separator="," open="(" close=")">
                #{source}
            </foreach>
        </if>
        AND `order_nutrition`.order_time &gt;= #{orderStart}
        AND `order_nutrition`.order_time &lt; #{orderEnd}
    </select>

    <select id="getNutritionOrderUniverseByDTOList"
            resultType="com.stbella.order.server.order.nutrition.dto.NutritionOrderUniverseByDTO">
        SELECT
        order_user_phone AS orderUserPhone,
        count(*) AS num,
        MIN( order_time ) AS firstOrderTime
        FROM
        order_nutrition
        WHERE
        deleted = 0
        <if test="phoneList != null">
            AND order_user_phone IN
            <foreach collection="phoneList" item="phone" separator="," open="(" close=")">
                #{phone}
            </foreach>
        </if>
        GROUP BY
        order_user_phone;
    </select>

</mapper>

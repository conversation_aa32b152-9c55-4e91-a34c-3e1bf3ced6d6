<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.nutrition.mapper.NutritionExtensionCostMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.order.nutrition.entity.NutritionExtensionCostPO">
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="relation_id" property="relationId"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="day" property="day"/>
        <result column="cost" property="cost"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        deleted,
        gmt_create,
        gmt_modified,
        relation_id, year, month, day, cost
    </sql>
    <select id="getExtensionList">
        select
        nec.*,
        ner.extension_type_id as extensionId,
        ner.extension_type_name as channelName,
        ner.type as storeType
        from nutrition_extension_relation ner
        left join nutrition_extension_cost nec on nec.relation_id = ner.id
        where nec.deleted = 0
        and ner.deleted = 0
        <if test="request.year != null  ">
            and nec.year = #{request.year}
        </if>
        <if test="request.month != null  ">
            and nec.month = #{request.month}
        </if>
        <if test="request.storeType != null  ">
            and ner.type = #{request.storeType}
        </if>

    </select>

</mapper>

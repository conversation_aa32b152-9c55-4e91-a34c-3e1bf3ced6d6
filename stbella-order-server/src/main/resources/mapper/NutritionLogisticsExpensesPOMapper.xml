<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.nutrition.mapper.NutritionLogisticsExpensesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.order.nutrition.entity.NutritionLogisticsExpensesPO">
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="year" property="year"/>
        <result column="january" property="january"/>
        <result column="february" property="february"/>
        <result column="march" property="march"/>
        <result column="april" property="april"/>
        <result column="may" property="may"/>
        <result column="june" property="june"/>
        <result column="july" property="july"/>
        <result column="august" property="august"/>
        <result column="september" property="september"/>
        <result column="october" property="october"/>
        <result column="november" property="november"/>
        <result column="december" property="december"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        deleted,
        gmt_create,
        gmt_modified,
        year, january, february, march, april, may, june, july, august, september, october, november, december
    </sql>

</mapper>

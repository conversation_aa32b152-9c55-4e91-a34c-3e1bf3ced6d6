<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.cts.mapper.OrderCtsApplyRefundMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.stbella.order.server.order.cts.entity.OrderCtsApplyRefundPO">
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="pay_record_id" property="payRecordId"/>
        <result column="order_no" property="orderNo"/>
        <result column="order_type" property="orderType"/>
        <result column="local_transactional_no" property="localTransactionalNo"/>
        <result column="transactional_no" property="transactionalNo"/>
        <result column="pay_type" property="payType"/>
        <result column="amount_type" property="amountType"/>
        <result column="pay_time" property="payTime"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="refund_status" property="refundStatus"/>
        <result column="refund_remark" property="refundRemark"/>
        <result column="refund_failed_reason" property="refundFailedReason"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_name" property="createByName"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_by_name" property="updateByName"/>
        <result column="audit_name" property="auditName"/>
        <result column="audit_time" property="auditTime"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="refund_option" property="refundOption"/>
        <result column="payee_account" property="payeeAccount"/>
        <result column="payee_bank" property="payeeBank"/>
        <result column="payee_name" property="payeeName"/>
        <result column="ding_approve_status" property="dingApproveStatus"/>

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        deleted,
        gmt_create,
        gmt_modified,
        audit_name,
        audit_time,
        process_instance_id,
        refund_option,
        payee_account,
        payee_bank,
        payee_name,
        ding_approve_status,
        pay_record_id, order_no, order_type, local_transactional_no, transactional_no, pay_type, amount_type, pay_time, pay_amount, refund_amount, refund_status, refund_remark,refund_failed_reason,  create_by, create_by_name, update_by, update_by_name
    </sql>

</mapper>

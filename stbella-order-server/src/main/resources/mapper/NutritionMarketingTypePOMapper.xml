<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.nutrition.mapper.NutritionMarketingTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.order.nutrition.entity.NutritionMarketingTypePO">
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="name" property="name"/>
        <result column="enable" property="enable"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        deleted,
        gmt_create,
        gmt_modified,
        name,
        enable
    </sql>

    <select id="selectAllNutritionMarketingType"
            resultType="com.stbella.order.server.order.nutrition.response.NutritionMarketingTypeVO">
        SELECT
        id,
        name AS channelName,
        gmt_create AS gmtCreate
        FROM
        nutrition_marketing_type
        WHERE
        deleted = 0
        <if test="name != null">
            AND name LIKE CONCAT('%',#{name},'%')
        </if>
        ORDER BY
        id DESC
    </select>

</mapper>

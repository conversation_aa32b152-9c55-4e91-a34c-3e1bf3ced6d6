<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.cts.mapper.OrderCtsApplyPayMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.order.cts.entity.OrderCtsApplyPayPO">
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="pay_record_id" property="payRecordId"/>
        <result column="order_no" property="orderNo"/>
        <result column="order_type" property="orderType"/>
        <result column="local_transactional_no" property="localTransactionalNo"/>
        <result column="transactional_no" property="transactionalNo"/>
        <result column="pay_type" property="payType"/>
        <result column="amount_type" property="amountType"/>
        <result column="pay_proof" property="payProof"/>
        <result column="sell_remark" property="sellRemark"/>
        <result column="pay_time" property="payTime"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="actually_pay_amount" property="actuallyPayAmount"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="account_time" property="accountTime"/>
        <result column="audit_time" property="auditTime"/>
        <result column="audit_proof" property="auditProof"/>
        <result column="audit_user_id" property="auditUserId"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_name" property="createByName"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_by_name" property="updateByName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        deleted,
        gmt_create,
        gmt_modified,
        pay_record_id, order_no, order_type, local_transactional_no, transactional_no, pay_type, amount_type, pay_proof, sell_remark, pay_time, pay_amount, actually_pay_amount, audit_status, account_time, audit_time, audit_proof, audit_user_id, create_by, create_by_name, update_by, update_by_name
    </sql>

</mapper>

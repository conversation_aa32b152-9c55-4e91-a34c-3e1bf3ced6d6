<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.nutrition.mapper.WdtSuiteMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.order.nutrition.entity.WdtSuitePO">
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="suite_id" property="suiteId"/>
        <result column="suite_no" property="suiteNo"/>
        <result column="barcode" property="barcode"/>
        <result column="suite_name" property="suiteName"/>
        <result column="short_name" property="shortName"/>
        <result column="retail_price" property="retailPrice"/>
        <result column="wholesale_price" property="wholesalePrice"/>
        <result column="member_price" property="memberPrice"/>
        <result column="market_price" property="marketPrice"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        deleted,
        gmt_create,
        gmt_modified,
        suite_id, suite_no, barcode, suite_name, short_name, retail_price, wholesale_price, member_price, market_price
    </sql>

</mapper>

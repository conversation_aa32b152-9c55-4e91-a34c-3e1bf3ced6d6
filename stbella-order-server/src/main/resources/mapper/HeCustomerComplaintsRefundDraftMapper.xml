<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeCustomerComplaintsRefundDraftMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeCustomerComplaintsRefundDraft">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="complaints_id" property="complaintsId"/>
        <result column="refund_info" property="refundInfo"/>
        <result column="offline_remaittance_info" property="offlineRemittanceInfo"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , order_id, complaints_id, refund_info, offline_remaittance_info, deleted, gmt_create, gmt_modified
    </sql>

</mapper>

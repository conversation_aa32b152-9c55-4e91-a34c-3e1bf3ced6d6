<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.nutrition.mapper.OrderNutritionGrossMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.order.nutrition.entity.OrderNutritionGrossPO">
        <result column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="deleted" property="deleted"/>
        <result column="type" property="type"/>
        <result column="year" property="year"/>
        <result column="mouth" property="mouth"/>
        <result column="first_buy" property="firstBuy"/>
        <result column="this_re_purchase" property="thisRePurchase"/>
        <result column="two_re_purchase" property="twoRePurchase"/>
        <result column="three_re_purchase" property="threeRePurchase"/>
        <result column="four_re_purchase" property="fourRePurchase"/>
        <result column="five_re_purchase" property="fiveRePurchase"/>
        <result column="six_re_purchase" property="sixRePurchase"/>
        <result column="seven_re_purchase" property="sevenRePurchase"/>
        <result column="eight_re_purchase" property="eightRePurchase"/>
        <result column="nine_re_purchase" property="nineRePurchase"/>
        <result column="ten_re_purchase" property="tenRePurchase"/>
        <result column="eleven_re_purchase" property="elevenRePurchase"/>
        <result column="twelve_re_purchase" property="twelveRePurchase"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        gmt_create,
        gmt_modified,
        deleted,
        type, year, mouth, first_buy, this_re_purchase, two_re_purchase, three_re_purchase, four_re_purchase, five_re_purchase, six_re_purchase, seven_re_purchase, eight_re_purchase, nine_re_purchase, ten_re_purchase, eleven_re_purchase, twelve_re_purchase
    </sql>

</mapper>

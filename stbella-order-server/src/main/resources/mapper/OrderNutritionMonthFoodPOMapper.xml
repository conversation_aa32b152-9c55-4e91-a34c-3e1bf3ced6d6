<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.nutrition.mapper.OrderNutritionMonthFoodMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.order.nutrition.entity.OrderNutritionMonthFoodPO">
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="order_no" property="orderNo"/>
        <result column="order_type" property="orderType"/>
        <result column="combo_id" property="comboId"/>
        <result column="combo_num" property="comboNum"/>
        <result column="channel_id" property="channelId"/>
        <result column="freight_price" property="freightPrice"/>
        <result column="order_time" property="orderTime"/>
        <result column="first_pay_time" property="firstPayTime"/>
        <result column="presentation_time" property="presentationTime"/>
        <result column="finish_time" property="finishTime"/>
        <result column="custom_id" property="customId"/>
        <result column="custom_name" property="customName"/>
        <result column="custom_phone" property="customPhone"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="payable_amount" property="payableAmount"/>
        <result column="discount_minus" property="discountMinus"/>
        <result column="activity_minus" property="activityMinus"/>
        <result column="coupon_minus" property="couponMinus"/>
        <result column="total_minus" property="totalMinus"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="refunded_amount" property="refundedAmount"/>
        <result column="freeze_amount" property="freezeAmount"/>
        <result column="reality_amount" property="realityAmount"/>
        <result column="status" property="status"/>
        <result column="sell_id" property="sellId"/>
        <result column="sell_name" property="sellName"/>
        <result column="plan_send_food_time" property="planSendFoodTime"/>
        <result column="send_food_address_province" property="sendFoodAddressProvince"/>
        <result column="send_food_address_city" property="sendFoodAddressCity"/>
        <result column="send_food_address_area" property="sendFoodAddressArea"/>
        <result column="send_food_address" property="sendFoodAddress"/>
        <result column="send_food_address_customer_name" property="sendFoodAddressCustomerName"/>
        <result column="send_food_address_customer_phone" property="sendFoodAddressCustomerPhone"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_name" property="createByName"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_by_name" property="updateByName"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        deleted,
        gmt_create,
        gmt_modified,
        order_no, order_type, combo_id, combo_num, channel_id, freight_price, order_time, first_pay_time, presentation_time, finish_time, custom_id, custom_name, custom_phone, total_amount, payable_amount, discount_minus, activity_minus, coupon_minus, total_minus, pay_amount, refunded_amount, freeze_amount, reality_amount, status, sell_id, sell_name, plan_send_food_time, send_food_address_province, send_food_address_city, send_food_address_area, send_food_address, send_food_address_customer_name, send_food_address_customer_phone, create_by, create_by_name, update_by, update_by_name, remark
    </sql>

    <select id="getExportList" resultType="com.stbella.order.server.order.nutrition.dto.NutritionExportDTO">
        SELECT order_nutrition_month_food.order_no,
        order_nutrition_month_food.order_time,
        order_nutrition_month_food.order_type,
        order_nutrition_month_food.custom_name,
        order_nutrition_month_food.custom_phone,
        order_nutrition_month_food.`status`,
        order_nutrition_month_food_snapshoot.combo_name,
        order_nutrition_month_food.combo_num,
        order_nutrition_month_food.payable_amount,
        order_nutrition_month_food.freight_price,
        order_nutrition_month_food.reality_amount,
        (order_nutrition_month_food.payable_amount - order_nutrition_month_food.reality_amount) AS unPayAmount,
        order_nutrition_month_food.refunded_amount,
        order_nutrition_month_food.plan_send_food_time,
        CONCAT(order_nutrition_month_food.send_food_address_province,
        order_nutrition_month_food.send_food_address_city,
        order_nutrition_month_food.send_food_address_area,
        order_nutrition_month_food.send_food_address) AS send_food_address
        FROM order_nutrition_month_food
        LEFT JOIN order_nutrition_month_food_snapshoot
        ON order_nutrition_month_food.order_no = order_nutrition_month_food_snapshoot.order_no
        where order_nutrition_month_food.deleted = 0
        <if test="managerOrderListRequest.orderNo != null">AND order_nutrition_month_food.order_no =
            #{managerOrderListRequest.orderNo}
        </if>
        <if test="managerOrderListRequest.customerName != null">AND order_nutrition_month_food.custom_name LIKE
            CONCAT('%',#{managerOrderListRequest.customerName},'%')
        </if>
        <if test="managerOrderListRequest.customerPhone != null">AND order_nutrition_month_food.custom_phone LIKE
            CONCAT('%',#{managerOrderListRequest.customerPhone},'%')
        </if>
        <if test="managerOrderListRequest.comboName != null">AND order_nutrition_month_food_snapshoot.combo_name LIKE
            CONCAT('%',#{managerOrderListRequest.comboName},'%')
        </if>
        <if test="managerOrderListRequest.orderStatus != null">AND order_nutrition_month_food.`status` =
            #{managerOrderListRequest.orderStatus}
        </if>
        <if test="managerOrderListRequest.orderTimeStart != null">AND order_nutrition_month_food.order_time &gt;=
            #{managerOrderListRequest.orderTimeStart}
        </if>
        <if test="managerOrderListRequest.orderTimeEnd != null">AND order_nutrition_month_food.order_time &lt;=
            #{managerOrderListRequest.orderTimeEnd}
        </if>
        <if test="managerOrderListRequest.saleName != null">AND order_nutrition_month_food.sell_name =
            #{managerOrderListRequest.saleName}
        </if>
        ORDER BY order_nutrition_month_food.order_time DESC
    </select>

</mapper>

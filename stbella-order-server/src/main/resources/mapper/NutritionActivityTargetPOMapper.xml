<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.nutrition.mapper.NutritionActivityTargetMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.order.nutrition.entity.NutritionActivityTargetPO">
        <id column="id" property="id" />
    <result column="deleted" property="deleted" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
        <result column="activity_id" property="activityId" />
        <result column="type" property="type" />
        <result column="base_amount" property="baseAmount" />
        <result column="amount" property="amount" />
        <result column="enable" property="enable" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        deleted,
        gmt_create,
        gmt_modified,
        id, activity_id, type, base_amount, amount, enable
    </sql>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.contract.mapper.UserESignMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.contract.entity.UserESignPO">
        <result column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="name" property="name"/>
        <result column="phone" property="phone"/>
        <result column="cer_type" property="cerType"/>
        <result column="cer_no" property="cerNo"/>
        <result column="status" property="status"/>
        <result column="e_sign_user_id" property="eSignUserId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        gmt_create,
        deleted,
        gmt_modified,
        create_by, update_by, name, phone, cer_type, cer_no, status, e_sign_user_id
    </sql>

</mapper>

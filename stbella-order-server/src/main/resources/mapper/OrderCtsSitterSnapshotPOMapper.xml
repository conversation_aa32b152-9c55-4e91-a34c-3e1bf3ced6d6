<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.mapper.OrderCtsSitterSnapshotMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.order.cts.entity.OrderCtsSitterSnapshotPO">
        <result column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="deleted" property="deleted"/>
        <result column="order_no" property="orderNo"/>
        <result column="order_type" property="orderType"/>
        <result column="product_id" property="productId"/>
        <result column="combo_id" property="comboId"/>
        <result column="sitter_id" property="sitterId"/>
        <result column="cts_site_id" property="ctsSiteId"/>
        <result column="sitter_name" property="sitterName"/>
        <result column="mobile" property="mobile"/>
        <result column="sitter_level" property="sitterLevel"/>
        <result column="sitter_wage" property="sitterWage"/>
        <result column="aunt_level" property="auntLevel"/>
        <result column="aunt_wage" property="auntWage"/>
        <result column="reality_serve_start" property="realityServeStart"/>
        <result column="reality_serve_end" property="realityServeEnd"/>
        <result column="non_holidays" property="nonHolidays"/>
        <result column="double_holidays" property="doubleHolidays"/>
        <result column="triple_holidays" property="tripleHolidays"/>
        <result column="bonus" property="bonus"/>
        <result column="salary" property="salary"/>
        <result column="match_date" property="matchDate"/>
        <result column="termination_service_date" property="terminationServiceDate"/>
        <result column="is_termination" property="isTermination"/>
        <result column="content" property="content"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_name" property="createByName"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_by_name" property="updateByName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        gmt_create,
        gmt_modified,
        deleted,
        order_no, order_type, product_id, combo_id, sitter_id, cts_site_id, sitter_name, mobile, sitter_level, sitter_wage, aunt_level, aunt_wage, reality_serve_start, reality_serve_end, non_holidays, double_holidays, triple_holidays, bonus, salary, match_date, termination_service_date, is_termination, content, create_by, create_by_name, update_by, update_by_name
    </sql>

</mapper>

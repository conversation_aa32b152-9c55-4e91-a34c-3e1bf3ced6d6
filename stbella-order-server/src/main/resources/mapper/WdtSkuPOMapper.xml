<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.nutrition.mapper.WdtSkuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.order.nutrition.entity.WdtSkuPO">
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="spec_id" property="specId"/>
        <result column="spec_no" property="specNo"/>
        <result column="spec_name" property="specName"/>
        <result column="spec_url" property="specUrl"/>
        <result column="goods_id" property="goodsId"/>
        <result column="lowest_price" property="lowestPrice"/>
        <result column="retail_price" property="retailPrice"/>
        <result column="wholesale_price" property="wholesalePrice"/>
        <result column="member_price" property="memberPrice"/>
        <result column="market_price" property="marketPrice"/>
        <result column="cost_price" property="costPrice"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        deleted,
        gmt_create,
        gmt_modified,
        spec_id, spec_no, spec_name, spec_url, goods_id, lowest_price, retail_price, wholesale_price, member_price, market_price, cost_price
    </sql>

</mapper>

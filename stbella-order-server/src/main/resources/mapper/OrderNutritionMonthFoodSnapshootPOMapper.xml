<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.nutrition.mapper.OrderNutritionMonthFoodSnapshootMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.stbella.order.server.order.nutrition.entity.OrderNutritionMonthFoodSnapshootPO">
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="order_no" property="orderNo"/>
        <result column="combo_id" property="comboId"/>
        <result column="combo_name" property="comboName"/>
        <result column="channel_id" property="channelId"/>
        <result column="channel_type" property="channelType"/>
        <result column="channel_name" property="channelName"/>
        <result column="channel_price" property="channelPrice"/>
        <result column="combo_days" property="comboDays"/>
        <result column="combo_status" property="comboStatus"/>
        <result column="standard_price" property="standardPrice"/>
        <result column="combo_code" property="comboCode"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_name" property="createByName"/>
        <result column="channel_remark" property="channelRemark"/>
        <result column="combo_remark" property="comboRemark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        deleted,
        gmt_create,
        gmt_modified,
        order_no, combo_id, combo_name, channel_id, channel_type, channel_name, channel_price, combo_days, combo_status, standard_price, combo_code, create_by, create_by_name, channel_remark, combo_remark
    </sql>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.cts.mapper.OrderCtsPaySignRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.order.cts.entity.OrderCtsPaySignRecordPO">
        <result column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="deleted" property="deleted"/>
        <result column="pay_sign" property="paySign"/>
        <result column="local_transactional_no" property="localTransactionalNo"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        gmt_create,
        gmt_modified,
        deleted,
        pay_sign, local_transactional_no
    </sql>

</mapper>

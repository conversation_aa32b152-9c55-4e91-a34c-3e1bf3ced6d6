<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.nutrition.mapper.WdtSuiteSkuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.order.nutrition.entity.WdtSuiteSkuPO">
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="suite_id" property="suiteId"/>
        <result column="spec_no" property="specNo"/>
        <result column="spec_name" property="specName"/>
        <result column="barcode" property="barcode"/>
        <result column="spec_code" property="specCode"/>
        <result column="goods_name" property="goodsName"/>
        <result column="goods_no" property="goodsNo"/>
        <result column="num" property="num"/>
        <result column="rec_id" property="recId"/>
        <result column="spec_id" property="specId"/>
        <result column="unit" property="unit"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        deleted,
        gmt_create,
        gmt_modified,
        suite_id, spec_no, spec_name, barcode, spec_code, goods_name, goods_no, num, rec_id, spec_id, unit
    </sql>

</mapper>

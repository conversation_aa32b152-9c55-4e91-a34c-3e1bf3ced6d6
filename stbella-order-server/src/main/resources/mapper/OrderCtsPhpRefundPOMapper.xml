<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.cts.mapper.OrderCtsPhpRefundMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.order.cts.entity.OrderCtsPhpRefundPO">
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="cts_site_id" property="ctsSiteId"/>
        <result column="cts_site_name" property="ctsSiteName"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="refund_gmt" property="refundGmt"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_name" property="createByName"/>
        <result column="valid" property="valid"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        deleted,
        gmt_create,
        gmt_modified,
        valid,
        cts_site_id, cts_site_name, refund_amount, refund_gmt,create_by, create_by_name
    </sql>

</mapper>

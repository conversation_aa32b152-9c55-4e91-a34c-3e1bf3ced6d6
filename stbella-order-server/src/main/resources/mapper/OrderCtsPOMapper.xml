<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.cts.mapper.OrderCtsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.order.cts.entity.OrderCtsPO">
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="order_no" property="orderNo"/>
        <result column="order_type" property="orderType"/>
        <result column="product_id" property="productId"/>
        <result column="combo_id" property="comboId"/>
        <result column="order_time" property="orderTime"/>
        <result column="first_pay_time" property="firstPayTime"/>
        <result column="first_pay_amount" property="firstPayAmount"/>
        <result column="half_pay_time" property="halfPayTime"/>
        <result column="finish_time" property="finishTime"/>
        <result column="custom_id" property="customId"/>
        <result column="sitter_id" property="sitterId"/>
        <result column="order_custom_certification_id" property="orderCustomCertificationId"/>
        <result column="live_province_address_id" property="liveProvinceAddressId"/>
        <result column="live_city_address_id" property="liveCityAddressId"/>
        <result column="live_area_address_id" property="liveAreaAddressId"/>
        <result column="live_address" property="liveAddress"/>
        <result column="expect_serve_start_date" property="expectServeStartDate"/>
        <result column="expect_serve_end_date" property="expectServeEndDate"/>
        <result column="serve_province_address_id" property="serveProvinceAddressId"/>
        <result column="serve_city_address_id" property="serveCityAddressId"/>
        <result column="serve_area_address_id" property="serveAreaAddressId"/>
        <result column="serve_address" property="serveAddress"/>
        <result column="serve_type" property="serveType"/>
        <result column="serve_type_remark" property="serveTypeRemark"/>
        <result column="is_cer" property="isCer"/>
        <result column="baby_num" property="babyNum"/>
        <result column="special_require" property="specialRequire"/>
        <result column="payable_amount" property="payableAmount"/>
        <result column="daily_payable_amount" property="dailyPayableAmount"/>
        <result column="discount" property="discount"/>
        <result column="refunded_amount" property="refundedAmount"/>
        <result column="freeze_amount" property="freezeAmount"/>
        <result column="reality_amount" property="realityAmount"/>
        <result column="status" property="status"/>
        <result column="state_machine_state" property="stateMachineState"/>
        <result column="sell_id" property="sellId"/>
        <result column="sell_name" property="sellName"/>
        <result column="cts_site_id" property="ctsSiteId"/>
        <result column="is_sign_protocol" property="isSignProtocol"/>
        <result column="is_sign_contract" property="isSignContract"/>
        <result column="remark" property="remark"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_name" property="createByName"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_by_name" property="updateByName"/>
        <result column="ext" property="ext"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        deleted,
        gmt_create,
        gmt_modified,
        order_no, order_type, product_id, combo_id, order_time, first_pay_time, first_pay_amount, half_pay_time, finish_time, custom_id, sitter_id, order_custom_certification_id, live_province_address_id, live_city_address_id, live_area_address_id, live_address, expect_serve_start_date, expect_serve_end_date, serve_province_address_id, serve_city_address_id, serve_area_address_id, serve_address, serve_type, serve_type_remark, baby_num, is_cer, special_require, payable_amount, daily_payable_amount, discount, refunded_amount, freeze_amount, reality_amount, status, state_machine_state, sell_id, sell_name, cts_site_id, is_sign_protocol, is_sign_contract, remark, create_by, create_by_name, update_by, update_by_name
            ,ext
    </sql>

    <select id="queryCustomerListByOrderNos" resultType="com.stbella.order.server.order.cts.dto.CustomerOrderDTO">
        SELECT
        us.NAME AS customerName,
        us.phone AS mobile,
        oc.cts_site_id AS ctsSiteId,
        oc.order_no AS orderNo
        FROM
        order_cts AS oc
        LEFT JOIN user_e_sign AS us ON us.id = oc.order_custom_certification_id
        WHERE
        oc.deleted = 0
        AND us.deleted = 0
        <if test="query != null and query.size() > 0 ">
            AND oc.order_no IN
            <foreach collection="query" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>

        ORDER BY
        oc.gmt_create DESC

    </select>


    <select id="queryMarginCustomerListByOrderNos" resultType="com.stbella.order.server.order.cts.dto.CustomerOrderDTO">
        SELECT
        ocss.sitter_name AS customerName,
        ocss.mobile AS mobile,
        oc.cts_site_id AS ctsSiteId,
        oc.order_no AS orderNo
        FROM
        order_cts AS oc
        LEFT JOIN order_cts_sitter_snapshot AS ocss ON ocss.order_no = oc.order_no
        WHERE
        oc.deleted = 0
        AND ocss.deleted = 0
        <if test="query != null and query.size() > 0 ">
            AND oc.order_no IN
            <foreach collection="query" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>

        ORDER BY
        oc.gmt_create DESC

    </select>

    <select id="queryAllOrderIds" resultType="com.stbella.order.server.order.cts.excel.AllCtsOrderIdsExcel">
        SELECT a.id, a.order_no, a.order_type, a.sitter_id, sis.sitter_name as sitterName , payable_amount,reality_amount, a.cts_site_id,a.status,site_name
             , custom_id, u.customer_name
        from order_cts a
                 LEFT JOIN order_cts_sitter_snapshot sis on a.sitter_id = sis.sitter_id
                 LEFT JOIN `stbella-store`.cts_site s ON a.cts_site_id = s.id
                 LEFT JOIN `stbella-customer`.customer_info_cts u ON a.custom_id = u.id
        where
            a.deleted = 0

    </select>

</mapper>

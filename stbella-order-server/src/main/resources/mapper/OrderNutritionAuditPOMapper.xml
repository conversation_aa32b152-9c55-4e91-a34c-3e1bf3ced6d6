<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.nutrition.mapper.OrderNutritionAuditMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.order.nutrition.entity.OrderNutritionAuditPO">
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="apply_id" property="applyId"/>
        <result column="order_no" property="orderNo"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="reject_reason" property="rejectReason"/>
        <result column="audit_by" property="auditBy"/>
        <result column="audit_name" property="auditName"/>
        <result column="audit_time" property="auditTime"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_name" property="createByName"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        deleted,
        gmt_create,
        apply_id, order_no, audit_status, reject_reason, create_by, create_by_name, remark, audit_by, audit_name, audit_time
    </sql>

</mapper>

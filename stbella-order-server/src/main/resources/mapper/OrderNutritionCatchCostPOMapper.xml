<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.nutrition.mapper.OrderNutritionCatchCostMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.order.nutrition.entity.OrderNutritionCatchCostPO">
        <id column=" id" property=" id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="deleted" property="deleted"/>
        <result column="type" property="type"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="expenses" property="expenses"/>
        <result column="first_buyer_num" property="firstBuyerNum"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        gmt_create
        ,
        gmt_modified,
        deleted,
         id, type, year, month, expenses, first_buyer_num
    </sql>

</mapper>

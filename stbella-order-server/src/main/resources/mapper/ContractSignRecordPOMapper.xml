<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.contract.mapper.ContractSignRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.contract.entity.ContractSignRecordPO">
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="contract_no" property="contractNo"/>
        <result column="order_id" property="orderId"/>
        <result column="template_id" property="templateId"/>
        <result column="customer_id" property="customerId"/>
        <result column="staff_id" property="staffId"/>
        <result column="status" property="status"/>
        <result column="param" property="param"/>
        <result column="e_sign_address" property="eSignAddress"/>
        <result column="s_sign_flow_id" property="sSignFlowId"/>
        <result column="e_sign_file_id" property="eSignFileId"/>
        <result column="e_sign_file_ame" property="eSignFileAme"/>
        <result column="e_sign_long_url" property="eSignLongUrl"/>
        <result column="e_sign_short_url" property="eSignShortUrl"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        gmt_create
        ,
        gmt_modified,
        id,
        create_by, update_by, contract_no, order_id, template_id, customer_id, staff_id, status, param, e_sign_address, s_sign_flow_id, e_sign_file_id, e_sign_file_ame, e_sign_long_url, e_sign_short_url
    </sql>

</mapper>

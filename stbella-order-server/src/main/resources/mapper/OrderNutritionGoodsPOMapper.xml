<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.nutrition.mapper.OrderNutritionGoodsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.order.nutrition.entity.OrderNutritionGoodsPO">
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="out_order_no" property="outOrderNo"/>
        <result column="order_time" property="orderTime"/>
        <result column="suite" property="suite"/>
        <result column="goods_code" property="goodsCode"/>
        <result column="quantity" property="quantity"/>
        <result column="retail_price" property="retailPrice"/>
        <result column="cost_price" property="costPrice"/>
        <result column="commodity_tax_rate" property="commodityTaxRate"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        deleted,
        gmt_create,
        gmt_modified,
        out_order_no, order_time, suite, goods_code, quantity, retail_price, cost_price, commodity_tax_rate
    </sql>

</mapper>

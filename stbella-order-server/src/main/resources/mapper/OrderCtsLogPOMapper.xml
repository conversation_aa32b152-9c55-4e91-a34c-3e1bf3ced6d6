<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.cts.mapper.OrderCtsLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.order.cts.entity.OrderCtsLogPO">
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="order_no" property="orderNo"/>
        <result column="method" property="method"/>
        <result column="type" property="type"/>
        <result column="param" property="param"/>
        <result column="exception" property="exception"/>
        <result column="success" property="success"/>
        <result column="order_content" property="orderContent"/>
        <result column="apply_refund_content" property="applyRefundContent"/>
        <result column="pay_record_content" property="payRecordContent"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        deleted,
        gmt_create,
        gmt_modified,
        order_no, method, type, param, exception, success, order_content, apply_refund_content, pay_record_content, create_by, update_by
    </sql>

</mapper>

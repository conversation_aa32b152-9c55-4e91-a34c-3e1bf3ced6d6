<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.nutrition.mapper.NutritionActivityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.order.nutrition.entity.NutritionActivityPO">
        <id column="id" property="id" />
    <result column="deleted" property="deleted" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
        <result column="activity_name" property="activityName" />
        <result column="remark" property="remark" />
        <result column="enable" property="enable" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        deleted,
        gmt_create,
        gmt_modified,
        id, activity_name, remark, enable
    </sql>

</mapper>

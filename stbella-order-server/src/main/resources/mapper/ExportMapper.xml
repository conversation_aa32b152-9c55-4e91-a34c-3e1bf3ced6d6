<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.cts.mapper.ExportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="ctsPayResultMap" type="com.stbella.order.server.order.cts.temp.po.PayDTO">
        <result column="订单编号" property="orderNumber" />
        <result column="订单类型" property="orderType" />
        <result column="销售名字" property="sellerName" />
        <result column="雇主名字" property="employerName" />
        <result column="育婴师名字" property="nannyName" />
        <result column="门店名称" property="storeName" />
        <result column="订单总金额" property="totalAmount" />
        <result column="创建时间" property="createTime" />
        <result column="支付金额" property="payAmount" />
        <result column="支付时间" property="payTime" />
        <result column="订单首次支付时间" property="firstPayTime" />
        <result column="审核状态" property="auditStatus" />
    </resultMap>
    <resultMap id="ctsRefundResultMap" type="com.stbella.order.server.order.cts.temp.po.RefundDTO">
        <result column="订单编号" property="orderNumber" />
        <result column="订单类型" property="orderType" />
        <result column="区域" property="region" />
        <result column="销售名字" property="sellerName" />
        <result column="雇主名字" property="employerName" />
        <result column="育婴师名字" property="nannyName" />
        <result column="实际退款时间" property="refundTime" />
        <result column="退款金额" property="refundAmount" />
        <result column="退款方式" property="refundMethod" />
        <result column="属性" property="attribute" />
        <result column="订单首次支付时间" property="firstPaymentTime" />
    </resultMap>
    <resultMap id="ctsComboResultMap" type="com.stbella.order.server.order.cts.temp.po.ComboDTO">
        <result column="订单编号" property="orderNumber" />
        <result column="订单类型" property="orderType" />
        <result column="分站名称" property="substationName" />
        <result column="销售名称" property="sellerName" />
        <result column="雇主名称" property="employerName" />
        <result column="套餐等级" property="packageLevel" />
        <result column="签约时长（天）" property="contractDuration" />
        <result column="上户育婴师" property="nannyName" />
        <result column="育婴师工资" property="nannySalary" />
        <result column="订单总金额" property="totalAmount" />
        <result column="折扣" property="discount" />
        <result column="已付金额" property="paidAmount" />
        <result column="创建时间" property="createTime" />
        <result column="首次支付时间" property="firstPaymentTime" />
        <result column="属性" property="attribute" />
        <result column="来源渠道" property="sourceChannel" />
        <result column="门店编号" property="storeNumber" />
        <result column="门店名称" property="storeName" />
        <result column="合同开始时间" property="contractBeginTime" />
        <result column="合同结束时间" property="contractEndTime" />
    </resultMap>
    <resultMap id="ctsTrainPayResultMap" type="com.stbella.order.server.order.cts.temp.po.TrainPayDTO">
        <result column="订单编号" property="orderNumber" />
        <result column="订单类型" property="orderType" />
        <result column="销售名字" property="sellerName" />
        <result column="雇主名字" property="employerName"/>
        <result column="育婴师名字" property="nannyName"/>
        <result column="门店名称" property="storeName"/>
        <result column="订单总金额" property="totalAmount"/>
        <result column="创建时间" property="createTime"/>
        <result column="支付金额" property="paymentAmount"/>
        <result column="支付时间" property="paymentTime"/>
        <result column="订单首次支付时间" property="firstPaymentTime"/>
        <result column="审核状态" property="auditStatus"/>
    </resultMap>
    <resultMap id="ctsPaymentFlowMap" type="com.stbella.order.server.order.cts.temp.po.ExportPaymentFlowDTO">
        <result column="订单编号" property="orderId"/>
        <result column="到家系统流水号" property="djjSerialNumber"/>
        <result column="支付宝微信线下汇款流水号" property="paymentSerialNumber"/>
        <result column="订单类型" property="orderType"/>
        <result column="支退金额" property="refundAmount"/>
        <result column="收款退款类型" property="paymentRefundType"/>
        <result column="支付类型" property="paymentMethod"/>
        <result column="微信支付宝账号" property="paymentAccount"/>
        <result column="财务审核状态" property="financialCheckStatus"/>
        <result column="到账时间" property="paymentTime"/>
        <result column="分站" property="substation"/>
        <result column="套餐名称" property="packageType"/>
        <result column="客户姓名" property="clientName"/>
        <result column="客户手机号" property="clientPhone"/>
        <result column="归属销售" property="salesPerson"/>
        <result column="订单创建时间" property="orderCreateTime"/>
        <result column="合同金额" property="contractAmount"/>
        <result column="育婴师工资" property="nannySalary"/>
        <result column="服务费" property="serviceFee"/>
        <result column="是否续签" property="isRenewal"/>
    </resultMap>


    <select id="ctsPay" resultMap="ctsPayResultMap">
        select abc.订单编号,
               abc.订单类型,
               abc.归属销售 as '销售名字', case abc.订单类型 when '雇主母婴订单' then abc.客户姓名
            when '雇主育婴师订单' then abc.客户姓名
        when '雇主通用订单' then abc.客户姓名
        end as '雇主名字',
        case abc.订单类型 when '育婴师培训课程订单' then abc.客户姓名
        when '育婴师平台管理费订单' then abc.客户姓名
        when '育婴师上户保证金订单' then abc.客户姓名
        when '育婴师通用订单' then abc.客户姓名
        end as '育婴师名字',
        abc.分站 as '门店名称',
        abc.合同金额 as '订单总金额',
        abc.订单创建时间 as '创建时间',
        abc.支退金额 as '支付金额',
        abc.到账时间 as '支付时间',
        abc.订单首次支付时间 AS '订单首次支付时间',
        abc.财务审核状态 as '审核状态'


        from (

        SELECT *
        FROM (
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 15 THEN
        '雇主通用订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '/'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        hgp.product_name           AS 套餐名称,
        cic.customer_name          AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-customer`.customer_info_cts AS cic ON cic.id = oc.custom_id
        LEFT JOIN `stbella-base`.home_general_product AS hgp ON hgp.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_status = 1
        AND opr.pay_type IN (1, 2)
        AND oc.order_type IN (15)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 15 THEN
        '雇主通用订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '已审核'
        WHEN 2 THEN
        '审核拒绝'
        WHEN 3 THEN
        '待审核'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        hgp.product_name           AS 套餐名称,
        cic.customer_name          AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-customer`.customer_info_cts AS cic ON cic.id = oc.custom_id
        LEFT JOIN `stbella-base`.home_general_product AS hgp ON hgp.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_type IN (4, 5, 6)
        AND oc.order_type IN (15)
        UNION ALL
        SELECT opr.order_no                                                              AS 订单编号,
        opr.local_transactional_no                                                AS 到家系统流水号,
        opr.transactional_no                                                      AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 9 THEN
        '育婴师培训课程订单'
        END                                                                   AS 订单类型,
        opr.pay_amount                                                            AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                                                                   AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                                                                   AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021001169668110【集团账号】'
        WHEN 2 THEN
        '1582271471【集团账号】'
        END                                                                   AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '/'
        END                                                                   AS 财务审核状态,
        opr.pay_time                                                              AS 到账时间,
        cs.site_name                                                              AS 分站,
        hsp.course_name                                                           AS 名称,
        ues.NAME                                                                  AS 客户姓名,
        oc.sell_name                                                              AS 归属销售,
        oc.gmt_create                                                             AS 订单创建时间,
        oc.payable_amount                                                         AS 合同金额,
        bs.sitter_wage                                                            AS 育婴师工资,
        IF
        (bs.sitter_wage &gt;= 10000, bs.sitter_wage * 0.3, bs.sitter_wage * 0.2) AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-order`.user_e_sign AS ues ON ues.id = oc.order_custom_certification_id
        LEFT JOIN `stbella-base`.home_sitter_product AS hsp ON hsp.id = oc.product_id
        LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_status = 1
        AND opr.pay_type IN (1, 2)
        AND oc.order_type IN (9)
        UNION ALL
        SELECT opr.order_no                                                              AS 订单编号,
        opr.local_transactional_no                                                AS 到家系统流水号,
        opr.transactional_no                                                      AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 9 THEN
        '育婴师培训课程订单'
        END                                                                   AS 订单类型,
        opr.pay_amount                                                            AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                                                                   AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                                                                   AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021001169668110【集团账号】'
        WHEN 2 THEN
        '1582271471【集团账号】'
        END                                                                   AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '已审核'
        WHEN 2 THEN
        '审核拒绝'
        WHEN 3 THEN
        '待审核'
        END                                                                   AS 财务审核状态,
        opr.pay_time                                                              AS 到账时间,
        cs.site_name                                                              AS 分站,
        hsp.course_name                                                           AS 名称,
        ues.NAME                                                                  AS 客户姓名,
        oc.sell_name                                                              AS 归属销售,
        oc.gmt_create                                                             AS 订单创建时间,
        oc.payable_amount                                                         AS 合同金额,
        bs.sitter_wage                                                            AS 育婴师工资,
        IF
        (bs.sitter_wage &gt;= 10000, bs.sitter_wage * 0.3, bs.sitter_wage * 0.2) AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-order`.user_e_sign AS ues ON ues.id = oc.order_custom_certification_id
        LEFT JOIN `stbella-base`.home_sitter_product AS hsp ON hsp.id = oc.product_id
        LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_type IN (4, 5, 6)
        AND oc.order_type IN (9)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 10 THEN
        '育婴师平台管理费订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '/'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        CASE
        hssf.deleted
        WHEN 0 THEN
        '育婴师平台管理费'
        END                    AS 商品名称,
        ues.NAME                   AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-order`.user_e_sign AS ues ON ues.id = oc.order_custom_certification_id
        LEFT JOIN `stbella-base`.home_sitter_serve_fee AS hssf ON hssf.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_status = 1
        AND opr.pay_type IN (1, 2)
        AND oc.order_type IN (10)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 10 THEN
        '育婴师平台管理费订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '已审核'
        WHEN 2 THEN
        '审核拒绝'
        WHEN 3 THEN
        '待审核'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        CASE
        hssf.deleted
        WHEN 0 THEN
        '育婴师平台管理费'
        END                    AS 商品名称,
        ues.NAME                   AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-order`.user_e_sign AS ues ON ues.id = oc.order_custom_certification_id
        LEFT JOIN `stbella-base`.home_sitter_serve_fee AS hssf ON hssf.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_type IN (4, 5, 6)
        AND oc.order_type IN (10)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 11 THEN
        '育婴师上户保证金订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '/'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        CASE
        hsm.deleted
        WHEN 0 THEN
        '育婴师上户保证金'
        END                    AS 商品名称,
        bs.NAME                    AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
        LEFT JOIN `stbella-base`.home_sitter_margin AS hsm ON hsm.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_status = 1
        AND opr.pay_type IN (1, 2)
        AND oc.order_type IN (11)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 11 THEN
        '育婴师上户保证金订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '已审核'
        WHEN 2 THEN
        '审核拒绝'
        WHEN 3 THEN
        '待审核'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        CASE
        hsm.deleted
        WHEN 0 THEN
        '育婴师上户保证金'
        END                    AS 商品名称,
        bs.NAME                    AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
        LEFT JOIN `stbella-base`.home_sitter_margin AS hsm ON hsm.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_type IN (4, 5, 6)
        AND oc.order_type IN (11)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 14 THEN
        '育婴师通用订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '/'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        hgp.product_name           AS 套餐名称,
        bs.NAME                    AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
        LEFT JOIN `stbella-base`.home_general_product AS hgp ON hgp.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_status = 1
        AND opr.pay_type IN (1, 2)
        AND oc.order_type IN (14)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 14 THEN
        '育婴师通用订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '已审核'
        WHEN 2 THEN
        '审核拒绝'
        WHEN 3 THEN
        '待审核'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        hgp.product_name           AS 套餐名称,
        bs.NAME                    AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
        LEFT JOIN `stbella-base`.home_general_product AS hgp ON hgp.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_type IN (4, 5, 6)
        AND oc.order_type IN (14)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 12 THEN
        '雇主母婴订单'
        WHEN 13 THEN
        '雇主育婴师订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '已审核'
        WHEN 2 THEN
        '审核拒绝'
        WHEN 3 THEN
        '待审核'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        hcp.combo_name             AS 套餐名称,
        cic.customer_name          AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-customer`.customer_info_cts AS cic ON cic.id = oc.custom_id
        LEFT JOIN `stbella-base`.home_customer_product AS hcp ON hcp.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_type IN (4, 5, 6)
        AND oc.order_type IN (12, 13)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 12 THEN
        '雇主母婴订单'
        WHEN 13 THEN
        '雇主育婴师订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '/'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        hcp.combo_name             AS 套餐名称,
        cic.customer_name          AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-customer`.customer_info_cts AS cic ON cic.id = oc.custom_id
        LEFT JOIN `stbella-base`.home_customer_product AS hcp ON hcp.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_status = 1
        AND opr.pay_type IN (1, 2)
        AND oc.order_type IN (12, 13)
        ) d


        ) abc
        where abc.收款退款类型 = '支付'
    </select>
    <select id="ctsRefund" resultMap="ctsRefundResultMap">
        select
        abc.订单编号,
        abc.订单类型,
        abc.分站 as '区域',
        abc.归属销售 as '销售名字',
        case abc.订单类型 when '雇主母婴订单' then abc.客户姓名
        when '雇主育婴师订单' then abc.客户姓名
        when '雇主通用订单' then abc.客户姓名
        end as '雇主名字',
        case abc.订单类型 when '育婴师培训课程订单' then abc.客户姓名
        when '育婴师平台管理费订单' then abc.客户姓名
        when '育婴师上户保证金订单' then abc.客户姓名
        when '育婴师通用订单' then abc.客户姓名
        end as '育婴师名字',
        abc.到账时间 as '实际退款时间',
        abc.支退金额 as '退款金额',

        case abc.退款方式 when 1 then '原路退回'
        when 2 then '原路退回'
        when 3 then '原路退回'
        when 4 then '手动打款'
        when 5 then '手动打款'
        when 6 then '手动打款'
        end as 退款方式,
        case abc.属性 when 0 then '新签'
        when 1 then '续签'
        end as 属性,
        abc.订单首次支付时间

        from (

        SELECT *
        FROM (
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 15 THEN
        '雇主通用订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '/'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        hgp.product_name           AS 套餐名称,
        cic.customer_name          AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,


        opr.pay_type AS 退款方式,
        oc.first_pay_time AS 订单首次支付时间,
        oc.renew_tag AS 属性

        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-customer`.customer_info_cts AS cic ON cic.id = oc.custom_id
        LEFT JOIN `stbella-base`.home_general_product AS hgp ON hgp.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_status = 1
        AND opr.pay_type IN (1, 2)
        AND oc.order_type IN (15)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 15 THEN
        '雇主通用订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '已审核'
        WHEN 2 THEN
        '审核拒绝'
        WHEN 3 THEN
        '待审核'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        hgp.product_name           AS 套餐名称,
        cic.customer_name          AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        opr.pay_type AS 退款方式,
        oc.first_pay_time AS 订单首次支付时间,
        oc.renew_tag AS 属性
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-customer`.customer_info_cts AS cic ON cic.id = oc.custom_id
        LEFT JOIN `stbella-base`.home_general_product AS hgp ON hgp.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_type IN (4, 5, 6)
        AND oc.order_type IN (15)
        UNION ALL
        SELECT opr.order_no                                                              AS 订单编号,
        opr.local_transactional_no                                                AS 到家系统流水号,
        opr.transactional_no                                                      AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 9 THEN
        '育婴师培训课程订单'
        END                                                                   AS 订单类型,
        opr.pay_amount                                                            AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                                                                   AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                                                                   AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021001169668110【集团账号】'
        WHEN 2 THEN
        '1582271471【集团账号】'
        END                                                                   AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '/'
        END                                                                   AS 财务审核状态,
        opr.pay_time                                                              AS 到账时间,
        cs.site_name                                                              AS 分站,
        hsp.course_name                                                           AS 名称,
        ues.NAME                                                                  AS 客户姓名,
        oc.sell_name                                                              AS 归属销售,
        oc.gmt_create                                                             AS 订单创建时间,
        oc.payable_amount                                                         AS 合同金额,
        bs.sitter_wage                                                            AS 育婴师工资,
        IF
        (bs.sitter_wage &gt;= 10000, bs.sitter_wage * 0.3, bs.sitter_wage * 0.2) AS 服务费,
        opr.pay_type AS 退款方式,
        oc.first_pay_time AS 订单首次支付时间,
        oc.renew_tag AS 属性
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-order`.user_e_sign AS ues ON ues.id = oc.order_custom_certification_id
        LEFT JOIN `stbella-base`.home_sitter_product AS hsp ON hsp.id = oc.product_id
        LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_status = 1
        AND opr.pay_type IN (1, 2)
        AND oc.order_type IN (9)
        UNION ALL
        SELECT opr.order_no                                                              AS 订单编号,
        opr.local_transactional_no                                                AS 到家系统流水号,
        opr.transactional_no                                                      AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 9 THEN
        '育婴师培训课程订单'
        END                                                                   AS 订单类型,
        opr.pay_amount                                                            AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                                                                   AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                                                                   AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021001169668110【集团账号】'
        WHEN 2 THEN
        '1582271471【集团账号】'
        END                                                                   AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '已审核'
        WHEN 2 THEN
        '审核拒绝'
        WHEN 3 THEN
        '待审核'
        END                                                                   AS 财务审核状态,
        opr.pay_time                                                              AS 到账时间,
        cs.site_name                                                              AS 分站,
        hsp.course_name                                                           AS 名称,
        ues.NAME                                                                  AS 客户姓名,
        oc.sell_name                                                              AS 归属销售,
        oc.gmt_create                                                             AS 订单创建时间,
        oc.payable_amount                                                         AS 合同金额,
        bs.sitter_wage                                                            AS 育婴师工资,
        IF
        (bs.sitter_wage &gt;= 10000, bs.sitter_wage * 0.3, bs.sitter_wage * 0.2) AS 服务费,
        opr.pay_type AS 退款方式,
        oc.first_pay_time AS 订单首次支付时间,
        oc.renew_tag AS 属性
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-order`.user_e_sign AS ues ON ues.id = oc.order_custom_certification_id
        LEFT JOIN `stbella-base`.home_sitter_product AS hsp ON hsp.id = oc.product_id
        LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_type IN (4, 5, 6)
        AND oc.order_type IN (9)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 10 THEN
        '育婴师平台管理费订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '/'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        CASE
        hssf.deleted
        WHEN 0 THEN
        '育婴师平台管理费'
        END                    AS 商品名称,
        ues.NAME                   AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        opr.pay_type AS 退款方式,
        oc.first_pay_time AS 订单首次支付时间,
        oc.renew_tag AS 属性
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-order`.user_e_sign AS ues ON ues.id = oc.order_custom_certification_id
        LEFT JOIN `stbella-base`.home_sitter_serve_fee AS hssf ON hssf.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_status = 1
        AND opr.pay_type IN (1, 2)
        AND oc.order_type IN (10)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 10 THEN
        '育婴师平台管理费订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '已审核'
        WHEN 2 THEN
        '审核拒绝'
        WHEN 3 THEN
        '待审核'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        CASE
        hssf.deleted
        WHEN 0 THEN
        '育婴师平台管理费'
        END                    AS 商品名称,
        ues.NAME                   AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        opr.pay_type AS 退款方式,
        oc.first_pay_time AS 订单首次支付时间,
        oc.renew_tag AS 属性
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-order`.user_e_sign AS ues ON ues.id = oc.order_custom_certification_id
        LEFT JOIN `stbella-base`.home_sitter_serve_fee AS hssf ON hssf.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_type IN (4, 5, 6)
        AND oc.order_type IN (10)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 11 THEN
        '育婴师上户保证金订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '/'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        CASE
        hsm.deleted
        WHEN 0 THEN
        '育婴师上户保证金'
        END                    AS 商品名称,
        bs.NAME                    AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        opr.pay_type AS 退款方式,
        oc.first_pay_time AS 订单首次支付时间,
        oc.renew_tag AS 属性
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
        LEFT JOIN `stbella-base`.home_sitter_margin AS hsm ON hsm.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_status = 1
        AND opr.pay_type IN (1, 2)
        AND oc.order_type IN (11)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 11 THEN
        '育婴师上户保证金订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '已审核'
        WHEN 2 THEN
        '审核拒绝'
        WHEN 3 THEN
        '待审核'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        CASE
        hsm.deleted
        WHEN 0 THEN
        '育婴师上户保证金'
        END                    AS 商品名称,
        bs.NAME                    AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        opr.pay_type AS 退款方式,
        oc.first_pay_time AS 订单首次支付时间,
        oc.renew_tag AS 属性
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
        LEFT JOIN `stbella-base`.home_sitter_margin AS hsm ON hsm.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_type IN (4, 5, 6)
        AND oc.order_type IN (11)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 14 THEN
        '育婴师通用订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '/'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        hgp.product_name           AS 套餐名称,
        bs.NAME                    AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        opr.pay_type AS 退款方式,
        oc.first_pay_time AS 订单首次支付时间,
        oc.renew_tag AS 属性
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
        LEFT JOIN `stbella-base`.home_general_product AS hgp ON hgp.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_status = 1
        AND opr.pay_type IN (1, 2)
        AND oc.order_type IN (14)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 14 THEN
        '育婴师通用订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '已审核'
        WHEN 2 THEN
        '审核拒绝'
        WHEN 3 THEN
        '待审核'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        hgp.product_name           AS 套餐名称,
        bs.NAME                    AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        opr.pay_type AS 退款方式,
        oc.first_pay_time AS 订单首次支付时间,
        oc.renew_tag AS 属性
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
        LEFT JOIN `stbella-base`.home_general_product AS hgp ON hgp.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_type IN (4, 5, 6)
        AND oc.order_type IN (14)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 12 THEN
        '雇主母婴订单'
        WHEN 13 THEN
        '雇主育婴师订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '已审核'
        WHEN 2 THEN
        '审核拒绝'
        WHEN 3 THEN
        '待审核'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        hcp.combo_name             AS 套餐名称,
        cic.customer_name          AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        opr.pay_type AS 退款方式,
        oc.first_pay_time AS 订单首次支付时间,
        oc.renew_tag AS 属性
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-customer`.customer_info_cts AS cic ON cic.id = oc.custom_id
        LEFT JOIN `stbella-base`.home_customer_product AS hcp ON hcp.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_type IN (4, 5, 6)
        AND oc.order_type IN (12, 13)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 12 THEN
        '雇主母婴订单'
        WHEN 13 THEN
        '雇主育婴师订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '/'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        hcp.combo_name             AS 套餐名称,
        cic.customer_name          AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        opr.pay_type AS 退款方式,
        oc.first_pay_time AS 订单首次支付时间,
        oc.renew_tag AS 属性
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-customer`.customer_info_cts AS cic ON cic.id = oc.custom_id
        LEFT JOIN `stbella-base`.home_customer_product AS hcp ON hcp.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_status = 1
        AND opr.pay_type IN (1, 2)
        AND oc.order_type IN (12, 13)
        ) d


        ) abc
        where abc.收款退款类型 = '退款'
    </select>
    <select id="ctsCombo" resultMap="ctsComboResultMap">
        SELECT o.order_no AS '订单编号', CASE
            o.order_type
            WHEN '12' THEN
                '雇主母婴订单'
            WHEN '15' THEN
                '雇主通用订单'
            WHEN '13' THEN
                '雇主育婴师订单'
            WHEN '14' THEN
                '育婴师通用订单'
            END AS '订单类型', site_name AS '分站名称', sell_name AS '销售名称', customer_name AS '雇主名称', CASE
            WHEN o.order_type = 12
                AND combo_level = 0 THEN
                '初级'
            WHEN o.order_type = 12
                AND combo_level = 1 THEN
                '中级'
            WHEN o.order_type = 12
                AND combo_level = 2 THEN
                '高级'
            WHEN o.order_type = 12
                AND combo_level = 3 THEN
                '精品'
            WHEN o.order_type = 12
                AND combo_level = 4 THEN
                '月子会所'
            WHEN o.order_type = 13
                AND combo_level = 0 THEN
                '优选'
            WHEN o.order_type = 13
                AND combo_level = 1 THEN
                '精选'
            WHEN o.order_type = 13
                AND combo_level = 2 THEN
                '特护'
            WHEN o.order_type = 13
                AND combo_level = 3 THEN
                '尊享'
            WHEN o.order_type = 13
                AND combo_level = 4 THEN
                '月子会所'
            WHEN o.order_type = 13
                AND combo_level = 5 THEN
                '书育'
            WHEN o.order_type = 13
                AND combo_level = 6 THEN
                '书育'
            WHEN o.order_type = 13
                AND combo_level = 7 THEN
                '礼育'
            WHEN o.order_type = 13
                AND combo_level = 8 THEN
                '御育'
            WHEN o.order_type = 13
                AND combo_level = 9 THEN
                '养成'
            END AS '套餐等级', combo_days * number AS '签约时长（天）', ss.sitter_name AS '上户育婴师', CASE
            o.order_type
            WHEN '12' THEN
                aunt_wage
            WHEN '15' THEN
                0
            WHEN '13' THEN
                sitter_wage
            END AS '育婴师工资', payable_amount AS '订单总金额', discount AS '折扣', reality_amount AS '已付金额', o.gmt_create AS '创建时间', first_pay_time AS '首次支付时间', CASE
            renew_tag
            WHEN '0' THEN
                '新签'
            WHEN '1' THEN
                '续签'
            END AS '属性', CONCAT( CASE channel
                                       WHEN '1' THEN '月子会所'
                                       WHEN '2' THEN '熟客转介绍'
                                       WHEN '3'
                                           THEN '400热线' END, '/', store_name ) AS '来源渠道', store.store_id AS '门店编号', store_name AS '门店名称', expect_serve_start_date AS '合同开始时间', expect_serve_end_date AS '合同结束时间'
        FROM `stbella-order`.order_cts o
                 LEFT JOIN `stbella-base`.home_customer_product p ON o.product_id = p.id
                 LEFT JOIN `stbella-store`.cts_site s ON o.cts_site_id = s.id
                 LEFT JOIN `stbella-customer`.customer_info_cts u ON o.custom_id = u.id
                 LEFT JOIN `stbella-order`.order_cts_sitter_snapshot ss ON ss.order_no = o.order_no
                 LEFT JOIN `ecp`.cfg_store store ON store.store_id = u.store_id
        WHERE o.order_type IN (12, 13, 15)
          AND o.deleted = 0
          AND o.order_no IN (SELECT order_no
                             FROM order_pay_record opr
                             WHERE deleted = 0
                               AND record_type = 1
                               AND opr.pay_status IN (1, 3)
                               AND opr.pay_time &gt;= #{start}
                               AND opr.pay_time &lt; #{end})
        ORDER BY o.order_type;
    </select>
    <select id="ctsTrainPay" resultMap="ctsTrainPayResultMap">
        select
        abc.订单编号,
        abc.订单类型,
        abc.归属销售 as '销售名字',
        case abc.订单类型 when '雇主母婴订单' then abc.客户姓名
        when '雇主育婴师订单' then abc.客户姓名
        when '雇主通用订单' then abc.客户姓名
        end as '雇主名字',
        case abc.订单类型 when '育婴师培训课程订单' then abc.客户姓名
        when '育婴师平台管理费订单' then abc.客户姓名
        when '育婴师上户保证金订单' then abc.客户姓名
        when '育婴师通用订单' then abc.客户姓名
        end as '育婴师名字',
        abc.分站 as '门店名称',
        abc.合同金额 as '订单总金额',
        abc.订单创建时间 as '创建时间',
        abc.支退金额 as '支付金额',
        abc.到账时间 as '支付时间',
        abc.订单首次支付时间 AS '订单首次支付时间',
        abc.财务审核状态 as '审核状态'

        from (

        SELECT *
        FROM (
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 15 THEN
        '雇主通用订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '/'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        hgp.product_name           AS 套餐名称,
        cic.customer_name          AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-customer`.customer_info_cts AS cic ON cic.id = oc.custom_id
        LEFT JOIN `stbella-base`.home_general_product AS hgp ON hgp.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_status = 1
        AND opr.pay_type IN (1, 2)
        AND oc.order_type IN (15)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 15 THEN
        '雇主通用订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '已审核'
        WHEN 2 THEN
        '审核拒绝'
        WHEN 3 THEN
        '待审核'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        hgp.product_name           AS 套餐名称,
        cic.customer_name          AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-customer`.customer_info_cts AS cic ON cic.id = oc.custom_id
        LEFT JOIN `stbella-base`.home_general_product AS hgp ON hgp.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_type IN (4, 5, 6)
        AND oc.order_type IN (15)
        UNION ALL
        SELECT opr.order_no                                                              AS 订单编号,
        opr.local_transactional_no                                                AS 到家系统流水号,
        opr.transactional_no                                                      AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 9 THEN
        '育婴师培训课程订单'
        END                                                                   AS 订单类型,
        opr.pay_amount                                                            AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                                                                   AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                                                                   AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021001169668110【集团账号】'
        WHEN 2 THEN
        '1582271471【集团账号】'
        END                                                                   AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '/'
        END                                                                   AS 财务审核状态,
        opr.pay_time                                                              AS 到账时间,
        cs.site_name                                                              AS 分站,
        hsp.course_name                                                           AS 名称,
        ues.NAME                                                                  AS 客户姓名,
        oc.sell_name                                                              AS 归属销售,
        oc.gmt_create                                                             AS 订单创建时间,
        oc.payable_amount                                                         AS 合同金额,
        bs.sitter_wage                                                            AS 育婴师工资,
        IF
        (bs.sitter_wage &gt;= 10000, bs.sitter_wage * 0.3, bs.sitter_wage * 0.2) AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-order`.user_e_sign AS ues ON ues.id = oc.order_custom_certification_id
        LEFT JOIN `stbella-base`.home_sitter_product AS hsp ON hsp.id = oc.product_id
        LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_status = 1
        AND opr.pay_type IN (1, 2)
        AND oc.order_type IN (9)
        UNION ALL
        SELECT opr.order_no                                                              AS 订单编号,
        opr.local_transactional_no                                                AS 到家系统流水号,
        opr.transactional_no                                                      AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 9 THEN
        '育婴师培训课程订单'
        END                                                                   AS 订单类型,
        opr.pay_amount                                                            AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                                                                   AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                                                                   AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021001169668110【集团账号】'
        WHEN 2 THEN
        '1582271471【集团账号】'
        END                                                                   AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '已审核'
        WHEN 2 THEN
        '审核拒绝'
        WHEN 3 THEN
        '待审核'
        END                                                                   AS 财务审核状态,
        opr.pay_time                                                              AS 到账时间,
        cs.site_name                                                              AS 分站,
        hsp.course_name                                                           AS 名称,
        ues.NAME                                                                  AS 客户姓名,
        oc.sell_name                                                              AS 归属销售,
        oc.gmt_create                                                             AS 订单创建时间,
        oc.payable_amount                                                         AS 合同金额,
        bs.sitter_wage                                                            AS 育婴师工资,
        IF
        (bs.sitter_wage &gt;= 10000, bs.sitter_wage * 0.3, bs.sitter_wage * 0.2) AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-order`.user_e_sign AS ues ON ues.id = oc.order_custom_certification_id
        LEFT JOIN `stbella-base`.home_sitter_product AS hsp ON hsp.id = oc.product_id
        LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_type IN (4, 5, 6)
        AND oc.order_type IN (9)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 10 THEN
        '育婴师平台管理费订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '/'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        CASE
        hssf.deleted
        WHEN 0 THEN
        '育婴师平台管理费'
        END                    AS 商品名称,
        ues.NAME                   AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-order`.user_e_sign AS ues ON ues.id = oc.order_custom_certification_id
        LEFT JOIN `stbella-base`.home_sitter_serve_fee AS hssf ON hssf.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_status = 1
        AND opr.pay_type IN (1, 2)
        AND oc.order_type IN (10)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 10 THEN
        '育婴师平台管理费订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '已审核'
        WHEN 2 THEN
        '审核拒绝'
        WHEN 3 THEN
        '待审核'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        CASE
        hssf.deleted
        WHEN 0 THEN
        '育婴师平台管理费'
        END                    AS 商品名称,
        ues.NAME                   AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-order`.user_e_sign AS ues ON ues.id = oc.order_custom_certification_id
        LEFT JOIN `stbella-base`.home_sitter_serve_fee AS hssf ON hssf.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_type IN (4, 5, 6)
        AND oc.order_type IN (10)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 11 THEN
        '育婴师上户保证金订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '/'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        CASE
        hsm.deleted
        WHEN 0 THEN
        '育婴师上户保证金'
        END                    AS 商品名称,
        bs.NAME                    AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
        LEFT JOIN `stbella-base`.home_sitter_margin AS hsm ON hsm.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_status = 1
        AND opr.pay_type IN (1, 2)
        AND oc.order_type IN (11)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 11 THEN
        '育婴师上户保证金订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '已审核'
        WHEN 2 THEN
        '审核拒绝'
        WHEN 3 THEN
        '待审核'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        CASE
        hsm.deleted
        WHEN 0 THEN
        '育婴师上户保证金'
        END                    AS 商品名称,
        bs.NAME                    AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
        LEFT JOIN `stbella-base`.home_sitter_margin AS hsm ON hsm.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_type IN (4, 5, 6)
        AND oc.order_type IN (11)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 14 THEN
        '育婴师通用订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '/'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        hgp.product_name           AS 套餐名称,
        bs.NAME                    AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
        LEFT JOIN `stbella-base`.home_general_product AS hgp ON hgp.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_status = 1
        AND opr.pay_type IN (1, 2)
        AND oc.order_type IN (14)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 14 THEN
        '育婴师通用订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '已审核'
        WHEN 2 THEN
        '审核拒绝'
        WHEN 3 THEN
        '待审核'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        hgp.product_name           AS 套餐名称,
        bs.NAME                    AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
        LEFT JOIN `stbella-base`.home_general_product AS hgp ON hgp.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_type IN (4, 5, 6)
        AND oc.order_type IN (14)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 12 THEN
        '雇主母婴订单'
        WHEN 13 THEN
        '雇主育婴师订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '已审核'
        WHEN 2 THEN
        '审核拒绝'
        WHEN 3 THEN
        '待审核'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        hcp.combo_name             AS 套餐名称,
        cic.customer_name          AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-customer`.customer_info_cts AS cic ON cic.id = oc.custom_id
        LEFT JOIN `stbella-base`.home_customer_product AS hcp ON hcp.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
        AND opr.deleted = 0
        AND opr.pay_time &gt;= #{start}
        AND opr.pay_time &lt; #{end}
        AND opr.pay_type IN (4, 5, 6)
        AND oc.order_type IN (12, 13)
        UNION ALL
        SELECT opr.order_no               AS 订单编号,
        opr.local_transactional_no AS 到家系统流水号,
        opr.transactional_no       AS 支付宝微信线下汇款流水号,
        CASE
        oc.order_type
        WHEN 12 THEN
        '雇主母婴订单'
        WHEN 13 THEN
        '雇主育婴师订单'
        END                    AS 订单类型,
        opr.pay_amount             AS 支退金额,
        CASE
        opr.record_type
        WHEN 1 THEN
        '支付'
        WHEN 2 THEN
        '退款'
        END                    AS 收款退款类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '支付宝'
        WHEN 2 THEN
        '微信'
        WHEN 4 THEN
        '线下支付'
        WHEN 5 THEN
        '线下支付'
        WHEN 6 THEN
        '线下支付'
        END                    AS 支付类型,
        CASE
        opr.pay_type
        WHEN 1 THEN
        '2021003125675134【到家账号】'
        WHEN 2 THEN
        '1622653226【到家账号】'
        END                    AS 微信支付宝账号,
        CASE
        opr.pay_status
        WHEN 1 THEN
        '/'
        END                    AS 财务审核状态,
        opr.pay_time               AS 到账时间,
        cs.site_name               AS 分站,
        hcp.combo_name             AS 套餐名称,
        cic.customer_name          AS 客户姓名,
        oc.sell_name               AS 归属销售,
        oc.gmt_create              AS 订单创建时间,
        oc.payable_amount          AS 合同金额,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 育婴师工资,
        CASE
        oc.deleted
        WHEN 0 THEN
        '/'
        END                    AS 服务费,
        oc.first_pay_time AS 订单首次支付时间
        FROM `stbella-order`.order_pay_record AS opr
        LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
        LEFT JOIN `stbella-customer`.customer_info_cts AS cic ON cic.id = oc.custom_id
        LEFT JOIN `stbella-base`.home_customer_product AS hcp ON hcp.id = oc.product_id
        LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
        WHERE oc.deleted = 0
          AND opr.deleted = 0
          AND opr.pay_time &gt;= #{start}
          AND opr.pay_time &lt; #{end}
          AND opr.pay_status = 1
          AND opr.pay_type IN (1, 2)
          AND oc.order_type IN (12, 13)) d) abc
        where abc.收款退款类型 = '支付'
          and (abc.订单类型 = '育婴师培训课程订单' or abc.订单类型 = '育婴师平台管理费订单' or
               abc.订单类型 = '育婴师上户保证金订单' or abc.订单类型 = '育婴师通用订单')
    </select>
    <select id="ctsPaymentFlow" resultMap="ctsPaymentFlowMap">
        SELECT *
        FROM (SELECT opr.order_no               AS 订单编号,
                     opr.local_transactional_no AS 到家系统流水号,
                     opr.transactional_no       AS 支付宝微信线下汇款流水号,
                     CASE
                         oc.order_type
                         WHEN 15 THEN
                             '雇主通用订单'
                         END                    AS 订单类型,
                     opr.pay_amount             AS 支退金额,
                     CASE
                         opr.record_type
                         WHEN 1 THEN
                             '支付'
                         WHEN 2 THEN
                             '退款'
                         END                    AS 收款退款类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '支付宝'
                         WHEN 2 THEN
                             '微信'
                         WHEN 4 THEN
                             '线下支付'
                         WHEN 5 THEN
                             '线下支付'
                         WHEN 6 THEN
                             '线下支付'
                         END                    AS 支付类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '2021003125675134【到家账号】'
                         WHEN 2 THEN
                             '1622653226【到家账号】'
                         END                    AS 微信支付宝账号,
                     CASE
                         opr.pay_status
                         WHEN 1 THEN
                             '/'
                         END                    AS 财务审核状态,
                     opr.pay_time               AS 到账时间,
                     cs.site_name               AS 分站,
                     hgp.product_name           AS 套餐名称,
                     cic.customer_name          AS 客户姓名,
                     cic.phone_number           AS 客户手机号,
                     oc.sell_name               AS 归属销售,
                     oc.gmt_create              AS 订单创建时间,
                     oc.payable_amount          AS 合同金额,
                     CASE
                         oc.deleted
                         WHEN 0 THEN
                             '/'
                         END                    AS 育婴师工资,
                     CASE
                         oc.deleted
                         WHEN 0 THEN
                             '/'
                         END                    AS 服务费,
                     CASE
                         oc.renew_tag
                         WHEN 0 THEN
                             '否'
                         WHEN 1 THEN
                             '是'
                         END                    AS 是否续签
              FROM `stbella-order`.order_pay_record AS opr
                       LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
                       LEFT JOIN `stbella-customer`.customer_info_cts AS cic ON cic.id = oc.custom_id
                       LEFT JOIN `stbella-base`.home_general_product AS hgp ON hgp.id = oc.product_id
                       LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
              WHERE oc.deleted = 0
                AND opr.deleted = 0
                AND opr.pay_time &gt;= #{start}
                AND opr.pay_time &lt; #{end}
                AND opr.pay_status = 1
                AND opr.pay_type IN (1, 2)
                AND oc.order_type IN (15)
              UNION ALL
              SELECT opr.order_no               AS 订单编号,
                     opr.local_transactional_no AS 到家系统流水号,
                     opr.transactional_no       AS 支付宝微信线下汇款流水号,
                     CASE
                         oc.order_type
                         WHEN 15 THEN
                             '雇主通用订单'
                         END                    AS 订单类型,
                     opr.pay_amount             AS 支退金额,
                     CASE
                         opr.record_type
                         WHEN 1 THEN
                             '支付'
                         WHEN 2 THEN
                             '退款'
                         END                    AS 收款退款类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '支付宝'
                         WHEN 2 THEN
                             '微信'
                         WHEN 4 THEN
                             '线下支付'
                         WHEN 5 THEN
                             '线下支付'
                         WHEN 6 THEN
                             '线下支付'
                         END                    AS 支付类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '2021003125675134【到家账号】'
                         WHEN 2 THEN
                             '1622653226【到家账号】'
                         END                    AS 微信支付宝账号,
                     CASE
                         opr.pay_status
                         WHEN 1 THEN
                             '已审核'
                         WHEN 2 THEN
                             '审核拒绝'
                         WHEN 3 THEN
                             '待审核'
                         END                    AS 财务审核状态,
                     opr.pay_time               AS 到账时间,
                     cs.site_name               AS 分站,
                     hgp.product_name           AS 套餐名称,
                     cic.customer_name          AS 客户姓名,
                     cic.phone_number           AS 客户手机号,
                     oc.sell_name               AS 归属销售,
                     oc.gmt_create              AS 订单创建时间,
                     oc.payable_amount          AS 合同金额,
                     CASE
                         oc.deleted
                         WHEN 0 THEN
                             '/'
                         END                    AS 育婴师工资,
                     CASE
                         oc.deleted
                         WHEN 0 THEN
                             '/'
                         END                    AS 服务费,
                     CASE
                         oc.renew_tag
                         WHEN 0 THEN
                             '否'
                         WHEN 1 THEN
                             '是'
                         END                    AS 是否续签
              FROM `stbella-order`.order_pay_record AS opr
                       LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
                       LEFT JOIN `stbella-customer`.customer_info_cts AS cic ON cic.id = oc.custom_id
                       LEFT JOIN `stbella-base`.home_general_product AS hgp ON hgp.id = oc.product_id
                       LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
              WHERE oc.deleted = 0
                AND opr.deleted = 0
                AND opr.pay_time &gt;= #{start}
                AND opr.pay_time &lt; #{end}
                AND opr.pay_type IN (4, 5, 6)
                AND oc.order_type IN (15)
              UNION ALL
              SELECT opr.order_no                                                                 AS 订单编号,
                     opr.local_transactional_no                                                   AS 到家系统流水号,
                     opr.transactional_no                                                         AS 支付宝微信线下汇款流水号,
                     CASE
                         oc.order_type
                         WHEN 9 THEN
                             '育婴师培训课程订单'
                         END                                                                      AS 订单类型,
                     opr.pay_amount                                                               AS 支退金额,
                     CASE
                         opr.record_type
                         WHEN 1 THEN
                             '支付'
                         WHEN 2 THEN
                             '退款'
                         END                                                                      AS 收款退款类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '支付宝'
                         WHEN 2 THEN
                             '微信'
                         WHEN 4 THEN
                             '线下支付'
                         WHEN 5 THEN
                             '线下支付'
                         WHEN 6 THEN
                             '线下支付'
                         END                                                                      AS 支付类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '2021001169668110【集团账号】'
                         WHEN 2 THEN
                             '1582271471【集团账号】'
                         END                                                                      AS 微信支付宝账号,
                     CASE
                         opr.pay_status
                         WHEN 1 THEN
                             '/'
                         END                                                                      AS 财务审核状态,
                     opr.pay_time                                                                 AS 到账时间,
                     cs.site_name                                                                 AS 分站,
                     hsp.course_name                                                              AS 名称,
                     ues.NAME                                                                     AS 客户姓名,
                     ues.phone                                                                    AS 客户手机号,
                     oc.sell_name                                                                 AS 归属销售,
                     oc.gmt_create                                                                AS 订单创建时间,
                     oc.payable_amount                                                            AS 合同金额,
                     bs.sitter_wage                                                               AS 育婴师工资,
                     IF
                         (bs.sitter_wage &gt;= 10000, bs.sitter_wage * 0.3, bs.sitter_wage * 0.2) AS 服务费,
                     CASE
                         oc.renew_tag
                         WHEN 0 THEN
                             '否'
                         WHEN 1 THEN
                             '是'
                         END                                                                      AS 是否续签
              FROM `stbella-order`.order_pay_record AS opr
                       LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
                       LEFT JOIN `stbella-order`.user_e_sign AS ues ON ues.id = oc.order_custom_certification_id
                       LEFT JOIN `stbella-base`.home_sitter_product AS hsp ON hsp.id = oc.product_id
                       LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
                       LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
              WHERE oc.deleted = 0
                AND opr.deleted = 0
                AND opr.pay_time &gt;= #{start}
                AND opr.pay_time &lt; #{end}
                AND opr.pay_status = 1
                AND opr.pay_type IN (1, 2)
                AND oc.order_type IN (9)
              UNION ALL
              SELECT opr.order_no                                                                 AS 订单编号,
                     opr.local_transactional_no                                                   AS 到家系统流水号,
                     opr.transactional_no                                                         AS 支付宝微信线下汇款流水号,
                     CASE
                         oc.order_type
                         WHEN 9 THEN
                             '育婴师培训课程订单'
                         END                                                                      AS 订单类型,
                     opr.pay_amount                                                               AS 支退金额,
                     CASE
                         opr.record_type
                         WHEN 1 THEN
                             '支付'
                         WHEN 2 THEN
                             '退款'
                         END                                                                      AS 收款退款类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '支付宝'
                         WHEN 2 THEN
                             '微信'
                         WHEN 4 THEN
                             '线下支付'
                         WHEN 5 THEN
                             '线下支付'
                         WHEN 6 THEN
                             '线下支付'
                         END                                                                      AS 支付类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '2021001169668110【集团账号】'
                         WHEN 2 THEN
                             '1582271471【集团账号】'
                         END                                                                      AS 微信支付宝账号,
                     CASE
                         opr.pay_status
                         WHEN 1 THEN
                             '已审核'
                         WHEN 2 THEN
                             '审核拒绝'
                         WHEN 3 THEN
                             '待审核'
                         END                                                                      AS 财务审核状态,
                     opr.pay_time                                                                 AS 到账时间,
                     cs.site_name                                                                 AS 分站,
                     hsp.course_name                                                              AS 名称,
                     ues.NAME                                                                     AS 客户姓名,
                     ues.phone                                                                    AS 客户手机号,
                     oc.sell_name                                                                 AS 归属销售,
                     oc.gmt_create                                                                AS 订单创建时间,
                     oc.payable_amount                                                            AS 合同金额,
                     bs.sitter_wage                                                               AS 育婴师工资,
                     IF
                         (bs.sitter_wage &gt;= 10000, bs.sitter_wage * 0.3, bs.sitter_wage * 0.2) AS 服务费,
                     CASE
                         oc.renew_tag
                         WHEN 0 THEN
                             '否'
                         WHEN 1 THEN
                             '是'
                         END                                                                      AS 是否续签
              FROM `stbella-order`.order_pay_record AS opr
                       LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
                       LEFT JOIN `stbella-order`.user_e_sign AS ues ON ues.id = oc.order_custom_certification_id
                       LEFT JOIN `stbella-base`.home_sitter_product AS hsp ON hsp.id = oc.product_id
                       LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
                       LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
              WHERE oc.deleted = 0
                AND opr.deleted = 0
                AND opr.pay_time &gt;= #{start}
                AND opr.pay_time &lt; #{end}
                AND opr.pay_type IN (4, 5, 6)
                AND oc.order_type IN (9)
              UNION ALL
              SELECT opr.order_no               AS 订单编号,
                     opr.local_transactional_no AS 到家系统流水号,
                     opr.transactional_no       AS 支付宝微信线下汇款流水号,
                     CASE
                         oc.order_type
                         WHEN 10 THEN
                             '育婴师平台管理费订单'
                         END                    AS 订单类型,
                     opr.pay_amount             AS 支退金额,
                     CASE
                         opr.record_type
                         WHEN 1 THEN
                             '支付'
                         WHEN 2 THEN
                             '退款'
                         END                    AS 收款退款类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '支付宝'
                         WHEN 2 THEN
                             '微信'
                         WHEN 4 THEN
                             '线下支付'
                         WHEN 5 THEN
                             '线下支付'
                         WHEN 6 THEN
                             '线下支付'
                         END                    AS 支付类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '2021003125675134【到家账号】'
                         WHEN 2 THEN
                             '1622653226【到家账号】'
                         END                    AS 微信支付宝账号,
                     CASE
                         opr.pay_status
                         WHEN 1 THEN
                             '/'
                         END                    AS 财务审核状态,
                     opr.pay_time               AS 到账时间,
                     cs.site_name               AS 分站,
                     CASE
                         hssf.deleted
                         WHEN 0 THEN
                             '育婴师平台管理费'
                         END                    AS 商品名称,
                     ues.NAME                   AS 客户姓名,
                     ues.phone                  AS 客户手机号,
                     oc.sell_name               AS 归属销售,
                     oc.gmt_create              AS 订单创建时间,
                     oc.payable_amount          AS 合同金额,
                     CASE
                         oc.deleted
                         WHEN 0 THEN
                             '/'
                         END                    AS 育婴师工资,
                     CASE
                         oc.deleted
                         WHEN 0 THEN
                             '/'
                         END                    AS 服务费,
                     CASE
                         oc.renew_tag
                         WHEN 0 THEN
                             '否'
                         WHEN 1 THEN
                             '是'
                         END                    AS 是否续签
              FROM `stbella-order`.order_pay_record AS opr
                       LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
                       LEFT JOIN `stbella-order`.user_e_sign AS ues ON ues.id = oc.order_custom_certification_id
                       LEFT JOIN `stbella-base`.home_sitter_serve_fee AS hssf ON hssf.id = oc.product_id
                       LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
              WHERE oc.deleted = 0
                AND opr.deleted = 0
                AND opr.pay_time &gt;= #{start}
                AND opr.pay_time &lt; #{end}
                AND opr.pay_status = 1
                AND opr.pay_type IN (1, 2)
                AND oc.order_type IN (10)
              UNION ALL
              SELECT opr.order_no               AS 订单编号,
                     opr.local_transactional_no AS 到家系统流水号,
                     opr.transactional_no       AS 支付宝微信线下汇款流水号,
                     CASE
                         oc.order_type
                         WHEN 10 THEN
                             '育婴师平台管理费订单'
                         END                    AS 订单类型,
                     opr.pay_amount             AS 支退金额,
                     CASE
                         opr.record_type
                         WHEN 1 THEN
                             '支付'
                         WHEN 2 THEN
                             '退款'
                         END                    AS 收款退款类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '支付宝'
                         WHEN 2 THEN
                             '微信'
                         WHEN 4 THEN
                             '线下支付'
                         WHEN 5 THEN
                             '线下支付'
                         WHEN 6 THEN
                             '线下支付'
                         END                    AS 支付类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '2021003125675134【到家账号】'
                         WHEN 2 THEN
                             '1622653226【到家账号】'
                         END                    AS 微信支付宝账号,
                     CASE
                         opr.pay_status
                         WHEN 1 THEN
                             '已审核'
                         WHEN 2 THEN
                             '审核拒绝'
                         WHEN 3 THEN
                             '待审核'
                         END                    AS 财务审核状态,
                     opr.pay_time               AS 到账时间,
                     cs.site_name               AS 分站,
                     CASE
                         hssf.deleted
                         WHEN 0 THEN
                             '育婴师平台管理费'
                         END                    AS 商品名称,
                     ues.NAME                   AS 客户姓名,
                     ues.phone                  AS 客户手机号,
                     oc.sell_name               AS 归属销售,
                     oc.gmt_create              AS 订单创建时间,
                     oc.payable_amount          AS 合同金额,
                     CASE
                         oc.deleted
                         WHEN 0 THEN
                             '/'
                         END                    AS 育婴师工资,
                     CASE
                         oc.deleted
                         WHEN 0 THEN
                             '/'
                         END                    AS 服务费,
                     CASE
                         oc.renew_tag
                         WHEN 0 THEN
                             '否'
                         WHEN 1 THEN
                             '是'
                         END                    AS 是否续签
              FROM `stbella-order`.order_pay_record AS opr
                       LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
                       LEFT JOIN `stbella-order`.user_e_sign AS ues ON ues.id = oc.order_custom_certification_id
                       LEFT JOIN `stbella-base`.home_sitter_serve_fee AS hssf ON hssf.id = oc.product_id
                       LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
              WHERE oc.deleted = 0
                AND opr.deleted = 0
                AND opr.pay_time &gt;= #{start}
                AND opr.pay_time &lt; #{end}
                AND opr.pay_type IN (4, 5, 6)
                AND oc.order_type IN (10)
              UNION ALL
              SELECT opr.order_no               AS 订单编号,
                     opr.local_transactional_no AS 到家系统流水号,
                     opr.transactional_no       AS 支付宝微信线下汇款流水号,
                     CASE
                         oc.order_type
                         WHEN 11 THEN
                             '育婴师上户保证金订单'
                         END                    AS 订单类型,
                     opr.pay_amount             AS 支退金额,
                     CASE
                         opr.record_type
                         WHEN 1 THEN
                             '支付'
                         WHEN 2 THEN
                             '退款'
                         END                    AS 收款退款类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '支付宝'
                         WHEN 2 THEN
                             '微信'
                         WHEN 4 THEN
                             '线下支付'
                         WHEN 5 THEN
                             '线下支付'
                         WHEN 6 THEN
                             '线下支付'
                         END                    AS 支付类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '2021003125675134【到家账号】'
                         WHEN 2 THEN
                             '1622653226【到家账号】'
                         END                    AS 微信支付宝账号,
                     CASE
                         opr.pay_status
                         WHEN 1 THEN
                             '/'
                         END                    AS 财务审核状态,
                     opr.pay_time               AS 到账时间,
                     cs.site_name               AS 分站,
                     CASE
                         hsm.deleted
                         WHEN 0 THEN
                             '育婴师上户保证金'
                         END                    AS 商品名称,
                     bs.NAME                    AS 客户姓名,
                     bs.mobile                  AS 客户手机号,
                     oc.sell_name               AS 归属销售,
                     oc.gmt_create              AS 订单创建时间,
                     oc.payable_amount          AS 合同金额,
                     CASE
                         oc.deleted
                         WHEN 0 THEN
                             '/'
                         END                    AS 育婴师工资,
                     CASE
                         oc.deleted
                         WHEN 0 THEN
                             '/'
                         END                    AS 服务费,
                     CASE
                         oc.renew_tag
                         WHEN 0 THEN
                             '否'
                         WHEN 1 THEN
                             '是'
                         END                    AS 是否续签
              FROM `stbella-order`.order_pay_record AS opr
                       LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
                       LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
                       LEFT JOIN `stbella-base`.home_sitter_margin AS hsm ON hsm.id = oc.product_id
                       LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
              WHERE oc.deleted = 0
                AND opr.deleted = 0
                AND opr.pay_time &gt;= #{start}
                AND opr.pay_time &lt; #{end}
                AND opr.pay_status = 1
                AND opr.pay_type IN (1, 2)
                AND oc.order_type IN (11)
              UNION ALL
              SELECT opr.order_no               AS 订单编号,
                     opr.local_transactional_no AS 到家系统流水号,
                     opr.transactional_no       AS 支付宝微信线下汇款流水号,
                     CASE
                         oc.order_type
                         WHEN 11 THEN
                             '育婴师上户保证金订单'
                         END                    AS 订单类型,
                     opr.pay_amount             AS 支退金额,
                     CASE
                         opr.record_type
                         WHEN 1 THEN
                             '支付'
                         WHEN 2 THEN
                             '退款'
                         END                    AS 收款退款类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '支付宝'
                         WHEN 2 THEN
                             '微信'
                         WHEN 4 THEN
                             '线下支付'
                         WHEN 5 THEN
                             '线下支付'
                         WHEN 6 THEN
                             '线下支付'
                         END                    AS 支付类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '2021003125675134【到家账号】'
                         WHEN 2 THEN
                             '1622653226【到家账号】'
                         END                    AS 微信支付宝账号,
                     CASE
                         opr.pay_status
                         WHEN 1 THEN
                             '已审核'
                         WHEN 2 THEN
                             '审核拒绝'
                         WHEN 3 THEN
                             '待审核'
                         END                    AS 财务审核状态,
                     opr.pay_time               AS 到账时间,
                     cs.site_name               AS 分站,
                     CASE
                         hsm.deleted
                         WHEN 0 THEN
                             '育婴师上户保证金'
                         END                    AS 商品名称,
                     bs.NAME                    AS 客户姓名,
                     bs.mobile                  AS 客户手机号,
                     oc.sell_name               AS 归属销售,
                     oc.gmt_create              AS 订单创建时间,
                     oc.payable_amount          AS 合同金额,
                     CASE
                         oc.deleted
                         WHEN 0 THEN
                             '/'
                         END                    AS 育婴师工资,
                     CASE
                         oc.deleted
                         WHEN 0 THEN
                             '/'
                         END                    AS 服务费,
                     CASE
                         oc.renew_tag
                         WHEN 0 THEN
                             '否'
                         WHEN 1 THEN
                             '是'
                         END                    AS 是否续签
              FROM `stbella-order`.order_pay_record AS opr
                       LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
                       LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
                       LEFT JOIN `stbella-base`.home_sitter_margin AS hsm ON hsm.id = oc.product_id
                       LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
              WHERE oc.deleted = 0
                AND opr.deleted = 0
                AND opr.pay_time &gt;= #{start}
                AND opr.pay_time &lt; #{end}
                AND opr.pay_type IN (4, 5, 6)
                AND oc.order_type IN (11)
              UNION ALL
              SELECT opr.order_no               AS 订单编号,
                     opr.local_transactional_no AS 到家系统流水号,
                     opr.transactional_no       AS 支付宝微信线下汇款流水号,
                     CASE
                         oc.order_type
                         WHEN 14 THEN
                             '育婴师通用订单'
                         END                    AS 订单类型,
                     opr.pay_amount             AS 支退金额,
                     CASE
                         opr.record_type
                         WHEN 1 THEN
                             '支付'
                         WHEN 2 THEN
                             '退款'
                         END                    AS 收款退款类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '支付宝'
                         WHEN 2 THEN
                             '微信'
                         WHEN 4 THEN
                             '线下支付'
                         WHEN 5 THEN
                             '线下支付'
                         WHEN 6 THEN
                             '线下支付'
                         END                    AS 支付类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '2021003125675134【到家账号】'
                         WHEN 2 THEN
                             '1622653226【到家账号】'
                         END                    AS 微信支付宝账号,
                     CASE
                         opr.pay_status
                         WHEN 1 THEN
                             '/'
                         END                    AS 财务审核状态,
                     opr.pay_time               AS 到账时间,
                     cs.site_name               AS 分站,
                     hgp.product_name           AS 套餐名称,
                     bs.NAME                    AS 客户姓名,
                     bs.mobile                  AS 客户手机号,
                     oc.sell_name               AS 归属销售,
                     oc.gmt_create              AS 订单创建时间,
                     oc.payable_amount          AS 合同金额,
                     CASE
                         oc.deleted
                         WHEN 0 THEN
                             '/'
                         END                    AS 育婴师工资,
                     CASE
                         oc.deleted
                         WHEN 0 THEN
                             '/'
                         END                    AS 服务费,
                     CASE
                         oc.renew_tag
                         WHEN 0 THEN
                             '否'
                         WHEN 1 THEN
                             '是'
                         END                    AS 是否续签
              FROM `stbella-order`.order_pay_record AS opr
                       LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
                       LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
                       LEFT JOIN `stbella-base`.home_general_product AS hgp ON hgp.id = oc.product_id
                       LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
              WHERE oc.deleted = 0
                AND opr.deleted = 0
                AND opr.pay_time &gt;= #{start}
                AND opr.pay_time &lt; #{end}
                AND opr.pay_status = 1
                AND opr.pay_type IN (1, 2)
                AND oc.order_type IN (14)
              UNION ALL
              SELECT opr.order_no               AS 订单编号,
                     opr.local_transactional_no AS 到家系统流水号,
                     opr.transactional_no       AS 支付宝微信线下汇款流水号,
                     CASE
                         oc.order_type
                         WHEN 14 THEN
                             '育婴师通用订单'
                         END                    AS 订单类型,
                     opr.pay_amount             AS 支退金额,
                     CASE
                         opr.record_type
                         WHEN 1 THEN
                             '支付'
                         WHEN 2 THEN
                             '退款'
                         END                    AS 收款退款类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '支付宝'
                         WHEN 2 THEN
                             '微信'
                         WHEN 4 THEN
                             '线下支付'
                         WHEN 5 THEN
                             '线下支付'
                         WHEN 6 THEN
                             '线下支付'
                         END                    AS 支付类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '2021003125675134【到家账号】'
                         WHEN 2 THEN
                             '1622653226【到家账号】'
                         END                    AS 微信支付宝账号,
                     CASE
                         opr.pay_status
                         WHEN 1 THEN
                             '已审核'
                         WHEN 2 THEN
                             '审核拒绝'
                         WHEN 3 THEN
                             '待审核'
                         END                    AS 财务审核状态,
                     opr.pay_time               AS 到账时间,
                     cs.site_name               AS 分站,
                     hgp.product_name           AS 套餐名称,
                     bs.NAME                    AS 客户姓名,
                     bs.mobile                  AS 客户手机号,
                     oc.sell_name               AS 归属销售,
                     oc.gmt_create              AS 订单创建时间,
                     oc.payable_amount          AS 合同金额,
                     CASE
                         oc.deleted
                         WHEN 0 THEN
                             '/'
                         END                    AS 育婴师工资,
                     CASE
                         oc.deleted
                         WHEN 0 THEN
                             '/'
                         END                    AS 服务费,
                     CASE
                         oc.renew_tag
                         WHEN 0 THEN
                             '否'
                         WHEN 1 THEN
                             '是'
                         END                    AS 是否续签
              FROM `stbella-order`.order_pay_record AS opr
                       LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
                       LEFT JOIN `stbella-base`.base_sitter AS bs ON bs.id = oc.sitter_id
                       LEFT JOIN `stbella-base`.home_general_product AS hgp ON hgp.id = oc.product_id
                       LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
              WHERE oc.deleted = 0
                AND opr.deleted = 0
                AND opr.pay_time &gt;= #{start}
                AND opr.pay_time &lt; #{end}
                AND opr.pay_type IN (4, 5, 6)
                AND oc.order_type IN (14)
              UNION ALL
              SELECT opr.order_no               AS 订单编号,
                     opr.local_transactional_no AS 到家系统流水号,
                     opr.transactional_no       AS 支付宝微信线下汇款流水号,
                     CASE
                         oc.order_type
                         WHEN 12 THEN
                             '雇主母婴订单'
                         WHEN 13 THEN
                             '雇主育婴师订单'
                         END                    AS 订单类型,
                     opr.pay_amount             AS 支退金额,
                     CASE
                         opr.record_type
                         WHEN 1 THEN
                             '支付'
                         WHEN 2 THEN
                             '退款'
                         END                    AS 收款退款类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '支付宝'
                         WHEN 2 THEN
                             '微信'
                         WHEN 4 THEN
                             '线下支付'
                         WHEN 5 THEN
                             '线下支付'
                         WHEN 6 THEN
                             '线下支付'
                         END                    AS 支付类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '2021003125675134【到家账号】'
                         WHEN 2 THEN
                             '1622653226【到家账号】'
                         END                    AS 微信支付宝账号,
                     CASE
                         opr.pay_status
                         WHEN 1 THEN
                             '已审核'
                         WHEN 2 THEN
                             '审核拒绝'
                         WHEN 3 THEN
                             '待审核'
                         END                    AS 财务审核状态,
                     opr.pay_time               AS 到账时间,
                     cs.site_name               AS 分站,
                     hcp.combo_name             AS 套餐名称,
                     cic.customer_name          AS 客户姓名,
                     cic.phone_number           AS 客户手机号,
                     oc.sell_name               AS 归属销售,
                     oc.gmt_create              AS 订单创建时间,
                     oc.payable_amount          AS 合同金额,
                     CASE
                         oc.deleted
                         WHEN 0 THEN
                             '/'
                         END                    AS 育婴师工资,
                     CASE
                         oc.deleted
                         WHEN 0 THEN
                             '/'
                         END                    AS 服务费,
                     CASE
                         oc.renew_tag
                         WHEN 0 THEN
                             '否'
                         WHEN 1 THEN
                             '是'
                         END                    AS 是否续签
              FROM `stbella-order`.order_pay_record AS opr
                       LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
                       LEFT JOIN `stbella-customer`.customer_info_cts AS cic ON cic.id = oc.custom_id
                       LEFT JOIN `stbella-base`.home_customer_product AS hcp ON hcp.id = oc.product_id
                       LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
              WHERE oc.deleted = 0
                AND opr.deleted = 0
                AND opr.pay_time &gt;= #{start}
                AND opr.pay_time &lt; #{end}
                AND opr.pay_type IN (4, 5, 6)
                AND oc.order_type IN (12, 13)
              UNION ALL
              SELECT opr.order_no               AS 订单编号,
                     opr.local_transactional_no AS 到家系统流水号,
                     opr.transactional_no       AS 支付宝微信线下汇款流水号,
                     CASE
                         oc.order_type
                         WHEN 12 THEN
                             '雇主母婴订单'
                         WHEN 13 THEN
                             '雇主育婴师订单'
                         END                    AS 订单类型,
                     opr.pay_amount             AS 支退金额,
                     CASE
                         opr.record_type
                         WHEN 1 THEN
                             '支付'
                         WHEN 2 THEN
                             '退款'
                         END                    AS 收款退款类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '支付宝'
                         WHEN 2 THEN
                             '微信'
                         WHEN 4 THEN
                             '线下支付'
                         WHEN 5 THEN
                             '线下支付'
                         WHEN 6 THEN
                             '线下支付'
                         END                    AS 支付类型,
                     CASE
                         opr.pay_type
                         WHEN 1 THEN
                             '2021003125675134【到家账号】'
                         WHEN 2 THEN
                             '1622653226【到家账号】'
                         END                    AS 微信支付宝账号,
                     CASE
                         opr.pay_status
                         WHEN 1 THEN
                             '/'
                         END                    AS 财务审核状态,
                     opr.pay_time               AS 到账时间,
                     cs.site_name               AS 分站,
                     hcp.combo_name             AS 套餐名称,
                     cic.customer_name          AS 客户姓名,
                     cic.phone_number           AS 客户手机号,
                     oc.sell_name               AS 归属销售,
                     oc.gmt_create              AS 订单创建时间,
                     oc.payable_amount          AS 合同金额,
                     CASE
                         oc.deleted
                         WHEN 0 THEN
                             '/'
                         END                    AS 育婴师工资,
                     CASE
                         oc.deleted
                         WHEN 0 THEN
                             '/'
                         END                    AS 服务费,
                     CASE
                         oc.renew_tag
                         WHEN 0 THEN
                             '否'
                         WHEN 1 THEN
                             '是'
                         END                    AS 是否续签
              FROM `stbella-order`.order_pay_record AS opr
                       LEFT JOIN `stbella-order`.order_cts AS oc ON opr.order_no = oc.order_no
                       LEFT JOIN `stbella-customer`.customer_info_cts AS cic ON cic.id = oc.custom_id
                       LEFT JOIN `stbella-base`.home_customer_product AS hcp ON hcp.id = oc.product_id
                       LEFT JOIN `stbella-store`.cts_site AS cs ON cs.id = oc.cts_site_id
              WHERE oc.deleted = 0
                AND opr.deleted = 0
                AND opr.pay_time &gt;= #{start}
                AND opr.pay_time &lt; #{end}
                AND opr.pay_status = 1
                AND opr.pay_type IN (1, 2)
                AND oc.order_type IN (12, 13)) d
    </select>

</mapper>

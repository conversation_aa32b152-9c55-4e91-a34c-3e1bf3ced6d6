<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.nutrition.mapper.OrderNutritionApplyRefundMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.order.nutrition.entity.OrderNutritionApplyRefundPO">
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="order_no" property="orderNo"/>
        <result column="food_status" property="foodStatus"/>
        <result column="refund_cause" property="refundCause"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_name" property="createByName"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        deleted,
        gmt_create,
        order_no, food_status, refund_cause, refund_amount, create_by, create_by_name, remark
    </sql>

</mapper>

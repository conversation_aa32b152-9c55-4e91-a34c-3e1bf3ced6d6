<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.month.mapper.OrderPayRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.stbella.order.server.order.month.entity.OrderPayRecordPO">
        <result column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="deleted" property="deleted"/>
        <result column="order_no" property="orderNo"/>
        <result column="local_transactional_no" property="localTransactionalNo"/>
        <result column="transactional_no" property="transactionalNo"/>
        <result column="pay_type" property="payType"/>
        <result column="amount_type" property="amountType"/>
        <result column="pay_status" property="payStatus"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="pay_time" property="payTime"/>
        <result column="request_body" property="requestBody"/>
        <result column="remark" property="remark"/>
        <result column="audit_reason" property="auditReason"/>
        <result column="pay_account_id" property="payAccountId"/>
        <result column="pay_channel" property="payChannel"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        gmt_create,
        gmt_modified,
        deleted,
        order_no, local_transactional_no, transactional_no, pay_type, amount_type, pay_status, process_instance_id, audit_status, pay_amount, pay_time, request_body, remark ,audit_reason,pay_account_id,pay_channel
    </sql>




    <resultMap id="DtoResultMap"
               type="com.stbella.order.server.order.nutrition.dto.CapitalRecordExportDTO">
        <result column="record_type" property="collectRefundType"/>
        <result column="pay_type" property="collectRefundMethod"/>
        <result column="order_no" property="orderNo"/>
        <result column="local_transactional_no" property="localTransactionNo"/>
        <result column="transactional_no" property="transactionNo"/>
        <result column="pay_amount" property="collectRefundAmount"/>
        <result column="custom_name" property="customerName"/>
        <result column="custom_phone" property="customerPhone"/>
        <result column="pay_status" property="collectRefundStatus"/>
        <result column="pay_time" property="arriveAccountTime"/>
    </resultMap>
    <select id="doExportCapitalRecord" resultMap="DtoResultMap">
        select order_pay_record.record_type,
        order_pay_record.local_transactional_no,
        order_pay_record.transactional_no,
        order_pay_record.pay_amount,
        order_pay_record.pay_type,
        order_pay_record.pay_time,
        order_pay_record.pay_status,
        order_pay_record.order_no,
        order_nutrition_month_food.custom_name,
        order_nutrition_month_food.custom_phone
        from order_pay_record
        left join order_nutrition_month_food on order_pay_record.order_no =
        order_nutrition_month_food.order_no
        where order_pay_record.deleted = 0
        and order_nutrition_month_food.deleted = 0
        <if test="capitalRecordRequest.collectRefundType != null ">
            AND order_pay_record.record_type = #{capitalRecordRequest.collectRefundType}
        </if>
        <if test="capitalRecordRequest.collectRefundMethod != null">
            AND order_pay_record.pay_type = #{capitalRecordRequest.collectRefundMethod}
        </if>
        <if test="capitalRecordRequest.collectRefundMethod == null">
            AND order_pay_record.pay_type IN ('1', '2')
        </if>
        <if test="capitalRecordRequest.collectRefundStatus != null">
            AND order_pay_record.pay_status = #{capitalRecordRequest.collectRefundStatus}
        </if>
        <if test="capitalRecordRequest.orderNo != null and capitalRecordRequest.orderNo != ''">AND
            order_pay_record.order_no =
            #{capitalRecordRequest.orderNo}
        </if>
        <if test="capitalRecordRequest.customerName != null and capitalRecordRequest.customerName != ''">
            AND order_nutrition_month_food.custom_name LIKE
            CONCAT('%',#{capitalRecordRequest.customerName},'%')
        </if>
        <if test="capitalRecordRequest.customerPhone != null and capitalRecordRequest.customerPhone != ''">
            AND order_nutrition_month_food.custom_phone LIKE
            CONCAT('%',#{capitalRecordRequest.customerPhone},'%')
        </if>
        <if test="capitalRecordRequest.transactionNo != null and capitalRecordRequest.transactionNo != ''">
            AND order_pay_record.transactional_no =
            #{capitalRecordRequest.transactionNo}
        </if>
        <if test="capitalRecordRequest.arriveAccountTimeStart != null">AND order_pay_record.pay_time
            &gt;=
            #{capitalRecordRequest.arriveAccountTimeStart}
        </if>
        <if test="capitalRecordRequest.arriveAccountTimeEnd != null">AND order_pay_record.pay_time
            &lt;=
            #{capitalRecordRequest.arriveAccountTimeEnd}
        </if>
        ORDER BY order_pay_record.pay_time DESC
    </select>


    <select id="getCtsPayRecordList" resultType="com.stbella.order.server.order.month.entity.OrderPayRecordPO">
        SELECT
        opr.id,opr.gmt_create,opr.gmt_modified,opr.deleted,opr.order_no,opr.record_type, opr.local_transactional_no,
        opr.transactional_no, opr.pay_type, opr.amount_type,
        opr.pay_status, opr.process_instance_id, opr.audit_status, opr.pay_amount, opr.pay_time, opr.request_body,
        opr.remark ,opr.audit_reason,
        oc.order_type,oc.create_by_name,oc.gmt_modified as gmtOpt
        FROM
        order_pay_record AS opr
        LEFT JOIN order_cts AS oc ON oc.order_no = opr.order_no
        where
        opr.deleted = 0 and oc.deleted = 0
        and opr.local_transactional_no LIKE 'CTS%'
        and opr.pay_status != 3
        <if test="query.localTransactionalNo != null ">
            and opr.local_transactional_no = #{query.localTransactionalNo}
        </if>
        <if test="query.orderNo != null">
            and opr.order_no = #{query.orderNo}
        </if>
        <if test="query.customerNos != null and query.customerNos.size() > 0">
            and opr.order_no in
            <foreach collection="query.customerNos" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.siteNos != null and query.siteNos.size() > 0">
            and opr.order_no in
            <foreach collection="query.siteNos" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.payStatus != null">
            and opr.pay_status = #{query.payStatus}
        </if>
        <if test="query.begin != null and query.end != null">
            and opr.gmt_create between #{query.begin} and #{query.end}
        </if>
        <if test="query.orderType != null">
            and oc.order_type = #{query.orderType}
        </if>
        <if test="query.transactionalNo != null and query.transactionalNo != ''">
            and opr.transactional_no = #{query.transactionalNo}
        </if>
        <if test="query.recordType != null">
            and opr.record_type = #{query.recordType}
        </if>
        <if test="query.payType != null">
            and opr.pay_type = #{query.payType}
        </if>
        <if test="query.operatorName != null and query.operatorName != ''">
            AND oc.create_by_name LIKE CONCAT('%',#{query.operatorName},'%')
        </if>

        order by opr.gmt_create desc

    </select>

    <select id="queryPerformanceOfPayRecord"
            resultType="com.stbella.order.server.order.cts.response.order.OrderPayRecordVO">
        select opr.id,
        opr.gmt_create,
        opr.gmt_modified,
        opr.deleted,
        opr.order_no,
        opr.local_transactional_no,
        opr.transactional_no,
        opr.pay_type,
        opr.amount_type,
        opr.record_type,
        opr.pay_status,
        opr.process_instance_id,
        opr.audit_status,
        opr.pay_amount,
        opr.pay_time,
        opr.request_body,
        opr.remark,
        opr.audit_reason,
        oc.cts_site_id,
        oc.renew_tag,
        oc.order_type
        from order_pay_record opr
        left join order_cts oc on oc.order_no = opr.order_no
        where oc.cts_site_id is not null
        and opr.deleted = 0
        <if test="recordType != null">
            and opr.record_type = #{recordType}
        </if>
        <if test="payStatuses != null and payStatuses.size() > 0">
            and opr.pay_status in
            <foreach collection="payStatuses" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null">
            and opr.pay_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and opr.pay_time &lt;= #{endTime}
        </if>
        <if test="orderTypes != null and orderTypes.size() > 0">
            and oc.order_type in
            <foreach collection="orderTypes" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        <if test="performance != null">
            and oc.performance = #{performance}
        </if>
        <if test="payTypes != null and payTypes.size() > 0">
            and oc.pay_type in
            <foreach collection="payTypes" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        <if test="ctsSiteId != null">
            and oc.cts_site_id = #{ctsSiteId}
        </if>
        <if test="sellId != null">
            and oc.sell_id = #{sellId}
        </if>
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.server.order.cts.mapper.OrderCtsPaySignMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.server.order.cts.entity.OrderCtsPaySignPO">
        <result column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="deleted" property="deleted"/>
        <result column="order_no" property="orderNo"/>
        <result column="pay_sign" property="paySign"/>
        <result column="amount_type" property="amountType"/>
        <result column="pay_status" property="payStatus"/>
        <result column="local_transactional_no" property="localTransactionalNo"/>
        <result column="target_pay_type" property="targetPayType"/>
        <result column="target_pay_percent" property="targetPayPercent"/>
        <result column="target_pay_amount" property="targetPayAmount"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        gmt_create,
        gmt_modified,
        deleted,
        order_no, pay_sign, amount_type, pay_status, local_transactional_no, target_pay_type, target_pay_percent, target_pay_amount
    </sql>

</mapper>

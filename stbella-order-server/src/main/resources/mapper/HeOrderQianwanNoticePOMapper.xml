<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeOrderQianwanNoticeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeOrderQianwanNoticePO">
        <result column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="deleted" property="deleted"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="group_total_amount" property="groupTotalAmount"/>
        <result column="group_net_amount" property="groupNetAmount"/>
        <result column="all_amount_standard_rate" property="allAmountStandardRate"/>
        <result column="achievement" property="achievement"/>
        <result column="notice_type" property="noticeType"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        gmt_create,
        gmt_modified,
        deleted,
        year, month, group_total_amount, group_net_amount, all_amount_standard_rate, achievement, notice_type
    </sql>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>stbella-order</artifactId>
        <groupId>com.stbella</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>stbella-order-server</artifactId>
    <version>2.0-SNAPSHOT</version>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>
    <dependencies>

        <!-- 项目依赖 -->
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-order-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-contract-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-contract-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-order-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-asset-api</artifactId>
        </dependency>
        <!--邮件配置-->
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-core-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <!--邮件配置-->
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-base-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-base-model</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.statemachine</groupId>
            <artifactId>spring-statemachine-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-core-swagger</artifactId>
        </dependency>

        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>financial-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-marketing-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-store-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-care-api</artifactId>
            <version>${stbella-care-api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-care-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-sso-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-customer-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-sso-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-core-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-pay-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-core-mvc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-core-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-core-redis</artifactId>
        </dependency>

        <!-- 项目依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--依赖undertow，红帽基于netty的服务器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <!-- nacos-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!-- dubbo相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- xxl-job-core -->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.4.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>3.3.2</version>
        </dependency>
        <!-- 数据源 -->

        <dependency>
            <groupId>com.youzan.cloud</groupId>
            <artifactId>open-sdk-gen</artifactId>
            <version>1.0.22.80701202109281055-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.youzan.cloud</groupId>
            <artifactId>open-sdk-core</artifactId>
            <version>1.0.25-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>order-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>order-common</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-sleuth</artifactId>
        </dependency>
        <dependency>
            <groupId>io.zipkin.brave</groupId>
            <artifactId>brave-instrumentation-dubbo</artifactId>
            <version>5.13.3</version>
        </dependency>

        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-core-pulsar</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>order-infrastructure</artifactId>
        </dependency>

        <dependency>
            <groupId>top.primecare</groupId>
            <artifactId>snowball-spring-boot-starter</artifactId>
            <version>${snowball-spring-boot-starter.version}</version>
        </dependency>

        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>store-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-actuator</artifactId>
        </dependency>

    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>

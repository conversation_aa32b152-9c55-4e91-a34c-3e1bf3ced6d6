<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.12.RELEASE</version>
        <relativePath/>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.stbella</groupId>
    <version>1.0-SNAPSHOT</version>
    <artifactId>stbella-order</artifactId>
    <packaging>pom</packaging>
    <properties>
        <projects.revision>2.0-SNAPSHOT</projects.revision>
        <!-- core -->
        <stbella-core-common.version>1.2.7</stbella-core-common.version>
        <stbella-core-mvc.version>1.2.6</stbella-core-mvc.version>
        <stbella-core-mybatis.version>1.0.1</stbella-core-mybatis.version>
        <stbella-core-dependenices.version>1.0-SNAPSHOT</stbella-core-dependenices.version>
        <stbella-core-swagger.version>1.0-SNAPSHOT</stbella-core-swagger.version>
        <stbella-core-redis.version>1.0-SNAPSHOT</stbella-core-redis.version>
        <stbella-core-mail.version>1.0.1</stbella-core-mail.version>
        <!-- 项目依赖 -->
        <stbella-order-api.version>2.7.12</stbella-order-api.version>
        <stbella-store-api.version>2.4.11</stbella-store-api.version>
        <stbella-care-api.version>2.1.8</stbella-care-api.version>
        <stbella-care-model.version>2.1.8</stbella-care-model.version>
        <stbella-sso-api.version>2.5.1</stbella-sso-api.version>
        <stbella-store-common.version>2.2.4</stbella-store-common.version>
        <stbella-pay-api.version>2.0.9</stbella-pay-api.version>
        <stbella-base-api.version>2.4.7</stbella-base-api.version>
        <stbella-contract-api.version>1.2.2</stbella-contract-api.version>
        <stbella-customer-api.version>3.0.0-SNAPSHOT</stbella-customer-api.version>
        <stbella-message-api.version>1.0-SNAPSHOT</stbella-message-api.version>
        <stbella-asset-api.version>1.4.3</stbella-asset-api.version>
        <stbella-marketing-api.version>1.0-SNAPSHOT</stbella-marketing-api.version>
        <easyexcel.version>3.3.1</easyexcel.version>
        <stbella-store-core-api.version>1.1.3-SNAPSHOT</stbella-store-core-api.version>

        <snowball-spring-boot-starter.version>1.2.0</snowball-spring-boot-starter.version>
        <rule-link-client.version>1.0.6</rule-link-client.version>
        <rule-link-api.version>1.0.4</rule-link-api.version>
        <financial-api.version>2.2.1</financial-api.version>

        <!-- 三方  -->
        <lombok>1.18.18</lombok>
        <org.mapstruct.version>1.4.1.Final</org.mapstruct.version>
        <com.qcloud.version>4.4</com.qcloud.version>
        <spring-statemachine-core.version>2.1.3.RELEASE</spring-statemachine-core.version>
        <mybatis.generator.core.version>1.3.4</mybatis.generator.core.version>
        <dozer.version>5.5.1</dozer.version>
        <transmittable-thread-local.version>2.11.0</transmittable-thread-local.version>
        <stbella-core-pulsar.version>1.1.4</stbella-core-pulsar.version>
    </properties>

    <modules>
        <module>stbella-order-model</module>
        <module>stbella-order-api</module>
        <module>stbella-order-server</module>
        <module>order-domain</module>
        <module>order-infrastructure</module>
        <module>order-common</module>
        <module>order-deploy</module>
    </modules>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos_api</artifactId>
                <version>${com.qcloud.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <!-- 项目依赖 -->
            <!--考虑到Hutool的非强制依赖性，因此zxing需要用户自行引入-->
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>3.3.3</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-order-api</artifactId>
                <version>${stbella-order-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-order-model</artifactId>
                <version>${stbella-order-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-core-pulsar</artifactId>
                <version>${stbella-core-pulsar.version}</version>
            </dependency>


            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-pay-api</artifactId>
                <version>${stbella-pay-api.version}</version>
            </dependency>
            <!--邮件配置-->
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-core-mail</artifactId>
                <version>${stbella-core-mail.version}</version>
            </dependency>
            <!--邮件配置-->
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-store-api</artifactId>
                <version>${stbella-store-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-store-model</artifactId>
                <version>${stbella-store-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-care-api</artifactId>
                <version>${stbella-care-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-care-model</artifactId>
                <version>${stbella-care-model.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>store-common</artifactId>
                <version>${stbella-store-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-customer-api</artifactId>
                <version>${stbella-customer-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-customer-model</artifactId>
                <version>${stbella-customer-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-contract-api</artifactId>
                <version>${stbella-contract-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-contract-model</artifactId>
                <version>${stbella-contract-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-base-api</artifactId>
                <version>${stbella-base-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-sso-api</artifactId>
                <version>${stbella-sso-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-sso-model</artifactId>
                <version>${stbella-sso-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-base-model</artifactId>
                <version>${stbella-base-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>order-common</artifactId>
                <version>${stbella-order-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>order-domain</artifactId>
                <version>${projects.revision}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>order-infrastructure</artifactId>
                <version>${projects.revision}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-order-server</artifactId>
                <version>${projects.revision}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>message-api</artifactId>
                <version>${stbella-message-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-asset-api</artifactId>
                <version>${stbella-asset-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-marketing-api</artifactId>
                <version>${stbella-marketing-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-core-mybatis</artifactId>
                <version>${stbella-core-mybatis.version}</version>
            </dependency>

            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-core-swagger</artifactId>
                <version>${stbella-core-swagger.version}</version>
            </dependency>

            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-core-common</artifactId>
                <version>${stbella-core-common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.dev33</groupId>
                        <artifactId>sa-token-spring-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.dev33</groupId>
                        <artifactId>sa-token-dao-redis-jackson</artifactId>
                    </exclusion>

                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-core-mvc</artifactId>
                <version>${stbella-core-mvc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-core-redis</artifactId>
                <version>${stbella-core-redis.version}</version>
            </dependency>
            <!-- 项目依赖 -->


            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>stbella-core-dependenices</artifactId>
                <version>${stbella-core-dependenices.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--状态机依赖-->
            <dependency>
                <groupId>org.springframework.statemachine</groupId>
                <artifactId>spring-statemachine-core</artifactId>
                <version>${spring-statemachine-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-core</artifactId>
                <version>${mybatis.generator.core.version}</version>
            </dependency>
            <dependency>
                <groupId>net.sf.dozer</groupId>
                <artifactId>dozer</artifactId>
                <version>${dozer.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <dependency>
                <groupId>top.primecare</groupId>
                <artifactId>snowball-spring-boot-starter</artifactId>
                <version>${snowball-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stbella.platform</groupId>
                <artifactId>rule-link-api</artifactId>
                <version>${rule-link-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>store-api</artifactId>
                <version>${stbella-store-core-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.stbella.platform</groupId>
                <artifactId>rule-link-client</artifactId>
                <version>${rule-link-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.stbella</groupId>
                <artifactId>financial-api</artifactId>
                <version>${financial-api.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
    <!-- maven私仓 -->
    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <name>Nexus Release Repository</name>
            <url>https://nexus.primecare.top/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>https://nexus.primecare.top/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
    <repositories>
        <repository>
            <id>maven-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
            <releases>
                <enabled>false</enabled>
            </releases>
            <url>https://nexus.primecare.top/repository/maven-snapshots/</url>
        </repository>
        <repository>
            <id>maven-releases</id>
            <name>Nexus Release Repository</name>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
            </snapshots>
            <releases>
                <enabled>false</enabled>
            </releases>
            <url>https://nexus.primecare.top/repository/maven-releases/</url>
        </repository>
    </repositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <annotationProcessorPaths>
                        <!--执行顺序 -->
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.0</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.1.0</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>

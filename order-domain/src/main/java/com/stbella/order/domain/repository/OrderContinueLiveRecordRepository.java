package com.stbella.order.domain.repository;

import com.stbella.order.domain.order.month.entity.HeOrderAdditionalRevenueEntity;
import com.stbella.order.domain.order.month.entity.HeOrderContinueLiveRecordEntity;
import com.stbella.order.server.order.month.req.OrderMonthReq;

import java.util.List;

public interface OrderContinueLiveRecordRepository {
    //保存续住
    void saveContinueLive(List<HeOrderAdditionalRevenueEntity> orderAdditionalRevenueEntities, Integer orderId, OrderMonthReq req);

    /**
     * 更新续住
     *
     * @param orderAdditionalRevenueEntities
     * @param orderId
     */
    void updateContinueLive(List<HeOrderAdditionalRevenueEntity> orderAdditionalRevenueEntities, Integer orderId, OrderMonthReq req);

    /**
     * 获取订单续住记录
     *
     * @param orderIdList
     */
    List<HeOrderContinueLiveRecordEntity> getOrderCostMultipleBirthsRecord(List<Integer> orderIdList);

    List<HeOrderContinueLiveRecordEntity> getOrderCostMultipleBirthsRecordBySn(List<String> orderSnList);

    List<HeOrderContinueLiveRecordEntity> getByOrderId(Integer orderId);

    List<HeOrderContinueLiveRecordEntity> getByOrderIdList(List<Integer> orderId);

}


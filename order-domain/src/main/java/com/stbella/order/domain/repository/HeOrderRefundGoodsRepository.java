package com.stbella.order.domain.repository;


import com.stbella.order.domain.order.month.entity.HeOrderRefundGoodsEntity;

import java.util.List;

/**
 * <p>
 * 订单退款商品记录表 服务类
 * </p>
 *
 * <AUTHOR> @since 2024-07-09
 */
public interface HeOrderRefundGoodsRepository {

    void saveList(List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList);

    List<HeOrderRefundGoodsEntity> queryByOrderId(Integer orderId);

    List<HeOrderRefundGoodsEntity> queryList(Integer orderId);

    void updateList(List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList);

    List<HeOrderRefundGoodsEntity> queryByRefundOrderSn(String refundOrderSn);

    List<HeOrderRefundGoodsEntity> queryListByOrderId(Integer orderId);

    List<HeOrderRefundGoodsEntity> queryListByOrderIdList(List<Integer> orderIdList);
}

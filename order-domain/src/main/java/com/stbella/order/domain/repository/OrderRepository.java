package com.stbella.order.domain.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stbella.core.base.PageVO;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.utils.dto.AsyncResultDTO;
import com.stbella.order.server.order.month.req.*;
import com.stbella.order.server.order.month.res.*;
import com.stbella.order.server.order.order.req.OrderBasicReq;
import com.stbella.order.server.order.order.req.OrderExportReq;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface OrderRepository {

    /**
     * 查询订单基本信息
     *
     * @param req 查询条件
     * @return
     */
    HeOrderEntity queryOrderInfo(OrderBasicReq req);

    /**
     * 根据taskId获取订单
     *
     * @param taskId
     * @return
     */
    HeOrderEntity queryOrderInfoByTaskId(Long taskId);

    /**
     * 缓存套餐的信息
     *
     * @param req
     */
    void orderGoodsCache(OrderGoodsCacheReq req);

    /**
     * 保存订单主信息
     */
    Integer saveOrderMonth(HeOrderEntity entity);

    HeOrderEntity convertOrderMonthReq2HeOrderEntity(boolean isCreate, OrderMonthReq req, AsyncResultDTO<OrderMonthGoodsCacheVO, List<OrderGiftCacheByUserVO>, List<OrderAdditionalRevenueCacheVO>, OrderDiscountsCacheVO> resultDTO);

    /**
     * 修改订单主信息
     */
    Integer updateOrderMonthByOrderId(HeOrderEntity entity);

    /**
     * 缓存套餐的额外礼赠信息
     *
     * @param req
     */
    void orderMonthGiftExtendCache(OrderMonthGiftExtendCacheReq req);

    /**
     * 缓存套餐的加收项缓存
     *
     * @param req
     */
    void orderMonthAddtionalRevenueCache(List<OrderMonthAdditionalRevenueCacheReq> req);

    /**
     * 套餐信息缓存获取
     *
     * @param req
     */
    OrderGoodsCacheReq getOrderGoodsCache(OrderGoodsCacheReq req);

    /**
     * 套餐缓存删除
     *
     * @param req
     * @return
     */
    Boolean delOrderGoodsCache(OrderCacheBaseReq req);

    /**
     * 套餐信息额外礼赠缓存获取
     *
     * @param req
     */
    List<OrderGiftCacheVO> getOrderMonthGiftExtendCache(OrderMonthGiftExtendCacheReq req);

    /**
     * 套餐额外礼赠缓存删除
     *
     * @param req
     * @return
     */
    Boolean delOrderMonthGiftExtendCache(OrderCacheBaseReq req);

    /**
     * 套餐信息加收项缓存获取
     *
     * @param req
     */
    List<OrderMonthAdditionalRevenueCacheReq> getOrderMonthAdditionalRevenueCache(OrderMonthAdditionalRevenueCacheReq req);


    /**
     * 订单加收项缓存删除
     *
     * @param req
     * @return
     */
    Boolean delOrderMonthAdditionalRevenueCache(OrderCacheBaseReq req);


    void updateOrderMonthGiftExtendCache(OrderMonthGiftExtendCacheReq req);

    HeOrderEntity queryOrderById(Integer orderId);

    /**
     * 下单成功删除所有的缓存
     *
     * @param req
     * @return
     */
    Boolean removeCache(OrderCacheBaseReq req);

    /**
     * 小助手-我的订单列表
     *
     * @param query
     * @return
     */
    Page<WechatMyOrderVO> getMyOrder(WechatMyOrderQuery query);

    /**
     * 订单其他操作进行缓存
     *
     * @param req
     */
    void orderOtherInfoCache(OrderOtherInfoCacheReq req);

    /**
     * 获取订单其他信息
     *
     * @param req
     * @return
     */
    OrderOtherInfoCacheVO getOrderOtherInfoCache(OrderCacheBaseReq req);

    /**
     * 删除订单其他信息
     *
     * @param req
     * @return
     */
    Boolean delOrderOtherInfoCache(OrderCacheBaseReq req);


    /**
     * 小助手  我的订单详情
     *
     * @param orderId
     * @return
     */
    HeOrderEntity getByOrderId(Integer orderId);

    /**
     * 销售下所有订单
     *
     * @param staffId
     * @return
     */
    List<HeOrderEntity> getByStaffId(Integer staffId);

    /**
     * 根据客户id获取客户的订单列表
     *
     * @param req
     * @return
     */
    PageVO<WechatMyOrderVO> getCustomerStoreOrderList(ClientOrderListReq req);

    /**
     * 通过订单id获取WechatMyOrderVO
     *
     * @param orderId
     * @return
     */
    WechatMyOrderVO getWechatOrderVOByOrderId(Integer orderId);

    /**
     * 清空所有的缓存
     */
    void removeAllBeforeOrderCache();

    /**
     * 根据不同的订单类型获取订单名称
     *
     * @param order
     * @return
     */
    String getGoodsNameByOrder(HeOrderEntity order);

    List<HeOrderEntity> queryByCondition(OrderQuery performanceQuery);

    void closeOrder(HeOrderEntity byOrderId);

    /**
     * 房态根据订单号修改订单状态
     */
    boolean updateOrderStatusByOrderNoAndStatus(String orderNo, Integer orderStatus);

    Page queryAdminPage(Page page, List<Integer> clientList, List<Integer> basicList, List<Integer> orderIds, QueryOrderPageReq queryOrderPageReq, List<String> approveOrderIds, Set<Integer> typeStore);

    List<Integer> getAllNewOrderIdList();

    /**
     * 获取C端客户订单列表
     */
    Page<CustomerMyOrderVO> getCustomerOrderList(CustomerMyOrderQuery customerMyOrderQuery);

    List<HeOrderEntity> getByOrderList(List<Integer> orderIdList);

    HeOrderEntity getByOrderSn(String orderSn);

    List<HeOrderEntity> getByOrderSnList(List<String> orderSnList);

    /**
     * 同步订单退款状态
     */
    Integer syncOrderRefundStatus(Integer orderId, Integer orderRefundStatus);

    /**
     * 根据门店获取老订单(不包含已关闭订单)
     */
    List<HeOrderEntity> getOldOrderByStoreIdAndType(Integer storeId, Integer orderType);

    /**
     * 通过订单id获取订单编号
     *
     * @param orderIds
     * @return java.util.List<com.stbella.order.domain.order.month.entity.HeOrderEntity>
     * @throws
     * <AUTHOR>
     * @date 2023/8/8 13:55
     * @since 1.0.0
     */
    List<HeOrderEntity> listOrderNoByIds(List<Integer> orderIds);

    /**
     * 获取一个老订单(不包含已关闭订单)
     */
    HeOrderEntity getOldOrderByType(Integer orderType);

    /**
     * 导出当月订单明细
     *
     * @param req
     * @return
     */
    List<HeOrderEntity> queryCurrMonthOrderIterm(OrderExportReq req);

    Integer updateSaleByOrderId(Integer orderId, Integer saleId);

    /**
     * 导出当月订单明细
     *
     * @param clientIds
     * @return
     */
    List<HeOrderEntity> queryByClientIds(List<Integer> clientIds);

    /**
     * 设置订单状态
     */
    void updateOrderStatus(Integer orderId, Integer status);


    /**
     * 订单变更
     *
     * @param entity
     * @return
     */
    Integer updateOne(HeOrderEntity entity);


    List<HeOrderEntity> getByOrderIdList(List<Integer> orderIds);

    List<HeOrderEntity> queryOrderForPayFirstRecord(PayFirstRecordReq req);

    /**
     * 查询指定日期签约的标准月子订单列表
     *
     * @param date
     * @return
     */
    List<HeOrderEntity> queryOrderMonthForDate(Date date);

    Page<HeOrderEntity> queryList(OrderPageReq req);

    Page<HeOrderEntity> queryList(WechatMyOrderNewQuery req);

    Page<HeOrderEntity> queryList(MyOrderNewQuery req);

    Page<HeOrderEntity> queryList(OrderPerformanceReq req);

    Page<HeOrderEntity> querySurveyCustomerList(SurveyCustomerReq req);

    List<HeOrderEntity> getInfoByByBasicUid(List<Integer> basicUidList, List<Integer> orderType);

    Page<HeOrderEntity> queryIsLaHealthyCustomerList(IsLaHealthyCustomerReq req);

    Page<HeOrderEntity> queryList(OrderPaySuccessReq req, List<Integer> giftOrderIdList);

    Page<HeOrderEntity> queryListByBasicUid(WechatMyOrderQuery basicUid);


    List<HeOrderEntity> getListByOrderSn(String keyword,Long basicUid);

    List<HeOrderEntity> filterBasicUid(List<Integer> categoryOrderIdList, Long basicUid);

    void batchUpdateById(List<HeOrderEntity> orderList);

    Page<HeOrderEntity> queryList(String orderSn, Integer pageNum, Integer pageSize);

    Page<HeOrderEntity> pageByOrderIdList(Integer pageNum, Integer pageSize, List<Integer> orderIdList);

    /**
     * same method pageByOrderIdList required percentFirstTime
     */
    Page<HeOrderEntity> pageByOrderIdListAndPercentPay(Integer pageNum, Integer pageSize, List<Integer> orderIdList, Boolean flg);

    Page<HeOrderEntity> queryGiftOrderList(WechatMyOrderQuery req);

    List<Integer> queryByClientIdsAndAssetType(List<Integer> clientIdList, List<Integer> list, Integer storeId, String orderSn);

    List<HeOrderEntity> listOldOrders();

    /**
     * 查询订单用于金额数据迁移
     * @param offset 偏移量
     * @param pageSize 页大小
     * @return 订单列表
     */
    List<HeOrderEntity> queryOrdersForMigration(int offset, int pageSize);

    /**
     * 按订单ID范围查询订单用于金额数据迁移
     * @param startOrderId 起始订单ID
     * @param pageSize 页大小
     * @param endOrderId 结束订单ID
     * @return 订单列表
     */
    List<HeOrderEntity> queryOrdersForMigrationByRange(Integer startOrderId, Integer pageSize, Integer endOrderId);
}

package com.stbella.order.domain.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.stbella.core.base.Operator;
import com.stbella.core.result.Result;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.common.utils.CompareUtil;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.domain.convert.OrderGoodsDomainConverter;
import com.stbella.order.domain.order.entity.OrderOperateLogEntity;
import com.stbella.order.domain.order.entity.OrderRelationEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsSbarEntity;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.domain.order.production.OrderGiftExtendEntity;
import com.stbella.order.domain.order.production.OrderProductionExtendEntity;
import com.stbella.order.domain.order.service.OrderCreateDomainService;
import com.stbella.order.domain.repository.*;
import com.stbella.order.domain.utils.TransactionUtil;
import com.stbella.order.server.order.OrderProductionCardContentItermDTO;
import com.stbella.order.server.order.month.enums.BuyTypeEnum;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import com.stbella.store.common.enums.core.GoodsTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-05-29  16:00
 * @Description: 创建订单领域服务
 */
@Service
@Slf4j
public class OrderCreateDomainServiceImpl implements OrderCreateDomainService {

    @Resource
    TransactionUtil transactionUtil;
    @Resource
    OrderRelationRepository relationRepository;
    @Resource
    OrderVoucherRepository voucherRepository;
    @Resource
    HeOrderAttachmentRepository attachmentRepository;
    @Resource
    OrderGoodsRepository goodsRepository;
    @Resource
    HeOrderGoodsSbarRepository goodsSbarRepository;
    @Resource
    OrderGoodsDomainConverter orderGoodsDomainConverter;
    @Resource
    private OrderGiftExtendRepository orderGiftExtendRepository;
    @Resource
    private OrderProductionExtendRepository orderProductionExtendRepository;
    @Resource
    OrderOperatorLogRepository operatorLogRepository;
    @Resource
    RuleLinkClient ruleLinkClient;

    /**
     * 其他订单类型 商品资产类型
     */
    private static  String OTHER_ORDER_TYPE_KEY = "order_order_goods_type";

    /**
     * 创建订单
     *
     * @param entity
     * @return
     */
    @Override
    public Result<Integer> create(HeOrderEntity entity, Operator operator) {

        /**
         * 主订单信息  ok
         * *主子订单关系 ok
         * 商品信息(含加收)  ok
         * 客户信息  （目前是在合同阶段再录入的，另外一个接口处理） ok
         * 委托人信息  （同上 这里无）
         * sbra商品  ok
         * 特殊订单凭证 ok
         * 商品附件信息 ok
         * 履约信息(自定义)OK
         * 产康商品。
         * *优惠记录
         */
        transactionUtil.transact(status -> {

            //订制：如果商品中goodsType 没有=0，且有1的，订单类型则写成 1小月子
            if (CollectionUtil.isNotEmpty(entity.getGoodsList())) {
                parseOrderType(entity);
            }
            int orderId = entity.add(operator);
            entity.setOrderId(orderId);

            //客户信息
            HeOrderUserSnapshotEntity customer = entity.getCustomer();
            customer.setOrderId(orderId);
            customer.add();

            // 商品 order id 赋值
            entity.getGoodsList().forEach(goodsEntity -> goodsEntity.setOrderId(orderId));
            log.info("订单商品信息：{}", JSONObject.toJSONString(entity.getGoodsList()));
            goodsRepository.batchInsert(entity.getGoodsList());

            // 处理产康商品 保存到产康表，方便核销
            saveProductionGoods(entity, operator);
            //处理 s-bra 商品
            List<HeOrderGoodsSbarEntity> heOrderGoodsSbarEntities = buildSbarGoods(entity, operator);
            if (CollectionUtil.isNotEmpty(heOrderGoodsSbarEntities)) {
                goodsSbarRepository.batchInsert(heOrderGoodsSbarEntities);
            }

            //商品附件
            if (CollectionUtil.isNotEmpty(entity.getAttachmentList())) {
                entity.getAttachmentList().forEach(attachmentEntity -> attachmentEntity.setOrderId(orderId));
                attachmentRepository.batchInsert(entity.getAttachmentList());
            }

            //特殊凭证
            if (CollectionUtil.isNotEmpty(entity.getVoucherEntityList())) {
                entity.getVoucherEntityList().forEach(voucherEntity -> voucherEntity.setOrderId(orderId));
                voucherRepository.batchInsert(entity.getVoucherEntityList());
            }

            if (!Strings.isNullOrEmpty(entity.getParentOrderNo())) {
                //父子关系表
                OrderRelationEntity relationEntity = new OrderRelationEntity();
                relationEntity.setParentOrderNo(entity.getParentOrderNo());
                relationEntity.setOrderNo(entity.getOrderSn());
                relationRepository.save(relationEntity);
            }


            //操作日志
            OrderOperateLogEntity logEntity = OrderOperateLogEntity.builder()
                    .operatorId(operator.getOperatorGuid())
                    .operatorName(operator.getOperatorName())
                    .orderSn(entity.getOrderSn())
                    .operatorPhone(operator.getOperatorPhone())
                    .operatorType(BizActivityEnum.CREATE_ORDER.code())
                    .remark("创建订单").build();

            operatorLogRepository.save(logEntity);

        });

        return Result.success(entity.getOrderId());
    }

    /**
     * 这里兼容原来的需求单独给sbra做的一个导出。这个在产品与技术上的思考都是不全面的，架构师每次的疏忽都会产生一个这样的问题。
     * 写于此，与君共勉。
     *
     * @param order
     * @return
     */
    protected List<HeOrderGoodsSbarEntity> buildSbarGoods(HeOrderEntity order, Operator operator) {

        List<HeOrderGoodsSbarEntity> braGoodsList = new ArrayList<>();
        for (HeOrderGoodsEntity goods : order.getGoodsList()) {
            if (!CompareUtil.integerEqual(goods.getGoodsType(), GoodsTypeEnum.S_BRA_PACKAGE.code())) {
                continue;
            }
            String storeName = "";
            if (ObjectUtil.isNotNull(order.getStore())) {
                storeName = order.getStore().getStoreName();
            }
            HeOrderGoodsSbarEntity heOrderGoodsSbra = new HeOrderGoodsSbarEntity();
            heOrderGoodsSbra.setOrderId(order.getOrderId());
            heOrderGoodsSbra.setOrderSn(order.getOrderSn());
            heOrderGoodsSbra.setOrderType(order.getOrderType());
            heOrderGoodsSbra.setBasicUid(order.getCustomer().getBasicUid());
            heOrderGoodsSbra.setClientUid(order.getCustomer().getClientUid());
            heOrderGoodsSbra.setClientName(order.getCustomer().getName());
            heOrderGoodsSbra.setClientPhone(order.getCustomer().getPhone());
            heOrderGoodsSbra.setBuyType(goods.getGift().intValue() == 1 ? BuyTypeEnum.GIFT.getCode() : BuyTypeEnum.BUY.getCode());
            heOrderGoodsSbra.setStoreId(order.getStoreId());
            heOrderGoodsSbra.setStoreName(storeName);
            heOrderGoodsSbra.setGoodsId(goods.getGoodsId());
            heOrderGoodsSbra.setGoodsName(goods.getGoodsName());
            heOrderGoodsSbra.setGoodsPrice(goods.getGoodsPriceOrgin());
            heOrderGoodsSbra.setCostPrice(goods.getGoodsCost());
            heOrderGoodsSbra.setSkuId(goods.getSkuId());
            heOrderGoodsSbra.setSkuName(goods.getSkuName());
            heOrderGoodsSbra.setSkuNum(1);
            heOrderGoodsSbra.setGoodsNum(goods.getGoodsNum());
            heOrderGoodsSbra.setOrderTime(new Date());
            heOrderGoodsSbra.setCreateBy(order.getStaffId().longValue());
            heOrderGoodsSbra.setUpdateBy(order.getStaffId().longValue());
            heOrderGoodsSbra.setCreateByName(operator.getOperatorName());
            heOrderGoodsSbra.setUpdateByName(operator.getOperatorName());
            braGoodsList.add(heOrderGoodsSbra);
        }

        return braGoodsList;
    }

    /**
     * 保存产康商品
     * 分购买与礼赠
     *
     * @param order
     * @param operator
     */
    protected void saveProductionGoods(HeOrderEntity order, Operator operator) {
        //gift
        List<OrderGiftExtendEntity> giftExtendEntityList = new ArrayList<>();
        // buy
        List<OrderProductionExtendEntity> productionExtendEntityList = new ArrayList<>();

        for (HeOrderGoodsEntity goods : order.getGoodsList()) {
            //非产康 不处理
            if (!CompareUtil.integerEqual(goods.getGoodsType(), GoodsTypeEnum.CHANKANG_SERVICE.code())) {
                continue;
            }
            //非单项不处理
            if (!CombineTypeEnum.isSimple(goods.getType())) {
                continue;
            }

            //区分购买与礼赠
            if (goods.getGift() == 1) {
                OrderGiftExtendEntity orderGiftExtendEntity = orderGoodsDomainConverter.entity2GiftExtend(goods);
                orderGiftExtendEntity.setBasicId(order.getBasicUid());
                giftExtendEntityList.add(orderGiftExtendEntity);

            } else {
                OrderProductionExtendEntity productionExtendEntity = orderGoodsDomainConverter.entity2ProductionExtend(goods);
                productionExtendEntity.setId(null);
                productionExtendEntity.setBasicId(order.getBasicUid());
                productionExtendEntityList.add(productionExtendEntity);
            }
        }

        if (CollectionUtil.isNotEmpty(productionExtendEntityList)) {
            //处理 content {"position":"","service_time":2,"serve_type":0,"service_tag":0} 这里默认写死吧。
            productionExtendEntityList.forEach(entity -> {
                OrderProductionCardContentItermDTO ext = new OrderProductionCardContentItermDTO()
                        .setPosition("")
                        .setService_time(entity.getServiceTime())
                        .setService_tag(0)
                        .setServe_type(entity.getServeType());

                entity.setContent(JSONUtil.toJsonStr(ext));
            });
            orderProductionExtendRepository.batchInsert(productionExtendEntityList);
        }
        if (CollectionUtil.isNotEmpty(giftExtendEntityList)) {
            orderGiftExtendRepository.batchInsert(giftExtendEntityList);
        }
    }

    /**
     * 解析订单类型
     *  月子订单 中
     *  1，商品中资产类型中没有标准月子有小月子，订单类型写成小月子
     *  2，商品中资产类型 全是管内增值服务，客房服务，实物商品，订单类型写其他订单类型
     * @param entity
     * @return
     */
    protected void parseOrderType(HeOrderEntity entity){
        if (CompareUtil.integerEqual(OmniOrderTypeEnum.MONTH_ORDER.getCode(), entity.getOrderType())){
            int normalCareCount = entity.getGoodsList().stream().filter(goodsEntity -> GoodsTypeEnum.CONFINEMENT_PACKAGE.code().equals(goodsEntity.getGoodsType())).collect(Collectors.toList()).size();
            int specialCareCount = entity.getGoodsList().stream().filter(goodsEntity -> GoodsTypeEnum.MISCARRIAGE_PACKAGE.code().equals(goodsEntity.getGoodsType())).collect(Collectors.toList()).size();
            //小月子
            if (normalCareCount == 0 && specialCareCount > 0){
                entity.setOrderType(OmniOrderTypeEnum.SMALL_MONTH_ORDER.getCode());
            }
            //其他订单类型
            parseOtherOrderType(entity);
        }
    }

    /**
     * 解析其他订单类型
     * 所有商品资产类型都在指定范围内，订单类型写成其他订单类型
     * @param entity
     */
    protected void parseOtherOrderType(HeOrderEntity entity){
        if (CompareUtil.integerEqual(OmniOrderTypeEnum.MONTH_ORDER.getCode(), entity.getOrderType())){
            Set<Integer> assetSet = queryOtherOrderGoodsTypeSet();
            boolean isMonthOrder = entity.getGoodsList().stream().filter(a-> CombineTypeEnum.isSimple(a.getType())).anyMatch(goodsEntity -> !assetSet.contains(goodsEntity.getGoodsType()));
            if (!isMonthOrder){
                entity.setOrderType(OmniOrderTypeEnum.OTHER_MONTH_ORDER.getCode());
            }
        }
    }

    /**
     * 其他订单类型商品资产类型的范围
     */
    protected Set<Integer> queryOtherOrderGoodsTypeSet(){
        Map<String, String> factMap = new HashMap<>();
        factMap.put("configCode",OTHER_ORDER_TYPE_KEY);
        ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req("order-center-config", factMap);
        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
        if (ObjectUtil.isNotNull(hitRuleVo)){
            Set<Integer> assetSet = Arrays.stream(hitRuleVo.getSimpleRuleValue().split(",")).map(a-> Integer.parseInt(a)).collect(Collectors.toSet());
            return assetSet;
        }
        return new HashSet<>();
    }

}

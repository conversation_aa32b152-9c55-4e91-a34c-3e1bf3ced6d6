package com.stbella.order.domain.order.month.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 订单费用加收项
 * </p>
 *
 * <AUTHOR> @since 2022-10-27
 */
@Data
@ApiModel(value = "HeOrderAdditionalRevenueEntity对象", description = "订单费用加收项Entity")
public class HeOrderAdditionalRevenueEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "是否开启（目前只有节假日）：0：不开启；1：开启")
    private Integer isFestival;

    @ApiModelProperty(value = "收费项名称")
    private String goodsName;

    @ApiModelProperty(value = "门店id")
    private Integer storeId;

    @ApiModelProperty(value = "关联订单id")
    private Integer orderId;

    @ApiModelProperty(value = "1=多胞胎费用 2=续住费用 3=变更房型 ")
    private Integer type;

    @ApiModelProperty(value = "应收")
    private Integer price;

    @ApiModelProperty(value = "原价")
    private Integer cost;

    @ApiModelProperty(value = "成本")
    private Integer costPrice;

    @ApiModelProperty(value = "具体信息")
    private String content;

    @ApiModelProperty(value = "赠送来源 1=小助手 2=后台  默认小助手 3=购买补录")
    private Integer source;

    @ApiModelProperty(value = "房型ID")
    private Integer roomId;

    @ApiModelProperty(value = "房型名称")
    private String roomName;

    @ApiModelProperty(value = "天数")
    private Integer days;

    @ApiModelProperty(value = "生效日期(年-月-日,年月日")
    private List<String> daysList;

    @ApiModelProperty(value = "胎数")
    private Integer embryoNumber;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    @ApiModelProperty("修改时间")
    private Date gmtModified;

    @ApiModelProperty("逻辑删除标识 0-未删除 1-已删除")
    private Integer deleted;
}

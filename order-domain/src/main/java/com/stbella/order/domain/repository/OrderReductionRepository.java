package com.stbella.order.domain.repository;

import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.server.order.month.req.OrderQuery;
import com.stbella.platform.order.api.reduction.req.ReductionQuery;

import java.util.ArrayList;
import java.util.List;

/**
 * 订单减免
 * 减免 - 下完订单后，手动减少签单金额
 */
public interface OrderReductionRepository {

    Long addOne(OrderReductionEntity orderDecreaseEntity);

    List<OrderReductionEntity> select(ReductionQuery query);

    OrderReductionEntity selectByLocalProcessId(String localProcessId);

    int updateOne(OrderReductionEntity orderDecreaseEntity);

    OrderReductionEntity getById(Long reductionId);

    /**
     * 减免金额
     * @param query
     * @return
     */
    Long reductionSum(OrderQuery query);

    /**
     * 根据合同id查询减免信息
     * @param id
     * @return
     */
    OrderReductionEntity selectByContractId(Long id);

    OrderReductionEntity selectFirstReductionEntity(Long orderId);

    List<OrderReductionEntity> listByOrderIdList(List<Integer> orderIdList, ArrayList<Integer> authStateList);
}

package com.stbella.order.domain.order.month.entity;

import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.store.common.enums.core.GoodsTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class HeQuotationGoodsEntity {

    private Integer id;

    @ApiModelProperty(value = "主订单商品ID")
    private Integer parentId;

    @ApiModelProperty(value = "报价单id'")
    private Integer quotationId;

    @ApiModelProperty(value = "规格id")
    private Integer skuId;

    @ApiModelProperty(value = "规格名称")
    private String skuName;

    @ApiModelProperty(value = "商品id")
    private Integer goodsId;

    @ApiModelProperty(value = "商品类型")
    private Integer goodsType;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品主图")
    private String goodsImage;

    @ApiModelProperty(value = "商品数量")
    private Integer goodsNum;

    @ApiModelProperty(value = "商品成本价")
    private Integer goodsCost;

    @ApiModelProperty(value = "商品原单价")
    private Integer goodsPriceOrgin;

    @ApiModelProperty(value = "商品实付单价")
    private Integer goodsPricePay;

    @ApiModelProperty(value = "应付金额")
    private Integer payAmount;

    @ApiModelProperty(value = "护理服务时长，单位")
    private Integer serviceDays;

    @ApiModelProperty(value = "物流状态：0无需发货，1待发货，2已发货/待签收，4已签收/待确认收货，3确认收货")
    private Integer sendStatus;

    @ApiModelProperty(value = "加购来源：同步自cart表的rource字段值，用于标示立即购买或加购来自哪里")
    private String source;

    @ApiModelProperty(value = "备注说明")
    private String content;

    @ApiModelProperty(value = "时间段id")
    private Integer skuExtendId;

    @ApiModelProperty(value = "商城商品类型 0=实物 1=虚拟 2=预约, 3套餐")
    private Integer goodsSellType;

    @ApiModelProperty(value = "单个商品积分兑换数量")
    private Integer integral;

    @ApiModelProperty(value = "应付总积分数")
    private Integer payIntegral;

    @ApiModelProperty(value = "70S-BRA订单类型用到的 件数")
    private Integer piece;

    @ApiModelProperty(value = "房间ID")
    private Integer roomId;

    @ApiModelProperty(value = "房间名称")
    private String roomName;

    @ApiModelProperty(value = "ecp房型配置")
    private Integer ecpRoomType;

    @ApiModelProperty(value = "套餐的自定义属性")
    private String definedProperty;

    @ApiModelProperty(value = "商品类型：2 表示单个普通商品，3 表示组合商品")
    private Integer type;

    @ApiModelProperty(value = "业务线")
    private String businessType;

    @ApiModelProperty(value = "资产类型")
    private String assetType;

    @ApiModelProperty(value = "后台类目id")
    private Long backCategoryId;

    @ApiModelProperty(value = "有效期开始")
    private Date validStartTime;

    @ApiModelProperty(value = "有效期斯结束")
    private Date validEndTime;

    @ApiModelProperty(value = "否礼赠: 0不是，1是")
    private Integer gift;

    @ApiModelProperty(value = "序号（关联用此字段）")
    private String orderGoodsSn;

    @ApiModelProperty(value = "组合上级sn")
    private String parentCombineSn;

    @ApiModelProperty(value = "分摊原价")
    private Integer allocationOriginPrice;

    @ApiModelProperty("物品清单")
    private String inventoryUrl;

    @ApiModelProperty(value = "是否删除：1是，0否")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    private Date updatedAt;


    /**
     * 是否是附加项
     *
     * @return
     */
    public boolean fetchIsAddition() {
        return this.goodsType.equals(GoodsTypeEnum.HOLIDAY_SERVICE.code()) || this.goodsType.equals(GoodsTypeEnum.MULTIPLE_BIRTHS.code());
    }

    /**
     * 是否是礼赠品
     * @return
     */
    public boolean fetchIsGift() {
        return this.gift != null && this.gift == 1;
    }

    /**
     * 是否是组合商品
     * @return
     */
    public boolean fetchIsCombine() {
        return this.type != null && this.type.equals(CombineTypeEnum.COMBINE.code());
    }
}

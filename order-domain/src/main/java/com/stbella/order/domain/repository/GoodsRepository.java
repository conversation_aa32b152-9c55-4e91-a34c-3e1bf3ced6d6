package com.stbella.order.domain.repository;

import com.stbella.order.domain.order.production.GoodsEntity;
import com.stbella.order.server.order.GoodsSimpleInfoModel;
import com.stbella.order.server.order.ProductionGoodsSkuModel;
import com.stbella.order.server.order.StoreGoodsSkuModel;

import java.util.List;

public interface GoodsRepository {

    GoodsEntity selectById(Integer goodsId);

    List<StoreGoodsSkuModel> getOrderGoodsName(List<Integer> goodsIdList);

    List<GoodsEntity> selectByIdList(List<Integer> goodsId);

    List<GoodsEntity> selectListByGoodsSellType(List<Integer> goodsSellTypeList);

    /**
     * 查询所有产康单项商品(这里后面商品改造后可能会有问题)
     *
     * @return
     */
    List<ProductionGoodsSkuModel> queryProductionGoods(List<Integer> skuIdList);

    List<GoodsSimpleInfoModel> queryGoodsSimpleInfoByGoodsIdList(List<Integer> goodsIdList);

    List<GoodsEntity> queryByIdForDelete(List<Integer> collect);
}

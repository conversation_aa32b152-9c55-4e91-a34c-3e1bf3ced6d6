package com.stbella.order.domain.order.entity;

import cn.hutool.json.JSONUtil;
import com.stbella.core.enums.BusinessEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.utils.SpringContextHolder;
import com.stbella.order.domain.repository.HeCartGoodsRepository;
import com.stbella.order.domain.repository.HeCartRepository;
import com.stbella.order.domain.utils.TransactionUtil;
import com.stbella.platform.order.api.res.PromotionInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
@Slf4j
public class HeCartEntity {

    @ApiModelProperty(value = "购物车ID")
    private Integer cartId;

    /**
     * 业务线
     * @see BusinessEnum
     */
    @ApiModelProperty(value = "BU 0:母婴 1:广禾 2:予家")
    private Integer bu = BusinessEnum.CARE_CENTER.getCode();

    /**
     * 订单类型 (可能有些场景，外部没有这个值，需要内部赋值)
     * @see OmniOrderTypeEnum
     */
    @ApiModelProperty(value = "订单类型")
    private Integer orderType = OmniOrderTypeEnum.MONTH_ORDER.code();

    @ApiModelProperty(value = "序号")
    private String cartSn;

    @ApiModelProperty(value = "门店ID")
    private Integer storeId;

    @ApiModelProperty(value = "加购客户全局ID")
    private Integer basicUid;

    @ApiModelProperty(value = "扩展信息：标准json，主要包含预产期、预入驻日期、胎数等")
    private String extendContent;

    @ApiModelProperty(value = "特殊订单：false-否，true-是")
    private Boolean special;

    @ApiModelProperty(value = "特殊订单标签ID")
    private Integer specialTag;

    @ApiModelProperty(value = "特殊订单标签名称")
    private String specialTagName;

    @ApiModelProperty(value = "特殊凭证URL集合，多个英文逗号分隔")
    private String specialVoucherUrls;

    @ApiModelProperty(value = "特殊订单说明")
    private String specialDesc;

    @ApiModelProperty(value = "是否已提交订单：false-否，true-是")
    private Boolean submitted;

    @ApiModelProperty(value = "订单ID等业务id")
    private Integer orderId;

    /**
     * 商品原价:分
     */
    private Integer totalAmount;

    /**
     * 场景：0 下单； 1 报价单
     * @see com.stbella.order.common.enums.core.CartSceneEnum
     */
    @ApiModelProperty(value = "场景：0 下单； 1 报价单,2,随心配")
    private Integer scene;

    /**
     * 订单原价:分（含有加收项目）
     */
    private Integer orderAmount;

    /**
     * 签单金额
     */
    private Integer payAmount;


    @ApiModelProperty(value = "创建时间")
    private Date addTime;

    @ApiModelProperty(value = "最近更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "是否删除：false-否，true-是")
    private boolean deleted;

    @ApiModelProperty(value = "加购商品集合")
    private List<HeCartGoodsEntity> goodsList;

    private HeCartRepository heCartRepository;

    private HeCartGoodsRepository heCartGoodsRepository;

    @ApiModelProperty(value = "客户系统ID")
    private Integer clientUid;

    @ApiModelProperty(value = "备注信息")
    private String remark;

    @ApiModelProperty(value = "销售员id（ecp库user表主键id）")
    private Integer staffId;

    @ApiModelProperty(value = "销售员电话")
    private String staffPhone;

    @ApiModelProperty(value = "销售员姓名")
    private String staffName;

    @ApiModelProperty(value = "原订单ID（用于订单升级）")
    private Integer originalOrderId;

    @ApiModelProperty(value = "原商品总价（用于订单升级）")
    private BigDecimal originalGoodsTotalPrice;

    TransactionUtil transactionUtil;

    public HeCartEntity() {
        this.heCartRepository = SpringContextHolder.getBean(HeCartRepository.class);
        this.heCartGoodsRepository = SpringContextHolder.getBean(HeCartGoodsRepository.class);
        this.transactionUtil = SpringContextHolder.getBean(TransactionUtil.class);
    }

    /**
     * 保存购物车
     * @return
     */
    public Integer saveOrUpdateCart() {

        transactionUtil.transact( status -> {
            cartId = heCartRepository.saveOrUpdate(this);
            this.deleteCartGoods();
            this.setCartId(cartId);
            if (CollectionUtils.isNotEmpty(goodsList)) {

                //goodsList的sub 展开
                List<HeCartGoodsEntity> insertList = new ArrayList<>();

                goodsList.forEach(cartGoods -> {
                    insertList.add(cartGoods);
                    if (CollectionUtils.isNotEmpty(cartGoods.getSubGoodsList())) {
                        insertList.addAll(cartGoods.getSubGoodsList());
                    }
                });
                insertList.forEach(cartGoods -> {
                    cartGoods.setCartId(cartId);
                    cartGoods.setStoreId(storeId);
                    cartGoods.setBasicUid(basicUid);
                });

                heCartGoodsRepository.saveOrUpdateBatch(insertList);
            }
        });

        return cartId;
    }

    /**
     * 删除购物车商品
     */
    public void deleteCartGoods() {
        heCartGoodsRepository.deleteByCartId(cartId);
    }

    /**
     * 提交订单后清空购物车（回写订单号，用于还原，复制之类的，方便还原数据，其实也可以用订单还原的）
     */
    public void finishCart(Integer orderId) {
        HeCartEntity modifyEntity = new HeCartEntity()
                .setCartId(this.cartId)
                .setOrderType(null)
                .setSubmitted(true)
                .setOrderId(orderId);

        heCartRepository.update(modifyEntity);
    }

    /**
     * 更新当前购物车修改金额
     */
    public void modifyAmount(Integer orderAmount, Integer goodsAmount, Integer payAmount) {
        HeCartEntity modifyEntity = new HeCartEntity()
                .setCartId(this.cartId)
                .setOrderAmount(orderAmount)
                .setTotalAmount(goodsAmount)
                .setPayAmount(payAmount)
                .setOrderType(null);

        log.info("cat modifyAmount:{}", JSONUtil.toJsonStr(modifyEntity));
        heCartRepository.update(modifyEntity);
    }


}

package com.stbella.order.domain.order.production;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.stbella.core.base.Operator;
import com.stbella.core.utils.DateUtils;
import com.stbella.order.common.constant.RedisConstant;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.core.IsNanEnum;
import com.stbella.order.common.exception.ApplicationException;
import com.stbella.order.common.utils.SpringContextHolder;
import com.stbella.order.domain.order.pipeline.context.AppointmentContext;
import com.stbella.order.domain.pipeline.FilterChainPipeline;
import com.stbella.order.domain.repository.OrderProductionCardExtendRepository;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.stbella.order.common.constant.BizConstant.*;
import static com.stbella.order.common.utils.DateUtils.YYYY_MM_DD;
import static com.stbella.order.domain.order.pipeline.context.AppointmentContext.METHOD_CLS;

/**
 * 订单生产任命实体
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2022/09/30
 * @since 2022-09-30 15:59
 */
@Data
@Slf4j
public class OrderProductionAppointmentEntity implements Serializable {

    private static final long serialVersionUID = -1212923614431278027L;

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    FilterChainPipeline<AppointmentContext> appointmentCreatePipeline;

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    FilterChainPipeline<AppointmentContext> appointmentUpdatePipeline;

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    FilterChainPipeline<AppointmentContext> appointmentCancelPipeline;

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    FilterChainPipeline<AppointmentContext> appointmentWriteOffPipeline;

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    OrderProductionCardExtendRepository orderProductionCardExtendRepository;

    public OrderProductionAppointmentEntity() {
        this.appointmentCreatePipeline = SpringContextHolder.getBean("appointmentCreatePipeline", FilterChainPipeline.class);
        this.appointmentUpdatePipeline = SpringContextHolder.getBean("appointmentUpdatePipeline", FilterChainPipeline.class);
        this.appointmentCancelPipeline = SpringContextHolder.getBean("appointmentCancelPipeline", FilterChainPipeline.class);
        this.appointmentWriteOffPipeline = SpringContextHolder.getBean("appointmentWriteOffPipeline", FilterChainPipeline.class);
        this.orderProductionCardExtendRepository = SpringContextHolder.getBean(OrderProductionCardExtendRepository.class);
    }

    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联购买资产订单
     */
    private Integer orderId;

    /**
     * 预约单号
     */
    private String orderSn;

    /**
     * 关联购买资产服务项
     */
    private Integer orderProductionCardId;

    /**
     * 关联购买资产服务项 卡项id
     */
    private Integer orderProductionCardExtendId;

    /**
     * 门店id
     */
    private Integer storeId;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 用户basic id
     */
    private Integer basicId;

    /**
     * 买家客户ID（ecp库的tab_client表主键）
     */
    private Integer clientId;

    /**
     * 客户名称
     */
    private String clientName;

    /**
     * 客户手机号
     */
    private String clientPhone;

    /**
     * 产康师id
     */
    private Long therapistId;

    /**
     * 产康师名称
     */
    private String therapistName;

    /**
     * 通卡次卡名称 单项服务为0
     */
    private Integer groupGoodsId;

    /**
     * 通卡次卡id 单项服务为空
     */
    private String groupGoodsName;

    /**
     * 通卡分组id, 次卡/单项服务为0
     */
    private Integer groupId;

    /**
     * 通卡组合名称
     */
    private String groupName;

    /**
     * 单项服务goodsid
     */
    private Integer productionGoodsId;

    /**
     * 单项服务商品名称
     */
    private String productionGoodsName;

    /**
     * 单项服务skuid
     */
    private Integer productionSkuId;

    /**
     * 单项服务sku名称
     */
    private String productionSkuName;

    /**
     * 组装好后的产康商品名称
     */
    private String productionName;

    /**
     * 订单类型 1: 购买 2：额外礼赠
     */
    private Integer orderProductionType;

    /**
     * 产康仪器id 0表示不需要仪器
     */
    private Long productionInstrId;

    /**
     * 1=通卡 0=次卡 2=单项服务
     */
    private Integer itemType;

    /**
     * 预约方式：1=客户预约;2=代客预约
     */
    private Integer bookType;

    /**
     * 预约单类型：1=标准;2=补单
     */
    private Integer appointmentType;

    /**
     * 服务费 分
     */
    private Integer serveFee;

    /**
     * 服务开始时间
     */
    private Date serveStart;

    /**
     * 服务结束时间
     */
    private Date serveEnd;

    /**
     * 预约日期 yyyy-MM-dd
     */
    private String serveDate;

    /**
     * 服务时长 分单位
     */
    private Integer serveTime;

    /**
     * 项目类型0=自营;1=三方
     */
    private Integer serveType;

    /**
     * 核销状态：0=待核销;1=已核销;2已预约；3已取消
     */
    private Integer verificationState;

    /**
     * 核销选项
     */
    private String optionsVerification;

    /**
     * 扩展json
     */
    private String extra;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 逻辑删除标志
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 修改人id
     */
    private Long updateId;

    /**
     * 修改人名称
     */
    private String updateName;

    /**
     * 请求来源
     */
    private String source;

    @ApiModelProperty(value = "产康订单创建人")
    private Integer createBy;

    /**
     * 门店产康仪器
     *
     * @deprecated
     */
    private List<ProductionInstrStoreEntity> productionInstrStoreEntities;

    /**
     * 预约日志
     */
    private List<OrderProductionAppointmentOptLogEntity> logEntities;

    /**
     * 创建预约单
     */
    public Long create(Operator operator) {
        AppointmentContext context = (AppointmentContext) AppointmentContext
                .create()
                .genAppointmentPayload(this, operator)
                .genPayloadData(METHOD_CLS, IsNanEnum.NO.code())
                .isTransactionManagerCtrl(true)
                .lockKey(
                        // 锁定产康资产
                        RedisConstant.APPOINTMENT_ASSERT_LOCK_KEY + this.orderId
                );
        appointmentCreatePipeline.fire(context);
        return context.loadEntity().getId();
    }

    /**
     * 修改预约单
     */
    public Long update(Operator operator) {
        AppointmentContext context = (AppointmentContext) AppointmentContext
                .create()
                .genAppointmentPayload(this, operator)
                .genPayloadData(METHOD_CLS, IsNanEnum.YES.code())
                .isTransactionManagerCtrl(true)
                .lockKey(
                        RedisConstant.APPOINTMENT_LOCK_KEY + this.id
                );
        appointmentUpdatePipeline.fire(context);
        return context.loadEntity().getId();
    }

    /**
     * 取消预约单
     */
    public Boolean cancel(Operator operator) {
        AppointmentContext context = (AppointmentContext) AppointmentContext
                .create()
                .genAppointmentPayload(this, operator)
                .isTransactionManagerCtrl(true)
                .lockKey(
                        RedisConstant.APPOINTMENT_LOCK_KEY + this.id
                );
        appointmentCancelPipeline.fire(context);
        return true;
    }

    /**
     * 核销预约单
     */
    public Boolean writeOff(Operator operator) {
        AppointmentContext context = (AppointmentContext) AppointmentContext
                .create()
                .genAppointmentPayload(this, operator)
                .isTransactionManagerCtrl(true)
                .lockKey(
                        RedisConstant.APPOINTMENT_LOCK_KEY + this.id
                );
        appointmentWriteOffPipeline.fire(context);
        return true;
    }

    /**
     * 服务时间段生成
     */
    public void genServeRange(String serveStart, String serveEnd) {
        try {
            this.setServeStart(DateUtils.parse(this.serveDate + " " + serveStart + ":00"));

            if (Objects.equals(serveEnd, "00:00")) {
                DateTime parse = DateUtils.parse(this.serveDate, YYYY_MM_DD);
                Calendar instance = Calendar.getInstance();
                instance.setTime(parse);
                instance.add(Calendar.DAY_OF_YEAR, 1);
                String format = DateUtils.format(instance.getTime(), YYYY_MM_DD);
                this.setServeEnd(DateUtils.parse(format + " " + serveEnd + ":00"));
            } else {
                this.setServeEnd(DateUtils.parse(this.serveDate + " " + serveEnd + ":00"));
            }
        } catch (Exception e) {
            throw new ApplicationException(ErrorCodeEnum.ILLEGAL_PARAMETERS.code(), "服务时间格式异常");
        }
    }

    /**
     * 获取组装好后的产康服务名称
     *
     * @return
     */
    public String assemblerProductionName() {
        switch (this.getItemType()) {
            case SECONDARY_CARD:
                // 次卡
                this.setProductionName(this.getGroupGoodsName());
                break;
            case PASS_CARD:
                // 通卡
                this.setProductionName(this.getGroupGoodsName() + this.getGroupName());
                break;
            case INDIVIDUAL_SERVICE:
                // 单项服务
                this.setProductionName(this.getProductionGoodsName());
                break;
            default:
                break;
        }

        return this.getProductionName();
    }

    public String assemblerProductionNameBack() {
        String productionName = "";
        switch (this.getItemType()) {
            // 次卡
            case SECONDARY_CARD:
                productionName = this.getGroupGoodsName() + "-" + this.getProductionGoodsName() + (!"".equals(this.getProductionSkuName()) ? "-" + this.getProductionSkuName() : "");
                break;
            case PASS_CARD:
                productionName = this.getGroupGoodsName() + "-" + this.getGroupName() + "-" + this.getProductionGoodsName() + (!"".equals(this.getProductionSkuName()) ? "-" + this.getProductionSkuName() : "");
                break;
            case INDIVIDUAL_SERVICE:
                productionName = this.getProductionGoodsName() + (!"".equals(this.getProductionSkuName()) ? "-" + this.getProductionSkuName() : "");
                break;
            default:
                break;
        }
        this.setProductionName(productionName);
        return this.getProductionName();
    }

    public String instrLockKey(String serveDate) {
        if (Objects.isNull(this.productionInstrId) || this.productionInstrId == 0) {
            return "";
        }
        return RedisConstant.APPOINTMENT_INSTR_LOCK_KEY + this.productionInstrId + ":" + serveDate;
    }
}

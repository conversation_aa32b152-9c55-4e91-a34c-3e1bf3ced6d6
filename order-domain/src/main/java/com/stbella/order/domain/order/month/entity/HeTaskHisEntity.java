package com.stbella.order.domain.order.month.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.stbella.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 合同&折扣审批历史
 * </p>
 *
 * <AUTHOR> @since 2022-03-16
 */
@Data
@ApiModel(value = "TaskHisEntity对象", description = "合同&折扣审批历史")
public class HeTaskHisEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long taskId;

    private Integer approvalType;

    private Integer approvalStatus;

    private Integer restarted;

    private Integer formTemplateType;

    private String taskName;

    private String instanceId;

//    @ApiModelProperty(value = "审批类型 0=钉钉;1=企微;")
//    private Integer oaPlatformType;
}

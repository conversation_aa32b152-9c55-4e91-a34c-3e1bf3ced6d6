package com.stbella.order.domain.repository;

import com.stbella.order.domain.order.month.entity.AheadOutRoomEntity;
import com.stbella.order.server.order.month.req.AheadOutRoomQuery;
import com.stbella.order.server.order.month.res.SimpleAheadOutRoomVO;

import java.util.List;

/**
 * <p>
 * 提前离管
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-12 11:20
 */
public interface AheadOutRoomRepository {


    /**
     * 新增
     * @param entity
     * @return {@link Boolean}
     */
    Integer saveOutRoom(AheadOutRoomEntity entity);

    /**
     * 修改保存
     * @param entity
     * @return {@link Boolean}
     */
    Boolean updateOutRoom(AheadOutRoomEntity entity);

    AheadOutRoomEntity queryByOrderId(AheadOutRoomQuery query);

    List<AheadOutRoomEntity> queryByOrderIds(List<Integer> orderIds);

    List<AheadOutRoomEntity> queryByOrderIdsAndStatus(List<Integer> orderIds);

    SimpleAheadOutRoomVO entity2SimpleAheadOutRoomVO(AheadOutRoomEntity entity);

}

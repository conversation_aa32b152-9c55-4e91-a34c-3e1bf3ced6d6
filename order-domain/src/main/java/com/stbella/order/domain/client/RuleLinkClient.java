package com.stbella.order.domain.client;

import com.stbella.rule.api.req.BatchExecuteRuleReq;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;

import java.util.List;
import java.util.Set;


/**
 * RuleLink client
 */
public interface RuleLinkClient {

    String SCENE_TEMPLATE_ID = "mounth_contrace_rule";

    String PAPER_STORE_IDS = "paper_store_ids_rule";

    /**
     * 订单折扣凭证
     */
    String ORDER_DISCOUNT_CONFIG = "order_discount_config";


    /**
     * 基本规则查询 - 返回单一规则，没有命中返回nll
     * @param req
     * @return
     */
    HitRuleVo hitOneRule(ExecuteRuleV2Req req);


    List<HitRuleVo> hitRuleList(ExecuteRuleV2Req req);

    /**
     * 基本规则查询 - 返回单一规则，没有命中抛异常
     * @param req
     * @return
     */
    HitRuleVo hitOneRuleWithException(ExecuteRuleV2Req req);

    /**
     * 批量执行规则，每个使用优先级最高的
     * @param req
     * @return
     */
    List<HitRuleVo> batchHitRule(BatchExecuteRuleReq req);


    /**
     * 批量执行规则，并计算，每个使用优先级最高的
     * @param req
     * @return
     */
    List<HitRuleVo> batchHitAndCalculateRule(BatchExecuteRuleReq req);

    /**
     * 查询老合同门店
     * @return
     */
    Set<String> getOldContractStoreIds();


    /**
     * 查询不需要签署的门店
     * @return
     */
    Set<Integer> getNoNeedSignStoreIds();
}

package com.stbella.order.domain.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stbella.core.base.PageVO;
import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsEntity;
import com.stbella.order.server.order.month.req.ComplaintsPicpPageReq;
import com.stbella.platform.order.api.req.CustomerComplaintsPageReq;

import java.util.List;

public interface HeCustomerComplaintsRepository {

    /**
     * 分页查询客诉列表
     *
     * @param pageReq
     * @return
     */
    PageVO<HeCustomerComplaintsEntity> pageList(CustomerComplaintsPageReq pageReq);

    Page<HeCustomerComplaintsEntity> pageList(ComplaintsPicpPageReq pageReq);

    /**
     * 删除客诉
     *
     * @param id
     * @return
     */
    Boolean deleteById(Long id);

    /**
     * 保存客诉
     *
     * @param heCustomerComplaintsEntity
     * @return
     */
    Long save(HeCustomerComplaintsEntity heCustomerComplaintsEntity);

    /**
     * 更新客诉
     *
     * @param heCustomerComplaintsEntity
     */
    void update(HeCustomerComplaintsEntity heCustomerComplaintsEntity);

    /**
     * 根据ID查询客诉
     * @param id
     * @return
     */
    HeCustomerComplaintsEntity selectById(Long id);

    /**
     * 根据订单ID查询客诉列表
     * @param orderId
     * @return
     */
    List<HeCustomerComplaintsEntity> getListByOrderId(Long orderId);

    HeCustomerComplaintsEntity selectByApproveId(String processId);

    HeCustomerComplaintsEntity selectByOrderRefundId(Integer orderRefundId);

    void updateCompensationOrderId(HeCustomerComplaintsEntity customerComplaintsEntity);

    List<HeCustomerComplaintsEntity> listByClientUid(List<Integer> clientIdList);
}

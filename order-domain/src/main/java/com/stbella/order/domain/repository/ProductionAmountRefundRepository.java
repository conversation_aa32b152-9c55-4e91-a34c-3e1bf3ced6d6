package com.stbella.order.domain.repository;

import com.stbella.order.domain.order.month.entity.HeUserProductionAmountRefundLogEntity;

import java.util.List;

public interface ProductionAmountRefundRepository {

    /**
     * 产康金退款记录保存
     * @param record
     * @return
     */
    Integer saveOne(HeUserProductionAmountRefundLogEntity record);

    /**
     * 更新产康金退款记录
     * @param entity
     * @return
     */
    boolean updateRecord(HeUserProductionAmountRefundLogEntity entity);

    /**
     * 根据id查询产康金退款记录
     * @param id
     * @return
     */
    HeUserProductionAmountRefundLogEntity queryById(Integer id);

    /**
     * 根据订单号查询成功的产康金退款记录
     * @param orderId
     * @return
     */
    List<HeUserProductionAmountRefundLogEntity> queryRecordListByOrderId(Integer orderId);

    /**
     * 根据订单号查询所有产康金退款记录
     * @param orderIds
     * @return
     */
    List<HeUserProductionAmountRefundLogEntity> queryAllRecordByOrderIds(List<Integer> orderIds);


}

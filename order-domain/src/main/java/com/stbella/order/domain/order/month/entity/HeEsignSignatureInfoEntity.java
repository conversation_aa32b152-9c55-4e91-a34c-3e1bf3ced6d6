package com.stbella.order.domain.order.month.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class HeEsignSignatureInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "模板ID")
    private Long templateId;

    @ApiModelProperty(value = "签署方类型;1-企业;2-个人")
    private Integer signatureParticipant;

    @ApiModelProperty(value = "签署方身份;1-甲方;2-乙方;-3-丙方;4-丁方")
    private Integer signatureIdentity;

    @ApiModelProperty(value = "签署要求; 1-模板指定;2-发起人本人;3-固定成员")
    private Integer signatureRequire;

    @ApiModelProperty(value = "签署形式;1-企业章;2-法人章;3-手绘签名")
    private Integer signatureType;

    @ApiModelProperty(value = "印章样式;1-电子印章;2-手绘印章")
    private Integer sealType;

    @ApiModelProperty(value = "签章坐标:X轴,y轴")
    private String posXY;

    @ApiModelProperty(value = "经办人ID;取he_org_handler_esign ID")
    private Long orgHandlerId;

    @ApiModelProperty(value = "机构账号ID")
    private String orgId;

    @ApiModelProperty(value = "印章ID,取E签宝")
    private String sealId;

    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    @ApiModelProperty(value = "创建人名称")
    private String createName;

    @ApiModelProperty(value = "修改人ID")
    private Long updateBy;

    @ApiModelProperty(value = "修改人名称")
    private String updateName;

    @ApiModelProperty(value = "逻辑删除标志")
    @TableLogic
    private Integer deleted;

    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "修改时间")
    private Date gmtModified;


}

package com.stbella.order.domain.client;

import com.stbella.customer.server.customer.request.goodsappointment.GoodsAppointmentQueryRequest;
import com.stbella.customer.server.customer.request.growth.CustomerAssetsFlowReq;
import com.stbella.customer.server.customer.vo.goodsappointment.GoodsAppointmentListVO;
import com.stbella.order.server.order.production.res.WechatUserVo;

import java.util.List;

/**
 * 客户成长值
 */
public interface CustomerGrowthClient {

    /**
     * 增加成长值
     * @param req
     */
    void  addGrowth(CustomerAssetsFlowReq req);

    /**
     * 查询小月龄订单预约状态
     * @param req
     * @return
     */
    List<GoodsAppointmentListVO> queryUserAppointment(List<GoodsAppointmentQueryRequest> req);
}

package com.stbella.order.domain.order.month.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.stbella.order.common.constant.TableFieldConstant;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 合同模版表,新版E签宝
 * </p>
 *
 * <AUTHOR> @since 2021-11-04
 */
@Data
public class MonthEsignTemplateEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "模版名称。")
    private String name;

    @ApiModelProperty(value = "模版类型 1:订单类  2:附件类 3:补充协议类")
    private Integer templateType;

    @ApiModelProperty(value = "适用order_type 多个，隔开")
    private String orderTypes;

    @ApiModelProperty(value = TableFieldConstant.templateContractType)
    private Integer templateContractType;

    @ApiModelProperty(value = "状态 0:失效 1:生效")
    private Integer state;

    @ApiModelProperty(value = "E签宝模版id。")
    private String esignFileId;

    @ApiModelProperty(value = "门店类型: 0圣贝拉，1小贝拉，2月子总部，3到家总部，4到家门店，5商城网店，6 BELLA VILLA")
    private Integer storeType;

    @ApiModelProperty(value = "模版备注")
    private String remark;

    @ApiModelProperty(value = "合同文件url")
    private String fileUrl;

    @ApiModelProperty(value = "合同页数")
    private Integer pageNum;

    @ApiModelProperty(value = "门店ID；默认为0，多个门店逗号分隔 特殊处理的门店为对应store表的store_id")
    private String storeId;

    @ApiModelProperty(value = "公司签章位置 x,y坐标")
    private String companySealPos;

    @ApiModelProperty(value = "个人签名x,y坐标")
    private String personSignaturePos;

    @ApiModelProperty(value = "签章所在页面第几页")
    private Integer posPage;

    /**
     * 逻辑删除标志
     */
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

}

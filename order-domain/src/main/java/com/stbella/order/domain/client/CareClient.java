package com.stbella.order.domain.client;
import cn.hutool.core.date.DatePattern;
import com.google.common.collect.Maps;

import com.stbella.care.server.care.dto.comment.CommentParamDTO;
import com.stbella.care.server.care.request.comment.CommentEntranceReq;
import com.stbella.care.server.care.service.comment.CommentCommandService;
import com.stbella.care.server.care.service.comment.CommentQueryService;
import com.stbella.care.server.care.vo.comment.CommentApplicationRelVO;
import com.stbella.message.scene.api.MessageCommandService;
import com.stbella.message.scene.req.SceneTriggerReq;
import com.stbella.order.domain.order.production.OrderProductionAppointmentEntity;
import com.stbella.order.domain.repository.GoodsRepository;
import com.stbella.order.server.order.month.enums.StoreTypeEnum;
import com.stbella.store.core.service.store.StoreQueryService;
import com.stbella.store.core.vo.res.store.StoreBaseVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
@Slf4j
public class CareClient {

    @DubboReference(timeout = 5000)
    private CommentQueryService commentQueryService;

    @DubboReference(timeout = 5000)
    private CommentCommandService commentCommandService;

    @DubboReference(timeout = 5000)
    private MessageCommandService messageCommandService;

    @DubboReference(timeout = 5000)
    private StoreQueryService storeQueryService;

    @Resource
    private GoodsRepository goodsRepository;



    /**
     * 产康核销后发送给用户评价消息
     * @param entity
     * @param commentApplicationId
     */
    public void genEntrance(OrderProductionAppointmentEntity entity,
                            Long commentApplicationId,
                            String appointmentCommentAvatar,
                            List<StoreBaseVO> storeBaseList,
                            CommentApplicationRelVO commentApplicationRel) {
        log.info("writeOffCommentRemind genEntrance 开始发送 核销单id={}", entity.getId());
        if (Objects.isNull(entity.getClientId())
                || Objects.isNull(entity.getStoreId())
                || Objects.isNull(entity.getBasicId())
                || Objects.isNull(entity.getProductionSkuId())
                || Objects.isNull(entity.getProductionGoodsId())
                || StringUtils.isBlank(entity.getProductionSkuName())
                || StringUtils.isBlank(entity.getProductionGoodsName())) {
            log.error("writeOffCommentRemind genEntrance 必要参数为空 核销单id={}", entity.getId());
            return;
        }

        StoreBaseVO storeBase = storeBaseList.stream()
                .filter(i -> Objects.equals(i.getStoreId(), entity.getStoreId().longValue()))
                .findFirst().orElse(null);
        if (Objects.isNull(storeBase)) {
            log.error("writeOffCommentRemind genEntrance 门店查询失败 核销单id=" + entity.getId());
            return;
        }
        if (!Objects.equals(storeBase.getType(), StoreTypeEnum.SAINT_BELLA.getCode()) &&
                !Objects.equals(storeBase.getType(), StoreTypeEnum.BABY_BELLA.getCode())) {
            log.error("writeOffCommentRemind genEntrance 门店错误 核销单id={}", entity.getId());
            return;
        }

        try {
            String commentUrl = this.getCommentUrl(entity, commentApplicationId, appointmentCommentAvatar, commentApplicationRel);
            log.info("writeOffCommentRemind genEntrance 获取评价地址={}, 核销单id={}", commentUrl, entity.getId());
            if (StringUtils.isBlank(commentUrl)){
                return;
            }
            sendCustomer(entity, storeBase, commentUrl);
        } catch (Exception e) {
            log.error("writeOffCommentRemind genEntrance 发送失败 核销单id={}", entity.getId(), e);
        }
        log.info("writeOffCommentRemind genEntrance 执行结束 核销单id={}", entity.getId());
    }




    private void sendCustomer(OrderProductionAppointmentEntity entity, StoreBaseVO storeBase, String commentUrl) {
        HashMap<String, String> paramMap = Maps.newHashMap();
        paramMap.put("goodsName", entity.getProductionGoodsName());
        paramMap.put("writeOffDate", DatePattern.NORM_DATETIME_FORMAT.format(entity.getGmtModified()));
        paramMap.put("storeName", storeBase.getStoreName() + "-产康项目邀请您去评价～");
        paramMap.put("commentUrl", commentUrl);
        SceneTriggerReq sceneTriggerReq = new SceneTriggerReq();
        sceneTriggerReq.setRequestId(UUID.randomUUID().toString().replace("-", ""));
        sceneTriggerReq.setContextData(paramMap);
        sceneTriggerReq.setTargetList(Collections.singletonList(entity.getClientId().toString()));
        if (Objects.equals(storeBase.getType(),StoreTypeEnum.SAINT_BELLA.getCode())){
            sceneTriggerReq.setSceneId(100168L);
        }else {
            sceneTriggerReq.setSceneId(100169L);
        }
        messageCommandService.triggerScene(sceneTriggerReq);
    }




    private String getCommentUrl(OrderProductionAppointmentEntity entity,
                                 Long commentApplicationId,
                                 String appointmentCommentAvatar,
                                 CommentApplicationRelVO commentApplicationRel){

        CommentEntranceReq req = new CommentEntranceReq();
        // 评价应用id
        req.setApplicationId(commentApplicationId);
        // 评价业务id
        req.setTargetBizId(entity.getOrderSn());
        // 评价业务名称
        req.setTargetBizName(entity.getProductionSkuName());
        // 评价客户basicId
        req.setTargetBasicId(entity.getBasicId().longValue());

        // 评价对象参数
        CommentParamDTO commentParam = new CommentParamDTO();
        commentParam.setObjId(commentApplicationRel.getObjs().get(0).getObjId());
        commentParam.setTargetObjId(String.valueOf(entity.getTherapistId()));
        commentParam.setTargetObjName(entity.getTherapistName());
        commentParam.setTargetObjAvatar(appointmentCommentAvatar);
        req.setObjPrams(Collections.singletonList(commentParam));
        req.setCreatedBy(-1L);

        // 评价入口id
        Long entranceId = commentCommandService.genEntrance(req);
        // 获取评价链接
        return commentQueryService.getCommentUrl(entranceId);
    }


}

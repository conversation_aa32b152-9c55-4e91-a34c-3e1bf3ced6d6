package com.stbella.order.domain.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.stbella.core.result.Result;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.enums.production.*;
import com.stbella.order.common.exception.ApplicationException;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.common.utils.JsonUtil;
import com.stbella.order.domain.order.month.entity.GoodsCategoryFrontEntity;
import com.stbella.order.domain.order.production.*;
import com.stbella.order.domain.order.service.ProductionAppointmentDomainService;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.order.month.constant.BaseConstant;
import com.stbella.order.server.order.production.req.AppointmentBehalfConsumerQuery;
import com.stbella.order.server.order.production.req.AppointmentBehalfTImeQuery;
import com.stbella.order.server.order.production.req.AppointmentServiceFeeByTherapistIdAndSkuIDQuery;
import com.stbella.order.server.order.production.res.*;
import com.stbella.store.common.enums.core.ServiceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09 11:47
 */
@Component
@Slf4j
public class ProductionAppointmentDomainServiceImpl implements ProductionAppointmentDomainService {

    @Resource
    ProductionInstrStoreExtRepository productionInstrStoreExtRepository;
    @Resource
    private ProductionAppointmentRepository appointmentRepository;
    @Resource
    private GoodsSkuRepository goodsSkuRepository;
    @Resource
    private OrderProductionExtendRepository orderProductionExtendRepository;
    @Resource
    private OrderProductionCardExtendRepository orderProductionCardExtendRepository;
    @Resource
    private OrderGiftExtendRepository orderGiftExtendRepository;
    @Resource
    private GoodsRepository goodsRepository;
    @Resource
    private TherapistRepository therapistRepository;
    @Resource
    private TherapistScheduleRepository therapistScheduleRepository;
    @Resource
    private GoodsCategoryFrontRepository goodsCategoryFrontRepository;

    @Override
    public List<Integer> distinctSkuIds(List<OrderProductionAppointmentEntity> productionAppointmentEntities) {
        if (CollectionUtils.isEmpty(productionAppointmentEntities)) {
            return Lists.newArrayList();
        }

        return productionAppointmentEntities.stream().map(OrderProductionAppointmentEntity::getProductionSkuId).distinct().collect(Collectors.toList());
    }

    @Override
    public Result<List<ProductionBehalfTimeShaftVo>> behalfTimeInfo(AppointmentBehalfTImeQuery query) {
        List<ProductionBehalfTimeShaftVo> result = new ArrayList<>();

        Date
                start = query.getStartDate(),
                end = query.getEndDate();

        Long
                //仪器ID
                productionInstrId = query.getProductionInstrId(),
                //产康师Id
                therapistId = query.getTherapistId();

        Integer
                //服务时长
                serviceTime = query.getServiceTime(),
                //门店Id
                storeId = query.getStoreId(),
                //0：预约；1：提交
                apiType = null == query.getApiType() ? 0 : query.getApiType(),
                //终端类型 1=（客户自己下单）;2=（销售代下单）
                bookType = query.getBookType();

        Boolean
                //是否是客户自己预约
                customerBook = ProductionBookTypeEnum.CUSTOMER_BOOK.code().equals(bookType);


        //产康师情况
        List<OrderProductionAppointmentEntity> orderProductionAppointmentEntities = appointmentRepository.queryAppointmentByTherapistIdAndDate(query.getTherapistId(), start, end, query.getProductionAppointmentId());
        //当前客户的预约情况
        List<OrderProductionAppointmentEntity> orderProductionAppointmentEntitiesClent = appointmentRepository.queryAppointmentByProductionClientIdAndDate(query.getClientId(), start, end, query.getProductionAppointmentId());
        //产康师可预约时间（用户自己预约的时候需要考虑产康师自己设置的可预约的时间）
        List<TherapistScheduleEntity> therapistScheduleList = therapistScheduleRepository.therapistScheduleListByTherapistId(customerBook ? therapistId : -1);

        if (customerBook && apiType == 0) {
            //如果是客户自己预约，那么就需要提前1个小时（预约页面需要，提交预约不需要），增加模拟订单排除订单
            OrderProductionAppointmentEntity orderProductionAppointmentEntity = new OrderProductionAppointmentEntity();

            Date
                    //模拟服务开始时间
                    serveStart,
                    //模拟服务结束时间
                    serveEnd,
                    now = new Date();

            int minute = DateUtil.minute(now);

            int tempMinute = minute;

            if (tempMinute > 5) {
                tempMinute = minute % 5;
            }
            int plusMinute = tempMinute - 5;
            serveStart = DateUtil.offsetMinute(now, Math.abs(plusMinute) - 5).setField(DateField.SECOND, 0).setField(DateField.MILLISECOND, 0);
            serveEnd = DateUtil.offsetMinute(now, Math.abs(plusMinute) + 60).setField(DateField.SECOND, 0).setField(DateField.MILLISECOND, 0);

            orderProductionAppointmentEntity.setServeStart(serveStart);
            orderProductionAppointmentEntity.setServeEnd(serveEnd);
            //新建模拟单
            orderProductionAppointmentEntitiesClent.add(orderProductionAppointmentEntity);
        }

        for (OrderProductionAppointmentEntity orderProductionAppointmentEntity : orderProductionAppointmentEntities) {
            if (
                //客户自己预约
                    customerBook &&
                            //不是客户自己
                            !orderProductionAppointmentEntity.getClientId().equals(query.getClientId()) &&
                            //产康师是同一个
                            orderProductionAppointmentEntity.getTherapistId().equals(query.getTherapistId())
            ) {
                //就将订单时间前后延长15分钟
                orderProductionAppointmentEntity.setServeStart(new Date(orderProductionAppointmentEntity.getServeStart().getTime() - 900000L));
                orderProductionAppointmentEntity.setServeEnd(new Date(orderProductionAppointmentEntity.getServeEnd().getTime() + 900000L));
            }
        }

        //仪器使用情况
        List<OrderProductionAppointmentEntity> orderProductionAppointmentEntityList = appointmentRepository.queryAppointmentByProductionInstrIdAndDate(query.getProductionInstrId(), start, end, storeId);
        //获取当前仪器在当前门店下的数量
        Integer instrNum = productionInstrStoreExtRepository.selectInstrCountByStoreIdAndInstrId(productionInstrId, storeId);

        List<Date> dayByStartAndEnd = DateUtils.getDayByStartAndEnd(start, end);

        Date curDate = new Date();

        for (int i = 0; i < dayByStartAndEnd.size(); i++) {
            //可用时间段
            List<ProductionBehalfTimeQuantumVo> usableQuantumVoList = new ArrayList<>();
            //不可用时间段
            List<ProductionBehalfTimeQuantumVo> unableQuantumVoList = new ArrayList<>();

            //初始化对象
            ProductionBehalfTimeVo of = ProductionBehalfTimeVo.of(dayByStartAndEnd.get(i), bookType);

            List<List<ProductionBehalfTimeMinuteQuantumVo>> usable = new ArrayList<>();

            //所有不可预约
            List<ProductionBehalfTimeMinuteQuantumVo> allUnable = new ArrayList<>();
            //所有空闲
            List<ProductionBehalfTimeMinuteQuantumVo> allUsable = new ArrayList<>();

            //根据时间和器材使用情况做初筛
            of.getProductionBehalfTimeHourQuantumVoList().forEach(hour -> {
                List<ProductionBehalfTimeMinuteQuantumVo> productionBehalfTimeMinuteQuantumVoList = hour.getProductionBehalfTimeMinuteQuantumVoList();
                //根据小时
                productionBehalfTimeMinuteQuantumVoList.forEach(minute -> {
                    //根据分钟
                    Date minuteStart = minute.getStart();
                    Date minuteEnd = minute.getEnd();
                    //判断这个时间内产康师有没有预约
                    List<OrderProductionAppointmentEntity> therapistList = orderProductionAppointmentEntities.stream().filter(order -> minuteStart.compareTo(order.getServeStart()) >= 0 && minuteEnd.compareTo(order.getServeEnd()) <= 0).collect(Collectors.toList());
                    //判断这个时间内当前客户是否有预约
                    List<OrderProductionAppointmentEntity> clientList = orderProductionAppointmentEntitiesClent.stream().filter(order -> minuteStart.compareTo(order.getServeStart()) >= 0 && minuteEnd.compareTo(order.getServeEnd()) <= 0).collect(Collectors.toList());

//                    if ((minuteStart.compareTo(new Date()) <= 0 || minuteEnd.compareTo(new Date()) <= 0) && Objects.equals(query.getAppointmentType(), ProductionAppointmentTypeEnum.APPOINTMENT_TYPE_ONE.code())) {
                    if (((minuteStart.compareTo(curDate) <= 0 || minuteEnd.compareTo(curDate) <= 0) && !Objects.equals(query.getAppointmentType(), ProductionAppointmentTypeEnum.APPOINTMENT_TYPE_TWO.code())) && false) {
                        //时间段小于当前时间
                        minute.setAppointment(OrderPropertyAppointmentStatusEnum.TIME_REPETITION.code());
                    } else {
                        if (CollectionUtils.isNotEmpty(therapistList) || CollectionUtils.isNotEmpty(clientList)) {
                            //这个时间内被占用（产康师是否有预约||用户是否有预约）
                            minute.setAppointment(OrderPropertyAppointmentStatusEnum.TIME_REPETITION.code());
                        } else if (instrNum == 0 && query.getProductionInstrId() > 0) {
                            //当前门店没有服务对应的器材
                            minute.setAppointment(OrderPropertyAppointmentStatusEnum.NO_EQUIPMENT_AVAILABLE.code());
                        }else {
                            //获取这个时间段内仪器使用数
                            List<OrderProductionAppointmentEntity> appointmentEntityList = orderProductionAppointmentEntityList.stream().filter(order -> minuteStart.compareTo(order.getServeStart()) >= 0 && minuteEnd.compareTo(order.getServeEnd()) <= 0).collect(Collectors.toList());
                            //当前已经预约的数量所使用的仪器大于了实际拥有的 || 已经预约的数量>门店拥有的仪器的数量
                            if (query.getProductionInstrId() > 0 && appointmentEntityList.size() >= instrNum && orderProductionAppointmentEntityList.size() >= instrNum) {
                                minute.setAppointment(OrderPropertyAppointmentStatusEnum.NO_EQUIPMENT_AVAILABLE.code());
                            } else {
                                //如果是用户自己预约
                                if (customerBook) {
                                    //判断产康师自己有没有设置在这个时间段可预约
                                    List<TherapistScheduleEntity> collect = therapistScheduleList.stream().filter(t -> minuteStart.compareTo(t.getMargeStartTime()) >= 0 && minuteEnd.compareTo(t.getMargeEndTime()) <= 0).collect(Collectors.toList());
                                    if (CollectionUtils.isEmpty(collect)) {
                                        //这个时间内被占用（产康师自己设置的）
                                        minute.setAppointment(OrderPropertyAppointmentStatusEnum.TIME_REPETITION.code());
                                    } else {
                                        //时间没被占用器材足够，暂时可状态可预约
                                        minute.setAppointment(OrderPropertyAppointmentStatusEnum.RESERVABLE.code());
                                    }
                                } else {
                                    //时间没被占用器材足够，暂时可状态可预约
                                    minute.setAppointment(OrderPropertyAppointmentStatusEnum.RESERVABLE.code());
                                }

                            }
                        }
                    }

                    // 补单类型 全部时间可选
                    if (Objects.equals(query.getAppointmentType(), ProductionAppointmentTypeEnum.APPOINTMENT_TYPE_TWO.code())) {
                        allUsable.add(minute);
                    }else {
                        if (minute.getAppointment() != 0) {
                            allUnable.add(minute);
                        } else {
                            allUsable.add(minute);
                        }
                    }
                });
                //获取可预签约的段
                usable.add(productionBehalfTimeMinuteQuantumVoList.stream().filter(p -> p.getAppointment().equals(OrderPropertyAppointmentStatusEnum.RESERVABLE.code())).collect(Collectors.toList()));
            });

            Map<Date, Date> timeRange = allUsable.stream().collect(Collectors.toMap(ProductionBehalfTimeMinuteQuantumVo::getStart, ProductionBehalfTimeMinuteQuantumVo::getEnd));

            //获取合并后的时间段
            timeRange = DateUtils.mergingTimeRange(timeRange);

            for (Date timeRangeStart : timeRange.keySet()) {
                Date timeRangeEnd = timeRange.get(timeRangeStart);

                long between = DateUtil.between(timeRangeStart, timeRangeEnd, DateUnit.MINUTE);
                //区间时间大于服务时间
                if (between >= serviceTime) {
                    //可用
                    ProductionBehalfTimeQuantumVo productionBehalfTimeQuantumVo = new ProductionBehalfTimeQuantumVo();
                    productionBehalfTimeQuantumVo.setStart(timeRangeStart);
                    productionBehalfTimeQuantumVo.setEnd(timeRangeEnd);
                    productionBehalfTimeQuantumVo.setMinutes(DateUtil.between(timeRangeStart, timeRangeEnd, DateUnit.MINUTE));
                    usableQuantumVoList.add(productionBehalfTimeQuantumVo);
                } else {
                    //不可用，判断哪些时间在这个之内
                    allUsable.stream().forEach(a -> {
                        if (a.getStart().compareTo(timeRangeStart) >= 0 && a.getEnd().compareTo(timeRangeEnd) <= 0) {
                            a.setAppointment(OrderPropertyAppointmentStatusEnum.INSUFFICIENT_TIME_LEFT.code());
                            allUnable.add(a);
                        }
                    });
                }
            }

            Map<Date, Date> unTimeRange = allUnable.stream().collect(Collectors.toMap(ProductionBehalfTimeMinuteQuantumVo::getStart, ProductionBehalfTimeMinuteQuantumVo::getEnd));

            unTimeRange = DateUtils.mergingTimeRange(unTimeRange);

            for (Date timeRangeStart : unTimeRange.keySet()) {
                Date timeRangeEnd = unTimeRange.get(timeRangeStart);
                ProductionBehalfTimeQuantumVo productionBehalfTimeQuantumVo = new ProductionBehalfTimeQuantumVo();
                productionBehalfTimeQuantumVo.setStart(timeRangeStart);
                productionBehalfTimeQuantumVo.setEnd(timeRangeEnd);
                productionBehalfTimeQuantumVo.setMinutes(DateUtil.between(timeRangeStart, timeRangeEnd, DateUnit.MINUTE));
                unableQuantumVoList.add(productionBehalfTimeQuantumVo);
            }

//            log.info("时间：" + of.getDate());
//            log.info("具体数据：" + of);

            ProductionBehalfTimeShaftVo productionBehalfTimeShaftVo = new ProductionBehalfTimeShaftVo();
            productionBehalfTimeShaftVo.setAppointment(of.getAppointment());
            productionBehalfTimeShaftVo.setDate(new SimpleDateFormat(DateUtils.YYYY_MM_DD).format(of.getDate()));
            productionBehalfTimeShaftVo.setUnableQuantumVoList(unableQuantumVoList);
            productionBehalfTimeShaftVo.setUsableQuantumVoList(usableQuantumVoList);
            productionBehalfTimeShaftVo.setAppointment(usableQuantumVoList.size() > 0 ? 1 : 0);
            productionBehalfTimeShaftVo.setAutoChoose(0);

            if (CollectionUtils.isNotEmpty(usableQuantumVoList)) {
                //获取下拉时间
                List<ProductionBehalfTimeStrQuantumVo> appointmentTimeDropDownList = new ArrayList<>();

                for (int j = 0; j < usableQuantumVoList.size(); j++) {

                    ProductionBehalfTimeQuantumVo productionBehalfTimeQuantumVo = usableQuantumVoList.get(j);

                    Date s = productionBehalfTimeQuantumVo.getStart();
                    Date e = productionBehalfTimeQuantumVo.getEnd();

                    ProductionBehalfTimeStrQuantumVo p = new ProductionBehalfTimeStrQuantumVo();
                    Date ps = productionBehalfTimeQuantumVo.getStart();
                    Date pe = new Date(productionBehalfTimeQuantumVo.getStart().getTime() + serviceTime * 60000L);
                    p.setStart(new SimpleDateFormat("HH:mm").format(ps));
                    p.setEnd(new SimpleDateFormat("HH:mm").format(pe));
                    p.setMinutes(DateUtil.between(ps, pe, DateUnit.MINUTE));
                    appointmentTimeDropDownList.add(p);
                    while (s.compareTo(e) < 0) {
                        s = new Date(s.getTime() + 300000L);
                        if (s.getTime() + serviceTime * 60000L <= e.getTime()) {
                            ProductionBehalfTimeStrQuantumVo newP = new ProductionBehalfTimeStrQuantumVo();
                            Date npe = new Date(s.getTime() + serviceTime * 60000L);
                            newP.setStart(new SimpleDateFormat("HH:mm").format(s));
                            newP.setEnd(new SimpleDateFormat("HH:mm").format(npe));
                            newP.setMinutes(DateUtil.between(s, npe, DateUnit.MINUTE));
                            appointmentTimeDropDownList.add(newP);
                        }
                    }
                }
                productionBehalfTimeShaftVo.setAppointmentTimeDropDownList(appointmentTimeDropDownList);

                List<ProductionBehalfTimeStrQuantumVo> collect = appointmentTimeDropDownList.stream().filter(u -> {
                    String startStr = new SimpleDateFormat(DateUtils.YYYY_MM_DD).format(of.getDate()) + " " + u.getStart();
                    String endStr = new SimpleDateFormat(DateUtils.YYYY_MM_DD).format(of.getDate()) + " " + u.getEnd();

                    int sHours = DateUtil.parse(startStr, "YYYY-MM-dd HH:mm").getHours();
                    int eHours = DateUtil.parse(endStr, "YYYY-MM-dd HH:mm").getHours();
                    return sHours >= 9 && eHours < 18 && eHours != 0;

                }).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    //有预约时间
                    productionBehalfTimeShaftVo.setAutoChoose(1);
                    productionBehalfTimeShaftVo.setAutoStart(collect.get(0).getStart());
                    productionBehalfTimeShaftVo.setAutoEnd(collect.get(0).getEnd());
                }
            }
            result.add(productionBehalfTimeShaftVo);
        }

        return Result.success(result);
    }

    @Override
    public Integer determineAppointmentAvailable(AppointmentBehalfTImeQuery query) {
        query.setBookType(ProductionBookTypeEnum.HELP_CUSTOMER_BOOK.code());
        Date startDate = query.getStartDate();
        Date endDate = query.getEndDate();

        if (endDate.compareTo(DateUtil.beginOfDay(endDate)) == 0) {
            endDate = new Date(endDate.getTime() - 1000L);
        }

        query.setStartDate(DateUtil.beginOfDay(startDate));
        query.setEndDate(DateUtil.endOfDay(startDate));

        Result<List<ProductionBehalfTimeShaftVo>> listResult = this.behalfTimeInfo(query);

        ProductionBehalfTimeShaftVo productionBehalfTimeShaftVo = listResult.getData().get(0);
        List<ProductionBehalfTimeQuantumVo> usableQuantumVoList = productionBehalfTimeShaftVo.getUsableQuantumVoList();

        if (usableQuantumVoList.size() == 0) {
            return 0;
        }

        for (ProductionBehalfTimeQuantumVo productionBehalfTimeQuantumVo : usableQuantumVoList) {
            //判断入参的时间是否在那天的可用时间内
            if (startDate.compareTo(productionBehalfTimeQuantumVo.getStart()) >= 0 && endDate.compareTo(productionBehalfTimeQuantumVo.getEnd()) <= 0) {
                return 1;
            }
        }
        return 0;
    }

    @Override
    public Result<ProductionAppointmentServiceFeeVo> getServiceFeeByTherapistIdAndSkuID(AppointmentServiceFeeByTherapistIdAndSkuIDQuery query) {
        ProductionAppointmentServiceFeeVo productionAppointmentServiceFeeVo = new ProductionAppointmentServiceFeeVo();
        BigDecimal result = BigDecimal.ZERO;

        // 当服务方式是三方是，服务费默认是0
        if (!Objects.equals(query.getServeType(), ServiceTypeEnum.THIRD_PARTY.code())) {
            //获取服务时长（根据he_goods_sku）
            GoodsSkuEntity goodsSkuEntity = goodsSkuRepository.selectById(query.getProductionSkuId());

            if (ObjectUtil.isNotEmpty(goodsSkuEntity)) {
                String producerConfig = goodsSkuEntity.getProducerConfig();
                if (StringUtils.isNotEmpty(producerConfig)) {
                    List<LinkedHashMap> read = JsonUtil.read(producerConfig, List.class);
                    if (CollectionUtils.isNotEmpty(read)) {
                        for (LinkedHashMap r : read) {
                            //等级+1
                            if (r.get("level").equals(query.getGradeId() + 1)) {
                                result = new BigDecimal(r.get("price").toString());
                            }
                        }
                    }
                }
            }
        }

        productionAppointmentServiceFeeVo.setServeFee(result.multiply(new BigDecimal(100)).intValue());
        DecimalFormat decimalFormat = new DecimalFormat("0.00#");
        productionAppointmentServiceFeeVo.setShowServeFee(decimalFormat.format(result));
        return Result.success(productionAppointmentServiceFeeVo);
    }

    @Override
    public Result<ProductionAppointmentGoodsSkuInfoVo> behalfGoodsInfo(AppointmentBehalfConsumerQuery query) {
        String
                //商品名称
                goodsName = "",
                //sku名称
                skuName = "",
                //通次卡单项服务名称
                serviceName = "",
                //通次卡的组合名称
                combinationName = "",
                //通卡次卡名称 单项服务为空
                groupGoodsName = "",
                //通卡分组名称
                groupName = "";

        Integer
                //价格
                price = 0,
                //服务时长
                serviceTime = 0,
                //服务类型 0:自营 1:三方
                goodsServiceType = -1,
                //资产来源
                propertySource = query.getPropertySource(),
                //资产ID
                propertyId = query.getPropertyId(),
                //单向服务SKU ID
                skuId = query.getSkuId(),
                //商品Id
                goodsId = null,
                //通卡次卡id 单项服务为0
                groupGoodsId = 0,
                //通卡分组id, 次卡/单项服务为0
                groupId = 0,
                //关联订单id
                orderId = 0,
                //用户basic id
                basicId = 0,
                //服务类型
                serveType = 0,
                //服务时长
                serveTime = 0;

        Long
                //次卡有效期开始时间
                validStartTime = null,
                //次卡有效期结束时间
                validEndTime = null;

        if (OrderProductionTypeEnum.BUY.code().equals(propertySource)) {
            Integer serviceType = query.getItemType();

            OrderProductionExtendEntity orderProductionExtendEntity = orderProductionExtendRepository.getById(propertyId);

            //资产不存在或者非当前用户
            if (ObjectUtil.isEmpty(orderProductionExtendEntity) /*|| !orderProductionExtendEntity.getBasicId().equals(query.getBasicId())*/) {
                throw new ApplicationException(ErrorCodeEnum.SYSTEM_ERROR.code(), "用户资产异常，请返回上一页面");
            }

            basicId = orderProductionExtendEntity.getBasicId();
            orderId = orderProductionExtendEntity.getOrderId();

            if (OrderProductionItemEnum.TYPE_COUNT.code().equals(serviceType) || OrderProductionItemEnum.TYPE_GROUP.code().equals(serviceType)) {
                //从he_order_production_card_extend的json数据中取sku的数据
                List<OrderProductionCardExtendEntity> orderProductionCardExtendEntities = orderProductionCardExtendRepository.selectByorderProductionId(propertyId);

                if (CollectionUtils.isEmpty(orderProductionCardExtendEntities)) {
                    throw new ApplicationException(ErrorCodeEnum.SYSTEM_ERROR.code(), "用户资产异常，请返回上一页面");
                }

                LinkedHashMap skuInfoMap = null;

                if (OrderProductionItemEnum.TYPE_GROUP.code().equals(serviceType)) {

                    if (CollectionUtil.isNotEmpty(orderProductionCardExtendEntities)) {


                        for (OrderProductionCardExtendEntity orderProductionCardExtendEntity : orderProductionCardExtendEntities) {

                            //数组
                            List<LinkedHashMap> read = JsonUtil.read(orderProductionCardExtendEntity.getContent(), List.class);

                            if (CollectionUtil.isNotEmpty(read)) {
                                Optional<LinkedHashMap> skuInfo = read.stream().filter(r -> skuId.equals(r.get("sku_id"))).findFirst();
                                if (skuInfo.isPresent()) {
                                    skuInfoMap = skuInfo.get();
                                    validStartTime = orderProductionCardExtendEntity.getValidStartTime();
                                    validEndTime = orderProductionCardExtendEntity.getValidEndTime();
                                }
                            }

                        }


                    }

                    // TODO combinationName有什么用？所有字段都应该按照数据库字段同名返回，避免造成歧义，serviceName也没什么用 @hanwei
                    // TODO groupGoodsId 通次卡商品id groupGoodsName 通次卡商品名称 groupId 分组id groupName分组名称
                    // TODO 单项服务：productionGoodsId、productionGoodsName、productionSkuId、productionSkuName 以上字段就能涵盖所有名称id展示
                    combinationName = orderProductionExtendEntity.getGoodsName();

                    Optional<OrderProductionCardExtendEntity> first = orderProductionCardExtendEntities.stream()
                            .filter(o -> Objects.equals(o.getId(), query.getOrderProductionCardExtendId()))
                            .findFirst();
                    if (!first.isPresent()) {
                        log.error("通次卡资产异常，未找到资产OrderProductionCardExtendId:{}", query.getOrderProductionCardExtendId());
                        throw new ApplicationException(ErrorCodeEnum.BIZ_ERROR.code(), "用户资产异常，请返回上一页面");
                    }
                    OrderProductionCardExtendEntity entity = first.get();
                    groupId = entity.getGroupId();
                    groupName = entity.getName();

                } else {
                    if (CollectionUtil.isNotEmpty(orderProductionCardExtendEntities)) {
                        for (OrderProductionCardExtendEntity orderProductionCardExtendEntity : orderProductionCardExtendEntities) {
                            LinkedHashMap read = JsonUtil.read(orderProductionCardExtendEntity.getContent(), LinkedHashMap.class);
                            if (skuId.equals(read.get("sku_id"))) {
                                skuInfoMap = read;
                                combinationName = orderProductionCardExtendEntity.getName();
                                validStartTime = orderProductionCardExtendEntity.getValidStartTime();
                                validEndTime = orderProductionCardExtendEntity.getValidEndTime();
                            }
                        }
                    }
                }
                if (ObjectUtil.isNotNull(skuInfoMap)) {
                    skuName = (String) skuInfoMap.get("sku_name");
                    goodsName = (String) skuInfoMap.get("goods_name");
                    goodsId = (Integer) skuInfoMap.get("goods_id");
                    serveType = (Integer) skuInfoMap.get("serve_type");
                    serveTime = (Integer) skuInfoMap.get("service_time");
                }

                if (ObjectUtil.isNotNull(orderProductionExtendEntity)) {
                    price = orderProductionExtendEntity.getPrice();
                    serviceName = orderProductionExtendEntity.getGoodsName();
                }

                groupGoodsId = orderProductionExtendEntity.getGoodsId();
                groupGoodsName = orderProductionExtendEntity.getGoodsName();

            } else {
                skuName = orderProductionExtendEntity.getSkuName();
                goodsName = orderProductionExtendEntity.getGoodsName();
                goodsId = orderProductionExtendEntity.getGoodsId();
                price = orderProductionExtendEntity.getPrice();
                serveType = orderProductionExtendEntity.getAppointmentAssertsEntity().getServeType();
                serveTime = orderProductionExtendEntity.getAppointmentAssertsEntity().getServiceTime();
            }
        } else if (OrderProductionTypeEnum.GIFT.code().equals(propertySource)) {
            //从he_order_gift_extend中且type=6取sku的数据
            OrderGiftExtendEntity orderGiftExtendEntity = orderGiftExtendRepository.selectByIdAndType(propertyId, OrderGiftExtendTypeEnum.INDUSTRIAL_HEALTH_SERVICE.code());

            //资产不存在或者非当前用户
            if (ObjectUtil.isEmpty(orderGiftExtendEntity) /*|| !orderGiftExtendEntity.getBasicId().equals(query.getBasicId())*/) {
                throw new ApplicationException(ErrorCodeEnum.SYSTEM_ERROR.code(), "用户资产异常，请返回上一页面");
            }

            serveType = orderGiftExtendEntity.getServeType();
            serveTime = orderGiftExtendEntity.getServiceTime();
            basicId = orderGiftExtendEntity.getBasicId();
            orderId = orderGiftExtendEntity.getOrderId();
            skuName = orderGiftExtendEntity.getSkuName();
            goodsName = orderGiftExtendEntity.getGoodsName();
            goodsId = orderGiftExtendEntity.getGoodsId();
            price = orderGiftExtendEntity.getPrice();
            validStartTime = orderGiftExtendEntity.getValidStartTime();
            validEndTime = orderGiftExtendEntity.getValidEndTime();

        }


        ProductionAppointmentGoodsSkuInfoVo productionAppointmentGoodsSkuInfoVo = new ProductionAppointmentGoodsSkuInfoVo();
        productionAppointmentGoodsSkuInfoVo.setProductionInstrId(0L);
        Map<Integer, GoodsCategoryFrontEntity> backCateFrontMap = goodsCategoryFrontRepository.queryAllCategory().stream().collect(Collectors.toMap(GoodsCategoryFrontEntity::getId, Function.identity(), (item1, item2) -> item1));


        //获取项目类型（根据he_goods表）
        GoodsEntity goodsEntity = goodsRepository.selectById(goodsId);
        if (ObjectUtil.isNotEmpty(goodsEntity)) {
            productionAppointmentGoodsSkuInfoVo.setProductionInstrId(goodsEntity.getProductionInstrId());
            productionAppointmentGoodsSkuInfoVo.setCategoryFrontTitle(getFrontCategoryName(backCateFrontMap, goodsEntity.getCategoryFront()));
            productionAppointmentGoodsSkuInfoVo.setGoodsImage(StringUtils.isNotBlank(goodsEntity.getImage()) ? goodsEntity.getImage() : BaseConstant.PRODUCTION_GOODS_IMAGE_DEFAULT);
        }

        productionAppointmentGoodsSkuInfoVo.setServeType(serveType);
        productionAppointmentGoodsSkuInfoVo.setServeTime(GoodsSkuServiceTImeEnum.fromCode(serveTime));
        productionAppointmentGoodsSkuInfoVo.setProductionGoodsId(goodsId);
        productionAppointmentGoodsSkuInfoVo.setProductionGoodsName(goodsName);
        productionAppointmentGoodsSkuInfoVo.setProductionSkuId(skuId);
        productionAppointmentGoodsSkuInfoVo.setProductionSkuName(skuName);

        productionAppointmentGoodsSkuInfoVo.setGroupGoodsId(groupGoodsId);
        productionAppointmentGoodsSkuInfoVo.setGroupGoodsName(groupGoodsName);
        productionAppointmentGoodsSkuInfoVo.setGroupId(groupId);
        productionAppointmentGoodsSkuInfoVo.setGroupName(groupName);
        productionAppointmentGoodsSkuInfoVo.setItemType(query.getItemType());

        productionAppointmentGoodsSkuInfoVo.setServiceName(serviceName);
        productionAppointmentGoodsSkuInfoVo.setCombinationName(combinationName);
        productionAppointmentGoodsSkuInfoVo.setPrice(price);
        productionAppointmentGoodsSkuInfoVo.setPropertySource(propertySource);
        productionAppointmentGoodsSkuInfoVo.setPropertyId(propertyId);

        productionAppointmentGoodsSkuInfoVo.setValidStartTime(validStartTime);
        productionAppointmentGoodsSkuInfoVo.setValidEndTime(validEndTime);

        productionAppointmentGoodsSkuInfoVo.setOrderId(orderId);
        productionAppointmentGoodsSkuInfoVo.setOrderProductionCardExtendId(query.getOrderProductionCardExtendId());
        productionAppointmentGoodsSkuInfoVo.setBasicId(basicId);
        productionAppointmentGoodsSkuInfoVo.setItemTypeName(OrderProductionItemEnum.of(query.getItemType()).desc());
        if (OrderProductionItemEnum.TYPE_PRODUCTION.code().equals(query.getItemType())) {
            productionAppointmentGoodsSkuInfoVo.setGroupGoodsName(goodsName);
        }
        return Result.success(productionAppointmentGoodsSkuInfoVo);
    }

    public String getFrontCategoryName(Map<Integer, GoodsCategoryFrontEntity> backCateFrontMap, Integer categoryId) {
        GoodsCategoryFrontEntity entity = backCateFrontMap.get(categoryId);
        if (Objects.isNull(entity)) {
            return "";
        }
        return entity.getName();
    }
}

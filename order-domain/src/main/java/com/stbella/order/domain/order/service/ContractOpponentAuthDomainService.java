package com.stbella.order.domain.order.service;

import com.stbella.core.base.Operator;
import com.stbella.core.result.Result;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-05-29  15:57
 * @Description:  订单合同相关方认证服务
 */
public interface ContractOpponentAuthDomainService {

    /**
     * 查询订单合同相关方认证状态
     * @param entity
     * @return
     */
    Boolean getAuthState(HeOrderEntity entity);
}

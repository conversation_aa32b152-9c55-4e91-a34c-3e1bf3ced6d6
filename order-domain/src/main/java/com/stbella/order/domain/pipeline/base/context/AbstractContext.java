package com.stbella.order.domain.pipeline.base.context;


import com.stbella.order.domain.pipeline.base.filters.AbstractFilter;
import com.stbella.order.domain.pipeline.base.filters.selector.FilterSelector;
import com.stbella.order.domain.pipeline.base.filters.selector.LocalBasedFilterSelector;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 功能描述:上下文
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/8/24.
 */
@Data
@NoArgsConstructor
public abstract class AbstractContext<F extends AbstractFilter> implements Context<F> {

    private FilterSelector<F> selector = new LocalBasedFilterSelector<>();

    private final Map<String, Object> payload = new HashMap<>();

    private boolean isTransactionManagerCtrl = false;

    private boolean isAsyncVerify = true;

    private String lockKey = null;

    public AbstractContext(FilterSelector<F> selector) {
        this.selector = selector;
    }

    public AbstractContext<F> genPayloadData(String key, Object o) {
        this.payload.put(key, o);
        return this;
    }

    public AbstractContext<F> lockKey(String lockKey) {
        if (StringUtils.isNotBlank(lockKey)) {
            this.lockKey = lockKey;
        }
        return this;
    }

    public AbstractContext<F> isTransactionManagerCtrl(boolean flg) {
        this.isTransactionManagerCtrl = flg;
        return this;
    }

    public Object loadPayloadData(String key) {
        return this.payload.get(key);
    }

    @Override
    public FilterSelector<F> getFilterSelector() {
        return this.selector;
    }
}

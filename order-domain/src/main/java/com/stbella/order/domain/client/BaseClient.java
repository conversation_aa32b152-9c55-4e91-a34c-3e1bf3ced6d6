package com.stbella.order.domain.client;


import com.stbella.base.server.common.UploadService;
import com.stbella.base.server.common.request.RealPaidClientUploadReq;
import com.stbella.base.server.common.request.RealPaidSummaryUploadReq;
import com.stbella.base.server.common.request.RealPaidUploadReq;
import com.stbella.base.server.sms.Sms;
import com.stbella.base.server.sms.SmsService;
import com.stbella.base.server.sms.enums.SmsAppEnum;
import com.stbella.base.server.sms.enums.SmsSignEnum;
import com.stbella.base.server.sms.enums.SmsTemplateV2Enum;
import com.stbella.base.server.sms.request.SmsRequest;
import com.stbella.scrm.request.ScrmSendMessageRequest;
import com.stbella.scrm.service.ScrmMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class BaseClient {

    @DubboReference
    private SmsService iSmsService;

    @DubboReference(timeout = 7000)
    private ScrmMessageService scrmMessageService;
    @DubboReference(timeout = 10000)
    private UploadService uploadService;

    public boolean sendMessage(String phone, SmsAppEnum smsAppEnum, SmsSignEnum smsSignEnum, SmsTemplateV2Enum smsTemplateV2Enum, String[] params) {
        Sms sms = Sms.start(phone)
                //选择应用
                .setMessageApp().babyBella()
                //选择签名
                .setMessageSign().piAida()
                //选择短信模板并设置内容
                .setMessageTemplate().setSmsTemplateV2Enum(smsTemplateV2Enum).setContent(params);

        return iSmsService.sendMessage(sms);
    }

    public boolean sendMessage(String phone, SmsTemplateV2Enum smsTemplateV2Enum, String[] params) {
        SmsRequest smsRequest = new SmsRequest();
        smsRequest.setPhone(phone);
        smsRequest.setSmsAppEnum(SmsAppEnum.BABY_BELLA);
        smsRequest.setSmsSignEnum(SmsSignEnum.PI_AIDA);
        smsRequest.setSmsTemplateV2Enum(smsTemplateV2Enum);
        smsRequest.setParams(params);
        return iSmsService.sendMessage(smsRequest);
    }

    public boolean sendMessage(String phone, String content, String customerName, Integer appId) {

        try {
            log.info("send message to phone: {}, content: {}, customerName: {}, appId: {}", phone, content, customerName, appId);
            ScrmSendMessageRequest scrmSendMessageRequest = new ScrmSendMessageRequest();
            scrmSendMessageRequest.setContent(content);
            scrmSendMessageRequest.setTouser(Collections.singletonList(phone));
            scrmSendMessageRequest.setCustomerName(customerName);
            scrmSendMessageRequest.setAppId(appId);
            Boolean sendResult = scrmMessageService.sendMessage(scrmSendMessageRequest);
            log.info("send message result: {}", sendResult);
            return sendResult;
        } catch (Exception e){
            log.error("send message error: {}", e.getMessage());
            return false;
        }
    }

    public String uploadRealPaidUpload(String startDate, String endDate, List<RealPaidUploadReq> req) {
        try {
            return uploadService.uploadRealPaidUpload(startDate, endDate, req);
        } catch (Exception e) {
            log.error("uploadRealPaidUpload error, startDate:{}, endDate:{}", startDate, endDate, e);
            throw e;
        }
    }

    public String uploadRealPaidSummaryUpload(String startDate, String endDate, List<RealPaidSummaryUploadReq> req) {
        try {
            return uploadService.uploadRealPaidSummaryUpload(startDate, endDate, req);
        } catch (Exception e) {
            log.error("uploadRealPaidSummaryUpload error, startDate:{}, endDate:{}", startDate, endDate, e);
            throw e;
        }
    }

    public String uploadClientRealPaidUpload(String startDate, String endDate, List<RealPaidClientUploadReq> req) {
        try {
            return uploadService.uploadClientRealPaidUpload(startDate, endDate, req);
        } catch (Exception e) {
            log.error("uploadClientRealPaidUpload error, startDate:{}, endDate:{}", startDate, endDate, e);
            throw e;
        }
    }
}


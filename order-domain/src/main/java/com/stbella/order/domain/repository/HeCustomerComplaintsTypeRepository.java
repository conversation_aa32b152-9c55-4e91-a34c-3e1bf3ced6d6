package com.stbella.order.domain.repository;

import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsTypeEntity;

import java.util.List;

public interface HeCustomerComplaintsTypeRepository {

    /**
     * 根据父ID获取客诉类型
     * @param parentIdList
     * @return
     */
    List<HeCustomerComplaintsTypeEntity> getListByParentId(List<Long> parentIdList);

    /**
     * 获取所有客诉类型
     * @return
     */
    List<HeCustomerComplaintsTypeEntity> getAll();

    HeCustomerComplaintsTypeEntity getOne(Long id);

}

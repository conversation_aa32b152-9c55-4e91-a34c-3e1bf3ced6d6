package com.stbella.order.domain.order.production;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 产康门店仪器
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductionInstrStoreEntity implements Serializable {

    private static final long serialVersionUID = -6291269356659791644L;

    /**
     *
     */
    private Long id;

    /**
     * 仪器id
     */
    private Long instrId;

    /**
     * 门店ID：0店铺类型下所有店铺可用的套餐，N归属店铺的店铺ID（ecp库cfg_store表的主键id)
     */
    private Integer storeId;

    /**
     * 仪器数量
     */
    private Integer num;

}

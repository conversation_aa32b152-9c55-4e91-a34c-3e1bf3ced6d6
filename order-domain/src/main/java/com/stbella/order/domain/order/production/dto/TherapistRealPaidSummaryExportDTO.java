package com.stbella.order.domain.order.production.dto;

import com.stbella.core.excel.Column;
import lombok.Data;

import java.io.Serializable;

@Data
public class TherapistRealPaidSummaryExportDTO implements Serializable {

    private static final long serialVersionUID = -8510775892254322220L;

    @Column(value = "门店名称", order = 1)
    private String storeName;

    @Column(value = "已核销金额", order = 2)
    private String writeOffPaid;

    @Column(value = "累计已核销金额", order = 3)
    private String writeOffSummaryPaid;

    @Column(value = "累计未核销金额", order = 4)
    private String unwrittenSummaryPaid;
}

package com.stbella.order.domain.order.service;


import com.stbella.order.common.enums.month.RefundRecordPayStatusEnum;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import com.stbella.platform.order.api.refund.req.QueryQueryEquallyGoodsReq;
import com.stbella.platform.order.api.refund.res.*;
import com.stbella.platform.order.api.res.QueryCombinationAdditionalRes;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface OrderRefundDomainService {

    /**
     * 查询订单商品 ，返回退款统计
     *
     * @param orderId
     * @return
     */
    List<QueryOrderRefundGoodsInfoRes> queryOrderGoodsWithRefundStatics(Integer orderId);


    /**
     * 查询所有成功的退款分摊
     *
     * @param orderId
     * @return
     */
    List<HeOrderRefundGoodsEntity> querySuccessfulOrderGoodsAllocationRefund(Integer orderId);


    /**
     * 退款成功后发送短信
     *
     * @param refundId
     */
    void afterRefundSms(Integer refundId);

    /**
     * 退款成功后续处理
     *
     * @param refundId
     */
    void afterRefundSuccess(Integer refundId);

    void afterRefundFail(Integer refundId);

    /**
     * 根据订单获取退款记录
     *
     * @param orderId
     * @return
     */
    List<OrderRefundListRes> refundListByOrderId(Integer orderId);

    /**
     * 获取退款详情
     *
     * @param refundId
     * @return
     */
    OrderRefundDetailsRes refundDetails(Integer refundId);

    /**
     * 获取退款进度
     *
     * @param refundId
     * @return
     */
    List<OrderRefundProgressRes> refundProgress(Integer refundId);

    /**
     * 更新主的退款状态
     *
     * @param refundId
     */
    void updateParentStatus(Integer refundId);

    /**
     * 审批拒绝或者发起失败操作
     *
     * @param refundId
     */
    void approveFailOrRefuse(Integer refundId, RefundRecordPayStatusEnum refundRecordPayStatusEnum);

    /**
     * 根据组合sn获取加收项的数据（已付、已退、总数量、可退数量、可退金额）
     *
     * @param combinationGoodsSn
     * @param goodsType
     */
    QueryCombinationAdditionalRes getCombinationAddition(Integer orderId, String combinationGoodsSn, Integer goodsType);

    /**
     * 退款都成功后发送短信
     *
     * @param orderId
     */
    void sendRefundAfter(Integer orderId, Integer refundId);

    /**
     * 退款审批结束后后发送短信
     *
     * @param orderId
     */
    void sendRefundAfterApprove(Boolean isRefund, String originatorPhone, String originatorName, Integer orderId, HeOrderRefundEntity heOrderRefundEntity);

    void sendRefundAfterApproveByAmount(Boolean isRefund, String originatorPhone, String originatorName, Integer orderId, HeOrderRefundEntity heOrderRefundEntity, BigDecimal refundAmount);

    /**
     * 刷新退款状态
     *
     * @param refundId
     * @return
     */
    OrderRefundProgressRes refreshStatus(Integer refundId);

    /**
     * 退款失败后重新发起
     *
     * @param refundId
     * @return
     */
    CreateRefundReq againRefund(Integer refundId);


    /**
     * 获取退款明细
     *
     * @param orderId
     * @return
     */
    QueryRefundInfoRes refundInfoRes(Integer orderId, List<HeOrderRefundEntity> orderRefundList, List<HeIncomeRecordEntity> orderIncomeList);

    /**
     * 根据子退款sn获取商品
     *
     * @param req
     * @return
     */
    QueryEquallyGoodsRes queryEquallyGoods(QueryQueryEquallyGoodsReq req);

    /**
     * 根据订单id获取退款商品信息
     *
     * @param orderIdList
     * @param orderGoodsList
     * @return
     */
    Map<Long, QueryOrderRefundGoodsInfoRes> queryOrderGoodsInfoMapByOrderIdList(List<Integer> orderIdList, List<HeOrderGoodsEntity> orderGoodsList);


    List<QueryOrderRefundGoodsInfoRes> queryOrderGoodsWithRefundStaticsList(Integer orderId, List<HeOrderGoodsEntity> orderGoodsList, List<IncomePaidAllocationEntity> incomePaidAllocationList, List<HeOrderRefundGoodsEntity> orderRefundGoodsList);

    /**
     * 客诉-退款进度
     *
     * @param complaintId
     * @return
     */
    List<OrderRefundProgressRes> complaintRefundProgress(Long complaintId);
}

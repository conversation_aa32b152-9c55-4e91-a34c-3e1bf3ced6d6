package com.stbella.order.domain.repository;

import com.stbella.order.domain.order.month.entity.IncomePaidAllocationEntity;

import java.util.Date;
import java.util.List;

public interface IncomePaidAllocationRepository {

    List<IncomePaidAllocationEntity> queryListByIncomeIds(List<Integer> incomeIds);

    boolean saveOrUpdateBatch(List<IncomePaidAllocationEntity> entityList);

    List<IncomePaidAllocationEntity> queryListByOrderId(Long orderId);

    void deleteByIncomeIds(List<Integer> incomeIds);

    List<IncomePaidAllocationEntity> queryListByOrderId(List<Long> orderIdList);

    boolean updatePaidAmount(IncomePaidAllocationEntity entity);

    List<IncomePaidAllocationEntity> queryListByOrderIdList(List<Integer> orderIdList);

    List<IncomePaidAllocationEntity> queryListByOrderIdAndOrderGoodsIdList(Integer orderId, List<Integer> orderGoodsIdList);

    List<IncomePaidAllocationEntity> queryListByOrderGoodsSnList(List<String> orderGoodsSnList);

    List<Integer> queryListByUpdateDate(Date updateStartTime, Date updateEndTime);
}

package com.stbella.order.domain.order.production;

import com.stbella.order.common.utils.SpringContextHolder;
import com.stbella.order.domain.repository.TherapistScheduleRepository;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
public class TherapistScheduleEntity implements Serializable {

    private static final long serialVersionUID = -6800680734766862691L;

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    TherapistScheduleRepository therapistScheduleRepository;
    /**
     *
     */
    private Long id;
    /**
     * 产康师id
     */
    private Long therapistId;
    /**
     * 预约日期 yyyy-MM-dd
     */
    private String serveDate;
    /**
     * 开始时间 HH:mm
     */
    private String startTime;
    /**
     * 结束时间 HH:mm
     */
    private String endTime;
    /**
     * 开始时间（合并之后）
     */
    private Date margeStartTime;
    /**
     * 结束时间（合并之后）
     */
    private Date margeEndTime;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 修改时间
     */
    private Date gmtModified;
    /**
     * 逻辑删除标志，0：未删除，1：逻辑删除
     */
    private Integer deleted;

    public TherapistScheduleEntity() {
        this.therapistScheduleRepository = SpringContextHolder.getBean(TherapistScheduleRepository.class);
    }

}

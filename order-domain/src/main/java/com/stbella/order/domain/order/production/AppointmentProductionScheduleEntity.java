package com.stbella.order.domain.order.production;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: Seven
 * @time: 2022/10/12 10:51
 */
@Data
public class AppointmentProductionScheduleEntity {
    /**
     * 今日预约
     * 卡片展示今日待服务的预约，包含已核销和待核销，通过卡片底色区分，点击卡片打开该预约单详情
     * 按照“服务时间”顺序展示，保持视野中有未核销卡片（如果均已核销则按顺序展示即可）
     * 项目规格值名称+服务时段+客户的微信头像（如果没有则用默认头像）+客户姓名
     */

    @ApiModelProperty("预约单ID")
    private Long id;

    @ApiModelProperty("项目规格名称")
    private String productionSkuName;

    @ApiModelProperty("客户微信头像")
    private String avatarUrl;

    @ApiModelProperty("服务开始时间")
    private Date serveStart;

    @ApiModelProperty("服务结束时间")
    private Date serveEnd;

    @ApiModelProperty("预约日期 yyyy-MM-dd")
    private String serveDate;

    @ApiModelProperty("客户ID")
    private Long clientId;

    @ApiModelProperty("客户名称")
    private String clientName;

    @ApiModelProperty("预约单ID")
    private Long appointmentId;

    @ApiModelProperty("核销状态：1=待核销;2=已核销;3=已取消")
    private Integer verificationState;

    @ApiModelProperty("服务时长 分单位")
    private String serveTime;

    @ApiModelProperty("产康师ID")
    private String therapistId;

    @ApiModelProperty("产康师名称")
    private String therapistName;
}

package com.stbella.order.domain.client;

import com.stbella.month.server.enums.OrderApproveRecordTypeEnum;
import com.stbella.notice.entity.OaProcessIdRelationPO;
import com.stbella.store.goodz.res.PropertyUserDefinedVO;

import java.util.List;


/**
 * <AUTHOR>
 * OA 审批
 */
public interface OaApproveGateway {

    /**
     * 根据业务id和类型获取最新审批记录
     * @param bizId
     * @param type
     * @return
     */
    OaProcessIdRelationPO getLastRecordByBizIdAndType(Long bizId, OrderApproveRecordTypeEnum type);

}

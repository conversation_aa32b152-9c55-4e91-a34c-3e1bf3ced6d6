package com.stbella.order.domain.order.service.impl;

import com.stbella.contract.model.enums.OrderSignTypeEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderBailorSnapshotEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.domain.order.service.ContractOpponentAuthDomainService;
import com.stbella.order.domain.repository.OrderBailorSnapshotRepository;
import com.stbella.order.domain.repository.OrderUserSnapshotRepository;
import com.stbella.order.server.order.month.enums.CardVerifyEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Objects;

/**
 * @Author: ji<PERSON><PERSON>an
 * @CreateTime: 2024-08-08  14:05
 * @Description: 订单合同相关方认证服务
 */
@Service
@Slf4j
public class ContractOpponentAuthDomainServiceImpl implements ContractOpponentAuthDomainService {

    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;

    @Resource
    private OrderBailorSnapshotRepository orderBailorSnapshotRepository;

    /**
     * 查询订单合同相关方认证状态
     *
     * @param entity
     * @return
     */
    @Override
    public Boolean getAuthState(HeOrderEntity entity) {

        if (Objects.isNull(entity.getVersion())){
            entity.setVersion(BigDecimal.ONE);
        }
        //押金不用验证合同主体状态
        if (OmniOrderTypeEnum.DEPOSIT_ORDER.getCode().intValue() == entity.getOrderType() || entity.getVersion().compareTo(new BigDecimal("3")) < 0){
            return true;
        }

        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(entity.getOrderId());
        if (Objects.isNull(entity.getSignType())) {
            return false;
        }
        if (!Arrays.asList(OrderSignTypeEnum.SIGN_TYPE_CLIENT.code(), OrderSignTypeEnum.SIGN_TYPE_BAILOR.code()).contains(entity.getSignType())) {
            return false;
        }
        if (OrderSignTypeEnum.SIGN_TYPE_CLIENT.code().equals(entity.getSignType())) {
            if (Objects.isNull(heOrderUserSnapshotEntity)) {
                return false;
            }

            if (CardVerifyEnum.successVerify(heOrderUserSnapshotEntity.getIsCardVerify())) {
                return Boolean.TRUE;
            }

            if (Objects.isNull(heOrderUserSnapshotEntity.getAuthType())) {
                return false;
            }

            if (heOrderUserSnapshotEntity.getAuthType() == 0 && heOrderUserSnapshotEntity.getIsCardVerify() == 2) {
                return true;
            }

            if (heOrderUserSnapshotEntity.getAuthType() == 1 && StringUtils.isNotEmpty(heOrderUserSnapshotEntity.getEmail())) {
                return true;
            }
        } else {
            HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity = orderBailorSnapshotRepository.queryByOrderId(entity.getOrderId());
            if (Objects.isNull(heOrderBailorSnapshotEntity)) {
                return false;
            }

            if (CardVerifyEnum.successVerify(heOrderBailorSnapshotEntity.getIsCardVerify())) {
                return Boolean.TRUE;
            }

            if (Objects.isNull(heOrderBailorSnapshotEntity.getAuthType())) {
                return false;
            }

            if (heOrderBailorSnapshotEntity.getAuthType() == 0 && heOrderBailorSnapshotEntity.getIsCardVerify() == 2) {
                return true;
            }
            if (heOrderBailorSnapshotEntity.getAuthType() == 1 && StringUtils.isNotEmpty(heOrderBailorSnapshotEntity.getEmail())) {
                return true;
            }
        }
        return Boolean.TRUE;
    }
}

package com.stbella.order.domain.repository.condition;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-08-06  17:07
 * @Description: 门店查询条件
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StoreQueryCondition implements Serializable {

    @ApiModelProperty("门店品牌")
    private List<Integer> types;
    @ApiModelProperty("子类型")
    private List<Integer> childTypes;
    @ApiModelProperty("门店ids")
    private List<Long> ids;
    @ApiModelProperty("不等于门店ids")
    private List<Long> neIds;
    @ApiModelProperty("门店启用，0：未启用，1：启用")
    private Integer active;
    @ApiModelProperty("战区codes")
    private List<Integer> warZones;
    @ApiModelProperty("业务线")
    private List<Integer> bu;

    @ApiModelProperty("门店名称")
    private String name;
}

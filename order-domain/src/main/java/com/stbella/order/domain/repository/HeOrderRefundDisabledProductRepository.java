package com.stbella.order.domain.repository;

import com.stbella.order.domain.order.month.entity.HeOrderRefundDisabledProductEntity;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR> @since 2025-04-25
 */
public interface HeOrderRefundDisabledProductRepository {

    void saveList(List<HeOrderRefundDisabledProductEntity> orderRefundDisabledProductEntityList);

    List<HeOrderRefundDisabledProductEntity> queryByRefundId(Long refundOrderId);
}

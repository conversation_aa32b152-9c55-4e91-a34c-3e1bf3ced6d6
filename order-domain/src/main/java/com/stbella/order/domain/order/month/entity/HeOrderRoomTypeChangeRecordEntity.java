package com.stbella.order.domain.order.month.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单变更房型记录表
 */
@Data
public class HeOrderRoomTypeChangeRecordEntity {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "订单编号")
    private Long orderId;

    @ApiModelProperty(value = "加收项主键")
    private Long additionalRevenueId;

    @ApiModelProperty(value = "原房型id")
    private Integer originalRoomId;

    @ApiModelProperty(value = "原房型名称")
    private String originalRoomName;

    @ApiModelProperty(value = "新房型id")
    private Integer newRoomId;

    @ApiModelProperty(value = "新房型名称")
    private String newRoomName;

    @ApiModelProperty(value = "加减天数")
    private Integer days;

    @ApiModelProperty(value = "最终天数")
    private Integer finalDays;

    @ApiModelProperty(value = "本次更改原价")
    private BigDecimal changeOriginalPrice;

    @ApiModelProperty(value = "最终原价")
    private BigDecimal finalChangeOriginalPrice;

    @ApiModelProperty(value = "本次更改实收")
    private BigDecimal changePrice;

    @ApiModelProperty(value = "最终实收")
    private BigDecimal finalChangePrice;

    @ApiModelProperty(value = "本次房型变更周期")
    private String roomTypeChangeCycle;

    @ApiModelProperty(value = "最终房型变更周期")
    private String finalRoomTypeChangeCycle;

    @ApiModelProperty(value = "操作人id")
    private String optId;

    @ApiModelProperty(value = "操作人名称")
    private String optName;

    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "更新时间")
    private Date gmtModified;

}

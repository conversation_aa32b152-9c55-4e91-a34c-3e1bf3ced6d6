package com.stbella.order.domain.order.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 产康馆外订单业绩操作记录
 * </p>
 *
 * <AUTHOR> @since 2024-08-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ExternalProductionOrderPerformance对象", description = "产康馆外订单业绩操作记录")
public class ExternalProductionOrderPerformanceEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "订单ID")
    private Long orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    @ApiModelProperty(value = "业绩生效时间")
    private Long percentFirstTime;

    @ApiModelProperty(value = "操作类型：1-计入业绩 2-剔除业绩")
    private Integer operationType;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人ID")
    private Long createdBy;

    @ApiModelProperty(value = "创建人姓名")
    private String createdByName;

    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "是否删除：0-否 1-是")
    private Integer isDeleted;
}

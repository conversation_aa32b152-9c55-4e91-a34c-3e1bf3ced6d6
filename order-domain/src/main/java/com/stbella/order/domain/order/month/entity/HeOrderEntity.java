package com.stbella.order.domain.order.month.entity;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.stbella.core.base.Operator;
import com.stbella.core.result.Result;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.core.OmniOrderStatusEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.core.OrderNoticeEnum;
import com.stbella.order.common.enums.month.OrderStatusV2Enum;
import com.stbella.order.common.enums.month.PayStatusV2Enum;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.order.common.utils.JsonUtil;
import com.stbella.order.common.utils.SpringContextHolder;
import com.stbella.order.domain.order.entity.OrderOperateLogEntity;
import com.stbella.order.domain.order.service.OrderRefundDomainService;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.order.month.enums.ApprovalDiscountStatusEnum;
import com.stbella.platform.order.api.OrderQueryService;
import com.stbella.platform.order.api.reduction.req.ReductionQuery;
import com.stbella.platform.order.api.req.ExtraInfo;
import com.stbella.platform.order.api.res.PromotionInfo;
import com.stbella.store.common.enums.core.GoodsTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.pulsar.shade.com.google.common.collect.Sets;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单表
 * </p>
 *
 * <AUTHOR> @since 2022-10-28
 */
@Data
@Slf4j
public class HeOrderEntity {

    /**
     * version >= 3 表示新订单
     */
    private static final int newOrderVersion = 3;


    /**
     * 剩余应收金额
     *
     * @return
     */
    public Integer leftPayAmount() {
        return calPayable() - sumPaidAmount() - cashReviewingAmount;
    }


    public Integer sumReduce() {
        return totalReductionAmount - reductionRefundAmount;
    }


    public Integer add(Operator operator) {
        log.info("saveOrderMonth");
        //如果传入的时间戳为毫秒需要转为秒
        if (ObjectUtil.isNotEmpty(this.getWantIn()) && this.getWantIn().toString().length() == 13) {
            this.setWantIn(this.getWantIn() / 1000);
        }
        this.setProductionAmountPay(0);
        this.setTotalReductionAmount(0);
        this.setReductionRefundAmount(0);
        this.setCashPaidAmount(0);
        this.setCashRefundAmount(0);
        this.setProductionCoinPaidAmount(0);
        this.setProductionCoinRefundAmount(0);
        this.setGoodsRefundAllocationAmount(0);
        this.setTempRefundAmount(0);
        this.setCashReviewingAmount(0);
        this.setTempRefundAmount(0);
        Integer orderId = SpringContextHolder.getBean(OrderRepository.class).saveOrderMonth(this);
        return orderId;
    }

    /**
     * 应付金额
     * 计算公式：最初签单金额- (商品退货数量 × 商品应付分摊)
     */
    public Integer calPayable() {
        Integer goodsRefundAllocation = goodsRefundAllocationAmount;

        int result = this.payAmount - goodsRefundAllocation + tempRefundAmount - sumReduce();

        log.debug("应付金额计算: 签单金额={},  商品退货分摊={}, 最终应付金额={}",
                this.payAmount, goodsRefundAllocation, result);

        return Math.max(0, result); // 确保应付金额不为负数
    }

    /**
     * 签单金额减去减免金额
     * @return
     */
    public Integer signPayAmount() {
        return this.payAmount - totalReductionAmount;
    }


    /**
     * 已付金额
     */
    private Integer sumPaidAmount() {
        return cashPaidAmount + productionCoinPaidAmount - cashRefundAmount - productionCoinRefundAmount;
    }







    /**
     * 未确认金额
     *
     * @return
     */
    public Integer fetchUnConfirmAmount() {
//        OrderIncomeDomainService incomeDomainService = SpringContextHolder.getBean(OrderIncomeDomainService.class);
//        List<HeIncomeRecordEntity> unConfirmRecordLis = incomeDomainService.queryEffectiveRecord(this).stream()
//                .filter(income -> OfflineAuditStatusV2Enum.UNDER_REVIEW.getCode().intValue() == income.getApproveStatus())
//                .collect(Collectors.toList());
//        int unConfirmAmount = unConfirmRecordLis.stream().mapToInt(HeIncomeRecordEntity::getIncome).sum();
//        return unConfirmAmount;
        return cashReviewingAmount;
    }

    /**
     * 订单减免金额
     *
     * @return
     */
    public Long fetchTotalReductionAmount() {
//        ReductionQuery reductionQuery = new ReductionQuery().setOrderId(this.getOrderId()).setAuthStateList(Lists.newArrayList(OrderDecreaseStatusEnum.APPROVED.getCode(), OrderDecreaseStatusEnum.NO_APPROVAL_NEEDED.getCode()));
//
//        OrderReductionRepository reductionRepository = SpringContextHolder.getBean(OrderReductionRepository.class);
//        List<OrderReductionEntity> reductionEntityList = reductionRepository.select(reductionQuery);
//        Long totalReductionAmount = reductionEntityList.stream().map(OrderReductionEntity::getDecreaseAmount).reduce(Long::sum).orElse(0L);
//        return totalReductionAmount;
        return Long.valueOf(totalReductionAmount);
    }


    /**
     * 退回重新支付
     *
     * @return
     */
    public Integer fetchTempRefund() {

//        OrderRefundDomainService refundDomainService = SpringContextHolder.getBean(OrderRefundDomainService.class);
//        //每个商品退回重付
//        List<HeOrderRefundGoodsEntity> orderGoodsRefundList = refundDomainService.querySuccessfulOrderGoodsAllocationRefund(this.getOrderId())
//                .stream().filter(a -> Objects.nonNull(a.getRefundNature()))
//                .filter(refund -> OrderRefundNatureEnum.TEMP_REFUND.code().intValue() == refund.getRefundNature())
//                .collect(Collectors.toList());
//        //计算总退回重复退款金额
//        Integer tempRefund = orderGoodsRefundList.stream().filter(refund -> OrderRefundNatureEnum.TEMP_REFUND.code().intValue() == refund.getRefundNature()).mapToInt(HeOrderRefundGoodsEntity::getRefundAmount).sum();
//
//        return tempRefund;
        return cashRefundAmount;
    }


    public void saveApprovalState() {
        HeOrderEntity updateModel = new HeOrderEntity();
        updateModel.setOrderId(this.getOrderId());
        updateModel.setApprovalDiscountStatus(this.getApprovalDiscountStatus());
        updateModel.setUpdatedAt(System.currentTimeMillis() / 1000);
        SpringContextHolder.getBean(OrderRepository.class).updateOne(updateModel);
    }


    /**
     * 报单
     */
    public void noticed() {
        HeOrderEntity updateModel = new HeOrderEntity();
        updateModel.setOrderId(this.getOrderId());
        updateModel.setIsNotice(OrderNoticeEnum.NOTICE_YES.code());
        updateModel.setUpdatedAt(System.currentTimeMillis() / 1000);
        SpringContextHolder.getBean(OrderRepository.class).updateOne(updateModel);
    }

    /**
     * 首次支付时间
     *
     * @return
     */
    public void firstPay() {

        HeOrderEntity updateModel = new HeOrderEntity();
        updateModel.setOrderId(this.getOrderId());
        updateModel.setPayFirstTime(this.getPayFirstTime());
        SpringContextHolder.getBean(OrderRepository.class).updateOrderMonthByOrderId(updateModel);

    }

    /**
     * 订单业绩生效
     *
     * @return
     */
    public void effectPerformance() {
        this.justEffect = true;
        this.setJustEffect(true);
        HeOrderEntity updateModel = new HeOrderEntity();
        updateModel.setOrderId(this.getOrderId());
        updateModel.setPercentFirstTime(this.getPercentFirstTime());
        SpringContextHolder.getBean(OrderRepository.class).updateOrderMonthByOrderId(updateModel);

    }

    public String fetchOrderStateLabel() {
        if (OrderStatusV2Enum.CLOSE.getCode().equals(this.getOrderStatus())) {
            return PayStatusV2Enum.CLOSED.getValue();
        }
        if (OrderStatusV2Enum.OUT_OF_STORE.getCode().intValue() == this.getOrderStatus() || OrderStatusV2Enum.ADVANCE_OUT_OF_STORE.getCode().intValue() == this.getOrderStatus()) {
            return "交易成功";
        }
        if (PayStatusV2Enum.WAIT_PAY.getCode().equals(this.getOrderStatus())) {
            return "未支付";
        }
        PayStatusV2Enum enumByCode = PayStatusV2Enum.getEnumByCode(this.getPayStatus());
        return enumByCode.getValue();
    }

    public String fetchOrderStateStr() {

        if (OrderStatusV2Enum.CLOSE.getCode().equals(this.getOrderStatus())) {
            return "已关闭";
        }
        if (OrderStatusV2Enum.OUT_OF_STORE.getCode().intValue() == this.getOrderStatus() || OrderStatusV2Enum.ADVANCE_OUT_OF_STORE.getCode().intValue() == this.getOrderStatus()) {
            return "已完成";
        }
        if (OrderStatusV2Enum.NONE.getCode().equals(this.getOrderStatus())) {
            return OrderStatusV2Enum.NONE.getValue();
        }
        if (OrderStatusV2Enum.TO_STAY_IN.getCode().equals(this.getOrderStatus())) {
            return "待发货";
        }
        if (OrderStatusV2Enum.STAY_IN.getCode().equals(this.getOrderStatus())) {
            return "待收货";
        }
        return "";
    }


    /**
     * 订单业绩失效
     *
     * @return
     */
    public void invalidPerformance() {
        this.justEffect = false;
        this.assetRevocation = true;
        HeOrderEntity updateModel = new HeOrderEntity();
        updateModel.setOrderId(this.getOrderId());
        updateModel.setPercentFirstTime(0);
        updateModel.setBkPercentFirstTime(0);
        SpringContextHolder.getBean(OrderRepository.class).updateOrderMonthByOrderId(updateModel);
        log.info("业绩失效 orerSn={}, 生效了吗：{}", this.getOrderSn(), this.isJustEffect());

    }


    /**
     * 全额支付
     * 规则：累计支付金额 = O签单 - 减免 + 退回重付
     *
     * @return
     */
    public void fullPayment(Long payFinishAt) {
        HeOrderEntity updateModel = new HeOrderEntity();
        if (Boolean.FALSE.equals(this.isPreviouslyFullyPaid)) {
            this.justFullPayment = true;
            updateModel.setIsPreviouslyFullyPaid(Boolean.TRUE);
            updateModel.setPayFinishAt(Objects.isNull(payFinishAt) ? (System.currentTimeMillis() / 1000) : payFinishAt);
            //TODO 省略一步，全额支付状态直接修改成待收货
            updateModel.setOrderStatus(OmniOrderStatusEnum.WAIT_RECEIVE.getCode());
        } else {
            this.justFullPayment = false;
        }
        updateModel.setPayStatus(PayStatusV2Enum.PAY_OFF.getCode());
        updateModel.setOrderId(this.getOrderId());
        updateModel.setPaidAmount(this.getPaidAmount());
        updateModel.setRealAmount(this.getRealAmount());
        updateModel.setProductionAmountPay(this.getProductionAmountPay());
        SpringContextHolder.getBean(OrderRepository.class).updateOrderMonthByOrderId(updateModel);

    }

    /**
     * 部分支付
     *
     * @return
     */
    public void partPayment() {
        this.justFullPayment = false;
        HeOrderEntity updateModel = new HeOrderEntity();
        updateModel.setOrderId(this.getOrderId());
        updateModel.setPaidAmount(this.getPaidAmount());
        updateModel.setRealAmount(this.getRealAmount());
        updateModel.setProductionAmountPay(this.getProductionAmountPay());
        updateModel.setPayStatus(PayStatusV2Enum.NO_PAY_OFF.getCode());
        SpringContextHolder.getBean(OrderRepository.class).updateOrderMonthByOrderId(updateModel);

    }

    /**
     * 关闭订单
     *
     * @return
     */
    @Transactional(transactionManager = "therapistTransactionManager", rollbackFor = Exception.class)
    public void close(Operator operator) {

        SpringContextHolder.getBean(OrderRepository.class).closeOrder(this);

        //操作日志
        OrderOperateLogEntity logEntity = OrderOperateLogEntity.builder().operatorId(operator.getOperatorGuid()).operatorName(operator.getOperatorName()).orderSn(this.getOrderSn()).operatorPhone(operator.getOperatorPhone()).operatorType(BizActivityEnum.CREATE_ORDER.code()).remark("创建订单").build();

        SpringContextHolder.getBean(OrderOperatorLogRepository.class).save(logEntity);

    }

    /**
     * 查询入住类服务天数
     *
     * @return
     */
    public Integer fetchServiceRoomDays() {

        List<HeOrderGoodsEntity> allItermByOrderId = SpringContextHolder.getBean(OrderGoodsRepository.class).getAllItermByOrderId(this.getOrderId());
        Set<Integer> filterTypes = Sets.newHashSet(CombineTypeEnum.SIMPLE.code(), CombineTypeEnum.COMBINE_SUB.code());
        int days = allItermByOrderId.stream().filter(a -> Objects.nonNull(a.getType()) && filterTypes.contains(a.getType())).filter(item -> BizConstant.OrderAppKey.ASSET_LIST_ROOM_SERVICE.contains(item.getGoodsType())).mapToInt(HeOrderGoodsEntity::getGoodsNum).sum();

        return days;

    }


    /**
     * 返回服务类商品
     *
     * @return
     */
    public List<HeOrderGoodsEntity> fetchServiceGoods() {

        List<Integer> serviceType = Lists.newArrayList(GoodsTypeEnum.CONFINEMENT_PACKAGE.code(), GoodsTypeEnum.MISCARRIAGE_PACKAGE.code(), GoodsTypeEnum.NURSE_FOR_HOME_PACKAGE.code());
        List<HeOrderGoodsEntity> serviceGoods = this.getGoodsList().stream().filter(a -> serviceType.contains(a.getGoodsType())).collect(Collectors.toList());
        return serviceGoods;
    }

    /**
     * 返回房间相关服务类商品
     *
     * @return
     */
    public List<HeOrderGoodsEntity> fetchRoomServiceGoods() {
        Set<Integer> filterTypes = Sets.newHashSet(CombineTypeEnum.SIMPLE.code(), CombineTypeEnum.COMBINE_SUB.code());
        List<HeOrderGoodsEntity> serviceGoods = this.getGoodsList().stream().filter(item -> filterTypes.contains(item.getType())).filter(a -> BizConstant.OrderAppKey.ASSET_LIST_ROOM_SERVICE.contains(a.getGoodsType())).collect(Collectors.toList());
        return serviceGoods;
    }

    /**
     * 是否百日随行套餐
     * 商品中有 百日随行 4个字(临时使用)
     *
     * @return
     */
    public String hundredDaysServiceTag() {
        String tag = "百日随行";
        for (HeOrderGoodsEntity item : this.getGoodsList()) {
            if (item.getGoodsName().contains(tag)) {
                return tag;
            }
        }
        return "";
    }

    /**
     * 返回服务类商品名称
     *
     * @return
     */
    public String fetchServiceGoodsName() {

        List<HeOrderGoodsEntity> serviceGoods = this.fetchServiceGoods();
        StringBuilder goodsName = new StringBuilder();
        if (ObjectUtil.isNotEmpty(serviceGoods)) {
            serviceGoods.forEach(a -> goodsName.append(a.getGoodsName()).append("x").append(a.getGoodsNum()).append(","));
            goodsName.deleteCharAt(goodsName.length() - 1);
        }

        String goodsNameStr = goodsName.toString();
        if (goodsNameStr.length() == 0) {
            goodsNameStr = "无服务类商品";
        }
        return goodsNameStr;
    }


    /**
     * 设置是否需要签合同
     */
    public void setNeedSign() {
        List<HeOrderGoodsEntity> needSignGoods = this.getGoodsList().stream().filter(a -> Objects.nonNull(a.getNeedSign()) && a.getNeedSign()).collect(Collectors.toList());
        this.setNeedSign(0);
        if (CollectionUtil.isNotEmpty(needSignGoods)) {
            this.setNeedSign(1);
        }
    }

    /**
     * 是否新订单
     */
    public boolean isNewOrder() {
        if (Objects.nonNull(this.getVersion()) && this.getVersion().intValue() >= newOrderVersion) {
            return true;
        }
        return false;
    }

    @ApiModelProperty(value = "订单ID（主键自增)")
    private Integer orderId;

    @ApiModelProperty(value = "父订单no - 续约等场景")
    private String parentOrderNo;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    @ApiModelProperty(value = " 订单类型： 0 月子客户的“普通月子套餐”订单； 1 月子客户的“小月子”订单； 10 到家客户的“到家服务”订单； 20 到家阿姨的“到家服务订单”； 21 到家阿姨的阿姨培训订单； 30 月子其他订单 31 到家其他订单 40 商城普通订单 50护士外派订单 60产康订单 70S-BRA订单 描述： 0-9 归属月子平台的客户订单类型 ； 10-19 归属到家客户的订单类型； 20-29到家阿姨的订单类型; 30-39 其他订单 40-49 商城订单类型 50护士外派订单 60产康订单 70S-BRA订单")
    private Integer orderType;

    @ApiModelProperty(value = "创建该订单的任务ID（task表的id字段）")
    private Long taskId;

    @ApiModelProperty(value = "全局用户ID")
    private Integer basicUid;

    @ApiModelProperty(value = "买家客户ID（ecp库的tab_client表主键）")
    private Integer clientUid;

    @ApiModelProperty(value = "客户类型：0无（0是问题滴），1月子客户，2到家阿姨，5到家客户，")
    private Integer clientType;

    @ApiModelProperty(value = "销售员id（ecp库user表主键id）")
    private Integer staffId;

    @ApiModelProperty(value = "销售员电话）")
    private String staffPhone;

    @ApiModelProperty(value = "门店ID(ecp库cfg_store表的主键id)")
    private Integer storeId;

    @ApiModelProperty(value = "是否全部确认收货 0=否 1=是")
    private Integer isDelivery;

    @ApiModelProperty(value = "是否存在退款中 0=否 1=是 2已同意待退款，3已同意已退款")
    private Integer isHasRefund;

    @ApiModelProperty(value = "是否关闭0=否 1=是")
    private Integer isClose;

    @ApiModelProperty(value = "支付状态：0待支付，1未全付，2已付清，3已取消 4-1")
    private Integer payStatus;

    @ApiModelProperty(value = "原价总金额，单位：分")
    private Integer orderAmount;

    @ApiModelProperty(value = "节假日费用金额，单位：分")
    private Integer holidayAmount;

    @ApiModelProperty(value = "法定节假日选择")
    private String holidayItems;

    @ApiModelProperty(value = "节假日天数")
    private Integer holidayNum;

    @ApiModelProperty(value = "应付总金额")
    private Integer payAmount;

    @ApiModelProperty(value = "首次支付时间")
    private Integer payFirstTime;

    @ApiModelProperty(value = "已支付总金额，单位：分")
    private Integer paidAmount;

    @ApiModelProperty(value = "实际金额(单位分,受退款影响)")
    private Integer realAmount;

    @ApiModelProperty(value = "冻结金额")
    private Integer freezeAmount;

    @ApiModelProperty(value = "应付积分总数")
    private Integer payIntegral;

    @ApiModelProperty(value = "产康金抵扣金额实际")
    private Integer productionAmountPay;

    @ApiModelProperty(value = "运费")
    private Integer freight;

    @ApiModelProperty(value = "入住时间")
    private Long wantIn;

    @ApiModelProperty(value = "是否备孕0=否 1=是")
    private Integer isPreparePregnancy;

    @ApiModelProperty(value = "订单备注")
    private String remark;

    @ApiModelProperty(value = "记录C端当前进行到的任务ID")
    private Long nowpaytask;

    @ApiModelProperty(value = "收件人姓名")
    private String expressName;

    @ApiModelProperty(value = "收件人手机号")
    private String expressPhone;

    @ApiModelProperty(value = "省份名称")
    private String province;

    @ApiModelProperty(value = "城市名称")
    private String city;

    @ApiModelProperty(value = "地区名称")
    private String area;

    @ApiModelProperty(value = "省份ID")
    private Integer provinceId;

    @ApiModelProperty(value = "城市ID")
    private Integer cityId;

    @ApiModelProperty(value = "地区ID")
    private Integer areaId;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "是否为冲刺单")
    private Integer isSprint;

    @ApiModelProperty(value = "是否全部发货：0否，1是")
    private Integer isSendAll;

    @ApiModelProperty(value = "下单时来源：0未知，1小贝拉，2圣贝拉，3occ后台")
    private Integer source;

    @ApiModelProperty(value = "该订单对应的押金sop的任务ID")
    private Long depositTaskId;

    @ApiModelProperty(value = "是否删除：1是，0否")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Long createdAt;

    @ApiModelProperty(value = "最近更新时间")
    private Long updatedAt;

    @ApiModelProperty(value = "最后更新销售id")
    private Integer updateStaffId;

    @ApiModelProperty(value = "商城订单类型（同步goods表的sell_type）： 0实物，1虚拟，2预约，3套餐")
    private Integer orderSellType;

    @ApiModelProperty(value = "产康订单类型： 旧版产康订单，1、新版产康服务")
    private Integer productionType;

    private String inforFields;

    @ApiModelProperty(value = "首次支付金额超过一半的时间")
    private Integer percentFirstTime;

    @ApiModelProperty(value = "备份首次支付金额超过一半的时间")
    private Integer bkPercentFirstTime;

    @ApiModelProperty(value = "0）正常/已恢复 1）已剔除 业绩")
    private Integer operationType;

    @ApiModelProperty(value = "订单表扩展信息")
    private String extendsContent;

    @ApiModelProperty(value = "订单表扩展信息")
    private ExtraInfo extraInfo;

    @ApiModelProperty(value = "优惠券优惠总金额，单位：分")
    private Integer couponAmount;

    @ApiModelProperty(value = "优惠券领取id")
    private Integer couponUserId;

    @ApiModelProperty(value = "签署合同类型1=育婴师中介型服务 2=雇主-中介-母婴护理 3=雇主-中介-育婴师 4=育婴师-套餐 5=雇主-套餐-母婴护理 6=雇主-套餐-育婴师 7=育婴师培训课程确认书协议 8=圣贝拉月子合同 9=小贝拉月子合同")
    private Integer contractType;

    @ApiModelProperty(value = "是否给推荐人加积分, 0:不加积分 1:加积分")
    private Integer inviteAddIntegralType;

    @ApiModelProperty(value = "毛利率")
    private BigDecimal grossMargin;

    @ApiModelProperty(value = "净利率")
    private BigDecimal netMargin;

    @ApiModelProperty(value = "折扣率")
    private BigDecimal discountMargin;

    @ApiModelProperty(value = "签单折扣率")
    private BigDecimal signOrderDiscountMargin;

    @ApiModelProperty(value = "订单折扣详情")
    private String discountDetails;

    @ApiModelProperty(value = "折扣审批状态 0=无需审批 1=审批中 2=审批通过 3=审批失败 4=发起失败")
    private Integer approvalDiscountStatus;

    @ApiModelProperty(value = "是否报单过:1=未报单;2=已报单")
    private Integer isNotice;

    @ApiModelProperty(value = "订单标签，来源于he_tags表")
    private Integer orderTag;

    @ApiModelProperty(value = "订单标签名称")
    private String orderTagName;

    @ApiModelProperty(value = "-1=未知;0=大陆身份证 1=护照 2=香港来往大陆通行证 3=澳门来往大陆通行证 4=台湾来往大陆通行证（只有身份证签订了主合同才是已认证 其他证件无需认证）")
    private Integer certType;

    @ApiModelProperty(value = "证件号")
    private String idCard;

    @ApiModelProperty(value = "证件正面 url")
    private String idCardFront;

    @ApiModelProperty(value = "证件背面图url")
    private String idCardBack;

    @ApiModelProperty(value = "sbra业绩归属，0:默认 1-月子业绩 2-产康业绩")
    private Integer sbraAchievementType;

    @ApiModelProperty(value = "二级来源 默认为0 1）孕期指南-孕期专题 2）孕期指南-参考清单 3）孕期指南-专家专栏")
    private Integer appointmentSecondType;

    @ApiModelProperty(value = "新老系统标志;0或null-老;1-新")
    private Integer oldOrNew;

    @ApiModelProperty(value = "签署标识0-客户,1-委托人")
    private Integer signType;
    @ApiModelProperty(value = "是否需要签署合同，0表示不需要，1表示需要")
    private Integer needSign;

    /**
     * 场景：0 下单； 1 报价单
     *
     * @see com.stbella.order.common.enums.core.CartSceneEnum
     */
    @ApiModelProperty(value = "场景：0 下单； 1 报价单,2,随心配")
    private Integer scene;

    /**
     * 是否需要签署合同
     *
     * @return
     */
    public boolean isNeedSign() {
        return needSign != null && needSign == 1;
    }

    @ApiModelProperty(value = "在馆状态0-未生效 1-待入住 2-入住中 3-已离店 4-已关闭 5-提前离馆")
    private Integer orderStatus;

    @ApiModelProperty(value = "退款状态：0-无退款 1-部分退款中 2-部分退款 3-全部退款中 4-全部退款")
    private Integer refundStatus;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "原订单ID（用于订单升级）")
    private Integer originalOrderId;

    @ApiModelProperty(value = "原商品总价（用于订单升级）")
    private BigDecimal originalGoodsTotalPrice;

    /**
     * 实际入住时间(履约开始时间)
     */
    private Date fulfillmentStartDate;

    /**
     * 实际离店时间(履约结束时间)
     */
    private Date fulfillmentEndDate;

    @ApiModelProperty(value = "BU 0:母婴 1:广禾 2:予家")
    private Integer bu;


    @ApiModelProperty(value = "版本号 3.00表示能用订单")
    private BigDecimal version;

    /**
     * 新增字段
     **/

    @ApiModelProperty(value = "总减免金额(分) - 避免查询order_reduction表")
    private Integer totalReductionAmount;

    @ApiModelProperty(value = "减免退款金额(分) - 避免查询退款表中的减免退款")
    private Integer reductionRefundAmount;

    @ApiModelProperty(value = "现金已付总金额(分) - 避免查询he_income_record按支付方式统计")
    private Integer cashPaidAmount;

    @ApiModelProperty(value = "现金退款金额(分)")
    private Integer cashRefundAmount;

    @ApiModelProperty(value = "产康金已付金额(分) - 产康金支付统计")
    private Integer productionCoinPaidAmount;

    @ApiModelProperty(value = "产康金退款金额(分)")
    private Integer productionCoinRefundAmount;

    @ApiModelProperty(value = "现金审批中金额(分) - 线下支付审批中的现金金额")
    private Integer cashReviewingAmount;

    @ApiModelProperty(value = "退回重付金额(分) - 特殊退款类型")
    private Integer tempRefundAmount;

    @ApiModelProperty(value = "商品退货分摊金额(分) - 商品退货数量 × 商品应付分摊")
    private Integer goodsRefundAllocationAmount;

    /**
     * 凭证列表（特殊折扣）
     */
    private List<HeOrderVoucherEntity> voucherEntityList;

    /**
     * 商品附件列表
     */
    private List<HeOrderAttachmentEntity> attachmentList;

    /**
     * 客户信息
     */
    private HeOrderUserSnapshotEntity customer;

    /**
     * 商品列表
     */
    private List<HeOrderGoodsEntity> goodsList;

    /**
     * 门店信息
     */
    private CfgStoreEntity store;

    /**
     * 刚刚生效 某个动作后，订单生效
     */
    private boolean justEffect;

    /**
     * 刚刚全额支付 某个动作后，订单全额支付
     */
    private boolean justFullPayment;

    /**
     * 线下支付申请被驳回，取消资产发放
     */
    private boolean assetRevocation = false;

    //用于折扣审批中使用的
    @ApiModelProperty(value = "本次折扣金额")
    private Integer disCountAmount;

    /**
     * 全额退款时间
     */
    private Date fullRefundDate;

    @ApiModelProperty(value = "是否曾经全额支付过")
    private Boolean isPreviouslyFullyPaid;

    @ApiModelProperty(value = "是否是订单减免")
    private Boolean isReduceDiscount;

    @ApiModelProperty(value = "外汇汇率")
    private BigDecimal fxRate;

    @ApiModelProperty(value = "服务类型 0自营 1三方")
    private Integer serviceType;

    @ApiModelProperty(value = "服务时长")
    private Integer serviceTime;

    @ApiModelProperty(value = "创建人Id")
    private Long createBy;

    @ApiModelProperty(value = "创建人姓名")
    private String createName;

    @ApiModelProperty(value = "折扣审批发起人")
    private String discountApprovePhone;

    @ApiModelProperty(value = "定制礼赠金额")
    private Integer giftAmount;

    @ApiModelProperty(value = "首次完款时间")
    private Long payFinishAt;

    @ApiModelProperty(value = "0-订单未开始核销；1-订单已核销")
    private Integer verificationStatus;

    @ApiModelProperty(value = "核销id")
    private Long verificationId;

    @ApiModelProperty(value = "是否是特定报单订单：如果订单下了某个特殊的商品，无视之前的逻辑，直接报单")
    private Boolean specificOrderItems = false;

    private Boolean transferOrder;

    @ApiModelProperty(value = "订单命中的活动信息")
    private List<PromotionInfo> promotionInfos;


    public Integer fetchFirstDiscountAmount() {
        return orderAmount - payAmount;
    }

    /**
     * 最新的审批中的减免记录
     *
     * @return
     */
    public OrderReductionEntity fetchLastOrderReduction() {
        ReductionQuery reductionQuery = new ReductionQuery().setOrderId(orderId);
        List<OrderReductionEntity> reductionEntityList = SpringContextHolder.getBean(OrderReductionRepository.class).select(reductionQuery);
        if (CollectionUtils.isNotEmpty(reductionEntityList)) {
            return reductionEntityList.get(0);
        }
        return null;
    }

    /**
     * 最新的审批中的退款记录
     *
     * @return
     */
    public HeOrderRefundEntity fetchLastApprovingOrderRefund() {
        List<HeOrderRefundEntity> heOrderRefundEntities = SpringContextHolder.getBean(OrderRefundRepository.class).getRefundByOrderId(orderId);
        if (CollectionUtils.isNotEmpty(heOrderRefundEntities)) {

            HeOrderRefundEntity heOrderRefundEntity = heOrderRefundEntities.stream().filter(item -> Objects.nonNull(item.getStatus()) && item.getStatus() == 1 && StringUtil.isEmpty(item.getIncomeSn())).findFirst().orElse(null);
            return heOrderRefundEntity;
        }
        return null;
    }

    /**
     * 待收款 =  支付状态：0待支付，1未全付
     *
     * @return
     */
    public boolean isWaitPay() {
        return Objects.equals(this.getPayStatus(), PayStatusV2Enum.WAIT_PAY.getCode()) || Objects.equals(this.getPayStatus(), PayStatusV2Enum.NO_PAY_OFF.getCode());
    }

    public boolean isDepositOrder() {
        return OmniOrderTypeEnum.DEPOSIT_ORDER.code().equals(this.getOrderType());
    }

    /**
     * 只有订单状态为待付款、待发货、待收货才允许退款！
     *
     * @return
     */
    public boolean isForbidRefund() {
        if (isDepositOrder()) {
            return false;
        }
        return this.isNewOrder() && !this.isWaitPay() && !isWaitSendGoods() && !isSendGoods();
    }

    //  待发货 =  订单状态：待入住

    /**
     * 待发货 =  订单状态：待入住
     *
     * @return
     */
    public boolean isWaitSendGoods() {
        return orderStatus.equals(OrderStatusV2Enum.TO_STAY_IN.getCode());
    }

    //  已发货 =  订单状态：已入住

    /**
     * 已发货 =  订单状态：已入住
     *
     * @return
     */
    public boolean isSendGoods() {
        return orderStatus.equals(OrderStatusV2Enum.STAY_IN.getCode());
    }

    public boolean isDiscountPass() {
        return Objects.nonNull(this.getApprovalDiscountStatus()) && (this.getApprovalDiscountStatus().equals(ApprovalDiscountStatusEnum.APPROVED.getCode()) || this.getApprovalDiscountStatus().equals(ApprovalDiscountStatusEnum.NO_APPROVAL_NEEDED.getCode()));
    }


    public Integer fetchSelectedAmount() {
        List<HeOrderGoodsEntity> allItermByOrderIdList = SpringContextHolder.getBean(OrderGoodsRepository.class).getAllItermByOrderId(this.getOrderId());
        if (CollectionUtils.isEmpty(allItermByOrderIdList)) {
            return 0;
        }
        //先把非礼赠和节假日费用过滤掉
        List<HeOrderGoodsEntity> commonGoodsList = allItermByOrderIdList.stream().filter(o -> !o.isGift() && !o.isAddition()).collect(Collectors.toList());
        List<String> commonGoodsListOrderGoodsSn = commonGoodsList.stream().map(HeOrderGoodsEntity::getOrderGoodsSn).collect(Collectors.toList());
        //再把当前商品的节假日找出来
        List<HeOrderGoodsEntity> holidayGoodsList = allItermByOrderIdList.stream().filter(o -> o.isAddition() && commonGoodsListOrderGoodsSn.contains(o.getParentCombineSn())).collect(Collectors.toList());

        //可以计算价格了
        //节假日多胞胎费用
        Integer holidayGoodsAmount = holidayGoodsList.stream().mapToInt(o -> o.getGoodsNum() * o.getGoodsPriceOrgin()).reduce(Integer::sum).orElse(0);

        //选购商品金额
        Integer selectedGoodsAmount = commonGoodsList.stream().filter(o -> StringUtil.isBlank(o.getParentCombineSn())).map(o -> o.getGoodsNum() * o.getGoodsPriceOrgin()).reduce(Integer::sum).orElse(0);

        return holidayGoodsAmount + selectedGoodsAmount;
    }


    //订单为入住中/已离店/已关闭/提前离馆 或者 订单已经已经核销过
    public Boolean verification() {
        log.info("订单的核销状态：{},{}", this.orderSn, this.getVerificationStatus());
        return ObjectUtil.isNotEmpty(this.getVerificationStatus()) && this.getVerificationStatus().equals(BooleanUtil.toInt(true));
    }

    public Boolean checkIn() {
        Result<Date> checkIn = SpringContextHolder.getBean(OrderQueryService.class).getCheckIn(this.getOrderSn());
        log.info("订单的入住时间：{}", JsonUtil.write(checkIn.getData()));
        return ObjectUtil.isNotEmpty(checkIn.getData());
    }
}

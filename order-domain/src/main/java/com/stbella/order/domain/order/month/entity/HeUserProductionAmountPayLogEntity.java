package com.stbella.order.domain.order.month.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "HeUserProductionAmountPayLogPO对象", description = "产康金支付记录")
public class HeUserProductionAmountPayLogEntity {

    private Integer id;

    @ApiModelProperty(value = "用户basic_id")
    private Integer basicId;

    @ApiModelProperty(value = "用户产康金id")
    private Integer productionId;

    @ApiModelProperty(value = "支付订单id")
    private Integer orderId;

    @ApiModelProperty(value = "支付产康金(单位:分)")
    private Integer payAmount;

    @ApiModelProperty(value = "已退产康金(单位:分)")
    private Integer refundAmount;

    @ApiModelProperty(value = "冻结产康金(单位:分)")
    private Integer freezeAmount = 0;

    @ApiModelProperty(value = "状态1: 支付成功 2:部分退款 3:已退完")
    private Integer status;

    @ApiModelProperty(value = "支付时间")
    private Long payTime;

    @ApiModelProperty(value = "操作人")
    private Integer applyUserId;

    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty(value = "是否删除：1是，0否")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Long createdAt;

    @ApiModelProperty(value = "最近更新时间")
    private Long updatedAt;
}

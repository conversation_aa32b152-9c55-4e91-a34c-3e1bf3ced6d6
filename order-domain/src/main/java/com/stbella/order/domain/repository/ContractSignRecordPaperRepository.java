package com.stbella.order.domain.repository;

import com.stbella.order.domain.order.month.entity.ContractSignRecordPaperEntity;
import com.stbella.order.server.contract.req.CreatePaperContractReq;
import com.stbella.order.server.contract.req.MonthContractSignQuery;

import java.util.List;

/**
 * <p>
 * 纸质合同表 服务类
 * </p>
 *
 * <AUTHOR> @since 2022-11-02
 */
public interface ContractSignRecordPaperRepository {

    /**
     * 保存纸质合同
     *
     * @param createPaperContractReq 参数
     * @return
     */
    Integer savePaperContract(CreatePaperContractReq createPaperContractReq);

    List<ContractSignRecordPaperEntity> getByOrderIdList(List<Integer> orderIdList);

    List<ContractSignRecordPaperEntity> getByOrderId(Integer orderId);

    List<ContractSignRecordPaperEntity> getByOrderId(Integer orderId, Integer contractStatus);

    List<ContractSignRecordPaperEntity> getByOrderId(Integer orderId, Integer contractStatus, Integer templateType);

    List<ContractSignRecordPaperEntity> queryListByCondition(MonthContractSignQuery monthContractSignQuery);

    ContractSignRecordPaperEntity queryById(Integer id);

    Boolean deleteById(Integer id);

    boolean updateOneById(ContractSignRecordPaperEntity contractSignRecordPaperEntity);


}

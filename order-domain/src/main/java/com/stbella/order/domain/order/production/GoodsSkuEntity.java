package com.stbella.order.domain.order.production;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.stbella.core.base.Operator;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * 商品SKU
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2022/09/30
 * @since 2022-09-30 15:59
 */
@Data
@Slf4j
public class GoodsSkuEntity implements Serializable {

    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商品id
     */
    private Integer goodsId;

    /**
     * 规格名称
     */
    private String skuName;

    /**
     * 原价（单位分）
     */
    private Integer originPrice;

    /**
     * 当前价（单位分）
     */
    private Integer goodsPrice;

    /**
     * 库存
     */
    private Integer stock;

    /**
     * 是否删除：1是，0否
     */
    private Boolean isDelete;

    /**
     * 创建时间
     */
    private Long createdAt;

    /**
     * 最近更新时间
     */
    private Long updatedAt;

    /**
     * 成本价
     */
    private Integer costPrice;

    /**
     * 规格编码
     */
    private String specCode;

    /**
     * 购买需要积分数量 默认0无需积分
     */
    private Integer integral;

    /**
     * 核销选项 “,”分隔
     */
    private String optionsVerification;

    /**
     * 模板sku id (门店套餐有用，其他为0)
     */
    private Integer templateSkuId;

    /**
     * 绑定的sku的规格数据
     */
    private String templateSkuProp;

    /**
     * sku 销售状态 ：0下架，1上架
     */
    private Integer skuSaleState;

    /**
     * 排序 升序
     */
    private Integer sort;

    /**
     * 服务时长 0:30分钟,1:40分钟,2:45分钟,3:60分钟;4:90分钟;5:180分钟;6:120分钟
     */
    private Integer serviceTime;

    /**
     * 产康师等级与手工费配置 json
     */
    private String producerConfig;

    /**
     * 子项目 服务类型 0:自营 1:三方(spu 的字段)
     */
    private Integer serviceType;

    private Integer skuNum;

    /**
     * 结算比例
     */
    private Integer scale;

    public void create(Operator operator) {

    }
}

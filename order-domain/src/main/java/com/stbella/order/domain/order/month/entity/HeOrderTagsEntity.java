package com.stbella.order.domain.order.month.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class HeOrderTagsEntity {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "标签名称")
    private String tagName;

    @ApiModelProperty(value = "标签对象：0=客户 1=到家雇主 2=到家育婴师 3=商品4=UGC平台 5=订单 6=产康服务")
    private Integer tagObj;

    @ApiModelProperty(value = "标签类型:0无逻辑，N+有逻辑（1客户无效标签）2=老客户逻辑 3=亲友逻辑 4=馆外网红逻辑")
    private Integer tagType;

    @ApiModelProperty(value = "标签分组id")
    private Integer tagGroupId;

    @ApiModelProperty(value = "是否同步到C端，0=否 1=是")
    private Boolean syncC;

    @ApiModelProperty(value = "来源 0 - 后台 1 - 前台")
    private Boolean source;

    @ApiModelProperty(value = "创建人basic_uid")
    private Integer basicUid;

    @ApiModelProperty(value = "0)否；1是")
    private Integer isPiShow;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "是否删除:0=否,1=是")
    private Boolean isDelete;

    @ApiModelProperty(value = "创建时间")
    private Integer createdAt;

    @ApiModelProperty(value = "更新时间")
    private Integer updatedAt;


}

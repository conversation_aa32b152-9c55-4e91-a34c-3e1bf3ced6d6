package com.stbella.order.domain.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.stbella.base.server.sms.enums.SmsTemplateV2Enum;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.customer.server.ecp.entity.UserPO;
import com.stbella.customer.server.ecp.service.UserService;
import com.stbella.notice.entity.OaProcessIdRelationPO;
import com.stbella.notice.entity.OaProcessRecordPO;
import com.stbella.notice.server.OaProcessIdRelationService;
import com.stbella.notice.server.OaProcessRecordService;
import com.stbella.order.common.constant.OtherConstant;
import com.stbella.order.common.enums.core.*;
import com.stbella.order.common.enums.month.OrderStatusV2Enum;
import com.stbella.order.common.enums.month.PayStatusV2Enum;
import com.stbella.order.common.enums.month.RefundReasonEnum;
import com.stbella.order.common.enums.month.RefundRecordPayStatusEnum;
import com.stbella.order.common.utils.JsonUtil;
import com.stbella.order.domain.client.BaseClient;
import com.stbella.order.domain.convert.OmniOrderRefundConverter;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.service.OrderRefundDomainService;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.contract.service.month.MonthContractAuthenticationService;
import com.stbella.order.server.order.cts.enums.DingApproveStatusEnum;
import com.stbella.order.server.order.cts.response.order.PayRefundResult;
import com.stbella.order.server.order.month.enums.CustomerComplaintsStatusEnum;
import com.stbella.order.server.order.month.enums.PayMethodEnum;
import com.stbella.order.server.order.month.enums.PayStatusEnum;
import com.stbella.order.server.order.month.enums.RefundTypeEnum;
import com.stbella.order.server.order.month.service.OrderPayV2Service;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import com.stbella.platform.order.api.refund.req.QueryQueryEquallyGoodsReq;
import com.stbella.platform.order.api.refund.res.*;
import com.stbella.platform.order.api.res.QueryCombinationAdditionalRes;
import com.stbella.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class OrderRefundDomainServiceImpl implements OrderRefundDomainService {

    @Resource
    private OrderGoodsRepository orderGoodsRepository;
    @Resource
    private IncomePaidAllocationRepository incomePaidAllocationRepository;
    @Resource
    private HeOrderRefundGoodsRepository orderRefundGoodsRepository;
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private IncomeRecordRepository incomeRecordRepository;
    @DubboReference
    private OaProcessRecordService oaProcessRecordService;
    @Resource
    private BaseClient baseClient;
    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;
    @Resource
    private MonthContractAuthenticationService contractAuthenticationService;
    @DubboReference
    private OaProcessIdRelationService oaProcessIdRelationService;
    @Resource
    private RedisService redisService;
    @Resource
    private OrderPayV2Service orderPayV2Service;
    @Resource
    private OmniOrderRefundConverter omniOrderRefundConverter;
    @Value("${pay.wechatPay.periodValidity}")
    private Long wechatPeriodValidity;
    @Value("${pay.aliPay.periodValidity}")
    private Long aliPayPeriodValidity;
    @Value("${pay.posPay.periodValidity}")
    private Long posPayPeriodValidity;
    @Value("${pay.expirationCountdown}")
    private Long expirationCountdown;
    @Resource
    private OrderRefundApplyGoodsRepository orderRefundApplyGoodsRepository;
    @Resource
    private OmniOrderRefundConverter orderRefundConverter;
    @Resource
    private IncomePaidAllocationRepository paidAllocationRepository;
    @Resource
    private HeCustomerComplaintsRepository customerComplaintsRepository;
    @DubboReference
    private UserService userService;

    /**
     * 查询订单商品 ，返回退款统计
     *
     * @param orderId
     * @return
     */
    @Override
    public List<QueryOrderRefundGoodsInfoRes> queryOrderGoodsWithRefundStatics(Integer orderId) {
        List<QueryOrderRefundGoodsInfoRes> result = new ArrayList<>();
        //获取当前商品的列表
        List<HeOrderGoodsEntity> orderGoods = orderGoodsRepository.getByOrderIdList(Arrays.asList(orderId));
        //支付记录
        List<IncomePaidAllocationEntity> incomePaidAllocationEntities = incomePaidAllocationRepository.queryListByOrderId(orderId.longValue());
        List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList = orderRefundGoodsRepository.queryByOrderId(orderId);

        for (HeOrderGoodsEntity orderGoodsEntity : orderGoods) {
            QueryOrderRefundGoodsInfoRes queryOrderRefundGoodsInfoRes = new QueryOrderRefundGoodsInfoRes();
            queryOrderRefundGoodsInfoRes.setOrderGoodsId(orderGoodsEntity.getId().longValue());
            queryOrderRefundGoodsInfoRes.setOrderGoodsSn(orderGoodsEntity.getOrderGoodsSn());
            queryOrderRefundGoodsInfoRes.setParentCombineSn(orderGoodsEntity.getParentCombineSn());
            queryOrderRefundGoodsInfoRes.setGoodsNum(orderGoodsEntity.getGoodsNum());
            queryOrderRefundGoodsInfoRes.setPayAmount(RMBUtils.bigDecimalF2Y(orderGoodsEntity.getPayAmount()));
            queryOrderRefundGoodsInfoRes.setGoodsType(orderGoodsEntity.getGoodsType());
            queryOrderRefundGoodsInfoRes.setGoodsName(orderGoodsEntity.getGoodsName());
            queryOrderRefundGoodsInfoRes.setSkuName(orderGoodsEntity.getSkuName());
            queryOrderRefundGoodsInfoRes.setGoodsId(orderGoodsEntity.getGoodsId());
            queryOrderRefundGoodsInfoRes.setSkuId(orderGoodsEntity.getSkuId());
            queryOrderRefundGoodsInfoRes.setType(orderGoodsEntity.getType());
            List<IncomePaidAllocationEntity> incomePaidAllocationEntityList = incomePaidAllocationEntities.stream().filter(i -> i.getOrderGoodsSn().equals(orderGoodsEntity.getOrderGoodsSn())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(incomePaidAllocationEntityList)) {
                //当前已付总金额
                BigDecimal paidAmount = RMBUtils.bigDecimalF2Y(incomePaidAllocationEntityList.stream().mapToInt(IncomePaidAllocationEntity::getPaidAmount).sum());
                queryOrderRefundGoodsInfoRes.setPaidAmount(paidAmount);
            }

            //商品的退款记录（进行中+已成功）
            List<HeOrderRefundGoodsEntity> goodsRefundList = orderRefundGoodsEntityList.stream().filter(i -> i.getOrderGoodsSn().equals(orderGoodsEntity.getOrderGoodsSn())).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(goodsRefundList)) {
                //进行中
                List<HeOrderRefundGoodsEntity> freezeList = goodsRefundList.stream().filter(g -> g.getStatus().equals(OrderRefundGoodsStatusEnum.REFUNDING.code())).collect(Collectors.toList());
                //成功
                List<HeOrderRefundGoodsEntity> successList = goodsRefundList.stream().filter(g -> g.getStatus().equals(OrderRefundGoodsStatusEnum.SUCCESS.code())).collect(Collectors.toList());
                //冻结金额从商品退款表进行中的取
                BigDecimal freezeAmount = RMBUtils.bigDecimalF2Y(freezeList.stream().mapToInt(HeOrderRefundGoodsEntity::getRefundAmount).sum());
                //冻结数量
                Integer freezeNum = freezeList.stream().mapToInt(HeOrderRefundGoodsEntity::getRefundNum).sum();
                //已退金额
                BigDecimal successAmount = RMBUtils.bigDecimalF2Y(successList.stream().mapToInt(HeOrderRefundGoodsEntity::getRefundAmount).sum());
                //已退数量
                Integer successNum = successList.stream().mapToInt(HeOrderRefundGoodsEntity::getRefundNum).sum();
                queryOrderRefundGoodsInfoRes.setFreezeAmount(freezeAmount);
                queryOrderRefundGoodsInfoRes.setFreezeNum(freezeNum);
                queryOrderRefundGoodsInfoRes.setAlreadyRefundAmount(successAmount);
                queryOrderRefundGoodsInfoRes.setAlreadyRefundNum(successNum);
                //退货退款、仅退款、退回重付、未知取最新的
                queryOrderRefundGoodsInfoRes.setRefundNature(goodsRefundList.get(goodsRefundList.size() - 1).getRefundNature());
            }
            queryOrderRefundGoodsInfoRes.setRefundGoodsNum(queryOrderRefundGoodsInfoRes.getGoodsNum() - queryOrderRefundGoodsInfoRes.getFreezeNum() - queryOrderRefundGoodsInfoRes.getAlreadyRefundNum());
            queryOrderRefundGoodsInfoRes.setRefundPayAmount(queryOrderRefundGoodsInfoRes.getPaidAmount().subtract(queryOrderRefundGoodsInfoRes.getFreezeAmount()).subtract(queryOrderRefundGoodsInfoRes.getAlreadyRefundAmount()));
            result.add(queryOrderRefundGoodsInfoRes);
        }
        return result;
    }

    /**
     * 查询所有成功的退款分摊
     *
     * @param orderId
     * @return
     */
    @Override
    public List<HeOrderRefundGoodsEntity> querySuccessfulOrderGoodsAllocationRefund(Integer orderId) {

        List<HeOrderRefundEntity> refundByOrderId = orderRefundRepository.getRefundByOrderId(orderId);

        if (CollectionUtil.isEmpty(refundByOrderId)) {
            return new ArrayList<>();
        }
        List<HeOrderRefundEntity> orderRefundEntities = refundByOrderId.stream().filter(o -> o.getParentRefundOrderSn() == null && o.getStatus().equals(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode())).collect(Collectors.toList());
        List<String> refundOrderSnList = orderRefundEntities.stream().map(HeOrderRefundEntity::getRefundOrderSn).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(refundOrderSnList)) {
            return new ArrayList<>();
        }

        List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList = orderRefundGoodsRepository.queryByOrderId(orderId);
        List<HeOrderRefundGoodsEntity> successfulRefundList = orderRefundGoodsEntityList.stream().filter(i -> i.getStatus().equals(OrderRefundGoodsStatusEnum.SUCCESS.code()) && refundOrderSnList.contains(i.getRefundOrderSn())).collect(Collectors.toList());
        return successfulRefundList;
    }

    @Override
    public void afterRefundSms(Integer refundId) {
        //从企微审批表中获取
        OaProcessRecordPO byRefundId = oaProcessRecordService.getByRefundId(refundId);
        HeOrderRefundEntity refundEntity = orderRefundRepository.getOneById(refundId);
        HeOrderEntity orderEntity = orderRepository.getByOrderId(refundEntity.getOrderId());
        //退款金额
        BigDecimal refundAmount = RMBUtils.bigDecimalF2Y(refundEntity.getApplyAmount());
        if (ObjectUtil.isNotEmpty(byRefundId)) {
            String params = byRefundId.getBody();
            JSONObject jsonObject = JSONUtil.toBean(params, JSONObject.class);
            String customerName = (String) jsonObject.get("customerName");
            String phone = (String) jsonObject.get("phone");
            try {
                log.info("退款成功发送给发起人:" + phone);
                //发给发起人（审批发起人）
                baseClient.sendMessage(phone, SmsTemplateV2Enum.HELPER_REFUND_ORIGINAL_BACK_USER, new String[]{customerName, refundAmount.setScale(2).toEngineeringString()});
            } catch (Exception e) {
                log.error("退款成功发送给发起人异常：{}，手机号：{}，内容：{}", e.getMessage(), phone, new String[]{customerName, refundAmount.setScale(2).toEngineeringString()});
            }
        } else {
            log.error("退款回调无法找到审批数据：oneByRefundOrderSn.getId" + refundId);
        }
        //用户订单用户快照
        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(refundEntity.getOrderId());
        if (ObjectUtil.isNotEmpty(heOrderUserSnapshotEntity)) {

            try {
                log.info("退款成功发送给客户:" + heOrderUserSnapshotEntity.getPhone());
                //发给客户（订单快照）
                baseClient.sendMessage(heOrderUserSnapshotEntity.getPhone(), SmsTemplateV2Enum.HELPER_REFUND_ORIGINAL_BACK_CLIENT, new String[]{orderEntity.getOrderSn().toString(), refundAmount.setScale(2).toEngineeringString()});
            } catch (Exception e) {
                log.error("退款成功发送给客户异常：{}，手机号：{}，内容：{}", e.getMessage(), heOrderUserSnapshotEntity.getPhone(), new String[]{orderEntity.getOrderId().toString(), refundAmount.setScale(2).toEngineeringString()});
            }
        } else {
            log.error("退款回调没有找到订单客户快照");
        }
    }

    @Override
    public void afterRefundSuccess(Integer refundId) {
        HeOrderRefundEntity orderRefundEntity = orderRefundRepository.getOneById(refundId);
        HeOrderEntity orderEntity = orderRepository.getByOrderId(orderRefundEntity.getOrderId());
        List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList = orderRefundGoodsRepository.queryByOrderId(orderRefundEntity.getOrderId());

        if (ObjectUtil.isNotEmpty(orderEntity) && ObjectUtil.isNotEmpty(orderEntity.getVersion()) && orderEntity.getVersion().compareTo(new BigDecimal(3.0)) >= 0) {
            //子项退款
            List<HeOrderRefundEntity> refundByParentSn = orderRefundRepository.getRefundByParentSn(orderRefundEntity.getRefundOrderSn());
            //子项退款的支付
            List<HeIncomeRecordEntity> recordListByIncomeSnList = incomeRecordRepository.getRecordListByIncomeSnList(refundByParentSn.stream().map(HeOrderRefundEntity::getIncomeSn).collect(Collectors.toList()));
            orderRefundGoodsEntityList = orderRefundGoodsEntityList.stream().filter(r -> r.getRefundOrderSn().equals(orderRefundEntity.getRefundOrderSn())).collect(Collectors.toList());

            //退款主项
            orderRefundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode());
            orderRefundEntity.setTransactionId("");//无第三方流水
            orderRefundEntity.setFinishAt(System.currentTimeMillis() / 1000);
            orderRefundEntity.setActualAmount(orderRefundEntity.getApplyAmount());
            //退款子项
            for (HeOrderRefundEntity heOrderRefundEntity : refundByParentSn) {
                heOrderRefundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode());
                heOrderRefundEntity.setTransactionId("");//无第三方流水
                heOrderRefundEntity.setFinishAt(System.currentTimeMillis() / 1000);
                heOrderRefundEntity.setActualAmount(heOrderRefundEntity.getApplyAmount());

                Optional<HeIncomeRecordEntity> first = recordListByIncomeSnList.stream().filter(r -> r.getIncomeSn().equals(heOrderRefundEntity.getIncomeSn())).findFirst();
                if (first.isPresent()) {
                    //支付记录修改
                    HeIncomeRecordEntity heIncomeRecordEntity = first.get();
                    heIncomeRecordEntity.setFreezeAmount(heIncomeRecordEntity.getFreezeAmount() - heOrderRefundEntity.getApplyAmount());
                    heIncomeRecordEntity.setAlreadyRefundAmount(heIncomeRecordEntity.getAlreadyRefundAmount() + heOrderRefundEntity.getApplyAmount());
                }
            }

            //订单退款商品表
            for (HeOrderRefundGoodsEntity heOrderRefundGoodsEntity : orderRefundGoodsEntityList) {
                heOrderRefundGoodsEntity.setStatus(OrderRefundGoodsStatusEnum.SUCCESS.code());
            }

            //订单实际金额改变
            orderEntity.setRealAmount(orderEntity.getRealAmount() - orderRefundEntity.getApplyAmount());
            orderEntity.setFreezeAmount(orderEntity.getFreezeAmount() - orderRefundEntity.getApplyAmount());

            //更新订单
            orderRepository.updateOne(orderEntity);
            //更新订单退款商品
            orderRefundGoodsRepository.updateList(orderRefundGoodsEntityList);
            //更新支付记录表
            refundByParentSn.add(orderRefundEntity);
            //更新退款记录表
            orderRefundRepository.batchUpdateById(refundByParentSn);
            //子项支付修改
            incomeRecordRepository.batchUpdateRecordList(recordListByIncomeSnList);
        }
    }

    @Override
    public void afterRefundFail(Integer refundId) {
        HeOrderRefundEntity orderRefundEntity = orderRefundRepository.getOneById(refundId);
        HeOrderEntity orderEntity = orderRepository.getByOrderId(orderRefundEntity.getOrderId());
        List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList = orderRefundGoodsRepository.queryByOrderId(orderRefundEntity.getOrderId());

        if (ObjectUtil.isNotEmpty(orderEntity) && ObjectUtil.isNotEmpty(orderEntity.getVersion()) && orderEntity.getVersion().compareTo(new BigDecimal(3.0)) >= 0) {
            //子项退款
            List<HeOrderRefundEntity> refundByParentSn = orderRefundRepository.getRefundByParentSn(orderRefundEntity.getRefundOrderSn());
            //子项退款的支付
            List<HeIncomeRecordEntity> recordListByIncomeSnList = incomeRecordRepository.getRecordListByIncomeSnList(refundByParentSn.stream().map(HeOrderRefundEntity::getIncomeSn).collect(Collectors.toList()));
            orderRefundGoodsEntityList = orderRefundGoodsEntityList.stream().filter(r -> r.getRefundOrderSn().equals(orderRefundEntity.getRefundOrderSn())).collect(Collectors.toList());

            //退款主项
            orderRefundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_2.getCode());
            orderRefundEntity.setTransactionId("");//无第三方流水
            //退款子项
            for (HeOrderRefundEntity heOrderRefundEntity : refundByParentSn) {
                heOrderRefundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_2.getCode());
                heOrderRefundEntity.setTransactionId("");//无第三方流水

                Optional<HeIncomeRecordEntity> first = recordListByIncomeSnList.stream().filter(r -> r.getIncomeSn().equals(heOrderRefundEntity.getIncomeSn())).findFirst();
                if (first.isPresent()) {
                    //支付记录修改
                    HeIncomeRecordEntity heIncomeRecordEntity = first.get();
                    heIncomeRecordEntity.setFreezeAmount(heIncomeRecordEntity.getFreezeAmount() - heOrderRefundEntity.getApplyAmount());
                }
            }

            //订单退款商品表
            for (HeOrderRefundGoodsEntity heOrderRefundGoodsEntity : orderRefundGoodsEntityList) {
                heOrderRefundGoodsEntity.setStatus(OrderRefundGoodsStatusEnum.FAIL.code());
            }

            //订单实际金额改变
            orderEntity.setFreezeAmount(orderEntity.getFreezeAmount() - orderRefundEntity.getApplyAmount());

            //更新订单
            orderRepository.updateOne(orderEntity);
            //更新订单退款商品
            orderRefundGoodsRepository.updateList(orderRefundGoodsEntityList);
            //更新支付记录表
            refundByParentSn.add(orderRefundEntity);
            //更新退款记录表
            orderRefundRepository.batchUpdateById(refundByParentSn);
            //子项支付修改
            incomeRecordRepository.batchUpdateRecordList(recordListByIncomeSnList);
        }
    }

    @Override
    public List<OrderRefundListRes> refundListByOrderId(Integer orderId) {
        HeOrderEntity orderEntity = orderRepository.getByOrderId(orderId);
        if (ObjectUtil.isEmpty(orderEntity)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "订单不存在!");
        }
        List<OrderRefundListRes> result = new ArrayList<>();
        List<HeOrderRefundEntity> refundByOrderId = orderRefundRepository.getRefundByOrderId(orderId);
        //获取主的退款记录
        List<HeOrderRefundEntity> parentRefund = refundByOrderId.stream().filter(r -> StringUtils.isEmpty(r.getParentRefundOrderSn())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(parentRefund)) {
            for (HeOrderRefundEntity refundEntity : parentRefund) {
                OrderRefundListRes orderRefundRes = new OrderRefundListRes();
                orderRefundRes.setId(refundEntity.getId());
                orderRefundRes.setRefundOrderSn(refundEntity.getRefundOrderSn());

                List<HeOrderRefundEntity> child = refundByOrderId.stream().filter(item ->
                        StringUtils.isNotEmpty(item.getParentRefundOrderSn())
                                && item.getParentRefundOrderSn().equals(refundEntity.getRefundOrderSn())
                                && !OmniPayTypeEnum.REDUCTION.getCode().equals(item.getRefundType())).collect(Collectors.toList());
                orderRefundRes.setApplyAmount(RMBUtils.bigDecimalF2Y(child.stream().mapToInt(HeOrderRefundEntity::getApplyAmount).sum()));

                orderRefundRes.setRefundStatus(refundEntity.getStatus());
                orderRefundRes.setRefundStatusStr(OmniRefundApproveEnum.getName(refundEntity.getStatus()));
                orderRefundRes.setSubmissionTime(DateUtil.formatDateTime(new Date(refundEntity.getCreatedAt() * 1000)));
                orderRefundRes.setCurrency(orderEntity.getCurrency());
                List<HeOrderRefundEntity> ckj = refundByOrderId.stream().filter(r -> {

                    return StringUtils.isNotEmpty(r.getParentRefundOrderSn()) && r.getParentRefundOrderSn().equals(refundEntity.getRefundOrderSn()) && r.getRefundType().equals(OmniPayTypeEnum.PRODUCTION_COIN.getCode());

                }).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(ckj)) {
                    Integer ckjSum = ckj.stream().mapToInt(HeOrderRefundEntity::getApplyAmount).sum();
                    orderRefundRes.setApplyCkjAmount(RMBUtils.bigDecimalF2Y(ckjSum));
                }
                result.add(orderRefundRes);
            }
        }
        return result;
    }

    @Override
    public OrderRefundDetailsRes refundDetails(Integer refundId) {
        OrderRefundDetailsRes result = new OrderRefundDetailsRes();
        HeOrderRefundEntity orderRefundEntity = orderRefundRepository.getOneById(refundId);
        if (ObjectUtil.isEmpty(orderRefundEntity)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "退款不存在！");
        }
        HeOrderEntity orderEntity = orderRepository.getByOrderId(orderRefundEntity.getOrderId());
        if (ObjectUtil.isEmpty(orderEntity)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "订单不存在！");
        }


        Integer payStatus = orderEntity.getPayStatus();
        Integer orderStatus = orderEntity.getOrderStatus();
        PayStatusV2Enum payStatusEnum = PayStatusV2Enum.getEnumByCode(payStatus);
        OrderStatusV2Enum orderStatusEnum = OrderStatusV2Enum.getByCode(orderStatus);
        boolean waitPay = payStatusEnum.equals(PayStatusV2Enum.WAIT_PAY) || payStatusEnum.equals(PayStatusV2Enum.NO_PAY_OFF);
        boolean waitSendGoods = orderStatusEnum.equals(OrderStatusV2Enum.TO_STAY_IN);
        boolean sendGoods = orderStatusEnum.equals(OrderStatusV2Enum.STAY_IN);
        if (waitPay) {
            result.setOrderStatus(0);
        } else if (waitSendGoods) {
            result.setOrderStatus(1);
        } else if (sendGoods) {
            result.setOrderStatus(2);
        } else {
            result.setOrderStatus(3);
        }
        List<HeOrderGoodsEntity> byOrderIdList = orderGoodsRepository.getByOrderIdList(Arrays.asList(orderRefundEntity.getOrderId()));


        //基本信息
        result.setCurrency(orderEntity.getCurrency());
        result.setOrderId(orderRefundEntity.getOrderId());
        result.setClientUid(orderEntity.getClientUid());
        result.setStoreId(orderEntity.getStoreId());
        Integer applyAmount = orderRefundEntity.getApplyAmount();
        result.setRefundAmount(RMBUtils.bigDecimalF2Y(applyAmount));
        result.setRefundReasonTypeStr(orderRefundEntity.getRefundReasonType() == 1 ? "正常退款" : "⾮正常退款（客诉）");
        result.setRefundReasonType(orderRefundEntity.getRefundReasonType());
        result.setRefundReasonStr(RefundReasonEnum.getName(orderRefundEntity.getRefundReason()));
        result.setRefundReason(orderRefundEntity.getRefundReason());

        result.setRemark(orderRefundEntity.getRemark());
        String refundInfo = orderRefundEntity.getRefundInfo();
        LinkedHashMap read = JsonUtil.read(refundInfo, LinkedHashMap.class);
        result.setAuditRefundProof((List<String>) read.get("images"));
        result.setAccountBank((String) read.get("bank_name"));
        result.setAccountName((String) read.get("name"));
        result.setBankNo((String) read.get("bank_number"));
        result.setRefundStatus(orderRefundEntity.getStatus());
        result.setRefundStatusStr(OmniRefundApproveEnum.getName(orderRefundEntity.getStatus()));
        result.setIsShowContractButton(BooleanUtil.toInt(contractAuthenticationService.signMasterContract(new Long(orderEntity.getOrderId())).getData()));
        result.setDeposit(orderEntity.getOrderType().equals(OmniOrderTypeEnum.DEPOSIT_ORDER.code()));
        OaProcessIdRelationPO refundFromByParamId = oaProcessIdRelationService.getRefundFromByParamId(orderRefundEntity.getId());
        if (ObjectUtil.isNotEmpty(refundFromByParamId)) {
            result.setApproveId(refundFromByParamId.getLocalProcessId());
        }
        List<OrderRefundDetailsRes.GoodsInfo> refundGoodsList = new ArrayList();

        //退款商品列表
        List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList = orderRefundGoodsRepository.queryByRefundOrderSn(orderRefundEntity.getRefundOrderSn());
        if (CollectionUtil.isEmpty(orderRefundGoodsEntityList)) {
            result.setRefundTypeStr(RefundTypeEnum.getValueByCode(orderRefundEntity.getRefundMethod()));
            result.setRefundAmount(RMBUtils.bigDecimalF2Y(applyAmount));
            result.setGoodsRefundTypeStr(OrderRefundNatureEnum.getValueByCode(orderRefundEntity.getRefundNature()));
            OrderRefundDetailsRes.GoodsInfo goodsInfo = new OrderRefundDetailsRes.GoodsInfo();
            CreateRefundReq.GoodsRefundAmountInfo goodsRefundAmountInfo = new CreateRefundReq.GoodsRefundAmountInfo();
            List<CreateRefundReq.GoodsRefundAmountInfo> goodsRefundAmountInfoList = new ArrayList<>();
            goodsRefundAmountInfo.setAmount(RMBUtils.bigDecimalF2Y(applyAmount));
            goodsRefundAmountInfo.setAmountType(0);
            goodsRefundAmountInfoList.add(goodsRefundAmountInfo);
            goodsInfo.setRefundAmount(RMBUtils.bigDecimalF2Y(applyAmount));
            goodsInfo.setRefundGoodsAmountInfoList(goodsRefundAmountInfoList);
            refundGoodsList.add(goodsInfo);
            result.setGoodsInfoList(refundGoodsList);
            return result;
        }
        HeOrderRefundGoodsEntity heOrderRefundGoodsEntity1 = orderRefundGoodsEntityList.get(0);

        result.setRefundTypeStr(RefundTypeEnum.getValueByCode(heOrderRefundGoodsEntity1.getRefundType()));
        result.setRefundType(heOrderRefundGoodsEntity1.getRefundType());
        result.setGoodsRefundTypeStr(OrderRefundNatureEnum.getValueByCode(heOrderRefundGoodsEntity1.getRefundNature()));
        result.setGoodsRefundType(heOrderRefundGoodsEntity1.getRefundNature());
        Map<String, List<HeOrderRefundGoodsEntity>> goodsSnMap = orderRefundGoodsEntityList.stream().collect(Collectors.groupingBy(HeOrderRefundGoodsEntity::getOrderGoodsSn));
        Integer sumTotal = 0;
        for (String goodsSn : goodsSnMap.keySet()) {
            OrderRefundDetailsRes.GoodsInfo goodsInfo = new OrderRefundDetailsRes.GoodsInfo();
            List<HeOrderRefundGoodsEntity> heOrderRefundGoodsEntities = goodsSnMap.get(goodsSn);

            List<CreateRefundReq.GoodsRefundAmountInfo> goodsRefundAmountInfoList = new ArrayList<>();
            for (PayMethodEnum value : PayMethodEnum.values()) {
                List<HeOrderRefundGoodsEntity> collect = heOrderRefundGoodsEntities.stream().filter(h -> h.getPaymentMethodByPayType().equals(value.getCode())).collect(Collectors.toList());
                int sum = 0;
                if (CollectionUtil.isNotEmpty(collect)) {
                    sum = collect.stream().mapToInt(HeOrderRefundGoodsEntity::getRefundAmount).sum();
                }
                CreateRefundReq.GoodsRefundAmountInfo goodsRefundAmountInfo = new CreateRefundReq.GoodsRefundAmountInfo();
                goodsRefundAmountInfo.setAmountType(Integer.valueOf(value.getCode()));
                goodsRefundAmountInfo.setAmount(RMBUtils.bigDecimalF2Y(sum));
                goodsRefundAmountInfo.setModel(value.getModel());
                goodsRefundAmountInfoList.add(goodsRefundAmountInfo);
                if (value.equals(PayMethodEnum.CASH)) {
                    goodsInfo.setRefundAmount(RMBUtils.bigDecimalF2Y(sum));
                }
                if (!PayMethodEnum.REDUCTION.equals(value)) {
                    sumTotal = sumTotal + sum;
                }
            }

            HeOrderRefundGoodsEntity heOrderRefundGoodsEntity = heOrderRefundGoodsEntities.get(0);
            goodsInfo.setRefundGoodsAmountInfoList(goodsRefundAmountInfoList);
            goodsInfo.setGoodsType(heOrderRefundGoodsEntity.getGoodsType());
            goodsInfo.setGoodsImage(heOrderRefundGoodsEntity.getGoodsImage());
            goodsInfo.setGoodsName(heOrderRefundGoodsEntity.getParentGoodsName());
            goodsInfo.setSkuName(heOrderRefundGoodsEntity.getSkuName());
            goodsInfo.setRefundNum(heOrderRefundGoodsEntity.getRefundNum());
            goodsInfo.setOrderGoodsId(heOrderRefundGoodsEntity.getOrderGoodsId());
            goodsInfo.setOrderGoodsSn(heOrderRefundGoodsEntity.getOrderGoodsSn());
            goodsInfo.setParentCombineSn(heOrderRefundGoodsEntity.getParentCombineSn());
            goodsInfo.setType(heOrderRefundGoodsEntity.getType());
            goodsInfo.setGoodsId(heOrderRefundGoodsEntity.getGoodsId());
            goodsInfo.setSkuId(heOrderRefundGoodsEntity.getSkuId());
            Optional<HeOrderGoodsEntity> first = byOrderIdList.stream().filter(b -> b.getOrderGoodsSn().equals(goodsInfo.getOrderGoodsSn())).findFirst();
            first.ifPresent(heOrderGoodsEntity -> goodsInfo.setGoodsNum(heOrderGoodsEntity.getGoodsNum()));
            goodsInfo.setRefundGoodsAmountInfoList(goodsRefundAmountInfoList);
            refundGoodsList.add(goodsInfo);
        }
        result.setRefundTotalAmount(RMBUtils.bigDecimalF2Y(sumTotal));
        result.setGoodsInfoList(refundGoodsList);
        return result;
    }

    private static void setRefundInfo(HeOrderRefundEntity refundEntity, HeOrderRefundEntity orderRefundEntity, HeOrderEntity orderEntity, List<OrderRefundProgressRes> result) {
        OrderRefundProgressRes orderRefundProgressRes = new OrderRefundProgressRes();
        orderRefundProgressRes.setId(refundEntity.getId());
        orderRefundProgressRes.setRefundAmount(RMBUtils.bigDecimalF2Y(refundEntity.getApplyAmount()));
        orderRefundProgressRes.setRefundType(refundEntity.getRefundType());
        orderRefundProgressRes.setRefundTypeStr(OmniRefundTypeEnum.getName(refundEntity.getRefundType()));
        orderRefundProgressRes.setStatus(refundEntity.getStatus());
        Integer refundType = refundEntity.getRefundType();
        OmniPayTypeEnum omniPayTypeEnum = OmniPayTypeEnum.getByCode(refundType);
        switch (omniPayTypeEnum) {
            case ALIPAY:
            case WECHAT:
            case ONLINE_POS:
            case BALANCE:
            case OTHER:
            case PRODUCTION_COIN:
                //线上统一用退款中
                orderRefundProgressRes.setStatusStr(OmniRefundApproveEnum.getName(orderRefundEntity.getStatus()));
                break;
            case OFFLINE:
                //线下显示等待财务打款
                orderRefundProgressRes.setStatusStr(OmniRefundApproveEnum.getOfflineName(orderRefundEntity.getStatus()));
                break;
        }
        orderRefundProgressRes.setCurrency(orderEntity.getCurrency());
        orderRefundProgressRes.setSubmissionTime(DateUtil.formatDateTime(new Date(refundEntity.getCreatedAt() * 1000)));
        if (ObjectUtil.isNotEmpty(refundEntity.getFinishAt()) && refundEntity.getFinishAt() > 0) {
            orderRefundProgressRes.setRefundSuccessTime(DateUtil.formatDateTime(new Date(refundEntity.getFinishAt() * 1000)));
        }
        orderRefundProgressRes.setRefundOrderSn(refundEntity.getRefundOrderSn());
        result.add(orderRefundProgressRes);
    }

    @Override
    public List<OrderRefundProgressRes> refundProgress(Integer refundId) {
        List<OrderRefundProgressRes> result = new ArrayList<>();
        HeOrderRefundEntity orderRefundEntity = orderRefundRepository.getOneById(refundId);
        if (ObjectUtil.isEmpty(orderRefundEntity)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "退款不存在！");
        }
        HeOrderEntity orderEntity = orderRepository.getByOrderId(orderRefundEntity.getOrderId());
        if (ObjectUtil.isEmpty(orderEntity)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "订单不存在！");
        }
        //获取子项
        List<HeOrderRefundEntity> childRefund = orderRefundRepository.getRefundByParentSn(orderRefundEntity.getRefundOrderSn());
        //过滤掉减免支付的
        childRefund  = childRefund.stream().filter(r -> !OmniPayTypeEnum.REDUCTION.getCode().equals(r.getRefundType())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(childRefund)) {
            for (HeOrderRefundEntity refundEntity : childRefund) {
                setRefundInfo(refundEntity, orderRefundEntity, orderEntity, result);
            }
        }
        return result;
    }

    /**
     * 审批中
     * 等待财务打款
     * 退款成功
     * 退款失败
     * 这四种需要判断
     *
     * @param refundId
     */
    @Override
    public void updateParentStatus(Integer refundId) {
        HeOrderRefundEntity parent = orderRefundRepository.getOneById(refundId);
        Integer status = parent.getStatus();
        if (ObjectUtil.isNotEmpty(parent) &&
                (!status.equals(RefundRecordPayStatusEnum.REFUND_RECORD_0.getCode()) &&
                        !status.equals(RefundRecordPayStatusEnum.REFUND_RECORD_2.getCode()))

        ) {
            List<HeOrderRefundEntity> child = orderRefundRepository.getRefundByParentSn(parent.getRefundOrderSn());
            log.info("refundId is {} child size is{}", refundId, child.size());
            //审批中
            long processing = child.stream().filter(c -> c.getStatus().equals(RefundRecordPayStatusEnum.REFUND_RECORD_1.getCode())).count();
            //退款成功
            long refundSuccess = child.stream().filter(c -> c.getStatus().equals(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode())).count();
            //等待财务打款
            long waitRefund = child.stream().filter(c -> c.getStatus().equals(RefundRecordPayStatusEnum.REFUND_RECORD_3.getCode())).count();
            //退款失败
            long refundFail = child.stream().filter(c -> c.getStatus().equals(RefundRecordPayStatusEnum.REFUND_RECORD_5.getCode())).count();

            if (processing > 0) {
                //有审批中，主的就是审批中
                parent.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_1.getCode());
            } else {
                //如果有一笔退款失败的
                if (refundFail > 0) {
                    parent.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_5.getCode());
                } else if (refundSuccess == child.size()) {
                    log.info("refundSuccess is {} child size is{}", refundSuccess, child.size());
                    //全部成功
                    parent.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_4.getCode());
                    HeCustomerComplaintsEntity customerComplaintsEntity = customerComplaintsRepository.selectByOrderRefundId(refundId);
                    if (ObjectUtil.isNotEmpty(customerComplaintsEntity)) {
                        customerComplaintsEntity.setComplaintStatus(CustomerComplaintsStatusEnum.SUCCESS.getCode());
                        customerComplaintsEntity.setResolveFinish(new Date());
                        customerComplaintsEntity.update();
                    }
                } else if (waitRefund == child.size()) {
                    //全部等待
                    parent.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_3.getCode());
                }
            }
            log.info("更新退款单主状态，数据为{}", com.alibaba.fastjson.JSONObject.toJSONString(parent));
            orderRefundRepository.updateOneById(parent);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approveFailOrRefuse(Integer refundId, RefundRecordPayStatusEnum refundRecordPayStatusEnum) {
        //本次退款Id
        HeOrderRefundEntity parentOrderRefundEntity = orderRefundRepository.getOneById(refundId);
        //获取子项
        List<HeOrderRefundEntity> refundByParentSn = orderRefundRepository.getRefundByParentSn(parentOrderRefundEntity.getRefundOrderSn());
        //退款的商品
        List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList = orderRefundGoodsRepository.queryByRefundOrderSn(parentOrderRefundEntity.getRefundOrderSn());
        //订单
        HeOrderEntity orderEntity = orderRepository.getByOrderId(parentOrderRefundEntity.getOrderId());
        //获取子项的income
        List<HeIncomeRecordEntity> recordListByIncomeSnList = incomeRecordRepository.getRecordListByIncomeSnList(refundByParentSn.stream().map(HeOrderRefundEntity::getIncomeSn).collect(Collectors.toList()));

        //回滚主项
        parentOrderRefundEntity.setStatus(refundRecordPayStatusEnum.getCode());
        //修改子项变失败
        for (HeOrderRefundEntity refundEntity : refundByParentSn) {
            refundEntity.setStatus(refundRecordPayStatusEnum.getCode());
            //支付记录冻结金额变少
            Optional<HeIncomeRecordEntity> first = recordListByIncomeSnList.stream().filter(r -> r.getIncomeSn().equals(refundEntity.getIncomeSn())).findFirst();
            first.ifPresent(heIncomeRecordEntity -> heIncomeRecordEntity.setFreezeAmount(heIncomeRecordEntity.getFreezeAmount() - refundEntity.getApplyAmount()));
        }

        //只有退回重付才去更新订单的冻结金
        if (orderRefundGoodsEntityList.get(0).getRefundNature().equals(OrderRefundNatureEnum.TEMP_REFUND.code())) {
            //订单的冻结金额变少
            orderEntity.setFreezeAmount(orderEntity.getFreezeAmount() - parentOrderRefundEntity.getApplyAmount());
        }

        for (HeOrderRefundGoodsEntity heOrderRefundGoodsEntity : orderRefundGoodsEntityList) {
            heOrderRefundGoodsEntity.setStatus(OrderRefundGoodsStatusEnum.FAIL.code());
        }

        //恢复退款商品数据
        orderRefundGoodsRepository.updateList(orderRefundGoodsEntityList);

        //恢复退款
        refundByParentSn.add(parentOrderRefundEntity);
        orderRefundRepository.batchUpdateById(refundByParentSn);

        //恢复支付
        incomeRecordRepository.batchUpdateRecordList(recordListByIncomeSnList);

        //恢复订单
        orderRepository.updateOne(orderEntity);

        //更新最新的状态
        updateParentStatus(parentOrderRefundEntity.getId());
    }

    @Override
    public QueryCombinationAdditionalRes getCombinationAddition(Integer orderId, String combinationGoodsSn, Integer goodsType) {

        QueryCombinationAdditionalRes result = new QueryCombinationAdditionalRes();
        result.setCombinationGoodsSn(combinationGoodsSn);
        result.setGoodsType(goodsType);


        //总数量
        Integer allNum = 0;
        //应退数量
        Integer refundGoodsNum = 0;
        //冻结数量
        Integer freezeNum = 0;
        //已退数量
        Integer alreadyRefundNum = 0;

        //应付总
        BigDecimal payAmount = BigDecimal.ZERO;
        //应退
        BigDecimal refundPayAmount = BigDecimal.ZERO;
        //已付
        BigDecimal paidAmount = BigDecimal.ZERO;
        //冻结
        BigDecimal freezeAmount = BigDecimal.ZERO;
        //已退
        BigDecimal alreadyRefundAmount = BigDecimal.ZERO;

        //获取订单的商品列表
        List<HeOrderGoodsEntity> orderGoodsEntityList = orderGoodsRepository.getByOrderIdList(Arrays.asList(orderId));

        //获取这个组合的子商品
        List<HeOrderGoodsEntity> childGoodsList = orderGoodsEntityList.stream().filter(o -> StringUtils.isNotEmpty(o.getParentCombineSn()) && o.getParentCombineSn().equals(combinationGoodsSn)).collect(Collectors.toList());

        //这个订单的退款记录
        List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList = orderRefundGoodsRepository.queryByOrderId(orderId);

        List<String> orderGoodsSnList = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(childGoodsList)) {
            for (HeOrderGoodsEntity child : childGoodsList) {
                //获取这个子商品的某个加收项
                List<HeOrderGoodsEntity> additionList = orderGoodsEntityList.stream().filter(o -> StringUtils.isNotEmpty(o.getParentCombineSn()) && o.getParentCombineSn().equals(child.getOrderGoodsSn()) && o.getGoodsType().equals(goodsType)).collect(Collectors.toList());
                for (HeOrderGoodsEntity orderGoodsEntity : additionList) {
                    allNum += orderGoodsEntity.getGoodsNum();
                    orderGoodsSnList.add(orderGoodsEntity.getOrderGoodsSn());
                    payAmount = payAmount.add(RMBUtils.bigDecimalF2Y(orderGoodsEntity.getPayAmount()));
                }
            }

            //这个订单所有已支付
            List<IncomePaidAllocationEntity> incomePaidAllocationEntities = incomePaidAllocationRepository.queryListByOrderId(orderId.longValue());

            //所有符合的已支付
            List<IncomePaidAllocationEntity> paidList = incomePaidAllocationEntities.stream().filter(i -> orderGoodsSnList.contains(i.getOrderGoodsSn())).collect(Collectors.toList());

            for (IncomePaidAllocationEntity incomePaidAllocationEntity : paidList) {
                paidAmount = paidAmount.add(RMBUtils.bigDecimalF2Y(incomePaidAllocationEntity.getPaidAmount()));
            }

            orderRefundGoodsEntityList = orderRefundGoodsEntityList.stream().filter(o -> orderGoodsSnList.contains(o.getOrderGoodsSn())).collect(Collectors.toList());

            for (HeOrderRefundGoodsEntity heOrderRefundGoodsEntity : orderRefundGoodsEntityList) {
                if (heOrderRefundGoodsEntity.getStatus().equals(OrderRefundGoodsStatusEnum.REFUNDING.code())) {
                    freezeAmount = freezeAmount.add(RMBUtils.bigDecimalF2Y(heOrderRefundGoodsEntity.getRefundAmount()));
                    freezeNum += heOrderRefundGoodsEntity.getRefundNum();
                }
                if (heOrderRefundGoodsEntity.getStatus().equals(OrderRefundGoodsStatusEnum.SUCCESS.code())) {
                    alreadyRefundAmount = alreadyRefundAmount.add(RMBUtils.bigDecimalF2Y(heOrderRefundGoodsEntity.getRefundAmount()));
                    alreadyRefundNum += heOrderRefundGoodsEntity.getRefundNum();
                }
            }

            //可退金额
            refundPayAmount = paidAmount.subtract(alreadyRefundAmount).subtract(freezeAmount);
            //可退数量
            refundGoodsNum = allNum - freezeNum - alreadyRefundNum;

        }
        result.setGoodsNum(allNum);
        result.setFreezeNum(freezeNum);
        result.setAlreadyRefundNum(alreadyRefundNum);
        result.setRefundGoodsNum(refundGoodsNum);
        result.setPayAmount(payAmount);
        result.setPaidAmount(paidAmount);
        result.setFreezeAmount(freezeAmount);
        result.setAlreadyRefundAmount(alreadyRefundAmount);
        result.setRefundPayAmount(refundPayAmount);
        return result;
    }

    @Override
    public void sendRefundAfter(Integer orderId, Integer refundId) {
//        Boolean send = redisService.getCacheObject(OtherConstant.REFUND_AFTER_SEND_SMS + ":" + orderId);
//        if (ObjectUtil.isEmpty(send)) {
//            return;
//        }
        HeOrderEntity orderEntity = orderRepository.getByOrderId(orderId);
        HeOrderRefundEntity orderRefundEntity = orderRefundRepository.getOneById(refundId);
        HeOrderUserSnapshotEntity orderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(orderId);

        List<HeOrderRefundEntity> refundByParentSn = orderRefundRepository.getRefundByParentSn(orderRefundEntity.getRefundOrderSn());
        OaProcessRecordPO oaProcessRecordPO = oaProcessRecordService.getByRefundId(refundId);

        String phone = "";

        if (ObjectUtil.isEmpty(oaProcessRecordPO)) {
            //如果审批为空，有可能是客诉单的审批
            HeCustomerComplaintsEntity customerComplaintsEntity = customerComplaintsRepository.selectByOrderRefundId(refundId);
            if (ObjectUtil.isNotEmpty(customerComplaintsEntity)) {

                Integer creator = customerComplaintsEntity.getCreator();

                UserPO creatorInfo = userService.queryUserById(creator.longValue());
                if (ObjectUtil.isNotEmpty(creatorInfo)) {
                    phone = creatorInfo.getPhone();
                }
            }

        } else {
            String params = oaProcessRecordPO.getBody();
            JSONObject jsonObject = JSONUtil.toBean(params, JSONObject.class);
            phone = (String) jsonObject.get("phone");
        }

        RefundTypeEnum refundTypeEnum = RefundTypeEnum.getEnumByCode(orderRefundEntity.getRefundMethod());
        //设置发送短信的模板
        SmsTemplateV2Enum sendCreator = null;
        SmsTemplateV2Enum sendCustomer = null;
        //短信的发送参数
        List<String> smsCreatorParam = new ArrayList<String>();
        smsCreatorParam.add(orderEntity.getOrderSn());

        List<String> smsCustomerParam = new ArrayList<String>();
        smsCustomerParam.add(orderEntity.getOrderSn());

        BigDecimal refundAmount = RMBUtils.bigDecimalF2Y(refundByParentSn.stream().mapToInt(HeOrderRefundEntity::getApplyAmount).sum());


        switch (refundTypeEnum) {
            case BACK_TRACK:
                sendCreator = SmsTemplateV2Enum.ORDER1_REFUND_ONLINE_CREATOR;
                smsCreatorParam.add(orderUserSnapshotEntity.getName());
                smsCreatorParam.add(refundByParentSn.size() + "");
                smsCreatorParam.add(refundAmount.toEngineeringString());

                sendCustomer = SmsTemplateV2Enum.ORDER1_REFUND_ONLINE_CUSTOMER;
                smsCustomerParam.add(refundAmount.toEngineeringString());
                break;
            case OFFLINE_REMITTANCE:
                sendCreator = SmsTemplateV2Enum.ORDER1_REFUND_OFFLINE_CREATOR;
                smsCreatorParam.add(orderUserSnapshotEntity.getName());
                smsCreatorParam.add(refundAmount.toEngineeringString());
                smsCreatorParam.add(DateUtil.formatDateTime(new Date()));

                sendCustomer = SmsTemplateV2Enum.ORDER1_REFUND_OFFLINE_CUSTOMER;
                smsCustomerParam.add(orderUserSnapshotEntity.getName());
                smsCustomerParam.add(refundAmount.toEngineeringString());
                smsCustomerParam.add(DateUtil.formatDateTime(new Date()));

                break;
            case BACK_ACCOUNT:
                sendCreator = SmsTemplateV2Enum.ORDER1_REFUND_BALANCE_CREATOR;
                smsCreatorParam.add(refundAmount.toEngineeringString());
                smsCreatorParam.add(orderUserSnapshotEntity.getName());

                sendCustomer = SmsTemplateV2Enum.ORDER1_REFUND_BALANCE_CUSTOMER;
                smsCustomerParam.add(refundAmount.toEngineeringString());
                break;
        }
        //发给发起人
        baseClient.sendMessage(phone, null, null, sendCreator, smsCreatorParam.toArray(new String[smsCreatorParam.size()]));
        //发给客户
        baseClient.sendMessage(orderUserSnapshotEntity.getPhone(), null, null, sendCustomer, smsCustomerParam.toArray(new String[smsCustomerParam.size()]));
        redisService.setCacheObject(OtherConstant.REFUND_AFTER_SEND_SMS + ":" + orderId, true);
    }

    @Override
    public void sendRefundAfterApprove(Boolean isRefund, String originatorPhone, String originatorName, Integer orderId, HeOrderRefundEntity heOrderRefundEntity) {
        List<String> param = new ArrayList<String>();
        param.add(originatorName);
        if (isRefund) {
            RefundTypeEnum refundTypeEnum = RefundTypeEnum.getEnumByCode(heOrderRefundEntity.getRefundMethod());
            param.add(refundTypeEnum.getValue());
            param.add(RMBUtils.bigDecimalF2Y(heOrderRefundEntity.getApplyAmount()).toEngineeringString());
        }
        baseClient.sendMessage(originatorPhone, null, null, isRefund ? SmsTemplateV2Enum.ORDER1_REFUND_SUCCESS : SmsTemplateV2Enum.ORDER1_REFUND_REFUSE, param.toArray(new String[param.size()]));
    }

    @Override
    public void sendRefundAfterApproveByAmount(Boolean isRefund, String originatorPhone, String originatorName, Integer orderId, HeOrderRefundEntity heOrderRefundEntity, BigDecimal refundAmount) {
        List<String> param = new ArrayList<String>();
        param.add(originatorName);
        if (isRefund) {
            RefundTypeEnum refundTypeEnum = RefundTypeEnum.getEnumByCode(heOrderRefundEntity.getRefundMethod());
            param.add(refundTypeEnum.getValue());
            param.add(refundAmount.toEngineeringString());
        }
        baseClient.sendMessage(originatorPhone, null, null, isRefund ? SmsTemplateV2Enum.ORDER1_REFUND_SUCCESS : SmsTemplateV2Enum.ORDER1_REFUND_REFUSE, param.toArray(new String[param.size()]));
    }

    @Override
    public OrderRefundProgressRes refreshStatus(Integer refundId) {
        OrderRefundProgressRes orderRefundProgressRes = new OrderRefundProgressRes();

        //获取退款
        HeOrderRefundEntity orderRefundEntity = orderRefundRepository.getOneById(refundId);
        if (ObjectUtil.isEmpty(orderRefundEntity)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "退款不存在!");
        }

        HeOrderEntity orderEntity = orderRepository.getByOrderId(orderRefundEntity.getOrderId());

        OaProcessIdRelationPO refundFromByParamId = null;

        if (orderEntity.isNewOrder()) {
            //主退款的sn
            String parentRefundOrderSn = orderRefundEntity.getParentRefundOrderSn();
            //获取主图款
            HeOrderRefundEntity oneByRefundOrderSn = orderRefundRepository.getOneByRefundOrderSn(parentRefundOrderSn);
            if (ObjectUtil.isEmpty(oneByRefundOrderSn)) {
                throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "主退款不存在!");
            }
            refundFromByParamId = oaProcessIdRelationService.getRefundFromByParamId(oneByRefundOrderSn.getId());
        } else {
            refundFromByParamId = oaProcessIdRelationService.getRefundFromByParamId(orderRefundEntity.getId());
        }

        if (ObjectUtil.isEmpty(refundFromByParamId)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "主退款异常!");
        }

        if (refundFromByParamId.getApproveStatus().equals(DingApproveStatusEnum.DING_SUCCESS.getCode()) && (orderRefundEntity.getStatus().equals(RefundRecordPayStatusEnum.REFUND_RECORD_5.getCode()) || orderRefundEntity.getStatus().equals(RefundRecordPayStatusEnum.REFUND_RECORD_2.getCode()))) {
            log.info("直接发起退款：{}", refundId);
            //直接退款
            PayRefundResult payRefundResult = orderPayV2Service.startRefund(refundId);
            log.info("直接发起退款结果：{}", payRefundResult);
        }
        //获取子项
        HeOrderRefundEntity refundEntity = orderRefundRepository.getOneById(refundId);
        if (ObjectUtil.isNotEmpty(refundEntity)) {
            orderRefundProgressRes.setId(refundEntity.getId());
            orderRefundProgressRes.setRefundAmount(RMBUtils.bigDecimalF2Y(refundEntity.getApplyAmount()));
            orderRefundProgressRes.setRefundType(refundEntity.getRefundType());
            orderRefundProgressRes.setRefundTypeStr(OmniRefundTypeEnum.getName(refundEntity.getRefundType()));
            orderRefundProgressRes.setStatus(refundEntity.getStatus());
            Integer refundType = refundEntity.getRefundType();
            OmniPayTypeEnum omniPayTypeEnum = OmniPayTypeEnum.getByCode(refundType);
            switch (omniPayTypeEnum) {
                case ALIPAY:
                case WECHAT:
                case ONLINE_POS:
                case BALANCE:
                case OTHER:
                    //线上统一用退款中
                    orderRefundProgressRes.setStatusStr(OmniRefundApproveEnum.getName(orderRefundEntity.getStatus()));
                    break;
                case OFFLINE:
                    //线下显示等待财务打款
                    orderRefundProgressRes.setStatusStr(OmniRefundApproveEnum.getOfflineName(orderRefundEntity.getStatus()));
                    break;
            }
            orderRefundProgressRes.setCurrency(refundEntity.getCurrency());
            orderRefundProgressRes.setSubmissionTime(DateUtil.formatDateTime(new Date(refundEntity.getCreatedAt() * 1000)));
            if (ObjectUtil.isNotEmpty(refundEntity.getFinishAt()) && refundEntity.getFinishAt() > 0) {
                orderRefundProgressRes.setRefundSuccessTime(DateUtil.formatDateTime(new Date(refundEntity.getFinishAt() * 1000)));
            }
            orderRefundProgressRes.setRefundOrderSn(refundEntity.getRefundOrderSn());
        }
        return orderRefundProgressRes;
    }


    @Override
    public CreateRefundReq againRefund(Integer refundId) {

        HeOrderRefundEntity orderRefundEntity = orderRefundRepository.getOneById(refundId);

        OaProcessIdRelationPO refundFromByParamId = oaProcessIdRelationService.getRefundFromByParamId(refundId);

        if (ObjectUtil.isEmpty(refundFromByParamId) || ObjectUtil.isEmpty(orderRefundEntity)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "退款不存在!");
        }
        if (refundFromByParamId.getApproveStatus() != 2 && refundFromByParamId.getApproveStatus() != 10) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "只有审批发起失败或者审批拒绝才可以重新发起退款!");
        }

        //获取退款详情
        OrderRefundDetailsRes orderRefundDetailsRes = refundDetails(refundId);
        if (ObjectUtil.isEmpty(orderRefundDetailsRes)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "退款明细不存在!");
        }

        return omniOrderRefundConverter.orderRefundDetailsRes2CreateRefundReq(orderRefundDetailsRes);
    }

    @Override
    public QueryRefundInfoRes refundInfoRes(Integer orderId, List<HeOrderRefundEntity> orderRefundList, List<HeIncomeRecordEntity> orderIncomeList) {
        QueryRefundInfoRes result = new QueryRefundInfoRes();
        result.setOrderId(orderId);
        //非主记录的退款数据
        orderRefundList = orderRefundList.stream()
                .filter(r -> StringUtils.isNotEmpty(r.getParentRefundOrderSn()))
                .collect(Collectors.toList());

        QueryRefundInfoRes.AmountInfo refundAmountInfo = calculateRefundAmount(orderRefundList);
        result.setRefundAmountInfo(refundAmountInfo);

        PaymentSummary paymentSummary = calculatePaymentSummary(orderIncomeList);
        result.setPaidAmountInfo(paymentSummary.payAmountInfo);
        result.setCanRefundAmountInfo(paymentSummary.canRefundAmountInfo);
        result.setAboutExpireInfo(paymentSummary.aboutExpireInfo);

        return result;
    }

    @Override
    public QueryEquallyGoodsRes queryEquallyGoods(QueryQueryEquallyGoodsReq req) {

        HeOrderRefundEntity oneByRefundOrderSn = orderRefundRepository.getOneByRefundOrderSn(req.getRefundSn());
        if (ObjectUtil.isEmpty(oneByRefundOrderSn)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "退款不存在!");
        }

        if (ObjectUtil.isNotEmpty(oneByRefundOrderSn.getOrderId()) && oneByRefundOrderSn.getOrderId() == 0) {
            //之前的押金订单
            QueryEquallyGoodsRes queryEquallyGoodsRes = new QueryEquallyGoodsRes();
            queryEquallyGoodsRes.setNewOrder(false);
            return queryEquallyGoodsRes;
        }

        HeOrderEntity orderEntity = orderRepository.getByOrderId(oneByRefundOrderSn.getOrderId());
        if (ObjectUtil.isEmpty(orderEntity)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "订单不存在!");
        }


        List<HeOrderGoodsEntity> allItermByOrderId = orderGoodsRepository.getAllItermByOrderId(oneByRefundOrderSn.getOrderId());

        //退款的商品列表
        List<HeOrderRefundApplyGoodsEntity> orderRefundApplyGoodsEntityList = orderRefundApplyGoodsRepository.queryByRefundSn(req.getRefundSn());

        if (Boolean.TRUE.equals(orderEntity.getTransferOrder()) && CollectionUtil.isEmpty(orderRefundApplyGoodsEntityList)) {
            //老订单转成新订单，不用考虑
            QueryEquallyGoodsRes queryEquallyGoodsRes = new QueryEquallyGoodsRes();
            queryEquallyGoodsRes.setNewOrder(false);
            queryEquallyGoodsRes.setOrderType(orderEntity.getOrderType());
            return queryEquallyGoodsRes;
        }

        QueryEquallyGoodsRes result = orderRefundConverter.refundApplyGoodsEntity2QueryEquallyGoodsRes(orderRefundApplyGoodsEntityList, orderEntity, allItermByOrderId, oneByRefundOrderSn);


        List<String> orderGoodsSnList = result.getQueryEquallyGoodsListResList().stream().map(QueryEquallyGoodsRes.QueryEquallyGoodsListRes::getOrderGoodsSn).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(result.getQueryEquallyGoodsListResList())) {
            List<IncomePaidAllocationEntity> incomePaidAllocationEntities = paidAllocationRepository.queryListByOrderGoodsSnList(orderGoodsSnList);
            for (QueryEquallyGoodsRes.QueryEquallyGoodsListRes queryEquallyGoodsListRes : result.getQueryEquallyGoodsListResList()) {
                String orderGoodsSn = queryEquallyGoodsListRes.getOrderGoodsSn();
                List<IncomePaidAllocationEntity> collect = incomePaidAllocationEntities.stream().filter(i -> i.getOrderGoodsSn().equals(orderGoodsSn)).collect(Collectors.toList());
                List<IncomePaidAllocationEntity> productAmount = collect.stream().filter(c -> StringUtils.isNotEmpty(c.getPaymentMethod()) && c.getPaymentMethod().equals(OmniPayTypeEnum.PRODUCTION_COIN.getCode().toString())).collect(Collectors.toList());
                List<IncomePaidAllocationEntity> caseAmount = collect.stream().filter(c -> StringUtils.isNotEmpty(c.getPaymentMethod()) && !c.getPaymentMethod().equals(OmniPayTypeEnum.PRODUCTION_COIN.getCode().toString())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(productAmount)) {
                    queryEquallyGoodsListRes.setCkjPaidAmount(RMBUtils.bigDecimalF2Y(productAmount.stream().mapToInt(IncomePaidAllocationEntity::getPaidAmount).sum()));
                }
                if (CollectionUtil.isNotEmpty(caseAmount)) {
                    queryEquallyGoodsListRes.setPaidAmount(RMBUtils.bigDecimalF2Y(caseAmount.stream().mapToInt(IncomePaidAllocationEntity::getPaidAmount).sum()));
                }
            }
        }

        result.setRefundNature(oneByRefundOrderSn.getRefundNature());
        return result;
    }

    @Override
    public Map<Long, QueryOrderRefundGoodsInfoRes> queryOrderGoodsInfoMapByOrderIdList(List<Integer> orderIdList, List<HeOrderGoodsEntity> orderGoods) {
        List<QueryOrderRefundGoodsInfoRes> result = new ArrayList<>();
        //支付记录
        List<IncomePaidAllocationEntity> incomePaidAllocationEntities = incomePaidAllocationRepository.queryListByOrderIdList(orderIdList);
        List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList = orderRefundGoodsRepository.queryListByOrderIdList(orderIdList);

        //都过滤掉减免的
        incomePaidAllocationEntities = incomePaidAllocationEntities.stream().filter(i -> !OmniPayTypeEnum.REDUCTION.getCode().equals(Integer.valueOf(i.getPaymentMethod()))).collect(Collectors.toList());
        orderRefundGoodsEntityList = orderRefundGoodsEntityList.stream().filter(i -> !OmniPayTypeEnum.REDUCTION.getCode().equals(i.getPayType())).collect(Collectors.toList());

        for (HeOrderGoodsEntity orderGoodsEntity : orderGoods) {
            QueryOrderRefundGoodsInfoRes queryOrderRefundGoodsInfoRes = new QueryOrderRefundGoodsInfoRes();
            queryOrderRefundGoodsInfoRes.setOrderGoodsId(orderGoodsEntity.getId().longValue());
            queryOrderRefundGoodsInfoRes.setOrderGoodsSn(orderGoodsEntity.getOrderGoodsSn());
            queryOrderRefundGoodsInfoRes.setParentCombineSn(orderGoodsEntity.getParentCombineSn());
            queryOrderRefundGoodsInfoRes.setGoodsNum(orderGoodsEntity.getGoodsNum());
            queryOrderRefundGoodsInfoRes.setPayAmount(RMBUtils.bigDecimalF2Y(orderGoodsEntity.getPayAmount()));
            queryOrderRefundGoodsInfoRes.setGoodsType(orderGoodsEntity.getGoodsType());
            queryOrderRefundGoodsInfoRes.setGoodsName(orderGoodsEntity.getGoodsName());
            queryOrderRefundGoodsInfoRes.setSkuName(orderGoodsEntity.getSkuName());
            queryOrderRefundGoodsInfoRes.setGoodsId(orderGoodsEntity.getGoodsId());
            queryOrderRefundGoodsInfoRes.setSkuId(orderGoodsEntity.getSkuId());
            queryOrderRefundGoodsInfoRes.setType(orderGoodsEntity.getType());
            List<IncomePaidAllocationEntity> incomePaidAllocationEntityList = incomePaidAllocationEntities.stream().filter(i -> i.getOrderGoodsSn().equals(orderGoodsEntity.getOrderGoodsSn())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(incomePaidAllocationEntityList)) {
                //当前已付总金额
                BigDecimal paidAmount = RMBUtils.bigDecimalF2Y(incomePaidAllocationEntityList.stream().mapToInt(IncomePaidAllocationEntity::getPaidAmount).sum());
                queryOrderRefundGoodsInfoRes.setPaidAmount(paidAmount);
            }

            //商品的退款记录（进行中+已成功）
            List<HeOrderRefundGoodsEntity> goodsRefundList = orderRefundGoodsEntityList.stream().filter(i -> i.getOrderGoodsSn().equals(orderGoodsEntity.getOrderGoodsSn())).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(goodsRefundList)) {
                //进行中
                List<HeOrderRefundGoodsEntity> freezeList = goodsRefundList.stream().filter(g -> g.getStatus().equals(OrderRefundGoodsStatusEnum.REFUNDING.code())).collect(Collectors.toList());
                //成功
                List<HeOrderRefundGoodsEntity> successList = goodsRefundList.stream().filter(g -> g.getStatus().equals(OrderRefundGoodsStatusEnum.SUCCESS.code())).collect(Collectors.toList());
                //冻结金额从商品退款表进行中的取
                BigDecimal freezeAmount = RMBUtils.bigDecimalF2Y(freezeList.stream().mapToInt(HeOrderRefundGoodsEntity::getRefundAmount).sum());
                //冻结数量
                Integer freezeNum = freezeList.stream().mapToInt(HeOrderRefundGoodsEntity::getRefundNum).sum();
                //已退金额
                BigDecimal successAmount = RMBUtils.bigDecimalF2Y(successList.stream().mapToInt(HeOrderRefundGoodsEntity::getRefundAmount).sum());
                //已退数量
                Integer successNum = successList.stream().mapToInt(HeOrderRefundGoodsEntity::getRefundNum).sum();
                queryOrderRefundGoodsInfoRes.setFreezeAmount(freezeAmount);
                queryOrderRefundGoodsInfoRes.setFreezeNum(freezeNum);
                queryOrderRefundGoodsInfoRes.setAlreadyRefundAmount(successAmount);
                queryOrderRefundGoodsInfoRes.setAlreadyRefundNum(successNum);
                //退货退款、仅退款、退回重付、未知取最新的
                queryOrderRefundGoodsInfoRes.setRefundNature(goodsRefundList.get(goodsRefundList.size() - 1).getRefundNature());
            }
            queryOrderRefundGoodsInfoRes.setRefundGoodsNum(queryOrderRefundGoodsInfoRes.getGoodsNum() - queryOrderRefundGoodsInfoRes.getFreezeNum() - queryOrderRefundGoodsInfoRes.getAlreadyRefundNum());
            queryOrderRefundGoodsInfoRes.setRefundPayAmount(queryOrderRefundGoodsInfoRes.getPaidAmount().subtract(queryOrderRefundGoodsInfoRes.getFreezeAmount()).subtract(queryOrderRefundGoodsInfoRes.getAlreadyRefundAmount()));
            result.add(queryOrderRefundGoodsInfoRes);
        }
        return result.stream().collect(Collectors.toMap(QueryOrderRefundGoodsInfoRes::getOrderGoodsId, entity -> entity, (v1, v2) -> v1));

    }

    @Override
    public List<OrderRefundProgressRes> complaintRefundProgress(Long complaintId) {

        List<OrderRefundProgressRes> result = new ArrayList<>();

        HeCustomerComplaintsEntity customerComplaintsEntity = customerComplaintsRepository.selectById(complaintId);

        if (ObjectUtil.isNotEmpty(customerComplaintsEntity)) {

            Integer cashRefundId = customerComplaintsEntity.getCashRefundId();

            HeOrderEntity orderEntity = orderRepository.getByOrderId(customerComplaintsEntity.getOrderId().intValue());

            if (orderEntity.isNewOrder()) {
                Long refundOrderId = customerComplaintsEntity.getRefundOrderId();
                if (ObjectUtil.isNotEmpty(refundOrderId)) {
                    result.addAll(refundProgress(refundOrderId.intValue()));
                }
            } else {
                HeOrderRefundEntity orderRefundEntity = orderRefundRepository.getOneById(customerComplaintsEntity.getRefundOrderId().intValue());
                setRefundInfo(orderRefundEntity, orderRefundEntity, orderEntity, result);
            }


            if (ObjectUtil.isNotEmpty(cashRefundId)) {
                HeOrderRefundEntity orderRefundEntity = orderRefundRepository.getOneById(cashRefundId);
                setRefundInfo(orderRefundEntity, orderRefundEntity, orderEntity, result);
            }
        }

        if (CollectionUtil.isNotEmpty(result)) {
            for (OrderRefundProgressRes orderRefundProgressRes : result) {
                orderRefundProgressRes.setRefundId(orderRefundProgressRes.getId());
            }
        }

        return result;
    }

    private QueryRefundInfoRes.AmountInfo calculateRefundAmount(List<HeOrderRefundEntity> orderRefundList) {
        QueryRefundInfoRes.AmountInfo refundAmountInfo = new QueryRefundInfoRes.AmountInfo();
        orderRefundList.stream()
                .filter(r -> HeOrderRefundEntity.REFUNDING_AND_REFUNDED_STATUS.contains(r.getStatus()))
                .forEach(refund -> {
                    BigDecimal amount = RMBUtils.bigDecimalF2Y(refund.getApplyAmount());
                    OmniPayTypeEnum payTypeEnum = OmniPayTypeEnum.getByCode(refund.getRefundType());
                    switch (payTypeEnum) {
                        case WECHAT:
                            refundAmountInfo.setWechatPay(refundAmountInfo.getWechatPay().add(amount));
                            break;
                        case ALIPAY:
                            refundAmountInfo.setAliPay(refundAmountInfo.getAliPay().add(amount));
                            break;
                        case OTHER:
                            refundAmountInfo.setOfflinePay(refundAmountInfo.getOfflinePay().add(amount));
                            break;
                        case ONLINE_POS:
                            refundAmountInfo.setPosPay(refundAmountInfo.getPosPay().add(amount));
                        case BALANCE:
                            refundAmountInfo.setBalance(refundAmountInfo.getBalance().add(amount));
                            break;
                        case PRODUCTION_COIN:
                            refundAmountInfo.setCjkPay(refundAmountInfo.getCjkPay().add(amount));
                            break;
                    }
                });

        return refundAmountInfo;
    }

    private PaymentSummary calculatePaymentSummary(List<HeIncomeRecordEntity> orderIncomeList) {
        PaymentSummary summary = new PaymentSummary();
        long nowTime = new Date().getTime() / 1000;

        orderIncomeList.stream()
                .filter(a -> a.getStatus().equals(PayStatusEnum.PAY_STATUS_SUCCESS.getCode()))
                .forEach(income -> {
                    BigDecimal incomeAmount = RMBUtils.bigDecimalF2Y(income.getIncome());
                    BigDecimal refundAmount = RMBUtils.bigDecimalF2Y(income.getRefundIncome());
                    OmniPayTypeEnum payType = OmniPayTypeEnum.getByCode(income.getPayType());
                    updateAmountInfo(summary.payAmountInfo, payType, incomeAmount);
                    updateAmountInfo(summary.canRefundAmountInfo, payType, refundAmount);
                    updateAboutExpireInfo(summary.aboutExpireInfo, payType, income, nowTime);
                });

        return summary;
    }

    private void updateAmountInfo(QueryRefundInfoRes.AmountInfo amountInfo, OmniPayTypeEnum payType, BigDecimal amount) {
        switch (payType) {
            case WECHAT:
                amountInfo.setWechatPay(amountInfo.getWechatPay().add(amount));
                break;
            case ALIPAY:
                amountInfo.setAliPay(amountInfo.getAliPay().add(amount));
                break;
            case OTHER:
                amountInfo.setOfflinePay(amountInfo.getOfflinePay().add(amount));
                break;
            case ONLINE_POS:
                amountInfo.setPosPay(amountInfo.getPosPay().add(amount));
            case BALANCE:
                amountInfo.setBalance(amountInfo.getBalance().add(amount));
                break;
            case PRODUCTION_COIN:
                amountInfo.setCjkPay(amountInfo.getCjkPay().add(amount));
        }
    }

    private void updateAboutExpireInfo(QueryRefundInfoRes.AboutExpireInfo aboutExpireInfo,
                                       OmniPayTypeEnum payType,
                                       HeIncomeRecordEntity income,
                                       long nowTime) {
        if (income.getRefundIncome().compareTo(0) <= 0) {
            return;
        }

        long expirationTime = income.getPayTime();
        switch (payType) {
            case WECHAT:
                expirationTime += wechatPeriodValidity;
                if (expirationTime - nowTime <= expirationCountdown) {
                    aboutExpireInfo.setWechatPay(true);
                    aboutExpireInfo.setWechatPayNum(aboutExpireInfo.getWechatPayNum() + 1);
                }
                break;
            case ALIPAY:
                expirationTime += aliPayPeriodValidity;
                if (expirationTime - nowTime <= expirationCountdown) {
                    aboutExpireInfo.setAliPay(true);
                    aboutExpireInfo.setAliPayNum(aboutExpireInfo.getAliPayNum() + 1);
                }
                break;
            case ONLINE_POS:
                expirationTime += posPayPeriodValidity;
                if (expirationTime - nowTime <= expirationCountdown) {
                    aboutExpireInfo.setPos(true);
                    aboutExpireInfo.setPosNum(aboutExpireInfo.getPosNum() + 1);
                }
                break;
        }
    }

    private static class PaymentSummary {
        QueryRefundInfoRes.AmountInfo payAmountInfo = new QueryRefundInfoRes.AmountInfo();
        QueryRefundInfoRes.AmountInfo canRefundAmountInfo = new QueryRefundInfoRes.AmountInfo();
        QueryRefundInfoRes.AboutExpireInfo aboutExpireInfo = new QueryRefundInfoRes.AboutExpireInfo();
    }
}

package com.stbella.order.domain.order.month.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 用户产康金记录
 * </p>
 *
 * <AUTHOR> @since 2023-06-13
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "HeUserProductionAmountListEntity对象", description = "用户产康金记录")
public class HeUserProductionAmountListEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "用户basic id")
    private Integer basicId;

    @ApiModelProperty(value = "总的产康金(单位:分)")
    private Long totalAmount;

    @ApiModelProperty(value = "已使用的产康金(单位:分)")
    private Long usedAmount;

    @ApiModelProperty(value = "来源: 1-月子订单固定赠送 2-月子订单额外赠送 3-产康订单额外赠送 4-小月子固定赠送 5-小月子额外赠送 6-后台赠送 7-客诉赠送 8-ugc赠送 9-馆内服务置换 10-其他赠送 11-房型升级赠送 12-续住赠送 13-产康订单赠送 14-月子订单赠送 15-购买补录 101-月子订单嘉人卡活动 102-月子其他订单嘉人卡活动赠送")
    private Integer source;

    @ApiModelProperty(value = "操作人:客户类型：0无（0是问题滴），1月子客户，2到家阿姨，5到家客户，6后台管理员,7员工")
    private Integer clientType;

    @ApiModelProperty(value = "操作人id")
    private Integer operatorId;

    @ApiModelProperty(value = "操作人")
    private String operator;

    @ApiModelProperty(value = "关联订单id")
    private Long orderId;

    @ApiModelProperty(value = "状态: 0-冻结 1-正常 2-已失效")
    private Integer status;

    @ApiModelProperty(value = "是否显示，0-不显示(没有提交任务) 1-显示")
    private Integer isShow;

    @ApiModelProperty(value = "有效期开始时间")
    private Integer validStartTime;

    @ApiModelProperty(value = "有效期结束时间")
    private Integer validEndTime;

    @ApiModelProperty(value = "激活时间")
    private Integer activeTime;

    @ApiModelProperty(value = "是否删除：1是，0否")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Integer createdAt;

    @ApiModelProperty(value = "更新时间")
    private Integer updatedAt;


}

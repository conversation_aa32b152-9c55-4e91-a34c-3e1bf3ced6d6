package com.stbella.order.domain.order.production;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class OrderProductionAppointmentOptLogEntity implements Serializable {

    private static final long serialVersionUID = -6800680734766862691L;
    /**
     *
     */
    private Long id;

    /**
     * 关联购买资产订单
     */
    private Integer orderId;

    /**
     * 订单编号
     */
    private String orderSn;

    /**
     * 操作类型
     * CREATED(0, "创建预约单"),
     * UPDATED(1, "修改预约单"),
     * CANCEL(2, "取消预约单"),
     * WRITE_OFF(2, "核销"),
     * {@link com.stbella.order.common.enums.production.OptTypeEnum}
     */
    private Integer optType;

    /**
     * 预约单ID
     */
    private Long appointmentId;

    /**
     * 操作人
     */
    private String optName;

    /**
     * 操作人ID
     */
    private Long optId;

    /**
     * 逻辑删除标志
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 原始预约单内容
     */
    private String originalContent;

    /**
     * 修改完后的预约单内容
     */
    private String optContent;

    /**
     * 操作来源
     */
    private String source;
}

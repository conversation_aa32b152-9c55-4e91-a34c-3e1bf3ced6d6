package com.stbella.order.domain.order.month.dto;

import com.google.common.collect.Lists;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.month.entity.IncomePaidAllocationEntity;
import com.stbella.order.domain.order.production.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class OrderGoodsInfoDTO {

    private Map<Integer, List<HeOrderGoodsEntity>> orderGoodsEntityMap;

    private List<OrderProductionExtendEntity> orderProductionExtendList;

    private List<OrderProductionCardExtendEntity> orderProductionCardExtendList;

    private List<OrderGiftExtendEntity> orderGiftExtendList = Lists.newArrayList();

    private Map<Integer, List<IncomePaidAllocationEntity>> incomePaidAllocationMap;

    private Map<Integer, GoodsEntity> goodsEntityMap;

    /**
     * 根据订单Id查询核销信息-按照订单Id分组
     */
    private Map<Long, List<OrderProductionVerificationLogEntity>> orderProductionVerificationMap;

    private Map<Integer, BigDecimal> goodsSkuPriceMap;

    /**
     * 新订单信息
     */
    private List<OrderProductionExtendEntity> newOrderProductionExtendList;

}

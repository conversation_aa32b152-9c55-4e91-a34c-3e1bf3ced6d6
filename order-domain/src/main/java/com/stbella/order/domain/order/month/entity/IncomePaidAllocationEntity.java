package com.stbella.order.domain.order.month.entity;

import com.stbella.order.server.order.month.enums.PayMethodEnum;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
public class IncomePaidAllocationEntity implements Serializable {

    /**
     * key
     */
    private Integer id;

    /**
     * orderId
     */
    private Integer orderId;

    /**
     * storeId
     */
    private Integer storeId;

    /**
     * basicId
     */
    private Integer basicId;

    /**
     * incomeId
     */
    private Integer incomeId;

    /**
     * goodsId
     */
    private Integer goodsId;

    /**
     * skuId
     */
    private Integer skuId;

    /**
     * skuName
     */
    private String skuName;

    /**
     * 后台分类id
     */
    private Long backCategoryId;

    /**
     * 百分比
     */
    private BigDecimal percent;

    /**
     * 付款金额
     */
    private Integer paidAmount;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 资产类型
     */
    private String assetType;

    /**
     * 删除标记
     */
    private Integer deleted;

    /**
     * add time
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 扩展
     */
    private String ext;

    /**
     * 支付方式: 积分，产康金，支付宝等
     */
    private String paymentMethod;

    /**
     * 订单商品序号
     */
    private String orderGoodsSn;

    /**
     * 订单商品id
     */
    private Long orderGoodsId;

    /**
     * 退款总金额
     */
    private Integer alreadyRefundAmount;


}

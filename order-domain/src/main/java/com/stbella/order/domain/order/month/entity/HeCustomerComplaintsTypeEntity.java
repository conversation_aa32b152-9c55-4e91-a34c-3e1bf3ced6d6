package com.stbella.order.domain.order.month.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 订单客诉类型
 * </p>
 *
 * <AUTHOR> @since 2022-10-28
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "HeCustomerComplaintsTypePO对象", description = "订单客诉类型")
public class HeCustomerComplaintsTypeEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID（主键自增)")
    private Long id;

    @ApiModelProperty(value = "父ID")
    private Integer parentId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "描述")
    private String description;

    private Integer sort;

    @ApiModelProperty("类型 100-产康相关，200-供应商相关")
    private String type;

    @ApiModelProperty(value = "子类目")
    private List<HeCustomerComplaintsTypeEntity> children;


}

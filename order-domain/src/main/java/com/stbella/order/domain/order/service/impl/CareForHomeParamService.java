package com.stbella.order.domain.order.service.impl;

import cn.hutool.json.JSONUtil;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.common.enums.order.*;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.domain.order.month.entity.UserEntity;
import com.stbella.order.domain.order.service.OrderParamTemplate;
import com.stbella.order.domain.repository.OrderGoodsRepository;
import com.stbella.order.domain.repository.OrderUserSnapshotRepository;
import com.stbella.order.domain.repository.UserRepository;
import com.stbella.order.server.contract.req.OrderParamHistoryPushDTO;
import com.stbella.order.server.contract.req.OrderParamHistoryValuePushDTO;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.platform.order.api.req.CustomAttribute;
import com.stbella.platform.order.api.req.ExtraInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class CareForHomeParamService implements OrderParamTemplate {

    private static final String flag = "√";

    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private OrderGoodsRepository orderGoodsRepository;

    @Override
    public OrderParamHistoryPushDTO buildOrderParamHistory(HeOrderEntity entity) {

        OrderParamHistoryPushDTO dto = new OrderParamHistoryPushDTO();
        dto.setOrderId(entity.getOrderId());
        dto.setOrderType(entity.getOrderType());
        dto.setStoreId(entity.getStoreId());
        dto.setTemplateContractType(TemplateContractTypeEnum.YZ_SAINTBELLA.code());
        dto.setParamList(buildParamList(entity));
        log.info("build order param end");
        return dto;
    }

    private List<OrderParamHistoryValuePushDTO> buildParamList(HeOrderEntity heOrderEntity) {

        log.info("开始构建予家订单主合同参数信息, heOrderEntity:{}", JSONUtil.toJsonStr(heOrderEntity));
        List<OrderParamHistoryValuePushDTO> paramList = new ArrayList<>();

        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(heOrderEntity.getOrderId());

        //甲方
        OrderParamHistoryValuePushDTO param1 = new OrderParamHistoryValuePushDTO();
        param1.setName("client_name");
        param1.setValue(heOrderUserSnapshotEntity.getName());
        paramList.add(param1);
        //地址
        OrderParamHistoryValuePushDTO param2 = new OrderParamHistoryValuePushDTO();
        param2.setName("address");
        param2.setValue(getHomeAddress(heOrderEntity.getExtraInfo(), OrderAdditionalKeyEnum.HOME_ADDRESS.code()));
        paramList.add(param2);
        //手机
        OrderParamHistoryValuePushDTO param3 = new OrderParamHistoryValuePushDTO();
        param3.setName("phone");
        param3.setValue(heOrderUserSnapshotEntity.getPhone());
        paramList.add(param3);
        //身份证号
        OrderParamHistoryValuePushDTO param4 = new OrderParamHistoryValuePushDTO();
        param4.setName("id_card");
        param4.setValue(heOrderUserSnapshotEntity.getIdCard());
        paramList.add(param4);

        //甲方紧急联络人姓名
        OrderParamHistoryValuePushDTO param5 = new OrderParamHistoryValuePushDTO();
        param5.setName("urgent_name");
        param5.setValue(heOrderUserSnapshotEntity.getUrgentName().concat(heOrderUserSnapshotEntity.getUrgentPhone()));
        paramList.add(param5);

        //订单总额(订单应收)
        Integer calPayable = heOrderEntity.calPayable();
        BigDecimal calPayableAmount = AmountChangeUtil.changeF2Y(calPayable);
        OrderParamHistoryValuePushDTO param6 = new OrderParamHistoryValuePushDTO();
        param6.setName("pay_amount");
        param6.setValue(RMBUtils.formatToseparaDecimals(calPayableAmount));
        paramList.add(param6);

        //订单总额(订单应收)大写
        OrderParamHistoryValuePushDTO param7 = new OrderParamHistoryValuePushDTO();
        param7.setName("pay_amount_words");
        param7.setValue(RMBUtils.numToRMBStr(Objects.isNull(calPayableAmount) ? 0 : calPayableAmount.doubleValue()));
        paramList.add(param7);

        UserEntity userEntity = userRepository.queryById(heOrderEntity.getStaffId());
        if (Objects.nonNull(userEntity)){
            OrderParamHistoryValuePushDTO param8 = new OrderParamHistoryValuePushDTO();
            param8.setName("companyContact");
            param8.setValue(userEntity.getPhone());
            paramList.add(param8);
        }

        OrderParamHistoryValuePushDTO param9 = new OrderParamHistoryValuePushDTO();
        param9.setName("serviceStartTime");
        param9.setValue(getHomeAddress(heOrderEntity.getExtraInfo(), OrderAdditionalKeyEnum.ESTIMATED_REGISTRATION_TIME.code()));
        paramList.add(param9);

        OrderParamHistoryValuePushDTO param10 = new OrderParamHistoryValuePushDTO();
        param10.setName("serviceEndTime");
        param10.setValue(getHomeAddress(heOrderEntity.getExtraInfo(), OrderAdditionalKeyEnum.ESTIMATED_OUT_TIME.code()));
        paramList.add(param10);

        OrderParamHistoryValuePushDTO param11 = new OrderParamHistoryValuePushDTO();
        param11.setName("specialDemand");
        param11.setValue(getHomeAddress(heOrderEntity.getExtraInfo(), OrderAdditionalKeyEnum.SPECIAL_REQUIREMENTS.code()));
        paramList.add(param11);

        //服务天数
        Integer serviceDays = 0;
        List<HeOrderGoodsEntity> orderGoodsList = orderGoodsRepository.getByOrderIdList(Collections.singletonList(heOrderEntity.getOrderId()));
        if (CollectionUtils.isNotEmpty(orderGoodsList)){
            serviceDays = orderGoodsList.stream().filter(a -> BizConstant.OrderAppKey.ASSET_LIST_HOME_CARE_ASSET.contains(a.getGoodsType())).filter(item -> Objects.nonNull(item.getGoodsNum())).mapToInt(HeOrderGoodsEntity::getGoodsNum).sum();
        }
        OrderParamHistoryValuePushDTO param12 = new OrderParamHistoryValuePushDTO();
        param12.setName("service_days");
        param12.setValue(String.valueOf(serviceDays));
        paramList.add(param12);

        OrderParamHistoryValuePushDTO param13 = new OrderParamHistoryValuePushDTO();
        param13.setName("remark");
        param13.setValue(heOrderEntity.getRemark());
        paramList.add(param13);

        OrderParamHistoryValuePushDTO param14 = new OrderParamHistoryValuePushDTO();
        param14.setName("serviceAllAddress");
        param14.setValue(getHomeAddress(heOrderEntity.getExtraInfo(), OrderAdditionalKeyEnum.SERVICE_ADDRESS.code()));
        paramList.add(param14);

        OrderParamHistoryValuePushDTO param15 = new OrderParamHistoryValuePushDTO();
        param15.setName("serviceOtherDes");
        param15.setValue("");
        paramList.add(param15);

        List<OrderParamHistoryValuePushDTO> serviceModeList = getServiceModeList(orderGoodsList);
        if (CollectionUtils.isNotEmpty(serviceModeList)){
            paramList.addAll(serviceModeList);
        }

        if (OmniOrderTypeEnum.DJ_MOTHER_CARE_ORDER.getCode().equals(heOrderEntity.getOrderType())){

            List<OrderParamHistoryValuePushDTO> careCenterGoodsSpecification = getCareCenterGoodsSpecification(orderGoodsList);
            if (CollectionUtils.isNotEmpty(careCenterGoodsSpecification)){
                paramList.addAll(careCenterGoodsSpecification);
            }
        }

        if (OmniOrderTypeEnum.DJ_INFANT_CARE_ORDER.getCode().equals(heOrderEntity.getOrderType())){
            List<OrderParamHistoryValuePushDTO> goodsSpecification = getGoodsSpecification(orderGoodsList);
            if (CollectionUtils.isNotEmpty(goodsSpecification)){
                paramList.addAll(goodsSpecification);
            }
        }

        Integer babyCount = this.getBabyCount(orderGoodsList);
        OrderParamHistoryValuePushDTO param16 = new OrderParamHistoryValuePushDTO();
        param16.setName("babyCount");
        param16.setValue(babyCount.toString());
        paramList.add(param16);

        BigDecimal orderAmount = AmountChangeUtil.changeF2Y(heOrderEntity.getOrderAmount());
        OrderParamHistoryValuePushDTO param17 = new OrderParamHistoryValuePushDTO();
        param17.setName("order_amount");
        param17.setValue(RMBUtils.formatToseparaDecimals(orderAmount));
        paramList.add(param17);

        //套餐费用(套餐原价格)大写
        OrderParamHistoryValuePushDTO param18 = new OrderParamHistoryValuePushDTO();
        param18.setName("order_amount_words");
        param18.setValue(RMBUtils.numToRMBStr(Objects.isNull(orderAmount) ? 0 : orderAmount.doubleValue()));
        paramList.add(param18);

        return paramList;
    }


    public Integer getBabyCount(List<HeOrderGoodsEntity> heOrderGoodsEntity) {

        Integer babyCount = 0;
        if (CollectionUtils.isEmpty(heOrderGoodsEntity)){
            return babyCount;
        }
        for (HeOrderGoodsEntity orderGoods : heOrderGoodsEntity) {
            if (!BizConstant.OrderAppKey.ASSET_LIST_HOME_CARE_ASSET.contains(orderGoods.getGoodsType())){
                continue;
            }
            Integer num = InfantsNumType.getCode(orderGoods.getSkuName());
            if (num != null && num > babyCount){
                babyCount = num;
            }
        }
        return babyCount;
    }

    private List<OrderParamHistoryValuePushDTO> getGoodsSpecification(List<HeOrderGoodsEntity> orderGoodsList){

        List<OrderParamHistoryValuePushDTO> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(orderGoodsList)){
            return resultList;
        }
        for (HeOrderGoodsEntity heOrderGoodsEntity : orderGoodsList) {
            if (!BizConstant.OrderAppKey.ASSET_LIST_HOME_CARE_ASSET.contains(heOrderGoodsEntity.getGoodsType())){
                continue;
            }
            String code = GoodsSpecificationEnum.getCodeByDesc(heOrderGoodsEntity.getSkuName());
            if (StringUtils.isEmpty(code)){
                continue;
            }
            OrderParamHistoryValuePushDTO orderParamHistoryValuePushDTO = resultList.stream().filter(item -> code.equals(item.getName())).findFirst().orElse(null);
            if (Objects.nonNull(orderParamHistoryValuePushDTO)){
                continue;
            }
            OrderParamHistoryValuePushDTO param = new OrderParamHistoryValuePushDTO();
            param.setName(code);
            param.setValue(flag);
            resultList.add(param);
        }
        return resultList;
    }

    private List<OrderParamHistoryValuePushDTO> getCareCenterGoodsSpecification(List<HeOrderGoodsEntity> orderGoodsList){

        List<OrderParamHistoryValuePushDTO> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(orderGoodsList)){
            return resultList;
        }
        for (HeOrderGoodsEntity heOrderGoodsEntity : orderGoodsList) {
            if (!BizConstant.OrderAppKey.ASSET_LIST_HOME_CARE_ASSET.contains(heOrderGoodsEntity.getGoodsType())){
                continue;
            }
            String code = CareCenterGoodsSpecificationEnum.getCodeByDesc(heOrderGoodsEntity.getSkuName());
            if (StringUtils.isEmpty(code)){
                continue;
            }
            OrderParamHistoryValuePushDTO orderParamHistoryValuePushDTO = resultList.stream().filter(item -> code.equals(item.getName())).findFirst().orElse(null);
            if (Objects.nonNull(orderParamHistoryValuePushDTO)){
                continue;
            }
            OrderParamHistoryValuePushDTO param = new OrderParamHistoryValuePushDTO();
            param.setName(code);
            param.setValue(flag);
            resultList.add(param);
        }
        return resultList;
    }

    private List<OrderParamHistoryValuePushDTO> getServiceModeList(List<HeOrderGoodsEntity> orderGoodsList){

        List<OrderParamHistoryValuePushDTO> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(orderGoodsList)){
            return resultList;
        }
        for (HeOrderGoodsEntity heOrderGoodsEntity : orderGoodsList) {
            if (!BizConstant.OrderAppKey.ASSET_LIST_HOME_CARE_ASSET.contains(heOrderGoodsEntity.getGoodsType())){
                continue;
            }
            String code = ServiceModeEnum.getCodeByDesc(heOrderGoodsEntity.getSkuName());
            if (StringUtils.isEmpty(code)){
                continue;
            }
            OrderParamHistoryValuePushDTO orderParamHistoryValuePushDTO = resultList.stream().filter(item -> code.equals(item.getName())).findFirst().orElse(null);
            if (Objects.nonNull(orderParamHistoryValuePushDTO)){
                continue;
            }
            OrderParamHistoryValuePushDTO param = new OrderParamHistoryValuePushDTO();
            param.setName(code);
            param.setValue(flag);
            resultList.add(param);
        }
        return resultList;
    }

    private String getHomeAddress(ExtraInfo extraInfo, String code){

        if (Objects.isNull(extraInfo)){
            return StringUtils.EMPTY;
        }
        List<CustomAttribute> fulfillExtraList = extraInfo.getFulfillExtraList();
        if (CollectionUtils.isEmpty(fulfillExtraList)){
            return StringUtils.EMPTY;
        }
        CustomAttribute customAttribute1 = fulfillExtraList.stream().filter(customAttribute -> code.equals(customAttribute.getCode())).findFirst().orElse(null);
        if (Objects.isNull(customAttribute1)){
            return StringUtils.EMPTY;
        }
        return customAttribute1.getShowStr();
    }

    @Override
    public Integer bizHandleType() {
        return BUEnum.CARE_FOR_HOME.getCode();
    }
}

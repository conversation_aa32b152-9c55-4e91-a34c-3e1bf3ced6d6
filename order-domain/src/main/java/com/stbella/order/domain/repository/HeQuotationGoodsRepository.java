package com.stbella.order.domain.repository;

import com.stbella.order.domain.order.month.entity.HeQuotationGoodsEntity;

import java.util.List;

public interface HeQuotationGoodsRepository {

    /**
     * 根据商品名称获取报价单信息
     * @return
     */
    List<HeQuotationGoodsEntity> getListByGoodsName(String goodsName);

    /**
     * 根据报价单Id，获取报价单商品数据
     * @param quotationIds
     * @return
     */
    List<HeQuotationGoodsEntity> getByQuotationIdsList(List<Integer> quotationIds);

    /**
     * 批量添加报价单对应的商品信息
     * @param heQuotationGoodsEntityList
     */
    void batchSave(List<HeQuotationGoodsEntity> heQuotationGoodsEntityList);
}

package com.stbella.order.domain.order.entity;

import com.google.common.collect.Lists;
import com.stbella.order.common.utils.SpringContextHolder;
import com.stbella.order.domain.repository.HeCartGoodsRepository;
import com.stbella.platform.order.api.res.PromotionInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Accessors(chain = true)
public class HeCartGoodsEntity {

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "购物车ID")
    private Integer cartId;

    @ApiModelProperty(value = "是否礼赠：false-否，true-是")
    private boolean gift;

    /**
     * sn
     */
    private String itermSn;

    /**
     * 类型：2表示单项，3表示组合
     */
    private Integer type;

    /**
     * 组合商品明细中，每个商品所属于的组
     */
    @ApiModelProperty(value = "组合商品明细中，每个商品所属于的组")
    private String groupKey;

    /**
     * 附属商品所属主商品记录
     */
    private String parentSn;

    /**
     * 用户id
     */
    private Integer basicUid;

    /**
     * 门店id
     */
    private Integer storeId;

    @ApiModelProperty(value = "商品SpuID")
    private Integer goodsId;

    @ApiModelProperty(value = "原商品ID（用于订单升级）")
    private Integer originalGoodsId;

    @ApiModelProperty(value = "原商品数量（用于订单升级）")
    private Integer originalGoodsNum;

    @ApiModelProperty(value = "goodsType")
    private Integer goodsType;

    @ApiModelProperty(value = "商品SkuID")
    private Integer skuId;

    @ApiModelProperty(value = "一级前台类目")
    private Integer firstFrontCategoryId;

    /**
     * 商品名称
     */
    private String skuName;

    @ApiModelProperty(value = "规格名称")
    private String specification;

    @ApiModelProperty(value = "加购数量")
    private Integer num;

    /**
     * 包含sku数量
     * 组合商品明细中，一个商品可能包含多个sku
     */
    @ApiModelProperty(value = "包含sku数量")
    private Integer skuNum = 1;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime addTime;

    @ApiModelProperty(value = "最近更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "是否删除：false-否，true-是")
    private Integer deleted;

    @ApiModelProperty("附件名称")
    private String goodsAttachmentName;

    @ApiModelProperty("附件url")
    private String goodsAttachment;

    @ApiModelProperty("商品图片")
    private String skuUrl;

    @ApiModelProperty("物品清单")
    private String inventoryUrl;

    @ApiModelProperty(value = "组合商品的子商品集合")
    private List<HeCartGoodsEntity> subGoodsList;

    @ApiModelProperty(value = "活动信息")
    private PromotionInfo promotionInfo;

    @ApiModelProperty(value = "组合类型：0-固定搭配；1;可选")
    private Integer combinationType;

    @ApiModelProperty(value = "0-单规格；1-多规格")
    private Integer specificationType;



    private HeCartGoodsRepository heCartGoodsRepository;

    public HeCartGoodsEntity() {
        this.heCartGoodsRepository = SpringContextHolder.getBean(HeCartGoodsRepository.class);
    }

    public void delete() {
        List<Integer> ids = Lists.newArrayList(id);
        if (CollectionUtils.isNotEmpty(subGoodsList))
            ids.addAll(subGoodsList.stream().map(HeCartGoodsEntity::getId).collect(Collectors.toList()));
        heCartGoodsRepository.deleteByIds(ids);
    }

}

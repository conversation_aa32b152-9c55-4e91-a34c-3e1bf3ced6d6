package com.stbella.order.domain.order.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.stbella.core.utils.BigDecimalUtil;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.month.TemplateContractTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.production.GoodsEntity;
import com.stbella.order.domain.order.service.OrderParamHistoryService;
import com.stbella.order.domain.repository.*;
import com.stbella.order.server.contract.req.OrderParamHistoryPushDTO;
import com.stbella.order.server.contract.req.OrderParamHistoryValuePushDTO;
import com.stbella.order.server.order.month.enums.RelationshipWithEnum;
import com.stbella.order.server.order.month.req.WechatMyOrderInfoNewQuery;
import com.stbella.order.server.order.month.res.OrderGoodsInfoVO;
import com.stbella.order.server.order.month.res.OrderGoodsVO;
import com.stbella.order.server.order.month.res.WechatMyOrderInfoNewVO;
import com.stbella.order.server.order.month.res.WechatMyOrderNewVO;
import com.stbella.order.server.order.month.service.AddressQueryService;
import com.stbella.order.server.order.month.service.OrderV3Service;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Service
@Slf4j
public class OrderParamHistoryServiceImpl implements OrderParamHistoryService {

    @Resource
    private AddressQueryService addressQueryService;

    @Resource
    private StoreRepository cfgStoreRepository;

    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;

    @Resource
    private DistrictRepository districtRepository;

    @Resource
    private OrderGoodsRepository orderGoodsRepository;

    @Resource
    private OrderBailorSnapshotRepository orderBailorSnapshotRepository;

    @Resource
    private OrderV3Service orderV3Service;

    @Resource
    private RuleLinkClient ruleLinkClient;

    @Override
    public OrderParamHistoryPushDTO buildOrderParamHistory(HeOrderEntity entity) {

        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderUserSnapshotRepository.queryByOrderId(entity.getOrderId());

        List<OrderParamHistoryValuePushDTO> paramList = new ArrayList<>();
        //甲方
        OrderParamHistoryValuePushDTO param1 = new OrderParamHistoryValuePushDTO();
        param1.setName("client_name");
        param1.setValue(heOrderUserSnapshotEntity.getName());
        paramList.add(param1);
        //地址
        OrderParamHistoryValuePushDTO param2 = new OrderParamHistoryValuePushDTO();
        param2.setName("address");
        param2.setValue(addressQueryService.getProvincesRegions(heOrderUserSnapshotEntity.getProvince(), heOrderUserSnapshotEntity.getCity(), heOrderUserSnapshotEntity.getRegion()) + heOrderUserSnapshotEntity.getAddress());
        paramList.add(param2);
        //手机
        OrderParamHistoryValuePushDTO param3 = new OrderParamHistoryValuePushDTO();
        param3.setName("phone");
        param3.setValue(heOrderUserSnapshotEntity.getPhone());
        paramList.add(param3);
        //身份证号
        OrderParamHistoryValuePushDTO param4 = new OrderParamHistoryValuePushDTO();
        param4.setName("id_card");
        param4.setValue(heOrderUserSnapshotEntity.getIdCard());
        paramList.add(param4);
        //甲方紧急联络人姓名
        OrderParamHistoryValuePushDTO param5 = new OrderParamHistoryValuePushDTO();
        param5.setName("urgent_name");
        param5.setValue(heOrderUserSnapshotEntity.getUrgentName());
        paramList.add(param5);
        //与甲方关系情况
        OrderParamHistoryValuePushDTO param6 = new OrderParamHistoryValuePushDTO();
        param6.setName("relation_with_client_detail");
        param6.setValue(RelationshipWithEnum.getValueByCode(heOrderUserSnapshotEntity.getRelationWithClient()));
        paramList.add(param6);
        //紧急联络人电话
        OrderParamHistoryValuePushDTO param7 = new OrderParamHistoryValuePushDTO();
        param7.setName("urgent_phone");
        param7.setValue(heOrderUserSnapshotEntity.getUrgentPhone());
        paramList.add(param7);

        //乙方
        OrderParamHistoryValuePushDTO param8 = new OrderParamHistoryValuePushDTO();
        param8.setName("company_name");
        param8.setValue(BizConstant.BANK_NAME);
        paramList.add(param8);

        OrderParamHistoryValuePushDTO param10 = new OrderParamHistoryValuePushDTO();
        param10.setName("predict_born_time");
        Date predictBornDate = heOrderUserSnapshotEntity.getPredictBornDate();
        param10.setValue(Objects.isNull(predictBornDate) || DateUtil.year(predictBornDate) == 2099 ? "/" : DateUtil.formatChineseDate(predictBornDate, false, false));
        paramList.add(param10);

        OrderParamHistoryValuePushDTO param11 = new OrderParamHistoryValuePushDTO();
        param11.setName("check_in_date");
        Date wantIn = heOrderUserSnapshotEntity.getWantIn();
        param11.setValue(Objects.isNull(wantIn) || DateUtil.year(wantIn) == 2099 ? "/" : DateUtil.formatChineseDate(wantIn, false, false));
        paramList.add(param11);

        BigDecimal orderAmount = AmountChangeUtil.changeF2Y(entity.getOrderAmount());
        OrderParamHistoryValuePushDTO param15 = new OrderParamHistoryValuePushDTO();
        param15.setName("order_amount");
        param15.setValue(RMBUtils.formatToseparaDecimals(orderAmount));
        paramList.add(param15);

        //套餐费用(套餐原价格)大写
        OrderParamHistoryValuePushDTO param16 = new OrderParamHistoryValuePushDTO();
        param16.setName("order_amount_words");
        param16.setValue(RMBUtils.numToRMBStr(Objects.isNull(orderAmount) ? 0 : orderAmount.doubleValue()));
        paramList.add(param16);

        //订单总额(订单应收)
        Integer calPayable = entity.calPayable();
        BigDecimal calPayableAmount = AmountChangeUtil.changeF2Y(calPayable);
        OrderParamHistoryValuePushDTO param17 = new OrderParamHistoryValuePushDTO();
        param17.setName("pay_amount");
        param17.setValue(RMBUtils.formatToseparaDecimals(calPayableAmount));
        paramList.add(param17);

        //订单总额(订单应收)大写
        OrderParamHistoryValuePushDTO param18 = new OrderParamHistoryValuePushDTO();
        param18.setName("pay_amount_words");
        param18.setValue(RMBUtils.numToRMBStr(Objects.isNull(calPayableAmount) ? 0 : calPayableAmount.doubleValue()));
        paramList.add(param18);

        //预计护理期限共计
        List<HeOrderGoodsEntity> orderGoodsList = orderGoodsRepository.getByOrderIdList(Collections.singletonList(entity.getOrderId()));
        Integer serviceDays = 0;
        if (CollectionUtils.isNotEmpty(orderGoodsList)){
            serviceDays = orderGoodsList.stream().filter(a -> BizConstant.OrderAppKey.ASSET_LIST_ROOM_SERVICE.contains(a.getGoodsType())).filter(item -> Objects.nonNull(item.getGoodsNum())).mapToInt(HeOrderGoodsEntity::getGoodsNum).sum();
        }
        OrderParamHistoryValuePushDTO param9 = new OrderParamHistoryValuePushDTO();
        param9.setName("service_days");
        param9.setValue(String.valueOf(serviceDays));
        paramList.add(param9);

        CfgStoreEntity cfgStoreEntity = cfgStoreRepository.queryCfgStoreById(entity.getStoreId());
        //门店名称
        OrderParamHistoryValuePushDTO param12 = new OrderParamHistoryValuePushDTO();
        param12.setName("store_name");
        param12.setValue(cfgStoreEntity.getStoreName());
        paramList.add(param12);

        //多胎费用
        BigDecimal originPriceAmount = AmountChangeUtil.changeF2Y(cfgStoreEntity.getMultipleBirthsPrice());
        OrderParamHistoryValuePushDTO param13 = new OrderParamHistoryValuePushDTO();
        param13.setName("multiple_birth_price");
        param13.setValue(RMBUtils.formatToseparaDecimals(originPriceAmount));
        paramList.add(param13);

        OrderParamHistoryValuePushDTO param14 = new OrderParamHistoryValuePushDTO();
        param14.setName("currency");
        param14.setValue(entity.getCurrency());
        paramList.add(param14);

        //每日节日费用
        Integer holiday = cfgStoreEntity.getHolidayPrice();
        BigDecimal holidayAmount = AmountChangeUtil.changeF2Y(holiday);
        OrderParamHistoryValuePushDTO param25 = new OrderParamHistoryValuePushDTO();
        param25.setName("holiday_price");
        param25.setValue(RMBUtils.formatToseparaDecimals(holidayAmount));
        paramList.add(param25);

        //预定金 签单金额的50%
        BigDecimal divide = Objects.isNull(calPayableAmount) ? BigDecimal.ZERO : BigDecimalUtil.divide(calPayableAmount, new BigDecimal(2));
        OrderParamHistoryValuePushDTO param38 = new OrderParamHistoryValuePushDTO();
        param38.setName("pay_amount_2");
        param38.setValue(RMBUtils.formatToseparaDecimals(divide));
        paramList.add(param38);
        //业绩生效金额临界值大写
        OrderParamHistoryValuePushDTO param39 = new OrderParamHistoryValuePushDTO();
        param39.setName("pay_amount_2_words");
        param39.setValue(RMBUtils.numToRMBStr(divide.doubleValue()));
        paramList.add(param39);

        //尾款
        OrderParamHistoryValuePushDTO param40 = new OrderParamHistoryValuePushDTO();
        param40.setName("pay_amount_3");
        param40.setValue(RMBUtils.formatToseparaDecimals(divide));
        paramList.add(param40);
        //尾款大写
        OrderParamHistoryValuePushDTO param41 = new OrderParamHistoryValuePushDTO();
        param41.setName("pay_amount_3_words");
        param41.setValue(RMBUtils.numToRMBStr(divide.doubleValue()));
        paramList.add(param41);

        //离馆日期
        OrderParamHistoryValuePushDTO param42 = new OrderParamHistoryValuePushDTO();
        param42.setName("check_out_date");
        param42.setValue(Objects.isNull(wantIn)|| DateUtil.year(wantIn) == 2099 ? "/" : DateUtil.formatChineseDate(DateUtils.addTime(wantIn, serviceDays, Calendar.DAY_OF_YEAR), false, false));
        paramList.add(param42);

        //门店地址
        OrderParamHistoryValuePushDTO param44 = new OrderParamHistoryValuePushDTO();
        param44.setName("storeAddress");
        param44.setValue(contractStoreAddress(cfgStoreEntity));
        paramList.add(param44);

        //服务对象人数
        OrderParamHistoryValuePushDTO param45 = new OrderParamHistoryValuePushDTO();
        param45.setName("service_people_num");
        param45.setValue(String.valueOf(heOrderUserSnapshotEntity.getFetusNum() + 1));
        paramList.add(param45);


        //每日节日费用
        Integer deposit = cfgStoreEntity.getDeposit();
        BigDecimal depositAmount = AmountChangeUtil.changeF2Y(deposit);
        OrderParamHistoryValuePushDTO param46 = new OrderParamHistoryValuePushDTO();
        param46.setName("deposit");
        param46.setValue(RMBUtils.formatToseparaDecimals(depositAmount));
        paramList.add(param46);

        HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity = orderBailorSnapshotRepository.queryByOrderId(entity.getOrderId());
        OrderParamHistoryValuePushDTO param48 = new OrderParamHistoryValuePushDTO();
        param48.setName("bailor_name");
        param48.setValue(Objects.isNull(heOrderBailorSnapshotEntity) || Objects.isNull(heOrderBailorSnapshotEntity.getName()) ? "/" : heOrderBailorSnapshotEntity.getName());
        paramList.add(param48);
        OrderParamHistoryValuePushDTO param49 = new OrderParamHistoryValuePushDTO();
        param49.setValue(Objects.isNull(heOrderBailorSnapshotEntity) || Objects.isNull(heOrderBailorSnapshotEntity.getIdCard()) ? "/" : heOrderBailorSnapshotEntity.getIdCard());
        param49.setName("bailor_id_card");
        paramList.add(param49);
        //折扣减免
        BigDecimal payAbleAmount = AmountChangeUtil.changeF2Y(entity.getOrderAmount() - calPayable);
        OrderParamHistoryValuePushDTO param50 = new OrderParamHistoryValuePushDTO();
        param50.setName("price_reduction");
        param50.setValue(RMBUtils.formatToseparaDecimals(payAbleAmount));
        paramList.add(param50);
        //折扣减免大写
        OrderParamHistoryValuePushDTO param51 = new OrderParamHistoryValuePushDTO();
        param51.setName("price_reduction_words");
        param51.setValue(RMBUtils.numToRMBStr(Objects.isNull(payAbleAmount) ? 0 : payAbleAmount.doubleValue()));
        paramList.add(param51);

        OrderParamHistoryValuePushDTO param52 = new OrderParamHistoryValuePushDTO();
        param52.setName("contract_sign_date");
        param52.setValue(DateTime.now().toDateStr());
        paramList.add(param52);

        fillRemarkInfo(entity.getOrderId(), paramList, entity.getRemark());
        OrderParamHistoryPushDTO dto = new OrderParamHistoryPushDTO();
        dto.setOrderId(entity.getOrderId());
        dto.setOrderType(entity.getOrderType());
        dto.setStoreId(entity.getStoreId());
        dto.setTemplateContractType(TemplateContractTypeEnum.YZ_SAINTBELLA.code());
        dto.setParamList(paramList);
        log.info("订单推送合同参数={}", JSONUtil.parse(dto));
        return dto;
    }

    private void fillRemarkInfo(Integer orderId, List<OrderParamHistoryValuePushDTO> paramList, String remarkInfo) {

        WechatMyOrderInfoNewQuery query = new WechatMyOrderInfoNewQuery();
        query.setOrderId(orderId);
        //TODO 合同修改
        WechatMyOrderInfoNewVO myOrderInfoNewVO = orderV3Service.myOrderInfo(query);
        WechatMyOrderNewVO orderInfo = myOrderInfoNewVO.getOrderInfo();
        if (Objects.isNull(orderInfo)) {
            return;
        }
        String currency = convertCurrency(orderInfo.getCurrency());
        //购买
        OrderParamHistoryValuePushDTO param52 = new OrderParamHistoryValuePushDTO();
        param52.setName("remark1");
        param52.setValue(setRemark(orderInfo.getNoGiftGoodsList(), currency));
        paramList.add(param52);

        //购买
        OrderParamHistoryValuePushDTO param54 = new OrderParamHistoryValuePushDTO();
        param54.setName("remark3");
        param54.setValue(setRemark(orderInfo.getPromotionGoodsList(), currency));
        paramList.add(param54);
        //礼赠
        OrderParamHistoryValuePushDTO param53 = new OrderParamHistoryValuePushDTO();
        param53.setName("remark2");

        String remark = setRemark(orderInfo.getGiftGoodsList(), currency);
        StringBuilder goodsRemarkInfo = new StringBuilder();
        if (StringUtils.isNotEmpty(remark)){
            goodsRemarkInfo.append("定制礼赠:\n").append(remark).append("\n\n\n");
        }
        goodsRemarkInfo.append("特殊事项备注:");
        if (StringUtils.isNotEmpty(remarkInfo)){
            goodsRemarkInfo.append("\n").append(remarkInfo);
        }
        param53.setValue(goodsRemarkInfo.toString());
        paramList.add(param53);
    }

    private String setRemark(List<OrderGoodsInfoVO> goodsList, String currencyInfo) {

        if (CollectionUtils.isEmpty(goodsList)) {
            return "";
        }
        final int[] i = {1};
        StringBuilder goodsInfo = new StringBuilder("");
        goodsList.forEach(good -> {
            goodsInfo.append(i[0]).append(".").append(good.getGoodsName()).append("*").append(good.getGoodsNum()).append("(").append(good.getGoodsPriceOrgin()).append(currencyInfo).append("*").append(good.getGoodsNum()).append(")");
            if (CollectionUtils.isNotEmpty(good.getOrderGoodsChildList())) {
                String childGoodsInfo = getChildGoodsInfo(good.getOrderGoodsChildList(), currencyInfo);
                if (StringUtils.isNotEmpty(childGoodsInfo)) {
                    if (CollectionUtils.isEmpty(good.getOrderGoodsSubList())) {
                        good.setOrderGoodsSubList(Collections.singletonList(childGoodsInfo));
                    } else {
                        good.getOrderGoodsSubList().add(childGoodsInfo);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(good.getOrderGoodsSubList())) {
                String containGoodsInfo = getContainGoodsInfo(good.getOrderGoodsSubList());
                if (StringUtils.isNotEmpty(containGoodsInfo)) {
                    goodsInfo.append(containGoodsInfo);
                }
            }
            goodsInfo.append("。");
            if (i[0] < goodsList.size()) {
                goodsInfo.append("\n");
            }
            i[0]++;
        });
        return goodsInfo.toString();
    }

    public static String getContainGoodsInfo(List<String> orderGoodsSubList) {
        try {
            StringBuilder goodsInfo = new StringBuilder("包含(");
            orderGoodsSubList.forEach(subGoods -> {
                goodsInfo.append(subGoods).append(";");
            });
            return goodsInfo.substring(0, goodsInfo.toString().length() - 1).concat(")");
        } catch (Exception e) {
            log.error("构建商品包含信息发生异常e:{}", e.getMessage(), e);
        }
        return null;
    }


    public static String getChildGoodsInfo(List<OrderGoodsVO> orderGoodsChildList, String currencyInfo) {
        try {
            StringBuilder requestUrl = new StringBuilder("");
            orderGoodsChildList.forEach(child -> {
                requestUrl.append(child.getGoodsName()).append("*").append(child.getGoodsNum()).append("(".concat(child.getGoodsPriceOrgin().toString()).concat(currencyInfo + "*" + child.getGoodsNum() + ");"));
            });
            return requestUrl.substring(0, requestUrl.toString().length() - 1);
        } catch (Exception e) {
            log.error("");
        }
        return null;
    }

    private String convertCurrency(String currency) {

        Map<String, String> factMap = new HashMap<>();
        factMap.put("configCode","contract_order_currency_key_to_value");
        ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req("systemConfig", factMap);
        HitRuleVo hitRuleVo = ruleLinkClient.hitOneRule(executeRuleV2Req);
        if (Objects.isNull(hitRuleVo) || StringUtils.isEmpty(hitRuleVo.getSimpleRuleValue())){
            return "元";
        }
        Map<String, String> map = JSONObject.parseObject(hitRuleVo.getSimpleRuleValue(), Map.class);
        if (map.containsKey(currency)){
            return map.get(currency);
        }
        return "元";
    }

    /**
     * 组合门店地址 加上省市区
     *
     * @param store
     * @return
     */
    private String contractStoreAddress(CfgStoreEntity store) {
        String address = "";
        List<String> removeDuplicatesNameList = districtRepository.getRemoveDuplicatesNameList(Arrays.asList(store.getProvince(), store.getCity(), store.getRegion()));
        StringBuilder stringBuilder = new StringBuilder();
        for (String s : removeDuplicatesNameList) {
            stringBuilder.append(s);
        }
        address = stringBuilder + store.getAddress();
        return address;
    }
}

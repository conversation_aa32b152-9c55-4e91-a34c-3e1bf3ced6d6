package com.stbella.order.domain.order.service.impl;

import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.service.OrderParamHistoryService;
import com.stbella.order.domain.order.service.OrderParamTemplate;
import com.stbella.order.server.contract.req.OrderParamHistoryPushDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;

@Service
@Slf4j
public class OrderParamHistoryServiceImpl implements OrderParamHistoryService {

    @Resource
    private OrderParamTemplate[] orderParamTemplates;

    @Override
    public OrderParamHistoryPushDTO buildOrderParamHistory(HeOrderEntity entity) {

        OrderParamTemplate template = getTemplate(entity.getBu());
        return template.buildOrderParamHistory(entity);
    }

    public OrderParamTemplate getTemplate(Integer bizType) {

        log.info("获取订单参数模板开始处理: dataType:{}", bizType);
        if (bizType == null) {
            return null;
        }
        return Arrays.stream(orderParamTemplates).filter(item -> item.bizHandleType().equals(bizType)).findAny()
                .orElse(null);
    }

}

package com.stbella.order.domain.order.month.entity;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * HeOrderAttachmentEntity
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20 13:33
 */
@Data
@ApiModel(value = "HeOrderAttachmentEntity对象", description = "订单附件表")
public class HeOrderAttachmentEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Integer id;

    /**
     * 订单id
     */
    @ApiModelProperty("订单id")
    private Integer orderId;

    /**
     * 订单商品id， 如果该附件跟随某个订单商品，则记录订单商品id
     */
    @ApiModelProperty("订单商品id， 如果该附件跟随某个订单商品，则记录订单商品id")
    private Integer ogId;

    /**
     * skuId
     */
    @ApiModelProperty("skuId")
    private Integer skuId;

    /**
     * 附件的存储地址
     */
    @ApiModelProperty("附件的存储地址")
    private String url;

    /**
     * 附件名称
     */
    @ApiModelProperty("附件名称")
    private String name;

    /**
     * 是否删除：1是，0否
     */
    @ApiModelProperty("是否删除：1是，0否")
    private int isDelete;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Long createdAt;

    /**
     * 最近更新时间
     */
    @ApiModelProperty("最近更新时间")
    private Long updatedAt;


}
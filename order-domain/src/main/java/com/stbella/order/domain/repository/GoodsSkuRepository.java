package com.stbella.order.domain.repository;

import com.stbella.order.domain.order.production.GoodsSkuEntity;

import java.util.List;

public interface GoodsSkuRepository {

    GoodsSkuEntity selectById(Integer skuId);

    /**
     * @description: 查询商品sku，包含已经删除的
     * @author: <PERSON><PERSON><PERSON><PERSON>
     * @date: 8/29/23 15:25
     * @param: [skuId]
     * @return: com.stbella.order.domain.order.production.GoodsSkuEntity
     **/
    GoodsSkuEntity selectForDeletedById(Integer skuId);

    GoodsSkuEntity selectByGoodsId(Integer goodsId);

    List<GoodsSkuEntity> selectByIdList(List<Integer> skuId);

    List<GoodsSkuEntity> selectForDeletedByIdList(List<Integer> skuId);

    List<GoodsSkuEntity> selectByGoodsIdListForDeleted(List<Integer> goodsIdList);

}

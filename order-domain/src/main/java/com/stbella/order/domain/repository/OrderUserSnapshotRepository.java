package com.stbella.order.domain.repository;

import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.server.order.month.req.OrderMonthClientReq;

import java.util.Date;
import java.util.List;

public interface OrderUserSnapshotRepository {

    /***
     * 保存订单用户快照
     */
    Integer insertOne(HeOrderUserSnapshotEntity entity);

    Integer insertOrUpdateOne(HeOrderUserSnapshotEntity entity);

    /***
     * 修改订单用户快照
     */
    Integer updateOne(HeOrderUserSnapshotEntity entity);

    /***
     * 请求转换 req -> entity
     */
    HeOrderUserSnapshotEntity orderMonthClientReq2Entity(OrderMonthClientReq orderMonthClientReq, Integer orderId);

    HeOrderUserSnapshotEntity queryByOrderId(Integer orderId);

    List<HeOrderUserSnapshotEntity> queryByOrderIdList(List<Integer> orderIdList);

    List<HeOrderUserSnapshotEntity> queryByUserIdOrNameOrSource(String name, String phone, List<Integer> source);

    /**
     * 测试 手机号查询
     * @param phone
     * @return
     */
    List<HeOrderUserSnapshotEntity> queryByPhoneForTest(String phone);

    /**
     * 获取指定日期之后的预产期订单客户列表
     *
     * @param date
     * @return
     */
    List<HeOrderUserSnapshotEntity> queryPredictBornDateListByDate(Date date);
}

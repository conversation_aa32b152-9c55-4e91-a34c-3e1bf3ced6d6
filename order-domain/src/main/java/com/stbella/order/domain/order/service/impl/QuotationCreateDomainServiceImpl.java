package com.stbella.order.domain.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.core.base.Operator;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.order.BizActivityEnum;
import com.stbella.order.common.enums.order.QuotationOrderStatusEnum;
import com.stbella.order.domain.order.entity.OrderOperateLogEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.month.entity.HeQuotationEntity;
import com.stbella.order.domain.order.month.entity.HeQuotationGoodsEntity;
import com.stbella.order.domain.order.service.QuotationCreateDomainService;
import com.stbella.order.domain.repository.HeQuotationGoodsRepository;
import com.stbella.order.domain.repository.HeQuotationRepository;
import com.stbella.order.domain.repository.OrderOperatorLogRepository;
import com.stbella.store.common.enums.core.GoodsTypeEnum;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class QuotationCreateDomainServiceImpl implements QuotationCreateDomainService {

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private OrderOperatorLogRepository operatorLogRepository;

    @Resource
    private HeQuotationRepository heQuotationRepository;

    @Resource
    private HeQuotationGoodsRepository heQuotationGoodsRepository;




    @Override
    public Integer createQuotation(HeOrderEntity entity, Operator operator) {

        if (CollectionUtil.isNotEmpty(entity.getGoodsList())) {
            fillOrderType(entity);
        }
        HeQuotationEntity heQuotationEntity = bulidQuotationEntity(entity, operator);
        Integer id = heQuotationRepository.save(heQuotationEntity);
        OrderOperateLogEntity logEntity = OrderOperateLogEntity.builder()
                .operatorId(operator.getOperatorGuid())
                .operatorName(operator.getOperatorName())
                .orderSn(entity.getOrderSn())
                .operatorPhone(operator.getOperatorPhone())
                .operatorType(BizActivityEnum.CREATE_ORDER.code())
                .remark("创建报价单").build();
        List<HeQuotationGoodsEntity> heQuotationGoodsEntityList = Lists.newArrayList();
        for (HeOrderGoodsEntity goodsEntity : entity.getGoodsList()) {
            goodsEntity.setOrderId(id);
            HeQuotationGoodsEntity heQuotationGoodsEntity = buildHeQuotationGoodsEntity(goodsEntity, id);
            if (Objects.isNull(heQuotationGoodsEntity)){
                continue;
            }
            heQuotationGoodsEntityList.add(heQuotationGoodsEntity);
        }
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@NonNull TransactionStatus transactionStatus) {
                heQuotationGoodsRepository.batchSave(heQuotationGoodsEntityList);
                operatorLogRepository.save(logEntity);
            }
        });
        return id;
    }

    private HeQuotationGoodsEntity buildHeQuotationGoodsEntity(HeOrderGoodsEntity goodsEntity, Integer quotationId) {

        if (Objects.isNull(goodsEntity)){
            return null;
        }
        HeQuotationGoodsEntity heQuotationGoodsEntity = new HeQuotationGoodsEntity();
        heQuotationGoodsEntity.setParentId(goodsEntity.getParentId());
        heQuotationGoodsEntity.setQuotationId(quotationId);
        heQuotationGoodsEntity.setSkuId(goodsEntity.getSkuId());
        heQuotationGoodsEntity.setSkuName(goodsEntity.getSkuName());
        heQuotationGoodsEntity.setGoodsId(goodsEntity.getGoodsId());
        heQuotationGoodsEntity.setGoodsType(goodsEntity.getGoodsType());
        heQuotationGoodsEntity.setGoodsName(goodsEntity.getGoodsName());
        heQuotationGoodsEntity.setGoodsImage(goodsEntity.getGoodsImage());
        heQuotationGoodsEntity.setGoodsNum(goodsEntity.getGoodsNum());
        heQuotationGoodsEntity.setGoodsCost(goodsEntity.getGoodsCost());
        heQuotationGoodsEntity.setGoodsPriceOrgin(goodsEntity.getGoodsPriceOrgin());
        heQuotationGoodsEntity.setGoodsPricePay(goodsEntity.getGoodsPricePay());
        heQuotationGoodsEntity.setPayAmount(goodsEntity.getPayAmount());
        heQuotationGoodsEntity.setServiceDays(goodsEntity.getServiceDays());
        heQuotationGoodsEntity.setSendStatus(goodsEntity.getSendStatus());
        heQuotationGoodsEntity.setSource(goodsEntity.getSource());
        heQuotationGoodsEntity.setContent(goodsEntity.getContent());
        heQuotationGoodsEntity.setSkuExtendId(goodsEntity.getSkuExtendId());
        heQuotationGoodsEntity.setGoodsSellType(goodsEntity.getGoodsSellType());
        heQuotationGoodsEntity.setIntegral(goodsEntity.getIntegral());
        heQuotationGoodsEntity.setPayIntegral(goodsEntity.getPayIntegral());
        heQuotationGoodsEntity.setPiece(goodsEntity.getPiece());
        heQuotationGoodsEntity.setRoomId(goodsEntity.getRoomId());
        heQuotationGoodsEntity.setRoomName(goodsEntity.getRoomName());
        heQuotationGoodsEntity.setEcpRoomType(goodsEntity.getEcpRoomType());
        heQuotationGoodsEntity.setDefinedProperty(goodsEntity.getDefinedProperty());
        heQuotationGoodsEntity.setType(goodsEntity.getType());
        heQuotationGoodsEntity.setBusinessType(goodsEntity.getBusinessType());
        heQuotationGoodsEntity.setAssetType(goodsEntity.getAssetType());
        heQuotationGoodsEntity.setBackCategoryId(goodsEntity.getBackCategoryId());
        heQuotationGoodsEntity.setValidStartTime(goodsEntity.getValidStartTime());
        heQuotationGoodsEntity.setValidEndTime(goodsEntity.getValidEndTime());
        heQuotationGoodsEntity.setGift(goodsEntity.getGift());
        heQuotationGoodsEntity.setInventoryUrl(goodsEntity.getInventoryUrl());
        heQuotationGoodsEntity.setOrderGoodsSn(goodsEntity.getOrderGoodsSn());
        heQuotationGoodsEntity.setParentCombineSn(goodsEntity.getParentCombineSn());
        heQuotationGoodsEntity.setAllocationOriginPrice(goodsEntity.getAllocationOriginPrice());
        heQuotationGoodsEntity.setCreatedAt(new Date());
        return heQuotationGoodsEntity;
    }

    private HeQuotationEntity bulidQuotationEntity(HeOrderEntity heOrder, Operator operator) {

        HeQuotationEntity heQuotationEntity = new HeQuotationEntity();
        heQuotationEntity.setQuotationSn(heOrder.getOrderSn());
        heQuotationEntity.setQuotationType(heOrder.getOrderType());
        heQuotationEntity.setBasicUid(heOrder.getBasicUid());
        heQuotationEntity.setClientUid(heOrder.getClientUid());
        heQuotationEntity.setClientType(heOrder.getClientType());
        heQuotationEntity.setStoreId(heOrder.getStoreId());
        heQuotationEntity.setOrderAmount(heOrder.getOrderAmount());
        heQuotationEntity.setPayAmount(heOrder.getPayAmount());
        heQuotationEntity.setIsPreparePregnancy(heOrder.getIsPreparePregnancy());
        heQuotationEntity.setWantIn(heOrder.getWantIn());
        heQuotationEntity.setRemark(heOrder.getRemark());
        heQuotationEntity.setSource(heOrder.getSource());
        heQuotationEntity.setGrossMargin(heOrder.getGrossMargin());
        heQuotationEntity.setNetMargin(heOrder.getNetMargin());
        heQuotationEntity.setDiscountMargin(heOrder.getDiscountMargin());
        heQuotationEntity.setApprovalDiscountStatus(heOrder.getApprovalDiscountStatus());
        heQuotationEntity.setDiscountDetails(heOrder.getDiscountDetails());
        heQuotationEntity.setOrderTag(heOrder.getOrderTag());
        heQuotationEntity.setOrderTagName(heOrder.getOrderTagName());
        heQuotationEntity.setOrderStatus(QuotationOrderStatusEnum.NO.getCode());
        heQuotationEntity.setCurrency(heOrder.getCurrency());
        heQuotationEntity.setBu(heOrder.getBu());
        heQuotationEntity.setNeedSign(heOrder.getNeedSign());
        heQuotationEntity.setOperatorGuid(Integer.parseInt(operator.getOperatorGuid()));
        heQuotationEntity.setExtraInfo(JSONUtil.toJsonStr(heOrder.getExtraInfo()));
        heQuotationEntity.setCreatedAt(new Date());
        return heQuotationEntity;
    }

    private void fillOrderType(HeOrderEntity heOrderEntity){

        int specialCareCount = (int) heOrderEntity.getGoodsList().stream().filter(goodsEntity -> GoodsTypeEnum.MISCARRIAGE_PACKAGE.code().equals(goodsEntity.getGoodsType())).count();
        int normalCareCount = (int) heOrderEntity.getGoodsList().stream().filter(goodsEntity -> GoodsTypeEnum.CONFINEMENT_PACKAGE.code().equals(goodsEntity.getGoodsType())).count();
        if (normalCareCount == 0 && specialCareCount > 0){
            heOrderEntity.setOrderType(OmniOrderTypeEnum.SMALL_MONTH_ORDER.getCode());
        }
    }


}

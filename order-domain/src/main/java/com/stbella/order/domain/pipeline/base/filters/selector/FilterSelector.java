package com.stbella.order.domain.pipeline.base.filters.selector;

import com.stbella.order.domain.pipeline.base.filters.AbstractFilter;

import java.util.List;

/**
 * 功能描述:
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/8/24.
 */
public interface FilterSelector<F extends AbstractFilter> {

    /**
     * 是否匹配
     *
     * @param currentFilter
     * @return boolean
     * @throws
     * <AUTHOR>
     * @date 2022/8/24 15:37
     * @since 1.0.0
     */
    boolean matchFilter(Class<F> currentFilter);

    /**
     * 按照顺序获取所有filter
     *
     * @param
     * @return java.util.List<java.lang.String>
     * @throws
     * <AUTHOR>
     * @date 2022/8/24 15:38
     * @since 1.0.0
     */
    List<AbstractFilter> getFilters();

}

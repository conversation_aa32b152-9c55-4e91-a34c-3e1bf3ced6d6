package com.stbella.order.domain.order.factory.impl;

import cn.hutool.core.lang.Assert;
import com.stbella.core.base.Operator;
import com.stbella.order.domain.order.factory.OrderFactory;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.domain.repository.StoreRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author: ji<PERSON><PERSON><PERSON>
 * @CreateTime: 2024-08-06  16:05
 * @Description: 订单工厂实现类
 */
@Service
@Slf4j
public class OrderFactoryImpl implements OrderFactory {

    @Resource
    OrderRepository orderRepository;
    @Resource
    StoreRepository storeRepository;

    /**
     * 重建订单对象
     *
     * @param orderId
     * @return
     */
    @Override
    public HeOrderEntity restore(Integer orderId) {
        HeOrderEntity order = orderRepository.queryOrderById(orderId);
        return order;
    }

    /**
     * 重建订单 - 含有门店信息
     *
     * @param orderId
     * @return
     */
    @Override
    public HeOrderEntity restoreWithStore(Integer orderId) {
        HeOrderEntity order = orderRepository.queryOrderById(orderId);
        Assert.notNull(order, "订单已删除");
        CfgStoreEntity store = storeRepository.queryCfgStoreById(order.getStoreId());
        order.setStore(store);
        return order;
    }
}

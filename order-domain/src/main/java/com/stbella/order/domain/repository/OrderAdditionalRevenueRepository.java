package com.stbella.order.domain.repository;

import com.stbella.order.domain.order.month.entity.HeOrderAdditionalRevenueEntity;
import com.stbella.order.server.order.month.req.OrderMonthAdditionalRevenueCacheReq;
import com.stbella.order.server.order.month.res.OrderAdditionalRevenueCacheVO;

import java.util.List;

public interface OrderAdditionalRevenueRepository {

    /***
     * 根据加收项IDs批量获取加收项
     * <AUTHOR>
     * @param ids
     * @return List<HeOrderAdditionalRevenueEntity>
     */
    List<HeOrderAdditionalRevenueEntity> getEntityListByIds(List<Long> ids);

    /***
     * 批量保存
     * <AUTHOR>
     * @param entityList
     * @return Boolean
     */
    List<HeOrderAdditionalRevenueEntity> batchInsert(List<HeOrderAdditionalRevenueEntity> entityList);


    /***
     * 批量修改
     * <AUTHOR>
     * @param entityList
     * @return Boolean
     */
    List<HeOrderAdditionalRevenueEntity> batchUpdate(List<HeOrderAdditionalRevenueEntity> entityList, Integer orderId);

    /***
     * 批量转换 vos -> entityList
     */
    List<HeOrderAdditionalRevenueEntity> orderAdditionalRevenueCacheVOS2EntityList(List<OrderAdditionalRevenueCacheVO> orderAdditionalRevenueCacheVOS, Integer orderId);

    /**
     * 获取订单加收项
     *
     * @return
     */
    List<HeOrderAdditionalRevenueEntity> getByOrderId(Integer orderId);

    List<HeOrderAdditionalRevenueEntity> getByOrderIdList(List<Integer> orderIdList);

    List<OrderMonthAdditionalRevenueCacheReq> voListToReq(List<OrderAdditionalRevenueCacheVO> orderAdditionalRevenueCacheVOS, OrderMonthAdditionalRevenueCacheReq req);

    void delByIdList(List<Long> needDeleteIds);

    List<HeOrderAdditionalRevenueEntity> queryAdditionalByType(Integer type);

    List<Integer> queryAdditionalOrderIdByType(Integer type, Integer status);

    List<HeOrderAdditionalRevenueEntity> queryAdditionalByOrderIdAndType(Integer orderId, Integer type);

    boolean delByOrderId(Integer orderId);
}

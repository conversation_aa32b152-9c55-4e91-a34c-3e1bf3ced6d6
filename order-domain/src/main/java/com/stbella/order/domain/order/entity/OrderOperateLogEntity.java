package com.stbella.order.domain.order.entity;

import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * 订单操作日志
 */
@Data
@Builder
public class OrderOperateLogEntity {
    /**
     * 主键
     */
    private Long id;

    /**
     * 
     */
    private String orderSn;

    /**
     * 操作人id
     */
    private String operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 操作人电话
     */
    private String operatorPhone;

    /**
     * 操作类型
     */
    private String operatorType;

    /**
     * 
     */
    private String remark;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private Date updateTime;

    /**
     * 删除
     */
    private Integer deleted;

}
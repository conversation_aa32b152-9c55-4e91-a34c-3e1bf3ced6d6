package com.stbella.order.domain.order.production.dto;

import com.stbella.core.excel.Column;
import lombok.Data;

import java.io.Serializable;

@Data
public class TherapistRealPaidDTO implements Serializable {

    private static final long serialVersionUID = -5993856169662643385L;

    @Column(value = "产康师id", order = 1)
    private Long therapistId;

    @Column(value = "产康师", order = 2)
    private String therapistName;

    @Column(value = "产康师手机号", order = 3)
    private String therapistMobile;

    @Column(value = "核销金额汇总", order = 4)
    private String realPaidSummary;
}

package com.stbella.order.domain.convert;

import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.order.production.OrderGiftExtendEntity;
import com.stbella.order.domain.order.production.OrderProductionExtendEntity;
import com.stbella.order.server.order.ProductionOrderGoodsModel;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import com.stbella.platform.order.api.res.SkuAdditionalInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * @Author: ji<PERSON><PERSON>an
 * @CreateTime: 2023-07-27  16:19
 * @Description: 订单明细转化器
 */
@Mapper(componentModel = "spring")
public interface OrderGoodsDomainConverter {

    /**
     * entity 转 gift extend (老的模型，兼容核销场景)
     * @param model
     * @return
     */
    @Mappings({
            @Mapping(target = "categoryId", source = "backCategoryId"),
            @Mapping(target = "source",  constant = "1"),
            @Mapping(target = "status",  constant = "0"),
            @Mapping(target = "goodsNum",  source = "goodsNum"),
            @Mapping(target = "skuNum",  constant = "1"),
            @Mapping(target = "type",  constant = "6"),
            @Mapping(target = "cost", source = "goodsCost"),
            @Mapping(target = "price", source = "goodsPriceOrgin"),
            @Mapping(target = "validStartTime", constant = "0L"),
            @Mapping(target = "validEndTime", constant = "0L"),
            @Mapping(target = "verificationStatus", constant = "0"),
            @Mapping(target = "validityType", constant = "1"),
            @Mapping(target = "serviceTag", constant = "1"),
            @Mapping(target = "serveType", source = "serviceType"),

    })
    OrderGiftExtendEntity entity2GiftExtend(HeOrderGoodsEntity model);


    /**
     * giftExtend 转 entity (将礼赠商品转为订单商品)
     * @param model 礼赠商品实体
     * @return 订单商品实体
     */
    @Mappings({
            @Mapping(target = "backCategoryId", source = "categoryId"),
            @Mapping(target = "goodsNum", source = "goodsNum"),
            @Mapping(target = "goodsCost", source = "cost"),
            @Mapping(target = "goodsPriceOrgin", source = "price"),
            @Mapping(target = "serviceType", source = "serveType"),
            @Mapping(target = "type", constant = "2"), // 单件商品
            @Mapping(target = "gift", constant = "1"), // 标记为礼赠
            @Mapping(target = "goodsId", source = "goodsId"),
            @Mapping(target = "goodsName", source = "goodsName"),
            @Mapping(target = "skuId", source = "skuId"),
            @Mapping(target = "skuName", source = "skuName"),
//            @Mapping(target = "goodsType", source = "goodsType"),
            @Mapping(target = "orderId", source = "orderId"),
            @Mapping(target = "storeId", source = "storeId"),
            @Mapping(target = "isDelete", constant = "0"),
            @Mapping(target = "validStartTime", ignore = true),
            @Mapping(target = "validEndTime", ignore = true)
    })
    HeOrderGoodsEntity giftExtend2Entity(OrderGiftExtendEntity model);

    /**
     * entity 转 production extend (老的模型，兼容核销场景)
     * @param model
     * @return
     */
    @Mappings({
            @Mapping(target = "status",  constant = "0"),
            @Mapping(target = "goodsNum",  source = "goodsNum"),
            @Mapping(target = "skuNum",  constant = "1"),
            @Mapping(target = "type",  constant = "2"),
            @Mapping(target = "cost", source = "goodsCost"),
            @Mapping(target = "price", source = "goodsPriceOrgin"),
            @Mapping(target = "validStartTime", constant = "0L"),
            @Mapping(target = "validEndTime", constant = "0L"),
            @Mapping(target = "verificationStatus", constant = "0"),
            @Mapping(target = "validityType", constant = "1"),
            @Mapping(target = "partDiscount", constant = "0"),
            @Mapping(target = "productionAmountPay", constant = "0"),
            @Mapping(target = "validityValue", constant = "0"),
            @Mapping(target = "discountRuleType", constant = "-1"),
            @Mapping(target = "serveType", source = "serviceType"),
    })
    OrderProductionExtendEntity entity2ProductionExtend(HeOrderGoodsEntity model);


}

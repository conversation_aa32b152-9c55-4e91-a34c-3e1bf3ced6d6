package com.stbella.order.domain.pipeline.base.filters;


import com.stbella.order.domain.pipeline.base.context.AbstractContext;

/**
 * 功能描述:
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/8/24.
 */
public interface <PERSON>lterChain<C extends AbstractContext> {

    /**
     * 处理
     *
     * @param context
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2022/8/24 16:28
     * @since 1.0.0
     */
    void handle(C context);

    /**
     * 开启下一个鉴权
     *
     * @param context
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2022/8/24 16:28
     * @since 1.0.0
     */
    void fireNext(C context);

}

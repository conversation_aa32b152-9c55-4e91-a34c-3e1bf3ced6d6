package com.stbella.order.domain.order.production.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.stbella.core.excel.Column;
import lombok.Data;

import java.io.Serializable;

@Data
public class ExportProductionBookDTO implements Serializable {

    private static final long serialVersionUID = -8510775892254322220L;

//    @Column(value = "预约单号", order = 1)
    @ExcelProperty("预约单号")
    private String orderNumber;

//    @Column(value = "门店", order = 2)
    @ExcelProperty("门店")
    private String storeName;

//    @Column(value = "客户", order = 3)
    @ExcelProperty("客户")
    private String clientName;

//    @Column(value = "客户basicuid", order = 4)
    @ExcelProperty("客户basicuid")
    private Integer basicId;

    // @Column(value = "客户手机号", order = 5)
    // private String clientPhone;

//    @Column(value = "产康师", order = 6)
    @ExcelProperty("产康师")
    private String therapistName;

//    @Column(value = "手工费", order = 7)
    @ExcelProperty("手工费")
    private String serveFee;

//    @Column(value = "状态", order = 8)
    @ExcelProperty("状态")
    private String verificationState;

//    @Column(value = "服务时间", order = 9)
    @ExcelProperty("服务时间")
    private String serveTime;

//    @Column(value = "预约单类型", order = 10)
    @ExcelProperty("预约单类型")
    private String appointmentType;

//    @Column(value = "产康订单号", order = 11)
    @ExcelProperty("产康订单号")
    private String productionOrderNo;

//    @Column(value = "产康名称", order = 12)
    @ExcelProperty("产康名称")
    private String productionName;

//    @Column(value = "产康种类", order = 13)
    @ExcelProperty("产康种类")
    private String productionType;

//    @Column(value = "组合名称", order = 14)
    @ExcelProperty("组合名称")
    private String combinationName;

//    @Column(value = "商品名称", order = 15)
    @ExcelProperty("商品名称")
    private String goodsName;

//    @Column(value = "规格值名称", order = 16)
    @ExcelProperty("规格值名称")
    private String specificationsName;

//    @Column(value = "项目类型", order = 17)
    @ExcelProperty("项目类型")
    private String projectType;

//    @Column(value = "预约方式", order = 18)
    @ExcelProperty("预约方式")
    private String bookType;

//    @Column(value = "下单人", order = 19)
    @ExcelProperty("下单人")
    private String createOrderName;

//    @Column(value = "下单时间", order = 20)
    @ExcelProperty("下单时间")
    private String createOrderTime;

//    @Column(value = "核销人", order = 21)
    @ExcelProperty("核销人")
    private String offName;

//    @Column(value = "核销项", order = 22)
    @ExcelProperty("核销项")
    private String offProject;

//    @Column(value = "核销时间", order = 23)
    @ExcelProperty("核销时间")
    private String offTime;

//    @Column(value = "取消人", order = 24)
    @ExcelProperty("取消人")
    private String cancelName;

//    @Column(value = "取消时间", order = 25)
    @ExcelProperty("取消时间")
    private String cancelTime;

//    @Column(value = "修改人", order = 26)
    @ExcelProperty("修改人")
    private String updateName;

//    @Column(value = "修改时间", order = 27)
    @ExcelProperty("修改时间")
    private String updateTime;

    @Column(value = "产康师id", order = 28)
    private Long therapistId;

    @Column(value = "核销金额", order = 29)
    private String realPaid;

    @Column(value = "是否已结算", order = 30)
    private String settleFlg;
}

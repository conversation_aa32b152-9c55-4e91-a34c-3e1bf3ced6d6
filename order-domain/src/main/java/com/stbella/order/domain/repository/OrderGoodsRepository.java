package com.stbella.order.domain.repository;

import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.server.order.month.req.OrderMonthClientReq;
import com.stbella.order.server.order.month.res.OrderMonthGoodsCacheVO;

import java.util.List;

public interface OrderGoodsRepository {
    /***
     * 订单商品保存
     */
    Integer insertOne(HeOrderGoodsEntity entity);

    /**
     * 批量保存
     *
     * @param entityList
     * @return Integer
     */
     Boolean batchInsert(List<HeOrderGoodsEntity> entityList);

    /***
     * 订单商品修改
     */
    @Deprecated
    Integer updateOne(HeOrderGoodsEntity entity);

    /***
     * 订单商品修改
     */
    Integer update(HeOrderGoodsEntity entity);

    /***
     * 订单商品转换vo -> entity
     */
    HeOrderGoodsEntity orderMonthGoodsCacheVO2Entity(OrderMonthGoodsCacheVO vo, OrderMonthClientReq orderMonthClientReq, Integer orderId, String goodsImage);

    HeOrderGoodsEntity getByOrderId(Integer orderId);

    /**
     * 查询所有订单商品
     * @param orderId
     * @return
     */
    List<HeOrderGoodsEntity> getAllItermByOrderId(Integer orderId);

    List<HeOrderGoodsEntity> getByOrderIdList(List<Integer> orderIdList);

    /**
     * 根据类目ID获取订单商品信息
     * @param backCategoryIdList
     * @return
     */
    List<HeOrderGoodsEntity> getByCategoryBackIdList(List<Long> backCategoryIdList, Integer orderId);

    /**
     * 通过订单ID查询订单套餐信息
     *
     * @param orderId
     * @return
     */
    HeOrderGoodsEntity queryOrderGoodsInfoByOrderId(Integer orderId);

    /**
     * 根据商品类目获取订单商品信息
     * @param backCategoryIds
     * @return
     */
    List<HeOrderGoodsEntity> getListByCategoryIds(List<Integer> backCategoryIds, String goodsName, Boolean isQuerySub);

    /**
     * 批量更新
     * @param orderGoodsEntityList
     */
    void batchUpdate(List<HeOrderGoodsEntity> orderGoodsEntityList);

    /**
     * 查询order_goods_sn等于parent_combine_sn的订单商品
     * @return
     */
    List<HeOrderGoodsEntity> getGoodsWithSameOrderSnAndParentSn();
}

package com.stbella.order.domain.order.service.impl;

import com.beust.jcommander.internal.Lists;
import com.stbella.order.common.enums.core.IncomeRecordPayStatusEnum;
import com.stbella.order.common.enums.core.OfflineAuditStatusV2Enum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.domain.order.month.entity.HeIncomeProofRecordEntity;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.service.OrderIncomeDomainService;
import com.stbella.order.domain.repository.IncomeProofRecordRepository;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-07-11  11:54
 * @Description: 订单支付记录服务
 */
@Service
@Slf4j
public class OrderIncomeDomainServiceImpl implements OrderIncomeDomainService {

    @Resource
    IncomeRecordRepository incomeRecordRepository;
    @Resource
    IncomeProofRecordRepository incomeProofRecordRepository;


    /**
     * 查询影响业绩的支付记录
     * 1，成功的在线支付记录
     * 2，非失败的线下支付记录
     *
     * @param entity
     * @return
     */
    @Override
    public List<HeIncomeRecordEntity> queryEffectiveRecord(HeOrderEntity entity) {
        List<HeIncomeRecordEntity> allRecordListByOrderId = incomeRecordRepository.getAllRecordListByOrderId(entity.getOrderId());

        List<HeIncomeProofRecordEntity> proofRecordList = incomeProofRecordRepository.getIncomeProofRecordByOrderId(entity.getOrderId());
        return filterEffectiveRecord(allRecordListByOrderId, proofRecordList);

    }

    @Override
    public List<HeIncomeRecordEntity> filterEffectiveRecord(List<HeIncomeRecordEntity> allRecordListByOrderId, List<HeIncomeProofRecordEntity> incomeProofRecordEntityList) {
        if (CollectionUtils.isEmpty(allRecordListByOrderId)) {
            return new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(incomeProofRecordEntityList)) {
            incomeProofRecordEntityList = new ArrayList<>();
        }
        //成功的在线支付记录
        List<HeIncomeRecordEntity> successfulOnlineRecords = allRecordListByOrderId.stream()
                .filter(income -> OmniPayTypeEnum.OFFLINE.getCode().intValue() != income.getPayType() && OmniPayTypeEnum.REDUCTION.getCode().intValue() != income.getPayType())
                .filter(income -> IncomeRecordPayStatusEnum.COMPLETE.getCode().intValue() == income.getStatus())
                .collect(Collectors.toList());

        successfulOnlineRecords.forEach(r -> {
            r.setApproveStatus(OfflineAuditStatusV2Enum.OTHER.getCode());
            r.setActualSuccess(true);
        });

        List<HeIncomeRecordEntity> notFailedOfflineRecords = allRecordListByOrderId.stream()
                .filter(income -> OmniPayTypeEnum.OFFLINE.getCode().intValue() == income.getPayType())
                .filter(income -> IncomeRecordPayStatusEnum.Failed.getCode().intValue() != income.getStatus())
                .collect(Collectors.toList());

        Map<Integer, Integer> approveStatusMap = incomeProofRecordEntityList.stream().collect(Collectors.toMap(HeIncomeProofRecordEntity::getIncomeId, HeIncomeProofRecordEntity::getStatus));
        notFailedOfflineRecords.forEach(r -> {
            r.setApproveStatus(0);
            if (approveStatusMap.containsKey(r.getId())) {
                r.setApproveStatus(approveStatusMap.get(r.getId()));
            } else {
                log.error("线下支付 无proof incomeId={}", r.getId());
            }
            r.setActualSuccess(false);
            if (r.getApproveStatus().intValue() == OfflineAuditStatusV2Enum.EXAMINATION_PASSED.getCode()) {
                r.setActualSuccess(true);
            }

        });


        successfulOnlineRecords.addAll(notFailedOfflineRecords);
        //按支付时间降序排序
        successfulOnlineRecords.sort((o1, o2) -> o2.getPayTime().compareTo(o1.getPayTime()));

        return successfulOnlineRecords;
    }

    /**
     * 根据支付记录id查询支付记录, 包含审批状态（线下支付场景）
     *
     * @param incomeId
     * @return
     */
    @Override
    public HeIncomeRecordEntity getWithAuthState(Integer incomeId) {
        List<HeIncomeRecordEntity> allRecordListByOrderId = Lists.newArrayList(incomeRecordRepository.getOneById(incomeId));
        HeIncomeProofRecordEntity incomeProofRecord = incomeProofRecordRepository.getLastOneByIncomeId(incomeId);
        List<HeIncomeProofRecordEntity> proofRecordList = new ArrayList<>();
        if (incomeProofRecord != null){
            proofRecordList.add(incomeProofRecord);
        }
        List<HeIncomeRecordEntity> incomeRecordEntities = filterEffectiveRecord(allRecordListByOrderId, proofRecordList);
        return incomeRecordEntities.get(0);
    }

}

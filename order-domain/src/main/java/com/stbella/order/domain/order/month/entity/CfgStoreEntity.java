package com.stbella.order.domain.order.month.entity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 门店相关配置表
 * </p>
 *
 * <AUTHOR> @since 2022-10-27
 */
@Data
public class CfgStoreEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "门店ID 0为总部")
    private Integer storeId;

    @ApiModelProperty(value = "门店名称(标识)")
    private String storeName;

    @ApiModelProperty(value = "省份")
    private Integer province;

    @ApiModelProperty(value = "城市")
    private Integer city;

    @ApiModelProperty(value = "地区")
    private Integer region;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "店长id")
    private Integer managerId;

    @ApiModelProperty(value = "状态")
    private Integer active;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "前缀用于生成订单")
    private String prefix;

    @ApiModelProperty(value = "城市中第几家店")
    private Integer cityNo;

    @ApiModelProperty(value = "最近生成订单的天数")
    private LocalDate billDate;

    @ApiModelProperty(value = "最近生成订单天数的数量")
    private Integer billCount;

    @ApiModelProperty(value = "护士班别配置")
    private String nurseShifts;

    @ApiModelProperty(value = "订单有效比例（订单支付超过这个值后，则订单变为已支付状态）")
    private String orderVo;

    @ApiModelProperty(value = "仓储配置")
    private String storageVo;

    @ApiModelProperty(value = "合同配置")
    private String pactVo;

    @ApiModelProperty(value = "紫消毒灯周期配置")
    private String uvVo;

    @ApiModelProperty(value = "经度")
    private BigDecimal lng;

    @ApiModelProperty(value = "纬度")
    private BigDecimal lat;

    @ApiModelProperty(value = "封面地址")
    private String coverUrl;

    @ApiModelProperty(value = "头图url")
    private String headUrl;

    @ApiModelProperty(value = "多张图片地址 json")
    private String imgs;

    @ApiModelProperty(value = "门店类型: 0圣贝拉，1小贝拉，2月子总部，3到家总部，4到家门店，5商城网店")
    private Integer type;

    @ApiModelProperty(value = "门店子类型;0-Baby BELLA Deluxe;1-BELLA  VILLA")
    private Integer childType;

    @ApiModelProperty(value = "门店名称别名")
    private String nameAlias;

    @ApiModelProperty(value = "门店名称别名 关联钉钉组织架构")
    private String nameAliasDing;

    @ApiModelProperty(value = "门店英文名")
    private String nameEn;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "对应公司名")
    private String companyName;

    @ApiModelProperty(value = "银行账号")
    private String bankSn;

    @ApiModelProperty(value = "银行开户行")
    private String bankOpen;

    @ApiModelProperty(value = "印章")
    private String stampUrl;

    @ApiModelProperty(value = "省份码")
    private String provinceCode;

    @ApiModelProperty(value = "城市码")
    private String cityCode;

    @ApiModelProperty(value = "地区码")
    private String regionCode;

    @ApiModelProperty(value = "主体名称")
    private String contractSignatureName;

    @ApiModelProperty(value = "主体签章图片")
    private String contractSignatureImage;

    @ApiModelProperty(value = "区域：0=未知;1=杭州区;2=苏甬区;3=华中区;4=华北区;5=珠三角区;6=深圳区;7=香港区;8=上海区")
    private Integer regional;

    @ApiModelProperty(value = "门店名称(C端标识)")
    private String storeNameToC;

    @ApiModelProperty(value = "1=E签宝 2=旧合同")
    private Integer contractType;

    @ApiModelProperty(value = "是否开通1v1预约活动：0-关闭，1-开启")
    private Integer appointmentStatus;

    @ApiModelProperty(value = "对应钉钉的部门id")
    private Long deptId;

    @ApiModelProperty(value = "押金金额(分)")
    private Integer deposit;

    @ApiModelProperty(value = "内容位ID")
    private Integer contentId;

    @ApiModelProperty(value = "排序")
    private Integer citySort;

    @ApiModelProperty(value = "节日成本 - 单位：分")
    private Integer holidayCost;

    @ApiModelProperty(value = "节日费用 单位：分")
    private Integer holidayPrice;

    @ApiModelProperty(value = "多胞胎成本元 一胎一天 单位：分")
    private Integer multipleBirthsCost;

    @ApiModelProperty(value = "多胞胎原价元 一胎一天 单位：分")
    private Integer multipleBirthsPrice;

    @ApiModelProperty(value = "门店头图")
    private String storesFigure;

    @ApiModelProperty(value = "开店时间")
    private Integer openTime;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    private Integer viewSort;

    @ApiModelProperty(value = "战区 0=未知 1=一战区 2=二战区 3=三战区")
    private Integer warzone;

    @ApiModelProperty(value = "BU 0:母婴 1:广禾 2:予家")
    private Integer bu;

    @ApiModelProperty(value = "税率, 格式：0.09 表示9个点的税，用于计算成本及毛利")
    private BigDecimal taxRate;

    @ApiModelProperty("组织架构id")
    private Long hrDepartmentId;

}

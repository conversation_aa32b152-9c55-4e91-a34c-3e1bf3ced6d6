package com.stbella.order.domain.client.impl;

import com.stbella.order.server.order.month.enums.StoreTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-07-31  21:11
 * @Description: 消息场景容器
 */
@Component
@Slf4j
public class MessageSceneContainer {

    /**
     * 门店类型与消息场景id映射 创建订单
     */
    Map<Integer, Long> createOrderSceneStoreTypeMap = new HashMap<>();

    /**
     * 门店类型与消息场景id映射 支付
     */
    Map<Integer, Long> paymentSceneStoreTypeMap = new HashMap<>();


    @PostConstruct
    public void init() {
        createOrderSceneStoreTypeMap.put(StoreTypeEnum.SAINT_BELLA.getCode(), 100001L);
        createOrderSceneStoreTypeMap.put(StoreTypeEnum.BABY_BELLA.getCode(), 100011L);
        createOrderSceneStoreTypeMap.put(StoreTypeEnum.ISLA.getCode(), 100164L);

        paymentSceneStoreTypeMap.put(StoreTypeEnum.SAINT_BELLA.getCode(), 100002L);
        paymentSceneStoreTypeMap.put(StoreTypeEnum.BABY_BELLA.getCode(), 100012L);
        paymentSceneStoreTypeMap.put(StoreTypeEnum.ISLA.getCode(), 100165L);
    }

    /**
     *  下单消息： 根据门店类型获取消息场景id
     * @param storeType
     * @return
     */
    public Long getCreateOrderMessageSceneId(Integer storeType) {
        return createOrderSceneStoreTypeMap.get(storeType);
    }

    public Long getPaymentMessageSceneId(Integer storeType) {
        return paymentSceneStoreTypeMap.get(storeType);
    }

}

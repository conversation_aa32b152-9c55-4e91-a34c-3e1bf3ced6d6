package com.stbella.order.domain.order.production.dto;

import com.stbella.core.excel.Column;
import lombok.Data;

import java.io.Serializable;

@Data
public class TherapistRealPaidExportDTO implements Serializable {

    private static final long serialVersionUID = -8510775892254322220L;

    @Column(value = "产康师id", order = 1)
    private Long id;

    @Column(value = "产康师电话", order = 2)
    private String mobile;

    @Column(value = "产康师姓名", order = 3)
    private String name;

    @Column(value = "产康师等级", order = 4)
    private String gradeTitle;

    @Column(value = "产康师类型", order = 5)
    private String natureTypeTitle;

    @Column(value = "产康师状态", order = 6)
    private String stateTitle;

    @Column(value = "已核销金额", order = 7)
    private String realPaid;
}

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>stbella-order</artifactId>
        <groupId>com.stbella</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>order-domain</artifactId>
    <version>2.0-SNAPSHOT</version>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-order-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-order-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>order-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-core-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-contract-model</artifactId>
        </dependency>


        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>store-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-store-model</artifactId>
        </dependency>


        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-store-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>message-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-care-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-care-model</artifactId>
        </dependency>

        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>store-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-sso-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
            <version>2.7.8</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-pay-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.stbella.platform</groupId>
            <artifactId>rule-link-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>store-api</artifactId>
            <version>${stbella-store-core-api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-customer-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-customer-model</artifactId>
        </dependency>
    </dependencies>

</project>

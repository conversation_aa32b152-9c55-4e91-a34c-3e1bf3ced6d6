package com.stbella.order.common.utils;

import com.google.common.base.Strings;
import com.stbella.order.common.constant.BizConstant;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-06-19  13:56
 * @Description: 处理属性值
 */
public class PropertyValueHelper {

    /**
     * 获取属性值 - bool类型
     * 格式 ["1"]
     * @param value
     * @return
     */
    public static boolean getBooleanPropertyValue(String value) {
        if (Strings.isNullOrEmpty(value)) {
            return false;
        }
        //过滤包装信息，原格式 ["1"];
        value = value.replace("[\"", "").replace("\"]", "");
        if (value.equals(BizConstant.TRUE_TAG)){
            return true;
        }
        return false;
    }

    /**
     * 获取属性值 - int 类型
     * 格式 ["1"]
     * @param value
     * @return
     */
    public static Integer getIntPropertyValue(String value) {
        if (Strings.isNullOrEmpty(value)) {
            return 0;
        }
        //过滤包装信息，原格式 ["1"];
        value = value.replace("[\"", "").replace("\"]", "");
        Integer intValue = Integer.parseInt(value);

        return intValue;
    }
}

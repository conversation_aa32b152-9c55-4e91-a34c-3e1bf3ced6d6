package com.stbella.order.common.enums.order;

import com.stbella.order.common.enums.BaseEnum;

import java.util.*;

public enum PartDiscountEnum implements BaseEnum<Integer, String> {
    //申请状态0=可修改;1=已退款或者部分退款 不可修改
    YES(0, "参与折扣"),
    NO(1, "不参与折扣"),
    ;

    private final Integer code;

    private final String desc;

    /**
     * 获取编码
     *
     * @return 编码
     */
    @Override
    public Integer code() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    @Override
    public String desc() {
        return desc;
    }

    PartDiscountEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private static final Map<Integer, String> allMap = new HashMap<>();

    // 静态代码块，随类的加载而执行
    static {
        for (PartDiscountEnum enums : PartDiscountEnum.values()) {
            allMap.put(enums.code, enums.desc);
        }
    }

}

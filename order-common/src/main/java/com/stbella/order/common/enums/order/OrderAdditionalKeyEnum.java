package com.stbella.order.common.enums.order;

import com.stbella.order.common.enums.BaseEnum;

import java.util.*;

/**
 * 订单附加信息枚举
 * <AUTHOR>
 */
public enum OrderAdditionalKeyEnum implements BaseEnum<String, String> {

    HOLIDAY_SERVICE_COST("holidayServiceCost", "节日费用"),
    MULTIPLE_BIRTHS("multipleBirths", "多胞胎费用"),
    FETUS_NUM("fetusNum", "胎数"),
    PREDICT_BORN_DATE("predictBornDate", "预产期"),
    WANT_IN_DATE("wantInDate", "预入住日期"),

    NEED_SIGN("need_sign", "是否签署合同"),

    ESTIMATED_ASSIGNMENT("estimatedDateAssignment", "预计到家外派日期"),
    ;

    private final String code;

    private final String desc;

    /**
     * 获取编码
     *
     * @return 编码
     */
    @Override
    public String code() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    @Override
    public String desc() {
        return desc;
    }

    OrderAdditionalKeyEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private static final Map<String, String> allMap = new HashMap<>();

    // 静态代码块，随类的加载而执行
    static {
        for (OrderAdditionalKeyEnum enums : OrderAdditionalKeyEnum.values()) {
            allMap.put(enums.code, enums.desc);
        }
    }

    public static OrderAdditionalKeyEnum from(String code) {
        return Arrays.stream(OrderAdditionalKeyEnum.values())
                .filter(o -> Objects.equals(o.code, code)).findFirst().orElse(null);
    }

}

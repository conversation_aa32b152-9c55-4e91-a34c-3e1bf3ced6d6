package com.stbella.order.common.enums.core;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 *
 * 购物车场景
 * <AUTHOR>
 */
@Slf4j
public enum CartSceneEnum {

    ORDER(0, "普通订单", ""),
    QUOTATION(1, "报价单", ""),
    CUSTOMIZE_AS_YOU_WISH(2, "随心拼订单", ""),
    CUSTOMER_COMPLAINTS(4, "客诉赔偿订单", ""),
    GIFT_ORDER(5, "赠送订单", ""),
    UPGRADE_ORDER(6, "升级订单", ""),

    OTHER(-99, "未知", ""),
    ;

    @Getter
    private final Integer code;

    public Integer code() {
        return code;
    }

    @Getter
    private final String desc;

    /**
     * 前缀
     */
    @Getter
    private final String preTag;

    CartSceneEnum(Integer orderType, String desc, String preTag) {
        this.code = orderType;
        this.desc = desc;
        this.preTag = preTag;
    }


    public static String getValueByCode(Integer orderType) {
        for (CartSceneEnum value : CartSceneEnum.values()) {
            if (value.code().equals(orderType)) {
                return value.desc;
            }
        }
//        log.error("未知购物车场景类型失败，程序继续执行：{}", orderType);
        return OTHER.desc;
    }


    /**
     * 返回enum
     * @param orderType
     * @return
     */
    public static CartSceneEnum getByCode(Integer orderType) {
        for (CartSceneEnum value : CartSceneEnum.values()) {
            if (value.code().equals(orderType)) {
                return value;
            }
        }
        log.error("未知购物车场景类型失败，程序继续执行：{}", orderType);
        return OTHER;
    }

    private static final Map<Integer, String> allMap = new HashMap<>();
    private static final List<Integer> allList = new ArrayList<>();

    // 静态代码块，随类的加载而执行
    static {
        for (CartSceneEnum enums : CartSceneEnum.values()) {
            allMap.put(enums.code, enums.desc);
            allList.add(enums.code);
        }
    }

    /**
     * 获取所有枚举
     * @return
     */
    public static List<Integer> allList() {
        return allList;
    }
}

package com.stbella.order.common.enums.core;

import com.stbella.order.common.enums.BaseEnum;

import java.util.*;

/**
 * 订单过程节点类型
 * <AUTHOR>
 */
public enum OrderProcessTypeEnum implements BaseEnum<String, String> {
    CREATE( "create", "创建"),
    SIGN("sign", "签署"),
    PAY("pay", "支付"),
    DERATE("derate", "减免"),
    REFUND("refund", "退款"),
    CANCEL("cancel", "取消"),
    ;

    private final String code;

    private final String desc;

    /**
     * 获取编码
     *
     * @return 编码
     */
    @Override
    public String code() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    @Override
    public String desc() {
        return desc;
    }

    OrderProcessTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private static final Map<String, String> allMap = new HashMap<>();

    // 静态代码块，随类的加载而执行
    static {
        for (OrderProcessTypeEnum enums : OrderProcessTypeEnum.values()) {
            allMap.put(enums.code, enums.desc);
        }
    }

    public static OrderProcessTypeEnum from(String code) {
        Optional<OrderProcessTypeEnum> first = Arrays.stream(OrderProcessTypeEnum.values()).filter(o -> Objects.equals(o.code, code)).findFirst();
        return first.orElse(null);
    }

}

package com.stbella.order.common.enums;

/**
 * 错误码约定
 * <p>
 * 100201001  错误码规范
 * <p>
 * 1002 ---- 代表系统
 * 01 ----- 代表域 01 表示门店，02表示商品, 03 订单 04 购物车
 * 001 ---- 代表域内错误信息
 * <p>
 * 001-009 表示系统级别的错误，比如数据库连接不了。
 * 010-099 表示应用级别的错误，比如订单提交失败。
 * 以上两个类错误要钉钉告警
 * <p>
 * 100以上表示业务异常，正常提示就可以。
 */

/**
 * @Author: ji<PERSON><PERSON>an
 * @CreateTime: 2022-08-22  10:33
 * @Description: 返回码
 */
public enum ErrorCodeEnum implements BaseEnum<Integer, String> {

    //平台错误 平台（00）
    BASE_ERROR(*********, "发生错误,请联系技术人员"),
    RPC_ERROR_CODE(*********, "RPC异常"),
    DEPENDENT_ERROR(*********, "依赖系统错误"),
    IO_ERROR(*********, "IO异常"),
    SYSTEM_ERROR(*********, "系统错误"),
    DB_ERROR(*********, "数据库操作异常"),
    REDIS_ERROR(*********, "redis数据异常"),


    // 核心错误
    MISS_PAY_ACCOUNT(*********, "支付账号查询失败，请检查配置"),



    //业务异常，直接提示即可
    BIZ_ERROR(*********, "业务逻辑异常"),
    ILLEGAL_ENUM_VALUE(*********, "枚举值未匹配异常"),
    ILLEGAL_PARAMETERS(*********, "参数不合法"),

    ILLEGAL_BOOK_FREEZE_CANCEL(*********, "该单项服务已核销，无法创建预约"),
    ILLEGAL_BOOK_FREEZE_APPOINTMENT(*********, "该单项服务已预约，请勿重复预约"),
    ILLEGAL_BOOK_FREEZE_UN_ENOUGH_NUM(*********, "很抱歉，该项目已无剩余可用次数"),
    ILLEGAL_CANCEL_ALREADY(*********, "预约取消失败:当前预约单已取消"),
    ILLEGAL_CANCEL_ALREADY_WRITE_OFF(*********, "预约取消失败:当前预约单已取消"),

    PAY_RULE_NOT_CONFIG(*********, "未查到支付规则，请检查配置"),

    CART_NOT_EXIST(*********, "购物车不存在"),

    APPROVE_PROCESSING(*********, "您发起的订单折扣审批当前仍在审批中，审批通过方可签署合同/收款"),

    APPROVE_REJECT(100203202, "您发起的订单折扣审批未通过/发起失败，可点击重新发起后再次尝试"),


    CONTRACT_NOT_SIGN(100203203, "请先签署订单主合同"),

    ENTRUST_CONTRACT_NOT_SIGN(100203207, "请先签署授权委托书"),

    ORDER_CLOSE(100203204, "订单已经关闭，不能继续操作"),

    ORDER_PAID(100203205, "订单已经全部支付，无需继续支付"),

    CONTRACT_SUBJECT_NOT_AUTH(100203206, "请先完成合同主体认证"),


    // 减免业务异常
    DECREASE_AMOUNT_ERROR(*********, "减免金额不可大于剩余应收金额！"),
    DECREASE_APPROVAL_ERROR(100204102, "您发起的订单减免审批当前仍在审批中，请等待审批完成后再次尝试！"),
    DECREASE_IN_REFUND_ERROR(100204103, "当前订单存在退款审批中流程，不可申请减免。请等待审批完成后再次尝试！"),
    DECREASE_INCOME_APPROVING_ERROR(100204104, "当前订单存在收款审阅中流程，不可申请减免。请等待审批完成后再次尝试！"),

    PROMOTION_CHANGE(100204105, "当前活动发生变更，请重新选购商品"),

    GIFT_ASSET_NULL(100204106, "暂无可用礼赠金！"),

    GIFT_ASSET_NOT_ENOUGH(100204107, "礼赠金额不可超过可用礼赠金！"),

    GIFT_ASSET_NOT_ENOUGH_TO_PAY(100204108, "礼赠金不足以支付订单！"),

    USER_NOT_EXIST(100204109, "用户不存在"),

    GOODS_OFFLINE(100204110, "商品已下架，请重新选择商品"),

    ALREADY_HAS_APPROVAL(100204111, "当前订单存在客诉审批中流程，不可提交客诉工单。请等待审批完成后再次尝试"),

    ALREADY_HAS_SUCCESS(100204112, "当前订单存在客诉审批中流程，不可提交客诉工单。请等待审批完成后再次尝试"),

    REFUND_NUM_OR_AMOUNT(100204113, "退款金额或者数量异常"),

    ;


    private Integer code;

    private String message;

    @Override
    public Integer code() {
        return code;
    }

    @Override
    public String desc() {
        return message;
    }

    ErrorCodeEnum(int code, String desc) {
        this.code = code;
        this.message = desc;
    }
}

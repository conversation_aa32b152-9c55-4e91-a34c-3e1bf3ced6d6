/**
 * DeliveryConstant.java
 * Copyright 2019 HelloBike , all rights reserved.
 * HelloBike PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.stbella.order.common.constant;

/**
 * <AUTHOR>
 * 常数配置
 */
public class RedisConstant {

    /**
     * 产康资产锁
     */
    public final static String APPOINTMENT_ASSERT_LOCK_KEY = "appointment:assert:";

    /**
     * 产康设备
     */
    public final static String APPOINTMENT_INSTR_LOCK_KEY = "appointment:instr:";

    /**
     * 产康师
     */
    public final static String APPOINTMENT_USR_LOCK_KEY = "appointment:usr:";

    /**
     * 预约单id
     */
    public final static String APPOINTMENT_LOCK_KEY = "appointment:identity:";

    /**
     * 订单消息通知（钉钉报单）
     */
    public final static String MONTH_ORDER_NOTICE_LOCK = "month:order:notice:lock:";

    /**
     * 创建合同锁 {0}合同模板类型 {1}订单id
     */
    public final static String MONTH_ORDER_CONTRACT_TEMPLATE_TYPE_LOCK = "month:order:contract:template:type:{0}:lock:{1}";

    /**
     * 创建押金订单锁
     */
    public final static String DEPOSIT_ORDER_CREATE_LOCK = "deposit_order_create_lock:type:{0}:uid:{1}:storeid:{2}";

    /**
     * 线下支付确认key
     */
    public final static String OFFLINE_PAY_CONFIRM_KEY = "incomeid:{0}";
}

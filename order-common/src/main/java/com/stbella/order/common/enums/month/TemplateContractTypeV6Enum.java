package com.stbella.order.common.enums.month;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

public enum TemplateContractTypeV6Enum {
    /**
     * 月子合同      '母婴护理服务合同书',
     */
    SUPPLIMENT(8, "补充协议", "对《母婴护理服务合同书》的额外补充"),
    SETTLEMENT_HEAD(12, "总部与客户和解及保密协议","升级至公共媒体级别的重大客诉且涉及现金退款或赔偿时签署"),
    SETTLEMENT_STORE(13, "门店与客户和解及保密协议","发生客诉且涉及现金退款或赔偿时签署"),
    GIFT_GIVING(14, "礼赠协议","限客诉时出现的任何礼赠赔付"),
    MEDIA(17, "门店与个人新媒体置换合同","当签单用户为个人新媒体置换资源需签署"),
    ENTRUST(22, "授权委托书","适用委托人代签合同的情况，为必须签署"),
    DISCOUNT(23, "订单折扣保密协议","当订单套餐折扣低于门店套餐折扣标准时，需客户签订该保密协议"),
    OTHER(100, "其他合同","可上传纸质合同"),
    ;

    private Integer code;

    private String desc;

    private String title;

    /**
     * 获取编码
     *
     * @return 编码
     */

    public Integer code() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    public String desc() {
        return desc;
    }

    /**
     * @return {@link String}
     */
    public String title() {
        return title;
    }

    TemplateContractTypeV6Enum(int code, String title, String desc) {
        this.code = code;
        this.title = title;
        this.desc = desc;
    }

    public static TemplateContractTypeV6Enum from(Integer code) {
        Optional<TemplateContractTypeV6Enum> first = Arrays.stream(TemplateContractTypeV6Enum.values()).filter(o -> Objects.equals(o.code, code)).findFirst();
        return first.orElse(null);
    }

}

/**
 * DeliveryConstant.java
 * Copyright 2019 HelloBike , all rights reserved.
 * HelloBike PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.stbella.order.common.constant;


import com.google.common.collect.Sets;

import java.math.BigDecimal;
import java.util.Set;

/**
 * <AUTHOR>
 * 常数配置
 */
public class BizConstant {

    /**
     * 真（true）的标记
     */
    public static final String TRUE_TAG = "1";

    /**
     * 订单按照人民币汇率计算积分和成长值的时间节点
     */
    public static final Long TIME_NODES = 1727366400L;

    /**
     * 删除标记
     */
    public interface DeleteFlag {
        int NOT_DELETE = 0;
        int DELETED = 1;
    }

    /**
     * 次卡
     */
    public static final int SECONDARY_CARD = 0;

    /**
     * 通卡
     */
    public static final int PASS_CARD = 1;

    /**
     * 单项服务
     */
    public static final int INDIVIDUAL_SERVICE = 2;

    public static final String EXPORT_FILE_NAME = "预约单列表";

    public static final String COMPANY_NAME = "杭州贝康健康科技集团有限公司";
    public static final String COMPANY_ADDRESS = "浙江省杭州市萧山区宁围街道建设二路666号信息港六期6幢";
    public static final String BANK_CARD = "***************";
    public static final String BANK = "招商银行钱塘支行";
    public static final String BANK_NAME = "杭州贝康健康科技集团有限公司";
    public static final String DEPOSIT_DAY = "14";
    //预入住日期
    public static final String WAIT_IN_STR1 = "服务周期：甲方入住乙方会所时间为        %s        11:00 以后，";
    //备孕中使用2
    public static final String WAIT_IN_STR2 = "甲方在签订本合同时，尚在备孕阶段，因此合同有效期自甲方实际入住之日开始计算";
    //预入住日期+套餐+续住天数
    public static final String SERVICE_END_DATE1 = "退房时间为        %s        10:30 以前。";
    //备孕中使用2
    public static final String SERVICE_END_DATE2 = "入住满        %s        天截止。退房规则按本合同约定执行。";

    //特殊事项备注
    public static final String REMARK = "特殊事项备注";
    //额外礼赠
    public static final String GIFT = "额外礼赠";

    public static String WAR_AREA = "warArea";

    /**
     * 所有订单标识- 用于当日订单统计
     */
    public static final Integer ALL_ORDER_TAG = 999;

    /**
     * 所有订单标识- 用于当日订报单金额统计
     */
    public static final Integer ALL_ORDER_AMOUNT_TAG = -999;

    /**
     * 三方产康核销来源
     */
    public static final String REQ_THIRD_THERAPIST_SOURCE = "third_therapist";

    /**
     * RuleLink Scene  config
     */
    public interface RuleLinkScene {
        /**
         * 门店财务相关配置
         */
        String Store_Finance_Config = "multiple_holidays";

        /**
         * 节日费用计算：入住日期向后计算
         */
        String HOLIDAYS_CONFIG = "holidays_config";

        /**
         * 客户合同签署主体
         */
        String SIGN_MAIN = "client_contract_sign_main";

        /**
         * 合同非必签门店
         */
        String NO_NEED_SIGN = "no_need_sign";
    }

    /**
     * 扩展信息的key
     */
    public interface ExtraKey {
        /**
         * 门店id
         */
        String storeId = "storeId";

        String scene = "scene";

        /**
         * 订单id
         */
        String orderId = "orderId";

        /**
         * 加价购的key
         */
        String extraPriceKey = "extraPrice";

        /**
         * 审批流id key
         */
        String approveIdKey = "approveIdKey";

        /**
         * 客户id
         */
        String clientUid = "clientUid";

        /**
         * 购物车id
         */
        String cartId = "cartId";

        /**
         * 活动id列表
         */
        String promotionInfos = "promotionInfos";

        /**
         * 赠送订单状态
         */
        String giftOrderStatus = "giftOrderStatus";

        /**
         * 赠送订单编码
         */
        String giftOrderSn = "giftOrderSn";

        /**
         * 赠送订单Id
         */
        String giftOrderId = "giftOrderId";

        /**
         * 审批流id key
         */
        String approveResult = "approveResult";
    }

    /**
     * 扩展信息的key
     */
    public interface OrderAppKey {
        /**
         * 备孕时，预产期年分定义。
         */
        Integer PreparationForPregnancyYearTag = 2099;

        /**
         * 计入业绩的最小金额
         */
        Integer PerformanceMinPaidAmount = 100;

        /**
         * 通用订单sn前缀
         */
        String OMNI_SN_PRE_KEY = "OMNI";

        /**
         * 特殊错误码
         */
        String SPECIAL_ERROR_CODE = "X0001";

        /**
         * [客房服务]资产
         */
        Set<Integer> ASSET_OF_ROOM = Sets.newHashSet(14);

        /**
         * [馆内护理服务]类的资产
         */
        Set<Integer> ASSET_LIST_ROOM_SERVICE = Sets.newHashSet(0, 1, 28, 31);


        /**
         * 一个月的秒数
         */
        Long monthSeconds = 30 * 24 * 60 * 60L;

        /**
         * 礼赠有效期 秒数(3个月)
         */
        Integer giftValidityMonth = 3;

        /**
         * 备孕时，预产期时间
         */
        Long PreparationForPregnancyTimestamp = 4070880000L;

        /**
         * 计入业绩的最小比例
         */
        double PerformanceMinPaidRate = 0.2;
    }

    public final static BigDecimal TAX_POINT = new BigDecimal("1.06");
}



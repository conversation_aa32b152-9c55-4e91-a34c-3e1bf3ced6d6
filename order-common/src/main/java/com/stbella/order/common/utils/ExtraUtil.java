package com.stbella.order.common.utils;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description 扩展字段 extra 操作类
 * <AUTHOR>
 * @Date 2020-06-23 14:26
 * @Version 1.0
 */
@Slf4j
public class ExtraUtil {

    /**
     * 扩展信息生成string json
     * @param extension
     * @return
     */
    public static String jsonStringFormat(Map<String, Object> extension){

        if (extension == null){
            extension = new HashMap<>();
        }
        ObjectMapper mapper = new ObjectMapper();

        try {
            String jsonString = mapper.writeValueAsString(extension);
            return jsonString;
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            log.error("jsonStringFormat error{}", extension.toString());
        }
        ;

        return "{}";
    }

    /**
     * 扩展信息,追加新的内容
     * @param appendExtension
     * @return
     */
    public static String jsonStringFormat(String jsonExtension, Map<String, Object> appendExtension){

        Map<String, Object> extension = jsonFormat(jsonExtension);
        for(Map.Entry<String, Object> entry : appendExtension.entrySet()){
            String mapKey = entry.getKey();
            extension.put(mapKey, entry.getValue());
        }

        return jsonStringFormat(extension);
    }


    /**
     * 扩展信息生成 Map 对象
     * @param jsonExtension
     * @return
     */
    public static Map<String, Object> jsonFormat(String jsonExtension){

        Map<String, Object> extension = new HashMap<>();
        if (Strings.isNullOrEmpty(jsonExtension)){
            return extension;
        }

        if (!JSONUtil.isJson(jsonExtension)) {
            log.error("扩展信息非json{}", jsonExtension);
            return extension;
        }
        extension = JSONUtil.toBean(jsonExtension,HashMap.class);

        return extension;
    }

    /**
     * 扩展信息生成 Map 对象
     * @param jsonExtension
     * @return
     */
    public static Object getByKey(String jsonExtension, String key){

        Map<String, Object> extension = jsonFormat(jsonExtension);
        Object value = extension.get(key);

        return value;
    }
}

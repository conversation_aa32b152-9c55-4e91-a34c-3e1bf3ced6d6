package com.stbella.order.common.enums.order;

import com.stbella.order.common.enums.BaseEnum;

import java.util.HashMap;
import java.util.Map;

public enum ProduceAmountDeductionEnum implements BaseEnum<Integer, String> {

    NO(0, "不可抵扣"),
    YES(1, "可抵扣"),
    ;

    private final Integer code;

    private final String desc;

    /**
     * 获取编码
     *
     * @return 编码
     */
    @Override
    public Integer code() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    @Override
    public String desc() {
        return desc;
    }

    ProduceAmountDeductionEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private static final Map<Integer, String> allMap = new HashMap<>();

    // 静态代码块，随类的加载而执行
    static {
        for (ProduceAmountDeductionEnum enums : ProduceAmountDeductionEnum.values()) {
            allMap.put(enums.code, enums.desc);
        }
    }

}

package com.stbella.order.common.enums.month;

import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.exception.ApplicationException;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;


/**
 * <AUTHOR>
 */
public enum PayStatusV2Enum {

    // 支付状态：0待支付，1未全付，2已付清，3已取消 4-超额支付
    WAIT_PAY(0, "未支付"),
    NO_PAY_OFF(1, "部分支付"),
    PAY_OFF(2, "全部支付"),
    CANCEL(3, "已取消"),
    EXCESS_PAY(4, "超额支付"),
    CLOSED(99, "交易关闭");
    @Getter
    private final Integer code;

    @Getter
    private final String value;

    PayStatusV2Enum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static PayStatusV2Enum getEnumByCode(Integer code) {
        for (PayStatusV2Enum enums : PayStatusV2Enum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new ApplicationException(ErrorCodeEnum.BASE_ERROR.code(), "没有找到对应的状态:" + code);
    }


    public static String getName(Integer code) {
        if (!Objects.isNull(code)) {
            final Optional<PayStatusV2Enum> first = Arrays.stream(PayStatusV2Enum.values()).filter(i -> Objects.equals(i.getCode(), code)).findFirst();
            return first.map(PayStatusV2Enum::getValue).orElse(null);
        }
        return null;

    }
}

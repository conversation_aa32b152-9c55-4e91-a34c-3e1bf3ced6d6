package com.stbella.order.common.enums.production;

import com.stbella.order.common.enums.BaseEnum;

import java.util.*;

/**
 * 产康服务有效期单位
 */
public enum ValidityTypeForCustomerEnum implements BaseEnum<Integer, String> {
    YEAR(0, "%d年内"),
    MONTH(1, "%d个月"),
    DAY(2, "%d日");

    private final Integer code;

    private final String desc;

    /**
     * 获取编码
     *
     * @return 编码
     */
    @Override
    public Integer code() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    @Override
    public String desc() {
        return desc;
    }

    ValidityTypeForCustomerEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Integer transToCalendarType(Integer code) {
        Optional<ValidityTypeForCustomerEnum> first = Arrays.stream(ValidityTypeForCustomerEnum.values()).filter(o -> Objects.equals(o.code, code)).findFirst();
        if (!first.isPresent()) {
            throw new IllegalArgumentException("ValidityType error:" + code);
        }
        ValidityTypeForCustomerEnum validityTypeEnum = first.get();
        switch (validityTypeEnum) {
            case DAY:
                return Calendar.DAY_OF_YEAR;
            case MONTH:
                return Calendar.MONTH;
            case YEAR:
                return Calendar.YEAR;
        }
        throw new IllegalArgumentException("ValidityType error:" + code);
    }

    private static final Map<Integer, String> allMap = new HashMap<>();

    // 静态代码块，随类的加载而执行
    static {
        for (ValidityTypeForCustomerEnum enums : ValidityTypeForCustomerEnum.values()) {
            allMap.put(enums.code, enums.desc);
        }
    }

    /**
     * 根据code 返回标签
     *
     * @param code
     * @return
     */
    public static String fromCode(Integer code) {
        return allMap.get(code);
    }
}

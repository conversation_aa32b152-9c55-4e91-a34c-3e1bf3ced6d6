package com.stbella.order.common.exception;

import com.stbella.core.exception.BusinessException;
import com.stbella.order.common.enums.ErrorCodeEnum;

/**
 * @Description 业务异常
 * <AUTHOR>
 * @Date 2019-09-05 21:45
 * @Version 1.0
 */
public class ApplicationException extends BusinessException {

    public ApplicationException(int errorCode, String errorMsg) {
        super(errorCode + "", errorMsg);
    }

    public ApplicationException(int errorCode, String errorMsg, String errorLog) {
        super(errorCode, errorMsg, errorLog);
    }

    public ApplicationException(ErrorCodeEnum errorCodeEnum) {
        super(errorCodeEnum.code() + "", errorCodeEnum.desc());
    }
}

package com.stbella.order.common.enums.production;

import com.stbella.order.common.enums.BaseEnum;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.exception.ApplicationException;

import java.util.*;

/**
 * 核销状态
 */
public enum VerificationStatusEnum implements BaseEnum<Integer, String> {

    NOT_WRITE_OFF(0, "未核销"),
    DONE_WRITE_OFF(1, "已核销"),
    BOOKED(2, "已预约"),
    CANCEL(3, "已取消");

    private final Integer code;

    private final String desc;

    /**
     * 获取编码
     *
     * @return 编码
     */
    @Override
    public Integer code() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    @Override
    public String desc() {
        return desc;
    }

    VerificationStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private static final Map<Integer, String> allMap = new HashMap<>();

    // 静态代码块，随类的加载而执行
    static {
        for (VerificationStatusEnum enums : VerificationStatusEnum.values()) {
            allMap.put(enums.code, enums.desc);
        }
    }

    public static VerificationStatusEnum of(Integer code) {
        Optional<VerificationStatusEnum> first = Arrays.stream(VerificationStatusEnum.values()).filter(o -> Objects.equals(o.code, code)).findFirst();
        if (first.isPresent()) {
            return first.get();
        }
        throw new ApplicationException(ErrorCodeEnum.ILLEGAL_ENUM_VALUE.code(), "核销状态枚举未匹配");
    }

    /**
     * 根据code 返回标签
     *
     * @param code
     * @return
     */
    public static String fromCode(Integer code) {
        return allMap.get(code);
    }


    /**
     * 根据终端类型和code返回
     *
     * @param terminalType 终端类型
     * @param code         code
     * @return String
     */
    public static String getDescByTerminalType(Integer terminalType, Integer code) {

        Optional<OrderProductionVerificationStateEnum> verificationStateEnumFirst = Arrays.stream(OrderProductionVerificationStateEnum.values()).filter(o -> Objects.equals(o.code(), code)).findFirst();
        if (!verificationStateEnumFirst.isPresent()) {
            throw new ApplicationException(ErrorCodeEnum.ILLEGAL_ENUM_VALUE.code(), "预约单状态枚举有误");
        }
        Optional<TerminalTypeEnum> first = Arrays.stream(TerminalTypeEnum.values()).filter(o -> Objects.equals(o.code(), terminalType)).findFirst();
        if (!first.isPresent()) {
            throw new ApplicationException(ErrorCodeEnum.ILLEGAL_ENUM_VALUE.code(), "终端类型枚举有误");
        }

        OrderProductionVerificationStateEnum verificationStateEnum = verificationStateEnumFirst.get();
        TerminalTypeEnum terminalTypeEnum = first.get();
        String desc = "";
        switch (verificationStateEnum) {
            case WRITTEN_OFF:
                switch (terminalTypeEnum) {
                    case TERMINAL_TYPE_CUSTOMER:
                        desc = "已完成";
                        break;
                    default:
                        desc = allMap.get(code);
                }
                break;
            case RESERVED:
                switch (terminalTypeEnum) {
                    case TERMINAL_TYPE_PI:
                        desc = "待核销";
                        break;
                    case TERMINAL_TYPE_BACK:
                        desc = "待核销";
                        break;
                    default:
                        desc = allMap.get(code);
                }
                break;
            default:
                desc = allMap.get(code);
                break;
        }
        return desc;
    }
}

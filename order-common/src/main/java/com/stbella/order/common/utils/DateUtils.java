package com.stbella.order.common.utils;

import cn.hutool.core.date.DateUtil;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2022-08-22  17:58
 * @Description: 时间帮助类
 */
public class DateUtils {

    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String MINUTE_TIME_FORMAT = "yyyy.MM.dd HH:mm";
    public static final String TIME_FORMAT = "HH:mm";
    public static final String CURRENT_TIME_FORMAT = "yyyyMMddHHMMss";
    public static final String YYYY_MM_DD_1 = "yyyy.MM.dd";

    public static final String YYYY_MM_DD_SLASH = "yyyy/MM/dd";

    /**
     * 返回当前时间时间戳（10位）
     *
     * @return
     */
    public static Long getTenBitTimestamp() {
        return System.currentTimeMillis() / 1000;
    }

    /**
     * 根据指定的格式得到指定时间的时间戳
     *
     * @param pattern 指定的格式
     * @return 指定格式的时间戳
     * @prram date 指定的时间
     */
    public static String format(Date date, String pattern) {
        return new SimpleDateFormat(pattern).format(date);
    }

    /**
     * 根据指定的格式得到指定时间的时间戳
     * yyyy-MM-dd HH:mm:ss
     *
     * @return 指定格式的时间戳
     * @prram date 指定的时间
     */
    public static String formatNormal(Date date) {
        return new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS).format(date);
    }

    /**
     * 根据10位时间戳返回标准格式
     * yyyy-MM-dd HH:mm:ss
     *
     * @return 指定格式的时间戳
     * @prram date 指定的时间
     */
    public static String formatNormal(Long timeSpanTenBit) {
        Date date = new Date(timeSpanTenBit * 1000L);
        return new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS).format(date);
    }

    public static Date parse(String dateStr, String format) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.parse(dateStr);
    }

    /**
     * 根据两个时间戳返回标准时间格式
     *
     * @param timeSpanTenBitStart 时间戳开始时间
     * @param timeSpanTenBitEnd   时间戳结束时间
     * @param desc                拼接关键字
     * @return String
     */
    public static String formatNormalByDesc(Long timeSpanTenBitStart, Long timeSpanTenBitEnd, String desc) {
        Date startDate = new Date(timeSpanTenBitStart * 1000L);
        Date endDate = new Date(timeSpanTenBitEnd * 1000L);
        return new SimpleDateFormat(YYYY_MM_DD).format(startDate) + desc + new SimpleDateFormat(YYYY_MM_DD).format(endDate);
    }

    public static String formatNormalByDesc(Long timeSpanTenBitStart, Long timeSpanTenBitEnd, String desc, String format) {
        Date startDate = new Date(timeSpanTenBitStart * 1000L);
        Date endDate = new Date(timeSpanTenBitEnd * 1000L);
        return new SimpleDateFormat(format).format(startDate) + desc + new SimpleDateFormat(format).format(endDate);
    }


    /**
     * 获取日期之间
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link List}<{@link String}>
     * @throws Exception 异常
     */
    public static List<String> getBetweenDate(String startTime, String endTime) throws Exception {
        List<String> list = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD);

        Date startDate = sdf.parse(startTime);
        Date endDate = sdf.parse(endTime);

        final Calendar calendar = Calendar.getInstance();
        while (startDate.getTime() <= endDate.getTime()) {
            list.add(sdf.format(startDate));
            calendar.setTime(startDate);
            calendar.add(Calendar.DATE, 1);
            startDate = calendar.getTime();
        }
        return list;
    }

    public static Date addMinute(Date date, Integer minutes) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        instance.add(Calendar.MINUTE, minutes);
        return instance.getTime();
    }

    public static Date addTime(Date date, Integer addMount, Integer type) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        instance.add(type, addMount);
        return instance.getTime();
    }

    public static String getSpecialDateToString(Date start, Date end) {
        SimpleDateFormat sdf = new SimpleDateFormat(MINUTE_TIME_FORMAT);
        String format = sdf.format(start);
        SimpleDateFormat sdf1 = new SimpleDateFormat(TIME_FORMAT);
        String format1 = sdf1.format(end);
        return format + "-" + format1;
    }

    /**
     * 合并时间段
     * <p>
     * 10:00,10:05
     * 10:05,10:10
     * 10:10,10:15
     * 11:00,11:05
     * 11:05,11:10
     * 11:15,11:20
     *
     * @param timeRange
     * @return 10:00,10:15
     * 11:00,11:10
     * 11:15,11:20
     */
    public static LinkedHashMap<Date, Date> mergingTimeRange(Map<Date, Date> timeRange) {
        LinkedHashMap<Date, Date> result = new LinkedHashMap();
        List<Date> start = new ArrayList<>(timeRange.keySet()).stream().sorted().collect(Collectors.toList());
        List<Date> end = timeRange.keySet().stream().map(t -> timeRange.get(t)).collect(Collectors.toList());

        List<Date> endUsed = new ArrayList<>();
        List<Date> startUsed = new ArrayList<>();

        for (Date c : start) {
            if (startUsed.contains(c)) {
                continue;
            }
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(c);
            boolean exist = true;
            Date endTime = calendar.getTime();
            while (exist) {
                calendar.setTime(endTime);
                calendar.add(Calendar.MINUTE, 5);
                Optional<Date> first = end.stream().filter(e -> e.compareTo(calendar.getTime()) == 0 && !endUsed.contains(calendar.getTime())).findFirst();
                if (!first.isPresent()) {
                    exist = false;
                } else {
                    endUsed.add(first.get());
                    endTime = calendar.getTime();
                    calendar.add(Calendar.MINUTE, -5);
                    startUsed.add(calendar.getTime());
                }
            }
            result.put(c, endTime);
        }
        return result;
    }

    public static List<Date> getDayByStartAndEnd(Date start, Date end) {

        List<Date> dateList = new ArrayList<>();
        //如果传的时间是反的
        if (start.after(end)) {
            Date temp = start;
            start = end;
            end = temp;
        }

        Calendar startDay = getCalendar(start);
        startDay.set(Calendar.HOUR_OF_DAY, 0);
        dateList.add(startDay.getTime());
        Calendar endDay = getCalendar(end);
        endDay.set(Calendar.HOUR_OF_DAY, 0);

        if (startDay.getTime().compareTo(endDay.getTime()) != 0) {
            while (startDay.getTime().compareTo(endDay.getTime()) < 0) {
                startDay.add(Calendar.DAY_OF_MONTH, 1);
                dateList.add(startDay.getTime());
            }
        }
        return dateList;
    }

    public static Date getDayStart(Date date) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        instance.set(Calendar.MILLISECOND, 0);
        return instance.getTime();
    }

    public static Date getDayEnd(Date date) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        instance.set(Calendar.HOUR_OF_DAY, 23);
        instance.set(Calendar.MINUTE, 59);
        instance.set(Calendar.SECOND, 59);
        instance.set(Calendar.MILLISECOND, 999);
        return instance.getTime();
    }

    public static Date getMonthStart(Date date) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        instance.set(Calendar.DAY_OF_MONTH, 1);
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        instance.set(Calendar.MILLISECOND, 0);
        return instance.getTime();
    }

    public static Date getMonthEnd(Date date) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        instance.set(Calendar.DAY_OF_MONTH, instance.getActualMaximum(Calendar.DAY_OF_MONTH));
        instance.set(Calendar.HOUR_OF_DAY, 23);
        instance.set(Calendar.MINUTE, 59);
        instance.set(Calendar.SECOND, 59);
        instance.set(Calendar.MILLISECOND, 999);
        return instance.getTime();
    }

    private static Calendar getCalendar(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(null == date ? new Date() : date);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar;
    }

    /**
     * 返回当月第一天0点0分0秒 10位时间戳
     *
     * @param end
     * @return
     */
    public static Long monthStart(Long end) {

        Date date = new Date((end - 1) * 1000);
        // 获取月初时间戳
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date firstDayOfMonth = calendar.getTime();
        return firstDayOfMonth.getTime() / 1000;
    }

    /**
     * 根据时间戳获取LocalDate（10位）
     *
     * @param timestampTen
     * @return
     */
    public static LocalDate getLocalDateByUnixTimestamp(Long timestampTen) {
        return LocalDateTime.ofEpochSecond(timestampTen, 0, ZoneOffset.ofHours(8)).toLocalDate();
    }

    /**
     * 返回当前时间时间戳（10位）
     *
     * @return
     */
    public static Integer getTenBitTimestamp(Date date) {
        return (int) (date.getTime() / 1000);
    }

    /**
     * 返回当前时间时间戳（10位） LocalDateTime
     *
     * @return
     */
    public static Integer getTenBitTimestamp(LocalDateTime date) {
        return (int) date.toEpochSecond(ZoneOffset.of("+8"));
    }

    public static Date getDateFromLong(Long time) {
        return new Date(time * 1000);
    }


    /**
     * 获取某一天开始时间
     *
     * @param date
     * @param day
     * @return
     */
    public static Date getStartDateOfDay(Date date, Integer day) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, day);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取某一天结束时间
     *
     * @param date
     * @param day
     * @return
     */
    public static Date getEndDateOfDay(Date date, Integer day) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, day);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }


    /**
     * 获取月末时间
     *
     * @param date
     * @return
     */
    public static Date getMonthEndDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     * 秒转成DateStr
     *
     * @param seconds
     * @return
     */
    public static String secondsToTimeStr(Integer seconds) {
        if (seconds == null || seconds <= 0) {
            return StringUtils.EMPTY;
        }
        return DateUtil.formatDateTime(secondsTaoDate(seconds));
    }

    /**
     * 秒转成Date
     * @param seconds
     * @return
     */
    public static Date secondsTaoDate(Integer seconds) {
        return new Date(seconds * 1000L);
    }

    public static Date addMonth(Date date, int n) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, n);
        return cal.getTime();
    }

    public static List<Date> getIntervalMonth(Date startDate, Date endDate) {

        startDate =  DateUtils.getMonthStart(startDate);
        endDate =  DateUtils.getMonthStart(endDate);

        List<Date> result = new ArrayList<Date>();
        Calendar tempStart = Calendar.getInstance();
        tempStart.setTime(startDate);
        tempStart.add(Calendar.MONTH, 1);

        Calendar tempEnd = Calendar.getInstance();
        tempEnd.setTime(endDate);
        result.add(startDate);
        while (tempStart.before(tempEnd)) {
            result.add(tempStart.getTime());
            tempStart.add(Calendar.MONTH, 1);
        }
        result.add(endDate);
        return result;
    }
}

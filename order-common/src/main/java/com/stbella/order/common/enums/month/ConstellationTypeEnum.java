package com.stbella.order.common.enums.month;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import lombok.Getter;

import java.util.Objects;

/**
 * 星座
 */
public enum ConstellationTypeEnum {
    ARIES(0, "白羊座"),
    TAURUS(1, "金牛座"),
    GEMINI(2, "双子座"),
    CANCER(3, "巨蟹座"),
    LEO(4, "狮子座"),
    VIRGO(5, "处女座"),
    LIBRA(6, "天秤座"),
    SCORPIO(7, "天蝎座"),
    SAGITTARIUS(8, "射手座"),
    CAPRICORN(9, "摩羯座"),
    AQUARIUS(10, "水瓶座"),
    PISCES(11, "双鱼座");;

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    ConstellationTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static ConstellationTypeEnum getEnumByCode(Integer code) {
        for (ConstellationTypeEnum enums : ConstellationTypeEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的状态:" + code);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return true;
        }
        for (ConstellationTypeEnum monthTypeEnum : values()) {
            if (Objects.equals(monthTypeEnum.getCode(), code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (ConstellationTypeEnum enums : ConstellationTypeEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的状态:" + code);
    }
}

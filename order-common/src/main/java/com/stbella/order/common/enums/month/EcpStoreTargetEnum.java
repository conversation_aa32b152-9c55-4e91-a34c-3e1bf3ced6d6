package com.stbella.order.common.enums.month;

import com.stbella.order.common.enums.BaseEnum;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-09 13:34
 */
public enum EcpStoreTargetEnum implements BaseEnum<Integer, String> {
    //类型: 1:月子总业绩  2:月子套餐  3:产康业务  4:护士外派  5:到家业务
    YZ_TOTAL(1,"月子总业绩"),
    YZ_TC(2,"月子套餐"),
    CK_YW(3,"产康业务"),
    CKHS_WP_YW(4,"护士外派"),
    DJ_YW(5,"到家业务"),
            ;

    private final Integer code;

    private final String desc;

    /**
     * 获取编码
     *
     * @return 编码
     */
    @Override
    public Integer code() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    @Override
    public String desc() {
        return desc;
    }

    EcpStoreTargetEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private static final Map<Integer, String> allMap = new HashMap<>();

    // 静态代码块，随类的加载而执行
    static {
        for (EcpStoreTargetEnum enums : EcpStoreTargetEnum.values()) {
            allMap.put(enums.code, enums.desc);
        }
    }

    public static EcpStoreTargetEnum from(Integer code) {
        Optional<EcpStoreTargetEnum> first = Arrays.stream(EcpStoreTargetEnum.values()).filter(o -> Objects.equals(o.code, code)).findFirst();
        return first.orElse(null);
    }

    /**
     * 根据code 返回标签
     *
     * @param code
     * @return
     */
    public static String fromCode(Integer code) {
        return allMap.getOrDefault(code,"");
    }
}

package com.stbella.order.common.exception;

import com.stbella.order.common.enums.ErrorCodeEnum;

/**
 * @Description 领域异常
 * <AUTHOR>
 * @Date 2019-09-05 21:45
 * @Version 1.0
 */
public class DomainException extends BaseException {

    public DomainException(int errorCode, String errorMsg) {
        super(errorCode, errorMsg);
    }

    public DomainException(int errorCode, String errorMsg, String errorLog) {
        super(errorCode, errorMsg, errorLog);
    }

    public DomainException(ErrorCodeEnum errorCodeEnum) {
        super(errorCodeEnum.code(), errorCodeEnum.desc());
    }
}

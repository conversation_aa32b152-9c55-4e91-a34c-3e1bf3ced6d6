package com.stbella.order.common.utils;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DecimalFormat;
import java.util.*;

/**
 * @Description: [string , list 工具类]
 * @Author: ji<PERSON><PERSON><PERSON>
 * @CreateDate: 2019-09-17
 * @Version: [v1.0]
 */
public class ParamUtil {
    private static DecimalFormat df = new DecimalFormat("#.00");

    /**
     * 如果字符串为空返回
     *
     * @param str
     * @return
     */
    public static boolean isStringEmpty(String str) {
        return (str == null || "".equals(str));
    }

    public static boolean isListEmpty(List list) {
        return (null == list || list.size() == 0);
    }


    /**
     * 四舍五入保留n位小数
     *
     * @param value value
     * @param scale n位小数
     * @return return
     */
    public static Double formatNum(Double value, int scale) {
        if (null == value) {
            return 0.0;
        }
        BigDecimal bd = new BigDecimal(value);
        return bd.setScale(scale, BigDecimal.ROUND_HALF_UP).doubleValue();
    }


    /**
     * 把list转换成相应的map结构
     *
     * @param list  list
     * @param key   key
     * @param clazz clazz
     * @param <T>   </T>
     * @return return
     */
    public static <T> Map<Long, T> listToMap(List<T> list, String key, Class<T> clazz) {
        Map<Long, T> result = new LinkedHashMap<>();
        if (null == list || list.size() == 0) {
            return result;
        }
        try {
            Field field = getDeclaredField(list.get(0), key);
            field.setAccessible(true);
            for (T item : list) {
                Long id = (Long) field.get(item);
                result.put(id, item);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public static Field getDeclaredField(Object object, String fieldName) {
        Field field = null;
        Class<?> clazz = object.getClass();
        for (; clazz != Object.class; clazz = clazz.getSuperclass()) {
            try {
                field = clazz.getDeclaredField(fieldName);
                return field;
            } catch (Exception e) {
                //这里甚么都不要做！并且这里的异常必须这样写，不能抛出去。
                //如果这里的异常打印或者往外抛，则就不会执行clazz = clazz.getSuperclass(),最后就不会进入到父类中了
            }
        }
        return null;
    }

    /**
     * 提取相关的变量
     *
     * @param list  list
     * @param key   key
     * @param clazz class
     * @param <T>   </T>
     * @return return
     */
    public static <T> List<Long> extractKey(List<T> list, String key, Class<T> clazz) {
        List<Long> result = new ArrayList<>();
        try {
            Field field = clazz.getDeclaredField(key);
            field.setAccessible(true);
            for (T item : list) {
                Long id = (Long) field.get(item);
                if (!result.contains(id)) {
                    result.add(id);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }


    /**
     * 提取相关的变量
     *
     * @param list  list
     * @param key   key
     * @param clazz class
     * @param <T>   </T>
     * @return return
     */
    public static <T> List<String> extractStringKey(List<T> list, String key, Class<T> clazz) {
        List<String> result = new ArrayList<>();
        try {
            Field field = clazz.getDeclaredField(key);
            field.setAccessible(true);
            for (T item : list) {
                String id = (String) field.get(item);
                if (!result.contains(id)) {
                    result.add(id);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }


    /**
     * 从map中提取相关变量
     *
     * @param list list
     * @param key  key
     * @return return
     */
    public static <K, V> List<Long> extractKeyFromMap(List<Map<K, V>> list, String key) {
        List<Long> result = new ArrayList<>();
        try {
            for (Map m : list) {
                if (m.containsKey(key)) {
                    Long item = Long.parseLong("" + m.get(key));
                    if (!result.contains(item)) {
                        result.add(item);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }


    /**
     * 根据条件过滤list,保留只满足条件的list
     *
     * @param list      list
     * @param key       key
     * @param conditons conditions
     * @param clazz     clazz
     * @param <T>       <T>
     * @return return
     */
    public static <T> List<T> filterList(List<T> list, String key, List<Long> conditons, Class<T> clazz) {
        if (null == list || list.size() == 0) {
            return new ArrayList<T>();
        }
        try {
            Field field = clazz.getDeclaredField(key);
            field.setAccessible(true);
            Iterator<T> itr = list.iterator();
            while (itr.hasNext()) {
                T item = itr.next();
                Long id = (Long) field.get(item);
                if (!conditons.contains(id)) {
                    itr.remove();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }


    /**
     * 判断字符串是否是整数
     */
    public static boolean isInteger(String value) {
        try {
            Integer.parseInt(value);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 将固定格式的字符串转换成list
     *
     * @param source    source
     * @param separator separator
     * @return return
     */
    public static List<Long> stringToList(String source, String separator) {
        List<Long> result = new ArrayList<>();
        if (StringUtils.isEmpty(source) || StringUtils.isEmpty(separator)) {
            return result;
        }
        String sourceArr[] = source.split(separator);
        for (int i = 0; i < sourceArr.length; i++) {
            String item = sourceArr[i];
            if (StringUtils.isNumeric(item)) {
                result.add(Long.parseLong(item));
            }
        }
        return result;
    }


    /**
     * 将固定格式的字符串转换成list
     *
     * @param source    source
     * @param separator separator
     * @return return
     */
    public static List<String> stringToStrList(String source, String separator) {
        List<String> result = new ArrayList<>();
        if (StringUtils.isEmpty(source) || StringUtils.isEmpty(separator)) {
            return result;
        }
        String sourceArr[] = source.split(separator);
        for (int i = 0; i < sourceArr.length; i++) {
            String item = sourceArr[i];
            result.add(item);
        }
        return result;
    }


    /**
     * 将大集合按照数量拆分成小集合
     *
     * @param totalList  被拆分集合
     * @param splitCount 拆分总集合
     * @return return
     */
    public static <T> List<List<T>> listSplit(List<T> totalList, int splitCount) {
        List<List<T>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(totalList) || splitCount < 1) {
            return result;
        }
        int size = totalList.size();
        //数据量不足count指定的大小
        if (size <= splitCount) {
            result.add(totalList);
        } else {
            int page = size / splitCount;
            int last = size % splitCount;
            //前面pre个集合，每个大小都是count个元素
            for (int i = 0; i < page; i++) {
                List<T> itemList = new ArrayList<T>();
                for (int j = 0; j < splitCount; j++) {
                    itemList.add(totalList.get(i * splitCount + j));
                }
                result.add(itemList);
            }
            //last的进行处理
            if (last > 0) {
                List<T> itemList = new ArrayList<T>();
                for (int i = 0; i < last; i++) {
                    itemList.add(totalList.get(page * splitCount + i));
                }
                result.add(itemList);
            }
        }
        return result;

    }

    /**
     * md5
     *
     * @param str
     * @return
     */
    public static String getMd5(String str) {
        String pwd = null;
        try {
            // 生成一个MD5加密计算摘要
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 计算md5函数
            md.update(str.getBytes());
            // digest()最后确定返回md5 hash值，返回值为8为字符串。因为md5 hash值是16位的hex值，实际上就是8位的字符
            // BigInteger函数则将8位的字符串转换成16位hex值，用字符串来表示；得到字符串形式的hash值
            pwd = new BigInteger(1, md.digest()).toString(16);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return pwd;
    }


    /**
     * Bean --> Map 1: 利用Introspector和PropertyDescriptor 将Bean --> Map
     *
     * @param obj
     * @return
     */
    public static Map<String, Object> transBean2Map(Object obj) {
        if (obj == null) {
            return null;
        }
        Map<String, Object> map = new HashMap<String, Object>();
        try {
            BeanInfo beanInfo = Introspector.getBeanInfo(obj.getClass());
            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
            for (PropertyDescriptor property : propertyDescriptors) {
                String key = property.getName();
                // 过滤class属性
                if (!key.equals("class")) {
                    // 得到property对应的getter方法
                    Method getter = property.getReadMethod();
                    Object value = getter.invoke(obj);
                    map.put(key, value);
                }
            }
        } catch (Exception e) {
            System.out.println("transBean2Map Error " + e);
        }
        return map;
    }

    /**
     * 移除map中的value空值
     *
     * @param map
     * @return
     */
    public static void removeNullValue(Map map) {
        Set set = map.keySet();
        for (Iterator iterator = set.iterator(); iterator.hasNext(); ) {
            Object obj = (Object) iterator.next();
            Object value = (Object) map.get(obj);
            remove(value, iterator);
        }
    }

    /**
     * 内部调用
     *
     * @param obj
     * @param iterator
     */
    private static void remove(Object obj, Iterator iterator) {
        if (obj instanceof String) {
            String str = (String) obj;
            if (isEmpty(str)) {  //过滤掉为null和""的值 主函数输出结果map：{2=BB, 1=AA, 5=CC, 8=  }
                //if("".equals(str.trim())){  //过滤掉为null、""和" "的值 主函数输出结果map：{2=BB, 1=AA, 5=CC}
                iterator.remove();
            }
        } else if (obj instanceof Collection) {
            Collection col = (Collection) obj;
            if (col == null || col.isEmpty()) {
                iterator.remove();
            }
        } else if (obj instanceof Map) {
            Map temp = (Map) obj;
            if (temp == null || temp.isEmpty()) {
                iterator.remove();
            }
        } else if (obj instanceof Object[]) {
            Object[] array = (Object[]) obj;
            if (array == null || array.length <= 0) {
                iterator.remove();
            }
        } else {
            if (obj == null) {
                iterator.remove();
            }
        }
    }

    public static boolean isEmpty(Object obj) {
        return obj == null || obj.toString().length() == 0;
    }

    public static String objectToString(Object o, boolean isDefalutEmpty) {
        if (null == o && isDefalutEmpty) {
            return "";
        } else if (null == o) {
            return null;
        }
        return o.toString();
    }

    public static String calCircleRatio(Object crrentValue, Object lastValue) {
        if (null == lastValue) {
            return "--";
        }
        if (null == crrentValue) {
            crrentValue = BigDecimal.ZERO;
        }
        BigDecimal crrentDecimalValue = BigDecimal.ZERO;
        BigDecimal lastDecimalValue = BigDecimal.ZERO;
        if (crrentValue instanceof BigDecimal) {
            crrentDecimalValue = (BigDecimal) crrentValue;
        } else {
            crrentDecimalValue = new BigDecimal(crrentValue.toString().trim());
        }
        if (lastValue instanceof BigDecimal) {
            lastDecimalValue = (BigDecimal) lastValue;
        } else {
            lastDecimalValue = new BigDecimal(lastValue.toString().trim());
        }
        if (BigDecimal.ZERO.compareTo(lastDecimalValue) == 0) {
            return "--";
        }
        BigDecimal subValue = crrentDecimalValue.subtract(lastDecimalValue);
        BigDecimal ratio = subValue.divide(lastDecimalValue, 2, BigDecimal.ROUND_UP);
        return ratio.toString();
    }

    public static String objectToString(Object o) {
        if (null == o) {
            return null;
        }
        return o.toString();
    }

    public static Long objectToLong(Object o) {
        if (null == o) {
            return null;
        }
        return Long.parseLong(o.toString());
    }

    public static Integer objectToInteger(Object o) {
        if (null == o) {
            return null;
        }
        Integer result = null;
        try {
            result = Integer.parseInt(o.toString());
        } catch (NumberFormatException e) {
            // 考虑类似 "3.00" 这种情况
            result = ((Number) Float.parseFloat(String.valueOf(o))).intValue();
        }

        return result;
    }

    public static Double objectToDouble(Object o) {
        if (null == o) {
            return null;
        }
        return Double.parseDouble(o.toString());
    }

    public static Double objectToDouble(Object o, boolean isDefalutZero) {
        if (null == o && isDefalutZero) {
            return 0d;
        }
        return objectToDouble(o);
    }

    public static Boolean objectToBoolean(Object o) {
        if (null == o) {
            return null;
        }
        return Boolean.parseBoolean(o.toString());
    }

    public static Float objectToFloat(Object o) {
        if (null == o) {
            return null;
        }
        return Float.parseFloat(o.toString());
    }

    public static Byte objectToByte(Object o) {
        if (null == o) {
            return null;
        }
        return Byte.parseByte(String.valueOf(o));
    }

    public static String objectToEmptyString(Object o) {
        if (null == o) {
            return "";
        }
        return o.toString();
    }

    public static String roundUpToTowDigit(BigDecimal value) {
        return df.format(value);
    }

    /**
     * string转换成Long
     *
     * @param str
     * @return
     */
    public static Long strToLong(String str) {
        if (StringUtils.isEmpty(str)) {
            return -1L;
        }
        try {
            Double doubleStr = Double.parseDouble(str);
            Long longStr = Math.round(doubleStr);
            return longStr;
        } catch (NumberFormatException ex) {

        }
        return 0L;
    }

    /**
     * 对double类型的数值保留指定位数的小数。<br>
     * 该方法舍入模式：向“最接近的”数字舍入，如果与两个相邻数字的距离相等，则为向上舍入的舍入模式。<br>
     * <b>注意：</b>如果精度要求比较精确请使用 keepPrecision(String number, int precision)方法
     *
     * @param number    要保留小数的数字
     * @param precision 小数位数
     * @return double 如果数值较大，则使用科学计数法表示
     */
    public static double keepPrecision(double number, int precision) {
        BigDecimal bg = new BigDecimal(number);
        return bg.setScale(precision, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 判断long字段是否为空
     *
     * @param item
     * @return
     */
    public static boolean isLongEmpty(Long item) {
        return (null == item || item == 0);
    }

    /**
     * 生成组合可以
     *
     * @param items
     * @return
     */
    public static String makeCompKey(Object... items) {
        if (null == items || items.length < 0) {
            return "";
        }
        StringBuffer buffer = new StringBuffer();

        for (int i = 0; i < items.length; i++) {
            buffer.append(items[i]);
            if (i != items.length - 1) {
                buffer.append("_");
            }
        }
        return buffer.toString();
    }

    public static boolean isDoubleEmpty(Double item) {
        return (null == item || item == 0.0);
    }

    /**
     * 提取相关的变量
     *
     * @param list  list
     * @param key   key
     * @param clazz class
     * @param <T>   </T>
     * @return return
     */
    public static <T> List<Integer> extractIntegerKey(List<T> list, String key, Class<T> clazz) {
        List<Integer> result = new ArrayList<>();
        try {
            Field field = clazz.getDeclaredField(key);
            field.setAccessible(true);
            for (T item : list) {
                Integer id = (Integer) field.get(item);
                if (!result.contains(id)) {
                    result.add(id);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 只要有一个list is not empty, 就返回true
     *
     * @param collections
     * @return
     */
    public static boolean checkCollectionsAtLeastOneIsNotEmpty(Collection<?>... collections) {
        if (collections != null && collections.length > 0) {
            for (Collection collection : collections) {
                if (CollectionUtils.isEmpty(collection)) {
                    continue;
                } else {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 提取相关的变量
     *
     * @param list list
     * @param key  key
     * @param <T>  </T>
     * @return return
     */
    public static <T> List<Long> changeListToLongList(List<T> list, String key) {
        if (null == list || list.size() == 0) {
            return new ArrayList<Long>();
        }
        List<Long> result = new ArrayList<>();
        try {
            Field field = getDeclaredField(list.get(0), key);
            field.setAccessible(true);
            for (T item : list) {
                Long id = (Long) field.get(item);
                if (!result.contains(id)) {
                    result.add(id);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public static String joinStr(Long... args) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < args.length; i++) {
            if (i == (args.length - 1)) {
                sb.append(args[i]);
            } else {
                sb.append(args[i]).append("_");
            }
        }

        return sb.toString();
    }

    public static boolean isIntegerEmpty(Integer item) {
        return (null == item || item == 0);
    }
}

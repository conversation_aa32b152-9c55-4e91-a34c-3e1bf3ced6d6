package com.stbella.order.common.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.formula.functions.T;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Excel导出工具类（基于EasyExcel）
 *
 * <AUTHOR>
 */
@Slf4j
public class EasyExcelUtil {


    public ByteArrayOutputStream easyOut(List<T> list, Class<T> clazz) throws Exception {
        //上传文件的命名规则，暂时没写
        String extension = ExcelTypeEnum.XLSX.getValue();

        //建立一个字节数组输出流，将excel文件的内容存入其中
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        /**
         *   这里指定用哪个class去写，然后写到第一个sheet，名字为模板，然后文件流会自动关闭
         *   这里可以定义表格的各种样式
         *   out为字节流
         *   clazz为实体类的反射
         */
        EasyExcel.write(out, clazz).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("模板").doWrite(list);

        return out;
    }


    /**
     * 导出Excel通用方法
     *
     * @param response  HttpServletResponse
     * @param data      导出数据
     * @param fileName  文件名
     * @param sheetName sheet名
     * @param headClass 表头类
     * @param <T>       数据类型
     */
    public static <T> ByteArrayOutputStream exportExcel(
            List<T> data,
            String fileName,
            String sheetName,
            Class<?> headClass) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            // 设置响应头
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");

            // 获取需要合并的列
            List<Integer> mergeColumnIndexes = getMergeColumns(headClass);

            // 创建写入对象
            EasyExcel.write(out, headClass)
                    .sheet(sheetName)
                    // 自定义写入处理器（包括样式和合并策略）
                    .registerWriteHandler(createStyleStrategy())
                    .registerWriteHandler(createMergeStrategy(data, mergeColumnIndexes))
                    .doWrite(data);

        } catch (Exception e) {
            log.error("Excel导出发生异常", e);
        }
        return out;
    }

    /**
     * 创建单元格样式策略
     *
     * @return HorizontalCellStyleStrategy
     */
    private static HorizontalCellStyleStrategy createStyleStrategy() {
        // 头部样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 内容样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

//    private static <T> RowWriteHandler createMergeStrategy(List<T> data, List<Integer> mergeColumnIndexes) {
//        return new RowWriteHandler() {
//            @Override
//            public void afterRowDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
//                                        Row row, Integer relativeRowIndex, Boolean isHead) {
//                // 仅处理数据行，跳过表头
//                if (isHead || relativeRowIndex == 0) return;
//
//                Sheet sheet = writeSheetHolder.getSheet();
//                Integer firstMatchColum = mergeColumnIndexes.get(0);
//                // 并行处理需要合并的列
//                mergeColumnIndexes.parallelStream().forEach(columnIndex -> {
//                    try {
//                        mergeColumnCells(sheet, row, columnIndex);
//                    } catch (Exception e) {
//                        log.error("列 {} 合并失败: {}", columnIndex, e.getMessage(), e);
//                    }
//                });
//            }
//
//            /**
//             * 合并指定列的单元格
//             */
//            private void mergeColumnCells(Sheet sheet, Row currentRow, int columnIndex) {
//                Cell currentCell = currentRow.getCell(columnIndex);
//                // 快速空值判断
//                if (currentCell == null || currentCell.getCellType() == CellType.BLANK) return;
//
//                int currentRowNum = currentRow.getRowNum();
//                Row previousRow = sheet.getRow(currentRowNum - 1);
//                Cell previousCell = previousRow.getCell(columnIndex);
//
//                String currentValue = getCellValueAsString(currentCell);
//                String previousValue = getCellValueAsString(previousCell);
//
//                // 值不相同，不需要合并
//                if (!Objects.equals(currentValue, previousValue)) return;
//
//                // 查找连续相同值的最后一行
//                int lastRow = findLastContinuousRow(sheet, columnIndex, currentRowNum, currentValue);
//
//                // 执行单元格合并
//                if (lastRow > currentRowNum - 1) {
//                    mergeCellRegion(sheet, columnIndex, currentRowNum - 1, lastRow);
//                }
//            }
//
//            /**
//             * 查找连续相同值的最后一行
//             */
//            private int findLastContinuousRow(Sheet sheet, int columnIndex, int startRowNum, String targetValue) {
//                int lastRow = startRowNum;
//                int totalRows = sheet.getLastRowNum();
//
//                while (lastRow + 1 <= totalRows) {
//                    Row nextRow = sheet.getRow(lastRow + 1);
//                    if (nextRow == null) break;
//
//                    Cell nextCell = nextRow.getCell(columnIndex);
//                    if (nextCell == null || !Objects.equals(getCellValueAsString(nextCell), targetValue)) {
//                        break;
//                    }
//                    lastRow++;
//                }
//
//                return lastRow;
//            }
//
//            /**
//             * 合并单元格区域
//             */
//            private void mergeCellRegion(Sheet sheet, int columnIndex, int firstRow, int lastRow) {
//                long startTime = System.currentTimeMillis();
//
//                // 移除可能存在的旧合并区域
//                List<CellRangeAddress> mergedRegions = sheet.getMergedRegions();
//                if (CollectionUtils.isNotEmpty(mergedRegions)) {
//                    for (int i = mergedRegions.size() - 1; i >= 0; i--) {
//                        CellRangeAddress region = mergedRegions.get(i);
//                        if (region.getFirstColumn() == columnIndex && region.getLastRow() == firstRow) {
//                            sheet.removeMergedRegion(i);
//                            break;
//                        }
//                    }
//                }
//
//                // 添加新的合并区域
//                CellRangeAddress cellRangeAddress = new CellRangeAddress(firstRow, lastRow, columnIndex, columnIndex);
//                sheet.addMergedRegionUnsafe(cellRangeAddress);
//
//                long endTime = System.currentTimeMillis();
//                log.info("合并单元格耗时：{} ms", endTime - startTime);
//            }
//        };
//    }

    /**
     * 创建单元格合并策略
     *
     * @param data               数据列表
     * @param mergeColumnIndexes 需要合并的列索引
     * @param <T>                数据类型
     * @return 自定义行写入处理器
     */
    private static <T> RowWriteHandler createMergeStrategy(List<T> data, List<Integer> mergeColumnIndexes) {
        return new RowWriteHandler() {
            @Override
            public void afterRowDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                        Row row, Integer relativeRowIndex, Boolean isHead) {
                // 只处理数据行（非表头）
                if (isHead) return;


                Integer firstMergeColumnIndex = mergeColumnIndexes.get(0);

                Cell cell = row.getCell(firstMergeColumnIndex);


                // 遍历需要合并的列
                for (Integer columnIndex : mergeColumnIndexes) {
                    // 如果是第一行，跳过
                    if (relativeRowIndex == 0) continue;

                    // 获取当前行和前一行的单元格值
                    Cell currentCell = row.getCell(columnIndex);
                    int beforeRowNum = row.getRowNum() - 1;
                    Row previousRow = writeSheetHolder.getSheet().getRow(beforeRowNum);
                    Cell previousCell = previousRow.getCell(columnIndex);

                    // 如果当前单元格为空，跳过
                    if (currentCell == null || currentCell.getCellType() == CellType.BLANK) continue;

                    // 比较当前行和前一行的单元格值
                    String currentValue = getCellValueAsString(currentCell);
                    String previousValue = getCellValueAsString(previousCell);

                    // 如果值相同，执行合并
                    if (currentValue.equals(previousValue)) {
                        // 查找连续相同值的最后一行
                        int lastRow = row.getRowNum();
                        Sheet sheet = writeSheetHolder.getSheet();
                        while (lastRow + 1 < sheet.getLastRowNum()) {
                            Row nextRow = sheet.getRow(lastRow + 1);
                            Cell nextCell = nextRow.getCell(columnIndex);

                            if (nextCell == null || !getCellValueAsString(nextCell).equals(currentValue)) {
                                break;
                            }
                            lastRow++;
                        }

                        // 如果有连续相同值，合并单元格
                        if (lastRow > beforeRowNum) {
                            try {
//                                CellRangeAddress cellRangeAddress = new CellRangeAddress(, lastRow, columnIndex, columnIndex);
//                                sheet.addMergedRegionUnsafe(cellRangeAddress);
                            } catch (IllegalStateException e) {
                                log.error("Failed to merge region: {}", e.getMessage());
                            }
                        }
                    }
                }
            }
        };
    }

    /**
     * 获取单元格值的字符串表示
     *
     * @param cell 单元格
     * @return 单元格值的字符串
     */
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) return "";

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case BLANK:
                return "";
            default:
                return "";
        }
    }

    /**
     * 获取需要合并的列索引
     *
     * @param headClass 表头类
     * @return 需要合并的列索引列表
     */
    private static List<Integer> getMergeColumns(Class<?> headClass) {
        List<Integer> mergeColumns = new ArrayList<>();
        Field[] fields = headClass.getDeclaredFields();

        for (int i = 0; i < fields.length; i++) {
            ExcelColumn excelColumn = fields[i].getAnnotation(ExcelColumn.class);
            if (excelColumn != null && excelColumn.isMerge()) {
                mergeColumns.add(i);
            }
        }

        return mergeColumns;
    }

    /**
     * 本地测试导出方法（不依赖HttpServletResponse）
     *
     * @param data      导出数据
     * @param fileName  文件名
     * @param sheetName sheet名
     * @param headClass 表头类
     * @param <T>       数据类型
     * @return 是否导出成功
     */
    public static <T> boolean exportDataLocally(List<T> data,
                                                String fileName,
                                                String sheetName,
                                                Class<?> headClass) {
        try {
            List<Integer> mergeColumnIndexes = getMergeColumns(headClass);

            EasyExcel.write(fileName + ".xlsx", headClass)
                    .sheet(sheetName)
                    .registerWriteHandler(createStyleStrategy())
                    .registerWriteHandler(createMergeStrategy(data, mergeColumnIndexes))
                    .doWrite(data);

            return true;
        } catch (Exception e) {
            log.error("本地Excel导出发生异常", e);
            return false;
        }
    }
}
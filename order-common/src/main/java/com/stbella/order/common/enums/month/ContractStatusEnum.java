package com.stbella.order.common.enums.month;

import com.stbella.order.common.enums.BaseEnum;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

public enum ContractStatusEnum implements BaseEnum<Integer, String> {

    // '合同状态 0 =未签署 1=签署中 2=已归档',
    NOT_SIGNED(0, "未签署"),
    WAITING(1, "签署中"),
    SIGNED(2, "已归档"),
    ;

    private final Integer code;

    private final String desc;

    /**
     * 获取编码
     *
     * @return 编码
     */
    @Override
    public Integer code() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    @Override
    public String desc() {
        return desc;
    }

    ContractStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private static final Map<Integer, String> allMap = new HashMap<>();

    // 静态代码块，随类的加载而执行
    static {
        for (ContractStatusEnum enums : ContractStatusEnum.values()) {
            allMap.put(enums.code, enums.desc);
        }
    }

    public static ContractStatusEnum from(Integer code) {
        Optional<ContractStatusEnum> first = Arrays.stream(ContractStatusEnum.values()).filter(o -> Objects.equals(o.code, code)).findFirst();
        return first.orElse(null);
    }

    /**
     * 根据code 返回标签
     *
     * @param code
     * @return
     */
    public static String fromCode(Integer code) {
        return allMap.get(code);
    }

}

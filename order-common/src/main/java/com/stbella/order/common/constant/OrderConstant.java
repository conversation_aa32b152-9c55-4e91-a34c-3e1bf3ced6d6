/**
 * DeliveryConstant.java
 * Copyright 2019 HelloBike , all rights reserved.
 * HelloBike PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.stbella.order.common.constant;


/**
 * 订单常量配置
 */
public class OrderConstant {

    public static final String PREFIX_ORDER_CACHE = "cache:order:";

    //订单缓存-套餐信息前缀
    public static final String PREFIX_ORDER_CACHE_GOODS = PREFIX_ORDER_CACHE + "goods:";

    //订单缓存-套餐额外礼赠前缀
    public static final String PREFIX_ORDER_CACHE_GIFT_EXTEND = PREFIX_ORDER_CACHE + "giftExtend:";

    //订单缓存-套餐加收项前缀
    public static final String PREFIX_ORDER_CACHE_ADDTIONAL_REVENUE = PREFIX_ORDER_CACHE + "addtionalRevenue:";

    //订单缓存-定点杆其他信息
    public static final String PREFIX_ORDER_CACHE_OTHER_INFO = PREFIX_ORDER_CACHE + "otherInfo:";


}

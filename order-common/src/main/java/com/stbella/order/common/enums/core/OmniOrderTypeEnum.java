package com.stbella.order.common.enums.core;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Sets;

import java.util.*;

/**
 *
 * 订单类型： 订单类型
 * <AUTHOR>
 * 0 月子客户的“普通月子套餐”订单
 * 1 月子客户的“小月子”订单
 * 10 到家客户的“到家服务”订单
 * 20 到家阿姨的“到家服务订单
 * 21 到家阿姨的阿姨培训订单
 * 30 月子其他订单
 */
@Slf4j
public enum OmniOrderTypeEnum {

    MONTH_ORDER(0, "月子订单", "NHEM"),
    SMALL_MONTH_ORDER(1, "小月子订单", "NHEM"),
    HOME_CUSTOMERS_ORDER(10, "到家客户的“到家服务”订单", "DJ"),
    HOME_AUNT_ORDER(20, "到家阿姨的“到家服务订单", "DJ"),
    HOME_AUNT_TRAINING_ORDER(21, "到家阿姨的阿姨培训订单", "DJ"),
    OTHER_MONTH_ORDER(30, "月子其他订单", "SH"),
    HOME_OTHER_ORDER(31, "到家其他订单", "DJ"),
    MALL_ORDER(40, "商城普通订单", "MALL"),
    NURSE_OUTSIDE_ORDER(50, "护士外派订单", "HEN"),
    PRODUCTION_ORDER(60, "产康订单", "CKFW"),
    SBRA_ORDER(70, "S-BRA订单","S"),

    DEPOSIT_ORDER(81, "押金订单","DE"),

    HOME_CUSTOMER_ORDER(90, "予家雇主订单","YJ"),

    HOME_SITTER_ORDER(91, "予家培训订单","PX"),

    OTHER(-99, "未知", "U"),
    ;

    @Getter
    private final Integer code;

    public Integer code() {
        return code;
    }

    @Getter
    private final String desc;

    /**
     * 前缀
     */
    @Getter
    private final String preTag;

    OmniOrderTypeEnum(Integer orderType, String desc, String preTag) {
        this.code = orderType;
        this.desc = desc;
        this.preTag = preTag;
    }


    public static String getValueByCode(Integer orderType) {
        for (OmniOrderTypeEnum value : OmniOrderTypeEnum.values()) {
            if (value.code().equals(orderType)) {
                return value.desc;
            }
        }
        log.error("未知订单类型失败，程序继续执行：{}", orderType);
        return OTHER.desc;
    }

    /**
     * 0,1,30,50,60,70
     * @return
     */
    public static List<Integer> getOrderTypeList(){
        return Arrays.asList(OmniOrderTypeEnum.MONTH_ORDER.getCode(), OmniOrderTypeEnum.SMALL_MONTH_ORDER.getCode(), OmniOrderTypeEnum.OTHER_MONTH_ORDER.getCode(), OmniOrderTypeEnum.NURSE_OUTSIDE_ORDER.getCode(), OmniOrderTypeEnum.PRODUCTION_ORDER.getCode(), OmniOrderTypeEnum.SBRA_ORDER.getCode());
    }

    public static String getOrderName(Integer orderType){

        if (Arrays.asList(OmniOrderTypeEnum.MONTH_ORDER.getCode(), OmniOrderTypeEnum.SMALL_MONTH_ORDER.getCode(), OmniOrderTypeEnum.OTHER_MONTH_ORDER.getCode(), OmniOrderTypeEnum.NURSE_OUTSIDE_ORDER.getCode()).contains(orderType)){
            return "月子订单";
        }
        return getValueByCode(orderType);
    }

    /**
     * 返回enum
     * @param orderType
     * @return
     */
    public static OmniOrderTypeEnum getByCode(Integer orderType) {
        for (OmniOrderTypeEnum value : OmniOrderTypeEnum.values()) {
            if (value.code().equals(orderType)) {
                return value;
            }
        }
        log.error("未知订单类型失败，程序继续执行：{}", orderType);
        return OTHER;
    }

    private static final Map<Integer, String> allMap = new HashMap<>();
    private static final List<Integer> allList = new ArrayList<>();

    private static final Set<Integer> enableCopyType = Sets.newHashSet(MONTH_ORDER.code,SMALL_MONTH_ORDER.code, PRODUCTION_ORDER.code, HOME_CUSTOMERS_ORDER.code, HOME_SITTER_ORDER.code);

    // 静态代码块，随类的加载而执行
    static {
        for (OmniOrderTypeEnum enums : OmniOrderTypeEnum.values()) {
            allMap.put(enums.code, enums.desc);
            allList.add(enums.code);
        }
    }

    /**
     * 获取所有枚举
     * @return
     */
    public static List<Integer> allList() {
        return allList;
    }

    /**
     * 是否可以复制
     * @param orderType
     * @return
     */
    public static boolean enableCopy(Integer orderType) {
        return enableCopyType.contains(orderType);
    }
}

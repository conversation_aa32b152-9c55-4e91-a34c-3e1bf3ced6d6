package com.stbella.order.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum GoodsSpecificationEnum {

    CONCENTRATION("concentration", "精选"),
    SPECIAL_CARE("specialCare", "特护"),
    EXCLUSIVE("exclusive", "尊享"),
    CONFINEMENT_CLUB("confinementClub", "月子会所"),
    LE_YU("leYu", "乐育"),
    SHU_YU("shuYu", "书育"),
    LI_YU("liYu", "礼育"),
    YU_YU("yuYu", "御育"),

    ;

    private final String code;

    private final String desc;

    public static String getCodeByDesc(String name) {

        if (StringUtils.isEmpty(name)){
            return null;
        }
        for (GoodsSpecificationEnum serviceModeEnum : GoodsSpecificationEnum.values()) {
            if (name.contains(serviceModeEnum.getDesc())){
                return serviceModeEnum.getCode();
            }
        }
        return null;
    }
}

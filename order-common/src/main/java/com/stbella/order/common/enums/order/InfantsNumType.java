package com.stbella.order.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum InfantsNumType {

    ONE_BABY(1, "单胎"),
    TWO_BABY(2, "双胎"),

    ;

    private final Integer code;

    private final String desc;

    public static Integer getCode(String name) {

        if (StringUtils.isEmpty(name)){
            return null;
        }
        for (InfantsNumType infantsNumType : InfantsNumType.values()) {
            if (name.contains(infantsNumType.getDesc())){
                return infantsNumType.getCode();
            }
        }
        return null;
    }

}

package com.stbella.order.common.enums.production;

import com.stbella.order.common.enums.BaseEnum;

import java.util.*;

/**
 * 产康服务有效期单位
 */
public enum ValidityTypeEnum implements BaseEnum<Integer, String> {
    YEAR(0, "年"),
    MONTH(1, "月"),
    DAY(2, "日");

    private final Integer code;

    private final String desc;

    /**
     * 获取编码
     *
     * @return 编码
     */
    @Override
    public Integer code() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    @Override
    public String desc() {
        return desc;
    }

    ValidityTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Integer transToCalendarType(Integer code) {
        Optional<ValidityTypeEnum> first = Arrays.stream(ValidityTypeEnum.values()).filter(o -> Objects.equals(o.code, code)).findFirst();
        if (!first.isPresent()) {
            throw new IllegalArgumentException("ValidityType error:" + code);
        }
        ValidityTypeEnum validityTypeEnum = first.get();
        switch (validityTypeEnum) {
            case DAY:
                return Calendar.DAY_OF_YEAR;
            case MONTH:
                return Calendar.MONTH;
            case YEAR:
                return Calendar.YEAR;
        }
        throw new IllegalArgumentException("ValidityType error:" + code);
    }

    private static final Map<Integer, String> allMap = new HashMap<>();

    // 静态代码块，随类的加载而执行
    static {
        for (ValidityTypeEnum enums : ValidityTypeEnum.values()) {
            allMap.put(enums.code, enums.desc);
        }
    }

    /**
     * 根据code 返回标签
     *
     * @param code
     * @return
     */
    public static String fromCode(Integer code) {
        return allMap.get(code);
    }
}

package com.stbella.order.common.constant;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <p>
 * php接口常量
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20 09:48
 */
@Component
public class PhpApiConstant {

    public static final Integer PHP_SUCCESS_CODE = 10000;

    public static final String CODE = "code";

    public static final String DATA = "data";
    /**
     * 客户微信信息获取
     */
    public static final String CUSTOMER_WECHAT = "/open-api/getCustomerWechatByPhone";

    /**
     * 小助手订单同步ecp
     */
    public static final String ECP_ORDER_SYNC = "/open-api/client/order-sync";


    /**
     * 订单同步SCRM
     */
    public static final String SYNC_ORDER_SCRM = "/open-api/client/scrm-get-order-detail";

    /**
     * 客户同步SCRM
     */
    public static final String SYNC_CUSTOMER_SCRM = "/open-api/client/scrm-get-client-detail";

    /**
     * 创建订单发送消息接口路径
     */
    public static final String SEND_TEMPLATE_ORDER_INFO = "/open-api/client/sendTemplateOrderInfo";

    /**
     * 创建订单发送消息固定参数
     */
    public static final Integer MONTH_ORDER_CREATE_PARAM = 1;


    public static final String  ORDER_INCOME_AUDIT = "/open-api/order/income/audit";


    /**
     * php api url
     */
    public static String phpApiUrl;

    @Value("${php-api.host}")
    public void setPhpApiUrl(String host) {
        phpApiUrl = host;
    }

}

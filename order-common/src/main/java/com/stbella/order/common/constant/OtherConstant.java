
package com.stbella.order.common.constant;


public class OtherConstant {

    public static String RESULT = "result";

    public static String PARENT_REFUND_ORDER_SN = "parentRefundOrderSn";

    public static String PARENT_REFUND_ORDER_ID = "parentRefundOrderId";

    public static String ORDER_GOODS_LIST = "orderGoodsList";

    public static String ORDER_REFUND_GOODS_LIST = "orderRefundGoodsList";

    public static String ORDER_INCOME_PAID = "orderIncomePaidList";

    public static String IS_REFUND = "isRefund";

    public static String REFUND_ID = "refundId";

    /**
     * 流程是否继续
     */
    public static String CONTINUE = "continue";

    public static String ORDER_ID = "orderId";

    /**
     * 本次退款中是否有仅退数量的，如果有将这类的退款归类到一笔退款中
     */
    public static String ONLY_REFUND_NUM = "onlyRefundNum";

    /**
     * 退款审批后是否有发短信
     */
    public static String REFUND_APPROVE_SEND_SMS = "refundApproveSendSms";

    /**
     * 全部退款成功后
     */
    public static String REFUND_AFTER_SEND_SMS = "refundAfterSendSms";

    /**
     * 待付款
     */
    public static String WAIT_PAY = "waitPay";

    /**
     * 待发货
     */
    public static String WAIT_SEND_GOODS = "waitSendGoods";

    /**
     * 待收货
     */
    public static String SEND_GOODS = "sendGoods";

    /**
     * 是否为押金
     */
    public static String DEPOSIT = "deposit";

    /**
     * 子退款列表
     */
    public static String ORDER_REFUND_CHILD = "orderRefundChild";

    /**
     * 仅退款
     */
    public static String REFUND_ONLY_NUM = "refundOnlyNum";


}



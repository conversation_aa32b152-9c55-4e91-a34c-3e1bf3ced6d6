package com.stbella.order.common.constant;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <p>
 * php接口常量
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20 09:48
 */
@Component
public class StbellaHelperApiConstant {

    public static final String SUCCESS_CODE = "00000";

    public static final String CODE = "code";

    public static final String DATA = "data";

    /**
     * 新版获取集团总业绩
     */
    public static final String INCOME_TOTAL = "/helper/v2/stat-day-store-per/income/total";

    public static final String INCOME_LIST = "/helper/v2/stat-day-store-per/income/list";


    /**
     * php api url
     */
    public static String apiUrl;

    @Value("${helper.address}")
    public void setApiUrl(String host) {
        apiUrl = host;
    }



}

package com.stbella.order.common.exception;


import com.stbella.order.common.enums.ErrorCodeEnum;

/**
 * 异常基类
 *
 * <AUTHOR>
 */
public class BaseException extends RuntimeException {

    private static final long serialVersionUID = -6003868869041167435L;

    private int errorCode;
    private String errorMsg;
    /**
     * print log detail
     */
    private String errorLog;
    private Throwable t;

    public BaseException(int errorCode) {
        this.errorCode = errorCode;
    }

    public BaseException(int errorCode, String errorMsg) {
        super(errorMsg);
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public BaseException(ErrorCodeEnum errorCodeEnum) {
        super(errorCodeEnum.desc());
        this.errorCode = errorCodeEnum.code();
        this.errorMsg = errorCodeEnum.desc();
    }


    public BaseException(int errorCode, String errorMsg, String errorLog) {
        super(errorMsg);
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
        this.errorLog = errorLog;
    }

    public BaseException(int errorCode, Throwable t) {
        super(t);
        this.errorCode = errorCode;
        this.t = t;
    }

    public BaseException(int errorCode, String errorMsg, Throwable t) {
        super(errorMsg, t);
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
        this.t = t;
    }

    public BaseException(String msg) {
        super(msg);
    }


    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Throwable getT() {
        return t;
    }

    public void setT(Throwable t) {
        this.t = t;
    }

}

package com.stbella.order.common.enums.month;

import com.stbella.order.common.enums.BaseEnum;

import java.util.*;

public enum StayinBirthTypeEnum implements BaseEnum<Integer, String> {
    //生产方式:0=自然生产,1=剖腹产
    NATURAL_PRODUCTION(0, "自然生产"),
    CAESAREAN_SECTION(1, "剖腹产");

    private static final Map<Integer, String> allMap = new HashMap<>();

    static {
        for (StayinBirthTypeEnum enums : StayinBirthTypeEnum.values()) {
            allMap.put(enums.code, enums.desc);
        }
    }

    private final Integer code;
    private final String desc;

    StayinBirthTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static StayinBirthTypeEnum from(Integer code) {
        Optional<StayinBirthTypeEnum> first = Arrays.stream(StayinBirthTypeEnum.values()).filter(o -> Objects.equals(o.code, code)).findFirst();
        return first.orElse(null);
    }

    /**
     * 根据code 返回标签
     *
     * @param code
     * @return
     */
    public static String fromCode(Integer code) {
        return allMap.getOrDefault(code, "");
    }

    @Override
    public Integer code() {
        return code;
    }

    @Override
    public String desc() {
        return desc;
    }
}

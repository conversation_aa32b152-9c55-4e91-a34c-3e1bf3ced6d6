package com.stbella.order.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum OrderGiftStatusEnum {

    APPROVAL(0, "审批中"),
    APPROVAL_SEND(1, "审批通过-已发放"),
    APPROVAL_NOT_SEND(2, "审批通过-未发放"),
    APPROVAL_REJECTION(3, "审批驳回"),
    CANCELED(4, "已取消"),

    APPROVAL_FAIL(5, "审批发起失败"),

    ;

    private final Integer code;

    private final String desc;

    public static String getNameByCode(Integer code) {

        for (OrderGiftStatusEnum e : OrderGiftStatusEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getDesc();
            }
        }
        return StringUtils.EMPTY;
    }
}

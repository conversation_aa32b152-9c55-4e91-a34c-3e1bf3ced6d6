package com.stbella.order.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SignBillTypeEnum {

    CARE_ORDER_NEW(0, "予家新签"),
    CARE_ORDER_RENEW(1, "予家续签"),

    ;

    private final Integer code;

    private final String desc;

    public static String getByCode(Integer code) {

        for (SignBillTypeEnum signBillTypeEnum : SignBillTypeEnum.values()) {
            if (signBillTypeEnum.getCode().equals(code)) {
                return signBillTypeEnum.getDesc();
            }
        }
        return null;
    }
}

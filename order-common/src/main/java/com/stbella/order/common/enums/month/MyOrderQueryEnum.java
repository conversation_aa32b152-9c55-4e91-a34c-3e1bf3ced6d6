package com.stbella.order.common.enums.month;

import lombok.Getter;


/**
 * 小程序 我的订单列表查询类型
 * <AUTHOR>
 */
public enum MyOrderQueryEnum {

    ALL(0, "全部"),
    TO_BE_EFFECTIVE(1, "待生效"),
    TO_STAY_IN(2, "待入住"),
    STAY_IN(3, "入住中"),
    LEAVE(4, "已离馆");

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    MyOrderQueryEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }
}

package com.stbella.order.common.enums.production;

import com.stbella.order.common.enums.BaseEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: 终端类型枚举
 * @author: Seven
 * @time: 2022/10/20 10:50
 */
public enum TerminalTypeEnum implements BaseEnum<Integer, String> {
    // 终端类型 0）后台 1）pi 2）c端
    TERMINAL_TYPE_BACK(0, "后台"),
    TERMINAL_TYPE_PI(1, "PI端"),
    TERMINAL_TYPE_CUSTOMER(2, "客户端"),
    ;
    private final Integer code;

    private final String desc;

    /**
     * 获取编码
     *
     * @return 编码
     */
    @Override
    public Integer code() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    @Override
    public String desc() {
        return desc;
    }

    TerminalTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private static final Map<Integer, String> allMap = new HashMap<>();

    // 静态代码块，随类的加载而执行
    static {
        for (TerminalTypeEnum enums : TerminalTypeEnum.values()) {
            allMap.put(enums.code, enums.desc);
        }
    }

    /**
     * 根据code 返回标签
     *
     * @param code
     * @return
     */
    public static String fromCode(Integer code) {
        return allMap.get(code);
    }
}

package com.stbella.order.common.enums.month;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;


/**
 * <AUTHOR>
 */
public enum RefundRecordPayStatusEnum {

    // 退款状态，1：审批中 2：审批失败  3：审批成功/等待打款/待确认 4：退款已到帐/已确认 5：到账失败/已拒绝
    REFUND_RECORD_0(0, "审批发起失败", "审批发起失败"),
    REFUND_RECORD_1(1, "审批中", "审批中"),
    REFUND_RECORD_2(2, "审批失败", "审批失败"),
    REFUND_RECORD_3(3, "等待财务打款", "审批成功"),
    REFUND_RECORD_4(4, "退款成功", "退款已到账"),
    REFUND_RECORD_5(5, "退款失败", "退款失败"),
    REFUND_RECORD_10(10, "发起失败", "发起失败"),
    REFUND_CANCEL_20(20, "发起人撤回", "发起人撤回")

    ;

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    @Getter
    private final String onlineValue;

    RefundRecordPayStatusEnum(Integer code, String value, String onlineValue) {
        this.code = code;
        this.value = value;
        this.onlineValue = onlineValue;
    }


    public static String getName(Integer code) {
        if (!Objects.isNull(code)) {
            final Optional<RefundRecordPayStatusEnum> first = Arrays.stream(RefundRecordPayStatusEnum.values()).filter(i -> Objects.equals(i.getCode(), code)).findFirst();
            return first.map(RefundRecordPayStatusEnum::getValue).orElse(null);
        }
        return null;

    }

    public static String getonLineName(Integer code) {
        if (!Objects.isNull(code)) {
            final Optional<RefundRecordPayStatusEnum> first = Arrays.stream(RefundRecordPayStatusEnum.values()).filter(i -> Objects.equals(i.getCode(), code)).findFirst();
            return first.map(RefundRecordPayStatusEnum::getOnlineValue).orElse(null);
        }
        return null;

    }


    public static RefundRecordPayStatusEnum getEnumByCode(Integer code) {
        for (RefundRecordPayStatusEnum enums : RefundRecordPayStatusEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        return null;
    }
}

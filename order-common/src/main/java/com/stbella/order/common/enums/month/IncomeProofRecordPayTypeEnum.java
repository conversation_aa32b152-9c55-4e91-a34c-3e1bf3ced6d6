package com.stbella.order.common.enums.month;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;


/**
 * <AUTHOR>
 */
public enum IncomeProofRecordPayTypeEnum {

    // 线下支付方式：0其他，1pos机， 2汇款 3现金
    OTHER(0, "其他"),
    POS(1, "pos机"),
    REMITTANCE(2, "汇款"),
    CASH(3, "现金");
    @Getter
    private final Integer code;

    @Getter
    private final String value;

    IncomeProofRecordPayTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }


    public static String getName(Integer code) {
        if (!Objects.isNull(code)) {
            final Optional<IncomeProofRecordPayTypeEnum> first = Arrays.stream(IncomeProofRecordPayTypeEnum.values()).filter(i -> Objects.equals(i.getCode(), code)).findFirst();
            return first.map(IncomeProofRecordPayTypeEnum::getValue).orElse(null);
        }
        return null;

    }
}

package com.stbella.order.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum ServiceModeEnum {

    ALL_DAY_LN_HOME("allDaylnHome", "全日住家型"),
    DAY_TAKE_CARE("dayTakeCare", "日间照料型"),
    SERVICE_OTHER("serviceOther", "其他"),

    ;

    private final String code;

    private final String desc;

    public static String getCodeByDesc(String name) {

        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (ServiceModeEnum serviceModeEnum : ServiceModeEnum.values()) {
            if (name.contains(serviceModeEnum.getDesc())){
                return serviceModeEnum.getCode();
            }
        }
        return null;
    }

}

package com.stbella.order.common.enums.core;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import io.swagger.annotations.ApiModel;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 *  目前母婴使用的。与予家不一样。
 */
@Getter
@ApiModel(value = "线下付款审核状态V2 母婴")
public enum OfflineAuditStatusV2Enum {

    UNDER_REVIEW(0, "审核中"),
    EXAMINATION_PASSED(1, "审核通过"),
    REVIEW_REJECTED(2, "审核驳回"),

    OTHER(999, "无需审核"),
    ;

    private final Integer code;

    private final String value;

    OfflineAuditStatusV2Enum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static OfflineAuditStatusV2Enum getEnumByCode(Integer code) {
        for (OfflineAuditStatusV2Enum enums : OfflineAuditStatusV2Enum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的支付类型:" + code);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return true;
        }
        for (OfflineAuditStatusV2Enum monthTypeEnum : values()) {
            if (Objects.equals(monthTypeEnum.getCode(), code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (OfflineAuditStatusV2Enum enums : OfflineAuditStatusV2Enum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的支付类型:" + code);
    }

}

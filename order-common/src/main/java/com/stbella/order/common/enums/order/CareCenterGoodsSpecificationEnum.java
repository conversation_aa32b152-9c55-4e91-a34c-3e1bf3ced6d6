package com.stbella.order.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum CareCenterGoodsSpecificationEnum {

    PRIMARY("primary", "初级"),
    MIDDLE("middle", "中级"),
    HIGH("high", "高级"),
    BOUTIQUE("boutique", "精品"),
    CONFINEMENT_CLUB("confinementClub", "月子会所"),

    ;

    private final String code;

    private final String desc;

    public static String getCodeByDesc(String name) {

        if (StringUtils.isEmpty(name)){
            return null;
        }
        for (CareCenterGoodsSpecificationEnum specificationEnum : CareCenterGoodsSpecificationEnum.values()) {
            if (name.contains(specificationEnum.getDesc())){
                return specificationEnum.getCode();
            }
        }
        return null;
    }
}

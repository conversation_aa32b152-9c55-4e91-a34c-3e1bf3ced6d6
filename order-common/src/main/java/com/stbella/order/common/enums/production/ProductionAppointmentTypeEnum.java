package com.stbella.order.common.enums.production;

import com.stbella.order.common.enums.BaseEnum;

import java.util.HashMap;
import java.util.Map;


/**
 * 预约单类型
 *
 * <AUTHOR>
 * @date 2022/10/08
 */
public enum ProductionAppointmentTypeEnum implements BaseEnum<Integer, String> {
    //预约单类型：1=标准;2=补单
    APPOINTMENT_TYPE_ONE(1, "标准"),
    APPOINTMENT_TYPE_TWO(2, "补单"),
    ;
    private final Integer code;

    private final String desc;

    /**
     * 获取编码
     *
     * @return 编码
     */
    @Override
    public Integer code() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    @Override
    public String desc() {
        return desc;
    }

    ProductionAppointmentTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private static final Map<Integer, String> allMap = new HashMap<>();

    // 静态代码块，随类的加载而执行
    static {
        for (ProductionAppointmentTypeEnum enums : ProductionAppointmentTypeEnum.values()) {
            allMap.put(enums.code, enums.desc);
        }
    }

    /**
     * 根据code 返回标签
     *
     * @param code
     * @return
     */
    public static String fromCode(Integer code) {
        return allMap.get(code);
    }
}

package com.stbella.platform.order.api.req;

import com.stbella.core.base.BasicReq;
import com.stbella.core.enums.BusinessEnum;
import com.stbella.order.common.enums.core.CartSceneEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.platform.order.api.res.PromotionInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 创建或更新购物车入参
 */
@Data
public class CreateOrUpdateCartReq extends BasicReq {

    @ApiModelProperty(value = "购物ID：更新购物车时必传")
    private Integer cartId;

    /**
     * 业务线
     * @see BusinessEnum
     */
    @ApiModelProperty(value = "BU 0:母婴 1:广禾 2:予家")
    private Integer bu = BusinessEnum.CARE_CENTER.getCode();

    /**
     * 订单类型 (可能有些场景，外部没有这个值，需要内部赋值)
     * @see OmniOrderTypeEnum
     */
    @ApiModelProperty(value = "订单类型")
    private Integer orderType = OmniOrderTypeEnum.MONTH_ORDER.code();

    /**
     * 购物车场景
     * @see CartSceneEnum
     */
    @ApiModelProperty(value = "场景")
    private Integer scene = CartSceneEnum.ORDER.code();

    @ApiModelProperty(value = "门店ID")
    @NotNull(message = "请输入门店ID")
    private Integer storeId;

    @ApiModelProperty(value = "客户全局ID")
    @NotNull(message = "请输入客户全局ID")
    private Integer basicUid;

    @ApiModelProperty(value = "加购商品集合")
    @NotNull(message = "sku")
    private List<SkuDetailInfo> skuList;

    @ApiModelProperty(value = "订单额外信息")
    private ExtraInfo extraInfo;

    @ApiModelProperty(value = "客户系统ID")
    private Integer clientUid;

    @ApiModelProperty(value = "订单所有人")
    private Integer staffId;

    @ApiModelProperty(value = "销售员电话")
    private String staffPhone;

    @ApiModelProperty(value = "活动id列表")
    private List<PromotionInfo> promotionInfos;

    @ApiModelProperty(value = "原订单ID（用于订单升级）")
    private Integer originalOrderId;

    @ApiModelProperty(value = "原商品总价（用于订单升级）")
    private BigDecimal originalGoodsTotalPrice;

    @ApiModelProperty(value = "原商品ID（用于订单升级）")
    private Integer originalGoodsId;

    @ApiModelProperty(value = "原商品数量（用于订单升级）")
    private Integer originalGoodsNum;

}

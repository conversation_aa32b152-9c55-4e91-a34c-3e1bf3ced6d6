package com.stbella.platform.order.api.req;

import com.stbella.core.base.PageBaseReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("客诉分页查询请求2")
@Data
public class CustomerComplaintsPageReq extends PageBaseReq {

    @ApiModelProperty(value = "查询条件")
    private String keyWord;

    private List<Integer> clientUidList;

    private List<Integer> storeIdList;
}

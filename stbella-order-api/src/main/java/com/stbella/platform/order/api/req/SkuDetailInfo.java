package com.stbella.platform.order.api.req;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.order.common.enums.order.CombineTypeEnum;
import com.stbella.platform.order.api.res.GoodsInfo;
import com.stbella.platform.order.api.res.PromotionInfo;
import com.stbella.platform.order.api.res.SkuAdditionalInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 提交订单商品信息
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SkuDetailInfo implements Serializable {

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "商品spuId")
    private Integer goodsId;

    @ApiModelProperty(value = "原商品ID（用于订单升级）")
    private Integer originalGoodsId;

    @ApiModelProperty(value = "原商品数量（用于订单升级）")
    private Integer originalGoodsNum;

    @ApiModelProperty(value = "商品skuId(组合商品，没有skuId)")
    private Integer skuId;

    @ApiModelProperty(value = "商品名称")
    private String skuName;

    @ApiModelProperty(value = "规格名称")
    private String specification;

    @ApiModelProperty("商品图片")
    private String skuUrl;

    @ApiModelProperty(value = "物品清单url")
    private String inventoryUrl = StringUtils.EMPTY;

    @ApiModelProperty("父级数量")
    private Integer parentNum = 1;

    @ApiModelProperty(value = "数量（份数）")
    private Integer num = 1;

    /**
     * 包含sku数量
     * 组合商品明细中，一个商品可能包含多个sku
     */
    @ApiModelProperty(value = "包含sku数量")
    private Integer skuNum = 1;


    @ApiModelProperty(value = "价格（元）-组合商品中，这个价格是表示一份中，一个或者多个商品的总价")
    private BigDecimal price;

    @ApiModelProperty(value = "加价购（元）")
    private BigDecimal extraPrice;

    @ApiModelProperty("成本价（元）")
    private BigDecimal costPrice;

    @ApiModelProperty("成本定价方式：0-全国统一定价；1-门店自定义价格；2-按销售价比例定价")
    private Integer costPricingMethod;

    @ApiModelProperty("成本比例")
    private Integer costRatio;

    @ApiModelProperty(value = "分类编号")
    private Integer backCategoryId;

    @ApiModelProperty(value = "商品分类：后台名称")
    private String categoryBackTitle;

    @ApiModelProperty(value = "一级前台类目")
    private Integer firstFrontCategoryId;


    @ApiModelProperty(value = "资产类型")
    private Integer assetType;

    @ApiModelProperty(value = "goodsType")
    private Integer goodsType;

    @ApiModelProperty(value = "业务类型")
    private Integer businessType;

    @ApiModelProperty(value = "是否赠品-不计总价，但分摊实付")
    private Boolean gift;

    /**
     * @see CombineTypeEnum
     */
    @ApiModelProperty(value = "商品类型：2表示单项，3表示组合")
    private Integer type;

    /**
     * 组合商品计算模式
     * CombinationTypeEnum
     * 组合商品计算模式
     * <p>
     * FIXED_COLLOCATION(0, "固定搭配") - 使用组合上的价格
     * OPTIONAL_COLLOCATION(1, "可选搭配")  -使用每个商品的组合销售价
     */
    @ApiModelProperty(value = "组合商品计算模式 0固定搭配、1可选搭配")
    private Integer combinationPricingMethod;


    /**
     * 组合商品明细中，每个商品所属于的组
     */
    @ApiModelProperty(value = "组合商品明细中，每个商品所属于的组")
    private String groupKey;

    @ApiModelProperty(value = "sku可用状态,1表示正常，0表示下架")
    private Integer skuSaleState;


    @ApiModelProperty(value = "关联小助手房型")
    private Integer roomType;

    @ApiModelProperty(value = "关联小助手房型名称")
    private String roomName;

    @ApiModelProperty(value = "关联ECP房型")
    private Integer ecpRoomType;

    @ApiModelProperty("附件名称")
    private String goodsAttachmentName;

    @ApiModelProperty("附件url")
    private String goodsAttachment;

    @ApiModelProperty(value = "是否需要签署合同")
    private Boolean needSign;

    @ApiModelProperty("单位")
    private String unitStr;

    @ApiModelProperty(value = "子商品集合")
    private List<SkuDetailInfo> subList;

    @ApiModelProperty(value = "商品附加项集合")
    private List<SkuAdditionalInfo> additionalList;

    @ApiModelProperty("是否计算节假日数量")
    private Boolean calHoliday;

    private Integer parentId;

    @ApiModelProperty("sku属性")
    private String propertyValue;

    /**
     * 产康金抵扣 0）不可抵扣 1)可抵扣
     */
    private Integer produceAmountDeduction;

    /**
     * 产康金抵扣规则 0:全额抵扣 1:满10000减2000
     */
    private Integer productionDiscountRuleType;

    @ApiModelProperty(value = "服务类型 0自营 1三方")
    private Integer serviceType;

    @ApiModelProperty(value = "服务时长")
    private Integer serviceTime;

    @ApiModelProperty(value = "活动信息")
    private PromotionInfo promotionInfo;

    @ApiModelProperty(value = "商品信息")
    private GoodsInfo goodsInfo;

    @ApiModelProperty("父级Id")
    private Integer skuParentId;

    @ApiModelProperty(value = "组合类型：0-固定搭配；1;可选")
    private Integer combinationType;

    @ApiModelProperty(value = "0-单规格；1-多规格")
    private Integer specificationType;


    public Integer getProduceAmountDeduction() {
        return ObjectUtil.isNotEmpty(getGift()) && getGift() ? null : produceAmountDeduction;
    }

    public Integer getProductionDiscountRuleType() {
        return ObjectUtil.isNotEmpty(getGift()) && getGift() ? null : productionDiscountRuleType;
    }


}

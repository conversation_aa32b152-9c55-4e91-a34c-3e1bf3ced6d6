package com.stbella.platform.order.api.req;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@ApiModel("客诉分页查询请求")
@Data
public class CustomerComplaintsCreateReq extends BasicReq {


    @ApiModelProperty(value = "客诉工单ID")
    private Long id;

    @ApiModelProperty(value = "订单ID")
    private Long orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    @ApiModelProperty(value = " 订单类型： 0 月子客户的“普通月子套餐”订单； 1 月子客户的“小月子”订单； 10 到家客户的“到家服务”订单； 20 到家阿姨的“到家服务订单”； 21 到家阿姨的阿姨培训订单； 30 月子其他订单 31 到家其他订单 40 商城普通订单 50护士外派订单 60产康订单 70S-BRA订单 描述： 0-9 归属月子平台的客户订单类型 ； 10-19 归属到家客户的订单类型； 20-29到家阿姨的订单类型; 30-39 其他订单 40-49 商城订单类型 50护士外派订单 60产康订单 70S-BRA订单")
    private Integer orderType;

    @ApiModelProperty(value = "门店ID")
    private Integer storeId;

    @ApiModelProperty(value = "门店名称")
    private String storeIdStr;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "客户ID")
    private Integer clientUid;

    @ApiModelProperty(value = "客户名称")
    private String customerIdStr;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "客户手机号")
    private String customerPhone;

    @ApiModelProperty(value = "时间")
    private Long eventTime;

    @ApiModelProperty(value = "地点")
    private String eventLocation;

    @ApiModelProperty(value = "人物")
    private String involvedPersons;

    @ApiModelProperty(value = "起因")
    private String cause;

    @ApiModelProperty(value = "经过")
    private String process;

    @ApiModelProperty(value = "结果")
    private String result;

    @ApiModelProperty(value = "凭证")
    private List<String> evidence;

    @ApiModelProperty(value = "客诉等级")
    private Integer complaintLevel;

    @ApiModelProperty(value = "客诉等级值")
    private String complaintLevelStr;

    /**
     * @see com.stbella.order.server.order.month.enums.CustomerComplaintsResponsibilityEnum
     */
    @ApiModelProperty(value = "定责类型 100-无责 200-我司全责 300-我司主责三方次责 400-三方主责我司次责 500-三方全责")
    private Integer responsibilityType;

    @ApiModelProperty(value = "定责类型值")
    private String responsibilityTypeStr;

    @ApiModelProperty(value = "主要责任人")
    private Integer primaryResponsiblePerson;

    @ApiModelProperty(value = "主要责任人值")
    private String primaryResponsiblePersonStr;

    @ApiModelProperty(value = "次要责任人")
    private List<Integer> secondaryResponsiblePerson;

    @ApiModelProperty(value = "次要责任人值")
    private List<String> secondaryResponsiblePersonStr;

    @ApiModelProperty(value = "供应商 --汇联易")
    private Long supplierId;

    @ApiModelProperty(value = "供应商值 --汇联易")
    private String supplierIdStr;

    @ApiModelProperty(value = "产康主供应商")
    private Integer mainSupplierId;

    @ApiModelProperty(value = "产康主供应商值")
    private String mainSupplierIdStr;

    @ApiModelProperty(value = "三方产康师")
    private Integer thirdPartyTherapistId;

    @ApiModelProperty(value = "产康师ID")
    private Integer therapistId;

    @ApiModelProperty(value = "产康师名称")
    private String thirdPartyTherapist;

    @ApiModelProperty(value = "三方产康师姓名")
    private String thirdPartyTherapistIdStr;

    @ApiModelProperty(value = "三方联系人姓名")
    private String thirdPartyContactName;

    @ApiModelProperty(value = "三方联系人电话")
    private String thirdPartyContactPhone;

    @ApiModelProperty(value = "是否草稿")
    private Boolean isDraft;

    @ApiModelProperty(value = "是否有额外赔偿意见 0-无，1-有")
    private Integer hasExtraCompensationOpinion;

    @ApiModelProperty(value = "扣除门店业绩")
    private BigDecimal netStorePerformance;

    @ApiModelProperty(value = "扣除销售业绩")
    private BigDecimal netSalesPerformance;

    @ApiModelProperty(value = "退款信息")
    private CustomerComplaintsCreateRefundReq createRefundReq;
}

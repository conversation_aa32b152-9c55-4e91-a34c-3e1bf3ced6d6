package com.stbella.platform.order.api.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class InnerRankRes implements Serializable {

    private static final long serialVersionUID = 5107307879464526365L;

    @ApiModelProperty(value = "门店ID")
    private Integer storeId;

    @ApiModelProperty(value = "门店名")
    private String name;

    @ApiModelProperty(value = "业绩金额, 无格式化")
    private Long totalValue;

    private String jobNumber;
}

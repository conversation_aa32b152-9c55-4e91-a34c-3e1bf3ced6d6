package com.stbella.platform.order.api;

import com.stbella.core.result.Result;
import com.stbella.order.server.order.month.req.OrderMonthIncomeQuery;
import com.stbella.order.server.order.month.res.LastOrderInfoVO;
import com.stbella.order.server.order.month.res.OrderInfoAndGoodsInfoVO;
import com.stbella.order.server.order.month.res.OrderRealAmountVO;
import com.stbella.order.server.order.month.res.OrderReductionVO;
import com.stbella.order.server.order.order.res.OrderMainInfo;
import com.stbella.platform.order.api.req.OrderDetailQuery;
import com.stbella.platform.order.api.res.QueryDepositAmountRes;

import java.util.Date;
import java.util.List;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-03-26  10:49
 * @Description: 订单查询服务
 */
public interface OrderQueryService {

    /**
     * 查询订单
     *
     * @param query
     * @return
     */
    Result<OrderMainInfo> getOne(OrderDetailQuery query);


    /**
     * 获取订单减免列表
     *
     * @param orderId
     * @return
     */
    Result<List<OrderReductionVO>> getSuccessListByOrderId(Integer orderId);

    Result<QueryDepositAmountRes> getDepositAmount(OrderMonthIncomeQuery query);

    /**
     * 根据basicUid获取用户最新的订单
     *
     * @param basicUidList
     * @return
     */
    Result<List<LastOrderInfoVO>> getLastOrderInfoByBasicUid(List<Integer> basicUidList, List<Integer> orderType);

    Result<Integer> queryOrderInfo(Integer orderId);

    Result<OrderInfoAndGoodsInfoVO> queryInfoAndGoodsInfo(String orderSn);

    Result<Date> getCheckIn(String orderSn);;

    /**
     * 根据收退记录，获取订单实付金额（去掉产康金）
     *
     * @param orderId
     * @return
     */
    Result<Integer> getOrderRealAmount(Integer orderId);

    List<OrderRealAmountVO> getOrderRealAmountByOrderList(List<Integer> collect);
}

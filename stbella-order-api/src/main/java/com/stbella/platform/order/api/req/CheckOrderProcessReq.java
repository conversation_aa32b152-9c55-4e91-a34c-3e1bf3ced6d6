package com.stbella.platform.order.api.req;

import com.stbella.core.base.BasicReq;
import com.stbella.core.enums.BusinessEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OrderProcessTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: ji<PERSON><PERSON><PERSON>
 * @CreateTime: 2024-03-26  10:53
 * @Description: 订单过程节点校验
 */
@Data
public class CheckOrderProcessReq extends BasicReq {

    /**
     *  过程（节点）
     * @see OrderProcessTypeEnum
     */
    @ApiModelProperty(value = "过程（节点）")
    private String processType;

    /**
     * 订单id
     */
    @ApiModelProperty(value = "订单id")
    @NotNull(message = "请输入订单id")
    private Integer orderId;

}

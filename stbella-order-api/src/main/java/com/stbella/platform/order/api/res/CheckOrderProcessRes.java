package com.stbella.platform.order.api.res;

import com.stbella.core.base.BasicReq;
import com.stbella.order.common.enums.core.OrderProcessTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-03-26  10:53
 * @Description: 订单过程节点校验
 */
@Data
@Accessors(chain = true)
public class CheckOrderProcessRes {
    @ApiModelProperty(value = "状态码 前端做特殊处理")
    private String code;

    @ApiModelProperty(value = "提示信息")
    private String message;


    @ApiModelProperty(value = "审批Id")
    private String approveInstanceId;

}

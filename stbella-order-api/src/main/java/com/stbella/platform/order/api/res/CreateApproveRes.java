package com.stbella.platform.order.api.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class CreateApproveRes implements Serializable {

    @ApiModelProperty(value = "0-存在1笔即将过期的支付宝支付记录，为避免款项无法正常原路返回，建议修改退款方式为线下打款。;1-线上支付金额小于申请退款金额，无法原路返回，建议修改退款方式为线下打款。；2-您发起的订单退款审批发起失败，可点击重新发起后再次尝试！")
    private Integer type;

    @ApiModelProperty(value = "0-存在1笔即将过期的支付宝支付记录，为避免款项无法正常原路返回，建议修改退款方式为线下打款。;1-线上支付金额小于申请退款金额，无法原路返回，建议修改退款方式为线下打款。；2-您发起的订单退款审批发起失败，可点击重新发起后再次尝试！")
    private String msg;

    @ApiModelProperty(value = "退款Id")
    private Integer refundId;


}


package com.stbella.platform.order.api.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 减免
 */
@Data
public class DecreaseCheckRes implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "是否包含审批")
    private Boolean containApprove;

    /**
     * @see com.stbella.order.server.order.cts.enums.ApprovalTypeEnum
     */
    @ApiModelProperty(value = "审批类型")
    private String approveType;

    @ApiModelProperty(value = "审批id")
    private String approveId;


}

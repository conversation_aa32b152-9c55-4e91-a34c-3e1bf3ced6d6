package com.stbella.platform.order.api.cashier;

import com.stbella.core.result.Result;
import com.stbella.order.server.order.month.request.standard.OrderSettlementQuery;
import com.stbella.order.server.order.month.res.OrderSettlementVO;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-07-05  11:23
 * @Description: 收银台查询接口
 */
public interface CashierQueryService {
    /**
     * 查询订单收款情况
     * @param paymentCollectionPageRequest
     * @return
     */
    Result<OrderSettlementVO> querySettlement(OrderSettlementQuery paymentCollectionPageRequest);

}

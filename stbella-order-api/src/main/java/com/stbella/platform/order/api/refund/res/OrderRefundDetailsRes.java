package com.stbella.platform.order.api.refund.res;

import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data

public class OrderRefundDetailsRes implements Serializable {
    @ApiModelProperty(value = "订单id")
    private Integer orderId;

    @ApiModelProperty(value = "订单状态：0-待收款；1-待收款；2-待收款；3-其他")
    private Integer orderStatus;

    @ApiModelProperty(value = "用户id")
    private Integer clientUid;

    @ApiModelProperty(value = "门店id")
    private Integer storeId;

    @ApiModelProperty(value = "退款类型：1=正常退款;2=⾮正常退款（客诉）")
    private String refundReasonTypeStr;

    @ApiModelProperty(value = "退款类型：1=正常退款;2=⾮正常退款（客诉）")
    private Integer refundReasonType;

    @ApiModelProperty(value = "退款原因：1-宝妈身体原因； 2-宝宝原因； 5-家人因素； 8-用户需求； 4-距离原因； 6-环境设施； 7-馆内服务； 10-门店承接问题； 9-订单变更；14-提前离馆； 3-疫情政策； 13-其他；100-月子餐问题；101-护理问题； 102-产康问题； 103-馆内活动问题； 104-第三方(宝宝艺术照)问题； 105-服务态度问题； 107酒店问题； 106其他")
    private String refundReasonStr;

    @ApiModelProperty(value = "退款原因：1-宝妈身体原因； 2-宝宝原因； 5-家人因素； 8-用户需求； 4-距离原因； 6-环境设施； 7-馆内服务； 10-门店承接问题； 9-订单变更；14-提前离馆； 3-疫情政策； 13-其他；100-月子餐问题；101-护理问题； 102-产康问题； 103-馆内活动问题； 104-第三方(宝宝艺术照)问题； 105-服务态度问题； 107酒店问题； 106其他")
    private Integer refundReason;

    @ApiModelProperty(value = "退款方式：1-原路退回；2-线下汇款；3-退款至余额")
    private Integer refundType;

    @ApiModelProperty(value = "退款方式：1-原路退回；2-线下汇款；3-退款至余额")
    private String refundTypeStr;

    @ApiModelProperty(value = "商品退款方式：1-退货退款；2-仅退款；3-退回重付；99-未知")
    private Integer goodsRefundType;

    @ApiModelProperty(value = "商品退款方式：1-退货退款；2-仅退款；3-退回重付；99-未知")
    private String goodsRefundTypeStr;

    @ApiModelProperty(value = "开户名")
    private String accountName;

    @ApiModelProperty(value = "银行卡号")
    private String bankNo;

    @ApiModelProperty(value = "开户行+支行")
    private String accountBank;

    @ApiModelProperty("退款凭证")
    private List<String> auditRefundProof;

    @ApiModelProperty(value = "退款说明")
    private String remark;

    @ApiModelProperty(value = "退款申请金额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "退款总金额")
    private BigDecimal refundTotalAmount;

    @ApiModelProperty(value = "退款商品列表")
    private List<OrderRefundDetailsRes.GoodsInfo> goodsInfoList;

    @ApiModelProperty(value = "货币码")
    private String currency;

    @ApiModelProperty(value = "状态")
    private Integer refundStatus;

    @ApiModelProperty(value = "状态")
    private String refundStatusStr;

    @ApiModelProperty(value = "是否展示查看合同按钮：0-不展示；1-展示")
    private Integer isShowContractButton;

    @ApiModelProperty(value = "审批id")
    private String approveId;

    @ApiModelProperty(value = "是否是押金订单")
    private Boolean deposit = false;

    @Data
    public static class GoodsInfo {
        @ApiModelProperty(value = "商品类型")
        private Integer goodsType;

        @ApiModelProperty(value = "商品图片")
        private String goodsImage;

        @ApiModelProperty(value = "商品名称")
        private String goodsName;

        @ApiModelProperty(value = "规格名称")
        private String skuName;

        @ApiModelProperty(value = "退款金额")
        private BigDecimal refundAmount;

        @ApiModelProperty(value = "新——退款金额和退款类型")
        private List<CreateRefundReq.GoodsRefundAmountInfo> refundGoodsAmountInfoList;

        @ApiModelProperty(value = "退款数量")
        private Integer refundNum;

        @ApiModelProperty(value = "订单商品表id")
        private Integer orderGoodsId;

        @ApiModelProperty(value = "商品编号")
        private String orderGoodsSn;

        @ApiModelProperty(value = "上级")
        private String parentCombineSn;

        @ApiModelProperty(value = "商品类型：2 表示单个普通商品，3 表示组合商品")
        private Integer type;

        @ApiModelProperty(value = "商品id")
        private Integer goodsId;

        @ApiModelProperty(value = "skuId")
        private Integer skuId;

        @ApiModelProperty(value = "总数量（如果是组合，显示整个组合数据）")
        private Integer goodsNum;
    }
}

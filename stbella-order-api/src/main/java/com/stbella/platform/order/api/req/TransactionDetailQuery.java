package com.stbella.platform.order.api.req;

import com.stbella.core.base.BasicReq;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-03-26  10:53
 * @Description: 交易查询
 */
@Data
public class TransactionDetailQuery extends BasicReq {

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 收款Id
     */
    @NotNull(message = "transactionId不能为空")
    private Integer transactionId;

}

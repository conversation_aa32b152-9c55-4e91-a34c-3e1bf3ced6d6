package com.stbella.platform.order.api;

import com.stbella.core.result.Result;
import com.stbella.platform.order.api.req.CheckSupplementAgreementReq;
import com.stbella.platform.order.api.res.CheckSupplementAgreementRes;

/**
 * 订单补充协议服务
 */
public interface OrderSupplementService {
    
    /**
     * 检查订单用户是否需要签订补充协议，如果需要则创建
     * 
     * @param req 请求参数
     * @return 检查结果
     */
    Result<CheckSupplementAgreementRes> checkAndCreateSupplementAgreement(CheckSupplementAgreementReq req);
}
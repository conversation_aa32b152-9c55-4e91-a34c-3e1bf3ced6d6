package com.stbella.platform.order.api.refund.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class QueryOrderRefundInfoRes implements Serializable {

    @ApiModelProperty(value = "订单状态")
    private Integer payStatus;

    @ApiModelProperty(value = "订单商品表id")
    private List<QueryRefundGoodsRes> queryRefundGoodsResList;

    @ApiModelProperty(value = "订单总金额")
    private BigDecimal payAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "已付总金额")
    private BigDecimal paidAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "冻结金额")
    private BigDecimal freezeAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "已退总金额")
    private BigDecimal alreadyRefundAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "现金支付总金额")
    private BigDecimal cashPaidAmount;

    @ApiModelProperty(value = "订单金额明细")
    private QueryRefundInfoRes queryRefundInfoRes;

    @ApiModelProperty(value = "是否是押金订单")
    private Boolean deposit = false;

    @ApiModelProperty(value = "订单状态：0-待付款；1-待发货；2-待收货")
    private Integer orderStatus;

    @ApiModelProperty(value = "货币码")
    private String currency;
}

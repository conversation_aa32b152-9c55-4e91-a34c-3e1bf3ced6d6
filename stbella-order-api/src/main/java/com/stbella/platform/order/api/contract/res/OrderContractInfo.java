package com.stbella.platform.order.api.contract.res;

import com.stbella.order.common.constant.TableFieldConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class OrderContractInfo implements Serializable {

    private static final long serialVersionUID = 2324914581780573002L;
    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @ApiModelProperty(value = "0-客户;1-委托人")
    private Integer signType;

    @ApiModelProperty(value = "合同主键id")
    private Integer contractId;

    @ApiModelProperty(value = "0=大陆身份证 1=护照 2=香港来往大陆通行证 3=澳门来往大陆通行证 4=台湾来往大陆通行证")
    private Integer certType;

    @ApiModelProperty(value = "名称")
    private String contractName;

    @ApiModelProperty(value = "描述")
    private String contractDesc;

    @ApiModelProperty(value = "合同类型 1=E签宝合同 2=旧合同 3=纸质合同(标记“纸质合同”的戳)")
    private Integer contractType;

    @ApiModelProperty(value = "合同类型   1=>主合同，2=>附件类，3=>附属类：补充协议", required = true)
    private Integer templateType;

    @ApiModelProperty(value = TableFieldConstant.templateContractType)
    private Integer templateContractType;

    @ApiModelProperty(value = "是否签订")
    private Boolean sign;

    @ApiModelProperty(value = "0=创建主合同类接口 1=创建附属类接口 2=其他无需调用接口")
    private Integer apiRoute;

    @ApiModelProperty(value = "是否显示待签订 true-显示")
    private Boolean signStatus = false;

    @ApiModelProperty(value = "是否为纸质 true-是")
    private Boolean paperContract = false;

    @ApiModelProperty("门店id")
    private Integer storeId;

    @ApiModelProperty("门店名称")
    private String storeName;

    @ApiModelProperty("客户名称")
    private String clientName;

    @ApiModelProperty("客户id")
    private Integer clientUid;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty(value = "合同模板类型 中文")
    private String templateContractTypeName;

    @ApiModelProperty(value = "缴纳意向金过去天数")
    private Integer intentionDay;

    @ApiModelProperty(value = "优惠还剩天数")
    private Integer preferentialDay;

    @ApiModelProperty(value = "合同创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "预约协议签订时间")
    private Date appointmentCreatedAt;

    @ApiModelProperty(value = "预约协议签订状态用于排序  0 =未签署 1=签署中 2=已归档")
    private Integer appointmentStatus;

    @ApiModelProperty(value = "新老系统标志;0或null-老;1-新")
    private Integer oldOrNew;

    @ApiModelProperty(value = "预约协议 需要支付意向金金额 单位元")
    private String amountEarnest;
}
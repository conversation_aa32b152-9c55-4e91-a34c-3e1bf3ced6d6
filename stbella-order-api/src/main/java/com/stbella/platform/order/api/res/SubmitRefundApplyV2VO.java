package com.stbella.platform.order.api.res;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.stbella.order.server.order.month.req.SubmitRefundApplyV2Request;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "SubmitRefundApplyV2VO", description = "SubmitRefundApplyV2VO")
@JsonIgnoreProperties(ignoreUnknown = true)
public class SubmitRefundApplyV2VO extends SubmitRefundApplyV2Request implements Serializable {
    private static final long serialVersionUID = 1L;

}

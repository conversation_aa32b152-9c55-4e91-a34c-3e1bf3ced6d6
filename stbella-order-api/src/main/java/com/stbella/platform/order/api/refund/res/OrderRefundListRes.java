package com.stbella.platform.order.api.refund.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class OrderRefundListRes implements Serializable {

    @ApiModelProperty(value = "主退款id")
    private Integer id;

    @ApiModelProperty(value = "主退款sn")
    private String refundOrderSn;

    @ApiModelProperty(value = "状态")
    private Integer refundStatus;

    @ApiModelProperty(value = "状态")
    private String refundStatusStr;

    @ApiModelProperty(value = "提交时间")
    private String submissionTime;

    @ApiModelProperty(value = "提交金额")
    private BigDecimal applyAmount;

    @ApiModelProperty(value = "提交产康金金额")
    private BigDecimal applyCkjAmount;

    @ApiModelProperty(value = "货币码")
    private String currency;

    @ApiModelProperty(value = "子项")
    private List<String> refundOrderItemSns;
}

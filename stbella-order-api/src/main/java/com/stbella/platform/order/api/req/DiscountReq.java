package com.stbella.platform.order.api.req;

import com.stbella.order.common.enums.core.CartSceneEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 查询折扣信息入参
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DiscountReq {

    @ApiModelProperty(value = "业务线：0-母婴，1-广禾， 2-予家")
    private Integer bu = 0;

    /**
     * @see OmniOrderTypeEnum
     */
    @ApiModelProperty(value = "订单类型")
    private Integer orderType;

    @ApiModelProperty(value = "购物车ID")
    private Integer cartId;

    @ApiModelProperty(value = "门店ID")
    private Integer storeId;

    @ApiModelProperty(value = "签单金额")
    private BigDecimal payAmount;

    /**
     * 购物车场景
     * @see CartSceneEnum
     */
    @ApiModelProperty(value = "场景")
    private Integer scene;

}

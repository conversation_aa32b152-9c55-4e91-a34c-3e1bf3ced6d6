package com.stbella.platform.order.api.req;

import com.stbella.core.base.BasicReq;
import com.stbella.order.server.order.month.annotations.EnumValue;
import com.stbella.order.server.order.month.constant.BaseConstant;
import com.stbella.order.server.order.month.enums.PayAmountEnum;
import com.stbella.order.server.order.month.req.PayReqV2;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 线下支付
 * <AUTHOR>
 */
@Data
public class OmniOfflinePayRequest extends PayReqV2 implements Serializable {

    @ApiModelProperty(value = "凭证地址")
    private String payProof;

    @ApiModelProperty(value = "备注")
    private String remark;
}

package com.stbella.platform.order.api.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 购物车数据更新
 */
@Data
public class CartUpdateReq {

    @ApiModelProperty(value = "购物ID")
    private Integer cartId;

    @ApiModelProperty(value = "场景")
    private Integer scene;

    @ApiModelProperty(value = "订单类型")
    private Integer orderType;

    @ApiModelProperty(value = "签单金额")
    private BigDecimal payAmount;
}

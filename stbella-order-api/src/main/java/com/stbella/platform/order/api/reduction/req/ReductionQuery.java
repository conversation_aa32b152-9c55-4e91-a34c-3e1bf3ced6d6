package com.stbella.platform.order.api.reduction.req;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 减免查询
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ReductionQuery extends BasicReq {

    private static final long serialVersionUID = 764094462271510834L;

    @ApiModelProperty(value = "订单ID，0表示直接清空")
    @NotNull(message = "请输入订单ID")
    private Integer orderId;

    private String processId;

    @ApiModelProperty(value = "审核状态列表")
    private List<Integer> authStateList;

}

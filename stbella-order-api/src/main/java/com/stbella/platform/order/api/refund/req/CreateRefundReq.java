package com.stbella.platform.order.api.refund.req;

import com.stbella.core.base.BasicReq;
import com.stbella.order.server.order.month.enums.PayMethodEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


@Data
public class CreateRefundReq extends BasicReq {

    @ApiModelProperty(value = "订单id")
    @NotNull(message = "订单id不能为空")
    private Integer orderId;

    @ApiModelProperty(value = "客诉单id")
    private Long complaintId;

    @ApiModelProperty(value = "退款类型：1=正常退款;2=⾮正常退款（客诉）")
//    @NotNull(message = "退款类型不能为空")
    private Integer refundReasonType;

    @ApiModelProperty(value = "退款原因：1-宝妈身体原因； 2-宝宝原因； 5-家人因素； 8-用户需求； 4-距离原因； 6-环境设施； 7-馆内服务； 10-门店承接问题； 9-订单变更；14-提前离馆； 3-疫情政策； 13-其他；100-月子餐问题；101-护理问题； 102-产康问题； 103-馆内活动问题； 104-第三方(宝宝艺术照)问题； 105-服务态度问题； 107酒店问题； 106其他")
//    @NotNull(message = "退款原因不能为空")
    private Integer refundReason;

    @ApiModelProperty(value = "退款方式：1-原路退回；2-线下汇款；3-退款至意向金")
    private Integer refundType = 1;

    @ApiModelProperty(value = "退款方式：1-原路退回；2-线下汇款；3-退款至意向金")
    private String refundTypeStr;

    @ApiModelProperty(value = "商品退款方式：1-退货退款；2-仅退款；3-退回重付；99-未知")
    @NotNull(message = "商品退款方式不能为空")
    private Integer goodsRefundType;

    @ApiModelProperty(value = "商品退款方式：1-退货退款；2-仅退款；3-退回重付；99-未知")
    private String goodsRefundTypeStr;

    @ApiModelProperty(value = "开户名")
    private String accountName;

    @ApiModelProperty(value = "银行卡号")
    private String bankNo;

    @ApiModelProperty(value = "开户行+支行")
    private String accountBank;

    @ApiModelProperty("退款凭证")
    private List<String> auditRefundProof;

    @ApiModelProperty(value = "退款说明")
    @Length(max = 1000, message = "退款说明最大可输入256字符")
    private String remark;

    @ApiModelProperty(value = "退款总金额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "退款金额和类型")
    private List<GoodsRefundAmountInfo> refundAmountInfoList;

    @ApiModelProperty(value = "退款商品列表")
    private List<GoodsInfo> goodsInfoList;

    @ApiModelProperty(value = "是否为升级订单自动退款（无需审批）")
    private Boolean isUpgradeOrderAutoRefund;

    @Data
    public static class GoodsInfo implements Serializable {
        @ApiModelProperty(value = "订单商品表id")
        private Long orderGoodsId;

        @ApiModelProperty(value = "商品编号")
        private String orderGoodsSn;

        @ApiModelProperty(value = "上级")
        private String parentCombineSn;

        @ApiModelProperty(value = "商品类型：2 表示单个普通商品，3 表示组合商品")
        private Integer type;

        @ApiModelProperty(value = "商品类型")
        private Integer goodsType;

        @ApiModelProperty(value = "商品id")
        private Integer goodsId;

        @ApiModelProperty(value = "skuId")
        private Integer skuId;

        @ApiModelProperty(value = "商品图片")
        private String goodsImage;

        @ApiModelProperty(value = "商品（组合）名称")
        private String goodsName;

        @ApiModelProperty(value = "规格名称（如果是组合，会显示组合下所有的商品的规格数据）")
        private String skuName;

        @ApiModelProperty(value = "规格名称（如果是组合，会显示组合下所有的商品的规格数据）")
        private List<String> specification;

        @ApiModelProperty(value = "退款金额")
        private BigDecimal refundAmount;

        @ApiModelProperty(value = "新——退款金额和退款类型")
        private List<GoodsRefundAmountInfo> refundGoodsAmountInfoList;

        @ApiModelProperty(value = "总数量（如果是组合，显示整个组合数据）")
        private Integer goodsNum;

        @ApiModelProperty(value = "退款数量")
        private Integer refundNum;

        @ApiModelProperty(value = "是否是组合的附加项")
        private Boolean combinationAddition;

        @ApiModelProperty(value = "如果是组合的附加项，组合的Sn")
        private String combinationSn;
    }

    @Data
    public static class GoodsRefundAmountInfo implements Serializable {
        @ApiModelProperty(value = "退款金额")
        private BigDecimal amount = BigDecimal.ZERO;

        private BigDecimal refundAmount;

        @ApiModelProperty(value = "退款类型：0-现金；1-产康金")
        private Integer amountType;

        @ApiModelProperty(value = "退款model")
        private String model;

        public Integer getAmountType() {
            return null == amountType ? new Integer(PayMethodEnum.getEnumByoModel(model).getCode()) : amountType;
        }
    }


}


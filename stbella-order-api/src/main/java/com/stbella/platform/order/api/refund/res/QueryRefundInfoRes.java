package com.stbella.platform.order.api.refund.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class QueryRefundInfoRes implements Serializable {

    @ApiModelProperty(value = "订单ID")
    private Integer orderId;

    @ApiModelProperty(value = "已付明细")
    private AmountInfo paidAmountInfo;

    @ApiModelProperty(value = "已退+冻结明细")
    private AmountInfo refundAmountInfo;

    @ApiModelProperty(value = "可退明细")
    private AmountInfo canRefundAmountInfo;

    @ApiModelProperty(value = "即将到期")
    private AboutExpireInfo aboutExpireInfo;

    @Data
    public static class AmountInfo {

        @ApiModelProperty(value = "支付宝")
        private BigDecimal aliPay = BigDecimal.ZERO;

        @ApiModelProperty(value = "微信")
        private BigDecimal wechatPay = BigDecimal.ZERO;

        @ApiModelProperty(value = "pos机")
        private BigDecimal posPay = BigDecimal.ZERO;

        @ApiModelProperty(value = "线下")
        private BigDecimal offlinePay = BigDecimal.ZERO;

        @ApiModelProperty(value = "余额")
        private BigDecimal balance = BigDecimal.ZERO;

        @ApiModelProperty(value = "产康金")
        private BigDecimal cjkPay = BigDecimal.ZERO;
    }

    @Data
    public static class AboutExpireInfo {

        @ApiModelProperty(value = "即将过期的支付宝")
        private Integer aliPayNum = 0;

        @ApiModelProperty(value = "支付宝")
        private Boolean aliPay = false;

        @ApiModelProperty(value = "即将过期的微信")
        private Integer wechatPayNum = 0;

        @ApiModelProperty(value = "微信")
        private Boolean wechatPay = false;

        @ApiModelProperty(value = "即将过期的pos")
        private Integer posNum = 0;

        @ApiModelProperty(value = "pos")
        private Boolean pos = false;

    }

}

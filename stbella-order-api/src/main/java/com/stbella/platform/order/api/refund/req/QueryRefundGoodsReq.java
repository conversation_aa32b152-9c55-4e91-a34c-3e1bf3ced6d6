package com.stbella.platform.order.api.refund.req;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;


@Data
public class QueryRefundGoodsReq extends BasicReq {

    @ApiModelProperty(value = "订单id")
    @NotNull(message = "订单id不能为空")
    private Integer orderId;

    @ApiModelProperty(value = "商品退款方式：1-退货退款；2-仅退款；3-退回重付；99-未知")
    private Integer goodsRefundType;

}

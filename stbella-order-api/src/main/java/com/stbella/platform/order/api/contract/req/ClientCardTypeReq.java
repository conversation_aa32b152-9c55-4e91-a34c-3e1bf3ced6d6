package com.stbella.platform.order.api.contract.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ClientCardTypeReq implements Serializable {

    private static final long serialVersionUID = 1557187387372151256L;

    @NotNull(message = "客户ID不能为空")
    @ApiModelProperty(value = "客户ID")
    private Integer basicUid;

    @NotNull(message = "订单ID不能为空")
    @ApiModelProperty(value = "订单ID")
    private Integer orderId;

    @NotNull(message = "合同类型不能为空")
    @ApiModelProperty(value = "合同类型")
    private Integer templateContractType;

    @NotNull(message = "签约类型不能为空")
    @ApiModelProperty(value = "0-客户;1-委托人", required = true)
    private Integer signType;
}

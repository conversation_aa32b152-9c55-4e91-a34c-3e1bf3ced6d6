package com.stbella.platform.order.api.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 商品附加项费用信息
 */
@Data
@Accessors(chain = true)
public class SkuAdditionalInfo {

    @ApiModelProperty(value = "属性ID")
    private Long propertyId;

    @ApiModelProperty(value = "属性编码")
    private String code;

    @ApiModelProperty(value = "属性名称")
    private String name;

    @ApiModelProperty(value = "加购数量")
    private Integer num;

    @ApiModelProperty(value = "费用单价：单位元")
    private BigDecimal price;

    @ApiModelProperty(value = "成本单价：单位元")
    private BigDecimal cost;

    /**
     * 资产类型
     * GoodsTypeEnum
     */
    private String goodsType;

    @ApiModelProperty(value = "扩展信息json")
    private String content;

}

package com.stbella.platform.order.api.cart;

import com.stbella.core.result.Result;
import com.stbella.platform.order.api.req.CartUpdateReq;
import com.stbella.platform.order.api.req.CreateOrderReq;
import com.stbella.platform.order.api.req.DiscountReq;
import com.stbella.platform.order.api.req.QueryCartReq;
import com.stbella.platform.order.api.res.CartInventoryRes;
import com.stbella.platform.order.api.res.CreateOrderRes;
import com.stbella.platform.order.api.res.DiscountRes;
import com.stbella.platform.order.api.res.CartRes;

import java.util.List;

/**
 * 购物车查询服务
 */
public interface CartQueryService {

    /**
     * 查询购物车
     * @param req
     * @return
     */
    Result<CartRes> queryCart(QueryCartReq req);

    /**
     * 查询购物车折扣信息
     * @param req
     * @return
     */
    Result<DiscountRes> queryDiscount(DiscountReq req);

    /**
     * 查询购物车商品对应的物料清单
     * @param req
     * @return
     */
    List<CartInventoryRes> queryInventory(QueryCartReq req);

    /**
     * 更新购物车签单金额
     * @param req
     * @return
     */
    Result<CartRes> updatePayAmount(CartUpdateReq req);
}

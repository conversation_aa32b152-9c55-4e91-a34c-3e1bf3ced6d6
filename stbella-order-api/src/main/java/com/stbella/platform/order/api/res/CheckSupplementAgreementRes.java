package com.stbella.platform.order.api.res;

import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 检查补充协议响应
 */
@Data
@ApiModel(value = "CheckSupplementAgreementRes", description = "检查补充协议响应")
public class CheckSupplementAgreementRes implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "是否需要补充协议")
    private Boolean needSupplement;

    @ApiModelProperty(value = "结果消息")
    private String message;

    @ApiModelProperty(value = "需要跳转的订单id")
    private Integer orderId;

    @ApiModelProperty(value = "是否已创建补充协议")
    private Boolean agreementCreated;

    OrderContractSignRecordVO supplementAgreement;

    public static CheckSupplementAgreementRes needSupplement(String message) {
        CheckSupplementAgreementRes res = new CheckSupplementAgreementRes();
        res.setNeedSupplement(true);
        res.setMessage(message);
        res.setAgreementCreated(true);
        return res;
    }

    public static CheckSupplementAgreementRes noNeedSupplement() {
        CheckSupplementAgreementRes res = new CheckSupplementAgreementRes();
        res.setNeedSupplement(false);
        res.setMessage("无需签订补充协议");
        res.setAgreementCreated(false);
        return res;
    }
}
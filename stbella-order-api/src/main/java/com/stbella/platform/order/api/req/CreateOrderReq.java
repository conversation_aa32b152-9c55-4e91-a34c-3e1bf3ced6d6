package com.stbella.platform.order.api.req;

import com.stbella.core.base.BasicReq;
import com.stbella.core.enums.BusinessEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.order.OrderSceneEnum;
import com.stbella.platform.order.api.res.PromotionInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-03-26  10:53
 * @Description: 标准订单创建请求
 */
@Data
public class CreateOrderReq extends BasicReq {

    /**
     * 业务线
     * @see BusinessEnum
     */
    @ApiModelProperty(value = "BU 0:母婴 1:广禾 2:予家")
    private Integer bu;

    /**
     * 订单类型 (可能有些场景，外部没有这个值，需要内部赋值)
     * @see OmniOrderTypeEnum
     */
    @ApiModelProperty(value = "订单类型")
    private Integer orderType = OmniOrderTypeEnum.MONTH_ORDER.code();

    /**
     * 场景编码
     * @see com.stbella.order.common.enums.order.OrderSceneEnum
     */
    @ApiModelProperty(value = "场景编码")
    private String sceneCode = OrderSceneEnum.OMNI.code();

    @ApiModelProperty(value = "门店ID")
    @NotNull(message = "请输入门店ID")
    private Integer storeId;

    @ApiModelProperty(value = "客户全局ID")
    @NotNull(message = "请输入客户全局ID")
    private Integer basicUid;

    @ApiModelProperty(value = "客户系统ID")
    private Integer clientUid;

    @ApiModelProperty(value = "sku")
    @NotNull(message = "sku")
    private List<SkuDetailInfo> skuList;

    @ApiModelProperty(value = "原价（元）")
    @NotNull(message = "原价不能为空")
    private  BigDecimal originalPrice;

    @ApiModelProperty(value = "现价（元）- 签单金额")
    @NotNull(message = "签单金额不能为空")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "促销活动，优惠券，满减等，本期无")
    private List<SalesPromotion> promotions;

    @ApiModelProperty(value = "订单额外信息")
    private ExtraInfo extraInfo;

    @ApiModelProperty(value = "订单来源")
    private Integer scene;

    @ApiModelProperty(value = "购物ID")
    private Integer cartId;

    @ApiModelProperty(value = "订单所有人")
    private Integer staffId;

    @ApiModelProperty(value = "订单所有人电话")
    private String staffPhone;

    @ApiModelProperty(value = "活动id列表")
    private List<PromotionInfo> promotionInfos;

}

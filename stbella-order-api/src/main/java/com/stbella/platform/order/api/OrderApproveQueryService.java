package com.stbella.platform.order.api;

import com.stbella.core.result.Result;
import com.stbella.order.server.order.month.req.OrderMonthIncomeQuery;
import com.stbella.order.server.order.month.res.OrderReductionVO;
import com.stbella.order.server.order.order.res.OrderMainInfo;
import com.stbella.platform.order.api.req.OrderDetailQuery;
import com.stbella.platform.order.api.res.ApproveRes;
import com.stbella.platform.order.api.res.QueryDepositAmountRes;

import java.util.List;

/**
 * @Author: ji<PERSON><PERSON><PERSON>
 * @CreateTime: 2024-03-26  10:49
 * @Description: 订单审批查询服务
 */
public interface OrderApproveQueryService {

    /**
     * 根据订单查询审批信息
     * @param query
     * @return
     */
    Result<ApproveRes> getApprove(OrderDetailQuery query);
}

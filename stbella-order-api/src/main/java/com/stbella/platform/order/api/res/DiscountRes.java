package com.stbella.platform.order.api.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@ApiModel(value = "折扣信息对象")
public class DiscountRes implements Serializable {

    @ApiModelProperty(value = "订单折扣率")
    private BigDecimal discountMargin = BigDecimal.ZERO;

    @ApiModelProperty(value = "订单毛利率")
    private BigDecimal grossMargin = BigDecimal.ZERO;

    @ApiModelProperty(value = "净折扣率")
    private BigDecimal netMargin = BigDecimal.ZERO;

    @ApiModelProperty(value = "签单折扣率")
    private BigDecimal signOrderDiscountMargin = BigDecimal.ZERO;

    @ApiModelProperty(value = "是否需要审批")
    @Deprecated
    private Boolean approval = false;

    /**
     * @See DiscountRuleResultEnum
     */
    @ApiModelProperty(value = "折扣判断结果")
    private Integer discountRuleResult;

    @ApiModelProperty(value = "税率, 格式：0.09 表示9个点的税，用于计算成本及毛利")
    private BigDecimal taxRate;

}

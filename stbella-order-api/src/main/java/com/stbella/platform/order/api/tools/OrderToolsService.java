package com.stbella.platform.order.api.tools;

import com.stbella.core.result.Result;
import com.stbella.order.server.order.month.req.OrderPageReq;
import com.stbella.platform.order.api.reduction.req.DecreaseCheckReq;
import com.stbella.platform.order.api.reduction.req.DecreaseReq;
import com.stbella.platform.order.api.req.CloseCommand;
import com.stbella.platform.order.api.res.DecreaseApprovalRes;
import com.stbella.platform.order.api.res.DecreaseCheckRes;

import java.util.List;

/**
 * @Author: ji<PERSON><PERSON><PERSON>
 * @CreateTime: 2024-05-27  13:25
 * @Description: 工具类
 */
public interface OrderToolsService {

    /**
     * 订单减免-审批校验
     */
    Result<String> resetAllocationPrice(OrderPageReq req);

    /**
     * 修复分摊错误
     */
    Result<String> repairAllocationPaidAmount(List<Integer> incomeIds);

    /**
     *  重新分摊
     */
    Result<String> retryAllocation(List<Integer> incomeIds);


}

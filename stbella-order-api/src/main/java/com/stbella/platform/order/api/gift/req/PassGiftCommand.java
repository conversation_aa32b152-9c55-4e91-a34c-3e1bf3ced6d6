package com.stbella.platform.order.api.gift.req;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-11-14  10:53
 * @Description:  礼赠审核通过
 */
@Data
public class PassGiftCommand extends BasicReq {

    /**
     * 礼赠批次号
     */
    @ApiModelProperty(value = "礼赠批次号")
    private String batchNo;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单id")
    private Integer orderId;
}

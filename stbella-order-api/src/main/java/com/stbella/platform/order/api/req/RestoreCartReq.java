package com.stbella.platform.order.api.req;

import com.stbella.core.base.BasicReq;
import com.stbella.core.enums.BusinessEnum;
import com.stbella.order.common.enums.core.CartSceneEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 还原购物车
 * @author: JJ
 */
@Data
public class RestoreCartReq extends BasicReq {

    @ApiModelProperty(value = "订单id等业务id")
    @NotNull(message = "请输入业务id")
    private Integer businessId;

    /**
     * 购物车场景
     * @see CartSceneEnum
     */
    @ApiModelProperty(value = "场景")
    private Integer scene = CartSceneEnum.ORDER.code();

}

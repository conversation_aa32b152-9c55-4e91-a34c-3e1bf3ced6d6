package com.stbella.platform.order.api.req;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 提交订单后删清空物车入参
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FinishCartReq extends BasicReq {

    private static final long serialVersionUID = -3135217018946551299L;

    @ApiModelProperty(value = "订单ID等业务id，0表示直接清空")
    @NotNull(message = "请输入订单ID")
    private Integer orderId;

    @ApiModelProperty(value = "购物车ID")
    @NotNull(message = "请输入购物车ID")
    private Integer cartId;

}

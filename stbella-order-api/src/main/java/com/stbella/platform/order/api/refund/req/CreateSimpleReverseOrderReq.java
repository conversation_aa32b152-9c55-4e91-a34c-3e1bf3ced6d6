package com.stbella.platform.order.api.refund.req;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-03-26  10:53
 * @Description: 简单逆向订单的创建请求
 *  生成一个负金额的订单，用于逆向操作，临时解决退款不退钱的问题
 */
@Data
public class CreateSimpleReverseOrderReq extends BasicReq {

    /**
     * 上级订单号
     */
    @ApiModelProperty(value = "上级订单号")
    @NotNull(message = "请输入上级订单号")
    private String parentOrderNo;

    @ApiModelProperty(value = "订单退款金额")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "订单备注")
    @NotNull(message = "请输入订单备注")
    private String remark;

}

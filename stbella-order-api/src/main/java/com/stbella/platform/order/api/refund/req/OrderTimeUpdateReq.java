package com.stbella.platform.order.api.refund.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
public class OrderTimeUpdateReq implements Serializable {

    private static final long serialVersionUID = 5868831516208033701L;

    @NotNull(message = "订单编号不能为空")
    private String orderSn;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date percentFirstTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payFirstTime;
}

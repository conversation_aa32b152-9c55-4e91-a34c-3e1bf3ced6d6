package com.stbella.platform.order.api.contract.api;

import com.stbella.core.result.Result;
import com.stbella.platform.order.api.contract.req.ContractBaseReq;
import com.stbella.platform.order.api.contract.res.OrderContractPreviewVO;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;

import javax.validation.Valid;

public interface ContractCommandService {


    /**
     * 检查合同状态
     *
     * @param req
     */
    void checkContractStatus(ContractBaseReq req);

    /**
     * 创建合同
     *
     * @param req
     * @return
     */
    Result<OrderContractSignRecordVO> createContract(ContractBaseReq req);

    /**
     * 预览合同
     *
     * @param req
     * @return
     */
    Result<OrderContractPreviewVO> previewContract(@Valid ContractBaseReq req);

//    Result<OrderCarrotContractBodyVO> buildCarrotContractBody(ContractBodyReq req);

//    Result<ContractSignAddressVO> getContractSignAddress(OrderSignContractReq signContractQuery);
}

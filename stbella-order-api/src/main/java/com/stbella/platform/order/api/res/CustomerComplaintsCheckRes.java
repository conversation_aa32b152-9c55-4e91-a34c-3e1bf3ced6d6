package com.stbella.platform.order.api.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel("客诉校验返回")
@Data
public class CustomerComplaintsCheckRes implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "是否展示审核 0-不展示 1-展示")
    private Boolean showApproval;

    @ApiModelProperty(value = "信息")
    private String msg;

}

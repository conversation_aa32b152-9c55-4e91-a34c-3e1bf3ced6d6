package com.stbella.platform.order.api.res;

import com.stbella.core.base.PageBaseReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel("客服闭环退款信息")
@Data
public class CustomerComplaintsCreateRefundVO extends PageBaseReq {

    @ApiModelProperty(value = "退款信息(新)")
    private CreateRefundVO newCreateRefundReq;

    @ApiModelProperty(value = "退款信息(旧)")
    private SubmitRefundApplyV2VO oldCreateRefundReq;

    @ApiModelProperty(value = "是否有额外赔偿意见 0-无，1-有")
    private Integer hasExtraCompensationOpinion;

    @ApiModelProperty(value = "赔偿金额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "开户名")
    private String accountName;

    @ApiModelProperty(value = "开户支行")
    private String accountBank;

    @ApiModelProperty(value = "银行账号")
    private String bankNo;

    @ApiModelProperty(value = "购物车id")
    private Integer cartId;

    @ApiModelProperty(value = "总计退款金额")
    private BigDecimal totalComplaintAmount;

    @ApiModelProperty(value = "扣除门店业绩")
    private BigDecimal totalBusinessAmount;

    @ApiModelProperty(value = "扣除销售业绩")
    private BigDecimal totalSaleAmount;

    @ApiModelProperty(value = "退款方式")
    private String goodsRefundTypeStr;

}

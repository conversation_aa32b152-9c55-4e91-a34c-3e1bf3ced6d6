package com.stbella.platform.order.api.refund.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class UpGradeGoodsRes implements Serializable {

    @ApiModelProperty(value = "订单商品表id")
    private Long orderGoodsId;

    private String orderGoodsSn;

    @ApiModelProperty(value = "上级")
    private String parentCombineSn;

    @ApiModelProperty(value = "商品类型：2 表示单个普通商品，3 表示组合商品")
    private Integer type;

    @ApiModelProperty(value = "商品类型")
    private Integer goodsType;

    @ApiModelProperty(value = "是否礼赠：0-否；1-是")
    private Integer gift;

    @ApiModelProperty(value = "商品id")
    private Integer goodsId;

    @ApiModelProperty(value = "skuId")
    private Integer skuId;

    @ApiModelProperty(value = "商品图片")
    private String goodsImage;

    @ApiModelProperty(value = "商品（组合）名称")
    private String goodsName;

    @ApiModelProperty(value = "规格名称（如果是组合，会显示组合下所有的商品的规格数据）")
    private String skuName;

    @ApiModelProperty(value = "规格名称（如果是组合，会显示组合下所有的商品的规格数据）")
    private List<String> specification;

    @ApiModelProperty(value = "应付总金额（如果是组合，显示整个组合数据）")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "总数量（如果是组合，显示整个组合数据）")
    private Integer goodsNum;

    @ApiModelProperty(value = "商品分摊单价")
    private BigDecimal allocationOriginPrice;

    private BigDecimal goodsPrice;

    @ApiModelProperty(value = "最大可升级数量")
    private Integer refundGoodsNum;


}

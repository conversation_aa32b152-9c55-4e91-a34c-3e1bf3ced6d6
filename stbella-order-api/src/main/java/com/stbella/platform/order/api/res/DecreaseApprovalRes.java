package com.stbella.platform.order.api.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: wangchuang
 * @CreateTime: 2024-05-29  10:53
 * @Description: 订单减免-审批校验
 */
@Data
public class DecreaseApprovalRes implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "折扣审批")
    private Boolean discountApproval;

    @ApiModelProperty(value = "合同修改审批")
    private Boolean orderModifyApproval;


}

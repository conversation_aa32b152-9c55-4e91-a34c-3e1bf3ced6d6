package com.stbella.platform.order.api.res;

import com.stbella.order.server.order.month.res.SelectRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@ApiModel("客诉详情")
@Data
public class CustomerComplaintsDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID（主键自增)")
    private Long id;

    private Long orderId;

    private String orderSn;

    private Integer orderType;


    @ApiModelProperty(value = "门店ID")
    private Integer storeId;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "客户ID")
    private Integer clientUid;

    private Integer basicUid;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "客户手机号")
    private String customerPhone;

    @ApiModelProperty(value = "客诉等级")
    private Integer complaintLevel;

    @ApiModelProperty(value = "客诉等级Str")
    private String complaintLevelStr;

    @ApiModelProperty(value = "客诉等级")
    private String complaintLevelName;

    @ApiModelProperty(value = "客诉类型")
    private String complaintTypeName;

    @ApiModelProperty(value = "客诉原因")
    private String complaintReason;

    /**
     * @see com.stbella.order.server.order.month.enums.CustomerComplaintsStatusEnum
     */
    @ApiModelProperty(value = "客诉状态 100-草稿 200-发起失败 300-审批中 400-审批拒绝 500-处理中 600-处理成功")
    private Integer complaintStatus;

    private Integer creator;

    @ApiModelProperty(value = "创建人")
    private String creatorName;

    @ApiModelProperty(value = "创建时间")
    private String createdAt;

    @ApiModelProperty(value = "订单创建时间")
    private Long orderCreatedAt;


    @ApiModelProperty(value = "定责类型")
    private Integer responsibilityType;

    @ApiModelProperty(value = "定责类型Str")
    private String responsibilityTypeStr;

    @ApiModelProperty(value = "时间")
    private Long eventTime;

    @ApiModelProperty(value = "地点")
    private String eventLocation;

    @ApiModelProperty(value = "人物")
    private String involvedPersons;

    @ApiModelProperty(value = "起因")
    private String cause;

    @ApiModelProperty(value = "经过")
    private String process;

    @ApiModelProperty(value = "结果")
    private String result;

    @ApiModelProperty(value = "凭证")
    private List<String> evidence;

    @ApiModelProperty(value = "主要责任人")
    private Integer primaryResponsiblePerson;

    @ApiModelProperty(value = "主要责任人姓名")
    private String primaryResponsiblePersonName;

    @ApiModelProperty(value = "次要责任人")
    private List<Integer> secondaryResponsiblePerson;

    @ApiModelProperty(value = "次要责任人姓名")
    private List<String> secondaryResponsiblePersonName;

    @ApiModelProperty(value = "供应商")
    private Long supplierId;

    @ApiModelProperty(value = "供应商值")
    private String supplierIdStr;

    @ApiModelProperty(value = "主供应商")
    private Integer mainSupplierId;

    @ApiModelProperty(value = "主供应商值")
    private String mainSupplierIdStr;

    @ApiModelProperty(value = "产康师ID")
    private Integer therapistId;

    private String therapistIdStr;

    @ApiModelProperty(value = "产康师姓名")
    private String therapistName;

    @ApiModelProperty(value = "三方产康师")
    private Integer thirdPartyTherapistId;

    private String thirdPartyTherapistIdStr;

    @ApiModelProperty(value = "三方产康师")
    private String thirdPartyTherapistName;

    @ApiModelProperty(value = "三方联系人姓名")
    private String thirdPartyContactName;

    @ApiModelProperty(value = "三方联系人电话")
    private String thirdPartyContactPhone;

    @ApiModelProperty(value = "退款订单id")
    private Long refundOrderId;

    @ApiModelProperty(value = "关联的购物车ID")
    private Long cartId;

    @ApiModelProperty(value = "购物车信息")
    private CartRes cartRes;

    @ApiModelProperty(value = "赔偿订单ID")
    private Long compensationOrderId;

    @ApiModelProperty(value = "是否有额外赔偿意见 0-无，1-有")
    private Integer hasExtraCompensationOpinion;

    @ApiModelProperty(value = "赔偿总金额")
    private BigDecimal compensationAmount;

    @ApiModelProperty(value = "是否跟产康相关 0-不相关，1-相关")
    private String productRelateType;

    @ApiModelProperty(value = "退款信息")
    private CustomerComplaintsCreateRefundVO customerComplaintsCreateRefundVO;

    @ApiModelProperty(value = "显示合同按钮 0-不显示，1-显示")
    private Integer isShowContractButton;

    @ApiModelProperty(value = "合同版本 0-无合同，1-有合同")
    private BigDecimal version;

    @ApiModelProperty(value = "审批id")
    private String approveId;

    @ApiModelProperty(value = "赔偿金额信息集")
    private List<ExtraRefundTypeRes> otherCompensationAmountList;

}

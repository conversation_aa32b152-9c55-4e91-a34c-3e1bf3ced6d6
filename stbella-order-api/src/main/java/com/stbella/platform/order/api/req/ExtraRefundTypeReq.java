package com.stbella.platform.order.api.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ExtraRefundTypeReq implements Serializable {

    private static final long serialVersionUID = 3739666408472874357L;

    @ApiModelProperty(value = "赔偿标识")
    private String extraCode;

    @ApiModelProperty(value = "赔偿金额")
    private BigDecimal extraRefundAmount;

    @ApiModelProperty(value = "退款方式 1-原路返回 2-线下汇款；3-退回至意向金")
    private Integer refundMethod;

    @ApiModelProperty(value = "退款方式描述信息")
    private String refundMethodStr;

    @ApiModelProperty(value = "开户名")
    private String accountName;

    @ApiModelProperty(value = "开户支行")
    private String accountBank;

    @ApiModelProperty(value = "银行账号")
    private String bankNo;
}

package com.stbella.platform.order.api.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class QueryDepositAmountRes implements Serializable {

    @ApiModelProperty(value = "押金可用余额")
    private BigDecimal depositAmount;

    @ApiModelProperty(value = "货币码")
    private String currency;

    @ApiModelProperty(value = "是否是旧的押金记录")
    private Boolean isOldDepositRecord;

}

package com.stbella.platform.order.api.refund.api;

import com.stbella.core.result.Result;
import com.stbella.platform.order.api.refund.req.*;
import com.stbella.platform.order.api.refund.res.*;
import com.stbella.platform.order.api.req.CustomerComplaintsCreateReq;
import com.stbella.platform.order.api.res.CreateApproveRes;

import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-03-26  10:49
 * @Description: 订单退款服务
 */
public interface OrderRefundService {

    /**
     * 创建简单反向订单
     *
     * @param req
     * @return
     */
    Result<String> createSimpleReverseOrder(CreateSimpleReverseOrderReq req);


    /**
     * 根据订单id获取退款的商品列表
     *
     * @param req
     * @return
     */
    Result<QueryOrderRefundInfoRes> queryGoods(QueryRefundGoodsReq req);

    /**
     * 发起退款申请
     *
     * @param req
     * @return
     */
    Result<CreateApproveRes> create(CreateRefundReq req);

    /**
     * 获取当前订单已退商品列表
     *
     * @param req
     * @return
     */
    Result<List<QueryOrderRefundGoodsInfoRes>> queryOrderGoodsWithRefundStatics(QueryOrderRefundGoodsInfoReq req);

    /**
     * 获取订单的退款列表
     *
     * @param req
     * @return
     */
    Result<List<OrderRefundListRes>> refundListByOrderId(OrderRefundListReq req);

    /**
     * 退款详情
     *
     * @param req
     * @return
     */
    Result<OrderRefundDetailsRes> refundDetails(OrderRefundDetailsReq req);

    /**
     * 退款进度
     *
     * @param req
     * @return
     */
    Result<List<OrderRefundProgressRes>> refundProgress(OrderRefundDetailsReq req);

    /**
     * 刷新状态：
     * 如果主退款审批通过，且当前收款类型为 支付宝、微信、pos，直接调用打款接口
     *
     * @param req
     * @return
     */
    Result<OrderRefundProgressRes> refreshStatus(OrderRefundDetailsReq req);

    /**
     * 退款失败后，重新发起
     *
     * @param req
     * @return
     */
    Result<CreateApproveRes> againRefund(AgainCreateRefundReq req);

    /**
     * 根据sn获取商品
     *
     * @param req
     * @return
     */
    Result<QueryEquallyGoodsRes> queryEquallyGoods(QueryQueryEquallyGoodsReq req);

    /**
     * 客诉退款进度
     *
     * @param req
     * @return
     */
    Result<List<OrderRefundProgressRes>> complaintRefundProgress(ComplaintRefundDetailsReq req);

    /**
     * 发起退款前校验
     *
     * @param createRefundReq
     * @return
     */
    Result<CreateApproveRes> verify(CustomerComplaintsCreateReq customerComplaintsCreateReq);

    /**
     * 判断订单是否有可退的意向金
     *
     * @param orderId
     * @return
     */
    Result<Integer> verifyOrderBalance(Integer orderId);

    /**
     * 计算本次退款的业绩扣除
     *
     * @param parentRefundId
     * @return
     */
    Result<Integer> calAchievement(Integer parentRefundId);


    Result<Integer> calRefundAchievement(Integer parentRefundId);

}

package com.stbella.platform.order.api.req;

import com.stbella.core.base.BasicReq;
import com.stbella.order.common.enums.core.CartSceneEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询购物车入参
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryCartReq extends BasicReq {

    @ApiModelProperty(value = "购物车ID：提交订单页查询购物车必传")
    private Integer cartId;

    @ApiModelProperty(value = "订单ID：修改或复制订单时必传")
    private Integer orderId;

    @ApiModelProperty(value = "门店ID：商品选择页查询购物车必传")
    private Integer storeId;

    @ApiModelProperty(value = "客户全局ID：商品选择页查询购物车必传")
    private Integer basicUid;

    @ApiModelProperty(value = "客户Uid")
    private Integer clientUid;

    /**
     * 订单类型 (可能有些场景，外部没有这个值，需要内部赋值)
     * @see OmniOrderTypeEnum
     */
    @ApiModelProperty(value = "订单类型")
    private Integer orderType = OmniOrderTypeEnum.MONTH_ORDER.code();

    /**
     * 购物车场景
     * @see CartSceneEnum
     */
    @ApiModelProperty(value = "场景")
    private Integer scene = CartSceneEnum.ORDER.code();

}

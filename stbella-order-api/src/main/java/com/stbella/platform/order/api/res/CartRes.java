package com.stbella.platform.order.api.res;

import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.platform.order.api.req.ExtraInfo;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 查询购物车出参
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CartRes implements Serializable {

    private static final long serialVersionUID = -9029612080827147L;

    @ApiModelProperty(value = "购物车ID")
    private Integer cartId;

    /**
     * 订单类型 (可能有些场景，外部没有这个值，需要内部赋值)
     * @see OmniOrderTypeEnum
     */
    @ApiModelProperty(value = "订单类型")
    private Integer orderType = OmniOrderTypeEnum.MONTH_ORDER.code();

    @ApiModelProperty(value = "门店ID")
    private Integer storeId;

    @ApiModelProperty(value = "客户全局ID")
    private Integer basicUid;

    @ApiModelProperty(value = "是否已提交订单：false-否，true-是")
    private boolean submitted;

    @ApiModelProperty(value = "订单ID")
    private Integer orderId;

    @ApiModelProperty(value = "订单原价：商品价格*Num")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "签单金额")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "原价（元）")
    private  BigDecimal originalPrice;

    @ApiModelProperty(value = "选购原价")
    private BigDecimal selectedAmount;

    @ApiModelProperty(value = "礼赠原价")
    private BigDecimal giftAmount;

    @ApiModelProperty(value = "加购商品集合")
    private List<SkuDetailInfo> skuList;

    @ApiModelProperty(value = "订单额外信息")
    private ExtraInfo extraInfo;

    private Integer bu;

    private Integer clientUid;

    @ApiModelProperty(value = "销售员id（ecp库user表主键id）")
    private Integer staffId;

    @ApiModelProperty(value = "销售员电话")
    private String staffPhone;

    @ApiModelProperty(value = "销售员姓名")
    private String staffName;

    @ApiModelProperty(value = "原订单ID（用于订单升级）")
    private Integer originalOrderId;

    @ApiModelProperty(value = "原商品总价（用于订单升级）")
    private BigDecimal originalGoodsTotalPrice;

    @ApiModelProperty(value = "予家订单-签单类型")
    private Integer signBillType;
}

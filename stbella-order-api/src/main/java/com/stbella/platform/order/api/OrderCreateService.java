package com.stbella.platform.order.api;

import com.stbella.core.result.Result;
import com.stbella.platform.order.api.req.CreateOrderReq;
import com.stbella.platform.order.api.refund.req.CreateSimpleReverseOrderReq;
import com.stbella.platform.order.api.res.CreateOrderRes;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-03-26  10:49
 * @Description: 订单创建服务
 */
public interface OrderCreateService {

    /**
     * 创建简单反向订单
     * @param req
     * @return
     */
    Result<String> createSimpleReverseOrder(CreateSimpleReverseOrderReq req);

    /**
     * 创建订单
     * @param req
     * @return
     */
    Result<CreateOrderRes> createOrder(CreateOrderReq req);

    /**
     * 创建押金订单
     * @param req
     * @return
     */
    Result<CreateOrderRes> createDepositOrder(CreateOrderReq req);
}

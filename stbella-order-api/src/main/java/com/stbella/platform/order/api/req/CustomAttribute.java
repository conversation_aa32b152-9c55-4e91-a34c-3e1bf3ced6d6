package com.stbella.platform.order.api.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 自定义属性信息
 * <AUTHOR>
 */
@Data
public class CustomAttribute implements Serializable {

    @ApiModelProperty(value = "服务属性ID")
    private Long propertyId;

    @ApiModelProperty(value = "服务属性名称")
    private String propertyName;

    @ApiModelProperty(value = "服务属性编码")
    private String code;

    @ApiModelProperty(value = "所属类目ID")
    private Long backId;

    @ApiModelProperty(value = "属性输入值")
    private String value;

    @ApiModelProperty(value = "属性显示值")
    private String showStr;

}

package com.stbella.platform.order.api.res;

import com.stbella.base.server.ding.response.CreateOrderApproveRecordVO;
import com.stbella.core.base.BasicReq;
import com.stbella.core.enums.BusinessEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.order.OrderSceneEnum;
import com.stbella.platform.order.api.req.ExtraInfo;
import com.stbella.platform.order.api.req.SalesPromotion;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-03-26  10:53
 * @Description: 标准订单创建响应
 */
@Data
@Builder
public class CreateOrderRes implements Serializable {

    @ApiModelProperty(value = "客户全局ID")
    @NotNull(message = "请输入客户全局ID")
    private Integer orderId;

    @ApiModelProperty(value = "客户系统ID")
    private String orderSn;

    @ApiModelProperty(value = "购物车ID")
    private Integer cartId;

    /**
     * 审批列表
     */
    List<CreateOrderApproveRecordVO> approveList;

}

package com.stbella.platform.order.api.refund.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class QueryRefundGoodsRes implements Serializable {

    @ApiModelProperty(value = "订单商品表id")
    private Long orderGoodsId;

    private String orderGoodsSn;

    @ApiModelProperty(value = "上级")
    private String parentCombineSn;

    @ApiModelProperty(value = "商品类型：2 表示单个普通商品，3 表示组合商品")
    private Integer type;

    @ApiModelProperty(value = "商品类型")
    private Integer goodsType;

    @ApiModelProperty(value = "是否礼赠：0-否；1-是")
    private Integer gift;

    @ApiModelProperty(value = "商品id")
    private Integer goodsId;

    @ApiModelProperty(value = "skuId")
    private Integer skuId;

    @ApiModelProperty(value = "商品图片")
    private String goodsImage;

    @ApiModelProperty(value = "商品（组合）名称")
    private String goodsName;

    @ApiModelProperty(value = "规格名称（如果是组合，会显示组合下所有的商品的规格数据）")
    private String skuName;

    @ApiModelProperty(value = "规格名称（如果是组合，会显示组合下所有的商品的规格数据）")
    private List<String> specification;

    @ApiModelProperty(value = "应付总金额（如果是组合，显示整个组合数据）")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "总数量（如果是组合，显示整个组合数据）")
    private Integer goodsNum;

    @ApiModelProperty(value = "商品分摊单价")
    private BigDecimal allocationOriginPrice;

    private BigDecimal goodsPrice;

    //这里统计的都是 现金支付的
    @ApiModelProperty(value = "已付总金额（如果是组合，显示整个组合数据）")
    @Deprecated
    private BigDecimal paidAmount;

    @ApiModelProperty(value = "已退总金额（如果是组合，显示整个组合数据）")
    @Deprecated
    private BigDecimal alreadyRefundAmount;

    @ApiModelProperty(value = "冻结可退金额")
    @Deprecated
    private BigDecimal freezeAmount;

    @ApiModelProperty(value = "应退总金额（如果是组合，显示整个组合数据）")
    @Deprecated
    private BigDecimal refundPayAmount;
    //这里统计的都是 现金支付的


    @ApiModelProperty(value = "退款金额明细")
    private List<RefundGoodsAmountInfo> refundGoodsAmountInfoList;

    public BigDecimal getPaidAmount() {
        return null == paidAmount ? BigDecimal.ZERO : paidAmount;
    }

    public BigDecimal getAlreadyRefundAmount() {
        return null == alreadyRefundAmount ? BigDecimal.ZERO : alreadyRefundAmount;
    }

    public BigDecimal getFreezeAmount() {
        return null == freezeAmount ? BigDecimal.ZERO : freezeAmount;
    }

    @ApiModelProperty(value = "可退数量（如果是组合，显示整个组合数据）")
    private Integer refundGoodsNum;


    @ApiModelProperty(value = "组合子商品数据")
    private List<QueryRefundGoodsRes> child;

    @ApiModelProperty(value = "附加项列表（假节日，多胞胎）")
    private List<QueryRefundGoodsRes> additionList;


}

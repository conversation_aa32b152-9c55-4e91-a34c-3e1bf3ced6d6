package com.stbella.platform.order.api.contract.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ButtonUpdateReq implements Serializable {

    private static final long serialVersionUID = 8753599593836511178L;

    @NotNull(message = "客户ID不能为空")
    @ApiModelProperty(value = "客户ID")
    private Integer basicUid;

    @NotNull(message = "订单ID不能为空")
    @ApiModelProperty(value = "订单ID")
    private Integer orderId;

    @NotNull(message = "合同主体类型不能为空")
    @ApiModelProperty(value = "0-客户;1-委托人")
    private Integer signType;
}

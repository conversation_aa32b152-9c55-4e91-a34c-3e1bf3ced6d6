package com.stbella.platform.order.api;

import com.stbella.core.result.Result;
import com.stbella.platform.order.api.req.CheckOrderProcessReq;
import com.stbella.platform.order.api.res.CheckOrderProcessRes;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-03-26  10:49
 * @Description: 订单流程节点服务 如果校验订单节点操作
 */
public interface OrderFlowService {

    /**
     * 过程节点校验
     * @param req
     * @return
     */
    Result<CheckOrderProcessRes> check(CheckOrderProcessReq req);

}

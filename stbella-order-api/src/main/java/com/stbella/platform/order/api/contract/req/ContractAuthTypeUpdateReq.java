package com.stbella.platform.order.api.contract.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ContractAuthTypeUpdateReq implements Serializable {

    private static final long serialVersionUID = 3689113580398888748L;

    @NotNull(message = "订单Id不能为空")
    @ApiModelProperty(name = "订单Id")
    private Integer orderId;

    @ApiModelProperty(value = "认证类型：0=中国大陆(不包含港澳台)-实名认证;1=邮箱", required = true)
    @NotNull(message = "认证类型不能为空")
    private Integer authType;

    @NotNull(message = "合同类型不能为空")
    @ApiModelProperty(value = "合同类型")
    private Integer templateContractType;

    @NotNull(message = "签约类型不能为空")
    @ApiModelProperty(value = "0-客户;1-委托人", required = true)
    private Integer signType;
}

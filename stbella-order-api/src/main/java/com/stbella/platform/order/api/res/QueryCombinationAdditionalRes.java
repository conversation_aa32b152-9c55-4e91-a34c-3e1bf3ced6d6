package com.stbella.platform.order.api.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class QueryCombinationAdditionalRes implements Serializable {

    @ApiModelProperty(value = "商品组合的sn")
    private String combinationGoodsSn;

    @ApiModelProperty(value = "商品类型：23-节假日；24-多胞胎")
    private Integer goodsType;

    @ApiModelProperty(value = "总数量")
    private Integer goodsNum = 0;

    @ApiModelProperty(value = "冻结数量")
    private Integer freezeNum = 0;

    @ApiModelProperty(value = "可退数量")
    private Integer refundGoodsNum = 0;

    @ApiModelProperty(value = "已退数量")
    private Integer alreadyRefundNum = 0;


    @ApiModelProperty(value = "应付总金额")
    private BigDecimal payAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "已付总金额")
    private BigDecimal paidAmount = BigDecimal.ZERO;


    @ApiModelProperty(value = "已退总金额")
    private BigDecimal alreadyRefundAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "冻结金额")
    private BigDecimal freezeAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "可退总金额")
    private BigDecimal refundPayAmount = BigDecimal.ZERO;


}

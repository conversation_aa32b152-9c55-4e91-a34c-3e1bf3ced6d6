package com.stbella.platform.order.api.refund.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel(value = "RefundGoodsAmountInfo对象", description = "退款金额信息")
public class RefundGoodsAmountInfo implements Serializable {

    @ApiModelProperty(value = "金额类型")
    private String model;

    @ApiModelProperty(value = "金额名称")
    private String label;

    @ApiModelProperty(value = "可退金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "可退金额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "支付方式,现金、产康金、积分")
    private String paymentMethod;

    /**
     * 可退金额单价（元）
     * 等于可退款金额除以可退数量
     */
    @ApiModelProperty(value = "可退金额单价（元）")
    private BigDecimal refundableUnitPrice;

    public BigDecimal getRefundAmount() {
        return this.amount;
    }
}

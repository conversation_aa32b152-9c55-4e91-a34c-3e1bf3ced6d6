package com.stbella.platform.order.api.contract.req;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/06/07
 */
@Data
public class ContractBaseReq extends BasicReq implements Serializable {

    private static final long serialVersionUID = 45717640179914002L;

    @ApiModelProperty(value = "订单id", required = true)
    @NotNull(message = "订单orderId不能为空")
    private Integer orderId;

    @ApiModelProperty(value = "0-客户;1-委托人", required = true)
//    @NotNull(message = "签署类型不能为空")
    private Integer signType;

    @ApiModelProperty("模板关联合同类型1育婴师-中介-贝康居间服务协议 " +
            "2雇主-中介-母婴护理-贝康母婴护理委托服务协议 " +
            "3雇主-中介-育婴师-贝康育婴师服务委托协议 " +
            "4育婴师-套餐-贝康服务合作协议 " +
            "5雇主-套餐-母婴护理-贝康母婴护理委托协议 " +
            "6雇主-套餐-育婴师-贝康育婴师服务委托协议 " +
            "7育婴师培训课程确认书协议 " +
            "8月子合同 " +
            "10.外派合同  " +
            "11.小月子合同 " +
            "12.总部与客户和解保密协议 " +
            "13.门店与客户和解保密协议 " +
            "14.礼赠协议 " +
            "15.补充协议  " +
            "16.无忧宝宝附属协议  " +
            "17.门店与个人新媒体置换合同 " +
            "21.预约协议 " +
            "22.授权委托书 " +
            "23.订单折扣保密协议 " +
            "24.合同解除协议 " +
            "25.离馆协议")
    private Integer templateContractType;

    /**
     * 合同类型
     */
    private Integer contractType;

    @ApiModelProperty(value = "合同id")
    private Integer contractId;

    @ApiModelProperty(value = "模板类型 1=订单类 2=附件类")
    private Integer templateType;

    @ApiModelProperty("预约协议 需要支付意向金金额 单位元")
    private BigDecimal amountEarnest;

    private Long reductionId;

}

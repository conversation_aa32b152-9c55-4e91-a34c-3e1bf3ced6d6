package com.stbella.platform.order.api.contract.api;

import com.stbella.core.result.Result;
import com.stbella.order.server.contract.res.ContractOrderVO;
import com.stbella.platform.order.api.contract.req.ContractBaseReq;
import com.stbella.platform.order.api.contract.req.ContractSignQuery;
import com.stbella.platform.order.api.contract.res.ContractReconstructOrderRes;
import com.stbella.platform.order.api.contract.res.OrderContractRemindCountVO;
import com.stbella.platform.order.api.contract.res.OrderContractRemindVO;
import com.stbella.platform.order.api.contract.res.OrderContractSignRecordVO;

import java.util.List;

public interface ContractQueryService {

    /**
     * 查看订单合同列表
     * @param contractBaseQuery
     * @return
     */
    Result<ContractReconstructOrderRes> list(ContractBaseReq contractBaseQuery);

    /**
     * 查看合同提醒数量
     * @param operatorGuid
     * @return
     */
    Result<OrderContractRemindCountVO> remindDetailCount(Integer operatorGuid);

    /**
     * 查看合同提醒详情
     * @param operatorGuid
     * @return
     */
    Result<OrderContractRemindVO> remindDetail(Integer operatorGuid);

    /**
     * 查看订单合同详情
     * @param orderId
     * @param templateContractType
     * @return
     */
    Result<OrderContractRemindVO> oldOrNewContractType(Long orderId, Integer templateContractType);

    /**
     * 查看合同签署记录
     * @param signContractQuery
     * @return
     */
    Result<List<OrderContractSignRecordVO>> querySignAndAttachmentList(ContractSignQuery signContractQuery);



}

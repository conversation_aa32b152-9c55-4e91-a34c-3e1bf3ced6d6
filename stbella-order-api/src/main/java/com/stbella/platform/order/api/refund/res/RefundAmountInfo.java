package com.stbella.platform.order.api.refund.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel(value = "RefundAmountInfo对象", description = "退款金额信息")
public class RefundAmountInfo implements Serializable {

    @ApiModelProperty(value = "支付方式,现金、产康金、积分")
    private String paymentMethod;

    /**
     * 已付款金额（分）
     * 只统计已支付成功的
     */
    @ApiModelProperty(value = "已付款金额")
    private BigDecimal paidAmount;

    /**
     * 已退款金额（分）
     * 包含本次之前的所有已成功退款金额
     */
    @ApiModelProperty(value = "已退款金额")
    private BigDecimal refundedAmount;

    /**
     * 可退款金额（分）
     * 等于原始支付金额减去已退款金额
     */
    @ApiModelProperty(value = "可退款金额")
    private BigDecimal refundableAmount;

    /**
     * 冻结退款金额（分）
     * 等于正在退款中的金额总和
     */
    @ApiModelProperty(value = "冻结退款金额（分）")
    private BigDecimal freeAmount;

    /**
     * 可退金额单价（元）
     * 等于可退款金额除以可退数量
     */
    @ApiModelProperty(value = "可退金额单价（元）")
    private BigDecimal refundableUnitPrice;

    public BigDecimal getRefundableAmount() {
        return getPaidAmount().subtract(getRefundedAmount()).subtract(getFreeAmount());
    }

    public BigDecimal getPaidAmount() {
        return null == paidAmount ? BigDecimal.ZERO : paidAmount;
    }

    public BigDecimal getRefundedAmount() {
        return null == refundedAmount ? BigDecimal.ZERO : refundedAmount;
    }

    public BigDecimal getFreeAmount() {
        return null == freeAmount ? BigDecimal.ZERO : freeAmount;
    }

    public BigDecimal getRefundableUnitPrice() {
        return null == refundableUnitPrice ? BigDecimal.ZERO : refundableUnitPrice;
    }
}

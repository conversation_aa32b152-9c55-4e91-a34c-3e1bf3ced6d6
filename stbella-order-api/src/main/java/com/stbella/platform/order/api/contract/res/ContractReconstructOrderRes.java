package com.stbella.platform.order.api.contract.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ContractReconstructOrderRes implements Serializable {

    private static final long serialVersionUID = 45717640179914002L;

    private Boolean isApprove;

    @ApiModelProperty("是否允许切换按钮 true-允许切换")
    private Boolean isPrincipal;

    @ApiModelProperty("订单合同列表")
    private List<OrderContractInfo> orderInfoList;

    private Boolean paperContract;

    private Integer signType;

}
package com.stbella.platform.order.api.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 检查补充协议请求
 */
@Data
@ApiModel(value = "CheckSupplementAgreementReq", description = "检查补充协议请求")
public class CheckSupplementAgreementReq implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "订单ID", required = true)
    @NotNull(message = "订单ID不能为空")
    private Integer orderId;
}
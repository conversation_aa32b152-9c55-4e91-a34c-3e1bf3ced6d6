package com.stbella.platform.order.api.contract.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class OrderAppurtenanceInfo {

    @ApiModelProperty("门店id")
    private Integer storeId;

    @ApiModelProperty("门店名称")
    private String storeName;

    @ApiModelProperty("客户名称")
    private String clientName;

    @ApiModelProperty("客户id")
    private Integer clientUid;

    @ApiModelProperty("订单id")
    private Long orderId;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "合同模板类型")
    private Integer templateContractType;

    @ApiModelProperty(value = "合同模板类型 中文")
    private String templateContractTypeName;

    @ApiModelProperty(value = "合同创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "新老系统标志;0或null-老;1-新")
    private Integer oldOrNew;

    @ApiModelProperty(value = "合同主键id")
    private Long contractId;

    @ApiModelProperty(value = "描述")
    private String contractDesc;

    @ApiModelProperty(value = "合同类型 1=E签宝合同 2=旧合同 3=纸质合同(标记“纸质合同”的戳)")
    private Integer contractType;

    @ApiModelProperty(value = "合同类型   1=>主合同，2=>附件类，3=>附属类：补充协议", required = true)
    private Integer templateType;

    @ApiModelProperty(value = "是否签订")
    private Boolean sign;

    @ApiModelProperty(value = "0=创建主合同类接口 1=创建附件类接口 2=其他无需调用接口")
    private Integer apiRoute;

    @ApiModelProperty(value = "0=大陆身份证 1=护照 2=香港来往大陆通行证 3=澳门来往大陆通行证 4=台湾来往大陆通行证")
    private Integer certType;

}

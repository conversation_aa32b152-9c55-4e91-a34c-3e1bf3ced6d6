package com.stbella.platform.order.api.refund.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class QueryEquallyGoodsRes implements Serializable {

    @ApiModelProperty(value = "订单id")
    private Integer orderId;

    @ApiModelProperty(value = "订单Sn")
    private String orderSn;

    @ApiModelProperty(value = "订单类型")
    private Integer orderType;

    @ApiModelProperty(value = "商品退款方式：1-退货退款；2-仅退款；3-退回重付；99-未知")
    private Integer refundNature;

    @ApiModelProperty(value = "订单版本")
    private BigDecimal version;

    @ApiModelProperty(value = "是否是新订单")
    private Boolean newOrder;

    @ApiModelProperty(value = "具体商品")
    private List<QueryEquallyGoodsListRes> queryEquallyGoodsListResList;

    @Data
    public static class QueryEquallyGoodsListRes implements Serializable {
        @ApiModelProperty(value = "自增ID")
        private Long id;

        @ApiModelProperty(value = "ApplyId")
        private Long refundApplyId;

        @ApiModelProperty(value = "退款申请Id")
        private Integer refundId;

        @ApiModelProperty(value = "退款SN")
        private String refundSn;

        @ApiModelProperty(value = "商品类型")
        private Integer goodsType;

        @ApiModelProperty(value = "资产类型")
        private Integer assetType;

        @ApiModelProperty(value = "订单商品序号")
        private String orderGoodsSn;

        @ApiModelProperty(value = "goodsId")
        private Integer goodsId;

        @ApiModelProperty(value = "skuId")
        private Integer skuId;

        @ApiModelProperty(value = "skuName")
        private String skuName;

        @ApiModelProperty(value = "申请退款商品数量")
        private Integer applyNum;

        @ApiModelProperty(value = "实际退款商品数量")
        private Integer actualNum;

        @ApiModelProperty(value = "申请退款金额")
        private BigDecimal applyAmount;

        @ApiModelProperty(value = "实际退款金额")
        private BigDecimal actualAmount;

        @ApiModelProperty(value = "申请产康金退款金额")
        private BigDecimal applyCkjAmount;

        @ApiModelProperty(value = "实际产康金退款金额")
        private BigDecimal actualCkjAmount;

        @ApiModelProperty(value = "现金分摊金额")
        private BigDecimal paidAmount;

        @ApiModelProperty(value = "产康金分摊金额")
        private BigDecimal ckjPaidAmount;

        @ApiModelProperty(value = "添加时间")
        private Date addTime;

        @ApiModelProperty(value = "更新时间")
        private Date updateTime;
    }


}

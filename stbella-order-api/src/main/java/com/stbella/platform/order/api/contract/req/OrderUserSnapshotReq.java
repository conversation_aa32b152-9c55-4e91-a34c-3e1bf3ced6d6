package com.stbella.platform.order.api.contract.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class OrderUserSnapshotReq implements Serializable {

    private static final long serialVersionUID = -7987583167432344803L;

    @ApiModelProperty(value = "0-客户;1-委托人", required = true)
    private Integer signType;

    private String emailVerifierCode;

    private String identificationVerifierCode;

    @ApiModelProperty(value = "订单Id")
    private Integer orderId;

    @ApiModelProperty(value = "建单时的客户id", required = true)
    private Integer clientUid;

    @ApiModelProperty(value = "委托人姓名")
    private String name;

    @ApiModelProperty(value = "手机号类型, 0:中国大陆 1:中国香港 2:中国澳门 3:中国台湾")
    private Integer phoneType;

    @ApiModelProperty(value = "委托人手机号")
    private String phone;

    @ApiModelProperty(value = "认证状态 0=手机号未验证过;1=已验证")
    private Integer isPhoneVerify = 1;

    private Integer certType;

    @ApiModelProperty(value = "证件号")
    private String idCard;

    @ApiModelProperty(value = "委托人证件复印件正面照")
    private String idCardFront;

    @ApiModelProperty(value = "委托人证件复印件反面照")
    private String idCardBack;

    private Integer isCardVerify = 0;

    @ApiModelProperty(value = "认证类型：0=中国大陆(不包含港澳台)-实名认证1=邮箱")
    private Integer authType;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "全局用户ID")
    private Integer basicUid;

    @ApiModelProperty(value = "邮箱认证状态 0=未认证 1=已认证")
    private Integer emailVerify;

    @ApiModelProperty(value = "省份。-1为其他")
    private Integer province;

    @ApiModelProperty(value = "城市。-1为其他")
    private Integer city;

    @ApiModelProperty(value = "地区。-1为其他")
    private Integer region;

    @ApiModelProperty(value = "家庭详细地址")
    private String address;

    @ApiModelProperty(value = "紧急联系人名字")
    private String urgentName;

    @ApiModelProperty(value = "紧急联系人号码")
    private String urgentPhone;

    @ApiModelProperty(value = "与委托人关系：0宝妈，1宝爸，2家属，3夫妻，4母女，5父女")
    private Integer relationWithClient;

    @ApiModelProperty(value = "所属门店")
    private Integer storeId;

    @ApiModelProperty(value = "预产期")
    private Date predictBornDate;

    @ApiModelProperty(value = "预计入住时间")
    private Long wantIn;

    @ApiModelProperty(value = "胎数")
    private Integer fetusNum;

    @ApiModelProperty(value = "胎次")
    private Integer bornNum;

    @ApiModelProperty(value = "分娩医院")
    private String hospital;

    @ApiModelProperty(value = "业务员id")
    private Integer sellerId;

    @ApiModelProperty(value = "业务员姓名")
    private String sellerName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "渠道来源：0未知-废弃，1(400大众点评)，2(400小红书)，3(400微博), 4(400线下广告)，5(400未告知)，6(400朋友推荐)，7(400未告知），8（400公众号/小程序），9（400官网），10（400直播），11（非400公众号），12（非400小红书），13（非400官网/小程序），14（非400微博），15（大众点评预约），16（大众点评在线咨询），17（新增小助手）,18（小程序预约） 19-口碑介绍, 20-合作渠道, 21-线下广告, 22-线下活动, 23-大众点评, 24-百度/谷歌, 25-官网, 26-小红书, 27-微信公众号, 28-微博, 29-其他, 30-微信小程序, 31-抖音, 32-2021抖音活动, 33-2021微信广告活动, 34-已入馆宝妈, 35-朋友圈广告, 36-医疗渠道, 37-小贝拉私域")
    private Integer fromType;

    @ApiModelProperty(value = "枚举星座类型： ‘0白羊座’,’1金牛座’,’2双子座’,’3巨蟹座’,’4狮子座’,’5处女座’,’6天秤座’,’7天蝎座’,’8射手座’,’9摩羯座’,’10水瓶座’,’11双鱼座''")
    private Integer constellationType;

    @ApiModelProperty(value = "星座")
    private String constellation;

    @ApiModelProperty(value = "客户职业")
    private String profession;

    @ApiModelProperty(value = "年龄")
    private Integer age;

    @ApiModelProperty(value = "血型：0未知，1A，2B，3O，4AB, 5RH")
    private Integer bloodType;

    @ApiModelProperty(value = "孕第几周")
    private Integer gestationWeekNow;

    @ApiModelProperty(value = "推荐码")
    private String qrCode;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "手机号-明文")
    private String phoneCiphertext;

    /**
     * 紧急联系人明文
     */
    private String urgentPhoneCiphertext;

    /**
     * 证件照正面 url 明文
     */
    private String idCardFrontCiphertext;

    /**
     * 证件照背面 url 明文
     */
    private String idCardBackCiphertext;
    /**
     * 证件号 明文
     */
    private String idCardCiphertext;

}

package com.stbella.platform.order.api.refund.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class OrderDivisionPerformanceReq implements Serializable {

    @ApiModelProperty(value = "划分Id")
    private Long operationId;

    @ApiModelProperty(value = "订单id")
    private Integer orderId;

    @ApiModelProperty(value = "总业绩（减去产康金支付）")
    private BigDecimal performance;

    @ApiModelProperty(value = "订单所属门店业绩金额")
    private BigDecimal retainedPerformance;

    @ApiModelProperty(value = "划分门店id")
    private Integer targetStoreId;

    @ApiModelProperty(value = "划分门店业绩金额")
    private BigDecimal targetPerformance;
}

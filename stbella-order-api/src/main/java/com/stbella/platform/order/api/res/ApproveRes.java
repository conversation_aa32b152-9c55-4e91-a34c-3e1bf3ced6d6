package com.stbella.platform.order.api.res;

import com.stbella.order.server.order.month.enums.ApprovalDiscountStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@ApiModel(value = "审批对象")
public class ApproveRes implements Serializable {

    @ApiModelProperty("审批本地id")
    private String id;

    /**
     * 审批信息
     * @see ApprovalDiscountStatusEnum
     */
    @ApiModelProperty("状态")
    private Integer status;
    @ApiModelProperty("标题")
    private String title;


}

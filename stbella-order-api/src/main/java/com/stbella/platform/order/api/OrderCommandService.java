package com.stbella.platform.order.api;

import com.stbella.core.result.Result;
import com.stbella.platform.order.api.reduction.req.DecreaseCheckReq;
import com.stbella.platform.order.api.reduction.req.DecreaseReq;
import com.stbella.platform.order.api.refund.req.OrderDivisionPerformanceReq;
import com.stbella.platform.order.api.req.CloseCommand;
import com.stbella.platform.order.api.res.DecreaseApprovalRes;
import com.stbella.platform.order.api.res.DecreaseCheckRes;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-05-27  13:25
 * @Description: 命令 - 取消，减免等操作
 */
public interface OrderCommandService {

    /**
     * 订单减免-审批校验
     */
    Result<DecreaseApprovalRes> submitCheck(DecreaseReq req);

    /**
     * 订单减免
     */
    Result reduction(DecreaseReq req);

    /**
     * 订单减免-点击校验
     */
    Result<DecreaseCheckRes> reductionCheck(DecreaseCheckReq req);


    /**
     * 关闭订单
     *
     * @param command
     * @return
     */
    Result<Void> close(CloseCommand command);

    /**
     * 批量更新订单核销状态
     *
     * @return
     */
    Result<Integer> batchUpdateVerification();


    /**
     * 划分订单业绩
     *
     * @param req
     */
    Result orderDivisionPerformance(OrderDivisionPerformanceReq req);

    /**
     * 删除业绩划分
     *
     * @param req
     * @return
     */
    Result orderDivisionPerformanceDelete(OrderDivisionPerformanceReq req);

    /**
     * 扣减产康金
     * @param orderId
     */
    void checkInDecreaseCKJ(Integer orderId);
}

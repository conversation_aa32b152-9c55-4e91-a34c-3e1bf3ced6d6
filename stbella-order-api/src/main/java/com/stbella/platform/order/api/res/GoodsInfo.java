package com.stbella.platform.order.api.res;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品信息
 */
@Data
@Accessors(chain = true)
public class GoodsInfo implements Serializable {

    /**
     * 主键
     */
    Integer id;

    /**
     * 创建时间
     */
    Long createdAt;

    /**
     * 最近更新时间
     */
    Long updatedAt;

    /**
     * 门店id
     */
    Integer storeId;

    /**
     * 商品类目 0月子标准套餐商品，1小月子商品，2到家雇主服务商品，3到家育婴师服务商品，4到家育婴师培训商品，5商城商品，
     * 6贝护家其他订单， 7护士外派 8=S-BRA商品
     */
    Integer goodsType;

    /**
     * 商品名称
     */
    String goodsName;

    /**
     * 商品价格，单位分
     */
    Integer goodsPrice;

    /**
     * 商品分类：后台
     */
    Integer categoryBack;

    /**
     * 商品分类：前台
     */
    Integer categoryFront;

    /**
     * 销售状态（上下价状态）：0下架，1上架，2定时上下架
     */
    Integer saleType;

    /**
     * 护理服务时长，单位：天
     */
    Integer serviceDays;

    /**
     * 商品缩略图
     */
    String image;

    /**
     * 商品轮播图，英文逗号分隔
     */
    String images;

    /**
     * 商品类型:0=真实商品;1=虚拟商品;2=预约商品;3=套餐商品;4=产康卡项;5=产康商品
     */
    Integer goodsSellType;


    /**
     * 套餐成本（元）
     */
    Integer packageCost;

    /**
     * 产康金
     */
    Integer productionAmount;

    /**
     * 产康金抵扣 0）不可抵扣 1)可抵扣
     */
    Integer produceAmountDeduction;
    /**
     * ecp房型配置
     */
    Integer ecpRoomType;
    /**
     * 父版本编号（新增的门店套站记录来源编号）
     */
    Integer parentId;
    /**
     * 是否删除
     */
    Integer isDelete;
    /**
     * 服务类型 0自营 1第三方
     */
    Integer serviceType;
    /**
     * 服务标签
     */
    Long serviceTag;
    /**
     * 产康仪器id 0表示不需要仪器
     */
    Long productionInstrId;
    /**
     * 手工费配置状态 0:未配置 1:已配置
     */
    Integer isConfig;
    /**
     * 手工费配置修改时间
     */
    Long configUpdateAt;
    /**
     * 产康金抵扣规则 0:全额抵扣 1:满10000减2000
     */
    private Integer productionDiscountRuleType;
    /**
     * 是否参与折扣 0:不参与 1:参与
     */
    private Integer partDiscount;
    /**
     * 0-非礼赠；1-礼赠
     */
    private Integer gift;
    /**
     * 商品单位
     */
    private Integer goodsUnit;
    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 供应商绑定时间
     */
    private Date supplierBindTime;

    /**
     * 父级商品名字
     */
    private String parentName;

    /**
     * 审批状态：0-无审批；1-审批中；2-审批通过；3-审批拒绝（撤销、删除）
     */
    private Integer processStatus;

    /**
     * 销售定价方式：0-全国统一定价；1-门店自定义价格；2-自选销售价加和（组合商品使用）
     */
    private Integer salesPricingMethod;

    /**
     * 成本定价方式：0-全国统一定价；1-门店自定义价格；2-按销售价比例定价
     */
    private Integer costPricingMethod;

    /**
     * 限购配置，组合商品使用 格式  [{“enable”:0,”type”:0,”num”:1}]
     */
    private String purchaseLimitationConfig;

    /**
     * 成本比例
     */
    private Integer costRatio;

    /**
     * 合作信息：0-全国独家供应商；1-门店指定供应商
     */
    private Integer cooperationInformation;

    /**
     * 合作主体id
     */
    private Long bankOpenAccountBank;

    /**
     * 结算类型：0-指定结算价；1-指定结算比例
     */
    private Integer settlementRatio;

    /**
     * 组合类型：0-固定搭配；
     */
    private Integer combinationType;

    /**
     * 定价方式：0-无；1-子商品分别定价；2-基础价+子商品加价
     */
    private Integer priceType;

    /**
     * 模板id
     */
    private Integer templateId;

    /**
     * 小助手房型id
     */
    private Integer roomId;

    /**
     * 三方结算价(分)
     */
    private Integer tripartiteSettlementPrice;

    /**
     * 三方结算比例
     */
    private Integer tripartiteSettlementRatio;

    /**
     * 商品版本
     */
    private BigDecimal goodsVersion;

    /**
     * 物品清单url
     */
    private String inventoryUrl;

    /**
     * 时间结算点：0-按收款结算；1-按核销结算
     */
    private Integer timeClearingPoint;

    /**
     * 主供应商Id
     */
    private Long mainSupplierId;

    /**
     * 生效阶段，孕期/产后
     */
    private String availableStage;


}

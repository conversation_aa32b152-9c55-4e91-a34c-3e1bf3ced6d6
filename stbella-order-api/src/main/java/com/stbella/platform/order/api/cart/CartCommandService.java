package com.stbella.platform.order.api.cart;

import com.stbella.core.result.Result;
import com.stbella.platform.order.api.req.ClearCartReq;
import com.stbella.platform.order.api.req.CreateOrUpdateCartReq;
import com.stbella.platform.order.api.req.FinishCartReq;
import com.stbella.platform.order.api.req.RestoreCartReq;

/**
 * 购物车操作服务
 */
public interface CartCommandService {

    /**
     * 创建或更新购物车
     * @param req
     * @return
     */
    Result<Integer> createOrUpdateCart(CreateOrUpdateCartReq req);

    /**
     * 提交订单后清空购物车 设置提交状态，
     * @param req
     * @return
     */
    Result<Boolean> finishCart(FinishCartReq req);

    /**
     * 清空购物
     * @param req
     * @return
     */
    Result<Boolean> clearCart(ClearCartReq req);

    /**
     * 还原购物车
     * 使用场景：
     * 1， 再来一单 - 从订单还原购物车。
     * 2， 报价单编辑 - 从报价单还原购物车。
     * @param req
     * @return
     */
    Result<Integer> restoreCart(RestoreCartReq req);

    /**
     * 复制购物车
     * @param req
     * @return
     */
    Result<Integer> copyCart(RestoreCartReq req);
}

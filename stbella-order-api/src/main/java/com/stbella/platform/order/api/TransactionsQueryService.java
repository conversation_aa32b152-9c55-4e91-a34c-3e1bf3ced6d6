package com.stbella.platform.order.api;

import com.stbella.core.result.Result;
import com.stbella.order.server.order.month.res.OrderReductionVO;
import com.stbella.order.server.order.month.response.QueryIncomeProofRecordByIncomeSnVO;
import com.stbella.order.server.order.order.res.OrderMainInfo;
import com.stbella.platform.order.api.req.OrderDetailQuery;
import com.stbella.platform.order.api.req.TransactionDetailQuery;
import com.stbella.platform.order.api.res.TransactionPaidAllocationDto;
import com.stbella.platform.order.api.res.TransactionWithAllocationDto;

import java.util.List;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-03-26  10:49
 * @Description: 交易查询接口
 */
public interface TransactionsQueryService {

    /**
     * 查询单个支付记录的分摊
     * @param query
     * @return
     */
    Result<TransactionWithAllocationDto> getTransactionAllocation(TransactionDetailQuery query);

    /**
     * 查询线下支付凭证
     * @param query
     * @return
     */
    Result<QueryIncomeProofRecordByIncomeSnVO> getOfflineProofByTransSn(TransactionDetailQuery query);


}

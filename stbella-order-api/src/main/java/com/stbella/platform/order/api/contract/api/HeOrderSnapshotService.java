package com.stbella.platform.order.api.contract.api;

import com.stbella.core.result.Result;
import com.stbella.order.server.order.month.response.ClientBooleanVO;
import com.stbella.order.server.order.month.response.OrderUserSnapshotVO;
import com.stbella.order.server.order.month.response.TabClientVO;
import com.stbella.platform.order.api.contract.req.ButtonUpdateReq;
import com.stbella.platform.order.api.contract.req.ClientCardTypeReq;
import com.stbella.platform.order.api.contract.req.ContractAuthTypeUpdateReq;
import com.stbella.platform.order.api.contract.req.OrderUserSnapshotReq;
import com.stbella.platform.order.api.contract.res.OrderContractSnapshotVO;

/**
 * <p>
 * 订单记录表（草稿表） 服务类
 * </p>
 *
 * <AUTHOR> @since 2021-12-10
 */
public interface HeOrderSnapshotService {

    /**
     * 更新订单用户快照表
     */
    OrderContractSnapshotVO updateSnapshot(OrderUserSnapshotReq req);


    /**
     * 查询订单用户快照表
     */
    OrderUserSnapshotVO queryBailorInfo(OrderUserSnapshotReq req);


    TabClientVO queryClientInfo(OrderUserSnapshotReq req);

    void authTypeUpdate(ContractAuthTypeUpdateReq req);

    Result<ClientBooleanVO> queryCardList(ClientCardTypeReq request);

    /**
     * 查询修改按钮
     * @param request
     * @return
     */
    Result<ClientBooleanVO> queryUpdateButton(ButtonUpdateReq request);
}

package com.stbella.platform.order.api.refund.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class OrderToolsReq implements Serializable {

    private static final long serialVersionUID = -8041594094011863106L;

    private String orderSn;

    private String newPhone;

    /**
     * 退款订单编码
     */
    private String refundOrderSn;

    /**
     * 退款方式
     */
    private Integer refundType;

    /**
     * 数据状态
     */
    private Integer status;
}

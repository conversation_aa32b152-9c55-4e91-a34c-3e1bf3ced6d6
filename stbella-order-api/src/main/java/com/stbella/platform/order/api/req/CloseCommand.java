package com.stbella.platform.order.api.req;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * 关闭订单请求
 */
@Data
public class CloseCommand extends BasicReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单id")
    @NotNull(message = "订单id不能为空")
    private Integer orderId;


}

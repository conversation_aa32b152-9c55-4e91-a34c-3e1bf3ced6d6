package com.stbella.platform.order.api.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel("客诉类型")
@Data
public class CustomerComplaintsTypeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("客诉类型ID")
    private String id;

    @ApiModelProperty("父ID")
    private Integer parentId;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("类型 100-产康相关，200-供应商相关")
    private Integer type;

    @ApiModelProperty("子集")
    private List<CustomerComplaintsTypeVO> children;

}

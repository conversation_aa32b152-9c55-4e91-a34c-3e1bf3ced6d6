package com.stbella.platform.order.api.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel("客诉类型")
@Data
public class CustomerComplaintsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID（主键自增)")
    private Long id;

    @ApiModelProperty(value = "门店ID")
    private Integer storeId;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "客户ID")
    private Integer clientUid;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "客户手机号")
    private String customerPhone;

    @ApiModelProperty(value = "客诉等级")
    private Integer complaintLevel;

    @ApiModelProperty(value = "客诉等级")
    private String complaintLevelName;

    @ApiModelProperty(value = "客诉类型")
    private String complaintTypeName;

    @ApiModelProperty(value = "客诉原因")
    private String complaintReason;

    /**
     * @see com.stbella.order.server.order.month.enums.CustomerComplaintsStatusEnum
     */
    @ApiModelProperty(value = "客诉状态 50:待处理 10:草稿 200:发起失败 300:审批中 400:审批拒绝 500:处理中 600:处理成功")
    private Integer complaintStatus;

    private Integer creator;

    @ApiModelProperty(value = "创建人")
    private String creatorName;

    @ApiModelProperty(value = "创建时间")
    private String createdAt;

}

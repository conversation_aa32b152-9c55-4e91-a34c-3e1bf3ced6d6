package com.stbella.platform.order.api.req;

import com.stbella.core.base.PageBaseReq;
import com.stbella.order.server.order.month.req.SubmitRefundApplyV2Request;
import com.stbella.platform.order.api.refund.req.CreateRefundReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@ApiModel("客服闭环退款信息")
@Data
public class CustomerComplaintsCreateRefundReq extends PageBaseReq {

    private static final long serialVersionUID = 3581965067360637924L;

    @ApiModelProperty(value = "退款信息(新)")
    private CreateRefundReq newCreateRefundReq;

    @ApiModelProperty(value = "退款信息(旧)")
    private SubmitRefundApplyV2Request oldCreateRefundReq;

    @ApiModelProperty(value = "是否有额外赔偿意见 0-无，1-有")
    private Integer hasExtraCompensationOpinion;

    @ApiModelProperty(value = "现金赔偿金额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "赔偿方式集合")
    private List<ExtraRefundTypeReq> extraRefundTypeList;

    @ApiModelProperty(value = "赔偿商品金额")
    private BigDecimal amountGoodsClaimed;

    @ApiModelProperty(value = "订单退款金额")
    private BigDecimal orderRefundAmount;

    @ApiModelProperty(value = "开户名")
    private String accountName;

    @ApiModelProperty(value = "开户支行")
    private String accountBank;

    @ApiModelProperty(value = "银行账号")
    private String bankNo;

    @ApiModelProperty(value = "购物车id")
    private Integer cartId;

}

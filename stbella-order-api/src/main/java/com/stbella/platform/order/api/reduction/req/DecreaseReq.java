package com.stbella.platform.order.api.reduction.req;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: wangchuang
 * @CreateTime: 2024-05-29  10:53
 * @Description: 订单减免
 */
@Data
public class DecreaseReq extends BasicReq implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "订单id")
    @NotNull(message = "订单id不能为空")
    private Long orderId;

    @ApiModelProperty(value = "优惠金额")
    @Min(value = 1, message = "优惠金额不能小于1元")
    private BigDecimal decreaseAmount;

    @ApiModelProperty(value = "原因")
    @NotNull(message = "原因不能为空")
    private String reason;

}

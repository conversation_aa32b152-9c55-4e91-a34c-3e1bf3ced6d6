package com.stbella.platform.order.api.res;

import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 支付记录查询
 */
@Data
@Accessors(chain = true)
public class TransactionWithAllocationDto implements Serializable {

    private Integer id;

    @ApiModelProperty(value = "用户id")
    private Integer clientUid;

    @ApiModelProperty(value = "订单类型")
    private Integer OrderType;

    @ApiModelProperty(value = "是否新订单")
    private Boolean newOrder;

    @ApiModelProperty(value = "全局用户ID")
    private Integer basicUid;

    @ApiModelProperty(value = "支付编号，相当于支付第三方的订单编号；一个订单ID对应多个支付编号代表一个订单是分多次支付的")
    private String incomeSn;

    @ApiModelProperty(value = "订单id(he_order表主键)")
    private Integer orderId;

    @ApiModelProperty(value = "门店ID(ecp库cfg_store表的主键id)")
    private Integer storeId;

    @ApiModelProperty(value = "收款渠道, 1-原生 2-创识,3-招商")
    private Integer channelType;

    @ApiModelProperty(value = "1=微信, 2=支付宝, 3=线下支付, 4=中信银行支付宝支付, 5=中信银行微信支付  8=在线pos机支付  9=C端小程序支付 10=其他")
    private Integer payType;

    @ApiModelProperty(value = "支付状态：0待支付，1已支付")
    private Integer status;

    @ApiModelProperty(value = "支付金额(单位元)")
    private BigDecimal income;

    @ApiModelProperty(value = "支付金额(单位元)")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "支付时间")
    private String payTime;

    @ApiModelProperty(value = "支付时间")
    private String payDate;

    @ApiModelProperty(value = "对账状态：0未对账，1正常，2异常")
    private Integer checkStatus;

    @ApiModelProperty(value = "第三方交易流水号")
    private String transactionId;

    @ApiModelProperty(value = "收款标识 1)意向金；2）预付款；3）尾款；4）押金")
    private Integer receiptType;

    @ApiModelProperty(value = "创建时间")
    private Long createdAt;

    @ApiModelProperty(value = "最近更新时间")
    private Long updatedAt;

    @ApiModelProperty(value = "支付账号主体ID,pay_account表主键")
    private Long accountId;

    @ApiModelProperty(value = "货币码")
    private String currency;

    /**
     * 分摊记录
     */
    List<TransactionPaidAllocationDto> allocationList;


}

package com.stbella.platform.order.api.refund.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class OrderRefundProgressRes implements Serializable {

    @ApiModelProperty(value = "退款id")
    private Integer id;

    @ApiModelProperty(value = "退款id")
    private Integer refundId;

    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "1-微信；2-支付宝；3-线下退款；8-在线pos机;101-余额")
    private Integer refundType;

    @ApiModelProperty(value = "1-微信；2-支付宝；3-线下退款；8-在线pos机;101-余额")
    private String refundTypeStr;

    @ApiModelProperty(value = "退款状态，1：审批中 2：审批失败  3：退款中 4：退款已到帐/已确认 5：到账失败/已拒绝")
    private Integer status;

    @ApiModelProperty(value = "退款状态，1：审批中 2：审批失败  3：退款中 4：退款已到帐/已确认 5：到账失败/已拒绝")
    private String statusStr;

    @ApiModelProperty(value = "退款申请时间")
    private String submissionTime;

    @ApiModelProperty(value = "退款到账时间")
    private String refundSuccessTime;

    @ApiModelProperty(value = "退款流水号")
    private String refundOrderSn;

    @ApiModelProperty(value = "货币码")
    private String currency;
}

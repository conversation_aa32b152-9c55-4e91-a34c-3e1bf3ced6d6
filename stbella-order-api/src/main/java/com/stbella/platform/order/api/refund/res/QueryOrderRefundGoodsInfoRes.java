package com.stbella.platform.order.api.refund.res;

import com.stbella.order.common.enums.core.OrderRefundNatureEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class QueryOrderRefundGoodsInfoRes implements Serializable {

    @ApiModelProperty(value = "订单商品表id")
    private Long orderGoodsId;

    @ApiModelProperty(value = "商品编号")
    private String orderGoodsSn;

    @ApiModelProperty(value = "上级")
    private String parentCombineSn;

    @ApiModelProperty(value = "商品类型：2 表示单个普通商品，3 表示组合商品")
    private Integer type;

    @ApiModelProperty(value = "商品类型")
    private Integer goodsType;

    @ApiModelProperty(value = "商品id")
    private Integer goodsId;

    @ApiModelProperty(value = "skuId")
    private Integer skuId;

    @ApiModelProperty(value = "商品（组合）名称")
    private String goodsName;

    @ApiModelProperty(value = "规格名称（如果是组合，会显示组合下所有的商品的规格数据）")
    private String skuName;

    @ApiModelProperty(value = "总数量")
    private Integer goodsNum = 0;

    @ApiModelProperty(value = "冻结数量")
    private Integer freezeNum = 0;

    @ApiModelProperty(value = "可退数量")
    private Integer refundGoodsNum = 0;

    @ApiModelProperty(value = "已退数量")
    private Integer alreadyRefundNum = 0;


    @ApiModelProperty(value = "应付总金额")
    private BigDecimal payAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "已付总金额")
    private BigDecimal paidAmount = BigDecimal.ZERO;


    @ApiModelProperty(value = "已退总金额")
    private BigDecimal alreadyRefundAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "冻结金额")
    private BigDecimal freezeAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "可退总金额")
    private BigDecimal refundPayAmount = BigDecimal.ZERO;

    /**
     * @see OrderRefundNatureEnum
     */
    @ApiModelProperty(value = "退款性质")
    private Integer refundNature;


}

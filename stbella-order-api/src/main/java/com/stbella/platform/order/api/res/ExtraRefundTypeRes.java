package com.stbella.platform.order.api.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ExtraRefundTypeRes implements Serializable {

    private static final long serialVersionUID = 7357318495799395422L;

    @ApiModelProperty(value = "赔偿标识")
    private String extraCode;

    @ApiModelProperty(value = "赔偿描述")
    private String extraValue;

    @ApiModelProperty(value = "赔偿金额")
    private BigDecimal extraRefundAmount;

    @ApiModelProperty(value = "退款方式 1-原路返回 2-线下汇款；3-退回至意向金")
    private Integer refundMethod;

    @ApiModelProperty(value = "退款方式描述信息")
    private Integer refundMethodStr;

    @ApiModelProperty(value = "开户名")
    private String accountName;

    @ApiModelProperty(value = "开户支行")
    private String accountBank;

    @ApiModelProperty(value = "银行账号")
    private String bankNo;
}

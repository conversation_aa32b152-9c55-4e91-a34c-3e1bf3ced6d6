package com.stbella.platform.order.api.upgrade.api;

import com.stbella.core.result.Result;
import com.stbella.platform.order.api.refund.res.UpGradeGoodsRes;

import java.util.List;

/**
 * 订单升级服务
 *
 * <AUTHOR>
 */
public interface OrderUpgradeService {

    /**
     * 查询可升级的商品列表
     *
     * @param orderId 订单ID
     * @return 可升级商品列表
     */
    Result<List<UpGradeGoodsRes>> queryUpgradeableGoods(Integer orderId);
}
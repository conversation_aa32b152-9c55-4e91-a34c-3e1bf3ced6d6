package com.stbella.platform.order.api.cashier;

import cn.hutool.json.JSONObject;
import com.stbella.core.result.Result;
import com.stbella.order.server.order.month.req.PayReqV2;
import com.stbella.order.server.order.month.request.standard.QrCodeRequest;
import com.stbella.platform.order.api.req.OmniOfflinePayRequest;
import com.stbella.platform.order.api.req.PosPayQrCodeRequest;
import com.stbella.platform.order.api.res.ReceiveQrcodeRes;

/**
 * @Author: ji<PERSON><PERSON>an
 * @CreateTime: 2024-07-05  11:23
 * @Description: 收银台接口
 */
public interface CashierCommandService {
    /**
     * 创建收银台聚合收款二维码
     * @param request
     * @return
     */
    Result<ReceiveQrcodeRes> createReceiveQrcode(QrCodeRequest request);

    /**
     * 创建pos支付二维码
     * pos 机扫此码，用户刷卡。
     * @param request
     * @return
     */
    Result<ReceiveQrcodeRes> createPosPayQrcode(PosPayQrCodeRequest request);

    /**
     * 在线支付
     * 三方，余额，产康金等。
     * @param request
     * @return
     */
    Result<JSONObject> onlinePay(PayReqV2 request);

    /**
     * 线下支付支付
     * @param request
     * @return
     */
    Result<Integer> offlinePay(OmniOfflinePayRequest request);
}

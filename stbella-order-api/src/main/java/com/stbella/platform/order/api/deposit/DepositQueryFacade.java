package com.stbella.platform.order.api.deposit;

import com.stbella.core.result.Result;
import com.stbella.order.server.order.month.req.OrderMonthIncomeQuery;
import com.stbella.order.server.order.month.res.DepositSettlementVO;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-08-07  11:39
 * @Description: 押金查询接口 兼容新老
 */
public interface DepositQueryFacade {

    /**
     * 押金支付查询
     * @param query
     * @return
     */
    Result<DepositSettlementVO> queryDeposit(OrderMonthIncomeQuery query);
}

package com.stbella.platform.order.api.contract.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "OrderContractPreviewVO", description = "合同预览表")
@Data
public class OrderContractPreviewVO implements Serializable {

    private static final long serialVersionUID = -1L;

    @ApiModelProperty("签约记录表")
    private Long id;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("预览地址")
    private String previewUrl;

    @ApiModelProperty("签约地址")
    private String signUrl;
}

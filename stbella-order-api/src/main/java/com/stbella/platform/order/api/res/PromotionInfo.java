package com.stbella.platform.order.api.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 活动信息
 */
@Data
@Accessors(chain = true)
public class PromotionInfo implements Serializable {

    @ApiModelProperty(value = "活动ID")
    private Integer promotionId;

    @ApiModelProperty(value = "活动名称")
    private String promotionName;

    @ApiModelProperty(value = "活动卡片名称")
    private String purchaseLimitationName;

    @ApiModelProperty(value = "选择类型，0-包含，1-任选")
    private Integer chooseType;

    @ApiModelProperty(value = "商品排序")
    private Integer sort;

    @ApiModelProperty(value = "活动更新时间")
    private String updateAt;

    @ApiModelProperty(value = "卡片UUID")
    private String uuid;

    @ApiModelProperty(value = "选择几份")
    private Integer optionalNumberCopies;

    @ApiModelProperty(value = "是否选择相同商品")
    private Integer chooseSameGoods;

}

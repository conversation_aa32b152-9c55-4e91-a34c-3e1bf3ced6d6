package com.stbella.platform.order.api.gift;

import com.stbella.core.result.Result;
import com.stbella.platform.order.api.gift.req.PassGiftCommand;
import com.stbella.platform.order.api.gift.req.RejectGiftCommand;
import com.stbella.platform.order.api.reduction.req.DecreaseCheckReq;
import com.stbella.platform.order.api.reduction.req.DecreaseReq;
import com.stbella.platform.order.api.req.CloseCommand;
import com.stbella.platform.order.api.res.DecreaseApprovalRes;
import com.stbella.platform.order.api.res.DecreaseCheckRes;

/**
 * @Author: ji<PERSON><PERSON>an
 * @CreateTime: 2024-05-27  13:25
 * @Description: 命令 - 礼赠相关操作
 */
public interface GiftCommandService {

    /**
     * 产康礼赠审核通过
     */
    Result<Void> pass(PassGiftCommand command);

    /**
     * 产康礼赠审核拒绝
     */
    Result<Void> reject(RejectGiftCommand command);

}

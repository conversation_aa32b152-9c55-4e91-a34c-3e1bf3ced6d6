package com.stbella.platform.order.api.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 订单额外信息， 其他未来走 req的 extension
 *
 * <AUTHOR>
 */
@Data
public class ExtraInfo implements Serializable {

    @ApiModelProperty(value = "是否为特殊订单")
    private boolean special;

    @ApiModelProperty(value = "特殊订单标签")
    private Integer orderTag;

    @ApiModelProperty(value = "特殊订单标签名称")
    private String orderTagName;

    @ApiModelProperty(value = "特殊事项备注")
    private String remark;

    @ApiModelProperty(value = "地址（发货场景）")
    private String address;

    @ApiModelProperty(value = "特殊标签凭证")
    private List<String> voucherUrlList;

    /**
     * 履约相关信息扩展信息
     */
    private List<CustomAttribute> fulfillExtraList;

    /**
     * 扩展信息（本接口覆盖父类的。）
     * 比如 续期，要传入父订单号
     */
    private Map<String, Object> extension;

}

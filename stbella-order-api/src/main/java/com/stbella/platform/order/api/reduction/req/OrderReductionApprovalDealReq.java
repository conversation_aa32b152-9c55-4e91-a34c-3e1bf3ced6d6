package com.stbella.platform.order.api.reduction.req;

import com.stbella.core.base.BasicReq;
import com.stbella.order.server.order.cts.enums.DecreaseTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@ApiModel(value = "OrderReductionApprovalDealReq", description = "减免审批后处理")
@EqualsAndHashCode(callSuper = true)
public class OrderReductionApprovalDealReq extends BasicReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "本地流程ID")
    private String localProcessId;

    @ApiModelProperty(value = "审批状态")
    private Integer authState;

    @ApiModelProperty(value = "减免审批类型，合同修改审批，折扣审批")
    private DecreaseTypeEnum type;


}

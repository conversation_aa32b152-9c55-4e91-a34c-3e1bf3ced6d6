package com.stbella.platform.order.api.req;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * pos机支付码-pos机扫码
 * pos 下单后，生成支付记录，用这个支付记录生成二维码去支付
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "PosPayQrCodeRequest", description = "pos支付码-pos机扫码")
public class PosPayQrCodeRequest extends BasicReq {

    @ApiModelProperty(value = "订单id")
    private Integer orderId;

    @ApiModelProperty(value = "收款记录编号")
    private String incomeSn;

    /**
     *  pos 从哪个渠道来的，
     *  PHP_MONTH(0, "母婴(PHP)"),
     *  JAVA_MONTH(1, "母婴(JAVA)"),
     */
    private Integer bizType;
}

package com.stbella.platform.order.api.contract.res;

import com.stbella.order.common.constant.TableFieldConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@ApiModel(value = "OrderContractSignRecordVO", description = "合同记录表")
@Data
public class OrderContractSignRecordVO implements Serializable {
    private static final long serialVersionUID = -1L;

    @ApiModelProperty("签约记录表")
    private Long id;

    @ApiModelProperty("客户ID（ecp库的tab_client表主键）")
    private Integer clientUid;

    @ApiModelProperty("销售员id（ecp库user表主键id）")
    private Integer staffId;

    @ApiModelProperty("门店ID(ecp库cfg_store表的主键id)")
    private Long storeId;

    @ApiModelProperty("签约类型：1订单， .......（未来可增加其他签约类型）")
    private Integer guideType;

    @ApiModelProperty("关联id（签约类型为订单，此处为订单id）")
    private Long guideId;

    @ApiModelProperty("签字区域：图片格式的签约内容：URL链接")
    private String contractImageSignature;

    @ApiModelProperty("整个html的图片格式：签约内容：URL链接")
    private String contractImageHtml;

    @ApiModelProperty("签约模版url")
    private String contractTempUrl;

    @ApiModelProperty("对应task_id 目前只附件合同有效")
    private Long taskId;

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("法大大合同下载链接")
    private String downloadUrl;

    @ApiModelProperty("合同名称")
    private String contractName;

    @ApiModelProperty("法大大合同查看链接")
    private String viewpdfUrl;

    @ApiModelProperty("合同状态 0 =未签署 1=签署中 2=已归档")
    private Integer contractStatus;

    @ApiModelProperty("合同类型 1=E签宝合同 2=旧合同 ")
    private Integer contractType;

    @ApiModelProperty("模板类型 1=订单类 2=附件类")
    private Integer templateType;

    @ApiModelProperty("模板关联合同类型1育婴师-中介-贝康居间服务协议 2雇主-中介-母婴护理-贝康母婴护理委托服务协议 3雇主-中介-育婴师-贝康育婴师服务委托协议 4育婴师-套餐-贝康服务合作协议 5雇主-套餐-母婴护理-贝康母婴护理委托协议 6雇主-套餐-育婴师-贝康育婴师服务委托协议 7育婴师培训课程确认书协议 8月子合同 10.外派合同  11.小月子合同 12.总部与客户和解保密协议 13.门店与客户和解保密协议 14.礼赠协议 15.补充协议  16.无忧宝宝附属协议  17.门店与个人新媒体置换合同 21.预约协议 22.授权委托书 23.订单折扣保密协议 24.合同解除协议 25.离馆协议")
    private Integer templateContractType;

    @ApiModelProperty("模板id")
    private Long templateId;

    @ApiModelProperty("创建时间")
    private Long createdAt;

    @ApiModelProperty("最近更新时间")
    private Long updatedAt;

    @ApiModelProperty("E签宝签署流程id")
    private String esignFlowId;

    @ApiModelProperty("E签宝文件id。")
    private String esignFileId;

    @ApiModelProperty("E签宝文件名称")
    private String esignFileName;

    @ApiModelProperty("E签宝合同查看长链接")
    private String contractLongUrl;

    @ApiModelProperty("E签宝合同查看短链接")
    private String contractShortUrl;

    @ApiModelProperty("合同签订入口0=未知;1=h5;2=微信;3=支付宝;4=链接签短信/邮箱")
    private Integer signFrom;

    @ApiModelProperty("认证类型：0=中国大陆(不包含港澳台)-实名认证;1=邮箱")
    private Integer authType;

    @ApiModelProperty("认证邮箱地址")
    private String email;

    @ApiModelProperty("上传纸质合同原因")
    private String reason;

    @ApiModelProperty("合同图片url数组")
    private List<String> img;

    @ApiModelProperty(value = "新老系统标志;0或null-老;1-新")
    private Integer oldOrNew;

    @ApiModelProperty(value = TableFieldConstant.templateContractType)
    private String templateContractTypeStr;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "订单类型")
    private Integer orderType;

    @ApiModelProperty(value = "合同类型 1=E签宝合同 2=旧合同 3=纸质合同 4=合同套餐附件pdf")
    private String contractTypeName;

    @ApiModelProperty(value = "合同类型 1=>主合同，2=>附件类，3=>附属类：补充协议 4=>附件")
    private String templateTypeName;

    private String operatorName;

    @ApiModelProperty(value = "签署时间")
    private LocalDateTime signTime;

    @ApiModelProperty(value = "合同文件url")
    private String url;

    @ApiModelProperty(value = "签署主体名称")
    private String signerEntityName;

    @ApiModelProperty(value = "签章机构id")
    private String orgId;

}

package com.stbella.order.server.contract.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 签约记录表
 * </p>
 *
 * <AUTHOR> @since 2021-11-03
 */
@Data
@ApiModel(value = "ESignCancleFlowRecordVO对象", description = "合同签署撤销")
public class ESignCancleFlowRecordVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "E签宝签署流程id")
    private String esignFlowId;

    @ApiModelProperty(value = "撤销是否成功,true|false")
    private Boolean isSuccess;

    @ApiModelProperty(value = "原因")
    private String msg;

}

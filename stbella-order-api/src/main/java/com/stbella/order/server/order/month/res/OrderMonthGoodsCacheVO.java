package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 月子套餐缓存获取
 * </p>
 *
 * <AUTHOR> @since 2022-10-29
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "OrderMonthGoodsCacheVO对象", description = "月子套餐缓存获取")
public class OrderMonthGoodsCacheVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "套餐父类ID")
    private Integer parentId;

    @ApiModelProperty(value = "套餐ID")
    private Integer goodsId;

    @ApiModelProperty(value = "套餐名称")
    private String goodsName;

    @ApiModelProperty(value = "ecp房型配置")
    private Integer ecpRoomType;

    @ApiModelProperty(value = "房型Id")
    private Integer roomId;

    @ApiModelProperty(value = "房型名称")
    private String roomName;

    @ApiModelProperty(value = "附件名称")
    private String attachmentName;

    @ApiModelProperty(value = "附件url")
    private String attachmentUrl;

    @ApiModelProperty(value = "套餐天数")
    private Integer serviceDays;

    @ApiModelProperty(value = "规格ID")
    private Integer skuId;

    @ApiModelProperty(value = "原价")
    private BigDecimal goodsPriceOrgin;

    @ApiModelProperty(value = "房型原价")
    private BigDecimal roomPrice;

    @ApiModelProperty(value = "成本价")
    private BigDecimal goodsPriceCost;

    @ApiModelProperty(value = "套餐应收金额")
    private BigDecimal goodsReceivableAmount;

    @ApiModelProperty(value = "商品自定义属性")
    private String definedProperty;

}

package com.stbella.order.server.order.month.res;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class OrderProductionCardExtendRes implements Serializable {

    private static final long serialVersionUID = 3972704783119301537L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 1=通卡 0=次卡
     */
    private Integer type;

    /**
     * 卡项商品名称，通卡对应的是组合名称，次卡对应的是商品名称
     */
    private String name;

    /**
     * 数量，通卡对应对应公用数量，次卡对应商品的数量
     */
    private Integer num;

    /**
     * 通卡存的是子商品的数据，次卡存的是该sku商品信息
     */
    private List<OrderProductionCardContentRes> contentList;

}

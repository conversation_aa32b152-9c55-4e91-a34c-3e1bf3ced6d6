package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OrderGoodsVO {

    @ApiModelProperty(value = "商品缩略图")
    private String goodsImage;

    private String code;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "与goods表的good_type保持一致")
    private Integer goodsType;

    @ApiModelProperty(value = "购买数量")
    private Integer goodsNum;

    @ApiModelProperty(value = "每份包含的商品数量")
    private Integer skuNum;

    @ApiModelProperty(value = "份数")
    private Integer num;

    @ApiModelProperty(value = "商品原单价")
    private BigDecimal goodsPriceOrgin;

    @ApiModelProperty(value = "商品原总价")
    private BigDecimal goodsPriceOrginTotal;

    @ApiModelProperty(value = "应付单价")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "应付总金额")
    private BigDecimal payTotalAmount;
}

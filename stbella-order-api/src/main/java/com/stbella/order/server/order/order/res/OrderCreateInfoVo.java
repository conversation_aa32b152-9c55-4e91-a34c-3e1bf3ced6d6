package com.stbella.order.server.order.order.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "OrderCreateInfoVo", description = "订单下单时的基本信息")
public class OrderCreateInfoVo implements Serializable {


    @ApiModelProperty(value = "订单ID")
    private Integer orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    @ApiModelProperty(value = "全局用户ID")
    private Integer basicUid;

    @ApiModelProperty(value = "买家客户ID（ecp库的tab_client表主键）")
    private Integer clientUid;

    @ApiModelProperty(value = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createdAt;

    @ApiModelProperty(value = "门店ID")
    private Integer storeId;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "门店别名")
    private String storeNameAlias;

    @ApiModelProperty(value = "门店类型")
    private Integer storeType;

    @ApiModelProperty(value = "套餐名称")
    private String goodsName;

    @ApiModelProperty(value = "新老系统标志;0或null-老;1-新")
    private Integer oldOrNew;
}

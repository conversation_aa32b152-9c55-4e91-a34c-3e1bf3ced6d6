package com.stbella.order.server.order.month.req.quotation;

import com.stbella.core.base.BasicReq;
import com.stbella.order.common.enums.core.CartSceneEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class CreateQuotationReq extends BasicReq {

    private static final long serialVersionUID = -7879274558078733800L;

    @ApiModelProperty(value = "购物ID")
    @NotNull(message = "购物车Id不能为空")
    private List<Integer> cartIdList;

    @ApiModelProperty(value = "场景")
    private Integer scene = CartSceneEnum.QUOTATION.code();
}

package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "小程序-我的订单详情-宝宝信息")
public class ClientOrderBabyDetailVO implements Serializable {

    @ApiModelProperty(value = "宝宝姓名")
    private String name;

    @ApiModelProperty(value = "宝宝性别")
    private String babyGender;

    @ApiModelProperty(value = "出生体重")
    private String bornWeight;

    @ApiModelProperty(value = "黄疸值")
    private String jaundiceValue;

}

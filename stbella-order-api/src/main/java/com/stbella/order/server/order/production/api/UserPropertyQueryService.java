package com.stbella.order.server.order.production.api;

import com.stbella.core.result.Result;
import com.stbella.order.server.order.production.req.ProductionVerificationStatReq;
import com.stbella.order.server.order.production.req.UserPropertyOptionsQuery;
import com.stbella.order.server.order.production.req.UserPropertyQuery;
import com.stbella.order.server.order.production.res.ProductionGoodsVO;
import com.stbella.order.server.order.production.res.ProductionPropertyListVo;
import com.stbella.order.server.order.production.res.ProductionPropertyVerificationOptionsVO;
import com.stbella.order.server.order.production.res.ProductionVerificationStatVo;

import java.util.List;

/**
 * @description:
 * @author: Seven
 * @time: 2022/10/14 15:27
 */
public interface UserPropertyQueryService {

    /**
     * 通过basicUid 获取用户资产
     *
     * @return Result<ProductionPropertyListVo>
     */
    Result<ProductionPropertyListVo> queryPropertyByBasicUid(UserPropertyQuery query);

    Result<ProductionPropertyVerificationOptionsVO> queryVerificationOptions(UserPropertyOptionsQuery query);

    /**
     * 查询产康核销统计
     *
     * @param req
     * @return
     */
    Result<ProductionVerificationStatVo> queryProductionVerificationStat(ProductionVerificationStatReq req);

    /**
     * 查询用户所有订单的产康资产
     *
     * @param query
     * @return
     */
    Result<List<ProductionGoodsVO>> queryCombineList(UserPropertyQuery query);
}

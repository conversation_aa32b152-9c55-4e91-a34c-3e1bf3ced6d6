package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 订单支付记录
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@Data
@ApiModel(value = "RefundRecordDetailVO对象", description = "订单退款详情记录")
public class RefundRecordDetailVO {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @ApiModelProperty(value = "订单id(he_order表主键)")
    private Integer orderId;

    @ApiModelProperty(value = "订单商品表ID/支付记录ID(income_record中的id)")
    private Integer orderGoodId;

    @ApiModelProperty(value = "退款订单编号")
    private String refundOrderSn;

    @ApiModelProperty(value = "申请退款金额（分）")
    private Integer applyAmount;

    @ApiModelProperty(value = "申请退款金额（元）")
    private BigDecimal applyAmountY;

    @ApiModelProperty(value = "最终退款方式(与income_record中的pay_type保持一致)，1=微信, 2=支付宝, 3=线下支付, 4=中信银行支付宝支付, 5=中信银行微信支付  8=在线pos机支付  9=C端小程序支付 10=其他")
    private Integer refundType;

    @ApiModelProperty(value = "最终退款方式(与income_record中的pay_type保持一致)，1=微信, 2=支付宝, 3=线下支付, 4=中信银行支付宝支付, 5=中信银行微信支付  8=在线pos机支付  9=C端小程序支付 10=其他")
    private String refundTypeStr;

    @ApiModelProperty(value = "同意时间")
    private Long agreeAt;

    @ApiModelProperty(value = "实际退款金额")
    private Integer actualAmount;

    @ApiModelProperty(value = "实际退款金额（元）")
    private BigDecimal actualAmountY;

    @ApiModelProperty(value = "退款状态，1：审批中 2：审批失败  3：审批成功/等待打款/待确认 4：退款已到帐/已确认 5：到账失败/已拒绝")
    private Integer status;

    @ApiModelProperty(value = "退款状态中文")
    private String statusStr;

    @ApiModelProperty(value = "支付金额(单位分)")
    private Integer income;

    @ApiModelProperty(value = "支付金额（元）")
    private BigDecimal incomeY;

    @ApiModelProperty(value = "支付时间")
    private Integer payTime;

    @ApiModelProperty(value = "对账状态：0未对账，1正常，2异常")
    private Integer checkStatus;

    @ApiModelProperty(value = "第三方交易流水号")
    private String transactionId;

    @ApiModelProperty(value = "收款标识 1)意向金；2）预付款；3）尾款；4）押金")
    private Integer receiptType;

    @ApiModelProperty(value = "创建时间")
    private Long createdAt;

    @ApiModelProperty(value = "完成时间/到账时间")
    private Long finishAt;

    @ApiModelProperty(value = "退款成功时间（YYYY-MM-DD HH:mm:ss）")
    private String refundDate;

    @ApiModelProperty("退款申请时间（YYYY-MM-DD HH:mm:ss）")
    private String refundApplyDate;

    @ApiModelProperty(value = "开户名,线下支付要填")
    private String accountName;

    @ApiModelProperty(value = "开户支行,线下支付要填")
    private String accountBank;

    @ApiModelProperty(value = "银行账号,线下支付要填")
    private String bankNo;

    @ApiModelProperty(value = " 退款原因(类型)一级：1=正常退款;2=⾮正常退款（客诉）")
    private Integer refundReasonType;

    @ApiModelProperty(value = "退款原因(0-99正常退款原因) (100以上是非正常退款原因):退款原因:0-默认 1-宝妈身体原因 2-宝宝原因 3-疫情政策 4-距离原因 5-家人因素 6-环境设施 7-馆内服务 8-用户需求 9-套餐变更 10-门店承接问题 11-客诉赔偿 12-退押金 13-其他    n100-月子餐问题 101-护理问题 102-产康问题 103-馆内活动问题 104-第三方(宝宝艺术照)问题 105-服务态度问题 106其他")
    private Integer refundReason;

    @ApiModelProperty(value = "退款原因具体值")
    private String refundReasonStr;

    @ApiModelProperty(value = "退款具体原因")
    private String remark;

    @ApiModelProperty(value = "  是否存在违约金, 0-默认 1-存在 2-不存在")
    private Integer hasLiquidatedDamages;

    @ApiModelProperty(value = "礼赠是否失效;0-不失效(默认),1-失效")
    private Integer giftExtendDisabled;

    @ApiModelProperty(value = "图片凭证")
    private List<String> images;

    @ApiModelProperty(value = "审批Id")
    private String approveId;

    @ApiModelProperty(value = "审批状态:0:进行中；1同意,2拒绝,3撤销,4发起失败")
    private Integer approveStatus;


    @ApiModelProperty(value = "货币码")
    private String currency;

}

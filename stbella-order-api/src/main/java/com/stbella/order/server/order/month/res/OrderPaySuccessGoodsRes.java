package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class OrderPaySuccessGoodsRes implements Serializable {

    private static final long serialVersionUID = 5712309850485098714L;

    @ApiModelProperty(value = "he_order_goods表的ID或者he_order_production_extend中ID或者he_order_gift_extend中ID")
    private Integer id;

    @ApiModelProperty(value = "he_order_production_card_extend表的ID")
    private List<Integer> cardIdList;

    @ApiModelProperty(value = "商品Id")
    private Integer goodsId;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "赠送来源 source > 6 表示资产中心-赠送 否则表示礼赠 source < 0 表示购买")
    private Integer source;

    @ApiModelProperty(value = "商品类型")
    private String goodsGiftType;

    @ApiModelProperty("产康主供应商")
    private Long mainSupplierId;

    @ApiModelProperty("商品购买总/赠送数量")
    private Integer goodsNum;

    @ApiModelProperty("实付分摊总额(元)")
    private BigDecimal payGoodsPriceTotal;

    @ApiModelProperty(value = "产康金分摊总额(元)")
    private BigDecimal payProductionPriceTotal;

    @ApiModelProperty("单次实付分摊金额(元)")
    private BigDecimal payGoodsPrice;

    @ApiModelProperty(value = "单次产康金分摊金额(元)")
    private BigDecimal payProductionPrice;

    @ApiModelProperty(value = "SKU ID")
    private Integer skuId;

    @ApiModelProperty(value = "SKU名称")
    private String skuName;

    @ApiModelProperty(value = "商品可核销的总数量")
    private Integer numTotal;

    @ApiModelProperty(value = "商品已使用数量")
    private Integer usedNum;

    @ApiModelProperty(value = "状态: 0-冻结 1-正常 2-已失效")
    private Integer status;

    @ApiModelProperty(value = "商品类型")
    private Integer goodsType;

    @ApiModelProperty(value = "服务类型 0自营 1三方")
    private Integer serviceType;

    @ApiModelProperty(value = "产康金抵扣规则 -1:不抵扣 0:全额抵扣 1:满10000减2000")
    private Integer productionDiscountRuleType;

    @ApiModelProperty(value = "商品单价")
    private BigDecimal goodsPrice;

    @ApiModelProperty(value = "商品SKU单价")
    private BigDecimal goodsSkuPrice;

    @ApiModelProperty(value = "产康金抵扣 0-不可抵扣 1-可抵扣")
    private Integer produceAmountDeduction;

    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "时间结算点：0-按收款结算；1-按核销结算")
    private Integer timeClearingPoint;

    /**
     * 序号（关联用此字段）退款关联
     */
    private String orderGoodsSn;

    /**
     *数据来源：0表示正常，1表示后台核销补录
     */
    private Integer dataSource;

    /**
     * 只有购买的商品查询当前数据信息
     * 新订单数据来源：0表示正常，1表示后台核销补录
     */
    private Map<Integer, Integer> newOrderDataSourceMap;


    @ApiModelProperty(value = "he_order_production_card_extend表中对应的数据明细")
    private List<OrderProductionCardExtendRes> orderProductionCardExtendList;

}

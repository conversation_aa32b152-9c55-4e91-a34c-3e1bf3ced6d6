package com.stbella.order.server.contract.service.month;

import com.stbella.order.server.contract.req.OrderParamHistoryPushDTO;
import com.stbella.order.server.order.month.res.MonthOrderParamHistoryVO;

import java.util.HashMap;
import java.util.List;

/**
 * <p>
 * 订单参数历史记录 服务类
 * </p>
 *
 * <AUTHOR>
/**
 * 废弃 已迁移 stbella-contract 服务
 * @see com.stbella.contract.api.MonthOrderParamHistoryService
 */
public interface MonthOrderParamHistoryService  {

    /**
     * 保存历史参数
     * 历史参数起效关联合同
     * 搜索有效的历史参数
     */

    Boolean pushOrderParamHistory(OrderParamHistoryPushDTO dto);

    /**
     * 获取模版所需要的数据
     *
     * @param orderId 订单id
     * @param type    参数类型 1:合同 2:补充协议
     * @return 合同模版所需参数
     */
    List<MonthOrderParamHistoryVO> getContractTemplateData(Long orderId, Integer type);

    /**
     * 订单所有的有效参数列表
     *
     * @param orderId 订单id
     * @param valid   生效状态,为null则忽略该条件
     * @return 参数列表
     */
    List<MonthOrderParamHistoryVO> list(Long orderId, Integer valid);

    /**
     * 获取合同对应的参数列表
     *
     * @param contractId 合同id
     * @return 参数list
     */
    List<MonthOrderParamHistoryVO> listByContractId(Long contractId);

    /**
     * 生效或失效协议对应的参数
     *
     * @param orderId    订单id
     * @param contractId 协议或合同id
     * @param type       类型 1:合同2:补充协议
     * @param valid      1:生效 2:失效
     * @return 操作结果
     */
    Boolean valid(Long orderId, Long contractId, Integer type, Integer valid);

    /**
     * 根据合同ID获取合同填充参数
     */
    HashMap<String, String> getContractParamByContractId(Integer contractId);

    /**
     * 添加合同ID到合同参数历史表(老订单迁移主合同id不存在的情况)
     */
    Boolean setContractId(Long orderId, Integer type, Integer contractId);
    /**
     * 订单所有的有效参数列表
     *
     * @param orderId 订单id
     * @param valid   生效状态,为null则忽略该条件
     * @param type   1,2
     * @return 参数列表
     */
    List<MonthOrderParamHistoryVO> list(Long orderId, Integer valid,List<Integer> type);


}



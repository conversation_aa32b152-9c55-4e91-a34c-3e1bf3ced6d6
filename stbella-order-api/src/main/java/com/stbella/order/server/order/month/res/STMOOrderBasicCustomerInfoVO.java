package com.stbella.order.server.order.month.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "OrderBasicCustomerInfoVO对象", description = "订单后台-订单详情-基本信息-客户信息")
public class STMOOrderBasicCustomerInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户ID")
    @NotNull(message = "客户ID不能为空")
    private Integer clientUid;

    @ApiModelProperty(value = "全局用户ID")
    @NotNull(message = "全局用户ID不能为空")
    private Integer basicUid;

    @ApiModelProperty(value = "客户姓名")
    @NotNull(message = "客户姓名不能为空")
    private String name;

    @ApiModelProperty(value = "手机号码")
    @NotNull(message = "手机号码不能为空")
    private String phone;

    @ApiModelProperty(value = "预产期")
    @NotNull(message = "预产期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date predictBornDate;

    @ApiModelProperty(value = "实际入住日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date roomCheckInDate;

    /**
     * @see com.stbella.order.server.contract.enums.IdTypeEnum
     */
    @ApiModelProperty(value = "证件类型 0=中国大陆身份证 1=护照 2=香港证 3=澳门证 4=台湾证")
    @NotNull(message = "证件类型不能为空")
    private Integer certType;

    @ApiModelProperty(value = "证件类型中文 0=中国大陆身份证 1=护照 2=香港证 3=澳门证 4=台湾证")
    private String certTypeStr;

    @ApiModelProperty(value = "证件号")
    @NotNull(message = "证件号不能为空")
    private String idCard;

    @ApiModelProperty(value = "证件照正面 url")
    @NotNull(message = "证件正面 不能为空")
    private String idCardFront;

    @ApiModelProperty(value = "证件照背面 url")
    @NotNull(message = "证件照背面不能为空")
    private String idCardBack;

    @ApiModelProperty(value = "枚举星座类型： ‘0白羊座’,’1金牛座’,’2双子座’,’3巨蟹座’,’4狮子座’,’5处女座’,’6天秤座’,’7天蝎座’,’8射手座’,’9摩羯座’,’10水瓶座’,’11双鱼座'")
    private Integer constellationType;

    @ApiModelProperty(value = "星座中文")
    private String constellationTypeStr;

    @ApiModelProperty(value = "省份。-1为其他")
    @NotNull(message = "省份ID不能为空")
    private Integer province;

    @ApiModelProperty(value = "城市。-1为其他")
    @NotNull(message = "城市ID不能为空")
    private Integer city;

    @ApiModelProperty(value = "地区。-1为其他")
    @NotNull(message = "地区ID不能为空")
    private Integer region;

    @ApiModelProperty(value = "详细地址")
    @NotNull(message = "详细地址不能为空")
    private String address;

    @ApiModelProperty(value = "省市区拼接详细地址")
    private String allAddress;

    /**
     * @see com.stbella.order.server.order.month.enums.BirthTypeEnum
     */
    @ApiModelProperty(value = "胎数")
    private Integer fetusNum;

    @ApiModelProperty(value = "胎数中文")
    private String fetusNumStr;

    @ApiModelProperty(value = "胎次")
    private Integer bornNum;

    @ApiModelProperty(value = "胎次中文")
    private String bornNumStr;

    @ApiModelProperty(value = "预计入住时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date wantIn;

    @ApiModelProperty(value = "分娩医院")
    @NotNull(message = "分娩医院不能为空")
    private String hospital;

    @ApiModelProperty(value = "紧急联系人名字")
    @NotNull(message = "紧急联系人名字不能为空")
    private String urgentName;

    @ApiModelProperty(value = "紧急联系人号码")
    @NotNull(message = "紧急联系人号码不能为空")
    private String urgentPhone;

    @ApiModelProperty(value = "客户职业")
    @NotNull(message = "客户职业不能为空")
    private String profession;

    @ApiModelProperty(value = "年龄")
    private Integer age;

    @ApiModelProperty(value = "孕第几周")
    private Integer gestationWeekNow;

    /**
     * @see com.stbella.order.server.order.month.enums.BloodTypeEnum
     */
    @ApiModelProperty(value = "血型：0未知，1A，2B，3O，4AB, 5RH")
    private Integer bloodType;

    @ApiModelProperty(value = "血型中文：0未知，1A，2B，3O，4AB, 5RH")
    private String bloodTypeStr;

    /**
     * @see com.stbella.order.server.order.month.enums.RelationshipWithEnum
     */
    @ApiModelProperty(value = "紧急联系人与客户关系：0宝妈，1宝爸，2亲属，3夫妻，4母女，5父女")
    @NotNull(message = "紧急联系人与客户关系不能为空")
    private Integer relationWithClient;

    @ApiModelProperty(value = "紧急联系人与客户关系中文：0宝妈，1宝爸，2亲属，3夫妻，4母女，5父女")
    private String relationWithClientStr;

    /**
     * @see com.stbella.order.server.order.month.enums.PhoneVerifyEnum
     */
    @ApiModelProperty(value = "认证状态 0=手机号未验证过;1=已验证")
    @NotNull(message = "认证状态不能为空")
    @Range(min = 1, max = 1, message = "手机号未验证过")
    private Integer isPhoneVerify;

    @ApiModelProperty(value = "认证状态中文 0=手机号未验证过;1=已验证")
    private String isPhoneVerifyStr;

    @ApiModelProperty(value = "是否有推荐码 0-没有; 1-有")
    @NotNull(message = "是否有推荐码不能为空")
    private Integer isHaveQrCode;

    @ApiModelProperty(value = "是否有推荐码中文 0-没有; 1-有")
    private String isHaveQrCodeStr;

    @ApiModelProperty(value = "推荐码")
    private String qrCode;

    /**
     * @see com.stbella.order.common.enums.month.FromTypeEnum
     */
    @ApiModelProperty(value = "渠道来源：0未知-废弃，1(400大众点评)，2(400小红书)，3(400微博), 4(400线下广告)，5(400未告知)，6(400朋友推荐)，7(400未告知），8（400公众号/小程序），9（400官网），10（400直播），11（非400公众号），12（非400小红书），13（非400官网/小程序），14（非400微博），15（大众点评预约），16（大众点评在线咨询），17（新增小助手）,18（小程序预约） 19-口碑介绍, 20-合作渠道, 21-线下广告, 22-线下活动, 23-大众点评, 24-百度/谷歌, 25-官网, 26-小红书, 27-微信公众号, 28-微博, 29-其他, 30-微信小程序, 31-抖音, 32-2021抖音活动, 33-2021微信广告活动, 34-已入馆宝妈, 35-朋友圈广告, 36-医疗渠道, 37-小贝拉私域'")
    private Integer fromType;

    @ApiModelProperty(value = "渠道来源中文 ：0未知-废弃，1(400大众点评)，2(400小红书)，3(400微博), 4(400线下广告)，5(400未告知)，6(400朋友推荐)，7(400未告知），8（400公众号/小程序），9（400官网），10（400直播），11（非400公众号），12（非400小红书），13（非400官网/小程序），14（非400微博），15（大众点评预约），16（大众点评在线咨询），17（新增小助手）,18（小程序预约） 19-口碑介绍, 20-合作渠道, 21-线下广告, 22-线下活动, 23-大众点评, 24-百度/谷歌, 25-官网, 26-小红书, 27-微信公众号, 28-微博, 29-其他, 30-微信小程序, 31-抖音, 32-2021抖音活动, 33-2021微信广告活动, 34-已入馆宝妈, 35-朋友圈广告, 36-医疗渠道, 37-小贝拉私域'")
    private String fromTypeStr;

    /***
     * @see com.stbella.order.server.order.month.enums.CardVerifyEnum
     */
    @ApiModelProperty("证件认证结果, 0:未认证 1:认证成功 2:认证失败 3:无需认证")
    private Integer isCardVerify;

    @ApiModelProperty("证件认证结果中文, 0:未认证 1:认证成功 2:认证失败 3:无需认证")
    private String isCardVerifyStr;

    @ApiModelProperty(value = "手机号-密文")
    private String phoneCiphertext;


    /**
     * 紧急联系人明文
     */
    private String urgentPhoneCiphertext;

    /**
     * 证件照正面 url 明文
     */
    private String idCardFrontCiphertext;

    /**
     * 证件照背面 url 密文
     */
    private String idCardBackCiphertext;
    /**
     * 证件号 密文
     */
    private String idCardCiphertext;

}

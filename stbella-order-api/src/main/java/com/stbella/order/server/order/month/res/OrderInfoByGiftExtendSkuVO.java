package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@ApiModel(value = "OrderInfoByGiftExtendSkuVO对象", description = "订单-详情-额外礼赠具体商品信息")
public class OrderInfoByGiftExtendSkuVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分类ID")
    private Integer categoryFront;

    @ApiModelProperty(value = "商品Id")
    private Integer goodsId;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "规格ID")
    private Integer skuId;

    @ApiModelProperty(value = "规格名称")
    private String skuName;

    @ApiModelProperty(value = "商品类型")
    private String natureTypeTitle;

    @ApiModelProperty(value = "商品类目")
    private String categoryBackTitle;

    @ApiModelProperty(value = "商品分组")
    private String goodsTypeTitle;

    @ApiModelProperty(value = "数量（产康金金额）")
    private Integer quantity;

    @ApiModelProperty(value = "数量（规格数量）")
    private Integer skuNum;

    @ApiModelProperty(value = "分类：1=产康金 2=房型升级 3=续住 4=节日费用 5=家属房 6=产康服务 7=实物商品")
    private Integer type;

    @ApiModelProperty(value = "单价")
    private String unitPrice;

    @ApiModelProperty(value = "总价")
    private String totalPrice;

    @ApiModelProperty(value = "单个成本")
    private String unitCost;

    @ApiModelProperty(value = "总成本")
    private String totalCost;

    @ApiModelProperty(value = "分组名称")
    private String categoryName;

}

package com.stbella.order.server.order.month.res;

import com.stbella.order.common.utils.ExcelColumn;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CareForHomeOrderExportVO implements Serializable {

    private static final long serialVersionUID = -5028373277433690L;

    @ExcelColumn(value = "序号", isMerge = true, location = 0)
    private Integer id;

    @ExcelColumn(value = "门店", isMerge = true, location = 1)
    private String storeName;

    @ExcelColumn(value = "订单类型", isMerge = true, location = 2)
    private String orderTypeStr;

    @ExcelColumn(value = "订单号", isMerge = true, location = 3)
    private String orderSn;

    @ExcelColumn(value = "客户姓名", isMerge = true, location = 4)
    private String customerName;

    @ExcelColumn(value = "客户Id", isMerge = true, location = 5)
    private String basicId;

    @ExcelColumn(value = "订单标签", isMerge = true, location = 6)
    private String sceneStr;

    @ExcelColumn(value = "签单类型", isMerge = true, location = 7)
    private String signBillTypeStr;

    @ExcelColumn(value = "预计上户时间", isMerge = true, location = 8)
    private String serveStartDate;

    @ExcelColumn(value = "预计上户午时间", isMerge = true, location = 9)
    private String serveEndDate;

    @ExcelColumn(value = "订单原价", isMerge = true, location = 10)
    private BigDecimal orderAmount;

    @ExcelColumn(value = "折扣金额", isMerge = true, location = 11)
    private BigDecimal discountAmount;

    @ExcelColumn(value = "签单金额", isMerge = true, location = 12)
    private BigDecimal calPayableAmount;

    @ExcelColumn(value = "订单折扣率", isMerge = true, location = 13)
    private String orderDiscount;

    @ExcelColumn(value = "订单毛利率", isMerge = true, location = 14)
    private String grossProfitMargin;

    @ExcelColumn(value = "已付金额", isMerge = true, location = 15)
    private BigDecimal realAmount;

    @ExcelColumn(value = "审阅中金额", isMerge = true, location = 16)
    private BigDecimal income;

    @ExcelColumn(value = "累计仅退款金额", isMerge = true, location = 17)
    private BigDecimal totalOnlyRefundAmount;

    @ExcelColumn(value = "累计退回重付金额", isMerge = true, location = 18)
    private BigDecimal totalRefreshPayAmount;

    @ExcelColumn(value = "累计减免总金额", isMerge = true, location = 19)
    private BigDecimal totalReductionAmount;

    @ExcelColumn(value = "待付金额", isMerge = true, location = 20)
    private BigDecimal remainingAmount;

    @ExcelColumn(value = "意向金支付金额", isMerge = true, location = 21)
    private String balanceAmount;

    @ExcelColumn(value = "订单折扣类型", isMerge = true, location = 22)
    private String orderTagName;

    @ExcelColumn(value = "其他备注说明", isMerge = true, location = 23)
    private String remark;

    @ExcelColumn(value = "订单所有人", isMerge = true, location = 24)
    private String staffName;

    @ExcelColumn(value = "创建时间", isMerge = true, location = 25)
    private String createdAt;

    @ExcelColumn(value = "首次支付时间", isMerge = true, location = 26)
    private String payFirstTime;

    @ExcelColumn(value = "商品名称", location = 27)
    private String goodsName;

    @ExcelColumn(value = "商品规格", location = 28)
    private String skuName;

    @ExcelColumn(value = "套餐等级", location = 29)
    private String goodsLevel;

    @ExcelColumn(value = "商品数量", location = 30)
    private Integer goodsNum;

}

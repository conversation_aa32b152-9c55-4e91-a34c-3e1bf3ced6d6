package com.stbella.order.server.order.month.service;

import com.stbella.order.server.order.month.dto.PageDTO;
import com.stbella.order.server.order.month.req.ComplaintsOrderReq;
import com.stbella.order.server.order.month.req.ComplaintsPicpAddReq;
import com.stbella.order.server.order.month.req.ComplaintsPicpInfoReq;
import com.stbella.order.server.order.month.req.ComplaintsPicpPageReq;
import com.stbella.order.server.order.month.res.*;
import com.stbella.order.server.order.order.res.OrderComplaintsExport;

import java.util.List;

public interface ComplaintsPicpService {

    /**
     * 查询当前客户所有订单列表
     * @param req
     * @return
     */
    PageDTO<WechatMyOrderNewVO> queryOrderList(ComplaintsOrderReq req);

    /**
     * 客诉工单添加
     * @param req
     * @return
     */
    Boolean add(ComplaintsPicpAddReq req);

    /**
     * 客诉工单分页查询
     * @param req
     * @return
     */
    PageDTO<ComplaintsPicpPageVO> page(ComplaintsPicpPageReq req);

    /**
     * 客诉工单详情查询
     * @param req
     * @return
     */
    ComplaintsPicpInfoVO info(ComplaintsPicpInfoReq req);

    /**
     * 客诉工单处理结果
     * @param req
     * @return
     */
    ComplaintsPicpHandleResultVO handleResult(ComplaintsPicpInfoReq req);

    /**
     * 客诉工单处理意见
     * @param req
     * @return
     */
    ComplaintsPicpHandleOpinionVO handleOpinion(ComplaintsPicpInfoReq req);

    /**
     * 客诉导出
     *
     * @param req
     * @return
     */
    List<OrderComplaintsExport> export(ComplaintsPicpPageReq req);

    /**
     * 查询客户投诉退款类型枚举
     * @return
     */
    List<SelectRespVO> queryCustomerComplaintsRefundEnum();
}

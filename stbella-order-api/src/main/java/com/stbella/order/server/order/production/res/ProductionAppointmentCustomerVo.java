package com.stbella.order.server.order.production.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description:
 * @author: Seven
 * @time: 2022/10/10 17:09
 */
@Data
@ApiModel(value = "预约页面用户数据", description = "预约页面用户数据")
public class ProductionAppointmentCustomerVo implements Serializable {

    @ApiModelProperty("客户ID")
    private String clientId;

    @ApiModelProperty("头像")
    private Integer avatarUrl;

    @ApiModelProperty("名称")
    private Date clientName;

    @ApiModelProperty("手机号")
    private Date clientPhone;

    @ApiModelProperty("门店编号")
    private Date storeId;

    @ApiModelProperty("门店名称")
    private Date storeName;
}

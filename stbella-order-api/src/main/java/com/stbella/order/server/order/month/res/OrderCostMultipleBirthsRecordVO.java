package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "OrderCostMultipleBirthsRecordVO对象", description = "订单续住房型记录反参" )
public class OrderCostMultipleBirthsRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "orderId" )
    private Integer orderId;

    @ApiModelProperty(value = "加收项id" )
    private Long additionalRevenueId;

    @ApiModelProperty(value = "最终续住天数" )
    private String finalDurationOfStay;

    @ApiModelProperty(value = "最终续住房型" )
    private String finalContinuousHousingType;

    @ApiModelProperty(value = "最终续住金额" )
    private String finalAmountOfRenewal;

    @ApiModelProperty(value = "最终续住签约金额")
    private String finalRenewTheContractAmount;

    @ApiModelProperty(value = "续住天数变化")
    private String changeOfStayDays;

    @ApiModelProperty(value = "续住房型")
    private String continuousHousingType;

    @ApiModelProperty(value = "续住金额变化")
    private String changeOfRenewalAmount;

    @ApiModelProperty(value = "续住签约金额变化")
    private String changeOfContractAmountForRenewal;

    @ApiModelProperty(value = "变更时间")
    private String timeOfChange;

    @ApiModelProperty(value = "变更人")
    private String changeOfPerson;

    private Integer index;
    private Integer packIndex;
    private Integer length;

}

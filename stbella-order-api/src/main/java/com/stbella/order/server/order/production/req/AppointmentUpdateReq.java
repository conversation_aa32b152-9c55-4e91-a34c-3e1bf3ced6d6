package com.stbella.order.server.order.production.req;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class AppointmentUpdateReq extends BasicReq {

    private static final long serialVersionUID = -6235869326788996635L;
    /**
     *
     */
    @NotNull
    @ApiModelProperty("预约单id")
    private Long id;

    /**
     * 产康师id
     */
    @NotNull(message = "产康师id不能为空")
    @ApiModelProperty("产康师id")
    private Long therapistId;

    /**
     * 产康师名称
     */
    @NotBlank(message = "产康师名称不能为空")
    @ApiModelProperty("产康师名称")
    private String therapistName;

    /**
     * 服务开始时间
     */
    @NotBlank(message = "服务开始时间不能为空")
    @ApiModelProperty("服务开始时间 HH:mm")
    private String serveStart;

    /**
     * 服务结束时间
     */
    @NotBlank(message = "服务结束时间不能为空")
    @ApiModelProperty("服务结束时间 HH:mm")
    private String serveEnd;

    /**
     * 预约日期 yyyy-MM-dd
     */
    @NotBlank(message = "预约日期不能为空")
    @ApiModelProperty("预约日期 yyyy-MM-dd")
    private String serveDate;

    /**
     * 服务费 分
     */
    @NotNull(message = "服务费不能为空")
    @ApiModelProperty("服务费 分")
    private Integer serveFee;

    @NotNull(message = "产康仪器id不能为空")
    @ApiModelProperty("产康仪器id 0表示不需要仪器")
    private Long productionInstrId;

    @ApiModelProperty("预约单类型 1）正常 2）补单")
    private Integer appointmentType;

}

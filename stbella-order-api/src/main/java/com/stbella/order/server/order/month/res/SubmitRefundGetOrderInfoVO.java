package com.stbella.order.server.order.month.res;

import com.stbella.order.server.order.month.constant.BaseConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 订单支付记录
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@Data
@ApiModel(value = "SubmitRefundGetOrderInfoVO对象", description = "发起退款获取订单详情出参")
public class SubmitRefundGetOrderInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户id")
    private Integer clientUid;

    @ApiModelProperty(value = "客户名称")
    private String clientName;

    @ApiModelProperty(value = "客户脱敏手机号")
    private String clientPhone;

    @ApiModelProperty(value = "客户手机号")
    private String phone;

    @ApiModelProperty(value = "门店ID")
    @NotNull(message = "门店编号不能为空")
    private Integer storeId;

    @ApiModelProperty(value = "门店名称")
    private String storeName;


    @ApiModelProperty(value = "套餐名字")
    private String goodsName;

    @ApiModelProperty(value = "套餐天数")
    private String goodsDays;

    @ApiModelProperty(value = "套餐总额")
    private String goodsAmount;


    @ApiModelProperty(value = BaseConstant.REFUND_TYPE)
    private Integer refundModel;

    @ApiModelProperty(value = "如果是原路退回，退回的名字")
    private String refundStr;

    @ApiModelProperty(value = "最大可退")
    private BigDecimal maxRefund;

    @ApiModelProperty(value = "流水可退金额")
    private BigDecimal incomeRefundableAmount;

    @ApiModelProperty(value = "提前离馆可退金额")
    private BigDecimal remainingRefundableAmount;

    @ApiModelProperty(value = "是否展示查看合同按钮：0-不展示；1-展示")
    private Integer isShowContractButton;

    @ApiModelProperty(value = "审批id")
    private Integer approveId;


    @ApiModelProperty(value = "货币码")
    private String currency;


}

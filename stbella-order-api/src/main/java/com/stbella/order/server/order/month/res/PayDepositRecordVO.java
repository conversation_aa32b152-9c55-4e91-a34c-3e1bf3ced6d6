package com.stbella.order.server.order.month.res;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 押金支付记录
 * <AUTHOR>
 * @date 2022/12/27
 */
@Data
@ApiModel(value = "押金支付记录", description = "押金支付记录")
public class PayDepositRecordVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户id")
    private Integer clientUid;

    @ApiModelProperty(value = "客户姓名")
    private String clientName;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "门店ID(ecp库cfg_store表的主键id)")
    private Integer storeId;

    @ApiModelProperty(value = "押金金额已收减去已退(单位元)")
    private BigDecimal depositAmount;

    @ApiModelProperty(value = "当前已累计收款押金(单位元)")
    private BigDecimal subtotalAmount;

    @ApiModelProperty(value = "当前已累计退回押金(单位元)")
    private BigDecimal refundAmount;

}

package com.stbella.order.server.order.month.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 月子订单套餐额外礼赠对象
 * </p>
 *
 * <AUTHOR> @since 2022-10-29
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "OrderMonthGiftExtendReq对象", description = "月子订单套餐额外礼赠对象")
public class OrderMonthGiftExtendReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "关联分组id")
    private Integer categoryId;

    @ApiModelProperty(value = "关联订单id")
    private Integer orderId;

    @ApiModelProperty(value = "用户basic id")
    private Integer basicId;

    @ApiModelProperty(value = "关联商品id")
    private Integer goodsId;

    @ApiModelProperty(value = "关联sku id")
    private Integer skuId;

    @ApiModelProperty(value = "1=产康金 2=房型升级 3=续住 4=节日费用 5=家属房 6=产康服务 7=实物商品")
    private Integer type;

    @ApiModelProperty(value = "价格")
    private Integer price;

    @ApiModelProperty(value = "商品数量 特殊商品默认为1 实物商品情况而定")
    private Integer goodsNum;


}

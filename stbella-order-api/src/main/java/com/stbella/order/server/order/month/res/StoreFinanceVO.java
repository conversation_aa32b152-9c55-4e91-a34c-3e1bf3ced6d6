package com.stbella.order.server.order.month.res;

import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 门店财务支付相关配置，如多胞胎服务费，节假日服务费
 * @Description 门店财务相关配置
 * <AUTHOR>
 * @CreateDate 2024-04-11 15:45
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "门店财务相关配置", description = "门店财务支付相关配置，如多胞胎服务费，节假日服务费")
public class StoreFinanceVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("多胞胎成本元 一胎一天")
    private BigDecimal multipleBirths;

    @ApiModelProperty("多胞胎原价元 一胎一天")
    private BigDecimal multipleBirthsOriginal;

    @ApiModelProperty("节假日服务费成本元/天")
    private BigDecimal holidayServiceCost;

    @ApiModelProperty("节假日服务费元/天")
    private BigDecimal holidayServiceCharge;

    @ApiModelProperty("押金（元）")
    private BigDecimal deposit;

}
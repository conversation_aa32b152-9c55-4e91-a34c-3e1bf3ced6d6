package com.stbella.order.server.order.month.req;

import com.stbella.core.base.PageBaseReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "BasicQueryRequest", description = "用户根据手机号或姓名模糊查询")
public class BasicQueryRequest extends PageBaseReq implements Serializable {

    private static final long serialVersionUID = -8007063890977291608L;

    @ApiModelProperty(value = "关键字")
    private String keyword;

}

package com.stbella.order.server.order.month.req;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Accessors(chain = true)
@ApiModel(value = "OrderCacheBaseReq对象", description = "下单前订单缓存基础对象")
public class OrderCacheBaseReq extends BasicReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户ID")
    @NotNull(message = "客户ID不能为空")
    private Integer clientUid;

    @ApiModelProperty(value = "门店id")
    @NotNull(message = "门店ID不能为空")
    private Integer storeId;

    @ApiModelProperty(value = "订单类型： 订单类型：\n" +
            "0 月子客户的“普通月子套餐”订单；\n" +
            "1 月子客户的“小月子”订单；\n" +
            "10 到家客户的“到家服务”订单；\n" +
            "20 到家阿姨的“到家服务订单”；\n" +
            "21 到家阿姨的阿姨培训订单；\n" +
            "30 月子其他订单")
    @NotNull(message = "订单类型不能为空")
    private Integer orderType;

    @ApiModelProperty(value = "订单Id")
    private Integer orderId;

    @ApiModelProperty(value = "数据库主键")
    private Integer id;

    @ApiModelProperty(value = "是否从缓存中获取数据,true 默认是,false 从数据库获取")
    private boolean fromCache = true;

}

package com.stbella.order.server.contract.res;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ContractSignVO implements Serializable {

    private static final long serialVersionUID = 45717640179914002L;
    @ApiModelProperty(value = "签约记录表")
    private Long id;

    @ApiModelProperty(value = "客户ID（ecp库的tab_client表主键）")
    private Integer clientUid;

    @ApiModelProperty(value = "门店ID(ecp库cfg_store表的主键id)")
    private Long storeId;

    @ApiModelProperty(value = "关联id（签约类型为订单，此处为订单id）")
    private Long orderId;

    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "合同状态 0 =未签署 1=签署中 2=已归档")
    private Integer contractStatus;

    @ApiModelProperty(value = "合同类型 1=E签宝合同 2=旧合同 3=纸质合同 ")
    private Integer contractType;

    @ApiModelProperty(value = "模板关联合同类型1育婴师-中介-贝康居间服务协议 2雇主-中介-母婴护理-贝康母婴护理委托服务协议 3雇主-中介-育婴师-贝康育婴师服务委托协议 4育婴师-套餐-贝康服务合作协议 5雇主-套餐-母婴护理-贝康母婴护理委托协议 6雇主-套餐-育婴师-贝康育婴师服务委托协议 7育婴师培训课程确认书协议 8月子合同 10.外派合同  11.小月子合同 12.总部与客户和解保密协议 13.门店与客户和解保密协议 14.礼赠协议 15.补充协议 21.预约协议 22.授权委托书 23.订单折扣保密协议 24.合同解除协议")
    private Integer templateContractType;

    @ApiModelProperty(value = "合同类型 1=>主合同，2=>附件类，3=>附属类：补充协议")
    private Integer templateType;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "合同内容图片")
    private List<String> img;

    @ApiModelProperty("签署时间")
    private Date signTime;

    @ApiModelProperty(value = "长链接")
    private String url;

    @ApiModelProperty(value = "合同签订入口0=未知;1=h5;2=微信;3=支付宝")
    private Integer signFrom;
}

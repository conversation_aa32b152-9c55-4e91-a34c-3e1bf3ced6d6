package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 月子套餐缓存获取
 * </p>
 *
 * <AUTHOR> @since 2022-10-29
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "OrderGiftByStoreGoodsSkuInfoVO对象", description = "下单页面选择额外礼赠sku列表数据")
public class OrderGiftByStoreGoodsSkuInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品规格ID")
    private Integer skuId;

    @ApiModelProperty(value = "商品规格名称")
    private String skuName;

    @ApiModelProperty(value = "展示图片")
    private String picUrl;

    @ApiModelProperty(value = "总商品售价(元)")
    private BigDecimal totalGoodsPrice;

    @ApiModelProperty(value = "总成本价(元)")
    private BigDecimal totalCostPrice;

    @ApiModelProperty(value = "商品售价(元)")
    private String goodsPrice;

    @ApiModelProperty(value = "成本价(元)")
    private String costPrice;

    @ApiModelProperty(value = "已选数量")
    private Integer quantity;
}

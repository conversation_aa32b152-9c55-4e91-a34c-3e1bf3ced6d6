package com.stbella.order.server.contract.res;

import com.stbella.order.common.constant.TableFieldConstant;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CanContractSignPaperVO implements Serializable {

    private static final long serialVersionUID = 45717640179914002L;


    @ApiModelProperty(value = TableFieldConstant.templateContractType)
    private Integer templateContractType;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "是否已签订0 未签订 1已签订")
    private Boolean sign;
}

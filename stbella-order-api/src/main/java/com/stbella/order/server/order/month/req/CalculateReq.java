package com.stbella.order.server.order.month.req;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Accessors(chain = true)
@ApiModel(value = "CalculateReq对象", description = "计算公式入参")
public class CalculateReq extends BasicReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "门店Id")
    @NotNull(message = "门店Id不能为空")
    private Integer storeId;
}

package com.stbella.order.server.order.month.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel(value = "OrderSwapSaleRequest", description = "订单更改销售请求类")
public class OrderSwapSaleRequest implements Serializable {

    private static final long serialVersionUID = -8007063890977291608L;

    @ApiModelProperty(value = "订单类型;0-母婴;1-予家", required = true)
    @NotNull(message = "订单类型不能为空")
    private Integer orderType;

    @ApiModelProperty(value = "订单编号", required = true)
    @NotNull(message = "订单编号不能为空")
    private String orderNo;

    @ApiModelProperty(value = "新销售ID,母婴取`ecp`.user的ID,予家取`stbella-sso`.dd_employee的ID", required = true)
    @NotNull(message = "新销售ID不能为空")
    private String saleId;

    @ApiModelProperty(value = "原销售手机号", required = true)
    private String originalSalePhone;

    @ApiModelProperty(value = "新销售手机号", required = true)
    @NotNull(message = "新销售手机号不能为空")
    private String newSalePhone;

    @ApiModelProperty(value = "原销售名称", required = true)
    private String originalSaleName;

    @ApiModelProperty(value = "新销售名称", required = true)
    @NotNull(message = "新销售名称不能为空")
    private String newSaleName;

}

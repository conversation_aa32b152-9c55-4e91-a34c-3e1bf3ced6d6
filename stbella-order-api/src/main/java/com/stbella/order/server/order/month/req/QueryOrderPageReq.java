package com.stbella.order.server.order.month.req;

import com.stbella.core.base.BasicReq;

import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotNull;

import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "QueryOrderPageReq对象", description = "后台订单列表查询对象")
public class QueryOrderPageReq extends BasicReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("basicUid")
    private String basicUid;

    @ApiModelProperty("客户姓名")
    private String customerName;

    @ApiModelProperty("客户手机号")
    private String customerMobile;

    @ApiModelProperty("门店编号集合")
    private List<Integer> storeIds;

    @ApiModelProperty(value = "门店品牌 1-圣贝拉，2小贝拉,3-Bella Villa,100-艾屿")
    private List<Integer> type;

    @ApiModelProperty(value = "折扣审批状态：0-无需审批；1-审批中；2-审批通过；3-审批失败；4-发起失败")
    private Integer discountApprovalStatus;

    @ApiModelProperty(value = "支付状态：0待支付，1未全付，2已付清，3已取消 4-超额支付")
    private Integer payStatus;

    @ApiModelProperty(value = "退款状态：0-无退款 1-部分退款中 2-部分退款 3-全部退款中 4-全部退款")
    private Integer refundStatus;

    @ApiModelProperty(value = "订单状态：0-未生效 1-待入住 2-入住中 3-已离店 4-已关闭 5-提前离馆")
    private Integer orderStatus;

    @ApiModelProperty(value = "客户来源渠道")
    private List<Integer> customerSource;

    @ApiModelProperty(value = "下单时来源：0未知，1小贝拉，2圣贝拉，3occ后台")
    private Integer orderSource;

    @ApiModelProperty("时间类型：0-创建时间；1-业绩生效时间（支付50%时间）,2 -履约开始时间")
    private Integer dateType;

    @ApiModelProperty("开始时间 秒级时间戳 00:00:00")
    private Long dateStart;

    @ApiModelProperty("结束时间 秒级时间戳 23:59:59")
    private Long dateEnd;

    @ApiModelProperty(value = "订单标签id")
    private Integer orderTagId;

    @ApiModelProperty(value = "是否续住：0-否；1-是")
    private Integer stayOver;

    @ApiModelProperty(value = "礼赠一行显示：0-否；1-是")
    private Integer giftOneRow;

    @ApiModelProperty(value = "是否获取所有订单类型")
    private Boolean allOrderType = false;


    @ApiModelProperty(value = "页数")
    @NotNull(message = "分页参数不能为空")
    private Integer pageNum;

    @ApiModelProperty(value = "每页展示条数")
    @NotNull(message = "分页参数不能为空")
    private Integer pageSize;



    public Integer getPageNum() {
        return ObjectUtil.isNotEmpty(pageNum) ? pageNum : 1;
    }

    public Integer getPageSize() {
        return ObjectUtil.isNotEmpty(pageSize) ? pageSize : 10;
    }

    public String getOrderNo() {
        return StringUtils.isNotEmpty(orderNo) ? orderNo.replace(" ", "") : orderNo;
    }

    public String getCustomerName() {
        return StringUtils.isNotEmpty(customerName) ? customerName.replace(" ", "") : customerName;
    }

    public String getCustomerMobile() {
        return StringUtils.isNotEmpty(customerMobile) ? customerMobile.replace(" ", "") : customerMobile;
    }

}

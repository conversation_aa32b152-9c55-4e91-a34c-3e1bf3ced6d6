package com.stbella.order.server.order.month.service;


import com.stbella.order.server.order.month.res.OrderRefundVO;

public interface OrderRefundMangerService {

    /**
     * 重置审批状态
     *
     * @param id
     * @param status 1：审批中 2：审批失败
     */
    void resetApprovalStatus(Integer id, Integer status);


    /**
     * 根据退款sn查询
     *
     * @param refundOrderSn
     * @return
     */
    OrderRefundVO getOneByRefundOrderSn(String refundOrderSn);

}

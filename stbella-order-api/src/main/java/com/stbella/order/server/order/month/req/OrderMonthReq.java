package com.stbella.order.server.order.month.req;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 月子订单对象
 * </p>
 *
 * <AUTHOR> @since 2022-10-29
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "OrderMonthReq对象", description = "月子订单对象")
public class OrderMonthReq extends BasicReq implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "订单ID")
    private Integer orderId;


    @ApiModelProperty(value = "订单类型")
    private Integer orderType;

    @ApiModelProperty(value = "月子订单客户对象")
    private OrderMonthClientReq orderMonthClientReq;

    @ApiModelProperty(value = "月子订单客户委托人对象")
    private OrderMonthClientBailorReq orderMonthClientBailorReq;

    @ApiModelProperty(value = "套餐信息")
    private OrderMonthGoodsReq orderMonthGoodsReq;

    @ApiModelProperty(value = "订单额外礼赠")
    private List<OrderMonthGiftExtendReq> orderMonthGiftExtendReqList;

    @ApiModelProperty(value = "订单加收项")
    private List<OrderMonthAdditionalRevenueReq> orderMonthAdditionalRevenueReqList;

    @ApiModelProperty(value = "订单其他信息")
    private OrderMonthOtherReq orderMonthOtherReq;

    @ApiModelProperty(value = "订单折扣详情")
    private OrderDiscountsReq orderDiscountsReq;


    public void setSallerName() {
        this.getOrderMonthClientReq().setSellerId(new Integer(this.getOperator().getOperatorGuid()));
        this.getOrderMonthClientReq().setSellerName(this.getOperator().getOperatorName());
    }

}

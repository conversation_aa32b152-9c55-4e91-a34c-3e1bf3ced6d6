package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ComplaintsOrderInfoVO implements Serializable {

    private static final long serialVersionUID = -418455677384944889L;

    @ApiModelProperty(value = "订单ID（主键自增)")
    private Integer orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    @ApiModelProperty(value = "门店ID 0为总部")
    private Integer storeId;

    @ApiModelProperty(value = "门店名称(标识)")
    private String storeName;

    @ApiModelProperty(value = "创建时间")
    private String createdAt;

    @ApiModelProperty(value = "省份")
    private Integer province;

    @ApiModelProperty(value = "城市")
    private Integer city;

    @ApiModelProperty(value = "地区")
    private Integer region;

    @ApiModelProperty(value = "省")
    private String provinceStr;

    @ApiModelProperty(value = "市")
    private String cityStr;

    @ApiModelProperty(value = "区县")
    private String regionStr;

    @ApiModelProperty(value = "地址")
    private String address;
}

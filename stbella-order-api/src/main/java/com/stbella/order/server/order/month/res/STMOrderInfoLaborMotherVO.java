package com.stbella.order.server.order.month.res;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "STMOrderInfoLaborMotherVO对象", description = "订单后台-订单详情-分娩喜报-宝妈信息")
public class STMOrderInfoLaborMotherVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预计入馆时间")
    private String stayinTime;

    @ApiModelProperty(value = "分娩方式")
    private String productionMode;

    @ApiModelProperty(value = "分娩方式值")
    private String productionModeStr;

    @ApiModelProperty(value = "分娩日期")
    private String productionDate;

    @ApiModelProperty(value = "分娩孕周")
    private String gestationWeek;

    @ApiModelProperty(value = "分娩孕天")
    private String gestationDays;

    @ApiModelProperty(value = "分娩医院")
    private String gestationHospital;

    @ApiModelProperty(value = "第几胎")
    private String births;

    @ApiModelProperty(value = "胎数")
    private Integer birthType;

    @ApiModelProperty(value = "胎数")
    private String birthTypeStr;

    @ApiModelProperty(value = "名族")
    private String nation;

    @ApiModelProperty(value = "哺乳方式")
    private String feedMode;

    @ApiModelProperty(value = "哺乳方式值")
    private Integer feedModeStr;

    @ApiModelProperty(value = "妊娠糖尿病")
    private String diabetes;

    @ApiModelProperty(value = "高血压")
    private String hypertension;

    @ApiModelProperty(value = "药物过敏史")
    private String allergy;

    @ApiModelProperty(value = "传染病检查")
    private String infectiousDiseases;

    @ApiModelProperty(value = "实物禁忌及过敏")
    private String physicalTaboo;

    @ApiModelProperty(value = "身高(cm)")
    private String height;

    @ApiModelProperty(value = "体重（kg）")
    private String weight;

    @ApiModelProperty(value = "接入住用车")
    private String pickCar;

    @ApiModelProperty(value = "具体医院地址（房间、房号）")
    private String hospitalAddress;

    @ApiModelProperty(value = "鲜花过敏")
    private String allergyFlower;

    @ApiModelProperty(value = "您希望我们对您的称呼")
    private String call;

    @ApiModelProperty(value = "是否还有其它过敏原;是/否")
    private String allergyOther;

    @ApiModelProperty(value = "如果有其他过敏原描述信息")
    private String allergyRemark;

    @ApiModelProperty(value = "哺乳服尺码")
    private String lactationClothingSize;
}

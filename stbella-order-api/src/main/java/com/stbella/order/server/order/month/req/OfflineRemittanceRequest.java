package com.stbella.order.server.order.month.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel(value = "线下汇款请求", description = "OfflineRemittanceRequest")
public class OfflineRemittanceRequest implements Serializable {

    @ApiModelProperty(value = "赔偿金额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "开户名")
    private String accountName;

    @ApiModelProperty(value = "开户支行")
    private String accountBank;

    @ApiModelProperty(value = "银行账号")
    private String bankNo;

    @ApiModelProperty(value = "门店扣除业绩")
    private BigDecimal refundAchievement;

    @ApiModelProperty(value = "客诉单id")
    private Long complaintId;

}

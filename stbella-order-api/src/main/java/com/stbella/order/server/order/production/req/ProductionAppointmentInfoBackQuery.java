package com.stbella.order.server.order.production.req;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @description:
 * @author: Seven
 * @time: 2022/10/10 18:07
 */
@Data
public class ProductionAppointmentInfoBackQuery extends BasicReq implements Serializable {

    @ApiModelProperty("预约单ID")
    @NotNull(message = "预约单ID不能为空")
    private Long id;
}

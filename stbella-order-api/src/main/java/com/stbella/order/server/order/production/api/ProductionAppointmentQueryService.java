package com.stbella.order.server.order.production.api;

import com.stbella.core.base.PageVO;
import com.stbella.core.result.Result;
import com.stbella.order.server.order.order.req.ExportProductionRealPaidReq;
import com.stbella.order.server.order.order.req.BatchProductionExportReq;
import com.stbella.order.server.order.order.req.ProductionRealPaidReq;
import com.stbella.order.server.order.production.req.*;
import com.stbella.order.server.order.production.res.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 预约查询服务
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2022/09/30
 * @since 2022-09-30 17:35
 */
public interface ProductionAppointmentQueryService {

    /**
     * 查询搜索预约列表
     *
     * @param query 查询
     * @return {@link Result}<{@link List}<{@link ProductionAppointmentVo}>>
     */
    Result<PageVO<ProductionAppointmentVo>> querySearchAppointmentList(ProductionAppointmentQuery query);

    /**
     * 查询搜索产康预约面板
     *
     * @param query 查询
     * @return {@link Result}<{@link List}<{@link ProductionAppointmentVo}>>
     */
    Result<ProductionBoardAppointmentVo> querySearchBoardAppointmentList(ProductionAppointmentQuery query) throws Exception;


    /**
     * 【后台】查询预约单列表
     *
     * @param query 筛选
     * @return Result<PageVO < List < ProductionAppointmentBackVo>>>
     */
    Result<PageVO<ProductionAppointmentBackVo>> queryAppointmentBackPage(ProductionAppointmentBackQuery query);


    /**
     * 【后台】预约单详情
     *
     * @param query 筛选
     * @return Result<ProductionAppointmentInfoBackVo>
     */
    Result<ProductionAppointmentInfoBackVo> queryAppointmentBackDetail(ProductionAppointmentInfoBackQuery query);

    /**
     * 后台预约单列表导出
     *
     * @param query    查询条件
     * @param response servlet
     * @return void
     */
    void doExportProductionBookList(ProductionAppointmentBackQuery query, HttpServletResponse response);

    /**
     * 产康师核销金额汇总
     */
    void therapistRealPaidExport(ProductionAppointmentBackQuery query, HttpServletResponse response);

    /**
     * 获取产康师手工费
     *
     * @param query 查询条件
     * @return Result<ProductionAppointmentBoardWorkmanshipVo>
     */
    Result<ProductionAppointmentBoardWorkmanshipVo> queryWorkmanship(AppointmentBoardTherapistQuery query);


    /**
     * 【MINI端 pi/c端】查询预约单列表
     *
     * @param query 筛选
     * @return Result<PageVO < List < ProductionAppointmentBackVo>>>
     */
    Result<PageVO<ProductionAppointmentMiniVo>> queryAppointmentMiniPage(ProductionAppointmentBackQuery query);

    /**
     * 【PI端/c端】查看产康日程
     *
     * @param query 筛选
     * @return Result<List < ProductionAppointmentProductionScheduleVo>>
     */
    Result<List<ProductionAppointmentProductionScheduleVo>> queryProductionSchedule(AppointmentProductionScheduleQuery query);

    /**
     * 约单详情
     *
     * @param id
     * @return com.stbella.core.result.Result<com.stbella.order.server.order.production.res.ProductionAppointmentDetailVO>
     * @throws
     * <AUTHOR>
     * @date 2022/10/14 00:03
     * @since 1.0.0
     */
    Result<ProductionAppointmentDetailVO> queryProductionById(Long id);

    /**
     * 预约-获取商品信息
     *
     * @param query 查询条件
     * @return Result
     */
    Result<ProductionAppointmentGoodsSkuInfoVo> behalfGoodsInfo(AppointmentBehalfConsumerQuery query);

    /**
     * 预约-获取预约时间数据
     *
     * @param query 查询条件
     * @return Result
     */
    Result<List<ProductionBehalfTimeShaftVo>> behalfTimeInfo(AppointmentBehalfTImeQuery query);

    /**
     * 预约-根据产康师+sku的id获取产康师的服务费
     *
     * @param query 查询条件
     * @return Result
     */
    Result<ProductionAppointmentServiceFeeVo> getServiceFeeByTherapistIdAndSkuID(AppointmentServiceFeeByTherapistIdAndSkuIDQuery query);

    /**
     * 产康师id查询 预约信息
     *
     * @param query 查询
     * @return {@link Result}<{@link List}<{@link ProductionAppointmentTherapistVo}>>
     */
    Result<ProductionAppointmentTherapistVo> queryProductionByTherapistId(ProductionAppointmentInfoQuery query);

    Result<ProductionAppointmentInfoBackVo> queryAppointmentBackDetail(Integer propertyId, Integer itemType, Integer orderProductionType);

    List<ProductionTherapistInfoVO> queryTherapistInfoByOrderSn(List<String> orderSnList);

    List<ProductionAppointmentVo> queryAppointmentListByServeDate(List<String> serveDateList);


    Result<ProductionRealPaidSummaryVO> queryRealPaidSummary(ProductionRealPaidReq req);

    /**
     * 产康师实付导出 全部门店
     */
    void exportTherapistRealPaidSummary(ProductionRealPaidReq req, HttpServletResponse response);

    /**
     * 产康师实付导出
     */
    void exportTherapistRealPaid(ProductionRealPaidReq req, HttpServletResponse response);

    /**
     * 批量导出客户实付
     */
    String batchExportClientRealPaid(BatchProductionExportReq req);

    /**
     * 批量导出汇总实付
     */
    String batchExportSummaryRealPaid(BatchProductionExportReq req);

    /**
     * 批量导出产康师实付
     */
    String batchExportTherapistRealPaid(BatchProductionExportReq req);

    /**
     * 查询产康师实付
     */
    Result<PageVO<ProductionTherapistRealPaidVO>> queryTherapistRealPaid(ProductionRealPaidReq req);

    /**
     * 查询客户核销金额
     */
    Result<PageVO<ProductionClientRealPaidVO>> queryClientRealPaid(ProductionRealPaidReq req);

    /**
     * 查询预约单核销金额
     */
    Result<PageVO<ProductionAppointmentRealPaidVO>> queryAppointmentRealPaid(ProductionRealPaidReq req);

    /**
     * 导出核销金额汇总
     */
    void exportTherapistSummary(ExportProductionRealPaidReq req, HttpServletResponse response);
}

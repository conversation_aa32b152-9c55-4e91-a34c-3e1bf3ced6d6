package com.stbella.order.server.order.nutrition.service;

import com.stbella.core.base.PageVO;
import com.stbella.order.server.order.nutrition.dto.PayNotifyMqDTO;
import com.stbella.order.server.order.nutrition.request.*;
import com.stbella.order.server.order.nutrition.response.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * pi小助手pro营养月子餐订单 服务类
 */
public interface OrderNutritionMealService {

    /**
     * 获取待付款详情页详情
     *
     * @param orderNo 订单号
     * @return 订单付款详情
     */
    OrderPayDetailVO getOrderPayDetail(String orderNo);

    /**
     * 获取订单详情
     *
     * @param orderNo 订单号
     * @return 订单详情信息
     */
    OrderInfoVO getOrderInfo(String orderNo);

    /**
     * 新建营养订单
     *
     * @param addOrderNutritionRequest
     */
    String createOrder(AddOrderNutritionRequest addOrderNutritionRequest);

    /**
     * 修改订单预计送餐时间
     *
     * @param updateOrderByPlanSendFoodRequest
     * @return
     */
    void updateOrderByPlanSendFood(UpdateOrderByPlanSendFoodRequest updateOrderByPlanSendFoodRequest);

    /**
     * 增加订单备注
     *
     * @param addOrderNutritionRequest
     */
    void addOrderRemark(AddOrderRemarkRequest addOrderNutritionRequest);

    /**
     * 小程序根据不同账号权限展示不同的订单列表
     *
     * @param appletOrderListRequest 请求参数
     * @return 订单列表
     */
    PageVO<List<AppletOrderListVO>> getAppletOrderList(QueryAppletOrderListRequest appletOrderListRequest);

    /**
     * picp管理后台月子餐订单列表
     *
     * @param managerOrderListRequest 请求参数
     * @return 列表
     */
    PageVO<List<ManagerOrderListVO>> getManagerOrderList(QueryManagerOrderListRequest managerOrderListRequest);

    /**
     * 导出订单
     *
     * @param managerOrderListRequest
     * @param response
     */
    void export(QueryManagerOrderListRequest managerOrderListRequest, HttpServletResponse response);

    /**
     * 营养支付回调
     *
     * @param payNotifyMqDTO
     */
    Boolean payNotify(PayNotifyMqDTO payNotifyMqDTO, String orderNo);

    /**
     * 订单订单审批
     *
     * @param disposeAuditRequrest
     */
    void disposeAudit(DisposeAuditRequrest disposeAuditRequrest);

    /**
     * 返回老客户订单送餐相关的信息
     *
     * @param customerId 客户id
     * @return 客户送餐相关信息
     */
    OrderCustomerDeliveryVO doBackOrderFoodDelivery(Long customerId);
}

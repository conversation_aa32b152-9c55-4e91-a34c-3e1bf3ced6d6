package com.stbella.order.server.order.month.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class OrderProductionCardContentRes implements Serializable {

    private static final long serialVersionUID = -3578373805708432154L;

    @JsonProperty("sku_id")
    private Integer skuId;

    @JsonProperty("goods_id")
    private Integer goodsId;

    @ApiModelProperty("单次实付分摊金额(元)")
    private BigDecimal realPaid;

    @ApiModelProperty("实付分摊总额(元)")
    private BigDecimal realPaidTotal;

}

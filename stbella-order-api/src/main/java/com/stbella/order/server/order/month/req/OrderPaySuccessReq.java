package com.stbella.order.server.order.month.req;

import com.stbella.core.base.PageBaseReq;
import com.stbella.order.common.enums.month.PayStatusV2Enum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Arrays;
import java.util.Date;
import java.util.List;


@Data
@EqualsAndHashCode(callSuper = true)
public class OrderPaySuccessReq extends PageBaseReq {

    private static final long serialVersionUID = -3277827939092201549L;

    /**
     * @see com.stbella.order.common.enums.core.OmniOrderTypeEnum
     *       订单类型
     *  60 - 产康订单
     */
    private Integer orderType;

    /**
     * 门店ID
     */
    private List<Integer> storeIdList;

    /**
     * 订单ID
     */
    private List<Integer> orderIdList;

    /**
     * 订单sn
     */
    private List<String> orderSnList;

    /**
     * 支付时间开始
     */
    private Date payTimeStart;

    /**
     * 支付时间结束
     */
    private Date payTimeEnd;

    /**
     * 是否仅查询新订单
     */
    private Boolean queryNewOrder = false;

    /**
     * 支付状态集
     */
    private List<Integer> payStatusList = Arrays.asList(PayStatusV2Enum.PAY_OFF.getCode(), PayStatusV2Enum.EXCESS_PAY.getCode());
}

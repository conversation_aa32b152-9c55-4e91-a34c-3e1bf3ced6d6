package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 门店经营情况
 */
@Data
public class StoreAchievementResultVO {

    @ApiModelProperty(value = "报单类型1-1千万报单;2-2千万报单;3-3千万报单;4-4千万报单;5-5千万报单;6-6千万报单;7-7千万报单;8-8千万报单;9-9千万报单")
    private Integer noticeType;

    @ApiModelProperty(value = "年")
    private Integer year;

    @ApiModelProperty(value = "月")
    private Integer month;

    @ApiModelProperty(value = "总业绩达成率")
    private String totalRate;

    @ApiModelProperty(value = "到家待确认总业绩达成率")
    private String waitConfirmTotalRate;

    @ApiModelProperty(value = "总业绩金额")
    private String totalAmount;

    @ApiModelProperty(value = "总业绩金额未格式化")
    private String totalAmountValue;

    @ApiModelProperty(value = "总业绩目标")
    private String totalStoreTarget;

    @ApiModelProperty(value = "到家待确认总业绩金额")
    private String waitConfirmTotalAmount;

    @ApiModelProperty(value = "业绩是否达标, null:无需显示 0:未达标 1:达标")
    private Integer rateClass;

    @ApiModelProperty(value = "到家待确认业绩是否达标, null:无需显示 0:未达标 1:达标")
    private Integer waitConfirmRateClass;

    @ApiModelProperty(value = "广禾堂无业绩目标月份")
    private List<Integer> nutritionNoTargetDate;

    @ApiModelProperty(value = "广禾堂无业绩目标渠道")
    private List<String> nutritionNoTargetChannelName;

    @ApiModelProperty(value = "品牌数据列表")
    private List<InnerBrandData> children;

    @ApiModelProperty(value = "类型, 1:母婴事业部总业绩 2:月子中心总业绩 3:月子中心净业绩 4:月子中心总退单业绩 5:月子销售净业绩 6:产康净业绩 " +
            "7:S-BRA净业绩 8:护士外派净业绩 9:个人排行 10:到家总业绩 11:广禾堂业绩 12:区域净业绩")
    private Integer rankType;

    @Data
    public static class InnerBrandData {
        @ApiModelProperty(value = "品牌类型, sbl:圣贝拉 xbl:小贝拉 villa:Villa primecare:贝康 home:到家 nutrition:广禾堂 isla:艾屿")
        private String type;

        @ApiModelProperty(value = "0圣贝拉，1小贝拉， 100艾屿")
        private Integer typeCode;

        @ApiModelProperty(value = "业绩目标对应的配置项")
        private List<Integer> targetConfigList;

        @ApiModelProperty(value = "品牌总业绩金额")
        private String total;

        @ApiModelProperty(value = "业绩达成率")
        private String rate;

        @ApiModelProperty(value = "品牌业绩金额未格式化:单位分")
        private String totalAmountValue;

        @ApiModelProperty(value = "门店业绩目标和:单位元")
        private String totalStoreTarget;

        @ApiModelProperty(value = "品牌业绩目标:单位分")
        private String totalBrandTarget;

        @ApiModelProperty(value = "未开门店的目标:单位元")
        private String unopenedStoreTarget;

        @ApiModelProperty(value = "是否达标, null:无需显示 0:未达标 1:达标")
        private Integer rateClass;

        @ApiModelProperty(value = "品牌logo链接")
        private String image;

        @ApiModelProperty(value = "门店排行列表")
        private List<InnerRankData> rankData;

        @Data
        public static class InnerRankData {
            @ApiModelProperty(value = "门店ID")
            private Integer storeId;

            @ApiModelProperty(value = "门店名")
            private String name;

            @ApiModelProperty(value = "排名")
            private Integer sort;

            @ApiModelProperty(value = "业绩金额")
            private String total;

            @ApiModelProperty(value = "业绩目标")
            private Long mubiao;

            @ApiModelProperty(value = "到家等待确认业绩金额")
            private String waitConfirmTotal;

            @ApiModelProperty(value = "业绩金额, 无格式化")
            private Long totalValue;

            @ApiModelProperty(value = "到家待确认业绩金额, 无格式化")
            private Long waitConfirmTotalValue;

            @ApiModelProperty(value = "业绩达成率")
            private String rate;

            @ApiModelProperty(value = "到家待确认业绩达成率")
            private String waitConirmRate;

            @ApiModelProperty(value = "业绩是否达标, null:无需显示 0:未达标 1:达标")
            private Integer rateClass;

            @ApiModelProperty(value = "到家待确认业绩是否达标, null:无需显示 0:未达标 1:达标")
            private Integer waitConirmRateClass;

            @ApiModelProperty(value = "业绩达成率, 不带%")
            private BigDecimal rateValue;

            @ApiModelProperty(value = "到家待确认业绩达成率, 不带%")
            private BigDecimal waitConirmRateValue;

            @ApiModelProperty(value = "广禾堂渠道排名")
            private List<InnerRankDataChild> rankDataChild;

            @ApiModelProperty(value = "区域净业绩有值其他业绩模块处理空, 战区增加已开门店达成率 公式：战区内已开门店的净业绩/战区内已开门店的目标  （已开门店 40%) 带%")
            private String openRate;

            @ApiModelProperty(value = "区域净业绩有值其他业绩模块处理空, 战区增加已开门店达成率 公式：战区内已开门店的净业绩/战区内已开门店的目标  （已开门店 40%)")
            private String openRateStr;

            @ApiModelProperty(value = "区域净业绩有值其他业绩模块处理空, 战区增加已开门店达成率 公式：战区内已开门店的净业绩/战区内已开门店的目标  （已开门店 40%) 不带%")
            private BigDecimal openRateValue;

            @ApiModelProperty(value = "type=1的母婴业绩目标之和")
            private Long targetMonthAmount;

            @Data
            public static class InnerRankDataChild {
                @ApiModelProperty(value = "渠道ID")
                private Integer storeId;

                @ApiModelProperty(value = "渠道名称")
                private String name;

                @ApiModelProperty(value = "排名")
                private Integer sort;

                @ApiModelProperty(value = "业绩金额")
                private String total;

                @ApiModelProperty(value = "业绩金额，无格式化")
                private Long totalValue;

                @ApiModelProperty(value = "业绩达成率")
                private String rate;

                @ApiModelProperty(value = "业绩达成率，不带%")
                private BigDecimal rateValue;

                @ApiModelProperty(value = "业绩是否达标, null:无需显示 0:未达标 1:达标")
                private Integer rateClass;
            }
        }
    }
}

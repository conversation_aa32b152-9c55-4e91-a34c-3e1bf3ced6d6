package com.stbella.order.server.contract.service;

import com.stbella.order.server.contract.param.BabysittingServiceAgreementParam;
import com.stbella.order.server.contract.param.PersonAuthParam;
import com.stbella.order.server.contract.req.OrderParamHistoryValuePushDTO;
import com.stbella.order.server.contract.vo.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ContractService {


    /**
     * @return
     */
    AuthDetailVO authDetail(Long customerId);

    /**
     * @param personAuthParam
     * @return
     */
    AuthDetailVO personAuth(PersonAuthParam personAuthParam);


    PersonBaseVO compare(PersonAuthParam personAuthParam);


    SignContractVO apply(BabysittingServiceAgreementParam babysittingServiceAgreementParam);

    List<ContractVO> contractListH5(String orderNo);

    ContractDetailVO contractDetail(String orderNo);

    /**
     * 生成合同填充参数
     * @param orderId
     */
    PushContractParamsVO pushContractParams(Integer orderId);

    /**
     * 订单1.0构建折扣保密协议参数
     * @param orderId
     * @return
     */
    Map<String, String> buildContractParams(Integer orderId);
}

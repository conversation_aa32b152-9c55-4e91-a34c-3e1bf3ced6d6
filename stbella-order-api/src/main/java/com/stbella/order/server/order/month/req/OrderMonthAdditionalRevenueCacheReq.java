package com.stbella.order.server.order.month.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 月子订单加收项费用对象
 * </p>
 *
 * <AUTHOR> @since 2022-10-29
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "OrderMonthAdditionalRevenueCacheReq对象", description = "月子订单加收项费用缓存入参")
public class OrderMonthAdditionalRevenueCacheReq extends OrderCacheBaseReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "是否填写完整")
    @NotNull(message = "0：否；1：是")
    private Integer integrality;

    @ApiModelProperty(value = "是否开启（目前只有节假日）")
    @NotNull(message = "0：不开启；1：开启")
    private Integer isFestival;

    @ApiModelProperty(value = "1=多胞胎费用 2=续住费用 3=变更房型 4=节假日")
    @NotNull(message = "类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "应收")
    private BigDecimal price;

    @ApiModelProperty(value = "房型ID、当类型是续住费用或者房型变更的必传")
    private Integer roomId;

    @ApiModelProperty(value = "胎数、当类型是多胞胎费用的时候传")
    private Integer embryoNumber;

    @ApiModelProperty(value = "天数（节假日）")
    private Integer days;

    @ApiModelProperty(value = "房间生效日期(年-月-日,年-月-日)、当类型是续住费用或者房型变更或者是节假日的必传")
    private List<String> daysList;

    @ApiModelProperty(value = "套餐Id")
    @NotNull(message = "套餐Id不能为空")
    private Integer goodsId;

}

package com.stbella.order.server.order.month.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 月子订单其他信息对象
 * </p>
 *
 * <AUTHOR> @since 2022-10-29
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "OrderMonthOtherReq对象", description = "月子订单其他信息对象")
public class OrderMonthOtherReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "特殊订单标签")
    private Integer orderTag;

    @ApiModelProperty(value = "特殊订单标签名称")
    private String orderTagName;

    @ApiModelProperty(value = "特殊事项备注")
    private String remark;

    @ApiModelProperty(value = "特殊标签凭证")
    private List<String> voucherUrlList;
}

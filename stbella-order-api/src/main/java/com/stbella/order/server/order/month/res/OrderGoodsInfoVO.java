package com.stbella.order.server.order.month.res;

import com.stbella.platform.order.api.res.PromotionInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class OrderGoodsInfoVO implements Serializable {

    private static final long serialVersionUID = -6653278981287435286L;

    private Integer id;

    @ApiModelProperty(value = "商品Id")
    private Integer goodsId;

    @ApiModelProperty(value = "商品缩略图")
    private String goodsImage;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "规格名称")
    private String skuName;

    @ApiModelProperty("后台类目id")
    private Long backCategoryId;

    @ApiModelProperty("类目名称")
    private String categoryName;

    @ApiModelProperty(value = "商品类型")
    private Integer goodsType;

    @ApiModelProperty(value = "购买数量")
    private Integer goodsNum;

    @ApiModelProperty(value = "每份包含的商品数量")
    private Integer skuNum;

    @ApiModelProperty(value = "份数")
    private Integer num;

    @ApiModelProperty(value = "商品原单价")
    private BigDecimal goodsPriceOrgin;

    @ApiModelProperty(value = "商品总价")
    private BigDecimal goodsTotalPriceOrgin;

    @ApiModelProperty(value = "应付金额")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "应付总金额")
    private BigDecimal payTotalAmount;

    @ApiModelProperty(value = "房型Id")
    private Integer roomId;

    @ApiModelProperty(value = "房型名称")
    private String roomName;

    @ApiModelProperty(value = "ecp房型配置")
    private Integer ecpRoomType;

    @ApiModelProperty("护理模式")
    private Integer careServiceType;

    @ApiModelProperty("护理模式名称")
    private String careServiceName;

    @ApiModelProperty("房间类型名称")
    private String roomTypeName;

    @ApiModelProperty(value = "套餐的自定义属性")
    private String definedProperty;

    private List<String> orderGoodsSubList;

    private List<OrderGoodsVO> orderGoodsChildList;

    /**
     * 否礼赠: 0不是，1是
     **/
    private Integer gift;

    private String giftStr;

    /**
     * 是否是小月龄商品 true表示是
     */
    private Boolean monthAgeGoods = Boolean.FALSE;

    private Integer monthAgeOrderStatus;

    private String monthAgeOrderStatusStr;

    private String monthAgeOrderDesc;

    @ApiModelProperty(value = "取消小月龄商品预约 ture - 可取消")
    private Boolean cancelMonthAgeOrder;

    @ApiModelProperty("物品清单")
    private String inventoryUrl;

    @ApiModelProperty(value = "预约单Id")
    private Long appointmentId;

    @ApiModelProperty("活动信息")
    private PromotionInfo promotionInfo;
}

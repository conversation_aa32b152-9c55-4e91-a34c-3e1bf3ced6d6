package com.stbella.order.server.order.month.res;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.math.BigDecimal;
import java.util.Date;

import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 押金支付记录
 * <AUTHOR>
 * @date 2022/12/27
 */
@Data
@ApiModel(value = "押金支付退款明细", description = "押金支付退款明细")
public class PayDepositDetailRecordVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "0收押金;1退押金")
    private Integer state;

    @ApiModelProperty(value = "0收押金;1退押金")
    private String stateName;

    @ApiModelProperty(value = "操作人名称")
    private String optName;

    @ApiModelProperty(value = "退回押金金额(单位元)")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "押金金额已收减去已退(单位元)")
    private BigDecimal depositAmount;

    @ApiModelProperty(value = "当前已累计收款押金(单位元)")
    private BigDecimal subtotalAmount;

    @ApiModelProperty(value = "最终退款方式(与income_record中的pay_type保持一致)，1=微信, 2=支付宝, 3=线下支付, 4=中信银行支付宝支付, 5=中信银行微信支付  8=在线pos机支付  9=C端小程序支付 10=其他")
    private Integer refundType;

    /**
     * @see  OmniPayTypeEnum
     */
    @ApiModelProperty(value = "最终退款方式(与income_record中的pay_type保持一致)，1=微信, 2=支付宝, 3=线下支付, 4=中信银行支付宝支付, 5=中信银行微信支付  8=在线pos机支付  9=C端小程序支付 10=其他")
    private String refundTypeName;

    @ApiModelProperty(value = "收款/退款 时间  完成时间/到账时间")
    private Date finishAt;

    @ApiModelProperty(value = "退款发起时间，年月日，时分秒；")
    private Date refundCreatedAt;

    @ApiModelProperty(value = "综合收款和退款排序时间")
    @JsonIgnore
    private Date sortAt;

    @ApiModelProperty(value = "第三方交易流水号 收款")
    private String incomeTransactionId;

    @ApiModelProperty(value = "第三方交易流水号 退款")
    private String refundTransactionId;
}

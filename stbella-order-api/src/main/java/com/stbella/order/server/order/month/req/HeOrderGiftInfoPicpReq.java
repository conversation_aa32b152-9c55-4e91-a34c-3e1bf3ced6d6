package com.stbella.order.server.order.month.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class HeOrderGiftInfoPicpReq implements Serializable {

    private static final long serialVersionUID = -6373022856318323700L;

    @ApiModelProperty("赠送记录编号")
    private String giftSn;

    @ApiModelProperty("战区")
    private List<Integer> warZoneList;

    @ApiModelProperty(value = "品牌")
    private List<Long> storeBrandList;

    @ApiModelProperty("门店")
    private List<Integer> storeIdList;

    @ApiModelProperty("客户Id")
    private Integer basicUid;

    @ApiModelProperty("客户名称")
    private String clientName;

    @ApiModelProperty("客户手机号")
    private String phone;

    @ApiModelProperty(value = "客户来源")
    private List<Integer> customerSourceList;

    @ApiModelProperty("赠送原因类型")
    private List<Integer> orderGiftReasonTypeList;

    @ApiModelProperty("关联订单号")
    private String orderSn;

    @ApiModelProperty("状态")
    private List<Integer> statusList;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "赠送订单号")
    private String giftOrderSn;

    @ApiModelProperty(value = "开始时间")
    private Date bizDateStart;

    @ApiModelProperty(value = "结束时间")
    private Date bizDateEnd;

    @ApiModelProperty(value = "时间类型 0:创建时间 1:审批通过时间 2:发放时间")
    private Integer dateType = 0;

    @ApiModelProperty(value = "页数")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "条数")
    private Integer pageSize = 10;

    private List<Integer> clientUidList;

    private List<Integer> creatorList;

    /** 测试门店Id集 **/
    private List<Integer> testStoreIdList;
}

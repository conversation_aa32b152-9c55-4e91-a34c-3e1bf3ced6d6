package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "STMOrderInfoMonthInfoVO对象", description = "订单后台-订单详情-订单信息-月子餐信息")
public class STMOrderInfoMonthInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "套餐名称")
    private String goodsName;

    @ApiModelProperty(value = "套餐天数")
    private Integer serviceDays;

    @ApiModelProperty(value = "房型Id")
    private Integer roomId;

    @ApiModelProperty(value = "房型名称")
    private String roomName;

    @ApiModelProperty(value = "套餐金额")
    private String goodsAmount;

    @ApiModelProperty(value = "签约金额")
    private String payableAmount;

    @ApiModelProperty(value = "签约金额(分)")
    private Integer payAmount;

    @ApiModelProperty(value = "套餐折扣率")
    private String goodsDiscount;

    @ApiModelProperty(value = "订单ID")
    private Integer orderId;

    @ApiModelProperty(value = "套餐ecp房型id")
    private Integer ecpRoomType;

    @ApiModelProperty(value = "套餐的自定义属性")
    private String definedProperty;

    @ApiModelProperty(value = "订单类型")
    private Integer orderType;
}

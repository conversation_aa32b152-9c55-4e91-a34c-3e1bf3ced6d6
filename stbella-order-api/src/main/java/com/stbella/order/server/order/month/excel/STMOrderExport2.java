package com.stbella.order.server.order.month.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.Data;

import java.io.Serializable;

@Data
@ExcelIgnoreUnannotated
//水平居中//垂直居中
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ContentFontStyle(fontName = "宋体")
public class STMOrderExport2 implements Serializable {

    private static final long serialVersionUID = -1L;

    @ExcelProperty(value = "序号", order = 1)
    private String no;

    @ExcelProperty(value = "订单类型", order = 2)
    private String orderType;

    @ExcelProperty(value = "订单号", order = 3)
    private String orderSn;

    @ExcelProperty(value = "战区", order = 4)
    private String areaName;

    @ExcelProperty(value = "品牌", order = 5)
    private String brandName;

    @ExcelProperty(value = "门店", order = 6)
    private String storeName;

    @ExcelProperty(value = "客户姓名", order = 7)
    private String customerName;

    @ExcelProperty(value = "客户成交跟进周期", order = 8)
    private String clientFollowUpPeriod;

    @ExcelProperty(value = "来谈渠道", order = 9)
    private String sourceChannel;

    @ExcelProperty(value = "预入住日期", order = 10)
    private String preCheckInDate;

    @ExcelProperty(value = "套餐名称", order = 11)
    private String goodsName;

    @ExcelProperty(value = "套餐天数", order = 12)
    private String serviceDays;

    @ExcelProperty(value = "套餐金额", order = 13)
    private String goodsAmount;

    @ExcelProperty(value = "套餐折扣", order = 14)
    private String packageDiscount;

    @ExcelProperty(value = "续住天数", order = 15)
    private String durationOfStay;

    @ExcelProperty(value = "续住房型", order = 16)
    private String rehousingType;

    @ExcelProperty(value = "续住费用金额", order = 17)
    private String amountRenewal;

    @ExcelProperty(value = "续住签约金额", order = 18)
    private String amountContractRenewal;

    @ExcelProperty(value = "房型变更周期", order = 19)
    private String roomTypeChangeCycle;

    @ExcelProperty(value = "原房型名称", order = 20)
    private String originalRoomName;

    @ExcelProperty(value = "新房型名称", order = 21)
    private String newRoomName;

    @ExcelProperty(value = "房型变更费用金额", order = 22)
    private String changeOriginalPrice;

    @ExcelProperty(value = "房型变更签单金额", order = 23)
    private String finalChangeOriginalPrice;

    @ExcelProperty(value = "胎数", order = 24)
    private String fetusNum;

    @ExcelProperty(value = "多胞胎费用金额", order = 25)
    private String babyAmount;

    @ExcelProperty(value = "多胞胎签单金额", order = 26)
    private String babyContractAmount;

    @ExcelProperty(value = "多胞胎费用折扣", order = 27)
    private String babyDiscount;

    @ExcelProperty(value = "节日费用金额", order = 28)
    private String holidayAmount;

    @ExcelProperty(value = "节日签单金额", order = 29)
    private String holidayContractAmount;

    @ExcelProperty(value = "订单总额", order = 30)
    private String orderAmount;

    @ExcelProperty(value = "订单签单总额", order = 31)
    private String orderContractAmount;

    @ExcelProperty(value = "入住总天数", order = 32)
    private String checkInTotalDays;

    @ExcelProperty(value = "订单折扣", order = 33)
    private String orderDiscount;

    @ExcelProperty(value = "净折扣率", order = 34)
    private String netDiscountRate;

    @ExcelProperty(value = "毛利率", order = 35)
    private String grossProfitMargin;

    @ExcelProperty(value = "已付金额", order = 36)
    private String paidAmount;

    @ExcelProperty(value = "待付金额", order = 37)
    private String remainingAmount;

    @ExcelProperty(value = "订单特殊标签", order = 38)
    private String orderTagName;

    @ExcelProperty(value = "创建时间", order = 39)
    private String createTime;

    @ExcelProperty(value = "订单所有人", order = 40)
    private String sellerName;

    @ExcelProperty(value = "更新人", order = 41)
    private String updatedAt;

    @ExcelProperty(value = "首次支付时间", order = 42)
    private String payFirstTime;

    @ExcelProperty(value = "业绩生效时间", order = 43)
    private String performanceEffectiveDate;

    @ExcelProperty(value = "是否已剔除业绩", order = 44)
    private String deletionPerformance;

    @ExcelProperty(value = "折扣审批状态", order = 45)
    private String discountApprovalStatus;

    @ExcelProperty(value = "订单状态", order = 46)
    private String orderStatus;

    @ExcelProperty(value = "支付状态", order = 47)
    private String payStatus;

    @ExcelProperty(value = "退款状态", order = 48)
    private String refundStatus;

    @ExcelProperty(value = "额外礼赠名称", order = 49)
    private String giftExtendName;

    @ExcelProperty(value = "规格名称", order = 50)
    private String giftExtendSkuName;

    @ExcelProperty(value = "额外礼赠数量", order = 51)
    private String giftExtendNum;

    @ExcelProperty(value = "额外礼赠金额", order = 52)
    private String giftExtendAmount;

    @ExcelProperty(value = "特殊备注事项", order = 53)
    private String remark;

    @ExcelProperty(value = "手机号", order = 54)
    private String customerMobile;

    public STMOrderExport2(String no, String orderType, String orderSn, String areaName, String brandName, String storeName, String customerName, String clientFollowUpPeriod, String sourceChannel, String preCheckInDate, String goodsName, String serviceDays, String goodsAmount, String packageDiscount, String durationOfStay, String rehousingType, String amountRenewal, String amountContractRenewal, String roomTypeChangeCycle, String originalRoomName, String newRoomName, String changeOriginalPrice, String finalChangeOriginalPrice, String fetusNum, String babyAmount, String babyContractAmount, String babyDiscount, String holidayAmount, String holidayContractAmount, String orderAmount, String orderContractAmount, String checkInTotalDays, String orderDiscount, String netDiscountRate, String grossProfitMargin, String paidAmount, String remainingAmount, String orderTagName, String createTime, String sellerName, String updatedAt, String payFirstTime, String performanceEffectiveDate, String deletionPerformance, String discountApprovalStatus, String orderStatus, String payStatus, String refundStatus, String giftExtendName, String giftExtendSkuName, String giftExtendNum, String giftExtendAmount, String remark, String customerMobile) {
        this.no = no;
        this.orderType = orderType;
        this.orderSn = orderSn;
        this.areaName = areaName;
        this.brandName = brandName;
        this.storeName = storeName;
        this.customerName = customerName;
        this.clientFollowUpPeriod = clientFollowUpPeriod;
        this.sourceChannel = sourceChannel;
        this.preCheckInDate = preCheckInDate;
        this.goodsName = goodsName;
        this.serviceDays = serviceDays;
        this.goodsAmount = goodsAmount;
        this.packageDiscount = packageDiscount;
        this.durationOfStay = durationOfStay;
        this.rehousingType = rehousingType;
        this.amountRenewal = amountRenewal;
        this.amountContractRenewal = amountContractRenewal;
        this.roomTypeChangeCycle = roomTypeChangeCycle;
        this.originalRoomName = originalRoomName;
        this.newRoomName = newRoomName;
        this.changeOriginalPrice = changeOriginalPrice;
        this.finalChangeOriginalPrice = finalChangeOriginalPrice;
        this.fetusNum = fetusNum;
        this.babyAmount = babyAmount;
        this.babyContractAmount = babyContractAmount;
        this.babyDiscount = babyDiscount;
        this.holidayAmount = holidayAmount;
        this.holidayContractAmount = holidayContractAmount;
        this.orderAmount = orderAmount;
        this.orderContractAmount = orderContractAmount;
        this.checkInTotalDays = checkInTotalDays;
        this.orderDiscount = orderDiscount;
        this.netDiscountRate = netDiscountRate;
        this.grossProfitMargin = grossProfitMargin;
        this.paidAmount = paidAmount;
        this.remainingAmount = remainingAmount;
        this.orderTagName = orderTagName;
        this.createTime = createTime;
        this.sellerName = sellerName;
        this.updatedAt = updatedAt;
        this.payFirstTime = payFirstTime;
        this.performanceEffectiveDate = performanceEffectiveDate;
        this.deletionPerformance = deletionPerformance;
        this.discountApprovalStatus = discountApprovalStatus;
        this.orderStatus = orderStatus;
        this.payStatus = payStatus;
        this.refundStatus = refundStatus;
        this.giftExtendName = giftExtendName;
        this.giftExtendSkuName = giftExtendSkuName;
        this.giftExtendNum = giftExtendNum;
        this.giftExtendAmount = giftExtendAmount;
        this.remark = remark;
        this.customerMobile = customerMobile;
    }

    public STMOrderExport2() {
    }
}

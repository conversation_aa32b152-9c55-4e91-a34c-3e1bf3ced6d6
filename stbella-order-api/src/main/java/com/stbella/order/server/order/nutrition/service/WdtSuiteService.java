package com.stbella.order.server.order.nutrition.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.stbella.core.base.PageVO;
import com.stbella.order.server.order.nutrition.entity.WdtSuitePO;
import com.stbella.order.server.order.nutrition.request.NutritionSuiteRequest;
import com.stbella.order.server.order.nutrition.request.TransactionAnalysisRequest;
import com.stbella.order.server.order.nutrition.response.CommodityAnalysisVO;
import com.stbella.order.server.order.nutrition.response.NutritionSuiteListVO;

import java.util.Date;

/**
 * <p>
 * 旺店通-组合装 服务类
 * </p>
 *
 * <AUTHOR> @since 2022-09-14
 */
public interface WdtSuiteService extends IService<WdtSuitePO> {

    /**
     * 获取旺店通组合装的数据
     *
     * @param start
     * @param end
     */
    void getSuiteList(Date start, Date end);

    /**
     * 获取组合装商品列表
     *
     * @param nutritionSuiteRequest
     * @return
     */
    PageVO<NutritionSuiteListVO> suiteList(NutritionSuiteRequest nutritionSuiteRequest);

    /**
     * 商品分析
     *
     * @param transactionAnalysisRequest
     * @return
     */
    CommodityAnalysisVO commodityAnalysis(TransactionAnalysisRequest transactionAnalysisRequest);
}

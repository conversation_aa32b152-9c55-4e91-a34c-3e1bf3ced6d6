package com.stbella.order.server.order.month.res;

import com.stbella.platform.order.api.req.ExtraInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class HeOrderVO implements Serializable {

    private static final long serialVersionUID = -582558387308084946L;

    @ApiModelProperty(value = "订单ID（主键自增)")
    private Integer orderId;

    @ApiModelProperty(value = "订单类型")
    private Integer orderType;

    @ApiModelProperty(value = "订单类型描述")
    private String orderTypeStr;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    @ApiModelProperty(value = "客户Id")
    private String basicId;

    @ApiModelProperty(value = "clientUid")
    private String clientUid;

    @ApiModelProperty("客户姓名")
    private String customerName;

    @ApiModelProperty("客户手机号")
    private String customerMobile;

    private String hidePhone;

    @ApiModelProperty("门店id")
    private Integer storeId;

    @ApiModelProperty("门店名称")
    private String storeName;

    @ApiModelProperty("品牌名称")
    private String typeName;

    @ApiModelProperty(value = "战区 0=未知 1=一战区 2=二战区 3=三战区")
    private Integer warZone;

    @ApiModelProperty(value = "战区描述")
    private String warZoneDesc;

    @ApiModelProperty(value = "订单原价")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "签单金额")
    private BigDecimal calPayableAmount;

    @ApiModelProperty(value = "应收金额")
    private BigDecimal calPayable;

    @ApiModelProperty(value = "已付金额")
    private BigDecimal realAmount;

    @ApiModelProperty(value = "累计已付金额")
    private BigDecimal paidAmount;

    @ApiModelProperty(value = "待付金额")
    private BigDecimal remainingAmount;

    @ApiModelProperty(value = "签单折扣率")
    private String signDiscount;

    @ApiModelProperty(value = "订单折扣")
    private String orderDiscount;

    @ApiModelProperty(value = "毛利率")
    private String grossProfitMargin;

    @ApiModelProperty(value = "折扣审批状态 0=无需审批 1=审批中 2=审批通过 3=审批失败 4=发起失败")
    private Integer approvalDiscountStatus;

    @ApiModelProperty(value = "折扣审批状态中文 0=无需审批 1=审批中 2=审批通过 3=审批失败 4=发起失败")
    private String approvalDiscountStatusStr;

    @ApiModelProperty(value = "客户来源")
    private Integer customerSource;

    @ApiModelProperty(value = "客户来源")
    private String customerSourceStr;

    @ApiModelProperty(value = "订单标签Id")
    private Integer orderTagId;

    @ApiModelProperty(value = "订单标签名称")
    private String orderTagName;

    @ApiModelProperty(value = "支付状态：0待支付，1未全付，2已付清，3已取消 4-超额支付")
    private Integer payStatus;

    @ApiModelProperty(value = "支付状态中文：0待支付，1未全付，2已付清，3已取消 4-超额支付")
    private String payStatusStr;

    @ApiModelProperty(value = "退款状态：0-无退款 1-部分退款中 2-部分退款 3-全部退款中 4-全部退款")
    private Integer refundStatus;

    @ApiModelProperty(value = "退款状态中文：0-无退款 1-部分退款中 2-部分退款 3-全部退款中 4-全部退款")
    private String refundStatusStr;

    @ApiModelProperty(value = "订单状态0-待付款 1-待发货 2-待收货 3-已完成 4-已关闭")
    private Integer orderStatus;

    @ApiModelProperty(value = "订单状态中文 0-未生效 1-待入住 2-入住中 3-已离店 4-已关闭 5-提前离馆")
    private String orderStatusStr;

    @ApiModelProperty("订单所有人Id")
    private Integer staffId;

    @ApiModelProperty("订单所有人名称")
    private String staffName;

    @ApiModelProperty("创建时间")
    private String createdAt;

    @ApiModelProperty(value = "首次支付时间")
    private String payFirstTime;

    @ApiModelProperty(value = "业绩生效时间")
    private String performanceEffectiveDate;

    @ApiModelProperty(value = "更新人Id")
    private Integer updateStaffId;

    @ApiModelProperty(value = "更新人姓名")
    private String updateByName;

    private String remark;

    @ApiModelProperty(value = "是否续签：false-否；true-是")
    private Boolean stayOver;

    @ApiModelProperty(value = "是否续签描述")
    private String stayOverStr;

    @ApiModelProperty(value = "是否减免：false-否；true-是")
    private Boolean discount;

    @ApiModelProperty(value = "是否减免描述")
    private String discountStr;

    @ApiModelProperty(value = "0-禁用 1-正常")
    private Integer isPerformanceNotice;

    @ApiModelProperty(value = "1-剔除 0-恢复")
    private Integer operationType;

    private BigDecimal income;

    private ExtraInfo extraInfo;

    @ApiModelProperty(value = "创建人Id")
    private Long createBy;

    @ApiModelProperty(value = "创建人姓名")
    private String createName;

    @ApiModelProperty(value = "应付总金额")
    private Integer payAmount;

    private Integer calPayableIntAmount;

    @ApiModelProperty(value = "订单标签 0-月子订单，2-随心拼订单")
    private Integer scene;

    @ApiModelProperty(value = "入住时间")
    private String checkInDate;

    private String sceneStr;

    @ApiModelProperty(value = "预计外派日期")
    private String estimatedDateAssignment;

    @ApiModelProperty(value = "三方产康专家")
    private String tripartiteHealthExperts;

    @ApiModelProperty(value = "产康金支付金额")
    private String productionAmountPay;

    @ApiModelProperty(value = "意向金支付金额")
    private String balanceAmount;

    @ApiModelProperty(value = "订单购买金额原价")
    private String buyOrderAmount;

    @ApiModelProperty(value = "订单购买金额签单")
    private String buyPayAmount;

    @ApiModelProperty(value = "减免总金额")
    private BigDecimal totalReductionAmount;

    @ApiModelProperty(value = "退回重付总金额")
    private BigDecimal totalRefreshPayAmount;

    @ApiModelProperty(value = "仅退款总金额")
    private BigDecimal totalOnlyRefundAmount;

    @ApiModelProperty(value = "折扣金额")
    private BigDecimal discountAmount;

    @ApiModelProperty(value = "予家订单-签单类型")
    private Integer signBillType;

    @ApiModelProperty(value = "予家订单-签单类型描述")
    private String signBillTypeStr;

    private String serveStartDate;

    private String serveEndDate;

}

package com.stbella.order.server.order.month.req;

import com.stbella.core.base.PageBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


@Data
@EqualsAndHashCode(callSuper = true)
public class QueryOrderGoodsInfoByPhoneOrNameReq extends PageBaseReq implements Serializable {

    private String keyword;

    private Integer storeId;

    private String orderSn;

}


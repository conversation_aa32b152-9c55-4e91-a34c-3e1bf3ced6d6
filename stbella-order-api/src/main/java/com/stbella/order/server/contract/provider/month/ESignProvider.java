package com.stbella.order.server.contract.provider.month;

import com.stbella.core.exception.BusinessException;
import com.stbella.order.server.contract.dto.*;

import java.security.NoSuchAlgorithmException;

public interface ESignProvider {

    /**
     * 【手机号认证】运营商3要素核身
     *
     * <AUTHOR>
     * @date 2023/7/10 18:08
     * @since 1.0.0
     * @param name
     * @param idNo
     * @param mobileNo
     * @return boolean
     * @throws
     */
    boolean telecom3Factors(String name, String idNo, String mobileNo);

    /**
     * 个人2要素（姓名、身份证号）信息比对
     *
     * @param name 用户姓名
     * @param idNo 证件号码
     */
    boolean doPersonIdentityComparison(String name, String idNo) throws BusinessException;

    /**
     * 个人3要素（姓名、身份证号、手机号）信息对比
     *
     * @param name     用户姓名
     * @param idNo     证件号码
     * @param mobileNo 手机号
     * @return
     * @throws BusinessException
     */
    boolean doPersonIdentityComparison(String name, String idNo, String mobileNo) throws BusinessException;

    /**
     * 创建个人签署账号
     *
     * @param personAccountDTO 请求参数
     * @return 个人账号id
     */
    String createPersonSignAccount(PersonAccountDTO personAccountDTO) throws BusinessException;

    /**
     * 修改个人签署账号信息
     *
     * @param updateAccountRequest 个人签署账号id
     * @return 更新的个人签署账号信息
     */
    String updatePersonSignAccount(UpdateAccountResquestDTO updateAccountRequest) throws BusinessException;

    /**
     * 获取模板文件上传地址
     *
     * @param fileByte 文件流
     * @return 模板文件id和上传链接
     */
    TemplateResponseDTO doUploadFileUrl(byte[] fileByte, String fileName) throws NoSuchAlgorithmException;

    /**
     * 上传文件流
     */
    String uploadFileStream(byte[] fileByte, String fileName) throws NoSuchAlgorithmException;

    /**
     * 对上传的合同模板文件填充相关合同内容
     *
     * @param contractParamFillRequest 需填充的合同参数
     * @return 待签署文件相关信息
     */
    ContractParamFillResponseDTO doFillContentTemplate(ContractParamFillRequestDTO contractParamFillRequest) throws BusinessException;

    /**
     * 创建机构签署账号
     *
     * @param companyAccountRequestDTO 机构相关参数信息
     * @return 账号id
     */
    String createCompanyAccount(CompanyAccountRequestDTO companyAccountRequestDTO);

    /**
     * 查询机构信息，有状态

     * @return 账号id
     */
    String queryCompanyAccount(String orgId);

    /**
     * 创建机构印章
     *
     * @param requestDTO 机构相关参数信息
     * @return 账号id
     */
    String createSealsOfficialTemplate(CompanySealRequestDTO requestDTO);

    /**
     * 查询机构印章

     * @return 账号id
     */
    String queryCompanySealList(String orgId);

    /**
     * 获取组织机构实名认证地址，使用这个接口的前提必须在e签宝创建组织账号
     *
     * @param companyAuthenticationRequest 认证请求参数
     * @param orgId                        组织机构账号id
     * @return 企业实名认证地址及相关信息
     */
    CompanyAuthenticationDTO doCompanyAuthentication(CompanyAuthenticationRequestDTO companyAuthenticationRequest, String orgId);

    /**
     * 设置企业自动授权盖章签署
     *
     * @param orgId 组织机构id
     * @return true表示设置成功   false表示失败
     */
    Boolean doSetCompanyAutoGrantAuthentication(String orgId) throws BusinessException;

    /**
     * 查询企业认证状态信息
     *
     * @param flowId 企业实名认证流程id
     * @return 企业认证状态信息
     */
    CompanyAuthenticationResponseDTO getCompanyAuthenticationState(String flowId) throws BusinessException;

    /**
     * 发起一步签署流程
     *
     * @param contractSignStartRequestDTO 发起流程请求相关参数
     * @return 流程id
     */
    String doInitiateSigningProcess(ContractSignStartRequestDTO contractSignStartRequestDTO) throws BusinessException;

    /**
     * 获取签署地址
     *
     * @param accountId 签署人在e签宝创建的账号id
     * @param flowId    签署流程返回的流程id
     * @return 签署链接（目前确认的方案是使用短链有效期30天，调用自己的短信中心发送给客户）
     */
    SignAddressResponseDTO getSignAddress(String accountId, String flowId) throws BusinessException;

    /**
     * 搜索关键字位置坐标
     *
     * @param keywords 关键字列表，逗号分割；注意要英文的逗号，不能中文逗号切不要有特殊字符
     * @param fileId   文件id
     * @return 关键字位置坐标信息
     */
    SignPositionResponseDTO searchWordsPosition(String keywords, String fileId) throws BusinessException;

    /**
     * 查询签署流程状态
     *
     * @param flowId 签署流程id
     * @return 签署流程相关信息
     */
    SignCompleteResponseDTO getSignStatus(String flowId) throws BusinessException;

    /**
     * 合同文件下载
     *
     * @param flowId 对应的合同流程id
     * @return 下载地址
     */
    DownloadUrlResponseDTO downloadContractFile(String flowId) throws BusinessException;

    /**
     * 注销个人在e签宝的签署账号
     *
     * @param accountId 账户id
     * @return true or false
     */
    Boolean cancellationAccount(String accountId) throws BusinessException;

    /**
     * 签署流程撤销
     *
     * @param flowId 签署流程id
     * @return true or false
     */
    Boolean cancelSignFlow(String flowId) throws BusinessException;

    /**
     * 查询个人签署账号
     * @param thirdPartyUserId 第三方平台的用户id
     * @return 查询个人签署账号信息
     */
    PersonAccountDTO getByThirdId(String thirdPartyUserId) throws BusinessException;

}

package com.stbella.order.server.contract.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


@Data
@ApiModel(value = "合同提醒列表数量")
public class ContractRemindCountVO {

    @ApiModelProperty(value = "订单类合同")
    private Integer orderInfoSize;

    @ApiModelProperty(value = "附属类合同")
    private Integer appurtenanceInfoSize;


}

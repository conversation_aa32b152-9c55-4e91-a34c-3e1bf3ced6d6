package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "STMOrderInfoGiftVO对象", description = "订单后台-订单详情-额外礼赠")
public class STMOrderInfoGiftVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "合计数量")
    private String totalQuantity;

    @ApiModelProperty(value = "总价值")
    private String globalValue;

    @ApiModelProperty(value = "总成本")
    private String allCost;

    @ApiModelProperty(value = "额外礼赠列表")
    private List<STMOrderInfoGiftListVO> orderInfoGiftListVOList;
}

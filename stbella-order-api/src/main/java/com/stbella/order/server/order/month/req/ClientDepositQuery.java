package com.stbella.order.server.order.month.req;

import com.stbella.core.base.PageBaseReq;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 押金支付记录
 * <AUTHOR>
 * @date 2022/12/27
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "押金支付记录", description = "押金支付记录")
public class ClientDepositQuery extends PageBaseReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "姓名/手机号")
    private String keyword;

    @ApiModelProperty(value = "门店列表")
    private List<Integer> storeIds;

}

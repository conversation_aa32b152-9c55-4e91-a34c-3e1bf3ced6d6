package com.stbella.order.server.order.order.req;

import com.stbella.core.base.BasePageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class ProductionRealPaidReq extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "门店id不能为空")
    @ApiModelProperty(value = "门店id")
    private Integer storeId;

    @NotNull(message = "核销开始时间不能为空")
    @ApiModelProperty(value = "核销开始时间")
    private Date writeOffStartDate;

    @NotNull(message = "核销结束时间不能为空")
    @ApiModelProperty(value = "核销结束时间")
    private Date writeOffEndDate;

    @ApiModelProperty(value = "姓名/手机号")
    private String keyword;

    @ApiModelProperty(value = "产康师id")
    private Long therapistId;

    @ApiModelProperty(value = "0:自营 1:三方", notes = "产康核销业绩核销记录自营详情/三方详情标识")
    private Integer realPaidServeType;
}

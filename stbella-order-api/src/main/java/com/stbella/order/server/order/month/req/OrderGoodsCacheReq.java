package com.stbella.order.server.order.month.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 月子订单其他信息对象
 * </p>
 *
 * <AUTHOR> @since 2022-10-29
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "OrderGoodsSavedRealTimeReq对象", description = "套餐信息缓存入参")
public class OrderGoodsCacheReq extends OrderCacheBaseReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "套餐父类ID")
    @NotNull(message = "套餐父类ID不能为空")
    private Integer parentId;

    @ApiModelProperty(value = "套餐ID")
    @NotNull(message = "套餐ID不能为空")
    private Integer goodsId;

    @ApiModelProperty(value = "规格ID")
    @NotNull(message = "规格ID不能为空")
    private Integer skuId;

    @ApiModelProperty(value = "套餐应收金额")
    private BigDecimal goodsReceivableAmount;
}

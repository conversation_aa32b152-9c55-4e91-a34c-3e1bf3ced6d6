package com.stbella.order.server.order.month.res;

import com.stbella.core.excel.Column;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "QueryOrderPageVO对象", description = "订单后台查询列表")
public class QueryOrderPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单ID（主键自增)")
    private Integer orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    @ApiModelProperty("客户姓名")
    private String customerName;

    @ApiModelProperty("客户手机号")
    private String customerMobile;

    @ApiModelProperty("门店id")
    private Integer storeId;

    @ApiModelProperty("门店名称")
    private String storeName;

    @ApiModelProperty("品牌名称")
    private String typeName;

    @ApiModelProperty(value = "套餐名称")
    private String goodsName;

    @ApiModelProperty(value = "套餐金额")
    private BigDecimal goodsAmount;

    @ApiModelProperty(value = "续住天数")
    private Integer durationOfStay;

    @ApiModelProperty(value = "入住总天数")
    private Integer checkInTotalDays;

    @ApiModelProperty(value = "订单总额")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "订单签单总额")
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "已付金额（去除退款）")
    private BigDecimal realAmount;

    @ApiModelProperty(value = "累计已付金额")
    private BigDecimal paidAmount;

    @ApiModelProperty(value = "待付金额")
    private BigDecimal remainingAmount;

    @ApiModelProperty(value = "产康金抵扣金额")
    private BigDecimal productionAmountPay;

    @ApiModelProperty(value = "订单折扣率")
    private String orderDiscount;

    @ApiModelProperty(value = "净折扣率")
    private String netDiscountRate;

    @ApiModelProperty(value = "毛利率")
    private String grossProfitMargin;

    @ApiModelProperty(value = "折扣审批状态 0=无需审批 1=审批中 2=审批通过 3=审批失败 4=发起失败")
    private Integer approvalDiscountStatus;

    @ApiModelProperty(value = "折扣审批状态中文 0=无需审批 1=审批中 2=审批通过 3=审批失败 4=发起失败")
    private String approvalDiscountStatusStr;

    @ApiModelProperty(value = "下单时来源：0未知，1小贝拉，2圣贝拉，3occ后台")
    private Integer source;

    @ApiModelProperty(value = "客户来源")
    private Integer customerSource;

    @ApiModelProperty(value = "客户来源")
    private String customerSourceStr;

    @ApiModelProperty(value = "订单标签Id")
    private Integer orderTagId;

    @ApiModelProperty(value = "订单标签名称")
    private String orderTagName;

    @ApiModelProperty(value = "支付状态：0待支付，1未全付，2已付清，3已取消 4-超额支付")
    private Integer payStatus;

    @ApiModelProperty(value = "支付状态中文：0待支付，1未全付，2已付清，3已取消 4-超额支付")
    private String payStatusStr;

    @ApiModelProperty(value = "退款状态：0-无退款 1-部分退款中 2-部分退款 3-全部退款中 4-全部退款")
    private Integer refundStatus;

    @ApiModelProperty(value = "退款状态中文：0-无退款 1-部分退款中 2-部分退款 3-全部退款中 4-全部退款")
    private String refundStatusStr;

    @ApiModelProperty(value = "订单状态0-未生效 1-待入住 2-入住中 3-已离店 4-已关闭 5-提前离馆")
    private Integer orderStatus;

    @ApiModelProperty(value = "订单状态中文 0-未生效 1-待入住 2-入住中 3-已离店 4-已关闭 5-提前离馆")
    private String orderStatusStr;

    @ApiModelProperty(value = "创建人Id")
    private Long createBy;

    @ApiModelProperty(value = "创建人姓名")
    private String createByName;

    @ApiModelProperty("创建时间")
    private String createdAt;

    @ApiModelProperty(value = "首次支付时间")
    private String payFirstTime;

    @ApiModelProperty(value = "业绩生效时间")
    private String performanceEffectiveDate;

    @ApiModelProperty(value = "业绩生效时间(日期)")
    private Date performanceEffective;

    @ApiModelProperty(value = "买家客户ID（ecp库的tab_client表主键）")
    private Integer clientUid;

    @ApiModelProperty(value = "全局客户ID")
    private Integer basicUid;

    @ApiModelProperty(value = "更新人Id")
    private Long updateBy;

    @ApiModelProperty(value = "更新人姓名")
    private String updateByName;

    @ApiModelProperty("更新时间")
    private String updatedAt;

    @ApiModelProperty(value = "是否能点击报单并计入业绩：0-不开启；1-开启")
    private Integer reportAccountPerformance;

    @ApiModelProperty(value = "0：展示剔除业绩按钮；1：展示恢复业绩按钮")
    private Integer operationType;

    @ApiModelProperty(value = "新老系统标志;0-老;1-新")
    private Integer oldOrNew;

    @ApiModelProperty(value = "创建该订单的任务ID（task表的id字段）")
    private Long taskId;

    private Integer isPerformanceNotice;

    /**
     * 套餐Id
     */
    private String goodsId;

    /**
     * BasicId
     */
    private String basicId;

}

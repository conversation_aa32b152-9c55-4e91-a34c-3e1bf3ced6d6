package com.stbella.order.server.order.order.req;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "OrderBasicReq", description = "订单信息查询")
public class OrderBasicReq extends BasicReq {

    private static final long serialVersionUID = 4780912340115077361L;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    @ApiModelProperty(value = "支付流水号")
    private String incomeSn;

    @ApiModelProperty(value = "订单ID")
    private Integer orderId;
}

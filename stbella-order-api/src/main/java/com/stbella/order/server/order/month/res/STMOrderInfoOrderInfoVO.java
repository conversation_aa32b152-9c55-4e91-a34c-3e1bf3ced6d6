package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "STMOrderInfoOrderInfoVO对象", description = "订单后台-订单详情-订单信息-订单信息")
public class STMOrderInfoOrderInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单金额")
    private String orderAmount;

    @ApiModelProperty(value = "订单金额（小数）")
    private BigDecimal orderAmountBigDecimal;

    @ApiModelProperty(value = "订单签约金额")
    private String payableAmount;

    @ApiModelProperty(value = "订单签约金额（小数）")
    private BigDecimal payableAmountBigDecimal;

    @ApiModelProperty(value = "订单折扣率")
    private String orderDiscount;

    @ApiModelProperty(value = "净折扣率")
    private String netDiscountRate;

    @ApiModelProperty(value = "毛利率")
    private String grossProfitMargin;

    @ApiModelProperty(value = "入住总天数")
    private Integer checkInTotalDays;

    @ApiModelProperty(value = "特殊订单标签")
    private String orderTagName;

    @ApiModelProperty(value = "特殊订单凭证")
    private List<String> voucherUrlList;

    @ApiModelProperty(value = "特殊事项备注")
    private String remark;
}

package com.stbella.order.server.order.month.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel(value = "SaleQueryByOrderRequest", description = "根据订单销售查询请求类")
public class SaleQueryByOrderRequest implements Serializable {

    private static final long serialVersionUID = -8007063890977291608L;

    @ApiModelProperty(value = "订单类型;0-母婴;1-予家", required = true)
    @NotNull(message = "订单类型不能为空")
    private Integer orderType;

    @ApiModelProperty(value = "订单号", required = true)
    @NotNull(message = "订单号不能为空")
    private String orderNo;


}

package com.stbella.order.server.contract.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: AddressVO
 * @date 2023/9/25 13:47
 */

@Data
@ApiModel(value = "省市区返回对象")
public class AddressVO implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "子级")
    List<AddressVO> childrenList;
    @ApiModelProperty(value = "id")
    private Integer id;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "名称后缀")
    private String suffix;
    @ApiModelProperty(value = "父级id")
    private Integer parentId;
    @ApiModelProperty(value = "类型 0-省;1市;2-区/县")
    private Integer type;
    @ApiModelProperty(value = "全称")
    private String nameSuffix;
}

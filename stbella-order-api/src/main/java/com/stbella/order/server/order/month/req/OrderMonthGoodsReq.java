package com.stbella.order.server.order.month.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 月子订单套餐对象
 * </p>
 *
 * <AUTHOR> @since 2022-10-29
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "OrderMonthGoodsReq对象", description = "月子订单套餐对象")
public class OrderMonthGoodsReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "套餐ID")
    private Integer goodsId;

    @ApiModelProperty(value = "套餐名称")
    private String goodsName;

    @ApiModelProperty(value = "商品图")
    private String goodsImage;

    @ApiModelProperty(value = "规格ID")
    private Integer skuId;

    @ApiModelProperty(value = "规格名称")
    private String skuName;

    @ApiModelProperty(value = "原价")
    private BigDecimal goodsPriceOrgin;

    @ApiModelProperty(value = "套餐应收金额")
    private BigDecimal goodsReceivableAmount;


}

package com.stbella.order.server.contract.res;

import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel(value = "合同提醒列表")
public class ContractRemindVO  {

    @ApiModelProperty(value = "订单类合同")
    private List<ContractInfo> orderInfoList;

    @ApiModelProperty(value = "附属类合同")
    private List<AppurtenanceInfo> appurtenanceInfoList;


    @Data
    public static class ContractInfo{

        @ApiModelProperty("门店id")
        private Integer storeId;

        @ApiModelProperty("门店名称")
        private String storeName;

        @ApiModelProperty("客户名称")
        private String clientName;

        @ApiModelProperty("客户id")
        private Integer clientUid;

        @ApiModelProperty("订单id")
        private Long orderId;

        @ApiModelProperty("商品名称")
        private String goodsName;

        @ApiModelProperty(value = "合同名称")
        private String contractName;

        @ApiModelProperty(value = "合同模板类型")
        private Integer templateContractType;

        @ApiModelProperty(value = "合同模板类型 中文")
        private String templateContractTypeName;

        @ApiModelProperty(value = "缴纳意向金过去天数")
        private Integer intentionDay;

        @ApiModelProperty(value = "优惠还剩天数")
        private Integer preferentialDay;

        @ApiModelProperty(value = "合同创建时间")
        private Date createdAt;

        @ApiModelProperty(value = "预约协议签订时间")
        private Date appointmentCreatedAt;

        @ApiModelProperty(value = "预约协议签订状态用于排序  0 =未签署 1=签署中 2=已归档")
        private Integer appointmentStatus;

        @ApiModelProperty(value = "新老系统标志;0或null-老;1-新")
        private Integer oldOrNew;
    }

    @Data
    public static class AppurtenanceInfo{

        @ApiModelProperty("门店id")
        private Integer storeId;

        @ApiModelProperty("门店名称")
        private String storeName;

        @ApiModelProperty("客户名称")
        private String clientName;

        @ApiModelProperty("客户id")
        private Integer clientUid;

        @ApiModelProperty("订单id")
        private Long orderId;

        @ApiModelProperty("商品名称")
        private String goodsName;

        @ApiModelProperty(value = "合同名称")
        private String contractName;

        @ApiModelProperty(value = "合同模板类型")
        private Integer templateContractType;

        @ApiModelProperty(value = "合同模板类型 中文")
        private String templateContractTypeName;


        @ApiModelProperty(value = "合同创建时间")
        private Date createdAt;


        @ApiModelProperty(value = "新老系统标志;0或null-老;1-新")
        private Integer oldOrNew;
    }

}

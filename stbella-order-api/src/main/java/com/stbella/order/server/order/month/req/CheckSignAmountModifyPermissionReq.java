package com.stbella.order.server.order.month.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 检查签单金额修改权限请求
 */
@Data
@ApiModel("检查签单金额修改权限请求")
public class CheckSignAmountModifyPermissionReq {

    @ApiModelProperty(value = "门店ID", required = true)
    @NotNull(message = "门店ID不能为空")
    private Integer storeId;

    private Integer orderType;

    private
}
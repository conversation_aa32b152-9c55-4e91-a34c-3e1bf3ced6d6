package com.stbella.order.server.order.month.res;

import com.stbella.platform.order.api.req.ExtraInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class OrderInfoNewVO implements Serializable {

    private static final long serialVersionUID = 51175796533590818L;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    @ApiModelProperty("门店id")
    private Integer storeId;

    @ApiModelProperty("门店名称")
    private String storeName;

    @ApiModelProperty("品牌名称")
    private String typeName;

    @ApiModelProperty(value = "战区 0=未知 1=一战区 2=二战区 3=三战区")
    private Integer warZone;

    @ApiModelProperty(value = "战区描述")
    private String warZoneDesc;

    @ApiModelProperty(value = "clientUid")
    private Integer clientUid;

    @ApiModelProperty("客户姓名")
    private String customerName;

    @ApiModelProperty("客户手机号")
    private String customerMobile;

    @ApiModelProperty(value = "订单状态中文 0-未生效 1-待入住 2-入住中 3-已离店 4-已关闭 5-提前离馆")
    private String orderStatusStr;

    @ApiModelProperty("提交人Id")
    private Integer staffId;

    @ApiModelProperty("订单所有人")
    private String staffName;

    @ApiModelProperty("提交人姓名")
    private String createName;

    @ApiModelProperty("提交人Id")
    private Long createBy;

    @ApiModelProperty(value = "业绩生效时间")
    private String performanceEffectiveDate;

    @ApiModelProperty(value = "折扣审批状态 0=无需审批 1=审批中 2=审批通过 3=审批失败 4=发起失败")
    private Integer approvalDiscountStatus;

    @ApiModelProperty(value = "折扣审批状态中文 0=无需审批 1=审批中 2=审批通过 3=审批失败 4=发起失败")
    private String approvalDiscountStatusStr;

    @ApiModelProperty(value = "创建时间")
    private String createdAt;

    @ApiModelProperty(value = "订单标签Id")
    private Integer orderTagId;

    @ApiModelProperty(value = "订单标签名称")
    private String orderTagName;

    private List<String> urlList;

    @ApiModelProperty(value = "特殊订单说明")
    private String remark;

    @ApiModelProperty(value = "预产期")
    private String predictBornDate;

    @ApiModelProperty(value = "胎数")
    private Integer fetusNum;

    @ApiModelProperty(value = "胎数描述")
    private String fetusNumStr;

    @ApiModelProperty(value = "预计入住时间")
    private String wantIn;

    private String wantInStr;

    @ApiModelProperty(value = "签单金额")
    private String payAmount;

    @ApiModelProperty(value = "应收金额")
    private String calPayableAmount;

    @ApiModelProperty(value = "订单原价")
    private String orderAmount;

    @ApiModelProperty(value = "订单折扣")
    private String orderDiscount;

    @ApiModelProperty(value = "签单折扣率")
    private String signDiscount;

    @ApiModelProperty(value = "折扣金额")
    private String discountAmount;

    @ApiModelProperty(value = "毛利率")
    private String grossProfitMargin;

    @ApiModelProperty(value = "订单扩展信息")
    private ExtraInfo extraInfo;

    @ApiModelProperty(value = "订单标签 0-月子订单，2-随心拼订单")
    private Integer scene;

    @ApiModelProperty(value = "订单标签描述")
    private String sceneStr;

    @ApiModelProperty(value = "订单类型")
    private String orderTypeStr;

    @ApiModelProperty(value = "选购原价")
    private String selectedAmount;

    @ApiModelProperty(value = "礼赠原价")
    private String giftAmount;

    @ApiModelProperty(value = "予家订单-签单类型")
    private Integer signBillType;

    @ApiModelProperty(value = "予家订单-签单类型描述")
    private String signBillTypeStr;
}

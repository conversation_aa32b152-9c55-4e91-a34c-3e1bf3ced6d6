package com.stbella.order.server.contract.service.month;

import com.stbella.order.server.order.month.res.MonthContractSignAgreementVO;
import com.stbella.order.server.order.month.res.MonthContractSignRecordVO;
import com.stbella.order.server.order.month.res.MonthOrderParamHistoryVO;

import java.util.List;

/**
 * <p>
 * 合同补充协议 服务类
 * </p>
 *
 * <AUTHOR>
/**
 * 废弃 已迁移 stbella-contract 服务
 * @see com.stbella.contract.api.MonthContractSignAgreementService
 */
public interface MonthContractSignAgreementService {


    /**
     * 生成补充协议
     * 补充协议失效
     */

    /**
     * 创建补充协议
     *
     * @param orderId          订单id
     * @param signRecord       合同
     * @param paramHistoryList 协议相关参数
     * @return 创建的协议id
     */
    Long create(Integer orderId, MonthContractSignRecordVO signRecord, List<MonthOrderParamHistoryVO> paramHistoryList,Integer templateContractType);

    /**
     * 失效补充协议
     *
     * @param id 协议id
     * @return 成功/失败
     */
    Boolean cancal(Long id);


    MonthContractSignAgreementVO getByContractAgreementId(Long id);
}

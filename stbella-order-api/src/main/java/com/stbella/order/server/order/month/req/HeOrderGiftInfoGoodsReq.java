package com.stbella.order.server.order.month.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HeOrderGiftInfoGoodsReq implements Serializable {

    private static final long serialVersionUID = 8613766963490144661L;

    @ApiModelProperty(value = "赠送订单ID")
    @NotNull(message = "赠送订单ID不能为空")
    private Long id;
}

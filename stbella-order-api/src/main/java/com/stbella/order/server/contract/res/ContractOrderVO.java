package com.stbella.order.server.contract.res;

import com.stbella.order.common.constant.TableFieldConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
@ApiModel(value = "订单下合同列表")
public class ContractOrderVO implements Serializable {

    private static final long serialVersionUID = 45717640179914002L;
    @ApiModelProperty(value = "订单类合同")
    private List<ContractInfo> orderInfoList;

    @ApiModelProperty(value = "附属类合同")
    private List<AppurtenanceInfo> appurtenanceInfoList;


    @Data
    public static class ContractInfo implements Serializable {

        private static final long serialVersionUID = 2324914581780573002L;
        @ApiModelProperty(value = "订单id")
        private Long orderId;

        @ApiModelProperty(value = "合同主键id")
        private Integer contractId;

        @ApiModelProperty(value = "0=大陆身份证 1=护照 2=香港来往大陆通行证 3=澳门来往大陆通行证 4=台湾来往大陆通行证")
        private Integer certType;

        @ApiModelProperty(value = "预约协议 需要支付意向金金额 单位元")
        private String amountEarnest;

        @ApiModelProperty(value = "名称")
        private String contractName;

        @ApiModelProperty(value = "描述")
        private String contractDesc;

        @ApiModelProperty(value = "合同类型 1=E签宝合同 2=旧合同 3=纸质合同(标记“纸质合同”的戳)")
        private Integer contractType;

        @ApiModelProperty(value = "合同类型   1=>主合同，2=>附件类，3=>附属类：补充协议", required = true)
        private Integer templateType;

        @ApiModelProperty(value = TableFieldConstant.templateContractType)
        private Integer templateContractType;

        @ApiModelProperty(value = "是否签订")
        private Boolean sign;

        @ApiModelProperty(value = "0=创建主合同类接口 1=创建附件类接口 2=其他无需调用接口")
        private Integer apiRoute;


        @ApiModelProperty(value = "货币码")
        private String currency;


    }


    @Data
    public static class AppurtenanceInfo implements Serializable {

        private static final long serialVersionUID = -9021633880416713300L;
        @ApiModelProperty(value = "合同主键id")
        private Long contractId;

        @ApiModelProperty(value = "订单id")
        private Long orderId;

        @ApiModelProperty(value = "名称")
        private String contractName;

        @ApiModelProperty(value = "描述")
        private String contractDesc;

        @ApiModelProperty(value = "合同类型 1=E签宝合同 2=旧合同 3=纸质合同(标记“纸质合同”的戳)")
        private Integer contractType;

        @ApiModelProperty(value = "合同类型   1=>主合同，2=>附件类，3=>附属类：补充协议", required = true)
        private Integer templateType;

        @ApiModelProperty(value = TableFieldConstant.templateContractType)
        private Integer templateContractType;

        @ApiModelProperty(value = "是否签订")
        private Boolean sign;

        @ApiModelProperty(value = "0=创建主合同类接口 1=创建附件类接口 2=其他无需调用接口")
        private Integer apiRoute;

        @ApiModelProperty(value = "0=大陆身份证 1=护照 2=香港来往大陆通行证 3=澳门来往大陆通行证 4=台湾来往大陆通行证")
        private Integer certType;

    }

}

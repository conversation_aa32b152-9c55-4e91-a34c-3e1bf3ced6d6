package com.stbella.order.server.order.month.res;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 合同参数模版配置表,新版E签宝
 * </p>
 *
 * <AUTHOR> @since 2021-11-04
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "EsignTemplateParamConfig对象", description = "合同参数模版配置表,新版E签宝")
public class MonthEsignTemplateParamConfigVO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "订单类型")
    private Integer orderType;

    @ApiModelProperty(value = "参数名称")
    private String name;

    @ApiModelProperty(value = "参数标识符,和合同模版中的占位符一一对应")
    private String mark;

    @ApiModelProperty(value = "排序号,正序排序")
    private Integer sort;

    /**
     * 逻辑删除标志
     */
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

}

package com.stbella.order.server.contract.service.month;

import com.stbella.order.server.order.month.res.MonthHeUserEsignVO;

/**
 * 废弃 已迁移 stbella-contract 服务
 * @see com.stbella.contract.api.MonthUserEsignService
 */
public interface MonthUserEsignService {

    /**
     * 根据名称和证件号查询对应的身份认证
     *
     * @param name     姓名
     * @param idCardNo 证件号
     * @return 匹配的信息
     */
    MonthHeUserEsignVO get(String name, String idCardNo);

    MonthHeUserEsignVO queryByConditionOne(String name, String phone, Integer idCardType, String idCardNo);

}

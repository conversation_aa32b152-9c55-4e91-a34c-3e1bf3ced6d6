package com.stbella.order.server.order.production.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: Seven
 * @time: 2022/10/11 16:34
 */
@Data
@ApiModel(value = "预约页面用户和商品的数据", description = "预约页面用户和商品的数据")
public class ProductionBehalfGoodsAndCustomerInfoVo implements Serializable {

    @ApiModelProperty("商品信息")
    private ProductionAppointmentGoodsSkuInfoVo productionAppointmentGoodsSkuInfoVo;

}

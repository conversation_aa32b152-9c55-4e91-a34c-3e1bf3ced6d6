package com.stbella.order.server.order.month.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class PicpOldOrderInfoQuery implements Serializable {

    private static final long serialVersionUID = -7745431139414198884L;

    @ApiModelProperty("订单ID")
    private Integer orderId;

    @ApiModelProperty(value = "合同状态 0 =未签署 1=签署中 2=已归档", required = true)
    private Integer contractStatus;
}

package com.stbella.order.server.contract.res;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ContractAddressVO implements Serializable {

    private static final long serialVersionUID = 45717640179914002L;
    @ApiModelProperty(value = "e签宝合同查看地址")
    private String checkLongUrl;

    @ApiModelProperty(value = "e签宝合同下载地址，有效期一个小时")
    private String downloadUrl;

    @ApiModelProperty(value = "e签宝合同查看短链地址")
    private String shortUrl;

    @ApiModelProperty(value = "合同签订入口0=未知;1=h5;2=微信;3=支付宝")
    private Integer signFrom;
}

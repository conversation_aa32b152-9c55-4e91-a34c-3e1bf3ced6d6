package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class HeOrderGiftInfoPicpVO implements Serializable {

    private static final long serialVersionUID = -2558452213054807207L;

    private Long id;

    @ApiModelProperty(value = "赠送记录编号")
    private String giftSn;

    @ApiModelProperty(value = "赠送原因类型")
    private Integer giftReasonType;

    @ApiModelProperty(value = "赠送原因类型描述")
    private String giftReasonTypeStr;

    @ApiModelProperty(value = "关联订单号")
    private String orderSn;

    @ApiModelProperty(value = "战区")
    private Integer warZone;

    @ApiModelProperty(value = "战区描述")
    private String warZoneDesc;

    @ApiModelProperty("品牌名称")
    private String brandName;

    @ApiModelProperty("门店id")
    private Integer storeId;

    @ApiModelProperty("门店名称")
    private String storeName;

    @ApiModelProperty(value = "客户Id")
    private Integer basicUid;

    @ApiModelProperty(value = "clientUid")
    private Integer clientUid;

    @ApiModelProperty("客户姓名")
    private String customerName;

    @ApiModelProperty("客户手机号")
    private String customerMobile;

    @ApiModelProperty(value = "客户脱敏手机号")
    private String clientPhone;

    @ApiModelProperty(value = "客户来源")
    private Integer customerSource;

    @ApiModelProperty(value = "客户来源")
    private String customerSourceStr;

    @ApiModelProperty(value = "赠送商品价值")
    private BigDecimal giftGoodsPrice;

    @ApiModelProperty(value = "赠送订单号")
    private String giftOrderSn;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "创建人")
    private Long createBy;

    @ApiModelProperty(value = "创建人描述")
    private String createByStr;

    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "状态描述")
    private String statusStr;

    @ApiModelProperty(value = "审批时间")
    private Date approveFinish;

    @ApiModelProperty(value = "发放时间")
    private Date releaseTime;

    @ApiModelProperty(value = "赠送具体原因")
    private String giftSpecificReason;

    private Date performanceEffectiveDate;

    private Integer orderId;

    /** 订单类型 */
    private Integer orderType;

    /** 购物车ID */
    private Integer cartId;

    /** 发布链接 */
    private String linkUrl;

    /** 订单其他备注 */
    private String remark;

}

package com.stbella.order.server.order.month.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class OrderGoodsInfoReq implements Serializable {

    private static final long serialVersionUID = -156776450758572092L;

    @ApiModelProperty(value = "订单Id")
    @NotNull(message = "订单Id不能为空")
    private Integer orderId;

    @ApiModelProperty(value = "订单对应商品唯一标识")
    @NotNull(message = "商品标识不能为空")
    private Integer orderGoodsId;
}

package com.stbella.order.server.order.month.res;

import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 下单的时候费用明细返回对象
 * </p>
 *
 * <AUTHOR> @since 2022-10-29
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "订单剩余可退金额查询", description = "订单剩余可退金额查询")
public class MonthIncomeRefundVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单剩余最多可退金额")
    private BigDecimal orderRefundAmountCurrentPrice;

    @ApiModelProperty(value = "所有支付记录中已经发起的退款申请(包含退款成功/申请/审批中)")
    private BigDecimal applyRefundPrice;
}

package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "PayFirstRecordVO对象", description = "订单首次支付-客户信息")
public class PayFirstRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户uid")
    private Integer basicUid;

    @ApiModelProperty(value = "客户名称")
    private String name;

    @ApiModelProperty(value = "客户手机号")
    private String phone;

}

package com.stbella.order.server.order.month.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 下单前获取当前订单的折扣
 * </p>
 *
 * <AUTHOR> @since 2022-10-29
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "OrderDiscountsCacheReq对象", description = "下单前获取当前订单的折扣")
public class OrderDiscountsCacheReq extends OrderCacheBaseReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "套餐Id")
    @NotNull(message = "套餐Id不能为空")
    private Integer goodsId;

}

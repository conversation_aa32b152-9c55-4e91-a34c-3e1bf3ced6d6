package com.stbella.order.server.contract.service.month;

import com.stbella.core.result.Result;
import com.stbella.order.server.contract.req.CanPaperContractQuery;
import com.stbella.order.server.contract.req.CreatePaperContractReq;
import com.stbella.order.server.contract.req.SavePaperContractReq;
import com.stbella.order.server.contract.res.CanContractSignPaperVO;
import com.stbella.order.server.contract.res.ContractSignPaperVO;

import java.util.List;

/**
 *
 * <p>
 * 纸质合同表 服务类
 * </p>
 *
 * <AUTHOR> @since 2022-11-02
 */
/**
 * 废弃 已迁移 stbella-contract 服务
 * @see com.stbella.contract.api.ContractSignRecordPaperService
 */
public interface ContractSignRecordPaperService {

    /**
     * 保存纸质合同
     *
     * @param createPaperContractReq 参数
     * @return
     */
    Result<Integer> createPaperContract(CreatePaperContractReq createPaperContractReq);

    Result<ContractSignPaperVO> queryById(Integer id);

    Result<Boolean> deleteById(SavePaperContractReq req);

    /**
     * 纸质合同列表配置
     *
     * @param canPaperContractQuery@return {@link Result}<{@link List}<{@link ContractSignPaperVO}>>
     */
    Result<List<CanContractSignPaperVO>> queryCanPaperList(CanPaperContractQuery canPaperContractQuery);
}

package com.stbella.order.server.order.production.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description:
 * @author: Seven
 * @time: 2022/10/11 16:34
 */
@Data
@ApiModel(value = "预约页面用户产康师时间轴具体数据（时间段）", description = "预约页面用户产康师时间轴具体数据（时间段）")
public class ProductionBehalfTimeMinuteQuantumVo implements Serializable {

    /**
     * 预约页面 产康师时间
     */

    @ApiModelProperty("开始时间")
    private Date start;

    @ApiModelProperty("结束时间")
    private Date end;

    /**
     * 0 可预约
     * 1 产康师时间被占用
     * 2 器材限制无法预约
     * 3 代客预约||用户预约但上单不是当前客户 造成的+15分钟等待期
     * 4 剩余时间小于服务时间
     */
    @ApiModelProperty("0 可预约 1 产康师时间被占用 2 器材限制 3 代客预约||用户预约但上单不是当前客户 造成的+15分钟等待期 4 剩余时间小于服务时间")
    private Integer appointment;

    public static ProductionBehalfTimeMinuteQuantumVo of(Date start, Date end) {
        ProductionBehalfTimeMinuteQuantumVo productionBehalfTimeHourQuantumVo = new ProductionBehalfTimeMinuteQuantumVo();
        productionBehalfTimeHourQuantumVo.setStart(start);
        productionBehalfTimeHourQuantumVo.setEnd(end);
        productionBehalfTimeHourQuantumVo.setAppointment(1);
        return productionBehalfTimeHourQuantumVo;
    }

}

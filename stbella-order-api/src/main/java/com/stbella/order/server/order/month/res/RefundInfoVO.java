package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class RefundInfoVO implements Serializable {

    private static final long serialVersionUID = -9028967548449409324L;

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "退款订单编号")
    private String refundOrderSn;

    @ApiModelProperty(value = "实际退款金额")
    private String actualAmount;

    @ApiModelProperty(value = "退款原因(0-99正常退款原因) (100以上是非正常退款原因):退款原因:0-默认 1-宝妈身体原因 2-宝宝原因 3-疫情政策 4-距离原因 5-家人因素 6-环境设施 7-馆内服务 8-用户需求 9-套餐变更 10-门店承接问题 11-客诉赔偿 12-退押金 13-其他    \n100-月子餐问题 101-护理问题 102-产康问题 103-管内活动问题 104-第三方(宝宝艺术照)问题 105-服务态度问题 106其他")
    private Integer refundReason;

    private String refundReasonStr;

    @ApiModelProperty(value = "退款状态，1：审批中 2：审批失败  3：审批成功/等待打款/待确认 4：退款已到帐/已确认 5：到账失败/已拒绝")
    private Integer status;

    private String statusStr;

    @ApiModelProperty(value = "退款类型：1=正常退款;2=⾮正常退款（客诉）")
    private Integer refundReasonType;

    private String refundReasonTypeStr;

    @ApiModelProperty(value = "退款方式 1=微信, 2=支付宝, 3=线下支付, 4=中信银行支付宝支付, 5=中信银行微信支付  8=在线pos机支付  9=C端小程序支付 10=其他")
    private Integer refundType;

    private String refundTypeStr;


    @ApiModelProperty(value = "退款凭证")
    private List<String> refundVoucherList;

    @ApiModelProperty(value = "提交时间")
    private String createdAt;

    @ApiModelProperty(value = "退款说明")
    private String remark;

    @ApiModelProperty(value = "提交人ID")
    private Integer applyId;

    @ApiModelProperty(value = "提交人姓名")
    private String applyName;

    @ApiModelProperty(value = "开户名")
    private String name;

    @ApiModelProperty(value = "开户行+支行")
    private String bankName;

    @ApiModelProperty(value = "银行卡号")
    private String bankNumber;

    private List<RefundRecord> refundRecordList;

    private List<RefundProcess> refundProcessList;

    @Data
    public static class RefundRecord implements Serializable {

        private static final long serialVersionUID = 5407377081647252388L;

        @ApiModelProperty(value = "id")
        private Long id;

        @ApiModelProperty(value = "下单时的商品名称")
        private String goodsName;

        @ApiModelProperty(value = "规格名称")
        private String skuName;

        @ApiModelProperty(value = "退款数量")
        private Integer refundNum;

        @ApiModelProperty(value = "商品原价")
        private String goodsPriceOrgin;

        @ApiModelProperty(value = "实付金额")
        private String goodsPricePay;

        @ApiModelProperty(value = "退款金额")
        private String refundAmount;

        @ApiModelProperty(value = "商品类型")
        private String giftStr;

        @ApiModelProperty(value = "购买数量")
        private Integer goodsNum;

    }

    @Data
    public static class RefundProcess implements Serializable {

        private static final long serialVersionUID = 8507330022230565231L;

        @ApiModelProperty(value = "id")
        private Integer id;

        @ApiModelProperty(value = "退款订单编号")
        private String refundOrderSn;

        @ApiModelProperty(value = "退款金额")
        private String actualAmount;

        @ApiModelProperty(value = "退款状态，1：审批中 2：审批失败  3：审批成功/等待打款/待确认 4：退款已到帐/已确认 5：到账失败/已拒绝")
        private Integer status;

        private String statusStr;

        @ApiModelProperty(value = "收款Id")
        private Integer incomeId;

        @ApiModelProperty(value = "支付流水编号")
        private String incomeSn;

        @ApiModelProperty(value = "退款成功时间")
        private String finishAt;

        @ApiModelProperty(value = "退款申请时间")
        private String createdAt;
    }

}

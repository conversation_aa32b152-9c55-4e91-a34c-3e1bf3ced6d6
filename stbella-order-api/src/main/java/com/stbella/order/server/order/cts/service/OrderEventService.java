package com.stbella.order.server.order.cts.service;

import com.stbella.order.server.order.cts.dto.RefundNotifyMqDTO;
import com.stbella.order.server.order.cts.request.order.*;
import com.stbella.order.server.order.nutrition.dto.PayNotifyMqDTO;

/**
 * 订单事件
 *
 * <AUTHOR>
 * @date 2022-03-18 14:19
 * @sine 1.0.0
 */
public interface OrderEventService {

    /**
     * 雇主-育婴师订单创建
     *
     * @param request
     * @return java.lang.String
     * @throws
     * <AUTHOR>
     * @date 2022/3/21 00:00
     * @since 1.0.0
     */
    String createOrderBabySitter(BabySitterOrderCreateRequest request);

    /**
     * 雇主-母婴订单创建
     *
     * @param request
     * @return java.lang.String
     * @throws
     * <AUTHOR>
     * @date 2022/3/21 00:00
     * @since 1.0.0
     */
    String createOrderAuntSitter(AuntSitterOrderCreateRequest request);

    /**
     * 育婴师-培训课程订单创建
     *
     * @param request
     * @return java.lang.String
     * @throws
     * <AUTHOR>
     * @date 2022/4/12 10:25
     * @since 1.0.0
     */
    String createOrderCourse(CourseOrderCreateRequest request);

    /**
     * 育婴师-保证金订单创建
     *
     * @param request
     * @return java.lang.String
     * @throws
     * <AUTHOR>
     * @date 2022/4/12 10:25
     * @since 1.0.0
     */
    String createOrderMargin(MarginOrderCreateRequest request);

    /**
     * 育婴师-管理费订单创建
     *
     * @param request
     * @return java.lang.String
     * @throws
     * <AUTHOR>
     * @date 2022/4/12 10:25
     * @since 1.0.0
     */
    String createOrderServeFee(ServeFeeOrderCreateRequest request);


    /**
     * 育婴师-通用商品订单创建
     *
     * @param request
     * @return
     */
    String createOrderGeneralSitter(GeneralSitterOrderCreateRequest request);


    /**
     * 雇主-通用商品订单创建
     *
     * @param request
     * @return
     */
    String createOrderGeneralCustomer(GeneralCustomerOrderCreateRequest request);


    /**
     * 订单取消
     *
     * @param request
     * @return java.lang.Boolean
     * @throws
     * <AUTHOR>
     * @date 2022/3/21 14:25
     * @since 1.0.0
     */
    Boolean cancelOrder(OrderCancelBaseRequest request);

    /**
     * 订单支付成功回调
     *
     * @param payNotifyMq
     * @param orderNo
     * @return java.lang.Boolean
     * @throws
     * <AUTHOR>
     * @date 2022/3/22 16:31
     * @since 1.0.0
     */
    Boolean payNotify(PayNotifyMqDTO payNotifyMq, String orderNo);

    /**
     * 申请退款
     *
     * @param request
     * @return java.lang.Long
     * @throws
     * <AUTHOR>
     * @date 2022/3/24 13:56
     * @since 1.0.0
     */
    Long applyRefund(OrderApplyRefundRequest request);

    /**
     * 退款通知
     *
     * @param refundNotifyMqDTO
     * @return
     */
    Boolean refundNotify(RefundNotifyMqDTO refundNotifyMqDTO);
}

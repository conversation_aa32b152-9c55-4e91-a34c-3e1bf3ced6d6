package com.stbella.order.server.order.cts.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.stbella.core.base.PageVO;
import com.stbella.order.server.order.cts.entity.OrderCtsPhpRefundPO;
import com.stbella.order.server.order.cts.request.order.OrderPHPAddRefundRequest;
import com.stbella.order.server.order.cts.request.order.OrderPHPSearchRefundRequest;
import com.stbella.order.server.order.cts.response.order.OrderCtsPhpRefundForHelperResponse;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 到家php系统退款金额 服务类
 * </p>
 *
 * <AUTHOR> @since 2022-08-25
 */
public interface OrderCtsPhpRefundService extends IService<OrderCtsPhpRefundPO> {

    /**
     * 新增php老系统退款业绩
     *
     * @param request
     */
    void addAdminPHPRefund(OrderPHPAddRefundRequest request);

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageVO<OrderCtsPhpRefundPO> pageAdminPHPRefundList(OrderPHPSearchRefundRequest query);


    /**
     * 根据年月获取有效的php老系统退款业绩
     *
     * @param year
     * @param month
     * @return
     */
    List<OrderCtsPhpRefundForHelperResponse> getListPHPValidRefundForHelper(Integer year, Integer month);

    /**
     * 根据时间段筛选
     *
     * <AUTHOR>
     * @date 2023/1/31 19:53
     * @since 1.0.0
     * @param beginDate
     * @param endDate
     * @return java.util.List<com.stbella.order.server.order.cts.entity.OrderCtsPhpRefundPO>
     * @throws
     */
    List<OrderCtsPhpRefundPO> listByDateRange(Date beginDate, Date endDate);
}

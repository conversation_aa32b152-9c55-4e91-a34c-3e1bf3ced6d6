package com.stbella.order.server.contract.service.month;

import com.stbella.core.result.Result;
import com.stbella.order.server.contract.req.CreateAccessoryContractReq;
import com.stbella.order.server.contract.req.CreateMainContractReq;
import com.stbella.order.server.contract.req.OldContractSignReq;
import com.stbella.order.server.order.month.req.MonthContractSignRecordReq;
import com.stbella.order.server.order.month.req.OrderContractQuery;
import com.stbella.order.server.order.month.res.ContractSignRecordVO;
import com.stbella.order.server.order.month.res.MonthContractSignRecordVO;

import java.util.List;

/**
 * <p>
 * 签署记录表 服务类
 * </p>
 *
 * <AUTHOR>
/**
 * 废弃 已迁移 stbella-contract 服务
 * @see com.stbella.contract.api.ContractSignRecordService
 */
public interface MonthContractSignRecordService {


    /**
     * 生成合同填充
     * @param req
     * @return {@link MonthContractSignRecordVO}
     */
    Result<MonthContractSignRecordVO> createContract(CreateMainContractReq req);


    /**
     * 创建附属合同
     * @param createAccessoryContractQuery
     * @return {@link MonthContractSignRecordVO}
     */
    Result<MonthContractSignRecordVO> createAccessoryContract(CreateAccessoryContractReq createAccessoryContractQuery);

    /**
     * 获取订单所属的合同信息
     *
     * @param orderId 订单id
     * @return 合同
     */
    MonthContractSignRecordVO getByOrderId(Integer orderId,Integer templateContractType);

    /**
     * 重载合同接口,合同关联的订单参数信息变更后会调用该接口进行合同信息重载,重新生成合同签署文件
     *
     * @param id        合同id
     * @param storeType
     * @return 操作成功/失败
     */
    MonthContractSignRecordVO reloadContract(Long id, Integer storeType, Integer templateContractType);

    MonthContractSignRecordVO queryByCondition(MonthContractSignRecordReq monthContractSignRecordReq);

    MonthContractSignRecordVO getByContractSignId(Long id);

    /**
     * 老订单签署
     */
    void oldContractSign(OldContractSignReq oldContractSignReq);

    /**
     * 重置合同签署状态
     */
    void resetContract(Integer contractId);

    /**
     * 客户详情-已签署合同列表
     */
    List<ContractSignRecordVO> contractSignRecordListByClientId(OrderContractQuery query);
}

package com.stbella.order.server.contract.service.month;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stbella.order.server.order.month.res.MonthEsignTemplateVO;

/**
 * <p>
 * 合同模版表,新版E签宝 服务类
 * </p>
 *
 * <AUTHOR>
/**
 * 废弃 已迁移 stbella-contract 服务
 * @see com.stbella.contract.application.context.component.processor.SelectTemplateProcessor
 * 合同模板选择已使用规则引擎组件处理 rule-link
 */
public interface MonthEsignTemplateService  {

    /**
     * 查询合同模版列表 默认排序为创建时间倒序
     *
     * @param page 分页信息
     * @return 列表数据
     */
    Page<MonthEsignTemplateVO> getPage(Page page);

    /**
     * 根据类型获取可用的合同模版
     *
     * @param orderType    订单类型
     * @param storeType    门店类型
     * @param templateType 模版类型1:订单 2:附件 3:补充协议
     * @return 合同模版, 如果有多个的话会返回查询到的第一个
     */
    MonthEsignTemplateVO getByTypes(Integer orderType, Integer storeType, Integer templateType,Integer templateContractType);

    /**
     * 根据类型获取可用的合同模版（增加合同页数·）
     *
     * @param orderType    订单类型
     * @param storeType    门店类型
     * @param templateType 模版类型1:订单 2:附件 3:补充协议
     * @param pageNum      合同页数 为0表示 判断不正常
     * @return 合同模版, 如果有多个的话会返回查询到的第一个
     */
    MonthEsignTemplateVO getByTypes(Integer orderType, Integer storeType, Integer templateType,Integer templateContractType, Integer pageNum);

    /**
     * 需要特殊处理的门店通过门店ID获取
     *
     * @param storeId 门店ID
     * @return MonthEsignTemplatePO 模版信息
     */
    MonthEsignTemplateVO specialGetByStoreId(Integer storeId,Integer orderType, Integer storeType, Integer templateType, Integer templateContractType, Integer pageNum);

    MonthEsignTemplateVO getByTemplateId(Long id);

}

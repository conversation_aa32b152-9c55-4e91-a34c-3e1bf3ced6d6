package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "SimpleStoreInfoVO对象", description = "SimpleStoreInfoVO")
public class SimpleStoreInfoVO implements Serializable {


    @ApiModelProperty(value = "门店id")
    private Integer storeId;

    @ApiModelProperty(value = "门店名")
    private String storeName;

    public SimpleStoreInfoVO(Integer storeId, String storeName) {
        this.storeId = storeId;
        this.storeName = storeName;
    }

    public SimpleStoreInfoVO() {
    }
}

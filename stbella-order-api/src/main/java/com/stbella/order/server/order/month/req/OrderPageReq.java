package com.stbella.order.server.order.month.req;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class OrderPageReq extends BasicReq {

    private static final long serialVersionUID = -2660388268849014009L;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("订单类型")
    private List<String> orderTypeList;

    @ApiModelProperty("商品类目")
    private List<Integer> goodsCategoryIdList;

    @ApiModelProperty("战区")
    private List<Integer> warZoneList;

    @ApiModelProperty(value = "门店品牌 1-圣贝拉，2小贝拉,3-Bella Villa,100-艾屿")
    private List<Long> storeBrandList;

    @ApiModelProperty("门店")
    private List<Integer> storeIdList;

    @ApiModelProperty("客户Id")
    private String basicUid;

    @ApiModelProperty("客户名称")
    private String clientName;

    @ApiModelProperty("客户手机号")
    private String phone;

    @ApiModelProperty(value = "客户来源")
    private List<Integer> customerSourceList;

    @ApiModelProperty(value = "在馆状态")
    private List<Integer> statusList;

    @ApiModelProperty(value = "订单状态")
    private Integer orderStatus;

    @ApiModelProperty(value = "支付状态")
    private Integer payStatus;

    private List<Integer> payStatusList;

    @ApiModelProperty(value = "退款状态")
    private Integer refundStatus;

    @ApiModelProperty(value = "折扣审批状态")
    private Integer approvalDiscountStatus;

    @ApiModelProperty(value = "特殊订单标签")
    private List<Integer> orderTagList;

    @ApiModelProperty(value = "时间类型：0-创建时间；1-首次支付时间；2-业绩生效时间；3-入住时间")
    private Integer dateType = 0;

    @ApiModelProperty(value = "开始时间")
    private Integer dateStart;

    @ApiModelProperty(value = "结束时间")
    private Integer dateEnd;

    /** 根据类目获取订单集合 **/
    private List<Integer> orderIdByCategory;

    /** 根据门店品牌获取Id集合 **/
    private List<Integer> storeIdListByBrand;

    /** 根据客户名称获取用户Id集合 **/
    private List<Integer> clientIds;

    private List<Integer> orderStatusList;

    @ApiModelProperty(value = "订单标签，0-月子订单，1-随心拼订单")
    private Integer scene;

    @ApiModelProperty(value = "页数")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "条数")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "用户basic_id")
    private Integer basicId;

}

package com.stbella.order.server.contract.service.month;

import com.stbella.core.result.Result;
import com.stbella.order.server.contract.req.MonthContractQuery;
import com.stbella.order.server.contract.req.MonthContractSignQuery;
import com.stbella.order.server.contract.req.SignContractQuery;
import com.stbella.order.server.contract.res.*;

import java.util.List;

/**
 * 废弃 已迁移 stbella-contract 服务
 * @see com.stbella.contract.api.MonthContractAuthenticationService
 */
public interface MonthContractAuthenticationService {


    /**
     * 签署合同获取e签宝签署地址
     *
     * @param signContractQuery 请求参数相关信息
     * @return 签署地址
     */
    Result<ContractSignAddressVO> getContractSignAddress(SignContractQuery signContractQuery);

    /**
     * 查看和下载e签宝合同的地址
     *
     * @param contractSignedType 合同类型
     * @param contractId         附加合同id
     * @param templateContractType
     * @return 地址
     */
    Result<ContractAddressVO> getCheckContractUrl(Long contractId, Integer templateType);

    /**
     * 订单下合同列表
     *
     * @param orderId
     * @param templateContractType
     * @return
     */
    Result<ContractOrderVO> orderList(Long orderId, Integer templateContractType);

    /**
     * 根据条件返回合同列表(新老合同判断)
     *
     * @param orderId
     * @param templateContractType
     * @return
     */
    Result<ContractOrderVO> oldOrNewContractType(Long orderId, Integer templateContractType);

    /**
     * 判断是否签订了合同
     *
     * @param orderId
     * @return
     */
    Result<Boolean> signMasterContract(Long orderId);

    /**
     * 判断是否签订了合同
     *
     * @param clientUids
     * @return
     */
    Result<Boolean> signMasterContract(List<Integer> clientUids);

    /**
     * 销售下方未签订合同列表
     *
     * @param operatorGuid
     * @return
     */
    Result<ContractRemindVO> remindDetail(Integer operatorGuid);

    /**
     * 销售下方未签订合同数量
     *
     * @param operatorGuid
     * @return
     */
    Result<ContractRemindCountVO> remindDetailCount(Integer operatorGuid);

    Result<ContractSignDetailVO> querySignDetail(MonthContractQuery monthContractQuery);

    Result<ContractSignDetailVO> querySignDetailWithContractStatus(MonthContractQuery monthContractQuery);

    Result<ContractSignStatusVO> querySignDetailV2(MonthContractQuery monthContractQuery);

    /**
     * 已签订合同列表
     *
     * @param signContractQuery
     * @return {@link List}<{@link ContractSignVO}>
     */
    Result<List<ContractSignVO>> querySignList(MonthContractSignQuery signContractQuery);

    /**
     * 已签订合同列表&&订单附件表
     *
     * @param signContractQuery
     * @return {@link List}<{@link ContractSignVO}>
     */
    Result<List<ContractAttachmentVO>> querySignAndAttachmentList(MonthContractSignQuery signContractQuery);


}

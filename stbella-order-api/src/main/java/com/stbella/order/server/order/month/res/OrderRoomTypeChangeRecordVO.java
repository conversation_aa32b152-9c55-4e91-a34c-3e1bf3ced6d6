package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "OrderRoomTypeChangeRecordVO对象", description = "订单房型变更记录反参")
public class OrderRoomTypeChangeRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "最终房型变更周期")
    private String finalDurationOfStayChanged;

    @ApiModelProperty(value = "最终原房型")
    private String finalOriginalRoomType;

    @ApiModelProperty(value = "最终新房型")
    private String finalNewRoomType;

    @ApiModelProperty(value = "最终变更天数")
    private String finalNumberOfDaysChanged;

    @ApiModelProperty(value = "最终变更金额")
    private String finalAmountChanged;

    @ApiModelProperty(value = "最终变更签约金额")
    private String finalSignedAmountChanged;

    @ApiModelProperty(value = "房型变更周期")
    private String durationOfStayChanged;

    @ApiModelProperty(value = "原房型")
    private String originalRoomType;

    @ApiModelProperty(value = "新房型")
    private String newRoomType;

    @ApiModelProperty(value = "变更天数变化")
    private String numberOfDaysChanged;

    @ApiModelProperty(value = "变更金额变化")
    private String amountChanged;

    @ApiModelProperty(value = "变更签约金额变化")
    private String signedAmountChanged;

    @ApiModelProperty(value = "变更时间")
    private String changeTime;

    @ApiModelProperty(value = "变更人")
    private String changer;

    @ApiModelProperty(value = "orderId")
    private Integer orderId;

    @ApiModelProperty(value = "加收项id")
    private Long additionalRevenueId;

    private Integer index;
    private Integer packIndex;
    private Integer length;
}

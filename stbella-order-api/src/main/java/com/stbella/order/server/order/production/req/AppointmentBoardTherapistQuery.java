package com.stbella.order.server.order.production.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @description: 手工费
 * @author: Seven
 * @time: 2022/10/11 17:57
 */
@Data
public class AppointmentBoardTherapistQuery {

    @ApiModelProperty("产康师ID")
    @NotNull(message = "产康师ID不能为空")
    private Long therapistId;

    @NotNull
    @ApiModelProperty("核销开始时间")
    private Date writeOffStart;

    @NotNull
    @ApiModelProperty("核销结束时间")
    private Date writeOffEnd;

    @JsonIgnore
    @ApiModelProperty("预约单ids(前端无需传参)")
    private List<Long> appointmentIds;
}

package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class IncomeInfoVO implements Serializable {

    private static final long serialVersionUID = -1401124110267121466L;

    private Integer id;

    @ApiModelProperty(value = "收款流水号")
    private String incomeSn;

    @ApiModelProperty(value = "收款金额")
    private String income;

    @ApiModelProperty(value = "收款方式")
    private Integer payType;

    private String payTypeStr;

    @ApiModelProperty(value = "支付状态：0待支付，1已支付,2支付失败")
    private Integer status;

    private String statusStr;

    @ApiModelProperty(value = "收款到账时间")
    private String payTime;

    @ApiModelProperty(value = "支付凭证")
    private List<String> payProof;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty("提交人Id")
    private Integer staffId;

    @ApiModelProperty("提交人")
    private String staffName;

    @ApiModelProperty(value = "提交时间")
    private String createdAt;

}

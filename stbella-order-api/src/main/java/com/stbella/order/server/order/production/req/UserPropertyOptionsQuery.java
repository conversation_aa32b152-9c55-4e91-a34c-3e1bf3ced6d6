package com.stbella.order.server.order.production.req;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: Seven
 * @time: 2022/10/25 10:51
 */
@Data
public class UserPropertyOptionsQuery extends BasicReq {

    private static final long serialVersionUID = 1836423102057284023L;

    @ApiModelProperty("关联购买资产服务项")
    private Integer orderProductionCardId;

    @ApiModelProperty("关联购买资产服务项 卡项id")
    private Integer orderProductionCardExtendId;

    @ApiModelProperty("订单类型 1: 购买 2：额外礼赠")
    private Integer orderProductionType;

    @ApiModelProperty("产康服务skuId")
    private Integer productionSkuId;

    @ApiModelProperty("0=次卡 1=通卡 2=单项服务")
    private Integer itemType;

}

package com.stbella.order.server.order.month.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class MyBenefitsQuery implements Serializable {

    private static final long serialVersionUID = -3326399477264530311L;

    @NotNull(message = "客户BasicUid不能为空")
    @ApiModelProperty(value = "客户BasicUid")
    private Integer basicUid;

    private Integer pageNum = 1;

    private Integer pageSize = 10;

    @ApiModelProperty("状态，10-待使用，20-已使用，30-待生效，40-已失效")
    private Integer status;

    @ApiModelProperty("类型，0-好运签，1-孕期签 -1-全部")
    private Integer type;

    @ApiModelProperty("订单ID")
    private Integer orderId;
}

package com.stbella.order.server.order.production.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(value = "预约单管理", description = "预约单管理")
public class ProductionAppointmentMiniVo implements Serializable {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("关联购买资产订单")
    private Integer orderId;

    @ApiModelProperty("预约单号")
    private String orderSn;

    @ApiModelProperty("门店id")
    private Integer storeId;

    @ApiModelProperty("产康仪器id 0表示不需要仪器")
    private Long productionInstrId;

    @ApiModelProperty("门店名称")
    private String storeName;

    @ApiModelProperty(value = "微信头像", notes = "微信头像")
    private String avatarUrl;

    @ApiModelProperty("买家客户ID（ecp库的tab_client表主键）")
    private Integer clientId;

    @ApiModelProperty("客户basicId")
    private Integer basicId;

    @ApiModelProperty("客户名称")
    private String clientName;

    @ApiModelProperty("客户手机号")
    private String clientPhone;

    @ApiModelProperty(value = "客户手机号掩码", notes = "客户手机号掩码")
    private String hidePhone;

    @ApiModelProperty("产康师id")
    private Long therapistId;

    @ApiModelProperty("产康师名称")
    private String therapistName;

    @ApiModelProperty("关联购买资产服务项")
    private Integer orderProductionCardId;

    @ApiModelProperty("订单类型 1: 购买 2：额外礼赠")
    private Integer orderProductionType;

    @ApiModelProperty("关联购买资产服务项 卡项id")
    private Integer orderProductionCardExtendId;

    @ApiModelProperty("通卡次卡名称 单项服务为0")
    private Integer groupGoodsId;

    @ApiModelProperty("通卡次卡名称 单项服务为空")
    private String groupGoodsName;

    @ApiModelProperty("通卡分组id, 次卡/单项服务为0")
    private Integer groupId;

    @ApiModelProperty("通卡组合名称")
    private String groupName;

    @ApiModelProperty("单项服务goodsid")
    private Integer productionGoodsId;

    @ApiModelProperty("单项服务商品名称")
    private String productionGoodsName;

    @ApiModelProperty("单项服务skuid")
    private Integer productionSkuId;

    @ApiModelProperty("单项服务sku名称")
    private String productionSkuName;

    @ApiModelProperty(value = "产康名称", notes = "产康名称")
    private String productionName;

    @ApiModelProperty("项目类型：1=通卡;2次卡")
    private Integer itemType;

    @ApiModelProperty("项目类型名称")
    private String itemTypeName;

    @ApiModelProperty("预约方式：1=客户预约;2=代客预约")
    private Integer bookType;

    @ApiModelProperty("预约方式名称")
    private String bookTypeName;

    @ApiModelProperty("预约单类型：1=标准;2=补单")
    private Integer appointmentType;

    @ApiModelProperty("预约单类型")
    private String appointmentTypeName;

    @ApiModelProperty("服务费 分")
    private String serveFee;

    @ApiModelProperty("预约日期 yyyy-MM-dd")
    private String serveDate;

    @ApiModelProperty("服务开始时间")
    private String serveStart;

    @ApiModelProperty("服务结束时间")
    private String serveEnd;

    @ApiModelProperty("服务时长 分单位")
    private Integer serveTime;

    @ApiModelProperty("项目类型0=自营;1=三方")
    private Integer serveType;

    @ApiModelProperty("项目类型名称")
    private String serveTypeName;

    @ApiModelProperty("核销状态：1=待核销;2=已核销;3=已取消")
    private Integer verificationState;

    @ApiModelProperty("核销状态名称")
    private String verificationStateName;

    @ApiModelProperty("核销状态pi端名称")
    private String verificationPiStateName;

    @ApiModelProperty("订单编号")
    private String orderNo;

    @ApiModelProperty("是否已结算 0:未结算 1:已结算")
    private Integer settleFlg;

    @ApiModelProperty("实付金额")
    private String realPaidAmount;
}

package com.stbella.order.server.order.production.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductionJudgeVO implements Serializable {

    private static final long serialVersionUID = -4148872413394284734L;

    @ApiModelProperty(value = "资产创建时间")
    private Long createAt;

    @ApiModelProperty(value = "2023-08-01之后创建的订单 是:true")
    private Boolean nonExpired;

    @ApiModelProperty(value = "是否为不用统计的商品 否:true")
    private Boolean nonHitGoods;

    @ApiModelProperty(value = "订单id")
    private Integer orderId;

    @ApiModelProperty(value = "预约单号")
    private String appointmentSn;

    /**
     * 是否参与统计 true:参与统计
     */
    public Boolean isHit() {
        return Objects.nonNull(this.nonExpired) && Objects.nonNull(this.nonHitGoods) && this.nonExpired && this.nonHitGoods;
    }
}

package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "OrderBasicContractInfoVO对象", description = "订单后台-订单详情-基本信息-合同信息")
public class STMOOrderBasicContractInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "签约记录表")
    private Long id;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "合同状态 0 =未签署 1=签署中 2=已归档")
    private Integer contractStatus;

    @ApiModelProperty(value = "合同状态中文 0 =未签署 1=签署中 2=已归档")
    private String contractStatusStr;

    @ApiModelProperty(value = "合同签署类型 1=E签宝合同 2=旧合同 3=纸质合同 ")
    private Integer contractSignType;

    @ApiModelProperty(value = "合同签署类型中文 1=E签宝合同 2=旧合同 3=纸质合同 ")
    private String contractSignTypeStr;

    @ApiModelProperty(value = "模板类型 1=订单类 2=附件类")
    private Integer templateType;

    @ApiModelProperty(value = "模板类型中文 1=订单类 2=附件类")
    private String templateTypeStr;

    @ApiModelProperty(value = "E签宝合同查看长链接")
    private String contractLongUrl;

    @ApiModelProperty(value = "E签宝合同下载链接")
    private String downloadUrl;

    @ApiModelProperty(value = "E签宝合同查看链接")
    private String viewpdfUrl;

    @ApiModelProperty(value = "生效时间")
    private Date updatedAt;

    @ApiModelProperty(value = "创建人ID")
    private Integer createdId;

    @ApiModelProperty(value = "创建人姓名")
    private String createByName;

    @ApiModelProperty(value = "合同图片url数组")
    private String img;
}

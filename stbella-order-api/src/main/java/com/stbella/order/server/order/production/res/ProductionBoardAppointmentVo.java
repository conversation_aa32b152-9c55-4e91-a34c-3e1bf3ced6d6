package com.stbella.order.server.order.production.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <p>
 * 预约面板
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-10 10:28
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@ApiModel(value = "预约订单面板", description = "预约订单面板")
public class ProductionBoardAppointmentVo implements Serializable {

    private static final long serialVersionUID = -2365163678343090937L;

    @ApiModelProperty(value = "Body信息Map看板表格内容 key为Body的主键day", notes = "看板表格内容 列表")
    private LinkedHashMap<String, List<Body>> body;

    @ApiModelProperty(value = "看板表格表头 列表", notes = "看板表格表头 列表")
    private List<Header> header;

    @ApiModelProperty(value = "Setting信息Map看板表格设置不可预约时间段 key为年月日", notes = "看板表格设置 列表")
    private LinkedHashMap<String, List<Setting>> setting;

    @ApiModelProperty(value = "当前日期为年月日", notes = "当前日期为年月日")
    private String nowDay;

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    @ApiModel(description = "Body")
    public static class Body implements Serializable {

        private static final long serialVersionUID = -3636295596095419020L;

        @ApiModelProperty(value = "预约单主键", notes = "预约单主键")
        private Long id;

        @ApiModelProperty(value = "客户名称", notes = "客户名称")
        private String clientName;

        @ApiModelProperty(value = "客户手机号", notes = "客户手机号")
        private String clientPhone;

        @ApiModelProperty(value = "客户手机号掩码", notes = "客户手机号掩码")
        private String hidePhone;

        @ApiModelProperty(value = "客户微信头像", notes = "客户微信头像")
        private String avatarUrl;

        @ApiModelProperty(value = "该天", notes = "该天")
        private String day;

        @ApiModelProperty(value = "服务开始时间", notes = "服务开始时间")
        private String serveStart;

        @ApiModelProperty(value = "服务结束时间", notes = "服务结束时间")
        @JsonFormat(pattern = "HH:mm")
        private String serveEnd;

        @ApiModelProperty(value = "服务时长", notes = "单位分钟")
        private Integer serveTime;

        @ApiModelProperty(value = "门店id", notes = "门店id")
        private Integer storeId;

        @ApiModelProperty(value = "手工费 元", notes = "手工费 分")
        private String serveFee;

        @ApiModelProperty(value = "门店名称", notes = "门店名称")
        private String storeName;

        @ApiModelProperty(value = "产康名称", notes = "产康名称")
        private String productionName;

        @ApiModelProperty(value = "单项服务商品名称", notes = "单项服务商品名称")
        private String productionGoodsName;

        @ApiModelProperty(value = "规格值名称", notes = "规格值名称")
        private String productionSkuName;

        @ApiModelProperty("项目类型0=自营;1=三方")
        private Integer serveType;

        @ApiModelProperty("项目类型名称")
        private String serveTypeName;

        @ApiModelProperty(value = "表格内容 颜色状态值1:彩色+人名：表示有待核销预约;2:灰底+人名：表示有已核销预约单 ", notes = "1:彩色+人名：表示有待核销预约;2:灰底+人名：表示有已核销预约单")
        private Integer status;

        @ApiModelProperty("核销状态中文")
        private String verificationStateName;

        @ApiModelProperty("核销状态pi/picp 端名称")
        private String verificationPiStateName;

        @ApiModelProperty("核销状态：0=待核销;1=已核销;2已预约；3已取消")
        private Integer verificationState;
    }


    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    @ApiModel(description = "Setting")
    public static class Setting implements Serializable {
        private static final long serialVersionUID = 5525533912778302018L;
        @ApiModelProperty(value = "不可预约 服务开始时间", notes = "不可预约 服务开始时间")
        private String serveStart;

        @ApiModelProperty(value = "不可预约 服务结束时间", notes = "不可预约 服务结束时间")
        private String serveEnd;

        @ApiModelProperty(value = "服务时长", notes = "单位分钟")
        private Integer serveTime;
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    @ApiModel(description = "Header")
    public static class Header implements Serializable {
        private static final long serialVersionUID = -1263088038666095285L;

        @ApiModelProperty(value = "不可预约 服务结束时间 格式 yyyy-MM-dd", notes = "格式 yyyy-MM-dd")
        private String day;

        @ApiModelProperty(value = "星期几", notes = "当天是周几")
        private String week;

        @ApiModelProperty(value = "是否是今天", notes = "当前日期是否是今天")
        private Integer isToday;

        @ApiModelProperty(value = "预约状态值0.不用标识任何颜色;1.蓝点标识该天存在(c端可预约时段)/(pi端存在已预约或者已核销);2.灰点标识该天无可预约时段", notes = "0.不用标识任何颜色;1.蓝点标识该天存在(c端可预约时段)/(pi端存在已预约或者已核销);2.灰点标识该天无可预约时段")
        private Integer status;
    }

}

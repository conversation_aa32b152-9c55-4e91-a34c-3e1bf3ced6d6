package com.stbella.order.server.order.nutrition.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.stbella.order.server.order.nutrition.entity.OrderNutritionMonthFoodRemarkPO;

import java.util.List;

/**
 * <p>
 * 营养月子餐订单备注表 服务类
 * </p>
 *
 * <AUTHOR> @since 2022-03-10
 */
public interface OrderNutritionMonthFoodRemarkService extends IService<OrderNutritionMonthFoodRemarkPO> {

    /**
     * 通过订单号查询订单备注信息
     *
     * @param orderNo 订单号
     * @return 订单备注信息
     */
    List<OrderNutritionMonthFoodRemarkPO> getOrderRemarkInfoByOrderNo(String orderNo);

}

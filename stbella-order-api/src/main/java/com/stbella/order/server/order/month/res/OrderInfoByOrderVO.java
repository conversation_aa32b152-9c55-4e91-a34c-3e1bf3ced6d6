package com.stbella.order.server.order.month.res;

import com.stbella.platform.order.api.req.ExtraInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "OrderInfoByOrderVO对象", description = "订单-详情-全部信息")
public class OrderInfoByOrderVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "月子订单客户对象")
    private OrderMonthClientVO orderMonthClientVO;
    @ApiModelProperty(value = "月子订单客户委托人对象")
    private OrderMonthClientBailorVO orderMonthClientBailorVO;
    @ApiModelProperty(value = "订单加收项信息")
    List<OrderInfoByAdditionalRevenueVO> orderInfoByAdditionalRevenueVOList;
    @ApiModelProperty(value = "套餐信息")
    private OrderInfoByGoodsVO orderInfoByGoodsVO;
    @ApiModelProperty(value = "套餐额外礼赠信息")
    private OrderInfoByGiftExtendVO orderInfoByGiftExtendVO;
    @ApiModelProperty(value = "订单其他信息信息")
    private OrderInfoByOtherInfoVO orderInfoByOtherInfoVO;
    @ApiModelProperty(value = "订单折扣明细")
    private OrderDiscountsCacheVO orderDiscountsCacheVO;
    @ApiModelProperty(value = "订单状态0-未生效 1-待入住 2-入住中 3-已离店 4-已关闭 5-提前离馆")
    private Integer orderRoomStatus;

    @ApiModelProperty(value = "货币码")
    private String currency;

    private ExtraInfo extraInfo;

}

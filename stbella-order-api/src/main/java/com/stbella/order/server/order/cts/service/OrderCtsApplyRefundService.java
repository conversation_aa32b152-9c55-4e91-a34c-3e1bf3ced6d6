package com.stbella.order.server.order.cts.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.stbella.order.server.order.cts.entity.OrderCtsApplyRefundPO;
import com.stbella.order.server.order.cts.request.order.CustomerPayApplyRefundAdminSearchRequest;
import com.stbella.order.server.order.cts.response.order.AdminApplyRefundResponse;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 到家订单申请退款表 服务类
 * </p>
 *
 * <AUTHOR> @since 2022-03-24
 */
public interface OrderCtsApplyRefundService extends IService<OrderCtsApplyRefundPO> {

    /**
     * 后管获取申请退款列表
     *
     * @param query
     * @return
     */
    Page<AdminApplyRefundResponse> pageAdminApplyRefundList(CustomerPayApplyRefundAdminSearchRequest query);


    void exportExcel(CustomerPayApplyRefundAdminSearchRequest query, HttpServletResponse httpServletResponse);
}

package com.stbella.order.server.contract.res;

import com.stbella.order.common.constant.TableFieldConstant;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ContractSignDetailVO implements Serializable {

    private static final long serialVersionUID = 45717640179914002L;
    @ApiModelProperty(value = "签约记录表")
    private Long id;

    @ApiModelProperty(value = "客户ID（ecp库的tab_client表主键）")
    private Integer clientUid;

    @ApiModelProperty(value = "销售员id（ecp库user表主键id）")
    private Integer staffId;

    @ApiModelProperty(value = "门店ID(ecp库cfg_store表的主键id)")
    private Long storeId;

    @ApiModelProperty(value = "签约类型：1订单， .......（未来可增加其他签约类型）")
    private Integer guideType;

    @ApiModelProperty(value = "关联id（签约类型为订单，此处为订单id）")
    private Long guideId;

    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "合同状态 0 =未签署 1=签署中 2=已归档")
    private Integer contractStatus;

    @ApiModelProperty(value = "合同类型 1=E签宝合同 2=旧合同  ")
    private Integer contractType;

    @ApiModelProperty(value = "模板类型 1=订单类 2=附件类")
    private Integer templateType;

    @ApiModelProperty(value = TableFieldConstant.templateContractType)
    private Integer templateContractType;

    @ApiModelProperty(value = "模板id")
    private Long templateId;
}

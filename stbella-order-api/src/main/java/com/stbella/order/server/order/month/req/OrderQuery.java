package com.stbella.order.server.order.month.req;

import com.stbella.core.base.BasicReq;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotNull;

import com.stbella.order.common.enums.core.OrderRefundStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 订单查询对象
 *
 * <AUTHOR> @since 2021-11-29
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "OrderQuery", description = "订单其他信息缓存入参")
public class OrderQuery extends BasicReq implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = " 订单类型： 0 月子客户的“普通月子套餐”订单； 1 月子客户的“小月子”订单； 10 到家客户的“到家服务”订单； 20 到家阿姨的“到家服务订单”； 21 到家阿姨的阿姨培训订单； 30 月子其他订单 31 到家其他订单 40 商城普通订单 50护士外派订单 60产康订单 70S-BRA订单 描述： 0-9 归属月子平台的客户订单类型 ； 10-19 归属到家客户的订单类型； 20-29到家阿姨的订单类型; 30-39 其他订单 40-49 商城订单类型 50护士外派订单 60产康订单 70S-BRA订单")
    private Integer orderType;

    /**
     * @see com.stbella.order.common.enums.core.OmniOrderTypeEnum
     */
    @ApiModelProperty(value = " 订单类型")
    private List<Integer> orderTypeList;

    /**
     * @see com.stbella.order.common.enums.month.OrderStatusV2Enum
     */
    @ApiModelProperty(value = "订单状态")
    private List<Integer> orderStatusList;


    /**
     * @see com.stbella.order.common.enums.month.PayStatusV2Enum
     */
    @ApiModelProperty(value = "支付状态")
    private List<Integer> payStatusList;

    /**
     * @see OrderRefundStatusEnum
     */
    @ApiModelProperty(value = "退款状态")
    private List<Integer> refundStatusList;

    @ApiModelProperty(value = "实际金额(单位分,受退款影响)")
    private Integer realAmount;

    @ApiModelProperty(value = "已支付总金额(单位分,不受退款影响)")
    private Integer paidAmount;

    @ApiModelProperty(value = "开始 首次支付金额超过一半的时间")
    @NotNull(message = "首次支付金额超过一半的时间不能为空")
    private Long startPercentFirstTime;

    @ApiModelProperty(value = "结束 首次支付金额超过一半的时间")
    @NotNull(message = "首次支付金额超过一半的时间不能为空")
    private Long endPercentFirstTime;

    @ApiModelProperty(value = "最大创建时间")
    private Long maxCreatedAt;

    @ApiModelProperty(value = "最小创建时间")
    private Long minCreatedAt;

    @ApiModelProperty(value = "最大全退款时间")
    private Date maxFullRefundDate;

    @ApiModelProperty(value = "最小全退款时间")
    private Date minFullRefundDate;


    @ApiModelProperty(value = "最小版本号")
    private BigDecimal minVersion;

    @ApiModelProperty(value = "门店id")
    private Integer storeId;

    @ApiModelProperty(value = "用户唯一编号")
    private Integer basicUid;

    @ApiModelProperty(value = "订单ID")
    private List<Integer> orderIdList;

    @ApiModelProperty(value = "销售id")
    private Integer staffId;

    @ApiModelProperty(value = "订单号")
    private List<String> orderSnList;

    @ApiModelProperty(value = "是否报单过:1=未报单;2=已报单")
    private Integer isNotice;

}

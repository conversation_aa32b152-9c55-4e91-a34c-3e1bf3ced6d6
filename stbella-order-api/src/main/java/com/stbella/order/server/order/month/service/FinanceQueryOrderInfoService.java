package com.stbella.order.server.order.month.service;

import com.stbella.order.server.order.month.dto.PageDTO;
import com.stbella.order.server.order.month.req.OrderPaySuccessReq;
import com.stbella.order.server.order.month.res.OrderPaySuccessRes;

public interface FinanceQueryOrderInfoService {

    /**
     * 查询支付成功订单列表
     * @param req
     * @return
     */
    PageDTO<OrderPaySuccessRes> queryPaySuccessOrderList(OrderPaySuccessReq req);
}

package com.stbella.order.server.order.production.req;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 产康师所有预约单信息查询
 *
 * <AUTHOR>
 * @date 2022/10/25
 */
@Data
public class ProductionAppointmentInfoQuery extends BasicReq implements Serializable {

    private static final long serialVersionUID = -2087262530069864775L;

    @ApiModelProperty("产康师id")
    @NotNull(message = "产康师ID不能为空")
    private Long therapistId;

}

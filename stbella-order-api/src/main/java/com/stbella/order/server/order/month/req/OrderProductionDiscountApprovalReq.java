package com.stbella.order.server.order.month.req;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 产康订单折扣审批对象
 * </p>
 *
 * <AUTHOR> @since 2022-10-29
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "OrderProductionDiscountApprovalReq对象", description = "产康订单折扣审批请求对象")
public class OrderProductionDiscountApprovalReq extends BasicReq implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "任务ID")
    @NotEmpty(message = "任务ID不能为空")
    private Long taskId;

    @ApiModelProperty(value = "买家客户ID（ecp库的tab_client表主键）")
    @NotEmpty(message = "clientId不能为空")
    private Integer clientId;

    @ApiModelProperty(value = "门店ID")
    @NotEmpty(message = "门店ID不能为空")
    private Integer storeId;

    @ApiModelProperty(value = "购买商品")
    @NotEmpty(message = "购买商品不能为空")
    private String goodsName;

    @ApiModelProperty(value = "订单原价")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "订单应收")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "参与折扣金额")
    private BigDecimal discountAmount;

    @ApiModelProperty(value = "参与抵扣金额")
    private BigDecimal deductionAmount;

    @ApiModelProperty(value = "折扣优惠金额")
    private BigDecimal discountOfferAmount;

    @ApiModelProperty(value = "订单折扣率")
    private BigDecimal discountMargin;

    @ApiModelProperty(value = "净折扣率")
    private BigDecimal netMargin;

    @ApiModelProperty(value = "毛利率")
    private BigDecimal grossMargin;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "姓名")
    private String customerName;

    @ApiModelProperty(value = "手机号")
    private String customerPhone;


}

package com.stbella.order.server.order.month.service;

import com.stbella.base.server.test.req.SwapPhoneReq;
import com.stbella.core.base.PageVO;
import com.stbella.core.result.Result;
import com.stbella.order.server.order.month.req.*;
import com.stbella.order.server.order.month.request.standard.UpdateCustomerNamePhoneReq;
import com.stbella.order.server.order.month.res.BasicSimpInfoVO;
import com.stbella.order.server.order.month.res.OrderSimpInfoVO;
import com.stbella.order.server.order.month.res.SaleSimpInfoVO;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * 运营工具服务
 * @date 2023/10/19 10:37
 */
public interface OperationalToolService {

    /**
     * 根据姓名或手机号模糊查询客户信息
     *
     * @param request
     * @return
     */
    Result<PageVO<BasicSimpInfoVO>> getByCustomerNameOrPhone(BasicQueryRequest request);

    /**
     * 根据手机号精确查询客户信息
     *
     * @param request
     * @return
     */
    Result<BasicSimpInfoVO> getByCustomerPhone(BasicQueryRequest request);

    /**
     * 根据姓手机号查询销售简洁信息
     *
     * @param request
     * @return
     */
    Result<SaleSimpInfoVO> getSaleSimpInfoByPhone(SaleQueryRequest request);

    /**
     * 根据订单号查询销售简洁信息
     *
     * @param request
     * @return
     */
    Result<SaleSimpInfoVO> getSaleSimpInfoByOrderNo(SaleQueryByOrderRequest request);

    /**
     * 根据订单号查询订单简洁信息
     *
     * @return
     */
    Result<OrderSimpInfoVO> getOrderSimpInfo(OrderSimpInfoQuery query);

    /**
     * 修改订单已付金额
     *
     * @return
     */
    Result<String> orderChangeAmount(OrderChangeAmountRequest request);

    /**
     * 更改客户手机号
     *
     * @param req
     * @return
     */
    Result<String> swapPhone(SwapPhoneReq req);

    /**
     * 更改销售
     *
     * @param req
     * @return
     */
    Result<String> swapSale(OrderSwapSaleRequest req);

    /**
     * 发送订单变更MQ
     *
     * @return
     */
    Result sendEventMq(Integer orderId);

    /**
     * 根据订单号查询订单简洁信息
     *
     * @return
     */
    Void updateCustomerNamePhone(UpdateCustomerNamePhoneReq req);

    /**
     * 更新认证方式
     *
     * @return
     */
    Void updateClientAuthType(UpdateCustomerNamePhoneReq req);
}

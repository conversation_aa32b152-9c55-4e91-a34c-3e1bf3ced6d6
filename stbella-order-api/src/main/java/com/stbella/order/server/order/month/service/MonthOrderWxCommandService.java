package com.stbella.order.server.order.month.service;

import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.order.server.order.month.req.*;
import com.stbella.order.server.order.month.request.pay.IncomeRecordTransferRequest;
import com.stbella.order.server.order.month.res.*;

import java.util.List;

/**
 * 月子餐订单服务
 */
public interface MonthOrderWxCommandService {

    /**
     * 套餐信息缓存
     *
     * @param req
     * @return
     */
    Result orderGoodsCache(OrderGoodsCacheReq req);


    /**
     * 套餐信息额外礼赠缓存
     *
     * @param req
     * @return
     */
    Result orderMonthGiftExtendCache(OrderMonthGiftExtendCacheReq req);


    /**
     * 创建订单
     *
     * @param req
     */
    Result<AddOrderVO> addOrderMonth(OrderMonthReq req);

    /**
     * 修改订单
     *
     * @param req
     */
    Result<EditOrderVO> editOrderMonth(OrderMonthReq req);

    /**
     * 套餐加收项缓存
     *
     * @param req
     * @return
     */
    Result orderMonthAdditionalRevenueCache(List<OrderMonthAdditionalRevenueCacheReq> req);

    /**
     * 订单额外礼赠缓存修改
     *
     * @param req
     * @return
     */
    Result updateOrderMonthGiftExtendCache(OrderMonthGiftExtendCacheReq req);

    /**
     * 创建订单后删除缓存
     *
     * @param req
     * @return
     */
    Boolean removeCache(OrderCacheBaseReq req);

    /**
     * 订单其他缓存操作
     *
     * @param req
     * @return
     */
    Result orderOtherInfoCache(OrderOtherInfoCacheReq req);

    /**
     * 下单后根据订单的数据生成一个缓存
     *
     * @return
     */
    Result getOrderCacheByOrderId(OrderCacheByOrderIdReq orderCacheByOrderIdReq);


    /**
     * 清空整个用户的额外礼赠
     *
     * @param req
     * @return
     */
    Result delAllOrderMonthGiftExtendCache(OrderGiftByStoreReq req);

    String convertOrderGift2String(List<OrderInfoByGiftExtendSkuVO> orderInfoByGiftExtendSkuVOList);

    /**
     * 下单后获取清空全部缓存
     *
     * @param removeAllOrderCacheReq
     * @return
     */
    Result removeAllOrderCache(RemoveAllOrderCacheReq removeAllOrderCacheReq);

    /**
     * 订单编辑页面判断是否有缓存
     *
     * @param removeAllOrderCacheReq
     * @return
     */
    Result<RemoveAllOrderCacheVO> checkCacheByOrderId(RemoveAllOrderCacheReq removeAllOrderCacheReq);

    Result getOrderCacheByOrderIdForCustomer(OrderCacheByOrderIdReq orderCacheByOrderIdReq);

    void createGiftExtendSkuCache(Integer orderId);

    /**
     * 下单后缓存-根据订单生成订单缓存（同时判断是否有缓存）
     *
     * @param orderCacheByOrderIdReq
     * @return
     */
    Result getOrderCacheByOrderIdAndCheck(OrderCacheByOrderIdReq orderCacheByOrderIdReq);

    Result getOrderCacheByOrderIdAndCheckForCustomer(OrderCacheByOrderIdReq orderCacheByOrderIdReq);

    Result removeAllBeforeOrderCache();

    /**
     * 关闭订单
     *
     * @param orderId
     * @return
     */
    Result closeOrder(Integer orderId);

    List<RefundReasonVO> getRefundReasonByType(Integer type);

    List<RefundReasonVO> getOmniRefundReasonByType(Integer type);

    /**
     * 同步订单退款状态
     */
    Result<Integer> syncOrderRefundStatus(Integer orderId) throws BusinessException;

    Result<Integer> syncOrderRefundStatusForNew(Integer orderId) throws BusinessException;

    /**
     * 老订单数据清洗新订单-订单ID
     *
     * @param orderId
     * @return
     */
    String oldOrderSyncByOrderId(Integer orderId);

    /**
     * 老订单数据清洗新订单-门店ID
     *
     * @param storeId
     * @return
     */
    String oldOrderSyncByStoreId(Integer storeId);

    /**
     * 老订单数据清洗新订单-定时任务
     *
     * @return
     */
    String oldOrderSyncByScheduler();

    /**
     * 订单支付记录转移
     */
    Result<Integer> incomeRecordTransfer(IncomeRecordTransferRequest request);

    /**
     * 获取订单退款状态
     * @param orderId
     * @return
     */
    Integer getRefundStatusByOrderId(Integer orderId);

    Result<Integer> allocationPayment(String incomeRecordId);

    Result<Integer> sendAllocationMq(Integer incomeRecordId);

    Result<?> fixAllocationPayment(List<Integer> orderId, Boolean fixAll);

    Result<?> fixGoodsAllocation();

    Result<?> fixAllocationPayment2(List<String> orderSnList, Boolean fixAll);
}

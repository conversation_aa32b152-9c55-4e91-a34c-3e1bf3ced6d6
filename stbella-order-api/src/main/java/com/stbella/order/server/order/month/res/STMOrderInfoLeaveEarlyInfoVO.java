package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel(value = "STMOrderInfoLeaveEarlyInfoVO对象", description = "订单后台-订单详情-订单信息-提前离馆信息")
public class STMOrderInfoLeaveEarlyInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "入住时间")
    private String checkInDate;

    @ApiModelProperty(value = "原离馆时间")
    private String originalDepartureDate;

    @ApiModelProperty(value = "提前离馆时间")
    private String earlyDepartureDate;

    @ApiModelProperty(value = "提前离馆天数")
    private Integer earlyDepartureDays;

    @ApiModelProperty(value = "退款原价（小数）")
    private BigDecimal refundOriginalAmountBigDecimal;

    @ApiModelProperty(value = "退款原价")
    private String refundOriginalAmount;

    @ApiModelProperty(value = "退款金额（小数）")
    private BigDecimal refundAmountBigDecimal;

    @ApiModelProperty(value = "退款金额")
    private String refundAmount;

    @ApiModelProperty(value = "退款状态")
    private String refundStatus;

    @ApiModelProperty(value = "申请人")
    private String optName;

    @ApiModelProperty(value = "申请时间")
    private String optTime;

}

package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Title: ContractSignRecordDTO
 * @author: yang
 */
@Data
@ApiModel(value = "ContractSignRecordVO对象", description = "客户详情-已签署合同列表")
public class ContractSignRecordVO {

    @ApiModelProperty(value = "模板名称")
    private String contractName;

    @ApiModelProperty(value = "客户id")
    private Integer clientUid;

    @ApiModelProperty(value = "订单类型")
    private Integer guideType;

    @ApiModelProperty(value = "下载地址")
    private String downloadUrl;

    @ApiModelProperty(value = "预览地址")
    private String viewPdfUrl;

    @ApiModelProperty(value = "合同类型")
    private Integer templateType;

    @ApiModelProperty(value = "合同模板id")
    private String templateId;

    @ApiModelProperty(value = "签署时间")
    private String signDate;

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty(value = "签约模板url")
    private String contractTempUrl;

    @ApiModelProperty(value = "订单类型")
    private String orderType;

    @ApiModelProperty(value = "合同类型")
    private Integer contractType;

    @ApiModelProperty(value = "模板关联合同类型合同类型")
    private Integer templateContractType;

    @ApiModelProperty(value = "E签宝流程id")
    private String esignFlowId;

    @ApiModelProperty(value = "E签宝文件id")
    private String esignFileId;

    @ApiModelProperty(value = "E签宝文件名称")
    private String esignFileName;

    @ApiModelProperty(value = "订单id")
    private Integer orderId;

    @ApiModelProperty(value = "是否为纸质")
    private Integer paper;
}

package com.stbella.order.server.order.cts.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.stbella.order.server.order.cts.dto.OrderAttachmentDTO;
import com.stbella.order.server.order.cts.entity.OrderCtsAttachmentPO;

import java.util.List;

/**
 * <p>
 * 到家订单附件表 服务类
 * </p>
 *
 * <AUTHOR> @since 2022-09-05
 */
public interface OrderCtsAttachmentService extends IService<OrderCtsAttachmentPO> {

    /**
     * 根据订单编号获取订单附件列表
     *
     * @param orderNo
     * @param type
     * @return
     */
    List<OrderAttachmentDTO> getOrderAttachmentListByType(String orderNo, Integer type);

}

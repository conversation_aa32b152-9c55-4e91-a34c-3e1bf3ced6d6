package com.stbella.order.server.order.month.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stbella.core.base.PageVO;
import com.stbella.core.result.Result;
import com.stbella.notice.entity.OaProcessIdRelationPO;
import com.stbella.order.server.order.month.dto.PageDTO;
import com.stbella.order.server.order.month.enums.OldOrderApproveStatusEnum;
import com.stbella.order.server.order.month.req.OrderPageReq;
import com.stbella.order.server.order.month.req.OrderPerformanceReq;
import com.stbella.order.server.order.month.req.PayFirstRecordReq;
import com.stbella.order.server.order.month.req.QueryOrderGoodsInfoByPhoneOrNameReq;
import com.stbella.order.server.order.month.req.QueryOrderPageReq;
import com.stbella.order.server.order.month.res.*;
import com.stbella.order.server.order.order.res.OrderExport;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 月子订单后台单服务
 */
public interface MonthOrderAdminQueryService {
    Page<QueryOrderPageVO> queryPage(QueryOrderPageReq queryOrderPageReq);

    /***
     * 根据订单号查询-订单详情-基本信息
     */
    Result<STMOOrderBasicInfoVO> queryOrderBasicInfoByOrderNo(Integer orderId);

    /**
     * 通过订单号查询订单套餐信息
     */
    Result<STMOrderInfoMonthInfoVO> queryOrderMonth(String orderNo);

    /***
     * 根据订单号查询-订单详情-订单信息
     */
    Result<STMOrderInfoVO> queryByOrderIdOrderInfo(Integer orderId);

    /***
     * 根据订单号查询-订单详情-财务信息
     */
    Result<STMOrderInfoFinancialVO> queryByOrderIdInancialInformation(Integer orderId);

    /**
     * 根据订单号查询-订单详情-额外礼赠
     *
     * @param orderId
     * @return
     */
    Result<STMOrderInfoGiftVO> queryByOrderIdGift(Integer orderId);

    Result<STMOrderInfoLaborVO> queryByOrderIdGoodNewsChildbirth(String orderNo);

    /**
     * 根据订单号查询-订单详情-分娩喜报
     *
     * @param orderId
     * @return
     */
    Result<STMOrderInfoLaborVO> queryByOrderIdGoodNewsChildbirth(Integer orderId);

    /**
     * 订单导出
     *
     * @param queryOrderPageReq
     * @param response
     */
    void stmOrderExport(QueryOrderPageReq queryOrderPageReq, HttpServletResponse response, Boolean includeMobile);


    /**
     * 到处前校验
     *
     * @param queryOrderPageReq
     * @return
     */
    Result queryPageVerify(QueryOrderPageReq queryOrderPageReq);

    /**
     * 根据订单ID获取套餐信息
     */
    Result<STMOrderInfoMonthInfoVO> queryOrderGoodsByOrderId(Integer orderId);

    /**
     * 根据订单IDList获取套餐信息
     */
    Result<List<STMOrderInfoMonthInfoVO>> queryOrderGoodsByOrderIdList(List<Integer> orderIdList);

    /**
     * 转换折扣枚举
     */
    OldOrderApproveStatusEnum convertOldOrderApproveStatusEnum(OaProcessIdRelationPO oaProcessIdRelationPO);

    /**
     * 根据OrderSN查询
     *
     * @param orderSnList
     * @return
     */
    List<OrderInfoVO> queryByOrderSn(List<String> orderSnList);

    List<OrderInfoVO> queryByOrderId(List<Integer> orderIds);

    /**
     * 根据支付时间查询-客户信息
     */
    Result<List<BasicSimpInfoVO>> queryOrderForPayFirstRecord(PayFirstRecordReq req);

    /**
     * PICP订单列表查询-版本
     *
     * @param req
     * @return
     */
    PageDTO<HeOrderVO> page(OrderPageReq req);

    /**
     * 订单导出(查询数据集)
     *
     * @param req
     * @return
     */
    List<OrderExport> orderExport(OrderPageReq req);

    /**
     * 查询订单是否有效(入馆服务调研)
     * @param orderNo
     * @return
     */
    Result<Integer> queryOrderEffective(String orderNo,Integer basicId);

    /**
     * 订单导出(执行异步导出)
     * @param req
     * @return
     */
    Result<String> execOrderExport(OrderPageReq req);

    String queryOrderRemark(String orderNo);

    Integer queryOrderBasicUid(String orderNo);

    Map<String, Integer> queryOrderBasicUid(List<String> orderNos);

    /**
     * 获取订单客户名称
     */
    String queryOrderClientName(String orderNo);

    PageVO<OrderGoodsInfoAndUserInfoVO> queryOrderGoodsInfoByPhoneOrName(QueryOrderGoodsInfoByPhoneOrNameReq queryOrderGoodsInfoByPhoneOrNameReq);

    /**
     * 修改门店业绩/操作管理搜索订单
     *
     * @param req
     * @return
     */
    Result<List<OrderPerformanceVO>> performanceSearchOneOrder(OrderPerformanceReq req, Integer type);

    /**
     * 修改门店业绩/修改记录搜索订单
     *
     * @param req
     * @return
     */
    Result<PageDTO<OrderPerformanceVO>> performanceSearchUpdateOrder(OrderPerformanceReq req);

    Result<List<OrderPerformanceVO>> searchOneOrder(OrderPerformanceReq req);

    /**
     * 予家订单-订单类型
     * @return
     */
    List<SelectRespVO> homeOrderType();

    /**
     * 予家订单导出
     * @param req
     * @param response
     */
    List<CareForHomeOrderExportVO> careForHomeExport(OrderPageReq req, HttpServletResponse response);
}

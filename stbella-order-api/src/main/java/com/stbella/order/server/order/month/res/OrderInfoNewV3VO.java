package com.stbella.order.server.order.month.res;

import com.stbella.platform.order.api.req.ExtraInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class OrderInfoNewV3VO implements Serializable {
    private static final long serialVersionUID = 4014879813027314477L;

    @ApiModelProperty("订单ID（主键自增)")
    private Long orderId;

    @ApiModelProperty("scrm客户id")
    private Long scrmCustomerId;

    @ApiModelProperty("customer手机号")
    private String customerPhone;

    @ApiModelProperty("客户id")
    private Integer clientUid;

    @ApiModelProperty("订单的销售id")
    private Integer staffId;

    @ApiModelProperty("storeId门店")
    private Integer storeId;

    @ApiModelProperty("staffPhone 销售手机号")
    private String staffPhone;

    @ApiModelProperty("销售scrm id所有人")
    private Long scrmOwnerId;

    @ApiModelProperty("销售所属部门")
    private Long scrmDimDepart;

    @ApiModelProperty("套餐名称")
    private String goodsName;

    @ApiModelProperty("产康商品/卡项名称")
    private String productionName;

    @ApiModelProperty("订单编号")
    private String orderSn;

    @ApiModelProperty(" 订单类型： 0 月子客户的“普通月子套餐”订单； 1 月子客户的“小月子”订单； 10 到家客户的“到家服务”订单； 20 到家阿姨的“到家服务订单”； 21 到家阿姨的阿姨培训订单； 30 月子其他订单 31 到家其他订单 40 商城普通订单 50护士外派订单 60产康订单 70S-BRA订单 描述： 0-9 归属月子平台的客户订单类型 ； 10-19 归属到家客户的订单类型； 20-29到家阿姨的订单类型; 30-39 其他订单 40-49 商城订单类型 50护士外派订单 60产康订单 70S-BRA订单")
    private Integer orderType;

    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;

    @ApiModelProperty("套餐金额")
    private BigDecimal goodsAmount;

    @ApiModelProperty("签单金额")
    private BigDecimal payAmount;

    @ApiModelProperty("已付金额")
    private BigDecimal paidAmount;

    @ApiModelProperty("退款金额")
    private BigDecimal refundAmount;

    @ApiModelProperty("退款中金额")
    private BigDecimal refundingAmount;

    @ApiModelProperty("套餐天数")
    private Integer goodsDays;

    @ApiModelProperty("支付状态：0待支付，1未全付，2已付清，3已取消 4-超额支付")
    private Integer payStatus;

    @ApiModelProperty("订单签约折扣 对应套餐折扣 订单签约折扣=签单金额/订单原价")
    private BigDecimal discountMargin;

    @ApiModelProperty("订单净折扣 对应净折扣率 系统的净折扣率=（订单应收-额外礼赠成本）／订单原价 ")
    private BigDecimal netMargin;

    @ApiModelProperty("毛利率")
    private BigDecimal grossMargin;

    @ApiModelProperty("剩余应收金额")
    private BigDecimal remainingAmount;

    @ApiModelProperty("审阅中金额")
    private BigDecimal reviewAmount;

    @ApiModelProperty("实际已付金额")
    private BigDecimal realAmount;

    @ApiModelProperty("额外礼赠")
    private String extraGift;

    @ApiModelProperty("特殊事项备注")
    private String remark;

    @ApiModelProperty("首次支付时间")
    private Date firstPayTime;

    @ApiModelProperty("首次支付金额超过一半的时间")
    private Date percentFirstTime;

    @ApiModelProperty("订单详情url")
    private String orderH5;

    @ApiModelProperty("是否已关联销售机会 1=否;2=是")
    private Integer saleChange;

    @ApiModelProperty("创建人id")
    private Long createBy;

    @ApiModelProperty("创建人名称")
    private String createByName;

    @ApiModelProperty("修改人id")
    private Long updateBy;

    @ApiModelProperty("修改人名称")
    private String updateByName;

    @ApiModelProperty("是否自动赢单状态：0不处理自动赢单，1处理自动赢单")
    private Integer isAutoSign;

    @ApiModelProperty("0-圣贝拉；1-小贝拉")
    private Integer storeType;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("签到id")
    private Long signId;

    @ApiModelProperty("产康订单 抵扣产康金")
    private BigDecimal productionAmountPay;

    @ApiModelProperty(value = "签单折扣率")
    private BigDecimal signOrderDiscountMargin;

    @ApiModelProperty("业绩是否删除 0否,1是")
    private Integer performanceStatus;

    @ApiModelProperty("订单状态, 0-待付款 1-待发货 2-待收货 3-已完成 4-已关闭")
    private Integer orderStatus;

    @ApiModelProperty("退款状态, 0-无退款 1-部分退款中 2-部分退款 3-全部退款中 4-全部退款")
    private Integer refundStatus;

    @ApiModelProperty("是否包含月子护理服务, 1-是 2-否")
    private Integer monthOrder;

    @ApiModelProperty("订单版本号默认 1.00")
    private BigDecimal version;

    @ApiModelProperty(value = "商品详列表")
    private List<OrderGoodsInfoVO> orderGoodsInfoVOS;

    @ApiModelProperty(value = "订单减免记录")
    private List<ScrmOrderReductionRecordVO> scrmOrderReductionRecordVOS;

    @ApiModelProperty(value = "订单扩展信息")
    private ExtraInfo extraInfo;
}

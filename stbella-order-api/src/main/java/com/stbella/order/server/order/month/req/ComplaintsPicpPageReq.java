package com.stbella.order.server.order.month.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class ComplaintsPicpPageReq implements Serializable {

    private static final long serialVersionUID = 3252194827893703496L;

    @ApiModelProperty("客诉id")
    private Long id;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("订单类型")
    private List<String> orderTypeList;

    @ApiModelProperty("战区")
    private List<Integer> warZoneList;

    @ApiModelProperty(value = "门店品牌")
    private List<Long> storeBrandList;

    @ApiModelProperty("门店")
    private List<Integer> storeIdList;

    private List<Integer> clientUidList;

    @ApiModelProperty("客户Id")
    private Integer basicUid;

    @ApiModelProperty("客户名称")
    private String clientName;

    @ApiModelProperty("客户手机号")
    private String phone;

    @ApiModelProperty(value = "客户来源")
    private List<Integer> customerSourceList;

    @ApiModelProperty(value = "客诉等级")
    private List<Integer> complaintLevelList;

    @ApiModelProperty(value = "定责类型")
    private List<Integer> responsibilityTypeList;

    @ApiModelProperty(value = "客诉状态")
    private List<Integer> complaintsStatusList;

    @ApiModelProperty(value = "创建人")
    private String creator;

    private List<Integer> creatorList;

    @ApiModelProperty(value = "时间类型")
    private Integer dateType = 0;

    @ApiModelProperty(value = "开始时间")
    private Date bizTimeStart;

    @ApiModelProperty(value = "结束时间")
    private Date bizTimeEnd;

    @ApiModelProperty(value = "开始时间")
    private Long bizDateStart;

    @ApiModelProperty(value = "结束时间")
    private Long bizDateEnd;

    @ApiModelProperty(value = "页数")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "条数")
    private Integer pageSize = 10;
}

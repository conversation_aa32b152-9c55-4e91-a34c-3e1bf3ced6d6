package com.stbella.order.server.order.month.req;

import com.stbella.core.base.BasicReq;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 押金支付记录
 * <AUTHOR>
 * @date 2022/12/27
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "押金支付记录明细", description = "押金支付记录明细")
public class ClientDepositDetailQuery extends BasicReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户id")
    @NotNull(message = "客户id不能为空")
    private Integer clientId;

}

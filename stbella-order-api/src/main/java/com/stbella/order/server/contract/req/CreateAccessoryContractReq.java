package com.stbella.order.server.contract.req;

import com.stbella.core.annotations.EnumValue;
import com.stbella.core.base.BasicReq;
import com.stbella.order.common.enums.month.TemplateContractTypeV2Enum;
import com.stbella.order.common.enums.month.TemplateTypeEnum;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel(value = "创建附属合同")
@Data
public class CreateAccessoryContractReq extends BasicReq implements Serializable {

    private static final long serialVersionUID = -7765075442929230881L;
    @ApiModelProperty(value = "订单id，这里传订单id主要是为了签附属协议时获取用户信息", required = true)
    @NotNull(message = "订单id不能为空")
    private Integer orderId;

    @ApiModelProperty(value = "合同类型   1=>主合同，2=>附件类，3=>附属类：补充协议", required = true)
    @NotNull(message = "合同类型不能为空")
    @EnumValue(enumClass = TemplateTypeEnum.class, enumMethod = "isValidCode",message="合同类型不在指定区间1,2,3内")
    private Integer templateType;

    @ApiModelProperty(value = "模板关联合同类型21.预约协议 22.授权委托书 23.订单折扣保密协议 24.合同解除协议", required = true)
    @NotNull(message = "模板关联合同类型不能为空")
    @EnumValue(enumClass = TemplateContractTypeV2Enum.class, enumMethod = "isValidCode",message="模板关联合同类型不在指定区间21,22,23,24内")
    private Integer templateContractType;

    @ApiModelProperty(value = "预约协议 需要支付意向金金额 单位元")
    private BigDecimal amountEarnest;
}

package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 合同模版表,新版E签宝
 * </p>
 *
 * <AUTHOR> @since 2021-11-04
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "OrderInfoByGoodsVO对象", description = "订单-详情-订单套餐信息")
public class OrderInfoByGoodsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "数据主键")
    private Integer id;

    @ApiModelProperty(value = "订单Id")
    private Integer orderId;

    @ApiModelProperty(value = "套餐ID")
    private Integer goodsId;

    @ApiModelProperty(value = "套餐名称")
    private String goodsName;

    @ApiModelProperty(value = "房型Id")
    private Integer roomId;

    @ApiModelProperty(value = "套餐天数")
    private Integer serviceDays;

    @ApiModelProperty(value = "套餐父类ID")
    private Integer parentId;

    @ApiModelProperty(value = "规格ID")
    private Integer skuId;

    @ApiModelProperty(value = "原价")
    private BigDecimal goodsPriceOrgin;

    @ApiModelProperty(value = "套餐应收金额")
    private BigDecimal goodsReceivableAmount;

    @ApiModelProperty(value = "附件名称")
    private String attachmentName;

    @ApiModelProperty(value = "附件url")
    private String attachmentUrl;

    @ApiModelProperty(value = "订单类型： 订单类型：\n" +
            "0 月子客户的“普通月子套餐”订单；\n" +
            "1 月子客户的“小月子”订单；\n" +
            "10 到家客户的“到家服务”订单；\n" +
            "20 到家阿姨的“到家服务订单”；\n" +
            "21 到家阿姨的阿姨培训订单；\n" +
            "30 月子其他订单")
    private Integer orderType;

}

package com.stbella.order.server.order.production.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description:
 * @author: Seven
 * @time: 2022/10/10 11:34
 */
@Data
@ApiModel(value = "预约单列表", description = "预约单")
public class ProductionAppointmentBackVo implements Serializable {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("关联购买资产订单")
    private Integer orderId;

    @ApiModelProperty("预约单号")
    private String orderSn;

    @ApiModelProperty("门店id")
    private Integer storeId;

    @ApiModelProperty("门店名称")
    private String storeName;

    @ApiModelProperty("买家客户ID（ecp库的tab_client表主键）")
    private Integer clientId;

    @ApiModelProperty("客户名称")
    private String clientName;

    @ApiModelProperty("客户手机号")
    private String clientPhone;

    @ApiModelProperty("产康师id")
    private Integer therapistId;

    @ApiModelProperty("产康师名称")
    private String therapistName;

    @ApiModelProperty("通卡次卡名称 单项服务为0")
    private Integer groupGoodsId;

    @ApiModelProperty(value = "产康名称", notes = "产康名称")
    private String productionName;

    @ApiModelProperty("通卡次卡名称 单项服务为空")
    private String groupGoodsName;


    @ApiModelProperty("通卡分组id, 次卡/单项服务为0")
    private Integer groupId;


    @ApiModelProperty("通卡组合名称")
    private String groupName;


    @ApiModelProperty("单项服务goodsid")
    private Integer productionGoodsId;


    @ApiModelProperty("单项服务商品名称")
    private String productionGoodsName;

    @ApiModelProperty("单项服务skuid")
    private Integer productionSkuId;

    @ApiModelProperty("单项服务sku名称")
    private String productionSkuName;
    /**
     * 项目类型：1=通卡;2次卡
     */
    @ApiModelProperty("项目类型：1=通卡;2次卡")
    private Integer itemType;

    @ApiModelProperty("项目类型名称")
    private String itemTypeName;

    /**
     * 预约方式：1=客户预约;2=代客预约
     */
    @ApiModelProperty("预约方式：1=客户预约;2=代客预约")
    private Integer bookType;

    @ApiModelProperty("预约方式名称")
    private String bookTypeName;

    /**
     * 预约单类型：1=标准;2=补单
     */
    @ApiModelProperty("预约单类型：1=标准;2=补单")
    private Integer appointmentType;

    @ApiModelProperty("预约单类型")
    private String appointmentTypeName;

    /**
     * 服务费 分
     */
    @ApiModelProperty("手工费 分")
    private String serveFee;
    /**
     * 服务开始时间
     */
    @ApiModelProperty("服务开始时间")
    private String serveStart;
    /**
     * 服务结束时间
     */
    @ApiModelProperty("服务结束时间")
    private String serveEnd;

    /**
     * 服务结束时间
     */
    @ApiModelProperty("服务结束时间")
    private String serveDate;

    /**
     * 服务时长 分单位
     */
    @ApiModelProperty("服务时长 分单位")
    private Integer serveTime;
    /**
     * 项目类型0=自营;1=三方
     */
    @ApiModelProperty("项目类型0=自营;1=三方")
    private Integer serveType;

    @ApiModelProperty("项目类型名称")
    private String serveTypeName;

    /**
     * 核销状态：1=已核销;2已预约；3已取消
     */
    @ApiModelProperty("核销状态：1=已核销;2已预约(=待核销)；3已取消")
    private Integer verificationState;

    @ApiModelProperty("核销状态名称")
    private String verificationStateName;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private Integer creatorId;
    /**
     * 创建人名称
     */
    @ApiModelProperty("创建人名称")
    private String creatorName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date gmtModified;

    @ApiModelProperty("核销时间")
    private Date writeOffDate;
}

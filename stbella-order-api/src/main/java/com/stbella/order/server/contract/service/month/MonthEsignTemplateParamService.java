package com.stbella.order.server.contract.service.month;

import com.stbella.order.server.order.month.res.MonthEsignTemplateParamVO;

import java.util.List;

/**
 * <p>
 * 合同参数模版表,新版E签宝 服务类
 * </p>
 *
 * <AUTHOR> @since 2021-11-03
 */
/**
 * 废弃 已迁移 stbella-contract 服务
 * @see com.stbella.contract.api.MonthEsignTemplateParamService
 */
public interface MonthEsignTemplateParamService  {


    /**
     * 查询模版下的参数列表
     * 先查询he_esign_template_param表的数据,如有则直接返回. 如没有则查询he_esign_template_param_config表数据 返回默认配置
     *
     * @param orderType 订单类型
     * @return 模版参数列表
     */
    List<MonthEsignTemplateParamVO> list(Integer orderType);

    List<MonthEsignTemplateParamVO> list(Integer orderType,Long esignTemplateId);

}

package com.stbella.order.server.order.month.res;

import com.stbella.platform.order.api.res.CartRes;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class HeOrderGiftInfoGoodsPiVO implements Serializable {

    private static final long serialVersionUID = 2527279002548619429L;

    private Long id;

    @ApiModelProperty(value = "赠送记录编号")
    private String giftSn;

    @ApiModelProperty("门店id")
    private Integer storeId;

    @ApiModelProperty("门店名称")
    private String storeName;

    @ApiModelProperty(value = "客户Id")
    private Integer basicUid;

    @ApiModelProperty(value = "clientUid")
    private Integer clientUid;

    @ApiModelProperty("客户姓名")
    private String customerName;

    @ApiModelProperty("客户手机号")
    private String customerMobile;

    @ApiModelProperty(value = "客户脱敏手机号")
    private String clientPhone;

    @ApiModelProperty(value = "赠送原因类型")
    private Integer giftReasonType;

    @ApiModelProperty(value = "赠送原因类型描述")
    private String giftReasonTypeStr;

    @ApiModelProperty(value = "发布链接")
    private String linkUrl;

    @ApiModelProperty(value = "关联订单Id")
    private Integer orderId;

    @ApiModelProperty(value = "关联订单号")
    private String orderSn;

    @ApiModelProperty(value = "业绩生效时间")
    private Date performanceEffectiveDate;

    @ApiModelProperty(value = "订单其他备注")
    private String remark;

    @ApiModelProperty(value = "赠送具体原因")
    private String giftSpecificReason;

    @ApiModelProperty(value = "证明截图")
    private List<String> evidence;

    @ApiModelProperty(value = "赠送商品价值")
    private BigDecimal giftGoodsPrice;

    @ApiModelProperty(value = "赠送订单号")
    private String giftOrderSn;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "状态描述")
    private String statusStr;

    @ApiModelProperty(value = "赠送商品")
    private CartRes cartRes;

    @ApiModelProperty(value = "审批id")
    private String approveId;

    @ApiModelProperty(value = "币种")
    private String currency;

}

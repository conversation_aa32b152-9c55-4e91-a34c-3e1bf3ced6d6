package com.stbella.order.server.order.month.res;

import com.stbella.platform.order.api.req.ExtraInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class OrderNewVO implements Serializable {

    @ApiModelProperty(value = "订单扩展信息")
    List<GoodsNewVO> goodsNewVOList;

    @ApiModelProperty(value = "订单扩展信息")
    private ExtraInfo extraInfo;

    private boolean isOldOrder;
}

package com.stbella.order.server.order.month.req;

import com.stbella.core.base.BasicReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 月子订单钉钉审批对象
 * </p>
 *
 * <AUTHOR> @since 2022-10-29
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "MonthDingContractPaperReq对象", description = "月子订单钉钉审批请求对象")
public class MonthDingContractPaperReq extends BasicReq implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "合同id")
    private Integer contractId;

    @ApiModelProperty(value = "文件列表")
    private List<String> fileIdList;


}

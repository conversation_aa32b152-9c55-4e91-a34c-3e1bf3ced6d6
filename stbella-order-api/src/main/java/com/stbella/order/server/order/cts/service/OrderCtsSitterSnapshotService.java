package com.stbella.order.server.order.cts.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.stbella.order.server.order.cts.entity.OrderCtsSitterSnapshotPO;

import java.util.List;

/**
 * <p>
 * 到家订单-育婴师快照 服务类
 * </p>
 *
 * <AUTHOR> @since 2022-03-21
 */
public interface OrderCtsSitterSnapshotService extends IService<OrderCtsSitterSnapshotPO> {

    /**
     * 根据订单编号获取绑定育婴师
     *
     * @param orderNos
     * @return java.util.List<com.stbella.order.server.order.cts.entity.OrderCtsSitterSnapshotPO>
     * @throws
     * <AUTHOR>
     * @date 2022/4/19 18:52
     * @since 1.0.0
     */
    List<OrderCtsSitterSnapshotPO> listByOrderNos(List<String> orderNos);

    /**
     * 绑定育婴师
     *
     * @param orderNo
     * @param sitterId
     * @return
     */
    Boolean saveOrderSnapshot(String orderNo, Long sitterId);
}

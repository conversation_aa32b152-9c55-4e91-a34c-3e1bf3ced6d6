package com.stbella.order.server.contract.req;


import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class OrderParamHistoryValuePushDTO implements Serializable {

    private static final long serialVersionUID = 8709118115964458529L;
    @ApiModelProperty(value = "参数名")
    private String name;
    @ApiModelProperty(value = "参数值")
    private String value;
}

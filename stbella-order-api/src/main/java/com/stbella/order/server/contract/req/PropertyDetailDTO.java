package com.stbella.order.server.contract.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class PropertyDetailDTO implements Serializable {

    private static final long serialVersionUID = -8366725444804051067L;

    private String id;

    @ApiModelProperty(value = "参数编码")
    private String code;

    @ApiModelProperty(value = "参数值")
    private String value;
}

package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 月子订单费用明细对象
 * </p>
 *
 * <AUTHOR> @since 2022-10-29
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "OrderMonthCostBreakdownVO对象", description = "月子订单费用明细对象")
public class OrderMonthCostBreakdownVO implements Serializable {

    private static final long serialVersionUID = 1L;


}

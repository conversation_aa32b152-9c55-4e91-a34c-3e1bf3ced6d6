package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "PlatformOrdeInfoVO对象", description = "订单平台-订单详情")
public class PlatformOrdeInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("订单后台-订单详情-基本信息")
    private STMOOrderBasicInfoVO stmoOrderBasicInfoVO;

    @ApiModelProperty("订单后台-订单详情-订单信息")
    private STMOrderInfoVO stmOrderInfoVO;

    @ApiModelProperty("订单后台-订单详情-财务信息")
    private STMOrderInfoFinancialVO stmOrderInfoFinancialVO;

    @ApiModelProperty("订单后台-订单详情-额外礼赠")
    private STMOrderInfoGiftVO stmOrderInfoGiftVO;

    @ApiModelProperty("订单后台-订单详情-分娩喜报")
    private STMOrderInfoLaborVO stmOrderInfoLaborVO;

}

package com.stbella.order.server.order.month.res;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 订单参数历史记录
 * </p>
 *
 * <AUTHOR>
@Data
@Accessors(chain = true)
@ApiModel(value = "OrderParamHistory对象", description = " 订单参数历史记录")
public class MonthOrderParamHistoryVO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @ApiModelProperty(value = "补充协议id")
    private Long contractId;

    @ApiModelProperty(value = "参数名称")
    private String name;

    @ApiModelProperty(value = "参数输入值")
    private String paramValue;

    @ApiModelProperty(value = "参数标识符,和合同模版中的占位符一一对应")
    private String mark;

    @ApiModelProperty(value = "所属条款。")
    private Integer terms;

    @ApiModelProperty(value = "所属项")
    private Integer item;

    @ApiModelProperty(value = "版本号")
    private Long history;

    @ApiModelProperty(value = "类型 1:主合同参数 2:补充协议参数")
    private Integer type;

    @ApiModelProperty(value = "生效状态 0:失效 1:有效")
    private Integer valid;

    /**
     * 逻辑删除标志
     */
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;
}

package com.stbella.order.server.order.month.res;

import com.stbella.platform.order.api.req.ExtraInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class WechatMyOrderInfoNewVO implements Serializable {

    private static final long serialVersionUID = 5278401466482782663L;

    private WechatMyOrderNewVO orderInfo;

    @ApiModelProperty(value = "货币符号")
    private String currency;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "特殊订单：false-否，true-是")
    private boolean special;

    @ApiModelProperty(value = "订单标签Id")
    private Integer orderTagId;

    @ApiModelProperty(value = "订单标签名称")
    private String orderTagName;

    private List<String> urlList;

    @ApiModelProperty(value = "特殊订单说明")
    private String remark;

    @ApiModelProperty(value = "预产期")
    private Date predictBornDate;

    private String predictBornDateStr;

    @ApiModelProperty(value = "胎数")
    private Integer fetusNum;

    private String fetusNumStr;

    @ApiModelProperty(value = "预计入住时间")
    private Date wantIn;

    private String wantInStr;

    @ApiModelProperty(value = "原始签单金额")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "应收金额")
    private BigDecimal calPayable;

    @ApiModelProperty(value = "订单原价")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "折扣金额")
    private BigDecimal discountAmount;

    @ApiModelProperty(value = "总礼赠金额")
    private BigDecimal giftAmount;

    @ApiModelProperty(value = "选购商品金额")
    private BigDecimal selectedAmount;

    @ApiModelProperty(value = "订单时间")
    private String orderDateStr;

    private List<IncomeRecord> incomeRecordList;

    private List<OrderRefund> orderRefundList;

    @ApiModelProperty(value = "订单扩展信息")
    private ExtraInfo extraInfo;

    @ApiModelProperty(value = "减免记录")
    private List<OrderReductionVO> orderReductionVOList;

    @ApiModelProperty("创建人id")
    private Long createBy;

    @ApiModelProperty("提交人姓名")
    private String createName;

    @ApiModelProperty("提交人Id")
    private Integer staffId;

    @ApiModelProperty("订单所有人")
    private String staffName;

    @ApiModelProperty(value = "减免总金额")
    private BigDecimal totalReductionAmount;

    @ApiModelProperty(value = "原商品退回金额")
    private BigDecimal originalGoodsTotalPrice;


    @Data
    public static class IncomeRecord implements Serializable {

        private static final long serialVersionUID = 2324914581780573002L;

        private Integer id;

        @ApiModelProperty(value = "支付编号")
        private String incomeSn;

        @ApiModelProperty(value = "1=微信, 2=支付宝, 3=线下支付, 4=中信银行支付宝支付, 5=中信银行微信支付  8=在线pos机支付  9=C端小程序支付 10=其他 11=线下支付-转账汇款 12=线下支付-现金")
        private Integer payType;

        private String payTypeStr;

        @ApiModelProperty(value = "支付状态：0待支付，1已支付,2支付失败")
        private Integer status;

        private String statusStr;

        @ApiModelProperty(value = "支付时间")
        private String payTime;

        @ApiModelProperty(value = "支付金额")
        private BigDecimal income;

        @ApiModelProperty(value = "币种")
        private String currency;
    }

    @Data
    public static class OrderRefund implements Serializable {

        private static final long serialVersionUID = 2324914581780573002L;

        @ApiModelProperty(value = "id")
        private Integer id;

        @ApiModelProperty(value = "退款订单编号")
        private String refundOrderSn;

        @ApiModelProperty(value = "添加时间")
        private String createdAt;

        @ApiModelProperty(value = "实际退款金额")
        private BigDecimal actualAmount;

        @ApiModelProperty(value = "退款状态，1：审批中 2：审批失败  3：审批成功/等待打款/待确认 4：退款已到帐/已确认 5：到账失败/已拒绝")
        private Integer status;

        private String statusStr;

        @ApiModelProperty(value = "币种")
        private String currency;
    }
}

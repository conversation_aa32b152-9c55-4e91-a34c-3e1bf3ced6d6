package com.stbella.order.server.order.month.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HeOrderGiftInfoGoodsAddVO implements Serializable {

    private static final long serialVersionUID = 2623100319304713025L;

    @ApiModelProperty(value = "赠送订单ID")
    private Long id;
}

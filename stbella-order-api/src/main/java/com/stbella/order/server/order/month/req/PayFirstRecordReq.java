package com.stbella.order.server.order.month.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel(value = "CalculateReq对象", description = "计算公式入参")
public class PayFirstRecordReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "首次支付时间-开始")
    @NotNull(message = "首次支付时间-开始不能为空")
    private Date payFirstStartTime;

    @ApiModelProperty(value = "首次支付时间-结束")
    @NotNull(message = "首次支付时间-结束不能为空")
    private Date payFirstEndTime;
}

package com.stbella.order.server.order.month.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stbella.order.server.order.month.req.QuerySbarGoodsOrderPageReq;
import com.stbella.order.server.order.month.res.QuerySbarGoodsOrderPageVO;

import javax.servlet.http.HttpServletResponse;

/**
 * Sbar订单后台单服务
 */
public interface SbarOrderAdminQueryService {

    Page<QuerySbarGoodsOrderPageVO> queryPageForSbarGoods(QuerySbarGoodsOrderPageReq queryOrderPageReq);

    void queryPageForSbarGoodsExport(QuerySbarGoodsOrderPageReq queryOrderPageReq, HttpServletResponse response);
}

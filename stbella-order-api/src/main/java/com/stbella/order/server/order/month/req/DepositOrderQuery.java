package com.stbella.order.server.order.month.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Set;

/**
 * 押金订单查询
 */
@Data
public class DepositOrderQuery implements Serializable {

    private static final long serialVersionUID = 1568030086891777252L;

    /***
     * 流水id
     */
    @ApiModelProperty(value = "流水id")
    private Long id;
    /***
     * 账户归属人
     */
    @ApiModelProperty(value = "账户归属人")
    private String userId;

    @ApiModelProperty(value = "客户id")
    private String clientUid;

    /***
     * 账户类型
     */
    @ApiModelProperty(value = "账户类型")
    private Long accountType;
    /****
     * 上级账户id
     */
    @ApiModelProperty(value = "上级账户id")
    private Long parentAccountId;
    /***
     * 二级账户id
     */
    @ApiModelProperty(value = "二级账户id")
    private Long accountId;
    /***
     * 流水资产类型
     */
    @ApiModelProperty(value = "流水资产类型")
    private Long assetType;
    /***
     * 资产id
     */
    @ApiModelProperty(value = "资产id")
    private String assetId;
    /***
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNo;
    /***
     * 来源业务id
     */
    @ApiModelProperty(value = "来源业务id")
    private String sourceBizId;
    /***
     * 交易类型集合
     */
    @ApiModelProperty(value = "交易类型集合")
    private Set<Long> tradeTypes;
}

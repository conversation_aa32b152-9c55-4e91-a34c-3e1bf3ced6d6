package com.stbella.order.server.order.cts.service;

import com.stbella.core.base.PageVO;
import com.stbella.core.base.UserTokenInfoDTO;
import com.stbella.order.server.order.cts.dto.RefundNotifyMqDTO;
import com.stbella.order.server.order.cts.dto.ServeFeeStatusDTO;
import com.stbella.order.server.order.cts.entity.OrderCtsPO;
import com.stbella.order.server.order.cts.request.order.*;
import com.stbella.order.server.order.cts.response.order.*;
import com.stbella.order.server.order.nutrition.dto.PayNotifyMqDTO;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 订单facade
 *
 * <AUTHOR>
 * @date 2022-03-18 14:19
 * @sine 1.0.0
 */
public interface OrderFacade {

    /**
     * 获取订单标识
     *
     * @param userId
     * @return java.lang.String
     * @throws
     * <AUTHOR>
     * @date 2022/3/18 15:26
     * @since 1.0.0
     */
    String orderSign(Long userId);

    /**
     * 雇主-育婴师订单创建
     *
     * @param request
     * @return java.lang.String
     * @throws
     * <AUTHOR>
     * @date 2022/3/21 00:18
     * @since 1.0.0
     */
    String createOrderBabySitter(BabySitterOrderCreateRequest request);

    /**
     * 雇主-母婴订单创建
     *
     * @param request
     * @return java.lang.String
     * @throws
     * <AUTHOR>
     * @date 2022/3/21 00:18
     * @since 1.0.0
     */
    String createOrderAuntSitter(AuntSitterOrderCreateRequest request);

    /**
     * 育婴师-培训课程订单创建
     *
     * @param request
     * @return java.lang.String
     * @throws
     * <AUTHOR>
     * @date 2022/4/12 10:24
     * @since 1.0.0
     */
    String createOrderCourse(CourseOrderCreateRequest request);

    /**
     * 育婴师-保证金订单创建
     *
     * @param request
     * @return java.lang.String
     * @throws
     * <AUTHOR>
     * @date 2022/4/12 10:24
     * @since 1.0.0
     */
    String createOrderMargin(MarginOrderCreateRequest request);

    /**
     * 育婴师-管理费订单创建
     *
     * @param request
     * @return java.lang.String
     * @throws
     * <AUTHOR>
     * @date 2022/4/12 10:24
     * @since 1.0.0
     */
    String createOrderServeFee(ServeFeeOrderCreateRequest request);

    /**
     * 育婴师-通用商品订单创建
     *
     * @param request
     * @return
     */
    String createOrderGeneralSitter(GeneralSitterOrderCreateRequest request);


    /**
     * 雇主-通用商品订单创建
     *
     * @param request
     * @return
     */
    String createOrderGeneralCustomer(GeneralCustomerOrderCreateRequest request);

    /**
     * 订单取消
     *
     * @param request
     * @return java.lang.Boolean
     * @throws
     * <AUTHOR>
     * @date 2022/3/21 14:25
     * @since 1.0.0
     */
    Boolean cancelOrder(OrderCancelBaseRequest request);

    /**
     * 通过支付标识订单支付页详情
     *
     * @param paySign
     * @return com.stbella.order.server.order.cts.response.order.CtsPayDetailsResponse
     * @throws
     * <AUTHOR>
     * @date 2022/3/21 20:51
     * @since 1.0.0
     */
    CtsPayDetailsResponse queryPayDetailByPaySign(String paySign);

    /**
     * 通过订单获取订单金额信息
     *
     * @param orderNo
     * @return com.stbella.order.server.order.cts.response.order.CtsPayDetailsBaseResponse
     * @throws
     * <AUTHOR>
     * @date 2022/4/7 13:12
     * @since 1.0.0
     */
    CtsPayDetailsBaseResponse queryPayDetailByOrderNo(String orderNo);

    /**
     * 支付回调
     *
     * @param payNotifyMq
     * @param orderNo
     * @return java.lang.Boolean
     * @throws
     * <AUTHOR>
     * @date 2022/3/22 16:26
     * @since 1.0.0
     */
    Boolean payNotify(PayNotifyMqDTO payNotifyMq, String orderNo);

    /**
     * 退款通知
     *
     * @param refundNotifyMqDTO
     * @return
     */
    Boolean refundNotify(RefundNotifyMqDTO refundNotifyMqDTO);

    /**
     * 获取订单匹配育婴师列表
     *
     * @param orderNo
     * @return java.util.List<com.stbella.order.server.order.cts.response.order.CustomerOrderSitterListResponse>
     * @throws
     * <AUTHOR>
     * @date 2022/3/23 14:54
     * @since 1.0.0
     */
    List<CustomerOrderSitterListResponse> getOrderSitterList(String orderNo);

    /**
     * 终止服务详情
     *
     * @param orderSitterSnapshotId
     * @return com.stbella.order.server.order.cts.response.order.CustomerTerminationDetailResponse
     * @throws
     * <AUTHOR>
     * @date 2022/3/23 15:21
     * @since 1.0.0
     */
    CustomerTerminationDetailResponse terminationDetail(Long orderSitterSnapshotId);

    /**
     * 终止服务
     *
     * @param request
     * @return java.lang.Boolean
     * @throws
     * <AUTHOR>
     * @date 2022/3/23 15:35
     * @since 1.0.0
     */
    Boolean termination(CustomerTerminationRequest request);

    /**
     * 雇主-申请退款
     *
     * @param request
     * @return java.lang.Long
     * @throws
     * <AUTHOR>
     * @date 2022/3/24 14:05
     * @since 1.0.0
     */
    Long applyRefund(OrderApplyRefundRequest request);

    /**
     * 订单权限信息
     *
     * @param jwtTokenUserInfo
     * @return com.stbella.order.server.order.cts.response.order.OrderAuthResponse
     * @throws
     * <AUTHOR>
     * @date 2022/4/1 10:45
     * @since 1.0.0
     */
    OrderAuthResponse orderAuth(UserTokenInfoDTO jwtTokenUserInfo);

    /**
     * 获取育婴师平台管理费状态
     *
     * @param sitterIdList
     * @return java.util.List<com.stbella.order.server.order.cts.dto.ServeFeeStatusDTO>
     * @throws
     * <AUTHOR>
     * @date 2022/4/13 18:46
     * @since 1.0.0
     */
    List<ServeFeeStatusDTO> getServeFeeStatus(List<Long> sitterIdList);

    /**
     * 订单列表分页
     *
     * @param request
     * @return com.stbella.core.base.PageVO<com.stbella.order.server.order.cts.response.order.PageOrderListResponse>
     * @throws
     * <AUTHOR>
     * @date 2022/4/14 18:17
     * @since 1.0.0
     */
    PageVO<PageOrderListResponse> pageOrderList(PageOrderListRequest request);


    /**
     * 根据客户姓名、手机、订单类型获取订单编号列表
     *
     * @param customerName
     * @param mobile
     * @param orderTypeList
     * @return
     */
    Set<String> getOrderNosByCustomer(String customerName, String mobile, List<Integer> orderTypeList);


    /**
     * 根据客户姓名、手机号获取订单编号列表
     *
     * @param customerName
     * @param mobile
     * @return
     */
    Set<String> getOrderNosByCustomer(String customerName, String mobile);

    /**
     * 根据分站id列表查询订单编号列表
     *
     * @param siteIds
     * @return
     */
    Set<String> getOrderNosBySiteIds(List<Long> siteIds);

    /**
     * 根据订单列表获取分站id和分站名map
     *
     * @param list
     * @return
     */
    HashMap<String, String> ctsSiteByOrders(List<OrderCtsPO> list);

    /**
     * 根据订单查询客户信息
     *
     * @param orderIds
     * @return
     */
    HashMap<String, AdminCustomerVO> customerByOrderIds(List<String> orderIds);

    /**
     * 根据订单列表到customer表中获取的客户信息map
     *
     * @param list
     * @return
     */
    HashMap<String, AdminCustomerVO> customerByOrders(List<OrderCtsPO> list);

    /**
     * 根据订单列表到育婴师镜像表中获取育婴师信息
     *
     * @param orderList
     * @return
     */
    Map<String, AdminCustomerVO> sitterByOrderIds(List<OrderCtsPO> orderList);

    /**
     * 订单上传附件
     *
     * @param request
     * @return
     */
    Boolean orderAttachmentSubmit(OrderAttachmentSubmitRequest request);
}

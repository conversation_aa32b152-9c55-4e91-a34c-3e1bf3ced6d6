package com.stbella.order.server.order.order.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
public class ExportProductionRealPaidReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "核销开始时间不能为空")
    @ApiModelProperty(value = "核销开始时间")
    private Date writeOffStartDate;

    @NotNull(message = "核销结束时间不能为空")
    @ApiModelProperty(value = "核销结束时间")
    private Date writeOffEndDate;

}

package com.stbella.order.infrastructure.repository.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.exception.ApplicationException;
import com.stbella.order.domain.order.production.TherapistScheduleEntity;
import com.stbella.order.domain.repository.TherapistScheduleRepository;
import com.stbella.order.infrastructure.repository.converter.ProductionAppointmentConverter;
import com.stbella.order.infrastructure.repository.mapper.saas.TherapistScheduleMapper;
import com.stbella.order.infrastructure.repository.po.TherapistSchedule;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-30 17:22
 */
@Repository
public class TherapistScheduleRepositoryImpl extends ServiceImpl<TherapistScheduleMapper, TherapistSchedule> implements TherapistScheduleRepository {

    @Resource
    private ProductionAppointmentConverter appointmentConverter;

    @Override
    public List<TherapistScheduleEntity> therapistScheduleListByTherapistId(Long therapistId) {
        List<TherapistSchedule> therapistSchedules = this.baseMapper.selectList(new LambdaQueryWrapper<TherapistSchedule>().eq(TherapistSchedule::getTherapistId, therapistId).eq(TherapistSchedule::getDeleted, 0));
        if (CollectionUtil.isEmpty(therapistSchedules)) {
            return new ArrayList();
        }
        List<TherapistScheduleEntity> therapistScheduleEntities = appointmentConverter.po2therapistSchedulesList(therapistSchedules);
        if (CollectionUtil.isNotEmpty(therapistScheduleEntities)) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            try {
                for (TherapistScheduleEntity scheduleEntity : therapistScheduleEntities) {
                    String serveDate = scheduleEntity.getServeDate();
                    String startTime = scheduleEntity.getStartTime();
                    Date ms = simpleDateFormat.parse(serveDate + " " + startTime);
                    scheduleEntity.setMargeStartTime(ms);
                    scheduleEntity.setMargeEndTime(new Date(ms.getTime() + 30 * 60 * 1000L));
                }
            } catch (Exception e) {
                log.error(e.getMessage());
                throw new ApplicationException(ErrorCodeEnum.BASE_ERROR.code(), "产康师预约时间获取时间失败");
            }
        }
        return therapistScheduleEntities;
    }

}

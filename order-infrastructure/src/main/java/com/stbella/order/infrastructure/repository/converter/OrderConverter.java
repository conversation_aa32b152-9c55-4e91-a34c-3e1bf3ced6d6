package com.stbella.order.infrastructure.repository.converter;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stbella.order.common.enums.core.OrderRefundStatusEnum;
import com.stbella.order.common.enums.month.OrderStatusV2Enum;
import com.stbella.order.common.enums.month.PayStatusV2Enum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.infrastructure.repository.po.*;
import com.stbella.order.server.order.month.req.OrderMonthClientBailorReq;
import com.stbella.order.server.order.month.req.OrderMonthClientReq;
import com.stbella.order.server.order.month.res.OrderAdditionalRevenueCacheVO;
import com.stbella.order.server.order.month.res.WechatMyOrderVO;
import com.stbella.order.server.order.month.response.OrderBailorSnapshotVO;
import com.stbella.order.server.order.month.response.OrderUserSnapshotVO;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.platform.order.api.req.ExtraInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring", imports = {AmountChangeUtil.class, Date.class, ObjectUtil.class, JSONUtil.class, ExtraInfo.class})
public interface OrderConverter {

    /**
     * 订单主信息
     * entity -> po
     */
    @Mapping(target = "extraInfo", expression = "java(JSONUtil.toJsonStr(entity.getExtraInfo()))")
    HeOrderPO heOrderEntity2HeOrderPO(HeOrderEntity entity);

    /**
     * 订单加收项
     * entityList -> poList
     */
    List<HeOrderAdditionalRevenuePO> listHeOrderAdditionalRevenueEntity2PO(List<HeOrderAdditionalRevenueEntity> entityList);

    /**
     * 订单加收项
     * poList -> entityList
     */
    List<HeOrderAdditionalRevenueEntity> listHeOrderAdditionalRevenuePO2Entity(List<HeOrderAdditionalRevenuePO> list);

    /**
     * 订单凭证
     * entityList -> poList
     */
    List<HeOrderVoucherPO> listHeOrderVoucherEntity2PO(List<HeOrderVoucherEntity> entityList);

    /**
     * 用户快照
     * entity -> po
     */
    @Mappings({
            @Mapping(target = "wantIn", expression = "java( ObjectUtil.isEmpty(entity.getWantIn())?null:entity.getWantIn().getTime()/1000)"),
    })
    HeOrderUserSnapshotPO heOrderUserSnapshotEntity2PO(HeOrderUserSnapshotEntity entity);

    /**
     * 订单商品
     * entity -> po
     */
    HeOrderGoodsPO heOrderGoodsEntity2PO(HeOrderGoodsEntity entity);

    /**
     * 订单客户
     * req -> entity
     */
    HeOrderUserSnapshotEntity orderMonthClientReq2Entity(OrderMonthClientReq orderMonthClientReq);

    /**
     * 订单主信息
     * req -> entity
     */
    @Mappings({
            @Mapping(target = "wantIn", expression = "java( ObjectUtil.isEmpty(req.getWantIn())?null:req.getWantIn().getTime()/1000)")
    })
    HeOrderEntity orderMonthClientReq2HeOrderEntity(OrderMonthClientReq req);

    /**
     * 订单主信息
     * po -> entity
     */
    @Mapping(target = "extraInfo", expression = "java(JSONUtil.toBean(po.getExtraInfo(),ExtraInfo.class))")
    HeOrderEntity orderPo2Entity(HeOrderPO po);

    List<HeOrderEntity> orderPoListEntity(List<HeOrderPO> poList);


    /**
     * 订单加收项
     * vo -> entity
     */
    @Mappings({
            @Mapping(target = "cost", expression = "java( Integer.valueOf(AmountChangeUtil.changeY2FCheckNullToZero(String.valueOf(orderAdditionalRevenueCacheVO.getCost()))))"),
            @Mapping(target = "price", expression = "java( Integer.valueOf(AmountChangeUtil.changeY2FCheckNullToZero(String.valueOf(orderAdditionalRevenueCacheVO.getPrice()))))"),
            @Mapping(target = "costPrice", expression = "java( Integer.valueOf(AmountChangeUtil.changeY2FCheckNullToZero(String.valueOf(orderAdditionalRevenueCacheVO.getCostPrice()))))"),
    })
    HeOrderAdditionalRevenueEntity orderAdditionalRevenueCacheVOS2Entity(OrderAdditionalRevenueCacheVO orderAdditionalRevenueCacheVO);

    /**
     * 订单加收项
     * vos -> entityList
     */
    List<HeOrderAdditionalRevenueEntity> orderAdditionalRevenueCacheVOS2EntityList(List<OrderAdditionalRevenueCacheVO> orderAdditionalRevenueCacheVOS);


    /**
     * 订单快照信息
     * po -> entity
     */
    @Mappings({
            @Mapping(target = "wantIn", expression = "java( ObjectUtil.isEmpty(po.getWantIn())?null:new Date(po.getWantIn() * 1000))")
    })
    HeOrderUserSnapshotEntity po2OrderUserSnapshotEntity(HeOrderUserSnapshotPO po);

    HeOrderGoodsEntity orderGoodsPo2Entity(HeOrderGoodsPO po);

    List<HeOrderGoodsEntity> orderGoodsPo2ListEntity(List<HeOrderGoodsPO> poList);


    List<HeOrderGoodsPO> orderGoodsListEntity2Po(List<HeOrderGoodsEntity> poList);

    /**
     * 委托人转换
     * entity -> po
     */
    HeOrderBailorSnapshotPO heOrderBailorSnapshotEntity2PO(HeOrderBailorSnapshotEntity entity);

    /**
     * 委托人转换
     * req -> entity
     */
    HeOrderBailorSnapshotEntity orderMonthClientBailorReq2Entity(OrderMonthClientBailorReq req);

    /**
     * 我的订单分页查询
     *
     * @param page
     * @param storeEntityList
     * @return
     */
    default Page<WechatMyOrderVO> converterMyOrderPage(Page<WechatMyOrderVO> page, List<CfgStoreEntity> storeEntityList, List<HeOrderUserSnapshotEntity> heOrderUserSnapshotEntities) {
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return page;
        }

        Map<Integer, CfgStoreEntity> storeMap = storeEntityList.stream().collect(Collectors.toMap(CfgStoreEntity::getStoreId, Function.identity()));
        Map<Integer, HeOrderUserSnapshotEntity> clientMap = heOrderUserSnapshotEntities.stream().collect(Collectors.toMap(HeOrderUserSnapshotEntity::getOrderId, Function.identity(),  (item1, item2) -> item1));

        page.getRecords().forEach(item -> {
            if (Objects.nonNull(item.getStoreId())) {
                item.setStoreName(storeMap.get(item.getStoreId()).getStoreName());
                item.setStoreType(storeMap.get(item.getStoreId()).getType());
            }

            Long realAmount = ObjectUtil.isEmpty(item.getRealAmount()) ? 0L : new Long(item.getRealAmount());
            item.setRealAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(realAmount)));
            item.setPayAmount(ObjectUtil.isEmpty(item.getPayAmount()) ? "0" : item.getPayAmount());
            item.setOrderAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(item.getOrderAmountLong())));
            item.setPaidAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(item.getPaidAmountLong())));
            item.setPayAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(item.getPayAmountLong())));
            long remainAmount = item.getPayAmountLong() - realAmount;
            item.setRemainAmount(remainAmount <= 0 ? "0.00" : RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(remainAmount)));
            item.setPayStatusName(PayStatusV2Enum.getName(item.getPayStatus()));
            if (OrderStatusV2Enum.CLOSE.getCode().intValue() == item.getOrderStatus()){
                item.setPayStatusName(PayStatusV2Enum.CLOSED.name());
            }

            item.setRefundStatusName(OrderRefundStatusEnum.getName(item.getRefundStatus()));

            Integer orderId = item.getOrderId();
            //获取订单客户快照表
            HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = clientMap.get(item.getOrderId());
            if (Objects.nonNull(heOrderUserSnapshotEntity)) {
                item.setPhone(heOrderUserSnapshotEntity.getPhone());
                item.setClientName(heOrderUserSnapshotEntity.getName());
            }

        });
        return page;
    }



    List<HeOrderAdditionalRevenueEntity> orderAdditionalRevenueEntityByPoList(List<HeOrderAdditionalRevenuePO> heOrderAdditionalRevenuePOS);

    List<HeOrderVoucherEntity> orderVoucherEntityByPoList(List<HeOrderVoucherPO> heOrderVoucherPOS);

    HeOrderBailorSnapshotEntity heOrderBailorSnapshotPOTOOrderBailorSnapshotEntity(HeOrderBailorSnapshotPO heOrderBailorSnapshotPO);

    /**
     * 订单其他po转entity
     *
     * @param po
     * @return
     */
    HeOrderOtherEntity orderOtherPo2Entity(HeOrderOtherPO po);

    /**
     * 订单entity转WechatMyOrderVO
     *
     * @param entity
     * @return
     */
    @Mappings({
            @Mapping(target = "orderAmountLong", expression = "java(entity.getOrderAmount().longValue())"),
            @Mapping(target = "payAmountLong", expression = "java(entity.getPayAmount().longValue())"),
            @Mapping(target = "paidAmountLong", expression = "java(entity.getPaidAmount().longValue())"),
    })
    WechatMyOrderVO orderEntity2WechatMyOrderVO(HeOrderEntity entity);

    List<HeOrderContinueLiveRecordEntity> orderContinueLiveRecordPOList2OrderContinueLiveRecordEntityList(List<HeOrderContinueLiveRecordPO> orderContinueLiveRecordPOList);

    List<HeOrderUserSnapshotEntity> heOrderUserSnapshotPOS2HeOrderUserSnapshotEntity(List<HeOrderUserSnapshotPO> heOrderUserSnapshotPOS);

    Page<HeOrderEntity> pagePO2PageEntity(Page pagePO);

    List<HeOrderRoomTypeChangeRecordEntity> orderRoomTypeChangeRecordPOList2OrderRoomTypeChangeRecordEntityList(List<HeOrderRoomTypeChangeRecordPO> heOrderRoomTypeChangeRecordPOS);

    @Mappings({
            @Mapping(target = "idNumber", source = "email"),
    })
    OrderUserSnapshotVO entity2OrderUserSnapshotVO(HeOrderUserSnapshotEntity entity);

    @Mappings({
            @Mapping(target = "idNumber", source = "email"),
    })
    OrderBailorSnapshotVO entity2OrderBailorSnapshotVO(HeOrderBailorSnapshotEntity entity);

    List<HeOrderPO> entityList2PoList(List<HeOrderEntity> orderList);
}

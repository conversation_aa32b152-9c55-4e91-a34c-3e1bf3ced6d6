package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsEntity;
import com.stbella.order.domain.order.month.entity.HeCustomerComplaintsTypeEntity;
import com.stbella.order.infrastructure.repository.po.HeCustomerComplaintsPO;
import com.stbella.order.infrastructure.repository.po.HeCustomerComplaintsTypePO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface HeCustomerComplaintsConverter {

    List<HeCustomerComplaintsTypeEntity> customerComplaintsTypePOToVO(List<HeCustomerComplaintsTypePO> heCustomerComplaintsTypePOS);

    HeCustomerComplaintsPO entity2Po(HeCustomerComplaintsEntity heCustomerComplaintsEntity);

    List<HeCustomerComplaintsEntity> customerComplaintsPOToEntity(List<HeCustomerComplaintsPO> records);

    HeCustomerComplaintsEntity po2Entity(HeCustomerComplaintsPO heCustomerComplaintsPO);

    HeCustomerComplaintsTypeEntity po2Entity(HeCustomerComplaintsTypePO heCustomerComplaintsTypePO);
}

package com.stbella.order.infrastructure.repository.mapper.saas;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stbella.core.base.StoreInfoDTO;
import com.stbella.core.utils.sso.EmployeeTokenHelper;
import com.stbella.order.common.enums.production.*;
import com.stbella.order.infrastructure.repository.condition.ProductionAppointmentBoardWorkmanshipCondition;
import com.stbella.order.infrastructure.repository.condition.ProductionAppointmentQueryBackCondition;
import com.stbella.order.infrastructure.repository.condition.ProductionAppointmentQueryCondition;
import com.stbella.order.infrastructure.repository.condition.ProductionAppointmentScheduleCondition;
import com.stbella.order.infrastructure.repository.po.OrderProductionAppointment;
import com.stbella.order.infrastructure.repository.po.ProductionAppointmentBoardWorkmanship;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

public interface OrderProductionAppointmentMapper extends BaseMapper<OrderProductionAppointment> {
    int deleteByPrimaryKey(Long id);

    int insertSelective(OrderProductionAppointment record);

    OrderProductionAppointment selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrderProductionAppointment record);

    int updateByPrimaryKey(OrderProductionAppointment record);

    List<OrderProductionAppointment> selectByParam(@Param("condition") ProductionAppointmentQueryCondition condition);

    long countByParam(@Param("condition") ProductionAppointmentQueryCondition condition);

    ProductionAppointmentBoardWorkmanship sumWorkmanshipByTherapistId(@Param("condition") ProductionAppointmentBoardWorkmanshipCondition condition);


    /**
     * 分页
     *
     * @param condition 筛选条件
     * @return Page<OrderProductionAppointment>
     */
    default Page<OrderProductionAppointment> page(ProductionAppointmentQueryBackCondition condition) {
        Boolean storeAuth = Objects.isNull(condition.getStoreAuth()) || condition.getStoreAuth();

        //只查询员工拥有的门店数据
        StoreInfoDTO storeInfoDTO = EmployeeTokenHelper.getCurrentEmployeeStoreInfo();
        if (storeAuth && !storeInfoDTO.getHasAllStoreInfo()) {
            if (CollectionUtils.isEmpty(condition.getStoreIds())){
                condition.setStoreIds(storeInfoDTO.getStoreIdList());
            }
        }

        if (storeAuth && !storeInfoDTO.getHasAllStoreInfo() && CollectionUtils.isEmpty(storeInfoDTO.getStoreIdList())) {
            Page nullPage = new Page();
            nullPage.setTotal(0);
            nullPage.setSize(condition.getPageSize());
            nullPage.setCurrent(condition.getPageNum());
            return nullPage;
        }

        // 产康师自己或代客人预约下单人自己
        return selectPage(new Page(condition.getPageNum(), condition.getPageSize()), new LambdaQueryWrapper<OrderProductionAppointment>()
                .and(
                        Objects.nonNull(condition.getTherapistId()),
                        queryWrapper -> queryWrapper.eq(OrderProductionAppointment::getTherapistId, condition.getTherapistId())
                                .or()
                                .eq(OrderProductionAppointment::getBookType, ProductionBookTypeEnum.HELP_CUSTOMER_BOOK.code())
                                .eq(OrderProductionAppointment::getCreatorId, condition.getTherapistId())
                )
                .and(
                        StringUtils.isNotBlank(condition.getKeyword()),
                        queryWrapper -> queryWrapper.like(OrderProductionAppointment::getClientName, condition.getKeyword())
                                .or()
                                .like(OrderProductionAppointment::getClientPhone, condition.getKeyword())
                                .or()
                                .like(OrderProductionAppointment::getOrderSn, condition.getKeyword())
                )
                .eq(Objects.nonNull(condition.getServiceTherapistId()), OrderProductionAppointment::getTherapistId, condition.getServiceTherapistId())
                .eq(Objects.nonNull(condition.getStoreId()), OrderProductionAppointment::getStoreId, condition.getStoreId())
                .eq(Objects.nonNull(condition.getClientId()), OrderProductionAppointment::getClientId, condition.getClientId())
                .eq(Objects.nonNull(condition.getBasicId()), OrderProductionAppointment::getBasicId, condition.getBasicId())
                .eq(Objects.nonNull(condition.getBookType()), OrderProductionAppointment::getBookType, condition.getBookType())
                .eq(Objects.nonNull(condition.getVerificationState()), OrderProductionAppointment::getVerificationState, condition.getVerificationState())
                .eq(Objects.nonNull(condition.getAppointmentType()), OrderProductionAppointment::getAppointmentType, condition.getAppointmentType())
                .ne(OrderProductionAppointment::getVerificationState, OrderProductionVerificationStateEnum.PENDING_WRITE_OFF.code())
                .and(
                        StringUtils.isNotBlank(condition.getClientName()),
                        queryWrapper -> queryWrapper.like(OrderProductionAppointment::getClientName, condition.getClientName())
                                .or()
                                .like(OrderProductionAppointment::getClientPhone, condition.getClientName())
                )
                .in(CollectionUtils.isNotEmpty(condition.getTherapistIdList()), OrderProductionAppointment::getTherapistId, condition.getTherapistIdList())
                .in(CollectionUtils.isNotEmpty(condition.getStoreIds()), OrderProductionAppointment::getStoreId, condition.getStoreIds())
                .ge(ObjectUtil.isNotNull(condition.getServeStart()), OrderProductionAppointment::getServeStart, condition.getServeStart())
                .le(ObjectUtil.isNotNull(condition.getServeEnd()), OrderProductionAppointment::getServeEnd, condition.getServeEnd())
                .in(CollectionUtils.isNotEmpty(condition.getAppointmentIds()), OrderProductionAppointment::getId, condition.getAppointmentIds())
                .eq(ObjectUtil.isNotNull(condition.getServeType()), OrderProductionAppointment::getServeType, condition.getServeType())
                .orderByDesc(OrderProductionAppointment::getGmtCreate)
        );
    }

    default List<OrderProductionAppointment> selectProductionSchedule(ProductionAppointmentScheduleCondition condition) {
        return selectList(new QueryWrapper<OrderProductionAppointment>()
                .orderByAsc("field(verification_state,2,1,3)")
                .lambda()
                .eq(condition.getTherapistId() != null, OrderProductionAppointment::getTherapistId, condition.getTherapistId())
                .eq(condition.getClientId() != null, OrderProductionAppointment::getClientId, condition.getClientId())
                .eq(condition.getBasicId() != null, OrderProductionAppointment::getBasicId, condition.getBasicId())
                .ne(OrderProductionAppointment::getVerificationState, OrderProductionVerificationStateEnum.PENDING_WRITE_OFF.code())
                .ge(ObjectUtil.isNotNull(condition.getServeStart()), OrderProductionAppointment::getServeStart, condition.getServeStart())
                .le(ObjectUtil.isNotNull(condition.getServeEnd()), OrderProductionAppointment::getServeEnd, condition.getServeEnd())
                .in(condition.getTerminalType() != null && condition.getTerminalType().equals(TerminalTypeEnum.TERMINAL_TYPE_PI.code()), OrderProductionAppointment::getVerificationState, Arrays.asList(VerificationStatusEnum.BOOKED.code(), VerificationStatusEnum.DONE_WRITE_OFF.code()))
                .eq(OrderProductionAppointment::getAppointmentType, ProductionAppointmentTypeEnum.APPOINTMENT_TYPE_ONE.code())
                .orderByAsc(OrderProductionAppointment::getServeStart));
    }

    /**
     * 预约单列表
     *
     * @param queryBackCondition 参数
     * @return list
     */
    List<OrderProductionAppointment> getProductionBookList(@Param("query") ProductionAppointmentQueryBackCondition queryBackCondition);

    Long countProductionBookList(@Param("query") ProductionAppointmentQueryBackCondition queryBackCondition);

    /**
     * 产康师添加查询
     *
     * @param condition 条件
     * @return {@link List}<{@link OrderProductionAppointment}>
     */
    default List<OrderProductionAppointment> queryProductionByTherapistId(ProductionAppointmentQueryBackCondition condition) {
        //todo 产康师自己或代客人预约下单人自己
        return selectList(new LambdaQueryWrapper<OrderProductionAppointment>()
                .and(
                        Objects.nonNull(condition.getTherapistId()),
                        queryWrapper -> queryWrapper.eq(OrderProductionAppointment::getTherapistId, condition.getTherapistId())
                                .or()
                                .eq(OrderProductionAppointment::getBookType, ProductionBookTypeEnum.HELP_CUSTOMER_BOOK.code())
                                .eq(OrderProductionAppointment::getCreatorId, condition.getTherapistId())
                )
        );
    }
}
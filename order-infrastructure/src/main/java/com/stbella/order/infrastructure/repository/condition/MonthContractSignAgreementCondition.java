package com.stbella.order.infrastructure.repository.condition;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.stbella.order.common.constant.TableFieldConstant;

import java.time.LocalDateTime;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 合同补充协议
 * </p>
 *
 * <AUTHOR> @since 2021-11-03
 */
@Data
@Accessors(chain = true)
public class MonthContractSignAgreementCondition {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "补充协议编号")
    private String code;

    @ApiModelProperty(value = "补充协议名称。")
    private String name;

    @ApiModelProperty(value = "状态 0:待签署 2:已签署")
    private Integer state;

    @ApiModelProperty(value = "签署时间")
    private LocalDateTime signTime;

    @ApiModelProperty(value = "门店id")
    private Long storeId;

    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @ApiModelProperty(value = "E签宝流程id。")
    private String esignFlowId;

    @ApiModelProperty(value = "E签宝文件id。")
    private String esignFileId;

    @ApiModelProperty(value = "E签宝模版id。")
    private String esignTemplateId;

    @ApiModelProperty(value = "E签宝文件名称")
    private String esignFileName;

    @ApiModelProperty(value = "盖章页码")
    private Integer pageNum;

    @ApiModelProperty(value = "甲方盖章x，y定位坐标，第一位x第二位y中间逗号隔开")
    private String positionA;

    @ApiModelProperty(value = "乙方盖章x，y定位坐标，第一位x第二位y中间逗号隔开")
    private String positionB;

    @ApiModelProperty(value = "E签宝合同查看长链接")
    private String contractLongUrl;

    @ApiModelProperty(value = "E签宝合同查看短链接")
    private String contractShortUrl;

    @ApiModelProperty(value = TableFieldConstant.templateContractType)
    private Integer templateContractType;

    /**
     * 合同签订入口0=未知;1=h5;2=微信;3=支付宝
     */
    @ApiModelProperty(value = "合同签订入口0=未知;1=h5;2=微信;3=支付宝")
    private Integer signFrom;

    /**
     * 逻辑删除标志
     */
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;
}

package com.stbella.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 套餐设置
 * </p>
 *
 * <AUTHOR> @since 2023-02-08
 */
@Data
@Accessors(chain = true)
@TableName("setting_package")
@ApiModel(value = "SettingPackagePO对象", description = "套餐设置")
public class SettingPackagePO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "原套餐id。副本记录原记录id")
    private Integer parentId;

    @ApiModelProperty(value = "订单id。用于记录改套餐被哪个订单绑定")
    private Integer orderId;

    @ApiModelProperty(value = "套餐名字")
    private String name;

    @ApiModelProperty(value = "门店id")
    private Integer storeId;

    @ApiModelProperty(value = "餐单id")
    private Integer mealMenuId;

    @ApiModelProperty(value = "房间类型id")
    private Integer roomTypeId;

    @ApiModelProperty(value = "物品明细。模板：{1: 12, 2: 28}     id 为 1 的物品 12 个，id 为 2 的物品 28 个")
    private String momItemVo;

    @ApiModelProperty(value = "宝宝物品")
    private String babyItemVo;

    @ApiModelProperty(value = "固定物品")
    private String fixedItemVo;

    @ApiModelProperty(value = "服务详情")
    private String serverVo;

    @ApiModelProperty(value = "产康项目详情")
    private String healthVo;

    @ApiModelProperty(value = "服务天数")
    private Integer serviceDays;

    @ApiModelProperty(value = "套餐价格，单位：元")
    private BigDecimal price;

    @ApiModelProperty(value = "是否小月子")
    private Integer isSmall;

    @ApiModelProperty(value = "启用状态。0:弃用 1:启用")
    private Integer active;

    @ApiModelProperty(value = "最后修改人")
    private Integer lastAdminId;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "记录时间")
    private Date recordTime;


}

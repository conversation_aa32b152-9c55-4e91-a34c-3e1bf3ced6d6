package com.stbella.order.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.order.common.enums.core.IsNanEnum;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.order.month.entity.HeOrderAttachmentEntity;
import com.stbella.order.domain.repository.HeOrderAttachmentRepository;
import com.stbella.order.infrastructure.repository.converter.OrderAttachmentConverter;
import com.stbella.order.infrastructure.repository.mapper.saas.HeOrderAttachmentMapper;
import com.stbella.order.infrastructure.repository.po.HeOrderAttachmentPO;
import com.stbella.order.server.contract.req.MonthContractSignQuery;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20 14:05
 */
@Repository
public class HeOrderAttachmentRepositoryImpl extends ServiceImpl<HeOrderAttachmentMapper, HeOrderAttachmentPO> implements HeOrderAttachmentRepository {

    @Resource
    private OrderAttachmentConverter orderAttachmentConverter;

    @Override
    public List<HeOrderAttachmentEntity> queryListByCondition(MonthContractSignQuery monthContractSignQuery) {
        LambdaQueryWrapper<HeOrderAttachmentPO> wrapper = new LambdaQueryWrapper<HeOrderAttachmentPO>()
                .eq(Objects.nonNull(monthContractSignQuery.getOrderId()), HeOrderAttachmentPO::getOrderId, monthContractSignQuery.getOrderId());
        List<HeOrderAttachmentPO> orderAttachmentPOS = this.baseMapper.selectList(wrapper);

        return orderAttachmentConverter.po2OrderAttachmentList(orderAttachmentPOS);
    }

    @Override
    public List<HeOrderAttachmentEntity> queryListByCondition(Integer orderId) {
        LambdaQueryWrapper<HeOrderAttachmentPO> wrapper = new LambdaQueryWrapper<HeOrderAttachmentPO>()
                .eq(Objects.nonNull(orderId), HeOrderAttachmentPO::getOrderId, orderId);
        List<HeOrderAttachmentPO> orderAttachmentPOS = this.baseMapper.selectList(wrapper);
        return orderAttachmentConverter.po2OrderAttachmentList(orderAttachmentPOS);
    }

    @Override
    public Integer insertOne(HeOrderAttachmentEntity heOrderAttachmentEntity) {

        HeOrderAttachmentPO heOrderAttachmentPO = orderAttachmentConverter.entity2OrderAttachmentPO(heOrderAttachmentEntity);
        //软删除
        this.deleteByOrderId(heOrderAttachmentEntity.getOrderId());
        //新增
        this.save(heOrderAttachmentPO);

        return heOrderAttachmentPO.getId();
    }

    /**
     * 批量保存
     *
     * @param entityList
     * @return Integer
     */
    @Override
    public Boolean batchInsert(List<HeOrderAttachmentEntity> entityList) {
        List<HeOrderAttachmentPO> attachmentPOS = BeanMapper.mapList(entityList, HeOrderAttachmentPO.class);
        //设置删除状态，时间等
        attachmentPOS.forEach(attachmentPO -> {
            attachmentPO.setIsDelete(0);
            attachmentPO.setCreatedAt(System.currentTimeMillis()/1000);
            attachmentPO.setUpdatedAt(System.currentTimeMillis()/1000);
        });

        return this.saveBatch(attachmentPOS);
    }

    @Override
    public Boolean deleteByOrderId(Integer orderId) {
        LambdaUpdateWrapper<HeOrderAttachmentPO> lu = new LambdaUpdateWrapper<>();
        lu.set(HeOrderAttachmentPO::getIsDelete, IsNanEnum.YES.code());
        lu.eq(HeOrderAttachmentPO::getOrderId, orderId);

        return this.update(lu);
    }

    @Override
    public List<HeOrderAttachmentEntity> queryListByCondition(List<Integer> orderIds) {
        LambdaQueryWrapper<HeOrderAttachmentPO> wrapper = new LambdaQueryWrapper<HeOrderAttachmentPO>()
                .in(CollectionUtils.isNotEmpty(orderIds), HeOrderAttachmentPO::getOrderId, orderIds);
        List<HeOrderAttachmentPO> orderAttachmentPOS = this.baseMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(orderAttachmentPOS)) {
            return new ArrayList<>();
        }
        return orderAttachmentConverter.po2OrderAttachmentList(orderAttachmentPOS);
    }

    @Override
    public Boolean batchAdd(List<HeOrderAttachmentEntity> entityList) {

        if (CollectionUtils.isEmpty(entityList)){
            return false;
        }
        List<HeOrderAttachmentPO> attachmentPOS = BeanMapper.mapList(entityList, HeOrderAttachmentPO.class);
        return this.saveBatch(attachmentPOS);
    }
}

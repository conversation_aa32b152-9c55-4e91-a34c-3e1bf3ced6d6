package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.common.enums.production.ProductionAppointmentTypeEnum;
import com.stbella.order.domain.order.production.*;
import com.stbella.order.infrastructure.repository.po.*;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-08 15:45
 */
@Mapper(componentModel = "spring", imports = ProductionAppointmentTypeEnum.class)
public interface ProductionAppointmentConverter {

    /**
     * entity2 po
     *
     * @param entityList 实体列表
     * @return {@link List}<{@link OrderProductionAppointment}>
     */
    List<OrderProductionAppointment> entity2PoForListProductionAppointment(List<OrderProductionAppointmentEntity> entityList);

    /**
     * @param poList 订单列表
     * @return {@link List}<{@link OrderProductionAppointmentEntity}>
     */
    List<OrderProductionAppointmentEntity> po2EntityForListProductionAppointment(List<OrderProductionAppointment> poList);

    /**
     * po -> AppointmentProductionScheduleEntity
     *
     * @param poList 预约单列表
     * @return List<AppointmentProductionScheduleEntity>
     */
    List<AppointmentProductionScheduleEntity> po2EntityForListProductionScheduleEntity(List<OrderProductionAppointment> poList);

    /**
     * entity2 -> po
     *
     * @param entity 实体
     * @return {@link OrderProductionAppointment}
     */
    OrderProductionAppointment entity2PoForProductionAppointment(OrderProductionAppointmentEntity entity);

    /**
     * po -> entity
     *
     * @param po
     * @return {@link OrderProductionAppointmentEntity}
     */
    OrderProductionAppointmentEntity po2EntityForProductionAppointment(OrderProductionAppointment po);

    /**
     * po -> entity
     *
     * @param po 实体
     * @return AppointmentBoardWorkmanshipEntity
     */
    AppointmentBoardWorkmanshipEntity po2EntityForProductionAppointmentBoardWorkmanship(ProductionAppointmentBoardWorkmanship po);

    /**
     * po -> entity
     *
     * @param po 实体
     * @return OrderProductionCardExtendEntity
     */
    OrderProductionCardExtendEntity po2OrderProductionCardExtend(OrderProductionCardExtend po);

    /**
     * po -> entity
     *
     * @param po 实体
     * @return OrderProductionExtendEntity
     */
    OrderProductionExtendEntity po2OrderProductionExtend(OrderProductionExtend po);

    /**
     * po -> entity
     *
     * @param po 实体
     * @return OrderGiftExtendEntity
     */
    OrderGiftExtendEntity po2OrderGiftExtend(OrderGiftExtend po);

    /**
     * OrderProductionCardExtendEntity
     *  entity - 》 po
     *
     * @param entity
     * @return OrderProductionCardExtend
     */
    OrderProductionCardExtend cardExtendEntity2Po(OrderProductionCardExtendEntity entity);

    /**
     * poList -> entityList
     *
     * @param poList 实体list
     * @return List<OrderGiftExtendEntity>
     */
    List<OrderGiftExtendEntity> po2ListOrderGiftExtendList(List<OrderGiftExtend> poList);

    /**
     * poList -> entityList
     *
     * @param poList 实体集合
     * @return OrderGiftExtendEntity
     */
    List<OrderProductionCardExtendEntity> po2OrderProductionCardExtendList(List<OrderProductionCardExtend> poList);

    /**
     * poList -> entityList
     *
     * @param poList 实体集合
     * @return TherapistScheduleEntity
     */
    List<TherapistScheduleEntity> po2therapistSchedulesList(List<TherapistSchedule> poList);
}

package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.month.entity.HeOrderApproveRecordEntity;
import com.stbella.order.infrastructure.repository.po.HeOrderApproveRecordPO;
import org.mapstruct.Mapper;

import java.util.List;


@Mapper(componentModel = "spring")
public interface OrderApproveRecordConverter {

    List<HeOrderApproveRecordEntity> OrderApproveRecordListToEntity(List<HeOrderApproveRecordPO> refundList);
}

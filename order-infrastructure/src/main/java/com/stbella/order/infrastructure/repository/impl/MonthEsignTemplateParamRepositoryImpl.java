package com.stbella.order.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.order.domain.order.month.entity.MonthEsignTemplateParamConfigEntity;
import com.stbella.order.domain.order.month.entity.MonthEsignTemplateParamEntity;
import com.stbella.order.domain.repository.MonthEsignTemplateParamConfigRepository;
import com.stbella.order.domain.repository.MonthEsignTemplateParamRepository;
import com.stbella.order.infrastructure.repository.converter.MonthContractSignRecordConverter;
import com.stbella.order.infrastructure.repository.mapper.saas.MonthEsignTemplateParamMapper;
import com.stbella.order.infrastructure.repository.po.MonthEsignTemplateParamPO;

import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;

/**
 * <p>
 * 合同参数模版表,新版E签宝 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2021-11-03
 */
@Repository
public class MonthEsignTemplateParamRepositoryImpl extends ServiceImpl<MonthEsignTemplateParamMapper, MonthEsignTemplateParamPO> implements MonthEsignTemplateParamRepository {


    @Resource
    private MonthContractSignRecordConverter monthContractSignRecordConverter;
    @Resource
    MonthEsignTemplateParamConfigRepository monthEsignTemplateParamConfigRepository;

    @Override
    public List<MonthEsignTemplateParamEntity> list(Integer orderType) {
        /**
         * 查询参数模版表中是否有数据
         */
        LambdaQueryWrapper<MonthEsignTemplateParamPO> wrapper = new LambdaQueryWrapper<MonthEsignTemplateParamPO>()
                .eq(MonthEsignTemplateParamPO::getOrderType, orderType)
                .orderByAsc(MonthEsignTemplateParamPO::getSort);
        List<MonthEsignTemplateParamPO> esignTemplateParams = this.list(wrapper);
        //如果参数模版表没有数据,则去查参数配置表  将参数配置表的数据转换后进行返回
        esignTemplateParams = getMonthEsignTemplateParamPOS(orderType, esignTemplateParams);

        return monthContractSignRecordConverter.po2MonthEsignTemplateParamEntityList(esignTemplateParams);
    }

    @Override
    public List<MonthEsignTemplateParamEntity> list(Integer orderType, Long esignTemplateId) {
        /**
         * 查询参数模版表中是否有数据
         */
        LambdaQueryWrapper<MonthEsignTemplateParamPO> wrapper = new LambdaQueryWrapper<MonthEsignTemplateParamPO>()
                .eq(MonthEsignTemplateParamPO::getOrderType, orderType)
                .apply(Objects.nonNull(esignTemplateId), "find_in_set({0},esign_template_id)", esignTemplateId)
                .orderByAsc(MonthEsignTemplateParamPO::getSort);
        List<MonthEsignTemplateParamPO> esignTemplateParams = this.list(wrapper);
        //如果参数模版表没有数据,则去查参数配置表  将参数配置表的数据转换后进行返回
        esignTemplateParams = getMonthEsignTemplateParamPOS(orderType, esignTemplateParams);

        return monthContractSignRecordConverter.po2MonthEsignTemplateParamEntityList(esignTemplateParams);
    }

    private List<MonthEsignTemplateParamPO> getMonthEsignTemplateParamPOS(Integer orderType, List<MonthEsignTemplateParamPO> esignTemplateParams) {
        if (esignTemplateParams == null || esignTemplateParams.size() <= 0) {
            List<MonthEsignTemplateParamConfigEntity> list = monthEsignTemplateParamConfigRepository.getList(orderType);
            esignTemplateParams = new ArrayList<>(list.size());
            for (MonthEsignTemplateParamConfigEntity esignTemplateParamConfig : list) {
                MonthEsignTemplateParamPO esignTemplateParam = BeanUtil.copyProperties(esignTemplateParamConfig, MonthEsignTemplateParamPO.class);
                esignTemplateParams.add(esignTemplateParam);
            }
        }
        return esignTemplateParams;
    }
}

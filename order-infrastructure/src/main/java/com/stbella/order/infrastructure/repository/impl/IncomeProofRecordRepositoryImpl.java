package com.stbella.order.infrastructure.repository.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.order.domain.order.month.entity.HeIncomeProofRecordEntity;
import com.stbella.order.domain.repository.IncomeProofRecordRepository;
import com.stbella.order.infrastructure.repository.converter.IncomeProofRecordConverter;
import com.stbella.order.infrastructure.repository.mapper.saas.HeIncomeProofRecordMapper;
import com.stbella.order.infrastructure.repository.po.HeIncomeProofRecordPO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Repository
public class IncomeProofRecordRepositoryImpl extends ServiceImpl<HeIncomeProofRecordMapper, HeIncomeProofRecordPO> implements IncomeProofRecordRepository {

    @Resource
    private IncomeProofRecordConverter incomeProofRecordConverter;


    @Override
    public List<HeIncomeProofRecordEntity> getIncomeProofRecordByOrderIdList(List<Integer> orderIdList) {
        if (CollectionUtil.isEmpty(orderIdList)) {
            return new ArrayList<>();
        }

        List<HeIncomeProofRecordPO> heIncomeProofRecord = this.baseMapper.selectList(new LambdaQueryWrapper<HeIncomeProofRecordPO>().in(HeIncomeProofRecordPO::getOrderId, orderIdList));
        if (CollectionUtil.isEmpty(heIncomeProofRecord)) {
            return new ArrayList<>();
        }
        return incomeProofRecordConverter.incomeProofRecordPoListToEntity(heIncomeProofRecord);
    }

    @Override
    public List<HeIncomeProofRecordEntity> getIncomeProofRecordByOrderId(Integer orderId) {
        List<HeIncomeProofRecordPO> heIncomeProofRecord = this.baseMapper.selectList(new LambdaQueryWrapper<HeIncomeProofRecordPO>().eq(HeIncomeProofRecordPO::getOrderId, orderId));
        return incomeProofRecordConverter.incomeProofRecordPoListToEntity(heIncomeProofRecord);
    }

    @Override
    public List<HeIncomeProofRecordEntity> getIncomeProofRecordByOrderIdAndStatus(Integer orderId, Integer status) {
        List<HeIncomeProofRecordPO> heIncomeProofRecord = this.baseMapper.selectList(new LambdaQueryWrapper<HeIncomeProofRecordPO>().eq(HeIncomeProofRecordPO::getOrderId, orderId).eq(HeIncomeProofRecordPO::getStatus, status));
        return incomeProofRecordConverter.incomeProofRecordPoListToEntity(heIncomeProofRecord);
    }

    @Override
    public Integer saveOne(HeIncomeProofRecordEntity heIncomeProofRecordEntity) {
        HeIncomeProofRecordPO heIncomeProofRecordPO = incomeProofRecordConverter.incomeProofRecordEntity2PO(heIncomeProofRecordEntity);
        this.save(heIncomeProofRecordPO);
        return heIncomeProofRecordPO.getId();
    }

    @Override
    public Integer updateOne(HeIncomeProofRecordEntity heIncomeProofRecordEntity) {
        HeIncomeProofRecordPO heIncomeProofRecordPO = incomeProofRecordConverter.incomeProofRecordEntity2PO(heIncomeProofRecordEntity);
        this.updateById(heIncomeProofRecordPO);
        return heIncomeProofRecordPO.getId();
    }

    @Override
    public HeIncomeProofRecordEntity getOneById(Integer id) {
        return incomeProofRecordConverter.heIncomeProofRecordPO2Entity(this.getById(id));
    }


    @Override
    public List<HeIncomeProofRecordEntity> getListByIncomeSnList(List<String> collect) {
        List<HeIncomeProofRecordPO> list = this.list(new LambdaQueryWrapper<HeIncomeProofRecordPO>().in(HeIncomeProofRecordPO::getIncomeSn, collect));
        return incomeProofRecordConverter.incomeProofRecordPoListToEntity(list);
    }

    @Override
    public HeIncomeProofRecordEntity getLastOneByIncomeSn(String incomeSn) {
        LambdaQueryWrapper<HeIncomeProofRecordPO> lq = new LambdaQueryWrapper<>();
        lq.eq(HeIncomeProofRecordPO::getIncomeSn, incomeSn)
                .orderByDesc(HeIncomeProofRecordPO::getCreatedAt).last(" limit 1");
        ;
        return incomeProofRecordConverter.heIncomeProofRecordPO2Entity(this.getOne(lq));
    }

    @Override
    public HeIncomeProofRecordEntity getLastOneByIncomeId(Integer incomeId) {
        LambdaQueryWrapper<HeIncomeProofRecordPO> lq = new LambdaQueryWrapper<>();
        lq.eq(HeIncomeProofRecordPO::getIncomeId, incomeId)
                .orderByDesc(HeIncomeProofRecordPO::getCreatedAt).last(" limit 1");
        return incomeProofRecordConverter.heIncomeProofRecordPO2Entity(this.getOne(lq));
    }

    @Override
    public List<HeIncomeProofRecordEntity> getIncomeProofRecordByIncomeIdList(List<Integer> collect) {
        if (CollectionUtil.isEmpty(collect)) {
            return new ArrayList<>();
        }

        List<HeIncomeProofRecordPO> heIncomeProofRecord = this.baseMapper.selectList(new LambdaQueryWrapper<HeIncomeProofRecordPO>().in(HeIncomeProofRecordPO::getIncomeId, collect));
        if (CollectionUtil.isEmpty(heIncomeProofRecord)) {
            return new ArrayList<>();
        }
        return incomeProofRecordConverter.incomeProofRecordPoListToEntity(heIncomeProofRecord);
    }

    /**
     * 押金转换特殊需求-根据ID设置orderId为null
     *
     * @param id
     */
    @Override
    public boolean updateSetOrderIdNullById(Integer id) {
        LambdaUpdateWrapper<HeIncomeProofRecordPO> lq = new LambdaUpdateWrapper<>();
        lq.eq(HeIncomeProofRecordPO::getId, id);
        lq.set(HeIncomeProofRecordPO::getOrderId, null);
        return this.update(lq);
    }

    @Override
    public HeIncomeProofRecordEntity getLastOneByIncomeIdAndStatus(Integer incomeId, int status) {
        LambdaQueryWrapper<HeIncomeProofRecordPO> lq = new LambdaQueryWrapper<>();
        lq.eq(HeIncomeProofRecordPO::getIncomeId, incomeId)
                .eq(HeIncomeProofRecordPO::getStatus, status)
                .orderByDesc(HeIncomeProofRecordPO::getCreatedAt).last(" limit 1");
        return incomeProofRecordConverter.heIncomeProofRecordPO2Entity(this.getOne(lq));
    }
}

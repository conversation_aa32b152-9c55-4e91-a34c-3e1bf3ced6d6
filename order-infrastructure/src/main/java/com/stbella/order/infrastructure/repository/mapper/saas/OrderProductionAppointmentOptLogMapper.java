package com.stbella.order.infrastructure.repository.mapper.saas;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.stbella.order.infrastructure.repository.po.OrderProductionAppointmentOptLog;

public interface OrderProductionAppointmentOptLogMapper extends BaseMapper<OrderProductionAppointmentOptLog> {
    int deleteByPrimaryKey(Long id);

    int insert(OrderProductionAppointmentOptLog record);

    int insertSelective(OrderProductionAppointmentOptLog record);

    OrderProductionAppointmentOptLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrderProductionAppointmentOptLog record);

    int updateByPrimaryKey(OrderProductionAppointmentOptLog record);

    default OrderProductionAppointmentOptLog selectByAppointmentId(Long appointmentId) {
        return selectOne(new LambdaQueryWrapper<OrderProductionAppointmentOptLog>().eq(OrderProductionAppointmentOptLog::getAppointmentId, appointmentId).last("limit 1"));
    }

    ;
}
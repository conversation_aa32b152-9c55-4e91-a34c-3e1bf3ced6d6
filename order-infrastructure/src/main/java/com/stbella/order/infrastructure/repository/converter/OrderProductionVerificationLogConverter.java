package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.production.OrderProductionVerificationLogEntity;
import com.stbella.order.infrastructure.repository.po.OrderProductionVerificationLog;
import java.util.List;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface OrderProductionVerificationLogConverter {

    /**
     * entity -> po
     *
     * @param entity
     * @return com.stbella.order.infrastructure.repository.po.OrderProductionVerificationLog
     * @throws
     * <AUTHOR>
     * @date 2022/10/17 18:29
     * @since 1.0.0
     */
    OrderProductionVerificationLog entity2POForProductionVerificationLog(OrderProductionVerificationLogEntity entity);

    /**
     * 核销记录列表po转entity
     *
     * @return
     */
    List<OrderProductionVerificationLogEntity> poList2Entitylist(List<OrderProductionVerificationLog> poList);

}

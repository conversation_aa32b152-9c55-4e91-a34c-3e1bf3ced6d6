package com.stbella.order.infrastructure.repository.mapper.saas;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.stbella.order.infrastructure.repository.po.OrderProductionVerificationLog;
import com.stbella.order.server.order.ProductionOrderGoodsModel;
import com.stbella.order.server.order.ProductionOrderPo;
import com.stbella.order.server.order.order.req.OrderExportReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderProductionVerificationLogMapper extends BaseMapper<OrderProductionVerificationLog> {
    int deleteByPrimaryKey(Long id);

    int insert(OrderProductionVerificationLog record);

    int insertSelective(OrderProductionVerificationLog record);

    OrderProductionVerificationLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrderProductionVerificationLog record);

    int updateByPrimaryKey(OrderProductionVerificationLog record);


    /**
     * 查询产康核销记录
     * @param req
     * @return
     */
    List<OrderProductionVerificationLog> queryProductionOrderVerificationLogList(@Param("req") OrderExportReq req);

    /**
     * 查询产康核销记录 -- 没有业绩的订单产生的
     * @param req
     * @return
     */
    List<ProductionOrderPo> queryProductionOrderVerificationLogForInvalidList(@Param("req") OrderExportReq req);
}
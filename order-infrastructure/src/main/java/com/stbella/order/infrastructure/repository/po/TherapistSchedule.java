package com.stbella.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

@TableName("he_therapist_schedule")
public class TherapistSchedule {

    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 产康师id
     */
    private Long therapistId;

    /**
     * 预约日期 yyyy-MM-dd
     */
    private String serveDate;

    /**
     * 开始时间 HH:mm
     */
    private String startTime;

    /**
     * 结束时间 HH:mm
     */
    private String endTime;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 逻辑删除标志，0：未删除，1：逻辑删除
     */
    private Integer deleted;

    /**
     * @return id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 产康师id
     *
     * @return therapist_id 产康师id
     */
    public Long getTherapistId() {
        return therapistId;
    }

    /**
     * 产康师id
     *
     * @param therapistId 产康师id
     */
    public void setTherapistId(Long therapistId) {
        this.therapistId = therapistId;
    }

    /**
     * 预约日期 yyyy-MM-dd
     *
     * @return serve_date 预约日期 yyyy-MM-dd
     */
    public String getServeDate() {
        return serveDate;
    }

    /**
     * 预约日期 yyyy-MM-dd
     *
     * @param serveDate 预约日期 yyyy-MM-dd
     */
    public void setServeDate(String serveDate) {
        this.serveDate = serveDate == null ? null : serveDate.trim();
    }

    /**
     * 开始时间 HH:mm
     *
     * @return start_time 开始时间 HH:mm
     */
    public String getStartTime() {
        return startTime;
    }

    /**
     * 开始时间 HH:mm
     *
     * @param startTime 开始时间 HH:mm
     */
    public void setStartTime(String startTime) {
        this.startTime = startTime == null ? null : startTime.trim();
    }

    /**
     * 结束时间 HH:mm
     *
     * @return end_time 结束时间 HH:mm
     */
    public String getEndTime() {
        return endTime;
    }

    /**
     * 结束时间 HH:mm
     *
     * @param endTime 结束时间 HH:mm
     */
    public void setEndTime(String endTime) {
        this.endTime = endTime == null ? null : endTime.trim();
    }

    /**
     * 创建时间
     *
     * @return gmt_create 创建时间
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * 创建时间
     *
     * @param gmtCreate 创建时间
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * 修改时间
     *
     * @return gmt_modified 修改时间
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * 修改时间
     *
     * @param gmtModified 修改时间
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * 逻辑删除标志，0：未删除，1：逻辑删除
     *
     * @return deleted 逻辑删除标志，0：未删除，1：逻辑删除
     */
    public Integer getDeleted() {
        return deleted;
    }

    /**
     * 逻辑删除标志，0：未删除，1：逻辑删除
     *
     * @param deleted 逻辑删除标志，0：未删除，1：逻辑删除
     */
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }
}
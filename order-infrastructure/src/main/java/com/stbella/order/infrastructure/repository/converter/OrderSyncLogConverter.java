package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.month.entity.HeOrderSyncLogEntity;
import com.stbella.order.infrastructure.repository.po.HeOrderSyncLog;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface OrderSyncLogConverter {

    HeOrderSyncLog entity2PO(HeOrderSyncLogEntity heOrderSyncLogEntity);

    HeOrderSyncLogEntity pO2Entity(HeOrderSyncLog heOrderSyncLog);
}

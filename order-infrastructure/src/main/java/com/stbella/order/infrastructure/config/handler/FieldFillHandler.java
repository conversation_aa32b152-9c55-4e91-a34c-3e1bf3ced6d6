package com.stbella.order.infrastructure.config.handler;


import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.stbella.order.common.enums.core.IsNanEnum;
import com.stbella.order.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

@Slf4j
@Component
public class FieldFillHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        final Date curDate = new Date();
        final Long tenBitTimestamp = DateUtils.getTenBitTimestamp();
        this.fillValue(metaObject, "gmtCreate", curDate);
        this.fillValue(metaObject, "gmtModified", curDate);
        this.fillValue(metaObject, "deleted", IsNanEnum.NO.code());

        this.fillValue(metaObject, "createTime", curDate);
        this.fillValue(metaObject, "updateTime", curDate);

        this.fillValue(metaObject, "createdAt", tenBitTimestamp);
        this.fillValue(metaObject, "updatedAt", tenBitTimestamp);
        this.fillValue(metaObject, "isDelete", IsNanEnum.NO.code());
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        final Date curDate = new Date();
        this.fillValue(metaObject, "updateTime", curDate);
        this.fillValue(metaObject, "gmtModified", curDate);
        this.fillValue(metaObject, "updatedAt", DateUtils.getTenBitTimestamp());
    }

    private void fillValue(MetaObject metaObject, String fieldName, Object value) {
        try {
            this.setFieldValByName(fieldName, value, metaObject);
        } catch (Exception e) {
            log.warn(e.getMessage());
        }
    }
}

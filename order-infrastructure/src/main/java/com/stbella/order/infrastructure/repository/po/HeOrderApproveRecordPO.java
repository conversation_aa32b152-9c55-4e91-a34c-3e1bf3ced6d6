package com.stbella.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 订单钉钉审批回调记录
 * </p>
 *
 * <AUTHOR> @since 2022-10-27
 */
@Data
@Accessors(chain = true)
@TableName("he_order_approve_record")
@ApiModel(value = "HeOrderApproveRecordPO对象", description = "订单钉钉审批回调记录")
public class HeOrderApproveRecordPO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "相同订单Id可能出现多条审批记录")
    private Integer orderId;

    @ApiModelProperty(value = "钉钉审批流程实例id")
    private String ddInstanceId;

    @ApiModelProperty(value = "审批类型 1=总部和解及保密协议 2=门店和解及保密协议 3=礼赠协议 4=退单协议 5=月子标准订单合同修改申请 6= 折扣审批 7=产康退单审批")
    private Integer type;

    @ApiModelProperty(value = "审批状态：1同意,2拒绝,3撤销")
    private Integer status;

    @ApiModelProperty(value = "是否删除：1是，0否")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Long createdAt;

    @ApiModelProperty(value = "最近更新时间")
    private Long updatedAt;

    @ApiModelProperty(value = "审批实例标题")
    private String title;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "结束时间")
    @TableField("finish_time")
    private Date finishTime;

    @ApiModelProperty(value = "发起人的userId")
    @TableField("originator_user_id")
    private String originatorUserId;

    @ApiModelProperty(value = "发起人的部门，-1表示根部门")
    @TableField("originator_dept_id")
    private String originatorDeptId;

    @ApiModelProperty(value = "发起人的部门名称")
    @TableField("originator_dept_name")
    private String originatorDeptName;


}

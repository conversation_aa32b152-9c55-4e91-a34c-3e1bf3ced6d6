package com.stbella.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 订单退款商品记录表
 * </p>
 *
 * <AUTHOR> @since 2024-07-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HeOrderRefundGoods对象", description = "订单退款商品记录表")
public class HeOrderRefundGoods implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @ApiModelProperty(value = "退款sn")
    private String refundOrderSn;

    @ApiModelProperty(value = "商品sn")
    private String orderGoodsSn;

    @ApiModelProperty(value = "商品上级")
    private String parentCombineSn;

    @ApiModelProperty(value = "skuId")
    private Integer skuId;

    @ApiModelProperty(value = "sku名")
    private String skuName;

    @ApiModelProperty(value = "商品id")
    private Integer goodsId;

    @ApiModelProperty(value = "商品名")
    private String goodsName;

    @ApiModelProperty(value = "商品图")
    private String goodsImage;

    @ApiModelProperty(value = "退款数量")
    private Integer refundNum;

    @ApiModelProperty(value = "退款金额")
    private Integer refundAmount;

    @ApiModelProperty(value = "状态：0-退款中；1-退款成功；2-退款失败")
    private Integer status;

    @ApiModelProperty(value = "商品退款方式：1-退货退款；2-仅退款；3-退回重付；99-未知")
    private Integer refundNature;

    @ApiModelProperty(value = "退款方式：1-原路退回；2-线下汇款；3-退款至余额")
    private Integer refundType;

    @TableLogic
    private Integer deleted;

    private Date gmtCreate;

    private Date gmtModified;

    @ApiModelProperty(value = "商品类型")
    private Integer goodsType;

    @ApiModelProperty(value = "商品原单价")
    private Integer goodsPriceOrgin;

    @ApiModelProperty(value = "商品实付单价")
    private Integer goodsPricePay;

    @ApiModelProperty(value = "是否礼赠: 0不是，1是")
    private Integer gift;

    @ApiModelProperty(value = "2-普通商品；3-组合")
    private Integer type;

    @ApiModelProperty(value = "商品表id")
    private Integer orderGoodsId;

    @ApiModelProperty(value = "商品名称，不带规格")
    private String parentGoodsName;

    @ApiModelProperty(value = "商品单位")
    private String goodsUnit;

    @ApiModelProperty(value = "退款方式")
    private Integer payType;
}

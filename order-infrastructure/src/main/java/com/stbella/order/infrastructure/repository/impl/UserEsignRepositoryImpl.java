package com.stbella.order.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.order.domain.order.month.entity.MonthHeUserEsignEntity;
import com.stbella.order.domain.repository.UserEsignRepository;
import com.stbella.order.infrastructure.repository.converter.MonthContractSignRecordConverter;
import com.stbella.order.infrastructure.repository.mapper.saas.MonthUserEsignMapper;
import com.stbella.order.infrastructure.repository.po.MonthHeUserEsignPO;

import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

@Repository
public class UserEsignRepositoryImpl extends ServiceImpl<MonthUserEsignMapper, MonthHeUserEsignPO> implements UserEsignRepository {

    @Resource
    private MonthContractSignRecordConverter monthContractSignRecordConverter;

    @Override
    public MonthHeUserEsignEntity queryByConditionOne(String name, String phone, Integer idCardType, String idCardNo) {
        LambdaQueryWrapper<MonthHeUserEsignPO> queryWrapper = new LambdaQueryWrapper<MonthHeUserEsignPO>()
                .eq(MonthHeUserEsignPO::getName, name)
                .eq(MonthHeUserEsignPO::getPhone, phone)
                .eq(MonthHeUserEsignPO::getIdCardType, idCardType)
                .eq(MonthHeUserEsignPO::getIdCardNo, idCardNo)
                .last("limit 1");

        MonthHeUserEsignPO userEsign = this.baseMapper.selectOne(queryWrapper);
        return monthContractSignRecordConverter.po2MonthHeUserEsignEntity(userEsign);
    }

    @Override
    public MonthHeUserEsignEntity queryByConditionOne(String name, String idCardNo) {
        LambdaQueryWrapper<MonthHeUserEsignPO> queryWrapper = new LambdaQueryWrapper<MonthHeUserEsignPO>()
                .eq(MonthHeUserEsignPO::getName, name)
                .eq(MonthHeUserEsignPO::getIdCardNo, idCardNo)
                .last("limit 1");

        MonthHeUserEsignPO userEsign = this.baseMapper.selectOne(queryWrapper);
        return monthContractSignRecordConverter.po2MonthHeUserEsignEntity(userEsign);
    }

}

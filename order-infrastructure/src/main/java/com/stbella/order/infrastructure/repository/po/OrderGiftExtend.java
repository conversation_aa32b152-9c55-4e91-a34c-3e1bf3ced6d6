package com.stbella.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("he_order_gift_extend")
public class OrderGiftExtend implements Serializable {
    private static final long serialVersionUID = -437273072177754311L;
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date gmtModified;

    /**
     * 逻辑删除
     */
    @ApiModelProperty("逻辑删除标识 0-未删除 1-已删除")
    @TableLogic(value = "0", delval = "1")
    @JsonIgnore
    private Integer deleted;

    /**
     * 关联分组id
     */
    private Integer categoryId;

    /**
     * 关联分组名称
     */
    private String categoryName;

    /**
     * 礼赠名称
     */
    private String goodsName;

    /**
     * 商城商品sku名称
     */
    private String skuName;

    /**
     * 赠送门店id
     */
    private Integer storeId;

    /**
     * 关联订单id
     */
    private Integer orderId;

    /**
     * 用户basic id
     */
    private Integer basicId;

    /**
     * 关联商品id
     */
    private Integer goodsId;

    /**
     * 商品性质（商品类型）
     */
    private String natureTypeTitle;

    /**
     * 后台分类名称（商品类目）
     */
    private String categoryBackTitle;

    /**
     * 商品类型名称
     */
    private String goodsTypeTitle;

    /**
     * 关联sku id
     */
    private Integer skuId;

    /**
     * 1=产康金 2=房型升级 3=续住 4=节日费用 5=家属房 6=产康服务 7=实物商品
     */
    private Integer type;

    /**
     * 价格
     */
    private Integer price;

    /**
     * 成本
     */
    private Integer cost;

    /**
     * 具体信息
     */
    private String content;

    /**
     * 商品数量 特殊商品默认为1 实物商品情况而定
     */
    private Integer goodsNum;

    /**
     * 规格数量
     */
    private Integer skuNum;

    /**
     * 状态: 0-冻结 1-正常 2-已失效
     */
    private Integer status;

    /**
     * 有效期开始时间
     */
    private Long validStartTime;

    /**
     * 有效期结束时间
     */
    private Long validEndTime;

    /**
     * 核销状态 0=未核销  1=已核销 2:已预约
     */
    private Integer verificationStatus;

    /**
     * 核销选项 “,”分隔
     */
    private String optionsVerification;

    /**
     * 赠送来源 1=小助手 2=后台  默认小助手 3=购买补录
     */
    private Integer source;

    /**
     * 产康服务有效期数值
     */
    private Integer validityValue;

    /**
     * 产康服务有效期单位枚举 1)日 2）月 3)年
     */
    private Integer validityType;

    /**
     * 服务时长 单位为分
     */
    private Integer serviceTime;

    /**
     * 服务类型 0）自营 1）三方
     */
    private Integer serveType;

    /**
     * 服务标签
     */
    private Integer serviceTag;

    /**
     * 赠送批次编号
     */
    private String batchNo;

    /**
     * 实际支付金额
     */
    private BigDecimal realPaid;

    /**
     *数据来源：0表示正常，1表示后台核销补录
     */
    private Integer dataSource;

    /**
     * 原始id
     */
    private Integer originalId;

    /**
     * 序号（关联用此字段）
     */
    private String orderGoodsSn;
}

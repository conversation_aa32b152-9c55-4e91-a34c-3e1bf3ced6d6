package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.month.entity.HeTaskEntity;
import com.stbella.order.infrastructure.repository.po.HeTaskPO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface OrderTaskConverter {
    HeTaskEntity heTaskPO2HeTaskEntity(HeTaskPO heTaskPO);

    HeTaskPO heTaskEntity2PO(HeTaskEntity heTaskEntity);

    List<HeTaskEntity> heTaskPOList2HeTaskEntityList(List<HeTaskPO> list);
}

package com.stbella.order.infrastructure.repository.po;

import lombok.Data;

import java.io.Serializable;

@Data
public class GoodsWithBLOBs extends Goods implements Serializable {
    /**
     * 商品轮播图，英文逗号分隔
     */
    private String images;

    /**
     * 图片url格式的商品详情，逗号分隔
     */
    private String contentImages;

    /**
     * 商品详情内容
     */
    private String goodsDetail;

    /**
     * 预约信息字段
     */
    private String inforFields;
}
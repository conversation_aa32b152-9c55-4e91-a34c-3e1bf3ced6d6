package com.stbella.order.infrastructure.repository.mapper.saas;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.infrastructure.repository.po.HeIncomeProofRecordPO;
import com.stbella.order.infrastructure.repository.po.HeIncomeRecordPO;
import com.stbella.order.infrastructure.repository.po.HeOrderPO;
import com.stbella.order.infrastructure.repository.po.HeOrderRefundPO;
import com.stbella.order.server.order.month.req.CustomerMyOrderQuery;
import com.stbella.order.server.order.month.req.OrderPaySuccessReq;
import com.stbella.order.server.order.month.req.WechatMyOrderQuery;
import com.stbella.order.server.order.month.res.CustomerMyOrderVO;
import com.stbella.order.server.order.month.res.WechatMyOrderVO;
import com.stbella.order.server.order.order.req.OrderExportReq;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2022-10-28
 */
@Repository
public interface HeOrderMapper extends BaseMapper<HeOrderPO> {

    /**
     * 小程序-订单列表
     *
     * @param page
     * @param query
     * @return
     */
    Page<WechatMyOrderVO> getMyOrderList(IPage page, @Param("query") WechatMyOrderQuery query);

    /**
     * C端小程序-客户订单列表
     *
     * @param page
     * @param query
     * @return
     */
    Page<CustomerMyOrderVO> getCustomerOrderList(IPage page, @Param("query") CustomerMyOrderQuery query);

    /**
     * 导出当月订单明细
     * @param req
     * @return
     */
    List<HeOrderEntity> queryCurrMonthOrderIterm(@Param("req") OrderExportReq req);

    /**
     * 获取订单信息
     * @param req
     * @param payTimeStart
     * @param payTimeEnd
     * @return
     */
    Page<HeOrderPO> queryList(IPage page, @Param("query") OrderPaySuccessReq req, @Param("payTimeStart") Long payTimeStart, @Param("payTimeEnd") Long payTimeEnd);

    /**
     * 根据资产类型获取订单SN
     *
     * @param clientIdList
     * @param list
     * @return
     */
    List<Integer> queryByClientIdsAndAssetType(@Param("clientIdList") List<Integer> clientIdList, @Param("assetType") List<Integer> assetType, @Param("storeId") Integer storeId, @Param("orderSn") String orderSn);

    List<HeOrderPO> listOldOrders2Trans();

    /**
     * 获取非线下支付信息
     * @param startTime
     * @param endTime
     * @return
     */
    List<HeIncomeRecordPO> getOrderNotProofPayRecordList(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 获取线下支付信息
     * @param startTime
     * @param endTime
     * @return
     */
    List<HeIncomeProofRecordPO> getOrderProofPayRecordList(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 获取订单退款信息
     * @param startTime
     * @param endTime
     * @return
     */
    List<HeOrderRefundPO> getOrderRefundRecordList(@Param("startTime") Long startTime, @Param("endTime") Long endTime);
}

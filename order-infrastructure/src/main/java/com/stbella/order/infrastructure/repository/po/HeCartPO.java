package com.stbella.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.stbella.core.enums.BusinessEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 购物车表
 */
@Data
@Accessors(chain = true)
@TableName("he_order_cart")
@ApiModel(value = "HeCartPO对象", description = "购物车表")
public class HeCartPO implements Serializable {

    @ApiModelProperty(value = "购物车ID（主键自增)")
    @TableId(value = "cart_id", type = IdType.AUTO)
    private Integer cartId;

    @ApiModelProperty(value = "序号")
    private String cartSn;

    /**
     * 业务线
     * @see BusinessEnum
     */
    @ApiModelProperty(value = "BU 0:母婴 1:广禾 2:予家")
    private Integer bu = BusinessEnum.CARE_CENTER.getCode();

    /**
     * 订单类型 (可能有些场景，外部没有这个值，需要内部赋值)
     * @see OmniOrderTypeEnum
     */
    @ApiModelProperty(value = "订单类型")
    private Integer orderType = OmniOrderTypeEnum.MONTH_ORDER.code();

    @ApiModelProperty(value = "门店ID")
    private Integer storeId;

    @ApiModelProperty(value = "客户全局ID")
    private Integer basicUid;

    @ApiModelProperty(value = "扩展信息：标准json，主要包含预产期、预入驻日期、胎数等")
    private String extendContent;

    @ApiModelProperty(value = "特殊订单：false-否，true-是")
    private Boolean special;

    @ApiModelProperty(value = "特殊订单标签ID")
    private Integer specialTag;

    @ApiModelProperty(value = "特殊订单标签名称")
    private String specialTagName;

    @ApiModelProperty(value = "特殊凭证URL集合，多个英文逗号分隔")
    private String specialVoucherUrls;

    @ApiModelProperty(value = "特殊订单说明")
    private String specialDesc;

    @ApiModelProperty(value = "是否已提交订单：false-否，true-是")
    private Boolean submitted;

    @ApiModelProperty(value = "订单ID")
    private Integer orderId;


    /**
     * 商品原价:分
     */
    private Integer totalAmount;

    /**
     * 场景：0 下单； 1 报价单
     * @see com.stbella.order.common.enums.core.CartSceneEnum
     */
    @ApiModelProperty(value = "场景：0 下单； 1 报价单,2,随心配")
    private Integer scene;

    /**
     * 订单原价:分（含有加收项目）
     */
    private Integer orderAmount;

    /**
     * 签单金额
     */
    private Integer payAmount;


    @ApiModelProperty(value = "创建时间")
    private Date addTime;

    @ApiModelProperty(value = "最近更新时间")
    private Date updateTime;

    @TableLogic(value = "false", delval = "true")
    @ApiModelProperty(value = "是否删除：false-否，true-是")
    private boolean deleted;

    @ApiModelProperty(value = "客户系统ID")
    private Integer clientUid;

    @ApiModelProperty(value = "备注信息")
    private String remark;

    @ApiModelProperty(value = "销售员id（ecp库user表主键id）")
    private Integer staffId;

    @ApiModelProperty(value = "销售员电话")
    private String staffPhone;

    @ApiModelProperty(value = "原订单ID（用于订单升级）")
    private Integer originalOrderId;

    @ApiModelProperty(value = "原商品总价（用于订单升级）")
    private BigDecimal originalGoodsTotalPrice;

    private Integer signBillType;

}

package com.stbella.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@TableName("setting_store_target")
@Data
@Accessors(chain = true)
@ApiModel(value = "SettingStoreTargetPo对象", description = "门店相关配置表")
public class SettingStoreTargetPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 门店id
     */
    @ApiModelProperty("门店id")
    private Integer storeId;

    /**
     * 年
     */
    @ApiModelProperty("年")
    private Integer year;

    /**
     * 月 0为年度记录
     */
    @ApiModelProperty("月 0为年度记录")
    private Integer month;

    /**
     * 年或年-月的格式
     */
    @ApiModelProperty("年或年-月的格式")
    private String period;

    /**
     * 目标金额
     */
    @ApiModelProperty("目标金额")
    private Integer amount;

    /**
     * 最后操作人
     */
    @ApiModelProperty("最后操作人")
    private Integer lastAdminId;

    /**
     * update_time
     */
    @ApiModelProperty("update_time")
    private Date updateTime;

    /**
     * record_time
     */
    @ApiModelProperty("record_time")
    private Date recordTime;

    /**
     * 类型: 1:月子总业绩 2:月子套餐 3:产康业务 4:护士外派 5:到家业务
     */
    @ApiModelProperty("类型: 1:月子总业绩 2:月子套餐 3:产康业务 4:护士外派 5:到家业务")
    private int type;

}
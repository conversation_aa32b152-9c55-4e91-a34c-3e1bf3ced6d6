package com.stbella.order.infrastructure.repository.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.mybatis.utils.AESUtil;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.domain.repository.OrderUserSnapshotRepository;
import com.stbella.order.infrastructure.repository.converter.OrderConverter;
import com.stbella.order.infrastructure.repository.mapper.saas.HeOrderUserSnapshotMapper;
import com.stbella.order.infrastructure.repository.po.HeOrderUserSnapshotPO;
import com.stbella.order.server.order.month.req.OrderMonthClientReq;
import java.util.Date;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Repository
public class OrderUserSnapshotRepositoryImpl extends ServiceImpl<HeOrderUserSnapshotMapper, HeOrderUserSnapshotPO> implements OrderUserSnapshotRepository {

    @Resource
    private OrderConverter orderConverter;
    @Resource
    private HeOrderUserSnapshotMapper heOrderUserSnapshotMapper;

    /***
     * 保存订单用户快照
     * @param entity
     */
    @Override
    public Integer insertOne(HeOrderUserSnapshotEntity entity) {
        HeOrderUserSnapshotPO heOrderUserSnapshotPO = orderConverter.heOrderUserSnapshotEntity2PO(entity);
        //如果传入的时间戳为毫秒需要转为秒
        if (ObjectUtil.isNotEmpty(heOrderUserSnapshotPO.getWantIn()) && heOrderUserSnapshotPO.getWantIn().toString().length() == 13) {
            heOrderUserSnapshotPO.setWantIn(heOrderUserSnapshotPO.getWantIn() / 1000);
        }
        this.save(heOrderUserSnapshotPO);
        return heOrderUserSnapshotPO.getId();
    }

    @Override
    public Integer insertOrUpdateOne(HeOrderUserSnapshotEntity entity) {
        HeOrderUserSnapshotPO heOrderUserSnapshotPO = orderConverter.heOrderUserSnapshotEntity2PO(entity);
        //如果传入的时间戳为毫秒需要转为秒
        if (ObjectUtil.isNotEmpty(heOrderUserSnapshotPO.getWantIn()) && heOrderUserSnapshotPO.getWantIn().toString().length() == 13) {
            heOrderUserSnapshotPO.setWantIn(heOrderUserSnapshotPO.getWantIn() / 1000);
        }
        this.saveOrUpdate(heOrderUserSnapshotPO);
        return heOrderUserSnapshotPO.getId();
    }

    /***
     * 修改订单用户快照
     * @param entity
     */
    @Override
    public Integer updateOne(HeOrderUserSnapshotEntity entity) {
        HeOrderUserSnapshotPO heOrderUserSnapshotPO = orderConverter.heOrderUserSnapshotEntity2PO(entity);
        //如果传入的时间戳为毫秒需要转为秒
        if (ObjectUtil.isNotEmpty(heOrderUserSnapshotPO.getWantIn()) && heOrderUserSnapshotPO.getWantIn().toString().length() == 13) {
            heOrderUserSnapshotPO.setWantIn(heOrderUserSnapshotPO.getWantIn() / 1000);
        }
        LambdaUpdateWrapper<HeOrderUserSnapshotPO> lq = new LambdaUpdateWrapper();
        lq.eq(HeOrderUserSnapshotPO::getClientUid, heOrderUserSnapshotPO.getClientUid());
        lq.eq(HeOrderUserSnapshotPO::getOrderId, heOrderUserSnapshotPO.getOrderId());
        this.update(heOrderUserSnapshotPO, lq);
        return heOrderUserSnapshotPO.getId();
    }

    /***
     * 请求转换 req -> entity
     * @param orderMonthClientReq
     */
    @Override
    public HeOrderUserSnapshotEntity orderMonthClientReq2Entity(OrderMonthClientReq orderMonthClientReq, Integer orderId) {
        HeOrderUserSnapshotEntity heOrderUserSnapshotEntity = orderConverter.orderMonthClientReq2Entity(orderMonthClientReq);
        heOrderUserSnapshotEntity.setOrderId(orderId);
        LambdaQueryWrapper<HeOrderUserSnapshotPO> lq = new LambdaQueryWrapper();
        lq.eq(HeOrderUserSnapshotPO::getOrderId, orderId)
                .last("limit 1");
        HeOrderUserSnapshotPO one = this.getOne(lq);
        if (ObjectUtil.isNotEmpty(one)) {
            heOrderUserSnapshotEntity.setId(one.getId());
        }
        return heOrderUserSnapshotEntity;
    }

    @Override
    public HeOrderUserSnapshotEntity queryByOrderId(Integer orderId) {
        HeOrderUserSnapshotPO orderPO = this.getBaseMapper().selectOne(
                new LambdaQueryWrapper<HeOrderUserSnapshotPO>()
                        .eq(HeOrderUserSnapshotPO::getOrderId, orderId)
        );

        return orderConverter.po2OrderUserSnapshotEntity(orderPO);
    }

    @Override
    public List<HeOrderUserSnapshotEntity> queryByOrderIdList(List<Integer> orderIdList) {
        if (CollectionUtil.isEmpty(orderIdList)) {
            return new ArrayList<>();
        }
        List<HeOrderUserSnapshotPO> heOrderUserSnapshotPOS = this.getBaseMapper().selectList(
                new LambdaQueryWrapper<HeOrderUserSnapshotPO>()
                        .in(HeOrderUserSnapshotPO::getOrderId, orderIdList)
        );
        return orderConverter.heOrderUserSnapshotPOS2HeOrderUserSnapshotEntity(heOrderUserSnapshotPOS);
    }

    @Override
    public List<HeOrderUserSnapshotEntity> queryByUserIdOrNameOrSource(String name, String phone, List<Integer> source) {
        if (StringUtils.isEmpty(name) && StringUtils.isEmpty(phone) && CollectionUtil.isEmpty(source)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HeOrderUserSnapshotPO> qr = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(name)) {
            qr.like(HeOrderUserSnapshotPO::getName, name);
        }
        if (StringUtils.isNotEmpty(phone)) {
            // 这里只能手动加密 这样实现很多不方便。目前官方还没有处理
            qr.eq(HeOrderUserSnapshotPO::getPhone, AESUtil.encrypt(phone));
        }
        if (CollectionUtil.isNotEmpty(source)) {
            qr.in(HeOrderUserSnapshotPO::getFromType, source);
        }
        List<HeOrderUserSnapshotPO> list = this.list(qr);
        if (CollectionUtil.isNotEmpty(list)) {
            return orderConverter.heOrderUserSnapshotPOS2HeOrderUserSnapshotEntity(list);
        }
        return new ArrayList();
    }

    /**
     * 测试 手机号查询
     *
     * @param phone
     * @return
     */
    @Override
    public List<HeOrderUserSnapshotEntity> queryByPhoneForTest(String phone) {

        List<HeOrderUserSnapshotPO> heOrderUserSnapshotPOS = heOrderUserSnapshotMapper.queryByPhoneForTest(phone);
        return orderConverter.heOrderUserSnapshotPOS2HeOrderUserSnapshotEntity(heOrderUserSnapshotPOS);

    }

    /**
     * 获取指定日期之后的预产期订单客户列表
     *
     * @param date
     * @return
     */
    @Override
    public List<HeOrderUserSnapshotEntity> queryPredictBornDateListByDate(Date date) {
        if (date == null) {
            date = new Date();
        }

        DateTime dateTime = DateUtil.beginOfDay(date);
        List<HeOrderUserSnapshotPO> heOrderUserSnapshotPOS = list(new LambdaQueryWrapper<HeOrderUserSnapshotPO>()
            .ge(HeOrderUserSnapshotPO::getPredictBornDate, dateTime));
        return orderConverter.heOrderUserSnapshotPOS2HeOrderUserSnapshotEntity(heOrderUserSnapshotPOS);
    }
}

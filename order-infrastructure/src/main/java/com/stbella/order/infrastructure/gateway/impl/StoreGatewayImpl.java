package com.stbella.order.infrastructure.gateway.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.stbella.core.enums.BrandTypeEnum;
import com.stbella.core.enums.BusinessEnum;
import com.stbella.core.result.Result;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.repository.StoreRepository;
import com.stbella.order.domain.repository.condition.StoreQueryCondition;
import com.stbella.order.infrastructure.repository.converter.CfgStoreConverter;
import com.stbella.order.server.order.StoreFinanceConfigFact;
import com.stbella.order.server.order.month.res.StoreFinanceVO;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import com.stbella.store.core.StoreService;
import com.stbella.store.core.service.finance.StoreFinanceQueryService;
import com.stbella.store.core.service.store.StoreQueryService;
import com.stbella.store.core.vo.req.store.StoreQueryReq;
import com.stbella.store.core.vo.res.finance.StoreFinanceDetailVO;
import com.stbella.store.core.vo.res.store.StoreBaseVO;
import com.stbella.store.core.vo.res.store.StoreVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import javax.annotation.Resource;
import java.math.BigDecimal;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 门店中心迁移到新服务, 为了不调整app应用，这里直接调整实现。
 */
@Service
@Slf4j
public class StoreGatewayImpl implements StoreRepository {

    @DubboReference(timeout = 5000)
    private StoreService storeService;
    @DubboReference(timeout = 5000)
    private StoreQueryService storeQueryService;

    @DubboReference(timeout = 5000)
    private StoreFinanceQueryService storeFinanceQueryService;
    @Resource
    private RuleLinkClient ruleLinkClient;

    @Resource
    CfgStoreConverter cfgStoreConverter;

    @Override
    public CfgStoreEntity queryCfgStoreById(Integer storeId) {
        Result<StoreVO> result = storeService.getStoreById(storeId.longValue());
        if (Objects.isNull(result.getData())){
            log.error("获取门店失败 storeId", storeId);
            return new CfgStoreEntity();
        }
        log.info("门店信息查询结果result:{}", JSONUtil.toJsonStr(result.getData()));
        CfgStoreEntity cfgStoreEntity = cfgStoreConverter.vo2CfgStoreEntity(result.getData());
        if (Objects.isNull(cfgStoreEntity.getBu())){
            cfgStoreEntity.setBu(BusinessEnum.CARE_FOR_HOME.getCode());
        }

        ExecuteRuleV2Req req = new ExecuteRuleV2Req(BizConstant.RuleLinkScene.Store_Finance_Config);
        StoreFinanceConfigFact fact = new StoreFinanceConfigFact();
        fact.setBu(cfgStoreEntity.getBu());
        fact.setStoreId(cfgStoreEntity.getStoreId());
        fact.setStoreType(cfgStoreEntity.getType());
        fact.setChildStoreType(cfgStoreEntity.getChildType());

        req.setFactObj(fact);

        HitRuleVo vo = ruleLinkClient.hitOneRuleWithException(req);
        StoreFinanceVO storeFinanceVO =  JSONUtil.toBean(vo.getSimpleRuleValue(), StoreFinanceVO.class);
        //转成分
        cfgStoreEntity.setDeposit(storeFinanceVO.getDeposit().multiply(new BigDecimal(100)).intValue());

        cfgStoreEntity.setHolidayCost(storeFinanceVO.getHolidayServiceCost().multiply(new BigDecimal(100)).intValue());
        cfgStoreEntity.setHolidayPrice(storeFinanceVO.getHolidayServiceCharge().multiply(new BigDecimal(100)).intValue());
        cfgStoreEntity.setMultipleBirthsCost(storeFinanceVO.getMultipleBirths().multiply(new BigDecimal(100)).intValue());
        cfgStoreEntity.setMultipleBirthsPrice(storeFinanceVO.getMultipleBirthsOriginal().multiply(new BigDecimal(100)).intValue());

        return cfgStoreEntity;
    }

    @Override
    public List<CfgStoreEntity> queryCfgStoreByIdList(List<Integer> storeIdList) {
        if (CollectionUtils.isEmpty(storeIdList)) {
            return new ArrayList<>();
        }
        // storeIdList 转long
        List<Long> IdList = storeIdList.stream().map(Long::valueOf).collect(Collectors.toList());
        List<StoreBaseVO> storeBaseByIdList = storeQueryService.getStoreBaseByIdList(IdList);
        List<CfgStoreEntity> cfgStoreEntities = cfgStoreConverter.baseVo2CfgStoreEntityList(storeBaseByIdList);
        return cfgStoreEntities;
    }

    @Override
    public List<CfgStoreEntity> queryCfgStoreByType(List<Integer> type) {
        if (CollectionUtils.isEmpty(type)) {
            return new ArrayList<>();
        }

        List<StoreBaseVO> storeBaseVOS = new ArrayList<>();

        List<Integer> types = new ArrayList<>();
        if (type.contains(1)) {
            types.add(BrandTypeEnum.SAINT_BELLA.getCode());
        }
        if (type.contains(2)) {
            types.add(BrandTypeEnum.BABY_BELLA.getCode());
        }
        if (type.contains(BrandTypeEnum.ISLA.getCode())) {
            types.add(BrandTypeEnum.ISLA.getCode());
        }
        StoreQueryReq req = new StoreQueryReq();
        req.setTypes(types);
        //根据类型查询
        List<StoreBaseVO> storeList = storeQueryService.getStoreBaseByCondition(req);
        storeBaseVOS.addAll(storeList);

        // Bella Villa 不知道为什么要这样的操作
        if (type.contains(3)) {
            StoreQueryReq specialReq = new StoreQueryReq();
            specialReq.setTypes(Lists.newArrayList(BrandTypeEnum.SAINT_BELLA.getCode()));
            specialReq.setChildTypes(Lists.newArrayList(1));
            List<StoreBaseVO> specialList = storeQueryService.getStoreBaseByCondition(specialReq);
            storeBaseVOS.addAll(specialList);
        }
        List<CfgStoreEntity> cfgStoreEntities = cfgStoreConverter.baseVo2CfgStoreEntityList(storeBaseVOS);
        if (CollectionUtil.isNotEmpty(cfgStoreEntities)) {
            cfgStoreEntities.stream().forEach(c -> c.setDeposit(0));
        }

        return cfgStoreEntities;

    }

    /**
     *全部 门店
     * @return
     */
    @Override
    public List<CfgStoreEntity> queryAllStore() {

        StoreQueryReq req = new StoreQueryReq();
        req.setActive(0);
        List<StoreBaseVO> allStoreList = storeQueryService.getStoreBaseByCondition(req);


        req = new StoreQueryReq();
        req.setActive(1);
        List<StoreBaseVO> activeList = storeQueryService.getStoreBaseByCondition(req);

        allStoreList.addAll(activeList);

        List<CfgStoreEntity> cfgStoreEntities = cfgStoreConverter.baseVo2CfgStoreEntityList(allStoreList);

        if (CollectionUtil.isNotEmpty(cfgStoreEntities)) {
            cfgStoreEntities.stream().forEach(c -> c.setDeposit(0));
        }
        return cfgStoreEntities;
    }

    /**
     * 根据条件查询名称
     *
     * @param req
     * @return
     */
    @Override
    public List<CfgStoreEntity> queryStoreBaseByCondition(StoreQueryCondition condition) {

        StoreQueryReq req = BeanMapper.map(condition, StoreQueryReq.class);
        List<StoreBaseVO> allStoreList = storeQueryService.getStoreBaseByCondition(req);
        List<CfgStoreEntity> cfgStoreEntities = cfgStoreConverter.baseVo2CfgStoreEntityList(allStoreList);
        if (CollectionUtil.isNotEmpty(cfgStoreEntities)) {
            cfgStoreEntities.stream().forEach(c -> c.setDeposit(0));
        }
        return cfgStoreEntities;
    }

    /**
     * 查询门店财务配置信息
     *
     * @param storeId
     * @return
     */
    @Override
    public StoreFinanceDetailVO queryStoreFinance(Integer storeId) {
        StoreFinanceDetailVO detail = storeFinanceQueryService.detail(storeId.longValue());
        return detail;
    }
}

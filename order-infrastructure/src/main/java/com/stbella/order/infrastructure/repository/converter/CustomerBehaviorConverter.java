package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.month.entity.HeCustomerBehaviorRecordEntity;
import com.stbella.order.infrastructure.repository.po.HeCustomerBehaviorRecordPO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface CustomerBehaviorConverter {

    /**
     * entity -> po
     *
     * @param entity 实体
     * @return HeCustomerBehaviorRecordPO
     */
    HeCustomerBehaviorRecordPO customerBehaviorRecordEntity2PO(HeCustomerBehaviorRecordEntity entity);

}

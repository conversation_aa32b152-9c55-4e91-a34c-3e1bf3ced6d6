package com.stbella.order.infrastructure.repository.condition;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-08 15:19
 */
@Data
public class ProductionAppointmentQueryCondition extends BaseCondition {

    @ApiModelProperty("门店id")
    private Integer storeId;

    @ApiModelProperty("服务开始时间")
    @NotNull(message = "服务开始时间不能为空")
    private Date serveStart;

    @ApiModelProperty("服务结束时间")
    @NotNull(message = "服务结束时间不能为空")
    private Date serveEnd;

    @ApiModelProperty("产康师id")
    @NotNull(message = "产康师不能为空")
    private Long therapistId;

    @ApiModelProperty("预约单类型：1=标准;2=补单")
    private Integer appointmentType;

    /**
     * sku id
     */
    private List<Integer> skuIds;

}

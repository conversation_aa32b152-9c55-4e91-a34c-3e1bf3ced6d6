package com.stbella.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 退款申请及审批日志表
 * </p>
 *
 * <AUTHOR> @since 2022-10-27
 */
@Data
@Accessors(chain = true)
@TableName("he_order_refund_review")
@ApiModel(value = "HeOrderRefundReviewPO对象", description = "退款申请及审批日志表")
public class HeOrderRefundReviewPO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "类型：0 申请， 1审批")
    private Integer type;

    @ApiModelProperty(value = "状态：0拒绝，1同意")
    private Integer status;

    @ApiModelProperty(value = "退款记录申请表ID")
    private Integer orderRefundId;

    @ApiModelProperty(value = "订单ID")
    private Integer orderId;

    @ApiModelProperty(value = "退款流水号")
    private String refundOrderSn;

    @ApiModelProperty(value = "sop项目id")
    private Long projectId;

    @ApiModelProperty(value = "操作此项任务的task_id")
    private Long taskId;

    @ApiModelProperty(value = "执行人")
    private Long opUid;

    @ApiModelProperty(value = "操作内容")
    private String opContent;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "添加时间")
    private Long createdAt;

    @ApiModelProperty(value = "更新时间")
    private Long updatedAt;


}

package com.stbella.order.infrastructure.repository.converter;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.infrastructure.repository.po.HeIncomeRecordPO;
import com.stbella.order.server.contract.dto.HeIncomeRecordDTO;
import com.stbella.order.server.order.month.res.DepositOrderRecordVO;
import org.mapstruct.Mapper;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Mapper(componentModel = "spring")
public interface IncomeRecordConverter {

    List<HeIncomeRecordEntity> incomeRecordPoListToEntity(List<HeIncomeRecordPO> poList);

    HeIncomeRecordPO entity2PO(HeIncomeRecordEntity entity);

    List<HeIncomeRecordPO> entity2POList(List<HeIncomeRecordEntity> entityList);

    HeIncomeRecordEntity po2Entity(HeIncomeRecordPO po);

    Page<HeIncomeRecordEntity> pagePo2Entity(Page<HeIncomeRecordPO> poPage);

    Page<HeIncomeRecordEntity> incomeRecordPoPageToEntityPage(Page<HeIncomeRecordPO> page);

    List<HeIncomeRecordDTO> entityList2DTO(List<HeIncomeRecordEntity> entityList);


    default List<DepositOrderRecordVO> entityList2DepositOrderRecordVO(List<HeIncomeRecordEntity> entityList) {
        if (CollectionUtil.isEmpty(entityList)) {
            return new ArrayList<>();
        }
        return entityList.stream().map(entity -> {
            DepositOrderRecordVO vo = new DepositOrderRecordVO();
            vo.setOrderNo(entity.getOrderSn());
            vo.setAmount(AmountChangeUtil.changeF2Y(entity.getIncome()));
            vo.setGmtCreate(DateUtils.getDateFromLong(entity.getPayTime().longValue()));
            vo.setTitle("订单收款" + "-" + OmniPayTypeEnum.getName(entity.getPayType()));
            vo.setShowTitle(vo.getTitle());
            vo.setTradeAffect("+");
            vo.setTradeType(101L);
            return vo;
        }).collect(Collectors.toList());
    }

}

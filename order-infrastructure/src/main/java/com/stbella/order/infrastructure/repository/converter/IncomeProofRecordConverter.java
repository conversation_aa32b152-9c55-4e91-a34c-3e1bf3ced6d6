package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.month.entity.HeIncomeProofRecordEntity;
import com.stbella.order.infrastructure.repository.po.HeIncomeProofRecordPO;
import org.mapstruct.Mapper;

import java.util.List;


@Mapper(componentModel = "spring")
public interface IncomeProofRecordConverter {
    HeIncomeProofRecordPO incomeProofRecordEntity2PO(HeIncomeProofRecordEntity entity);


    List<HeIncomeProofRecordEntity> incomeProofRecordPoListToEntity(List<HeIncomeProofRecordPO> refundList);

    HeIncomeProofRecordEntity heIncomeProofRecordPO2Entity(HeIncomeProofRecordPO po);
}

package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.production.OrderProductionExtendEntity;
import com.stbella.order.infrastructure.repository.po.OrderProductionExtend;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface OrderProductionExtendConverter {

    /**
     * po -> entity
     *
     * @param po
     * @return com.stbella.order.domain.order.production.OrderProductionExtendEntity
     * @throws
     * <AUTHOR>
     * @date 2022/10/13 11:09
     * @since 1.0.0
     */
    OrderProductionExtendEntity po2EntityForProductionExtend(OrderProductionExtend po);

    /**
     * po2-> entity
     *
     * @param poList polist
     * @return List<OrderProductionExtendEntity>
     */
    List<OrderProductionExtendEntity> po2EntityForListProductionExtend(List<OrderProductionExtend> poList);

    /**
     * entity -> po
     *
     * @param po
     * @return com.stbella.order.infrastructure.repository.po.OrderProductionExtend
     * @throws
     * <AUTHOR>
     * @date 2022/10/13 11:10
     * @since 1.0.0
     */
    OrderProductionExtend entity2POForProductionExtend(OrderProductionExtendEntity entity);

    List<OrderProductionExtend> entityList2POList(List<OrderProductionExtendEntity> entityList);

}

package com.stbella.order.infrastructure.repository.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.order.month.entity.HeOrderOtherEntity;
import com.stbella.order.domain.repository.OrderOtherRepository;
import com.stbella.order.infrastructure.repository.converter.OrderConverter;
import com.stbella.order.infrastructure.repository.mapper.saas.HeOrderOtherMapper;
import com.stbella.order.infrastructure.repository.po.HeOrderOtherPO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class OrderOtherRepositoryImpl extends ServiceImpl<HeOrderOtherMapper, HeOrderOtherPO> implements OrderOtherRepository {

    @Resource
    private OrderConverter orderConverter;

    /**
     * 根据订单id获取其他订单的拓展信息
     *
     * @param orderId
     * @return
     */
    @Override
    public HeOrderOtherEntity getByOrderId(Integer orderId) {
        HeOrderOtherPO heOrderOtherPO = this.baseMapper
            .selectOne(new LambdaQueryWrapper<HeOrderOtherPO>()
                .eq(HeOrderOtherPO::getOrderId, orderId)
                .eq(HeOrderOtherPO::getIsDelete, 0)
                .last("limit 1"));


        return orderConverter.orderOtherPo2Entity(heOrderOtherPO);
    }

    @Override
    public List<HeOrderOtherEntity> getByOrderIdList(List<Integer> orderIdList) {
        List<HeOrderOtherPO> list = this.list(new LambdaQueryWrapper<HeOrderOtherPO>().in(HeOrderOtherPO::getOrderId, orderIdList));
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(i -> orderConverter.orderOtherPo2Entity(i)).collect(Collectors.toList());
    }

    /**
     * 保存
     *
     * @param entity
     * @return
     */
    @Override
    public int save(HeOrderOtherEntity entity) {
        HeOrderOtherPO heOrderOtherPO = BeanMapper.map(entity, HeOrderOtherPO.class);
        heOrderOtherPO.setCreatedAt((int) (System.currentTimeMillis() / 1000));
        heOrderOtherPO.setUpdatedAt((int) (System.currentTimeMillis() / 1000));
        heOrderOtherPO.setIsDelete(0);
        this.baseMapper.insert(heOrderOtherPO);

        return 0;
    }
}

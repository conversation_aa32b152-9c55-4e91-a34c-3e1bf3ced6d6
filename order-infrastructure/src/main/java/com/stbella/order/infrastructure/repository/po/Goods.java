package com.stbella.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("he_goods")
public class Goods implements Serializable {
    /**
     * 商品id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 归属店铺类型：0圣贝拉，1小贝拉(跟ecp库的store表的type保持一致)- (废弃)
     */
    private Integer storeType;

    /**
     * 门店ID：0店铺类型下所有店铺可用的套餐，N归属店铺的店铺ID（ecp库cfg_store表的主键id)
     */
    private Integer storeId;

    /**
     * 商品类型：0月子标准套餐商品，1小月子商品，2到家雇主服务商品，3到家育婴师服务商品，4到家育婴师培训商品，5商城商品， 6贝护家其他订单， 7护士外派 8=S-BRA商品
     */
    private Integer goodsType;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 商品单价，单位：分
     */
    private Integer goodsPrice;

    /**
     * 规格名称（系统采用单规格）
     */
    private String specName;

    /**
     * 商品分类：后台
     */
    private Integer categoryBack;

    /**
     * 商品分类：前台
     */
    private Integer categoryFront;

    /**
     * 销售状态（上下价状态）：0下架，1上架，2定时上下架
     */
    private Integer saleType;

    /**
     * 定时上架销售时的上架时间
     */
    private Long upTime;

    /**
     * 定时上架销售时的下架时间
     */
    private Long downTime;

    /**
     * 护理服务时长，单位：天
     */
    private Integer serviceDays;

    /**
     * 套餐允许的折扣（8,8.1,8.2,8.3,8.4,8.5,8.6,8.7,8.8,8.9,9,9.1,9.2,9.3,9.4,9.5,9.6,9.7,9.8,9.9,10）
     */
    private String percentOptions;

    /**
     * 商品缩略图
     */
    private String image;

    /**
     * 下单按钮文字表述
     */
    private String buttonName;

    /**
     * 是否删除：1是，0否
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Long createdAt;

    /**
     * 最近更新时间
     */
    private Long updatedAt;

    /**
     * 商品类型:0=真实商品;1=虚拟商品;2=预约商品;3=套餐商品;4=产康卡项
     */
    private Byte goodsSellType;

    /**
     * 核销方式 默认1=二维码;2=小助手签到;3=到期自动核销
     */
    private Byte cancelAfterVerificationType;

    /**
     * 商品子类型: 0=默认;1=产康预约;2=预约参观;3=1v1预约;4=团课;5=查房
     */
    private Byte goodsSubSellType;

    /**
     * 备注是否必填 默认0=不必填 1=必填
     */
    private Byte remarkIsRequired;

    /**
     * 备注提示信息
     */
    private String remarkInfo;

    /**
     * 预约商品关联线下门店
     */
    private String offlineStoreId;

    /**
     * 预约商品服务人员数量
     */
    private Integer servicePersonCount;

    /**
     * 预约时间单位 2=日期 1=具体时间
     */
    private Byte unitTime;

    /**
     * 预约有效期
     */
    private String validStart;

    /**
     * 预约有效期
     */
    private String validEnd;

    /**
     * 0=不限购;1=终生限购;2=按周期限购
     */
    private Byte limitType;

    /**
     * limit_type=0:时没有规则;
     * limit_type=1:limit_num为限购数量;
     * limit_type=2:limit_num为限购数量;limit_cycle=day,week,month;
     */
    private String limitRule;

    /**
     * 商品标签,对应tags中的id
     */
    private Integer tags;

    /**
     * 可修改预约类型, 0=会员不可修改预约;1=会员可在活动开始前24小时修改预约
     */
    private Byte canModifyType;

    /**
     * 活动时长, 0=无;1=15分钟;2=30分钟;3=1小时;4=90分钟;5=2小时;6=150分钟;7=3小时
     */
    private Byte activityTimeType;

    /**
     * 是否满月宴商品类型：0-普通，1-满月宴
     */
    private Byte isFullMoonType;

    /**
     * 套餐成本（元）
     */
    private Integer packageCost;

    /**
     * 产康金
     */
    private Integer productionAmount;

    /**
     * 产康金抵扣 0）不可抵扣 1)可抵扣
     */
    private Byte produceAmountDeduction;

    /**
     * S-BRA件数,S-BRA商品字段
     */
    private Integer sbraPiece;

    /**
     * ecp房型配置
     */
    private Integer ecpRoomType;

    /**
     * 父版本编号（新增的门店套站记录来源编号）
     */
    private Integer parentId;

    /**
     * 父类名称
     */
    private String parentName;

    /**
     * 商品版本，默认2 表示新商品，老商品为空（编辑后变成2）
     */
    private Integer newFlag;

    /**
     * 服务类型 0:自营 1:三方
     */
    private Byte serviceType;

    /**
     * 服务标签id
     */
    private Long serviceTag;

    /**
     * 有效期数值 年1 月12 日365
     */
    private Byte validityValue;

    /**
     * 有效期单位 0:年，1:月，2:日
     */
    private Integer validityType;

    /**
     * 关联仪器id
     */
    private Long productionInstrId;

    /**
     * 商品单位
     */
    private Integer goodsUnit;

    /**
     * 物品清单url
     */
    private String inventoryUrl;

    private Long mainSupplierId;

    private Integer productionDiscountRuleType;

    /**
     * 时间结算点：0-按收款结算；1-按核销结算
     */
    private Integer timeClearingPoint;

    /**
     * 组合类型：0-固定搭配；1-可选搭配
     */
    private Integer combinationType;
}

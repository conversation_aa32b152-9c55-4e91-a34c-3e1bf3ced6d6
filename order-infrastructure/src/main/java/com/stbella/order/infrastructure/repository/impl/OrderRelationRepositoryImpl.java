package com.stbella.order.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.order.entity.OrderRelationEntity;
import com.stbella.order.domain.repository.OrderRelationRepository;
import com.stbella.order.infrastructure.repository.mapper.saas.HeOrderRefundMapper;
import com.stbella.order.infrastructure.repository.mapper.saas.OrderRelationMapper;
import com.stbella.order.infrastructure.repository.po.HeOrderRefundPO;
import com.stbella.order.infrastructure.repository.po.OrderRelationPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-05-29  17:29
 * @Description: 订单关联表
 */
@Repository
@Slf4j
public class OrderRelationRepositoryImpl extends ServiceImpl<OrderRelationMapper, OrderRelationPO> implements OrderRelationRepository {
    /**
     * 获取子订单
     *
     * @param parentOrderId
     * @return
     */
    @Override
    public List<OrderRelationEntity> getChildOrderList(String parentOrderNO) {
        List<OrderRelationPO> pos = this.baseMapper.selectList(new LambdaQueryWrapper<OrderRelationPO>().eq(OrderRelationPO::getParentOrderNo, parentOrderNO).eq(OrderRelationPO::getDeleted, 0));
        List<OrderRelationEntity> orderRelationEntities = BeanMapper.mapList(pos, OrderRelationEntity.class);
        return orderRelationEntities;
    }

    /**
     * 获取父订单
     *
     * @param orderId
     * @return
     */
    @Override
    public OrderRelationEntity getParentOrder(String orderNo) {

        List<OrderRelationPO> pos = this.baseMapper.selectList(new LambdaQueryWrapper<OrderRelationPO>().eq(OrderRelationPO::getOrderNo, orderNo).eq(OrderRelationPO::getDeleted, 0));
        List<OrderRelationEntity> orderRelationEntities = BeanMapper.mapList(pos, OrderRelationEntity.class);
        if  (orderRelationEntities.size() > 0) {
            return orderRelationEntities.get(0);
        }
        return null;
    }

    /**
     * 保存
     *
     * @param entity
     * @return
     */
    @Override
    public int save(OrderRelationEntity entity) {
        OrderRelationPO relationPO = BeanMapper.map(entity, OrderRelationPO.class);
        relationPO.setDeleted(0);
        relationPO.setAddTime(new Date());
        relationPO.setUpdateTime(new Date());
        this.save(relationPO);
        return 1;
    }
}

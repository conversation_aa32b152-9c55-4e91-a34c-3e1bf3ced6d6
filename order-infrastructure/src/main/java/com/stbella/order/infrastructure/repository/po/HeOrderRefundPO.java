package com.stbella.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 退款记录表
 * </p>
 *
 * <AUTHOR> @since 2022-10-27
 */
@Data
@Accessors(chain = true)
@TableName("he_order_refund")
@ApiModel(value = "HeOrderRefundPO对象", description = "退款记录表")
public class HeOrderRefundPO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "订单ID")
    private Integer orderId;

    @ApiModelProperty(value = "订单商品表ID/支付记录ID(income_record中的id)")
    private Integer orderGoodId;

    @ApiModelProperty(value = "支付流水编号")
    private String incomeSn;

    @ApiModelProperty(value = "退款类型, 0: 按商品发起退款 1:按收款记录发起退款")
    private Integer type;

    @ApiModelProperty(value = "退款订单编号")
    private String refundOrderSn;

    @ApiModelProperty(value = "申请退款金额")
    private Integer applyAmount;

    @ApiModelProperty(value = "最终退款方式(与income_record中的pay_type保持一致)，1=微信, 2=支付宝, 3=线下支付, 4=中信银行支付宝支付, 5=中信银行微信支付  8=在线pos机支付  9=C端小程序支付 10=其他")
    private Integer refundType;

    @ApiModelProperty(value = "1-原路退回；2-线下汇款；3-退回至意向金")
    private Integer refundMethod;

    @ApiModelProperty(value = "sop项目id")
    private Long projectId;

    @ApiModelProperty(value = "同意时间")
    private Long agreeAt;

    @ApiModelProperty(value = "实际退款金额")
    private Integer actualAmount;

    @ApiModelProperty(value = "实际退款业绩")
    private Integer refundAchievement;

    @ApiModelProperty(value = "完成时间/到账时间")
    private Long finishAt;

    @ApiModelProperty(value = "json,存储财务上传的退款成功信息记录或第三方退款实际返回值")
    private String paymentResult;

    @ApiModelProperty(value = "退款状态，1：审批中 2：审批失败  3：审批成功/等待打款/待确认 4：退款已到帐/已确认 5：到账失败/已拒绝")
    private Integer status;

    @ApiModelProperty(value = "确认人")
    private Long confirmUid;

    @ApiModelProperty(value = "是否删除:0=未删除,1=已删除")
    private Integer isDelete;

    @ApiModelProperty(value = "添加时间")
    private Long createdAt;

    @ApiModelProperty(value = "更新时间")
    private Long updatedAt;

    @ApiModelProperty(value = "用户退款基本信息(开户名，银行卡号，开户行+支行)")
    private String refundInfo;

    @ApiModelProperty(value = "售后说明/申请信息(json), 存储退款申请时 退款具体原因")
    private String remark;

    @ApiModelProperty(value = "退款原因(类型)一级：1=正常退款;2=⾮正常退款（客诉）")
    private Integer refundReasonType;

    @ApiModelProperty(value = "退款原因(0-99正常退款原因) (100以上是非正常退款原因):退款原因:0-默认 1-宝妈身体原因 2-宝宝原因 3-疫情政策 4-距离原因 5-家人因素 6-环境设施 7-馆内服务 8-用户需求 9-套餐变更 10-门店承接问题 11-客诉赔偿 12-退押金 13-其他    \n100-月子餐问题 101-护理问题 102-产康问题 103-管内活动问题 104-第三方(宝宝艺术照)问题 105-服务态度问题 106其他")
    private Integer refundReason;

    @ApiModelProperty(value = "是否存在违约金, 0-默认 1-存在 2-不存在")
    private Integer hasLiquidatedDamages;

    @ApiModelProperty(value = "快递公司编码")
    private String expressCode;

    @ApiModelProperty(value = "快递公司简称")
    private String expressName;

    @ApiModelProperty(value = "快递单号")
    private String expressNumber;

    @ApiModelProperty(value = "任务ID，非sop触发默认为0，任务开始时的task_id")
    private Long taskId;

    @ApiModelProperty(value = "当前最后一次操作完成的task_id")
    private Long lastTaskId;

    @ApiModelProperty(value = "对账状态：0未对账，1正常，2异常")
    private Integer checkStatus;

    @ApiModelProperty(value = "第三方交易流水号")
    private String transactionId;

    @ApiModelProperty(value = "是否人工修改过退单业绩，0-否 1-是")
    private Integer isAdminModify;

    @ApiModelProperty(value = "礼赠是否失效;0-不失效(默认),1-失效")
    private Integer giftExtendDisabled;

    @ApiModelProperty(value = "申请人ID")
    private Integer applyId;

    @ApiModelProperty(value = "申请人姓名")
    private String applyName;

    @ApiModelProperty(value = "申请人手机号")
    private String applyPhone;

    @ApiModelProperty(value = "上级退款Sn")
    private String parentRefundOrderSn;

    @ApiModelProperty(value = "退款性质")
    private Integer refundNature;

    @ApiModelProperty(value = "货币码")
    private String currency;

    public String paymentResultBk;
}

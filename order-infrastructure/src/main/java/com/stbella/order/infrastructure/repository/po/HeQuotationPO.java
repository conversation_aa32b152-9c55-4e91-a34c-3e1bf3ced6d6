package com.stbella.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
@TableName("he_quotation")
@ApiModel(value = "HeQuotationPO对象", description = "报价单表")
public class HeQuotationPO {

    @ApiModelProperty(value = "主键自增")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "报价单编号")
    private String quotationSn;

    @ApiModelProperty(value = "报价单类型")
    private Integer quotationType;

    @ApiModelProperty(value = "全局用户ID")
    private Integer basicUid;

    @ApiModelProperty(value = "客户ID")
    private Integer clientUid;

    @ApiModelProperty(value = "客户类型")
    private Integer clientType;

    @ApiModelProperty(value = "门店ID")
    private Integer storeId;

    @ApiModelProperty(value = "原价总金额，单位：分")
    private Integer orderAmount;

    @ApiModelProperty(value = "应付总金额")
    private Integer payAmount;

    @ApiModelProperty(value = "是否备孕0=否 1=是")
    private Integer isPreparePregnancy;

    @ApiModelProperty(value = "入住时间")
    private Long wantIn;

    @ApiModelProperty(value = "订单备注")
    private String remark;

    @ApiModelProperty(value = "报价单来源：0未知，1小贝拉，2圣贝拉")
    private Integer source;

    @ApiModelProperty(value = "毛利率")
    private BigDecimal grossMargin;

    @ApiModelProperty(value = "净利率")
    private BigDecimal netMargin;

    @ApiModelProperty(value = "折扣率")
    private BigDecimal discountMargin;

    //TODO 报价单要不要加这个

    @ApiModelProperty(value = "折扣审批状态")
    private Integer approvalDiscountStatus;

    @ApiModelProperty(value = "订单折扣详情")
    private String discountDetails;

    @ApiModelProperty(value = "标签")
    private Integer orderTag;

    @ApiModelProperty(value = "标签名称")
    private String orderTagName;

    @ApiModelProperty(value = "报价单状态 是否转化成订单 0-否 1-是")
    private Integer orderStatus;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "BU母婴为0")
    private Integer bu;

    @ApiModelProperty(value = "是否需要签署合同，0表示不需要，1表示需要")
    private Integer needSign;

    @ApiModelProperty(value = "操作人id")
    private Integer operatorGuid;

    @ApiModelProperty(value = "报价单扩展信息")
    private String extraInfo;

    @ApiModelProperty(value = "是否删除：1是，0否")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    private Date updatedAt;

}

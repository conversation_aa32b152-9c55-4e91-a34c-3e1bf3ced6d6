package com.stbella.order.infrastructure.gateway.impl;

import cn.hutool.json.JSONUtil;
import com.stbella.customer.server.customer.request.goodsappointment.GoodsAppointmentQueryRequest;
import com.stbella.customer.server.customer.request.growth.CustomerAssetsFlowReq;
import com.stbella.customer.server.customer.service.CustomerGoodsAppointmentService;
import com.stbella.customer.server.customer.service.CustomerGrowthService;
import com.stbella.customer.server.customer.vo.goodsappointment.GoodsAppointmentListVO;
import com.stbella.order.domain.client.CustomerGrowthClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: jijunjian
 * @CreateTime: 2024-08-01  13:36
 * @Description: 客户增长接口实现
 * @Version: 1.0
 */
@Component
@Slf4j
public class CustomerGrowthClientImpl implements CustomerGrowthClient {

    @DubboReference
    private CustomerGrowthService growthService;

    @DubboReference
    private CustomerGoodsAppointmentService customerGoodsAppointmentService;

    /**
     * 增加成长值
     *
     * @param req
     */
    @Override
    public void addGrowth(CustomerAssetsFlowReq req) {
        try {
            log.info("addGrowth req:{}", JSONUtil.toJsonStr(req));
            growthService.addAssertsGrowthFlow(req);
        }catch (Exception e){
            log.error("addGrowth error",e);
        }
    }

    @Override
    public List<GoodsAppointmentListVO> queryUserAppointment(List<GoodsAppointmentQueryRequest> req) {
        try {
            log.info("queryUserAppointment req:{}", JSONUtil.toJsonStr(req));
            return customerGoodsAppointmentService.queryUserAppointment(req);
        }catch (Exception e){
            log.error("queryUserAppointment error :{}",e.getMessage(), e);
            return Lists.newArrayList();
        }
    }
}

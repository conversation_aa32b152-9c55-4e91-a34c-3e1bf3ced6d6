package com.stbella.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 产康馆外订单表
 * </p>
 *
 * <AUTHOR> @since 2024-08-15
 */
@Data
@Accessors(chain = true)
@TableName("he_order_external_production")
@ApiModel(value = "ExternalProductionOrderPO对象", description = "产康馆外订单表")
public class ExternalProductionOrderPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    @ApiModelProperty(value = "客户ID")
    private Integer clientUid;

    @ApiModelProperty(value = "门店ID")
    private Integer storeId;

    @ApiModelProperty(value = "供应商ID")
    private Long supplierId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "项目类型")
    private String projectType;

    @ApiModelProperty(value = "交易金额（分）")
    private Integer amount;

    @ApiModelProperty(value = "项目金额")
    private Integer productAmount;

    @ApiModelProperty(value = "交易日期")
    private Date transactionDate;

    @ApiModelProperty(value = "专家姓名")
    private String expertName;

    @ApiModelProperty(value = "订单所有人ID")
    private Long ownerId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "订单状态：0-草稿 1-审核中 2-已通过 3-已拒绝 4-已撤回")
    private Integer status;

    @ApiModelProperty(value = "创建人ID")
    private Long createdBy;

    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    private Date updatedAt;

    @ApiModelProperty(value = "是否删除：0-否 1-是")
    @TableLogic
    private Integer isDeleted;

    @ApiModelProperty(value = "业绩生效时间（可为null，更新时可设为null）")
    @TableField(value = "percent_first_time", updateStrategy = FieldStrategy.IGNORED)
    private Long percentFirstTime;

    @ApiModelProperty(value = "是否报单过:1=未报单;2=已报单")
    private Integer isNotice;
}

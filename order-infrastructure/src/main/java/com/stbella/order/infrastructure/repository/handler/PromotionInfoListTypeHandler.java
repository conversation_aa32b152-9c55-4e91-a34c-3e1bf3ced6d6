package com.stbella.order.infrastructure.repository.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.stbella.platform.order.api.res.PromotionInfo;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * 自定义List<PromotionInfo>类型处理器
 * 解决JacksonTypeHandler无法正确处理泛型类型的问题
 */
@MappedTypes(List.class)
public class PromotionInfoListTypeHandler extends BaseTypeHandler<List<PromotionInfo>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<PromotionInfo> parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, JSON.toJSONString(parameter));
    }

    @Override
    public List<PromotionInfo> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String jsonString = rs.getString(columnName);
        return parsePromotionInfoList(jsonString);
    }

    @Override
    public List<PromotionInfo> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String jsonString = rs.getString(columnIndex);
        return parsePromotionInfoList(jsonString);
    }

    @Override
    public List<PromotionInfo> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String jsonString = cs.getString(columnIndex);
        return parsePromotionInfoList(jsonString);
    }

    private List<PromotionInfo> parsePromotionInfoList(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }
        try {
            return JSON.parseObject(jsonString, new TypeReference<List<PromotionInfo>>() {});
        } catch (Exception e) {
            throw new RuntimeException("Failed to parse PromotionInfo list from JSON: " + jsonString, e);
        }
    }
}
package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.month.entity.GoodsCategoryFrontEntity;
import com.stbella.order.domain.order.production.GoodsCategoryBackEntity;
import com.stbella.order.domain.order.production.GoodsEntity;
import com.stbella.order.domain.order.production.GoodsSkuEntity;
import com.stbella.order.infrastructure.repository.po.Goods;
import com.stbella.order.infrastructure.repository.po.GoodsCategoryBack;
import com.stbella.order.infrastructure.repository.po.GoodsCategoryFront;
import com.stbella.order.infrastructure.repository.po.GoodsSku;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface GoodsConverter {

    /**
     * po -> entity
     *
     * @param po 实体
     * @return GoodsSkuEntity
     */
    GoodsSkuEntity po2GoodsSkuEntity(GoodsSku po);

    /**
     * po -> entity
     *
     * @param po 实体
     * @return GoodsEntity
     */
    GoodsEntity po2GoodsEntity(Goods po);

    List<GoodsEntity> po2EntityForListGoods(List<Goods> poList);

    List<GoodsCategoryBackEntity> goodsCategoryBackList2GoodsCategoryBackEntityList(List<GoodsCategoryBack> list);

    List<GoodsCategoryFrontEntity> goodsCategoryFrontList2GoodsCategoryFrontEntityList(List<GoodsCategoryFront> list);

    List<GoodsSkuEntity> poList2EntityList(List<GoodsSku> goodsSkus);
}

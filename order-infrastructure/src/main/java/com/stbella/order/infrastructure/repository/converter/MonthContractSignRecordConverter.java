package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.infrastructure.repository.condition.MonthContractSignAgreementCondition;
import com.stbella.order.infrastructure.repository.po.*;
import com.stbella.order.server.order.month.req.MonthContractSignAgreementReq;
import com.stbella.order.server.order.month.req.MonthContractSignRecordReq;
import com.stbella.order.server.order.month.res.MonthContractSignRecordVO;
import com.stbella.order.server.order.month.res.MonthOrderParamHistoryVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface MonthContractSignRecordConverter {

    /**
     * po -> entity
     *
     * @param po 实体
     * @return GoodsEntity
     */
    MonthContractSignRecordEntity po2MonthContractSignRecordEntity(MonthContractSignRecordPO po);

    List<MonthContractSignRecordEntity> po2MonthContractSignRecordEntityList(List<MonthContractSignRecordPO>  poList);

    /**
     * req -> po
     * @param req
     * @return {@link MonthContractSignRecordPO}
     */
    MonthContractSignRecordPO req2MonthContractSignRecordPo(MonthContractSignRecordReq req);

    /**
     * po -> entity
     *
     * @param vo 实体
     * @return MonthContractSignRecordVO
     */
    MonthContractSignRecordPO vo2MonthContractSignRecordPo(MonthContractSignRecordVO vo);

    /**
     * entity -> po
     * @param entity
     * @return {@link MonthContractSignRecordPO}
     */
    MonthContractSignRecordPO entity2MonthContractSignRecordPo(MonthContractSignRecordEntity entity);

    /**
     * po -> entity 多条
     * @param monthContractSignRecordPOS
     * @return {@link List}<{@link MonthContractSignRecordEntity}>
     */
    List<MonthContractSignRecordEntity> po2MonthContractSignRecordList(List<MonthContractSignRecordPO> monthContractSignRecordPOS);

    List<MonthContractSignAgreementEntity> po2MonthContractSignAgreementRecordList(List<MonthContractSignAgreementPO> monthContractSignAgreementPOS);

    /**
     * po -> entity
     * @param po
     * @return {@link MonthContractSignAgreementEntity}
     */
    MonthContractSignAgreementEntity po2MonthContractSignAgreementRecordEntity(MonthContractSignAgreementPO po);

    /**
     * @param entities
     * @return {@link List}<{@link MonthContractSignAgreementPO}>
     */
    List<MonthContractSignAgreementPO> entity2MonthContractSignAgreementRecordPoList(List<MonthContractSignAgreementEntity> entities);


    /**
     * @param entity
     * @return {@link MonthContractSignAgreementPO}
     */
    MonthContractSignAgreementPO entity2MonthContractSignAgreementRecordPo(MonthContractSignAgreementEntity entity);

    /**
     * po -> entity
     * @param po
     * @return {@link MonthEsignTemplateParamConfigEntity}
     */
    MonthEsignTemplateParamConfigEntity po2MonthEsignTemplateParamConfigEntity(MonthEsignTemplateParamConfigPO po);

    /**
     * po -> entity 批量
     * @param pos
     * @return {@link List}<{@link MonthEsignTemplateParamConfigEntity}>
     */
    List<MonthEsignTemplateParamConfigEntity> po2MonthEsignTemplateParamConfigEntityList(List<MonthEsignTemplateParamConfigPO> pos);

    /**
     * req -> po
     * @param req
     * @return {@link MonthContractSignAgreementPO}
     */
    MonthContractSignAgreementPO req2MonthContractSignAgreementRecordPo(MonthContractSignAgreementReq req);

    List<MonthContractSignAgreementEntity> po2MonthContractSignAgreementEntityList(List<MonthContractSignAgreementPO>  poList);

    List<ContractSignRecordPaperEntity> po2MonthContracSignRecordPaperEntityList(List<ContractSignRecordPaperPO>  poList);

    ContractSignRecordPaperEntity po2MonthContracSignRecordPaperEntity(ContractSignRecordPaperPO  po);

    ContractSignRecordPaperPO entity2MonthContracSignRecordPaperPO(ContractSignRecordPaperEntity  entity);

    MonthHeUserEsignEntity po2MonthHeUserEsignEntity(MonthHeUserEsignPO po);


    List<MonthHeUserEsignEntity> po2MonthHeUserEsignEntityList(List<MonthHeUserEsignPO> pos);

    MonthEsignTemplateParamEntity po2MonthEsignTemplateParamEntity(MonthEsignTemplateParamPO po);

    List<MonthEsignTemplateParamEntity> po2MonthEsignTemplateParamEntityList(List<MonthEsignTemplateParamPO> pos);

    MonthEsignTemplateEntity po2MonthEsignTemplateEntity(MonthEsignTemplatePO po);

    List<MonthEsignTemplateEntity> po2MonthEsignTemplateEntityList(List<MonthEsignTemplatePO> pos);

    MonthOrderParamHistoryEntity po2MonthOrderParamHistoryEntity(MonthOrderParamHistoryPO po);

    List<MonthOrderParamHistoryEntity> po2MonthOrderParamHistoryEntityList(List<MonthOrderParamHistoryPO> pos);

    MonthOrderParamHistoryPO vo2MonthOrderParamHistoryPo(MonthOrderParamHistoryVO vo);

    List<MonthOrderParamHistoryPO> vo2MonthOrderParamHistoryPoList(List<MonthOrderParamHistoryVO> vos);

    MonthContractSignAgreementCondition req2MonthContractSignAgreementCondition(MonthContractSignAgreementReq req);

}

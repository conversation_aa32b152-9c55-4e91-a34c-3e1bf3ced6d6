package com.stbella.order.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.domain.repository.OrderReductionRepository;
import com.stbella.order.infrastructure.repository.converter.OrderDecreaseConverter;
import com.stbella.order.infrastructure.repository.mapper.saas.OrderReductionMapper;
import com.stbella.order.infrastructure.repository.po.OrderReductionPO;
import com.stbella.order.server.order.month.req.OrderQuery;
import com.stbella.platform.order.api.reduction.req.ReductionQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单减免
 * 减免 - 下完订单后，手动减少签单金额
 */
@Slf4j
@Repository
public class OrderReductionRepositoryImpl extends ServiceImpl<OrderReductionMapper, OrderReductionPO> implements OrderReductionRepository {

    @Resource
    private OrderDecreaseConverter orderDecreaseConverter;

    @Override
    public Long addOne(OrderReductionEntity orderDecreaseEntity) {
        OrderReductionPO reductionPO = orderDecreaseConverter.entity2PO(orderDecreaseEntity);
        this.save(reductionPO);
        return reductionPO.getId();
    }

    @Override
    public List<OrderReductionEntity> select(ReductionQuery query) {

        LambdaQueryWrapper<OrderReductionPO> queryWrapper = new LambdaQueryWrapper<OrderReductionPO>()
                .in(CollectionUtils.isNotEmpty(query.getAuthStateList()), OrderReductionPO::getAuthState, query.getAuthStateList())
                .eq(OrderReductionPO::getOrderId, query.getOrderId())
                .orderByDesc(OrderReductionPO::getGmtCreate);
        queryWrapper.orderByDesc(OrderReductionPO::getGmtModified);
        List<OrderReductionPO> reductionPOS = this.baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(reductionPOS)) {
            return new ArrayList<>();
        }

        List<OrderReductionEntity> orderReductionEntities = BeanMapper.mapList(reductionPOS, OrderReductionEntity.class);
        return orderReductionEntities;
    }

    @Override
    public OrderReductionEntity selectByLocalProcessId(String localProcessId) {
        OrderReductionPO one = this.getOne(new LambdaQueryWrapper<OrderReductionPO>().eq(OrderReductionPO::getLocalProcessId, localProcessId));
        return orderDecreaseConverter.po2Entity(one);
    }

    @Override
    public int updateOne(OrderReductionEntity orderDecreaseEntity) {
        return this.baseMapper.updateById(orderDecreaseConverter.entity2PO(orderDecreaseEntity));
    }

    @Override
    public OrderReductionEntity getById(Long reductionId) {
        OrderReductionPO one = this.getOne(new LambdaQueryWrapper<OrderReductionPO>().eq(OrderReductionPO::getId, reductionId));
        return orderDecreaseConverter.po2Entity(one);

    }

    /**
     * 减免金额
     *
     * @param query
     * @return
     */
    @Override
    public Long reductionSum(OrderQuery query) {
        Long aLong = this.baseMapper.reductionSum(query);
        return aLong;
    }

    @Override
    public OrderReductionEntity selectByContractId(Long contractId) {
        OrderReductionPO one = this.getOne(new LambdaQueryWrapper<OrderReductionPO>().eq(OrderReductionPO::getContractId, contractId));
        return orderDecreaseConverter.po2Entity(one);
    }

    @Override
    public OrderReductionEntity selectFirstReductionEntity(Long orderId) {
        OrderReductionPO one = this.getOne(new LambdaQueryWrapper<OrderReductionPO>().eq(OrderReductionPO::getOrderId, orderId)
                .orderByAsc(OrderReductionPO::getGmtCreate).last("limit 1"));
        return orderDecreaseConverter.po2Entity(one);
    }

    @Override
    public List<OrderReductionEntity> listByOrderIdList(List<Integer> orderIdList, ArrayList<Integer> authStateList) {
        LambdaQueryWrapper<OrderReductionPO> queryWrapper = new LambdaQueryWrapper<OrderReductionPO>()
                .in(OrderReductionPO::getAuthState, authStateList)
                .in(OrderReductionPO::getOrderId, orderIdList);
        List<OrderReductionPO> reductionPOS = this.baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(reductionPOS)) {
            return new ArrayList<>();
        }

        List<OrderReductionEntity> orderReductionEntities = BeanMapper.mapList(reductionPOS, OrderReductionEntity.class);
        return orderReductionEntities;
    }
}

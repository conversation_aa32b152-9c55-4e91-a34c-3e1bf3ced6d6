package com.stbella.order.infrastructure.oss;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.request.UploadFileRequest;
import com.qcloud.cos.sign.Credentials;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.infrastructure.oss.config.CloudStorageConfig;
import com.stbella.order.server.oss.response.OssUploadResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.IOUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;


/**
 * 腾讯云存储
 *
 * <AUTHOR>
 */
@Slf4j
public class QcloudCloudStorageService extends CloudStorageService {
    private COSClient client;


    public QcloudCloudStorageService(CloudStorageConfig config) {
        this.config = config;

        //初始化
        init();
    }

    private void init() {
        Credentials credentials = new Credentials(config.getQcloudAppId(), config.getQcloudSecretId(),
                config.getQcloudSecretKey());

        //初始化客户端配置
        ClientConfig clientConfig = new ClientConfig();
        //设置bucket所在的区域，华南：gz 华北：tj 华东：sh
        clientConfig.setRegion(config.getQcloudRegion());

        client = new COSClient(clientConfig, credentials);
    }

    @Override
    public OssUploadResponse upload(byte[] data, String path) {

        log.info("开始上传文件到腾讯云");
        OssUploadResponse ossUploadResponse = new OssUploadResponse();

        //腾讯云必需要以"/"开头
        if (!path.startsWith("/")) {
            path = "/" + path;
        }

        //上传到腾讯云
        log.info("正式处理上传文件到腾讯云操作");
        UploadFileRequest request = new UploadFileRequest(config.getQcloudBucketName(), path, data);
        log.info("开始上传文件到腾讯云，请求路径path={}", path);
        String response = client.uploadFile(request);
        log.info("上传文件到腾讯云成功，返回结果={}", response);

        JSONObject jsonObject = JSONUtil.parseObj(response);
        if (jsonObject.getInt("code") != 0) {
            throw new BusinessException(ResultEnum.FILE_ERROR, "文件上传失败，" + jsonObject.getStr("message"));
        }
        ossUploadResponse.setSuccess(true);
        ossUploadResponse.setUrl(config.getQcloudDomain() + path);
        return ossUploadResponse;
    }

    @Override
    public OssUploadResponse upload(InputStream inputStream, String path) {
        try {
            byte[] data = IOUtils.toByteArray(inputStream);
            return this.upload(data, path);
        } catch (IOException e) {
            log.info("上传文件失败={}", e);
            throw new BusinessException(ResultEnum.FILE_ERROR, "上传文件失败");
        } catch (Throwable e) {
            log.info("执行文件上传发生异常", e);
            throw new BusinessException(ResultEnum.FILE_ERROR, "上传文件失败");
        }
    }

    @Override
    public OssUploadResponse uploadSuffix(byte[] data, String suffix) {
        return upload(data, getPath(config.getQcloudPrefix(), suffix));
    }

    /**
     * 文件上传
     *
     * @param file 文件
     * @return OssUploadResponse
     */
    @Override
    public OssUploadResponse uploadFile(MultipartFile file, String path) {
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            log.info("上传文件失败={}", e);
            throw new BusinessException(ResultEnum.FILE_ERROR, "上传文件失败");
        }
        OssUploadResponse ossUploadResponse = upload(inputStream, path);
        return ossUploadResponse;
    }


    @Override
    public OssUploadResponse uploadSuffix(InputStream inputStream, String suffix) {
        return upload(inputStream, getPath(config.getQcloudPrefix(), suffix));
    }

    //暂未实现
//    @Override
//    public OssUploadResponse uploadFile(MultipartFile file) {
//        return null;
//    }
}

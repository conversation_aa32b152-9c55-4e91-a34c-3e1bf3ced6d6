package com.stbella.order.infrastructure.repository.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.enums.core.IncomeRecordPayStatusEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.TabClientEntity;
import com.stbella.order.domain.repository.ClientRepository;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.infrastructure.repository.condition.ClientDepositCondition;
import com.stbella.order.infrastructure.repository.converter.IncomeRecordConverter;
import com.stbella.order.infrastructure.repository.mapper.saas.HeIncomeRecordMapper;
import com.stbella.order.infrastructure.repository.po.HeIncomeRecordPO;
import com.stbella.order.server.order.month.req.ClientDepositQuery;
import com.stbella.order.server.order.month.req.IncomeRecordListReq;
import com.stbella.order.server.order.month.res.PayDepositRecordVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Repository
public class IncomeRecordRepositoryImpl extends ServiceImpl<HeIncomeRecordMapper, HeIncomeRecordPO> implements IncomeRecordRepository {

    @Resource
    private IncomeRecordConverter incomeRecordConverter;

    @Resource
    private ClientRepository clientRepository;
    @Resource
    private HeIncomeRecordMapper heIncomeRecordMapper;

    /**
     * 获取已支付的流水
     */
    @Override
    public List<HeIncomeRecordEntity> getSuccessfulRecordListByOrderId(Integer orderId) {
        List<HeIncomeRecordPO> heIncomeRecord = this.baseMapper.selectList(
                new LambdaQueryWrapper<HeIncomeRecordPO>()
                        .eq(HeIncomeRecordPO::getOrderId, orderId)
                        .eq(HeIncomeRecordPO::getStatus, 1)
                        .orderByDesc(HeIncomeRecordPO::getPayTime)
        );
        return incomeRecordConverter.incomeRecordPoListToEntity(heIncomeRecord);
    }

    @Override
    public List<HeIncomeRecordEntity> getAllRecordListByOrderId(Integer orderId) {
        List<HeIncomeRecordPO> heIncomeRecord = this.baseMapper.selectList(
                new LambdaQueryWrapper<HeIncomeRecordPO>()
                        .eq(HeIncomeRecordPO::getOrderId, orderId)
                        .eq(HeIncomeRecordPO::getIsDelete, 0)
                        .orderByDesc(HeIncomeRecordPO::getId)
        );
        return incomeRecordConverter.incomeRecordPoListToEntity(heIncomeRecord);
    }

    /***
     * 获取线下审核被拒绝的审批支付记录
     */
    @Override
    public List<HeIncomeRecordEntity> getOfflineRefuseRecordListByOrderId(Integer orderId) {
        List<HeIncomeRecordPO> heIncomeRecord = this.baseMapper.selectList(
                new LambdaQueryWrapper<HeIncomeRecordPO>()
                        .eq(HeIncomeRecordPO::getOrderId, orderId)
                        .eq(HeIncomeRecordPO::getStatus, 2)
                        .eq(HeIncomeRecordPO::getPayType, OmniPayTypeEnum.OFFLINE.getCode())
                        .orderByDesc(HeIncomeRecordPO::getPayTime)
        );
        return incomeRecordConverter.incomeRecordPoListToEntity(heIncomeRecord);
    }

    @Override
    public Page<HeIncomeRecordEntity> getRecordPageByOrderId(Integer orderId, Integer pageNum, Integer pageSize) {

        // 使用 LambdaQueryWrapper
        LambdaQueryWrapper<HeIncomeRecordPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();

        lambdaQueryWrapper.eq(HeIncomeRecordPO::getIsDelete, 0)
                .eq(HeIncomeRecordPO::getOrderId, orderId)
                .ne(HeIncomeRecordPO::getPayType,OmniPayTypeEnum.REDUCTION.getCode())
                .and(wrapper -> wrapper.eq(HeIncomeRecordPO::getStatus, IncomeRecordPayStatusEnum.COMPLETE.getCode())
                        .or()
                        .eq(HeIncomeRecordPO::getStatus, IncomeRecordPayStatusEnum.Failed.getCode())
                        .eq(HeIncomeRecordPO::getPayType, OmniPayTypeEnum.OFFLINE.getCode()))
                .orderByDesc(HeIncomeRecordPO::getPayTime);
        Page<HeIncomeRecordPO> heIncomeRecordPOPage = heIncomeRecordMapper.selectPage(new Page<>(pageNum, pageSize), lambdaQueryWrapper);

        return incomeRecordConverter.incomeRecordPoPageToEntityPage(heIncomeRecordPOPage);
    }

    @Override
    public List<HeIncomeRecordEntity> getRecordListByOrderIdList(List<Integer> orderIdList, Integer status, Integer receiptType) {

        List<HeIncomeRecordPO> heIncomeRecord = this.baseMapper.selectList(new LambdaQueryWrapper<HeIncomeRecordPO>()
                .in(CollectionUtil.isNotEmpty(orderIdList), HeIncomeRecordPO::getOrderId, orderIdList)
                .eq(Objects.nonNull(status), HeIncomeRecordPO::getStatus, status)
                .eq(Objects.nonNull(receiptType), HeIncomeRecordPO::getReceiptType, receiptType));
        if (CollectionUtil.isEmpty(heIncomeRecord)) {
            return new ArrayList<>();
        }
        return incomeRecordConverter.incomeRecordPoListToEntity(heIncomeRecord);
    }


    /**
     * 修改
     *
     * @param heIncomeRecordEntity
     */
    @Override
    public boolean updateRecord(HeIncomeRecordEntity heIncomeRecordEntity) {
        HeIncomeRecordPO heIncomeRecordPO = incomeRecordConverter.entity2PO(heIncomeRecordEntity);
        heIncomeRecordPO.setCreatedAt(null);

        return this.updateById(heIncomeRecordPO);
    }

    /**
     * 批量修改
     *
     * @param heIncomeRecordEntityList
     */
    @Override
    public boolean batchUpdateRecordList(List<HeIncomeRecordEntity> heIncomeRecordEntityList) {
        List<HeIncomeRecordPO> heIncomeRecordPOS = incomeRecordConverter.entity2POList(heIncomeRecordEntityList);
        return this.updateBatchById(heIncomeRecordPOS);
    }

    @Override
    public HeIncomeRecordEntity getRecordByIncomeSn(String incomeSn) {
        List<HeIncomeRecordPO> heIncomeRecord = this.baseMapper.selectList(new LambdaQueryWrapper<HeIncomeRecordPO>()
                .eq(Objects.nonNull(incomeSn), HeIncomeRecordPO::getIncomeSn, incomeSn));
        if (CollectionUtil.isEmpty(heIncomeRecord)) {
            log.error("支付记录不存在,支付编号=" + incomeSn);
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "支付记录不存在,支付编号=" + incomeSn);
        }
        return incomeRecordConverter.po2Entity(heIncomeRecord.get(0));
    }

    @Override
    public Integer saveOne(HeIncomeRecordEntity entity) {
        HeIncomeRecordPO heIncomeRecordPO = incomeRecordConverter.entity2PO(entity);
        this.save(heIncomeRecordPO);
        return heIncomeRecordPO.getId();
    }

    /**
     * 根据ID获取记录
     *
     * @param id
     */
    @Override
    public HeIncomeRecordEntity getOneById(Integer id) {
        return incomeRecordConverter.po2Entity(this.getById(id));
    }

    @Override
    public List<HeIncomeRecordEntity> getRecordListByClientUid(Integer clientUid, Integer receiptType) {
        List<HeIncomeRecordPO> heIncomeRecordPOS = this.baseMapper.selectList(new LambdaQueryWrapper<HeIncomeRecordPO>()
                .eq(HeIncomeRecordPO::getClientUid, clientUid)
                .eq(HeIncomeRecordPO::getReceiptType, receiptType)
                .eq(HeIncomeRecordPO::getStatus, IncomeRecordPayStatusEnum.COMPLETE.getCode())
                .eq(HeIncomeRecordPO::getIsDelete, 0)
        );
        return incomeRecordConverter.incomeRecordPoListToEntity(heIncomeRecordPOS);
    }


    @Override
    public List<HeIncomeRecordEntity> getSuccessfulRecordListByOrderId(Integer orderId, Integer status, List<Integer> receiptType) {
        List<HeIncomeRecordPO> heIncomeRecordPOS = this.baseMapper.selectList(new LambdaQueryWrapper<HeIncomeRecordPO>()
                .eq(HeIncomeRecordPO::getOrderId, orderId)
                .in(HeIncomeRecordPO::getReceiptType, receiptType)
                .eq(HeIncomeRecordPO::getStatus, status)
                .eq(HeIncomeRecordPO::getIsDelete, 0)
        );
        return incomeRecordConverter.incomeRecordPoListToEntity(heIncomeRecordPOS);
    }

    @Override
    public Page<PayDepositRecordVO> queryStatisticsIncome(ClientDepositQuery query) {
        ClientDepositCondition condition = new ClientDepositCondition();
        condition.setStoreIds(query.getStoreIds());
        if (StringUtils.isNotBlank(query.getKeyword())) {
            List<TabClientEntity> tabClientList = clientRepository.getTabClientByNameAndPhone(query.getKeyword());
            List<Integer> clientIdList = CollectionUtils.isNotEmpty(tabClientList) ? tabClientList.stream().map(TabClientEntity::getId).collect(Collectors.toList()) : null;
            condition.setClientIds(clientIdList);
        }
        return this.getBaseMapper().queryStatisticsIncome(new Page<>(query.getPageNum(), query.getPageSize()), condition);
    }

    @Override
    public List<HeIncomeRecordEntity> getRecordListById(List<Integer> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return incomeRecordConverter.incomeRecordPoListToEntity(baseMapper.selectBatchIds(ids));
    }

    @Override
    public Page<HeIncomeRecordEntity> getPageRecordListByClientUid(Page page, Integer clientUid, Integer receiptType, Integer status) {
        LambdaQueryWrapper<HeIncomeRecordPO> lq = new LambdaQueryWrapper();
        lq.eq(HeIncomeRecordPO::getClientUid, clientUid);
        lq.eq(HeIncomeRecordPO::getReceiptType, receiptType);
        lq.eq(HeIncomeRecordPO::getStatus, status);
        lq.orderByDesc(HeIncomeRecordPO::getPayTime);
        Page PageHeIncomeRecordPO = this.page(page, lq);
        return incomeRecordConverter.pagePo2Entity(PageHeIncomeRecordPO);
    }

    @Override
    public List<HeIncomeRecordEntity> getRecordListByIncomeSnList(List<String> collect) {
        List<HeIncomeRecordPO> list = this.list(new LambdaQueryWrapper<HeIncomeRecordPO>().in(HeIncomeRecordPO::getIncomeSn, collect));
        return incomeRecordConverter.incomeRecordPoListToEntity(list);
    }

    @Override
    public boolean deleteByIncomeSn(List<String> incomeSnList) {
        LambdaUpdateWrapper<HeIncomeRecordPO> luq = new LambdaUpdateWrapper<>();
        luq.set(HeIncomeRecordPO::getIsDelete, 1);
        luq.in(HeIncomeRecordPO::getIncomeSn, incomeSnList);
        boolean update = this.update(luq);
        return update;
    }

    /**
     * 获取已支付的流水
     *
     * @param orderId
     * @param subtotalIncome
     */
    @Override
    public boolean syncSubtotalIncome(Integer orderId, Integer subtotalIncome) {
        LambdaUpdateWrapper<HeIncomeRecordPO> lq = new LambdaUpdateWrapper<>();
        lq.eq(HeIncomeRecordPO::getOrderId, orderId);
        lq.set(HeIncomeRecordPO::getSubtotalIncome, subtotalIncome);
        return this.update(lq);
    }

    /**
     * 押金转换特殊需求-根据ID设置orderId为null
     *
     * @param id
     */
    @Override
    public boolean updateSetOrderIdNullById(Integer id) {
        LambdaUpdateWrapper<HeIncomeRecordPO> lq = new LambdaUpdateWrapper<>();
        lq.eq(HeIncomeRecordPO::getId, id);
        lq.set(HeIncomeRecordPO::getOrderId, null);
        return this.update(lq);
    }

    /**
     * 业财-查询退款记录
     *
     * @param req
     * @return
     */
    @Override
    public List<HeIncomeRecordEntity> getIncomeRecordForFinancial(IncomeRecordListReq req) {
        LambdaQueryWrapper<HeIncomeRecordPO> lq = new LambdaQueryWrapper();
        if (ObjectUtil.isNotEmpty(req.getIncomeSnList())) {
            lq.in(HeIncomeRecordPO::getIncomeSn, req.getIncomeSnList());
        }
        if (CollectionUtils.isNotEmpty(req.getIncomeIds())) {
            lq.in(HeIncomeRecordPO::getId, req.getIncomeIds());
        }
        if (ObjectUtil.isNotEmpty(req.getPayTimeStart())) {
            lq.ge(HeIncomeRecordPO::getPayTime, req.getPayTimeStart());
        }
        if (ObjectUtil.isNotEmpty(req.getPayTimeEnd())) {
            lq.le(HeIncomeRecordPO::getPayTime, req.getPayTimeEnd());
        }
        if (ObjectUtil.isNotEmpty(req.getUpdateTimeStart())) {
            lq.ge(HeIncomeRecordPO::getUpdatedAt, req.getUpdateTimeStart());
        }
        if (ObjectUtil.isNotEmpty(req.getUpdateTimeEnd())) {
            lq.le(HeIncomeRecordPO::getUpdatedAt, req.getUpdateTimeEnd());
        }
        if (ObjectUtil.isNotEmpty(req.getPayStatus())) {
            lq.eq(HeIncomeRecordPO::getStatus, req.getPayStatus());
        }
        lq.eq(HeIncomeRecordPO::getIsDelete, BizConstant.DeleteFlag.NOT_DELETE);
        List<HeIncomeRecordPO> list = this.list(lq);
        return incomeRecordConverter.incomeRecordPoListToEntity(list);
    }

    @Override
    public List<HeIncomeRecordEntity> queryCurrencyIsNull(Long startDate) {
        List<HeIncomeRecordPO> list = this.list(
                new LambdaUpdateWrapper<HeIncomeRecordPO>().isNull(HeIncomeRecordPO::getCurrency).ge(HeIncomeRecordPO::getCreatedAt, startDate)
        );
        return incomeRecordConverter.incomeRecordPoListToEntity(list);
    }

    @Override
    public Integer getRealIncome(Integer orderId) {
        return this.heIncomeRecordMapper.getRealIncome(orderId);
    }

    @Override
    public List<HeIncomeRecordEntity> queryAllRecordListByOrderIds(List<Integer> orderIds) {

        if (CollectionUtils.isEmpty(orderIds)){
            return Collections.emptyList();
        }
        List<HeIncomeRecordPO> heIncomeRecord = this.baseMapper.selectList(
                new LambdaQueryWrapper<HeIncomeRecordPO>()
                        .in(HeIncomeRecordPO::getOrderId, orderIds)
                        .eq(HeIncomeRecordPO::getStatus, 1)
                        .orderByDesc(HeIncomeRecordPO::getId)
        );
        return incomeRecordConverter.incomeRecordPoListToEntity(heIncomeRecord);
    }
}

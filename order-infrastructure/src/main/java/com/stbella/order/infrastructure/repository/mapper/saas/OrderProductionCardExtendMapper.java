package com.stbella.order.infrastructure.repository.mapper.saas;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.stbella.order.infrastructure.repository.po.OrderProductionCardExtend;
import com.stbella.order.server.order.order.req.OrderExportReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface OrderProductionCardExtendMapper extends BaseMapper<OrderProductionCardExtend> {
    int deleteByPrimaryKey(Integer id);
    OrderProductionCardExtend selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(OrderProductionCardExtend record);

    int updateByPrimaryKeyWithBLOBs(OrderProductionCardExtend record);

    int updateByPrimaryKey(OrderProductionCardExtend record);

    /**
     * 通过通卡次卡关联ID 获取资产商品信息
     *
     * @param productionIdList 通卡/次卡
     * @return List<OrderProductionCardExtend>
     */
    default List<OrderProductionCardExtend> selectPropertyGoodsByProductionIdList(Set<Integer> productionIdList) {
        return selectList(new LambdaQueryWrapper<OrderProductionCardExtend>()
                .in(OrderProductionCardExtend::getOrderProductionId, productionIdList));
    }

    /**
     * 根据id列表批量失效过期的产康资产
     *
     * @param cardProductionIds
     * @return
     */
    int expiredByProductionIds(@Param("cardProductionIds") List<Integer> cardProductionIds);

    /**
     * 查询产康订单卡项目
     * @param req
     * @return
     */
    List<OrderProductionCardExtend> queryProductionOrderCardList(@Param("req") OrderExportReq req);

    /**
     * 查询单个产康订单卡项目
     * @param orderSn
     * @return
     */
    List<OrderProductionCardExtend> queryProductionOrderCardByOrderSn(@Param("orderSn") String orderSn);
}
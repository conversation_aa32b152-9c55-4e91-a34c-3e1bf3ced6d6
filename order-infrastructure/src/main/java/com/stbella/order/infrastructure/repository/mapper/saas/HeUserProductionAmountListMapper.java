package com.stbella.order.infrastructure.repository.mapper.saas;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.stbella.order.infrastructure.repository.po.HeUserProductionAmountListPO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 用户产康金记录 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-06-13
 */
public interface HeUserProductionAmountListMapper extends BaseMapper<HeUserProductionAmountListPO> {

    HeUserProductionAmountListPO queryByOrderIdAndSource(@Param("orderId") Integer orderId, @Param("source") Integer source);

    void updateIgnoreDelete(@Param("po") HeUserProductionAmountListPO po);

    void updateByOrderAndSource(@Param("id") Integer id, @Param("productionAmount") Integer productionAmount, @Param("timestamp") Integer timestamp);
}

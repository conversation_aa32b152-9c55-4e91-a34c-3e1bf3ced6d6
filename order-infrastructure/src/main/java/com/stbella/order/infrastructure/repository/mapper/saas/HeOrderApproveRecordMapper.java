package com.stbella.order.infrastructure.repository.mapper.saas;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.stbella.order.domain.order.month.entity.HeOrderApproveRecordEntity;
import com.stbella.order.infrastructure.repository.po.HeOrderApproveRecordPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 订单钉钉审批回调记录 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2022-10-27
 */
public interface HeOrderApproveRecordMapper extends BaseMapper<HeOrderApproveRecordPO> {


    @Select("select id,originator_phone As originatorPhone,params from he_order_approve_record where params->'$.orderRefundId' = #{refundId}")
    HeOrderApproveRecordEntity selectApproveRecordPhoneByOrderRefundId(@Param("refundId") Integer refundId);

    @Select("select id,originator_phone As originatorPhone,params from he_order_approve_record where params->'$.id' = #{id}")
    HeOrderApproveRecordEntity selectApproveRecordPhoneById(@Param("id") Integer id);

    @Select("select id,originator_phone As originatorPhone,params from he_order_approve_record where params->'$.recordId' = #{recordId}")
    HeOrderApproveRecordEntity selectApproveRecordByRecordId(@Param("recordId") Integer recordId);

}

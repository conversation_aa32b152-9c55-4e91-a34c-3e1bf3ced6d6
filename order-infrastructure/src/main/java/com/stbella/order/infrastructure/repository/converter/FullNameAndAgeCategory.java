package com.stbella.order.infrastructure.repository.converter;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
public class FullNameAndAgeCategory {

    @ApiModelProperty(value = "应付总金额")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "购买数量")
    private Integer goodsNum;

}

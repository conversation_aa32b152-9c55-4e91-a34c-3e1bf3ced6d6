package com.stbella.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.stbella.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 合同&折扣审批历史
 * </p>
 *
 * <AUTHOR> @since 2022-03-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("he_task_his")
@ApiModel(value = "TaskHisEntity对象", description = "合同&折扣审批历史")
public class HeTaskHisPO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    private Long taskId;

    private Integer approvalType;

    private Integer approvalStatus;

    private Integer restarted;

    private Integer formTemplateType;

    private String taskName;

    private String instanceId;

//    @ApiModelProperty(value = "审批类型 0=钉钉;1=企微;")
//    private Integer oaPlatformType;
}

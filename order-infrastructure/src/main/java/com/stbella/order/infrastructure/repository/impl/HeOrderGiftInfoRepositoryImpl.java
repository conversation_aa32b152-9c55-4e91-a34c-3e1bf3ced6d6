package com.stbella.order.infrastructure.repository.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.order.common.enums.order.OrderGiftStatusEnum;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.order.month.entity.HeOrderGiftExtendEntity;
import com.stbella.order.domain.repository.HeOrderGiftInfoRepository;
import com.stbella.order.infrastructure.repository.mapper.saas.HeOrderGiftInfoMapper;
import com.stbella.order.infrastructure.repository.po.HeOrderGiftInfoPO;
import com.stbella.order.server.order.month.req.HeOrderGiftInfoPiReq;
import com.stbella.order.server.order.month.req.HeOrderGiftInfoPicpReq;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;


@Repository
public class HeOrderGiftInfoRepositoryImpl extends ServiceImpl<HeOrderGiftInfoMapper, HeOrderGiftInfoPO> implements HeOrderGiftInfoRepository {

    @Resource
    private HeOrderGiftInfoMapper heOrderGiftInfoMapper;

    @Override
    public Page<HeOrderGiftExtendEntity> pageList(HeOrderGiftInfoPiReq pageReq) {
        IPage<HeOrderGiftInfoPO> page = heOrderGiftInfoMapper.selectPage(new Page<>(pageReq.getPageNum(), pageReq.getPageSize()), buildQueryWrapper(pageReq));
        Page<HeOrderGiftExtendEntity> resPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        resPage.setPages(page.getPages());
        resPage.setRecords(BeanMapper.mapList(page.getRecords(), HeOrderGiftExtendEntity.class));
        return resPage;
    }

    @Override
    public Page<HeOrderGiftExtendEntity> pageList(HeOrderGiftInfoPicpReq pageReq) {
        IPage<HeOrderGiftInfoPO> page = heOrderGiftInfoMapper.selectPage(new Page<>(pageReq.getPageNum(), pageReq.getPageSize()), buildQueryWrapper(pageReq));
        Page<HeOrderGiftExtendEntity> resPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        resPage.setPages(page.getPages());
        resPage.setRecords(BeanMapper.mapList(page.getRecords(), HeOrderGiftExtendEntity.class));
        return resPage;
    }

    @Override
    public HeOrderGiftExtendEntity selectById(Long id) {

        if (Objects.isNull(id)) {
            return null;
        }
        HeOrderGiftInfoPO heOrderGiftInfoPO = getById(id);
        if (Objects.isNull(heOrderGiftInfoPO)){
            return null;
        }
        return BeanMapper.map(heOrderGiftInfoPO, HeOrderGiftExtendEntity.class);
    }

    @Override
    public Long save(HeOrderGiftExtendEntity record) {

        Assert.notNull(record, "参数不能为空");
        HeOrderGiftInfoPO heOrderGiftInfoPO = BeanMapper.map(record, HeOrderGiftInfoPO.class);
        Assert.isTrue(save(heOrderGiftInfoPO), "保存失败");
        return heOrderGiftInfoPO.getId();
    }

    @Override
    public void update(HeOrderGiftExtendEntity record) {

        Assert.notNull(record, "参数不能为空");
        HeOrderGiftInfoPO heOrderGiftInfoPO = BeanMapper.map(record, HeOrderGiftInfoPO.class);
        heOrderGiftInfoPO.setUpdatedAt(new Date());
        Assert.isTrue(updateById(heOrderGiftInfoPO), "更新失败");
    }

    @Override
    public HeOrderGiftExtendEntity selectByApproveId(String processId) {

        LambdaQueryWrapper<HeOrderGiftInfoPO> lq = new LambdaQueryWrapper<>();
        lq.eq(HeOrderGiftInfoPO::getApproveId, processId).orderByDesc(HeOrderGiftInfoPO::getId);
        List<HeOrderGiftInfoPO> list = list(lq);
        if (CollectionUtils.isNotEmpty(list)) {
            return BeanMapper.map(list.get(0), HeOrderGiftExtendEntity.class);
        }
        return null;
    }

    @Override
    public HeOrderGiftExtendEntity selectByOrderId(Integer orderId) {
        LambdaQueryWrapper<HeOrderGiftInfoPO> lq = new LambdaQueryWrapper<>();
        lq.eq(HeOrderGiftInfoPO::getOrderId, orderId).eq(HeOrderGiftInfoPO :: getStatus, OrderGiftStatusEnum.APPROVAL_NOT_SEND.getCode()).orderByDesc(HeOrderGiftInfoPO::getId);
        List<HeOrderGiftInfoPO> list = list(lq);
        if (CollectionUtils.isNotEmpty(list)) {
            return BeanMapper.map(list.get(0), HeOrderGiftExtendEntity.class);
        }
        return null;
    }

    private LambdaQueryWrapper<HeOrderGiftInfoPO> buildQueryWrapper(HeOrderGiftInfoPiReq query) {
        return Wrappers.<HeOrderGiftInfoPO>lambdaQuery()
//                .eq(Objects.nonNull(query.getOperator().getOperatorGuid()), HeOrderGiftInfoPO::getCreateBy, query.getOperator().getOperatorGuid())
                .in(CollectionUtils.isNotEmpty(query.getAuthStoreIdList()), HeOrderGiftInfoPO::getStoreId, query.getAuthStoreIdList())
                .and(CollectionUtils.isNotEmpty(query.getClientUidList()) || CollectionUtils.isNotEmpty(query.getStoreIdList()),
                        item -> item.in(CollectionUtils.isNotEmpty(query.getClientUidList()), HeOrderGiftInfoPO::getClientUid, query.getClientUidList())
                        .or(CollectionUtils.isNotEmpty(query.getStoreIdList()), subItem -> subItem.in(HeOrderGiftInfoPO::getStoreId, query.getStoreIdList())))
                .lt(Objects.nonNull(query.getRetryNum()), HeOrderGiftInfoPO::getRetryNum, query.getRetryNum())
                .eq(Objects.nonNull(query.getStatus()), HeOrderGiftInfoPO::getStatus, query.getStatus())
                .eq(StringUtils.isNotEmpty(query.getGiftSn()), HeOrderGiftInfoPO::getGiftSn, query.getGiftSn())
                .eq(HeOrderGiftInfoPO::getDeleted, 0)
                .orderByDesc(HeOrderGiftInfoPO::getId);
    }


    private LambdaQueryWrapper<HeOrderGiftInfoPO> buildQueryWrapper(HeOrderGiftInfoPicpReq query) {
        return Wrappers.<HeOrderGiftInfoPO>lambdaQuery()
                .eq(HeOrderGiftInfoPO::getDeleted, 0)
                .eq(StringUtils.isNotBlank(query.getGiftSn()), HeOrderGiftInfoPO::getGiftSn, query.getGiftSn())
                .eq(StringUtils.isNotBlank(query.getGiftOrderSn()), HeOrderGiftInfoPO::getGiftOrderSn, query.getGiftOrderSn())
                .in(CollectionUtils.isNotEmpty(query.getCreatorList()), HeOrderGiftInfoPO::getCreateBy, query.getCreatorList())
                .eq(StringUtils.isNotBlank(query.getOrderSn()), HeOrderGiftInfoPO::getOrderSn, query.getOrderSn())
                .in(CollectionUtils.isNotEmpty(query.getClientUidList()), HeOrderGiftInfoPO::getClientUid, query.getClientUidList())
                .in(CollectionUtils.isNotEmpty(query.getStoreIdList()), HeOrderGiftInfoPO::getStoreId, query.getStoreIdList())
                .in(CollectionUtils.isNotEmpty(query.getStatusList()), HeOrderGiftInfoPO::getStatus, query.getStatusList())
                .in(CollectionUtils.isNotEmpty(query.getOrderGiftReasonTypeList()), HeOrderGiftInfoPO::getGiftReasonType, query.getOrderGiftReasonTypeList())
                .in(Objects.nonNull(query.getBasicUid()), HeOrderGiftInfoPO::getBasicUid, query.getBasicUid())
                .notIn(CollectionUtils.isNotEmpty(query.getTestStoreIdList()), HeOrderGiftInfoPO::getStoreId, query.getTestStoreIdList())
                .ge(Objects.nonNull(query.getBizDateStart()) && query.getDateType() == 0, HeOrderGiftInfoPO::getCreatedAt, query.getBizDateStart())
                .le(Objects.nonNull(query.getBizDateEnd()) && query.getDateType() == 0, HeOrderGiftInfoPO::getCreatedAt, query.getBizDateEnd())
                .ge(Objects.nonNull(query.getBizDateStart()) && query.getDateType() == 1, HeOrderGiftInfoPO::getApproveFinish, query.getBizDateStart())
                .le(Objects.nonNull(query.getBizDateEnd()) && query.getDateType() == 1, HeOrderGiftInfoPO::getApproveFinish, query.getBizDateEnd())
                .ge(Objects.nonNull(query.getBizDateStart()) && query.getDateType() == 2, HeOrderGiftInfoPO::getReleaseTime, query.getBizDateStart())
                .le(Objects.nonNull(query.getBizDateEnd()) && query.getDateType() == 2, HeOrderGiftInfoPO::getReleaseTime, query.getBizDateEnd())
                .orderByDesc(query.getDateType() == 0, HeOrderGiftInfoPO::getCreatedAt)
                .orderByDesc(query.getDateType() == 1, HeOrderGiftInfoPO::getApproveFinish)
                .orderByDesc(query.getDateType() == 2, HeOrderGiftInfoPO::getReleaseTime)
                .orderByDesc(HeOrderGiftInfoPO::getId);
    }
}

package com.stbella.order.infrastructure.repository.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.order.domain.order.month.entity.HeOrderApproveRecordEntity;
import com.stbella.order.domain.repository.OrderApproveRecordRepository;
import com.stbella.order.infrastructure.repository.converter.OrderApproveRecordConverter;
import com.stbella.order.infrastructure.repository.mapper.saas.HeOrderApproveRecordMapper;
import com.stbella.order.infrastructure.repository.po.HeOrderApproveRecordPO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Repository
public class OrderApproveRecordRepositoryImpl extends ServiceImpl<HeOrderApproveRecordMapper, HeOrderApproveRecordPO> implements OrderApproveRecordRepository {


    @Resource
    private OrderApproveRecordConverter orderApproveRecordConverter;


    @Override
    public List<HeOrderApproveRecordEntity> getApproveRecordListByOrderIdList(List<Integer> orderIdList) {
        if (CollectionUtil.isEmpty(orderIdList)){
            return new ArrayList<>();
        }
        List<HeOrderApproveRecordPO> heOrderApproveRecord = this.baseMapper.selectList(new LambdaQueryWrapper<HeOrderApproveRecordPO>().in(HeOrderApproveRecordPO::getOrderId, orderIdList));
        if (CollectionUtil.isEmpty(heOrderApproveRecord)){
            return new ArrayList<>();
        }
        return orderApproveRecordConverter.OrderApproveRecordListToEntity(heOrderApproveRecord);
    }

    @Override
    public List<HeOrderApproveRecordEntity> getApproveRecordListByOrderId(Integer orderId) {
        List<HeOrderApproveRecordPO> heOrderApproveRecord = this.baseMapper.selectList(
                new LambdaQueryWrapper<HeOrderApproveRecordPO>().eq(HeOrderApproveRecordPO::getOrderId, orderId));

        if (CollectionUtil.isEmpty(heOrderApproveRecord)) {
            return new ArrayList<>();
        }
        return orderApproveRecordConverter.OrderApproveRecordListToEntity(heOrderApproveRecord);
    }

    @Override
    public HeOrderApproveRecordEntity selectApproveRecordPhoneByOrderRefundId(Integer id) {
        return this.baseMapper.selectApproveRecordPhoneByOrderRefundId(id);
    }

    @Override
    public HeOrderApproveRecordEntity selectApproveRecordPhoneById(Integer id) {
        return this.baseMapper.selectApproveRecordPhoneById(id);
    }


    @Override
    public HeOrderApproveRecordEntity getPageIdByRecordId(Integer recordId) {
        return this.baseMapper.selectApproveRecordByRecordId(recordId);
    }
}


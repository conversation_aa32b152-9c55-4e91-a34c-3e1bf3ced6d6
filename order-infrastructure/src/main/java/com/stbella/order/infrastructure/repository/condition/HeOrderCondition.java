package com.stbella.order.infrastructure.repository.condition;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class HeOrderCondition {

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("商品类目")
    private Integer goodsCategoryId;

    @ApiModelProperty("战区")
    private Integer warZone;

    @ApiModelProperty(value = "门店品牌 1-圣贝拉，2小贝拉,3-Bella Villa,100-艾屿")
    private List<Integer> storeIdList;

    @ApiModelProperty("用户Id")
    private String basicUid;


}

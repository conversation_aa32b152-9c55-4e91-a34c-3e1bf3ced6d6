package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.month.entity.AheadOutRoomEntity;
import com.stbella.order.infrastructure.repository.po.AheadOutRoomPO;
import com.stbella.order.server.order.month.res.SimpleAheadOutRoomVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-12 11:28
 */
@Mapper(componentModel = "spring")
public interface AheadOutRoomConverter {

    /**
     * @param po
     * @return {@link AheadOutRoomEntity}
     */
    AheadOutRoomEntity po2AheadOutRoomEntity(AheadOutRoomPO po);

    /**
     * @param entity
     * @return {@link AheadOutRoomPO}
     */
    AheadOutRoomPO entity2AheadOutRoomPo(AheadOutRoomEntity entity);

    List<AheadOutRoomEntity> po2AheadOutRoomEntityList(List<AheadOutRoomPO> po);

    SimpleAheadOutRoomVO entity2SimpleAheadOutRoomVO(AheadOutRoomEntity entity);

}

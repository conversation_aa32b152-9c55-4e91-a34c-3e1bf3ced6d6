package com.stbella.order.infrastructure.repository.mapper.saas;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.stbella.order.infrastructure.repository.po.TherapistSchedule;

public interface TherapistScheduleMapper extends BaseMapper<TherapistSchedule> {
    int deleteByPrimaryKey(Long id);

    int insert(TherapistSchedule record);

    int insertSelective(TherapistSchedule record);

    TherapistSchedule selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TherapistSchedule record);

    int updateByPrimaryKey(TherapistSchedule record);
}
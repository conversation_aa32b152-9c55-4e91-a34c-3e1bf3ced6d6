package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.entity.ExternalProductionOrderEntity;
import com.stbella.order.infrastructure.repository.po.ExternalProductionOrderPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <p>
 * 产康馆外订单转换器
 * </p>
 *
 * <AUTHOR> @since 2024-08-15
 */
@Mapper(componentModel = "spring")
public interface ExternalProductionOrderConverter {

    ExternalProductionOrderConverter INSTANCE = Mappers.getMapper(ExternalProductionOrderConverter.class);

    /**
     * PO转Entity
     *
     * @param po PO对象
     * @return Entity对象
     */
    ExternalProductionOrderEntity poToEntity(ExternalProductionOrderPO po);

    /**
     * Entity转PO
     *
     * @param entity Entity对象
     * @return PO对象
     */
    ExternalProductionOrderPO entityToPo(ExternalProductionOrderEntity entity);

    /**
     * PO列表转Entity列表
     *
     * @param poList PO列表
     * @return Entity列表
     */
    List<ExternalProductionOrderEntity> poListToEntityList(List<ExternalProductionOrderPO> poList);

    /**
     * Entity列表转PO列表
     *
     * @param entityList Entity列表
     * @return PO列表
     */
    List<ExternalProductionOrderPO> entityListToPoList(List<ExternalProductionOrderEntity> entityList);
}

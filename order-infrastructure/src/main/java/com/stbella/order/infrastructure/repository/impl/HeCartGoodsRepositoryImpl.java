package com.stbella.order.infrastructure.repository.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.order.entity.HeCartGoodsEntity;
import com.stbella.order.domain.repository.HeCartGoodsRepository;
import com.stbella.order.infrastructure.repository.converter.CartGoodsPOConverter;
import com.stbella.order.infrastructure.repository.mapper.saas.HeCartGoodsMapper;
import com.stbella.order.infrastructure.repository.po.HeCartGoodsPO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Repository
public class HeCartGoodsRepositoryImpl extends ServiceImpl<HeCartGoodsMapper, HeCartGoodsPO> implements HeCartGoodsRepository {

    @Resource
    private CartGoodsPOConverter cartGoodsPOConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdateBatch(List<HeCartGoodsEntity> cartGoodsEntityList) {
        List<HeCartGoodsPO> cartGoodsPOList = BeanMapper.mapList(cartGoodsEntityList, HeCartGoodsPO.class);
        //设置创建时间，删除等默认值
        cartGoodsPOList.forEach(cartGoodsPO -> {
            cartGoodsPO.setAddTime(new Date());
            cartGoodsPO.setUpdateTime(new Date());
            cartGoodsPO.setDeleted(0);
        });
        return saveOrUpdateBatch(cartGoodsPOList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }

        return removeByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByCartId(Integer cartId) {
        if (ObjectUtil.isNull(cartId)) {
            return false;
        }

        LambdaQueryWrapper<HeCartGoodsPO> queryWrapper = Wrappers.lambdaQuery(HeCartGoodsPO.class)
                .eq(HeCartGoodsPO::getCartId, cartId);

        return remove(queryWrapper);
    }

    @Override
    public List<HeCartGoodsEntity> queryListByCartId(Integer cartId) {
        LambdaQueryWrapper<HeCartGoodsPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HeCartGoodsPO::getCartId, cartId);
        List<HeCartGoodsPO> poList = list(queryWrapper);
        //所有子商品 分组
        Map<String, List<HeCartGoodsPO>> subPOMap = poList.stream()
                .filter(po -> po.getParentSn().length() > 0)
                .collect(Collectors.groupingBy(HeCartGoodsPO::getParentSn));

        List<HeCartGoodsEntity> parentGoodsList = poList.stream().filter(po -> po.getParentSn().length() == 0)
                .map(po -> cartGoodsPOConverter.po2Entity(po))
                .collect(Collectors.toList());

        parentGoodsList.forEach(parentGoods -> {
            List<HeCartGoodsEntity> subGoodsList = cartGoodsPOConverter.poList2EntityList(subPOMap.getOrDefault(parentGoods.getItermSn(), new ArrayList<>()));
            parentGoods.setSubGoodsList(subGoodsList);
        });

        return parentGoodsList;
    }

    @Override
    public List<HeCartGoodsEntity> queryList(Integer cartId) {
        if (ObjectUtil.isNull(cartId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HeCartGoodsPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HeCartGoodsPO::getCartId, cartId);
        List<HeCartGoodsPO> poList = list(queryWrapper);
        return cartGoodsPOConverter.poList2EntityList(poList);
    }

}

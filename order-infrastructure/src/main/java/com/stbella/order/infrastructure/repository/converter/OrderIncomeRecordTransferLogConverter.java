package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.month.entity.HeOrderIncomeRecordTransferLogEntity;
import com.stbella.order.infrastructure.repository.po.HeOrderIncomeRecordTransferLog;
import org.mapstruct.Mapper;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20 14:09
 */
@Mapper(componentModel = "spring")
public interface OrderIncomeRecordTransferLogConverter {

    HeOrderIncomeRecordTransferLog entity2PO(HeOrderIncomeRecordTransferLogEntity entity);
}

package com.stbella.order.infrastructure.repository.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.order.production.UserProductionAmountPiLogEntity;
import com.stbella.order.domain.repository.HeUserProductionAmountPiLogRepository;
import com.stbella.order.infrastructure.repository.mapper.saas.HeUserProductionAmountPiLogMapper;
import com.stbella.order.infrastructure.repository.po.HeUserProductionAmountPiLogPO;
import org.springframework.stereotype.Repository;

@Repository
public class HeUserProductionAmountPiLogRepositoryImpl extends ServiceImpl<HeUserProductionAmountPiLogMapper, HeUserProductionAmountPiLogPO> implements HeUserProductionAmountPiLogRepository {

    @Override
    public Boolean saveOne(UserProductionAmountPiLogEntity userProductionAmountPiLogEntity) {
        return this.save(BeanMapper.map(userProductionAmountPiLogEntity, HeUserProductionAmountPiLogPO.class));
    }

    @Override
    public Boolean update(UserProductionAmountPiLogEntity userProductionAmountPiLogEntity) {
        return this.updateById(BeanMapper.map(userProductionAmountPiLogEntity, HeUserProductionAmountPiLogPO.class));
    }

    @Override
    public UserProductionAmountPiLogEntity queryByOrderIdAndType(Long orderId, Integer type) {
        HeUserProductionAmountPiLogPO one = this.getOne(new LambdaQueryWrapper<HeUserProductionAmountPiLogPO>().eq(HeUserProductionAmountPiLogPO::getOrderId, orderId).eq(HeUserProductionAmountPiLogPO::getType, type));
        if (ObjectUtil.isNotEmpty(one)) {
            return BeanMapper.map(one, UserProductionAmountPiLogEntity.class);
        }
        return null;
    }

}

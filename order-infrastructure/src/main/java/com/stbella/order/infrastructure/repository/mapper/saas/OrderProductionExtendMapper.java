package com.stbella.order.infrastructure.repository.mapper.saas;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.stbella.order.domain.order.production.OrderProductionAppointmentEntity;
import com.stbella.order.infrastructure.repository.po.OrderProductionExtend;
import com.stbella.order.server.order.ProductionClientRealPaidModel;
import com.stbella.order.server.order.ProductionOrderGoodsModel;
import com.stbella.order.server.order.ProductionTherapistRealPaidModel;
import com.stbella.order.server.order.order.req.OrderExportReq;
import com.stbella.order.server.order.order.req.ProductionReq;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface OrderProductionExtendMapper extends BaseMapper<OrderProductionExtend> {
    int deleteByPrimaryKey(Integer id);

    int save(OrderProductionExtend record);

    OrderProductionExtend selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(OrderProductionExtend record);

    int updateByPrimaryKey(OrderProductionExtend record);

    /**
     * 获取用户不同类型下的可用资产信息
     *
     * @param basicUid 用户ID
     * @return List<OrderProductionExtend>
     */
    default List<OrderProductionExtend> selectPropertyByBasicUid(Long basicUid) {
        return selectList(new LambdaQueryWrapper<OrderProductionExtend>()
                .eq(OrderProductionExtend::getBasicId, basicUid)
                .eq(OrderProductionExtend::getStatus, 1));
    }

    ;

    /**
     * 根据id列表批量失效过期的产康资产
     *
     * @param productionIds
     * @return
     */
    int expiredByProductionIds(@Param("productionIds") List<Integer> productionIds);

    /**
     * 查询产康订单商品列表
     * @param req
     * @return
     */
    List<ProductionOrderGoodsModel> queryProductionOrderGoodsList( @Param("req") OrderExportReq req);

    /**
     * 查询礼赠实付金额
     */
    BigDecimal getGiftRealPaid(@Param("req") ProductionReq req);

    /**
     * 查询礼赠实付金额(三方)
     */
    BigDecimal getGiftRealPaidThird(@Param("req") ProductionReq req);

    /**
     * 查询单项实付金额
     */
    BigDecimal getProductionRealPaid(@Param("req") ProductionReq req);

    /**
     * 查询单项实付金额(三方)
     */
    BigDecimal getProductionRealPaidThird(@Param("req") ProductionReq req);

    /**
     * 查询通次卡实付金额
     */
    BigDecimal getProductionCardRealPaid(@Param("req") ProductionReq req);

    /**
     * 查询通次卡实付金额(三方)
     */
    BigDecimal getProductionCardRealPaidThird(@Param("req") ProductionReq req);

    /**
     * 未核销金额
     */
    BigDecimal getUnwrittenSummary(@Param("req") ProductionReq req);

    /**
     * 产康师核销金额
     */
    List<ProductionTherapistRealPaidModel> getTherapistRealPaid(@Param("req") ProductionReq req);

    /**
     * 门店客户核销金额
     */
    List<ProductionClientRealPaidModel> getClientRealPaid(@Param("req") ProductionReq req);

    /**
     * 门店客户未核销金额
     */
    List<ProductionClientRealPaidModel> getClientUnwrittenSummary(@Param("req") ProductionReq req);

    /**
     * 产康师核销金额(三方)
     */
    List<OrderProductionAppointmentEntity> getTherapistRealPaidThird(@Param("req") ProductionReq req);

    /**
     * 门店客户核销金额(三方)
     */
    List<OrderProductionAppointmentEntity> getClientRealPaidThird(@Param("req") ProductionReq req);
}
package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.entity.ExternalProductionOrderPerformanceEntity;
import com.stbella.order.infrastructure.repository.po.ExternalProductionOrderPerformancePO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <p>
 * 产康馆外订单业绩操作记录转换器
 * </p>
 *
 * <AUTHOR> @since 2024-08-15
 */
@Mapper(componentModel = "spring")
public interface ExternalProductionOrderPerformanceConverter {

    ExternalProductionOrderPerformanceConverter INSTANCE = Mappers.getMapper(ExternalProductionOrderPerformanceConverter.class);

    /**
     * PO转Entity
     *
     * @param po PO对象
     * @return Entity对象
     */
    ExternalProductionOrderPerformanceEntity poToEntity(ExternalProductionOrderPerformancePO po);

    /**
     * Entity转PO
     *
     * @param entity Entity对象
     * @return PO对象
     */
    ExternalProductionOrderPerformancePO entityToPo(ExternalProductionOrderPerformanceEntity entity);

    /**
     * PO列表转Entity列表
     *
     * @param poList PO列表
     * @return Entity列表
     */
    List<ExternalProductionOrderPerformanceEntity> poListToEntityList(List<ExternalProductionOrderPerformancePO> poList);

    /**
     * Entity列表转PO列表
     *
     * @param entityList Entity列表
     * @return PO列表
     */
    List<ExternalProductionOrderPerformancePO> entityListToPoList(List<ExternalProductionOrderPerformanceEntity> entityList);
}

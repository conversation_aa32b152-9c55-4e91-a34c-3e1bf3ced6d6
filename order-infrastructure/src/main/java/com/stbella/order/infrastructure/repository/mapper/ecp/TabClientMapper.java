package com.stbella.order.infrastructure.repository.mapper.ecp;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.stbella.order.infrastructure.repository.po.TabClientPO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 客户表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2022-10-27
 */
public interface TabClientMapper extends BaseMapper<TabClientPO> {

    /**
     * 根据id更新手机号
     * @param id
     * @param phone
     * @return
     */
    int updatePhoneById(@Param("id") Integer id, @Param("phone") String phone);

}

package com.stbella.order.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.order.month.entity.HeOrderRefundDisabledProductEntity;
import com.stbella.order.domain.repository.HeOrderRefundDisabledProductRepository;
import com.stbella.order.infrastructure.repository.mapper.saas.HeOrderRefundDisabledProductMapper;
import com.stbella.order.infrastructure.repository.po.HeOrderRefundDisabledProduct;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2025-04-25
 */
@Service
public class HeOrderRefundDisabledProductRepositoryImpl extends ServiceImpl<HeOrderRefundDisabledProductMapper, HeOrderRefundDisabledProduct> implements HeOrderRefundDisabledProductRepository {

    @Override
    public void saveList(List<HeOrderRefundDisabledProductEntity> orderRefundDisabledProductEntityList) {
        this.saveBatch(BeanMapper.mapList(orderRefundDisabledProductEntityList, HeOrderRefundDisabledProduct.class));
    }

    @Override
    public List<HeOrderRefundDisabledProductEntity> queryByRefundId(Long refundOrderId) {
        LambdaQueryWrapper<HeOrderRefundDisabledProduct> queryWrapper = Wrappers.lambdaQuery(HeOrderRefundDisabledProduct.class)
                .eq(HeOrderRefundDisabledProduct::getRefundId, refundOrderId);
        return BeanMapper.mapList(this.list(queryWrapper), HeOrderRefundDisabledProductEntity.class);
    }
}

package com.stbella.order.infrastructure.repository.mapper.saas;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.stbella.order.infrastructure.repository.po.HeTaskPO;
import com.stbella.order.server.contract.dto.Old2NewOrderDTO;
import com.stbella.order.server.contract.dto.OrderV1V2RelatedDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 任务表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-04-20
 */
public interface HeTaskMapper extends BaseMapper<HeTaskPO> {

    OrderV1V2RelatedDTO getOrderV1V2RelatedDTO(@Param("orderId") Integer orderId);

    List<Old2NewOrderDTO> getTaskHisProcess(@Param("taskId") Long taskId);

    List<Old2NewOrderDTO> getTaskProcessByProjectId(@Param("projectIdList") List<Long> projectIdList);
}

package com.stbella.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 合同参数模版表,新版E签宝
 * </p>
 *
 * <AUTHOR> @since 2021-11-03
 */
@Data
@Accessors(chain = true)
@TableName("he_esign_template_param")
@ApiModel(value = "EsignTemplateParam对象", description = "合同参数模版表,新版E签宝")
public class MonthEsignTemplateParamPO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "订单类型")
    private Integer orderType;

    @ApiModelProperty(value = "参数名称")
    private String name;

    @ApiModelProperty(value = "参数标识符,和合同模版中的占位符一一对应")
    private String mark;

    @ApiModelProperty(value = "排序号,正序排序")
    private Integer sort;

    @ApiModelProperty(value = "所属条款。")
    private Integer terms;

    @ApiModelProperty(value = "所属项")
    private Integer item;

    /**
     * 逻辑删除标志
     */
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    @ApiModelProperty(value = "管理合同模板 主键id, 多个逗号分隔使用不用合同模板id")
    private String esignTemplateId;
}

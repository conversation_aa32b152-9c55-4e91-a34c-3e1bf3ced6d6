package com.stbella.order.infrastructure.repository.condition;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.stbella.order.common.constant.TableFieldConstant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 签约记录表
 * </p>
 *
 * <AUTHOR> @since 2021-11-03
 */
@Data
@ApiModel(value = "ContractSignRecord对象", description = "签约记录表")
public class MonthContractSignRecordCondition {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "签约记录表")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "客户ID（ecp库的tab_client表主键）")
    private Integer clientUid;

    @ApiModelProperty(value = "销售员id（ecp库user表主键id）")
    private Integer staffId;

    @ApiModelProperty(value = "门店ID(ecp库cfg_store表的主键id)")
    private Long storeId;

    @ApiModelProperty(value = "签约类型：1订单， .......（未来可增加其他签约类型）")
    private Integer guideType;

    @ApiModelProperty(value = "关联id（签约类型为订单，此处为订单id）")
    private Long guideId;

    @ApiModelProperty(value = "签字区域：图片格式的签约内容：URL链接")
    private String contractImageSignature;

    @ApiModelProperty(value = "整个html的图片格式：签约内容：URL链接")
    private String contractImageHtml;

    @ApiModelProperty(value = "签约模版url")
    private String contractTempUrl;

    @ApiModelProperty(value = "对应task_id 目前只附件合同有效")
    private Long taskId;

    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @ApiModelProperty(value = "法大大合同下载链接")
    private String downloadUrl;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "法大大合同查看链接")
    private String viewpdfUrl;

    @ApiModelProperty(value = "合同状态 0 =未签署 1=签署中 2=已归档")
    private Integer contractStatus;

    @ApiModelProperty(value = "合同类型 1=E签宝合同 2=旧合同 ")
    private Integer contractType;

    @ApiModelProperty(value = "模板类型 1=订单类 2=附件类")
    private Integer templateType;

    @ApiModelProperty(value = TableFieldConstant.templateContractType)
    private Integer templateContractType;

    @ApiModelProperty(value = "模板id")
    private Long templateId;

    @ApiModelProperty(value = "是否删除：1是，0否")
    private Boolean isDelete;

    @ApiModelProperty(value = "创建时间")
    private Long createdAt;

    @ApiModelProperty(value = "最近更新时间")
    private Long updatedAt;

    @ApiModelProperty(value = "E签宝签署流程id")
    private String esignFlowId;

    @ApiModelProperty(value = "E签宝文件id。")
    private String esignFileId;

    @ApiModelProperty(value = "E签宝文件名称")
    private String esignFileName;

    @ApiModelProperty(value = "E签宝合同查看长链接")
    private String contractLongUrl;

    @ApiModelProperty(value = "E签宝合同查看短链接")
    private String contractShortUrl;

    /**
     * 合同签订入口0=未知;1=h5;2=微信;3=支付宝
     */
    @ApiModelProperty(value = "合同签订入口0=未知;1=h5;2=微信;3=支付宝")
    private Integer signFrom;


}

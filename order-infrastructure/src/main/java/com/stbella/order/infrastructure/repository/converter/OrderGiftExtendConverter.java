package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.domain.order.production.OrderGiftExtendEntity;
import com.stbella.order.infrastructure.repository.po.OrderGiftExtend;
import com.stbella.order.server.order.month.res.OrderGiftCacheByUserVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

@Mapper(componentModel = "spring", imports = {AmountChangeUtil.class})
public interface OrderGiftExtendConverter {

    /**
     * po -> entity
     *
     * @param entity
     * @return com.stbella.order.domain.order.production.OrderGiftExtendEntity
     * @throws
     * <AUTHOR>
     * @date 2022/10/18 13:12
     * @since 1.0.0
     */
    OrderGiftExtendEntity po2EntityForProductionVerificationLog(OrderGiftExtend entity);

    /**
     * entity -> PO
     */
    OrderGiftExtend orderGiftExtendEntity2PO(OrderGiftExtendEntity entity);

    /**
     * entityList -> poList
     */
    List<OrderGiftExtend> orderGiftExtendEntityList2POList(List<OrderGiftExtendEntity> entityList);


    @Mappings({
            @Mapping(target = "cost", expression = "java( Integer.valueOf(AmountChangeUtil.changeY2F(String.valueOf(orderGiftCacheByUserVO.getCostPrice()))))"),
            @Mapping(target = "price", expression = "java( Integer.valueOf(AmountChangeUtil.changeY2F(String.valueOf(orderGiftCacheByUserVO.getPrice()))))")
    })
    OrderGiftExtendEntity orderGiftCacheByUserVOS2Entity(OrderGiftCacheByUserVO orderGiftCacheByUserVO);

    /**
     * 批量转换vos -> entityList
     *
     * @param orderGiftCacheByUserVOS
     * @return List<OrderGiftExtendEntity>
     */
    List<OrderGiftExtendEntity> orderGiftCacheByUserVOS2EntityList(List<OrderGiftCacheByUserVO> orderGiftCacheByUserVOS);

    List<OrderGiftExtendEntity> orderGiftEntityByPoList(List<OrderGiftExtend> poList);
}

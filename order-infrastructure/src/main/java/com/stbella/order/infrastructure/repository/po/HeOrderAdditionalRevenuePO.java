package com.stbella.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.*;
import com.stbella.order.server.order.month.handler.ListConfigHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.JdbcType;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 订单费用加收项
 * </p>
 *
 * <AUTHOR> @since 2022-10-27
 */
@Data
@Accessors(chain = true)
@TableName("he_order_additional_revenue")
@ApiModel(value = "HeOrderAdditionalRevenuePO对象", description = "订单费用加收项")
public class HeOrderAdditionalRevenuePO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "是否开启（目前只有节假日）：0：不开启；1：开启")
    private Integer isFestival;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "收费项名称")
    private String goodsName;

    @ApiModelProperty(value = "门店id")
    private Integer storeId;

    @ApiModelProperty(value = "关联订单id")
    private Integer orderId;

    @ApiModelProperty(value = "1=多胞胎费用 2=续住费用 3=变更房型 ")
    private Integer type;

    @ApiModelProperty(value = "应收")
    private Integer price;

    @ApiModelProperty(value = "原价")
    private Integer cost;

    @ApiModelProperty(value = "成本")
    private Integer costPrice;

    @ApiModelProperty(value = "具体信息")
    private String content;

    @ApiModelProperty(value = "赠送来源 1=小助手 2=后台  默认小助手 3=购买补录")
    private Integer source;

    @ApiModelProperty(value = "房型ID")
    private Integer roomId;

    @ApiModelProperty(value = "房型名称")
    private String roomName;

    @ApiModelProperty(value = "天数")
    private Integer days;

    @ApiModelProperty(value = "生效日期(年-月-日,年月日")
    @TableField(value = "days_list", typeHandler = ListConfigHandler.class, jdbcType = JdbcType.VARCHAR, javaType = true)
    private List<String> daysList;

    @ApiModelProperty(value = "胎数")
    private Integer embryoNumber;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;
    @ApiModelProperty("修改时间")
    private Date gmtModified;

    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty("逻辑删除标识 0-未删除 1-已删除")
    private Integer deleted;
}

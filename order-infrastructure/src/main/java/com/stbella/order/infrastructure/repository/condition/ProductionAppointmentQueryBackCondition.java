package com.stbella.order.infrastructure.repository.condition;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @description: 【后台】预约单列表筛选
 * @author: Seven
 * @time: 2022/10/9 14:06
 */
@Data
public class ProductionAppointmentQueryBackCondition extends BaseCondition {

    @ApiModelProperty("门店ID")
    private Integer storeId;

    @ApiModelProperty("门店ids")
    private List<Integer> storeIds;

    @ApiModelProperty("预约单号/客户姓名/手机号")
    private String keyword;

    @ApiModelProperty("服务开始时间")
    private Date serveStart;

    @ApiModelProperty("服务结束时间")
    private Date serveEnd;

    @ApiModelProperty("产康师名称")
    private String therapistName;

    @ApiModelProperty("产康师id")
    private Long therapistId;

    @ApiModelProperty("服务产康师id")
    private Long serviceTherapistId;

    @ApiModelProperty("客户名称")
    private String clientName;

    @ApiModelProperty("客户id")
    private Integer clientId;

    @ApiModelProperty("客户basicId")
    private Integer basicId;

    @ApiModelProperty("核销状态")
    private Integer verificationState;

    @ApiModelProperty("预约方式：1=客户预约;2=代客预约")
    private Integer bookType;

    @ApiModelProperty("产康师ID列表， 用来产康师筛选")
    private List<Long> therapistIdList;

    @ApiModelProperty("预约单类型 1=标准;2=补单")
    private Integer appointmentType;

    @ApiModelProperty("预约单ids")
    private List<Long> appointmentIds;

    @ApiModelProperty("项目类型0=自营;1=三方")
    private Integer serveType;

    @ApiModelProperty(value = "使用门店权限")
    private Boolean storeAuth;
}

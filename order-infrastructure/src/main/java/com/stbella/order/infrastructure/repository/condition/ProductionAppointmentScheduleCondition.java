package com.stbella.order.infrastructure.repository.condition;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @description: 手工费
 * @author: Seven
 * @time: 2022/10/11 17:57
 */
@Data
public class ProductionAppointmentScheduleCondition extends BaseCondition {

    /**
     * 兼容c端以及pi端 产康日程接口
     * pi端用therapistId ｜ therapistPhone 查找
     * c端用clientId
     */

    @ApiModelProperty("产康师ID")
    private Long therapistId;

    @ApiModelProperty("客户ID")
    private Long clientId;

    @ApiModelProperty("客户basicUid")
    private Long basicId;

    @ApiModelProperty("服务开始时间")
    @NotNull(message = "服务开始时间不能为空")
    private Date serveStart;

    @ApiModelProperty("服务结束时间")
    @NotNull(message = "服务结束时间不能为空")
    private Date serveEnd;

    @ApiModelProperty("终端类型 0）后台 1）pi 2）c端")
    private Integer terminalType;

}

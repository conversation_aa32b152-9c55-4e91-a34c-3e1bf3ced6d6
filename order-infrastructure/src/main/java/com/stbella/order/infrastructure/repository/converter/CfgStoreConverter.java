package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.month.entity.CfgStoreEntity;
import com.stbella.order.domain.order.month.entity.SettingRoomTypeEntity;
import com.stbella.order.domain.order.month.entity.SettingStoreTargetEntity;
import com.stbella.order.infrastructure.repository.po.SettingRoomTypePo;
import com.stbella.order.infrastructure.repository.po.SettingStoreTargetPo;
import com.stbella.store.core.vo.res.store.StoreBaseVO;
import com.stbella.store.core.vo.res.store.StoreVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CfgStoreConverter {


    SettingStoreTargetEntity po2SettingStoreTargetEntity(SettingStoreTargetPo po);

    SettingRoomTypeEntity po2SettingRoomTypeEntity(SettingRoomTypePo po);

    /**
     * po -> entity
     *
     * @param Vo 实体
     * @return GoodsEntity
     */
    @Mappings({
            @Mapping(target = "storeId", source = "id"),
    })
    CfgStoreEntity vo2CfgStoreEntity(StoreVO Vo);

    /**
     * po -> entity
     *
     * @param Vo 实体
     * @return GoodsEntity
     */
    CfgStoreEntity baseVo2CfgStoreEntity(StoreBaseVO Vo);

    /**
     * po -> entity
     *
     * @param Vo list 实体
     * @return GoodsEntity
     */
    List<CfgStoreEntity> baseVo2CfgStoreEntityList(List<StoreBaseVO> voList);

}

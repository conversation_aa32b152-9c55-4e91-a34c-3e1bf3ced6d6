package com.stbella.order.infrastructure.repository.mapper.saas;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.stbella.order.infrastructure.repository.po.ProductionInstrStoreExt;

public interface ProductionInstrStoreExtMapper extends BaseMapper<ProductionInstrStoreExt> {
    int deleteByPrimaryKey(Long id);

    int insert(ProductionInstrStoreExt record);

    int insertSelective(ProductionInstrStoreExt record);

    ProductionInstrStoreExt selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductionInstrStoreExt record);

    int updateByPrimaryKey(ProductionInstrStoreExt record);
}
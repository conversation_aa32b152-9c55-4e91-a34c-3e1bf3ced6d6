package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.month.entity.HeOrderBailorSnapshotEntity;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.domain.order.month.entity.TabClientEntity;
import com.stbella.order.domain.order.month.entity.UserEntity;
import com.stbella.order.infrastructure.repository.po.TabClientPO;
import com.stbella.order.infrastructure.repository.po.UserPO;
import com.stbella.order.server.order.month.res.OrderMonthClientBailorVO;
import com.stbella.order.server.order.month.res.OrderMonthClientVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TabClientConverter {

    List<TabClientEntity> poListToEntityList(List<TabClientPO> poList);

    OrderMonthClientVO clientEntityToClientVO(HeOrderUserSnapshotEntity heOrderUserSnapshotEntity);

    OrderMonthClientBailorVO clientBailorEntityToClientBailorVO(HeOrderBailorSnapshotEntity heOrderBailorSnapshotEntity);

    TabClientEntity tabClientPO2Entity(TabClientPO tabClientPO);

    UserEntity userPO2Entity(UserPO userPO);

    List<UserEntity> userPO2EntityList(List<UserPO> userPO);

    OrderMonthClientVO tabClientEntityToClientVO(TabClientEntity tabClientEntity);

}

package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.month.entity.CfgDistrictEntity;
import com.stbella.order.infrastructure.repository.po.CfgDistrictPO;
import com.stbella.order.server.contract.res.AddressVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <AUTHOR>
 * @description: OrderNutritionConvert
 * @date 2022/1/12 2:30 下午
 */
@Mapper(componentModel = "spring")
public interface DistrictConvert {
    CfgDistrictEntity po2Entity(CfgDistrictPO po);

    List<CfgDistrictEntity> po2EntityList(List<CfgDistrictPO> poList);

    @Mappings({
            @Mapping(target = "nameSuffix", expression = "java(entity.getName()+ entity.getSuffix())")
    })
    AddressVO entity2VO(CfgDistrictEntity entity);

    List<AddressVO> entity2VOList(List<CfgDistrictEntity> entityList);
}

package com.stbella.order.infrastructure.repository.converter;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stbella.asset.api.enums.TradeType;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import com.stbella.order.common.enums.month.RefundRecordPayStatusEnum;
import com.stbella.order.common.enums.month.RefundRecordPayStatusSimpleEnum;
import com.stbella.order.common.utils.AmountChangeUtil;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.domain.order.month.entity.HeOrderRefundApplyEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundApplyGoodsEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.order.month.entity.HeOrderRefundReviewEntity;
import com.stbella.order.infrastructure.repository.po.HeOrderRefundApplyGoodsPO;
import com.stbella.order.infrastructure.repository.po.HeOrderRefundApplyPO;
import com.stbella.order.infrastructure.repository.po.HeOrderRefundPO;
import com.stbella.order.infrastructure.repository.po.HeOrderRefundReviewPO;
import com.stbella.order.server.order.month.req.OfflineRemittanceRequest;
import com.stbella.order.server.order.month.res.DepositOrderRecordVO;
import com.stbella.order.server.order.month.res.OrderRefundVO;
import com.stbella.order.server.order.month.utils.RMBUtils;
import org.mapstruct.Mapper;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Mapper(componentModel = "spring")
public interface OrderRefundConverter {
    HeOrderRefundEntity orderRefundPoToEntity(HeOrderRefundPO heOrderRefundPO);

    HeOrderRefundPO heOrderRefundEntityToPO(HeOrderRefundEntity entity);

    List<HeOrderRefundPO> heOrderRefundEntityListToPOList(List<HeOrderRefundEntity> entityList);

    List<HeOrderRefundEntity> orderRefundPoListToEntity(List<HeOrderRefundPO> refundList);

    Page<HeOrderRefundEntity> page2Entity(Page<HeOrderRefundPO> poPage);

    HeOrderRefundReviewPO heOrderRefundReviewEntity2PO(HeOrderRefundReviewEntity heOrderRefundReviewEntity);

    default List<DepositOrderRecordVO> entityList2DepositOrderRecordVO(List<HeOrderRefundEntity> entityList) {
        if (CollectionUtil.isEmpty(entityList)) {
            return new ArrayList<>();
        }
        return entityList.stream().map(entity -> {
            DepositOrderRecordVO vo = new DepositOrderRecordVO();
            vo.setAmount(AmountChangeUtil.changeF2Y(entity.getApplyAmount()));
            vo.setGmtCreate(DateUtils.getDateFromLong(entity.getCreatedAt()));
            Integer status = entity.getStatus();
            if (status == RefundRecordPayStatusSimpleEnum.REFUND_RECORD_2.getCode() || status == RefundRecordPayStatusSimpleEnum.REFUND_RECORD_5.getCode()) {
                if (status == RefundRecordPayStatusSimpleEnum.REFUND_RECORD_5.getCode()) {
                    vo.setTitle("收款-订单退款-退款失败");
                } else {
                    vo.setTitle("收款-订单退款-审批拒绝");
                }
                vo.setTradeAffect("+");
                vo.setTradeType(TradeType.REFUND_TO_BALANCE.getCode());
                vo.setGmtCreate(DateUtils.getDateFromLong(entity.getUpdatedAt()));
            } else {
                vo.setTitle("订单退款-申请审批");
                vo.setTradeAffect("-");
                vo.setTradeType(TradeType.BALANCE_PAY.getCode());
                vo.setGmtCreate(DateUtils.getDateFromLong(entity.getCreatedAt()));
            }
            vo.setShowTitle(vo.getTitle());
            return vo;
        }).collect(Collectors.toList());
    }

    HeOrderRefundApplyEntity refundEntity2RefundApplyEntity(HeOrderRefundEntity parentRefund);

    HeOrderRefundApplyPO orderRefundApplyEntity2OrderRefundApplyPo(HeOrderRefundApplyEntity heOrderRefundApplyEntity);

    List<HeOrderRefundApplyGoodsPO> applyGoodsEntityList2OrderRefundApplyGoodsPOList(List<HeOrderRefundApplyGoodsEntity> orderRefundApplyGoodsEntityList);

    List<HeOrderRefundApplyGoodsEntity> applyGoodsPOList2OrderRefundApplyGoodsEntityList(List<HeOrderRefundApplyGoodsPO> list);

    OrderRefundVO entity2VO(HeOrderRefundEntity oneByRefundOrderSn);

    default HeOrderRefundEntity offlineRemittanceRequest2OrderRefundEntity(OfflineRemittanceRequest offlineRemittanceRequest, String refundOrderSn) {
        HeOrderRefundEntity heOrderRefundEntity = new HeOrderRefundEntity();
        heOrderRefundEntity.setParentRefundOrderSn(null);
        heOrderRefundEntity.setOrderId(null);
        heOrderRefundEntity.setOrderGoodId(0);
        heOrderRefundEntity.setIncomeSn("");
        heOrderRefundEntity.setRefundType(OmniPayTypeEnum.OFFLINE.getCode());
        heOrderRefundEntity.setRefundMethod(OmniPayTypeEnum.OFFLINE.getCode());
        heOrderRefundEntity.setType(4);
        heOrderRefundEntity.setRefundOrderSn(refundOrderSn);
        heOrderRefundEntity.setApplyAmount(offlineRemittanceRequest.getRefundAmount().multiply(new BigDecimal(100)).intValue());
        heOrderRefundEntity.setStatus(RefundRecordPayStatusEnum.REFUND_RECORD_1.getCode());
        heOrderRefundEntity.setCreatedAt(System.currentTimeMillis() / 1000);
        heOrderRefundEntity.setApplyId(0);
        heOrderRefundEntity.setApplyName("");
        heOrderRefundEntity.setApplyPhone("");
        Map<String, Object> map = new HashMap<>();
        map.put("name", offlineRemittanceRequest.getAccountName());
        map.put("bank_number", offlineRemittanceRequest.getBankNo());
        map.put("bank_name", offlineRemittanceRequest.getAccountBank());
        heOrderRefundEntity.setRefundInfo(JSONUtil.toJsonStr(map));
        heOrderRefundEntity.setRemark("");
        heOrderRefundEntity.setRefundReasonType(2);
        heOrderRefundEntity.setRefundReason(0);
        heOrderRefundEntity.setHasLiquidatedDamages(0);
        heOrderRefundEntity.setComplaintId(offlineRemittanceRequest.getComplaintId());
        heOrderRefundEntity.setGiftExtendDisabled(0);
        System.out.println("开始扣业绩");
        heOrderRefundEntity.setRefundAchievement(RMBUtils.YTFLong(offlineRemittanceRequest.getRefundAchievement()).intValue());
        return heOrderRefundEntity;
    }
}

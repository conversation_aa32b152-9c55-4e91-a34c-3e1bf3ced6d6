package com.stbella.order.infrastructure.repository.condition;

import java.io.Serializable;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2022/12/05
 */
@ApiModel(value = "合同状态筛选")
@NoArgsConstructor
@AllArgsConstructor
@Data
@Accessors(chain = true)
public class MonthContractSignCondition extends BaseCondition implements Serializable {

    private static final long serialVersionUID = 45717640179914002L;

    @ApiModelProperty(value = "订单id")
    private Integer orderId;

    @ApiModelProperty(value = "客户id")
    private Integer clientId;

    @ApiModelProperty(value = "合同状态 0 =未签署 1=签署中 2=已归档")
    @NotBlank(message = "合同状态参数不能为空")
    private Integer contractStatus;
}

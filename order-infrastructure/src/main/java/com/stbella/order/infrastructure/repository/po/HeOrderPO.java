package com.stbella.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.*;
import com.stbella.order.infrastructure.repository.handler.PromotionInfoListTypeHandler;
import com.stbella.platform.order.api.res.PromotionInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 订单表
 * </p>
 *
 * <AUTHOR> @since 2022-10-28
 */
@Data
@Accessors(chain = true)
@TableName(value = "he_order",autoResultMap = true)
@ApiModel(value = "HeOrderPO对象", description = "订单表")
public class HeOrderPO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单ID（主键自增)")
    @TableId(value = "order_id", type = IdType.AUTO)
    private Integer orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    @ApiModelProperty(value = " 订单类型： 0 月子客户的“普通月子套餐”订单； 1 月子客户的“小月子”订单； 10 到家客户的“到家服务”订单； 20 到家阿姨的“到家服务订单”； 21 到家阿姨的阿姨培训订单； 30 月子其他订单 31 到家其他订单 40 商城普通订单 50护士外派订单 60产康订单 70S-BRA订单 描述： 0-9 归属月子平台的客户订单类型 ； 10-19 归属到家客户的订单类型； 20-29到家阿姨的订单类型; 30-39 其他订单 40-49 商城订单类型 50护士外派订单 60产康订单 70S-BRA订单")
    private Integer orderType;

    @ApiModelProperty(value = "创建该订单的任务ID（task表的id字段）")
    private Long taskId;

    @ApiModelProperty(value = "全局用户ID")
    private Integer basicUid;

    @ApiModelProperty(value = "买家客户ID（ecp库的tab_client表主键）")
    private Integer clientUid;

    @ApiModelProperty(value = "客户类型：0无（0是问题滴），1月子客户，2到家阿姨，5到家客户，")
    private Integer clientType;

    @ApiModelProperty(value = "销售员id（ecp库user表主键id）")
    private Integer staffId;

    @ApiModelProperty(value = "门店ID(ecp库cfg_store表的主键id)")
    private Integer storeId;

    @ApiModelProperty(value = "是否全部确认收货 0=否 1=是")
    private Integer isDelivery;

    @ApiModelProperty(value = "是否存在退款中 0=否 1=是 2已同意待退款，3已同意已退款")
    private Integer isHasRefund;

    @ApiModelProperty(value = "是否关闭0=否 1=是")
    private Integer isClose;

    @ApiModelProperty(value = "支付状态：0待支付，1未全付，2已付清，3已取消 4-超额支付")
    private Integer payStatus;

    @ApiModelProperty(value = "原价总金额，单位：分")
    private Integer orderAmount;

    @ApiModelProperty(value = "节假日费用金额，单位：分")
    private Integer holidayAmount;

    @ApiModelProperty(value = "法定节假日选择")
    private String holidayItems;

    @ApiModelProperty(value = "节假日天数")
    private Integer holidayNum;

    @ApiModelProperty(value = "应付总金额")
    private Integer payAmount;

    @ApiModelProperty(value = "首次支付时间")
    private Integer payFirstTime;

    @ApiModelProperty(value = "已支付总金额，单位：分")
    private Integer paidAmount;

    @ApiModelProperty(value = "实际金额(单位分,受退款影响)")
    private Integer realAmount;

    @ApiModelProperty(value = "冻结金额")
    private Integer freezeAmount;

    @ApiModelProperty(value = "应付积分总数")
    private Integer payIntegral;

    @ApiModelProperty(value = "产康金抵扣金额")
    private Integer productionAmountPay;

    @ApiModelProperty(value = "运费")
    private Integer freight;

    @ApiModelProperty(value = "入住时间")
    private Long wantIn;

    @ApiModelProperty(value = "是否备孕0=否 1=是")
    private Integer isPreparePregnancy;

    @ApiModelProperty(value = "订单备注")
    private String remark;

    @ApiModelProperty(value = "记录C端当前进行到的任务ID")
    private Long nowpaytask;

    @ApiModelProperty(value = "收件人姓名")
    private String expressName;

    @ApiModelProperty(value = "收件人手机号")
    private String expressPhone;

    @ApiModelProperty(value = "省份名称")
    private String province;

    @ApiModelProperty(value = "城市名称")
    private String city;

    @ApiModelProperty(value = "地区名称")
    private String area;

    @ApiModelProperty(value = "省份ID")
    private Integer provinceId;

    @ApiModelProperty(value = "城市ID")
    private Integer cityId;

    @ApiModelProperty(value = "地区ID")
    private Integer areaId;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "是否为冲刺单")
    private Integer isSprint;

    @ApiModelProperty(value = "是否全部发货：0否，1是")
    private Integer isSendAll;

    @ApiModelProperty(value = "下单时来源：0未知，1小贝拉，2圣贝拉，3occ后台")
    private Integer source;

    @ApiModelProperty(value = "该订单对应的押金sop的任务ID")
    private Long depositTaskId;

    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty(value = "是否删除：1是，0否")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Long createdAt;

    @ApiModelProperty(value = "最近更新时间")
    private Long updatedAt;

    @ApiModelProperty(value = "最后更新销售id")
    private Integer updateStaffId;

    @ApiModelProperty(value = "商城订单类型（同步goods表的sell_type）： 0实物，1虚拟，2预约，3套餐")
    private Integer orderSellType;

    @ApiModelProperty(value = "产康订单类型： 旧版产康订单，1、新版产康服务")
    private Integer productionType;

    private String inforFields;

    @ApiModelProperty(value = "首次支付金额超过一半的时间")
    private Integer percentFirstTime;

    @ApiModelProperty(value = "备份首次支付金额超过一半的时间")
    private Integer bkPercentFirstTime;

    @ApiModelProperty(value = "0）正常/已恢复 1）已剔除 业绩")
    private Integer operationType;

    @ApiModelProperty(value = "订单表扩展信息")
    private String extendsContent;

    @ApiModelProperty(value = "优惠券优惠总金额，单位：分")
    private Integer couponAmount;

    @ApiModelProperty(value = "优惠券领取id")
    private Integer couponUserId;

    @ApiModelProperty(value = "签署合同类型1=育婴师中介型服务 2=雇主-中介-母婴护理 3=雇主-中介-育婴师 4=育婴师-套餐 5=雇主-套餐-母婴护理 6=雇主-套餐-育婴师 7=育婴师培训课程确认书协议 8=圣贝拉月子合同 9=小贝拉月子合同")
    private Integer contractType;

    @ApiModelProperty(value = "是否给推荐人加积分, 0:不加积分 1:加积分")
    private Integer inviteAddIntegralType;

    @ApiModelProperty(value = "毛利率")
    private BigDecimal grossMargin;

    @ApiModelProperty(value = "净利率")
    private BigDecimal netMargin;

    @ApiModelProperty(value = "折扣率")
    private BigDecimal discountMargin;

    @ApiModelProperty(value = "签单折扣率")
    private BigDecimal signOrderDiscountMargin;

    @ApiModelProperty(value = "订单折扣详情")
    private String discountDetails;

    @ApiModelProperty(value = "折扣审批状态 0=无需审批 1=审批中 2=审批通过 3=审批失败 4=发起失败")
    private Integer approvalDiscountStatus;

    @ApiModelProperty(value = "是否报单过:1=未报单;2=已报单")
    private Integer isNotice;

    @ApiModelProperty(value = "订单标签，来源于he_tags表")
    private Integer orderTag;

    @ApiModelProperty(value = "订单标签名称")
    private String orderTagName;

    @ApiModelProperty(value = "-1=未知;0=大陆身份证 1=护照 2=香港来往大陆通行证 3=澳门来往大陆通行证 4=台湾来往大陆通行证（只有身份证签订了主合同才是已认证 其他证件无需认证）")
    private Integer certType;

    @ApiModelProperty(value = "证件号")
    private String idCard;

    @ApiModelProperty(value = "证件正面 url")
    private String idCardFront;

    @ApiModelProperty(value = "证件背面图url")
    private String idCardBack;

    @ApiModelProperty(value = "sbra业绩归属，0:默认 1-月子业绩 2-产康业绩")
    private Integer sbraAchievementType;

    @ApiModelProperty(value = "二级来源 默认为0 1）孕期指南-孕期专题 2）孕期指南-参考清单 3）孕期指南-专家专栏")
    private Integer appointmentSecondType;

    @ApiModelProperty(value = "新老系统标志;0或null-老;1-新")
    private Integer oldOrNew;

    @ApiModelProperty(value = "签署标识0-客户,1-委托人")
    private Integer signType;

    @ApiModelProperty(value = "是否需要签署合同，0表示不需要，1表示需要")
    private Integer needSign;

    @ApiModelProperty(value = "订单状态0-未生效 1-待入住 2-入住中 3-已离店 4-已关闭 5-提前离馆")
    private Integer orderStatus;

    @ApiModelProperty(value = "退款状态：0-无退款 1-部分退款中 2-部分退款 3-全部退款中 4-全部退款")
    private Integer refundStatus;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "原订单ID（用于订单升级）")
    private Integer originalOrderId;

    @ApiModelProperty(value = "原商品总价（用于订单升级）")
    private BigDecimal originalGoodsTotalPrice;

    /**
     * 实际入住时间(履约开始时间)
     */
    private Date fulfillmentStartDate;

    /**
     * 实际离店时间(履约结束时间)
     */
    private Date fulfillmentEndDate;

    @ApiModelProperty(value = "版本号 3.00表示能用订单")
    private BigDecimal version;

    @ApiModelProperty(value = "BU 0:母婴 1:广禾 2:予家")
    private Integer bu;

    /**
     * 全额退款时间
     */
    private Date fullRefundDate;

    @ApiModelProperty(value = "是否曾经全额支付过")
    private Boolean isPreviouslyFullyPaid;

    private String extraInfo;

    @ApiModelProperty(value = "外汇汇率")
    private BigDecimal fxRate;

    /**
     * 场景：0 下单； 1 报价单
     *
     * @see com.stbella.order.common.enums.core.CartSceneEnum
     */
    @ApiModelProperty(value = "场景：0 下单； 1 报价单,2,随心配")
    private Integer scene;

    @ApiModelProperty(value = "创建人Id")
    private Long createBy;

    @ApiModelProperty(value = "定制礼赠金额")
    private Integer giftAmount;

    @ApiModelProperty(value = "首次完款时间")
    private Long payFinishAt;

    @ApiModelProperty(value = "0-订单未开始核销；1-订单已核销")
    private Integer verificationStatus;

    @ApiModelProperty(value = "核销id")
    private Long verificationId;

    private Boolean transferOrder;

    // ============ 新增金额字段 - 避免查询子表 ============

    @ApiModelProperty(value = "总减免金额(分)")
    private Integer totalReductionAmount;

    @ApiModelProperty(value = "减免退款金额(分)")
    private Integer reductionRefundAmount;

    @ApiModelProperty(value = "退回重付金额(分)")
    private Integer tempRefundAmount;

    @ApiModelProperty(value = "现金已付金额(分)")
    private Integer cashPaidAmount;

    @ApiModelProperty(value = "产康金已付金额(分)")
    private Integer productionCoinPaidAmount;

    @ApiModelProperty(value = "现金退款金额(分)")
    private Integer cashRefundAmount;

    @ApiModelProperty(value = "产康金退款金额(分)")
    private Integer productionCoinRefundAmount;

    @ApiModelProperty(value = "现金审批中金额(分)")
    private Integer cashReviewingAmount;

    @ApiModelProperty(value = "商品退货分摊金额(分) - 商品退货数量 × 商品应付分摊")
    private Integer goodsRefundAllocationAmount;

    @ApiModelProperty(value = "订单命中的活动信息")
    @TableField(value = "promotion_infos", typeHandler = PromotionInfoListTypeHandler.class)
    private List<PromotionInfo> promotionInfos;

}

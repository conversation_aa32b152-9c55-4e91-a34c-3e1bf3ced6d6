package com.stbella.order.infrastructure.repository.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.stbella.care.api.appointment.req.external.PageOrderNoReq;
import com.stbella.care.server.care.entity.RoomStateCheckInInfoPO;
import com.stbella.care.server.care.service.RoomExternalQuery;
import com.stbella.care.server.care.service.RoomStateCheckInInfoService;
import com.stbella.core.base.Operator;
import com.stbella.core.base.PageVO;
import com.stbella.core.base.StoreInfoDTO;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.core.utils.sso.EmployeeTokenHelper;
import com.stbella.notice.enums.AuditTypeEnum;
import com.stbella.notice.server.OaProcessIdRelationService;
import com.stbella.order.common.constant.OrderConstant;
import com.stbella.order.common.enums.core.IsNanEnum;
import com.stbella.order.common.enums.core.OmniOrderStatusEnum;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.common.enums.core.OrderRefundStatusEnum;
import com.stbella.order.common.enums.month.*;
import com.stbella.order.common.utils.JsonUtil;
import com.stbella.order.domain.order.month.entity.*;
import com.stbella.order.domain.order.production.OrderGiftExtendEntity;
import com.stbella.order.domain.repository.*;
import com.stbella.order.domain.utils.dto.AsyncResultDTO;
import com.stbella.order.infrastructure.repository.converter.OrderConverter;
import com.stbella.order.infrastructure.repository.mapper.saas.HeOrderMapper;
import com.stbella.order.infrastructure.repository.po.HeOrderPO;
import com.stbella.order.server.order.StoreGoodsSkuModel;
import com.stbella.order.server.order.cts.enums.ClientTypeEnum;
import com.stbella.order.server.order.month.req.*;
import com.stbella.order.server.order.month.res.*;
import com.stbella.order.server.order.month.utils.RMBUtils;
import com.stbella.order.server.order.order.req.OrderBasicReq;
import com.stbella.order.server.order.order.req.OrderExportReq;
import com.stbella.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class OrderRepositoryImpl extends ServiceImpl<HeOrderMapper, HeOrderPO> implements OrderRepository {

    @Resource
    private RedisService redisService;
    @Resource
    private OrderConverter orderConverter;
    @Resource
    private HeOrderMapper heOrderMapper;

    @Resource
    private StoreRepository storeRepository;

    @Resource
    private ClientRepository clientRepository;

    @Resource
    private OrderGoodsRepository orderGoodsRepository;

    @Resource
    private OrderOtherRepository orderOtherRepository;

    @Resource
    private OrderProductionExtendRepository orderProductionExtendRepository;

    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;
    @Resource
    private MonthContractSignRecordRepository monthContractSignRecordRepository;
    @Resource
    private HeTaskRepository heTaskRepository;

    @DubboReference
    private OaProcessIdRelationService oaProcessIdRelationService;
    @Resource
    private OrderGiftExtendRepository orderGiftExtendRepository;

    @Resource
    private GoodsRepository goodsRepository;
    @Resource
    private GoodsSkuRepository goodsSkuRepository;
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @DubboReference
    private RoomStateCheckInInfoService roomStateCheckInInfoService;
    @DubboReference
    private RoomExternalQuery roomExternalQuery;

    @Value("${care.pageSize}")
    private Integer pageSize;

    private void saveCache(String key, Object value) {
        redisService.setCacheObject(key, value);
    }


    /**
     * 查询订单基本信息
     *
     * @param req 查询条件
     * @return
     */
    @Override
    public HeOrderEntity queryOrderInfo(OrderBasicReq req) {
        HeOrderPO orderPO = getOne(new LambdaQueryWrapper<HeOrderPO>()
                .eq(ObjectUtil.isNotEmpty(req.getOrderSn()), HeOrderPO::getOrderSn, req.getOrderSn())
                .eq(ObjectUtil.isNotEmpty(req.getOrderId()), HeOrderPO::getOrderId, req.getOrderId())
                .eq(HeOrderPO::getIsDelete, 0)
                .last("limit 1"));

        HeOrderEntity order = orderConverter.orderPo2Entity(orderPO);
        if (Objects.nonNull(order) && ObjectUtil.isNull(order.getPercentFirstTime())) {
            order.setPercentFirstTime(0);
        }

        return order;
    }

    /**
     * 根据taskId获取订单
     *
     * @param taskId
     * @return
     */
    @Override
    public HeOrderEntity queryOrderInfoByTaskId(Long taskId) {
        HeOrderPO orderPO = getOne(new LambdaQueryWrapper<HeOrderPO>()
                .eq(HeOrderPO::getTaskId, taskId)
                .eq(HeOrderPO::getIsDelete, 0)
                .last("limit 1"));
        return orderConverter.orderPo2Entity(orderPO);
    }

    /**
     * 保存订单主信息
     *
     * @param entity
     */
    @Override
    public Integer saveOrderMonth(HeOrderEntity entity) {
        log.info("saveOrderMonth");
        HeOrderPO heOrderPO = orderConverter.heOrderEntity2HeOrderPO(entity);
        //如果传入的时间戳为毫秒需要转为秒
        if (ObjectUtil.isNotEmpty(heOrderPO.getWantIn()) && heOrderPO.getWantIn().toString().length() == 13) {
            heOrderPO.setWantIn(heOrderPO.getWantIn() / 1000);
        }
        this.save(heOrderPO);
        entity.setOrderId(heOrderPO.getOrderId());
        return entity.getOrderId();
    }

    @Override
    public HeOrderEntity convertOrderMonthReq2HeOrderEntity(boolean isCreate, OrderMonthReq req, AsyncResultDTO<OrderMonthGoodsCacheVO, List<OrderGiftCacheByUserVO>, List<OrderAdditionalRevenueCacheVO>, OrderDiscountsCacheVO> resultDTO) {

        //todo 这个转化用仓储来实现，在基础设施能层，是有什么考虑吗，如果只是一个转化，就在应用层就可以了

        Operator operator = req.getOperator();
        //月子订单客户对象")
        OrderMonthClientReq orderMonthClientReq = req.getOrderMonthClientReq();
        //如果是备孕中/2099 入住时间要改为null
        if (ObjectUtil.isNotEmpty(orderMonthClientReq.getPredictBornDate()) && DateUtil.year(orderMonthClientReq.getPredictBornDate()) == 2099) {
            orderMonthClientReq.setWantIn(null);
        }
        //订单费用名称")
        OrderDiscountsCacheVO orderDiscountsCacheVO = resultDTO.getResult4();
        //"订单其他信息")
        OrderMonthOtherReq orderMonthOtherReq = req.getOrderMonthOtherReq();
        //订单主表
        HeOrderEntity entity = orderConverter.orderMonthClientReq2HeOrderEntity(orderMonthClientReq);
        ;
        if (!isCreate) {
            entity.setOrderStatus(this.getByOrderId(req.getOrderId()).getOrderStatus());
        }
        entity.setOrderId(req.getOrderId());
        entity.setOrderType(OmniOrderTypeEnum.MONTH_ORDER.getCode());
        entity.setBasicUid(orderMonthClientReq.getBasicUid());
        entity.setClientType(ClientTypeEnum.MONTH_CLIENT.getCode());
        entity.setUpdateStaffId(Integer.valueOf(operator.getOperatorGuid()));
        if (isCreate) {
            entity.setStaffId(Integer.valueOf(operator.getOperatorGuid()));
        }

        //元转分
        Long payAmount = orderDiscountsCacheVO.getOrderReceivableAmountCurrentPrice().multiply(new BigDecimal(100)).longValue();
        if (payAmount > Integer.MAX_VALUE) {
            throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), "订单应付金额过大，请修改订单");
        }
        entity.setPayAmount(payAmount.intValue());
        entity.setOrderAmount(orderDiscountsCacheVO.getOrderReceivableAmountOriginalPrice().multiply(new BigDecimal(100)).intValue());
        entity.setOrderTag(orderMonthOtherReq.getOrderTag());
        entity.setRemark(orderMonthOtherReq.getRemark());
        entity.setOrderTagName(orderMonthOtherReq.getOrderTagName());
        entity.setInviteAddIntegralType(orderMonthClientReq.getInviteAddIntegralType());
        entity.setDiscountMargin(ObjectUtil.isNotEmpty(orderDiscountsCacheVO.getOrderDiscount()) ? orderDiscountsCacheVO.getOrderDiscount().divide(new BigDecimal(100)).setScale(2) : BigDecimal.ONE);
        entity.setGrossMargin(ObjectUtil.isNotEmpty(orderDiscountsCacheVO.getGrossProfitMargin()) ? orderDiscountsCacheVO.getGrossProfitMargin().divide(new BigDecimal(100)).setScale(2) : BigDecimal.ONE);
        entity.setNetMargin(ObjectUtil.isNotEmpty(orderDiscountsCacheVO.getNetDiscountRate()) ? orderDiscountsCacheVO.getNetDiscountRate().divide(new BigDecimal(100)).setScale(2) : BigDecimal.ONE);

        //订单折扣详情
        entity.setDiscountDetails(ObjectUtil.isEmpty(orderDiscountsCacheVO) ? "" : JsonUtil.write(orderDiscountsCacheVO));
        //是否为委托人签署
        entity.setSignType(orderMonthClientReq.getSignType());
        //新系统标识
        entity.setOldOrNew(1);
        return entity;
    }

    /**
     * 修改订单主信息
     *
     * @param entity
     */
    @Override
    public Integer updateOrderMonthByOrderId(HeOrderEntity entity) {
        log.info("updateOrderMonthByOrderId");
        HeOrderPO heOrderPO = orderConverter.heOrderEntity2HeOrderPO(entity);
        //如果传入的时间戳为毫秒需要转为秒
        if (ObjectUtil.isNotEmpty(heOrderPO.getWantIn()) && heOrderPO.getWantIn().toString().length() == 13) {
            heOrderPO.setWantIn(heOrderPO.getWantIn() / 1000);
        }
        LambdaUpdateWrapper<HeOrderPO> lq = new LambdaUpdateWrapper<>();
        lq.eq(HeOrderPO::getOrderId, heOrderPO.getOrderId());
        this.update(heOrderPO, lq);
        return heOrderPO.getOrderId();
    }

    /**
     * 获取订单信息缓存key
     *
     * @param basePrefix        基础前缀
     * @param connect           连接符号
     * @param orderCacheBaseReq key内容
     * @return
     */
    private static String getOrderCacheKey(String basePrefix, String connect, OrderCacheBaseReq orderCacheBaseReq) {
        //有订单号是创建订单后的操作
        if (ObjectUtil.isNotEmpty(orderCacheBaseReq.getOrderId())) {
            return basePrefix + connect + orderCacheBaseReq.getOrderId();
        }
        //创建订单前的操作
        return
                basePrefix +
                        connect +
                        orderCacheBaseReq.getOperator().getOperatorGuid() + connect +
                        orderCacheBaseReq.getClientUid() + connect +
                        orderCacheBaseReq.getStoreId() + connect +
                        orderCacheBaseReq.getOrderType();
    }

    @Override
    public void orderGoodsCache(OrderGoodsCacheReq req) {
        String orderCacheKey = getOrderCacheKey(OrderConstant.PREFIX_ORDER_CACHE_GOODS, "_", req);
        saveCache(orderCacheKey, JsonUtil.write(req));
    }

    @Override
    public void orderMonthGiftExtendCache(OrderMonthGiftExtendCacheReq req) {
        String orderCacheKey = getOrderCacheKey(OrderConstant.PREFIX_ORDER_CACHE_GIFT_EXTEND, "_", req);
        saveCache(orderCacheKey, JsonUtil.write(req));
    }

    @Override
    public void orderMonthAddtionalRevenueCache(List<OrderMonthAdditionalRevenueCacheReq> req) {
        String orderCacheKey = getOrderCacheKey(OrderConstant.PREFIX_ORDER_CACHE_ADDTIONAL_REVENUE, "_", req.get(0));
        saveCache(orderCacheKey, JsonUtil.write(req));
    }

    @Override
    public OrderGoodsCacheReq getOrderGoodsCache(OrderGoodsCacheReq req) {
        if (req.isFromCache()) {
            String orderCacheKey = getOrderCacheKey(OrderConstant.PREFIX_ORDER_CACHE_GOODS, "_", req);
            String cacheObject = redisService.getCacheObject(orderCacheKey);
            if (StringUtils.isNotEmpty(cacheObject)) {
                return JsonUtil.read(cacheObject, OrderGoodsCacheReq.class);
            }
        } else {

        }
        return null;
    }

    @Override
    public Boolean delOrderGoodsCache(OrderCacheBaseReq req) {
        String orderCacheKey = getOrderCacheKey(
                OrderConstant.PREFIX_ORDER_CACHE_GOODS, "_", req);
        return redisService.deleteObject(orderCacheKey);
    }

    @Override
    public List<OrderGiftCacheVO> getOrderMonthGiftExtendCache(OrderMonthGiftExtendCacheReq req) {
        List<OrderGiftCacheVO> list = new ArrayList<>();
        if (req.isFromCache()) {
            String orderCacheKey = getOrderCacheKey(
                    OrderConstant.PREFIX_ORDER_CACHE_GIFT_EXTEND, "_", req);

            String cacheStr = redisService.getCacheObject(orderCacheKey);

            List<OrderMonthGiftExtendCacheReq> orderMonthGiftExtendCacheReqList = JsonUtil.readList(cacheStr, OrderMonthGiftExtendCacheReq.class);
            if (CollectionUtils.isNotEmpty(orderMonthGiftExtendCacheReqList)) {
                for (OrderMonthGiftExtendCacheReq orderMonthGiftExtendCacheReq : orderMonthGiftExtendCacheReqList) {
                    OrderGiftCacheVO orderGiftCacheVO = new OrderGiftCacheVO();
                    orderGiftCacheVO.setCategoryFront(orderMonthGiftExtendCacheReq.getCategoryFront());
                    orderGiftCacheVO.setGoodsId(orderMonthGiftExtendCacheReq.getGoodsId());
                    orderGiftCacheVO.setSkuId(orderMonthGiftExtendCacheReq.getSkuId());
                    orderGiftCacheVO.setQuantity(orderMonthGiftExtendCacheReq.getQuantity());
                    list.add(orderGiftCacheVO);
                }
            }
        } else {
            List<OrderGiftExtendEntity> giftExtendEntityList = orderGiftExtendRepository.getByOrderId(req.getOrderId());
            if (ObjectUtil.isNotEmpty(giftExtendEntityList)) {
                for (OrderGiftExtendEntity orderGiftExtendEntity : giftExtendEntityList) {
                    OrderGiftCacheVO orderGiftCacheVO = new OrderGiftCacheVO();
                    orderGiftCacheVO.setCategoryFront(orderGiftExtendEntity.getCategoryId());
                    orderGiftCacheVO.setGoodsId(orderGiftExtendEntity.getGoodsId());
                    orderGiftCacheVO.setSkuId(orderGiftExtendEntity.getSkuId());
                    orderGiftCacheVO.setQuantity(orderGiftExtendEntity.getGoodsNum());
                    list.add(orderGiftCacheVO);
                }
            }
        }

        return list;
    }

    @Override
    public Boolean delOrderMonthGiftExtendCache(OrderCacheBaseReq req) {
        String orderCacheKey = getOrderCacheKey(
                OrderConstant.PREFIX_ORDER_CACHE_GIFT_EXTEND, "_", req);
        return redisService.deleteObject(orderCacheKey);
    }

    @Override
    public List<OrderMonthAdditionalRevenueCacheReq> getOrderMonthAdditionalRevenueCache(OrderMonthAdditionalRevenueCacheReq req) {
        String orderCacheKey = getOrderCacheKey(
                OrderConstant.PREFIX_ORDER_CACHE_ADDTIONAL_REVENUE, "_", req);

        String cacheObject = redisService.getCacheObject(orderCacheKey);
        return JsonUtil.readList(cacheObject, OrderMonthAdditionalRevenueCacheReq.class);
    }

    @Override
    public Boolean delOrderMonthAdditionalRevenueCache(OrderCacheBaseReq req) {
        String orderCacheKey = getOrderCacheKey(
                OrderConstant.PREFIX_ORDER_CACHE_ADDTIONAL_REVENUE, "_", req);
        return redisService.deleteObject(orderCacheKey);
    }

    @Override
    public void updateOrderMonthGiftExtendCache(OrderMonthGiftExtendCacheReq req) {
        String orderCacheKey = getOrderCacheKey(
                OrderConstant.PREFIX_ORDER_CACHE_GIFT_EXTEND, "_", req);

        String cacheStr = redisService.getCacheObject(orderCacheKey);

        //从缓存中获取当前的数据
        List<OrderMonthGiftExtendCacheReq> orderMonthGiftExtendCacheReqList = JsonUtil.readList(cacheStr, OrderMonthGiftExtendCacheReq.class);


        if (CollectionUtils.isEmpty(orderMonthGiftExtendCacheReqList)) {
            orderMonthGiftExtendCacheReqList = new ArrayList<>();
            //目前缓存无数据库，全量往里丢
            orderMonthGiftExtendCacheReqList.add(req);
        } else {
            if (ObjectUtils.isEmpty(req.getCategoryFront()) && ObjectUtils.isEmpty(req.getGoodsId()) && ObjectUtils.isEmpty(req.getSkuId())) {
                Optional<OrderMonthGiftExtendCacheReq> first = orderMonthGiftExtendCacheReqList.stream().filter(o -> ObjectUtils.isEmpty(o.getCategoryFront()) && ObjectUtils.isEmpty(o.getGoodsId()) && ObjectUtils.isEmpty(o.getSkuId())).findFirst();
                if (first.isPresent()) {
                    first.get().setQuantity(req.getQuantity());
                } else {
                    orderMonthGiftExtendCacheReqList.add(req);
                }
            } else {
                //遍历缓存的集合
                Optional<OrderMonthGiftExtendCacheReq> first = orderMonthGiftExtendCacheReqList.stream().filter(o ->
                        ObjectUtils.isNotEmpty(o.getGoodsId()) && o.getGoodsId().equals(req.getGoodsId()) &&
                                ObjectUtils.isNotEmpty(o.getSkuId()) && o.getSkuId().equals(req.getSkuId())
                ).findFirst();
                if (first.isPresent()) {
                    OrderMonthGiftExtendCacheReq orderMonthGiftExtendCacheReq = first.get();
                    Integer quantity = req.getQuantity();
                    //有相同的商品且数量大于
                    if (quantity > 0) {
                        orderMonthGiftExtendCacheReq.setQuantity(quantity);
                        //因为一个goodsId只能选择一个sku，所以进行覆盖
                        orderMonthGiftExtendCacheReq.setSkuId(req.getSkuId());
                    } else {
                        orderMonthGiftExtendCacheReqList.remove(orderMonthGiftExtendCacheReq);
                    }
                } else {
                    orderMonthGiftExtendCacheReqList.add(req);
                }
            }


        }
        redisService.setCacheObject(orderCacheKey, JsonUtil.write(orderMonthGiftExtendCacheReqList));
    }


    @Override
    public HeOrderEntity queryOrderById(Integer orderId) {
        HeOrderPO orderPO = this.getById(orderId);

        return orderConverter.orderPo2Entity(orderPO);
    }

    /**
     * 下单成功删除所有的缓存
     *
     * @param req
     * @return
     */
    @Override
    public Boolean removeCache(OrderCacheBaseReq req) {
        //删除套餐
        this.delOrderGoodsCache(req);
        //删除额外礼赠
        this.delOrderMonthGiftExtendCache(req);
        //删除加收项
        this.delOrderMonthAdditionalRevenueCache(req);
        //删除订单其他信息
        this.delOrderOtherInfoCache(req);
        //删除用户
        return true;
    }

    @Override
    public Page<WechatMyOrderVO> getMyOrder(WechatMyOrderQuery query) {
        if (ObjectUtil.isEmpty(query.getOperator())) {
            return new Page<>();
        }

        Page page = new Page(query.getPageNum(), query.getPageSize());
        Page<WechatMyOrderVO> orderPage = this.baseMapper.getMyOrderList(page, query);
        if (CollectionUtil.isEmpty(orderPage.getRecords())) {
            return new Page<>();
        }
        List<CfgStoreEntity> cfgStoreList = storeRepository.queryCfgStoreByIdList(orderPage.getRecords().stream().map(WechatMyOrderVO::getStoreId).collect(Collectors.toList()));
        List<HeOrderUserSnapshotEntity> heOrderUserSnapshotEntities = orderUserSnapshotRepository.queryByOrderIdList(orderPage.getRecords().stream().map(WechatMyOrderVO::getOrderId).collect(Collectors.toList()));
        return orderConverter.converterMyOrderPage(orderPage, cfgStoreList, heOrderUserSnapshotEntities);
    }

    @Override
    public void orderOtherInfoCache(OrderOtherInfoCacheReq req) {
        String orderCacheKey = getOrderCacheKey(
                OrderConstant.PREFIX_ORDER_CACHE_OTHER_INFO, "_", req);
        redisService.setCacheObject(orderCacheKey, JsonUtil.write(req));
    }

    @Override
    public OrderOtherInfoCacheVO getOrderOtherInfoCache(OrderCacheBaseReq req) {
        String orderCacheKey = getOrderCacheKey(OrderConstant.PREFIX_ORDER_CACHE_OTHER_INFO, "_", req);
        String cacheObject = redisService.getCacheObject(orderCacheKey);
        if (StringUtils.isNotEmpty(cacheObject)) {
            return JsonUtil.read(cacheObject, OrderOtherInfoCacheVO.class);
        }
        return null;
    }

    @Override
    public Boolean delOrderOtherInfoCache(OrderCacheBaseReq req) {
        String orderCacheKey = getOrderCacheKey(OrderConstant.PREFIX_ORDER_CACHE_OTHER_INFO, "_", req);
        return redisService.deleteObject(orderCacheKey);
    }

    @Override
    public HeOrderEntity getByOrderId(Integer orderId) {
        HeOrderPO heOrderPO = this.baseMapper.selectById(orderId);
        if (ObjectUtil.isEmpty(heOrderPO)) {
            throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "订单不存在");
        }
        HeOrderEntity order = orderConverter.orderPo2Entity(heOrderPO);
        if (ObjectUtil.isNull(order.getPercentFirstTime())) {
            order.setPercentFirstTime(0);
        }

        return order;
    }

    @Override
    public List<HeOrderEntity> getByStaffId(Integer staffId) {
        List<HeOrderPO> heOrderList = this.baseMapper.selectList(new LambdaQueryWrapper<HeOrderPO>().eq(HeOrderPO::getStaffId, staffId));
        return orderConverter.orderPoListEntity(heOrderList);
    }

    /**
     * 根据客户id获取客户的订单列表
     *
     * @param req
     * @return
     */
    @Override
    public PageVO<WechatMyOrderVO> getCustomerStoreOrderList(ClientOrderListReq req) {
        LambdaQueryWrapper<HeOrderPO> queryWrapper = new LambdaQueryWrapper<HeOrderPO>()
                .eq(HeOrderPO::getIsDelete, 0)
                .eq(HeOrderPO::getClientType, 1)
                .eq(HeOrderPO::getClientUid, req.getClientUid())
                .ne(HeOrderPO::getOrderStatus, 4)
                .ne(HeOrderPO::getOrderType, OmniOrderTypeEnum.MALL_ORDER.getCode())
                .lt(HeOrderPO::getVersion, 3)
                .orderByDesc(HeOrderPO::getCreatedAt);

        Page<HeOrderPO> orderList = page(new Page<>(req.getPageNum(), req.getPageSize()), queryWrapper);

        log.info("orderList:{}", JSONUtil.toJsonStr(orderList));
        log.info("orderList.getTotal:{}", orderList.getTotal());
        if (orderList.getTotal() == 0) {
            return new PageVO<>(Lists.newArrayList(), (int) orderList.getTotal(), (int) orderList.getSize(), (int) orderList.getCurrent());
        }

        TabClientEntity tabClientEntity = clientRepository.getTabClientById(req.getClientUid());
        CfgStoreEntity storeEntity = null;
        if (ObjectUtil.isNotNull(tabClientEntity)) {
            storeEntity = storeRepository.queryCfgStoreById(tabClientEntity.getStoreId());
        }

        List<HeOrderEntity> heOrderEntityList = orderConverter
                .orderPoListEntity(orderList.getRecords());
        List<Integer> orderIdList = heOrderEntityList.stream().map(HeOrderEntity::getOrderId).collect(Collectors.toList());
        List<WechatMyOrderVO> wechatMyOrderVOList = new ArrayList<>();
        //查询合同解除协议
        List<MonthContractSignRecordEntity> byOrderIdListAndContractStatus = monthContractSignRecordRepository.getByOrderIdListAndContractStatus(orderIdList, TemplateContractTypeEnum.RELEASE.code(), ContractStatusEnum.SIGNED.code());
        for (HeOrderEntity heOrderEntity : heOrderEntityList) {
            WechatMyOrderVO myOrderVO = orderConverter.orderEntity2WechatMyOrderVO(heOrderEntity);

            //判断是否签署离馆协议
            if (ObjectUtil.isNotEmpty(byOrderIdListAndContractStatus)) {
                List<MonthContractSignRecordEntity> collect = byOrderIdListAndContractStatus.stream().filter(x -> x.getGuideId().equals(Long.valueOf(heOrderEntity.getOrderId()))).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(collect)) {
                    myOrderVO.setHasSignContractReleaseAgreement(collect.get(0).getContractStatus());
                }
            }

            if (ObjectUtil.isNotNull(tabClientEntity)) {
                myOrderVO.setClientName(tabClientEntity.getName());
                myOrderVO.setPhone(tabClientEntity.getPhone());
            }

            if (ObjectUtil.isNotNull(storeEntity)) {
                myOrderVO.setStoreName(storeEntity.getStoreName());
                myOrderVO.setStoreType(storeEntity.getType());
            }

            myOrderVO.setOrderAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(myOrderVO.getOrderAmountLong())));
            myOrderVO.setPayAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(myOrderVO.getPayAmountLong())));
            myOrderVO.setPaidAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(myOrderVO.getPaidAmountLong())));


            long remainAmount = 0;
            if (ObjectUtil.isNotNull(myOrderVO.getOldOrNew()) && myOrderVO.getOldOrNew().equals(1)) {
                // 新订单，状态走订单表中的字段
                myOrderVO.setPayStatusName(PayStatusV2Enum.getName(myOrderVO.getPayStatus()));
                myOrderVO.setRefundStatusName(OrderRefundStatusEnum.getName(myOrderVO.getRefundStatus()));

                myOrderVO.setRealAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(new Integer(myOrderVO.getRealAmount()))));
                remainAmount = myOrderVO.getPayAmountLong() - heOrderEntity.getRealAmount().longValue() - heOrderEntity.getProductionAmountPay().longValue();
                //剩余支付金额 = 订单金额-实际已支付金额
                myOrderVO.setRemainAmount(remainAmount <= 0L ? "0.00" : RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(heOrderEntity.getPayAmount()).subtract(RMBUtils.bigDecimalF2Y(heOrderEntity.getRealAmount()))));
            } else {
                remainAmount = myOrderVO.getPayAmountLong() - myOrderVO.getPaidAmountLong();
                // 旧订单的状态
                myOrderVO.setPayStatusName(PayStatusOldEnum.getName(myOrderVO.getPayStatus()));
                myOrderVO.setRealAmount(myOrderVO.getPaidAmount());
                myOrderVO.setRemainAmount(remainAmount <= 0L ? "0.00" : RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(remainAmount)));
            }

            String goodsName = getGoodsNameByOrder(heOrderEntity);
            myOrderVO.setGoodsName(goodsName);

            if (ObjectUtil.isNull(myOrderVO.getOldOrNew())) {
                myOrderVO.setOldOrNew(0);
            }

            wechatMyOrderVOList.add(myOrderVO);
        }
        //查询合同解除协议

        log.info("wechatMyOrderVOList:{}", JSONUtil.toJsonStr(wechatMyOrderVOList));
        return new PageVO<>(wechatMyOrderVOList,
                (int) orderList.getTotal(), (int) orderList.getSize(), (int) orderList.getCurrent());
    }

    /**
     * 通过订单id获取WechatMyOrderVO
     *
     * @param orderId
     * @return
     */
    @Override
    public WechatMyOrderVO getWechatOrderVOByOrderId(Integer orderId) {
        HeOrderPO orderPO = this.getById(orderId);

        HeOrderEntity heOrderEntity = orderConverter.orderPo2Entity(orderPO);

        return orderConverter.orderEntity2WechatMyOrderVO(heOrderEntity);
    }

    @Override
    public void removeAllBeforeOrderCache() {
        //获取所有的缓存
        Collection<String> cache = redisService.keys("cache");
        for (String s : cache) {
            redisService.deleteObject(s);
        }
    }

    /**
     * 根据不同的订单类型获取订单名称
     *
     * @param order
     * @return
     */
    @Override
    public String getGoodsNameByOrder(HeOrderEntity order) {
        String goodsName = "";

        // 不同类型的订单获取商品名
        if (OmniOrderTypeEnum.MONTH_ORDER.getCode().equals(order.getOrderType())
                || OmniOrderTypeEnum.SMALL_MONTH_ORDER.getCode().equals(order.getOrderType())
                || OmniOrderTypeEnum.NURSE_OUTSIDE_ORDER.getCode().equals(order.getOrderType())
                || OmniOrderTypeEnum.SBRA_ORDER.getCode().equals(order.getOrderType())) {
            // 标准月子、小月子、护士外派、sbra等订单的goodsName需要从he_order_goods表中获取
            HeOrderGoodsEntity orderGoodsEntity = orderGoodsRepository.getByOrderId(order.getOrderId());

            if (ObjectUtil.isNotNull(orderGoodsEntity)) {
                List<StoreGoodsSkuModel> orderGoodsName = goodsRepository.getOrderGoodsName(new ArrayList<>(order.getOrderId()));
                if (ObjectUtil.isNotEmpty(orderGoodsName) && orderGoodsName.get(0).checkGoodsAndSku()) {
                    goodsName = orderGoodsName.get(0).getAllGoodsName();
                } else {
                    goodsName = orderGoodsEntity.getGoodsName();
                }
            }
        } else if (OmniOrderTypeEnum.OTHER_MONTH_ORDER.getCode().equals(order.getOrderType())) {
            // 其他订单的goodsName需要从he_order_other表中获取
            HeOrderOtherEntity orderOtherEntity = orderOtherRepository
                    .getByOrderId(order.getOrderId());

            if (ObjectUtil.isNotNull(orderOtherEntity)) {
                goodsName = orderOtherEntity.getGoodsName();
            }
        } else if (OmniOrderTypeEnum.PRODUCTION_ORDER.getCode().equals(order.getOrderType())) {
            // 产康订单判断新老产康
            if (order.getProductionType().equals(1)) {
                // 新版产康订单的商品需要从he_order_production_extend获取
                goodsName = orderProductionExtendRepository
                        .getGoodsNameByOrderId(order.getOrderId());
            } else {
                // 旧版产康订单需要从order_other中获取
                HeOrderOtherEntity orderOtherEntity = orderOtherRepository
                        .getByOrderId(order.getOrderId());

                if (ObjectUtil.isNotNull(orderOtherEntity)) {
                    goodsName = orderOtherEntity.getGoodsName();
                }
            }
        }

        return goodsName;
    }


    @Override
    public List<HeOrderEntity> queryByCondition(OrderQuery orderQuery) {
        LambdaQueryWrapper<HeOrderPO> list = new LambdaQueryWrapper<HeOrderPO>()
                .eq(Objects.nonNull(orderQuery.getOrderType()), HeOrderPO::getOrderType, orderQuery.getOrderType())
                .in(CollectionUtils.isNotEmpty(orderQuery.getOrderIdList()), HeOrderPO::getOrderId, orderQuery.getOrderIdList())
                .in(CollectionUtils.isNotEmpty(orderQuery.getOrderSnList()), HeOrderPO::getOrderSn, orderQuery.getOrderSnList())
                .in(CollectionUtil.isNotEmpty(orderQuery.getOrderTypeList()), HeOrderPO::getOrderType, orderQuery.getOrderTypeList())
                .in(CollectionUtil.isNotEmpty(orderQuery.getOrderStatusList()), HeOrderPO::getOrderStatus, orderQuery.getOrderStatusList())
                .in(CollectionUtil.isNotEmpty(orderQuery.getPayStatusList()), HeOrderPO::getPayStatus, orderQuery.getPayStatusList())
                .in(CollectionUtil.isNotEmpty(orderQuery.getRefundStatusList()), HeOrderPO::getRefundStatus, orderQuery.getRefundStatusList())
                .lt(Objects.nonNull(orderQuery.getMaxCreatedAt()), HeOrderPO::getCreatedAt, orderQuery.getMaxCreatedAt())
                .gt(Objects.nonNull(orderQuery.getMinCreatedAt()), HeOrderPO::getCreatedAt, orderQuery.getMinCreatedAt())
                .eq(Objects.nonNull(orderQuery.getStoreId()), HeOrderPO::getStoreId, orderQuery.getStoreId())
                .eq(Objects.nonNull(orderQuery.getIsNotice()), HeOrderPO::getIsNotice, orderQuery.getIsNotice())
                .eq(Objects.nonNull(orderQuery.getBasicUid()), HeOrderPO::getBasicUid, orderQuery.getBasicUid())
                .eq(Objects.nonNull(orderQuery.getRealAmount()), HeOrderPO::getRealAmount, orderQuery.getRealAmount())
                .ge(Objects.nonNull(orderQuery.getMinVersion()), HeOrderPO::getVersion, orderQuery.getMinVersion())
                .eq(Objects.nonNull(orderQuery.getStaffId()), HeOrderPO::getStaffId, orderQuery.getStaffId())
                // 大于100，或者小于-100
                .nested(Objects.nonNull(orderQuery.getPaidAmount()), i -> i.gt(Objects.nonNull(orderQuery.getPaidAmount()), HeOrderPO::getPaidAmount, orderQuery.getPaidAmount())
                        .or(a -> a.lt(Objects.nonNull(orderQuery.getPaidAmount()), HeOrderPO::getPaidAmount, -1 * orderQuery.getPaidAmount())))

                .between(Objects.nonNull(orderQuery.getMinFullRefundDate()), HeOrderPO::getFullRefundDate, orderQuery.getMinFullRefundDate(), orderQuery.getMaxFullRefundDate())
                .between(Objects.nonNull(orderQuery.getStartPercentFirstTime()) && Objects.nonNull(orderQuery.getEndPercentFirstTime()), HeOrderPO::getPercentFirstTime, orderQuery.getStartPercentFirstTime(), orderQuery.getEndPercentFirstTime());
        return orderConverter.orderPoListEntity(list(list));
    }

    @Override
    public void closeOrder(HeOrderEntity byOrderId) {
        HeOrderPO heOrderPO = new HeOrderPO();
        heOrderPO.setOrderId(byOrderId.getOrderId());
        heOrderPO.setIsClose(1);
        heOrderPO.setPayStatus(PayStatusV2Enum.CANCEL.getCode());
        heOrderPO.setOrderStatus(OrderStatusV2Enum.CLOSE.getCode());
        this.baseMapper.updateById(heOrderPO);
    }

    /**
     * 房态根据订单号修改订单状态
     *
     * @param orderNo
     * @param orderStatus
     */
    @Override
    public boolean updateOrderStatusByOrderNoAndStatus(String orderNo, Integer orderStatus) {
        LambdaUpdateWrapper<HeOrderPO> lu = new LambdaUpdateWrapper<>();
        lu.eq(HeOrderPO::getOrderSn, orderNo);
        lu.set(HeOrderPO::getOrderStatus, orderStatus);
        return this.update(lu);
    }

    @Override
    public Page<HeOrderEntity> queryAdminPage(
            Page page,
            List<Integer> clientList,
            List<Integer> basicList,
            List<Integer> orderIds,
            QueryOrderPageReq queryOrderPageReq,
            List<String> approveOrderIds,
            Set<Integer> typeStore
    ) {
        StoreInfoDTO storeInfoDTO = EmployeeTokenHelper.getCurrentEmployeeStoreInfo();
        if (
                (ObjectUtil.isNotNull(clientList) && clientList.size() == 0) ||
                        (ObjectUtil.isNotNull(basicList) && basicList.size() == 0) ||
                        (ObjectUtil.isNotNull(orderIds) && orderIds.size() == 0) ||
                        (ObjectUtil.isNotNull(approveOrderIds) && approveOrderIds.size() == 0) ||
                        (ObjectUtil.isNotNull(typeStore) && typeStore.size() == 0) ||
                        (!storeInfoDTO.getHasAllStoreInfo() && CollectionUtils.isEmpty(storeInfoDTO.getStoreIdList()))
        ) {
            Page nullPage = new Page();
            nullPage.setTotal(0);
            nullPage.setSize(queryOrderPageReq.getPageSize());
            nullPage.setCurrent(queryOrderPageReq.getPageNum());
            return nullPage;
        }

        //只查询员工拥有的门店数据
        if (!storeInfoDTO.getHasAllStoreInfo()) {
            if (CollectionUtils.isEmpty(queryOrderPageReq.getStoreIds())) {
                queryOrderPageReq.setStoreIds(storeInfoDTO.getStoreIdList());
            }
        }

        LambdaQueryWrapper<HeOrderPO> eq = new LambdaQueryWrapper<HeOrderPO>()
                .in(CollectionUtil.isNotEmpty(orderIds), HeOrderPO::getOrderId, orderIds)
                .eq(ObjectUtil.isNotEmpty(queryOrderPageReq.getOrderNo()), HeOrderPO::getOrderSn, queryOrderPageReq.getOrderNo())
                .in(CollectionUtil.isNotEmpty(queryOrderPageReq.getStoreIds()), HeOrderPO::getStoreId, queryOrderPageReq.getStoreIds())
                .in(CollectionUtil.isNotEmpty(typeStore), HeOrderPO::getStoreId, typeStore)
                .eq(ObjectUtil.isNotEmpty(queryOrderPageReq.getOrderTagId()), HeOrderPO::getOrderTag, queryOrderPageReq.getOrderTagId())
                .eq(ObjectUtil.isNotEmpty(queryOrderPageReq.getBasicUid()), HeOrderPO::getBasicUid, queryOrderPageReq.getBasicUid())
                .eq(ObjectUtil.isNotEmpty(queryOrderPageReq.getPayStatus()), HeOrderPO::getPayStatus, queryOrderPageReq.getPayStatus())
                .eq(ObjectUtil.isNotEmpty(queryOrderPageReq.getRefundStatus()), HeOrderPO::getRefundStatus, queryOrderPageReq.getRefundStatus())
                .eq(ObjectUtil.isNotEmpty(queryOrderPageReq.getOrderStatus()), HeOrderPO::getOrderStatus, queryOrderPageReq.getOrderStatus())
                .eq(ObjectUtil.isNotEmpty(queryOrderPageReq.getOrderSource()), HeOrderPO::getSource, queryOrderPageReq.getOrderSource())
                .lt(HeOrderPO::getVersion, 3.00);

        if (ObjectUtil.isNotEmpty(queryOrderPageReq.getDiscountApprovalStatus())) {
            if (queryOrderPageReq.getDiscountApprovalStatus() == 0) {
                Set<String> allRecordOrderByTypeAndStatus = oaProcessIdRelationService.getAllRecordOrderByTypeAndStatus(AuditTypeEnum.DISCOUNT_APPROVAL.getCode());

                if (CollectionUtils.isNotEmpty(allRecordOrderByTypeAndStatus)) {
                    List<Integer> collect = this.list(new LambdaQueryWrapper<HeOrderPO>().select(HeOrderPO::getOrderId).eq(HeOrderPO::getOrderType, OmniOrderTypeEnum.MONTH_ORDER.getCode()).notIn(HeOrderPO::getOrderId, allRecordOrderByTypeAndStatus)).stream().map(HeOrderPO::getOrderId).collect(Collectors.toList());

                    if (CollectionUtils.isNotEmpty(collect)) {
                        //无需审批：新订单没有进行过审批或者老订单的审批状态是0
                        eq.and(w ->
                                //新订单
                                w.and(w1 -> w1.eq(HeOrderPO::getOldOrNew, 1).in(CollectionUtil.isNotEmpty(collect), HeOrderPO::getOrderId, collect))
                                        //老订单
                                        .or(w1 -> w1.eq(HeOrderPO::getOldOrNew, 0).eq(HeOrderPO::getApprovalDiscountStatus, queryOrderPageReq.getDiscountApprovalStatus()))
                        );
                    }
                }
            } else {
                if (CollectionUtil.isNotEmpty(approveOrderIds)) {
                    eq.and(w ->
                            //新订单
                            w.and(w1 -> w1.eq(HeOrderPO::getOldOrNew, 1).in(CollectionUtil.isNotEmpty(approveOrderIds), HeOrderPO::getOrderId, approveOrderIds))
                                    //老订单
                                    .or(w1 -> w1.eq(HeOrderPO::getOldOrNew, 0).eq(HeOrderPO::getApprovalDiscountStatus, queryOrderPageReq.getDiscountApprovalStatus()))
                    );
                } else {
                    eq.eq(HeOrderPO::getOldOrNew, 0).eq(HeOrderPO::getApprovalDiscountStatus, queryOrderPageReq.getDiscountApprovalStatus());
                }
            }
        }

        if (CollectionUtils.isNotEmpty(clientList)) {
            eq.in(HeOrderPO::getClientUid, new HashSet(clientList));
        }

        if (CollectionUtils.isNotEmpty(basicList)) {
            eq.in(HeOrderPO::getBasicUid, new HashSet(basicList));
        }

        // 解析时间
        if (ObjectUtil.isNotEmpty(queryOrderPageReq.getDateType())) {
            if (queryOrderPageReq.getDateType() == 0) {
                if (ObjectUtil.isNotEmpty(queryOrderPageReq.getDateStart())) {
                    eq.ge(HeOrderPO::getCreatedAt, queryOrderPageReq.getDateStart());
                }
                if (ObjectUtil.isNotEmpty(queryOrderPageReq.getDateEnd())) {
                    eq.le(HeOrderPO::getCreatedAt, queryOrderPageReq.getDateEnd());
                }
            }
            if (queryOrderPageReq.getDateType() == 1) {
                if (ObjectUtil.isNotEmpty(queryOrderPageReq.getDateStart())) {
                    eq.ge(HeOrderPO::getPercentFirstTime, queryOrderPageReq.getDateStart());
                }
                if (ObjectUtil.isNotEmpty(queryOrderPageReq.getDateEnd())) {
                    eq.le(HeOrderPO::getPercentFirstTime, queryOrderPageReq.getDateEnd());
                }
            }
            // (履约开始) 入住时间
            if (queryOrderPageReq.getDateType() == 2) {
                if (ObjectUtil.isNotEmpty(queryOrderPageReq.getDateStart())) {
                    // 转成时间
                    eq.ge(HeOrderPO::getFulfillmentStartDate, new Date(queryOrderPageReq.getDateStart() * 1000));
                }
                if (ObjectUtil.isNotEmpty(queryOrderPageReq.getDateEnd())) {
                    eq.le(HeOrderPO::getFulfillmentStartDate, new Date(queryOrderPageReq.getDateEnd() * 1000));
                }
            }

        }
        //是否获取所有订单类型
        if (!queryOrderPageReq.getAllOrderType()) {
            eq.eq(HeOrderPO::getOrderType, OmniOrderTypeEnum.MONTH_ORDER.getCode());
        }
        eq.orderByDesc(HeOrderPO::getCreatedAt);
        eq.select(HeOrderPO::getOrderId);
        //第一次查询id
        Page<HeOrderPO> orderPOPage = this.page(page, eq);

        //第二次查全量
        List<HeOrderPO> records = orderPOPage.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            List<HeOrderPO> list = this.list(new LambdaQueryWrapper<HeOrderPO>().in(HeOrderPO::getOrderId, records.stream().map(HeOrderPO::getOrderId).collect(Collectors.toList())).orderByDesc(HeOrderPO::getCreatedAt));
            orderPOPage.setRecords(list);
        }
        return orderConverter.pagePO2PageEntity(orderPOPage);
    }

    @Override
    public List<Integer> getAllNewOrderIdList() {
        return this.list(new LambdaQueryWrapper<HeOrderPO>().eq(HeOrderPO::getOldOrNew, 1)).stream().map(HeOrderPO::getOrderId).collect(Collectors.toList());
    }

    /**
     * 获取C端客户订单列表
     */
    @Override
    public Page<CustomerMyOrderVO> getCustomerOrderList(CustomerMyOrderQuery query) {

        Page page = new Page(query.getPageNum(), query.getPageSize());
        Page<CustomerMyOrderVO> orderPage = this.baseMapper.getCustomerOrderList(page, query);
        if (CollectionUtil.isEmpty(orderPage.getRecords())) {
            return new Page<>();
        }
        List<CfgStoreEntity> cfgStoreList = storeRepository.queryCfgStoreByIdList(orderPage.getRecords().stream().map(CustomerMyOrderVO::getStoreId).collect(Collectors.toList()));
        return this.converterCustomerOrderPage(orderPage, cfgStoreList);
    }

    @Override
    public List<HeOrderEntity> getByOrderList(List<Integer> orderIdList) {
        if (CollectionUtil.isEmpty(orderIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HeOrderPO> list = new LambdaQueryWrapper<HeOrderPO>();
        list.in(HeOrderPO::getOrderId, orderIdList);
        List<HeOrderPO> resList = list(list);
        if (CollectionUtil.isEmpty(resList)) {
            return new ArrayList<>();
        }
        return orderConverter.orderPoListEntity(resList);

    }

    @Override
    public HeOrderEntity getByOrderSn(String orderSn) {
        LambdaQueryWrapper<HeOrderPO> lq = new LambdaQueryWrapper<HeOrderPO>();
        lq.eq(HeOrderPO::getOrderSn, orderSn)
                .eq(HeOrderPO::getIsDelete, 0)
                .last("limit 1");
        HeOrderPO one = this.getOne(lq);
        return orderConverter.orderPo2Entity(one);
    }

    @Override
    public List<HeOrderEntity> getByOrderSnList(List<String> orderSnList) {
        if (CollectionUtil.isEmpty(orderSnList)) {
            return null;
        }
        List<HeOrderPO> orderList = list(new LambdaQueryWrapper<HeOrderPO>().in(HeOrderPO::getOrderSn, orderSnList));
        return CollectionUtil.isEmpty(orderList) ? null : orderConverter.orderPoListEntity(orderList);
    }

    /**
     * 同步订单退款状态
     *
     * @param orderId
     * @param orderRefundStatus
     */
    @Override
    public Integer syncOrderRefundStatus(Integer orderId, Integer orderRefundStatus) {
        if (ObjectUtil.isEmpty(orderId)) {
            throw new BusinessException(ResultEnum.ILLEGAL_ARGUMENT.getCode(), "同步订单退款状态,订单ID不能为空");
        }
        LambdaUpdateWrapper<HeOrderPO> lq = new LambdaUpdateWrapper<>();
        lq.eq(HeOrderPO::getOrderId, orderId);
        lq.set(HeOrderPO::getRefundStatus, orderRefundStatus);
        this.update(lq);
        return orderId;
    }

    /**
     * 根据门店获取老订单(不包含已关闭订单)
     *
     * @param storeId
     * @param orderType
     */
    @Override
    public List<HeOrderEntity> getOldOrderByStoreIdAndType(Integer storeId, Integer orderType) {
        LambdaQueryWrapper<HeOrderPO> lq = new LambdaQueryWrapper<>();
        lq.eq(HeOrderPO::getStoreId, storeId)
                .eq(HeOrderPO::getOldOrNew, 0)
                .eq(HeOrderPO::getOrderType, orderType)
                .eq(HeOrderPO::getIsDelete, 0)
                .ne(HeOrderPO::getOrderStatus, 4);

        return orderConverter.orderPoListEntity(this.list(lq));
    }

    /**
     * 获取一个老订单(不包含已关闭订单)
     *
     * @param orderType
     */
    @Override
    public HeOrderEntity getOldOrderByType(Integer orderType) {
        LambdaQueryWrapper<HeOrderPO> lq = new LambdaQueryWrapper<>();
        lq.eq(HeOrderPO::getOldOrNew, 0)
                .eq(HeOrderPO::getOrderType, orderType)
                .eq(HeOrderPO::getIsDelete, 0)
                .ne(HeOrderPO::getOrderStatus, 4)
                .last("limit 1");

        return orderConverter.orderPo2Entity(this.getOne(lq));
    }

    /**
     * 导出当月订单明细
     *
     * @param req
     * @return
     */
    @Override
    public List<HeOrderEntity> queryCurrMonthOrderIterm(OrderExportReq req) {
        return heOrderMapper.queryCurrMonthOrderIterm(req);
    }

    @Override
    public Integer updateSaleByOrderId(Integer orderId, Integer saleId) {
        LambdaUpdateWrapper<HeOrderPO> lq = new LambdaUpdateWrapper<>();
        lq.set(HeOrderPO::getStaffId, saleId)
                .eq(HeOrderPO::getOrderId, orderId);
        this.update(lq);
        return orderId;
    }

    @Override
    public List<HeOrderEntity> queryByClientIds(List<Integer> clientIds) {
        LambdaQueryWrapper<HeOrderPO> lq = new LambdaQueryWrapper<>();
        lq.in(HeOrderPO::getOrderType, Arrays.asList(0, 1, 30, 50, 60, 70))
                .gt(HeOrderPO::getPercentFirstTime, 0)
                .eq(HeOrderPO::getIsDelete, 0)
                .in(HeOrderPO::getClientUid, clientIds);
        return orderConverter.orderPoListEntity(this.list(lq));
    }

    /**
     * 设置订单状态
     *
     * @param orderId
     * @param status
     */
    @Override
    public void updateOrderStatus(Integer orderId, Integer status) {
        LambdaUpdateWrapper<HeOrderPO> lq = new LambdaUpdateWrapper<>();
        lq.eq(HeOrderPO::getOrderId, orderId)
                .set(HeOrderPO::getOrderStatus, status);
        this.update(lq);
    }

    /**
     * 通过订单id获取订单编号
     *
     * @param orderIds
     * @return java.util.List<com.stbella.order.domain.order.month.entity.HeOrderEntity>
     * @throws
     * <AUTHOR>
     * @date 2023/8/8 13:55
     * @since 1.0.0
     */
    @Override
    public List<HeOrderEntity> listOrderNoByIds(List<Integer> orderIds) {
        if (CollectionUtil.isEmpty(orderIds)) {
            return Lists.newArrayList();
        }
        List<HeOrderPO> list = this.list(
                new LambdaQueryWrapper<HeOrderPO>()
                        .in(HeOrderPO::getOrderId, orderIds)
                        .eq(HeOrderPO::getIsDelete, IsNanEnum.NO.code())
                        .select(HeOrderPO::getOrderId, HeOrderPO::getOrderSn)
        );
        return orderConverter.orderPoListEntity(list);
    }


    /**
     * C端-我的订单分页查询
     *
     * @param page
     * @param storeEntityList
     * @return
     */
    private Page<CustomerMyOrderVO> converterCustomerOrderPage(Page<CustomerMyOrderVO> page, List<CfgStoreEntity> storeEntityList) {
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return page;
        }
        Map<Integer, String> storeMap = storeEntityList.stream().collect(Collectors.toMap(CfgStoreEntity::getStoreId, CfgStoreEntity::getStoreName));
        page.getRecords().forEach(item -> {
            if (Objects.nonNull(item.getStoreId())) {
                item.setStoreName(storeMap.get(item.getStoreId()));
            }

            Long realAmount = ObjectUtil.isEmpty(item.getRealAmount()) ? 0L : new Long(item.getRealAmount());
            item.setRealAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(realAmount)));
            item.setPayAmount(ObjectUtil.isEmpty(item.getPayAmount()) ? "0" : item.getPayAmount());
            item.setOrderAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(item.getOrderAmountLong())));
            item.setPaidAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(item.getPaidAmountLong())));
            item.setPayAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(item.getPayAmountLong())));
            long remainAmount = item.getPayAmountLong() - realAmount;
            item.setRemainAmount(remainAmount <= 0 ? "0.00" : RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(remainAmount)));
            item.setPayStatusName(PayStatusV2Enum.getName(item.getPayStatus()));
            if (item.getPayStatus().equals(PayStatusV2Enum.EXCESS_PAY.getCode())) {
                //C端超额要展示为全部支付
                item.setPayStatusName(PayStatusV2Enum.PAY_OFF.getValue());
            }
            item.setDiscountAmount(RMBUtils.formatToseparaDecimals(RMBUtils.bigDecimalF2Y(item.getOrderAmountLong() - item.getPayAmountLong())));
            //老订单要额外设置nowpaytask
            if (ObjectUtil.isEmpty(item.getOldOrNew()) || 0 == item.getOldOrNew()) {
                if (ObjectUtil.isEmpty(item.getNowpaytask()) || 0 == item.getNowpaytask()) {
                    HeTaskEntity byTaskId = heTaskRepository.getByTaskId(item.getTaskId());
                    if (ObjectUtil.isNotEmpty(byTaskId)) {
                        HeTaskEntity oneByProjectIdAndFormteplateType = heTaskRepository.getOneByProjectIdAndFormteplateType(byTaskId.getProjectId(), 7L);
                        item.setNowpaytask(oneByProjectIdAndFormteplateType.getId());
                    }
                }

            }
        });
        return page;
    }

    /**
     * 订单变更
     *
     * @param entity
     * @return
     */
    @Override
    public Integer updateOne(HeOrderEntity entity) {
        HeOrderPO heOrderPO = orderConverter.heOrderEntity2HeOrderPO(entity);
        return this.baseMapper.updateById(heOrderPO);
    }

    /**
     * @param orderIds
     * @return
     */
    @Override
    public List<HeOrderEntity> getByOrderIdList(List<Integer> orderIds) {
        if (CollectionUtil.isEmpty(orderIds)) {
            return new ArrayList<>();
        }
        List<HeOrderPO> orderList = list(new LambdaQueryWrapper<HeOrderPO>().in(HeOrderPO::getOrderId, orderIds));
        return CollectionUtil.isEmpty(orderList) ? new ArrayList<>() : orderConverter.orderPoListEntity(orderList);
    }

    @Override
    public List<HeOrderEntity> queryOrderForPayFirstRecord(PayFirstRecordReq req) {
        Long payFirstStartTime = req.getPayFirstStartTime().getTime() / 1000;
        Long payFirstEndTime = req.getPayFirstEndTime().getTime() / 1000;
        List<HeOrderPO> orderList = list(new LambdaQueryWrapper<HeOrderPO>().ge(HeOrderPO::getPayFirstTime, payFirstStartTime).le(HeOrderPO::getPayFirstTime, payFirstEndTime));
        return CollectionUtil.isEmpty(orderList) ? null : orderConverter.orderPoListEntity(orderList);
    }

    /**
     * 查询指定日期签约的标准月子订单列表
     *
     * @param date
     * @return
     */
    @Override
    public List<HeOrderEntity> queryOrderMonthForDate(Date date) {
        if (date == null) {
            date = new Date();
        }

        DateTime dateTime = DateUtil.beginOfDay(date);

        List<HeOrderPO> orderList = list(new LambdaQueryWrapper<HeOrderPO>()
                .eq(HeOrderPO::getIsDelete, IsNanEnum.NO.code())
                .eq(HeOrderPO::getOrderType, OmniOrderTypeEnum.MONTH_ORDER.getCode())
                .ge(HeOrderPO::getPercentFirstTime, dateTime.getTime() / 1000));
        return CollectionUtil.isEmpty(orderList) ? null : orderConverter.orderPoListEntity(orderList);
    }

    @Override
    public Page<HeOrderEntity> queryList(OrderPageReq req) {
        IPage<HeOrderPO> page = heOrderMapper.selectPage(new Page<>(req.getPageNum(), req.getPageSize()),
                buildQueryWrapper(req)
        );
        Page<HeOrderEntity> resPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        resPage.setPages(page.getPages());

        List<HeOrderEntity> heOrderEntities = orderConverter.orderPoListEntity(page.getRecords());
        List<RoomStateCheckInInfoPO> checkedInRoomInfoByOrderNos = roomStateCheckInInfoService.getCheckedInRoomInfoByOrderNos(heOrderEntities.stream().map(HeOrderEntity::getOrderSn).collect(Collectors.toList()));
        for (HeOrderEntity heOrderEntity : heOrderEntities) {
            Optional<RoomStateCheckInInfoPO> first = checkedInRoomInfoByOrderNos.stream().filter(c -> c.getOrderNo().equals(heOrderEntity.getOrderSn())).findFirst();
            if (first.isPresent()) {
                LocalDate checkInDate = first.get().getCheckInDate();
                heOrderEntity.setFulfillmentStartDate(Date.from(checkInDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
            }
        }
        resPage.setRecords(orderConverter.orderPoListEntity(page.getRecords()));
        return resPage;
    }

    @Override
    public Page<HeOrderEntity> queryList(WechatMyOrderNewQuery req) {

        List<Integer> refundOrderIdList = new ArrayList<>();

        List<Integer> refundStatusList = req.getRefundStatusList();

        if (
                CollectionUtil.isNotEmpty(refundStatusList) &&
                        refundStatusList.contains(OrderRefundStatusEnum.PART_OF_THE_REFUND.getCode()) &&
                        refundStatusList.contains(OrderRefundStatusEnum.PARTIAL_REFUND.getCode()) &&
                        refundStatusList.contains(OrderRefundStatusEnum.FULL_REFUND_IN_PROGRESS.getCode()) &&
                        refundStatusList.contains(OrderRefundStatusEnum.FULL_REFUND.getCode())
        ) {
            //如果有退款
            refundOrderIdList.addAll(orderRefundRepository.queryAllOrderId());
            req.setRefundStatusList(new ArrayList<>());
        }


        String operatorGuid = req.getOperator().getOperatorGuid();
        IPage<HeOrderPO> page = heOrderMapper.selectPage(new Page<>(req.getPageNum(), req.getPageSize()),
                Wrappers.<HeOrderPO>lambdaQuery()
                        .and(StringUtils.isNotBlank(operatorGuid), wrapper ->
                                wrapper.eq(HeOrderPO::getStaffId, operatorGuid)
                                        .or().eq(HeOrderPO::getCreateBy, operatorGuid))
                        .eq(Objects.nonNull(req.getStoreId()), HeOrderPO::getStoreId, req.getStoreId())
                        .eq(Objects.nonNull(req.getClientId()), HeOrderPO::getClientUid, req.getClientId())
                        .in(CollectionUtils.isNotEmpty(req.getOrderStatusList()), HeOrderPO::getOrderStatus, req.getOrderStatusList())
                        .in(CollectionUtils.isNotEmpty(req.getRefundStatusList()), HeOrderPO::getRefundStatus, req.getRefundStatusList())
                        .in(CollectionUtils.isNotEmpty(refundOrderIdList), HeOrderPO::getOrderId, refundOrderIdList)
                        .eq(HeOrderPO::getVersion, 3)
                        .eq(HeOrderPO::getIsDelete, false)
                        .and(CollectionUtils.isNotEmpty(req.getOrderIds()) || CollectionUtils.isNotEmpty(req.getClientIds()), item -> item.in(CollectionUtils.isNotEmpty(req.getClientIds()), HeOrderPO::getClientUid, req.getClientIds()).or().in(CollectionUtils.isNotEmpty(req.getOrderIds()), HeOrderPO::getOrderId, req.getOrderIds()))
                        .orderByAsc(HeOrderPO::getIsClose)
                        .orderByAsc(HeOrderPO::getOrderStatus)
                        .orderByAsc(HeOrderPO::getPayStatus)
                        .orderByDesc(HeOrderPO::getOrderId)
        );
        Page<HeOrderEntity> resPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        resPage.setPages(page.getPages());
        resPage.setRecords(orderConverter.orderPoListEntity(page.getRecords()));
        return resPage;
    }

    @Override
    public Page<HeOrderEntity> queryList(MyOrderNewQuery req) {
        IPage<HeOrderPO> page = heOrderMapper.selectPage(new Page<>(req.getPageNum(), req.getPageSize()),
                Wrappers.<HeOrderPO>lambdaQuery()
                        .eq(Objects.nonNull(req.getBasicUid()), HeOrderPO::getBasicUid, req.getBasicUid())
                        .ne(HeOrderPO::getOrderStatus, OrderStatusV2Enum.CLOSE.getCode())
                        .and(common -> common.in(CollectionUtils.isNotEmpty(req.getOrderTypeList()), HeOrderPO::getOrderType, req.getOrderTypeList())
                                .in(CollectionUtils.isNotEmpty(req.getPayStatusList()), HeOrderPO::getPayStatus, req.getPayStatusList())
                                .in(CollectionUtils.isNotEmpty(req.getOrderStatusList()), HeOrderPO::getOrderStatus, req.getOrderStatusList())
                                .in(CollectionUtils.isNotEmpty(req.getRefundStatusList()), HeOrderPO::getRefundStatus, req.getRefundStatusList())
                                .in(CollectionUtils.isNotEmpty(req.getOrderIdList()), HeOrderPO::getOrderId, req.getOrderIdList())
                                .ge(req.getWaitPay(), HeOrderPO::getVersion, 3)
                                .or(req.getWaitPay(), oldOrder -> oldOrder
                                        .eq(HeOrderPO::getOrderType, OmniOrderTypeEnum.MONTH_ORDER.getCode())
                                        .in(HeOrderPO::getPayStatus, req.getPayStatusList())
                                        .lt(HeOrderPO::getVersion, 3))
                                .or(req.getWaitDelivery(), item -> item
                                        .in(HeOrderPO::getOrderType, Lists.newArrayList(OmniOrderTypeEnum.MONTH_ORDER.getCode(), OmniOrderTypeEnum.SMALL_MONTH_ORDER.getCode(), OmniOrderTypeEnum.PRODUCTION_ORDER.getCode()))
                                        .eq(HeOrderPO::getOrderStatus, OmniOrderStatusEnum.WAIT_DELIVERY.getCode())
                                        .ge(HeOrderPO::getVersion, 3)))
                        .orderByAsc(HeOrderPO::getOrderStatus)
                        .orderByAsc(HeOrderPO::getPayStatus)
                        .orderByDesc(HeOrderPO::getOrderId)
        );
        Page<HeOrderEntity> resPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        resPage.setPages(page.getPages());
        resPage.setRecords(orderConverter.orderPoListEntity(page.getRecords()));
        return resPage;
    }

    @Override
    public Page<HeOrderEntity> queryList(OrderPerformanceReq query) {
        LambdaQueryWrapper<HeOrderPO> heOrderPOLambdaQueryWrapper = Wrappers.<HeOrderPO>lambdaQuery()
                .eq(StringUtils.isNotBlank(query.getOrderNo()), HeOrderPO::getOrderSn, query.getOrderNo())
                .eq(StringUtils.isNotEmpty(query.getBasicUid()), HeOrderPO::getBasicUid, query.getBasicUid())
                .in(CollectionUtils.isNotEmpty(query.getClientIds()), HeOrderPO::getClientUid, query.getClientIds())
                .in(CollectionUtils.isNotEmpty(query.getStoreIdListByBrand()), HeOrderPO::getStoreId, query.getStoreIdListByBrand())
                .in(CollectionUtils.isNotEmpty(query.getStoreIdList()), HeOrderPO::getStoreId, query.getStoreIdList())
                .in(CollectionUtils.isNotEmpty(query.getOrderIdList()), HeOrderPO::getOrderId, query.getOrderIdList())
                .ge(Objects.nonNull(query.getDateStart()) && query.getDateType() == 0, HeOrderPO::getCreatedAt, query.getDateStart())
                .le(Objects.nonNull(query.getDateEnd()) && query.getDateType() == 0, HeOrderPO::getCreatedAt, query.getDateEnd())
                .ge(Objects.nonNull(query.getDateStart()) && query.getDateType() == 1, HeOrderPO::getPayFirstTime, query.getDateStart())
                .le(Objects.nonNull(query.getDateEnd()) && query.getDateType() == 1, HeOrderPO::getPayFirstTime, query.getDateEnd())
                .ge(Objects.nonNull(query.getDateStart()) && query.getDateType() == 2, HeOrderPO::getPercentFirstTime, query.getDateStart())
                .le(Objects.nonNull(query.getDateEnd()) && query.getDateType() == 2, HeOrderPO::getPercentFirstTime, query.getDateEnd())
                .eq(HeOrderPO::getIsDelete, false)
                .orderByDesc(HeOrderPO::getCreatedAt);
        List<String> orderSnList = null;
        if (query.getDateType() == 3 && ObjectUtil.isNotEmpty(query.getDateStart()) && ObjectUtil.isNotEmpty(query.getDateEnd())) {
            PageOrderNoReq pageOrderNoReq = new PageOrderNoReq();
            pageOrderNoReq.setPageNum(1);
            pageOrderNoReq.setPageSize(pageSize);
            pageOrderNoReq.setStartDate(new Date(query.getDateStart().longValue() * 1000));
            pageOrderNoReq.setEndDate(new Date(query.getDateEnd().longValue() * 1000));
            pageOrderNoReq.setCheckInStatusList(Arrays.asList(2, 3));
            log.info("根据入住时间开始查询：{}", JsonUtil.write(pageOrderNoReq));
            PageVO<String> stringPageVO = roomExternalQuery.pageOrderNoByDate(pageOrderNoReq);
            log.info("根据入住时间查询结果：{}", JsonUtil.write(stringPageVO));
            orderSnList = stringPageVO.getList();
            if (CollectionUtils.isNotEmpty(orderSnList)) {
                heOrderPOLambdaQueryWrapper.in(HeOrderPO::getOrderSn, orderSnList);
            } else {
                heOrderPOLambdaQueryWrapper.isNull(HeOrderPO::getOrderSn);
            }
        }
        Page page = this.page(new Page<>(query.getPageNum(), query.getPageSize()), heOrderPOLambdaQueryWrapper);
        page.setRecords(orderConverter.orderPoListEntity(page.getRecords()));
        return page;
    }

    @Override
    public Page<HeOrderEntity> queryIsLaHealthyCustomerList(IsLaHealthyCustomerReq req) {
        IPage<HeOrderPO> page = heOrderMapper.selectPage(new Page<>(req.getPageNum(), req.getPageSize()),
                Wrappers.<HeOrderPO>lambdaQuery()
                        .in(CollectionUtil.isNotEmpty(req.getStoreIdList()), HeOrderPO::getStoreId, req.getStoreIdList())
                        .in(HeOrderPO::getOrderType, Lists.newArrayList(OmniOrderTypeEnum.MONTH_ORDER.getCode(), OmniOrderTypeEnum.SMALL_MONTH_ORDER.getCode()))
                        .eq(HeOrderPO::getOrderStatus, OrderStatusV2Enum.STAY_IN.getCode())
                        .in(CollectionUtil.isNotEmpty(req.getBasicIdList()), HeOrderPO::getBasicUid, req.getBasicIdList())
                        .orderByDesc(HeOrderPO::getUpdatedAt)
        );
        Page<HeOrderEntity> resPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        resPage.setPages(page.getPages());
        resPage.setRecords(orderConverter.orderPoListEntity(page.getRecords()));
        return resPage;
    }

    @Override
    public Page<HeOrderEntity> queryList(OrderPaySuccessReq req, List<Integer> giftOrderIdList) {
        long payTimeStart = Objects.isNull(req.getPayTimeStart()) ? 0 : req.getPayTimeStart().getTime() / 1000;
        long payTimeEnd = Objects.isNull(req.getPayTimeEnd()) ? 0 : req.getPayTimeEnd().getTime() / 1000;
        IPage<HeOrderPO> page = heOrderMapper.selectPage(new Page<>(req.getPageNum(), req.getPageSize()),
                Wrappers.<HeOrderPO>lambdaQuery()
                        .in(CollectionUtils.isNotEmpty(req.getOrderIdList()), HeOrderPO::getOrderId, req.getOrderIdList())
                        .in(CollectionUtils.isNotEmpty(req.getPayStatusList()), HeOrderPO::getPayStatus, req.getPayStatusList())
                        .in(CollectionUtils.isNotEmpty(req.getStoreIdList()), HeOrderPO::getStoreId, req.getStoreIdList())
                        .eq(Objects.nonNull(req.getOrderType()), HeOrderPO::getOrderType, req.getOrderType())
                        .ge(Objects.nonNull(req.getPayTimeStart()) && Objects.nonNull(req.getPayTimeEnd()), HeOrderPO::getPayFinishAt, payTimeStart)
                        .le(Objects.nonNull(req.getPayTimeStart()) && Objects.nonNull(req.getPayTimeEnd()), HeOrderPO::getPayFinishAt, payTimeEnd)
                        .ge(req.getQueryNewOrder(), HeOrderPO::getVersion, 3)
                        .eq(HeOrderPO::getIsDelete, false)
                        .or(CollectionUtils.isNotEmpty(giftOrderIdList), item -> item.in(HeOrderPO::getOrderId, giftOrderIdList))
                        .orderByDesc(HeOrderPO::getOrderId)
        );
        Page<HeOrderEntity> resPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        resPage.setPages(page.getPages());
        resPage.setRecords(orderConverter.orderPoListEntity(page.getRecords()));
        return resPage;
    }

    @Override
    public List<HeOrderEntity> getListByOrderSn(String keyword, Long basicUid) {
        return orderConverter.orderPoListEntity(this.list(new LambdaQueryWrapper<HeOrderPO>().like(HeOrderPO::getOrderSn, keyword).eq(HeOrderPO::getBasicUid, basicUid)));
    }

    @Override
    public List<HeOrderEntity> filterBasicUid(List<Integer> categoryOrderIdList, Long basicUid) {
        return orderConverter.orderPoListEntity(this.list(new LambdaQueryWrapper<HeOrderPO>().in(HeOrderPO::getOrderId, categoryOrderIdList).eq(HeOrderPO::getBasicUid, basicUid)));
    }

    @Override
    public void batchUpdateById(List<HeOrderEntity> orderList) {
        this.updateBatchById(orderConverter.entityList2PoList(orderList));
    }

    @Override
    public Page<HeOrderEntity> pageByOrderIdList(Integer pageNum, Integer pageSize, List<Integer> orderIdList) {
        IPage<HeOrderPO> page = heOrderMapper.selectPage(new Page<>(pageNum, pageSize),
                new LambdaQueryWrapper<HeOrderPO>().in(HeOrderPO::getOrderId, orderIdList).orderByDesc(HeOrderPO::getUpdatedAt)
        );
        Page<HeOrderEntity> resPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        resPage.setPages(page.getPages());
        resPage.setRecords(orderConverter.orderPoListEntity(page.getRecords()));
        return resPage;
    }

    /**
     * same method pageByOrderIdList required percentFirstTime
     */
    @Override
    public Page<HeOrderEntity> pageByOrderIdListAndPercentPay(Integer pageNum, Integer pageSize, List<Integer> orderIdList, Boolean flg) {
        IPage<HeOrderPO> page = heOrderMapper.selectPage(new Page<>(pageNum, pageSize),
                new LambdaQueryWrapper<HeOrderPO>()
                        .in(HeOrderPO::getOrderId, orderIdList)
                        .isNotNull(flg, HeOrderPO::getPercentFirstTime)
                        .isNull(flg, HeOrderPO::getFullRefundDate)
                        .gt(flg, HeOrderPO::getPercentFirstTime, 0)
                        .eq(HeOrderPO::getIsClose, 0)
                        .ne(HeOrderPO::getRefundStatus, OrderRefundStatusEnum.FULL_REFUND.getCode())
                        .orderByDesc(HeOrderPO::getUpdatedAt)
        );
        Page<HeOrderEntity> resPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        resPage.setPages(page.getPages());
        resPage.setRecords(orderConverter.orderPoListEntity(page.getRecords()));
        return resPage;
    }

    @Override
    public Page<HeOrderEntity> queryGiftOrderList(WechatMyOrderQuery req) {
        IPage<HeOrderPO> page = heOrderMapper.selectPage(new Page<>(req.getPageNum(), req.getPageSize()),
                Wrappers.<HeOrderPO>lambdaQuery()
                        .gt(HeOrderPO::getPercentFirstTime, 0)
                        .and(item -> item.gt(HeOrderPO::getRealAmount, 0)
                                .or(subItem -> subItem.gt(HeOrderPO::getPaidAmount, 0).lt(HeOrderPO::getVersion, 3)))
                        .eq(Objects.nonNull(req.getBasicUid()), HeOrderPO::getBasicUid, req.getBasicUid())
                        .ne(HeOrderPO::getOrderStatus, OrderStatusV2Enum.CLOSE.getCode())
                        .in(CollectionUtils.isNotEmpty(req.getStoreIds()), HeOrderPO::getStoreId, req.getStoreIds())
                        .in(CollectionUtils.isNotEmpty(req.getOrderIds()), HeOrderPO::getOrderId, req.getOrderIds())
                        .orderByAsc(HeOrderPO::getOrderStatus)
                        .orderByAsc(HeOrderPO::getPayStatus)
                        .orderByDesc(HeOrderPO::getOrderId)
        );
        Page<HeOrderEntity> resPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        resPage.setPages(page.getPages());
        resPage.setRecords(orderConverter.orderPoListEntity(page.getRecords()));
        return resPage;
    }

    @Override
    public List<Integer> queryByClientIdsAndAssetType(List<Integer> clientIdList, List<Integer> list, Integer storeId, String orderSn) {
        return heOrderMapper.queryByClientIdsAndAssetType(clientIdList, list, storeId, orderSn);
    }

    @Override
    public List<HeOrderEntity> listOldOrders() {
        List<HeOrderPO> heOrderPOS = heOrderMapper.listOldOrders2Trans();
        return orderConverter.orderPoListEntity(heOrderPOS);
    }

    @Override
    public List<HeOrderEntity> queryOrdersForMigration(int offset, int pageSize) {
        LambdaQueryWrapper<HeOrderPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HeOrderPO::getIsDelete, 0)
               .orderBy(true, true, HeOrderPO::getOrderId)
               .last("LIMIT " + offset + ", " + pageSize);

        List<HeOrderPO> orderPOList = this.list(wrapper);
        return orderConverter.orderPoListEntity(orderPOList);
    }

    @Override
    public List<HeOrderEntity> queryOrdersForMigrationByRange(Integer startOrderId, Integer pageSize, Integer endOrderId) {
        LambdaQueryWrapper<HeOrderPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HeOrderPO::getIsDelete, 0)
               .ge(HeOrderPO::getOrderId, startOrderId)
               .le(HeOrderPO::getOrderId, endOrderId)
               .orderBy(true, true, HeOrderPO::getOrderId)
               .last("LIMIT " + pageSize);

        List<HeOrderPO> orderPOList = this.list(wrapper);
        return orderConverter.orderPoListEntity(orderPOList);
    }

    @Override
    public Page<HeOrderEntity> queryList(String orderSn, Integer pageNum, Integer pageSize) {
        IPage<HeOrderPO> page = heOrderMapper.selectPage(new Page<>(pageNum, pageSize),
                Wrappers.<HeOrderPO>lambdaQuery()
                        .eq(StringUtils.isNotEmpty(orderSn), HeOrderPO::getOrderSn, orderSn)
                        .orderByAsc(HeOrderPO::getOrderId)
        );
        Page<HeOrderEntity> resPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        resPage.setPages(page.getPages());
        resPage.setRecords(orderConverter.orderPoListEntity(page.getRecords()));
        return resPage;
    }

    @Override
    public Page<HeOrderEntity> queryListByBasicUid(WechatMyOrderQuery basicUid) {

        List<HeOrderPO> allBasicOrderId = heOrderMapper.selectList(
                Wrappers.<HeOrderPO>lambdaQuery()
                        .eq(HeOrderPO::getBasicUid, basicUid.getBasicUid())
        );
        if (CollectionUtils.isEmpty(allBasicOrderId)) {
            return new Page<>();
        }

        List<String> orderSN = allBasicOrderId.stream().map(HeOrderPO::getOrderSn).collect(Collectors.toList());

        IPage<HeOrderPO> page = heOrderMapper.selectPage(new Page<>(basicUid.getPageNum(), basicUid.getPageSize()),
                Wrappers.<HeOrderPO>lambdaQuery()
                        .eq(HeOrderPO::getBasicUid, basicUid.getBasicUid())
                        .eq(StringUtils.isNotEmpty(basicUid.getOrderNo()), HeOrderPO::getOrderSn, basicUid.getOrderNo())
                        .in(CollectionUtils.isNotEmpty(basicUid.getClientIdList()), HeOrderPO::getClientUid, basicUid.getClientIdList())
                        .in(CollectionUtils.isNotEmpty(basicUid.getStoreIds()), HeOrderPO::getStoreId, basicUid.getStoreIds())
                        .in(CollectionUtils.isNotEmpty(basicUid.getOrderIds()), HeOrderPO::getOrderId, basicUid.getOrderIds())
                        .and(w ->
                                w.and(inner -> inner.eq(HeOrderPO::getVersion, new BigDecimal(3.0)).in(HeOrderPO::getOrderSn, orderSN))
                                        .or(inner -> inner.eq(HeOrderPO::getVerificationStatus, 1))
                                        .or(inner -> inner.ne(HeOrderPO::getVersion, new BigDecimal(3.0)).in(HeOrderPO::getOrderStatus, Arrays.asList(OmniOrderStatusEnum.WAIT_RECEIVE.getCode(), OmniOrderStatusEnum.FINISH.getCode(), OmniOrderStatusEnum.ADVANCE_OUT_OF_STORE.getCode())))
                        ).orderByDesc(HeOrderPO::getUpdatedAt));
        Page<HeOrderEntity> resPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        resPage.setPages(page.getPages());
        resPage.setRecords(orderConverter.orderPoListEntity(page.getRecords()));
        return resPage;
    }

    @Override
    public Page<HeOrderEntity> querySurveyCustomerList(SurveyCustomerReq req) {
        IPage<HeOrderPO> page = heOrderMapper.selectPage(new Page<>(req.getPageNum(), req.getPageSize()),
                Wrappers.<HeOrderPO>lambdaQuery()
                        .in(HeOrderPO::getStoreId, req.getStoreIdList())
                        .eq(HeOrderPO::getOrderType, OmniOrderTypeEnum.MONTH_ORDER.getCode())
                        .eq(HeOrderPO::getOrderStatus, OrderStatusV2Enum.STAY_IN.getCode())
                        .ne(HeOrderPO::getRefundStatus, OrderRefundStatusEnum.FULL_REFUND.getCode())
                        .in(CollectionUtil.isNotEmpty(req.getBasicIdList()), HeOrderPO::getBasicUid, req.getBasicIdList())
                        .orderByDesc(HeOrderPO::getUpdatedAt)
        );
        Page<HeOrderEntity> resPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        resPage.setPages(page.getPages());
        resPage.setRecords(orderConverter.orderPoListEntity(page.getRecords()));
        return resPage;
    }

    @Override
    public List<HeOrderEntity> getInfoByByBasicUid(List<Integer> basicUidList, List<Integer> orderType) {
        List<HeOrderPO> orderList = list(new LambdaQueryWrapper<HeOrderPO>()
                .in(HeOrderPO::getBasicUid, basicUidList)
                .in(ObjectUtil.isNotEmpty(orderType), HeOrderPO::getOrderType, orderType)
                .orderByDesc(HeOrderPO::getCreatedAt)
        );
        return CollectionUtil.isEmpty(orderList) ? new ArrayList<>() : orderConverter.orderPoListEntity(orderList);
    }

    private LambdaQueryWrapper<HeOrderPO> buildQueryWrapper(OrderPageReq query) {

        LambdaQueryWrapper<HeOrderPO> heOrderPOLambdaQueryWrapper = Wrappers.<HeOrderPO>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(query.getOrderTypeList()), HeOrderPO::getOrderType, query.getOrderTypeList())
                .eq(StringUtils.isNotBlank(query.getOrderNo()), HeOrderPO::getOrderSn, query.getOrderNo())
                .eq(StringUtils.isNotEmpty(query.getBasicUid()), HeOrderPO::getBasicUid, query.getBasicUid())
                .eq(Objects.nonNull(query.getBasicId()), HeOrderPO::getBasicUid, query.getBasicId())
                .eq(Objects.nonNull(query.getPayStatus()), HeOrderPO::getPayStatus, query.getPayStatus())
                .in(CollectionUtils.isNotEmpty(query.getOrderStatusList()), HeOrderPO::getOrderStatus, query.getOrderStatusList())
                .in(CollectionUtils.isNotEmpty(query.getPayStatusList()), HeOrderPO::getPayStatus, query.getPayStatusList())
                .eq(Objects.nonNull(query.getRefundStatus()), HeOrderPO::getRefundStatus, query.getRefundStatus())
                .eq(Objects.nonNull(query.getApprovalDiscountStatus()), HeOrderPO::getApprovalDiscountStatus, query.getApprovalDiscountStatus())
                .in(CollectionUtils.isNotEmpty(query.getOrderTagList()), HeOrderPO::getOrderTag, query.getOrderTagList())
                .in(CollectionUtils.isNotEmpty(query.getClientIds()), HeOrderPO::getClientUid, query.getClientIds())
                .in(CollectionUtils.isNotEmpty(query.getOrderIdByCategory()), HeOrderPO::getOrderId, query.getOrderIdByCategory())
                .in(CollectionUtils.isNotEmpty(query.getStoreIdListByBrand()), HeOrderPO::getStoreId, query.getStoreIdListByBrand())
                .in(CollectionUtils.isNotEmpty(query.getStoreIdList()), HeOrderPO::getStoreId, query.getStoreIdList())
                .eq(HeOrderPO::getVersion, 3)
                .ge(Objects.nonNull(query.getDateStart()) && query.getDateType() == 0, HeOrderPO::getCreatedAt, query.getDateStart())
                .le(Objects.nonNull(query.getDateEnd()) && query.getDateType() == 0, HeOrderPO::getCreatedAt, query.getDateEnd())
                .ge(Objects.nonNull(query.getDateStart()) && query.getDateType() == 1, HeOrderPO::getPayFirstTime, query.getDateStart())
                .le(Objects.nonNull(query.getDateEnd()) && query.getDateType() == 1, HeOrderPO::getPayFirstTime, query.getDateEnd())
                .ge(Objects.nonNull(query.getDateStart()) && query.getDateType() == 2, HeOrderPO::getPercentFirstTime, query.getDateStart())
                .le(Objects.nonNull(query.getDateEnd()) && query.getDateType() == 2, HeOrderPO::getPercentFirstTime, query.getDateEnd())
                .eq(Objects.nonNull(query.getScene()), HeOrderPO::getScene, query.getScene())
                .eq(HeOrderPO::getIsDelete, false)
                .orderByDesc(HeOrderPO::getCreatedAt);

        List<String> orderSnList = null;
        if (query.getDateType() == 3 && ObjectUtil.isNotEmpty(query.getDateStart()) && ObjectUtil.isNotEmpty(query.getDateEnd())) {
            PageOrderNoReq pageOrderNoReq = new PageOrderNoReq();
            pageOrderNoReq.setPageNum(1);
            pageOrderNoReq.setPageSize(pageSize);
            pageOrderNoReq.setStartDate(new Date(query.getDateStart().longValue() * 1000));
            pageOrderNoReq.setEndDate(new Date(query.getDateEnd().longValue() * 1000));
            pageOrderNoReq.setCheckInStatusList(Arrays.asList(2, 3));
            log.info("根据入住时间开始查询：{}", JsonUtil.write(pageOrderNoReq));
            PageVO<String> stringPageVO = roomExternalQuery.pageOrderNoByDate(pageOrderNoReq);
            log.info("根据入住时间查询结果：{}", JsonUtil.write(stringPageVO));
            orderSnList = stringPageVO.getList();
            if (CollectionUtils.isNotEmpty(orderSnList)) {
                heOrderPOLambdaQueryWrapper.in(HeOrderPO::getOrderSn, orderSnList);
            } else {
                heOrderPOLambdaQueryWrapper.isNull(HeOrderPO::getOrderSn);
            }
        }
        return heOrderPOLambdaQueryWrapper;
    }
}

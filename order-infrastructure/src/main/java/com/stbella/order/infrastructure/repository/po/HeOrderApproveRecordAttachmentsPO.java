package com.stbella.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 钉钉审批评论附件表
 * </p>
 *
 * <AUTHOR> @since 2022-10-27
 */
@Data
@Accessors(chain = true)
@TableName("he_order_approve_record_attachments")
@ApiModel(value = "HeOrderApproveRecordAttachmentsPO对象", description = "钉钉审批评论附件表")
public class HeOrderApproveRecordAttachmentsPO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "审批操作记录id")
    private Long operationId;

    @ApiModelProperty(value = "附件名称")
    @TableField("fileName")
    private String fileName;

    @ApiModelProperty(value = "附件大小")
    @TableField("fileSize")
    private String fileSize;

    @ApiModelProperty(value = "附件ID")
    @TableField("fileId")
    private String fileId;

    @ApiModelProperty(value = "附件类型")
    @TableField("fileType")
    private String fileType;


}

package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.server.order.month.req.OrderMonthAdditionalRevenueCacheReq;
import com.stbella.order.server.order.month.res.OrderAdditionalRevenueCacheVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface OrderAdditionalRevenueConverter {

    List<OrderMonthAdditionalRevenueCacheReq> voListToReq(List<OrderAdditionalRevenueCacheVO> orderAdditionalRevenueCacheVOS);

}

package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.production.OrderProductionCardExtendEntity;
import com.stbella.order.domain.order.production.OrderProductionExtendEntity;
import com.stbella.order.infrastructure.repository.po.OrderProductionCardExtend;
import com.stbella.order.infrastructure.repository.po.OrderProductionExtend;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface OrderProductionCardExtendConverter {

    /**
     * po -> entity
     *
     * @param po
     * @return com.stbella.order.domain.order.production.OrderProductionCardExtendEntity
     * @throws
     * <AUTHOR>
     * @date 2022/10/13 10:31
     * @since 1.0.0
     */
    OrderProductionCardExtendEntity po2EntityForProductionCardExtend(OrderProductionCardExtend po);

    /**
     * poList -> entity
     *
     * @param poList polist
     * @return
     */
    List<OrderProductionCardExtendEntity> po2EntityForListProductionCardExtend(List<OrderProductionCardExtend> poList);

    /**
     * entity -> po
     *
     * @param po
     * @return com.stbella.order.infrastructure.repository.po.OrderProductionCardExtend
     * @throws
     * <AUTHOR>
     * @date 2022/10/13 10:54
     * @since 1.0.0
     */
    OrderProductionCardExtend entity2POForProductionCardExtend(OrderProductionCardExtendEntity po);

    List<OrderProductionCardExtend> entityList2POList(List<OrderProductionCardExtendEntity> entityList);

}

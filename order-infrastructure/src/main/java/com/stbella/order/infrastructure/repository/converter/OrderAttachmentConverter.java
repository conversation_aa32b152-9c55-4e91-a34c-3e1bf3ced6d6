package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.month.entity.HeOrderAttachmentEntity;
import com.stbella.order.infrastructure.repository.po.HeOrderAttachmentPO;
import com.stbella.order.server.order.month.response.OrderAttachmentVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20 14:09
 */
@Mapper(componentModel = "spring")
public interface OrderAttachmentConverter {

    /**
     * po -> entity 多条
     * @param orderAttachmentPOS
     * @return {@link List}<{@link HeOrderAttachmentEntity}>
     */
    List<HeOrderAttachmentEntity> po2OrderAttachmentList(List<HeOrderAttachmentPO> orderAttachmentPOS);


    /**
     * entity -> po 单条
     *
     * @param entity
     * @return {@link HeOrderAttachmentPO}
     */
    HeOrderAttachmentPO entity2OrderAttachmentPO(HeOrderAttachmentEntity entity);

    List<OrderAttachmentVO> entity2VOList(List<HeOrderAttachmentEntity> entityList);

}

package com.stbella.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 客户表
 * </p>
 *
 * <AUTHOR> @since 2022-10-27
 */
@Data
@Accessors(chain = true)
@TableName("tab_client")
@ApiModel(value = "TabClientPO对象", description = "客户表")
public class TabClientPO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "系统全局用户表")
    private Integer basicUid;

    @ApiModelProperty(value = "所属门店")
    private Integer storeId;

    @ApiModelProperty(value = "客户姓名")
    private String name;

    @ApiModelProperty(value = "会员等级: 1..")
    private Integer memberLevel;

    @ApiModelProperty(value = "手机号码")
    private String phone;

    @ApiModelProperty(value = "电子邮箱")
    private String email;

    @ApiModelProperty(value = "预产期")
    private Date predictBornDate;

    @ApiModelProperty(value = "0=大陆身份证 1=护照 2=香港来往大陆通行证 3=澳门来往大陆通行证 4=台湾来往大陆通行证")
    private Integer certType;

    @ApiModelProperty(value = "身份证")
    private String idCard;

    @ApiModelProperty(value = "身份证正面 url")
    private String idCardFront;

    @ApiModelProperty(value = "身份证背面图url")
    private String idCardBack;

    @ApiModelProperty(value = "饮食忌口")
    private String diet;

    @ApiModelProperty(value = "微信号")
    private String wechat;

    @ApiModelProperty(value = "民族")
    private String nation;

    @ApiModelProperty(value = "出生日")
    private Date birthdate;

    @ApiModelProperty(value = "农历生日")
    private String lunarBirthday;

    @ApiModelProperty(value = "枚举星座类型： ‘0白羊座’,’1金牛座’,’2双子座’,’3巨蟹座’,’4狮子座’,’5处女座’,’6天秤座’,’7天蝎座’,’8射手座’,’9摩羯座’,’10水瓶座’,’11双鱼座', ")
    private Integer constellationType;

    @ApiModelProperty(value = "星座")
    private String constellation;

    @ApiModelProperty(value = "性别 0保密 1男 2女")
    private Integer sex;

    @ApiModelProperty(value = "省份。-1为其他")
    private Integer province;

    @ApiModelProperty(value = "城市。-1为其他")
    private Integer city;

    @ApiModelProperty(value = "地区。-1为其他")
    private Integer region;

    @ApiModelProperty(value = "家庭详细地址")
    private String address;

    @ApiModelProperty(value = "收货地址")
    private String receivingAddress;

    @ApiModelProperty(value = "客户状态")
    private Integer active;

    @ApiModelProperty(value = "预产次数")
    private Integer predictBornNum;

    @ApiModelProperty(value = "胎数")
    private Integer fetusNum;

    @ApiModelProperty(value = "分娩医院")
    private String hospital;

    @ApiModelProperty(value = "生产时间")
    private Date bornTime;

    @ApiModelProperty(value = "紧急联系人名字")
    private String urgentName;

    @ApiModelProperty(value = "紧急联系人号码")
    private String urgentPhone;

    @ApiModelProperty(value = "用户类型id")
    private Integer clientTypeId;

    @ApiModelProperty(value = "用户子项类型")
    private String clientSubType;

    @ApiModelProperty(value = "来源渠道")
    private Integer source;

    @ApiModelProperty(value = "子渠道")
    private Integer subSource;

    @ApiModelProperty(value = "客户标签")
    private String mark;

    @ApiModelProperty(value = "客户职业")
    private String profession;

    @ApiModelProperty(value = "业务员id")
    private Integer sellerId;

    @ApiModelProperty(value = "注册时添加人: user表的用户id")
    private Integer upId;

    @ApiModelProperty(value = "意向门店")
    private Integer woundStoreId;

    @ApiModelProperty(value = "意向套餐id")
    private Integer packageId;

    @ApiModelProperty(value = "意向月子套餐id")
    private Integer mealPackageId;

    @ApiModelProperty(value = "入住时间")
    private Date checkInDate;

    @ApiModelProperty(value = "订金")
    private Integer deposit;

    @ApiModelProperty(value = "意向费用")
    private Integer expectCost;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "最后修改者")
    private Integer lastAdminId;

    @ApiModelProperty(value = "记录时间")
    private Date recordTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "年龄")
    private Integer age;

    @ApiModelProperty(value = "血型：0未知，1A，2B，3O，4AB, 5RH")
    private Integer bloodType;

    @ApiModelProperty(value = "渠道来源：0未知-废弃，1(400大众点评)，2(400小红书)，3(400微博), 4(400线下广告)，5(400未告知)，6(400朋友推荐)，7(400未告知），8（400公众号/小程序），9（400官网），10（400直播），11（非400公众号），12（非400小红书），13（非400官网/小程序），14（非400微博），15（大众点评预约），16（大众点评在线咨询），17（新增小助手）,18（小程序预约） 19-口碑介绍, 20-合作渠道, 21-线下广告, 22-线下活动, 23-大众点评, 24-百度/谷歌, 25-官网, 26-小红书, 27-微信公众号, 28-微博, 29-其他, 30-微信小程序, 31-抖音, 32-2021抖音活动, 33-2021微信广告活动, 34-已入馆宝妈, 35-朋友圈广告, 36-医疗渠道, 37-小贝拉私域")
    private Integer fromType;

    @ApiModelProperty(value = "与委托人关系：0宝妈，1宝爸，2家属，3夫妻，4母女，5父女")
    private Integer relationWithClient;

    @ApiModelProperty(value = "标签ids, 逗号分隔")
    private String tags;

    @ApiModelProperty(value = "关注：1=楼层、环境、房型;2=月子餐口感;3=产后修复;4=泌乳调理;5=宝宝护理;6=专业水平、服务能力;7=其他")
    private Integer attention;

    @ApiModelProperty(value = "预算：1=10万以内;2=10-20万;3=20-30万;4=30万以内;5=无明确预算")
    private Integer budget;

    @ApiModelProperty(value = "0=中国大陆;1=香港;2=澳门;3=台湾省")
    private Integer phoneType;

    @ApiModelProperty(value = "0=手机号未验证过;1=已验证")
    private Integer isPhoneVerify;

    @ApiModelProperty(value = "尘锋crm系统id")
    private String chenFengId;

    @ApiModelProperty(value = "尘锋销售机会id")
    private String chenFengSaleChanceId;

    @ApiModelProperty(value = "尘锋家庭标签")
    private String familyTag;

    @ApiModelProperty(value = "大众点评api过来的门店id")
    private Integer dianpingStoreId;

    @ApiModelProperty(value = "scrmId")
    private String scrmId;

    @ApiModelProperty(value = "予家来源渠道")
    private Integer ctsFromType;


}

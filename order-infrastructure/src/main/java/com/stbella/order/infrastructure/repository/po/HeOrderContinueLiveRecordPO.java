package com.stbella.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 订单续住操作记录
 * </p>
 *
 * <AUTHOR> @since 2022-11-25
 */
@Data
@Accessors(chain = true)
@TableName("he_order_continue_live_record")
@ApiModel(value = "HeOrderContinueLiveRecordPO对象", description = "订单续住操作记录")
public class HeOrderContinueLiveRecordPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "订单编号")
    private Integer orderId;

    @ApiModelProperty(value = "房型Id")
    private Integer roomId;

    @ApiModelProperty(value = "加收项主键")
    private Long additionalRevenueId;

    @ApiModelProperty(value = "最终天数")
    private Integer finalDays;

    @ApiModelProperty(value = "房型名称")
    private String roomName;

    @ApiModelProperty(value = "本次更改的原价")
    private BigDecimal changeOriginalPrice;

    @ApiModelProperty(value = "本次更改的应收")
    private BigDecimal changePrice;

    @ApiModelProperty(value = "最终原价")
    private BigDecimal finalOriginalPrice;

    @ApiModelProperty(value = "最终应收")
    private BigDecimal finalPrice;

    @ApiModelProperty(value = "天数加减")
    private Integer days;

    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "修改时间")
    private Date gmtModified;

    @ApiModelProperty(value = "操作人id")
    private String optId;

    @ApiModelProperty(value = "操作人名称")
    private String optName;


}

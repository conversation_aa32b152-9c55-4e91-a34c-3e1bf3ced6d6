package com.stbella.order.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.order.domain.order.month.entity.HeOrderTagsEntity;
import com.stbella.order.domain.repository.TagsRepository;
import com.stbella.order.infrastructure.repository.converter.OrderTagsConverter;
import com.stbella.order.infrastructure.repository.mapper.saas.HeTagsMapper;
import com.stbella.order.infrastructure.repository.po.HeTagsPO;
import com.stbella.order.server.order.month.req.TagReq;
import com.stbella.order.server.order.month.res.TagVO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Repository
public class TagsRepositoryImpl extends ServiceImpl<HeTagsMapper, HeTagsPO> implements TagsRepository {

    @Resource
    private OrderTagsConverter orderTagsConverter;

    @Override
    public List<HeOrderTagsEntity> getAllOrderTags() {
        List<HeTagsPO> heTagsPOS = this.baseMapper.selectList(new LambdaQueryWrapper<HeTagsPO>().eq(HeTagsPO::getTagObj, 5).eq(HeTagsPO::getIsDelete, 0));
        return orderTagsConverter.poList2EntityList(heTagsPOS);
    }

    /**
     * 根据条件查询标签内容
     *
     * @param req
     * @return
     */
    @Override
    public TagVO queryTagByReq(TagReq req) {
        HeTagsPO heTagsPO = this.baseMapper.selectOne(new LambdaQueryWrapper<HeTagsPO>()
            .eq(Objects.nonNull(req.getId()), HeTagsPO::getId, req.getId())
            .eq(Objects.nonNull(req.getTagName()), HeTagsPO::getTagName, req.getTagName())
            .eq(Objects.nonNull(req.getTagObj()), HeTagsPO::getTagObj, req.getTagObj())
            .eq(Objects.nonNull(req.getTagType()), HeTagsPO::getTagType, req.getTagType())
            .eq(Objects.nonNull(req.getTagGroupId()), HeTagsPO::getTagGroupId, req.getTagGroupId())
            .eq(HeTagsPO::getIsDelete, 0)
            .last("limit 1"));

        return orderTagsConverter.tagsPO2VO(heTagsPO);
    }
}

package com.stbella.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.stbella.platform.order.api.res.PromotionInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 购物车加购商品表
 */
@Data
@Accessors(chain = true)
@TableName(value = "he_order_cart_goods", autoResultMap = true)
@ApiModel(value = "HeCartGoodsPO对象", description = "购物车加购商品表")
public class HeCartGoodsPO implements Serializable {

    @ApiModelProperty(value = "主键自增")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "购物车ID")
    private Integer cartId;

    /**
     * sn
     */
    private String itermSn;

    /**
     * 类型：2表示单项，3表示组合
     */
    private Integer type;

    /**
     * 组合商品明细中，每个商品所属于的组
     */
    @ApiModelProperty(value = "组合商品明细中，每个商品所属于的组")
    private String groupKey;

    @ApiModelProperty(value = "是否礼赠：false-否，true-是")
    private Integer gift;

    /**
     * 附属商品所属主商品记录
     */
    private String parentSn;

    @ApiModelProperty(value = "一级前台类目")
    private Integer firstFrontCategoryId;

    /**
     * 用户id
     */
    private Integer basicUid;

    /**
     * 门店id
     */
    private Integer storeId;

    @ApiModelProperty(value = "商品SpuID")
    private Integer goodsId;

    @ApiModelProperty(value = "原商品ID（用于订单升级）")
    private Integer originalGoodsId;

    @ApiModelProperty(value = "原商品数量（用于订单升级）")
    private Integer originalGoodsNum;

    @ApiModelProperty(value = "goodsType")
    private Integer goodsType;

    @ApiModelProperty(value = "商品SkuID")
    private Integer skuId;

    /**
     * 商品名称
     */
    private String skuName;


    @ApiModelProperty(value = "规格名称")
    private String specification;

    @ApiModelProperty(value = "加购数量")
    private Integer num;

    /**
     * 包含sku数量
     * 组合商品明细中，一个商品可能包含多个sku
     */
    @ApiModelProperty(value = "包含sku数量")
    private Integer skuNum = 1;

    @ApiModelProperty(value = "创建时间")
    private Date addTime;

    @ApiModelProperty(value = "最近更新时间")
    private Date updateTime;

    @TableLogic(value = "false", delval = "true")
    @ApiModelProperty(value = "是否删除：false-否，true-是")
    private Integer deleted;

    @ApiModelProperty("附件名称")
    private String goodsAttachmentName;

    @ApiModelProperty("附件url")
    private String goodsAttachment;

    @ApiModelProperty("商品图片")
    private String skuUrl;

    @ApiModelProperty("物品清单")
    private String inventoryUrl;

    @ApiModelProperty(value = "活动信息")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private PromotionInfo promotionInfo;

    @ApiModelProperty(value = "组合类型：0-固定搭配；1;可选")
    private Integer combinationType;

    @ApiModelProperty(value = "0-单规格；1-多规格")
    private Integer specificationType;



}

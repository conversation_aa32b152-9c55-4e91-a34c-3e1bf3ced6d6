package com.stbella.order.infrastructure.repository.condition;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 分页查询基类
 * <AUTHOR>
 * @Date 2020/8/19 9:21 AM
 * @Version 1.0
 */
@Data
public class BaseCondition implements Serializable {

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    /**
     * 页数
     */
    private Integer pageNum = 1;

    /**
     * 偏移量
     * long offset = (req.getPageIndex() - 1) * req.getPageSize();
     */
    private Long offset = 0L;


    public Integer getLimit() {
        return pageSize;
    }

    public Long getOffset() {
        return (long) ((pageNum - 1) * pageSize);
    }

    public void defaultSet() {
        this.pageNum = 1;
        this.pageSize = 10;
    }

}

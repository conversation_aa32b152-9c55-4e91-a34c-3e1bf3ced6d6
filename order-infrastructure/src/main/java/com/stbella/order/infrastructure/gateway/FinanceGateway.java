package com.stbella.order.infrastructure.gateway;

import com.stbella.financial.res.CompanyInfoVO;
import com.stbella.financial.res.OrderTradeFlowPageVO;

import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-12-05  17:03
 * @Description: 财务网关
 */
public interface FinanceGateway {

    /**
     *
     * @param id
     * @return
     */
    CompanyInfoVO queryCompanyInfo(Long id);

    /**
     * 根据支付ID查询线下支付对账时间
     * @param payRecordIdList
     * @return
     */
    List<OrderTradeFlowPageVO> orderTradeFlowPage(List<Long> payRecordIdList);
}

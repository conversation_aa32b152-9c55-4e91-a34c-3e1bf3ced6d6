package com.stbella.order.infrastructure.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.order.domain.order.month.entity.HeOrderIncomeRecordTransferLogEntity;
import com.stbella.order.domain.repository.HeOrderIncomeRecordTransferLogRepository;
import com.stbella.order.infrastructure.repository.converter.OrderIncomeRecordTransferLogConverter;
import com.stbella.order.infrastructure.repository.mapper.saas.HeOrderIncomeRecordTransferLogMapper;
import com.stbella.order.infrastructure.repository.po.HeOrderIncomeRecordTransferLog;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-08-14
 */
@Service
public class HeOrderIncomeRecordTransferLogRepositoryImpl extends ServiceImpl<HeOrderIncomeRecordTransferLogMapper, HeOrderIncomeRecordTransferLog> implements HeOrderIncomeRecordTransferLogRepository {

    @Resource
    private OrderIncomeRecordTransferLogConverter converter;

    @Override
    public Boolean saveOne(HeOrderIncomeRecordTransferLogEntity entity) {

        return save(converter.entity2PO(entity));
    }
}

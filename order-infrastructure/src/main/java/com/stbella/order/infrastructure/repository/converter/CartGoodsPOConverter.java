package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.entity.HeCartGoodsEntity;
import com.stbella.order.infrastructure.repository.po.HeCartGoodsPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CartGoodsPOConverter {

    @Mappings({
            @Mapping(target = "gift", expression = "java(entity.isGift()?1:0)")
    })
    HeCartGoodsPO entity2PO(HeCartGoodsEntity entity);

    List<HeCartGoodsPO> entityList2POList(List<HeCartGoodsEntity> entityList);

    @Mappings({
            @Mapping(target = "gift", expression = "java(po.getGift() == 1 ? true:false)")
    })
    HeCartGoodsEntity po2Entity(HeCartGoodsPO po);

    List<HeCartGoodsEntity> poList2EntityList(List<HeCartGoodsPO> poList);

}

package com.stbella.order.infrastructure.utils;

import com.stbella.order.common.enums.ErrorCodeEnum;
import com.stbella.order.common.exception.ApplicationException;
import com.stbella.order.domain.utils.TransactionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * @Author: jock
 * @CreateTime: 2022-08-20  22:28
 * @Description: 编程事务
 */
@Component
@Slf4j
public class TransactionServiceImpl implements TransactionUtil {

    private final List<String> localUnNoticeErrorCodes = Arrays.asList(
            ErrorCodeEnum.ILLEGAL_PARAMETERS.code().toString()
    );

    @Autowired
    @Qualifier("therapistTransactionManager")
    private PlatformTransactionManager transactionManager;

    @Override
    public void transact(Consumer consumer) {
        TransactionStatus status = transactionManager.getTransaction(new DefaultTransactionDefinition());
        try {
            consumer.accept(null);
            transactionManager.commit(status);
        } catch (Exception e) {
            String code = null;
            if (e instanceof ApplicationException) {
                ApplicationException applicationException = (ApplicationException) e;
                code = applicationException.getCode();
            }

            if (Objects.isNull(code) || !localUnNoticeErrorCodes.contains(code)) {
                log.error("事务异常：", e);
            }
            transactionManager.rollback(status);
            throw e;
        }
    }
}

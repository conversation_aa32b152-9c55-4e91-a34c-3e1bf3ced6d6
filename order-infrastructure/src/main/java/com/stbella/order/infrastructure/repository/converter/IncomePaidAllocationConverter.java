package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.month.entity.IncomePaidAllocationEntity;
import com.stbella.order.infrastructure.repository.po.IncomePaidAllocationPO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface IncomePaidAllocationConverter {

    IncomePaidAllocationEntity po2Entity(IncomePaidAllocationPO po);

    List<IncomePaidAllocationEntity> POList2EntityList(List<IncomePaidAllocationPO> poList);

    IncomePaidAllocationPO entity2PO(IncomePaidAllocationEntity entity);

    List<IncomePaidAllocationPO> entityList2POList(List<IncomePaidAllocationEntity> entityList);

}

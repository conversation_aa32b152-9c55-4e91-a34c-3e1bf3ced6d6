package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.month.entity.HeOrderRefundGoodsEntity;
import com.stbella.order.infrastructure.repository.po.HeOrderRefundGoods;
import org.mapstruct.Mapper;

import java.util.List;


@Mapper(componentModel = "spring")
public interface OrderRefundGoodsConverter {

    List<HeOrderRefundGoods> entityList2PoList(List<HeOrderRefundGoodsEntity> orderRefundGoodsEntityList);

    List<HeOrderRefundGoodsEntity> poList2EntityList(List<HeOrderRefundGoods> list);
}

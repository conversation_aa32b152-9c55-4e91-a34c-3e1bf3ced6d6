package com.stbella.order.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.domain.order.month.entity.HeQuotationEntity;
import com.stbella.order.domain.repository.HeQuotationRepository;
import com.stbella.order.infrastructure.repository.mapper.saas.HeQuotationMapper;
import com.stbella.order.infrastructure.repository.po.HeQuotationPO;
import com.stbella.order.server.order.month.req.quotation.QuotationPageQuery;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

@Repository
public class HeQuotationRepositoryImpl extends ServiceImpl<HeQuotationMapper, HeQuotationPO> implements HeQuotationRepository {

    @Resource
    public HeQuotationMapper heQuotationMapper;

    @Override
    public Page<HeQuotationEntity> queryList(QuotationPageQuery req) {

        IPage<HeQuotationPO> page = heQuotationMapper.selectPage(new Page<>(req.getPageNum(), req.getPageSize()),
                Wrappers.<HeQuotationPO>lambdaQuery()
                        .eq(StringUtils.isNotBlank(req.getOperator().getOperatorGuid()), HeQuotationPO::getOperatorGuid, req.getOperator().getOperatorGuid())
                        .eq(Objects.nonNull(req.getStoreId()), HeQuotationPO::getStoreId, req.getStoreId())
                        .eq(Objects.nonNull(req.getClientId()), HeQuotationPO::getClientUid, req.getClientId())
                        .eq(HeQuotationPO::getIsDelete, false)
                        .and(CollectionUtils.isNotEmpty(req.getQuotationIdList()) || CollectionUtils.isNotEmpty(req.getTabClientList()), item -> item
                                .in(CollectionUtils.isNotEmpty(req.getTabClientList()), HeQuotationPO::getClientUid, req.getTabClientList())
                                .or()
                                .in(CollectionUtils.isNotEmpty(req.getQuotationIdList()), HeQuotationPO::getId, req.getQuotationIdList()))
                        .orderByDesc(HeQuotationPO::getId)
        );
        Page<HeQuotationEntity> resPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        resPage.setPages(page.getPages());
        resPage.setRecords(BeanMapper.mapList(page.getRecords(), HeQuotationEntity.class));
        return resPage;
    }

    @Override
    public HeQuotationEntity queryById(Integer id) {
        if (Objects.isNull(id)){
            return null;
        }
        HeQuotationPO quotationPO = this.getById(id);
        return BeanMapper.map(quotationPO, HeQuotationEntity.class);
    }

    @Override
    public Integer save(HeQuotationEntity req) {
        Assert.isTrue(Objects.nonNull(req), "报价单数据体为空");
        HeQuotationPO heQuotationPO = BeanMapper.map(req, HeQuotationPO.class);
        this.save(heQuotationPO);
        return heQuotationPO.getId();
    }

    @Override
    public Boolean update(HeQuotationEntity heQuotationEntity) {
        if (Objects.isNull(heQuotationEntity)){
            return Boolean.FALSE;
        }
        heQuotationEntity.setUpdatedAt(new Date());
        return this.updateById(BeanMapper.map(heQuotationEntity, HeQuotationPO.class));
    }
}

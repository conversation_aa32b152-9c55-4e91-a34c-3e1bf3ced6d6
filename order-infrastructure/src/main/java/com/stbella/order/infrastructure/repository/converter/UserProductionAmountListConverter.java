package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.month.entity.HeUserProductionAmountListEntity;
import com.stbella.order.infrastructure.repository.po.HeUserProductionAmountListPO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface UserProductionAmountListConverter {

    HeUserProductionAmountListPO entity2PO(HeUserProductionAmountListEntity entity);

    HeUserProductionAmountListEntity po2Entity(HeUserProductionAmountListPO po);

}

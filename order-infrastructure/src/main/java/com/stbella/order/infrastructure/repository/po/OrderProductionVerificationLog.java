package com.stbella.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("he_order_production_verification_log")
public class OrderProductionVerificationLog implements Serializable {
    private static final long serialVersionUID = 8232598463892705683L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户basic id
     */
    private Integer basicId;

    /**
     * 通卡、次卡、单项id、额外礼赠id
     */
    private Integer productionId;

    /**
     * 类型， 1: 购买 2：额外礼赠
     */
    private Byte type;

    /**
     * 产康商品卡项子表id
     */
    private Integer cardId;

    /**
     * 核销商品id
     */
    private Integer goodsId;

    /**
     * 核销商品名称
     */
    private String goodsName;

    /**
     * 核销规格id
     */
    private Integer skuId;

    /**
     * 规格名称
     */
    private String skuName;

    /**
     * 核销部位
     */
    private String positionName;

    /**
     * 关联订单id
     */
    private Long orderId;

    /**
     * 核销内容
     */
    private String verificationContent;

    /**
     * 核销人
     */
    private Long operatorId;

    /**
     * 核销人名称
     */
    private String operatorName;

    /**
     * 核销门店
     */
    private Integer operatorStoreId;

    /**
     * 预约单号
     */
    private String appointmentOrderSn;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 逻辑删除
     */
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;

}
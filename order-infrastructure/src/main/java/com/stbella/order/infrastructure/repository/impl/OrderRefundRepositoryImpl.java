package com.stbella.order.infrastructure.repository.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.domain.order.month.entity.HeOrderRefundEntity;
import com.stbella.order.domain.repository.OrderRefundRepository;
import com.stbella.order.infrastructure.repository.converter.OrderRefundConverter;
import com.stbella.order.infrastructure.repository.mapper.saas.HeOrderRefundMapper;
import com.stbella.order.infrastructure.repository.po.HeOrderRefundPO;
import com.stbella.order.server.order.ProductionOrderGoodsModel;
import com.stbella.order.server.order.cts.enums.YesOrNoEnum;
import com.stbella.order.server.order.month.req.OrderRefundQuery;
import com.stbella.order.server.order.month.req.RefundRecordListReq;
import com.stbella.order.server.order.order.req.OrderExportReq;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
@DubboService
public class OrderRefundRepositoryImpl extends ServiceImpl<HeOrderRefundMapper, HeOrderRefundPO> implements OrderRefundRepository {

    @Resource
    private OrderRefundConverter orderRefundConverter;
    @Resource
    private HeOrderRefundMapper heOrderRefundMapper;

    @Override
    public List<HeOrderRefundEntity> getRefundByOrderIdList(List<Integer> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return new ArrayList<>();
        }
        List<HeOrderRefundPO> heOrderRefundList = this.baseMapper.selectList(new LambdaQueryWrapper<HeOrderRefundPO>().in(HeOrderRefundPO::getOrderId, orderIdList));
        if (CollectionUtils.isEmpty(heOrderRefundList)) {
            return new ArrayList<>();
        }
        return orderRefundConverter.orderRefundPoListToEntity(heOrderRefundList);
    }

    /**
     * 查询单个订单退款记录
     *
     * @param orderId
     * @return
     */
    @Override
    public List<HeOrderRefundEntity> getRefundByOrderId(Integer orderId) {

        if (Objects.isNull(orderId)){
            return new ArrayList<>();
        }
        List<HeOrderRefundPO> heOrderRefundList = this.baseMapper.selectList(new LambdaQueryWrapper<HeOrderRefundPO>().eq(HeOrderRefundPO::getOrderId, orderId).orderByDesc(HeOrderRefundPO::getCreatedAt));

        if (CollectionUtils.isEmpty(heOrderRefundList)) {
            return new ArrayList<>();
        }
        return orderRefundConverter.orderRefundPoListToEntity(heOrderRefundList);
    }

    /**
     * 查询订单退款记录
     *
     * @param orderId
     * @param status  退款状态，1：审批中 2：审批失败  3：审批成功/等待打款/待确认 4：退款已到帐/已确认 5：到账失败/已拒绝
     * @return
     */
    @Override
    public List<HeOrderRefundEntity> getRefundByOrderId(Integer orderId, Integer status) {
        List<HeOrderRefundPO> heOrderRefundList = this.baseMapper.selectList(new LambdaQueryWrapper<HeOrderRefundPO>().eq(HeOrderRefundPO::getOrderId, orderId).eq(HeOrderRefundPO::getIsDelete, 0).eq(ObjectUtil.isNotEmpty(status), HeOrderRefundPO::getStatus, status));

        if (CollectionUtils.isEmpty(heOrderRefundList)) {
            return new ArrayList<>();
        }
        return orderRefundConverter.orderRefundPoListToEntity(heOrderRefundList);
    }

    /**
     * 根据ID查询单个订单退款记录
     *
     * @param id
     * @return
     */
    @Override
    public HeOrderRefundEntity getOneById(Integer id) {
        return orderRefundConverter.orderRefundPoToEntity(this.getById(id));
    }

    @Override
    public List<HeOrderRefundEntity> batchQueryEntityByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids))
            return Collections.emptyList();
        List<HeOrderRefundPO> heOrderRefundPOS = this.listByIds(ids);
        if (CollectionUtils.isEmpty(heOrderRefundPOS)){
            return Collections.emptyList();
        }
        return orderRefundConverter.orderRefundPoListToEntity(heOrderRefundPOS);
    }

    /**
     * 根据refundOrderSn查询单个订单退款记录
     *
     * @param refundOrderSn
     * @return
     */
    @Override
    public HeOrderRefundEntity getOneByRefundOrderSn(String refundOrderSn) {
        LambdaQueryWrapper<HeOrderRefundPO> lq = new LambdaQueryWrapper();
        lq.eq(HeOrderRefundPO::getRefundOrderSn, refundOrderSn);
        return orderRefundConverter.orderRefundPoToEntity(this.getOne(lq));
    }

    /**
     * 根据ID修改订单退款记录
     *
     * @param heOrderRefundEntity
     * @return
     */
    @Override
    public boolean updateOneById(HeOrderRefundEntity heOrderRefundEntity) {
        heOrderRefundEntity.setUpdatedAt(System.currentTimeMillis() / 1000);
        return this.updateById(orderRefundConverter.heOrderRefundEntityToPO(heOrderRefundEntity));
    }


    /**
     * 批量修改
     *
     * @param heOrderRefundEntityList
     * @return
     */
    @Override
    public boolean batchUpdateById(List<HeOrderRefundEntity> heOrderRefundEntityList) {
        for (HeOrderRefundEntity orderRefundEntity : heOrderRefundEntityList) {
            orderRefundEntity.setUpdatedAt(System.currentTimeMillis() / 1000);
        }
        return this.updateBatchById(orderRefundConverter.heOrderRefundEntityListToPOList(heOrderRefundEntityList));
    }

    @Override
    public Long queryTotalRefundAchievement(OrderRefundQuery refundQuery) {
        final Long totalRefundAchievement = this.getBaseMapper().queryTotalRefundAchievement(refundQuery);
        return Objects.nonNull(totalRefundAchievement) ? totalRefundAchievement : 0L;
    }

    @Override
    public List<HeOrderRefundEntity> getRefundByOrderIncomeIdList(List<Integer> orderIncomeId) {
        if (CollectionUtils.isEmpty(orderIncomeId)) {
            return Lists.newArrayList();
        }

        LambdaQueryWrapper queryWrapper = new LambdaQueryWrapper<HeOrderRefundPO>().in(HeOrderRefundPO::getOrderGoodId, orderIncomeId).eq(HeOrderRefundPO::getType, 1);

        return orderRefundConverter.orderRefundPoListToEntity(this.list(queryWrapper));
    }

    @Override
    public List<HeOrderRefundEntity> getRefundSuccessByOrderIncomeIdList(List<Integer> orderIncomeId) {
        if (CollectionUtils.isEmpty(orderIncomeId)) {
            return Lists.newArrayList();
        }

        LambdaQueryWrapper queryWrapper = new LambdaQueryWrapper<HeOrderRefundPO>().in(HeOrderRefundPO::getOrderGoodId, orderIncomeId);

        return orderRefundConverter.orderRefundPoListToEntity(this.list(queryWrapper));
    }

    @Override
    public Integer saveOne(HeOrderRefundEntity heOrderRefundEntity) {
        HeOrderRefundPO heOrderRefundPO = orderRefundConverter.heOrderRefundEntityToPO(heOrderRefundEntity);
        heOrderRefundPO.setCreatedAt(DateUtils.getTenBitTimestamp());
        heOrderRefundPO.setUpdatedAt(DateUtils.getTenBitTimestamp());
        this.save(heOrderRefundPO);
        return heOrderRefundPO.getId();
    }

    @Override
    public Integer saveOneList(List<HeOrderRefundEntity> orderRefundEntityList) {
        for (HeOrderRefundEntity orderRefundEntity : orderRefundEntityList) {
            HeOrderRefundPO heOrderRefundPO = orderRefundConverter.heOrderRefundEntityToPO(orderRefundEntity);
            heOrderRefundPO.setCreatedAt(DateUtils.getTenBitTimestamp());
            heOrderRefundPO.setUpdatedAt(DateUtils.getTenBitTimestamp());
            this.save(heOrderRefundPO);
            orderRefundEntity.setId(heOrderRefundPO.getId());
        }
        return orderRefundEntityList.size();
    }

    /**
     * 根据订单ID分页获取退款记录
     *
     * @param page
     * @param orderId
     * @return
     */
    @Override
    public Page<HeOrderRefundEntity> getPageRefundRecordByOrderId(Page page, Integer orderId) {
        LambdaQueryWrapper<HeOrderRefundPO> lq = new LambdaQueryWrapper<>();
        lq.eq(HeOrderRefundPO::getOrderId, orderId);
        lq.orderByDesc(HeOrderRefundPO::getCreatedAt);
        return orderRefundConverter.page2Entity(this.page(page, lq));
    }


    /**
     * 支付记录查退款记录
     *
     * @param page
     * @param orderIncomeId
     * @return {@link Page}<{@link HeOrderRefundEntity}>
     */
    @Override
    public Page<HeOrderRefundEntity> getPageRefundRecordByIncomeId(Page page, List<Integer> orderIncomeId) {
        if (CollectionUtils.isEmpty(orderIncomeId)) {
            return new Page<>();
        }
        LambdaQueryWrapper<HeOrderRefundPO> lq = new LambdaQueryWrapper<>();
        lq.in(HeOrderRefundPO::getOrderGoodId, orderIncomeId);
        lq.eq(HeOrderRefundPO::getType, 1);
        lq.orderByDesc(HeOrderRefundPO::getCreatedAt);
        return orderRefundConverter.page2Entity(this.page(page, lq));
    }



    /**
     * 押金特殊逻辑-设置订单ID为null
     *
     * @param id
     */
    @Override
    public boolean updateSetOrderIdNullById(Integer id) {
        LambdaUpdateWrapper<HeOrderRefundPO> lq = new LambdaUpdateWrapper<>();
        lq.eq(HeOrderRefundPO::getId, id);
        lq.set(HeOrderRefundPO::getOrderId, null);
        return this.update(lq);
    }
    /**
     * 根据订单类型和时间查询退款记录
     *
     * @param req
     * @return
     */
    @Override
    public List<ProductionOrderGoodsModel> queryOrderRefundByCondition(OrderExportReq req) {

        List<ProductionOrderGoodsModel> heOrderRefundPOS = heOrderRefundMapper.queryOrderRefundByCondition(req);


        return heOrderRefundPOS;
    }

    @Override
    public List<HeOrderRefundEntity> getRefundByParentSn(String refundOrderSn) {
        LambdaQueryWrapper<HeOrderRefundPO> lq = new LambdaQueryWrapper<HeOrderRefundPO>().eq(HeOrderRefundPO::getParentRefundOrderSn, refundOrderSn);
        List<HeOrderRefundPO> list = this.list(lq);
        return orderRefundConverter.orderRefundPoListToEntity(list);
    }

    /**
     * 业财-获取退款记录
     *
     * @param req
     */
    @Override
    public List<HeOrderRefundEntity> getRefundListForFinancial(RefundRecordListReq req) {
        List<HeOrderRefundEntity> heOrderRefundEntities = heOrderRefundMapper.getRefundListForFinancial(req);
        return heOrderRefundEntities;
    }


    @Override
    public List<HeOrderRefundEntity> queryCurrencyIsNull(Long startDate) {
        return this.baseMapper.queryCurrencyIsNull(startDate);
    }

    @Override
    public List<Integer> queryAllOrderId() {
        LambdaQueryWrapper<HeOrderRefundPO> lq = new LambdaQueryWrapper();
        lq.eq(HeOrderRefundPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .ne(HeOrderRefundPO::getOrderId, 0)
                .isNotNull(HeOrderRefundPO::getOrderId);
        lq.select(HeOrderRefundPO::getOrderId);
        List<HeOrderRefundPO> list = this.list(lq);
        return list.stream().map(HeOrderRefundPO::getOrderId).collect(Collectors.toList());
    }

    @Override
    public void delById(Integer cashRefundId) {
        LambdaUpdateWrapper<HeOrderRefundPO> lq = new LambdaUpdateWrapper();
        lq.eq(HeOrderRefundPO::getId, cashRefundId)
                .set(HeOrderRefundPO::getIsDelete, 1);
        this.update(lq);
    }


}

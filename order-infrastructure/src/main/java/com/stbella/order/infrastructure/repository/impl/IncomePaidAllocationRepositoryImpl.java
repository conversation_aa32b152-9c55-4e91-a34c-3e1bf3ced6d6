package com.stbella.order.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.stbella.order.domain.order.month.entity.IncomePaidAllocationEntity;
import com.stbella.order.domain.repository.IncomePaidAllocationRepository;
import com.stbella.order.infrastructure.repository.converter.IncomePaidAllocationConverter;
import com.stbella.order.infrastructure.repository.mapper.saas.IncomePaidAllocationMapper;
import com.stbella.order.infrastructure.repository.po.IncomePaidAllocationPO;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class IncomePaidAllocationRepositoryImpl extends ServiceImpl<IncomePaidAllocationMapper, IncomePaidAllocationPO> implements IncomePaidAllocationRepository {

    @Resource
    private IncomePaidAllocationConverter incomePaidAllocationConverter;

    @Override
    public List<IncomePaidAllocationEntity> queryListByIncomeIds(List<Integer> incomeIds) {
        if (CollectionUtils.isEmpty(incomeIds)) {
            return Lists.newArrayList();
        }

        LambdaQueryWrapper<IncomePaidAllocationPO> queryWrapper = Wrappers.lambdaQuery(IncomePaidAllocationPO.class)
                .in(IncomePaidAllocationPO::getIncomeId, incomeIds);
        return incomePaidAllocationConverter.POList2EntityList(list(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateBatch(List<IncomePaidAllocationEntity> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return false;
        }
        List<IncomePaidAllocationPO> incomePaidAllocationPOS = incomePaidAllocationConverter.entityList2POList(entityList);
        incomePaidAllocationPOS.forEach(po -> {
            po.setDeleted(0);
            po.setAddTime(new Date());
            po.setUpdateTime(new Date());
        });

        return saveOrUpdateBatch(incomePaidAllocationPOS);
    }

    @Override
    public List<IncomePaidAllocationEntity> queryListByOrderId(Long orderId) {
        LambdaQueryWrapper<IncomePaidAllocationPO> queryWrapper = Wrappers.lambdaQuery(IncomePaidAllocationPO.class)
                .eq(IncomePaidAllocationPO::getOrderId, orderId);
        return incomePaidAllocationConverter.POList2EntityList(list(queryWrapper));
    }

    @Override
    public void deleteByIncomeIds(List<Integer> incomeIds) {
        if (CollectionUtils.isEmpty(incomeIds)) {
            return;
        }

        LambdaQueryWrapper<IncomePaidAllocationPO> queryWrapper = Wrappers.lambdaQuery(IncomePaidAllocationPO.class)
                .in(IncomePaidAllocationPO::getIncomeId, incomeIds);
        remove(queryWrapper);
    }

    @Override
    public List<IncomePaidAllocationEntity> queryListByOrderId(List<Long> orderIdList) {
        LambdaQueryWrapper<IncomePaidAllocationPO> queryWrapper = Wrappers.lambdaQuery(IncomePaidAllocationPO.class)
                .in(IncomePaidAllocationPO::getOrderId, orderIdList);
        return incomePaidAllocationConverter.POList2EntityList(list(queryWrapper));
    }

    @Override
    public boolean updatePaidAmount(IncomePaidAllocationEntity entity) {

        LambdaUpdateWrapper<IncomePaidAllocationPO> lq = new LambdaUpdateWrapper<>();
        lq.eq(IncomePaidAllocationPO::getId, entity.getId());
        lq.set(IncomePaidAllocationPO::getUpdateTime, new Date());
        lq.set(IncomePaidAllocationPO::getPaidAmount, entity.getPaidAmount());
        return this.update(lq);

    }

    @Override
    public List<IncomePaidAllocationEntity> queryListByOrderIdList(List<Integer> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)){
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<IncomePaidAllocationPO> queryWrapper = Wrappers.lambdaQuery(IncomePaidAllocationPO.class)
                .in(IncomePaidAllocationPO::getOrderId, orderIds)
                .orderByDesc(IncomePaidAllocationPO::getId);
        return incomePaidAllocationConverter.POList2EntityList(list(queryWrapper));
    }

    @Override
    public List<IncomePaidAllocationEntity> queryListByOrderIdAndOrderGoodsIdList(Integer orderId, List<Integer> orderGoodsIdList) {
        LambdaQueryWrapper<IncomePaidAllocationPO> queryWrapper = Wrappers.lambdaQuery(IncomePaidAllocationPO.class)
                .eq(IncomePaidAllocationPO::getOrderId, orderId)
                .in(IncomePaidAllocationPO::getOrderGoodsId, orderGoodsIdList);
        return incomePaidAllocationConverter.POList2EntityList(list(queryWrapper));
    }

    @Override
    public List<IncomePaidAllocationEntity> queryListByOrderGoodsSnList(List<String> orderGoodsSnList) {
        LambdaQueryWrapper<IncomePaidAllocationPO> queryWrapper = Wrappers.lambdaQuery(IncomePaidAllocationPO.class)
                .in(IncomePaidAllocationPO::getOrderGoodsSn, orderGoodsSnList);
        return incomePaidAllocationConverter.POList2EntityList(list(queryWrapper));
    }

    @Override
    public List<Integer> queryListByUpdateDate(Date updateStartTime, Date updateEndTime) {
        if (updateStartTime == null || updateEndTime == null){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<IncomePaidAllocationPO> queryWrapper = Wrappers.lambdaQuery(IncomePaidAllocationPO.class)
                .ge(IncomePaidAllocationPO::getUpdateTime, updateStartTime)
                .le(IncomePaidAllocationPO::getUpdateTime, updateEndTime);

        List<IncomePaidAllocationPO> list = list(queryWrapper);
        return CollectionUtils.isEmpty(list)? Collections.emptyList() : list.stream().map(IncomePaidAllocationPO::getOrderId).map(Math::toIntExact).distinct().collect(Collectors.toList());
    }

}

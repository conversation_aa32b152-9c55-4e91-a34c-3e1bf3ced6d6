package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.production.OrderProductionAppointmentOptLogEntity;
import com.stbella.order.infrastructure.repository.po.OrderProductionAppointmentOptLog;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @description:
 * @author: Seven
 * @time: 2022/10/11 13:55
 */
@Mapper(componentModel = "spring")
public interface ProductionAppointmentOptLogConverter {

    /**
     * po -> entity
     *
     * @param po
     * @return AppointmentOptLogEntity
     */
    OrderProductionAppointmentOptLogEntity po2EntityForProductionAppointmentOptLog(OrderProductionAppointmentOptLog po);

    /**
     * entity -> po
     *
     * @param entity
     * @return com.stbella.order.infrastructure.repository.po.OrderProductionAppointmentOptLog
     * @throws
     * <AUTHOR>
     * @date 2022/10/13 14:17
     * @since 1.0.0
     */
    OrderProductionAppointmentOptLog entity2POForProductionAppointmentOptLog(OrderProductionAppointmentOptLogEntity entity);

    /**
     * 实体类转换
     *
     * @param productionAppointmentOptLogs po
     * @return entity
     */
    List<OrderProductionAppointmentOptLogEntity> poListToEntityList(List<OrderProductionAppointmentOptLog> productionAppointmentOptLogs);
}

package com.stbella.order.infrastructure.repository.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.stbella.order.domain.order.entity.HeCartEntity;
import com.stbella.order.domain.repository.HeCartRepository;
import com.stbella.order.infrastructure.repository.converter.CartPOConverter;
import com.stbella.order.infrastructure.repository.mapper.saas.HeCartMapper;
import com.stbella.order.infrastructure.repository.po.HeCartPO;
import com.stbella.platform.order.api.req.QueryCartReq;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Repository
public class HeCartRepositoryImpl extends ServiceImpl<HeCartMapper, HeCartPO> implements HeCartRepository {

    @Resource
    private CartPOConverter cartPOConverter;

    @Override
    @Transactional
    public Integer saveOrUpdate(HeCartEntity HeCartEntity) {
        HeCartPO heCartPO = cartPOConverter.entity2PO(HeCartEntity);
        saveOrUpdate(heCartPO);
        return heCartPO.getCartId();
    }

    @Override
    public Integer update(HeCartEntity orderCartEntity) {
        HeCartPO heCartPO = cartPOConverter.entity2PO(orderCartEntity);
        heCartPO.setUpdateTime(new Date());
        this.updateById(heCartPO);

        return heCartPO.getCartId();
    }

    @Override
    public HeCartEntity queryOne(QueryCartReq queryReq) {
        LambdaQueryWrapper<HeCartPO> queryWrapper = Wrappers.lambdaQuery(HeCartPO.class);
        if (ObjectUtil.isNotNull(queryReq.getCartId())) {
            queryWrapper.eq(HeCartPO::getCartId, queryReq.getCartId());
        }
        queryWrapper.eq(ObjectUtil.isNotNull(queryReq.getOrderId()),HeCartPO::getOrderId, queryReq.getOrderId());
        queryWrapper.eq(Objects.nonNull(queryReq.getOrderType()), HeCartPO::getOrderType, queryReq.getOrderType());
        queryWrapper.eq(Objects.nonNull(queryReq.getScene()), HeCartPO::getScene, queryReq.getScene());

        if (ObjectUtil.isNotNull(queryReq.getStoreId()) && ObjectUtil.isNotNull(queryReq.getBasicUid())) {
            queryWrapper.eq(HeCartPO::getStoreId, queryReq.getStoreId());
            queryWrapper.eq(HeCartPO::getBasicUid, queryReq.getBasicUid());
            queryWrapper.eq(HeCartPO::getSubmitted, false);
        }
        queryWrapper.orderByDesc(HeCartPO::getUpdateTime);
        queryWrapper.last("limit 1");


        return cartPOConverter.po2Entity(getOne(queryWrapper));
    }

    /**
     * 根据 门店，客户id，订单类型，场景 删除未提交的购物车记录
     * @param queryReq
     * @return
     */
    @Override
    public Boolean deleteUnSubmitRecord(QueryCartReq queryReq) {

        LambdaQueryWrapper<HeCartPO> queryWrapper = Wrappers.lambdaQuery(HeCartPO.class)
                .eq(HeCartPO::getSubmitted, false);
        queryWrapper.eq(HeCartPO::getStoreId, queryReq.getStoreId());
        queryWrapper.eq(HeCartPO::getBasicUid, queryReq.getBasicUid());
        queryWrapper.eq(HeCartPO::getOrderType, queryReq.getOrderType());
        queryWrapper.eq(HeCartPO::getScene, queryReq.getScene());

        return remove(queryWrapper);
    }

    @Override
    public List<HeCartEntity> queryList(List<Integer> cartIdList) {

        if (CollectionUtils.isEmpty(cartIdList)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HeCartPO> queryWrapper = Wrappers.lambdaQuery(HeCartPO.class);
        queryWrapper.in(HeCartPO::getCartId, cartIdList);
        queryWrapper.orderByDesc(HeCartPO::getCartId);
        List<HeCartPO> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)){
            return Collections.emptyList();
        }
        List<HeCartEntity> resultList = Lists.newArrayList();
        list.forEach(item -> {
            resultList.add(cartPOConverter.po2Entity(item));
        });
        return resultList;
    }

}

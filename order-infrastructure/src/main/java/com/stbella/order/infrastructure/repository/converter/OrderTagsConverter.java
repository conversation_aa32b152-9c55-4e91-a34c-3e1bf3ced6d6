package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.month.entity.HeOrderTagsEntity;
import com.stbella.order.infrastructure.repository.po.HeTagsPO;
import com.stbella.order.server.order.month.res.TagVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface OrderTagsConverter {

    List<HeOrderTagsEntity> poList2EntityList(List<HeTagsPO> heTagsPOS);

    HeOrderTagsEntity tagsPO2Entity(HeTagsPO heTagsPO);

    TagVO tagsEntity2VO(HeOrderTagsEntity entity);

    TagVO tagsPO2VO(HeTagsPO heTagsPO);
}

package com.stbella.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 集团千万报单记录表
 * </p>
 *
 * <AUTHOR> @since 2023-05-11
 */
@Data
@Accessors(chain = true)
@TableName("he_order_qianwan_notice")
@ApiModel(value = "HeOrderQianwanNoticePO对象", description = "集团千万报单记录表")
public class HeOrderQianwanNoticePO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id（主键自增)")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "年")
    private Integer year;

    @ApiModelProperty(value = "月")
    private Integer month;

    @ApiModelProperty(value = "集团总业绩")
    private String groupTotalAmount;

    @ApiModelProperty(value = "集团净收入总金额")
    private String groupNetAmount;

    @ApiModelProperty(value = "集团总业绩达成率")
    private String allAmountStandardRate;

    @ApiModelProperty(value = "净收入达成率")
    private String achievement;

    @ApiModelProperty(value = "报单类型1-1千万报单;2-2千万报单;3-3千万报单;4-4千万报单;5-5千万报单;6-6千万报单;7-7千万报单;8-8千万报单;9-9千万报单")
    private Integer noticeType;

    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "修改时间")
    private Date gmtModified;

    @ApiModelProperty(value = "删除标记;0-正常 ;1-删除")
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;
}

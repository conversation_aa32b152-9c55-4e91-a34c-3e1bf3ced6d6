package com.stbella.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 订单sbar商品表
 * </p>
 *
 * <AUTHOR> @since 2023-11-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HeOrderGoodsSbra对象", description = "订单sbar商品表")
public class HeOrderGoodsSbra implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "订单id")
    private Integer orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    @ApiModelProperty(value = " 订单类型： 0 月子客户的“普通月子套餐”订单； 1 月子客户的“小月子”订单； 10 到家客户的“到家服务”订单； 20 到家阿姨的“到家服务订单”； 21 到家阿姨的阿姨培训订单； 30 月子其他订单 31 到家其他订单 40 商城普通订单 50护士外派订单 60产康订单 70S-BRA订单 描述： 0-9 归属月子平台的客户订单类型 ； 10-19 归属到家客户的订单类型； 20-29到家阿姨的订单类型; 30-39 其他订单 40-49 商城订单类型 50护士外派订单 60产康订单 70S-BRA订单")
    private Integer orderType;

    @ApiModelProperty(value = "全局用户ID")
    private Integer basicUid;

    @ApiModelProperty(value = "买家客户ID（ecp库的tab_client表主键）")
    private Integer clientUid;

    @ApiModelProperty(value = "客户名称")
    private String clientName;

    @ApiModelProperty(value = "客户手机号")
    private String clientPhone;

    @ApiModelProperty(value = "0:礼赠 1:购买")
    private Integer buyType;

    @ApiModelProperty(value = "门店id")
    private Integer storeId;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "主商品id")
    private Integer goodsId;

    @ApiModelProperty(value = "商品数量")
    private Integer goodsNum;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "销售价")
    private Integer goodsPrice;

    @ApiModelProperty(value = "成本价")
    private Integer costPrice;

    @ApiModelProperty(value = "规格id")
    private Integer skuId;

    @ApiModelProperty(value = "规格商品名称")
    private String skuName;

    @ApiModelProperty(value = "规格数量")
    private Integer skuNum;

    @ApiModelProperty(value = "新人操作人id")
    private Long createBy;

    @ApiModelProperty(value = "修改操作人id")
    private Long updateBy;

    @ApiModelProperty(value = "创建人姓名")
    private String createByName;

    @ApiModelProperty(value = "修改人姓名")
    private String updateByName;

    private Date orderTime;

    @TableLogic
    private Integer deleted;

    private Date gmtCreate;

    private Date gmtModified;


}

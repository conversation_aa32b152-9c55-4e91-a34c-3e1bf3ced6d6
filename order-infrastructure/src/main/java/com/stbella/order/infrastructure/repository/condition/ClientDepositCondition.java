package com.stbella.order.infrastructure.repository.condition;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 押金支付记录
 * <AUTHOR>
 * @date 2022/12/27
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "押金支付记录", description = "押金支付记录")
public class ClientDepositCondition extends BaseCondition implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户id列表")
    private List<Integer> clientIds;

    @ApiModelProperty(value = "门店列表")
    private List<Integer> storeIds;

}

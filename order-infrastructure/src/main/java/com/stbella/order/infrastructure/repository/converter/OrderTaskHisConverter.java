package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.month.entity.HeTaskHisEntity;
import com.stbella.order.infrastructure.repository.po.HeTaskHisPO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface OrderTaskHisConverter {
    HeTaskHisEntity heTaskPO2HeTaskEntity(HeTaskHisPO heTaskHisPO);

    HeTaskHisPO heTaskEntity2PO(HeTaskHisEntity heTaskHisEntity);

    List<HeTaskHisEntity> heTaskPOList2HeTaskEntityList(List<HeTaskHisPO> list);
}

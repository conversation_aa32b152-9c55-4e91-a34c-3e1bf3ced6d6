package com.stbella.order.infrastructure.repository.condition;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @description: 产康师手工费
 * @author: Seven
 * @time: 2022/10/11 17:54
 */
@Data
public class ProductionAppointmentBoardWorkmanshipCondition extends BaseCondition {

    @ApiModelProperty("产康师ID")
    private Long therapistId;

    @ApiModelProperty("服务开始时间")
    private Date serveStart;

    @ApiModelProperty("服务结束时间")
    private Date serveEnd;

    @ApiModelProperty("预约方式")
    private Integer bookType;

    @ApiModelProperty("门店ID")
    private Integer storeId;

    @ApiModelProperty("服务产康师id")
    private Long serviceTherapistId;
}

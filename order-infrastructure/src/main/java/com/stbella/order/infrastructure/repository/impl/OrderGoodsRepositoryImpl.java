package com.stbella.order.infrastructure.repository.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.common.utils.JsonUtil;
import com.stbella.order.domain.order.month.entity.HeOrderGoodsEntity;
import com.stbella.order.domain.repository.OrderGoodsRepository;
import com.stbella.order.infrastructure.repository.converter.OrderConverter;
import com.stbella.order.infrastructure.repository.mapper.saas.HeOrderGoodsMapper;
import com.stbella.order.infrastructure.repository.po.HeOrderGoodsPO;
import com.stbella.order.server.order.month.req.OrderMonthClientReq;
import com.stbella.order.server.order.month.res.OrderMonthGoodsCacheVO;
import com.stbella.platform.order.api.req.SkuDetailInfo;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

@Repository
@Log4j2
public class OrderGoodsRepositoryImpl extends ServiceImpl<HeOrderGoodsMapper, HeOrderGoodsPO> implements OrderGoodsRepository {

    @Resource
    private OrderConverter orderConverter;

    /**
     * 通过订单ID查询订单套餐信息
     *
     * @param orderId
     * @return
     */
    @Override
    public HeOrderGoodsEntity queryOrderGoodsInfoByOrderId(Integer orderId) {
        HeOrderGoodsPO orderGoodsPO = getOne(new LambdaQueryWrapper<HeOrderGoodsPO>()
                .eq(HeOrderGoodsPO::getOrderId, orderId)
                .eq(HeOrderGoodsPO::getIsDelete, 0)
                .last("limit 1"));
        return orderConverter.orderGoodsPo2Entity(orderGoodsPO);
    }

    @Override
    public List<HeOrderGoodsEntity> getListByCategoryIds(List<Integer> backCategoryIds, String goodsName, Boolean isQuerySub) {

        List<HeOrderGoodsPO> orderGoodsPOList = list(new LambdaQueryWrapper<HeOrderGoodsPO>()
                .in(CollectionUtils.isNotEmpty(backCategoryIds), HeOrderGoodsPO::getBackCategoryId, backCategoryIds)
                .like(StringUtils.isNotEmpty(goodsName), HeOrderGoodsPO::getGoodsName, goodsName)
                .isNull(isQuerySub, HeOrderGoodsPO::getParentCombineSn)
                .eq(HeOrderGoodsPO::getIsDelete, 0));
        return orderConverter.orderGoodsPo2ListEntity(orderGoodsPOList);
    }

    @Override
    public void batchUpdate(List<HeOrderGoodsEntity> orderGoodsEntityList) {
        List<HeOrderGoodsPO> orderGoodsPOList = orderConverter.orderGoodsListEntity2Po(orderGoodsEntityList);
        this.updateBatchById(orderGoodsPOList);
    }

    /***
     * 订单商品保存
     * @param entity
     */
    @Override
    public Integer insertOne(HeOrderGoodsEntity entity) {
        HeOrderGoodsPO heOrderGoodsPO = orderConverter.heOrderGoodsEntity2PO(entity);
        this.save(heOrderGoodsPO);
        return heOrderGoodsPO.getId();
    }

    /**
     * 批量保存
     *
     * @param entityList
     * @return Integer
     */
    @Override
    public Boolean batchInsert(List<HeOrderGoodsEntity> entityList) {

        List<HeOrderGoodsPO> orderGoodsPOList = BeanMapper.mapList(entityList, HeOrderGoodsPO.class);
        //设置删除状态，时间等
        orderGoodsPOList.forEach(goods -> {
            goods.setIsDelete(0);
            goods.setCreatedAt(System.currentTimeMillis()/1000);
            goods.setUpdatedAt(System.currentTimeMillis()/1000);
            goods.setUsedNum(0);
        });

        return this.saveBatch(orderGoodsPOList);

    }

    /***
     * 订单商品修改(废弃)
     * @param entity
     */
    @Override
    @Deprecated
    public Integer updateOne(HeOrderGoodsEntity entity) {
        HeOrderGoodsPO heOrderGoodsPO = orderConverter.heOrderGoodsEntity2PO(entity);
        LambdaUpdateWrapper<HeOrderGoodsPO> lq = new LambdaUpdateWrapper<>();
        lq.eq(HeOrderGoodsPO::getOrderId, heOrderGoodsPO.getOrderId());
        this.update(heOrderGoodsPO, lq);
        HeOrderGoodsEntity orderGoodsEntity = this.getByOrderId(heOrderGoodsPO.getOrderId());
        return ObjectUtil.isNotEmpty(orderGoodsEntity) ? orderGoodsEntity.getId() : null;
    }

    /***
     * 订单商品修改
     * @param entity
     */
    @Override
    public Integer update(HeOrderGoodsEntity entity) {
        HeOrderGoodsPO heOrderGoodsPO = orderConverter.heOrderGoodsEntity2PO(entity);
        LambdaUpdateWrapper<HeOrderGoodsPO> lq = new LambdaUpdateWrapper<>();
        lq.eq(HeOrderGoodsPO::getId, heOrderGoodsPO.getId());
        this.update(heOrderGoodsPO, lq);
        return heOrderGoodsPO.getId();
    }

    /***
     * 订单商品转换vo -> entity
     * @param vo
     */
    @Override
    public HeOrderGoodsEntity orderMonthGoodsCacheVO2Entity(OrderMonthGoodsCacheVO vo, OrderMonthClientReq orderMonthClientReq, Integer orderId, String goodsImage) {
        log.info("处理订单的套餐的数据:{}", JsonUtil.write(vo));
        HeOrderGoodsEntity entity = new HeOrderGoodsEntity();
        entity.setParentId(vo.getParentId());
        entity.setBasicUid(orderMonthClientReq.getBasicUid());
        entity.setGoodsId(vo.getGoodsId());
        entity.setGoodsName(vo.getGoodsName());
        entity.setGoodsNum(1);//商品数量写死1
        entity.setGoodsImage(goodsImage);
        entity.setGoodsPriceOrgin(vo.getGoodsPriceOrgin().multiply(new BigDecimal(100)).intValue());
        entity.setPayAmount(vo.getGoodsReceivableAmount().multiply(new BigDecimal(100)).intValue());
        entity.setGoodsCost(vo.getGoodsPriceCost().multiply(new BigDecimal(100)).intValue());
        entity.setClientUid(orderMonthClientReq.getClientUid());
        entity.setClientType(0);
        entity.setSkuId(vo.getSkuId());
        entity.setStaffId(orderMonthClientReq.getSellerId());
        entity.setStoreId(orderMonthClientReq.getStoreId());
        entity.setOrderId(orderId);
        entity.setServiceDays(vo.getServiceDays());
        entity.setRoomId(vo.getRoomId());
        entity.setRoomName(vo.getRoomName());
        entity.setEcpRoomType(vo.getEcpRoomType());
        entity.setDefinedProperty(vo.getDefinedProperty());
        return entity;
    }

    @Override
    public HeOrderGoodsEntity getByOrderId(Integer orderId) {
        List<HeOrderGoodsPO> orderGoodsPOList = this.baseMapper.selectList(new LambdaQueryWrapper<HeOrderGoodsPO>().eq(HeOrderGoodsPO::getOrderId, orderId));
        //兼容数据为空的情况
        if (CollectionUtils.isEmpty(orderGoodsPOList)){
            return null;
        }
        // 这里先兼容下不报错。现在一个订单可能多个商品
        //降序 先排大的
        Collections.sort(orderGoodsPOList, new Comparator<HeOrderGoodsPO>() {
            @Override
            public int compare(HeOrderGoodsPO o1, HeOrderGoodsPO o2) {
                int o2Num = o2.getGoodsNum();
                int o1Num = o1.getGoodsNum();
                return o2Num - o1Num;
            }
        });
        //优先返回 月子，小月子
        for (HeOrderGoodsPO po : orderGoodsPOList){
            if (po.getGoodsType() == 0){
                return orderConverter.orderGoodsPo2Entity(po);
            }
            if (po.getGoodsType() == 1){
                return orderConverter.orderGoodsPo2Entity(po);
            }
        }

        return orderConverter.orderGoodsPo2Entity(orderGoodsPOList.get(0));
    }

    /**
     * 查询所有订单商品
     *
     * @param orderId
     * @return
     */
    @Override
    public List<HeOrderGoodsEntity> getAllItermByOrderId(Integer orderId) {
        List<HeOrderGoodsPO> orderGoodsPOList = this.baseMapper.selectList(new LambdaQueryWrapper<HeOrderGoodsPO>().eq(HeOrderGoodsPO::getOrderId, orderId));
        return orderConverter.orderGoodsPo2ListEntity(orderGoodsPOList);
    }

    @Override
    public List<HeOrderGoodsEntity> getByOrderIdList(List<Integer> orderIdList) {
        if (CollectionUtil.isEmpty(orderIdList)) {
            return new ArrayList<>();
        }
        List<HeOrderGoodsPO> heOrderGoodsList = this.baseMapper.selectList(new LambdaQueryWrapper<HeOrderGoodsPO>()
                .in(HeOrderGoodsPO::getOrderId, orderIdList)
                .eq(HeOrderGoodsPO::getIsDelete, 0));
        if (CollectionUtil.isEmpty(heOrderGoodsList)) {
            return new ArrayList<>();
        }
        return orderConverter.orderGoodsPo2ListEntity(heOrderGoodsList);
    }

    @Override
    public List<HeOrderGoodsEntity> getByCategoryBackIdList(List<Long> backCategoryIdList, Integer orderId) {

        List<HeOrderGoodsPO> orderGoodsPOList = this.baseMapper.selectList(new LambdaQueryWrapper<HeOrderGoodsPO>()
                .in(HeOrderGoodsPO::getBackCategoryId, backCategoryIdList)
                .eq(HeOrderGoodsPO::getOrderId, orderId));
        if (CollectionUtils.isEmpty(orderGoodsPOList)){
            return Lists.newArrayList();
        }
        return orderConverter.orderGoodsPo2ListEntity(orderGoodsPOList);
    }

    @Override
    public List<HeOrderGoodsEntity> getGoodsWithSameOrderSnAndParentSn() {
        List<HeOrderGoodsPO> orderGoodsPOList = this.baseMapper.selectList(new LambdaQueryWrapper<HeOrderGoodsPO>()
                .apply("order_goods_sn = parent_combine_sn")
                .isNotNull(HeOrderGoodsPO::getOrderGoodsSn)
                .isNotNull(HeOrderGoodsPO::getParentCombineSn)
                .eq(HeOrderGoodsPO::getIsDelete, 0));
        if (CollectionUtils.isEmpty(orderGoodsPOList)) {
            return Lists.newArrayList();
        }
        return orderConverter.orderGoodsPo2ListEntity(orderGoodsPOList);
    }

}

package com.stbella.order.infrastructure.gateway.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.Result;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.utils.BeanMapper;
import com.stbella.order.common.utils.JsonUtil;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.rule.api.RuleHitService;
import com.stbella.rule.api.req.BatchExecuteRuleReq;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import com.stbella.rule.client.execute.RuleActuator;
import com.stbella.rule.client.initiator.model.req.BatchExecuteRuleCommand;
import com.stbella.rule.client.initiator.model.req.ExecuteRuleCommand;
import com.stbella.rule.client.initiator.model.res.HitRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * rule engine link client
 *
 * @Author: jijunjian
 */
@Component
@Slf4j
public class RuleLinkGatewayImpl implements RuleLinkClient {

    @DubboReference(timeout = 30000)
    private RuleHitService ruleHitService;

    @Value("${rule.client}")
    private Boolean ruleClient;

    @Resource
    RuleActuator ruleActuator;

    /**
     * 基本规则查询 - 返回单一规则，没有命中返回 null
     *
     * @param req
     * @return
     */
    @Override
    public HitRuleVo hitOneRule(ExecuteRuleV2Req req) {
        List<HitRuleVo> hitRuleVos = hitRuleList(req);
        if (CollectionUtils.isEmpty(hitRuleVos)) {
            return null;
        }
        return hitRuleVos.get(0);
    }

    @Override
    public List<HitRuleVo> hitRuleList(ExecuteRuleV2Req req) {
        log.info("规则请求 ====>{}", JSONUtil.toJsonStr(req));
        if (ruleClient) {
            return hitRuleListForClient(req);
        }

        Result<List<HitRuleVo>> ruleResult = ruleHitService.hitRuleV2(req);
        List<HitRuleVo> data = ruleResult.getData();
        if (CollectionUtils.isEmpty(data)) {
            log.warn("未查到规则，请检查配置，{}", JSONUtil.toJsonStr(req));
            return null;
        }
        return data;
    }

    /**
     * 基本规则查询 - 返回单一规则，没有命中抛异常
     *
     * @param req
     * @return
     */
    public List<HitRuleVo> hitRuleListForClient(ExecuteRuleV2Req req) {
        log.info("rule link client 调用开始，{}", JSONUtil.toJsonStr(req));
        ExecuteRuleCommand command = toCommand(req);
        List<HitRule> hitRules = ruleActuator.execute(command);
        if (CollectionUtils.isEmpty(hitRules)) {
            log.warn("client 未查到规则，请检查配置，{},{}", JSONUtil.toJsonStr(req));
            return null;
        }

        List<HitRuleVo> hitRuleVos = BeanUtil.copyToList(hitRules, HitRuleVo.class);

        return hitRuleVos;
    }


    /**
     * 基本规则查询 - 返回单一规则，没有命中抛异常
     *
     * @param req
     * @return
     */
    public HitRuleVo hitOneRuleForClient(ExecuteRuleV2Req req) {
        List<HitRuleVo> hitRuleVos = hitRuleListForClient(req);
        if (CollectionUtils.isEmpty(hitRuleVos)) {
            return null;
        }
        return hitRuleVos.get(0);
    }


    /**
     * 基本规则查询 - 返回单一规则，没有命中抛异常
     *
     * @param req
     * @return
     */
    @Override
    public HitRuleVo hitOneRuleWithException(ExecuteRuleV2Req req) {

        HitRuleVo hitRuleVo = hitOneRule(req);
        if (hitRuleVo != null) {
            return hitRuleVo;
        }
        throw new BusinessException(ResultEnum.NOT_EXIST.getCode(), "规则不存在，请检查配置");
    }

    /**
     * 批量执行规则，每个使用优先级最高的
     *
     * @param req
     * @return
     */
    @Override
    public List<HitRuleVo> batchHitRule(BatchExecuteRuleReq req) {
        if (ruleClient) {
            return batchHitRuleForClient(req);
        }
        Result<List<HitRuleVo>> ruleResult = ruleHitService.batchHitRule(req);
        log.info("ruleLink响应结果:{}", JsonUtil.write(ruleResult));
        return ruleResult.getData();
    }


    /**
     * 批量执行规则，并计算，每个使用优先级最高的
     *
     * @param req
     * @return
     */
    @Override
    public List<HitRuleVo> batchHitAndCalculateRule(BatchExecuteRuleReq req) {
        if (ruleClient) {
            return batchHitAndCalculateRuleForClient(req);
        }
        Result<List<HitRuleVo>> ruleResult = ruleHitService.batchHitAndCalculate(req);
        return ruleResult.getData();
    }

    /**
     * 批量执行规则，每个使用优先级最高的
     *
     * @param req
     * @return
     */
    public List<HitRuleVo> batchHitRuleForClient(BatchExecuteRuleReq req) {
        log.info("ruleLink client batch:请求-" + JSON.toJSONString(req));
        BatchExecuteRuleCommand command = toCommand(req);
        List<HitRule> hitRules = ruleActuator.batchExecute(command);

        List<HitRuleVo> hitRuleVos = new ArrayList<>();
        hitRules.forEach(hitRule -> {
            if (Objects.nonNull(hitRule)) {
                HitRuleVo vo = BeanMapper.map(hitRule, HitRuleVo.class);
                hitRuleVos.add(vo);
            } else {
                hitRuleVos.add(null);
            }
        });

        return hitRuleVos;
    }

    /**
     * 批量执行规则，每个使用优先级最高的
     *
     * @param req
     * @return
     */
    public List<HitRuleVo> batchHitAndCalculateRuleForClient(BatchExecuteRuleReq req) {
        log.info("ruleLink client batchHitAndCalculateRule:请求-" + JSON.toJSONString(req));
        BatchExecuteRuleCommand command = toCommand(req);
        List<HitRule> hitRules = ruleActuator.batchExeAndCalculate(command);

        List<HitRuleVo> hitRuleVos = new ArrayList<>();
        hitRules.forEach(hitRule -> {
            if (Objects.nonNull(hitRule)) {
                HitRuleVo vo = BeanMapper.map(hitRule, HitRuleVo.class);
                hitRuleVos.add(vo);
            } else {
                hitRuleVos.add(null);
            }
        });

        return hitRuleVos;
    }


    protected ExecuteRuleCommand toCommand(ExecuteRuleV2Req req) {
        ExecuteRuleCommand map = BeanMapper.map(req, ExecuteRuleCommand.class);
        map.setFactObj(req.getFactObj());
        return map;
    }

    protected BatchExecuteRuleCommand toCommand(BatchExecuteRuleReq req) {
        BatchExecuteRuleCommand command = BeanMapper.map(req, BatchExecuteRuleCommand.class);

        List<ExecuteRuleCommand> soleCommandList = new ArrayList<>();
        req.getIterm().forEach(iterm -> {
            soleCommandList.add(toCommand(iterm));
        });

        command.setIterm(soleCommandList);
        return command;
    }

    /**
     * 查询老合同门店
     *
     * @return
     */
    public Set<String> getOldContractStoreIds() {

        ExecuteRuleV2Req req = new ExecuteRuleV2Req();
        req.setSceneCode("order-center-config");
        Map<String, String> factMap = new HashMap<>();
        factMap.put("configCode", "old_contract");
        req.setFactObj(factMap);

        HitRuleVo hitRule = hitOneRuleForClient(req);
        log.info("rule link client 调用结束，{}", JSONUtil.toJsonStr(hitRule));

        if (hitRule == null) {
            return new HashSet<>();
        }
        Set<String> collect = Arrays.stream(hitRule.getSimpleRuleValue().split(",")).collect(Collectors.toSet());
        return collect;
    }

    @Override
    public Set<Integer> getNoNeedSignStoreIds() {
        ExecuteRuleV2Req req = new ExecuteRuleV2Req();
        req.setSceneCode("systemConfig");
        Map<String, String> factMap = new HashMap<>();
        factMap.put("configCode", BizConstant.RuleLinkScene.NO_NEED_SIGN);
        req.setFactObj(factMap);

        HitRuleVo hitRule = hitOneRuleForClient(req);
        log.info("rule link client 调用结束，{}", JSONUtil.toJsonStr(hitRule));

        if (hitRule == null) {
            return new HashSet<>();
        }
        return Arrays.stream(hitRule.getSimpleRuleValue().split(","))
                .map(Integer::valueOf)
                .collect(Collectors.toSet());
    }
}

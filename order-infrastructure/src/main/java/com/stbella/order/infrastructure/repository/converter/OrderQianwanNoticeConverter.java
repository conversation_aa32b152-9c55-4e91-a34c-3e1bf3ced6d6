package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.month.entity.HeOrderQianwanNoticeEntity;
import com.stbella.order.infrastructure.repository.po.HeOrderQianwanNoticePO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface OrderQianwanNoticeConverter {

    HeOrderQianwanNoticeEntity heOrderQianwanNoticePO2Entity(HeOrderQianwanNoticePO po);

    HeOrderQianwanNoticePO heOrderQianwanNoticeEntity2PO(HeOrderQianwanNoticeEntity entity);
}

package com.stbella.order.infrastructure.repository.converter;

import com.stbella.order.domain.order.month.entity.HeOrderNoticeLogEntity;
import com.stbella.order.infrastructure.repository.po.HeOrderNoticeLogPO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface OrderNoticeConverter {

    HeOrderNoticeLogPO entity2OrderNoticeLogPO(HeOrderNoticeLogEntity orderNoticeLogEntity);

    HeOrderNoticeLogEntity orderNoticeLogPO2Entity(HeOrderNoticeLogPO one);
}

package com.stbella.order.infrastructure.repository.converter;

import com.stbella.customer.server.scrm.dto.OrderReductionRecordDTO;
import com.stbella.order.domain.order.month.entity.OrderReductionEntity;
import com.stbella.order.infrastructure.repository.po.OrderReductionPO;
import com.stbella.order.server.order.month.res.OrderReductionVO;
import com.stbella.order.server.order.month.res.ScrmOrderReductionRecordVO;
import org.mapstruct.Mapper;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2023-07-27  16:19
 * @Description: 订单减免转换
 */
@Mapper(componentModel = "spring")
public interface OrderDecreaseConverter {

    OrderReductionPO entity2PO(OrderReductionEntity entity);

    OrderReductionEntity po2Entity(OrderReductionPO po);

    OrderReductionRecordDTO entity2DTO(OrderReductionEntity orderReductionEntity);

    ScrmOrderReductionRecordVO vo2DTO(OrderReductionVO orderReductionVO);
}

package com.stbella.order.infrastructure.repository.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stbella.order.domain.order.month.entity.HeOrderAdditionalRevenueEntity;
import com.stbella.order.domain.repository.OrderAdditionalRevenueRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.infrastructure.repository.converter.OrderAdditionalRevenueConverter;
import com.stbella.order.infrastructure.repository.converter.OrderConverter;
import com.stbella.order.infrastructure.repository.mapper.saas.HeOrderAdditionalRevenueMapper;
import com.stbella.order.infrastructure.repository.po.HeOrderAdditionalRevenuePO;
import com.stbella.order.server.order.month.req.OrderMonthAdditionalRevenueCacheReq;
import com.stbella.order.server.order.month.res.OrderAdditionalRevenueCacheVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class OrderAdditionalRevenueRepositoryImpl extends ServiceImpl<HeOrderAdditionalRevenueMapper, HeOrderAdditionalRevenuePO> implements OrderAdditionalRevenueRepository {

    @Resource
    private OrderConverter orderConverter;
    @Resource
    private OrderAdditionalRevenueConverter orderAdditionalRevenueConverter;
    @Resource
    private OrderRepository orderRepository;


    /***
     * 根据加收项IDs批量获取加收项
     * <AUTHOR>
     * @param ids
     * @return List<HeOrderAdditionalRevenueEntity>
     */
    @Override
    public List<HeOrderAdditionalRevenueEntity> getEntityListByIds(List<Long> ids) {
        List<HeOrderAdditionalRevenuePO> heOrderAdditionalRevenuePOS = this.listByIds(ids);
        return orderConverter.listHeOrderAdditionalRevenuePO2Entity(heOrderAdditionalRevenuePOS);
    }

    /***
     * 批量保存
     * <AUTHOR>
     * @param entityList
     * @return Boolean
     */
    @Override
    public List<HeOrderAdditionalRevenueEntity> batchInsert(List<HeOrderAdditionalRevenueEntity> entityList) {
        List<HeOrderAdditionalRevenuePO> heOrderAdditionalRevenuePOS = orderConverter.listHeOrderAdditionalRevenueEntity2PO(entityList);
        this.saveBatch(heOrderAdditionalRevenuePOS);
        return orderConverter.listHeOrderAdditionalRevenuePO2Entity(heOrderAdditionalRevenuePOS);
    }

    /***
     * 批量修改
     * 与数据库比对,①ID数据库有,缓存有,则修改,②ID数据库有,缓存没有,则删除,③ID数据库没有,缓存没有则新增
     * <AUTHOR>
     * @param entityList
     * @return Boolean
     */
    @Override
    public List<HeOrderAdditionalRevenueEntity> batchUpdate(List<HeOrderAdditionalRevenueEntity> entityList, Integer orderId) {
//        只对非续住、非房型变更做处理（老订单迁移后没有中间变化过程，该逻辑去除）
//        entityList = entityList.stream().filter(e -> !((MonthAdditionalRevenueEnum.STAY_COST.getCode().equals(e.getType()) || MonthAdditionalRevenueEnum.ROOM_CHANGES.getCode().equals(e.getType())) && null == e.getId())).collect(Collectors.toList());

        LambdaQueryWrapper<HeOrderAdditionalRevenuePO> lq = new LambdaQueryWrapper();
        lq.eq(HeOrderAdditionalRevenuePO::getOrderId, orderId);
//        lq.notIn(HeOrderAdditionalRevenuePO::getType, MonthAdditionalRevenueEnum.STAY_COST.getCode(), MonthAdditionalRevenueEnum.ROOM_CHANGES.getCode());
        List<HeOrderAdditionalRevenuePO> databasePos = this.list(lq);
        List<Long> databaseIds = databasePos.stream().map(HeOrderAdditionalRevenuePO::getId).distinct().collect(Collectors.toList());
        List<Long> cachedIds = entityList.stream().map(HeOrderAdditionalRevenueEntity::getId).distinct().collect(Collectors.toList());
        List<Long> needDeleteIds = new ArrayList<>();
        for (Long databaseId : databaseIds) {
            if (!cachedIds.contains(databaseId)) {
                needDeleteIds.add(databaseId);
            }
        }
        //删除
        if (ObjectUtil.isNotEmpty(needDeleteIds)) {
            LambdaUpdateWrapper<HeOrderAdditionalRevenuePO> lu = new LambdaUpdateWrapper();
            lu.set(HeOrderAdditionalRevenuePO::getDeleted, 1);
            lu.in(HeOrderAdditionalRevenuePO::getId, needDeleteIds);
            this.update(lu);
        }
        //新增或修改
        List<HeOrderAdditionalRevenuePO> heOrderAdditionalRevenuePOS = orderConverter.listHeOrderAdditionalRevenueEntity2PO(entityList);
        this.saveOrUpdateBatch(heOrderAdditionalRevenuePOS);
        return orderConverter.listHeOrderAdditionalRevenuePO2Entity(heOrderAdditionalRevenuePOS);
    }

    /***
     * 批量转换 vos -> entityList
     * @param orderAdditionalRevenueCacheVOS
     * @param orderId
     */
    @Override
    public List<HeOrderAdditionalRevenueEntity> orderAdditionalRevenueCacheVOS2EntityList(List<OrderAdditionalRevenueCacheVO> orderAdditionalRevenueCacheVOS, Integer orderId) {
        if (CollectionUtils.isEmpty(orderAdditionalRevenueCacheVOS)) {
            return new ArrayList<>();
        }
        List<HeOrderAdditionalRevenueEntity> heOrderAdditionalRevenueEntities = orderConverter.orderAdditionalRevenueCacheVOS2EntityList(orderAdditionalRevenueCacheVOS);
        heOrderAdditionalRevenueEntities.forEach(x -> {
            x.setOrderId(orderId);
        });
        return heOrderAdditionalRevenueEntities;
    }

    @Override
    public List<HeOrderAdditionalRevenueEntity> getByOrderId(Integer orderId) {
        List<HeOrderAdditionalRevenuePO> heOrderAdditionalRevenuePOS = this.baseMapper.selectList(new LambdaQueryWrapper<HeOrderAdditionalRevenuePO>()
                .eq(HeOrderAdditionalRevenuePO::getOrderId, orderId)
                .eq(HeOrderAdditionalRevenuePO::getDeleted, 0)
        );
        return orderConverter.orderAdditionalRevenueEntityByPoList(heOrderAdditionalRevenuePOS);
    }

    @Override
    public List<HeOrderAdditionalRevenueEntity> getByOrderIdList(List<Integer> orderIdList) {
        if (CollectionUtil.isEmpty(orderIdList)) {
            return new ArrayList<>();
        }
        List<HeOrderAdditionalRevenuePO> heOrderAdditionalRevenuePOS = this.baseMapper.selectList(new LambdaQueryWrapper<HeOrderAdditionalRevenuePO>()
                .in(HeOrderAdditionalRevenuePO::getOrderId, orderIdList)
                .eq(HeOrderAdditionalRevenuePO::getDeleted, 0)
        );
        return orderConverter.orderAdditionalRevenueEntityByPoList(heOrderAdditionalRevenuePOS);
    }

    @Override
    public List<OrderMonthAdditionalRevenueCacheReq> voListToReq(List<OrderAdditionalRevenueCacheVO> orderAdditionalRevenueCacheVOS, OrderMonthAdditionalRevenueCacheReq req) {
        List<OrderMonthAdditionalRevenueCacheReq> orderMonthAdditionalRevenueCacheReqs = orderAdditionalRevenueConverter.voListToReq(orderAdditionalRevenueCacheVOS);
        for (OrderMonthAdditionalRevenueCacheReq orderMonthAdditionalRevenueCacheReq : orderMonthAdditionalRevenueCacheReqs) {
            orderMonthAdditionalRevenueCacheReq.setClientUid(req.getClientUid());
            orderMonthAdditionalRevenueCacheReq.setOrderType(req.getOrderType());
            orderMonthAdditionalRevenueCacheReq.setOperator(req.getOperator());
        }
        return orderMonthAdditionalRevenueCacheReqs;
    }

    @Override
    public void delByIdList(List<Long> needDeleteIds) {
        LambdaUpdateWrapper<HeOrderAdditionalRevenuePO> lu = new LambdaUpdateWrapper();
        lu.set(HeOrderAdditionalRevenuePO::getDeleted, 1);
        lu.in(HeOrderAdditionalRevenuePO::getId, needDeleteIds);
        this.update(lu);
    }

    @Override
    public List<HeOrderAdditionalRevenueEntity> queryAdditionalByType(Integer type) {
        LambdaQueryWrapper<HeOrderAdditionalRevenuePO> lq = new LambdaQueryWrapper<>();
        lq.eq(HeOrderAdditionalRevenuePO::getType, type);
        return orderConverter.orderAdditionalRevenueEntityByPoList(this.list(lq));
    }

    @Override
    public List<Integer> queryAdditionalOrderIdByType(Integer type, Integer status) {
        LambdaQueryWrapper<HeOrderAdditionalRevenuePO> lq = new LambdaQueryWrapper<>();
        lq.eq(HeOrderAdditionalRevenuePO::getType, type);

        List<HeOrderAdditionalRevenuePO> list = this.list(lq);

        List<Integer> allOrderIds = orderRepository.getAllNewOrderIdList();
        //所有的订单号
        List<Integer> has = list.stream().map(HeOrderAdditionalRevenuePO::getOrderId).collect(Collectors.toList());

        if (status == 0) {
            //获取所有不带类型的订单
            List<Integer> notHas = new ArrayList<>();
            for (Integer integer : allOrderIds) {
                if (!has.contains(integer)) {
                    notHas.add(integer);
                }
            }
            return notHas;
        } else {
            //获取所有带类型的订单
            return has;
        }
    }

    @Override
    public List<HeOrderAdditionalRevenueEntity> queryAdditionalByOrderIdAndType(Integer orderId, Integer type) {
        LambdaQueryWrapper<HeOrderAdditionalRevenuePO> lq = new LambdaQueryWrapper<>();
        lq.eq(HeOrderAdditionalRevenuePO::getType, type);
        lq.eq(HeOrderAdditionalRevenuePO::getOrderId, orderId);
        return orderConverter.orderAdditionalRevenueEntityByPoList(this.list(lq));
    }

    @Override
    public boolean delByOrderId(Integer orderId) {
        LambdaUpdateWrapper<HeOrderAdditionalRevenuePO> lu = new LambdaUpdateWrapper();
        lu.set(HeOrderAdditionalRevenuePO::getDeleted, 1);
        lu.eq(HeOrderAdditionalRevenuePO::getOrderId, orderId);
        return this.update(lu);
    }


}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.ecp.SettingStoreTargetPoMapper">
  <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.SettingStoreTargetPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="store_id" jdbcType="INTEGER" property="storeId" />
    <result column="year" jdbcType="INTEGER" property="year" />
    <result column="month" jdbcType="INTEGER" property="month" />
    <result column="period" jdbcType="VARCHAR" property="period" />
    <result column="amount" jdbcType="INTEGER" property="amount" />
    <result column="last_admin_id" jdbcType="INTEGER" property="lastAdminId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="record_time" jdbcType="TIMESTAMP" property="recordTime" />
    <result column="type" jdbcType="TINYINT" property="type" />
  </resultMap>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.ecp.TabClientMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.TabClientPO">
        <result column="id" property="id"/>
        <result column="basic_uid" property="basicUid"/>
        <result column="store_id" property="storeId"/>
        <result column="name" property="name"/>
        <result column="member_level" property="memberLevel"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="predict_born_date" property="predictBornDate"/>
        <result column="cert_type" property="certType"/>
        <result column="id_card" property="idCard"/>
        <result column="id_card_front" property="idCardFront"/>
        <result column="id_card_back" property="idCardBack"/>
        <result column="diet" property="diet"/>
        <result column="wechat" property="wechat"/>
        <result column="nation" property="nation"/>
        <result column="birthdate" property="birthdate"/>
        <result column="lunar_birthday" property="lunarBirthday"/>
        <result column="constellation_type" property="constellationType"/>
        <result column="constellation" property="constellation"/>
        <result column="sex" property="sex"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="region" property="region"/>
        <result column="address" property="address"/>
        <result column="receiving_address" property="receivingAddress"/>
        <result column="active" property="active"/>
        <result column="predict_born_num" property="predictBornNum"/>
        <result column="fetus_num" property="fetusNum"/>
        <result column="hospital" property="hospital"/>
        <result column="born_time" property="bornTime"/>
        <result column="urgent_name" property="urgentName"/>
        <result column="urgent_phone" property="urgentPhone"/>
        <result column="client_type_id" property="clientTypeId"/>
        <result column="client_sub_type" property="clientSubType"/>
        <result column="source" property="source"/>
        <result column="sub_source" property="subSource"/>
        <result column="mark" property="mark"/>
        <result column="profession" property="profession"/>
        <result column="seller_id" property="sellerId"/>
        <result column="up_id" property="upId"/>
        <result column="wound_store_id" property="woundStoreId"/>
        <result column="package_id" property="packageId"/>
        <result column="meal_package_id" property="mealPackageId"/>
        <result column="check_in_date" property="checkInDate"/>
        <result column="deposit" property="deposit"/>
        <result column="expect_cost" property="expectCost"/>
        <result column="remark" property="remark"/>
        <result column="last_admin_id" property="lastAdminId"/>
        <result column="record_time" property="recordTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="age" property="age"/>
        <result column="blood_type" property="bloodType"/>
        <result column="from_type" property="fromType"/>
        <result column="relation_with_client" property="relationWithClient"/>
        <result column="tags" property="tags"/>
        <result column="attention" property="attention"/>
        <result column="budget" property="budget"/>
        <result column="phone_type" property="phoneType"/>
        <result column="is_phone_verify" property="isPhoneVerify"/>
        <result column="chen_feng_id" property="chenFengId"/>
        <result column="chen_feng_sale_chance_id" property="chenFengSaleChanceId"/>
        <result column="family_tag" property="familyTag"/>
        <result column="dianping_store_id" property="dianpingStoreId"/>
        <result column="scrm_id" property="scrmId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        basic_uid, store_id, name, member_level, phone, email, predict_born_date, cert_type, id_card, id_card_front, id_card_back, diet, wechat, nation, birthdate, lunar_birthday, constellation_type, constellation, sex, province, city, region, address, receiving_address, active, predict_born_num, fetus_num, hospital, born_time, urgent_name, urgent_phone, client_type_id, client_sub_type, source, sub_source, mark, profession, seller_id, up_id, wound_store_id, package_id, meal_package_id, check_in_date, deposit, expect_cost, remark, last_admin_id, record_time, update_time, age, blood_type, from_type, relation_with_client, tags, attention, budget, phone_type, is_phone_verify, chen_feng_id, chen_feng_sale_chance_id, family_tag, dianping_store_id, scrm_id
    </sql>

    <update id="updatePhoneById">
        update tab_client
        set phone = #{phone}
        where id = #{id}
    </update>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.ecp.UserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.UserPO">
        <result column="id" property="id"/>
        <result column="basic_uid" property="basicUid"/>
        <result column="work_number" property="workNumber"/>
        <result column="human_id" property="humanId"/>
        <result column="store_id" property="storeId"/>
        <result column="group_id" property="groupId"/>
        <result column="job_id" property="jobId"/>
        <result column="boss_id" property="bossId"/>
        <result column="special_menu_auth" property="specialMenuAuth"/>
        <result column="store_auth" property="storeAuth"/>
        <result column="account" property="account"/>
        <result column="password" property="password"/>
        <result column="name" property="name"/>
        <result column="nickname" property="nickname"/>
        <result column="english_name" property="englishName"/>
        <result column="email" property="email"/>
        <result column="email_password" property="emailPassword"/>
        <result column="phone" property="phone"/>
        <result column="sign" property="sign"/>
        <result column="work_status" property="workStatus"/>
        <result column="head_pic_file_id" property="headPicFileId"/>
        <result column="sex" property="sex"/>
        <result column="level" property="level"/>
        <result column="auth_code" property="authCode"/>
        <result column="auth_code_dead_time" property="authCodeDeadTime"/>
        <result column="active" property="active"/>
        <result column="last_login_time" property="lastLoginTime"/>
        <result column="login_time" property="loginTime"/>
        <result column="last_admin_id" property="lastAdminId"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator_id" property="creatorId"/>
        <result column="create_time" property="createTime"/>
        <result column="username" property="username"/>
        <result column="auth_key" property="authKey"/>
        <result column="password_hash" property="passwordHash"/>
        <result column="password_reset_token" property="passwordResetToken"/>
        <result column="status" property="status"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="auth_phone" property="authPhone"/>
        <result column="landline" property="landline"/>
        <result column="hand_pic" property="handPic"/>
        <result column="supervisor" property="supervisor"/>
        <result column="staff_type" property="staffType"/>
        <result column="dept_id" property="deptId"/>
        <result column="entry_time" property="entryTime"/>
        <result column="is_admin" property="isAdmin"/>
        <result column="paiban_store_id" property="paibanStoreId"/>
        <result column="ding_talk_user_id" property="dingTalkUserId"/>
        <result column="ding_talk_user_icon" property="dingTalkUserIcon"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        basic_uid, work_number, human_id, store_id, group_id, job_id, boss_id, special_menu_auth, store_auth, account, password, name, nickname, english_name, email, email_password, phone, sign, work_status, head_pic_file_id, sex, level, auth_code, auth_code_dead_time, active, last_login_time, login_time, last_admin_id, update_time, creator_id, create_time, username, auth_key, password_hash, password_reset_token, status, created_at, updated_at, auth_phone, landline, hand_pic, supervisor, staff_type, dept_id, entry_time, is_admin, paiban_store_id, ding_talk_user_id, ding_talk_user_icon
    </sql>

</mapper>

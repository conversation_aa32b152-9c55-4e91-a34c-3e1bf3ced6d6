<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.ecp.CfgDistrictMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.CfgDistrictPO">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="parent_id" property="parentId"/>
        <result column="initial" property="initial"/>
        <result column="initials" property="initials"/>
        <result column="pinyin" property="pinyin"/>
        <result column="extra" property="extra"/>
        <result column="suffix" property="suffix"/>
        <result column="code" property="code"/>
        <result column="area_code" property="areaCode"/>
        <result column="order" property="order"/>
        <result column="type" property="type"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        name, parent_id, initial, initials, pinyin, extra, suffix, code, area_code , order, type
    </sql>

</mapper>

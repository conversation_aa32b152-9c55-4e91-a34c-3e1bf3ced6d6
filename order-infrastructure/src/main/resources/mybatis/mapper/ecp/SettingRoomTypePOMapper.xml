<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.ecp.SettingRoomTypePoMapper">

    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.SettingRoomTypePo" >
        <result column="id" property="id" />
        <result column="store_id" property="storeId" />
        <result column="room_type_name" property="roomTypeName" />
        <result column="price" property="price" />
        <result column="remark" property="remark" />
        <result column="last_admin_id" property="lastAdminId" />
        <result column="record_time" property="recordTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
        store_id,
        room_type_name,
        price,
        remark,
        last_admin_id,
        record_time,
        update_time
    </sql>
</mapper>
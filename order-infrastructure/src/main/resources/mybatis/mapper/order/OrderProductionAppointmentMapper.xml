<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.OrderProductionAppointmentMapper">
  <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.OrderProductionAppointment">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="order_sn" jdbcType="VARCHAR" property="orderSn" />
    <result column="order_production_card_id" jdbcType="INTEGER" property="orderProductionCardId" />
    <result column="order_production_card_extend_id" jdbcType="INTEGER" property="orderProductionCardExtendId" />
    <result column="store_id" jdbcType="INTEGER" property="storeId" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="basic_id" jdbcType="INTEGER" property="basicId"/>
    <result column="client_id" jdbcType="INTEGER" property="clientId" />
    <result column="client_name" jdbcType="VARCHAR" property="clientName" />
    <result column="client_phone" jdbcType="VARCHAR" property="clientPhone" />
    <result column="therapist_id" jdbcType="BIGINT" property="therapistId" />
    <result column="therapist_name" jdbcType="VARCHAR" property="therapistName" />
    <result column="group_goods_id" jdbcType="INTEGER" property="groupGoodsId" />
    <result column="group_goods_name" jdbcType="VARCHAR" property="groupGoodsName" />
    <result column="group_id" jdbcType="INTEGER" property="groupId" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="production_goods_id" jdbcType="INTEGER" property="productionGoodsId" />
    <result column="production_goods_name" jdbcType="VARCHAR" property="productionGoodsName" />
    <result column="production_sku_id" jdbcType="INTEGER" property="productionSkuId" />
    <result column="production_sku_name" jdbcType="VARCHAR" property="productionSkuName" />
    <result column="order_production_type" jdbcType="TINYINT" property="orderProductionType" />
    <result column="production_instr_id" jdbcType="BIGINT" property="productionInstrId" />
    <result column="item_type" jdbcType="BIT" property="itemType" />
    <result column="book_type" jdbcType="BIT" property="bookType" />
    <result column="appointment_type" jdbcType="BIT" property="appointmentType" />
    <result column="serve_fee" jdbcType="INTEGER" property="serveFee" />
    <result column="serve_start" jdbcType="TIMESTAMP" property="serveStart" />
    <result column="serve_end" jdbcType="TIMESTAMP" property="serveEnd" />
    <result column="serve_date" jdbcType="VARCHAR" property="serveDate" />
    <result column="serve_time" jdbcType="INTEGER" property="serveTime" />
    <result column="serve_type" jdbcType="BIT" property="serveType" />
    <result column="verification_state" jdbcType="BIT" property="verificationState" />
    <result column="options_verification" jdbcType="VARCHAR" property="optionsVerification" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="update_id" jdbcType="BIGINT" property="updateId"/>
    <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, order_id, order_sn, order_production_card_id, order_production_card_extend_id, store_id, store_name, basic_id, client_id,
    client_name, client_phone, therapist_id, therapist_name, group_goods_id, group_goods_name, 
    group_id, group_name, production_goods_id, production_goods_name, production_sku_id, 
    production_sku_name, order_production_type, production_instr_id, item_type, book_type,
    appointment_type, serve_fee, serve_start, serve_end, serve_date, serve_time, serve_type, 
    verification_state, options_verification, extra, creator_id, creator_name, deleted, gmt_create, gmt_modified, update_id, update_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from he_order_production_appointment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from he_order_production_appointment
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" parameterType="com.stbella.order.infrastructure.repository.po.OrderProductionAppointment">
    insert into he_order_production_appointment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderSn != null">
        order_sn,
      </if>
      <if test="orderProductionCardId != null">
        order_production_card_id,
      </if>
      <if test="orderProductionCardExtendId != null">
        order_production_card_extend_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="basicId != null">
        basic_id,
      </if>
      <if test="clientId != null">
        client_id,
      </if>
      <if test="clientName != null">
        client_name,
      </if>
      <if test="clientPhone != null">
        client_phone,
      </if>
      <if test="therapistId != null">
        therapist_id,
      </if>
      <if test="therapistName != null">
        therapist_name,
      </if>
      <if test="groupGoodsId != null">
        group_goods_id,
      </if>
      <if test="groupGoodsName != null">
        group_goods_name,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="groupName != null">
        group_name,
      </if>
      <if test="productionGoodsId != null">
        production_goods_id,
      </if>
      <if test="productionGoodsName != null">
        production_goods_name,
      </if>
      <if test="productionSkuId != null">
        production_sku_id,
      </if>
      <if test="productionSkuName != null">
        production_sku_name,
      </if>
      <if test="orderProductionType != null">
        order_production_type,
      </if>
      <if test="productionInstrId != null">
        production_instr_id,
      </if>
      <if test="itemType != null">
        item_type,
      </if>
      <if test="bookType != null">
        book_type,
      </if>
      <if test="appointmentType != null">
        appointment_type,
      </if>
      <if test="serveFee != null">
        serve_fee,
      </if>
      <if test="serveStart != null">
        serve_start,
      </if>
      <if test="serveEnd != null">
        serve_end,
      </if>
      <if test="serveDate != null">
        serve_date,
      </if>
      <if test="serveTime != null">
        serve_time,
      </if>
      <if test="serveType != null">
        serve_type,
      </if>
      <if test="verificationState != null">
        verification_state,
      </if>
      <if test="optionsVerification != null">
        options_verification,
      </if>
      <if test="extra != null">
        extra,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="creatorName != null">
        creator_name,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderSn != null">
        #{orderSn,jdbcType=VARCHAR},
      </if>
      <if test="orderProductionCardId != null">
        #{orderProductionCardId,jdbcType=INTEGER},
      </if>
      <if test="orderProductionCardExtendId != null">
        #{orderProductionCardExtendId,jdbcType=INTEGER},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=INTEGER},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="basicId != null">
        #{basicId,jdbcType=INTEGER},
      </if>
      <if test="clientId != null">
        #{clientId,jdbcType=INTEGER},
      </if>
      <if test="clientName != null">
        #{clientName,jdbcType=VARCHAR},
      </if>
      <if test="clientPhone != null">
        #{clientPhone,jdbcType=VARCHAR},
      </if>
      <if test="therapistId != null">
        #{therapistId,jdbcType=BIGINT},
      </if>
      <if test="therapistName != null">
        #{therapistName,jdbcType=VARCHAR},
      </if>
      <if test="groupGoodsId != null">
        #{groupGoodsId,jdbcType=INTEGER},
      </if>
      <if test="groupGoodsName != null">
        #{groupGoodsName,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=INTEGER},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="productionGoodsId != null">
        #{productionGoodsId,jdbcType=INTEGER},
      </if>
      <if test="productionGoodsName != null">
        #{productionGoodsName,jdbcType=VARCHAR},
      </if>
      <if test="productionSkuId != null">
        #{productionSkuId,jdbcType=INTEGER},
      </if>
      <if test="productionSkuName != null">
        #{productionSkuName,jdbcType=VARCHAR},
      </if>
      <if test="orderProductionType != null">
        #{orderProductionType,jdbcType=TINYINT},
      </if>
      <if test="productionInstrId != null">
        #{productionInstrId,jdbcType=BIGINT},
      </if>
      <if test="itemType != null">
        #{itemType,jdbcType=BIT},
      </if>
      <if test="bookType != null">
        #{bookType,jdbcType=BIT},
      </if>
      <if test="appointmentType != null">
        #{appointmentType,jdbcType=BIT},
      </if>
      <if test="serveFee != null">
        #{serveFee,jdbcType=INTEGER},
      </if>
      <if test="serveStart != null">
        #{serveStart,jdbcType=TIMESTAMP},
      </if>
      <if test="serveEnd != null">
        #{serveEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="serveDate != null">
        #{serveDate,jdbcType=VARCHAR},
      </if>
      <if test="serveTime != null">
        #{serveTime,jdbcType=INTEGER},
      </if>
      <if test="serveType != null">
        #{serveType,jdbcType=BIT},
      </if>
      <if test="verificationState != null">
        #{verificationState,jdbcType=BIT},
      </if>
      <if test="optionsVerification != null">
        #{optionsVerification,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.stbella.order.infrastructure.repository.po.OrderProductionAppointment">
    update he_order_production_appointment
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderSn != null">
        order_sn = #{orderSn,jdbcType=VARCHAR},
      </if>
      <if test="orderProductionCardId != null">
        order_production_card_id = #{orderProductionCardId,jdbcType=INTEGER},
      </if>
      <if test="orderProductionCardExtendId != null">
        order_production_card_extend_id = #{orderProductionCardExtendId,jdbcType=INTEGER},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=INTEGER},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="basicId != null">
        basic_id = #{basicId,jdbcType=INTEGER},
      </if>
      <if test="clientId != null">
        client_id = #{clientId,jdbcType=INTEGER},
      </if>
      <if test="clientName != null">
        client_name = #{clientName,jdbcType=VARCHAR},
      </if>
      <if test="clientPhone != null">
        client_phone = #{clientPhone,jdbcType=VARCHAR},
      </if>
      <if test="therapistId != null">
        therapist_id = #{therapistId,jdbcType=BIGINT},
      </if>
      <if test="therapistName != null">
        therapist_name = #{therapistName,jdbcType=VARCHAR},
      </if>
      <if test="groupGoodsId != null">
        group_goods_id = #{groupGoodsId,jdbcType=INTEGER},
      </if>
      <if test="groupGoodsName != null">
        group_goods_name = #{groupGoodsName,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=INTEGER},
      </if>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="productionGoodsId != null">
        production_goods_id = #{productionGoodsId,jdbcType=INTEGER},
      </if>
      <if test="productionGoodsName != null">
        production_goods_name = #{productionGoodsName,jdbcType=VARCHAR},
      </if>
      <if test="productionSkuId != null">
        production_sku_id = #{productionSkuId,jdbcType=INTEGER},
      </if>
      <if test="productionSkuName != null">
        production_sku_name = #{productionSkuName,jdbcType=VARCHAR},
      </if>
      <if test="orderProductionType != null">
        order_production_type = #{orderProductionType,jdbcType=TINYINT},
      </if>
      <if test="productionInstrId != null">
        production_instr_id = #{productionInstrId,jdbcType=BIGINT},
      </if>
      <if test="itemType != null">
        item_type = #{itemType,jdbcType=BIT},
      </if>
      <if test="bookType != null">
        book_type = #{bookType,jdbcType=BIT},
      </if>
      <if test="appointmentType != null">
        appointment_type = #{appointmentType,jdbcType=BIT},
      </if>
      <if test="serveFee != null">
        serve_fee = #{serveFee,jdbcType=INTEGER},
      </if>
      <if test="serveStart != null">
        serve_start = #{serveStart,jdbcType=TIMESTAMP},
      </if>
      <if test="serveEnd != null">
        serve_end = #{serveEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="serveDate != null">
        serve_date = #{serveDate,jdbcType=VARCHAR},
      </if>
      <if test="serveTime != null">
        serve_time = #{serveTime,jdbcType=INTEGER},
      </if>
      <if test="serveType != null">
        serve_type = #{serveType,jdbcType=BIT},
      </if>
      <if test="verificationState != null">
        verification_state = #{verificationState,jdbcType=BIT},
      </if>
      <if test="optionsVerification != null">
        options_verification = #{optionsVerification,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.stbella.order.infrastructure.repository.po.OrderProductionAppointment">
    update he_order_production_appointment
    set order_id = #{orderId,jdbcType=INTEGER},
      order_sn = #{orderSn,jdbcType=VARCHAR},
      order_production_card_id = #{orderProductionCardId,jdbcType=INTEGER},
      order_production_card_extend_id = #{orderProductionCardExtendId,jdbcType=INTEGER},
      store_id = #{storeId,jdbcType=INTEGER},
      store_name = #{storeName,jdbcType=VARCHAR},
      basic_id = #{basicId,jdbcType=INTEGER},
      client_id = #{clientId,jdbcType=INTEGER},
      client_name = #{clientName,jdbcType=VARCHAR},
      client_phone = #{clientPhone,jdbcType=VARCHAR},
      therapist_id = #{therapistId,jdbcType=BIGINT},
      therapist_name = #{therapistName,jdbcType=VARCHAR},
      group_goods_id = #{groupGoodsId,jdbcType=INTEGER},
      group_goods_name = #{groupGoodsName,jdbcType=VARCHAR},
      group_id = #{groupId,jdbcType=INTEGER},
      group_name = #{groupName,jdbcType=VARCHAR},
      production_goods_id = #{productionGoodsId,jdbcType=INTEGER},
      production_goods_name = #{productionGoodsName,jdbcType=VARCHAR},
      production_sku_id = #{productionSkuId,jdbcType=INTEGER},
      production_sku_name = #{productionSkuName,jdbcType=VARCHAR},
      order_production_type = #{orderProductionType,jdbcType=TINYINT},
      production_instr_id = #{productionInstrId,jdbcType=BIGINT},
      item_type = #{itemType,jdbcType=BIT},
      book_type = #{bookType,jdbcType=BIT},
      appointment_type = #{appointmentType,jdbcType=BIT},
      serve_fee = #{serveFee,jdbcType=INTEGER},
      serve_start = #{serveStart,jdbcType=TIMESTAMP},
      serve_end = #{serveEnd,jdbcType=TIMESTAMP},
      serve_date = #{serveDate,jdbcType=VARCHAR},
      serve_time = #{serveTime,jdbcType=INTEGER},
      serve_type = #{serveType,jdbcType=BIT},
      verification_state = #{verificationState,jdbcType=BIT},
      options_verification = #{optionsVerification,jdbcType=VARCHAR},
      extra = #{extra,jdbcType=VARCHAR},
      creator_id = #{creatorId,jdbcType=INTEGER},
      creator_name = #{creatorName,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=BIT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByParam"
          parameterType="com.stbella.order.infrastructure.repository.condition.ProductionAppointmentQueryCondition"
          resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from he_order_production_appointment
    <if test="condition != null">
      <where>
        deleted = 0
        and verification_state in(1,2)
        <if test="condition.appointmentType != null">
          and appointment_type = #{condition.appointmentType,jdbcType=NUMERIC}
        </if>
        <if test="condition.storeId != null">
          AND store_id = #{condition.storeId,jdbcType=NUMERIC}
        </if>
        <if test="condition.therapistId != null">
          AND therapist_id = #{condition.therapistId,jdbcType=NUMERIC}
        </if>
        <if test="condition.serveStart != null">
          AND `serve_start` >= #{condition.serveStart}
        </if>
        <if test="condition.serveEnd != null">
          AND `serve_end` &lt;= #{condition.serveEnd}
        </if>
      </where>
    </if>
    ORDER BY
    <if test="condition != null and condition.skuIds != null and condition.skuIds.size() > 0">
      FIELD(production_sku_id,<foreach collection="condition.skuIds" separator="," item="item">
      #{item}
    </foreach>),
    </if>
    LEFT(serve_start, 10) DESC,
    RIGHT(serve_start, 8) ASC
    <if test="condition != null">
      <if test="condition.limit != null and condition.offset != null">
        LIMIT #{condition.limit} OFFSET #{condition.offset}
      </if>
    </if>
  </select>

  <select id="countByParam"
          parameterType="com.stbella.order.infrastructure.repository.condition.ProductionAppointmentQueryCondition"
          resultType="java.lang.Long">
    select count(id)
    from he_order_production_appointment
    <if test="condition != null">
      <where>
        deleted = 0 and verification_state in(1,2)
        <if test="condition.storeId != null">
          AND store_id = #{condition.storeId,jdbcType=NUMERIC}
        </if>
        <if test="condition.therapistId != null">
          AND therapist_id = #{condition.therapistId,jdbcType=NUMERIC}
        </if>
        <if test="condition.serveStart != null">
          AND `serve_start` >= #{condition.serveStart}
        </if>
        <if test="condition.serveEnd != null">
          AND `serve_end` &lt;= #{condition.serveEnd}
        </if>
      </where>
    </if>
  </select>
  <select id="sumWorkmanshipByTherapistId"
          resultType="com.stbella.order.infrastructure.repository.po.ProductionAppointmentBoardWorkmanship">
    SELECT t.id as therapist_id, t.name as therapist_name,
           SUM(IF(opa.verification_state = 2, opa.serve_fee, 0))                             wait,
           SUM(IF(opa.verification_state = 1, opa.serve_fee, 0))                             consumed,
           SUM(IF((opa.verification_state = 1 or opa.verification_state = 2), opa.serve_fee, 0)) cumulative
    FROM he_therapist as t
           JOIN he_order_production_appointment as opa on opa.therapist_id = t.id
    <if test="condition != null">
      <where>
        opa.deleted = 0
        <if test="condition.therapistId != null">
          AND t.id = #{condition.therapistId,jdbcType=NUMERIC}
        </if>
        <if test="condition.serveStart != null">
          AND opa.serve_start >= #{condition.serveStart}
        </if>
        <if test="condition.serveEnd != null">
          AND opa.serve_end &lt;= #{condition.serveEnd}
        </if>
        <if test="condition.bookType != null">
          AND opa.book_type = #{condition.bookType,jdbcType=NUMERIC}
        </if>
        <if test="condition.storeId != null">
          AND opa.store_id = #{condition.storeId,jdbcType=NUMERIC}
        </if>
      </where>
    </if>
  </select>

  <select id="getProductionBookList" resultType="com.stbella.order.infrastructure.repository.po.OrderProductionAppointment">
    select
    t.id, t.order_id, t.order_sn, t.order_production_card_id, t.order_production_card_extend_id, t.store_id, t.store_name, t.basic_id, t.client_id,
    t.client_name, t.client_phone, t.therapist_id, t.therapist_name, t.group_goods_id, t.group_goods_name,
    t.group_id, t.group_name, t.production_goods_id, t.production_goods_name, t.production_sku_id,
    t.production_sku_name, t.order_production_type, t.production_instr_id, t.item_type, t.book_type,
    t.appointment_type, t.serve_fee, t.serve_start, t.serve_end, t.serve_date, t.serve_time, t.serve_type,
    t.verification_state, t.options_verification, t.creator_id, t.creator_name, t.gmt_create, t.gmt_modified, t.update_id, t.update_name
    from he_order_production_appointment t
    left join he_therapist h on (h.id = t.therapist_id and h.deleted = 0)
    where t.deleted = 0 and t.verification_state != 0 
    <if test="query.therapistName != null and query.therapistName != ''">
      and (t.therapist_name like concat('%', #{query.therapistName,jdbcType=VARCHAR}, '%')
      or h.mobile like concat('%', #{query.therapistName,jdbcType=VARCHAR}, '%'))
    </if>
    <if test="query.clientName != null and query.clientName != ''">
      and (t.client_name like concat('%', #{query.clientName,jdbcType=VARCHAR}, '%')
      or t.client_phone like concat('%', #{query.clientName,jdbcType=VARCHAR}, '%'))
    </if>
    <if test="query.storeId != null">
      and t.store_id = #{query.storeId}
    </if>
    <if test="query.verificationState != null">
      and t.verification_state = #{query.verificationState}
    </if>
    <if test="query.bookType != null">
      and t.book_type = #{query.bookType}
    </if>

    <if test="query.serveStart != null">
      and t.serve_start &gt;= #{query.serveStart}
    </if>
    <if test="query.serveEnd != null">
      and t.serve_end &lt;= #{query.serveEnd}
    </if>
    <if test="query != null and query.appointmentIds != null and query.appointmentIds.size() != 0 ">
      AND t.id IN
      <foreach collection="query.appointmentIds" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    order by t.gmt_create DESC
  </select>
</mapper>
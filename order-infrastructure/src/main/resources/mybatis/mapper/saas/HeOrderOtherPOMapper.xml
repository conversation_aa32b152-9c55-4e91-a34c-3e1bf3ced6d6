<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeOrderOtherMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeOrderOtherPO">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="goods_name" property="goodsName"/>
        <result column="goods_type" property="goodsType"/>
        <result column="goods_type_name" property="goodsTypeName"/>
        <result column="remark" property="remark"/>
        <result column="discount_amount" property="discountAmount"/>
        <result column="discount" property="discount"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        order_id, goods_name, goods_type, goods_type_name, remark, discount_amount, discount, is_delete, created_at, updated_at
    </sql>

</mapper>

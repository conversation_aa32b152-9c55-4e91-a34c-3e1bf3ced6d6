<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.HeOrderGoodsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeOrderGoodsPO">
    <result column="id" property="id" />
        <result column="parent_id" property="parentId" />
        <result column="basic_uid" property="basicUid" />
        <result column="client_uid" property="clientUid" />
        <result column="client_type" property="clientType" />
        <result column="staff_id" property="staffId" />
        <result column="store_id" property="storeId" />
        <result column="order_id" property="orderId" />
        <result column="pay_status" property="payStatus" />
        <result column="goods_type" property="goodsType" />
        <result column="sku_name" property="skuName" />
        <result column="sku_id" property="skuId" />
        <result column="goods_id" property="goodsId" />
        <result column="goods_name" property="goodsName" />
        <result column="goods_image" property="goodsImage" />
        <result column="goods_num" property="goodsNum" />
        <result column="goods_cost" property="goodsCost"/>
        <result column="goods_price_orgin" property="goodsPriceOrgin" />
        <result column="goods_price_pay" property="goodsPricePay" />
        <result column="pay_amount" property="payAmount" />
        <result column="send_status" property="sendStatus" />
        <result column="refund_status" property="refundStatus" />
        <result column="source" property="source" />
        <result column="content" property="content" />
        <result column="is_close" property="isClose" />
        <result column="is_delete" property="isDelete" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt"/>
        <result column="sku_extend_day" property="skuExtendDay"/>
        <result column="sku_extend_value" property="skuExtendValue"/>
        <result column="sku_extend_id" property="skuExtendId"/>
        <result column="goods_sell_type" property="goodsSellType"/>
        <result column="integral" property="integral"/>
        <result column="pay_integral" property="payIntegral"/>
        <result column="coupon_amount" property="couponAmount"/>
        <result column="coupon_user_id" property="couponUserId"/>
        <result column="piece" property="piece"/>
        <result column="ecp_room_type" property="ecpRoomType"/>
        <result column="defined_property" property="definedProperty"/>
        <result column="type" jdbcType="INTEGER" property="type" />
        <result column="business_type" jdbcType="VARCHAR" property="businessType" />
        <result column="asset_type" jdbcType="VARCHAR" property="assetType" />
        <result column="back_category_id" jdbcType="BIGINT" property="backCategoryId" />
        <result column="valid_start_time" jdbcType="TIMESTAMP" property="validStartTime" />
        <result column="valid_end_time" jdbcType="TIMESTAMP" property="validEndTime" />
        <result column="state" jdbcType="INTEGER" property="state" />
        <result column="verification_status" jdbcType="INTEGER" property="verificationStatus" />
        <result column="used_num" jdbcType="INTEGER" property="usedNum" />
        <result column="gift" jdbcType="INTEGER" property="gift" />
        <result column="production_discount_rule" jdbcType="VARCHAR" property="productionDiscountRule" />

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        parent_id, basic_uid, client_uid, client_type, staff_id, store_id, order_id, pay_status, goods_type, sku_name, sku_id, goods_id, goods_name, goods_image, goods_num, goods_cost, goods_price_orgin, goods_price_pay, pay_amount, send_status, refund_status, source, content, is_close, is_delete, created_at, updated_at, sku_extend_day, sku_extend_value, sku_extend_id, goods_sell_type, integral, pay_integral, coupon_amount, coupon_user_id, piece,ecp_room_type, defined_property
           ,type, business_type, asset_type, back_category_id, valid_start_time, valid_end_time, state, verification_status, used_num, gift,production_discount_rule


    </sql>

</mapper>

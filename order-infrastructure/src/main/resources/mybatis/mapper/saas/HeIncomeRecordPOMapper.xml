<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeIncomeRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeIncomeRecordPO">
    <result column="id" property="id" />
        <result column="client_uid" property="clientUid"/>
        <result column="basic_uid" property="basicUid"/>
        <result column="income_sn" property="incomeSn" />
        <result column="order_id" property="orderId" />
        <result column="order_goods_id" property="orderGoodsId" />
        <result column="task_id" property="taskId" />
        <result column="offline_task_id" property="offlineTaskId" />
        <result column="store_id" property="storeId" />
        <result column="channel_type" property="channelType" />
        <result column="pay_type" property="payType" />
        <result column="params" property="params" />
        <result column="status" property="status" />
        <result column="income" property="income" />
        <result column="bonus_status" property="bonusStatus" />
        <result column="pay_num" property="payNum" />
        <result column="pay_time" property="payTime" />
        <result column="check_status" property="checkStatus" />
        <result column="transaction_id" property="transactionId" />
        <result column="subtotal_income" property="subtotalIncome" />
        <result column="receipt_type" property="receiptType" />
        <result column="is_delete" property="isDelete" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="account_id" property="accountId"/>
        <result column="currency" property="currency"/>
        <result column="order_id_bak" property="orderIdBak"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        basic_uid,client_uid,income_sn, order_id, order_goods_id, task_id, offline_task_id, store_id, channel_type, pay_type, params, status, income, bonus_status, pay_num, pay_time, check_status, transaction_id, subtotal_income, receipt_type, is_delete, created_at, updated_at,account_id,order_id_bak
            ,currency,remark
    </sql>

    <!-- 用户维度已支付押金统计 -->
    <select id="queryStatisticsIncome"
            parameterType="com.stbella.order.infrastructure.repository.condition.ClientDepositCondition"
            resultType="com.stbella.order.server.order.month.res.PayDepositRecordVO">
        select client_uid,store_id,sum(income) subtotal_amount,sum(already_refund_amount) refund_amount
        from he_income_record where `status`=1 and receipt_type=4 and client_uid is not null
        <if test="query != null">
            <if test="query.storeIds != null and query.storeIds.size() >0">
                and `store_id` in
                <foreach collection="query.storeIds" item="item" separator="," open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.clientIds != null and query.clientIds.size() >0">
                and `client_uid` in
                <foreach collection="query.clientIds" item="item" separator="," open="("
                         close=")">
                    #{item}
                </foreach>
            </if>

        </if>
        GROUP BY client_uid order by subtotal_amount desc
    </select>
    <select id="getRecordPageByOrderIdWithPayType"
            resultType="com.stbella.order.infrastructure.repository.po.HeIncomeRecordPO">

        SELECT *
        FROM he_income_record
        WHERE is_delete = 0
          AND order_id = #{orderId}
          AND (status = 1
            OR (pay_type = 3 AND status = 2))
        ORDER BY pay_time DESC
    </select>


    <select id="getRealIncome" resultType="Integer">
        SELECT (SELECT SUM(i.income)
                FROM he_order
                         LEFT JOIN (SELECT he_income_record.order_id,
                                           IF
                                           (he_income_record.income IS NULL, 0, he_income_record.income) AS income
                                    FROM he_income_record
                                    WHERE he_income_record.pay_type NOT IN (3, 100)
                                      AND he_income_record.`status` = 1
                                      AND he_income_record.order_id = #{orderId}
                                    UNION ALL
                                    SELECT he_income_record.order_id,
                                           IF
                                           (he_income_record.income IS NULL, 0, he_income_record.income) AS income
                                    FROM he_income_record
                                             LEFT JOIN he_income_proof_record
                                                       ON he_income_record.id = he_income_proof_record.income_id
                                    WHERE he_income_record.pay_type = 3
                                      AND he_income_record.`status` = 1
                                      AND he_income_proof_record.`status` = 1
                                      AND he_income_record.order_id = #{orderId}) AS i
                                   ON i.order_id = he_order.order_id) -
               (SELECT COALESCE(SUM(IF(actual_amount IS NULL OR actual_amount = '', 0, actual_amount)),
                                0) AS actualAmount
                FROM he_order_refund
                WHERE order_id = #{orderId}
                  AND STATUS IN (1, 3, 4)
                  AND refund_type NOT IN (3, 100)
                  AND type = 1) AS 'realAmount'


    </select>
</mapper>

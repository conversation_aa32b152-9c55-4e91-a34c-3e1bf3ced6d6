<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeOrderAttachmentMapper">
  <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeOrderAttachmentPO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="og_id" jdbcType="INTEGER" property="ogId" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="is_delete" jdbcType="BIT" property="isDelete" />
    <result column="created_at" jdbcType="BIGINT" property="createdAt" />
    <result column="updated_at" jdbcType="BIGINT" property="updatedAt" />
    <result column="sku_id" jdbcType="INTEGER" property="skuId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_id, og_id, url, name, is_delete, created_at, updated_at,skuId
  </sql>
</mapper>
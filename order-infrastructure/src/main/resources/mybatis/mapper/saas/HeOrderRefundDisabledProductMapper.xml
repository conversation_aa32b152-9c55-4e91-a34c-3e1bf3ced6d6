<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeOrderRefundDisabledProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeOrderRefundDisabledProduct">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="product_type" property="productType"/>
        <result column="product_id" property="productId"/>
        <result column="refund_id" property="refundId"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , order_id, product_type, product_id, refund_id, deleted, gmt_create, gmt_modified
    </sql>

</mapper>

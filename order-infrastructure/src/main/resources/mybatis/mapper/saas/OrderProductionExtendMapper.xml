<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.OrderProductionExtendMapper">
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.OrderProductionExtend">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
        <result column="order_id" jdbcType="INTEGER" property="orderId"/>
        <result column="basic_id" jdbcType="INTEGER" property="basicId"/>
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName"/>
        <result column="sku_id" jdbcType="INTEGER" property="skuId"/>
        <result column="sku_name" jdbcType="VARCHAR" property="skuName"/>
        <result column="type" jdbcType="BIT" property="type"/>
        <result column="price" jdbcType="INTEGER" property="price"/>
        <result column="cost" jdbcType="INTEGER" property="cost"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="valid_start_time" jdbcType="INTEGER" property="validStartTime"/>
        <result column="valid_end_time" jdbcType="INTEGER" property="validEndTime"/>
        <result column="verification_status" jdbcType="TINYINT" property="verificationStatus"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="store_id" jdbcType="INTEGER" property="storeId"/>
        <result column="goods_id" jdbcType="INTEGER" property="goodsId"/>
        <result column="goods_num" jdbcType="INTEGER" property="goodsNum"/>
        <result column="validity_value" jdbcType="INTEGER" property="validityValue"/>
        <result column="validity_type" jdbcType="TINYINT" property="validityType"/>
        <result column="real_paid" property="realPaid"/>
        <result column="data_source" property="dataSource"/>
        <result column="original_id" property="originalId"/>
        <result column="order_goods_sn" property="orderGoodsSn"/>
    </resultMap>



    <sql id="Base_Column_List">
       *
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from he_order_production_extend
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from he_order_production_extend
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="save" parameterType="com.stbella.order.infrastructure.repository.po.OrderProductionExtend">
        insert into he_order_production_extend (id, gmt_create, gmt_modified,
                                                deleted, order_id, basic_id,
                                                goods_name, sku_id, sku_name,
                                                type, price, cost, content,
                                                valid_start_time, valid_end_time, verification_status,
                                                status, store_id, goods_id,
                                                goods_num, validity_value, validity_type)
        values (#{id,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP},
                #{deleted,jdbcType=BIT}, #{orderId,jdbcType=INTEGER}, #{basicId,jdbcType=INTEGER},
                #{goodsName,jdbcType=VARCHAR}, #{skuId,jdbcType=INTEGER}, #{skuName,jdbcType=VARCHAR},
                #{type,jdbcType=BIT}, #{price,jdbcType=INTEGER}, #{cost,jdbcType=INTEGER}, #{content,jdbcType=VARCHAR},
                #{validStartTime,jdbcType=INTEGER}, #{validEndTime,jdbcType=INTEGER},
                #{verificationStatus,jdbcType=TINYINT},
                #{status,jdbcType=TINYINT}, #{storeId,jdbcType=INTEGER}, #{goodsId,jdbcType=INTEGER},
                #{goodsNum,jdbcType=INTEGER}, #{validityValue,jdbcType=INTEGER},
                #{validityType,jdbcType=TINYINT})
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.stbella.order.infrastructure.repository.po.OrderProductionExtend">
        update he_order_production_extend
        <set>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=INTEGER},
            </if>
            <if test="basicId != null">
                basic_id = #{basicId,jdbcType=INTEGER},
            </if>
            <if test="goodsName != null">
                goods_name = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="skuId != null">
                sku_id = #{skuId,jdbcType=INTEGER},
            </if>
            <if test="skuName != null">
                sku_name = #{skuName,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=BIT},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=INTEGER},
            </if>
            <if test="cost != null">
                cost = #{cost,jdbcType=INTEGER},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="validStartTime != null">
                valid_start_time = #{validStartTime,jdbcType=INTEGER},
            </if>
            <if test="validEndTime != null">
                valid_end_time = #{validEndTime,jdbcType=INTEGER},
            </if>
            <if test="verificationStatus != null">
                verification_status = #{verificationStatus,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="storeId != null">
                store_id = #{storeId,jdbcType=INTEGER},
            </if>
            <if test="goodsId != null">
                goods_id = #{goodsId,jdbcType=INTEGER},
            </if>
            <if test="goodsNum != null">
                goods_num = #{goodsNum,jdbcType=INTEGER},
            </if>
            <if test="validityValue != null">
                validity_value = #{validityValue,jdbcType=INTEGER},
            </if>
            <if test="validityType != null">
                validity_type = #{validityType,jdbcType=TINYINT},
            </if>
            <if test="realPaid != null">
                real_paid = #{realPaid},
            </if>

        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.stbella.order.infrastructure.repository.po.OrderProductionExtend">
        update he_order_production_extend
        set gmt_create          = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_modified        = #{gmtModified,jdbcType=TIMESTAMP},
            deleted             = #{deleted,jdbcType=BIT},
            order_id            = #{orderId,jdbcType=INTEGER},
            basic_id            = #{basicId,jdbcType=INTEGER},
            goods_name          = #{goodsName,jdbcType=VARCHAR},
            sku_id              = #{skuId,jdbcType=INTEGER},
            sku_name            = #{skuName,jdbcType=VARCHAR},
            type                = #{type,jdbcType=BIT},
            price               = #{price,jdbcType=INTEGER},
            cost                = #{cost,jdbcType=INTEGER},
            content             = #{content,jdbcType=VARCHAR},
            valid_start_time    = #{validStartTime,jdbcType=INTEGER},
            valid_end_time      = #{validEndTime,jdbcType=INTEGER},
            verification_status = #{verificationStatus,jdbcType=TINYINT},
            status              = #{status,jdbcType=TINYINT},
            store_id            = #{storeId,jdbcType=INTEGER},
            goods_id            = #{goodsId,jdbcType=INTEGER},
            goods_num           = #{goodsNum,jdbcType=INTEGER} validity_value = #{validityValue,jdbcType=INTEGER},
            validity_type = #{validityType,jdbcType=TINYINT}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="expiredByProductionIds">
        update he_order_production_extend
        set status=2 ,gmt_modified=now()
        where
        deleted= 0
        <if test="productionIds !=null and productionIds.size() > 0">
            and id in(
            <foreach collection="productionIds" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>
    </update>

    <select id="queryProductionOrderGoodsList" resultType="com.stbella.order.server.order.ProductionOrderGoodsModel">

        select
        o.order_id, o.order_sn, o.store_id, o.client_uid, o.basic_uid,o.order_type,o.pay_amount,o.paid_amount,o.order_amount,o.production_amount_pay,
        o.created_at
        ,o.percent_first_time,
        p.valid_start_time,p.valid_end_time, p.verification_status, p.status, p.goods_num, p.price, p.type, p.goods_id, p.goods_name, p.sku_id, p.sku_name,p.sku_num
        ,p.id,p.real_paid,data_source

        from
        he_order_production_extend p left join he_order o on o.order_id = p.order_id
        where
        o.order_type = #{req.orderType}

        and o.is_Delete = 0
        <if test="req.orderSn != null">
            and  o.order_sn = #{req.orderSn}
        </if>
        <if test="req.orderSn == null">
            and o.percent_first_time >=  #{req.startTime}
            AND (`paid_amount` > 100 or `paid_amount` &lt; -100)
            and percent_first_time &lt;= #{req.endTime}
        </if>


        and p.deleted = 0

    </select>

    <!--  获取礼赠资产的实付金额统计  -->
    <select id="getGiftRealPaid" resultType="java.math.BigDecimal">
        select sum(ta.real_paid) as gift_sum
        from he_order_gift_extend ta
        where ta.deleted = 0
          and ta.id in (select tb.order_production_card_id
                        from he_order_production_appointment tb
                            join he_order tc on tb.order_id = tc.order_id
                        where
                          tb.deleted = 0
                          and tc.is_delete = 0
                          and tb.serve_type = 0
                          and tc.created_at &gt;= #{req.orderCreatedAt}
                          <if test="req.productionGoodsIds !=null and req.productionGoodsIds.size() > 0">
                              and tb.production_goods_id not in (
                              <foreach collection="req.productionGoodsIds" separator="," item="item">
                                  #{item}
                              </foreach>
                              )
                          </if>
                          <if test="req.basicIds !=null and req.basicIds.size() > 0">
                              and tb.basic_id in (
                              <foreach collection="req.basicIds" separator="," item="item">
                                  #{item}
                              </foreach>
                              )
                          </if>
                          <if test="req.storeId != null">
                              and tb.store_id = #{req.storeId}
                          </if>
                          <if test="req.serveStartDate != null and req.serveEndDate != null">
                              and tb.serve_start between #{req.serveStartDate} and #{req.serveEndDate}
                          </if>
                          <if test="req.appointmentIds !=null and req.appointmentIds.size() > 0">
                              and tb.id in (
                                  <foreach collection="req.appointmentIds" separator="," item="item">
                                      #{item}
                                  </foreach>
                              )
                          </if>
                          and tb.item_type = 2
                          and tb.order_production_type = 2
                          and tb.verification_state = #{req.verificationState})
          and ta.real_paid > 0
          and ta.verification_status = 1
          and ta.real_paid is not null;
    </select>

    <!--  获取礼赠资产的实付金额统计(三方)  -->
    <select id="getGiftRealPaidThird" resultType="java.math.BigDecimal">
        select sum(a.real_paid) as gift_sum
        from he_order_gift_extend a
                 join he_order_production_appointment b
                      on a.id = b.order_production_card_id
                 join he_order c on a.order_id = c.order_id
        where a.deleted = 0
          and b.deleted = 0
          and c.is_delete = 0
          and b.serve_type = 1
          and a.real_paid is not null
          and a.real_paid > 0
          and a.verification_status = 1
          and b.verification_state = 1
          and b.order_production_type = 2
          and b.item_type = 2
          and a.store_id = #{req.storeId}
          and c.created_at &gt;= #{req.orderCreatedAt}
        <if test="req.appointmentIds !=null and req.appointmentIds.size() > 0">
            and b.id in (
            <foreach collection="req.appointmentIds" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>
        <if test="req.productionGoodsIds !=null and req.productionGoodsIds.size() > 0">
            and b.production_goods_id not in (
            <foreach collection="req.productionGoodsIds" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>;
    </select>

    <!--  获取单项的实付金额统计  -->
    <select id="getProductionRealPaid" resultType="java.math.BigDecimal">
        select sum(ta.real_paid) as production_sum
        from he_order_production_extend ta
        where ta.deleted = 0
          and ta.id in (select tb.order_production_card_id
                from he_order_production_appointment tb
                    join he_order tc on tb.order_id = tc.order_id
                where
                tb.deleted = 0
                and tc.is_delete = 0
                and tb.serve_type = 0
                and tc.created_at &gt;= #{req.orderCreatedAt}
                <if test="req.productionGoodsIds !=null and req.productionGoodsIds.size() > 0">
                    and tb.production_goods_id not in (
                    <foreach collection="req.productionGoodsIds" separator="," item="item">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="req.basicIds !=null and req.basicIds.size() > 0">
                    and tb.basic_id in (
                    <foreach collection="req.basicIds" separator="," item="item">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="req.storeId != null">
                    and tb.store_id = #{req.storeId}
                </if>
                <if test="req.serveStartDate != null and req.serveEndDate != null">
                    and tb.serve_start between #{req.serveStartDate} and #{req.serveEndDate}
                </if>
                <if test="req.appointmentIds !=null and req.appointmentIds.size() > 0">
                    and tb.id in (
                    <foreach collection="req.appointmentIds" separator="," item="item">
                        #{item}
                    </foreach>
                    )
                </if>
                and tb.item_type = 2
                and tb.order_production_type = 1
                and tb.verification_state = #{req.verificationState})
          and ta.type = 2
          and ta.real_paid > 0
          and ta.real_paid is not null;
    </select>

    <!--  获取单项的实付金额统计(三方)  -->
    <select id="getProductionRealPaidThird" resultType="java.math.BigDecimal">
        select sum(a.real_paid) as production_sum
        from he_order_production_extend a
                 join he_order_production_appointment b
                      on a.id = b.order_production_card_id
                 join he_order c on a.order_id = c.order_id
        where a.deleted = 0
          and b.deleted = 0
          and c.is_delete = 0
          and b.serve_type = 1
          and a.real_paid is not null
          and a.real_paid > 0
          and b.verification_state = 1
          and b.order_production_type = 1
          and b.item_type = 2
          and a.type = 2
          and a.store_id = #{req.storeId}
          and c.created_at &gt;= #{req.orderCreatedAt}
        <if test="req.appointmentIds !=null and req.appointmentIds.size() > 0">
            and b.id in (
            <foreach collection="req.appointmentIds" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>
        <if test="req.productionGoodsIds !=null and req.productionGoodsIds.size() > 0">
            and b.production_goods_id not in (
            <foreach collection="req.productionGoodsIds" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>;
    </select>

    <!--  获取通次卡的实付金额统计  -->
    <select id="getProductionCardRealPaid" resultType="java.math.BigDecimal">
        select sum(ta.real_paid) as production_card_sum
        from he_order_production_card_extend ta
                 join he_order_production_appointment tb on ta.id = tb.order_production_card_extend_id
                 join he_order tc on ta.order_id = tc.order_id
        where ta.deleted = 0
          and tb.deleted = 0
          and tb.serve_type = 0
          and ta.real_paid is not null
          and ta.real_paid > 0
          and tb.order_production_type = 1
          and tb.verification_state = #{req.verificationState}
          and tc.is_delete = 0
          and tc.created_at &gt;= #{req.orderCreatedAt}
          <if test="req.productionGoodsIds !=null and req.productionGoodsIds.size() > 0">
              and tb.production_goods_id not in (
              <foreach collection="req.productionGoodsIds" separator="," item="item">
                  #{item}
              </foreach>
              )
          </if>
          <if test="req.basicIds !=null and req.basicIds.size() > 0">
              and tb.basic_id in (
              <foreach collection="req.basicIds" separator="," item="item">
                  #{item}
              </foreach>
              )
          </if>
          <if test="req.storeId != null">
              and tb.store_id = #{req.storeId}
          </if>
          <if test="req.serveStartDate != null and req.serveEndDate != null">
              and tb.serve_start between #{req.serveStartDate} and #{req.serveEndDate}
          </if>
          <if test="req.appointmentIds !=null and req.appointmentIds.size() > 0">
              and tb.id in (
              <foreach collection="req.appointmentIds" separator="," item="item">
                  #{item}
              </foreach>
              )
          </if>
          and tb.item_type in (0, 1);
    </select>

    <!--  获取通次卡的实付金额统计(三方)  -->
    <select id="getProductionCardRealPaidThird" resultType="java.math.BigDecimal">
        select
            sum(a.real_paid) as production_sum
        from he_order_production_card_extend a
            join he_order_production_appointment b
                on a.id = b.order_production_card_extend_id
            join he_order c on a.order_id = c.order_id
        where a.deleted = 0
            and b.deleted = 0
            and c.is_delete = 0
            and b.serve_type = 1
            and a.real_paid is not null
            and a.real_paid > 0
            and b.verification_state = 1
            and b.order_production_type = 1
            and b.item_type = 2
            and a.type = 2
            and b.item_type in (0, 1)
            and c.store_id = #{req.storeId}
            and c.created_at &gt;= #{req.orderCreatedAt}
        <if test="req.appointmentIds !=null and req.appointmentIds.size() > 0">
            and b.id in (
            <foreach collection="req.appointmentIds" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>
        <if test="req.productionGoodsIds !=null and req.productionGoodsIds.size() > 0">
            and b.production_goods_id not in (
            <foreach collection="req.productionGoodsIds" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>;
    </select>

    <select id="getUnwrittenSummary" resultType="java.math.BigDecimal">
        select if(a.gift is null, 0, a.gift) +
               if(b.production is null, 0, b.production) +
               if(c.production_card is null, 0, c.production_card) as unwritten_summary
        from (select sum(ta.real_paid) as gift
              from he_order_gift_extend ta
                       join he_order tb on ta.order_id = tb.order_id
              where ta.deleted = 0
                and tb.is_delete = 0
                <if test="req.storeId != null">
                    and ta.store_id = #{req.storeId}
                </if>
                <if test="req.productionGoodsIds !=null and req.productionGoodsIds.size() > 0">
                    and ta.goods_id not in (
                    <foreach collection="req.productionGoodsIds" separator="," item="item">
                        #{item}
                    </foreach>
                    )
                </if>
                and ta.status = 1
                and tb.created_at &gt;= #{req.orderCreatedAt}
                and ta.verification_status in (0, 2)
                and ta.real_paid is not null
                and ta.real_paid > 0
                and ta.type = 6) a,
             (select sum(ta.real_paid) as production
              from he_order_production_extend ta
                join he_order tb on ta.order_id = tb.order_id
              where ta.deleted = 0
                and tb.is_delete = 0
                and ta.status = 1
                <if test="req.storeId != null">
                    and ta.store_id = #{req.storeId}
                </if>
                <if test="req.productionGoodsIds !=null and req.productionGoodsIds.size() > 0">
                    and ta.goods_id not in (
                    <foreach collection="req.productionGoodsIds" separator="," item="item">
                        #{item}
                    </foreach>
                    )
                </if>
                and tb.created_at &gt;= #{req.orderCreatedAt}
                and ta.real_paid is not null
                and ta.verification_status in (0, 2)
                and ta.type = 2
                and ta.real_paid > 0) b,
             (select sum((tb.num - if(tb.used_num is null, 0, tb.used_num)) * tb.real_paid) as production_card
              from he_order_production_extend ta
                       join he_order_production_card_extend tb on ta.id = tb.order_production_id
                        join he_order tc on ta.order_id = tc.order_id
              where ta.deleted = 0
                and tb.deleted = 0
                and tc.is_delete = 0
                and tc.created_at &gt;= #{req.orderCreatedAt}
                and ta.type in (0, 1)
                and tb.type in (0, 1)
                and tb.status = 1
                and tb.real_paid is not null
                and tb.real_paid > 0
                and tb.num > 0
                <if test="req.storeId != null">
                    and ta.store_id = #{req.storeId}
                </if>
              ) c;
    </select>

    <select id="getTherapistRealPaid" resultType="com.stbella.order.server.order.ProductionTherapistRealPaidModel">
        select a.therapist_id, sum(a.val) as val
        from (
         ## 礼赠
         select tb.therapist_id, sum(ta.real_paid) as val
         from he_order_gift_extend ta
                  join he_order_production_appointment tb on ta.id = tb.order_production_card_id
                  join he_order tc on tb.order_id = tc.order_id
         where ta.deleted = 0
           and tb.deleted = 0
           and tc.is_delete = 0
           and tc.created_at &gt;= #{req.orderCreatedAt}
            <if test="req.productionGoodsIds !=null and req.productionGoodsIds.size() > 0">
                and tb.production_goods_id not in (
                <foreach collection="req.productionGoodsIds" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
           and tb.store_id = #{req.storeId}
            <if test="req.appointmentIds !=null and req.appointmentIds.size() > 0">
                and tb.id in (
                <foreach collection="req.appointmentIds" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
           and tb.serve_type = 0 ## 只获取自营项目
           and tb.item_type = 2
           and tb.order_production_type = 2
           and tb.verification_state = #{req.verificationState}
           and ta.real_paid is not null
           and ta.real_paid > 0
            <if test="req.therapistIds !=null and req.therapistIds.size() > 0">
                and tb.therapist_id in (
                <foreach collection="req.therapistIds" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
         group by tb.therapist_id
         union all
         ## 单项
         select tb.therapist_id, sum(ta.real_paid) as val
         from he_order_production_extend ta
                  join he_order_production_appointment tb on ta.id = tb.order_production_card_id
                  join he_order tc on tb.order_id = tc.order_id
         where ta.deleted = 0
           and tb.deleted = 0
           and tc.is_delete = 0
           and tc.created_at &gt;= #{req.orderCreatedAt}
            <if test="req.productionGoodsIds !=null and req.productionGoodsIds.size() > 0">
                and tb.production_goods_id not in (
                <foreach collection="req.productionGoodsIds" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
           and tb.store_id = #{req.storeId}
            <if test="req.appointmentIds !=null and req.appointmentIds.size() > 0">
                and tb.id in (
                <foreach collection="req.appointmentIds" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
           and tb.serve_type = 0 ## 只获取自营项目
           and tb.item_type = 2
           and tb.order_production_type = 1
           and tb.verification_state = #{req.verificationState}
           and ta.type = 2
           and ta.real_paid is not null
           and ta.real_paid > 0
            <if test="req.therapistIds !=null and req.therapistIds.size() > 0">
                and tb.therapist_id in (
                <foreach collection="req.therapistIds" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
         group by tb.therapist_id
         union all
         ## 卡项
         select tb.therapist_id, sum(ta.real_paid) as val
         from he_order_production_card_extend ta
                  join he_order_production_appointment tb on ta.id = tb.order_production_card_extend_id
                  join he_order tc on tb.order_id = tc.order_id
         where ta.deleted = 0
           and tb.deleted = 0
           and tc.is_delete = 0
           and tc.created_at &gt;= #{req.orderCreatedAt}
            <if test="req.productionGoodsIds !=null and req.productionGoodsIds.size() > 0">
                and tb.production_goods_id not in (
                <foreach collection="req.productionGoodsIds" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
           and tb.store_id = #{req.storeId}
            <if test="req.appointmentIds !=null and req.appointmentIds.size() > 0">
                and tb.id in (
                <foreach collection="req.appointmentIds" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
           and tb.serve_type = 0 ## 只获取自营项目
           and tb.item_type in (0, 1)
           and tb.order_production_type = 1
           and tb.verification_state = #{req.verificationState}
           and ta.type in (0, 1)
           and ta.real_paid is not null
           and ta.real_paid > 0
            <if test="req.therapistIds !=null and req.therapistIds.size() > 0">
                and tb.therapist_id in (
                <foreach collection="req.therapistIds" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
         group by tb.therapist_id
        ) a
        group by a.therapist_id
    </select>

    <select id="getClientRealPaid" resultType="com.stbella.order.server.order.ProductionClientRealPaidModel">
        select a.client_id, sum(a.val) as val
        from (
            ## 礼赠
            select tb.client_id, sum(ta.real_paid) as val
            from he_order_gift_extend ta
            join he_order_production_appointment tb on ta.id = tb.order_production_card_id
            join he_order tc on tb.order_id = tc.order_id
            where ta.deleted = 0
            and tb.deleted = 0
            and tc.is_delete = 0
            and tc.created_at &gt;= #{req.orderCreatedAt}
            <if test="req.productionGoodsIds !=null and req.productionGoodsIds.size() > 0">
                and tb.production_goods_id not in (
                <foreach collection="req.productionGoodsIds" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
            and tb.store_id = #{req.storeId}
            <if test="req.appointmentIds !=null and req.appointmentIds.size() > 0">
                and tb.id in (
                <foreach collection="req.appointmentIds" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
            and tb.serve_type = 0
            and tb.item_type = 2
            and tb.order_production_type = 2
            and tb.verification_state = #{req.verificationState}
            and ta.real_paid is not null
            and ta.real_paid > 0
            <if test="req.clientIds !=null and req.clientIds.size() > 0">
                and tb.client_id in (
                <foreach collection="req.clientIds" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
            group by tb.client_id
            union all
            ## 单项
            select tb.client_id, sum(ta.real_paid) as val
            from he_order_production_extend ta
            join he_order_production_appointment tb on ta.id = tb.order_production_card_id
            join he_order tc on tb.order_id = tc.order_id
            where ta.deleted = 0
            and tb.deleted = 0
            and tc.is_delete = 0
            and tc.created_at &gt;= #{req.orderCreatedAt}
            <if test="req.productionGoodsIds !=null and req.productionGoodsIds.size() > 0">
                and tb.production_goods_id not in (
                <foreach collection="req.productionGoodsIds" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
            and tb.store_id = #{req.storeId}
            <if test="req.appointmentIds !=null and req.appointmentIds.size() > 0">
                and tb.id in (
                <foreach collection="req.appointmentIds" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
            and tb.serve_type = 0
            and tb.item_type = 2
            and tb.order_production_type = 1
            and tb.verification_state = #{req.verificationState}
            and ta.type = 2
            and ta.real_paid is not null
            and ta.real_paid > 0
            <if test="req.clientIds !=null and req.clientIds.size() > 0">
                and tb.client_id in (
                <foreach collection="req.clientIds" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
            group by tb.client_id
            union all
            ## 卡项
            select tb.client_id, sum(ta.real_paid) as val
            from he_order_production_card_extend ta
            join he_order_production_appointment tb on ta.id = tb.order_production_card_extend_id
            join he_order tc on tb.order_id = tc.order_id
            where ta.deleted = 0
            and tb.deleted = 0
            and tc.is_delete = 0
            and tc.created_at &gt;= #{req.orderCreatedAt}
            <if test="req.productionGoodsIds !=null and req.productionGoodsIds.size() > 0">
                and tb.production_goods_id not in (
                <foreach collection="req.productionGoodsIds" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
            and tb.store_id = #{req.storeId}
            <if test="req.appointmentIds !=null and req.appointmentIds.size() > 0">
                and tb.id in (
                <foreach collection="req.appointmentIds" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
            and tb.serve_type = 0
            and tb.item_type in (0, 1)
            and tb.order_production_type = 1
            and tb.verification_state = #{req.verificationState}
            and ta.type in (0, 1)
            and ta.real_paid is not null
            and ta.real_paid > 0
            <if test="req.clientIds !=null and req.clientIds.size() > 0">
                and tb.client_id in (
                <foreach collection="req.clientIds" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
            group by tb.client_id
        ) a
        group by a.client_id
    </select>

    <select id="getClientUnwrittenSummary" resultType="com.stbella.order.server.order.ProductionClientRealPaidModel">
        select a.basic_id as client_id, sum(a.val) as val from (
            select
                ta.basic_id,
                sum(ta.real_paid) as val
            from he_order_gift_extend ta
                join he_order tb on ta.order_id = tb.order_id
            where ta.deleted = 0
                and tb.is_delete = 0
                <if test="req.storeId != null">
                    and ta.store_id = #{req.storeId}
                </if>
                <if test="req.productionGoodsIds !=null and req.productionGoodsIds.size() > 0">
                    and ta.goods_id not in (
                    <foreach collection="req.productionGoodsIds" separator="," item="item">
                        #{item}
                    </foreach>
                    )
                </if>
                and ta.status = 1
                and tb.created_at &gt;= #{req.orderCreatedAt}
                <if test="req.basicIds !=null and req.basicIds.size() > 0">
                    and ta.basic_id in (
                    <foreach collection="req.basicIds" separator="," item="item">
                        #{item}
                    </foreach>
                    )
                </if>
                and ta.verification_status in (0, 2)
                and ta.real_paid is not null
                and ta.real_paid > 0
                and ta.type = 6
            group by ta.basic_id
            union all
            select
                ta.basic_id,
                sum(ta.real_paid) as val
            from he_order_production_extend ta
                join he_order tb on ta.order_id = tb.order_id
            where ta.deleted = 0
                and tb.is_delete = 0
                and ta.status = 1
                <if test="req.storeId != null">
                    and ta.store_id = #{req.storeId}
                </if>
                <if test="req.productionGoodsIds !=null and req.productionGoodsIds.size() > 0">
                    and ta.goods_id not in (
                    <foreach collection="req.productionGoodsIds" separator="," item="item">
                        #{item}
                    </foreach>
                    )
                </if>
                and tb.created_at &gt;= #{req.orderCreatedAt}
                <if test="req.basicIds !=null and req.basicIds.size() > 0">
                    and ta.basic_id in (
                    <foreach collection="req.basicIds" separator="," item="item">
                        #{item}
                    </foreach>
                    )
                </if>
                and ta.real_paid is not null
                and ta.verification_status in (0, 2)
                and ta.type = 2
                and ta.real_paid > 0
            group by ta.basic_id
            union all
            select
                ta.basic_id,
                sum((tb.num - if(tb.used_num is null, 0, tb.used_num)) * tb.real_paid) as val
            from he_order_production_extend ta
                join he_order_production_card_extend tb on ta.id = tb.order_production_id
                join he_order tc on ta.order_id = tc.order_id
            where ta.deleted = 0
                and tb.deleted = 0
                and tc.is_delete = 0
                and tc.created_at &gt;= #{req.orderCreatedAt}
                and ta.type in (0, 1)
                and tb.type in (0, 1)
                and tb.status = 1
                and tb.real_paid is not null
                and tb.real_paid > 0
                and tb.num is not null
                and tb.num > 0
                <if test="req.storeId != null">
                    and ta.store_id = #{req.storeId}
                </if>
                <if test="req.basicIds !=null and req.basicIds.size() > 0">
                    and ta.basic_id in (
                    <foreach collection="req.basicIds" separator="," item="item">
                        #{item}
                    </foreach>
                    )
                </if>
            group by ta.basic_id
        ) a group by a.basic_id
    </select>

    <select id="getTherapistRealPaidThird" resultType="com.stbella.order.domain.order.production.OrderProductionAppointmentEntity">
        select a.*, b.staff_id as create_by
        from he_order_production_appointment a
                 join he_order b on a.order_id = b.order_id
                 join he_order_production_appointment_opt_log c on a.id = c.appointment_id and c.opt_type = 3
        where a.deleted = 0
          and b.is_delete = 0
          and c.deleted = 0
          and a.verification_state = 1
          and a.serve_type = 1
        <if test="req.storeId !=null">
            and b.store_id = #{req.storeId}
        </if>
        <if test="req.serveStartDate !=null">
            and a.serve_start &gt;= #{req.serveStartDate}
        </if>
        <if test="req.serveEndDate !=null">
            and a.serve_end &lt;= #{req.serveEndDate}
        </if>
        <if test="req.orderCreatedAt !=null">
            and b.created_at &gt;= #{req.orderCreatedAt}
        </if>
        <if test="req.writeOffStartDate !=null">
            and c.gmt_create &gt;= #{req.writeOffStartDate}
        </if>
        <if test="req.writeOffEndDate !=null">
            and c.gmt_create &lt;= #{req.writeOffEndDate}
        </if>
        <if test="req.userIds !=null and req.userIds.size() > 0">
            and b.staff_id in (
            <foreach collection="req.userIds" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>
        <if test="req.productionGoodsIds !=null and req.productionGoodsIds.size() > 0">
            and a.production_goods_id not in (
            <foreach collection="req.productionGoodsIds" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>
    </select>

    <select id="getClientRealPaidThird" resultType="com.stbella.order.domain.order.production.OrderProductionAppointmentEntity">
        select a.*
        from he_order_production_appointment a
            join he_order b on a.order_id = b.order_id
            join he_order_production_appointment_opt_log c on a.id = c.appointment_id and c.opt_type = 3
        where a.deleted = 0
        and b.is_delete = 0
        and c.deleted = 0
        and a.verification_state = 1
        and a.serve_type = 1
        and b.store_id = #{req.storeId}
        and b.created_at &gt;= #{req.orderCreatedAt}
        <if test="req.clientIds !=null and req.clientIds.size() > 0">
            and a.client_id in (
            <foreach collection="req.clientIds" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>
        <if test="req.writeOffStartDate !=null">
            and c.gmt_create &gt;= #{req.writeOffStartDate}
        </if>
        <if test="req.writeOffEndDate !=null">
            and c.gmt_create &lt;= #{req.writeOffEndDate}
        </if>
        <if test="req.productionGoodsIds !=null and req.productionGoodsIds.size() > 0">
            and a.production_goods_id not in (
            <foreach collection="req.productionGoodsIds" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>
    </select>
</mapper>
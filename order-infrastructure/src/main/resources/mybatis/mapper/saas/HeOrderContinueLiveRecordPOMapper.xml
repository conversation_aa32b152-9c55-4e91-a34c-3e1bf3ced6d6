<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeOrderContinueLiveRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeOrderContinueLiveRecordPO">
        <result column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="additional_revenue_id" property="additionalRevenueId"/>
        <result column="deleted" property="deleted"/>
        <result column="order_id" property="orderId"/>
        <result column="room_id" property="roomId"/>
        <result column="room_name" property="roomName"/>
        <result column="change_original_price" property="changeOriginalPrice"/>
        <result column="change_price" property="changePrice"/>
        <result column="final_original_price" property="finalOriginalPrice"/>
        <result column="final_price" property="finalPrice"/>
        <result column="days" property="days"/>
        <result column="final_days" property="finalDays"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        gmt_create,
        gmt_modified,
        additional_revenue_id,
        deleted,
        order_id, room_id, room_name, change_original_price, change_price, final_original_price, final_price, days, final_days
    </sql>

</mapper>

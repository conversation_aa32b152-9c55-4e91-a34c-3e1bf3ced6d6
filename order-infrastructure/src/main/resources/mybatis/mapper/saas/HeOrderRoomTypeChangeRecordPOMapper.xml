<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeOrderRoomTypeChangeRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeOrderRoomTypeChangeRecordPO">
        <result column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="deleted" property="deleted"/>
        <result column="order_id" property="orderId"/>
        <result column="additional_revenue_id" property="additionalRevenueId"/>
        <result column="original_room_id" property="originalRoomId"/>
        <result column="original_room_name" property="originalRoomName"/>
        <result column="new_room_id" property="newRoomId"/>
        <result column="new_room_name" property="newRoomName"/>
        <result column="days" property="days"/>
        <result column="change_original_price" property="changeOriginalPrice"/>
        <result column="change_price" property="changePrice"/>
        <result column="room_type_change_cycle" property="roomTypeChangeCycle"/>
        <result column="final_room_type_change_cycle" property="finalRoomTypeChangeCycle"/>
        <result column="opt_id" property="optId"/>
        <result column="opt_name" property="optName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        gmt_create,
        gmt_modified,
        deleted,
        order_id, additional_revenue_id, original_room_id, original_room_name, new_room_id, new_room_name, days, change_original_price, change_price, room_type_change_cycle, final_room_type_change_cycle, opt_id, opt_name
    </sql>

</mapper>

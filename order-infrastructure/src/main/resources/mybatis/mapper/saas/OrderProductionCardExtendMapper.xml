<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.OrderProductionCardExtendMapper">
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.OrderProductionCardExtend">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
        <result column="order_production_id" jdbcType="INTEGER" property="orderProductionId"/>
        <result column="order_id" jdbcType="INTEGER" property="orderId"/>
        <result column="basic_id" jdbcType="INTEGER" property="basicId"/>
        <result column="type" jdbcType="BIT" property="type"/>
        <result column="group_id" jdbcType="INTEGER" property="groupId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="num" jdbcType="INTEGER" property="num"/>
        <result column="used_num" jdbcType="INTEGER" property="usedNum"/>
        <result column="appointment_num" jdbcType="INTEGER" property="appointmentNum"/>
        <result column="valid_start_time" jdbcType="INTEGER" property="validStartTime"/>
        <result column="valid_end_time" jdbcType="INTEGER" property="validEndTime"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="reset" jdbcType="TINYINT" property="reset"/>
        <result column="real_paid" jdbcType="DECIMAL" property="realPaid"/>
        <result column="original_id" property="originalId"/>

    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs"
               type="com.stbella.order.infrastructure.repository.po.OrderProductionCardExtend">
        <result column="content" jdbcType="LONGVARCHAR" property="content"/>

    </resultMap>
    <sql id="Base_Column_List">
        *
    </sql>
    <sql id="Blob_Column_List">
        content
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from he_order_production_card_extend
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from he_order_production_card_extend
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="save" parameterType="com.stbella.order.infrastructure.repository.po.OrderProductionCardExtend">
        insert into he_order_production_card_extend (id, gmt_create, gmt_modified,
                                                     deleted, order_production_id, order_id,
                                                     basic_id, type, group_id,
                                                     name, num, used_num, appointment_num,
                                                     valid_start_time, valid_end_time, status,
                                                     content)
        values (#{id,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP},
                #{deleted,jdbcType=BIT}, #{orderProductionId,jdbcType=INTEGER}, #{orderId,jdbcType=INTEGER},
                #{basicId,jdbcType=INTEGER}, #{type,jdbcType=BIT}, #{groupId,jdbcType=INTEGER},
                #{name,jdbcType=VARCHAR}, #{num,jdbcType=INTEGER}, #{usedNum,jdbcType=INTEGER},
                #{validStartTime,jdbcType=INTEGER}, #{validEndTime,jdbcType=INTEGER}, #{status,jdbcType=TINYINT},
                #{content,jdbcType=LONGVARCHAR})
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.stbella.order.infrastructure.repository.po.OrderProductionCardExtend">
        update he_order_production_card_extend
        <set>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
            <if test="orderProductionId != null">
                order_production_id = #{orderProductionId,jdbcType=INTEGER},
            </if>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=INTEGER},
            </if>
            <if test="basicId != null">
                basic_id = #{basicId,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=BIT},
            </if>
            <if test="groupId != null">
                group_id = #{groupId,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="num != null">
                num = #{num,jdbcType=INTEGER},
            </if>
            <if test="usedNum != null">
                used_num = #{usedNum,jdbcType=INTEGER},
            </if>
            <if test="appointmentNum != null">
                appointment_num = #{appointmentNum,jdbcType=INTEGER},
            </if>
            <if test="validStartTime != null">
                valid_start_time = #{validStartTime,jdbcType=INTEGER},
            </if>
            <if test="validEndTime != null">
                valid_end_time = #{validEndTime,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=LONGVARCHAR},
            </if>
            <if test="reset != null">
                reset =  #{reset,jdbcType=INTEGER},
            </if>
            <if test="realPaid != null">
                real_paid = #{realPaid},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs"
            parameterType="com.stbella.order.infrastructure.repository.po.OrderProductionCardExtend">
        update he_order_production_card_extend
        set gmt_create          = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_modified        = #{gmtModified,jdbcType=TIMESTAMP},
            deleted             = #{deleted,jdbcType=BIT},
            order_production_id = #{orderProductionId,jdbcType=INTEGER},
            order_id            = #{orderId,jdbcType=INTEGER},
            basic_id            = #{basicId,jdbcType=INTEGER},
            type                = #{type,jdbcType=BIT},
            group_id            = #{groupId,jdbcType=INTEGER},
            name                = #{name,jdbcType=VARCHAR},
            num                 = #{num,jdbcType=INTEGER},
            used_num            = #{usedNum,jdbcType=INTEGER},
            appointment_num     = #{appointmentNum,jdbcType=INTEGER},
            valid_start_time    = #{validStartTime,jdbcType=INTEGER},
            valid_end_time      = #{validEndTime,jdbcType=INTEGER},
            status              = #{status,jdbcType=TINYINT},
            content             = #{content,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.stbella.order.infrastructure.repository.po.OrderProductionCardExtend">
        update he_order_production_card_extend
        set gmt_create          = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_modified        = #{gmtModified,jdbcType=TIMESTAMP},
            deleted             = #{deleted,jdbcType=BIT},
            order_production_id = #{orderProductionId,jdbcType=INTEGER},
            order_id            = #{orderId,jdbcType=INTEGER},
            basic_id            = #{basicId,jdbcType=INTEGER},
            type                = #{type,jdbcType=BIT},
            group_id            = #{groupId,jdbcType=INTEGER},
            name                = #{name,jdbcType=VARCHAR},
            num                 = #{num,jdbcType=INTEGER},
            used_num            = #{usedNum,jdbcType=INTEGER},
            appointment_num     = #{appointmentNum,jdbcType=INTEGER},
            valid_start_time    = #{validStartTime,jdbcType=INTEGER},
            valid_end_time      = #{validEndTime,jdbcType=INTEGER},
            status              = #{status,jdbcType=TINYINT}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="expiredByProductionIds">
        update he_order_production_card_extend
        set status=2 ,gmt_modified=now()
        where
        deleted= 0
        <if test="cardProductionIds !=null and cardProductionIds.size() > 0">
            and order_production_id in(
            <foreach collection="cardProductionIds" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>
    </update>

    <select id="queryProductionOrderCardList" resultMap="BaseResultMap">

        select
        o.order_id

        ,o.percent_first_time,

        p.valid_start_time,p.valid_end_time, p.status, p.num, p.used_num, p.type, p.group_id, p.name, p.content

        ,p.id,
        p.order_production_id,p.real_paid

        from
        he_order_production_card_extend p left join he_order o on o.order_id = p.order_id
        where

       1=1

        <if test="req.orderSn != null">
            and  o.order_sn = #{req.orderSn}
        </if>

        <if test="req.orderSn == null">
            and o.percent_first_time >=  #{req.startTime}
            and percent_first_time &lt;= #{req.endTime}
            AND (`paid_amount` > 100 or `paid_amount` &lt; -100)
        </if>


        and o.is_Delete = 0
        and p.deleted = 0
        and o.order_type = #{req.orderType}

    </select>

    <select id="queryProductionOrderCardByOrderSn" resultMap="BaseResultMap">

        select
            o.order_id

                ,o.percent_first_time,

            p.valid_start_time,p.valid_end_time, p.status, p.num, p.used_num, p.type, p.group_id, p.name, p.content

                ,p.id,
            p.order_production_id,p.reset

        from
            he_order_production_card_extend p left join he_order o on o.order_id = p.order_id
        where
            o.order_sn = #{orderSn}
          and o.is_Delete = 0
          and p.deleted = 0


    </select>




</mapper>
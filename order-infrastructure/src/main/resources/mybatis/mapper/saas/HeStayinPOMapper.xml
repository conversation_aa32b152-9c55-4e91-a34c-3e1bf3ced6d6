<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeStayinMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeStayinPO">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="stayin_time" property="stayinTime"/>
        <result column="production_mode" property="productionMode"/>
        <result column="feed_mode" property="feedMode"/>
        <result column="production_date" property="productionDate"/>
        <result column="production_pregnancy" property="productionPregnancy"/>
        <result column="gestation_week" property="gestationWeek"/>
        <result column="gestation_day" property="gestationDay"/>
        <result column="production_hospital" property="productionHospital"/>
        <result column="baby_gender" property="babyGender"/>
        <result column="born_weight" property="bornWeight"/>
        <result column="jaundice_value" property="jaundiceValue"/>
        <result column="births" property="births"/>
        <result column="birth_type" property="birthType"/>
        <result column="national" property="national"/>
        <result column="is_diabetes" property="isDiabetes"/>
        <result column="is_hypertension" property="isHypertension"/>
        <result column="allergy" property="allergy"/>
        <result column="infectious_diseases" property="infectiousDiseases"/>
        <result column="dietary_taboos" property="dietaryTaboos"/>
        <result column="height" property="height"/>
        <result column="weight" property="weight"/>
        <result column="is_car" property="isCar"/>
        <result column="hospital_address" property="hospitalAddress"/>
        <result column="is_allergy_flower" property="isAllergyFlower"/>
        <result column="salutation" property="salutation"/>
        <result column="baby_list" property="babyList"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        order_id, stayin_time, production_mode, feed_mode, production_date, production_pregnancy, gestation_week, gestation_day, production_hospital, baby_gender, born_weight, jaundice_value, births, birth_type, national, is_diabetes, is_hypertension, allergy, infectious_diseases, dietary_taboos, height, weight, is_car, hospital_address, is_allergy_flower, salutation, baby_list, created_at, updated_at
    </sql>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.OrderRelationMapper">
  <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.OrderRelationPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="parent_order_no" jdbcType="VARCHAR" property="parentOrderNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="ext" jdbcType="VARCHAR" property="ext" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, parent_order_no, order_no, ext, deleted, add_time, update_time
  </sql>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeCustomerBehaviorRecordMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeCustomerBehaviorRecordPO">
    <result column="id" property="id"/>
    <result column="title" property="title"/>
    <result column="content" property="content"/>
    <result column="imgs" property="imgs"/>
    <result column="basic_uid" property="basicUid"/>
    <result column="client_uid" property="clientUid"/>
    <result column="client_type" property="clientType"/>
    <result column="opt_basic_uid" property="optBasicUid"/>
    <result column="opt_client_uid" property="optClientUid"/>
    <result column="opt_client_type" property="optClientType"/>
    <result column="add_time" property="addTime"/>
    <result column="stayin_time" property="stayinTime"/>
    <result column="type" property="type"/>
    <result column="extend" property="extend"/>
    <result column="created_at" property="createdAt"/>
    <result column="updated_at" property="updatedAt"/>
    <result column="is_delete" property="isDelete"/>
  </resultMap>

  <!-- 通用查询结果列 -->
  <sql id="Base_Column_List">
    id
    ,
        title, content, imgs, basic_uid, client_uid, client_type, opt_basic_uid, opt_client_uid, opt_client_type, add_time, stayin_time, type, extend, created_at, updated_at, is_delete
  </sql>

</mapper>

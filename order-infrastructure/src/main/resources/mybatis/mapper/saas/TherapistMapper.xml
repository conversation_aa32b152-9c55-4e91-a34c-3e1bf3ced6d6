<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.TherapistMapper">
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.Therapist">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="deleted" jdbcType="INTEGER" property="deleted"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="grade" jdbcType="TINYINT" property="grade"/>
        <result column="nature_type" jdbcType="TINYINT" property="natureType"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
        <result column="serve_start" jdbcType="VARCHAR" property="serveStart"/>
        <result column="serve_end" jdbcType="VARCHAR" property="serveEnd"/>
        <result column="appointment" jdbcType="TINYINT" property="appointment"/>
        <result column="created_id" jdbcType="BIGINT" property="createdId"/>
        <result column="created_name" jdbcType="VARCHAR" property="createdName"/>
        <result column="update_id" jdbcType="BIGINT" property="updateId"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , deleted, gmt_create, gmt_modified, mobile, name, grade, nature_type, state, serve_start,
    serve_end, appointment, created_id, created_name, update_id, update_name
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from he_therapist
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from he_therapist
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.stbella.order.infrastructure.repository.po.Therapist">
        insert into he_therapist (id, deleted, gmt_create,
                                  gmt_modified, mobile, name,
                                  grade, nature_type, state,
                                  serve_start, serve_end, appointment,
                                  created_id, created_name, update_id,
                                  update_name)
        values (#{id,jdbcType=BIGINT}, #{deleted,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP},
                #{gmtModified,jdbcType=TIMESTAMP}, #{mobile,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
                #{grade,jdbcType=TINYINT}, #{natureType,jdbcType=TINYINT}, #{state,jdbcType=TINYINT},
                #{serveStart,jdbcType=VARCHAR}, #{serveEnd,jdbcType=VARCHAR}, #{appointment,jdbcType=TINYINT},
                #{createdId,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updateId,jdbcType=BIGINT},
                #{updateName,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.stbella.order.infrastructure.repository.po.Therapist">
        insert into he_therapist
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="grade != null">
                grade,
            </if>
            <if test="natureType != null">
                nature_type,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="serveStart != null">
                serve_start,
            </if>
            <if test="serveEnd != null">
                serve_end,
            </if>
            <if test="appointment != null">
                appointment,
            </if>
            <if test="createdId != null">
                created_id,
            </if>
            <if test="createdName != null">
                created_name,
            </if>
            <if test="updateId != null">
                update_id,
            </if>
            <if test="updateName != null">
                update_name,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="mobile != null">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="grade != null">
                #{grade,jdbcType=TINYINT},
            </if>
            <if test="natureType != null">
                #{natureType,jdbcType=TINYINT},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="serveStart != null">
                #{serveStart,jdbcType=VARCHAR},
            </if>
            <if test="serveEnd != null">
                #{serveEnd,jdbcType=VARCHAR},
            </if>
            <if test="appointment != null">
                #{appointment,jdbcType=TINYINT},
            </if>
            <if test="createdId != null">
                #{createdId,jdbcType=BIGINT},
            </if>
            <if test="createdName != null">
                #{createdName,jdbcType=VARCHAR},
            </if>
            <if test="updateId != null">
                #{updateId,jdbcType=BIGINT},
            </if>
            <if test="updateName != null">
                #{updateName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.stbella.order.infrastructure.repository.po.Therapist">
        update he_therapist
        <set>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="grade != null">
                grade = #{grade,jdbcType=TINYINT},
            </if>
            <if test="natureType != null">
                nature_type = #{natureType,jdbcType=TINYINT},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=TINYINT},
            </if>
            <if test="serveStart != null">
                serve_start = #{serveStart,jdbcType=VARCHAR},
            </if>
            <if test="serveEnd != null">
                serve_end = #{serveEnd,jdbcType=VARCHAR},
            </if>
            <if test="appointment != null">
                appointment = #{appointment,jdbcType=TINYINT},
            </if>
            <if test="createdId != null">
                created_id = #{createdId,jdbcType=BIGINT},
            </if>
            <if test="createdName != null">
                created_name = #{createdName,jdbcType=VARCHAR},
            </if>
            <if test="updateId != null">
                update_id = #{updateId,jdbcType=BIGINT},
            </if>
            <if test="updateName != null">
                update_name = #{updateName,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.stbella.order.infrastructure.repository.po.Therapist">
        update he_therapist
        set deleted      = #{deleted,jdbcType=INTEGER},
            gmt_create   = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            mobile       = #{mobile,jdbcType=VARCHAR},
            name         = #{name,jdbcType=VARCHAR},
            grade        = #{grade,jdbcType=TINYINT},
            nature_type  = #{natureType,jdbcType=TINYINT},
            state        = #{state,jdbcType=TINYINT},
            serve_start  = #{serveStart,jdbcType=VARCHAR},
            serve_end    = #{serveEnd,jdbcType=VARCHAR},
            appointment  = #{appointment,jdbcType=TINYINT},
            created_id   = #{createdId,jdbcType=BIGINT},
            created_name = #{createdName,jdbcType=VARCHAR},
            update_id    = #{updateId,jdbcType=BIGINT},
            update_name  = #{updateName,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.GoodsSkuMapper">
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.GoodsSku">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="goods_id" jdbcType="INTEGER" property="goodsId"/>
        <result column="sku_name" jdbcType="VARCHAR" property="skuName"/>
        <result column="origin_price" jdbcType="INTEGER" property="originPrice"/>
        <result column="goods_price" jdbcType="INTEGER" property="goodsPrice"/>
        <result column="stock" jdbcType="INTEGER" property="stock"/>
        <result column="is_delete" jdbcType="BIT" property="isDelete"/>
        <result column="created_at" jdbcType="BIGINT" property="createdAt"/>
        <result column="updated_at" jdbcType="BIGINT" property="updatedAt"/>
        <result column="cost_price" jdbcType="INTEGER" property="costPrice"/>
        <result column="spec_code" jdbcType="VARCHAR" property="specCode"/>
        <result column="integral" jdbcType="INTEGER" property="integral"/>
        <result column="options_verification" jdbcType="VARCHAR" property="optionsVerification"/>
        <result column="template_sku_id" jdbcType="INTEGER" property="templateSkuId"/>
        <result column="template_sku_prop" jdbcType="VARCHAR" property="templateSkuProp"/>
        <result column="sku_sale_state" jdbcType="INTEGER" property="skuSaleState"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="service_time" jdbcType="TINYINT" property="serviceTime"/>
        <result column="producer_config" jdbcType="VARCHAR" property="producerConfig"/>
        <result column="service_type" jdbcType="INTEGER" property="serviceType"/>
        <result column="sku_num" jdbcType="INTEGER" property="skuNum"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , goods_id, sku_name, origin_price, goods_price, stock, is_delete, created_at,
    updated_at, cost_price, spec_code, integral, options_verification, template_sku_id, template_sku_prop,
    sku_sale_state, sort, service_time, producer_config,service_type,sku_num
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select sku.*, g.service_type
        from he_goods_sku sku left JOIN he_goods g on g.id = sku.goods_id
        where sku.id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from he_goods_sku
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.stbella.order.infrastructure.repository.po.GoodsSku">
        insert into he_goods_sku (id, goods_id, sku_name,
                                  origin_price, goods_price, stock,
                                  is_delete, created_at, updated_at,
                                  cost_price, spec_code, integral,
                                  options_verification, template_sku_id, sku_sale_state,
                                  sort, service_time, producer_config)
        values (#{id,jdbcType=INTEGER}, #{goodsId,jdbcType=INTEGER}, #{skuName,jdbcType=VARCHAR},
                #{originPrice,jdbcType=INTEGER}, #{goodsPrice,jdbcType=INTEGER}, #{stock,jdbcType=INTEGER},
                #{isDelete,jdbcType=BIT}, #{createdAt,jdbcType=BIGINT}, #{updatedAt,jdbcType=BIGINT},
                #{costPrice,jdbcType=INTEGER}, #{specCode,jdbcType=VARCHAR}, #{integral,jdbcType=INTEGER},
                #{optionsVerification,jdbcType=VARCHAR}, #{templateSkuId,jdbcType=INTEGER},
                #{skuSaleState,jdbcType=INTEGER},
                #{sort,jdbcType=INTEGER}, #{serviceTime,jdbcType=TINYINT}, #{producerConfig,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.stbella.order.infrastructure.repository.po.GoodsSku">
        insert into he_goods_sku
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="goodsId != null">
                goods_id,
            </if>
            <if test="skuName != null">
                sku_name,
            </if>
            <if test="originPrice != null">
                origin_price,
            </if>
            <if test="goodsPrice != null">
                goods_price,
            </if>
            <if test="stock != null">
                stock,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="createdAt != null">
                created_at,
            </if>
            <if test="updatedAt != null">
                updated_at,
            </if>
            <if test="costPrice != null">
                cost_price,
            </if>
            <if test="specCode != null">
                spec_code,
            </if>
            <if test="integral != null">
                integral,
            </if>
            <if test="optionsVerification != null">
                options_verification,
            </if>
            <if test="templateSkuId != null">
                template_sku_id,
            </if>
            <if test="skuSaleState != null">
                sku_sale_state,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="serviceTime != null">
                service_time,
            </if>
            <if test="producerConfig != null">
                producer_config,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="goodsId != null">
                #{goodsId,jdbcType=INTEGER},
            </if>
            <if test="skuName != null">
                #{skuName,jdbcType=VARCHAR},
            </if>
            <if test="originPrice != null">
                #{originPrice,jdbcType=INTEGER},
            </if>
            <if test="goodsPrice != null">
                #{goodsPrice,jdbcType=INTEGER},
            </if>
            <if test="stock != null">
                #{stock,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=BIT},
            </if>
            <if test="createdAt != null">
                #{createdAt,jdbcType=BIGINT},
            </if>
            <if test="updatedAt != null">
                #{updatedAt,jdbcType=BIGINT},
            </if>
            <if test="costPrice != null">
                #{costPrice,jdbcType=INTEGER},
            </if>
            <if test="specCode != null">
                #{specCode,jdbcType=VARCHAR},
            </if>
            <if test="integral != null">
                #{integral,jdbcType=INTEGER},
            </if>
            <if test="optionsVerification != null">
                #{optionsVerification,jdbcType=VARCHAR},
            </if>
            <if test="templateSkuId != null">
                #{templateSkuId,jdbcType=INTEGER},
            </if>
            <if test="skuSaleState != null">
                #{skuSaleState,jdbcType=INTEGER},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=INTEGER},
            </if>
            <if test="serviceTime != null">
                #{serviceTime,jdbcType=TINYINT},
            </if>
            <if test="producerConfig != null">
                #{producerConfig,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.stbella.order.infrastructure.repository.po.GoodsSku">
        update he_goods_sku
        <set>
            <if test="goodsId != null">
                goods_id = #{goodsId,jdbcType=INTEGER},
            </if>
            <if test="skuName != null">
                sku_name = #{skuName,jdbcType=VARCHAR},
            </if>
            <if test="originPrice != null">
                origin_price = #{originPrice,jdbcType=INTEGER},
            </if>
            <if test="goodsPrice != null">
                goods_price = #{goodsPrice,jdbcType=INTEGER},
            </if>
            <if test="stock != null">
                stock = #{stock,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=BIT},
            </if>
            <if test="createdAt != null">
                created_at = #{createdAt,jdbcType=BIGINT},
            </if>
            <if test="updatedAt != null">
                updated_at = #{updatedAt,jdbcType=BIGINT},
            </if>
            <if test="costPrice != null">
                cost_price = #{costPrice,jdbcType=INTEGER},
            </if>
            <if test="specCode != null">
                spec_code = #{specCode,jdbcType=VARCHAR},
            </if>
            <if test="integral != null">
                integral = #{integral,jdbcType=INTEGER},
            </if>
            <if test="optionsVerification != null">
                options_verification = #{optionsVerification,jdbcType=VARCHAR},
            </if>
            <if test="templateSkuId != null">
                template_sku_id = #{templateSkuId,jdbcType=INTEGER},
            </if>
            <if test="skuSaleState != null">
                sku_sale_state = #{skuSaleState,jdbcType=INTEGER},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="serviceTime != null">
                service_time = #{serviceTime,jdbcType=TINYINT},
            </if>
            <if test="producerConfig != null">
                producer_config = #{producerConfig,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.stbella.order.infrastructure.repository.po.GoodsSku">
        update he_goods_sku
        set goods_id             = #{goodsId,jdbcType=INTEGER},
            sku_name             = #{skuName,jdbcType=VARCHAR},
            origin_price         = #{originPrice,jdbcType=INTEGER},
            goods_price          = #{goodsPrice,jdbcType=INTEGER},
            stock                = #{stock,jdbcType=INTEGER},
            is_delete            = #{isDelete,jdbcType=BIT},
            created_at           = #{createdAt,jdbcType=BIGINT},
            updated_at           = #{updatedAt,jdbcType=BIGINT},
            cost_price           = #{costPrice,jdbcType=INTEGER},
            spec_code            = #{specCode,jdbcType=VARCHAR},
            integral             = #{integral,jdbcType=INTEGER},
            options_verification = #{optionsVerification,jdbcType=VARCHAR},
            template_sku_id      = #{templateSkuId,jdbcType=INTEGER},
            sku_sale_state       = #{skuSaleState,jdbcType=INTEGER},
            sort                 = #{sort,jdbcType=INTEGER},
            service_time         = #{serviceTime,jdbcType=TINYINT},
            producer_config      = #{producerConfig,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectForDeletedByIdList" resultMap="BaseResultMap">
        select id, goods_id, sku_name, origin_price, goods_price, template_sku_id, template_sku_prop from he_goods_sku
        where id in
        <foreach collection="skuId" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>

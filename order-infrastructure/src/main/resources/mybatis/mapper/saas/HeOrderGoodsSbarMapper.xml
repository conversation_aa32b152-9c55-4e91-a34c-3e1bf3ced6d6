<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeOrderGoodsSbarMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeOrderGoodsSbra">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="order_type" property="orderType"/>
        <result column="basic_uid" property="basicUid"/>
        <result column="client_uid" property="clientUid"/>
        <result column="client_name" property="clientName"/>
        <result column="client_phone" property="clientPhone"/>
        <result column="buy_type" property="buyType"/>
        <result column="store_id" property="storeId"/>
        <result column="store_name" property="storeName"/>
        <result column="goods_id" property="goodsId"/>
        <result column="goods_num" property="goodsNum"/>
        <result column="goods_name" property="goodsName"/>
        <result column="goods_price" property="goodsPrice"/>
        <result column="cost_price" property="costPrice"/>
        <result column="sku_id" property="skuId"/>
        <result column="sku_name" property="skuName"/>
        <result column="sku_num" property="skuNum"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by_name" property="createByName"/>
        <result column="update_by_name" property="updateByName"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , order_id, order_sn, order_type, basic_uid, client_uid, client_name, client_phone, buy_type, store_id, store_name, goods_id, goods_num, goods_name, goods_price, cost_price, sku_id, sku_name, sku_num, create_by, update_by, create_by_name, update_by_name, deleted, gmt_create, gmt_modified
    </sql>

</mapper>

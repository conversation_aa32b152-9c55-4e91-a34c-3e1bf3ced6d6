<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeUserProductionAmountListMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeUserProductionAmountListPO">
        <result column="id" property="id"/>
        <result column="basic_id" property="basicId"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="used_amount" property="usedAmount"/>
        <result column="source" property="source"/>
        <result column="client_type" property="clientType"/>
        <result column="operator_id" property="operatorId"/>
        <result column="operator" property="operator"/>
        <result column="order_id" property="orderId"/>
        <result column="status" property="status"/>
        <result column="is_show" property="isShow"/>
        <result column="valid_start_time" property="validStartTime"/>
        <result column="valid_end_time" property="validEndTime"/>
        <result column="active_time" property="activeTime"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        basic_id, total_amount, used_amount, source, client_type, operator_id, operator, order_id, status, is_show, valid_start_time, valid_end_time, active_time, is_delete, created_at, updated_at
    </sql>

    <select id="queryByOrderIdAndSource" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from he_user_production_amount_list where order_id = #{orderId} and `source` = #{source}
    </select>


    <update id="updateIgnoreDelete"
            parameterType="com.stbella.order.infrastructure.repository.po.HeUserProductionAmountListPO">
        update he_user_production_amount_list
        <set>
            <if test="po.totalAmount != null">
                total_amount = #{po.totalAmount},
            </if>
            <if test="po.usedAmount != null">
                used_amount = #{po.usedAmount},
            </if>
            <if test="po.isDelete != null">
                is_delete = #{po.isDelete},
            </if>
            <if test="po.updatedAt != null">
                updated_at = #{po.updatedAt},
            </if>
        </set>
        where id = #{id}
    </update>


    <update id="updateByOrderAndSource">
        update he_user_production_amount_list
        set total_amount = #{productionAmount},
            is_delete    = 0,
            updated_at   = #{timestamp}
        where id = #{id}
    </update>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeOrderPerformanceOperationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeOrderPerformanceOperation">
        <id column="id" property="id"/>
        <result column="source _store_id" property="source StoreId"/>
        <result column="order_id" property="orderId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="order_percent_first_time" property="orderPercentFirstTime"/>
        <result column="order_total_amount" property="orderTotalAmount"/>
        <result column="source_store_amount" property="sourceStoreAmount"/>
        <result column="target_store_amount" property="targetStoreAmount"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="create_id" property="createId"/>
        <result column="create_name" property="createName"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="modify_id" property="modifyId"/>
        <result column="modify_name" property="modifyName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , source _store_id, order_id, order_sn, order_percent_first_time, order_total_amount, source_store_amount, target_store_amount, deleted, gmt_create, create_id, create_name, gmt_modified, modify_id, modify_name
    </sql>

</mapper>

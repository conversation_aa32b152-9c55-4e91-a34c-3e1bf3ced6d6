<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.sso.infrastructure.repository.mapper.TherapistStoreMapper">
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.TherapistStore">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="deleted" jdbcType="INTEGER" property="deleted"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="therapist_id" jdbcType="BIGINT" property="therapistId"/>
        <result column="store_id" jdbcType="INTEGER" property="storeId"/>
        <result column="bind_type" jdbcType="INTEGER" property="bindType"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, deleted, gmt_create, gmt_modified, therapist_id, store_id, bindType
    </sql>
</mapper>
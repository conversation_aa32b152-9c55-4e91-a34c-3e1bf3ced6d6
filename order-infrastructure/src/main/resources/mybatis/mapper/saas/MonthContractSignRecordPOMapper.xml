<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.MonthContractSignRecordMapper">
    <resultMap id="Base_Result_Map" type="com.stbella.order.infrastructure.repository.po.MonthContractSignRecordPO">
        <id column="id" property="id" />
        <result column="client_uid" property="clientUid" />
        <result column="staff_id" property="staffId" />
        <result column="store_id" property="storeId" />
        <result column="guide_type" property="guideType" />
        <result column="guide_id" property="guideId" />
        <result column="contract_image_signature" property="contractImageSignature" />
        <result column="contract_image_html" property="contractImageHtml" />
        <result column="contract_temp_url" property="contractTempUrl" />
        <result column="contract_no" property="contractNo" />
        <result column="download_url" property="downloadUrl" />
        <result column="contract_name" property="contractName" />
        <result column="viewpdf_url" property="viewpdfUrl" />
        <result column="contract_status" property="contractStatus" />
        <result column="contract_type" property="contractType" />
        <result column="template_type" property="templateType" />
        <result column="template_contract_type" property="templateContractType" />
        <result column="template_id" property="templateId" />
        <result column="is_delete" property="isDelete" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="task_id" property="taskId" />
        <result column="contract_long_url" property="contractLongUrl"/>
        <result column="contract_short_url" property="contractShortUrl"/>
    </resultMap>
</mapper>
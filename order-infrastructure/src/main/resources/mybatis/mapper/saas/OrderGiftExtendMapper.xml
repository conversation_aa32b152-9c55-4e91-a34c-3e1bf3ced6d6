<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.OrderGiftExtendMapper">
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.OrderGiftExtend">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="deleted" property="deleted"/>
        <result column="category_id" property="categoryId"/>
        <result column="category_name" property="categoryName"/>
        <result column="goods_name" property="goodsName"/>
        <result column="sku_name" property="skuName"/>
        <result column="store_id" property="storeId"/>
        <result column="order_id" property="orderId"/>
        <result column="basic_id" property="basicId"/>
        <result column="goods_id" property="goodsId"/>
        <result column="sku_id" property="skuId"/>
        <result column="type" property="type"/>
        <result column="price" property="price"/>
        <result column="cost" property="cost"/>
        <result column="content" property="content"/>
        <result column="goods_num" property="goodsNum"/>
        <result column="sku_num" property="skuNum"/>
        <result column="status" property="status"/>
        <result column="valid_start_time" property="validStartTime"/>
        <result column="valid_end_time" property="validEndTime"/>
        <result column="verification_status" property="verificationStatus"/>
        <result column="options_verification" property="optionsVerification"/>
        <result column="source" property="source"/>
        <result column="validity_value" property="validityValue"/>
        <result column="validity_type" property="validityType"/>

        <result column="service_time" property="serviceTime"/>
        <result column="serve_type" property="serveType"/>
        <result column="service_tag" property="serviceTag"/>
        <result column="batch_no" property="batchNo"/>
        <result column="real_paid" property="realPaid"/>
        <result column="data_source" property="dataSource"/>
        <result column="original_id" property="originalId"/>
        <result column="order_goods_sn" property="orderGoodsSn"/>

    </resultMap>
    <sql id="Base_Column_List">
        *
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from he_order_gift_extend
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from he_order_gift_extend
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <update id="deleteByOrderId">
        update he_order_gift_extend
        set deleted =1
        where deleted = 0
          and order_id = #{orderId}
    </update>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.stbella.order.infrastructure.repository.po.OrderGiftExtend">
        update he_order_gift_extend
        <set>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
            <if test="categoryId != null">
                category_id = #{categoryId,jdbcType=INTEGER},
            </if>
            <if test="categoryName != null">
                category_name = #{categoryName,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null">
                goods_name = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="skuName != null">
                sku_name = #{skuName,jdbcType=VARCHAR},
            </if>
            <if test="storeId != null">
                store_id = #{storeId,jdbcType=INTEGER},
            </if>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=INTEGER},
            </if>
            <if test="basicId != null">
                basic_id = #{basicId,jdbcType=INTEGER},
            </if>
            <if test="goodsId != null">
                goods_id = #{goodsId,jdbcType=INTEGER},
            </if>
            <if test="skuId != null">
                sku_id = #{skuId,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=BIT},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=INTEGER},
            </if>
            <if test="cost != null">
                cost = #{cost,jdbcType=INTEGER},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="goodsNum != null">
                goods_num = #{goodsNum,jdbcType=INTEGER},
            </if>
            <if test="skuNum != null">
                sku_num = #{skuNum,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="validStartTime != null">
                valid_start_time = #{validStartTime,jdbcType=INTEGER},
            </if>
            <if test="validEndTime != null">
                valid_end_time = #{validEndTime,jdbcType=INTEGER},
            </if>
            <if test="verificationStatus != null">
                verification_status = #{verificationStatus,jdbcType=TINYINT},
            </if>
            <if test="optionsVerification != null">
                options_verification = #{optionsVerification,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                source = #{source,jdbcType=TINYINT},
            </if>
            <if test="validityValue != null">
                validity_value = #{validityValue,jdbcType=INTEGER},
            </if>
            <if test="validityType != null">
                validity_type = #{validityType,jdbcType=TINYINT},
            </if>

            <if test="serviceTime != null">
                service_time = #{serviceTime,jdbcType=INTEGER},
            </if>
            <if test="serveType != null">
                serve_type = #{serveType,jdbcType=TINYINT},
            </if>
            <if test="serviceTag != null">
                service_tag = #{serviceTag,jdbcType=INTEGER},
            </if>
            <if test="realPaid != null">
                real_paid = #{realPaid},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.stbella.order.infrastructure.repository.po.OrderGiftExtend">
        update he_order_gift_extend
        set gmt_create           = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_modified         = #{gmtModified,jdbcType=TIMESTAMP},
            deleted              = #{deleted,jdbcType=BIT},
            category_id          = #{categoryId,jdbcType=INTEGER},
            category_name        = #{categoryName,jdbcType=VARCHAR},
            goods_name           = #{goodsName,jdbcType=VARCHAR},
            sku_name             = #{skuName,jdbcType=VARCHAR},
            store_id             = #{storeId,jdbcType=INTEGER},
            order_id             = #{orderId,jdbcType=INTEGER},
            basic_id             = #{basicId,jdbcType=INTEGER},
            goods_id             = #{goodsId,jdbcType=INTEGER},
            sku_id               = #{skuId,jdbcType=INTEGER},
            type                 = #{type,jdbcType=BIT},
            price                = #{price,jdbcType=INTEGER},
            cost                 = #{cost,jdbcType=INTEGER},
            content              = #{content,jdbcType=VARCHAR},
            goods_num            = #{goodsNum,jdbcType=INTEGER},
            sku_num              = #{skuNum,jdbcType=INTEGER},
            status               = #{status,jdbcType=TINYINT},
            valid_start_time     = #{validStartTime,jdbcType=INTEGER},
            valid_end_time       = #{validEndTime,jdbcType=INTEGER},
            verification_status  = #{verificationStatus,jdbcType=TINYINT},
            options_verification = #{optionsVerification,jdbcType=VARCHAR},
            source               = #{source,jdbcType=TINYINT},
            validity_value       = #{validityValue,jdbcType=INTEGER},
            validity_type        = #{validityType,jdbcType=TINYINT},
            service_time         = #{serviceTime,jdbcType=INTEGER},
            serve_type           = #{serveType,jdbcType=TINYINT},
            service_tag          = #{serviceTag,jdbcType=INTEGER},
            where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="expiredByGiftIds">
        update he_order_gift_extend
        set status=2 ,gmt_modified=now()
        where
        deleted= 0
        <if test="giftIds !=null and giftIds.size() > 0">
            and id in(
            <foreach collection="giftIds" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>
    </update>

    <select id="queryProductionOrderGiftList" resultType="com.stbella.order.server.order.ProductionOrderGoodsModel">

        select
        o.order_id, o.order_sn, o.created_at, o.store_id, o.client_uid, o.basic_uid,o.order_type,o.pay_amount,o.paid_amount,o.order_amount,o.production_amount_pay

        ,o.percent_first_time,

        p.valid_start_time,p.valid_end_time, p.verification_status, p.status, p.goods_num, p.price, p.type, p.goods_id, p.goods_name, p.sku_id, p.sku_name

        ,p.id,p.source,p.real_paid,data_source

        from
        he_order_gift_extend p left join he_order o on o.order_id = p.order_id
        where

         o.is_Delete = 0
        <if test="req.orderSn != null">
            and  o.order_sn = #{req.orderSn}
        </if>
        <if test="req.orderSn == null">
            and  o.percent_first_time >=  #{req.startTime}
            and percent_first_time &lt;= #{req.endTime}
            AND (`paid_amount` > 100 or `paid_amount` &lt; -100)
        </if>

        and o.order_type = #{req.orderType}
        and p.deleted = 0
        and p.type > 1
        and p.source =1

    </select>

    <select id="queryProductionOrderAdminGiftList" resultType="com.stbella.order.server.order.ProductionOrderGoodsModel">

        select
            o.order_id, o.order_sn, o.created_at, o.store_id, o.client_uid, o.basic_uid,o.order_type,o.pay_amount,o.paid_amount,o.order_amount,o.production_amount_pay

             ,o.percent_first_time,

            p.valid_start_time,p.valid_end_time, p.verification_status, p.status, p.goods_num, p.price, p.type, p.goods_id, p.goods_name, p.sku_id, p.sku_name

             ,p.id,p.source,p.sku_num,data_source

        from
            he_order_gift_extend p left join he_order o on o.order_id = p.order_id
        where
            o.percent_first_time >=  #{req.startTime}
          and percent_first_time &lt;= #{req.endTime}
          and o.is_Delete = 0
          and o.order_type = #{req.orderType}
          and p.deleted = 0
          and p.type = 6
          and p.source  > 1

    </select>



    <select id="queryMonthOrderGiftList" resultType="com.stbella.order.server.order.ProductionOrderGoodsModel">

        select
            o.order_id, o.order_sn, o.created_at, o.store_id, o.client_uid, o.basic_uid,o.order_type,o.pay_amount,o.paid_amount,o.order_amount,o.production_amount_pay
             ,o.percent_first_time,
            p.valid_start_time,p.valid_end_time, p.verification_status, p.status, p.goods_num, p.price, p.type, p.goods_id, p.goods_name, p.sku_id, p.sku_name
             ,p.id,p.source,p.sku_num,data_source
        from
            he_order_gift_extend p left join he_order o on o.order_id = p.order_id
        where
            o.percent_first_time >=  #{req.startTime}
          and percent_first_time &lt;= #{req.endTime}
          and o.is_Delete = 0
          and o.order_type in (0,1,30,50,70)
          and p.deleted = 0
          and p.type = 6

    </select>

    <select id="queryMonthOrderGiftListForBookAndDelAnd" resultType="com.stbella.order.server.order.ProductionOrderGoodsModel">

        select
            o.order_id, o.order_sn, o.created_at, o.store_id, o.client_uid, o.basic_uid,o.order_type,o.pay_amount,o.paid_amount,o.order_amount,o.production_amount_pay
             ,o.percent_first_time,
            p.valid_start_time,p.valid_end_time, p.verification_status, p.status, p.goods_num, p.price, p.type, p.goods_id, p.goods_name, p.sku_id, p.sku_name
             ,p.id,p.source,p.sku_num,data_source
        from
            he_order_gift_extend p left join he_order o on o.order_id = p.order_id
        where
            o.percent_first_time >=  #{req.startTime}
          and percent_first_time &lt;= #{req.endTime}
          and o.is_Delete = 0
          and o.order_type in (0,1,30,50,70)
          and p.deleted = 1
          and p.type = 6
          and p.verification_status in (1,2)

    </select>





</mapper>

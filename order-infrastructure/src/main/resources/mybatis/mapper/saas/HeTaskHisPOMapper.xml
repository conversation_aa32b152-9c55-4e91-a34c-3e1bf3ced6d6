<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeTaskHisMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeTaskHisPO">
        <id column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="deleted" property="deleted"/>
        <result column="task_id" property="taskId"/>
        <result column="approval_type" property="approvalType"/>
        <result column="approval_status" property="approvalStatus"/>
        <result column="form_template_type" property="formTemplateType"/>
        <result column="task_name" property="taskName"/>
        <result column="instance_id" property="instanceId"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        gmt_create
        ,
        deleted,
        id, task_id,approval_type,form_template_type, approval_status,task_name, instance_id, gmt_update
    </sql>
</mapper>

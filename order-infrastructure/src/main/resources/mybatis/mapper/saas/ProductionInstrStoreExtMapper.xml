<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.ProductionInstrStoreExtMapper">
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.ProductionInstrStoreExt">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="instr_id" jdbcType="INTEGER" property="instrId"/>
        <result column="store_id" jdbcType="INTEGER" property="storeId"/>
        <result column="num" jdbcType="INTEGER" property="num"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , instr_id, store_id, num, deleted, gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from he_production_instr_store_ext
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from he_production_instr_store_ext
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.stbella.order.infrastructure.repository.po.ProductionInstrStoreExt">
        insert into he_production_instr_store_ext (id, instr_id, store_id,
                                                   num, deleted, gmt_create,
                                                   gmt_modified)
        values (#{id,jdbcType=BIGINT}, #{instrId,jdbcType=INTEGER}, #{storeId,jdbcType=INTEGER},
                #{num,jdbcType=INTEGER}, #{deleted,jdbcType=BIT}, #{gmtCreate,jdbcType=TIMESTAMP},
                #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.stbella.order.infrastructure.repository.po.ProductionInstrStoreExt">
        insert into he_production_instr_store_ext
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="instrId != null">
                instr_id,
            </if>
            <if test="storeId != null">
                store_id,
            </if>
            <if test="num != null">
                num,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="instrId != null">
                #{instrId,jdbcType=INTEGER},
            </if>
            <if test="storeId != null">
                #{storeId,jdbcType=INTEGER},
            </if>
            <if test="num != null">
                #{num,jdbcType=INTEGER},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BIT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.stbella.order.infrastructure.repository.po.ProductionInstrStoreExt">
        update he_production_instr_store_ext
        <set>
            <if test="instrId != null">
                instr_id = #{instrId,jdbcType=INTEGER},
            </if>
            <if test="storeId != null">
                store_id = #{storeId,jdbcType=INTEGER},
            </if>
            <if test="num != null">
                num = #{num,jdbcType=INTEGER},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.stbella.order.infrastructure.repository.po.ProductionInstrStoreExt">
        update he_production_instr_store_ext
        set instr_id     = #{instrId,jdbcType=INTEGER},
            store_id     = #{storeId,jdbcType=INTEGER},
            num          = #{num,jdbcType=INTEGER},
            deleted      = #{deleted,jdbcType=BIT},
            gmt_create   = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
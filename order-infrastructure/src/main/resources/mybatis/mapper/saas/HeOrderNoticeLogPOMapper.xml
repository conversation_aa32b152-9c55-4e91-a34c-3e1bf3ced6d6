<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeOrderNoticeLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeOrderNoticeLogPO">
        <result column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="order_id" property="orderId"/>
        <result column="basic_uid" property="basicUid"/>
        <result column="client_uid" property="clientUid"/>
        <result column="client_type" property="clientType"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        gmt_create,
        gmt_modified,
        order_id, basic_uid, client_uid, client_type, is_delete
    </sql>

</mapper>

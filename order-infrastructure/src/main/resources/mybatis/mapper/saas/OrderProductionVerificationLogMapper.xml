<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.OrderProductionVerificationLogMapper">
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.OrderProductionVerificationLog">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="basic_id" jdbcType="INTEGER" property="basicId"/>
        <result column="production_id" jdbcType="INTEGER" property="productionId"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="card_id" jdbcType="INTEGER" property="cardId"/>
        <result column="goods_id" jdbcType="INTEGER" property="goodsId"/>
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName"/>
        <result column="sku_id" jdbcType="INTEGER" property="skuId"/>
        <result column="sku_name" jdbcType="VARCHAR" property="skuName"/>
        <result column="position_name" jdbcType="VARCHAR" property="positionName"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="verification_content" jdbcType="VARCHAR" property="verificationContent"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="operator_name" jdbcType="VARCHAR" property="operatorName"/>
        <result column="operator_store_id" jdbcType="INTEGER" property="operatorStoreId"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , basic_id, production_id, type, card_id, goods_id, goods_name, sku_id, sku_name,
    position_name, order_id, verification_content, operator_id, operator_name, operator_store_id, gmt_create,
    gmt_modified, deleted
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from he_order_production_verification_log
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from he_order_production_verification_log
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.stbella.order.infrastructure.repository.po.OrderProductionVerificationLog">
        insert into he_order_production_verification_log (id, basic_id, production_id,
                                                          type, card_id, goods_id,
                                                          goods_name, sku_id, sku_name,
                                                          position_name, order_id, verification_content,
                                                          operator_id, operator_name, operator_store_id, gmt_create,
                                                          gmt_modified, deleted)
        values (#{id,jdbcType=BIGINT}, #{basicId,jdbcType=INTEGER}, #{productionId,jdbcType=INTEGER},
                #{type,jdbcType=TINYINT}, #{cardId,jdbcType=INTEGER}, #{goodsId,jdbcType=INTEGER},
                #{goodsName,jdbcType=VARCHAR}, #{skuId,jdbcType=INTEGER}, #{skuName,jdbcType=VARCHAR},
                #{positionName,jdbcType=VARCHAR}, #{orderId,jdbcType=BIGINT}, #{verificationContent,jdbcType=VARCHAR},
                #{operatorId,jdbcType=BIGINT}, #{operatorName,jdbcType=VARCHAR}, #{operatorStoreId,jdbcType=INTEGER},
                #{gmtCreate,jdbcType=TIMESTAMP},
                #{gmtModified,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT})
    </insert>
    <insert id="insertSelective"
            parameterType="com.stbella.order.infrastructure.repository.po.OrderProductionVerificationLog">
        insert into he_order_production_verification_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="basicId != null">
                basic_id,
            </if>
            <if test="productionId != null">
                production_id,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="cardId != null">
                card_id,
            </if>
            <if test="goodsId != null">
                goods_id,
            </if>
            <if test="goodsName != null">
                goods_name,
            </if>
            <if test="skuId != null">
                sku_id,
            </if>
            <if test="skuName != null">
                sku_name,
            </if>
            <if test="positionName != null">
                position_name,
            </if>
            <if test="orderId != null">
                order_id,
            </if>
            <if test="verificationContent != null">
                verification_content,
            </if>
            <if test="operatorId != null">
                operator_id,
            </if>
            <if test="operatorName != null">
                operator_name,
            </if>
            <if test="operatorStoreId != null">
                operator_store_id,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="basicId != null">
                #{basicId,jdbcType=INTEGER},
            </if>
            <if test="productionId != null">
                #{productionId,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType=TINYINT},
            </if>
            <if test="cardId != null">
                #{cardId,jdbcType=INTEGER},
            </if>
            <if test="goodsId != null">
                #{goodsId,jdbcType=INTEGER},
            </if>
            <if test="goodsName != null">
                #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="skuId != null">
                #{skuId,jdbcType=INTEGER},
            </if>
            <if test="skuName != null">
                #{skuName,jdbcType=VARCHAR},
            </if>
            <if test="positionName != null">
                #{positionName,jdbcType=VARCHAR},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=BIGINT},
            </if>
            <if test="verificationContent != null">
                #{verificationContent,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null">
                #{operatorId,jdbcType=BIGINT},
            </if>
            <if test="operatorName != null">
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="operatorStoreId != null">
                #{operatorStoreId,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BIT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.stbella.order.infrastructure.repository.po.OrderProductionVerificationLog">
        update he_order_production_verification_log
        <set>
            <if test="basicId != null">
                basic_id = #{basicId,jdbcType=INTEGER},
            </if>
            <if test="productionId != null">
                production_id = #{productionId,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=TINYINT},
            </if>
            <if test="cardId != null">
                card_id = #{cardId,jdbcType=INTEGER},
            </if>
            <if test="goodsId != null">
                goods_id = #{goodsId,jdbcType=INTEGER},
            </if>
            <if test="goodsName != null">
                goods_name = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="skuId != null">
                sku_id = #{skuId,jdbcType=INTEGER},
            </if>
            <if test="skuName != null">
                sku_name = #{skuName,jdbcType=VARCHAR},
            </if>
            <if test="positionName != null">
                position_name = #{positionName,jdbcType=VARCHAR},
            </if>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="verificationContent != null">
                verification_content = #{verificationContent,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null">
                operator_id = #{operatorId,jdbcType=BIGINT},
            </if>
            <if test="operatorName != null">
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="operatorStoreId != null">
                operator_store_id = #{operatorStoreId,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.stbella.order.infrastructure.repository.po.OrderProductionVerificationLog">
        update he_order_production_verification_log
        set basic_id             = #{basicId,jdbcType=INTEGER},
            production_id        = #{productionId,jdbcType=INTEGER},
            type                 = #{type,jdbcType=TINYINT},
            card_id              = #{cardId,jdbcType=INTEGER},
            goods_id             = #{goodsId,jdbcType=INTEGER},
            goods_name           = #{goodsName,jdbcType=VARCHAR},
            sku_id               = #{skuId,jdbcType=INTEGER},
            sku_name             = #{skuName,jdbcType=VARCHAR},
            position_name        = #{positionName,jdbcType=VARCHAR},
            order_id             = #{orderId,jdbcType=BIGINT},
            verification_content = #{verificationContent,jdbcType=VARCHAR},
            operator_id          = #{operatorId,jdbcType=BIGINT},
            operator_name        = #{operatorName,jdbcType=VARCHAR},
            operator_store_id    = #{operatorStoreId,jdbcType=INTEGER},
            gmt_create           = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_modified         = #{gmtModified,jdbcType=TIMESTAMP},
            deleted              = #{deleted,jdbcType=BIT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="queryProductionOrderVerificationLogList" resultMap="BaseResultMap">
        select
        o.order_id,
        p.card_id,
        p.type,
        p.goods_id,
        p.goods_name,
        p.sku_id,
        p.sku_name,
        p.position_name,
        p.verification_content,
        p.operator_name,
        p.operator_store_id,
        p.production_id,
        p.operator_store_id,
        p.gmt_create
        from
        he_order_production_verification_log p
        left join he_order o on o.order_id = p.order_id
        where
        p.gmt_create >=   FROM_UNIXTIME(#{req.startTime})
        and p.gmt_create &lt;=  FROM_UNIXTIME(#{req.endTime})
        and o.is_Delete = 0
        and p.deleted = 0

    </select>

    <select id="queryProductionOrderVerificationLogForInvalidList"  resultType="com.stbella.order.server.order.ProductionOrderPo">

        select
            o.store_id,
            o.basic_uid,
            o.client_uid,
            o.order_sn,
            l.operator_store_id as checkoutStoreId,
            l.production_id as orderProductionId,
            o.basic_uid as orderBasicId,
            l.gmt_create as checkoutTime,
            l.operator_name as checkoutUserName,
            concat(l.verification_content,'/',l.sku_name)   as productionChildName,
            '未生效的订单核销记录' as verificationRemark

        from
            he_order_production_verification_log l
                left join he_order o on l.order_id = o.order_id
        where
        l.deleted = 0
        and l.gmt_create >= FROM_UNIXTIME(#{req.startTime})
        and l.gmt_create &lt;  FROM_UNIXTIME(#{req.endTime})
        and percent_first_time &lt;= 0


    </select>

</mapper>
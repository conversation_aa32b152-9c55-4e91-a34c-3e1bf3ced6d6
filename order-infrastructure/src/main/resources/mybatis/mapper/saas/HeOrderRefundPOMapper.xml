<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeOrderRefundMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.stbella.order.infrastructure.repository.po.HeOrderRefundPO">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="order_good_id" property="orderGoodId"/>
        <result column="type" property="type"/>
        <result column="refund_order_sn" property="refundOrderSn"/>
        <result column="apply_amount" property="applyAmount"/>
        <result column="refund_type" property="refundType"/>
        <result column="refund_method" property="refundMethod"/>
        <result column="project_id" property="projectId"/>
        <result column="agree_at" property="agreeAt"/>
        <result column="actual_amount" property="actualAmount"/>
        <result column="refund_achievement" property="refundAchievement"/>
        <result column="finish_at" property="finishAt"/>
        <result column="payment_result" property="paymentResult"/>
        <result column="status" property="status"/>
        <result column="confirm_uid" property="confirmUid"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="refund_info" property="refundInfo"/>
        <result column="remark" property="remark"/>
        <result column="refund_reason_type" property="refundReasonType"/>
        <result column="refund_reason" property="refundReason"/>
        <result column="has_liquidated_damages" property="hasLiquidatedDamages"/>
        <result column="express_code" property="expressCode"/>
        <result column="express_name" property="expressName"/>
        <result column="express_number" property="expressNumber"/>
        <result column="task_id" property="taskId"/>
        <result column="last_task_id" property="lastTaskId"/>
        <result column="check_status" property="checkStatus"/>
        <result column="transaction_id" property="transactionId"/>
        <result column="is_admin_modify" property="isAdminModify"/>
        <result column="apply_id" property="applyId"/>
        <result column="apply_name" property="applyName"/>
        <result column="apply_phone" property="applyPhone"/>
        <result column="refund_nature" property="refundNature"/>
        <result column="currency" property="currency"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        order_id,
        order_good_id,
        type,
        refund_order_sn,
        apply_amount,
        refund_type,
        refund_method,
        project_id,
        agree_at,
        actual_amount,
        refund_achievement,
        finish_at,
        payment_result,
        status,
        confirm_uid,
        is_delete,
        created_at,
        updated_at,
        refund_info,
        remark,
        refund_reason_type,
        refund_reason,
        has_liquidated_damages,
        express_code,
        express_name,
        express_number,
        task_id,
        last_task_id,
        check_status,
        transaction_id,
        is_admin_modify,
        refund_nature,
        currency
    </sql>
    <select id="queryTotalRefundAchievement"
            parameterType="com.stbella.order.server.order.month.req.OrderRefundQuery"
            resultType="java.lang.Long">
        select sum(`refund_achievement`) as aggregate
        from `he_order_refund`
                where
        <if test="condition != null">
            <if test="condition.startPercentFirstTime != null and condition.endPercentFirstTime != null">
                `finish_at` between #{condition.startPercentFirstTime,jdbcType=NUMERIC} and #{condition.endPercentFirstTime,jdbcType=NUMERIC}
            </if>
            and `is_delete` = '0'
            and exists(select 1
                       from `he_order`
                    where `he_order_refund`.`order_id` = `he_order`.`order_id`
            <if test="condition.orderType != null and condition.orderType.size() > 0">
                and `order_type` in
                <foreach collection="condition.orderType" item="item" separator="," open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            and `percent_first_time` > '0'
            <if test="condition.storeId != null and condition.storeId.size() > 0">
                and `store_id` in
                <foreach collection="condition.storeId" item="item" separator="," open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            )
                    and exists(select 1
                               from `he_income_record`
                    where `he_order_refund`.`order_good_id` = `he_income_record`.`id`
            <if test="condition.storeId != null and condition.storeId.size() > 0">
                and `receipt_type` in
                <foreach collection="condition.incomeReceiptType" item="item" separator="," open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            )
                    and `is_delete` = '0'
        </if>
    </select>

    <select id="queryOrderRefundByCondition" resultType="com.stbella.order.server.order.ProductionOrderGoodsModel">

        select
            o.order_id, o.order_sn, o.store_id, o.client_uid, o.basic_uid,o.order_type,o.pay_amount/100 as pay_amount ,p.actual_amount/100 as paidAmount  ,o.order_amount/100 as  order_amount,o.production_amount_pay,
            o.created_at
                ,p.finish_at as percentFirstTime,  p.finish_at as validStartTime

        from
        he_order_refund p left join he_order o on o.order_id = p.order_id
        where
        o.percent_first_time >=  #{req.startTime}
          and percent_first_time &lt;= #{req.endTime}
          and p.finish_at >= #{req.startTime}
          and p.finish_at &lt;= #{req.endTime}
          and o.is_Delete = 0
          and o.order_type = #{req.orderType}
          and p.is_delete = 0
          and p.status = 4


    </select>
    <select id="getRefundListForFinancial" resultType="com.stbella.order.domain.order.month.entity.HeOrderRefundEntity">
        SELECT
        hor.*,hir.account_id
        FROM he_order_refund hor
        left join he_income_record hir on hir.id = hor.order_good_id
        WHERE hor.is_delete = 0
        <if test="query.refundIdList != null and query.refundIdList.size() > 0  ">
           and hor.id IN
            <foreach item="item" index="index" collection="query.refundIdList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.status != null">
            AND hor.status = #{query.status}
        </if>
        <if test="query.payTimeStart != null">
            AND hor.finish_at &gt;= #{query.payTimeStart}
        </if>
        <if test="query.payTimeEnd != null">
            AND hor.finish_at &lt;= #{query.payTimeEnd}
        </if>
        <if test="query.updateTimeStart != null">
            AND hor.updated_at &gt;= #{query.updateTimeStart}
        </if>
        <if test="query.updateTimeEnd != null">
            AND hor.updated_at &lt;= #{query.updateTimeEnd}
        </if>
        <if test="query.storeIds != null and query.storeIds.size() > 0">
            AND hir.store_id in
            <foreach collection="query.storeIds" item="storeId" separator="," open="("
                     close=")">
                #{storeId}
            </foreach>
        </if>

    </select>

    <select id="queryCurrencyIsNull" resultType="com.stbella.order.domain.order.month.entity.HeOrderRefundEntity">
        SELECT he_order_refund.id,
               he_order_refund.order_id  AS orderId,
               he_order_refund.income_sn AS incomeSn,
               he_order.store_id         AS storeId
        FROM he_order_refund
                 LEFT JOIN he_order ON he_order_refund.order_id = he_order.order_id
        where he_order_refund.currency is null
          and he_order_refund.order_id != 0
            and he_order_refund.is_delete = 0
            and he_order_refund.created_at &gt;= #{startDate}

        union all

        SELECT he_order_refund.id,
               he_order_refund.order_id  AS orderId,
               he_order_refund.income_sn AS incomeSn,
               he_income_record.store_id AS storeId
        FROM he_order_refund
                 LEFT JOIN he_income_record ON he_order_refund.income_sn = he_income_record.income_sn
        where he_order_refund.currency is null
          and he_order_refund.order_id = 0
          and he_order_refund.is_delete = 0
          and he_order_refund.created_at &gt;= #{startDate}
    </select>

</mapper>

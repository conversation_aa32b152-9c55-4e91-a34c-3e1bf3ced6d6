<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeOrderPO">
        <id column="order_id" property="orderId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="order_type" property="orderType"/>
        <result column="task_id" property="taskId"/>
        <result column="basic_uid" property="basicUid"/>
        <result column="client_uid" property="clientUid"/>
        <result column="client_type" property="clientType"/>
        <result column="staff_id" property="staffId"/>
        <result column="store_id" property="storeId"/>
        <result column="is_delivery" property="isDelivery"/>
        <result column="is_has_refund" property="isHasRefund"/>
        <result column="is_close" property="isClose"/>
        <result column="pay_status" property="payStatus"/>
        <result column="order_amount" property="orderAmount"/>
        <result column="holiday_amount" property="holidayAmount"/>
        <result column="holiday_items" property="holidayItems"/>
        <result column="holiday_num" property="holidayNum"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="pay_first_time" property="payFirstTime"/>
        <result column="paid_amount" property="paidAmount"/>
        <result column="pay_integral" property="payIntegral"/>
        <result column="production_amount_pay" property="productionAmountPay"/>
        <result column="freight" property="freight"/>
        <result column="want_in" property="wantIn"/>
        <result column="remark" property="remark"/>
        <result column="nowpaytask" property="nowpaytask"/>
        <result column="express_name" property="expressName"/>
        <result column="express_phone" property="expressPhone"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="area" property="area"/>
        <result column="province_id" property="provinceId"/>
        <result column="city_id" property="cityId"/>
        <result column="area_id" property="areaId"/>
        <result column="address" property="address"/>
        <result column="is_sprint" property="isSprint"/>
        <result column="is_send_all" property="isSendAll"/>
        <result column="source" property="source"/>
        <result column="deposit_task_id" property="depositTaskId"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="order_sell_type" property="orderSellType"/>
        <result column="production_type" property="productionType"/>
        <result column="infor_fields" property="inforFields"/>
        <result column="percent_first_time" property="percentFirstTime"/>
        <result column="bk_percent_first_time" property="bkPercentFirstTime"/>
        <result column="operation_type" property="operationType"/>
        <result column="extends_content" property="extendsContent"/>
        <result column="coupon_amount" property="couponAmount"/>
        <result column="coupon_user_id" property="couponUserId"/>
        <result column="contract_type" property="contractType"/>
        <result column="invite_add_integral_type" property="inviteAddIntegralType"/>
        <result column="gross_margin" property="grossMargin"/>
        <result column="net_margin" property="netMargin"/>
        <result column="discount_margin" property="discountMargin"/>
        <result column="discount_details" property="discountDetails"/>
        <result column="approval_discount_status" property="approvalDiscountStatus"/>
        <result column="is_notice" property="isNotice"/>
        <result column="order_tag" property="orderTag"/>
        <result column="order_tag_name" property="orderTagName"/>
        <result column="cert_type" property="certType"/>
        <result column="id_card" property="idCard"/>
        <result column="id_card_front" property="idCardFront"/>
        <result column="id_card_back" property="idCardBack"/>
        <result column="sbra_achievement_type" property="sbraAchievementType"/>
        <result column="appointment_second_type" property="appointmentSecondType"/>
        <result column="old_or_new" property="oldOrNew"/>
        <result column="sign_type" property="signType"/>
        <result column="version" property="version"/>
        <result column="currency" property="currency"/>
        <result column="is_prepare_pregnancy" property="isPreparePregnancy"/>
        <result column="full_refund_date" property="fullRefundDate"/>
        <result column="verification_status" property="verificationStatus"/>
        <result column="verification_id" property="verificationId"/>
    </resultMap>


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        order_id
        ,currency
        , order_sn, order_type, task_id, basic_uid, client_uid, client_type, staff_id, store_id, is_delivery, is_has_refund, is_close, pay_status, order_amount, holiday_amount, holiday_items, holiday_num, pay_amount, pay_first_time, paid_amount, pay_integral, production_amount_pay, freight, want_in, remark, nowpaytask, express_name, express_phone, province, city, area, province_id, city_id, area_id, address, is_sprint, is_send_all, source, deposit_task_id, is_delete, created_at, updated_at, order_sell_type, production_type, infor_fields, percent_first_time, bk_percent_first_time, operation_type, extends_content, coupon_amount, coupon_user_id, contract_type, invite_add_integral_type, gross_margin, net_margin, discount_margin, discount_details, approval_discount_status, is_notice, order_tag, order_tag_name, cert_type, id_card, id_card_front, id_card_back, sbra_achievement_type
            , appointment_second_type, old_or_new,order_status,refund_status,sign_type,version,is_prepare_pregnancy,full_refund_date,verification_status,verification_id
    </sql>


    <select id="getMyOrderList" resultType="com.stbella.order.server.order.month.res.WechatMyOrderVO">
        select
        og.goods_name goodsName,
        og.id goodsId,
        og.service_days serviceDays,
        r.pay_amount payAmountLong,
        r.order_amount orderAmountLong,
        r.paid_amount paidAmountLong,
        r.client_uid clientUid,
        r.store_id storeId,
        r.basic_uid basicUid,
        r.order_sn orderSn,
        r.order_type orderType,
        r.order_id orderId,
        r.pay_status payStatus,
        r.order_status orderStatus,
        r.old_or_new oldOrNew,
        r.refund_status refundStatus,
        r.sign_type signType,
        r.real_amount realAmount,
        r.approval_discount_status approvalDiscountStatus,
        r.verification_status As verificationStatus
        from he_order r,
        he_order_goods og
        where
        r.staff_id = #{query.operator.operatorGuid}
        and r.old_or_new = 1
        and r.order_id = og.order_id
        and r.is_delete = 0
        and og.is_delete = 0
        and r.order_status != 4
        and r.version &lt; 3


        <if test="query.orderStatus != null and query.orderStatus != -1 ">
            and r.order_status = #{query.orderStatus}
        </if>
        <if test="query.orderType != null and query.orderType != -1 ">
            and r.order_type = #{query.orderType}
        </if>
        <if test="query.keyword != null and query.keyword != '' ">
            and (
            r.client_uid IN
            <foreach collection="query.clientIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            or og.goods_name like CONCAT('%',#{query.keyword}, '%')
            )
        </if>
        order by r.created_at desc
    </select>

    <select id="getCustomerOrderList" resultType="com.stbella.order.server.order.month.res.CustomerMyOrderVO">
        select
        r.pay_amount payAmountLong,
        r.order_amount orderAmountLong,
        r.paid_amount paidAmountLong,
        r.client_uid clientUid,
        r.store_id storeId,
        r.basic_uid basicUid,
        r.order_sn orderSn,
        r.order_id orderId,
        r.pay_status payStatus,
        r.order_status orderStatus,
        r.old_or_new oldOrNew,
        r.task_id taskId,
        r.nowpaytask nowpaytask,
        r.order_type orderType,
        r.real_amount realAmount
        from he_order r
        where
        r.is_delete = 0
        and r.order_status != 4
        and (r.order_type in (0,1,30,50,60,70))
        <if test="query.phone != null and query.phone != '' ">
            and (
            r.client_uid IN
            <foreach collection="query.clientIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            )
        </if>
        order by r.created_at desc
    </select>

    <select id="queryCurrMonthOrderIterm" resultType="com.stbella.order.domain.order.month.entity.HeOrderEntity">

        SELECT
        o.order_id , o.order_sn ,  order_amount/100 as order_amount,pay_amount/100 as pay_amount, paid_amount/100 as paid_amount ,o.production_amount_pay/100 as  production_amount_pay ,store_id,basic_uid,
        percent_first_time,o.order_type
        from he_order o
        where order_type in (0,1,30, 50,60,70)
        and   percent_first_time >= #{req.startTime}
        and percent_first_time &lt; #{req.endTime}
        and paid_amount > 100


    </select>
    <select id="queryList" resultType="com.stbella.order.infrastructure.repository.po.HeOrderPO">
        SELECT
        heOrder.order_id,
        heOrder.currency,
        heOrder.order_sn,
        heOrder.order_type,
        heOrder.task_id,
        heOrder.basic_uid,
        heOrder.client_uid,
        heOrder.client_type,
        heOrder.staff_id,
        heOrder.store_id,
        heOrder.is_delivery,
        heOrder.is_has_refund,
        heOrder.is_close,
        heOrder.pay_status,
        heOrder.order_amount,
        heOrder.holiday_amount,
        heOrder.holiday_items,
        heOrder.holiday_num,
        heOrder.pay_amount,
        heOrder.pay_first_time,
        heOrder.paid_amount,
        heOrder.pay_integral,
        heOrder.production_amount_pay,
        heOrder.freight,
        heOrder.want_in,
        heOrder.remark,
        heOrder.nowpaytask,
        heOrder.express_name,
        heOrder.express_phone,
        heOrder.province,
        heOrder.city,
        heOrder.area,
        heOrder.province_id,
        heOrder.city_id,
        heOrder.area_id,
        heOrder.address,
        heOrder.is_sprint,
        heOrder.is_send_all,
        heOrder.source,
        heOrder.deposit_task_id,
        heOrder.is_delete,
        heOrder.created_at,
        heOrder.updated_at,
        heOrder.order_sell_type,
        heOrder.production_type,
        heOrder.infor_fields,
        heOrder.percent_first_time,
        heOrder.bk_percent_first_time,
        heOrder.operation_type,
        heOrder.extends_content,
        heOrder.coupon_amount,
        heOrder.coupon_user_id,
        heOrder.contract_type,
        heOrder.invite_add_integral_type,
        heOrder.gross_margin,
        heOrder.net_margin,
        heOrder.discount_margin,
        heOrder.discount_details,
        heOrder.approval_discount_status,
        heOrder.is_notice,
        heOrder.order_tag,
        heOrder.order_tag_name,
        heOrder.cert_type,
        heOrder.id_card,
        heOrder.id_card_front,
        heOrder.id_card_back,
        heOrder.sbra_achievement_type,
        heOrder.appointment_second_type,
        heOrder.old_or_new,
        heOrder.order_status,
        heOrder.refund_status,
        heOrder.sign_type,
        heOrder.version,
        heOrder.is_prepare_pregnancy,
        heOrder.full_refund_date,
        heOrder.is_previously_fully_paid,
        heOrder.extra_info,
        heOrder.fx_rate,
        heOrder.scene,
        heOrder.create_by,
        heOrder.gift_amount,
        heOrder.pay_finish_at
        FROM
        he_order heOrder
        left join he_order_gift_extend gift on heOrder.order_id = gift.order_id
        <where>
            heOrder.is_delete = false
            <if test="query.orderType != null and query.orderType != -1 ">
                and heOrder.order_type = #{query.orderType}
            </if>
            <if test="query.storeIdList != null and query.storeIdList.size() > 0">
                and
                heOrder.store_id IN
                <foreach collection="query.storeIdList" item="storeId" separator="," open="(" close=")">
                    #{storeId}
                </foreach>
            </if>
            <if test="query.orderIdList != null and query.orderIdList.size() > 0">
                and
                heOrder.order_id IN
                <foreach collection="query.orderIdList" item="orderId" separator="," open="(" close=")">
                    #{orderId}
                </foreach>
            </if>

            <if test="payTimeStart != 0 and payTimeEnd != 0 ">
                and heOrder.pay_finish_at >= #{payTimeStart}
                and heOrder.pay_finish_at &lt; #{payTimeEnd}
            </if>

            <if test="query.payTimeStart != null and query.payTimeEnd != null ">
                or (gift.gmt_create >= #{query.payTimeStart}
                and gift.gmt_create &lt; #{query.payTimeEnd}
                and gift.type = 6
                and gift.status != 0)
            </if>
        </where>
        order by heOrder.order_id desc
    </select>

    <select id="queryByClientIdsAndAssetType" resultType="Integer">
        SELECT DISTINCT
        he_order.order_id
        FROM
        he_order
        LEFT JOIN he_order_goods ON he_order.order_id = he_order_goods.order_id
        WHERE
        he_order.is_delete = 0
          AND he_order.order_type IN (0,1)
        <if test="orderSn != null">
            AND he_order.order_sn = #{orderSn}
        </if>
        AND he_order_goods.is_delete = 0
        <if test="storeId != null">
            AND he_order.store_id = #{storeId}
        </if>
<!--        AND he_order_goods.goods_type IN-->
<!--        <foreach collection="assetType" item="asset" separator="," open="(" close=")">-->
<!--            #{asset}-->
<!--        </foreach>-->
        <if test="clientIdList != null and clientIdList.size() > 0">
            AND he_order.client_uid IN
            <foreach collection="clientIdList" item="clientId" separator="," open="(" close=")">
                #{clientId}
            </foreach>
        </if>
        ORDER BY
        he_order.client_uid,
        he_order.order_sn
        limit 30
    </select>


    <select id="listOldOrders2Trans" resultType="com.stbella.order.infrastructure.repository.po.HeOrderPO">
        SELECT
        ho.*
        FROM
        he_order ho
        WHERE
        ho.order_status != 3
        AND store_id NOT IN ( 1044, 1066, 1112, 1113, 1118 )
        AND -- 老月子订单
        ( version &lt;  '3.0.0' )
        AND order_type IN (0,1) -- 订单类型为月子订单
        AND is_close = 0 -- 未关闭状态、未删除
        AND is_delete = 0
        AND percent_first_time > 0 -- 已报单
        AND pay_status IN ( 1, 2, 4 ) -- 已经支付
        AND created_at > 1640966400 -- 2022年1月1日之后的
        AND paid_amount > 0 -- 订单金额大于支付金额
        AND pay_amount > paid_amount
        AND (
        ( SELECT COALESCE ( SUM( apply_amount ), 0 ) FROM he_order_refund hor WHERE hor.order_id = ho.order_id AND hor.`status` = 4 ) &lt;  paid_amount
          OR ( SELECT COUNT(*) FROM he_order_refund hor WHERE hor.order_id = ho.order_id AND hor.`status` = 4 ) = 0
        )
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeOrderPO">
        <id column="order_id" property="orderId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="order_type" property="orderType"/>
        <result column="task_id" property="taskId"/>
        <result column="basic_uid" property="basicUid"/>
        <result column="client_uid" property="clientUid"/>
        <result column="client_type" property="clientType"/>
        <result column="staff_id" property="staffId"/>
        <result column="store_id" property="storeId"/>
        <result column="is_delivery" property="isDelivery"/>
        <result column="is_has_refund" property="isHasRefund"/>
        <result column="is_close" property="isClose"/>
        <result column="pay_status" property="payStatus"/>
        <result column="order_amount" property="orderAmount"/>
        <result column="holiday_amount" property="holidayAmount"/>
        <result column="holiday_items" property="holidayItems"/>
        <result column="holiday_num" property="holidayNum"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="pay_first_time" property="payFirstTime"/>
        <result column="paid_amount" property="paidAmount"/>
        <result column="pay_integral" property="payIntegral"/>
        <result column="production_amount_pay" property="productionAmountPay"/>
        <result column="freight" property="freight"/>
        <result column="want_in" property="wantIn"/>
        <result column="remark" property="remark"/>
        <result column="nowpaytask" property="nowpaytask"/>
        <result column="express_name" property="expressName"/>
        <result column="express_phone" property="expressPhone"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="area" property="area"/>
        <result column="province_id" property="provinceId"/>
        <result column="city_id" property="cityId"/>
        <result column="area_id" property="areaId"/>
        <result column="address" property="address"/>
        <result column="is_sprint" property="isSprint"/>
        <result column="is_send_all" property="isSendAll"/>
        <result column="source" property="source"/>
        <result column="deposit_task_id" property="depositTaskId"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="order_sell_type" property="orderSellType"/>
        <result column="production_type" property="productionType"/>
        <result column="infor_fields" property="inforFields"/>
        <result column="percent_first_time" property="percentFirstTime"/>
        <result column="bk_percent_first_time" property="bkPercentFirstTime"/>
        <result column="operation_type" property="operationType"/>
        <result column="extends_content" property="extendsContent"/>
        <result column="coupon_amount" property="couponAmount"/>
        <result column="coupon_user_id" property="couponUserId"/>
        <result column="contract_type" property="contractType"/>
        <result column="invite_add_integral_type" property="inviteAddIntegralType"/>
        <result column="gross_margin" property="grossMargin"/>
        <result column="net_margin" property="netMargin"/>
        <result column="discount_margin" property="discountMargin"/>
        <result column="discount_details" property="discountDetails"/>
        <result column="approval_discount_status" property="approvalDiscountStatus"/>
        <result column="is_notice" property="isNotice"/>
        <result column="order_tag" property="orderTag"/>
        <result column="order_tag_name" property="orderTagName"/>
        <result column="cert_type" property="certType"/>
        <result column="id_card" property="idCard"/>
        <result column="id_card_front" property="idCardFront"/>
        <result column="id_card_back" property="idCardBack"/>
        <result column="sbra_achievement_type" property="sbraAchievementType"/>
        <result column="appointment_second_type" property="appointmentSecondType"/>
        <result column="old_or_new" property="oldOrNew"/>
        <result column="sign_type" property="signType"/>
        <result column="version" property="version"/>
        <result column="currency" property="currency"/>
        <result column="is_prepare_pregnancy" property="isPreparePregnancy"/>
        <result column="full_refund_date" property="fullRefundDate"/>
        <result column="verification_status" property="verificationStatus"/>
        <result column="verification_id" property="verificationId"/>
    </resultMap>


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        order_id
        ,currency
        , order_sn, order_type, task_id, basic_uid, client_uid, client_type, staff_id, store_id, is_delivery, is_has_refund, is_close, pay_status, order_amount, holiday_amount, holiday_items, holiday_num, pay_amount, pay_first_time, paid_amount, pay_integral, production_amount_pay, freight, want_in, remark, nowpaytask, express_name, express_phone, province, city, area, province_id, city_id, area_id, address, is_sprint, is_send_all, source, deposit_task_id, is_delete, created_at, updated_at, order_sell_type, production_type, infor_fields, percent_first_time, bk_percent_first_time, operation_type, extends_content, coupon_amount, coupon_user_id, contract_type, invite_add_integral_type, gross_margin, net_margin, discount_margin, discount_details, approval_discount_status, is_notice, order_tag, order_tag_name, cert_type, id_card, id_card_front, id_card_back, sbra_achievement_type
            , appointment_second_type, old_or_new,order_status,refund_status,sign_type,version,is_prepare_pregnancy,full_refund_date,verification_status,verification_id
    </sql>


    <select id="getMyOrderList" resultType="com.stbella.order.server.order.month.res.WechatMyOrderVO">
        select
        og.goods_name goodsName,
        og.id goodsId,
        og.service_days serviceDays,
        r.pay_amount payAmountLong,
        r.order_amount orderAmountLong,
        r.paid_amount paidAmountLong,
        r.client_uid clientUid,
        r.store_id storeId,
        r.basic_uid basicUid,
        r.order_sn orderSn,
        r.order_type orderType,
        r.order_id orderId,
        r.pay_status payStatus,
        r.order_status orderStatus,
        r.old_or_new oldOrNew,
        r.refund_status refundStatus,
        r.sign_type signType,
        r.real_amount realAmount,
        r.approval_discount_status approvalDiscountStatus,
        r.verification_status As verificationStatus
        from he_order r,
        he_order_goods og
        where
        r.staff_id = #{query.operator.operatorGuid}
        and r.old_or_new = 1
        and r.order_id = og.order_id
        and r.is_delete = 0
        and og.is_delete = 0
        and r.order_status != 4
        and r.version &lt; 3


        <if test="query.orderStatus != null and query.orderStatus != -1 ">
            and r.order_status = #{query.orderStatus}
        </if>
        <if test="query.orderType != null and query.orderType != -1 ">
            and r.order_type = #{query.orderType}
        </if>
        <if test="query.keyword != null and query.keyword != '' ">
            and (
            r.client_uid IN
            <foreach collection="query.clientIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            or og.goods_name like CONCAT('%',#{query.keyword}, '%')
            )
        </if>
        order by r.created_at desc
    </select>

    <select id="getCustomerOrderList" resultType="com.stbella.order.server.order.month.res.CustomerMyOrderVO">
        select
        r.pay_amount payAmountLong,
        r.order_amount orderAmountLong,
        r.paid_amount paidAmountLong,
        r.client_uid clientUid,
        r.store_id storeId,
        r.basic_uid basicUid,
        r.order_sn orderSn,
        r.order_id orderId,
        r.pay_status payStatus,
        r.order_status orderStatus,
        r.old_or_new oldOrNew,
        r.task_id taskId,
        r.nowpaytask nowpaytask,
        r.order_type orderType,
        r.real_amount realAmount
        from he_order r
        where
        r.is_delete = 0
        and r.order_status != 4
        and (r.order_type in (0,1,30,50,60,70))
        <if test="query.phone != null and query.phone != '' ">
            and (
            r.client_uid IN
            <foreach collection="query.clientIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            )
        </if>
        order by r.created_at desc
    </select>

    <select id="queryCurrMonthOrderIterm" resultType="com.stbella.order.domain.order.month.entity.HeOrderEntity">

        SELECT
        o.order_id , o.order_sn ,  order_amount/100 as order_amount,pay_amount/100 as pay_amount, paid_amount/100 as paid_amount ,o.production_amount_pay/100 as  production_amount_pay ,store_id,basic_uid,
        percent_first_time,o.order_type
        from he_order o
        where order_type in (0,1,30, 50,60,70)
        and   percent_first_time >= #{req.startTime}
        and percent_first_time &lt; #{req.endTime}
        and paid_amount > 100


    </select>
    <select id="queryList" resultType="com.stbella.order.infrastructure.repository.po.HeOrderPO">
        SELECT
        heOrder.order_id,
        heOrder.currency,
        heOrder.order_sn,
        heOrder.order_type,
        heOrder.task_id,
        heOrder.basic_uid,
        heOrder.client_uid,
        heOrder.client_type,
        heOrder.staff_id,
        heOrder.store_id,
        heOrder.is_delivery,
        heOrder.is_has_refund,
        heOrder.is_close,
        heOrder.pay_status,
        heOrder.order_amount,
        heOrder.holiday_amount,
        heOrder.holiday_items,
        heOrder.holiday_num,
        heOrder.pay_amount,
        heOrder.pay_first_time,
        heOrder.paid_amount,
        heOrder.pay_integral,
        heOrder.production_amount_pay,
        heOrder.freight,
        heOrder.want_in,
        heOrder.remark,
        heOrder.nowpaytask,
        heOrder.express_name,
        heOrder.express_phone,
        heOrder.province,
        heOrder.city,
        heOrder.area,
        heOrder.province_id,
        heOrder.city_id,
        heOrder.area_id,
        heOrder.address,
        heOrder.is_sprint,
        heOrder.is_send_all,
        heOrder.source,
        heOrder.deposit_task_id,
        heOrder.is_delete,
        heOrder.created_at,
        heOrder.updated_at,
        heOrder.order_sell_type,
        heOrder.production_type,
        heOrder.infor_fields,
        heOrder.percent_first_time,
        heOrder.bk_percent_first_time,
        heOrder.operation_type,
        heOrder.extends_content,
        heOrder.coupon_amount,
        heOrder.coupon_user_id,
        heOrder.contract_type,
        heOrder.invite_add_integral_type,
        heOrder.gross_margin,
        heOrder.net_margin,
        heOrder.discount_margin,
        heOrder.discount_details,
        heOrder.approval_discount_status,
        heOrder.is_notice,
        heOrder.order_tag,
        heOrder.order_tag_name,
        heOrder.cert_type,
        heOrder.id_card,
        heOrder.id_card_front,
        heOrder.id_card_back,
        heOrder.sbra_achievement_type,
        heOrder.appointment_second_type,
        heOrder.old_or_new,
        heOrder.order_status,
        heOrder.refund_status,
        heOrder.sign_type,
        heOrder.version,
        heOrder.is_prepare_pregnancy,
        heOrder.full_refund_date,
        heOrder.is_previously_fully_paid,
        heOrder.extra_info,
        heOrder.fx_rate,
        heOrder.scene,
        heOrder.create_by,
        heOrder.gift_amount,
        heOrder.pay_finish_at
        FROM
        he_order heOrder
        left join he_order_gift_extend gift on heOrder.order_id = gift.order_id
        <where>
            heOrder.is_delete = false
            <if test="query.orderType != null and query.orderType != -1 ">
                and heOrder.order_type = #{query.orderType}
            </if>
            <if test="query.storeIdList != null and query.storeIdList.size() > 0">
                and
                heOrder.store_id IN
                <foreach collection="query.storeIdList" item="storeId" separator="," open="(" close=")">
                    #{storeId}
                </foreach>
            </if>
            <if test="query.orderIdList != null and query.orderIdList.size() > 0">
                and
                heOrder.order_id IN
                <foreach collection="query.orderIdList" item="orderId" separator="," open="(" close=")">
                    #{orderId}
                </foreach>
            </if>

            <if test="payTimeStart != 0 and payTimeEnd != 0 ">
                and heOrder.pay_finish_at >= #{payTimeStart}
                and heOrder.pay_finish_at &lt; #{payTimeEnd}
            </if>

            <if test="query.payTimeStart != null and query.payTimeEnd != null ">
                or (gift.gmt_create >= #{query.payTimeStart}
                and gift.gmt_create &lt; #{query.payTimeEnd}
                and gift.type = 6
                and gift.status != 0)
            </if>
        </where>
        order by heOrder.order_id desc
    </select>

    <select id="queryByClientIdsAndAssetType" resultType="Integer">
        SELECT DISTINCT
        he_order.order_id
        FROM
        he_order
        LEFT JOIN he_order_goods ON he_order.order_id = he_order_goods.order_id
        WHERE
        he_order.is_delete = 0
          AND he_order.order_type IN (0,1)
        <if test="orderSn != null">
            AND he_order.order_sn = #{orderSn}
        </if>
        AND he_order_goods.is_delete = 0
        <if test="storeId != null">
            AND he_order.store_id = #{storeId}
        </if>
<!--        AND he_order_goods.goods_type IN-->
<!--        <foreach collection="assetType" item="asset" separator="," open="(" close=")">-->
<!--            #{asset}-->
<!--        </foreach>-->
        <if test="clientIdList != null and clientIdList.size() > 0">
            AND he_order.client_uid IN
            <foreach collection="clientIdList" item="clientId" separator="," open="(" close=")">
                #{clientId}
            </foreach>
        </if>
        ORDER BY
        he_order.client_uid,
        he_order.order_sn
        limit 30
    </select>


    <select id="listOldOrders2Trans" resultType="com.stbella.order.infrastructure.repository.po.HeOrderPO">
        SELECT
        ho.*
        FROM
        he_order ho
        where order_sn in ('HEM742633223306031','HEN743660114201533','HEN743662122008453','HEN743662353483835','HEN743665023036291','HEN743665189815387','HEN743665574321114','HEN743670568522123','HEN743671264110838','HEN743671366124809','NHEM202402031557129755943','NHEM202406061745059337047','NHEM202406091901360475100','NHEM202406131808114000599','NHEM202407071940147509399','NHEM202407141221047177081','NHEM202407231308068715931','NHEM202407281728195559796','NHEM202407291707507219843','NHEM202407301722429841437','NHEM202407302111069899419','NHEM202407311856418862544','NHEM202408021358357869740','NHEM202408031056133013738','NHEM202408101301110708285','NHEM202408101541208507555','NHEM202408102206287555827','NHEM202408111924509850022','NHEM202408161852170825839','NHEM202408181749586994556','NHEM202408191500147950212','NHEM202408221526540586336','NHEM202408222123052083465','NHEM202408251425205688496','NHEM202408251602153230869','NHEM202408251948384009046','NHEM202408262122155454839','NHEM202408271105268019744','NHEM202408281224224047055','NHEM202408281706200740865','NHEM202408291412104425540','NHEM202408302056591835010','NHEM202408311328270665337','NHEM202408311519085908701','NHEM202408311734482239435','NHEM202409010950579010172','NHEM202409012046535631674','NHEM202409021259190557479','NHEM202409041607511163545','NHEM202409061730182212495','NHEM202409071224494736281','NHEM202409071900365473718','NHEM202409081724457366164','NHEM202409081818542266001','NHEM202409101803338904958','NHEM202409132037260138711','NHEM202409141750488905858','NHEM202409171307046523182','NHEM202409171539570554490','NHEM202409181158293694600','NHEM202409191208441148908','NHEM202409191429237012181','NHEM202409221010439426092','NHEM202409221152542089920','NHEM202409221515246309845','NHEM202409221545180916019','NHEM202409221548046540654','NHEM202409221738393179526','NHEM202409222037470665045','NHEM202409231152155497931','NHEM202409232040279629381','NHEM202409251236569156650','NHEM202409251650323273922','NHEM202409251929593494370','NHEM202409281141489410394','NHEM202409281544393847859','NHEM202409281646377566542','NHEM202409281655097216395','NHEM202409281706387799641','NHEM202409290018490010266','NHEM202409291655174402100','NHEM202409291712082244634','NHEM202409292032500279922','NHEM202409292136127120663','NHEM202409301333441841542','NHEM202409301833078365710','NHEM202410011254588621894','NHEM202410021411275663506','NHEM202410021758088188069','NHEM202410031157103580938','NHEM202410031355322917295','NHEM202410041034448429986','NHEM202410051054380490360','NHEM202410051455306854202','NHEM202410051556136354272','NHEM202410051616039449478','NHEM202410051907436998639','NHEM202410071746567671692','NHEM202410081408489922297','NHEM202410081422427154441','NHEM202410091217440577547','NHEM202410131449257829064','NHEM202410131749230586135','NHEM202410131759435680348','NHEM202410141124097129067','NHEM202410151017227193195','NHEM202410151044128343270','NHEM202410151506530437413','NHEM202410151647116176369','NHEM202410172046363540776','NHEM202410191427253742034','NHEM202410191641303221769','NHEM202410191826083136625','NHEM202410191847562452184571906','NHEM202410201226471779098','NHEM202410201407200177282','NHEM202410201802239531869','NHEM202410202120371825678','NHEM202410202237344925333','NHEM202410221353392919242','NHEM202410231558585034876','NHEM202410232058358595669','NHEM202410250943349133543','NHEM202410251543356216372','NHEM202410251734081131310','NHEM202410251933308015105','NHEM202410261328010807040','NHEM202410271514094662556','NHEM202410271835545615642','NHEM202410271906311912360','NHEM202410281642441761036','NHEM202410281850848185155633153','NHEM202410291348165710677','NHEM202410291700171710857','NHEM202410301515118534441','NHEM202410301851606089466699778','NHEM202410311142393808135','NHEM202410311604199846469','NHEM202410311851824090182647810','NHEM202410311851859066873774081','NHEM202410311851919668774494209','NHEM202410311938048721416','NHEM202411021239234905381','NHEM202411021542336214178','NHEM202411021944095643124','NHEM202411031442510060053','NHEM202411041615245212830','NHEM202411041643236557812','NHEM202411051325588418937','NHEM202411051330400400641','NHEM202411061821438590354','NHEM202411061854118907662413825','NHEM202411081854731756279246850','NHEM202411091855195738957844481','NHEM202411091855251217637609474','NHEM202411101855498098867343361','NHEM202411171857993402971500545','NHEM202411201859105343959248897','NHEM202411211859531604831821825','NHEM202411211859619887658905602','NHEM202411231860277883724697602','NHEM202411261861291355030740994','NHEM202411301862705547642200065','NHEM202411301862873404160860162','NHEM202412011863139419852955650','NHEM202412041864221952755412994','NHEM202412041864262572841054209','NHEM202412051864644173487452161','NHEM202412141867788613489119233','NHEM202412161868634504903499778','NHEM202412171869008981401460737','NHEM202412181869272060959121409','NHEM202412231870942823264940033','NHEM202412241871540052793135106','NHEM202412281872839379182919682','NHEM202412291873332573196754945','NHEM202412291873354951146450946','NHEM202412301873722979121999874','NHEM202412311873983982007197698','NHEM202412311874000605778780161','NHEM202501031875090314730270721','NHEM202501041875436601382858753','NHEM202501071876613039398121473','NHEM202501091877168245210935298','NHEM202501111877964683034312705','NHEM202501171880089889503903745','NHEM202501191880856458291367937','NHEM202501201881224272114823169','NHEM202501201881311198893129729','NHEM202501211881669722244591618','NHEM202501221881937051741003777','NHEM202502041886732680326201345','NHEM202502151890629618218086401','NHEM202502211892926617893920770','NHEM202502231893526008468004866','NHEM202502241893945302779871233','NHEM202502281895466826798903298','NHEM202503011895708266917441538','NHEM202503111899417299075260417','NHEM202503211902930489303429122','NHEM202503231903684767656099842','NHEM202503241904037835888640002','NHEM202503251904424785168506882','NHEM202504021907273823910330369','NHEM202504061908773902741975041','NHEM202504181913052323772936193','NHEM202504201913902402689892354','NHEM202504261916094949894184961','NHEM202505051919235218336821250','NHEM202505161923251050254663681','NHEM202505191924378493522518017','NHEM202505241926258545660858369','NHEM202505281927573068678111233') and version=1.00 and pay_status=2;

    </select>
    <select id="getOrderNotProofPayRecordList"
            resultType="com.stbella.order.infrastructure.repository.po.HeIncomeRecordPO">
        SELECT
        he_order.store_id as storeId,
        he_order.staff_id as staffId,
        he_order.client_uid as clientUid,
        he_income_record.`income` as income
        FROM
        `he_order` he_order
        LEFT JOIN `he_income_record` he_income_record on he_order.order_id = he_income_record.order_id
        WHERE he_order.`order_type` in (110, 120) and he_income_record.pay_type not in (3, 102)
        AND he_income_record.`created_at` BETWEEN #{startTime} AND #{endTime} and he_income_record.`status` = 1
    </select>

    <select id="getOrderProofPayRecordList"
            resultType="com.stbella.order.infrastructure.repository.po.HeIncomeProofRecordPO">
        SELECT
        proof_record.`income_proof` as incomeProof
        FROM
        `he_income_proof_record` proof_record
        LEFT JOIN `he_order` he_order on proof_record.order_id = he_order.order_id
        WHERE he_order.`order_type` in (110, 120) and proof_record.`status` in (0, 1)
        <if test="startTime != null and endTime != null">
            and proof_record.`created_at` between #{startTime} and #{endTime}
        </if>
    </select>

    <select id="getOrderRefundRecordList"
            resultType="com.stbella.order.infrastructure.repository.po.HeOrderRefundPO">

        SELECT
        order_refund.`apply_amount` as applyAmount
        FROM
        `he_order_refund` order_refund
        LEFT JOIN `he_order` he_order on order_refund.order_id = he_order.order_id
        WHERE he_order.`order_type` in (110, 120) and order_refund.`status` = 4 and order_refund.`parent_refund_order_sn` is not null
        and order_refund.`refund_type` != '102'
        <if test="startTime != null and endTime != null">
            and order_refund.`created_at` between #{startTime} and #{endTime}
        </if>
    </select>


</mapper>

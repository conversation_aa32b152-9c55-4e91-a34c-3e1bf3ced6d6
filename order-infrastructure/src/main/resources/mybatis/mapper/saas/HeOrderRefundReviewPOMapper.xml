<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeOrderRefundReviewMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeOrderRefundReviewPO">
        <result column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="order_refund_id" property="orderRefundId"/>
        <result column="order_id" property="orderId"/>
        <result column="refund_order_sn" property="refundOrderSn"/>
        <result column="project_id" property="projectId"/>
        <result column="task_id" property="taskId"/>
        <result column="op_uid" property="opUid"/>
        <result column="op_content" property="opContent"/>
        <result column="remark" property="remark"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        type, status, order_refund_id, order_id, refund_order_sn, project_id, task_id, op_uid, op_content, remark, created_at, updated_at
    </sql>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeUserProductionBackendLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeUserProductionBackendLogPO">
        <result column="id" property="id"/>
        <result column="basic_id" property="basicId"/>
        <result column="order_id" property="orderId"/>
        <result column="type" property="type"/>
        <result column="operate_content" property="operateContent"/>
        <result column="client_type" property="clientType"/>
        <result column="operator_id" property="operatorId"/>
        <result column="operator" property="operator"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        basic_id, order_id, type, operate_content, client_type, operator_id, operator, is_delete, created_at, updated_at
    </sql>

</mapper>

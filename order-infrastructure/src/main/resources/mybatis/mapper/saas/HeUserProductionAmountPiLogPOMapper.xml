<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeUserProductionAmountPiLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeUserProductionAmountPiLogPO">
        <result column="id" property="id"/>
        <result column="basic_id" property="basicId"/>
        <result column="production_id" property="productionId"/>
        <result column="type" property="type"/>
        <result column="order_id" property="orderId"/>
        <result column="order_name" property="orderName"/>
        <result column="title" property="title"/>
        <result column="operate_amount" property="operateAmount"/>
        <result column="detail" property="detail"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        basic_id, production_id, type, order_id, order_name, title, operate_amount, detail, is_delete, created_at, updated_at
    </sql>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeOrderApproveRecordOperationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeOrderApproveRecordOperationPO">
        <result column="id" property="id"/>
        <result column="record_id" property="recordId"/>
        <result column="user_name" property="userName"/>
        <result column="user_id" property="userId"/>
        <result column="date" property="date"/>
        <result column="type" property="type"/>
        <result column="result" property="result"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        record_id, user_name, user_id, date, type, result, remark
    </sql>

</mapper>

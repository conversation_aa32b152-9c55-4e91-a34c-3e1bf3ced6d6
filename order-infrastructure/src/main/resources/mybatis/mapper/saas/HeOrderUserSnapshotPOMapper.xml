<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeOrderUserSnapshotMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeOrderUserSnapshotPO">
        <result column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="deleted" property="deleted"/>
        <result column="order_id" property="orderId"/>
        <result column="client_uid" property="clientUid"/>
        <result column="basic_uid" property="basicUid"/>
        <result column="store_id" property="storeId"/>
        <result column="predict_born_date" property="predictBornDate"/>
        <result column="born_num" property="bornNum"/>
        <result column="fetus_num" property="fetusNum"/>
        <result column="hospital" property="hospital"/>
        <result column="seller_id" property="sellerId"/>
        <result column="seller_name" property="sellerName"/>
        <result column="remark" property="remark"/>
        <result column="from_type" property="fromType"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="region" property="region"/>
        <result column="address" property="address"/>
        <result column="urgent_name" property="urgentName"/>
        <result column="urgent_phone" property="urgentPhone" typeHandler="com.stbella.mybatis.typeHandler.EncryptAndDencryptHandler"/>
        <result column="relation_with_client" property="relationWithClient"/>
        <result column="constellation_type" property="constellationType"/>
        <result column="constellation" property="constellation"/>
        <result column="profession" property="profession"/>
        <result column="age" property="age"/>
        <result column="blood_type" property="bloodType"/>
        <result column="gestation_week_now" property="gestationWeekNow"/>
        <result column="qr_code" property="qrCode"/>
        <result column="name" property="name"/>
        <result column="phone" property="phone" typeHandler="com.stbella.mybatis.typeHandler.EncryptAndDencryptHandler"/>
        <result column="phone_type" property="phoneType"/>
        <result column="cert_type" property="certType"/>
        <result column="id_card" property="idCard" typeHandler="com.stbella.mybatis.typeHandler.EncryptAndDencryptHandler"/>
        <result column="id_card_front" property="idCardFront" typeHandler="com.stbella.mybatis.typeHandler.EncryptAndDencryptHandler"/>
        <result column="id_card_back" property="idCardBack" typeHandler="com.stbella.mybatis.typeHandler.EncryptAndDencryptHandler"/>
        <result column="want_in" property="wantIn"/>
        <result column="is_card_verify" property="isCardVerify"/>
        <result column="is_phone_verify" property="isPhoneVerify"/>
        <result column="phone_ciphertext" property="phoneCiphertext" />
        <result column="urgent_phone_ciphertext"  property="urgentPhoneCiphertext"  />
        <result column="id_card_front_ciphertext"  property="idCardFrontCiphertext"  />
        <result column="id_card_back_ciphertext" property="idCardBackCiphertext"  />
        <result column="id_card_ciphertext" property="idCardCiphertext"  />
    </resultMap>

    <select id="queryByPhoneForTest" resultMap="BaseResultMap">

        SELECT * from he_order_user_snapshot where phone = #{phone,typeHandler=com.stbella.mybatis.typeHandler.EncryptAndDencryptHandler}


    </select>


</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeOrderRefundGoodsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeOrderRefundGoods">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="refund_order_sn" property="refundOrderSn"/>
        <result column="order_goods_sn" property="orderGoodsSn"/>
        <result column="sku_id" property="skuId"/>
        <result column="sku_name" property="skuName"/>
        <result column="goods_id" property="goodsId"/>
        <result column="goods_name" property="goodsName"/>
        <result column="goods_image" property="goodsImage"/>
        <result column="refund_num" property="refundNum"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="status" property="status"/>
        <result column="refund_type" property="refundType"/>
        <result column="refund_nature" property="refundNature"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="goods_type" property="goodsType"/>
        <result column="goods_price_orgin" property="goodsPriceOrgin"/>
        <result column="goods_price_pay" property="goodsPricePay"/>
        <result column="gift" property="gift"/>
        <result column="parent_combine_sn" property="parentCombineSn"/>
        <result column="order_goods_id" property="orderGoodsId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        goods_price_orgin,goods_price_pay,gift, order_id, refund_order_sn, order_goods_sn, sku_id, sku_name, goods_id, goods_name, goods_image, refund_num, refund_amount, refund_type, refund_nature, deleted, gmt_create, gmt_modified,status,goods_type, parent_combine_sn,order_goods_id
    </sql>

</mapper>

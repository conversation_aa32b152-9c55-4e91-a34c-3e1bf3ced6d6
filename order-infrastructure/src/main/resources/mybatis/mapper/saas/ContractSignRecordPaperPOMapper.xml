<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.ContractSignRecordPaperMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.ContractSignRecordPaperPO">
        <result column="id" property="id"/>
        <result column="client_uid" property="clientUid"/>
        <result column="staff_id" property="staffId"/>
        <result column="store_id" property="storeId"/>
        <result column="order_id" property="orderId"/>
        <result column="contract_name" property="contractName"/>
        <result column="code" property="code"/>
        <result column="contract_status" property="contractStatus"/>
        <result column="img" property="img"/>
        <result column="contract_type" property="contractType"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="reason" property="reason"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        client_uid, staff_id, store_id, order_id, contract_name, code, contract_status, img, contract_type, is_delete, created_at, updated_at, reason
    </sql>

</mapper>

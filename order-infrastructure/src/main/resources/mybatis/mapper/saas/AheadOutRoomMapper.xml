<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.AheadOutRoomMapper">
  <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.AheadOutRoomPO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="check_out_date" jdbcType="TIMESTAMP" property="checkOutDate" />
    <result column="check_out_days" jdbcType="INTEGER" property="checkOutDays" />
    <result column="basic_uid" jdbcType="INTEGER" property="basicUid" />
    <result column="client_uid" jdbcType="INTEGER" property="clientUid" />
    <result column="client_name" jdbcType="VARCHAR" property="clientName" />
    <result column="client_type" jdbcType="BIT" property="clientType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="original_amount" jdbcType="INTEGER" property="originalAmount" />
    <result column="babys_amount" jdbcType="INTEGER" property="babysAmount" />
    <result column="holiday_amount" jdbcType="INTEGER" property="holidayAmount" />
    <result column="refunded_amount" jdbcType="INTEGER" property="refundedAmount" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_id, check_out_date, check_out_days, basic_uid, client_uid, client_name, 
    client_type, remark, original_amount, babys_amount, holiday_amount, refunded_amount, state,
    gmt_create, gmt_modified, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from he_ahead_out_room
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from he_ahead_out_room
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertOne" parameterType="com.stbella.order.infrastructure.repository.po.AheadOutRoomPO" useGeneratedKeys="true" keyProperty="id">
    insert into he_ahead_out_room (id, order_id, check_out_date, 
      check_out_days, basic_uid, client_uid, 
      client_name, client_type, remark, 
      original_amount, babys_amount, holiday_amount, 
      refunded_amount,state, gmt_create, gmt_modified,
      deleted)
    values (#{id,jdbcType=INTEGER}, #{orderId,jdbcType=INTEGER}, #{checkOutDate,jdbcType=TIMESTAMP}, 
      #{checkOutDays,jdbcType=INTEGER}, #{basicUid,jdbcType=INTEGER}, #{clientUid,jdbcType=INTEGER}, 
      #{clientName,jdbcType=VARCHAR}, #{clientType,jdbcType=BIT}, #{remark,jdbcType=VARCHAR}, 
      #{originalAmount,jdbcType=INTEGER}, #{babysAmount,jdbcType=INTEGER}, #{holidayAmount,jdbcType=INTEGER}, 
      #{refundedAmount,jdbcType=INTEGER},#{state,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP},
      #{deleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.stbella.order.infrastructure.repository.po.AheadOutRoomPO" useGeneratedKeys="true" keyProperty="id">
    insert into he_ahead_out_room
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="checkOutDate != null">
        check_out_date,
      </if>
      <if test="checkOutDays != null">
        check_out_days,
      </if>
      <if test="basicUid != null">
        basic_uid,
      </if>
      <if test="clientUid != null">
        client_uid,
      </if>
      <if test="clientName != null">
        client_name,
      </if>
      <if test="clientType != null">
        client_type,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="originalAmount != null">
        original_amount,
      </if>
      <if test="babysAmount != null">
        babys_amount,
      </if>
      <if test="holidayAmount != null">
        holiday_amount,
      </if>
      <if test="refundedAmount != null">
        refunded_amount,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="checkOutDate != null">
        #{checkOutDate,jdbcType=TIMESTAMP},
      </if>
      <if test="checkOutDays != null">
        #{checkOutDays,jdbcType=INTEGER},
      </if>
      <if test="basicUid != null">
        #{basicUid,jdbcType=INTEGER},
      </if>
      <if test="clientUid != null">
        #{clientUid,jdbcType=INTEGER},
      </if>
      <if test="clientName != null">
        #{clientName,jdbcType=VARCHAR},
      </if>
      <if test="clientType != null">
        #{clientType,jdbcType=BIT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="originalAmount != null">
        #{originalAmount,jdbcType=INTEGER},
      </if>
      <if test="babysAmount != null">
        #{babysAmount,jdbcType=INTEGER},
      </if>
      <if test="holidayAmount != null">
        #{holidayAmount,jdbcType=INTEGER},
      </if>
      <if test="refundedAmount != null">
        #{refundedAmount,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.stbella.order.infrastructure.repository.po.AheadOutRoomPO">
    update he_ahead_out_room
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="checkOutDate != null">
        check_out_date = #{checkOutDate,jdbcType=TIMESTAMP},
      </if>
      <if test="checkOutDays != null">
        check_out_days = #{checkOutDays,jdbcType=INTEGER},
      </if>
      <if test="basicUid != null">
        basic_uid = #{basicUid,jdbcType=INTEGER},
      </if>
      <if test="clientUid != null">
        client_uid = #{clientUid,jdbcType=INTEGER},
      </if>
      <if test="clientName != null">
        client_name = #{clientName,jdbcType=VARCHAR},
      </if>
      <if test="clientType != null">
        client_type = #{clientType,jdbcType=BIT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="originalAmount != null">
        original_amount = #{originalAmount,jdbcType=INTEGER},
      </if>
      <if test="babysAmount != null">
        babys_amount = #{babysAmount,jdbcType=INTEGER},
      </if>
      <if test="holidayAmount != null">
        holiday_amount = #{holidayAmount,jdbcType=INTEGER},
      </if>
      <if test="refundedAmount != null">
        refunded_amount = #{refundedAmount,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.stbella.order.infrastructure.repository.po.AheadOutRoomPO">
    update he_ahead_out_room
    set order_id = #{orderId,jdbcType=INTEGER},
      check_out_date = #{checkOutDate,jdbcType=TIMESTAMP},
      check_out_days = #{checkOutDays,jdbcType=INTEGER},
      basic_uid = #{basicUid,jdbcType=INTEGER},
      client_uid = #{clientUid,jdbcType=INTEGER},
      client_name = #{clientName,jdbcType=VARCHAR},
      client_type = #{clientType,jdbcType=BIT},
      remark = #{remark,jdbcType=VARCHAR},
      original_amount = #{originalAmount,jdbcType=INTEGER},
      babys_amount = #{babysAmount,jdbcType=INTEGER},
      holiday_amount = #{holidayAmount,jdbcType=INTEGER},
      refunded_amount = #{refundedAmount,jdbcType=INTEGER},
      state = #{state,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>
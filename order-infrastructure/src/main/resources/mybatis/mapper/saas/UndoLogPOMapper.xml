<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.UndoLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.UndoLogPO">
        <id column="branch_id" property="branchId"/>
        <result column="xid" property="xid"/>
        <result column="context" property="context"/>
        <result column="rollback_info" property="rollbackInfo"/>
        <result column="log_status" property="logStatus"/>
        <result column="log_created" property="logCreated"/>
        <result column="log_modified" property="logModified"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        branch_id
        , xid, context, rollback_info, log_status, log_created, log_modified
    </sql>

</mapper>

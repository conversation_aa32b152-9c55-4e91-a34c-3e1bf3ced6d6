<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.MonthOrderParamHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.MonthOrderParamHistoryPO">
        <id column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="order_id" property="orderId"/>
        <result column="name" property="name"/>
        <result column="param_value" property="paramValue"/>
        <result column="mark" property="mark"/>
        <result column="terms" property="terms"/>
        <result column="item" property="item"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        deleted,
        gmt_create,
        gmt_modified,
        id, order_id,  name, param_value, mark, terms, item
    </sql>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeOrderAdditionalRevenueMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeOrderAdditionalRevenuePO">
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="goods_name" property="goodsName"/>
        <result column="store_id" property="storeId"/>
        <result column="order_id" property="orderId"/>
        <result column="type" property="type"/>
        <result column="price" property="price"/>
        <result column="cost" property="cost"/>
        <result column="cost_price" property="costPrice"/>
        <result column="content" property="content"/>
        <result column="source" property="source"/>
        <result column="room_id" property="roomId"/>
        <result column="room_name" property="roomName"/>
        <result column="days" property="days"/>
        <result column="days_list" property="daysList"/>
        <result column="embryo_number" property="embryoNumber"/>
    </resultMap>


</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.OrderGiftMapper">
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.OrderGift">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="og_id" jdbcType="INTEGER" property="ogId"/>
        <result column="order_id" jdbcType="INTEGER" property="orderId"/>
        <result column="client_uid" jdbcType="INTEGER" property="clientUid"/>
        <result column="staff_id" jdbcType="INTEGER" property="staffId"/>
        <result column="store_id" jdbcType="INTEGER" property="storeId"/>
        <result column="goods_type" jdbcType="BIT" property="goodsType"/>
        <result column="goods_id" jdbcType="INTEGER" property="goodsId"/>
        <result column="goods_price" jdbcType="INTEGER" property="goodsPrice"/>
        <result column="goods_num" jdbcType="INTEGER" property="goodsNum"/>
        <result column="goods_amount" jdbcType="INTEGER" property="goodsAmount"/>
        <result column="used_goods_num" jdbcType="INTEGER" property="usedGoodsNum"/>
        <result column="is_delete" jdbcType="BIT" property="isDelete"/>
        <result column="created_at" jdbcType="BIGINT" property="createdAt"/>
        <result column="updated_at" jdbcType="BIGINT" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , og_id, order_id, client_uid, staff_id, store_id, goods_type, goods_id, goods_price,
    goods_num, goods_amount, used_goods_num, is_delete, created_at, updated_at
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from he_order_gift
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from he_order_gift
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.stbella.order.infrastructure.repository.po.OrderGift">
        insert into he_order_gift (id, og_id, order_id,
                                   client_uid, staff_id, store_id,
                                   goods_type, goods_id, goods_price,
                                   goods_num, goods_amount, used_goods_num,
                                   is_delete, created_at, updated_at)
        values (#{id,jdbcType=INTEGER}, #{ogId,jdbcType=INTEGER}, #{orderId,jdbcType=INTEGER},
                #{clientUid,jdbcType=INTEGER}, #{staffId,jdbcType=INTEGER}, #{storeId,jdbcType=INTEGER},
                #{goodsType,jdbcType=BIT}, #{goodsId,jdbcType=INTEGER}, #{goodsPrice,jdbcType=INTEGER},
                #{goodsNum,jdbcType=INTEGER}, #{goodsAmount,jdbcType=INTEGER}, #{usedGoodsNum,jdbcType=INTEGER},
                #{isDelete,jdbcType=BIT}, #{createdAt,jdbcType=BIGINT}, #{updatedAt,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" parameterType="com.stbella.order.infrastructure.repository.po.OrderGift">
        insert into he_order_gift
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="ogId != null">
                og_id,
            </if>
            <if test="orderId != null">
                order_id,
            </if>
            <if test="clientUid != null">
                client_uid,
            </if>
            <if test="staffId != null">
                staff_id,
            </if>
            <if test="storeId != null">
                store_id,
            </if>
            <if test="goodsType != null">
                goods_type,
            </if>
            <if test="goodsId != null">
                goods_id,
            </if>
            <if test="goodsPrice != null">
                goods_price,
            </if>
            <if test="goodsNum != null">
                goods_num,
            </if>
            <if test="goodsAmount != null">
                goods_amount,
            </if>
            <if test="usedGoodsNum != null">
                used_goods_num,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="createdAt != null">
                created_at,
            </if>
            <if test="updatedAt != null">
                updated_at,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="ogId != null">
                #{ogId,jdbcType=INTEGER},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=INTEGER},
            </if>
            <if test="clientUid != null">
                #{clientUid,jdbcType=INTEGER},
            </if>
            <if test="staffId != null">
                #{staffId,jdbcType=INTEGER},
            </if>
            <if test="storeId != null">
                #{storeId,jdbcType=INTEGER},
            </if>
            <if test="goodsType != null">
                #{goodsType,jdbcType=BIT},
            </if>
            <if test="goodsId != null">
                #{goodsId,jdbcType=INTEGER},
            </if>
            <if test="goodsPrice != null">
                #{goodsPrice,jdbcType=INTEGER},
            </if>
            <if test="goodsNum != null">
                #{goodsNum,jdbcType=INTEGER},
            </if>
            <if test="goodsAmount != null">
                #{goodsAmount,jdbcType=INTEGER},
            </if>
            <if test="usedGoodsNum != null">
                #{usedGoodsNum,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=BIT},
            </if>
            <if test="createdAt != null">
                #{createdAt,jdbcType=BIGINT},
            </if>
            <if test="updatedAt != null">
                #{updatedAt,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.stbella.order.infrastructure.repository.po.OrderGift">
        update he_order_gift
        <set>
            <if test="ogId != null">
                og_id = #{ogId,jdbcType=INTEGER},
            </if>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=INTEGER},
            </if>
            <if test="clientUid != null">
                client_uid = #{clientUid,jdbcType=INTEGER},
            </if>
            <if test="staffId != null">
                staff_id = #{staffId,jdbcType=INTEGER},
            </if>
            <if test="storeId != null">
                store_id = #{storeId,jdbcType=INTEGER},
            </if>
            <if test="goodsType != null">
                goods_type = #{goodsType,jdbcType=BIT},
            </if>
            <if test="goodsId != null">
                goods_id = #{goodsId,jdbcType=INTEGER},
            </if>
            <if test="goodsPrice != null">
                goods_price = #{goodsPrice,jdbcType=INTEGER},
            </if>
            <if test="goodsNum != null">
                goods_num = #{goodsNum,jdbcType=INTEGER},
            </if>
            <if test="goodsAmount != null">
                goods_amount = #{goodsAmount,jdbcType=INTEGER},
            </if>
            <if test="usedGoodsNum != null">
                used_goods_num = #{usedGoodsNum,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=BIT},
            </if>
            <if test="createdAt != null">
                created_at = #{createdAt,jdbcType=BIGINT},
            </if>
            <if test="updatedAt != null">
                updated_at = #{updatedAt,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.stbella.order.infrastructure.repository.po.OrderGift">
        update he_order_gift
        set og_id          = #{ogId,jdbcType=INTEGER},
            order_id       = #{orderId,jdbcType=INTEGER},
            client_uid     = #{clientUid,jdbcType=INTEGER},
            staff_id       = #{staffId,jdbcType=INTEGER},
            store_id       = #{storeId,jdbcType=INTEGER},
            goods_type     = #{goodsType,jdbcType=BIT},
            goods_id       = #{goodsId,jdbcType=INTEGER},
            goods_price    = #{goodsPrice,jdbcType=INTEGER},
            goods_num      = #{goodsNum,jdbcType=INTEGER},
            goods_amount   = #{goodsAmount,jdbcType=INTEGER},
            used_goods_num = #{usedGoodsNum,jdbcType=INTEGER},
            is_delete      = #{isDelete,jdbcType=BIT},
            created_at     = #{createdAt,jdbcType=BIGINT},
            updated_at     = #{updatedAt,jdbcType=BIGINT}
        where id = #{id,jdbcType=INTEGER}
    </update>
</mapper>
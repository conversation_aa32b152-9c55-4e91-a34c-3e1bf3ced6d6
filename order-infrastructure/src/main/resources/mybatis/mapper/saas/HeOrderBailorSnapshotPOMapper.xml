<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeOrderBailorSnapshotMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeOrderBailorSnapshotPO">
        <result column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="deleted" property="deleted"/>
        <result column="client_uid" property="clientUid"/>
        <result column="bailor_id" property="bailorId"/>
        <result column="name" property="name"/>
        <result column="phone_type" property="phoneType"/>
        <result column="phone" property="phone"/>
        <result column="cert_type" property="certType"/>
        <result column="id_card" property="idCard"/>
        <result column="id_card_front" property="idCardFront"/>
        <result column="id_card_back" property="idCardBack"/>
        <result column="is_card_verify" property="isCardVerify"/>
        <result column="is_phone_verify" property="isPhoneVerify"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        gmt_create,
        gmt_modified,
        deleted,
        client_uid, bailor_id, name, phone_type, phone, cert_type, id_card, id_card_front, id_card_back, is_card_verify, is_phone_verify
    </sql>

</mapper>

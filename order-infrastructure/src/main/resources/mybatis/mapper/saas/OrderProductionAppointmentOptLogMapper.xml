<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.OrderProductionAppointmentOptLogMapper">
    <resultMap id="BaseResultMap"
               type="com.stbella.order.infrastructure.repository.po.OrderProductionAppointmentOptLog">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_id" jdbcType="INTEGER" property="orderId"/>
        <result column="order_sn" jdbcType="VARCHAR" property="orderSn"/>
        <result column="opt_type" jdbcType="TINYINT" property="optType"/>
        <result column="appointment_id" jdbcType="INTEGER" property="appointmentId"/>
        <result column="opt_name" jdbcType="VARCHAR" property="optName"/>
        <result column="opt_id" jdbcType="INTEGER" property="optId"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="original_content" jdbcType="VARCHAR" property="originalContent"/>
        <result column="opt_content" jdbcType="VARCHAR" property="optContent"/>
        <result column="source" jdbcType="VARCHAR" property="source"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , order_id, order_sn, opt_type, appointment_id, opt_name, opt_id, deleted, gmt_create,
    gmt_modified, original_content, opt_content, source
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from he_order_production_appointment_opt_log
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from he_order_production_appointment_opt_log
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.stbella.order.infrastructure.repository.po.OrderProductionAppointmentOptLog">
        insert into he_order_production_appointment_opt_log (id, order_id, order_sn,
                                                             opt_type, appointment_id, opt_name,
                                                             opt_id, deleted, gmt_create,
                                                             gmt_modified, original_content, opt_content)
        values (#{id,jdbcType=BIGINT}, #{orderId,jdbcType=INTEGER}, #{orderSn,jdbcType=VARCHAR},
                #{optType,jdbcType=TINYINT}, #{appointmentId,jdbcType=INTEGER}, #{optName,jdbcType=VARCHAR},
                #{optId,jdbcType=INTEGER}, #{deleted,jdbcType=BIT}, #{gmtCreate,jdbcType=TIMESTAMP},
                #{gmtModified,jdbcType=TIMESTAMP}, #{originalContent,jdbcType=VARCHAR}, #{optContent,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective"
            parameterType="com.stbella.order.infrastructure.repository.po.OrderProductionAppointmentOptLog">
        insert into he_order_production_appointment_opt_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="orderId != null">
                order_id,
            </if>
            <if test="orderSn != null">
                order_sn,
            </if>
            <if test="optType != null">
                opt_type,
            </if>
            <if test="appointmentId != null">
                appointment_id,
            </if>
            <if test="optName != null">
                opt_name,
            </if>
            <if test="optId != null">
                opt_id,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="originalContent != null">
                original_content,
            </if>
            <if test="optContent != null">
                opt_content,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=INTEGER},
            </if>
            <if test="orderSn != null">
                #{orderSn,jdbcType=VARCHAR},
            </if>
            <if test="optType != null">
                #{optType,jdbcType=TINYINT},
            </if>
            <if test="appointmentId != null">
                #{appointmentId,jdbcType=INTEGER},
            </if>
            <if test="optName != null">
                #{optName,jdbcType=VARCHAR},
            </if>
            <if test="optId != null">
                #{optId,jdbcType=INTEGER},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BIT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="originalContent != null">
                #{originalContent,jdbcType=VARCHAR},
            </if>
            <if test="optContent != null">
                #{optContent,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.stbella.order.infrastructure.repository.po.OrderProductionAppointmentOptLog">
        update he_order_production_appointment_opt_log
        <set>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=INTEGER},
            </if>
            <if test="orderSn != null">
                order_sn = #{orderSn,jdbcType=VARCHAR},
            </if>
            <if test="optType != null">
                opt_type = #{optType,jdbcType=TINYINT},
            </if>
            <if test="appointmentId != null">
                appointment_id = #{appointmentId,jdbcType=INTEGER},
            </if>
            <if test="optName != null">
                opt_name = #{optName,jdbcType=VARCHAR},
            </if>
            <if test="optId != null">
                opt_id = #{optId,jdbcType=INTEGER},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="originalContent != null">
                original_content = #{originalContent,jdbcType=VARCHAR},
            </if>
            <if test="optContent != null">
                opt_content = #{optContent,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.stbella.order.infrastructure.repository.po.OrderProductionAppointmentOptLog">
        update he_order_production_appointment_opt_log
        set order_id         = #{orderId,jdbcType=INTEGER},
            order_sn         = #{orderSn,jdbcType=VARCHAR},
            opt_type         = #{optType,jdbcType=TINYINT},
            appointment_id   = #{appointmentId,jdbcType=INTEGER},
            opt_name         = #{optName,jdbcType=VARCHAR},
            opt_id           = #{optId,jdbcType=INTEGER},
            deleted          = #{deleted,jdbcType=BIT},
            gmt_create       = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_modified     = #{gmtModified,jdbcType=TIMESTAMP},
            original_content = #{originalContent,jdbcType=VARCHAR},
            opt_content      = #{optContent,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
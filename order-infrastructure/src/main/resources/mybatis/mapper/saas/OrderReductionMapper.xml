<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.OrderReductionMapper">
  <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.OrderReductionPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId"/>
    <result column="basic_id" jdbcType="BIGINT" property="basicId"/>
    <result column="decrease_amount" jdbcType="BIGINT" property="decreaseAmount"/>
    <result column="decrease_date" jdbcType="TIMESTAMP" property="decreaseDate"/>
    <result column="local_process_id" jdbcType="VARCHAR" property="localProcessId"/>
    <result column="auth_state" jdbcType="INTEGER" property="authState"/>
    <result column="operator" jdbcType="VARCHAR" property="operator"/>
    <result column="operator_id" jdbcType="VARCHAR" property="operatorId"/>
    <result column="reason" jdbcType="VARCHAR" property="reason"/>
    <result column="ext" jdbcType="VARCHAR" property="ext"/>
    <result column="deleted" jdbcType="INTEGER" property="deleted"/>
    <result column="gmt_create" property="gmtCreate"/>
    <result column="gmt_modified" property="gmtModified"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, order_id, store_id, basic_id, decrease_amount, decrease_date,local_process_id, auth_state, operator,
    operator_id, reason, ext, deleted, gmt_create, gmt_modified
  </sql>

  <select id="reductionSum" resultType="Long"  >
    select IFNULL(sum(decrease_amount),0) as decrease_amount
    from `order_reduction` a left join he_order o on a.`order_id` = o.`order_id`
    where `is_delete` = '0' and a.deleted = 0 and auth_state = 2

    <if test="startPercentFirstTime != null">
      and `decrease_date` >= FROM_UNIXTIME(#{startPercentFirstTime})
    </if>
    <if test="endPercentFirstTime != null">
      and decrease_date &lt;= FROM_UNIXTIME(#{endPercentFirstTime})
    </if>
    <if test="paidAmount != null">
      and o.percent_first_time >= #{startPercentFirstTime}
      and   o.percent_first_time &lt;= #{endPercentFirstTime}
    </if>
    <if test="paidAmount != null">
      and ( `paid_amount` > 100 or `paid_amount` &lt; -100)
    </if>
    <if test="storeId != null">
      and  o.store_id = #{storeId}
    </if>
    <if test="orderTypeList != null  and orderTypeList.size() > 0">
      and `order_type` in
      <foreach collection="orderTypeList" item="item" index="i" separator="," open="(" close=")">
        #{item}
      </foreach>
    </if>
  </select>


</mapper>
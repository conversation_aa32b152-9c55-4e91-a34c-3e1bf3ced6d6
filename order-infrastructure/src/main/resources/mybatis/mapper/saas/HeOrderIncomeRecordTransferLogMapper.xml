<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeOrderIncomeRecordTransferLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeOrderIncomeRecordTransferLog">
        <id column="id" property="id"/>
        <result column="old_order_id" property="oldOrderId"/>
        <result column="new_order_id" property="newOrderId"/>
        <result column="body_log" property="bodyLog"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , old_order_id, new_order_id, body_log, gmt_create, gmt_modified
    </sql>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeOrderVoucherMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeOrderVoucherPO">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="og_id" property="ogId"/>
        <result column="url" property="url"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        order_id, og_id, url, is_delete, created_at, updated_at
    </sql>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeOrderApproveRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeOrderApproveRecordPO">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="dd_instance_id" property="ddInstanceId"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="title" property="title"/>
        <result column="create_time" property="createTime"/>
        <result column="finish_time" property="finishTime"/>
        <result column="originator_user_id" property="originatorUserId"/>
        <result column="originator_dept_id" property="originatorDeptId"/>
        <result column="originator_dept_name" property="originatorDeptName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        order_id, dd_instance_id, type, status, is_delete, created_at, updated_at, title, create_time, finish_time, originator_user_id, originator_dept_id, originator_dept_name
    </sql>

</mapper>

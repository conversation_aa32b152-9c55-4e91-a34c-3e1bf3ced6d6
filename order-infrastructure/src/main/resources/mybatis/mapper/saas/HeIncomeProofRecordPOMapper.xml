<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeIncomeProofRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.HeIncomeProofRecordPO">
        <result column="id" property="id"/>
        <result column="income_sn" property="incomeSn"/>
        <result column="pay_proof" property="payProof"/>
        <result column="pay_type" property="payType"/>
        <result column="bank_name" property="bankName"/>
        <result column="remark" property="remark"/>
        <result column="income_proof" property="incomeProof"/>
        <result column="income_finance" property="incomeFinance"/>
        <result column="income_finance_time" property="incomeFinanceTime"/>
        <result column="income_id" property="incomeId"/>
        <result column="order_id" property="orderId"/>
        <result column="task_id" property="taskId"/>
        <result column="status" property="status"/>
        <result column="audit_basic_id" property="auditBasicId"/>
        <result column="audit_time" property="auditTime"/>
        <result column="audit_remark" property="auditRemark"/>
        <result column="audit_type" property="auditType"/>
        <result column="audit_proof" property="auditProof"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="deleted_at" property="deletedAt"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        income_sn, pay_proof, pay_type, bank_name, remark, income_proof, income_finance, income_finance_time, income_id, order_id, task_id, status, audit_basic_id, audit_time, audit_remark, audit_type, audit_proof, created_at, updated_at, deleted_at
    </sql>

</mapper>

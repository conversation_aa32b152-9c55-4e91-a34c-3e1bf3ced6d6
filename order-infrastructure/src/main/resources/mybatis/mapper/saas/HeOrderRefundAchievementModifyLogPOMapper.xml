<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.HeOrderRefundAchievementModifyLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.stbella.order.infrastructure.repository.po.HeOrderRefundAchievementModifyLogPO">
        <result column="id" property="id"/>
        <result column="refund_id" property="refundId"/>
        <result column="before_refund_amount" property="beforeRefundAmount"/>
        <result column="now_refund_amount" property="nowRefundAmount"/>
        <result column="modify_user" property="modifyUser"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        refund_id, before_refund_amount, now_refund_amount, modify_user, created_at, updated_at
    </sql>

</mapper>

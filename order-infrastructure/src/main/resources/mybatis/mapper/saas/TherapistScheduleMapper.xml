<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.TherapistScheduleMapper">
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.TherapistSchedule">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="therapist_id" jdbcType="BIGINT" property="therapistId"/>
        <result column="serve_date" jdbcType="VARCHAR" property="serveDate"/>
        <result column="start_time" jdbcType="VARCHAR" property="startTime"/>
        <result column="end_time" jdbcType="VARCHAR" property="endTime"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="deleted" jdbcType="INTEGER" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , therapist_id, serve_date, start_time, end_time, gmt_create, gmt_modified, deleted
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from he_therapist_schedule
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from he_therapist_schedule
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.stbella.order.infrastructure.repository.po.TherapistSchedule">
        insert into he_therapist_schedule (id, therapist_id, serve_date,
                                           start_time, end_time, gmt_create,
                                           gmt_modified, deleted)
        values (#{id,jdbcType=BIGINT}, #{therapistId,jdbcType=BIGINT}, #{serveDate,jdbcType=VARCHAR},
                #{startTime,jdbcType=VARCHAR}, #{endTime,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP},
                #{gmtModified,jdbcType=TIMESTAMP}, #{deleted,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" parameterType="com.stbella.order.infrastructure.repository.po.TherapistSchedule">
        insert into he_therapist_schedule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="therapistId != null">
                therapist_id,
            </if>
            <if test="serveDate != null">
                serve_date,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="therapistId != null">
                #{therapistId,jdbcType=BIGINT},
            </if>
            <if test="serveDate != null">
                #{serveDate,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.stbella.order.infrastructure.repository.po.TherapistSchedule">
        update he_therapist_schedule
        <set>
            <if test="therapistId != null">
                therapist_id = #{therapistId,jdbcType=BIGINT},
            </if>
            <if test="serveDate != null">
                serve_date = #{serveDate,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.stbella.order.infrastructure.repository.po.TherapistSchedule">
        update he_therapist_schedule
        set therapist_id = #{therapistId,jdbcType=BIGINT},
            serve_date   = #{serveDate,jdbcType=VARCHAR},
            start_time   = #{startTime,jdbcType=VARCHAR},
            end_time     = #{endTime,jdbcType=VARCHAR},
            gmt_create   = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            deleted      = #{deleted,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stbella.order.infrastructure.repository.mapper.saas.GoodsMapper">
    <resultMap id="BaseResultMap" type="com.stbella.order.infrastructure.repository.po.Goods">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="store_type" jdbcType="BIT" property="storeType"/>
        <result column="store_id" jdbcType="INTEGER" property="storeId"/>
        <result column="goods_type" jdbcType="BIT" property="goodsType"/>
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="goods_price" jdbcType="INTEGER" property="goodsPrice"/>
        <result column="spec_name" jdbcType="VARCHAR" property="specName"/>
        <result column="category_back" jdbcType="INTEGER" property="categoryBack"/>
        <result column="category_front" jdbcType="INTEGER" property="categoryFront"/>
        <result column="sale_type" jdbcType="BIT" property="saleType"/>
        <result column="up_time" jdbcType="BIGINT" property="upTime"/>
        <result column="down_time" jdbcType="BIGINT" property="downTime"/>
        <result column="service_days" jdbcType="INTEGER" property="serviceDays"/>
        <result column="percent_options" jdbcType="VARCHAR" property="percentOptions"/>
        <result column="image" jdbcType="VARCHAR" property="image"/>
        <result column="button_name" jdbcType="VARCHAR" property="buttonName"/>
        <result column="is_delete" jdbcType="BIT" property="isDelete"/>
        <result column="created_at" jdbcType="BIGINT" property="createdAt"/>
        <result column="updated_at" jdbcType="BIGINT" property="updatedAt"/>
        <result column="goods_sell_type" jdbcType="TINYINT" property="goodsSellType"/>
        <result column="cancel_after_verification_type" jdbcType="TINYINT" property="cancelAfterVerificationType"/>
        <result column="goods_sub_sell_type" jdbcType="TINYINT" property="goodsSubSellType"/>
        <result column="remark_is_required" jdbcType="TINYINT" property="remarkIsRequired"/>
        <result column="remark_info" jdbcType="VARCHAR" property="remarkInfo"/>
        <result column="offline_store_id" jdbcType="VARCHAR" property="offlineStoreId"/>
        <result column="service_person_count" jdbcType="INTEGER" property="servicePersonCount"/>
        <result column="unit_time" jdbcType="TINYINT" property="unitTime"/>
        <result column="valid_start" jdbcType="VARCHAR" property="validStart"/>
        <result column="valid_end" jdbcType="VARCHAR" property="validEnd"/>
        <result column="limit_type" jdbcType="TINYINT" property="limitType"/>
        <result column="limit_rule" jdbcType="VARCHAR" property="limitRule"/>
        <result column="tags" jdbcType="INTEGER" property="tags"/>
        <result column="can_modify_type" jdbcType="TINYINT" property="canModifyType"/>
        <result column="activity_time_type" jdbcType="TINYINT" property="activityTimeType"/>
        <result column="is_full_moon_type" jdbcType="TINYINT" property="isFullMoonType"/>
        <result column="package_cost" jdbcType="INTEGER" property="packageCost"/>
        <result column="production_amount" jdbcType="INTEGER" property="productionAmount"/>
        <result column="produce_amount_deduction" jdbcType="TINYINT" property="produceAmountDeduction"/>
        <result column="sbra_piece" jdbcType="INTEGER" property="sbraPiece"/>
        <result column="ecp_room_type" jdbcType="INTEGER" property="ecpRoomType"/>
        <result column="parent_id" jdbcType="INTEGER" property="parentId"/>
        <result column="parent_name" jdbcType="VARCHAR" property="parentName"/>
        <result column="new_flag" jdbcType="INTEGER" property="newFlag"/>
        <result column="service_type" jdbcType="TINYINT" property="serviceType"/>
        <result column="service_tag" jdbcType="BIGINT" property="serviceTag"/>
        <result column="validity_value" jdbcType="TINYINT" property="validityValue"/>
        <result column="validity_type" jdbcType="INTEGER" property="validityType"/>
        <result column="production_instr_id" jdbcType="BIGINT" property="productionInstrId"/>
        <result column="combination_type" jdbcType="INTEGER" property="combinationType"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs"
               type="com.stbella.order.infrastructure.repository.po.GoodsWithBLOBs">
        <result column="images" jdbcType="LONGVARCHAR" property="images"/>
        <result column="content_images" jdbcType="LONGVARCHAR" property="contentImages"/>
        <result column="goods_detail" jdbcType="LONGVARCHAR" property="goodsDetail"/>
        <result column="infor_fields" jdbcType="LONGVARCHAR" property="inforFields"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , store_type, store_id, goods_type, goods_name, description, goods_price, spec_name,
    category_back, category_front, sale_type, up_time, down_time, service_days, percent_options,
    image, button_name, is_delete, created_at, updated_at, goods_sell_type, cancel_after_verification_type,
    goods_sub_sell_type, remark_is_required, remark_info, offline_store_id, service_person_count,
    unit_time, valid_start, valid_end, limit_type, limit_rule, tags, can_modify_type,
    activity_time_type, is_full_moon_type, package_cost, production_amount, produce_amount_deduction,
    sbra_piece, ecp_room_type, parent_id, parent_name, new_flag, service_type, service_tag, validity_value,
    validity_type, production_instr_id,goods_unit,combination_type
    </sql>
    <sql id="Blob_Column_List">
        images
        , content_images, goods_detail, infor_fields
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from he_goods
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from he_goods
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.stbella.order.infrastructure.repository.po.GoodsWithBLOBs">
        insert into he_goods (id, store_type, store_id,
                              goods_type, goods_name, description,
                              goods_price, spec_name, category_back,
                              category_front, sale_type, up_time,
                              down_time, service_days, percent_options,
                              image, button_name, is_delete,
                              created_at, updated_at, goods_sell_type,
                              cancel_after_verification_type, goods_sub_sell_type,
                              remark_is_required, remark_info, offline_store_id,
                              service_person_count, unit_time, valid_start,
                              valid_end, limit_type, limit_rule,
                              tags, can_modify_type, activity_time_type,
                              is_full_moon_type, package_cost, production_amount,
                              produce_amount_deduction, sbra_piece, ecp_room_type,
                              parent_id, new_flag, service_type,
                              service_tag, validity_value, validity_type,
                              production_instr_id, images, content_images,
                              goods_detail, infor_fields)
        values (#{id,jdbcType=INTEGER}, #{storeType,jdbcType=BIT}, #{storeId,jdbcType=INTEGER},
                #{goodsType,jdbcType=BIT}, #{goodsName,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR},
                #{goodsPrice,jdbcType=INTEGER}, #{specName,jdbcType=VARCHAR}, #{categoryBack,jdbcType=INTEGER},
                #{categoryFront,jdbcType=INTEGER}, #{saleType,jdbcType=BIT}, #{upTime,jdbcType=BIGINT},
                #{downTime,jdbcType=BIGINT}, #{serviceDays,jdbcType=INTEGER}, #{percentOptions,jdbcType=VARCHAR},
                #{image,jdbcType=VARCHAR}, #{buttonName,jdbcType=VARCHAR}, #{isDelete,jdbcType=BIT},
                #{createdAt,jdbcType=BIGINT}, #{updatedAt,jdbcType=BIGINT}, #{goodsSellType,jdbcType=TINYINT},
                #{cancelAfterVerificationType,jdbcType=TINYINT}, #{goodsSubSellType,jdbcType=TINYINT},
                #{remarkIsRequired,jdbcType=TINYINT}, #{remarkInfo,jdbcType=VARCHAR},
                #{offlineStoreId,jdbcType=VARCHAR},
                #{servicePersonCount,jdbcType=INTEGER}, #{unitTime,jdbcType=TINYINT}, #{validStart,jdbcType=VARCHAR},
                #{validEnd,jdbcType=VARCHAR}, #{limitType,jdbcType=TINYINT}, #{limitRule,jdbcType=VARCHAR},
                #{tags,jdbcType=INTEGER}, #{canModifyType,jdbcType=TINYINT}, #{activityTimeType,jdbcType=TINYINT},
                #{isFullMoonType,jdbcType=TINYINT}, #{packageCost,jdbcType=INTEGER},
                #{productionAmount,jdbcType=INTEGER},
                #{produceAmountDeduction,jdbcType=TINYINT}, #{sbraPiece,jdbcType=INTEGER},
                #{ecpRoomType,jdbcType=INTEGER},
                #{parentId,jdbcType=INTEGER}, #{newFlag,jdbcType=INTEGER}, #{serviceType,jdbcType=TINYINT},
                #{serviceTag,jdbcType=BIGINT}, #{validityValue,jdbcType=TINYINT}, #{validityType,jdbcType=INTEGER},
                #{productionInstrId,jdbcType=BIGINT}, #{images,jdbcType=LONGVARCHAR},
                #{contentImages,jdbcType=LONGVARCHAR},
                #{goodsDetail,jdbcType=LONGVARCHAR}, #{inforFields,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.stbella.order.infrastructure.repository.po.GoodsWithBLOBs">
        insert into he_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="storeType != null">
                store_type,
            </if>
            <if test="storeId != null">
                store_id,
            </if>
            <if test="goodsType != null">
                goods_type,
            </if>
            <if test="goodsName != null">
                goods_name,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="goodsPrice != null">
                goods_price,
            </if>
            <if test="specName != null">
                spec_name,
            </if>
            <if test="categoryBack != null">
                category_back,
            </if>
            <if test="categoryFront != null">
                category_front,
            </if>
            <if test="saleType != null">
                sale_type,
            </if>
            <if test="upTime != null">
                up_time,
            </if>
            <if test="downTime != null">
                down_time,
            </if>
            <if test="serviceDays != null">
                service_days,
            </if>
            <if test="percentOptions != null">
                percent_options,
            </if>
            <if test="image != null">
                image,
            </if>
            <if test="buttonName != null">
                button_name,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="createdAt != null">
                created_at,
            </if>
            <if test="updatedAt != null">
                updated_at,
            </if>
            <if test="goodsSellType != null">
                goods_sell_type,
            </if>
            <if test="cancelAfterVerificationType != null">
                cancel_after_verification_type,
            </if>
            <if test="goodsSubSellType != null">
                goods_sub_sell_type,
            </if>
            <if test="remarkIsRequired != null">
                remark_is_required,
            </if>
            <if test="remarkInfo != null">
                remark_info,
            </if>
            <if test="offlineStoreId != null">
                offline_store_id,
            </if>
            <if test="servicePersonCount != null">
                service_person_count,
            </if>
            <if test="unitTime != null">
                unit_time,
            </if>
            <if test="validStart != null">
                valid_start,
            </if>
            <if test="validEnd != null">
                valid_end,
            </if>
            <if test="limitType != null">
                limit_type,
            </if>
            <if test="limitRule != null">
                limit_rule,
            </if>
            <if test="tags != null">
                tags,
            </if>
            <if test="canModifyType != null">
                can_modify_type,
            </if>
            <if test="activityTimeType != null">
                activity_time_type,
            </if>
            <if test="isFullMoonType != null">
                is_full_moon_type,
            </if>
            <if test="packageCost != null">
                package_cost,
            </if>
            <if test="productionAmount != null">
                production_amount,
            </if>
            <if test="produceAmountDeduction != null">
                produce_amount_deduction,
            </if>
            <if test="sbraPiece != null">
                sbra_piece,
            </if>
            <if test="ecpRoomType != null">
                ecp_room_type,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="newFlag != null">
                new_flag,
            </if>
            <if test="serviceType != null">
                service_type,
            </if>
            <if test="serviceTag != null">
                service_tag,
            </if>
            <if test="validityValue != null">
                validity_value,
            </if>
            <if test="validityType != null">
                validity_type,
            </if>
            <if test="productionInstrId != null">
                production_instr_id,
            </if>
            <if test="images != null">
                images,
            </if>
            <if test="contentImages != null">
                content_images,
            </if>
            <if test="goodsDetail != null">
                goods_detail,
            </if>
            <if test="inforFields != null">
                infor_fields,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="storeType != null">
                #{storeType,jdbcType=BIT},
            </if>
            <if test="storeId != null">
                #{storeId,jdbcType=INTEGER},
            </if>
            <if test="goodsType != null">
                #{goodsType,jdbcType=BIT},
            </if>
            <if test="goodsName != null">
                #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="goodsPrice != null">
                #{goodsPrice,jdbcType=INTEGER},
            </if>
            <if test="specName != null">
                #{specName,jdbcType=VARCHAR},
            </if>
            <if test="categoryBack != null">
                #{categoryBack,jdbcType=INTEGER},
            </if>
            <if test="categoryFront != null">
                #{categoryFront,jdbcType=INTEGER},
            </if>
            <if test="saleType != null">
                #{saleType,jdbcType=BIT},
            </if>
            <if test="upTime != null">
                #{upTime,jdbcType=BIGINT},
            </if>
            <if test="downTime != null">
                #{downTime,jdbcType=BIGINT},
            </if>
            <if test="serviceDays != null">
                #{serviceDays,jdbcType=INTEGER},
            </if>
            <if test="percentOptions != null">
                #{percentOptions,jdbcType=VARCHAR},
            </if>
            <if test="image != null">
                #{image,jdbcType=VARCHAR},
            </if>
            <if test="buttonName != null">
                #{buttonName,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=BIT},
            </if>
            <if test="createdAt != null">
                #{createdAt,jdbcType=BIGINT},
            </if>
            <if test="updatedAt != null">
                #{updatedAt,jdbcType=BIGINT},
            </if>
            <if test="goodsSellType != null">
                #{goodsSellType,jdbcType=TINYINT},
            </if>
            <if test="cancelAfterVerificationType != null">
                #{cancelAfterVerificationType,jdbcType=TINYINT},
            </if>
            <if test="goodsSubSellType != null">
                #{goodsSubSellType,jdbcType=TINYINT},
            </if>
            <if test="remarkIsRequired != null">
                #{remarkIsRequired,jdbcType=TINYINT},
            </if>
            <if test="remarkInfo != null">
                #{remarkInfo,jdbcType=VARCHAR},
            </if>
            <if test="offlineStoreId != null">
                #{offlineStoreId,jdbcType=VARCHAR},
            </if>
            <if test="servicePersonCount != null">
                #{servicePersonCount,jdbcType=INTEGER},
            </if>
            <if test="unitTime != null">
                #{unitTime,jdbcType=TINYINT},
            </if>
            <if test="validStart != null">
                #{validStart,jdbcType=VARCHAR},
            </if>
            <if test="validEnd != null">
                #{validEnd,jdbcType=VARCHAR},
            </if>
            <if test="limitType != null">
                #{limitType,jdbcType=TINYINT},
            </if>
            <if test="limitRule != null">
                #{limitRule,jdbcType=VARCHAR},
            </if>
            <if test="tags != null">
                #{tags,jdbcType=INTEGER},
            </if>
            <if test="canModifyType != null">
                #{canModifyType,jdbcType=TINYINT},
            </if>
            <if test="activityTimeType != null">
                #{activityTimeType,jdbcType=TINYINT},
            </if>
            <if test="isFullMoonType != null">
                #{isFullMoonType,jdbcType=TINYINT},
            </if>
            <if test="packageCost != null">
                #{packageCost,jdbcType=INTEGER},
            </if>
            <if test="productionAmount != null">
                #{productionAmount,jdbcType=INTEGER},
            </if>
            <if test="produceAmountDeduction != null">
                #{produceAmountDeduction,jdbcType=TINYINT},
            </if>
            <if test="sbraPiece != null">
                #{sbraPiece,jdbcType=INTEGER},
            </if>
            <if test="ecpRoomType != null">
                #{ecpRoomType,jdbcType=INTEGER},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=INTEGER},
            </if>
            <if test="newFlag != null">
                #{newFlag,jdbcType=INTEGER},
            </if>
            <if test="serviceType != null">
                #{serviceType,jdbcType=TINYINT},
            </if>
            <if test="serviceTag != null">
                #{serviceTag,jdbcType=BIGINT},
            </if>
            <if test="validityValue != null">
                #{validityValue,jdbcType=TINYINT},
            </if>
            <if test="validityType != null">
                #{validityType,jdbcType=INTEGER},
            </if>
            <if test="productionInstrId != null">
                #{productionInstrId,jdbcType=BIGINT},
            </if>
            <if test="images != null">
                #{images,jdbcType=LONGVARCHAR},
            </if>
            <if test="contentImages != null">
                #{contentImages,jdbcType=LONGVARCHAR},
            </if>
            <if test="goodsDetail != null">
                #{goodsDetail,jdbcType=LONGVARCHAR},
            </if>
            <if test="inforFields != null">
                #{inforFields,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.stbella.order.infrastructure.repository.po.GoodsWithBLOBs">
        update he_goods
        <set>
            <if test="storeType != null">
                store_type = #{storeType,jdbcType=BIT},
            </if>
            <if test="storeId != null">
                store_id = #{storeId,jdbcType=INTEGER},
            </if>
            <if test="goodsType != null">
                goods_type = #{goodsType,jdbcType=BIT},
            </if>
            <if test="goodsName != null">
                goods_name = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="goodsPrice != null">
                goods_price = #{goodsPrice,jdbcType=INTEGER},
            </if>
            <if test="specName != null">
                spec_name = #{specName,jdbcType=VARCHAR},
            </if>
            <if test="categoryBack != null">
                category_back = #{categoryBack,jdbcType=INTEGER},
            </if>
            <if test="categoryFront != null">
                category_front = #{categoryFront,jdbcType=INTEGER},
            </if>
            <if test="saleType != null">
                sale_type = #{saleType,jdbcType=BIT},
            </if>
            <if test="upTime != null">
                up_time = #{upTime,jdbcType=BIGINT},
            </if>
            <if test="downTime != null">
                down_time = #{downTime,jdbcType=BIGINT},
            </if>
            <if test="serviceDays != null">
                service_days = #{serviceDays,jdbcType=INTEGER},
            </if>
            <if test="percentOptions != null">
                percent_options = #{percentOptions,jdbcType=VARCHAR},
            </if>
            <if test="image != null">
                image = #{image,jdbcType=VARCHAR},
            </if>
            <if test="buttonName != null">
                button_name = #{buttonName,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=BIT},
            </if>
            <if test="createdAt != null">
                created_at = #{createdAt,jdbcType=BIGINT},
            </if>
            <if test="updatedAt != null">
                updated_at = #{updatedAt,jdbcType=BIGINT},
            </if>
            <if test="goodsSellType != null">
                goods_sell_type = #{goodsSellType,jdbcType=TINYINT},
            </if>
            <if test="cancelAfterVerificationType != null">
                cancel_after_verification_type = #{cancelAfterVerificationType,jdbcType=TINYINT},
            </if>
            <if test="goodsSubSellType != null">
                goods_sub_sell_type = #{goodsSubSellType,jdbcType=TINYINT},
            </if>
            <if test="remarkIsRequired != null">
                remark_is_required = #{remarkIsRequired,jdbcType=TINYINT},
            </if>
            <if test="remarkInfo != null">
                remark_info = #{remarkInfo,jdbcType=VARCHAR},
            </if>
            <if test="offlineStoreId != null">
                offline_store_id = #{offlineStoreId,jdbcType=VARCHAR},
            </if>
            <if test="servicePersonCount != null">
                service_person_count = #{servicePersonCount,jdbcType=INTEGER},
            </if>
            <if test="unitTime != null">
                unit_time = #{unitTime,jdbcType=TINYINT},
            </if>
            <if test="validStart != null">
                valid_start = #{validStart,jdbcType=VARCHAR},
            </if>
            <if test="validEnd != null">
                valid_end = #{validEnd,jdbcType=VARCHAR},
            </if>
            <if test="limitType != null">
                limit_type = #{limitType,jdbcType=TINYINT},
            </if>
            <if test="limitRule != null">
                limit_rule = #{limitRule,jdbcType=VARCHAR},
            </if>
            <if test="tags != null">
                tags = #{tags,jdbcType=INTEGER},
            </if>
            <if test="canModifyType != null">
                can_modify_type = #{canModifyType,jdbcType=TINYINT},
            </if>
            <if test="activityTimeType != null">
                activity_time_type = #{activityTimeType,jdbcType=TINYINT},
            </if>
            <if test="isFullMoonType != null">
                is_full_moon_type = #{isFullMoonType,jdbcType=TINYINT},
            </if>
            <if test="packageCost != null">
                package_cost = #{packageCost,jdbcType=INTEGER},
            </if>
            <if test="productionAmount != null">
                production_amount = #{productionAmount,jdbcType=INTEGER},
            </if>
            <if test="produceAmountDeduction != null">
                produce_amount_deduction = #{produceAmountDeduction,jdbcType=TINYINT},
            </if>
            <if test="sbraPiece != null">
                sbra_piece = #{sbraPiece,jdbcType=INTEGER},
            </if>
            <if test="ecpRoomType != null">
                ecp_room_type = #{ecpRoomType,jdbcType=INTEGER},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId,jdbcType=INTEGER},
            </if>
            <if test="newFlag != null">
                new_flag = #{newFlag,jdbcType=INTEGER},
            </if>
            <if test="serviceType != null">
                service_type = #{serviceType,jdbcType=TINYINT},
            </if>
            <if test="serviceTag != null">
                service_tag = #{serviceTag,jdbcType=BIGINT},
            </if>
            <if test="validityValue != null">
                validity_value = #{validityValue,jdbcType=TINYINT},
            </if>
            <if test="validityType != null">
                validity_type = #{validityType,jdbcType=INTEGER},
            </if>
            <if test="productionInstrId != null">
                production_instr_id = #{productionInstrId,jdbcType=BIGINT},
            </if>
            <if test="images != null">
                images = #{images,jdbcType=LONGVARCHAR},
            </if>
            <if test="contentImages != null">
                content_images = #{contentImages,jdbcType=LONGVARCHAR},
            </if>
            <if test="goodsDetail != null">
                goods_detail = #{goodsDetail,jdbcType=LONGVARCHAR},
            </if>
            <if test="inforFields != null">
                infor_fields = #{inforFields,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs"
            parameterType="com.stbella.order.infrastructure.repository.po.GoodsWithBLOBs">
        update he_goods
        set store_type                     = #{storeType,jdbcType=BIT},
            store_id                       = #{storeId,jdbcType=INTEGER},
            goods_type                     = #{goodsType,jdbcType=BIT},
            goods_name                     = #{goodsName,jdbcType=VARCHAR},
            description                    = #{description,jdbcType=VARCHAR},
            goods_price                    = #{goodsPrice,jdbcType=INTEGER},
            spec_name                      = #{specName,jdbcType=VARCHAR},
            category_back                  = #{categoryBack,jdbcType=INTEGER},
            category_front                 = #{categoryFront,jdbcType=INTEGER},
            sale_type                      = #{saleType,jdbcType=BIT},
            up_time                        = #{upTime,jdbcType=BIGINT},
            down_time                      = #{downTime,jdbcType=BIGINT},
            service_days                   = #{serviceDays,jdbcType=INTEGER},
            percent_options                = #{percentOptions,jdbcType=VARCHAR},
            image                          = #{image,jdbcType=VARCHAR},
            button_name                    = #{buttonName,jdbcType=VARCHAR},
            is_delete                      = #{isDelete,jdbcType=BIT},
            created_at                     = #{createdAt,jdbcType=BIGINT},
            updated_at                     = #{updatedAt,jdbcType=BIGINT},
            goods_sell_type                = #{goodsSellType,jdbcType=TINYINT},
            cancel_after_verification_type = #{cancelAfterVerificationType,jdbcType=TINYINT},
            goods_sub_sell_type            = #{goodsSubSellType,jdbcType=TINYINT},
            remark_is_required             = #{remarkIsRequired,jdbcType=TINYINT},
            remark_info                    = #{remarkInfo,jdbcType=VARCHAR},
            offline_store_id               = #{offlineStoreId,jdbcType=VARCHAR},
            service_person_count           = #{servicePersonCount,jdbcType=INTEGER},
            unit_time                      = #{unitTime,jdbcType=TINYINT},
            valid_start                    = #{validStart,jdbcType=VARCHAR},
            valid_end                      = #{validEnd,jdbcType=VARCHAR},
            limit_type                     = #{limitType,jdbcType=TINYINT},
            limit_rule                     = #{limitRule,jdbcType=VARCHAR},
            tags                           = #{tags,jdbcType=INTEGER},
            can_modify_type                = #{canModifyType,jdbcType=TINYINT},
            activity_time_type             = #{activityTimeType,jdbcType=TINYINT},
            is_full_moon_type              = #{isFullMoonType,jdbcType=TINYINT},
            package_cost                   = #{packageCost,jdbcType=INTEGER},
            production_amount              = #{productionAmount,jdbcType=INTEGER},
            produce_amount_deduction       = #{produceAmountDeduction,jdbcType=TINYINT},
            sbra_piece                     = #{sbraPiece,jdbcType=INTEGER},
            ecp_room_type                  = #{ecpRoomType,jdbcType=INTEGER},
            parent_id                      = #{parentId,jdbcType=INTEGER},
            new_flag                       = #{newFlag,jdbcType=INTEGER},
            service_type                   = #{serviceType,jdbcType=TINYINT},
            service_tag                    = #{serviceTag,jdbcType=BIGINT},
            validity_value                 = #{validityValue,jdbcType=TINYINT},
            validity_type                  = #{validityType,jdbcType=INTEGER},
            production_instr_id            = #{productionInstrId,jdbcType=BIGINT},
            images                         = #{images,jdbcType=LONGVARCHAR},
            content_images                 = #{contentImages,jdbcType=LONGVARCHAR},
            goods_detail                   = #{goodsDetail,jdbcType=LONGVARCHAR},
            infor_fields                   = #{inforFields,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.stbella.order.infrastructure.repository.po.Goods">
        update he_goods
        set store_type                     = #{storeType,jdbcType=BIT},
            store_id                       = #{storeId,jdbcType=INTEGER},
            goods_type                     = #{goodsType,jdbcType=BIT},
            goods_name                     = #{goodsName,jdbcType=VARCHAR},
            description                    = #{description,jdbcType=VARCHAR},
            goods_price                    = #{goodsPrice,jdbcType=INTEGER},
            spec_name                      = #{specName,jdbcType=VARCHAR},
            category_back                  = #{categoryBack,jdbcType=INTEGER},
            category_front                 = #{categoryFront,jdbcType=INTEGER},
            sale_type                      = #{saleType,jdbcType=BIT},
            up_time                        = #{upTime,jdbcType=BIGINT},
            down_time                      = #{downTime,jdbcType=BIGINT},
            service_days                   = #{serviceDays,jdbcType=INTEGER},
            percent_options                = #{percentOptions,jdbcType=VARCHAR},
            image                          = #{image,jdbcType=VARCHAR},
            button_name                    = #{buttonName,jdbcType=VARCHAR},
            is_delete                      = #{isDelete,jdbcType=BIT},
            created_at                     = #{createdAt,jdbcType=BIGINT},
            updated_at                     = #{updatedAt,jdbcType=BIGINT},
            goods_sell_type                = #{goodsSellType,jdbcType=TINYINT},
            cancel_after_verification_type = #{cancelAfterVerificationType,jdbcType=TINYINT},
            goods_sub_sell_type            = #{goodsSubSellType,jdbcType=TINYINT},
            remark_is_required             = #{remarkIsRequired,jdbcType=TINYINT},
            remark_info                    = #{remarkInfo,jdbcType=VARCHAR},
            offline_store_id               = #{offlineStoreId,jdbcType=VARCHAR},
            service_person_count           = #{servicePersonCount,jdbcType=INTEGER},
            unit_time                      = #{unitTime,jdbcType=TINYINT},
            valid_start                    = #{validStart,jdbcType=VARCHAR},
            valid_end                      = #{validEnd,jdbcType=VARCHAR},
            limit_type                     = #{limitType,jdbcType=TINYINT},
            limit_rule                     = #{limitRule,jdbcType=VARCHAR},
            tags                           = #{tags,jdbcType=INTEGER},
            can_modify_type                = #{canModifyType,jdbcType=TINYINT},
            activity_time_type             = #{activityTimeType,jdbcType=TINYINT},
            is_full_moon_type              = #{isFullMoonType,jdbcType=TINYINT},
            package_cost                   = #{packageCost,jdbcType=INTEGER},
            production_amount              = #{productionAmount,jdbcType=INTEGER},
            produce_amount_deduction       = #{produceAmountDeduction,jdbcType=TINYINT},
            sbra_piece                     = #{sbraPiece,jdbcType=INTEGER},
            ecp_room_type                  = #{ecpRoomType,jdbcType=INTEGER},
            parent_id                      = #{parentId,jdbcType=INTEGER},
            new_flag                       = #{newFlag,jdbcType=INTEGER},
            service_type                   = #{serviceType,jdbcType=TINYINT},
            service_tag                    = #{serviceTag,jdbcType=BIGINT},
            validity_value                 = #{validityValue,jdbcType=TINYINT},
            validity_type                  = #{validityType,jdbcType=INTEGER},
            production_instr_id            = #{productionInstrId,jdbcType=BIGINT}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="queryProductionGoods" resultType="com.stbella.order.server.order.ProductionGoodsSkuModel">

        SELECT spu.id as goodsId
             , goods_name
             , sku.id as sku_id
             , sku.`sku_name`
             , sku.goods_price
             , sku.cost_price
             , service_type
             ,goods_type
             , produce_amount_deduction
             , production_amount
             , is_full_moon_type
             ,scale,settlement_ratio,tripartite_settlement_price,tripartite_settlement_ratio,parent_id as parentGoodsId
            ,supplier_id
        from he_goods spu
                 left JOIN he_goods_sku sku on spu.id = sku.goods_id
        where
           1=1
        <if test="skuIdList !=null and skuIdList.size() > 0">
            and sku.id in(
            <foreach collection="skuIdList" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>
        <if test="skuIdList == null or  skuIdList.size() == 0">
            and goods_type in (5, 8,12,22)
        </if>



    </select>

    <select id="getOrderGoodsName" resultType="com.stbella.order.server.order.StoreGoodsSkuModel">
        SELECT
        he_order_goods.id,
        he_order_goods.order_id as orderId,
        he_goods.id as goodsId,
        he_goods.goods_name as goodsName,
        he_goods.parent_id as parentId,
        he_goods.parent_name as parentName,
        he_goods_sku.id as skuId,
        he_goods_sku.sku_name as skuName,
        he_goods_sku.template_sku_id as templateSkuId,
        he_goods_sku.template_sku_prop as templateSkuProp
        FROM
        he_order_goods
        LEFT JOIN he_goods ON he_order_goods.goods_id = he_goods.id
        LEFT JOIN he_goods_sku ON he_goods.id = he_goods_sku.goods_id
        where he_order_goods.order_id in
        <foreach collection="orderIdList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="queryGoodsSimpleInfoByGoodsIdList" resultType="com.stbella.order.server.order.GoodsSimpleInfoModel">
        SELECT
        he_goods.id as goodsId,
        he_goods.goods_type as goodsType,
        he_goods_category_front.id as frontId,
        he_goods_category_front.`name` as frontStr,
        he_goods_category_back.id as backId,
        he_goods_category_back.`name` as backStr
        FROM
        he_goods
        LEFT JOIN he_goods_category_front ON he_goods.category_front = he_goods_category_front.id
        LEFT JOIN he_goods_category_back ON he_goods.category_back = he_goods_category_back.id
        where he_goods.id in
        <foreach collection="goodsIdList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="queryByIdForDelete" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from he_goods where id in
        <foreach collection="goodsIdList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>

    <context id="MBG" targetRuntime="MyBatis3">

        <commentGenerator type="com.stbella.order.infrastructure.generator.MyCommentGenerator">
            <property name="javaFileEncoding" value="UTF-8"/>
        </commentGenerator>


        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="*************************************************************************************" userId="stbellaout"
                        password="Stbellaout@2022">
        </jdbcConnection>

        <javaModelGenerator targetPackage="com.stbella.order.infrastructure.repository.po"
                            targetProject="/Users/<USER>/logs">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="mybatis.mapper.orders"
                         targetProject="/Users/<USER>/logs">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <javaClientGenerator type="XMLMAPPER" targetPackage="com.stbella.order.infrastructure.repository.mapper"
                             targetProject="/Users/<USER>/logs">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <table tableName="h3_user" domainObjectName="h3User"
               enableCountByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
               enableUpdateByExample="false" selectByExampleQueryId="false">
        </table>

    </context>
</generatorConfiguration>

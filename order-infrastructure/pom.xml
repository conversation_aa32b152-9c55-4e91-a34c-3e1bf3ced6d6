<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>stbella-order</artifactId>
        <groupId>com.stbella</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>order-infrastructure</artifactId>
    <version>2.0-SNAPSHOT</version>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
            <version>2.0.5</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>3.3.2</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.5.6</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.2.6</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>order-domain</artifactId>
            <scope>compile</scope>
        </dependency>

        <!-- 资产中心 -->
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-asset-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.5.6</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>order-common</artifactId>
            <scope>compile</scope>
        </dependency>

        <!-- 日志相关  mybatis-->
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-core-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
            <version>2.7.8</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-order-model</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-context</artifactId>
        </dependency>

        <dependency>
            <groupId>com.stbella.platform</groupId>
            <artifactId>rule-link-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-contract-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-contract-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-customer-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.stbella.platform</groupId>
            <artifactId>rule-link-client</artifactId>
            <version>${rule-link-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>financial-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stbella</groupId>
            <artifactId>stbella-care-api</artifactId>
        </dependency>
    </dependencies>



</project>

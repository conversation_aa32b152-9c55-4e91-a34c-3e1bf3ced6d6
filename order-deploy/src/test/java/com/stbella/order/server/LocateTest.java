package com.stbella.order.server;

import cn.hutool.core.date.DateUtil;
import com.stbella.order.common.constant.BizConstant;
import com.stbella.order.common.utils.DateUtils;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-07-11  16:50
 * @Description: te
 */
public class LocateTest {
    public static void main(String[] args) {
        int second = DateUtil.second(new Date());

        String s = "2";
        System.out.println("aaa " +  (s == "2"));


        calculate(new int[]{2, 3, 5, 7, 11,13,17,19});


        LocalDateTime now = LocalDateTime.now();
        LocalDateTime validEndTime = now.plusMonths(BizConstant.OrderAppKey.giftValidityMonth);
        Long times = DateUtils.getTenBitTimestamp(validEndTime).longValue();

        System.out.println("now " + times);

        now = LocalDateTime.of(2024, 2, 29, 16, 50);
        validEndTime = now.plusMonths(BizConstant.OrderAppKey.giftValidityMonth);
        times = DateUtils.getTenBitTimestamp(validEndTime).longValue();

        System.out.println("2-29 " + times);

        now = LocalDateTime.of(2024, 1, 31, 16, 50);
        validEndTime = now.plusMonths(BizConstant.OrderAppKey.giftValidityMonth);
        times = DateUtils.getTenBitTimestamp(validEndTime).longValue();

        System.out.println("1-31 " + times);

    }

    public static void calculate(int[] array) {
        int sum = 23;
        // 找出三个数相加和为23的数
        for (int i = 0; i < array.length; i++) {
            for (int j = i + 1; j < array.length; j++) {
                for (int k = j + 1; k < array.length; k++) {
                    if (array[i] + array[j] + array[k] == sum) {
                        System.out.println("结果："+array[i] + " " + array[j] + " " + array[k]);
                    }
                }
            }
       }
    }
}

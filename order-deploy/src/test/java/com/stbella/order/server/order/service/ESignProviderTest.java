package com.stbella.order.server.order.service;

import cn.hutool.json.JSONUtil;
import com.stbella.order.server.BaseTestCase;
import com.stbella.order.server.config.ContractContextConfig;
import com.stbella.order.server.contract.dto.*;
import com.stbella.order.server.contract.provider.month.ESignProvider;
import com.stbella.order.server.contract.service.ESignService;
import com.stbella.order.server.contract.service.impl.ESignServiceImpl;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * @Author: jijunjian
 * @CreateTime: 2023-02-16  14:23
 * @Description: TODO
 */
@Slf4j
@ActiveProfiles("dev")
public class ESignProviderTest extends BaseTestCase {

    @Resource
    ESignProvider provider;
    @Resource
    ESignService eSignService;

    @Test
    public void  doPersonIdentityComparisonError(){
        boolean result = provider.doPersonIdentityComparison("戢俊建", "555555");
        log.info("error result =" + result);
    }

    @Test
    public void  doPersonIdentityComparisonSuccess(){
        boolean result = provider.doPersonIdentityComparison("戢俊建", "513902198611299437");
        log.info("success result =" + result);
    }

    @Test
    public void  createPersonSignAccount(){

        // dea0e4a531124d0281842eba660fd8c1
        //{"code":********,"message":"账号已存在","data":{"accountId":"dea0e4a531124d0281842eba660fd8c1"}}
        PersonAccountDTO personAccountDTO = new PersonAccountDTO();
        personAccountDTO.setThirdPartyUserId("513902198611299437");
        personAccountDTO.setName("戢俊建");
        personAccountDTO.setIdType("CRED_PSN_CH_IDCARD");
        personAccountDTO.setIdNumber("513902198611299437");
        personAccountDTO.setMobile("***********");

        String result = provider.createPersonSignAccount(personAccountDTO);
        log.info("success result =" + result);


    }

    @Test
    public void companyAuthMonthTest() {

        CompanyAccountRequestDTO companyAccountRequestDTO = new CompanyAccountRequestDTO();
        companyAccountRequestDTO.setThirdPartyUserId("91330109MA2H33DH25");
        companyAccountRequestDTO.setName("杭州贝康恩护家政服务有限公司");
        companyAccountRequestDTO.setIdNumber("91330109MA2H33DH25");

        String companyAccount = provider.createCompanyAccount(companyAccountRequestDTO);
        log.info("success result =" + companyAccount);
        System.out.println(companyAccount);

        // {"code":0,"message":"成功","data":{"orgId":"4782244311e14f1daf0f5044fdfc31cd"}}
    }

    @Test
    public void queryCompanyAuthMonthTest() {

        String companyAccount = provider.queryCompanyAccount("4782244311e14f1daf0f5044fdfc31cd");
        log.info(" 杭州贝康恩护家政服务有限公司 状态  success result =" + companyAccount);
        System.out.println(companyAccount);


    }

    @Test
    public void companyAuthMonthTest_悦子阁() {

        CompanyAccountRequestDTO companyAccountRequestDTO = new CompanyAccountRequestDTO();
        companyAccountRequestDTO.setThirdPartyUserId("91310115074793744M");
        companyAccountRequestDTO.setName("悦子阁（上海）健康服务有限公司");
        companyAccountRequestDTO.setIdNumber("91310115074793744M");

        String companyAccount = provider.createCompanyAccount(companyAccountRequestDTO);
        log.info("success result =" + companyAccount);
        System.out.println(companyAccount);

        // {"code":0,"message":"成功","data":{"orgId":"4782244311e14f1daf0f5044fdfc31cd"}}
    }


    /**
     * 公司实名
     */
    @Test
    public void doCompanyAuthentication() {

        CompanyAuthenticationRequestDTO companyAccountRequestDTO = new CompanyAuthenticationRequestDTO();
        companyAccountRequestDTO.setAgentAccountId("dea0e4a531124d0281842eba660fd8c1");

        CompanyAuthenticationDTO result = provider.doCompanyAuthentication(companyAccountRequestDTO, "4782244311e14f1daf0f5044fdfc31cd");
        log.info("success result =" + JSONUtil.toJsonStr(result));

        // {"shortLink":"https://smlt.esign.cn/xxOveBb","url":"https://smlfront.esign.cn:8890/identity/login?param=0Q%2F05wKQq6chhW8X2%2FYSBX23dBUu%2BPzUGabNSU4HirmKTDHmxocFc5Tm1%2BbtDhWlLIAEtn%2BzXgcPF1RGVhWLojVo8V5NfSS7pBpObYkoVUpvP4HHDhS13zpUNRSHzAboa7Nh744wnnr4NtFBz%2F%2FIkEzyzQWA1eUZeFIZ4tmcz0I5U3fMovHH7rA%2BILEYjiTfHvn3d%2FGBRmKmlkWGITjOajxXWSnIhrrcqadVkI%2BuA1f%2FXNdHXoRju6J%2BE3AIcmZnxTqfqmoV4iNcADKQWhsGWw%3D%3D&appId=**********&tsignversion=eve&indiversion=eve&lang=zh-CN","flowId":"3029051057319183256"}

    }


    /**
     * 创建机构印章  杭州贝康恩护家政服务有限公司
     */
    @Test
    public void createSealsOfficialTemplate() {

        CompanySealRequestDTO companyAccountRequestDTO = new CompanySealRequestDTO();
        companyAccountRequestDTO.setOrgId("4782244311e14f1daf0f5044fdfc31cd");
        companyAccountRequestDTO.setAlias("合同");

        String result = provider.createSealsOfficialTemplate(companyAccountRequestDTO);
        log.info("success createSealsOfficialTemplate sealId =" + result);

        //{"code":0,"message":"成功","data":{"orgId":"4782244311e14f1daf0f5044fdfc31cd"}}

    }


    @Test
    public void companySchoolTest() {

        CompanyAccountRequestDTO companyAccountRequestDTO = new CompanyAccountRequestDTO();
        companyAccountRequestDTO.setThirdPartyUserId("91330109MAC7GXYN7Y");
        companyAccountRequestDTO.setName("杭州贝康职业技能培训学校有限公司");
        companyAccountRequestDTO.setIdNumber("91330109MAC7GXYN7Y");

        String companyAccount = provider.createCompanyAccount(companyAccountRequestDTO);
        log.info("success result =" + companyAccount);
        System.out.println(companyAccount);

        // {"code":0,"message":"成功","data":{"orgId":"ae70e0e48eea43fc92072fe8dcd0efe1"}}
    }


    @Test
    public void companyAuthTest() {
        ContractContextConfig.setValue(true);
        CompanyAccountRequestDTO companyAccountRequestDTO = new CompanyAccountRequestDTO();
        companyAccountRequestDTO.setThirdPartyUserId("91440400MA4W4AYY7X");
        companyAccountRequestDTO.setName("杭州贝康健康科技集团有限公司");
        companyAccountRequestDTO.setIdNumber("91440400MA4W4AYY7X");

        String companyAccount = eSignService.createCompanyAccount(companyAccountRequestDTO);

        System.out.println(companyAccount);
    }
}

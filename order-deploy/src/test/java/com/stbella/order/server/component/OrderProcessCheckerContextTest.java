package com.stbella.order.server.component;

import cn.hutool.json.JSONUtil;
import com.stbella.core.result.Result;
import com.stbella.order.common.enums.core.OrderProcessTypeEnum;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.server.BaseTestCase;
import com.stbella.order.server.context.component.checker.OrderProcessCheckerContext;
import com.stbella.platform.order.api.req.CheckOrderProcessReq;
import com.stbella.platform.order.api.req.CreateOrderReq;
import org.junit.Assert;
import org.junit.Test;
import top.primecare.snowball.flow.core.context.FlowContext;

import javax.annotation.Resource;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-07-05  11:14
 * @Description: TODO
 */
public class OrderProcessCheckerContextTest extends BaseTestCase {

    @Resource
    OrderProcessCheckerContext orderProcessCheckerContext;
    @Resource
    OrderRepository orderRepository;

    @Test
    public void  checkTest(){

        HeOrderEntity order = orderRepository.getByOrderId(26781);

        Result check = orderProcessCheckerContext.check(order, OrderProcessTypeEnum.PAY);
        Assert.assertTrue(check.getSuccess());

    }
}

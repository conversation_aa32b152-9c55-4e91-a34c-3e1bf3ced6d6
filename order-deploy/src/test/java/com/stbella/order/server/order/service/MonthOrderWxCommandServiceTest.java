package com.stbella.order.server.order.service;

import com.stbella.core.utils.BigDecimalUtil;
import com.stbella.order.server.BaseTestCase;
import com.stbella.order.server.async.AsyncOrder;
import com.stbella.order.server.order.month.utils.RMBUtils;

import org.junit.Test;
import org.springframework.test.context.ActiveProfiles;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.atomic.AtomicReference;

import javax.annotation.Resource;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13 16:50
 */
@Slf4j
@ActiveProfiles("dev")
public class MonthOrderWxCommandServiceTest extends BaseTestCase {

    @Resource
    private AsyncOrder asyncOrder;

    @Test
    public void test1() {
        //尾款
        AtomicReference<BigDecimal> finalPayment = new AtomicReference<>(BigDecimal.ZERO);
        String s = RMBUtils.formatToseparaDecimals(BigDecimalUtil.divide(finalPayment.get(), new BigDecimal(100)));
        String s1 = RMBUtils.numToRMBStr(BigDecimalUtil.divide(finalPayment.get(), new BigDecimal(100)).doubleValue());
        System.out.println("new BigDecimal(111112 / 100) = " + new BigDecimal(111112 / 100));
        System.out.println("RMBUtils.formatToseparaDecimals(new BigDecimal(111112 / 100)) = " + RMBUtils.formatToseparaDecimals(new BigDecimal(111112 / 100)));
        log.info("ss{}{}", s, s1);
    }

    @Test
    public void test2() {
        Long signDate = 1676217600L;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(signDate*1000));
        calendar.add(Calendar.DAY_OF_MONTH, 4);
        Date failDate = calendar.getTime();
        System.out.println("DateUtil.formatDateTime(failDate) = " + DateUtil.formatDateTime(failDate));
        int failDay = (int) DateUtil.between(failDate, new Date(), DateUnit.DAY);
        System.out.println("failDay = " + failDay);
    }

    @Test
    public void test3() {
        BigDecimal bigDecimal = BigDecimalUtil.divide(new BigDecimal(10),new BigDecimal(100));
        System.out.println("bigDecimal = " + bigDecimal);
    }

    @Test
    public void name() {
        //报单测试
        //{"orderId":15089,"percentFirstTime":1677048508}
        asyncOrder.asyncClientSendNotice(15089, 1677048508);
    }
}

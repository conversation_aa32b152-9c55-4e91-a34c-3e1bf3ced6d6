package com.stbella.order.server.order.service;

import cn.hutool.core.date.DateUtil;
import com.stbella.core.utils.PrivacyUtil;

import java.util.Date;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2023-06-21  16:33
 * @Description: TODO
 */
public class MainTest {

    public static void main(String[] args) {

        Date currentMonth = new Date((1690819200 - 1) *1000);
        boolean sameMonth = DateUtil.isSameMonth(new Date(), currentMonth);

        System.out.println(sameMonth);

        String phone = "18768120650";

        String s = PrivacyUtil.encryptPhoneNo(phone);
        System.out.println("phone "+phone +":"+ s);

        phone = "187681206501";
        s = PrivacyUtil.encryptPhoneNo(phone);
        System.out.println("phone "+phone +":"+ s);

        phone = "1876812065";
        s = PrivacyUtil.encryptPhoneNo(phone);
        System.out.println("phone "+phone +":"+ s);

        phone = "80377777";
        s = PrivacyUtil.encryptPhoneNo(phone);
        System.out.println("phone "+phone +":"+ s);

        phone = "8037777";
        s = PrivacyUtil.encryptPhoneNo(phone);
        System.out.println("phone "+phone +":"+ s);


        phone = "803777";
        s = PrivacyUtil.encryptPhoneNo(phone);
        System.out.println("phone "+phone +":"+ s);

        phone = "8034";
        s = PrivacyUtil.encryptPhoneNo(phone);
        System.out.println("phone "+phone +":"+ s);

        phone = "803";
        s = PrivacyUtil.encryptPhoneNo(phone);
        System.out.println("phone "+phone +":"+ s);

        phone = "80";
        s = PrivacyUtil.encryptPhoneNo(phone);
        System.out.println("phone "+phone +":"+ s);

    }
}

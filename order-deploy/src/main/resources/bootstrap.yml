server:
  port: ${server.port:8080} #服务端口
  undertow:
    # Undertow服务器配置
    io-threads: 16
    worker-threads: 256
    buffer-size: 1024
    direct-buffers: true
    # 连接超时配置
    options:
      server:
        # 请求超时时间 (60秒)
        REQUEST_PARSE_TIMEOUT: 60000
        # 空闲超时时间 (30秒)
        NO_REQUEST_TIMEOUT: 30000
  # HTTP连接超时配置
  connection-timeout: 60000

app:
  id: order-center


spring:
  application:
    name: order-center
  cloud:
    nacos:
      discovery:
        server-addr: ${spring.clould.nacos.server-addr}
        namespace: ${spring.clould.nacos.discovery.namespace}
        username: ${discovery.name}
        password: ${discovery.pwd}
        group: ${discovery.group:DEFAULT_GROUP}
      config:
        server-addr: ${spring.clould.nacos.server-addr}
        namespace: ${spring.clould.nacos.config.namespace}
        username: ${config.name}
        password: ${config.pwd}
        prefix: order-config
        group: ${discovery.group:DEFAULT_GROUP}
        file-extension: yml
#        shared-configs:
  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 200MB
      enabled: true
      location: /var/tmp
      # 文件写入磁盘的阈值
      file-size-threshold: 2KB
      # 是否延迟解析multipart请求，直到文件或参数被访问
      resolve-lazily: false

snowball:
  global:
    appId: order-center
    transactionTemplateBeanName: transactionTemplate
  flow:
    drive: local-xsd
  ##stateMachine:
    ##drive: local-xsd

management:
  endpoint:
    health:
      probes:
        enabled: true
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true

rule:
  client: true
  connectTimeout: 3000
  read-timeout: 5000
  refresh-interval: 5
  load-config-qps: 2
  longPollQPS: 2
  long-polling-initial-delay-in-mills: 90000

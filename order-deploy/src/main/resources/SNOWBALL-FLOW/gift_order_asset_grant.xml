<snowballFlow name="gift_order_asset_grant" version="1">

    <!-- 赠送订单审批通过处理流程 -->
    <flow name="gift_order_asset_grant" identifier="GiftOrder:AssetGrant" bizActivity="GIFT_ORDER_ASSET_GRANT">

        <step name="assembler" priority="1" check="true">
            <stream priority="1">
            </stream>
        </step>
        <step name="validate" priority="2" check="true">
            <stream priority="1">
            </stream>
        </step>

        <!--主业务处理器-->
        <step name="transaction" priority="3" check="true">
            <stream priority="1" transaction="true">
                <node type="com.stbella.order.server.context.component.processor.AssetGrantProcessor" />
                <node type="com.stbella.order.server.context.component.processor.SyncEcpProcessor" />
                <node type="com.stbella.order.server.context.component.processor.SyncOrder2ScrmProcessor" />
                <node type="com.stbella.order.server.context.component.processor.assets.EffectAssetsProcessor" />
                <node type="com.stbella.order.server.context.component.processor.coin.GiveProductionCoinProcessor" />
            </stream>
        </step>

    </flow>

</snowballFlow>

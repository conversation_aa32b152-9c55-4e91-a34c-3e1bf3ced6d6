<snowballFlow name="customer_complaints_cancel" version="1">

    <!-- 客诉工单取消业务处理流程 -->
    <flow name="customer_complaints_cancel" identifier="Complaints:Cancel" bizActivity="CUSTOMER_COMPLAINTS_CANCEL">

        <step name="assembler" priority="1" check="true">
            <stream priority="1">
                <node type="com.stbella.order.server.context.component.cancel.assembler.ComplaintCancelAssemblerProcessor"/>
            </stream>
        </step>

        <!--校验器-->
        <step name="validate" priority="1" check="true">
            <stream priority="2">
                <!--暂无-->
            </stream>
        </step>

        <!--处理器-->
        <step name="transaction" priority="2" check="true">
            <stream priority="3" transaction="true">
                <node type="com.stbella.order.server.context.component.cancel.exec.ComplaintCancelRefundExecProcessor"/>
                <node type="com.stbella.order.server.context.component.cancel.exec.ComplaintCancelAssetExecProcessor"/>

            </stream>
        </step>

    </flow>

</snowballFlow>

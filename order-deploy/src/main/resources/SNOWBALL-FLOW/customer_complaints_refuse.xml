<snowballFlow name="create_common_cart" version="1"
>


    <!-- flow：业务流程，identifier为业务身份，流程的唯一标识，由多个维度笛卡尔积组成； -->

    <flow name="customer_complaints_refuse" identifier="Customer:Complaints:Refuse"
          bizActivity="CUSTOMER_COMPLAINTS_REFUSE">

        <!--
        step：流程从概念上划分为多个步骤组成，如：
              参数校验，准入控制，业务校验，数据装配，业务执行，业务后处理..
        属性选项：
            check：是否允许空step，也就是不添加任何组件；
            transaction：整个step是否以事务形式运行，缺省：false；
        -->

        <step name="assembler" priority="1" check="true">
            <!--
            stream：组件运行流；
            属性选项：
                type: 以并行还是顺序形式运行组件；
                    sequence：顺序同步；缺省；
                    parallel：异步并行；
            -->
            <stream priority="1">
            </stream>
        </step>

        <!--购物车校验器-->
        <step name="validate" priority="2" check="true">
            <stream priority="1">
            </stream>
        </step>


        <step name="transaction" priority="3" check="true">
            <stream priority="1" transaction="true">
                <node type="com.stbella.order.server.context.component.refund.processor.ComplaintApproveCashProcessor"/>
                <node type="com.stbella.order.server.context.component.refund.processor.ComplaintApproveAgreeProcessor"/>
                <node type="com.stbella.order.server.context.component.refund.processor.ComplaintApproveAssetBecomesEffectiveProcessor"/>
            </stream>
        </step>

    </flow>

</snowballFlow>

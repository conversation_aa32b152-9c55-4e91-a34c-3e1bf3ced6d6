<snowballFlow name="order_refund_after" version="1">
    <!-- flow：业务流程，identifier为业务身份，流程的唯一标识，由多个维度笛卡尔积组成； -->
    <flow name="order_refund_success" identifier="Order:Refund:After" bizActivity="ORDER_REFUND_SUCCESS">


        <step name="PARAM_VALID" priority="0" check="true">
            <stream priority="1">
                <node type="com.stbella.order.server.context.component.validator.OrderRefundAfterValidator"/>
            </stream>
        </step>

        <step name="transaction" priority="1" check="true">
            <stream priority="1">
                <node type="com.stbella.order.server.context.component.assembler.OrderRefundAfterAmountAssembler"/>
                <node type="com.stbella.order.server.context.component.processor.performance.OrderAmountProcessor" />
                <node type="com.stbella.order.server.context.component.assembler.OrderRefundAfterOtherAssembler"/>
                <node type="com.stbella.order.server.context.component.assembler.OrderRefundAfterSMSAssembler"/>
                <node type="com.stbella.order.server.context.component.assembler.RefundAfterForComplaintAssembler"/>
                <node type="com.stbella.order.server.context.component.refund.processor.OrderRefundAfterCloseProcessor"/>
            </stream>
        </step>


    </flow>

</snowballFlow>

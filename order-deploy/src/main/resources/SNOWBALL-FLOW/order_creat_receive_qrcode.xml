<snowballFlow name="order_creat_receive_qrcode" version="1"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="http://www.guanghetang.cn/schema/snowball/flow/snowballFlow.xsd">


    <!-- 业务线：场景：订单类型 -->

    <flow name="order_creat_receive_qrcode" identifier="CREATE_ORDER_RECEIVE_QRCODE"  bizActivity="CREATE_ORDER_RECEIVE_QRCODE" >

        <step name="PARAM_VALID" priority="0" check="true">
            <stream priority="1">
                <node type="com.stbella.order.server.context.component.validator.PayOrderValidator"/>
            </stream>
        </step>


        <step name="transaction" priority="2" check="true" >
            <stream priority="1" transaction="true">
                <node type="com.stbella.order.server.context.component.processor.cachier.CreateReceiveQrcodeProcessor" />
            </stream>
        </step>

      </flow>

  </snowballFlow>

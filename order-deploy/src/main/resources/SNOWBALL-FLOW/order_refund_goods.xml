<snowballFlow name="order_refund_goods" version="1">
    <flow name="order_refund_goods" identifier="Order:Refund:Goods" bizActivity="ORDER_REFUND_GOODS">

        <!--
         获取订单的退款商品列表
        -->
        <step name="assembler" priority="0" check="true">
            <stream priority="1">
                <node type="com.stbella.order.server.context.component.validator.OrderRefundBeforeAssembler"/>
            </stream>
        </step>

        <!--
            获取订单的退款商品列表
        -->
        <step name="PARAM_VALID" priority="1" check="true">
            <stream priority="1">
                <node type="com.stbella.order.server.context.component.validator.OrderRefundValidator"/>
            </stream>
        </step>

        <!--
            退款商品数据从 he_order_goods取，
            可退数量 = he_order_goods下单数量 - he_order_refund_goods正在退+已退
            可退金额 = he_income_paid_allocation已付金额 - he_order_refund_goods正在退+已退
        -->
        <step name="assembler" priority="2" check="true">
            <stream priority="1">
                <node type="com.stbella.order.server.context.component.assembler.OrderRefundGoodsAssembler"/>
                <node type="com.stbella.order.server.context.component.assembler.OrderGoodsRefundPayInfoAssembler"/>
                <node type="com.stbella.order.server.context.component.assembler.OrderRefundPayInfoAssembler"/>
            </stream>
        </step>

    </flow>

</snowballFlow>

<snowballFlow name="create_common_cart" version="1"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="http://www.guanghetang.cn/schema/snowball/flow/snowballFlow.xsd">


    <!-- 还原购物车 用于再来一单等场景 -->

    <flow name="restore_common_cart" identifier="Cart:Restore:*"  bizActivity="RESTORE_CART" >

        <!--
        step：流程从概念上划分为多个步骤组成，如：
              参数校验，准入控制，业务校验，数据装配，业务执行，业务后处理..
        属性选项：
            check：是否允许空step，也就是不添加任何组件；
            transaction：整个step是否以事务形式运行，缺省：false；
        -->

        <step name="assembler" priority="1" check="true">
            <!--
            stream：组件运行流；
            属性选项：
                type: 以并行还是顺序形式运行组件；
                    sequence：顺序同步；缺省；
                    parallel：异步并行；
            -->
            <stream priority="1" >
                <node type="com.stbella.order.server.context.component.cart.assembler.CartBusinessEntityAssembler" />
            </stream>
        </step>

        <step name="transaction" priority="2" check="true" >
            <stream priority="1" transaction="true">
                <node type="com.stbella.order.server.context.component.cart.processor.CopyCartProcessor" />
            </stream>
        </step>

    </flow>

</snowballFlow>

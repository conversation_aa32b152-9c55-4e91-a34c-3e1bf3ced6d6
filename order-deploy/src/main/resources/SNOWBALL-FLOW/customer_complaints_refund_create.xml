<snowballFlow name="order_refund_create" version="1">
    <!-- flow：业务流程，identifier为业务身份，流程的唯一标识，由多个维度笛卡尔积组成； -->
    <flow name="complaint_refund_create" identifier="Complaint:Refund:Create" bizActivity="COMPLAINT_REFUND_CREATE">


        <step name="PARAM_VALID" priority="0" check="true">
            <stream priority="1">
                <node type="com.stbella.order.server.context.component.validator.ComplaintsRefundBeforeValidator"/>
            </stream>
        </step>

        <step name="transaction" priority="1" check="true">
            <stream priority="1">
                <node type="com.stbella.order.server.context.component.complaints.processor.ComplaintRefundDraftProcessor"/>
                <node type="com.stbella.order.server.context.component.complaints.processor.ComplaintRefundCreateProcessor"/>
            </stream>
        </step>

        <step name="afterTransaction" priority="3" check="true">
            <stream priority="1">
            </stream>
        </step>


    </flow>

</snowballFlow>

<snowballFlow name="flow_carrot"
              version="1"
              xmlns="http://www.guanghetang.cn/schema/snowball/flow"
>


    <!-- flow：业务流程，identifier为业务身份，流程的唯一标识，由多个维度笛卡尔积组成； -->

    <flow name="contract_create" identifier="CARE_CENTER:contract_create" bizActivity="CONTRACT_CREATE">

        <!--
        step：流程从概念上划分为多个步骤组成，如：
              参数校验，准入控制，业务校验，数据装配，业务执行，业务后处理..
        属性选项：
            check：是否允许空step，也就是不添加任何组件；
            transaction：整个step是否以事务形式运行，缺省：false；
        -->
        <step name="PARAM_VALID" priority="0" check="true">

            <!--
            stream：组件运行流；
            属性选项：
                type: 以并行还是顺序形式运行组件；
                    sequence：顺序同步；缺省；
                    parallel：异步并行；DecreseValidator
            -->
            <stream priority="1">
                <node type="com.stbella.order.server.context.component.contract.processor.ContractCreateValidatorProcessor"/>
            </stream>
        </step>

        <step name="assembler" priority="1" check="true">

            <!--
            stream：组件运行流；
            属性选项：
                type: 以并行还是顺序形式运行组件；
                    sequence：顺序同步；缺省；
                    parallel：异步并行；
            -->
            <stream priority="1">
                <node type="com.stbella.order.server.context.component.contract.processor.ContractCreateParamFillProcessor"/>
            </stream>
        </step>
        <step name="transaction" priority="2" check="true">

            <!--
            stream：组件运行流；
            属性选项：
                type: 以并行还是顺序形式运行组件；
                    sequence：顺序同步；缺省；
                    parallel：异步并行；
            -->
            <stream priority="1">
                <node type="com.stbella.order.server.context.component.contract.processor.ContractCancelProcessor"/>
                <node type="com.stbella.order.server.context.component.contract.processor.ContractCreateProcessor"/>
                <node type="com.stbella.order.server.context.component.contract.processor.SupplementContractAfterCreateProcessor"/>
                <node type="com.stbella.order.server.context.component.contract.processor.MainContractAfterCreateProcessor"/>
            </stream>
        </step>


    </flow>

</snowballFlow>

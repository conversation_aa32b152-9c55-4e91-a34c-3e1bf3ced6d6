<snowballFlow name="copy_common_cart" version="1"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="http://www.guanghetang.cn/schema/snowball/flow/snowballFlow.xsd">

    <flow name="copy_common_cart" identifier="Cart:Copy:*"  bizActivity="COPY_CART" >

        <step name="assembler" priority="1" check="true">
            <stream priority="1" >
                <node type="com.stbella.order.server.context.component.cart.assembler.CartCopyEntityAssembler" />
            </stream>
        </step>

        <step name="transaction" priority="2" check="true" >
            <stream priority="1" transaction="true">
                <node type="com.stbella.order.server.context.component.cart.processor.CopyCartEntityProcessor" />
            </stream>
        </step>

    </flow>

</snowballFlow>

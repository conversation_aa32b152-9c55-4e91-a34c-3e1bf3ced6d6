<snowballFlow name="succeed_online_pay" version="1"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
>


    <!-- 业务线：场景：订单类型 -->

    <flow name="succeed_online_pay" identifier="(CARE_CENTER|CARE_FOR_HOME):OFFLINE"  bizActivity="SUCCEED_PAY" >

        <!--
        step：流程从概念上划分为多个步骤组成，如：
              参数校验，准入控制，业务校验，数据装配，业务执行，业务后处理..
        属性选项：
            check：是否允许空step，也就是不添加任何组件；
            transaction：整个step是否以事务形式运行，缺省：false；
        -->

        <step name="transaction" priority="1" check="true">
            <!--
            stream：组件运行流；
            属性选项：
                type: 以并行还是顺序形式运行组件；
                    sequence：顺序同步；缺省；
                    parallel：异步并行；
            -->
            <stream priority="1" >
                <node type="com.stbella.order.server.context.component.processor.pay.SucceedOrderTransactionProcessor" />
                <node type="com.stbella.order.server.context.component.processor.pay.ModifyOfflineProofProcessor" />
                <node type="com.stbella.order.server.context.component.processor.pay.AllocationPaymentProcessor" />
                <node type="com.stbella.order.server.context.component.processor.performance.OrderAmountProcessor" />
                <node type="com.stbella.order.server.context.component.processor.performance.PerformanceEffectAndFullPaymentProcessor" />
            </stream>
        </step>

        <step name="afterTransaction" priority="2" check="true" >
            <stream priority="1">
                <node type="com.stbella.order.server.context.component.processor.OrderRefundSuccessGoodsAssembler" />
                <node type="com.stbella.order.server.context.component.processor.GiftOrderAssetGrantProcessor" />
                <node type="com.stbella.order.server.context.component.processor.BroadcastProcessor" />
<!--                <node type="com.stbella.order.server.context.component.processor.AssetEffectProcessor" />-->
                <node type="com.stbella.order.server.context.component.processor.SyncEcpProcessor" />
                <node type="com.stbella.order.server.context.component.processor.SyncOrder2ScrmProcessor" />
                <node type="com.stbella.order.server.context.component.processor.assets.GiveInviterPointsProcessor" />
                <node type="com.stbella.order.server.context.component.processor.assets.EffectAssetsProcessor" />
                <node type="com.stbella.order.server.context.component.processor.assets.GiveGrowthProcessor" />
                <node type="com.stbella.order.server.context.component.processor.coin.GiveProductionCoinProcessor" />
                <node type="com.stbella.order.server.context.component.processor.assets.GivePointsForSucceedPayProcessor" />
                <node type="com.stbella.order.server.context.component.processor.notice.OfficialAccountsPaymentNoticeProcessor" />
                <node type="com.stbella.order.server.context.component.processor.notice.PerformanceEffectivenessOrderNoticeProcessor"/>
            </stream>
        </step>
      </flow>

  </snowballFlow>

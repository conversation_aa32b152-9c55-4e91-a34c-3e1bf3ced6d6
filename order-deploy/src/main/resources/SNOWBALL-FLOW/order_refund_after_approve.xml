<snowballFlow name="order_refund_after_approve" version="1">
    <!-- flow：业务流程，identifier为业务身份，流程的唯一标识，由多个维度笛卡尔积组成； -->
    <flow name="order_refund_after_approve" identifier="Order:Refund:After:Approve"
          bizActivity="ORDER_REFUND_AFTER_APPROVE">


        <step name="PARAM_VALID" priority="0" check="true">
            <stream priority="1">
                <node type="com.stbella.order.server.context.component.validator.OrderRefundAfterApproveValidator"/>
            </stream>
        </step>

        <step name="transaction" priority="1" check="true">
            <stream priority="1">
                <node type="com.stbella.order.server.context.component.assembler.OrderRefundAfterApproveSMSAssembler"/>
                <node type="com.stbella.order.server.context.component.assembler.OrderRefundAfterApproveUpdateStatusAssembler"/>
                <node type="com.stbella.order.server.context.component.assembler.OrderRefundAfterApproveOnlyNumAssembler"/>
                <node type="com.stbella.order.server.context.component.assembler.OrderRefundAfterApproveAmountAssembler"/>
                <node type="com.stbella.order.server.context.component.assembler.OrderRefundAfterAssetAssembler"/>
            </stream>
        </step>

        <step name="afterTransaction" priority="3" check="true">
            <stream priority="1">
            </stream>
        </step>


    </flow>

</snowballFlow>

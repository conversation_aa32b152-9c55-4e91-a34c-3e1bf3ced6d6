<snowballFlow name="order_refund_create" version="1">
    <!-- flow：业务流程，identifier为业务身份，流程的唯一标识，由多个维度笛卡尔积组成； -->
    <flow name="order_refund_create" identifier="Order:Refund:Create" bizActivity="ORDER_REFUND_CREATE">

        <step name="assembler" priority="0" check="true">
            <stream priority="1">
                <node type="com.stbella.order.server.context.component.validator.OrderRefundBeforeAssembler"/>
                <node type="com.stbella.order.server.context.component.validator.OrderRefundAndReturnAmountAssembler"/>
                <node type="com.stbella.order.server.context.component.validator.OrderRefundBackAssembler"/>
            </stream>
        </step>

        <step name="PARAM_VALID" priority="1" check="true">
            <stream priority="1">
                <node type="com.stbella.order.server.context.component.validator.OrderRefundValidator"/>
                <node type="com.stbella.order.server.context.component.validator.OrderRefundTypeValidator"/>
                <node type="com.stbella.order.server.context.component.validator.OrderRefundCreateValidator"/>
                <node type="com.stbella.order.server.context.component.validator.AssetAccountStatusValidator"/>
                <node type="com.stbella.order.server.context.component.assembler.OrderGoodsRefundNumCheckAssembler"/>
            </stream>
        </step>

        <step name="transaction" priority="2" check="true">
             <stream priority="1">
                 <node type="com.stbella.order.server.context.component.assembler.OrderRefundCreateProcessPaymentAssembler"/>
                 <node type="com.stbella.order.server.context.component.assembler.OrderRefundCapitationFeeAssembler"/>
                 <node type="com.stbella.order.server.context.component.assembler.OrderRefundCreateOperationGoodsAssembler"/>
                 <node type="com.stbella.order.server.context.component.assembler.OrderRefundCreateProcessAchievementAssembler"/>
                 <node type="com.stbella.order.server.context.component.assembler.OrderRefundCreateAssetAssembler"/>
             </stream>
        </step>

        <step name="afterTransaction" priority="3" check="true">
            <stream priority="1">
                <node type="com.stbella.order.server.context.component.refund.processor.OrderRefundCreateApprovalProcessor"/>
                <node type="com.stbella.order.server.context.component.refund.processor.OrderRefundCreateFailProcessor"/>
                <node type="com.stbella.order.server.context.component.refund.processor.OrderRefundCreateOtherItemProcessor"/>
                <node type="com.stbella.order.server.context.component.assembler.OrderRefundAfterDisabledCKGoodsAssembler"/>
                <node type="com.stbella.order.server.context.component.assembler.OrderRefundReservationFormInvalidAssembler"/>
                <node type="com.stbella.order.server.context.component.assembler.OrderRefundAfterForComplaintAssembler"/>
            </stream>
        </step>


    </flow>

</snowballFlow>

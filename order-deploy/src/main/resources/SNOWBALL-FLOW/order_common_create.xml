<snowballFlow name="create_common_order" version="1"
>


    <!-- 业务线：场景：订单类型 -->

    <flow name="create_common_order" identifier="(CARE_CENTER|CARE_FOR_HOME|CARE_FOR_HOME):OMNI:(MONTH_ORDER|PRODUCTION_ORDER|SMALL_MONTH_ORDER|DJ_ORDER)"  bizActivity="CREATE_ORDER" >

        <!--
        step：流程从概念上划分为多个步骤组成，如：
              参数校验，准入控制，业务校验，数据装配，业务执行，业务后处理..
        属性选项：
            check：是否允许空step，也就是不添加任何组件；
            transaction：整个step是否以事务形式运行，缺省：false；
        -->
        <step name="PARAM_VALID" priority="0" check="true">

            <!--
            stream：组件运行流；
            属性选项：
                type: 以并行还是顺序形式运行组件；
                    sequence：顺序同步；缺省；
                    parallel：异步并行；
            -->
            <stream priority="1">
                <node type="com.stbella.order.server.context.component.validator.CustomerValidator" async="true"/>
                <node type="com.stbella.order.server.context.component.validator.CustomAttributeValidator"/>
                <node type="com.stbella.order.server.context.component.validator.UpgradeOrderValidator"/>
            </stream>
        </step>

        <step name="assembler" priority="1" check="true">
            <!--
            stream：组件运行流；
            属性选项：
                type: 以并行还是顺序形式运行组件；
                    sequence：顺序同步；缺省；
                    parallel：异步并行；
            -->
            <stream priority="1">
                <node type="com.stbella.order.server.context.component.assembler.StoreInfoAssembler"/>
                <node type="com.stbella.order.server.context.component.assembler.SkuDetailInfoAssembler"/>
                <node type="com.stbella.order.server.context.component.assembler.OrderPriceAssembler"/>
                <node type="com.stbella.order.server.context.component.assembler.OrderIndexCalcuAssembler"/>
                <node type="com.stbella.order.server.context.component.assembler.DiscountAssembler"/>
                <node type="com.stbella.order.server.context.component.assembler.DiscountApprovalAssembler"/>
                <node type="com.stbella.order.server.context.component.assembler.SkuAttachmentAssembler"/>
                <node type="com.stbella.order.server.context.component.assembler.CustomerAssembler"/>
                <node type="com.stbella.order.server.context.component.assembler.OrderEntityForCartAssembler" />
            </stream>
        </step>


        <step name="PARAM_VALID2" priority="2" check="true">

            <!--
            stream：组件运行流；
            属性选项：
                type: 以并行还是顺序形式运行组件；
                    sequence：顺序同步；缺省；
                    parallel：异步并行；
            -->
            <stream priority="1">
                <node type="com.stbella.order.server.context.component.cart.validator.SkuDetailInfoValidator" />
                <node type="com.stbella.order.server.context.component.cart.validator.GiftAssetValidator" />
                <node type="com.stbella.order.server.context.component.cart.validator.PromotionValidator" />
                <node type="com.stbella.order.server.context.component.validator.DiscountRuleValidator"/>
            </stream>
        </step>

        <step name="transaction" priority="3" check="true">
            <stream priority="1" transaction="true">
                <node type="com.stbella.order.server.context.component.processor.OccupyGiftAccountProcessor"/>
                <node type="com.stbella.order.server.context.component.processor.UpgradeOrderProcessor"/>
                <node type="com.stbella.order.server.context.component.processor.CreateOrderProcessor"/>
            </stream>
        </step>

        <step name="afterTransaction" priority="4" check="true">
            <stream priority="1">
                <node type="com.stbella.order.server.context.component.processor.notice.OfficialAccountsCreateNoticeProcessor"/>
                <node type="com.stbella.order.server.context.component.processor.InitiateDiscountApprovalProcessor"/>
                <node type="com.stbella.order.server.context.component.processor.AfterDiscountApprovalProcessor"/>
                <node type="com.stbella.order.server.context.component.processor.SyncMarketingProcessor"/>
                <node type="com.stbella.order.server.context.component.processor.OrderGoodsActivityProcessor"/>

            </stream>
        </step>
    </flow>

</snowballFlow>

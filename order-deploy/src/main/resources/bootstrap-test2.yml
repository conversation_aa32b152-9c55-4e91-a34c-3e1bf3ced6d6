server:
  port: 8080 #服务端口
app:
  id: order-center
  env: test


spring:
  application:
    name: order-center
  cloud:
    nacos:
      # 注册中心
      discovery:
        server-addr: https://nacos-test.primecare.top
        namespace: 1614acfe-08ec-4127-a54f-561f65f271b5
        username: test
        password: test@primecare
        group: DEFAULT_GROUP
      config:
        server-addr: https://nacos-test.primecare.top
        namespace: 1614acfe-08ec-4127-a54f-561f65f271b5
        username: test
        password: test@primecare
        prefix: order-config
        file-extension: yml
        shared-configs:
          - { data-id: order-config.yml, refresh: true }
        group: DEFAULT_GROUP
        extension-configs:
          - { data-id: order-app.yml, refresh: true }

mybatis-plus:
  mapper-locations: classpath*:/mapper/*.xml
  typeAliasesPackage: com.stbella.*.server.*.entity
package com.stbella.order.controller.app.v4;

import com.stbella.core.annotations.PreventDuplication;
import com.stbella.core.base.PageVO;
import com.stbella.core.result.Result;
import com.stbella.mvc.base.BaseController;
import com.stbella.order.server.order.month.service.OrderCustomerComplaintsService;
import com.stbella.platform.order.api.req.CustomerComplaintsCreateReq;
import com.stbella.platform.order.api.req.CustomerComplaintsPageReq;
import com.stbella.platform.order.api.res.CustomerComplaintsCheckRes;
import com.stbella.platform.order.api.res.CustomerComplaintsDetailVO;
import com.stbella.platform.order.api.res.CustomerComplaintsTypeVO;
import com.stbella.platform.order.api.res.CustomerComplaintsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@Api(tags = "订单客诉相关2")
@RequestMapping("/customerComplaints/v4")
public class OrderCustomerComplaintsController extends BaseController {

    @Resource
    private OrderCustomerComplaintsService orderCustomerComplaintsService;


    @ApiOperation("获取客诉类型列表")
    @GetMapping("/getCustomerComplaintsTypeList")
    public Result<List<CustomerComplaintsTypeVO>> getCustomerComplaintsTypeList(Long parentId) {
        return orderCustomerComplaintsService.getCustomerComplaintsTypeList(parentId);
    }

    @ApiOperation("查询客诉列表")
    @PostMapping("/page")
    public Result<PageVO<CustomerComplaintsVO>> page(@RequestBody CustomerComplaintsPageReq pageReq) {
        return orderCustomerComplaintsService.page(pageReq);
    }


    @ApiOperation("新增客诉")
    @PostMapping("/add")
    @PreventDuplication
    public Result<Long> add(@RequestBody @Valid CustomerComplaintsCreateReq customerComplaintsCreateReq) {
        customerComplaintsCreateReq.setIsDraft(Boolean.FALSE);
        return orderCustomerComplaintsService.add(customerComplaintsCreateReq);
    }


    @ApiOperation("客诉校验")
    @PostMapping("/check")
    public Result<CustomerComplaintsCheckRes> check(@RequestBody @Valid CustomerComplaintsCreateReq customerComplaintsCreateReq) {
        customerComplaintsCreateReq.setIsDraft(Boolean.FALSE);
        return orderCustomerComplaintsService.check(customerComplaintsCreateReq);
    }


    @ApiOperation("存草稿")
    @PostMapping("/draft")
    @PreventDuplication
    public Result<Long> draft(@RequestBody CustomerComplaintsCreateReq customerComplaintsCreateReq) {
        customerComplaintsCreateReq.setIsDraft(Boolean.TRUE);
        return orderCustomerComplaintsService.add(customerComplaintsCreateReq);
    }


    @ApiOperation("删除客诉")
    @DeleteMapping("/delete")
    public Result<Boolean> delete(Long id) {
        return orderCustomerComplaintsService.delete(id);
    }


    @ApiOperation("查询客诉详情")
    @GetMapping("/detail")
    public Result<CustomerComplaintsDetailVO> detail(Long id) {
        try {
            log.info("查询客诉详情，id={}", id);
            return orderCustomerComplaintsService.detail(id);
        } catch (Exception e) {
            log.error("查询客诉详情异常", e);
        }
        return Result.success();
    }

    @ApiOperation("创建客诉零元单")
    @GetMapping("/createOrder")
    public Result<?> createOrder(Long cartId){
        orderCustomerComplaintsService.createOrder(cartId);
        return Result.success();
    }

    @ApiOperation("重新发起")
    @GetMapping("/relaunch")
    public Result<Long> relaunch(Long id) {
        log.info("重新发起客诉, id: {}", id);
        return orderCustomerComplaintsService.relaunch(id);
    }


}

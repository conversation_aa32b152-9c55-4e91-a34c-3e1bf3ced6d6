package com.stbella.order.controller.test;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.stbella.base.server.test.req.SwapPhoneReq;
import com.stbella.core.base.PageVO;
import com.stbella.core.result.Result;
import com.stbella.mvc.aspect.OperateLog;
import com.stbella.order.common.enums.month.StoreAchievementTypeEnum;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.domain.order.month.entity.HeIncomeRecordEntity;
import com.stbella.order.domain.order.month.entity.HeOrderEntity;
import com.stbella.order.domain.order.month.entity.HeOrderUserSnapshotEntity;
import com.stbella.order.domain.repository.IncomeRecordRepository;
import com.stbella.order.domain.repository.OrderRepository;
import com.stbella.order.domain.repository.OrderUserSnapshotRepository;
import com.stbella.order.server.context.component.processor.AssetEffectProcessor;
import com.stbella.order.server.context.component.processor.GiftOrderAssetGrantProcessor;
import com.stbella.order.server.context.component.processor.SyncEcpProcessor;
import com.stbella.order.server.context.component.processor.SyncOrder2ScrmProcessor;
import com.stbella.order.server.contract.dto.CompanyAccountRequestDTO;
import com.stbella.order.server.contract.month.component.MonthContractAssembler;
import com.stbella.order.server.contract.provider.month.ESignProvider;
import com.stbella.order.server.contract.req.ESignFlowRecordReq;
import com.stbella.order.server.contract.res.ESignCancleFlowRecordVO;
import com.stbella.order.server.fact.OrderFact;
import com.stbella.order.server.manager.MailManager;
import com.stbella.order.server.order.cts.service.CtsPayService;
import com.stbella.order.server.order.job.YjOrderRefundCheckJobHandler;
import com.stbella.order.server.order.month.component.IncomeTotalAssembler;
import com.stbella.order.server.order.month.dto.OrderDataRemindDTO;
import com.stbella.order.server.order.month.dto.OrderInfoDTO;
import com.stbella.order.server.order.month.req.*;
import com.stbella.order.server.order.month.request.mail.SendMailToFinanceRequest;
import com.stbella.order.server.order.month.request.standard.UpdateCustomerNamePhoneReq;
import com.stbella.order.server.order.month.res.BasicSimpInfoVO;
import com.stbella.order.server.order.month.res.OrderSimpInfoVO;
import com.stbella.order.server.order.month.res.SaleSimpInfoVO;
import com.stbella.order.server.order.month.res.StoreAchievementResultVO;
import com.stbella.order.server.order.month.service.MonthOrderAdminQueryService;
import com.stbella.order.server.order.month.service.MonthOrderWxCommandService;
import com.stbella.order.server.order.month.service.OperationalToolService;
import com.stbella.order.server.order.nutrition.service.job.JobHandler;
import com.stbella.order.server.order.order.api.OrderInfoService;
import com.stbella.order.server.order.order.req.OrderFulfillmentReq;
import com.stbella.order.server.order.order.res.ListOrderV1Res;
import com.stbella.order.server.order.order.res.OrderFulfillmentRes;
import com.stbella.order.server.order.production.res.ProductionPropertyListVo;
import com.stbella.order.server.utils.DateUtils;
import com.stbella.platform.order.api.res.InnerRankRes;
import com.stbella.rule.api.req.ExecuteRuleV2Req;
import com.stbella.rule.api.res.HitRuleVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.primecare.snowball.flow.core.context.FlowContext;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;

/**
 * @description:
 * @author: Seven
 * @time: 2022/10/14 16:29
 */
@Validated
@Api(tags = "订单运营工具类")
@RestController
@RequestMapping("/test")
@Slf4j
public class TestController {

    @Resource
    CtsPayService ctsPayService;
    @Resource
    YjOrderRefundCheckJobHandler yjOrderRefundCheckJobHandler;

    @Resource
    private MonthOrderAdminQueryService monthOrderAdminQueryService;
    @Resource
    private HttpServletResponse response;
    @Resource
    private MonthOrderWxCommandService monthOrderWxCommandService;
    @Resource
    private OperationalToolService operationalToolService;
    @Resource
    private OrderInfoService orderInfoService;

    @Resource
    RuleLinkClient ruleLinkClient;

    @Resource
    ESignProvider eSignProvider;

//    @Resource
//    ScrmExportService scrmExportService;

    @Resource
    MonthContractAssembler monthContractAssembler;
    @Resource
    private MailManager mailManager;
    @Resource
    private JobHandler jobHandler;

    @Resource
    private OrderUserSnapshotRepository orderUserSnapshotRepository;

    @Resource
    private IncomeTotalAssembler incomeTotalAssembler;

    @Resource
    private GiftOrderAssetGrantProcessor giftOrderAssetGrantProcessor;

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private SyncEcpProcessor syncEcpProcessor;

    @Resource
    private SyncOrder2ScrmProcessor syncOrder2ScrmProcessor;

    @Resource
    private AssetEffectProcessor assetEffectProcessor;

    @Resource
    private IncomeRecordRepository incomeRecordRepository;


    @ApiOperation("到家退款模拟审批回掉")
    @GetMapping("/executeRefundForDing")
    public Result<ProductionPropertyListVo> executeRefundForDing(@RequestParam String processInstanceId, @RequestParam boolean success) {
        ctsPayService.executeRefundForDing(processInstanceId, success);
        return Result.success();
    }


    @ApiOperation("退款处理中模拟审批回掉")
    @GetMapping("/refundInProcessingOrder")
    public Result<ProductionPropertyListVo> refundInProcessingOrder() {
        yjOrderRefundCheckJobHandler.yjOrderRefundCheck();
        return Result.success();
    }

    @ApiOperation("老订单数据清洗新订单-订单ID")
    @GetMapping("/oldOrderSyncByOrderId")
    public Result<String> oldOrderSyncByOrderId(@RequestParam Integer orderId) {
        return Result.success(monthOrderWxCommandService.oldOrderSyncByOrderId(orderId));
    }

    @ApiOperation("老订单数据清洗新订单-门店ID")
    @GetMapping("/oldOrderSyncByStoreId")
    public Result<String> oldOrderSyncByStoreId(@RequestParam Integer storeId) {
        return Result.success(monthOrderWxCommandService.oldOrderSyncByStoreId(storeId));
    }

    @ApiOperation(value = "ruleDubboTest")
    @PostMapping("/ruleTest")
    public Result<HitRuleVo> importRankData(@RequestBody OrderFact orderFact) {

        ExecuteRuleV2Req executeRuleV2Req = new ExecuteRuleV2Req();
        executeRuleV2Req.setSceneCode(orderFact.getTenantId());
        //executeRuleV2Req.setFactObj(orderFact);

        Map<String, Object> map = BeanUtil.beanToMap(orderFact);
        executeRuleV2Req.setFactObj(map);

        HitRuleVo listResult = ruleLinkClient.hitOneRule(executeRuleV2Req);

        return Result.success(listResult);
    }


    @ApiOperation(value = "createEsignCompanyTest")
    @PostMapping("/createEsignCompanyTest")
    public Result<String> createEsignCompanyTest(@RequestBody CompanyAccountRequestDTO requestDTO) {


        log.info("companyAccountRequestDTO:{}", JSONUtil.toJsonStr(requestDTO));

        String companyAccount = eSignProvider.createCompanyAccount(requestDTO);
        log.info("companyAccount:{}", companyAccount);


        return Result.success(companyAccount);
    }

    @ApiOperation("根据姓名或手机号模糊查询客户简洁信息")
    @PostMapping("/getByCustomerNameOrPhone/like")
    public Result<PageVO<BasicSimpInfoVO>> getByCustomerNameOrPhone(@Valid @RequestBody BasicQueryRequest request) {
        return operationalToolService.getByCustomerNameOrPhone(request);
    }

    @ApiOperation("根据手机号精确查询客户简洁信息")
    @PostMapping("/getByCustomerPhone")
    public Result<BasicSimpInfoVO> getByCustomerPhone(@Valid @RequestBody BasicQueryRequest request) {
        return operationalToolService.getByCustomerPhone(request);
    }

    @ApiOperation("根据姓手机号查询销售简洁信息")
    @PostMapping("/getSaleSimpInfoByPhone")
    public Result<SaleSimpInfoVO> getSaleSimpInfoByPhone(@Valid @RequestBody SaleQueryRequest request) {
        return operationalToolService.getSaleSimpInfoByPhone(request);
    }


    @ApiOperation("根据订单号查询销售简洁信息")
    @PostMapping("/getSaleSimpInfoByOrderNo")
    public Result<SaleSimpInfoVO> getSaleSimpInfoByOrderNo(@Valid @RequestBody SaleQueryByOrderRequest request) {
        return operationalToolService.getSaleSimpInfoByOrderNo(request);
    }

    @ApiOperation("根据订单号查询订单简洁信息")
    @PostMapping("/getOrderSimpInfo")
    public Result<OrderSimpInfoVO> getOrderSimpInfoVO(@Valid @RequestBody OrderSimpInfoQuery query) {
        return operationalToolService.getOrderSimpInfo(query);
    }

    @ApiOperation("修改订单已付金额")
    @PostMapping("/orderChangeAmount")
    @OperateLog
    public Result<String> orderChangeAmount(@Valid @RequestBody OrderChangeAmountRequest request) {
        return operationalToolService.orderChangeAmount(request);
    }

    @ApiOperation("更换客户手机号")
    @PostMapping("/customer/swapPhone")
    @OperateLog
    public Result<String> swapPhone(@Valid @RequestBody SwapPhoneReq req) {
        return operationalToolService.swapPhone(req);
    }


    @ApiOperation("订单更换销售")
    @PostMapping("/order/swapSale")
    @OperateLog
    public Result<String> swapSale(@Valid @RequestBody OrderSwapSaleRequest req) {
        return operationalToolService.swapSale(req);
    }


    @ApiOperation("订单变更发送MQ")
    @GetMapping("/send/order/event/mq")
    public Result sendEventMq(Integer orderId) {
        return operationalToolService.sendEventMq(orderId);
    }

    @ApiOperation("房态获取订单信息")
    @GetMapping("/queryOrderForRoom")
    public Result<OrderInfoDTO> queryOrderForRoom(String orderNo) {
        return orderInfoService.queryOrderForRoom(orderNo);
    }


    @ApiOperation("履约查询订单信息")
    @PostMapping("/queryOrderForFulfillment")
    public Result<List<OrderFulfillmentRes>> queryOrderForFulfillment(@RequestBody OrderFulfillmentReq req) {
        return orderInfoService.queryOrderFulfillment(req);
    }

    @ApiOperation("批量获取护理获取订单信息")
    @PostMapping("/queryOrderForCare")
    public Result<List<OrderDataRemindDTO>> queryOrderForCare(@Valid @RequestBody List<String> orderNos) {
        return orderInfoService.queryOrderForCare(orderNos);
    }

    @ApiOperation(value = "he_contract_sign_record 表流程id撤销")
    @PostMapping("/cancleSignFlowsContractTable")
    public Result<List<ESignCancleFlowRecordVO>> cancleSignFlowsContractTable(@Valid @RequestBody List<ESignFlowRecordReq> eSignFlowRecordReqList) {
        return Result.success(monthContractAssembler.cancleFlowRecord(eSignFlowRecordReqList));
    }

    @ApiOperation(value = "he_contract_sign_agreement 表流程id撤销")
    @PostMapping("/cancleSignFlowsAgreementTable")
    public Result<List<ESignCancleFlowRecordVO>> cancleSignFlowsAgreementTable(@Valid @RequestBody List<ESignFlowRecordReq> eSignFlowRecordReqList) {
        return Result.success(monthContractAssembler.cancleSignFlowsAgreementTable(eSignFlowRecordReqList));
    }

    @ApiOperation(value = "手动发送财务邮件-订单变更")
    @PostMapping("/sendMailToFinance")
    public Result sendMailToFinance(@Valid @RequestBody List<SendMailToFinanceRequest> requestList) {

        for (SendMailToFinanceRequest request : requestList) {
            mailManager.sendMailToFinance(request);
        }
        return Result.success();
    }

    @ApiOperation("手机号查询-lamada")
    @PostMapping("/query/phone1")
    @OperateLog
    public Result<List<HeOrderUserSnapshotEntity>> phoneQuery1(@Valid @RequestBody SwapPhoneReq req) {
        List<HeOrderUserSnapshotEntity> heOrderUserSnapshotEntities = orderUserSnapshotRepository.queryByUserIdOrNameOrSource(null, req.getNewPhone(), null);
        return Result.success(heOrderUserSnapshotEntities);
    }

    @ApiOperation("手机号查询-xml")
    @PostMapping("/query/phone2")
    @OperateLog
    public Result<List<HeOrderUserSnapshotEntity>> phoneQuery2(@Valid @RequestBody SwapPhoneReq req) {
        List<HeOrderUserSnapshotEntity> heOrderUserSnapshotEntities = orderUserSnapshotRepository.queryByPhoneForTest(req.getNewPhone());
        return Result.success(heOrderUserSnapshotEntities);
    }

    @ApiOperation("listOrderV1")
    @GetMapping("/order/listOrderV1")
    public Result<List<ListOrderV1Res>> listOrderV1(@Valid String orderNo) {
        List<ListOrderV1Res> listOrderV1Res = orderInfoService.listOrderV1(Lists.newArrayList(orderNo));
        return Result.success(listOrderV1Res);
    }

    @ApiOperation("推送产康客户服务数据")
    @GetMapping("/pushProductionCustomerServices")
    public Result<Void> pushProductionCustomerServices(String startDate, String endDate) {
        jobHandler.pushProductionCustomerServices(
                DateUtils.parseDate(startDate),
                DateUtils.parseDate(endDate)
        );
        return Result.success();
    }

    @ApiOperation("修改客户姓名，手机号")
    @PostMapping("/updateCustomerNamePhone")
    public Result<Void> updateCustomerNamePhone(@RequestBody UpdateCustomerNamePhoneReq req) {
        return Result.success(operationalToolService.updateCustomerNamePhone(req));
    }


    @ApiOperation("更新认证方式为邮箱认证")
    @PostMapping("/updateClientAuthType")
    public Result<Void> updateClientAuthType(@RequestBody UpdateCustomerNamePhoneReq req) {
        return Result.success(operationalToolService.updateClientAuthType(req));
    }

    @ApiOperation("查询战区净业绩")
    @GetMapping("/getRankData")
    public Result<List<InnerRankRes>> getRankData(@RequestParam Integer year, @RequestParam Integer month) {
        return Result.success(incomeTotalAssembler.getRankData(year, month));
    }

    @ApiOperation("资产发放-赠品订单")
    @GetMapping("/giftOrderAssetGrant")
    public Result<ProductionPropertyListVo> giftOrderAssetGrant(@RequestParam Integer orderId) {

        HeOrderEntity byOrderId = orderRepository.getByOrderId(orderId);
        if (byOrderId == null) {
            return Result.failed("订单不存在");
        }
        byOrderId.setJustFullPayment(true);
        FlowContext bizContext = new FlowContext();
        bizContext.setAttribute(HeOrderEntity.class, byOrderId);
        giftOrderAssetGrantProcessor.run(bizContext);
        return Result.success();
    }

    @ApiOperation("订单同步ECP")
    @GetMapping("/syncEcpProcessor")
    public Result<String> syncEcpProcessor(@RequestParam Integer orderId) {

        HeOrderEntity byOrderId = orderRepository.getByOrderId(orderId);
        if (byOrderId == null) {
            return Result.failed("订单不存在");
        }
        byOrderId.setJustEffect(true);
        FlowContext bizContext = new FlowContext();
        bizContext.setAttribute(HeOrderEntity.class, byOrderId);
        syncEcpProcessor.run(bizContext);
        syncOrder2ScrmProcessor.run(bizContext);
        return Result.success();
    }

    @ApiOperation("订单资产生效")
    @GetMapping("/syncOrderAsset")
    public Result<String> syncOrderAsset(@RequestParam Integer orderId, @RequestParam Integer incomeId) {

        HeOrderEntity byOrderId = orderRepository.getByOrderId(orderId);
        if (byOrderId == null) {
            return Result.failed("订单不存在");
        }
        HeIncomeRecordEntity oneById = incomeRecordRepository.getOneById(incomeId);
        if (Objects.isNull(oneById)){
            return Result.failed("支付记录不存在");
        }
        FlowContext bizContext = new FlowContext();
        bizContext.setAttribute(HeOrderEntity.class, byOrderId);
        bizContext.setAttribute(HeIncomeRecordEntity.class, oneById);

        assetEffectProcessor.run(bizContext);
        return Result.success();
    }

    @ApiOperation("订单资产生效-批量测试")
    @GetMapping("/syncOrderAssetTest")
    public Result<String> syncOrderAssetTest(@RequestParam Integer orderId, @RequestParam Integer id) {

        String str = "[152683,152555,152432,152535,152385,152386,152387,152388,152389,152390,152391,152393,152403,152394,152395,152396,152397,152398,152399,152400,152405,152404,152406,152408,152409,152411,152412,152894,152418,152419,152421,152422,152426,152425,152428,152429,152430,152431,152434,152481,152442,152445,152446,152447,152450,152459,152460,152461,152462,152463,152464,152466,152467,152483,152475,152477,152479,152480,152485,152486,152490,152491,152492,152503,152506,152507,152522,152508,152512,152513,152515,152519,152520,152525,152526,152530,152531,152532,152536,152537,152538,152540,152541,152542,152543,152544,152546,152547,152548,152549,152550,152552,152554,152556,152557,152559,152561,152560,152567,152565,152564,152568,152569,152578,152573,152581,152579,152580,152582,152583,152584,152585,152588,152590,152591,152593,152592,152596,152668,152601,152604,152618,152608,152610,152611,152614,152620,152621,152623,152634,152624,152625,152626,152628,152629,152633,152635,152639,152640,152646,152641,152643,152644,152645,152647,152651,152652,152667,152658,152659,152660,152661,152663,152666,152670,152671,152672,152673,152674,152675,152677,152681,152682,152689,152690,152691,152692,152693,152695]";
        List<Integer> incomeIdList = JSONUtil.toList(str, Integer.class);

        for (Integer incomeId : incomeIdList) {

            HeIncomeRecordEntity oneById = incomeRecordRepository.getOneById(incomeId);
            if (Objects.isNull(oneById) || Objects.isNull(oneById.getOrderId())){
                continue;
            }

            HeOrderEntity byOrderId = orderRepository.getByOrderId(oneById.getOrderId());
            if (byOrderId == null) {
                continue;
            }
            FlowContext bizContext = new FlowContext();
            bizContext.setAttribute(HeOrderEntity.class, byOrderId);
            bizContext.setAttribute(HeIncomeRecordEntity.class, oneById);
            assetEffectProcessor.run(bizContext);
        }
        return Result.success();
    }


}

package com.stbella.order.controller.admin.v2;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stbella.core.base.PageVO;
import com.stbella.core.result.Result;
import com.stbella.mvc.aspect.OperateLog;
import com.stbella.order.common.utils.DateUtils;
import com.stbella.order.common.utils.ExcelUtil;
import com.stbella.order.infrastructure.oss.OSSFactory;
import com.stbella.order.server.manager.CareManager;
import com.stbella.order.server.order.month.component.EcpOrderSyncAssembler;
import com.stbella.order.server.order.month.dto.PageDTO;
import com.stbella.order.server.order.month.enums.CustomerFromTypeEnum;
import com.stbella.order.server.order.month.req.*;
import com.stbella.order.server.order.month.res.*;
import com.stbella.order.server.order.month.service.MonthOrderAdminCommandService;
import com.stbella.order.server.order.month.service.MonthOrderAdminQueryService;
import com.stbella.order.server.order.order.api.OrderInfoService;
import com.stbella.order.server.order.order.res.OrderExport;
import com.stbella.order.server.order.order.res.OrderMainInfo;
import com.stbella.order.server.oss.response.OssUploadResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-30 17:39
 */
@Validated
@Api(tags = "月子订单-后台")
@RestController
@RequestMapping("/order/month/v2")
@Slf4j
public class MonthOrderController {


    @Resource
    private MonthOrderAdminQueryService monthOrderAdminQueryService;
    @Resource
    private MonthOrderAdminCommandService monthOrderAdminCommandService;
    @Resource
    private CareManager careManager;
    @Resource
    private EcpOrderSyncAssembler ecpOrderSyncAssembler;
    @Resource
    private HttpServletResponse response;
    @Resource
    private OrderInfoService orderInfoService;

    @ApiOperation(value = "分页查询订单列表")
    @PostMapping("/queryPage")
    public Result<Page<QueryOrderPageVO>> queryPage(@RequestBody QueryOrderPageReq queryOrderPageReq) {
        return Result.success(monthOrderAdminQueryService.queryPage(queryOrderPageReq));
    }

    @ApiOperation(value = "分页查询订单列表")
    @PostMapping("/page")
    public Result<PageDTO<HeOrderVO>> page(@RequestBody OrderPageReq req) {
        log.info("订单列表查询请求开始处理req:{}", req);
        try {
            return Result.success(monthOrderAdminQueryService.page(req));
        } catch (Exception e) {
            log.error("订单列表查询发生异常", e);
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation(value = "订单列表导出校验")
    @GetMapping("/queryPageVerify")
    public Result queryPageVerify(@RequestBody QueryOrderPageReq queryOrderPageReq) {
        return Result.success(monthOrderAdminQueryService.queryPageVerify(queryOrderPageReq));
    }

    @ApiOperation(value = "订单列表导出")
    @GetMapping("/stm/order/export")
    public void stmOrderExport(QueryOrderPageReq queryOrderPageReq) {
        monthOrderAdminQueryService.stmOrderExport(queryOrderPageReq, response, false);
    }

    @ApiOperation(value = "订单列表导出（包含手机号）")
    @GetMapping("/stm/order/exportIncludeMobile")
    public void stmOrderExportIncludeMobile(QueryOrderPageReq queryOrderPageReq) {
        monthOrderAdminQueryService.stmOrderExport(queryOrderPageReq, response, true);
    }

    @ApiOperation(value = "根据订单号查询-订单详情-基本信息")
    @GetMapping("/queryOrderBasicInfoByOrderId")
    public Result<STMOOrderBasicInfoVO> queryOrderBasicInfoByOrderNo(@RequestParam("orderId") Integer orderId) {
        return monthOrderAdminQueryService.queryOrderBasicInfoByOrderNo(orderId);
    }

    @ApiOperation(value = "根据订单号查询-订单详情-订单信息")
    @GetMapping("/queryByOrderNo/orderInfo")
    public Result<STMOrderInfoVO> queryByOrderIdOrderInfo(@RequestParam Integer orderId) {
        return monthOrderAdminQueryService.queryByOrderIdOrderInfo(orderId);
    }

    @ApiOperation(value = "根据订单号查询-续住信息")
    @GetMapping("/queryByOrderNo/stayOver")
    public Result<OrderStayOverInfoVO> queryByOrderIdStayOver(Integer orderId) {
        return null;
    }

    @ApiOperation(value = "根据订单号查询-房型变更信息")
    @GetMapping("/queryByOrderNo/changeRoom}")
    public Result<OrderRoomChangeInfoVO> queryByOrderIdChangeRoom(Integer orderId) {
        return null;
    }


    @ApiOperation(value = "根据订单号查询-订单详情-财务信息")
    @GetMapping("/queryByOrderNo/financialInformation")
    public Result<STMOrderInfoFinancialVO> queryByOrderIdInancialInformation(Integer orderId) {
        return monthOrderAdminQueryService.queryByOrderIdInancialInformation(orderId);
    }

    @ApiOperation(value = "根据订单号查询-订单详情-额外礼赠")
    @GetMapping("/queryByOrderNo/gift")
    public Result<STMOrderInfoGiftVO> queryByOrderIdGift(@RequestParam("orderId") Integer orderId) {
        return monthOrderAdminQueryService.queryByOrderIdGift(orderId);
    }

    @ApiOperation(value = "根据订单号查询-订单详情-分娩喜报")
    @GetMapping("/queryByOrderNo/goodNewsChildbirth")
    public Result<STMOrderInfoLaborVO> queryByOrderIdGoodNewsChildbirth(@RequestParam("orderId") Integer orderId) {
        return monthOrderAdminQueryService.queryByOrderIdGoodNewsChildbirth(orderId);
    }

    @ApiOperation(value = "根据订单号查询-订单详情-分娩喜报(byOrderNo)")
    @GetMapping("/queryByOrderNo/goodNewsChildbirth-byOrderNo")
    public Result<STMOrderInfoLaborVO> queryByOrderIdGoodNewsChildbirth(@RequestParam("orderNo") String orderNo) {
        return monthOrderAdminQueryService.queryByOrderIdGoodNewsChildbirth(orderNo);
    }


    @ApiOperation(value = "资金记录导出")
    @PostMapping("/moneyRecordExportExcel")
    @ResponseBody
    public void moneyRecordExportExcel() {

    }

    @ApiOperation(value = "人工报单")
    @PostMapping("/notice")
    @OperateLog
    public Result notice(@Valid @RequestBody OrderNoticeReq orderNoticeReq) {
        orderNoticeReq.setPercentFirstTime(Math.toIntExact(System.currentTimeMillis() / 1000));
        return monthOrderAdminCommandService.userSendNotice(orderNoticeReq);

    }

    /***
     * 千万报单服务
     * 报单类型1-1千万报单;2-2千万报单;3-3千万报单;4-4千万报单;5-5千万报单;6-6千万报单;7-7千万报单;8-8千万报单;9-9千万报单
     */
    @ApiOperation(value = "人工千万报单")
    @GetMapping("/qianwan/notice")
    public Result<String> qianwanNotice(Integer year, Integer month) {
        return monthOrderAdminCommandService.orderQianwanNotice(year, month);

    }

    @ApiOperation(value = "订单手动推送房态")
    @GetMapping("/orderPushRoom")
    public Result orderPushRoom(@RequestParam("orderId") Integer orderId) {
        careManager.autoCreateFangTai(orderId);
        return Result.success();
    }

    @ApiOperation("根据订单ID-批量推送房态")
    @PostMapping("/batch/orderPushRoom")
    public Result<String> oldOrderSyncByStoreId(@RequestBody List<Integer> orderIds) {
        for (Integer orderId : orderIds) {
            careManager.autoCreateFangTai(orderId);
            try {
                Thread.sleep(2000L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        return Result.success();
    }

    @ApiOperation(value = "订单手动推送ecp")
    @GetMapping("/orderPushEcp")
    public Result orderPushEcp(@RequestParam("orderId") Integer orderId) {
        ecpOrderSyncAssembler.syncEcpOrder(orderId);
        return Result.success();
    }

    @ApiOperation(value = "剃除业绩/恢复业绩")
    @GetMapping("/deleteOrRecoverPercent")
    @OperateLog
    public Result deleteOrRecoverPercent(@RequestParam("orderId") Integer orderId, @RequestParam("operationType") Integer operationType) {
        monthOrderAdminCommandService.deleteOrRecoverPercent(orderId, operationType);
        return Result.success();
    }

    @ApiOperation(value = "获取所有客户来源渠道")
    @GetMapping("/customerSource")
    public Result customerSource() {
        return Result.success(CustomerFromTypeEnum.getAllEnums());
    }

    @ApiOperation(value = "订单审批状态变更通过")
    @PostMapping("/changeApproval")
    @OperateLog
    public Result changeApproval(@Valid @RequestBody OrderApprovalReq orderApprovalReq) {
        return monthOrderAdminCommandService.changeApproval(orderApprovalReq);
    }

    @ApiOperation(value = "根据支付时间查询-客户信息")
    @PostMapping("/queryOrderForPayFirstRecord")
    public Result<List<BasicSimpInfoVO>> queryOrderForPayFirstRecord(@RequestBody PayFirstRecordReq req) {
        return monthOrderAdminQueryService.queryOrderForPayFirstRecord(req);
    }

    @PostMapping("/orderExport")
    @ApiOperation("PICP订单列表导出")
    public Result<?> orderExport(@RequestBody OrderPageReq req) {
        log.info("订单列表导出请求开始处理req:{}", req);
//        return monthOrderAdminQueryService.execOrderExport(req);
        try {
            long currentTimeMillis = System.currentTimeMillis();
            List<OrderExport> source = monthOrderAdminQueryService.orderExport(req);
            log.info("订单数据源获取耗时{}", System.currentTimeMillis() - currentTimeMillis);
            Workbook sheets = ExcelUtil.exportData(new OrderExport(), source);
            byte[] bytes = getByteArray(sheets).toByteArray();
            log.info("订单数据导出耗时{}", System.currentTimeMillis() - currentTimeMillis);
            OssUploadResponse ossUploadResponse = OSSFactory.build().upload(bytes, String.format("/picp/订单中心/月子订单/%s/%s.xlsx", System.currentTimeMillis(), "订单".concat(DateUtils.format(new Date(), DateUtils.YYYY_MM_DD))));
            log.info("订单数据导出上传oss耗时{}", System.currentTimeMillis() - currentTimeMillis);
            return Result.success(ossUploadResponse);
        } catch (Exception e) {
            log.error("订单列表导出发生异常", e);
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation(value = "查询订单是否有效(入馆服务调研)")
    @GetMapping("/survey/effective")
    public Result<Integer> queryOrderEffective(@RequestParam String orderNo, @RequestParam Integer basicId) {
        return monthOrderAdminQueryService.queryOrderEffective(orderNo, basicId);
    }

    @ApiOperation(value = "通过订单号获取订单主体信息")
    @GetMapping("/queryOrderInfo")
    public Result<OrderMainInfo> queryOrderInfo(String orderNo) {
        return orderInfoService.queryOrderInfo(orderNo);
    }

    private ByteArrayOutputStream getByteArray(Workbook workbook) throws IOException {
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        if (workbook != null) {
            workbook.write(stream);
        }
        return stream;
    }

    @ApiOperation(value = "根据客户或者手机号查询是否有包含某些资产类型的订单以及商品")
    @PostMapping("/queryOrderGoodsInfoByPhoneOrName")
    public PageVO<OrderGoodsInfoAndUserInfoVO> queryOrderGoodsInfoByPhoneOrName(@RequestBody QueryOrderGoodsInfoByPhoneOrNameReq queryOrderGoodsInfoByPhoneOrNameReq) {
        return monthOrderAdminQueryService.queryOrderGoodsInfoByPhoneOrName(queryOrderGoodsInfoByPhoneOrNameReq);
    }


    @ApiOperation(value = "修改门店业绩/操作管理搜索订单")
    @PostMapping("/performance/searchOneOrder")
    public Result<List<OrderPerformanceVO>> searchOneOrder(@RequestBody OrderPerformanceReq req) {
        log.info("修改门店业绩/操作管理搜索订单请求开始req:{}", req);
        try {
            return monthOrderAdminQueryService.searchOneOrder(req);
        } catch (Exception e) {
            log.error("修改门店业绩/操作管理搜索订单发生异常", e);
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation(value = "修改门店业绩/修改记录搜索订单")
    @PostMapping("/performance/searchUpdateOrder")
    public Result<PageDTO<OrderPerformanceVO>> performanceSearchUpdateOrder(@RequestBody OrderPerformanceReq req) {
        log.info("修改门店业绩/修改记录搜索订单开始req:{}", req);
        try {
            return monthOrderAdminQueryService.performanceSearchUpdateOrder(req);
        } catch (Exception e) {
            log.error("修改门店业绩/修改记录搜索订单发生异常", e);
            return Result.failed(e.getMessage());
        }
    }

}

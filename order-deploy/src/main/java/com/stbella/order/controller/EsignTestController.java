package com.stbella.order.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.stbella.core.result.Result;
import com.stbella.mvc.aspect.OperateLog;
import com.stbella.order.domain.client.RuleLinkClient;
import com.stbella.order.server.contract.dto.CompanyAccountRequestDTO;
import com.stbella.order.server.contract.provider.month.ESignProvider;
import com.stbella.order.server.fact.OrderFact;
import com.stbella.order.server.order.cts.service.CtsPayService;
import com.stbella.order.server.order.job.YjOrderRefundCheckJobHandler;
import com.stbella.order.server.order.month.request.pay.IncomeRecordTransferRequest;
import com.stbella.order.server.order.month.service.MonthOrderAdminQueryService;
import com.stbella.order.server.order.month.service.MonthOrderWxCommandService;
import com.stbella.order.server.order.production.res.ProductionPropertyListVo;
import com.stbella.pay.server.rules.req.ExecuteRuleV2Req;
import com.stbella.pay.server.rules.res.HitRuleVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Map;

/**
 * @description:
 * @author: Seven
 * @time: 2022/10/14 16:29
 */
@Validated
@Api(tags = "订单运营工具类")
@RestController
@RequestMapping("/test/esign")
@Slf4j
public class EsignTestController {




    @Resource
    ESignProvider eSignProvider;


    @ApiOperation(value = "createEsignCompanyTest")
    @PostMapping("/createEsignCompanyTest")
    public Result<String> createEsignCompanyTest(@RequestBody CompanyAccountRequestDTO requestDTO){
        log.info("companyAccountRequestDTO:{}", JSONUtil.toJsonStr(requestDTO));
        String companyAccount = eSignProvider.createCompanyAccount(requestDTO);
        log.info("companyAccount:{}",companyAccount);
        return Result.success(companyAccount);
    }

    @ApiOperation(value = "queryCompanyAccount 查询机构")
    @PostMapping("/queryCompanyAccount")
    public Result<String> queryCompanyAccount(@RequestBody CompanyAccountRequestDTO requestDTO){
        log.info("companyAccountRequestDTO:{}", JSONUtil.toJsonStr(requestDTO));
        String companyAccount = eSignProvider.queryCompanyAccount(requestDTO.getThirdPartyUserId());
        log.info("companyAccount:{}",companyAccount);
        return Result.success(companyAccount);
    }

    @ApiOperation(value = "queryCompanySealList 查询机构seal")
    @PostMapping("/queryCompanySealList")
    public Result<String> queryCompanySealList(@RequestBody CompanyAccountRequestDTO requestDTO){
        log.info("companyAccountRequestDTO:{}", JSONUtil.toJsonStr(requestDTO));
        String companyAccount = eSignProvider.queryCompanySealList(requestDTO.getThirdPartyUserId());
        log.info("companyAccount:{}",companyAccount);
        return Result.success(companyAccount);
    }
}

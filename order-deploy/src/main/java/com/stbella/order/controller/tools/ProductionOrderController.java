package com.stbella.order.controller.tools;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stbella.core.result.Result;
import com.stbella.order.server.order.month.req.platform.PlatformOrderPageRequest;
import com.stbella.order.server.order.month.req.platform.PlatformOrderRequest;
import com.stbella.order.server.order.month.res.PlatformOrdeInfoVO;
import com.stbella.order.server.order.month.res.QueryOrderPageVO;
import com.stbella.order.server.order.month.service.PlatformOrderQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-28 13:39
 */
@Validated
@Api(tags = "产康订单操作")
@RestController
@RequestMapping("/order/production/")
@Slf4j
public class ProductionOrderController {


    @Resource
    private PlatformOrderQueryService platformOrderQueryService;


    @ApiOperation(value = "分摊实付")
    @PostMapping("/split")
    public Result<Page<QueryOrderPageVO>> split(@RequestBody PlatformOrderPageRequest platformOrderPageRequest) {
        return null;
    }


}

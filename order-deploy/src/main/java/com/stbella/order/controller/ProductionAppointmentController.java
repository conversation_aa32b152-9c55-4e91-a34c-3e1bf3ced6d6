package com.stbella.order.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.stbella.core.base.PageVO;
import com.stbella.core.result.Result;
import com.stbella.mvc.base.BaseController;
import com.stbella.order.infrastructure.repository.mapper.saas.OrderProductionExtendMapper;
import com.stbella.order.server.order.order.req.ExportProductionRealPaidReq;
import com.stbella.order.server.order.order.req.BatchProductionExportReq;
import com.stbella.order.common.exception.ApplicationException;
import com.stbella.order.server.order.order.req.ProductionRealPaidReq;
import com.stbella.order.server.order.production.api.ProductionAppointmentCommandService;
import com.stbella.order.server.order.production.api.ProductionAppointmentQueryService;
import com.stbella.order.server.order.production.req.*;
import com.stbella.order.server.order.production.res.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import com.stbella.order.server.order.production.req.AppointmentBasicReq;
import com.stbella.order.server.order.production.req.AppointmentBehalfConsumerQuery;
import com.stbella.order.server.order.production.req.AppointmentBehalfTImeQuery;
import com.stbella.order.server.order.production.req.AppointmentBoardTherapistQuery;
import com.stbella.order.server.order.production.req.AppointmentCreateReq;
import com.stbella.order.server.order.production.req.AppointmentProductionScheduleQuery;
import com.stbella.order.server.order.production.req.AppointmentServiceFeeByTherapistIdAndSkuIDQuery;
import com.stbella.order.server.order.production.req.AppointmentUpdateReq;
import com.stbella.order.server.order.production.req.AppointmentWriteOffReq;
import com.stbella.order.server.order.production.req.ProductionAppointmentBackQuery;
import com.stbella.order.server.order.production.req.ProductionAppointmentInfoBackQuery;
import com.stbella.order.server.order.production.req.ProductionAppointmentInfoQuery;
import com.stbella.order.server.order.production.req.ProductionAppointmentQuery;
import com.stbella.order.server.order.production.res.ProductionAppointmentBackVo;
import com.stbella.order.server.order.production.res.ProductionAppointmentBoardWorkmanshipVo;
import com.stbella.order.server.order.production.res.ProductionAppointmentDetailVO;
import com.stbella.order.server.order.production.res.ProductionAppointmentGoodsSkuInfoVo;
import com.stbella.order.server.order.production.res.ProductionAppointmentInfoBackVo;
import com.stbella.order.server.order.production.res.ProductionAppointmentMiniVo;
import com.stbella.order.server.order.production.res.ProductionAppointmentProductionScheduleVo;
import com.stbella.order.server.order.production.res.ProductionAppointmentServiceFeeVo;
import com.stbella.order.server.order.production.res.ProductionAppointmentTherapistVo;
import com.stbella.order.server.order.production.res.ProductionAppointmentVo;
import com.stbella.order.server.order.production.res.ProductionBehalfTimeShaftVo;
import com.stbella.order.server.order.production.res.ProductionBoardAppointmentVo;
import com.stbella.order.server.order.production.res.StoreVO;

import com.stbella.store.common.enums.ErrorCodeEnum;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-30 17:39
 */
@Validated
@Api(tags = "产康预约")
@RestController
@RequestMapping("/order/appointment")
@Slf4j
public class ProductionAppointmentController extends BaseController {

    @Resource
    ProductionAppointmentQueryService appointmentQueryService;
    @Resource
    private ProductionAppointmentCommandService productionAppointmentCommandService;

    /**
     * 查询产康师搜索预约列表
     *
     * @param query 查询
     * @return {@link Result}<{@link PageVO}<{@link List}<{@link ProductionAppointmentVo}>>>
     */
    @ApiOperation("产康师 预约单项目列表")
    @PostMapping("/production/query/list")
    public Result<PageVO<ProductionAppointmentVo>> querySearchAppointmentList(@RequestBody ProductionAppointmentQuery query) {
        return appointmentQueryService.querySearchAppointmentList(query);
    }

    /**
     * 查询搜索产康预约面板
     *
     * @param query 查询
     * @return {@link Result}<{@link List}<{@link ProductionAppointmentVo}>>
     */
    @ApiOperation("产康师 预约单看板")
    @PostMapping("/production/query/boardList")
    public Result<ProductionBoardAppointmentVo> querySearchBoardAppointmentList(@RequestBody ProductionAppointmentQuery query) throws Exception {
        query.setPageSize(10000);
        return appointmentQueryService.querySearchBoardAppointmentList(query);
    }
    /**
     * 【后台】预约单列表
     * @param query 筛选
     * @return Result<PageVO<List<ProductionAppointmentBackVo>>>
     */
    @ApiOperation("预约单列表")
    @PostMapping("/back/appointment/query")
    public Result<PageVO<ProductionAppointmentBackVo>> queryAppointmentBackPage(@RequestBody ProductionAppointmentBackQuery query) {
        return appointmentQueryService.queryAppointmentBackPage(query);
    }

    @ApiOperation("预约单详情")
    @PostMapping("/back/appointment/info")
    public Result<ProductionAppointmentInfoBackVo> queryDetail(@RequestBody ProductionAppointmentInfoBackQuery query) {
        return appointmentQueryService.queryAppointmentBackDetail(query);
    }

    @ApiOperation(value = "后台预约单列表导出")
    @GetMapping("/export")
    public void doExportProductionBookList(ProductionAppointmentBackQuery query, HttpServletResponse response){
        appointmentQueryService.doExportProductionBookList(query, response);
    }

    @ApiOperation(value = "后台预约单列表导出")
    @GetMapping("/export-therapist-real-paid")
    public void therapistRealPaidExport(ProductionAppointmentBackQuery query, HttpServletResponse response){
        appointmentQueryService.therapistRealPaidExport(query, response);
    }

    @ApiOperation("【PI】产康师手工费")
    @PostMapping("/workmanship")
    public Result<ProductionAppointmentBoardWorkmanshipVo> queryWorkmanship(@RequestBody @Valid AppointmentBoardTherapistQuery query) {
        return appointmentQueryService.queryWorkmanship(query);
    }

    @ApiOperation("产康日程")
    @PostMapping("/production/schedule")
    public Result<List<ProductionAppointmentProductionScheduleVo>> queryProductionSchedule(@RequestBody AppointmentProductionScheduleQuery query) {
        return appointmentQueryService.queryProductionSchedule(query);
    }

    @ApiOperation("【MINI端 pi/c端】查询预约单列表")
    @PostMapping("/appointment/query")
    public Result<PageVO<ProductionAppointmentMiniVo>> queryAppointmentMiniPage(@RequestBody ProductionAppointmentBackQuery query) {
        return appointmentQueryService.queryAppointmentMiniPage(query);
    }

    @ApiOperation("创建预约单")
    @PostMapping("/create")
    public Result<Long> createAppointment(@Valid @RequestBody AppointmentCreateReq req) {
        return productionAppointmentCommandService.createAppointment(req);
    }

    @ApiOperation("更新预约单")
    @PostMapping("/update")
    public Result<Long> updateAppointment(@Valid @RequestBody AppointmentUpdateReq req) {
        return productionAppointmentCommandService.updateAppointment(req);
    }

    @ApiOperation("取消预约单")
    @PostMapping("/cancel")
    public Result<Boolean> cancelAppointment(@Valid @RequestBody AppointmentBasicReq req) {
        return productionAppointmentCommandService.cancelAppointment(req);
    }

    @ApiOperation("核销预约单")
    @PostMapping("/writeOff")
    public Result<Boolean> writeOffAppointment(@Valid @RequestBody AppointmentWriteOffReq req) {
        return productionAppointmentCommandService.writeOffAppointment(req);
    }

    @ApiOperation("预约单详情")
    @GetMapping("/detail")
    public Result<ProductionAppointmentDetailVO> detail(Long id) {
        return appointmentQueryService.queryProductionById(id);
    }

    @ApiOperation("预约-获取商品的数据")
    @PostMapping("/appointment/behalfGoodsInfo")
    public Result<ProductionAppointmentGoodsSkuInfoVo> behalfGoodsInfo(@RequestBody AppointmentBehalfConsumerQuery query) {
        return appointmentQueryService.behalfGoodsInfo(query);
    }

    @ApiOperation("预约-获取产康师对应的sku的服务费")
    @PostMapping("/appointment/getServiceFeeByTherapistIdAndSkuID")
    public Result<ProductionAppointmentServiceFeeVo> getServiceFeeByTherapistIdAndSkuID(@RequestBody AppointmentServiceFeeByTherapistIdAndSkuIDQuery query) {
        return appointmentQueryService.getServiceFeeByTherapistIdAndSkuID(query);
    }

    @ApiOperation("代客预约-获取预约时间数据")
    @PostMapping("/appointment/behalfTimeInfo")
    public Result<List<ProductionBehalfTimeShaftVo>> behalfTimeInfo(@RequestBody AppointmentBehalfTImeQuery query) {
        return appointmentQueryService.behalfTimeInfo(query);
    }

    @ApiOperation("预约列表-获取已预约门店&产康师列表")
    @PostMapping("/appointment/queryProductionByTherapistId")
    public Result<ProductionAppointmentTherapistVo> queryProductionByTherapistId(@Valid @RequestBody ProductionAppointmentInfoQuery query) {
        return appointmentQueryService.queryProductionByTherapistId(query);
    }

    @ApiOperation("查询门店统计金额")
    @PostMapping("/queryRealPaidSummary")
    public Result<ProductionRealPaidSummaryVO> queryRealPaidSummary(@Valid @RequestBody ProductionRealPaidReq req) {
        return appointmentQueryService.queryRealPaidSummary(req);
    }

    @ApiOperation(value = "产康师实付金额导出")
    @GetMapping("/exportTherapistRealPaid")
    public void exportTherapistRealPaid(ProductionRealPaidReq req, HttpServletResponse response) {
        appointmentQueryService.exportTherapistRealPaid(req, response);
    }

    @ApiOperation(value = "批量导出产康师核销金额")
    @PostMapping("/batchExportTherapistRealPaid")
    public Result<String> batchExportTherapistRealPaid(@Valid @RequestBody BatchProductionExportReq req) {
        req.setLoginUserDTO(this.currentUser());
        return Result.success(appointmentQueryService.batchExportTherapistRealPaid(req));
    }

    @ApiOperation(value = "批量导出客户核销金额")
    @PostMapping("/batchExportClientRealPaid")
    public Result<String> batchExportClientRealPaid(@Valid @RequestBody BatchProductionExportReq req) {
        req.setLoginUserDTO(this.currentUser());
        return Result.success(appointmentQueryService.batchExportClientRealPaid(req));
    }

    @ApiOperation(value = "批量导出门店汇总核销金额")
    @PostMapping("/batchExportSummaryRealPaid")
    public Result<String> batchExportSummaryRealPaid(@Valid @RequestBody BatchProductionExportReq req) {
        req.setLoginUserDTO(this.currentUser());
        return Result.success(appointmentQueryService.batchExportSummaryRealPaid(req));
    }

    @ApiOperation(value = "产康师实付金额汇总导出")
    @GetMapping("/exportTherapistRealPaidSummary")
    public void exportTherapistRealPaidSummary(String startDate, String endDate, HttpServletResponse response) {
        ProductionRealPaidReq req = new ProductionRealPaidReq();
        req.setWriteOffStartDate(DateUtil.parseDate(startDate));
        req.setWriteOffEndDate(DateUtil.parseDate(endDate));
        appointmentQueryService.exportTherapistRealPaidSummary(req, response);
    }

    @ApiOperation("产康师核销实付分页")
    @PostMapping("/queryTherapistRealPaid")
    public Result<PageVO<ProductionTherapistRealPaidVO>> queryTherapistRealPaid(@Valid @RequestBody ProductionRealPaidReq req) {
        return appointmentQueryService.queryTherapistRealPaid(req);
    }

    @ApiOperation("查询客户核销金额")
    @PostMapping("/queryClientRealPaid")
    public Result<PageVO<ProductionClientRealPaidVO>> queryClientRealPaid(@Valid @RequestBody ProductionRealPaidReq req) {
        return appointmentQueryService.queryClientRealPaid(req);
    }

    @ApiOperation("查询预约单核销金额")
    @PostMapping("/queryAppointmentRealPaid")
    public Result<PageVO<ProductionAppointmentRealPaidVO>> queryAppointmentRealPaid(@Valid @RequestBody ProductionRealPaidReq req) {
        return appointmentQueryService.queryAppointmentRealPaid(req);
    }

    @ApiOperation("导出核销金额汇总")
    @GetMapping("/exportTherapistSummary")
    public Result<Boolean> exportTherapistSummary(String startDate, String endDate, HttpServletResponse response) {
        ExportProductionRealPaidReq req = new ExportProductionRealPaidReq();
        req.setWriteOffStartDate(DateUtil.parseDate(startDate));
        req.setWriteOffEndDate(DateUtil.parseDate(endDate));
        appointmentQueryService.exportTherapistSummary(req, response);
        return Result.success();
    }
}

package com.stbella.order.controller.test;

import com.stbella.order.server.consumer.PicpDingNotityConsumer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.Message;
import org.apache.pulsar.client.api.MessageId;
import org.apache.pulsar.common.api.EncryptionContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description: TestErrorController
 * @date 2024/3/5 16:02
 */
@Validated
@Api(tags = "测试异常方法")
@RestController
@RequestMapping("/testError")
@Slf4j
public class TestErrorController {

    @Resource
    private PicpDingNotityConsumer picpDingNotityConsumer;


    @ApiOperation(value = "钉钉审批消息回调")
    @PostMapping("/picpDingNotityConsumer")
    public void picpDingNotityConsumer() {
        Message message = new Message() {
            @Override
            public Map<String, String> getProperties() {
                return null;
            }

            @Override
            public boolean hasProperty(String s) {
                return false;
            }

            @Override
            public String getProperty(String s) {
                return null;
            }

            @Override
            public byte[] getData() {
                return new byte[0];
            }

            @Override
            public Object getValue() {
                return null;
            }

            @Override
            public MessageId getMessageId() {
                return null;
            }

            @Override
            public long getPublishTime() {
                return 0;
            }

            @Override
            public long getEventTime() {
                return 0;
            }

            @Override
            public long getSequenceId() {
                return 0;
            }

            @Override
            public String getProducerName() {
                return null;
            }

            @Override
            public boolean hasKey() {
                return false;
            }

            @Override
            public String getKey() {
                return null;
            }

            @Override
            public boolean hasBase64EncodedKey() {
                return false;
            }

            @Override
            public byte[] getKeyBytes() {
                return new byte[0];
            }

            @Override
            public boolean hasOrderingKey() {
                return false;
            }

            @Override
            public byte[] getOrderingKey() {
                return new byte[0];
            }

            @Override
            public String getTopicName() {
                return null;
            }

            @Override
            public Optional<EncryptionContext> getEncryptionCtx() {
                return Optional.empty();
            }

            @Override
            public int getRedeliveryCount() {
                return 0;
            }

            @Override
            public byte[] getSchemaVersion() {
                return new byte[0];
            }

            @Override
            public boolean isReplicated() {
                return false;
            }

            @Override
            public String getReplicatedFrom() {
                return null;
            }
        };
        picpDingNotityConsumer.getMsg(message);
    }

}

package com.stbella.order.controller.admin.v2;

import com.stbella.core.result.Result;
import com.stbella.mvc.aspect.OperateLog;
import com.stbella.mvc.base.BaseController;
import com.stbella.order.server.order.month.request.pay.IncomeRecordTransferRequest;
import com.stbella.order.server.order.month.service.MonthOrderWxCommandService;
import com.stbella.platform.order.api.OldOrderTransService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;


@Validated
@Api(tags = "订单相关运行工具")
@RestController
@RequestMapping("")
@Slf4j
public class OrderToolsController extends BaseController {

    @Resource
    private MonthOrderWxCommandService monthOrderWxCommandService;
    @Resource
    private OldOrderTransService oldOrderTransService;

    @ApiOperation("订单支付记录转移")
    @PostMapping("/test/incomeRecordTransfer")
    @OperateLog
    public Result<Integer> incomeRecordTransfer(@Valid @RequestBody IncomeRecordTransferRequest request) {
        return monthOrderWxCommandService.incomeRecordTransfer(request);
    }


    @ApiOperation("支付记录分摊实付")
    @GetMapping("/test/allocationPayment")
    @OperateLog
    public Result<Integer> allocationPayment(String incomeSn) {
        return monthOrderWxCommandService.allocationPayment(incomeSn);
    }


    @ApiOperation("支付记录分摊实付")
    @GetMapping("/test/sendAllocationMq")
    @OperateLog
    public Result<Integer> sendAllocationMq(Integer incomeRecordId) {
        return monthOrderWxCommandService.sendAllocationMq(incomeRecordId);
    }


    @ApiOperation("修复分摊实付")
    @GetMapping("/test/fixAllocationPayment")
    public Result<?> fixAllocationPayment(@RequestParam List<Integer> orderId, @RequestParam Boolean fixAll) {
        return monthOrderWxCommandService.fixAllocationPayment(orderId, fixAll);
    }

    @ApiOperation("修复分摊实付2")
    @GetMapping("/test/fixAllocationPayment2")
    public Result<?> fixAllocationPayment2(@RequestParam List<String> orderSnList, @RequestParam Boolean fixAll) {
        return monthOrderWxCommandService.fixAllocationPayment2(orderSnList, fixAll);
    }

    @ApiOperation("修复分摊应付和实付")
    @GetMapping("/test/fixAllocationPayment3")
    public Result<?> fixAllocationPayment3(@RequestParam List<String> orderSnList, @RequestParam Boolean fixAll) {
        return monthOrderWxCommandService.fixAllocationPayment3(orderSnList, fixAll);
    }

    @ApiOperation("修复商品分摊数据")
    @GetMapping("/test/fixGoodsAllocation")
    public Result<?> fixGoodsAllocation() {
        return monthOrderWxCommandService.fixGoodsAllocation();
    }


    @ApiOperation("旧订单转新订单")
    @GetMapping("/test/transOldOrder2NewOrder")
    public Result<?> transOldOrder2NewOrder(@RequestParam Integer orderId) {
        oldOrderTransService.transOldOrder2NewOrder(orderId);
        return Result.success();
    }

    @ApiOperation("修复订单金额字段")
    @GetMapping("/fixAmount")
    public Result<?> fixAmount(@RequestParam Integer orderId) {
        oldOrderTransService.fixAmount(orderId);
        return Result.success();
    }


    @ApiOperation("修复订单商品金额字段")
    @GetMapping("/fixGoodsAmount")
    public Result<?> fixGoodsAmount(@RequestParam Boolean fixAll) {
        oldOrderTransService.fixGoodsAmount(fixAll);
        return Result.success();
    }
}

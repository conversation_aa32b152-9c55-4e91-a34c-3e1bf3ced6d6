package com.stbella.order.controller.admin.v2;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stbella.core.result.Result;
import com.stbella.order.server.order.month.req.QuerySbarGoodsOrderPageReq;
import com.stbella.order.server.order.month.res.QuerySbarGoodsOrderPageVO;
import com.stbella.order.server.order.month.service.SbarOrderAdminQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;


@Validated
@Api(tags = "Sbar-后台")
@RestController
@RequestMapping("/order/sbar")
@Slf4j
public class SbarOrderController {

    @Resource
    private SbarOrderAdminQueryService sbarOrderAdminQueryService;

    @ApiOperation(value = "分页查询产康/Sbar商品列表")
    @PostMapping("/queryPageForSbarGoods")
    public Result<Page<QuerySbarGoodsOrderPageVO>> queryPageForSbarGoods(@RequestBody QuerySbarGoodsOrderPageReq queryOrderPageReq) {
        return Result.success(sbarOrderAdminQueryService.queryPageForSbarGoods(queryOrderPageReq));
    }

    @ApiOperation(value = "导出产康/Sbar商品列表")
    @GetMapping("/queryPageForSbarGoods/export")
    public void queryPageForSbarGoodsExport(QuerySbarGoodsOrderPageReq queryOrderPageReq, HttpServletResponse response) {
        sbarOrderAdminQueryService.queryPageForSbarGoodsExport(queryOrderPageReq, response);
    }
}

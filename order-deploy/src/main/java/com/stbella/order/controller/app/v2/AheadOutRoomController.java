package com.stbella.order.controller.app.v2;

import com.stbella.core.result.Result;
import com.stbella.order.server.order.month.req.AheadOutRoomQuery;
import com.stbella.order.server.order.month.req.ClientRoomOutQuery;
import com.stbella.order.server.order.month.req.ClientRoomQuery;
import com.stbella.order.server.order.month.req.CreateAheadOutRoomReq;
import com.stbella.order.server.order.month.req.SaveAheadOutRoomReq;
import com.stbella.order.server.order.month.res.AheadOutRoomRefundAmountVO;
import com.stbella.order.server.order.month.res.AheadOutRoomVO;
import com.stbella.order.server.order.month.res.CalcOutRoomVO;
import com.stbella.order.server.order.month.res.RoomStateQueryVO;
import com.stbella.order.server.order.month.service.AheadOutRoomCommandService;
import com.stbella.order.server.order.month.service.AheadOutRoomQueryService;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-12 12:01
 */
@Validated
@Api(tags = "月子订单-提前离管")
@RestController
@RequestMapping("/order/outRoom")
@Slf4j
public class AheadOutRoomController {
    @Resource
    private AheadOutRoomCommandService aheadOutRoomCommandService;

    @Resource
    private AheadOutRoomQueryService aheadOutRoomQueryService;


    @ApiOperation(value = "创建")
    @PostMapping("/create")
    public Result<Integer> createAheadOutRoom(@Valid @RequestBody CreateAheadOutRoomReq req) {
        return aheadOutRoomCommandService.createAheadOutRoom(req);
    }

    @ApiOperation(value = "更新")
    @PostMapping("/update")
    public Result<Boolean> updateAheadOutRoom(@Valid @RequestBody SaveAheadOutRoomReq req) {
        return aheadOutRoomCommandService.updateAheadOutRoom(req);
    }

    @ApiOperation(value = "查询详情")
    @PostMapping("/queryByOrderId")
    public Result<AheadOutRoomVO> queryByOrderId(@Valid @RequestBody AheadOutRoomQuery query) {
        return aheadOutRoomQueryService.queryByOrderId(query);
    }

    @ApiOperation(value = "查询客户房态信息")
    @PostMapping("/queryClientRoomStateByOrderId")
    public Result<RoomStateQueryVO> queryClientRoomStateByOrderId(@Valid @RequestBody ClientRoomQuery query) {
        return aheadOutRoomQueryService.queryClientRoomStateByOrderId(query);
    }

    @ApiOperation(value = "提前离管费用计算")
    @PostMapping("/changeOutRoomDate")
    public Result<CalcOutRoomVO> changeOutRoomDate(@Valid @RequestBody ClientRoomOutQuery query) {
        return aheadOutRoomQueryService.changeOutRoomDate(query);
    }

    @ApiOperation(value = "提前离管剩余金额查询")
    @PostMapping("/queryRefundAmountByOrderId")
    public Result<AheadOutRoomRefundAmountVO> queryRefundAmountByOrderId(@Valid @RequestBody AheadOutRoomQuery query) {
        return aheadOutRoomQueryService.queryRefundAmountByOrderId(query);
    }

}

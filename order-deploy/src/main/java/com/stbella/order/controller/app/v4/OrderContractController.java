package com.stbella.order.controller.app.v4;

import com.stbella.core.annotations.PreventDuplication;
import com.stbella.core.result.Result;
import com.stbella.mvc.base.BaseController;
import com.stbella.order.server.order.month.response.ClientBooleanVO;
import com.stbella.order.server.order.month.response.OrderUserSnapshotVO;
import com.stbella.order.server.order.month.response.TabClientVO;
import com.stbella.platform.order.api.contract.api.ContractCommandService;
import com.stbella.platform.order.api.contract.api.ContractQueryService;
import com.stbella.platform.order.api.contract.api.HeOrderSnapshotService;
import com.stbella.platform.order.api.contract.req.*;
import com.stbella.platform.order.api.contract.res.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(tags = "订单合同相关")
@RequestMapping("/contract/v4")
public class OrderContractController extends BaseController {

    @Resource
    private ContractQueryService contractQueryService;
    @Resource
    private ContractCommandService contractCommandService;
    @Resource
    private HeOrderSnapshotService orderSnapshotService;

    @ApiOperation(value = "合同列表")
    @PostMapping("/list")
    public Result<ContractReconstructOrderRes> list(@Valid @RequestBody ContractBaseReq contractBaseReq) {
        try {
            return contractQueryService.list(contractBaseReq);
        } catch (Exception e) {
            log.error("订单下签合同列表发生异常", e);
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation(value = "提交认证订单合同主体信息")
    @PostMapping("/orderContractSnapshot")
    @Deprecated
    public Result<OrderContractSnapshotVO> orderContractSnapshot(@Valid @RequestBody OrderUserSnapshotReq req) {

        log.info("提交认证订单合同主体信息请求开始处理req:{}", req);
        if (Objects.isNull(req.getIsCardVerify())) {
            req.setIsCardVerify(0);
        }
        OrderContractSnapshotVO updateSnapshot = orderSnapshotService.updateSnapshot(req);
        return Result.success(updateSnapshot);
    }

    @ApiOperation(value = "委托人信息查询")
    @PostMapping("/queryBailorInfo")
    @Deprecated
    public Result<OrderUserSnapshotVO> queryBailorInfo(@Valid @RequestBody OrderUserSnapshotReq req) {
        log.info("委托人信息查询开始处理req:{}", req);
        OrderUserSnapshotVO tabClientBailorVO = orderSnapshotService.queryBailorInfo(req);
        return Result.success(tabClientBailorVO);
    }

    @ApiOperation(value = "客户信息查询")
    @PostMapping("/queryClientInfo")
    @Deprecated
    public Result<TabClientVO> queryClientInfo(@Valid @RequestBody OrderUserSnapshotReq req) {
        log.info("客户下单信息查询开始处理req:{}", req);
        TabClientVO tabClientVO = orderSnapshotService.queryClientInfo(req);
        return Result.success(tabClientVO);
    }

    @ApiOperation(value = "检查合同状态")
    @PostMapping("/checkContractStatus")
    @Deprecated
    public Result<?> checkContractStatus(@Valid @RequestBody ContractBaseReq req) {

        log.info("检查合同状态开始处理req:{}", req);
        try {
            contractCommandService.checkContractStatus(req);
            return Result.success();
        } catch (Exception e) {
            log.error("检查合同状态发生异常", e);
            return Result.failed(e.getMessage());
        }
    }

    /**
     * 接收订单id生成合同
     * 合同列表查询接口
     * 合同详情接口
     *
     * @param req
     * @return {@link Result}<{@link OrderContractSignRecordVO}>
     */
    @ApiOperation(value = "创建合同")
    @PostMapping("/createContract")
    @PreventDuplication
    public Result<OrderContractSignRecordVO> createContract(@Valid @RequestBody ContractBaseReq req) {
        return contractCommandService.createContract(req);
    }


    /**
     * 预览合同
     *
     * @param req
     * @return {@link Result}<{@link OrderContractSignRecordVO}>
     */
    @ApiOperation(value = "预览合同")
    @PostMapping("/previewContract")
    @PreventDuplication
    public Result<OrderContractPreviewVO> previewContract(@Valid @RequestBody ContractBaseReq req) {
        return contractCommandService.previewContract(req);
    }


    @ApiOperation(value = "合同提醒数量总计")
    @GetMapping("/remind/count")
    public Result<OrderContractRemindCountVO> remindCount(@RequestParam Integer operatorGuid) {
        return contractQueryService.remindDetailCount(operatorGuid);
    }

    @ApiOperation(value = "合同提醒等待签订合同列表")
    @GetMapping("/remind/detail")
    public Result<OrderContractRemindVO> remindDetail(@RequestParam Integer operatorGuid) {
        return contractQueryService.remindDetail(operatorGuid);
    }


    @ApiOperation(value = "订单下签合同列表/合同解除协议")
    @GetMapping("/order/list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderId", value = "订单id", required = true, paramType = "query", dataType = "Long"),
            @ApiImplicitParam(name = "templateContractType", value = "24 查询月子标准订单的解除协议", paramType = "query", dataType = "Integer")
    })
    public Result<OrderContractRemindVO> orderList(@RequestParam Long orderId, @RequestParam(name = "templateContractType", defaultValue = "") Integer templateContractType) {
        return contractQueryService.oldOrNewContractType(orderId, templateContractType);
    }

    @ApiOperation(value = "当前订单已签订的合同&&套餐附件列表")
    @PostMapping("/sign/querySignAndAttachmentList")
    public Result<List<OrderContractSignRecordVO>> querySignAndAttachmentList(@Valid @RequestBody ContractSignQuery signContractQuery) {
        return contractQueryService.querySignAndAttachmentList(signContractQuery);
    }

    @ApiOperation(value = "更改合同签署方式")
    @PostMapping("/authTypeUpdate")
    public Result<?> authTypeUpdate(@Valid @RequestBody ContractAuthTypeUpdateReq req) {
        try {
            log.info("更改合同签署方式开始处理req:{}", req);
            orderSnapshotService.authTypeUpdate(req);
            return Result.success();
        }catch ( Exception e){
            log.error("更改合同签署方式发生异常", e);
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation(value = "查询客户认证方式")
    @PostMapping(value = "/queryCardList")
    public Result<ClientBooleanVO> queryCardList(@RequestBody @Valid ClientCardTypeReq request) {
        return orderSnapshotService.queryCardList(request);
    }

    @ApiOperation(value = "查询修改按钮")
    @PostMapping(value = "/queryUpdateButton")
    public Result<ClientBooleanVO> queryUpdateButton(@RequestBody @Valid ButtonUpdateReq request) {
        return orderSnapshotService.queryUpdateButton(request);
    }
}

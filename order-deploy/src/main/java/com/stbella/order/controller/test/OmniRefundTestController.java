package com.stbella.order.controller.test;

import com.stbella.financial.message.CheckBillMessage;
import com.stbella.order.common.utils.JsonUtil;
import com.stbella.order.server.consumer.PayOfflineNotifyConsumer;
import com.stbella.order.server.consumer.PicpDingNotityConsumer;
import com.stbella.platform.order.api.refund.req.OrderRefundSuccessReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Validated
@Api(tags = "通用退款")
@RestController
@RequestMapping("/order/refund/v3")
@Slf4j
public class OmniRefundTestController {

    @Resource
    private PicpDingNotityConsumer picpDingNotityConsumer;

    @Resource
    private PayOfflineNotifyConsumer payOfflineNotifyConsumer;

    @ApiOperation(value = "模拟通过（余额，第三方）")
    @PostMapping("/success")
    public void approveSuccess(@RequestBody OrderRefundSuccessReq req) {
        req.setResult("agree");
        req.setEventType("bpms_instance_change");
        req.setType("finish");
        picpDingNotityConsumer.getMsg(JsonUtil.write(req));
    }

    @ApiOperation(value = "模拟拒绝（余额，第三方）")
    @PostMapping("/fail")
    public void approveFail(@RequestBody OrderRefundSuccessReq req) {
        req.setResult("refuse");
        req.setEventType("bpms_instance_change");
        req.setType("finish");
        picpDingNotityConsumer.getMsgFail(JsonUtil.write(req));
    }

    @ApiOperation(value = "模拟通过（线下）")
    @PostMapping("/successOffline")
    public void successOffline(@RequestBody CheckBillMessage req) {
        req.setCheckBillStatus(2);
        payOfflineNotifyConsumer.getMsg(req);
    }

    @ApiOperation(value = "模拟拒绝（线下）")
    @PostMapping("/failOffline")
    public void failOffline(@RequestBody CheckBillMessage req) {
        req.setCheckBillStatus(0);
        payOfflineNotifyConsumer.getMsg(req);
    }

}

package com.stbella.order.controller.app.v3;

import com.stbella.core.annotations.PreventDuplication;
import com.stbella.core.result.Result;
import com.stbella.order.common.constant.RedisConstant;
import com.stbella.order.common.enums.core.OmniOrderTypeEnum;
import com.stbella.order.server.order.month.req.CheckSignAmountModifyPermissionReq;
import com.stbella.order.server.order.month.req.OrderMonthIncomeQuery;
import com.stbella.order.server.order.month.service.MonthOrderAdminCommandService;
import com.stbella.platform.order.api.*;
import com.stbella.platform.order.api.req.CheckOrderProcessReq;
import com.stbella.platform.order.api.req.CheckSupplementAgreementReq;
import com.stbella.platform.order.api.req.CloseCommand;
import com.stbella.platform.order.api.req.CreateOrderReq;
import com.stbella.platform.order.api.res.CheckOrderProcessRes;
import com.stbella.platform.order.api.res.CheckSupplementAgreementRes;
import com.stbella.platform.order.api.res.CreateOrderRes;
import com.stbella.platform.order.api.res.QueryDepositAmountRes;
import com.stbella.redisson.DistributedLocker;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.text.MessageFormat;
import java.util.Set;

import static com.stbella.order.common.constant.BizConstant.OrderAppKey.ASSET_LIST_ROOM_SERVICE;

/**
 * <AUTHOR>
 */
@Validated
@Api(tags = "通用订单")
@RestController
@RequestMapping("/order/v3")
@Slf4j
public class OmniOrderController {

    @Resource
    OrderCreateService orderCreateService;
    @Resource
    OrderFlowService orderFlowService;
    @Resource
    OrderCommandService orderCommandService;
    @Resource
    private DistributedLocker redisson;
    @Resource
    OrderQueryService orderQueryService;
    @Resource
    OrderSupplementService orderSupplementService;
    @Resource
    MonthOrderAdminCommandService monthOrderAdminCommandService;

    @ApiOperation(value = "创建通用订单")
    @PostMapping("/create")
    public Result<CreateOrderRes> create(@RequestBody CreateOrderReq req) {
        Result<CreateOrderRes> result = orderCreateService.createOrder(req);
        return result;
    }

    @ApiOperation(value = "创建押金订单")
    @PostMapping("/deposit/create")
    public Result<CreateOrderRes> createDepositOrder(@RequestBody CreateOrderReq req) {
        RLock lock = redisson.lock(MessageFormat.format(RedisConstant.DEPOSIT_ORDER_CREATE_LOCK, OmniOrderTypeEnum.DEPOSIT_ORDER.code(), req.getBasicUid(), req.getStoreId()));
        try {
            return orderCreateService.createDepositOrder(req);
        } finally {
            if (lock != null && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    @ApiOperation(value = "节点校验")
    @PostMapping("/check")
    public Result<CheckOrderProcessRes> nodeCheck(@RequestBody CheckOrderProcessReq req) {
        Result<CheckOrderProcessRes> check = orderFlowService.check(req);
        return check;
    }


    @ApiOperation(value = "关闭订单")
    @PostMapping("/close")
    @PreventDuplication
    public Result<Void> close(@RequestBody CloseCommand command) {
        Result<Void> result = orderCommandService.close(command);
        return result;
    }

    @ApiOperation(value = "获取押金金额")
    @PostMapping("/deposit/amount")
    public Result<QueryDepositAmountRes> getDepositAmount(@RequestBody @Valid OrderMonthIncomeQuery query) {
        return orderQueryService.getDepositAmount(query);
    }

    @ApiOperation(value = "判断订单用户是否需要签订补充协议")
    @PostMapping("/check/supplement/agreement")
    public Result<CheckSupplementAgreementRes> checkSupplementAgreement(@RequestBody @Valid CheckSupplementAgreementReq req) {
        return orderSupplementService.checkAndCreateSupplementAgreement(req);
    }

    @ApiOperation(value = "检查签单金额修改权限")
    @PostMapping("/check-sign-amount-modify-permission")
    public Result<Boolean> checkSignAmountModifyPermission(@RequestBody @Valid CheckSignAmountModifyPermissionReq req) {
        return monthOrderAdminCommandService.checkSignAmountModifyPermission(req);
    }


    @ApiOperation(value = "获取天数")
    @GetMapping("/getGoodsType")
    public Result<Set<Integer>> getGoodsType() {
        return Result.success(ASSET_LIST_ROOM_SERVICE);
    }
}

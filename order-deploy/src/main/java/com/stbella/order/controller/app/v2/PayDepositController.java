package com.stbella.order.controller.app.v2;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stbella.core.base.PageVO;
import com.stbella.core.result.Result;
import com.stbella.core.annotations.PreventDuplication;
import com.stbella.order.server.order.cts.response.order.PayRefundResult;
import com.stbella.order.server.order.month.req.*;
import com.stbella.order.server.order.month.res.*;
import com.stbella.order.server.order.month.service.OrderPayDepositService;
import com.stbella.redisson.DistributedLocker;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Validated
@Api(tags = "押金支付 不走订单流程")
@RestController
@RequestMapping("/order/pay/deposit")
@Slf4j
public class PayDepositController {

    @Resource
    private OrderPayDepositService orderPayDepositService;

    @Resource
    private DistributedLocker redisson;

    @ApiOperation(value = "支付宝付款")
    @PostMapping("/payment")
    @PreventDuplication
    public Result<String> payment(@Valid @RequestBody PayDepositReq depositReq) {
        return Result.success(orderPayDepositService.payment(depositReq));
    }

    @ApiOperation(value = "微信付款")
    @PostMapping("/wxPayment")
    @PreventDuplication
    public Result<JSONObject> wxPayment(@Valid @RequestBody PayDepositReq depositReq) {
        return Result.success(orderPayDepositService.wxPayment(depositReq));
    }

//    @ApiOperation(value = "pos机付款")
//    @PostMapping("/posPayment")
//    public Result<JSONObject> posPayment(@Valid @RequestBody PayDepositReq depositReq) {
//        return Result.success(orderPayDepositService.posPayment(depositReq));
//    }

    @ApiOperation(value = "押金退款")
    @PostMapping("/refund")
    @PreventDuplication
    public Result<String> refund(Integer id, boolean posScanTransCode, Integer accountType) {
        PayRefundResult payRefundResult = orderPayDepositService.refund(id, posScanTransCode, accountType);
        return Result.success(payRefundResult.getResult());
    }

    //    @ApiOperation(value = "线下支付")
//    @PostMapping("/offlinePay")
//    public Result<Integer> offlinePay(@Valid @RequestBody OfflinePayDepositRequest request) {
//        return Result.success(orderPayDepositService.offlinePay(request));
//    }

    @ApiOperation(value = "picp 管理端 押金记录列表")
    @PostMapping("/list")
    public Result<PageVO<PayDepositRecordVO>> list(@Valid @RequestBody ClientDepositQuery query) {
        return orderPayDepositService.list(query);
    }

    @ApiOperation(value = "picp 管理端 押金记录明细流水")
    @PostMapping("/detailList")
    public Result<List<PayDepositDetailRecordVO>> detail(@Valid @RequestBody ClientDepositDetailQuery query) {
        return orderPayDepositService.detail(query);
    }

    @ApiOperation(value = "helper-押金支付记录列表")
    @PostMapping("/queryPagePayRecordByClientUid")
    public Result<Page<IncomeRecordVO>> queryPagePayRecordByClientUid(@Valid @RequestBody ClientDepositPageQuery query) {
        return orderPayDepositService.queryPagePayRecordByClientUid(query);
    }

    @ApiOperation(value = "helper-押金-退款记录列表")
    @PostMapping("/queryRefundRecordByClientUid")
    public Result<Page<RefundRecordListVO>> queryRefundRecordByClientUid(@Valid @RequestBody ClientDepositPageQuery query) {
        return orderPayDepositService.queryPageRefundRecordByClientUid(query);
    }

    @ApiOperation(value = "提交押金退款申请")
    @PostMapping("/submitRefundApply")
    @PreventDuplication
    public Result<Integer> submitRefundApply(@Valid @RequestBody SubmitDepositRefundV2Request request) {
        ////押金退款的审批字段：客户姓名、所属门店、手机号、押金收款总额、申请退款金额 、退款原因
        return orderPayDepositService.submitRefundApply(request);
    }

    @ApiOperation(value = "helper 押金支付退款流水列表")
    @PostMapping("/helperQueryRecordByClientUid")
    public Result<List<PayDepositClientListRecordVO>> queryPageRecordByClientUid(@Valid @RequestBody ClientDepositDetailQuery query) {
        return orderPayDepositService.queryRecordByClientUid(query);
    }

    @ApiOperation(value = "招商聚合押金支付")
    @PostMapping("/cmbPayment")
    @PreventDuplication
    public Result<JSONObject> cmbPayment(@Valid @RequestBody PayDepositReq payReqV2) {
        return Result.success(orderPayDepositService.cmbPayment(payReqV2));
    }

}

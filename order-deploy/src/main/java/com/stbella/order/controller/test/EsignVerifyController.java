package com.stbella.order.controller.test;

import com.stbella.order.server.contract.provider.month.ESignProvider;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Validated
@Api(tags = "E签宝认证")
@RestController
@RequestMapping("/esign/verify/")
@Slf4j
public class EsignVerifyController {

    @Resource
    ESignProvider provider;

    @GetMapping("/userInfo")
    @ApiOperation(value = "E签宝三要素认证")
    public void doPersonIdentityComparisonSuccess2(String name, String idCard, String phone) {
        boolean result = provider.doPersonIdentityComparison(name, idCard, phone);
        log.info("success result =" + result);
    }
}

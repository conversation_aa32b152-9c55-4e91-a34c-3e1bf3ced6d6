package com.stbella.order.controller.app.v5;

import cn.hutool.json.JSONUtil;
import com.stbella.core.result.Result;
import com.stbella.order.server.order.month.dto.PageDTO;
import com.stbella.order.server.order.month.req.HeOrderGiftInfoGoodsReq;
import com.stbella.order.server.order.month.req.HeOrderGiftInfoPicpReq;
import com.stbella.order.server.order.month.res.HeOrderGiftInfoGoodsPicpVO;
import com.stbella.order.server.order.month.res.HeOrderGiftInfoPicpVO;
import com.stbella.order.server.order.month.service.HeOrderGiftInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@Slf4j
@RestController
@Api(tags = "赠送订单-PICP端")
@RequestMapping("/giftOrder/picp/v5")
public class HeOrderGiftInfoPicpController {

    @Resource
    private HeOrderGiftInfoService heOrderGiftInfoService;

    @ApiOperation(value = "赠送订单-PICP端分页查询")
    @PostMapping("/page")
    public Result<PageDTO<HeOrderGiftInfoPicpVO>> page(@Valid @RequestBody HeOrderGiftInfoPicpReq req) {

        try {
            log.info("赠送订单-PICP端分页查询请求参数:{}", req);
            return Result.success(heOrderGiftInfoService.page(req));
        }catch (Exception e){
            log.error("赠送订单-PICP端分页查询失败", e);
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation(value = "赠送订单-PICP端详情查询")
    @PostMapping("/queryGiftOrderInfoPicp")
    public Result<HeOrderGiftInfoGoodsPicpVO> queryGiftOrderInfoPicp(@Valid @RequestBody HeOrderGiftInfoGoodsReq req) {

        try {
            log.info("赠送订单-PICP端详情查询请求参数:{}", req);
            return Result.success(heOrderGiftInfoService.queryGiftOrderInfoPicp(req));
        }catch (Exception e){
            log.error("赠送订单-PICP端详情查询失败", e);
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation(value = "导出账户列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody HeOrderGiftInfoPicpReq req) {
        log.info("导出账户列表请求参数:{}", JSONUtil.toJsonStr(req));
        heOrderGiftInfoService.export(req, response);
    }
}

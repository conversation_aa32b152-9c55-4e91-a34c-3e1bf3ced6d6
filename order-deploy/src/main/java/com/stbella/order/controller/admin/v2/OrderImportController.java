package com.stbella.order.controller.admin.v2;

import com.stbella.core.result.Result;
import com.stbella.mvc.base.BaseController;
import com.stbella.order.server.context.component.imports.InStoreDataRepairImportDTO;
import com.stbella.order.server.listener.ExportMonthOrderItermListener;
import com.stbella.order.server.listener.event.ExportAllCtsOrderIdsEvent;
import com.stbella.order.server.listener.event.ExportMonthOrderItermEvent;
import com.stbella.order.server.order.order.api.ProductionOrderExportService;
import com.stbella.order.server.order.order.api.ProductionOrderImportService;
import com.stbella.order.server.order.order.req.OrderExportReq;
import com.stbella.order.server.order.repair.service.InStoreDataRepairProcessor;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;


@Validated
@Api(tags = "订单导入类")
@RestController
@RequestMapping("/order/import")
@Slf4j
public class OrderImportController extends BaseController {

    @Resource
    private ApplicationContext applicationContext;
    @Resource
    ProductionOrderExportService productionOrderExportService;
    @Resource
    private HttpServletResponse httpServletResponse;
    @Resource
    ExportMonthOrderItermListener exportMonthOrderIterm;
    @Resource
    ProductionOrderImportService productionOrderImportService;
    @Autowired
    private InStoreDataRepairProcessor repairProcessor;


    @ApiOperation(value = "产康订单导出-异步")
    @PostMapping("/production/async")
    public Result asyncProduction(@Valid @RequestBody OrderExportReq req) {
        productionOrderExportService.asyncExport(req);
        return Result.success("正在生成文件，请稍后到oos中下载 路径  /stbella/order/excel");
    }

    @ApiOperation(value = "所有予家订单id导出-异步")
    @PostMapping("/yj/async")
    public Result asyncYj(@Valid @RequestBody ExportAllCtsOrderIdsEvent req) {

        log.info("开始导出所有予家订单id");
        ExportAllCtsOrderIdsEvent event = new ExportAllCtsOrderIdsEvent();
        event.setEventTime(System.currentTimeMillis()/1000);
        event.setSource("job");
        applicationContext.publishEvent(event);

        return Result.success("正在生成文件，请注意查收邮件");
    }

    @ApiOperation(value = "导出业绩订单明细-异步")
    @PostMapping("/month/allItem")
    public Result allItem(@Valid @RequestBody ExportMonthOrderItermEvent req) {

        log.info("开始导出月子订单明细");
        exportMonthOrderIterm.exportEvent(req);

        return Result.success("正在生成文件，请注意查收邮件");
    }

    @ApiOperation(value = "三方产康订单导入")
    @PostMapping("/productThirdOrder")
    public Result<Void> productThirdOrder(@RequestParam("file") MultipartFile file){

        productionOrderImportService.importOrder(file);


        return Result.success();
    }

    @ApiOperation(value = "导入采购金")
    @PostMapping("/orderPurchaseCoin")
    public Result<Void> importOrderPurchaseCoin(@RequestParam("file") MultipartFile file){

        productionOrderImportService.importOrderPurchaseCoin(file);

        return Result.success();
    }


    @ApiOperation(value = "恢复产康资产")
    @PostMapping("/restore/thirdProduct")
    public Result<Void> restoreProductionAsset(@RequestParam("file") MultipartFile file){

//        productionOrderImportService.restoreProductionAsset(file);
        uploadAndProcess(file);

        return Result.success();
    }

    @PostMapping("/uploadAndProcess")
    @ApiOperation("上传Excel文件并处理数据修复")
    public Result<String> uploadAndProcess(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.failed("上传文件不能为空");
        }

        String fileName = file.getOriginalFilename();
        if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
            return Result.failed("请上传Excel格式文件(.xlsx 或 .xls)");
        }

        try {
            log.info("开始处理上传的Excel文件: {}", fileName);

            // 解析上传的Excel文件
            List<InStoreDataRepairImportDTO> dataList = parseUploadedExcel(file);

            if (dataList.isEmpty()) {
                return Result.failed("Excel文件中没有有效数据");
            }

            log.info("成功解析Excel文件，共{}条数据", dataList.size());

            // 处理数据修复
            boolean result = repairProcessor.processDataRepair(dataList);

            if (result) {
                return Result.success("处理成功" + String.format("Excel文件 %s 处理完成，共处理 %d 条数据", fileName, dataList.size()));
            } else {
                return Result.failed("部分数据处理失败，请查看日志");
            }

        } catch (IOException e) {
            log.error("读取上传文件失败: {}", fileName, e);
            return Result.failed("读取文件失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("处理上传文件失败: {}", fileName, e);
            return Result.failed("处理文件失败: " + e.getMessage());
        }
    }

    /**
     * 解析上传的Excel文件
     */
    private List<InStoreDataRepairImportDTO> parseUploadedExcel(MultipartFile file) throws IOException {
        List<InStoreDataRepairImportDTO> dataList = new ArrayList<>();

        com.alibaba.excel.EasyExcel.read(file.getInputStream(), InStoreDataRepairImportDTO.class,
                new com.alibaba.excel.read.listener.ReadListener<InStoreDataRepairImportDTO>() {
                    private int rowCount = 0;

                    @Override
                    public void invoke(InStoreDataRepairImportDTO data, com.alibaba.excel.context.AnalysisContext context) {
                        rowCount++;

                        // 基本数据验证
                        if (data.getOrderSn() != null && !data.getOrderSn().trim().isEmpty()) {
                            dataList.add(data);
                            log.debug("解析第{}行: 订单号={}, 客户={}", rowCount, data.getOrderSn(), data.getCustomerName());
                        } else {
                            log.warn("第{}行数据无效，订单号为空", rowCount);
                        }
                    }

                    @Override
                    public void doAfterAllAnalysed(com.alibaba.excel.context.AnalysisContext context) {
                        log.info("Excel解析完成，有效数据{}条", dataList.size());
                    }
                }).sheet().doRead();

        return dataList;
    }
}

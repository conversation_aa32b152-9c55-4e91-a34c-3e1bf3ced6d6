package com.stbella.order.controller.tools;

import com.stbella.core.result.Result;
import com.stbella.order.server.context.component.imports.InStoreDataRepairImportDTO;
import com.stbella.order.server.order.repair.service.InStoreDataRepairProcessor;
import com.stbella.order.server.utils.ExcelAnalyzer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 馆内数据修复测试控制器
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Slf4j
@RestController
@RequestMapping("/api/repair/test")
@Api(tags = "馆内数据修复测试")
public class InStoreDataRepairTestController {

    @Autowired
    private ExcelAnalyzer excelAnalyzer;

    @Autowired
    private InStoreDataRepairProcessor repairProcessor;

//    @PostMapping("/parseExcel")
//    @ApiOperation("解析Excel文件")
//    public Result<List<InStoreDataRepairImportDTO>> parseExcel() {
//        try {
//            List<InStoreDataRepairImportDTO> dataList = ExcelAnalyzer.parseExcelToDTO();
//            return Result.success(dataList);
//        } catch (Exception e) {
//            log.error("解析Excel文件失败", e);
//            return Result.failed("解析Excel文件失败: " + e.getMessage());
//        }
//    }



    @PostMapping(value = "/uploadRawAndProcess", consumes = "application/octet-stream")
    @ApiOperation("上传原始Excel文件并处理数据修复（绕过multipart解析）")
    public Result<String> uploadRawAndProcess(HttpServletRequest request, 
                                             @RequestHeader(value = "X-File-Name", required = false) String fileName) {
        try {
            if (fileName == null || fileName.trim().isEmpty()) {
                fileName = "upload.xlsx"; // 默认文件名
            }
            
            if (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls")) {
                return Result.failed("请上传Excel格式文件(.xlsx 或 .xls)");
            }

            log.info("开始处理原始上传的Excel文件: {}", fileName);

            // 读取请求体中的文件数据
            byte[] fileData;
            try (InputStream inputStream = request.getInputStream();
                 ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                fileData = outputStream.toByteArray();
            }

            if (fileData.length == 0) {
                return Result.failed("文件内容为空");
            }

            log.info("成功读取文件数据，大小: {}KB", fileData.length / 1024);

            // 解析Excel文件
            List<InStoreDataRepairImportDTO> dataList = parseExcelFromBytes(fileData);

            if (dataList.isEmpty()) {
                return Result.failed("Excel文件中没有有效数据");
            }

            log.info("成功解析Excel文件，共{}条数据", dataList.size());

            // 处理数据修复
            boolean result = repairProcessor.processDataRepair(dataList);

            if (result) {
                return Result.success("处理成功：Excel文件 " + fileName + " 处理完成，共处理 " + dataList.size() + " 条数据");
            } else {
                return Result.failed("部分数据处理失败，请查看日志");
            }

        } catch (IOException e) {
            log.error("读取上传文件失败: {}", e.getMessage(), e);
            return Result.failed("读取文件失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("处理上传文件失败: {}", e.getMessage(), e);
            return Result.failed("处理文件失败: " + e.getMessage());
        }
    }

    @PostMapping("/uploadAndParse")
    @ApiOperation("上传Excel文件并解析（不处理数据）")
    public Result<List<InStoreDataRepairImportDTO>> uploadAndParse(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.failed("上传文件不能为空");
        }

        String fileName = file.getOriginalFilename();
        if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
            return Result.failed("请上传Excel格式文件(.xlsx 或 .xls)");
        }

        try {
            log.info("开始解析上传的Excel文件: {}", fileName);

            // 解析上传的Excel文件
            List<InStoreDataRepairImportDTO> dataList = parseUploadedExcel(file);

            log.info("成功解析Excel文件，共{}条数据", dataList.size());

            return Result.success(dataList);

        } catch (IOException e) {
            log.error("读取上传文件失败: {}", fileName, e);
            return Result.failed("读取文件失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("解析上传文件失败: {}", fileName, e);
            return Result.failed("解析文件失败: " + e.getMessage());
        }
    }

//    @PostMapping("/processData")
//    @ApiOperation("处理数据修复")
//    public Result<String> processData(@RequestBody List<InStoreDataRepairImportDTO> dataList) {
//        try {
//            boolean result = repairProcessor.processDataRepair(dataList);
//            if (result) {
//                return Result.success("数据处理成功", "所有数据处理完成");
//            } else {
//                return Result.failed("部分数据处理失败，请查看日志");
//            }
//        } catch (Exception e) {
//            log.error("处理数据失败", e);
//            return Result.failed("处理数据失败: " + e.getMessage());
//        }
//    }

    /**
     * 解析上传的Excel文件
     */
    private List<InStoreDataRepairImportDTO> parseUploadedExcel(MultipartFile file) throws IOException {
        List<InStoreDataRepairImportDTO> dataList = new ArrayList<>();

        com.alibaba.excel.EasyExcel.read(file.getInputStream(), InStoreDataRepairImportDTO.class,
                new com.alibaba.excel.read.listener.ReadListener<InStoreDataRepairImportDTO>() {
                    private int rowCount = 0;

                    @Override
                    public void invoke(InStoreDataRepairImportDTO data, com.alibaba.excel.context.AnalysisContext context) {
                        rowCount++;

                        // 基本数据验证
                        if (data.getOrderSn() != null && !data.getOrderSn().trim().isEmpty()) {
                            dataList.add(data);
                            log.debug("解析第{}行: 订单号={}, 客户={}", rowCount, data.getOrderSn(), data.getCustomerName());
                        } else {
                            log.warn("第{}行数据无效，订单号为空", rowCount);
                        }
                    }

                    @Override
                    public void doAfterAllAnalysed(com.alibaba.excel.context.AnalysisContext context) {
                        log.info("Excel解析完成，有效数据{}条", dataList.size());
                    }
                }).sheet().doRead();

        return dataList;
    }

    /**
     * 从字节数组解析Excel文件
     */
    private List<InStoreDataRepairImportDTO> parseExcelFromBytes(byte[] fileData) throws IOException {
        List<InStoreDataRepairImportDTO> dataList = new ArrayList<>();

        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(fileData)) {
            com.alibaba.excel.EasyExcel.read(inputStream, InStoreDataRepairImportDTO.class,
                    new com.alibaba.excel.read.listener.ReadListener<InStoreDataRepairImportDTO>() {
                        private int rowCount = 0;

                        @Override
                        public void invoke(InStoreDataRepairImportDTO data, com.alibaba.excel.context.AnalysisContext context) {
                            rowCount++;

                            // 基本数据验证
                            if (data.getOrderSn() != null && !data.getOrderSn().trim().isEmpty()) {
                                dataList.add(data);
                                log.debug("解析第{}行: 订单号={}, 客户={}", rowCount, data.getOrderSn(), data.getCustomerName());
                            } else {
                                log.warn("第{}行数据无效，订单号为空", rowCount);
                            }
                        }

                        @Override
                        public void doAfterAllAnalysed(com.alibaba.excel.context.AnalysisContext context) {
                            log.info("Excel解析完成，有效数据{}条", dataList.size());
                        }
                    }).sheet().doRead();
        }

        return dataList;
    }
}
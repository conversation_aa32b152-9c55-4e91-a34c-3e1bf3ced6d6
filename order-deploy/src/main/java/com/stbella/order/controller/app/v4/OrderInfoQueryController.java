package com.stbella.order.controller.app.v4;

import com.stbella.core.result.Result;
import com.stbella.order.server.order.month.dto.PageDTO;
import com.stbella.order.server.order.month.req.OrderPaySuccessReq;
import com.stbella.order.server.order.month.req.WechatMyOrderInfoNewQuery;
import com.stbella.order.server.order.month.res.OrderPaySuccessRes;
import com.stbella.order.server.order.month.res.PicpGoodsV4VO;
import com.stbella.order.server.order.month.service.FinanceQueryOrderInfoService;
import com.stbella.order.server.order.month.service.OrderV3Service;
import com.stbella.order.server.order.month.service.OrderV4Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Slf4j
@Api(tags = "订单信息查询")
@RestController
@RequestMapping("/order/v4")
public class OrderInfoQueryController {

    @Resource
    private FinanceQueryOrderInfoService financeQueryOrderInfoService;

    @Resource
    private OrderV4Service orderV4Service;

    @ApiOperation(value = "查询支付成功订单列表")
    @PostMapping("/page")
    public PageDTO<OrderPaySuccessRes> list(@RequestBody OrderPaySuccessReq req) {
        return financeQueryOrderInfoService.queryPaySuccessOrderList(req);
    }

    @ApiOperation(value = "PICP-商品清单")
    @PostMapping("/goodsList")
    public Result<List<PicpGoodsV4VO>> goodsList(@Valid @RequestBody WechatMyOrderInfoNewQuery req) {
        log.info("PICP商品清单查询请求开始req:{}", req);
        try {
            return Result.success(orderV4Service.goodsList(req));
        }catch (Exception e){
            log.error("PICP商品清单查询发生异常", e);
            return Result.failed(e.getMessage());
        }
    }


}

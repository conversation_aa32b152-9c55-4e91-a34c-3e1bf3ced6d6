package com.stbella.order.controller.app.v5;

import com.stbella.core.result.Result;
import com.stbella.order.server.order.month.dto.PageDTO;
import com.stbella.order.server.order.month.req.HeOrderGiftInfoGoodsAddReq;
import com.stbella.order.server.order.month.req.HeOrderGiftInfoGoodsReq;
import com.stbella.order.server.order.month.req.HeOrderGiftInfoPiReq;
import com.stbella.order.server.order.month.res.HeOrderGiftInfoGoodsAddVO;
import com.stbella.order.server.order.month.res.HeOrderGiftInfoGoodsPiVO;
import com.stbella.order.server.order.month.res.HeOrderGiftInfoPiVO;
import com.stbella.order.server.order.month.res.RefundReasonVO;
import com.stbella.order.server.order.month.service.HeOrderGiftInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@Api(tags = "赠送订单-PI端")
@RequestMapping("/giftOrder/pi/v5")
public class HeOrderGiftInfoPiController {

    @Resource
    private HeOrderGiftInfoService heOrderGiftInfoService;

    @ApiOperation(value = "赠送订单列表查询")
    @PostMapping("/queryGiftOrderList")
    public Result<PageDTO<HeOrderGiftInfoPiVO>> queryGiftOrderList(@Valid @RequestBody HeOrderGiftInfoPiReq req) {

        try {
            log.info("赠送订单列表查询请求参数:{}", req);
            return Result.success(heOrderGiftInfoService.queryGiftOrderList(req));
        }catch (Exception e){
            log.error("赠送订单列表查询失败", e);
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation(value = "赠送订单详情查询")
    @PostMapping("/queryGiftOrderInfoPi")
    public Result<HeOrderGiftInfoGoodsPiVO> queryGiftOrderInfoPi(@Valid @RequestBody HeOrderGiftInfoGoodsReq req) {

        try {
            log.info("赠送订单详情查询请求参数:{}", req);
            return Result.success(heOrderGiftInfoService.queryGiftOrderInfoPi(req));
        }catch (Exception e){
            log.error("赠送订单详情查询失败", e);
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation(value = "赠送订单添加")
    @PostMapping("/addGiftOrderInfoPi")
    public Result<HeOrderGiftInfoGoodsAddVO> addGiftOrderInfoPi(@Valid @RequestBody HeOrderGiftInfoGoodsAddReq req) {

        try {
            log.info("赠送订单添加请求参数:{}", req);
            return Result.success(heOrderGiftInfoService.addGiftOrderInfoPi(req));
        }catch (Exception e){
            log.error("赠送订单添加请求处理失败", e);
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation(value = "赠送订单取消")
    @PostMapping("/cancelGiftOrderInfoPi")
    public Result<String> cancelGiftOrderInfoPi(@Valid @RequestBody HeOrderGiftInfoGoodsReq req) {

        try {
            log.info("赠送订单取消请求参数:{}", req);
            return Result.success(heOrderGiftInfoService.cancelGiftOrderInfoPi(req));
        }catch (Exception e){
            log.error("赠送订单取消请求处理失败", e);
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation(value = "赠送原因类型")
    @PostMapping("/queryOrderGiftReasonTypeList")
    public Result<List<RefundReasonVO>> queryOrderGiftReasonTypeList() {
        return Result.success(heOrderGiftInfoService.queryOrderGiftReasonTypeList());
    }
}

package com.stbella.order;

import com.stbella.rule.client.spring.annotation.EnableRuleLinkClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.TimeZone;


@Slf4j
@SpringBootApplication(scanBasePackages = {
        "com.stbella.core.utils",
        "com.stbella.mvc.aspect",
        "com.stbella.order.server",
        "com.stbella.order",
        "com.stbella.order.infrastructure",
        "com.stbella.mail"

})
@EnableDiscoveryClient
@EnableDubbo
@EnableCaching
@EnableScheduling
@EnableRuleLinkClient
@MapperScan({
        "com.stbella.order.server.*.mapper",
        "com.stbella.order.server.mapper",
        "com.stbella.order.infrastructure.repository.mapper.*"

})
@EnableAsync
public class OrderLauncher {
    public static void main(String[] args) throws Exception {
        TimeZone.setDefault(TimeZone.getTimeZone("GMT+8"));
        SpringApplication.run(OrderLauncher.class, args);
    }
}

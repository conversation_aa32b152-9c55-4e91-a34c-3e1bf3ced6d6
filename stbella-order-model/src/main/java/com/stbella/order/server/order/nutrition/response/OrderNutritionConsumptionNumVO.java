package com.stbella.order.server.order.nutrition.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 消费人数返回对象@date 2022/6/7 3:49 下午
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "OrderNutritionConsumptionNumVO对象", description = "消费人数返回对象")
public class OrderNutritionConsumptionNumVO extends OrderNutritionConsumptionBaseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "总消费人数")
    private String totalConsumptionNum;


}

package com.stbella.order.server.order.month.response;

import com.stbella.order.server.order.cts.enums.OfflineAuditStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "订单支付列表", description = "订单支付列表")
@Data
public class QueryOrderByCustomerIdQueryVO {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @ApiModelProperty(value = "支付编号，相当于支付第三方的订单编号；一个订单ID对应多个支付编号代表一个订单是分多次支付的")
    private String incomeSn;

    @ApiModelProperty(value = "订单id(he_order表主键)")
    private Integer orderId;

    @ApiModelProperty(value = "门店ID(ecp库cfg_store表的主键id)")
    private Integer storeId;

    @ApiModelProperty(value = "1=微信, 2=支付宝, 3=线下支付, 4=中信银行支付宝支付, 5=中信银行微信支付  8=在线pos机支付  9=C端小程序支付 10=其他 11=线下支付-转账汇款 12=线下支付-现金")
    private Integer payType;

    @ApiModelProperty(value = "支付状态：0待支付，1已支付,2支付失败")
    private Integer status;

    /**
     * @see OfflineAuditStatusV2Enum
     */
    @ApiModelProperty(value = "审批状态 0=未审核 1=审核通过 2=审核拒绝")
    private Integer approveStatus;

    @ApiModelProperty(value = "支付金额(单位分)")
    private Integer income;

    @ApiModelProperty(value = "支付金额元")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "支付时间")
    private Date payDate;

    @ApiModelProperty(value = "支付时间（秒）")
    private Integer payTime;

    @ApiModelProperty(value = "是否可修改：0-不可 1-可")
    private Integer edit;


    @ApiModelProperty(value = "货币码")
    private String currency;

}

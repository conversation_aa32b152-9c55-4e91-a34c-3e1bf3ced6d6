package com.stbella.order.server.order.month.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description: OrderTypeEnum 月子套餐类型枚举
 * @date 2021/11/17 3:20 下午
 */
public enum CardVerifyEnum {
    //证件认证结果, 0:未认证 1:认证成功 2:认证失败 3:无需认证
    VERIFY_NONE(0, "未认证"),
    VERIFY_SUCCESS(1, "认证成功"),
    VERIFY_FAIL(2, "认证失败"),
    VERIFY_NEVER(3, "无需认证"),

    ;

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    CardVerifyEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static CardVerifyEnum getEnumByCode(Integer code) {
        for (CardVerifyEnum enums : CardVerifyEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的业务子类型:" + code);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return true;
        }
        for (CardVerifyEnum monthTypeEnum : values()) {
            if (Objects.equals(monthTypeEnum.getCode(), code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (CardVerifyEnum enums : CardVerifyEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的业务子类型:" + code);
    }



    /**
     * 认证成功或者无需认证
     * @param code
     * @return
     */
    public static boolean successVerify(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return false;
        }
        if (code.intValue() == CardVerifyEnum.VERIFY_SUCCESS.getCode() || code.intValue() == CardVerifyEnum.VERIFY_NEVER.getCode()){
            return true;
        }
        return false;
    }
}

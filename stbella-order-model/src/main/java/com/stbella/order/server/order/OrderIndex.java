package com.stbella.order.server.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2023-07-25  16:06
 * @Description: 订单指标
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderIndex implements Serializable {

    @ApiModelProperty(value = "订单折扣")
    private BigDecimal discountMargin = new BigDecimal(0);

    @ApiModelProperty(value = "净折扣率")
    private BigDecimal netMargin = new BigDecimal(0);

    @ApiModelProperty(value = "毛利率")
    private BigDecimal grossMargin= new BigDecimal(0);

    @ApiModelProperty(value = "签单折扣率")
    private BigDecimal signOrderDiscountMargin = new BigDecimal(0);


}

package com.stbella.order.server.order.nutrition.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "OrderNutritionManagerMaoRiVO对象", description = "毛利分析-管理视窗")
public class OrderNutritionManagerMaoRiVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品收入")
    private String income;

    @ApiModelProperty(value = "商品成本")
    private String cost;

    @ApiModelProperty(value = "销售毛利")
    private String salesMaoRi;

    @ApiModelProperty(value = "支出类")
    private String outgo;

    @ApiModelProperty(value = "利润")
    private String profit;

    @ApiModelProperty(value = "毛利率")
    private String maoRiRate;

    @ApiModelProperty(value = "收支项目")
    private List<SimpleManagerMaoRiVO> simpleManagerMaoRiVOList;


}

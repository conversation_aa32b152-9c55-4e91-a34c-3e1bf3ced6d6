package com.stbella.order.server.order.month.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import lombok.Getter;

import java.util.Objects;

/**
 * 记录类型:1-支付;2-退款
 */
public enum RecordTypeEnum {
    //记录类型:1-支付;2-退款
    PAY(1, "支付"),
    REFUND(2, "退款");

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    RecordTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static RecordTypeEnum getEnumByCode(Integer code) {
        for (RecordTypeEnum enums : RecordTypeEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的收退类型:" + code);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return true;
        }
        for (RecordTypeEnum monthTypeEnum : values()) {
            if (Objects.equals(monthTypeEnum.getCode(), code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (RecordTypeEnum enums : RecordTypeEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的收退类型:" + code);
    }
}

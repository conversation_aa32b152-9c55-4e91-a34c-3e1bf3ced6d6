package com.stbella.order.server.contract.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class UserSignDTO {

    @ApiModelProperty(value = "用户姓名")
    private String name;

    @ApiModelProperty(value = "手机号码")
    private String phone;

    @ApiModelProperty(value = "证件类型 证件类型 0大陆身份证 1护照 2香港 3澳门  4台湾")
    private Integer idCardType;

    @ApiModelProperty(value = "证件号码")
    private String idCardNo;

    @ApiModelProperty(value = "认证状态 0未认证  1已认证")
    private Integer state;

    @ApiModelProperty(value = "e签宝用户id")
    private String esignUserId;
}

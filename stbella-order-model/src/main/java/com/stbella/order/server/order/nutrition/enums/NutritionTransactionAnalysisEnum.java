package com.stbella.order.server.order.nutrition.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import lombok.Getter;

import java.util.Objects;

public enum NutritionTransactionAnalysisEnum {
    NATURAL_MONTH(10, "自然月"),
    NATURAL_QUARTER(11, "自然季度"),
    IN_NATURAL(12, "自然年");

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    NutritionTransactionAnalysisEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static NutritionTransactionAnalysisEnum getEnumByCode(Integer code) {
        for (NutritionTransactionAnalysisEnum enums : NutritionTransactionAnalysisEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的日期格式:" + code);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return true;
        }
        for (NutritionTransactionAnalysisEnum monthTypeEnum : values()) {
            if (Objects.equals(monthTypeEnum.getCode(), code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (NutritionTransactionAnalysisEnum enums : NutritionTransactionAnalysisEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的日期格式:" + code);
    }
}

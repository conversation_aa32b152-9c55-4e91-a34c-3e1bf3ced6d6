package com.stbella.order.server.order.cts.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import lombok.Getter;

import java.util.Objects;

/**
 * e签宝用户认证状态
 */
public enum UserESignStatusEnum {

    CLOSE(0, "未认证"),
    OPEN(1, "已认证"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    UserESignStatusEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static UserESignStatusEnum getEnumByCode(Integer code) {
        for (UserESignStatusEnum enums : UserESignStatusEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的订单退款审核状态:" + code);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return true;
        }
        for (UserESignStatusEnum monthTypeEnum : values()) {
            if (Objects.equals(monthTypeEnum.getCode(), code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (UserESignStatusEnum enums : UserESignStatusEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的订单退款审核状态:" + code);
    }
}

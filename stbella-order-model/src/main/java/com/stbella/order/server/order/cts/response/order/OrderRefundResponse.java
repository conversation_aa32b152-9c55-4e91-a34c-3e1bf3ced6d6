/*
 * Copyright (c) 2001-2022 GuaHao.com Corporation Limited. All rights reserved.
 * This software is the confidential and proprietary information of GuaHao Company.
 * ("Confidential Information").
 * You shall not disclose such Confidential Information and shall use it only
 * in accordance with the terms of the license agreement you entered into with GuaHao.com.
 */
package com.stbella.order.server.order.cts.response.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-03-31 16:00
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "退款信息", description = "退款信息")
public class OrderRefundResponse implements Serializable {
    @ApiModelProperty(value = "退款具体原因")
    private String refundCause;

    @ApiModelProperty(value = "退款原因-单选")
    private String refundOption;

    @ApiModelProperty(value = "支付方式:1-支付宝;2-微信;3-POS机;4-汇款;5-现金;6-其他,web显示1、2、3为原路退回，4、5、6财务打款")
    private Integer payType;

    @ApiModelProperty(value = "1:支付成功, 3支付中, 4、支付失败")
    private Integer refundStatus;

    @ApiModelProperty(value = "退款时支付失败的原因")
    private String refundFailedRemark;

    @ApiModelProperty(value = "审核状态,0:待审核 1:审核通过 2:审核驳回")
    private Integer auditStatus;

    @ApiModelProperty(value = "退款金额(用户)")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "审核驳回理由")
    private String rejectAuditReason;

    @ApiModelProperty(value = "退款凭证")
    private List<String> auditRefundProof;

    @ApiModelProperty(value = "线下转账时间")
    private Date transferTime;

    @ApiModelProperty(value = "退款金额(线下付款财务确认后的金额)")
    private BigDecimal actuallyAmount;


}

package com.stbella.order.server.order.nutrition.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "OperatorMaoRiVO对象", description = "毛利分析-运营视窗返回数据")
public class OperatorMaoRiVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "电商运营利润率")
    private String profitMargin;

    @ApiModelProperty(value = "利润")
    private String profit;

    @ApiModelProperty(value = "销售毛利")
    private String salesMaoRi;

    @ApiModelProperty(value = "支出")
    private String outgo;

    @ApiModelProperty(value = "收支项目")
    private List<SimpleOperatorMaoRiVO> simpleOperatorMaoRiVOList;

}

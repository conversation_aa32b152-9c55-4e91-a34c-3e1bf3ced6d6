package com.stbella.order.server.order.cts.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import lombok.Getter;

import java.util.Objects;

/**
 * 审批通过类型
 */
public enum DecreaseTypeEnum {

    //减免审批
    NO_APPROVAL_NEEDED(1, "无需审批"),
    //折扣审批
    DISCOUNT(0, "折扣审批"),
    //修改合同审批
    MODIFY_APPROVAL(2, "修改合同审批"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    DecreaseTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }


    public static DecreaseTypeEnum getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "code 不能为空");
        }
        for (DecreaseTypeEnum enums : DecreaseTypeEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums;
            }
        }

        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的审批类型:" + code);
    }


}

package com.stbella.order.server.order.nutrition.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@ApiModel(value = "MaoriSaveShopByYearVO对象", description = "店铺毛利返回对象")
public class MaoriSaveShopByYearVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "月份（0-12月，0月代表毛利均值）")
    private Integer mouth;

    @ApiModelProperty(value = "首单购买")
    private BigDecimal firstBuy;

    @ApiModelProperty(value = "当月复购")
    private BigDecimal thisRePurchase;

    @ApiModelProperty(value = "第2个月复购")
    private BigDecimal twoRePurchase;

    @ApiModelProperty(value = "第3个月复购")
    private BigDecimal threeRePurchase;

    @ApiModelProperty(value = "第4个月复购")
    private BigDecimal fourRePurchase;

    @ApiModelProperty(value = "第5个月复购")
    private BigDecimal fiveRePurchase;

    @ApiModelProperty(value = "第6个月复购")
    private BigDecimal sixRePurchase;

    @ApiModelProperty(value = "第7个月复购")
    private BigDecimal sevenRePurchase;

    @ApiModelProperty(value = "第8个月复购")
    private BigDecimal eightRePurchase;

    @ApiModelProperty(value = "第9个月复购")
    private BigDecimal nineRePurchase;

    @ApiModelProperty(value = "第10个月复购")
    private BigDecimal tenRePurchase;

    @ApiModelProperty(value = "第11个月复购")
    private BigDecimal elevenRePurchase;

    @ApiModelProperty(value = "第12个月复购")
    private BigDecimal twelveRePurchase;
}

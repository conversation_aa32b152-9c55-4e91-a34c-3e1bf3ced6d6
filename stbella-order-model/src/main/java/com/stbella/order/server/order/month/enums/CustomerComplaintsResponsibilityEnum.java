package com.stbella.order.server.order.month.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import lombok.Getter;

import java.util.Objects;

/**
 * 定责类型
 *
 * <AUTHOR>
 * @description: CustomerComplaintsStatusEnum 客诉状态
 */
public enum CustomerComplaintsResponsibilityEnum {
    // 100:无责 200:我司全责 300:我司主责三方次责 400:三方主责我司次责 500:三方全责
    NO_RESPONSIBILITY(100, "无责"),
    OUR_FULL_RESPONSIBILITY(200, "我司全责"),
    OUR_MAIN_RESPONSIBILITY_THIRD_PARTY_SECONDARY(300, "我司主责三方次责"),
    THIRD_PARTY_MAIN_RESPONSIBILITY_OUR_SECONDARY(400, "三方主责我司次责"),
    THIRD_PARTY_FULL_RESPONSIBILITY(500, "三方全责");

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    CustomerComplaintsResponsibilityEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static CustomerComplaintsResponsibilityEnum getEnumByCode(Integer code) {
        for (CustomerComplaintsResponsibilityEnum enums : CustomerComplaintsResponsibilityEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的业务类型:" + code);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return true;
        }
        for (CustomerComplaintsResponsibilityEnum monthTypeEnum : values()) {
            if (Objects.equals(monthTypeEnum.getCode(), code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (CustomerComplaintsResponsibilityEnum enums : CustomerComplaintsResponsibilityEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的业务类型:" + code);
    }
}

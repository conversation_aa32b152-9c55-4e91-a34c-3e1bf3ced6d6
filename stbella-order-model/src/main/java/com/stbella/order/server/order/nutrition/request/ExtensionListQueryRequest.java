package com.stbella.order.server.order.nutrition.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ExtensionListQueryRequest对象", description = "推广费用列表查询对象")
public class ExtensionListQueryRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "年")
    @NotNull(message = "年不能为空")
    private Integer year;

    @ApiModelProperty(value = "月")
    @NotNull(message = "月不能为空")
    private Integer month;

    @ApiModelProperty(value = "店铺类型")
    @NotNull(message = "店铺类型不能为空")
    private Integer storeType;

}

package com.stbella.order.server.order.nutrition.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@ApiModel(value = "SumAllOperateRequest对象", description = "计算业绩入参")
public class SumAllOperateRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "年份")
    @NotNull(message = "年份不能为空")
    private String year;

    @ApiModelProperty(value = "月份")
    @NotNull(message = "月份不能为空")
    private String month;

    @ApiModelProperty(value = "电商渠道销售业绩")
    @NotNull(message = "电商渠道销售总业绩商不能为空")
    private BigDecimal onlineShop;

    @ApiModelProperty(value = "门店渠道销售业绩")
    @NotNull(message = "门店渠道销售总业绩不能为空")
    private BigDecimal store;

    @ApiModelProperty(value = "分销渠道销售业绩")
    @NotNull(message = "分销渠道销售总业绩不能为空")
    private BigDecimal distribution;

    @ApiModelProperty(value = "新零售销售业绩")
    @NotNull(message = "新零售销售业绩不能为空")
    private BigDecimal smallStore;


}

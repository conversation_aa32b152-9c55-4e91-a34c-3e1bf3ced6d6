package com.stbella.order.server.order.cts.constant;

public class CtsOrderConstant {

    /**
     * 订单事件类型:管理费
     */
    public static final Integer EVENT_TYPE_SERVE_FEE = 0;

    /**
     * 订单事件类型:培训课程
     */
    public static final Integer EVENT_TYPE_COURSE = 1;

    /**
     * 订单事件类型:保证金
     */
    public static final Integer EVENT_TYPE_MARGIN = 2;

    /**
     * 订单事件类型:育婴师
     */
    public static final Integer EVENT_TYPE_BABY_SITTER = 3;

    /**
     * 订单事件类型:母婴护理
     */
    public static final Integer EVENT_TYPE_AUNT_SITTER = 4;

    /**
     * 订单事件类型:育婴师通用商品
     */
    public static final Integer EVENT_TYPE_GENERAL_SITTER = 5;
    /**
     * 订单事件类型:雇主通用商品
     */
    public static final Integer EVENT_TYPE_GENERAL_CUSTOMER = 6;


    public static final String MACHINE_VARIABLES_ORDER_NO = "orderNo";

    public static final String MSG_PAYED = "当前二维码已支付完成，无需支付";

    public static final String MSG_OVER_PAY = "当前二维码支付金额大于订单待支付金额，请联系销售重新发送";

    public static final String MSG_FINISH = "此订单已支付完成，无需支付";

    public static final String MSG_CLOSE = "此订单已关闭，不可继续支付";

    public static final String MSG_APPLY_REFUND = "此订单正在退款中，不可继续支付";

    public static final String MSG_REFUND = "此订单存在退款，不可继续支付";

    public static final String MSG_CANCEL = "此订单已取消，不可继续支付";
    public static final String NOT_EMPTY_NAME = "请去管理后台填写姓名";
    public static final String NOT_EMPTY_MOBILE = "请去管理后台填写手机号码";

    public static final String CTS_PRODUCT_MARGIN_NAME = "上户保证金";
    public static final String CTS_PRODUCT_SERVE_FEE_NAME = "平台管理费";

    /**
     * 育婴师日工资计算因子
     */
    public static final String CTS_SITTER_WAGE_26 = "26";

    /**
     * 到家 旧报单文案
     */
    public static final String CTS_OLD_NOTICE = "\uD83C\uDF8A捷报\uD83C\uDF8A\n" +
            "恭喜【{0}】，签约【{1}】，签约金额【{2}】，本月累计签约【{3}】单，销售额【{4}元】，实收金额【{5}元】，目标达成率【{6}%】！\n" +
            "愿事业部业绩长虹，天天爆单\uD83D\uDD25\uD83D\uDD25\uD83D\uDD25";

    /**
     * 雇主订单、雇主通用订单 报单文案
     */
    public static final String CTS_EMPLOYER_NOTICE = "\uD83C\uDF8A捷报\uD83C\uDF8A\n" +
            "恭喜销售中心【{0}】，签约【{1}】，签约金额【{2}】，本月累计签约【{3}】单，实收金额【{4}元】。\n" +
            "销售中心本月累计销售额【{5}元】，实收金额【{6}元】！\n" +
            "愿事业部业绩长虹，天天爆单\uD83D\uDD25\uD83D\uDD25\uD83D\uDD25";

    /**
     * 育婴师培训订单、育婴师通用订单 报单文案
     */
    public static final String CTS_NURSERY_NOTICE = "\uD83C\uDF8A捷报\uD83C\uDF8A\n" +
            "恭喜供给中心【{0}】，签约【{1}】，签约金额【{2}】，本月累计签约【{3}】单，实收金额【{4}元】。\n" +
            "供给中心本月累计销售额【{5}元】，实收金额【{6}元】！\n" +
            "愿事业部业绩长虹，天天爆单\uD83D\uDD25\uD83D\uDD25\uD83D\uDD25";


}


package com.stbella.order.server.order.cts.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import lombok.Getter;

import java.util.Objects;

/**
 * enable or not enable
 */
public enum YesOrNoEnum {
    NO(0, "否"),
    YES(1, "是");

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    YesOrNoEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static YesOrNoEnum getEnumByCode(Integer code) {
        for (YesOrNoEnum enums : YesOrNoEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的枚举值:" + code);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return true;
        }
        for (YesOrNoEnum monthTypeEnum : values()) {
            if (Objects.equals(monthTypeEnum.getCode(), code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (YesOrNoEnum enums : YesOrNoEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的枚举值:" + code);
    }
}

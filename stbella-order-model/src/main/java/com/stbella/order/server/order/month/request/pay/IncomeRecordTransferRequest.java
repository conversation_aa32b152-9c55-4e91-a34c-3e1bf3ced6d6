package com.stbella.order.server.order.month.request.pay;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: IncomeRecordTransferRequest
 * @date 2023/8/7 16:29
 */
@Data
@ApiModel(value = "IncomeRecordTransferRequest基础类", description = "支付记录转移对象")
public class IncomeRecordTransferRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "支付记录ID列表", required = true)
    @NotNull(message = "支付记录不能为空")
    private List<Integer> incomeIdList;

    @ApiModelProperty(value = "新订单号", required = true)
    @NotNull(message = "新订单号不能为空")
    private String orderSnNew;

    @ApiModelProperty(value = "新订单是否要报单,0-不需要,1-需要报单", required = true)
    @NotNull(message = "新订单是否要报单不能为空")
    private Integer isNotice;
}

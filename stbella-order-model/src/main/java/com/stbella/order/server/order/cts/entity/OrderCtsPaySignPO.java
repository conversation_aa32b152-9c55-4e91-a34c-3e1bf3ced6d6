package com.stbella.order.server.order.cts.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.stbella.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 订单支付标志
 * </p>
 *
 * <AUTHOR> @since 2022-04-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("order_cts_pay_sign")
@ApiModel(value = "OrderCtsPaySignPO对象", description = "订单支付标志")
public class OrderCtsPaySignPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "支付标识")
    private String paySign;

    @ApiModelProperty(value = "支付类型 1-意向金;2-预付款;3-尾款;4-押金;5-全款")
    private Integer amountType;

    @ApiModelProperty(value = "支付状态:1-支付成功;2-支付失败;3-处理中")
    private Integer payStatus;

    @ApiModelProperty(value = "本地流水号(支付成功时赋值)")
    private String localTransactionalNo;

    @ApiModelProperty(value = "0:比例支付 1:自定义金额")
    private Integer targetPayType;

    @ApiModelProperty(value = "选择支付比例 例:50%->50")
    private BigDecimal targetPayPercent;

    @ApiModelProperty(value = "自定义支付金额")
    private BigDecimal targetPayAmount;

}

package com.stbella.order.server.order.nutrition.excel;

import com.stbella.core.excel.Column;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@ApiModel(value = "TestExcel对象", description = "分析模板")
public class NutritionExportUniverseExcel implements Serializable {

    private static final long serialVersionUID = 1L;

    @Column(value = "1", order = 1)
    private String column1;

    @Column(value = "2", order = 2)
    private String column2;

    @Column(value = "3", order = 3)
    private String column3;

    @Column(value = "4", order = 4)
    private String column4;

    @Column(value = "5", order = 5)
    private String column5;

    @Column(value = "6", order = 6)
    private String column6;

    @Column(value = "7", order = 7)
    private String column7;

    @Column(value = "8", order = 8)
    private String column8;

    @Column(value = "9", order = 9)
    private String column9;


}

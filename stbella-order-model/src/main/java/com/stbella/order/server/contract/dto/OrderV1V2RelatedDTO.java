package com.stbella.order.server.contract.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class OrderV1V2RelatedDTO implements Serializable {

    private static final long serialVersionUID = 8709118115964458529L;

    @ApiModelProperty(value = "订单ID")
    private Integer orderId;

    @ApiModelProperty(value = "V1V2订单赠送服务拼接字符串")
    private String relatedServiceNames;

    @ApiModelProperty(value = "V1V2订单赠送产康累加值")
    private Integer relatedCouponAmount;

}

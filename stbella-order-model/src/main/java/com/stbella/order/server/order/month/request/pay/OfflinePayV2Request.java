package com.stbella.order.server.order.month.request.pay;

import com.stbella.core.base.BasicReq;
import com.stbella.order.server.order.month.annotations.EnumValue;
import com.stbella.order.server.order.month.constant.BaseConstant;
import com.stbella.order.server.order.month.enums.PayAmountEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class OfflinePayV2Request extends BasicReq implements Serializable {

    @ApiModelProperty(value = "线下支付信息记录ID,新增不传,修改必传")
    private Integer id;

    @ApiModelProperty(value = "支付记录ID")
    private Integer incomeId;

    @ApiModelProperty(value = BaseConstant.AMOUNT_TYPE, required = true)
    @EnumValue(enumClass = PayAmountEnum.class, enumMethod = "isValidCode", message = "付款类型有误")
    @NotNull(message = "付款类型不能为空")
    private Integer amountType;

    @ApiModelProperty(value = "支付账号类型,默认0-贝康;1-广禾贝康;2-贝康到家", required = true)
    private Integer accountType;

    @ApiModelProperty(value = "storeId pos机必填", required = true)
    private Integer storeId;
    /***
     * @see   com.stbella.order.common.enums.month.IncomeProofRecordPayTypeEnum
     */
    @ApiModelProperty(value = "线下支付方式：0其他，1pos机， 2汇款 ,3现金")
    private Integer payType;

    @ApiModelProperty(value = "订单号", required = true)
    @NotNull(message = "订单号不能为空")
    private Integer orderId;

    @ApiModelProperty(value = "凭证地址")
    private String payProof;

    @ApiModelProperty(value = "凭证金额")
    private BigDecimal incomeProof;

    @ApiModelProperty(value = "备注")
    private String remark;
}

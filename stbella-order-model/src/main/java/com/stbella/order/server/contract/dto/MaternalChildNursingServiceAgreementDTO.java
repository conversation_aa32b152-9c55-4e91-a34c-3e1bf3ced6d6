package com.stbella.order.server.contract.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 母音护理服务委托协议
 *
 * <AUTHOR>
 */
@Data
public class MaternalChildNursingServiceAgreementDTO implements Serializable {

    private static final long serialVersionUID = 8709118115964458529L;

    @ApiModelProperty(value = "客户签名")
    private String customerName;
    @ApiModelProperty(value = "合同编号")
    private String signNumber;
    @ApiModelProperty(value = "签约地址")
    private String signAddress;
    @ApiModelProperty(value = "签约日期")
    private String signDate;
    @ApiModelProperty(value = "证件类型")
    private String cerType;
    @ApiModelProperty(value = "身份证")
    private String idNumber;
    @ApiModelProperty(value = "客户手机号")
    private String customerMobile;
    @ApiModelProperty(value = "家庭住址")
    private String homeAddress;
    @ApiModelProperty(value = "公司联系方式")
    private String companyContact;
    @ApiModelProperty(value = "服务手机号")
    private String serviceMobile;
    @ApiModelProperty(value = "经营地址")
    private String businessAddress;
    @ApiModelProperty(value = "婴儿数量flag")
    private String babyCountFlag;
    @ApiModelProperty(value = "婴儿数量")
    private String babyCount;
    @ApiModelProperty(value = "妈妈数量flag")
    private String momCountFlag;
    @ApiModelProperty(value = "妈妈数量")
    private String momCount;
    @ApiModelProperty(value = "特殊需求")
    private String specialDemand;
    @ApiModelProperty(value = "紧急联系人信息")
    private String emergencyContactInfo;
    @ApiModelProperty(value = "服务地址全名")
    private String serviceAllAddress;
    @ApiModelProperty(value = "服务省")
    private String serviceProvince;
    @ApiModelProperty(value = "服务市")
    private String serviceCity;
    @ApiModelProperty(value = "服务区")
    private String serviceArea;
    @ApiModelProperty(value = "服务街道")
    private String serviceStreet;
    @ApiModelProperty(value = "号")
    private String serviceNumber;
    @ApiModelProperty(value = "室")
    private String serviceRoom;
    @ApiModelProperty(value = "服务地址")
    private String serviceAddress;
    /*服务方式*/
    @ApiModelProperty(value = "全日住家型")
    private String allDayInHome;
    @ApiModelProperty(value = "日间照料型")
    private String dayTakeCare;
    @ApiModelProperty(value = "服务其他")
    private String serviceOther;
    @ApiModelProperty(value = "服务其他描述")
    private String serviceOtherDes;


    @ApiModelProperty(value = "初级")
    private String primary;
    @ApiModelProperty(value = "中级")
    private String middle;
    @ApiModelProperty(value = "高级")
    private String high;
    @ApiModelProperty(value = "精品")
    private String boutique;
    @ApiModelProperty(value = "月子会所")
    private String confinementClub;


    @ApiModelProperty(value = "服务开始年")
    private String serviceStartYear;
    @ApiModelProperty(value = "服务开始月")
    private String serviceStartMonth;
    @ApiModelProperty(value = "服务开始日")
    private String serviceStartDay;
    @ApiModelProperty(value = "服务结束年")
    private String serviceEndYear;
    @ApiModelProperty(value = "服务结束月")
    private String serviceEndMonth;
    @ApiModelProperty(value = "服务结束日")
    private String serviceEndDay;
    @ApiModelProperty(value = "服务总共天")
    private String serviceDay;

    @ApiModelProperty(value = "服务费")
    private String serviceFee;
    @ApiModelProperty(value = "服务费大写人民币")
    private String serviceFeeCapitalize;
    @ApiModelProperty(value = "日均服务费用")
    private String serviceFeeDayAverage;
    @ApiModelProperty(value = "大写服务费日平均")
    private String serviceFeeDayAverageCapitalize;

    @ApiModelProperty(value = "公司开户名")
    private String companyBankOpenAccountName;
    @ApiModelProperty(value = "公司开户行")
    private String companyBankOpenAccountBank;
    @ApiModelProperty(value = "公司账号")
    private String companyBankAccount;

    @ApiModelProperty(value = "客户签约手机号")
    private String customerSignMobile;
    @ApiModelProperty(value = "销售名字")
    private String staffSignContact;
    @ApiModelProperty(value = "签约年")
    private String signYear;
    @ApiModelProperty(value = "签约月")
    private String signMonth;
    @ApiModelProperty(value = "签约日")
    private String signDay;
    @ApiModelProperty(value = "备注")
    private String remark;
}

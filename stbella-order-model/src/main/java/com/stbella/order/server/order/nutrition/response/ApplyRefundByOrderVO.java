package com.stbella.order.server.order.nutrition.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ApplyRefundByOrderVO implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "审批信息列表（审批+退款申请为一条记录）")
    List<SimpleApplyRefundAuditVO> simpleApplyRefundVOList;
    @ApiModelProperty(value = "订单号")
    private String orderNo;
}

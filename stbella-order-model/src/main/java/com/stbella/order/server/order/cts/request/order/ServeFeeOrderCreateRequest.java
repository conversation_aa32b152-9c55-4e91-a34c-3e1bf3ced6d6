package com.stbella.order.server.order.cts.request.order;

import com.stbella.order.server.order.cts.enums.ServeTypeEnum;
import com.stbella.order.server.order.month.annotations.EnumValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 育婴师-平台管理费-订单创建request
 *
 * <AUTHOR>
 * @date 2022-03-18 14:31
 * @sine 1.0.0
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "育婴师-平台管理费订单创建Model", description = "育婴师-平台管理费订单创建Model")
public class ServeFeeOrderCreateRequest extends SitterOrderCreateBaseRequest implements Serializable {

    private static final long serialVersionUID = -6954321524751326029L;

    @ApiModelProperty(value = "客户认证id")
    private Long orderCustomCertificationId;

    @ApiModelProperty(value = "家庭地址-省")
    private Integer liveProvinceAddressId;

    @ApiModelProperty(value = "家庭地址-市")
    private Integer liveCityAddressId;

    @ApiModelProperty(value = "家庭地址-区")
    private Integer liveAreaAddressId;

    @Size(max = 100, message = "家庭详细地址不能超过100个字符")
    @NotBlank(message = "家庭地址-详细地址不能为空")
    @ApiModelProperty(value = "家庭地址-详细地址 限制100个字符")
    private String liveAddress;

    @EnumValue(enumClass = ServeTypeEnum.class, enumMethod = "isValidCode")
    @ApiModelProperty(value = "服务方式 0:全日住家 1:日间照料 2:其他")
    private Integer serveType;

    @Size(max = 10, message = "服务方式备注不能超过10个字符")
    @ApiModelProperty(value = "服务方式备注 限制10个字符")
    private String serveTypeRemark;
}

package com.stbella.order.server.order.cts.request.order;

import com.stbella.order.server.order.cts.enums.TargetPayTypeEnum;
import com.stbella.order.server.order.month.annotations.EnumValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@ApiModel(value = "订单支付标识", description = "订单支付标识")
public class OrderPaySignRequest implements Serializable {

    private static final long serialVersionUID = 5562635134379195041L;

    @NotBlank(message = "订单编号不能为空")
    @ApiModelProperty("订单编号")
    private String orderNo;

    @EnumValue(enumClass = TargetPayTypeEnum.class, enumMethod = "isValidCode")
    @ApiModelProperty("选择支付类型 0:比例支付 1:自定义金额")
    private Integer targetPayType;

    @ApiModelProperty("选择支付比例 例:50%->50")
    private BigDecimal targetPayPercent;

    @ApiModelProperty("自定义金额")
    private BigDecimal targetPayAmount;
}

package com.stbella.order.server.order.cts.response.order;

import com.stbella.order.server.order.cts.dto.OrderAttachmentDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class SitterOrderDetailVO implements Serializable {


    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "下单时间")
    private Date orderTime;

    @ApiModelProperty(value = "姓名")
    private String sitterName;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "订单状态:100-待支付;200-部分支付;300-全部支付;400-订单取消;500-退款中;600-部分退款;700-全部退款")
    private Integer status;

    @ApiModelProperty(value = "需要签订合同不")
    private Boolean needSignContract;

    @ApiModelProperty(value = "是否签署合同 0否,1是")
    private Integer isSignContract;

    @ApiModelProperty(value = "身份证号")
    private String idCardNo;

    @ApiModelProperty(value = "家庭住址")
    private String homeAddress;

    @ApiModelProperty(value = "育婴师级别")
    private String sitterLevel;

    @ApiModelProperty(value = "育婴师工资 元")
    private BigDecimal sitterWage;

    @ApiModelProperty(value = "管理费金额/保证金/课程价格")
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "课程名(培训课程时生效)")
    private String courseName;

    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @ApiModelProperty(value = "合同签订时间")
    private Date contractSignTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "套餐折扣")
    private BigDecimal discount;

    @ApiModelProperty(value = "是否续单 1.是 0.否")
    private Integer renewTag;

    @ApiModelProperty(value = "支付记录")
    private List<OrderPayRecordResponse> orderPayRecordResponseList;

    @ApiModelProperty("图片附件列表url")
    private List<OrderAttachmentDTO> picUrlList;

    @ApiModelProperty("文件附件列表url")
    private List<OrderAttachmentDTO> fileUrlList;

    @ApiModelProperty(value = "开课时间 13位时间戳")
    private String openingTime;

    @ApiModelProperty(value = "培训有效期开始时间")
    private String trainStart;

    @ApiModelProperty(value = "线下培训地址")
    private String trainingAddress;
}

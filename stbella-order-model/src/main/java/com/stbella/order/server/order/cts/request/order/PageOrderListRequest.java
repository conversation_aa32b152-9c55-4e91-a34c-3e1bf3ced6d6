/*
 * Copyright (c) 2001-2022 GuaHao.com Corporation Limited. All rights reserved.
 * This software is the confidential and proprietary information of GuaHao Company.
 * ("Confidential Information").
 * You shall not disclose such Confidential Information and shall use it only
 * in accordance with the terms of the license agreement you entered into with GuaHao.com.
 */
package com.stbella.order.server.order.cts.request.order;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stbella.core.base.BasePageQuery;
import com.stbella.core.base.UserTokenInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-03-23 10:34
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "订单列表查询", description = "订单列表查询")
public class PageOrderListRequest extends BasePageQuery implements Serializable {
    private static final long serialVersionUID = -1796895447781841983L;

    @ApiModelProperty(value = "订单类型")
    @NotNull(message = "订单类型不能为空")
    private Integer orderType;

    @ApiModelProperty(value = "搜索关键字(客户手机号、姓名)")
    private String searchKey;

    @JsonIgnore
    private UserTokenInfoDTO userTokenInfoDTO;
}

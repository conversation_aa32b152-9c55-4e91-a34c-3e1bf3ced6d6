package com.stbella.order.server.contract.res;

import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/10/16 11:16
 */
@ApiModel(value = "合同主体返回类")
@Data
public class StoreEsignVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "app-id")
    private String appId;

    @ApiModelProperty(value = "app-secret")
    private String appSecret;

    @ApiModelProperty(value = "逻辑删除标志")
    @TableLogic
    private Integer deleted;

    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "修改时间")
    private Date gmtModified;

    @ApiModelProperty(value = "机构/门店名称")
    private String name;

    @ApiModelProperty(value = "E签宝机构唯一标识")
    private String thirdPartyUserId;

    @ApiModelProperty(value = "公司或者门店在e签宝的账户id")
    private String orgId;

    @ApiModelProperty(value = "公司或者门店证件号码，必须填写为社会统一信用代码")
    private String orgIdNo;

    @ApiModelProperty(value = "企业认证经办人或者操作人姓名")
    private String orgHandlerName;

    @ApiModelProperty(value = "企业认证经办人或者操作人身份证号码")
    private String orgHandlerIdNo;

    @ApiModelProperty(value = "经办人账户id(E签宝)。")
    private String accountId;

    @ApiModelProperty(value = "E签宝流程id。")
    private String esignFlowId;

    @ApiModelProperty(value = "状态 0:未认证  1:已认证")
    private Integer state;

    @ApiModelProperty(value = "法人")
    private String legalPerson;

    @ApiModelProperty(value = "省")
    private Integer province;

    @ApiModelProperty(value = "市")
    private Integer city;

    @ApiModelProperty(value = "区/县")
    private Integer region;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "开户名称")
    private String bankOpenAccountName;

    @ApiModelProperty(value = "开户银行")
    private String bankOpenAccountBank;

    @ApiModelProperty(value = "银行账号")
    private String bankAccount;

    @ApiModelProperty(value = "印章ID,取E签宝授权印章ID")
    private String sealId;

    @ApiModelProperty(value = "备注")
    private String remark;

}

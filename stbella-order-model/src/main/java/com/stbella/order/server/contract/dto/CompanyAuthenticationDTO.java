package com.stbella.order.server.contract.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CompanyAuthenticationDTO implements Serializable {

    private static final long serialVersionUID = 8709118115964458529L;

    @ApiModelProperty(value = "实名认证流程id")
    private String flowId;

    @ApiModelProperty(value = "组织机构实名认证短链接，链接有效期30天")
    private String shortLink;

    @ApiModelProperty(value = "组织机构实名认证长链接，链接永久有效")
    private String url;
}

package com.stbella.order.server.order.cts.response.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Classname OrderStatusResponse
 * @Description TODO
 * @Date 2022/4/14 16:14
 * @Created by <PERSON><PERSON>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "订单状态", description = "订单状态")
public class OrderStatusResponse implements Serializable {

    @ApiModelProperty(value = "订单状态:100-待支付;200-部分支付;300-全部支付;400-订单取消;500-退款中;600-部分退款;700-全部退款")
    private Integer status;

}

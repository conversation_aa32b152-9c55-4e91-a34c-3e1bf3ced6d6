package com.stbella.order.server.order.cts.constant;

public class RedisConstant {

    /**
     * 订单标志
     */
    public final static String CTS_ORDER_SIGN_KEY = "cts:order:sign:";

    /**
     * 订单标志锁
     */
    public final static String CTS_ORDER_SIGN_LOCK = "cts:order:sign:lock:";

    /**
     * 订单锁
     */
    public final static String CTS_ORDER_LOCK = "cts:order:lock:";

    /**
     * 订单锁-锁定时间(秒)
     */
    public final static Integer CTS_ORDER_LOCK_SECONDS = 5;

    /**
     * 订单支付标识锁
     */
    public final static String CTS_ORDER_PAY_SIGN_LOCK = "cts:order:pay-sign:lock:";

    /**
     * 订单支付标识
     */
    public final static String CTS_ORDER_PAY_SIGN_KEY = "cts:order:pay-sign:";

    /**
     * 到家后台随机验证码
     */
    public static final String CTS_RANDOM_CODE = "cts:random:code:";
}


package com.stbella.order.server.order.nutrition.dto;

import com.stbella.order.server.order.nutrition.excel.NutritionImportExcel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
@Accessors(chain = true)
@ApiModel(value = "NutritionImportExcel对象（京东）", description = "营养订单导入表格（京东）")
public class NutritionImportExcelJD extends NutritionImportExcel implements Serializable {

    private static final long serialVersionUID = 1L;

    private String srcTids;

    private String csRemark;

    private String buyerMessage;

    private BigDecimal income;
}

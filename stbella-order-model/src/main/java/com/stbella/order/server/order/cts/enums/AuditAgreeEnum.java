package com.stbella.order.server.order.cts.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */

public enum AuditAgreeEnum {

    DISAGREE(0, "不同意"),
    AGREE(1, "同意"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    AuditAgreeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static AuditAgreeEnum getEnumByCode(Integer code) {
        for (AuditAgreeEnum enums : AuditAgreeEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的类型:" + code);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return true;
        }
        for (AuditAgreeEnum monthTypeEnum : values()) {
            if (Objects.equals(monthTypeEnum.getCode(), code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (AuditAgreeEnum enums : AuditAgreeEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的类型:" + code);
    }
}

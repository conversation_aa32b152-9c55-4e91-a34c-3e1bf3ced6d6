package com.stbella.order.server.contract.dto;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ContractSignStartRequestDTO implements Serializable {

    private static final long serialVersionUID = 8709118115964458529L;

    @ApiModelProperty(value = "流程基本信息")
    private InnerFlowInfo flowInfo;

    @Data
    public static class InnerFlowInfo {

        @ApiModelProperty(value = "是否自动归档，默认true自动归档")
        private Boolean autoArchive = true;

        @ApiModelProperty(value = "是否自动开启, true表示自动开启签署流程")
        private Boolean autoInitiate = true;

        @ApiModelProperty(value = "待签署文件名称主题用途名称")
        private String businessScene;

        @ApiModelProperty(value = "任务配置信息")
        private InnerFlowConfigInfo flowConfigInfo;

        @Data
        public static class InnerFlowConfigInfo {

            @ApiModelProperty(value = "通知方式 短信或者邮件获取到的签署链接，有效期默认30天；如果客户需要不通知，可以设置noticeType=\"\"")
            private String noticeType;

            @ApiModelProperty(value = "通知开发者地址")
            private String noticeDeveloperUrl;

            @ApiModelProperty(value = "签署平台，可选择多种签署平台，逗号分割，1-开放服务h5，2-支付宝签,根据业务场景这里填1")
            private String signPlatform;

            @ApiModelProperty(value = "签署完成后重定向地址")
            private String redirectUrl;

            @ApiModelProperty(value = "签署完成重定向跳转延迟时间")
            private Integer redirectDelayTime;

            @ApiModelProperty(value = "个人页面显示实名认证方式 PSN_FACEAUTH_BYURL个人刷脸认证，" +
                    "PSN_TELECOM_AUTHCODE个人运营商三要素认证， PSN_BANK4_AUTHCODE个人银行卡四要素认证，以逗号分隔三种都填进去")
            private List<String> personAvailableAuthTypes;

            @ApiModelProperty(value = "签署页是否显示“一键落章”按钮,此处填false不显示")
            private Boolean batchDropSeal;

            @ApiModelProperty(value = "页面指定意愿认证方式")
            private List<String> willTypes;
        }
    }

    @ApiModelProperty(value = "签署方信息")
    private List<InnerSigners> signers;

    @Data
    public static class InnerSigners {

        @ApiModelProperty(value = "是否平台自动签署, 乙方企业等于false，甲方个人等于true")
        private Boolean platformSign = false;

        @ApiModelProperty(value = "签署方账户信息")
        private InnerSignerAccount signerAccount;

        @Data
        public static class InnerSignerAccount {

            @ApiModelProperty(value = "签署个人账号")
            private String signerAccount;

            @ApiModelProperty(value = "签署主体账号")
            private String authorizedAccountId;

        }

        @ApiModelProperty(value = "签署方的签署区数据")
        private List<InnerSignfields> signfields;

        @Data
        public static class InnerSignfields {

            @ApiModelProperty(value = "是否自动执行，默认false（如果企业自动签署，必须设置为true）")
            private Boolean autoExecute;

            @ApiModelProperty(value = "签署主体是企业时，该字段必传，传入2-机构盖章")
            private Integer actorIndentityType;

            @ApiModelProperty(value = "待签署文件id")
            private String fileId;

            @ApiModelProperty(value = "印章ID")
            private String sealId;

            @ApiModelProperty(value = "签署方式，个人签署时支持多种签署方式，0-手绘签名  ，1-模板印章签名，该业务场景需填0")
            private String sealType;

            @ApiModelProperty(value = "签署区位置信息")
            private InnerPosBean posBean;

            @Data
            public static class InnerPosBean {

                @ApiModelProperty(value = "页码信息")
                private Integer posPage;

                @ApiModelProperty(value = "x坐标")
                private Integer posX;

                @ApiModelProperty(value = "y坐标")
                private Integer posY;
            }
        }

    }

    @ApiModelProperty(value = "待签署文件信息")
    private List<InnerDocs> docs;

    @Data
    public static class InnerDocs {

        @ApiModelProperty(value = "待签署文件id")
        private String fileId;

        @ApiModelProperty(value = "待签署文件名称")
        private String fileName;
    }
}

package com.stbella.order.server.order.nutrition.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@ApiModel(value = "SimpleCommodityAnalysisVO对象", description = "商品分析中商品列表的数据")
public class SimpleCommodityAnalysisVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "序号")
    private Integer no;

    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "零售价（元）")
    private String retailPrice;

    @ApiModelProperty(value = "本期销量（件）")
    private Integer saleNum;

    @ApiModelProperty(value = "销售总额")
    private String saleTotalPrice;

    @ApiModelProperty(value = "销售占比")
    private Double saleProportion;
}

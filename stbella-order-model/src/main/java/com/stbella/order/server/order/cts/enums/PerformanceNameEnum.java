package com.stbella.order.server.order.cts.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import lombok.Getter;

import java.util.Objects;

public enum PerformanceNameEnum {

    CUSTOMER(0, "套餐业绩"),
    NEW(1, "新签业绩"),
    RE_NEW(2, "续签业绩"),
    OTHER(3, "其他业绩"),
    SITTER(4, "培训业绩"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    PerformanceNameEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static PerformanceNameEnum getEnumByCode(Integer code) {
        for (PerformanceNameEnum enums : PerformanceNameEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的类型:" + code);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return true;
        }
        for (PerformanceNameEnum monthTypeEnum : values()) {
            if (Objects.equals(monthTypeEnum.getCode(), code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (PerformanceNameEnum enums : PerformanceNameEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的类型:" + code);
    }
}

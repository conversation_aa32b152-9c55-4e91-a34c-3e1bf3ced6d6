package com.stbella.order.server.order.cts.request.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 支付记录业绩统计查询
 */
@Data
public class PayRecordPerformanceQuery implements Serializable {

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "订单类型")
    private List<Integer> orderTypes;

    @ApiModelProperty(value = "支付状态")
    private List<Integer> payStatuses;

    @ApiModelProperty(value = "记录类型")
    private Integer recordType;

    @ApiModelProperty(value = "是否是业绩")
    private Integer performance;

    @ApiModelProperty(value = "支付类型")
    private List<Integer> payTypes;

    @ApiModelProperty(value = "分站id")
    private Long ctsSiteId;

    @ApiModelProperty(value = "所属销售ID")
    private Long sellId;

}

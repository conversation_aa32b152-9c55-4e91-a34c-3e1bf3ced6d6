package com.stbella.order.server.order.month.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 客诉状态
 *
 * <AUTHOR>
 * @description: CustomerComplaintsStatusEnum 客诉状态
 */
public enum CustomerComplaintsStatusEnum {
    // 10:草稿 50:待处理  200:发起失败 300:审批中 400:审批拒绝 500:处理中 600:处理成功
    WAIT_PROCESS(50, "待处理"),
    DRAFT(10, "草稿"),
    APPROVAL_FAILED(200, "发起失败"),
    APPROVAL_ING(300, "审批中"),
    APPROVAL_REFUSE(400, "审批拒绝"),
    PROCESS(500, "处理中"),
    SUCCESS(600, "处理成功"),
    CANCEL(700, "发起人撤回"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    CustomerComplaintsStatusEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static CustomerComplaintsStatusEnum getEnumByCode(Integer code) {
        for (CustomerComplaintsStatusEnum enums : CustomerComplaintsStatusEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的业务类型:" + code);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return true;
        }
        for (CustomerComplaintsStatusEnum monthTypeEnum : values()) {
            if (Objects.equals(monthTypeEnum.getCode(), code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (CustomerComplaintsStatusEnum enums : CustomerComplaintsStatusEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的业务类型:" + code);
    }

    public static List<Integer> getPicpQueryStatusList() {

        return Arrays.asList(CustomerComplaintsStatusEnum.WAIT_PROCESS.getCode(),
                CustomerComplaintsStatusEnum.APPROVAL_FAILED.getCode(),
                CustomerComplaintsStatusEnum.APPROVAL_ING.getCode(),
                CustomerComplaintsStatusEnum.APPROVAL_REFUSE.getCode(),
                CustomerComplaintsStatusEnum.CANCEL.getCode(),
                CustomerComplaintsStatusEnum.PROCESS.getCode(),
                CustomerComplaintsStatusEnum.SUCCESS.getCode());
    }
}

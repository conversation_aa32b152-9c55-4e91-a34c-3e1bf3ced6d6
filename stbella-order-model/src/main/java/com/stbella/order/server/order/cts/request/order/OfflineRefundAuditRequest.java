package com.stbella.order.server.order.cts.request.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class OfflineRefundAuditRequest implements Serializable {

    @ApiModelProperty(value = "退款记录id")
    @NotNull(message = "退款记录id不能为空")
    private Long id;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty("第三方流水号")
    private String transactionalNo;

    @ApiModelProperty("退款金额")
    private BigDecimal refundAmount;

    @ApiModelProperty("退款凭证")
    private List<String> auditRefundProof;

    @ApiModelProperty("转账时间")
    private Date transferTime;

    @NotBlank(message = "随机数不能为空")
    @ApiModelProperty(value = "随机数")
    private String random;

}

package com.stbella.order.server.order.cts.response.order;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import com.stbella.core.excel.Column;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname OrderAdminListResponse
 * @Description TODO
 * @Date 2022/3/21 16:57
 * @Created by Jacky
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "后管订单列表", description = "后管订单列表")
public class OrderAdminListResponse implements Serializable {


    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "客户手机号")
    private String mobile;

    @ApiModelProperty(value = "签约分站")
    private String ctsSiteName;

    @ApiModelProperty(value = "订单类型;9-育婴师培训课程订单;10-育婴师平台管理费订单;" +
            "11-育婴师上户保证金订单;12-雇主母婴订单;13-雇主育婴师订单，14-育婴师通用订单，15-雇主通用订单")
    private Integer orderType;

    @ApiModelProperty(value = "应收金额")
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "已付金额")
    private BigDecimal realityAmount;

    @ApiModelProperty(value = "累计退款金额")
    private BigDecimal refundedAmount;


    @ApiModelProperty(value = "订单状态:100-待支付;200-部分支付;300-全部支付;400-订单取消;500-退款中;600-部分退款;700-全部退款")
    private Integer status;

    @ApiModelProperty(value = "客户归属销售")
    private String sellName;

    @ApiModelProperty(value = "创建人")
    private String createByName;

    @ApiModelProperty(value = "创建时间，时间戳")
    private Long createByTime;
    /**
     * 雇主
     */
    @ApiModelProperty(value = "套餐等级，根据orderType变动,套餐等级，母婴护理等级：0初级，1中级，2高级，3精品，4月子会所，育婴师等级，0优选，1精选，2特护，3尊享，4月子会所，5乐育，6书育，7礼育，8御育，9养成")
    private Integer comboLevel;

    @ApiModelProperty(value = "套餐天数，根据orderType变动")
    private Integer comboDays;

    @ApiModelProperty(value = "套餐价格")
    private BigDecimal comboPrice;

    @ApiModelProperty(value = "套餐折扣")
    private BigDecimal discount;

    @ApiModelProperty(value = "日均费用")
    private BigDecimal dailyPayableAmount;

    /**
     * 育婴师平台管理
     */

    @ApiModelProperty(value = "日工资")
    private BigDecimal sitterWage;

    @ApiModelProperty(value = "育婴师月工资")
    private BigDecimal monthSitterWage;

    /**
     * 育婴师培训课程
     */

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "课程单价")
    private BigDecimal coursePrice;

    @ApiModelProperty(value = "证书费")
    private BigDecimal cerFee;

    @ApiModelProperty(value = "是否需要证书")
    private Integer isCer;

    /**
     * 雇主通用订单、育婴师通用订单
     */
    @ApiModelProperty(value = "通用商品名称")
    private String productName;

    @ApiModelProperty(value = "是否续单:1-是 0-不是")
    private Integer renewTag;

    @ApiModelProperty(value = "客户渠道")
    private String customerChannel;

    @ApiModelProperty(value = "首次支付时间")
    private Date firstPayTime;

    @ApiModelProperty(value = "完成支付时间")
    private Date finishTime;

    @ApiModelProperty(value = "到家育婴师名")
    private String ctsSitterName;

    @ApiModelProperty(value = "上户育婴师等级")
    private String ctsSitterLevel;

    @ApiModelProperty(value = "上户育婴师工资")
    private BigDecimal ctsSitterWage;

    @ApiModelProperty(value = "上户育婴师奖金")
    private BigDecimal ctsSitterReward;

    @ApiModelProperty(value = "应结工资")
    private BigDecimal salary;

    @ApiModelProperty(value = "实际上户时间")
    private Date realityServeStart;

    @ApiModelProperty(value = "实际下户时间")
    private Date realityServeEnd;

    @ApiModelProperty(value = "预计上户时间")
    private Date expectServeStartDate;

    @ApiModelProperty(value = "预计上户午时间")
    private Date expectServeEndDate;


}

/*
 * Copyright (c) 2001-2022 GuaHao.com Corporation Limited. All rights reserved.
 * This software is the confidential and proprietary information of GuaHao Company.
 * ("Confidential Information").
 * You shall not disclose such Confidential Information and shall use it only
 * in accordance with the terms of the license agreement you entered into with GuaHao.com.
 */
package com.stbella.order.server.order.cts.response.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-03-23 19:42
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "到家订单中存在通用商品", description = "到家订单中存在通用商品")
public class OrderGeneralProductResponse implements Serializable {
    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("通用商品名称")
    private String productName;
    @ApiModelProperty("通用商品类型，1雇主类型，2育婴师类型，3雇主、育婴师")
    private Integer productType;
    @ApiModelProperty("通用商品订单价格")
    private BigDecimal productPrice;
    @ApiModelProperty("是否计入经营业绩，0不计入业绩，1计入业绩")
    private Integer performance;
    @ApiModelProperty("状态，0启用，1禁用")
    private Integer status;
}

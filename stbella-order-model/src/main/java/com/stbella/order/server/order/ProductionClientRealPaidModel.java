package com.stbella.order.server.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ProductionClientRealPaidModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Integer clientId;

    @ApiModelProperty(value = "实付金额")
    private BigDecimal val;

}

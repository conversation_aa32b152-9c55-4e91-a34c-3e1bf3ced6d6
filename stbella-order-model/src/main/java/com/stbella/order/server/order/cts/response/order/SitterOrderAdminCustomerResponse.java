package com.stbella.order.server.order.cts.response.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 育婴师信息
 *
 * <AUTHOR>
 */

@Data
public class SitterOrderAdminCustomerResponse implements Serializable {
    private static final long serialVersionUID = -3396783430477725381L;


    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "客户手机号")
    private String mobile;

    @ApiModelProperty(value = "证件类型")
    private String idCardTypeName;

    @ApiModelProperty(value = "证件号")
    private String idCard;

    @ApiModelProperty(value = "住宅地址")
    private String liveAddress;
}

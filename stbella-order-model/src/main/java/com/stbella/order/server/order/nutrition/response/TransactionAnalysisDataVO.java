package com.stbella.order.server.order.nutrition.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "TransactionAnalysisDataVO对象", description = "交易分析输出")
public class TransactionAnalysisDataVO implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "各渠道数据")
    List<EachChannelVO> eachChannelVOList;
    @ApiModelProperty(value = "累计订单金额（总）")
    private String totalOrderAmount;
    @ApiModelProperty(value = "累计订单数（总）")
    private String cumulativeOrders;

}

package com.stbella.order.server.order.nutrition.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "OnlineRetailergetSkuByGoodsIdVO对象", description = "电商中心sku列表")
public class OnlineRetailergetSkuByGoodsIdVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品编码")
    private String goodsNo;

    @ApiModelProperty(value = "商品图片")
    private String specUrl;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品税率")
    private BigDecimal commodityTaxRate;

    @ApiModelProperty(value = "具体sku列表")
    private List<SimpleSkuVO> simpleSkuVOList;


}

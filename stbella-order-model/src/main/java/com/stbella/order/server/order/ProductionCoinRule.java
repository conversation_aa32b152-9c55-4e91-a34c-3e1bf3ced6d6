package com.stbella.order.server.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2023-07-25  16:06
 * @Description: 产康币规则
 * {"type":0,"limit":0,"reduction":0}
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductionCoinRule implements Serializable {

    @ApiModelProperty(value = "类型：-1 不可用，0 全额，1 每满减")
    private Integer type;

    @ApiModelProperty(value = "满多少")
    private Integer limit;

    @ApiModelProperty(value = "减多少")
    private Integer reduction;

}

package com.stbella.order.server.order;

import com.stbella.core.base.BasicReq;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "多胞胎节假日费用规则", description = "多胞胎规则实体")
public class StoreFinanceConfigFact extends BasicReq implements Serializable {
    private static final long serialVersionUID = 6616565746205526624L;

    @ApiModelProperty(value = "业务线 BusinessEnum")
    @NotNull
    private Integer bu = 0;

    @ApiModelProperty(value = "门店id")
    private Integer storeId;

    @ApiModelProperty(value = "门店类型 看枚举 StoreTypeEnum")
    private Integer storeType;

    @ApiModelProperty(value = "门店子品牌 看枚举 StoreChildTypeEnum ")
    private Integer childStoreType;


}

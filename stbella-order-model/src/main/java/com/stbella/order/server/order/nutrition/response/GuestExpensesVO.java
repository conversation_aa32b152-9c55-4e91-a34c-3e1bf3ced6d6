package com.stbella.order.server.order.nutrition.response;

import com.stbella.order.server.order.nutrition.enums.MonthEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "GuestExpensesVO对象", description = "获客成本-花费")
@Accessors(chain = true)
public class GuestExpensesVO extends GuestBaseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "类型：1-花费；2-当月首单购买人数；3-全部购买人数；4-新客成本；5-全部客户成本；6-CAC")
    private String type = "花费（元）";

    public BigDecimal getByMonth(int month) {
        switch (MonthEnum.getEnumByCode(month)) {
            case JANUARY:
                return StringUtils.isNotEmpty(getJanuary()) ? new BigDecimal(getJanuary()) : null;
            case FEBRUARY:
                return StringUtils.isNotEmpty(getFebruary()) ? new BigDecimal(getFebruary()) : null;
            case MARCH:
                return StringUtils.isNotEmpty(getMarch()) ? new BigDecimal(getMarch()) : null;
            case APRIL:
                return StringUtils.isNotEmpty(getApril()) ? new BigDecimal(getApril()) : null;
            case MAY:
                return StringUtils.isNotEmpty(getMay()) ? new BigDecimal(getMay()) : null;
            case JUNE:
                return StringUtils.isNotEmpty(getJune()) ? new BigDecimal(getJune()) : null;
            case JULY:
                return StringUtils.isNotEmpty(getJuly()) ? new BigDecimal(getJuly()) : null;
            case AUGUST:
                return StringUtils.isNotEmpty(getAugust()) ? new BigDecimal(getAugust()) : null;
            case SEPTEMBER:
                return StringUtils.isNotEmpty(getSeptember()) ? new BigDecimal(getSeptember()) : null;
            case OCTOBER:
                return StringUtils.isNotEmpty(getOctober()) ? new BigDecimal(getOctober()) : null;
            case NOVEMBER:
                return StringUtils.isNotEmpty(getNovember()) ? new BigDecimal(getNovember()) : null;
            case DECEMBER:
                return StringUtils.isNotEmpty(getDecember()) ? new BigDecimal(getDecember()) : null;
            default:
                return null;
        }
    }
}
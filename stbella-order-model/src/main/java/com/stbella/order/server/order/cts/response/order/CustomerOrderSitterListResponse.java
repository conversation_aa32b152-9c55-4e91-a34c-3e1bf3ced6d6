package com.stbella.order.server.order.cts.response.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel(value = "雇主订单-育婴师列表", description = "雇主订单-育婴师列表")
public class CustomerOrderSitterListResponse implements Serializable {

    private static final long serialVersionUID = -3883050310002296755L;

    @ApiModelProperty("订单育婴师快照id")
    private Long orderSitterSnapshotId;

    @ApiModelProperty(value = "匹配日期")
    private Date matchDate;

    @ApiModelProperty(value = "月工资")
    private BigDecimal wage;

    @ApiModelProperty(value = "日工资")
    private BigDecimal dailyWage;

    @ApiModelProperty(value = "育婴师姓名")
    private String sitterName;

    @ApiModelProperty(value = "是否终止服务 0:否 1:是")
    private Integer isTermination;
}

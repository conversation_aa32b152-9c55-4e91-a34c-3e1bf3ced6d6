package com.stbella.order.server.order.nutrition.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ExtensionListRequest对象", description = "推广费用列表添加对象")
public class ExtensionListRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "推广费用列表类型添加对象")
    private List<ExtensionListTypeRequest> extensionListTypeRequestList;

    @ApiModelProperty(value = "店铺类型")
    @NotNull(message = "店铺类型不能为空")
    private Integer storeType;

}

/*
 * Copyright (c) 2001-2022 GuaHao.com Corporation Limited. All rights reserved.
 * This software is the confidential and proprietary information of GuaHao Company.
 * ("Confidential Information").
 * You shall not disclose such Confidential Information and shall use it only
 * in accordance with the terms of the license agreement you entered into with GuaHao.com.
 */
package com.stbella.order.server.order.cts.request.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-03-23 10:34
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "订单类型查询", description = "订单类型查询")
public class OrderTypeRequest implements Serializable {
    private static final long serialVersionUID = 1820466408136586490L;

    @ApiModelProperty(value = "订单类型")
    private List<Integer> orderTypeList;

}

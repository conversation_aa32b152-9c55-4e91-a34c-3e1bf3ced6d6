package com.stbella.order.server.order.nutrition.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.stbella.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 广禾堂业绩目标
 * </p>
 *
 * <AUTHOR> @since 2022-02-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("order_nutrition_objectives")
@ApiModel(value = "OrderNutritionObjectivesPO对象", description = "广禾堂业绩目标")
public class OrderNutritionObjectivesPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "1-广禾堂销售总业绩；2-电商渠道总业绩；3-门店渠道总业绩；4-分销渠道总业绩；5-新零售")
    private Integer type;

    @ApiModelProperty(value = "年份")
    private Integer yearNum;

    @ApiModelProperty(value = "一月目标")
    private BigDecimal january;

    @ApiModelProperty(value = "二月目标")
    private BigDecimal february;

    @ApiModelProperty(value = "三月目标")
    private BigDecimal march;

    @ApiModelProperty(value = "四月目标")
    private BigDecimal april;

    @ApiModelProperty(value = "五月目标")
    private BigDecimal may;

    @ApiModelProperty(value = "六月目标")
    private BigDecimal june;

    @ApiModelProperty(value = "七月目标")
    private BigDecimal july;

    @ApiModelProperty(value = "八月目标")
    private BigDecimal august;

    @ApiModelProperty(value = "九月目标")
    private BigDecimal september;

    @ApiModelProperty(value = "十月目标")
    private BigDecimal october;

    @ApiModelProperty(value = "十一月目标")
    private BigDecimal november;

    @ApiModelProperty(value = "十二月目标")
    private BigDecimal december;

    private Long createBy;

    private String createByName;

    private Long updateBy;

    private String updateByName;


}

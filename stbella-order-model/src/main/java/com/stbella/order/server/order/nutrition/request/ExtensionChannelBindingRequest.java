package com.stbella.order.server.order.nutrition.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ExtensionChannelBindingRequest对象", description = "推广费用绑定渠道对象")
public class ExtensionChannelBindingRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "推广类型id")
    @NotNull(message = "推广类型id不能为空")
    private List<Long> extensionTypeIdList;

    @ApiModelProperty(value = "店铺类型")
    @NotNull(message = "店铺类型不能为空")
    private Integer storeType;


}

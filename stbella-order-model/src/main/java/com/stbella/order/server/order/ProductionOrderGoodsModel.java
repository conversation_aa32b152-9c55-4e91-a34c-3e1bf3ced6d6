package com.stbella.order.server.order;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2023-07-25  16:06
 * @Description: 产康商品明细，包含订单信息
 */
@Data
public class ProductionOrderGoodsModel implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "订单ID（主键自增)")
    private Integer orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    @ApiModelProperty(value = " 订单类型： 0 月子客户的“普通月子套餐”订单； 1 月子客户的“小月子”订单； 10 到家客户的“到家服务”订单； 20 到家阿姨的“到家服务订单”； 21 到家阿姨的阿姨培训订单； 30 月子其他订单 31 到家其他订单 40 商城普通订单 50护士外派订单 60产康订单 70S-BRA订单 描述： 0-9 归属月子平台的客户订单类型 ； 10-19 归属到家客户的订单类型； 20-29到家阿姨的订单类型; 30-39 其他订单 40-49 商城订单类型 50护士外派订单 60产康订单 70S-BRA订单")
    private Integer orderType;

    @ApiModelProperty(value = "全局用户ID")
    private Integer basicUid;

    @ApiModelProperty(value = "买家客户ID（ecp库的tab_client表主键）")
    private Integer clientUid;

    @ApiModelProperty(value = "门店ID(ecp库cfg_store表的主键id)")
    private Integer storeId;

    @ApiModelProperty(value = "原价总金额，单位：分")
    private Integer orderAmount;


    @ApiModelProperty(value = "应付总金额")
    private Integer payAmount;

    @ApiModelProperty(value = "已支付总金额，单位：分")
    private Integer paidAmount;

    @ApiModelProperty(value = "实际金额(单位分,受退款影响)")
    private Integer realAmount;

    @ApiModelProperty(value = "产康金抵扣金额")
    private Integer productionAmountPay;

    @ApiModelProperty(value = "付款时间")
    private Integer percentFirstTime;

    @ApiModelProperty(value = "订单创建时间")
    private Integer createdAt;

    /**
     * extend id 产康商品自增id
     */
    private Integer id;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 关联sku id
     */
    private Integer skuId;

    /**
     * 规格名称
     */
    private String skuName;

    /**
     * 1=通卡 0=次卡 2=单项服务
     */
    private Integer type;

    /**
     * 价格
     */
    private Integer price;

    /**
     * 成本
     */
    private Integer cost;

    /**
     * 具体信息
     */
    private String content;

    /**
     * 单项服务有效期开始时间
     */
    private Long validStartTime;

    /**
     * 单项服务有效期结束时间
     */
    private Long validEndTime;

    /**
     * 核销状态 0=未核销  1=已核销 2已预约
     */
    private Integer verificationStatus;

    /**
     * 状态: 0-冻结 1-正常 2-已失效',
     */
    private Integer status;

    /**
     * 关联商品id
     */
    private Integer goodsId;

    /**
     * 商品数量 特殊商品默认为1 实物商品情况而定
     */
    private Integer goodsNum;

    /**
     * 单个商品包含的sku数量（有些商品一个含有10个），这个影响价格
     */
    private Integer skuNum;


    /**
     * 礼赠-来源：1 表示是小助手，不是1表示后台
     */
    private Integer source;


    /**
     * 实际支付金额
     */
    private BigDecimal realPaid;

    /**
     *数据来源：0表示正常，1表示后台核销补录
     */
    private Integer dataSource;
}

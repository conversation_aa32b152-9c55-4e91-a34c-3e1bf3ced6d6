package com.stbella.order.server.order.nutrition.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.stbella.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 旺店通-sku列表
 * </p>
 *
 * <AUTHOR> @since 2022-09-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("wdt_sku")
@ApiModel(value = "WdtSkuPO对象", description = "旺店通-sku列表")
public class WdtSkuPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商家编码")
    private String specId;

    @ApiModelProperty(value = "规格码")
    private String specNo;

    @ApiModelProperty(value = "规格名称")
    private String specName;

    @ApiModelProperty(value = "sku图片")
    private String specUrl;

    @ApiModelProperty(value = "货品ID")
    private Long goodsId;

    @ApiModelProperty(value = "最低价")
    private BigDecimal lowestPrice;

    @ApiModelProperty(value = "零售价")
    private BigDecimal retailPrice;

    @ApiModelProperty(value = "批发价")
    private BigDecimal wholesalePrice;

    @ApiModelProperty(value = "会员价")
    private BigDecimal memberPrice;

    @ApiModelProperty(value = "市场价")
    private BigDecimal marketPrice;

    @ApiModelProperty(value = "成本价")
    private BigDecimal costPrice;


}

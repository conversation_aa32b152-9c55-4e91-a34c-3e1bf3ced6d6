package com.stbella.order.server.order.nutrition.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "AddEnableMarketingRequest对象", description = "品牌营销启动渠道请求参数")
public class AddEnableMarketingRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "渠道id列表")
    @NotNull(message = "渠道列表不能为空")
    private List<Long> idList;

}

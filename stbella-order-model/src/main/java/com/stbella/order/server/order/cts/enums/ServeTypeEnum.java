package com.stbella.order.server.order.cts.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import lombok.Getter;

import java.util.Objects;

/**
 * 服务方式
 */
public enum ServeTypeEnum {
    ALL_DAY(0, "全日住家"),
    WORK_DAY(1, "日间照料"),
    OTHER(2, "其他"),
    AUNT_SITTER(3, "母婴护理师"),
    BABY_SITTER(4, "育婴师"),
    Home_Companion(5, "家庭陪伴师");

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    ServeTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static ServeTypeEnum getEnumByCode(Integer code) {
        for (ServeTypeEnum enums : ServeTypeEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的服务方式:" + code);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return true;
        }
        for (ServeTypeEnum monthTypeEnum : values()) {
            if (Objects.equals(monthTypeEnum.getCode(), code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (ServeTypeEnum enums : ServeTypeEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的服务方式:" + code);
    }
}

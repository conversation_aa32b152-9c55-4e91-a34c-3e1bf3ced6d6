package com.stbella.order.server.contract.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 门店认证信息
 * </p>
 *
 * <AUTHOR> @since 2023-10-16
 */
@Data
@ApiModel(value = "修改门店认证信息请求类")
public class StoreEsignUpdateRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "app-id")
    private String appId;

    @ApiModelProperty(value = "app-secret")
    private String appSecret;

    @ApiModelProperty(value = "机构/门店名称")
    @NotNull(message = "企业认证经办人或者操作人身份证号码不能为空")
    private String name;

    @ApiModelProperty(value = "E签宝机构唯一标识")
    private String thirdPartyUserId;

    @ApiModelProperty(value = "公司或者门店在e签宝的账户id")
    private String orgId;

    @ApiModelProperty(value = "公司或者门店证件号码，必须填写为社会统一信用代码")
    @NotNull(message = "社会统一信用代码不能为空")
    private String orgIdNo;

    @ApiModelProperty(value = "企业认证经办人或者操作人姓名")
    private String orgHandlerName;

    @ApiModelProperty(value = "企业认证经办人或者操作人身份证号码")
    private String orgHandlerIdNo;

    @ApiModelProperty(value = "经办人账户id(E签宝)。")
    private String accountId;

    @ApiModelProperty(value = "E签宝流程id。")
    private String esignFlowId;

    @ApiModelProperty(value = "状态 0:未认证  1:已认证")
    private Integer state;

    @ApiModelProperty(value = "法人")
    @NotNull(message = "法人不能为空")
    private String legalPerson;

    @ApiModelProperty(value = "省")
    @NotNull(message = "省不能为空")
    private Integer province;

    @ApiModelProperty(value = "市")
    @NotNull(message = "市不能为空")
    private Integer city;

    @ApiModelProperty(value = "区/县")
    @NotNull(message = "区/县不能为空")
    private Integer region;

    @ApiModelProperty(value = "详细地址")
    @NotNull(message = "详细地址不能为空")
    private String address;

    @ApiModelProperty(value = "开户名称")
    @NotNull(message = "开户名称不能为空")
    private String bankOpenAccountName;

    @ApiModelProperty(value = "开户银行")
    @NotNull(message = "开户银行不能为空")
    private String bankOpenAccountBank;

    @ApiModelProperty(value = "银行账号")
    @NotNull(message = "银行账号不能为空")
    private String bankAccount;

    @ApiModelProperty(value = "印章ID,取E签宝授权印章ID")
    @NotNull(message = "印章ID不能为空")
    private String sealId;

    @ApiModelProperty(value = "备注")
    private String remark;


}

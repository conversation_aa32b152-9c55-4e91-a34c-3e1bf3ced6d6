package com.stbella.order.server.order.nutrition.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import lombok.Getter;

import java.util.Objects;

/**
 * 营养订单申请退款的时候当前的订单的出餐状态
 */
public enum OrderNutritionFoodStatusEnum {
    NOT_MEAL(0, "未出餐"),
    HAVE_MEAL(1, "已出餐");

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    OrderNutritionFoodStatusEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static OrderNutritionFoodStatusEnum getEnumByCode(Integer code) {
        for (OrderNutritionFoodStatusEnum enums : OrderNutritionFoodStatusEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的业务类型:" + code);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return true;
        }
        for (OrderNutritionFoodStatusEnum monthTypeEnum : values()) {
            if (Objects.equals(monthTypeEnum.getCode(), code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (OrderNutritionFoodStatusEnum enums : OrderNutritionFoodStatusEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的业务类型:" + code);
    }
}

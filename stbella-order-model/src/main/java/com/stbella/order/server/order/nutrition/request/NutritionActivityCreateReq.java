package com.stbella.order.server.order.nutrition.request;

import com.baomidou.mybatisplus.annotation.*;
import com.stbella.core.base.BasicReq;

import java.util.Date;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 广禾堂-活动业绩-类型
 * </p>
 *
 * <AUTHOR> @since 2023-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="NutritionActivityCreateReq", description="广禾堂-活动业绩-类型")
public class NutritionActivityCreateReq extends BasicReq {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "活动名称")
    @NotNull(message = "活动名称不能为空")
    private String activityName;

    @ApiModelProperty(value = "活动备注")
    private String remark;


}

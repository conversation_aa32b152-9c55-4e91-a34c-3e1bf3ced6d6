package com.stbella.order.server.order.nutrition.request;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel(value = "OrderNutritionRequest对象", description = "营养订单查询对象")
public class AddOrderNutritionRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户编号")
    @NotNull(message = "用户编号不能为空")
    @Max(value = Long.MAX_VALUE, message = "用户不存在")
    private Long customId;

    @ApiModelProperty(value = "套餐编号")
    @NotNull(message = "套餐编号不能为空")
    @Max(value = Long.MAX_VALUE, message = "套餐不存在")
    private Long comboId;

    @ApiModelProperty(value = "套餐数量")
    @NotNull(message = "套餐数量不能为空")
    @Min(value = 1, message = "套餐数量最低为1份")
    @Max(value = 999999, message = "套餐数量最高为999999份")
    private Integer comboNum;

    @ApiModelProperty(value = "渠道价格编号")
    @NotNull(message = "渠道价格不能为空")
    @Max(value = Long.MAX_VALUE, message = "渠道价格不存在或已被下架")
    private Long channelId;

    @ApiModelProperty(value = "运费")
    private BigDecimal freightPrice;

    @ApiModelProperty(value = "预计送餐时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date planSendFoodTime;

    @ApiModelProperty(value = "送餐地址-省")
    @NotNull(message = "送餐地址-省不能为空")
    @Length(max = 256, message = "送餐地址-省最大可输入256字符")
    private String sendFoodAddressProvince;

    @ApiModelProperty(value = "送餐地址-市")
    @NotNull(message = "送餐地址-市不能为空")
    @Length(max = 256, message = "送餐地址-市最大可输入256字符")
    private String sendFoodAddressCity;

    @ApiModelProperty(value = "送餐地址-区")
    @NotNull(message = "送餐地址-区不能为空")
    @Length(max = 256, message = "送餐地址-区最大可输入256字符")
    private String sendFoodAddressArea;

    @ApiModelProperty(value = "送餐地址-详细地址")
    @NotNull(message = "送餐地址-详细地址不能为空")
    @Length(max = 256, message = "送餐地址最大可输入256字符")
    private String sendFoodAddress;

    @ApiModelProperty(value = "订单备注")
    @Length(max = 50, message = "备注最大可输入50个")
    private String remark;

    @ApiModelProperty(value = "送餐地址-用户姓名")
    private String sendFoodAddressCustomerName;

    @ApiModelProperty(value = "送餐地址-用户手机号")
    private String sendFoodAddressCustomerPhone;

    public BigDecimal getFreightPrice() {
        return ObjectUtil.isNull(freightPrice) ? BigDecimal.ZERO : freightPrice;
    }
}

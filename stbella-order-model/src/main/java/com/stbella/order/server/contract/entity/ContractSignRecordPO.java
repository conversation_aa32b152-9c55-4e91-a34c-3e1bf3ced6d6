package com.stbella.order.server.contract.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.stbella.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 签署记录表
 * </p>
 *
 * <AUTHOR> @since 2022-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("contract_sign_record")
@ApiModel(value = "ContractSignRecordPO对象", description = "签署记录表")
public class ContractSignRecordPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "合同模版id")
    private String templateId;

    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @ApiModelProperty(value = "销售id")
    private Long staffId;

    @ApiModelProperty(value = "合同状态")
    private Integer status;

    @ApiModelProperty(value = "签约参数")
    private String param;


    @ApiModelProperty(value = "E签宝签署流程id")
    private String eSignFlowId;

    @ApiModelProperty(value = "E签宝文件id")
    private String eSignFileId;

    @ApiModelProperty(value = "E签宝文件名称")
    private String eSignFileName;

    @ApiModelProperty(value = "E签宝合同查看长链接")
    private String eSignLongUrl;

    @ApiModelProperty(value = "E签宝合同查看短链接")
    private String eSignShortUrl;

    @ApiModelProperty(value = "签约时间")
    private Date signTime;


}

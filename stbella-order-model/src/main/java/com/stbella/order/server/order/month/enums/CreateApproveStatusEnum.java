package com.stbella.order.server.order.month.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import lombok.Getter;

import java.util.Objects;

public enum CreateApproveStatusEnum {
    //折扣审批状态 0=无需审批 1=审批中 2=审批通过 3=审批失败 4=发起失败
    SUCCESS(1, "发起成功"),
    FAILED(2, "发起失败");

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    CreateApproveStatusEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static CreateApproveStatusEnum getEnumByCode(Integer code) {
        for (CreateApproveStatusEnum enums : CreateApproveStatusEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的业务类型:" + code);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return true;
        }
        for (CreateApproveStatusEnum monthTypeEnum : values()) {
            if (Objects.equals(monthTypeEnum.getCode(), code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (CreateApproveStatusEnum enums : CreateApproveStatusEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的业务类型:" + code);
    }
}

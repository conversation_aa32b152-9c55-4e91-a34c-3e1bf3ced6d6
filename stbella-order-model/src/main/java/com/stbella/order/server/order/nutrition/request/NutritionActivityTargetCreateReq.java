package com.stbella.order.server.order.nutrition.request;

import com.stbella.core.base.BasicReq;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 广禾堂-活动业绩-类型
 * </p>
 *
 * <AUTHOR> @since 2023-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="NutritionActivityTargetCreateReq", description="广禾堂-活动业绩-目标")
public class NutritionActivityTargetCreateReq extends BasicReq {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动业绩类型id",required = true)
    @NotNull(message = "活动业绩类型不能为空")
    private Long activityId;

    @ApiModelProperty(value = "目标渠道值")
    private List<ActivityTargetReq> targetList;


    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    @ApiModel(description = "ActivityTargetReq")
    public static class  ActivityTargetReq implements Serializable {

        private static final long serialVersionUID = -2426967218502013725L;

        @ApiModelProperty(value = "分类1-天猫;2-抖音;3-京东;4-私域;",required = true)
        private Integer type;

        @ApiModelProperty(value = "t-1日总业绩,金额元,手动输入")
        private BigDecimal baseAmount;

        @ApiModelProperty(value = "t日总业绩,金额元,影刀抓取")
        private BigDecimal amount;
    }


}

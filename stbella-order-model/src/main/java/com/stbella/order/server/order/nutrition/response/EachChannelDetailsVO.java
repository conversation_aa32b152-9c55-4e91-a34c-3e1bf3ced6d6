package com.stbella.order.server.order.nutrition.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "TransactionAnalysisVO对象", description = "交易分析反参")
public class EachChannelDetailsVO implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "订单数量分布")
    List<OrderQuantityDistributionVO> orderQuantityDistributionList;
    @ApiModelProperty(value = "订单金额分布")
    List<String> orderAmountDistributionList;
    @ApiModelProperty(value = "表格数据")
    List<AmountDistributionVO> amountDistributionVOList;
    @ApiModelProperty(value = "类型：100-电商全部；101-天猫；102-京东；200-新零售全部；201-有赞；300-分销全部；301-分销；400-门店全部；401-上海；402-北京")
    private Integer channelDetailsType;
}

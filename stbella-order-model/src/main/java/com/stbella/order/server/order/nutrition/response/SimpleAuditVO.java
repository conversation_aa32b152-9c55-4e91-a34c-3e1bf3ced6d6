package com.stbella.order.server.order.nutrition.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SimpleAuditVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "审核状态;0-审批中;1-审批通过;2-审批拒绝")
    private Integer auditStatus;

    @ApiModelProperty(value = "驳回原因")
    private String rejectReason;

    @ApiModelProperty(value = "审批人姓名")
    private String auditName;

    @ApiModelProperty(value = "审批时间")
    private String auditTime;

    @ApiModelProperty(value = "创建人姓名")
    private String createByName;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;


}

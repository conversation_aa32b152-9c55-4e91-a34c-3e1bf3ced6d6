package com.stbella.order.server.order.month.entity;

import com.stbella.core.base.BaseEntity;
import com.stbella.order.server.order.month.constant.BaseConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel(value = "资金记录表PO对象", description = "资金记录表PO对象")
public class MoneyRecordPO extends BaseEntity {

    @ApiModelProperty(value = BaseConstant.AMOUNT_TYPE)
    private Integer recordType;
    @ApiModelProperty(value = "本地流水号")
    private String localTransactionalNo;
    @ApiModelProperty(value = BaseConstant.AMOUNT_TYPE)
    private Integer amountType;
    @ApiModelProperty(value = "订单类型")
    private String orderNo;
    @ApiModelProperty(value = "第三方流水号")
    private String transactionalNo;
    @ApiModelProperty(value = BaseConstant.ORDER_TYPR)
    private Integer orderType;
    @ApiModelProperty(value = BaseConstant.ORDER_CHILD_TYPE)
    private Integer orderChildType;
    @ApiModelProperty(value = "门店编号")
    private Long storeId;
    @ApiModelProperty(value = "客户编号")
    private Long customId;
    @ApiModelProperty(value = "客户姓名")
    private String customName;
    @ApiModelProperty(value = "客户手机号")
    private String customPhone;
    @ApiModelProperty(value = "收退金额")
    private BigDecimal payAmount;
    @ApiModelProperty(value = "订单实收款")
    private BigDecimal realityAmount;
    @ApiModelProperty(value = BaseConstant.PAY_TYPE)
    private Integer payType;
    @ApiModelProperty(value = "支付时间")
    private Date payTime;
    @ApiModelProperty(value = BaseConstant.PAY_STATUS)
    private Integer payStatus;

}

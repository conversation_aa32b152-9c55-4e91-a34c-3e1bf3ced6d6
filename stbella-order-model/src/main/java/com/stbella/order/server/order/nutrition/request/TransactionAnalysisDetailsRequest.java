package com.stbella.order.server.order.nutrition.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Accessors(chain = true)
@ApiModel(value = "TransactionAnalysisDetailsRequest对象", description = "交易分析详情请求参数")
public class TransactionAnalysisDetailsRequest extends TransactionAnalysisRequest implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "渠道编号")
    @NotNull(message = "渠道不能为空")
    private Integer channelId;

    @ApiModelProperty(value = "具体的子渠道编号（查全部传空）")
    private Integer simpleEachChannelId;

}

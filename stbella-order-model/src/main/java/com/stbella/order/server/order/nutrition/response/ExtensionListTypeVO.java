package com.stbella.order.server.order.nutrition.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ExtensionListTypeVO对象", description = "推广费用列表类型对象")
public class ExtensionListTypeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "推广类型id")
    private Long extensionTypeId;

    @ApiModelProperty(value = "推广渠道名称")
    private String extensionTypeName;

    @ApiModelProperty(value = "推广费用列表返回具体对象")
    private List<ExtensionListDetailVO> extensionListDetailVOList;


}

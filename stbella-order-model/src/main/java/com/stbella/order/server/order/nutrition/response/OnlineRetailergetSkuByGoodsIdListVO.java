package com.stbella.order.server.order.nutrition.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "OnlineRetailergetSkuByGoodsIdVO对象", description = "电商中心sku列表")
@Accessors(chain = true)
public class OnlineRetailergetSkuByGoodsIdListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "sku列表")
    private List<AllOnlineRetailergetSkuByGoodsIdVO> onlineRetailergetSkuByGoodsIdVOList;


}

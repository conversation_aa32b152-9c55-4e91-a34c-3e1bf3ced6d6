package com.stbella.order.server.order.month.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "线下支付凭证", description = "线下支付凭证出参")
@Data
public class QueryIncomeProofRecordByIncomeSnVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "支付凭证id")
    private Long id;

    @ApiModelProperty(value = "支付凭证")
    private String payProof;

    @ApiModelProperty(value = "线下支付方式：0其他，1pos机， 2汇款 3现金")
    private Integer payType;

    @ApiModelProperty(value = "凭证金额：上传凭证时输入的金额(单位元)")
    private BigDecimal incomeProof;

    @ApiModelProperty(value = "凭证备注")
    private String remark;

    @ApiModelProperty(value = "创建人名称")
    private String createdName;

    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "状态 0=未审核 1=审核通过 2=审核拒绝")
    private Integer status;

    @ApiModelProperty(value = "审核人basic_uid")
    private Integer auditBasicId;

    @ApiModelProperty(value = "审核人姓名")
    private String auditName;

    @ApiModelProperty(value = "审核时间")
    private Date auditDate;

    @ApiModelProperty(value = "支付记录ID")
    private Integer incomeId;

    @ApiModelProperty(value = "货币码")
    private String currency;


}

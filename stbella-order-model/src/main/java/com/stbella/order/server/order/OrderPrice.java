package com.stbella.order.server.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: ji<PERSON><PERSON><PERSON>
 * @CreateTime: 2023-07-25  16:06
 * @Description: 价格对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class OrderPrice implements Serializable {

    @ApiModelProperty(value = "选购金额")
    private BigDecimal selectedAmount;

    @ApiModelProperty(value = "订单原价：商品价格*Num(含有加收项)")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "商品原价：商品价格*Num")
    private BigDecimal goodsAmount;

    @ApiModelProperty(value = "成本")
    private BigDecimal totalCost;

    @ApiModelProperty(value = "签单金额")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "礼赠金额")
    private BigDecimal giftAmount;

}

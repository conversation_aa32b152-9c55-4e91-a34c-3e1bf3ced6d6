package com.stbella.order.server.order.cts.excel;

import com.stbella.core.excel.Column;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class GeneralOrderExcel {

    @Column(value = "订单编号", order = 1)
    private String orderNo;

    @Column(value = "门店名称", order = 2)
    private String siteName;

    @Column(value = "订单类型", order = 3)
    private String orderTypeName;

    @Column(value = "商品名称", order = 4)
    private String productName;

    @Column(value = "销售名称", order = 5)
    private String sellName;

    @Column(value = "客户名称", order = 6)
    private String customerName;

    @Column(value = "签单金额", order = 7)
    private BigDecimal payableAmount;

    @Column(value = "已付金额", order = 8)
    private BigDecimal realityAmount;

    @Column(value = "累计退款金额", order = 9)
    private BigDecimal refundedAmount;

    @Column(value = "创建时间", order = 10)
    private String gmtCreate;

    @Column(value = "首次支付时间", order = 11)
    private String firstPayTime;

    @Column(value = "支付完成时间", order = 12)
    private String finishPayTime;

    @Column(value = "订单状态", order = 13)
    private String statusName;

}

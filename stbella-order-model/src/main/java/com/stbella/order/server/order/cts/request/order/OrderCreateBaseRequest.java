package com.stbella.order.server.order.cts.request.order;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stbella.core.base.UserTokenInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单创建base request
 *
 * <AUTHOR>
 * @date 2022-03-18 14:31
 * @sine 1.0.0
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "订单创建Base", description = "订单创建Base")
public class OrderCreateBaseRequest implements Serializable {

    private static final long serialVersionUID = -6478780861589062742L;

    @NotBlank(message = "订单标识不能为空")
    @ApiModelProperty(value = "订单标识")
    private String orderSign;

    @ApiModelProperty(value = "套餐折扣 套餐折扣值 0.98/0.99/1")
    private BigDecimal discount;

    @ApiModelProperty(value = "自定义折扣金额")
    private BigDecimal discountAmount;

    @ApiModelProperty(value = "续单标志")
    private Integer renewTag;

    @JsonIgnore
    private UserTokenInfoDTO userTokenInfoDTO;

    @ApiModelProperty(value = "订单分站id")
    private Long ctsSiteId;
}

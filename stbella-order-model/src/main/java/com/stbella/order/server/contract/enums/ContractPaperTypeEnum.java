package com.stbella.order.server.contract.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 纸质合同类型
 */
public enum ContractPaperTypeEnum {

    MONTH_CONTRACT(8, "月子合同"),
    EXPATRIATE_CONTRACT(10, "外派合同"),
    SMALL_MONTH_CONTRACT(11, "小月子合同"),
    GROUP_SECRECY_AGREEMENT(12, "总部和解及保密协议"),
    STORE_SECRECY_AGREEMENT(13, "门店和解及保密协议"),
    GIFT_AGREEMENT(14, "礼赠协议"),
    SUPPLEMENT_AGREEMENT(15, "补充协议"),
    STORE_PERSON_CHANGE_CONTRACT(17, "门店与个人新媒体置换合同");


    private Integer code;
    private String value;

    ContractPaperTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    private static final Map<Integer, String> allMap = new HashMap<>();

    static {
        for (ContractPaperTypeEnum enums : ContractPaperTypeEnum.values()) {
            allMap.put(enums.code, enums.value);
        }
    }

    public static String fromCode(Integer code) {
        return allMap.get(code);
    }
}

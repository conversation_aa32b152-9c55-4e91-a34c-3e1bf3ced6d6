package com.stbella.order.server.order.nutrition.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@ApiModel(value = "SimpleGuestSaveCostRequest对象", description = "获客成本具体数据")
public class SimpleGuestSaveCostRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "年")
    @NotNull(message = "年不能为空")
    private Integer year;

    @ApiModelProperty(value = "月")
    @NotNull(message = "月不能为空")
    private Integer month;

    @ApiModelProperty(value = "花费")
    private BigDecimal expenses;

    @ApiModelProperty(value = "首单购买人数")
    private Integer firstBuyerNum;

    @ApiModelProperty(value = "店铺类型;2-天猫(默认)")
    private Integer type = 2;
}

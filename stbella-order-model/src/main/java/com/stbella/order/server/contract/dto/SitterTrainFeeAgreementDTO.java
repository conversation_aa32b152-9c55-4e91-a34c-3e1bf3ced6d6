package com.stbella.order.server.contract.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SitterTrainFeeAgreementDTO implements Serializable {

    private static final long serialVersionUID = 8709118115964458529L;
    @ApiModelProperty(value = "合同编号")
    private String signNumber;
    /**
     * 乙方
     */
    @ApiModelProperty(value = "用户名")
    private String sitterName;
    @ApiModelProperty(value = "用户身份证")
    private String idNumber;
    @ApiModelProperty(value = "联系电话")
    private String sitterMobile;
    @ApiModelProperty(value = "联系地址")
    private String homeAddress;
    @ApiModelProperty(value = "紧急联系人")
    private String emergencyContact;
    @ApiModelProperty(value = "紧急联系人电话")
    private String emergencyContactPhone;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "线上培训")
    private String onlineTrain;
    @ApiModelProperty(value = "线上培训天")
    private String onlineTrainDay;
    @ApiModelProperty(value = "线上培训课时")
    private String onlineTrainClassHour;


    @ApiModelProperty(value = "线下培训")
    private String offlineTrain;
    @ApiModelProperty(value = "线下培训天")
    private String offlineTrainDay;
    @ApiModelProperty(value = "线下培训课时")
    private String offlineTrainClassHour;


    @ApiModelProperty(value = "线上线下培训结合")
    private String mixTrain;
    @ApiModelProperty(value = "线上线下培训天")
    private String mixTrainDay;
    @ApiModelProperty(value = "线上线下培训课时")
    private String mixTrainClassHour;

    @ApiModelProperty(value = "课程总价")
    private String coursePrice;
    @ApiModelProperty(value = "课程总价大写")
    private String coursePriceCapitalize;

    @ApiModelProperty(value = "签约年")
    private String signYear;
    @ApiModelProperty(value = "签约月")
    private String signMonth;
    @ApiModelProperty(value = "签约日")
    private String signDay;

    @ApiModelProperty(value = "线上培训开始年")
    private String trainStartYear;

    @ApiModelProperty(value = "线上培训开始月")
    private String trainStartMonth;

    @ApiModelProperty(value = "线上培训开始日")
    private String trainStartDay;

    @ApiModelProperty(value = "线上培训结束年")
    private String trainEndYear;

    @ApiModelProperty(value = "线上培训结束月")
    private String trainEndMonth;

    @ApiModelProperty(value = "线上培训结束日")
    private String trainEndDay;

    @ApiModelProperty(value = "线下培训开始年")
    private String offlineTrainStartYear;

    @ApiModelProperty(value = "线下培训开始月")
    private String offlineTrainStartMonth;

    @ApiModelProperty(value = "线下培训开始日")
    private String offlineTrainStartDay;

    @ApiModelProperty(value = "线下培训结束年")
    private String offlineTrainEndYear;

    @ApiModelProperty(value = "线下培训结束月")
    private String offlineTrainEndMonth;

    @ApiModelProperty(value = "线下培训结束日")
    private String offlineTrainEndDay;


    @ApiModelProperty(value = "开课年")
    private String openingTimeYear;

    @ApiModelProperty(value = "开课月")
    private String openingTimeMonth;

    @ApiModelProperty(value = "线下培训地址")
    private String trainingAddress;


    @ApiModelProperty(value = "备注")
    private String remark;


}

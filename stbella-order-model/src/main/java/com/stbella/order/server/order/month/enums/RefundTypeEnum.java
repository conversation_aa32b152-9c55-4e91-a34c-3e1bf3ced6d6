package com.stbella.order.server.order.month.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Getter
public enum RefundTypeEnum {

    BACK_TRACK(1, "原路退回", Arrays.asList(1, 2, 8)),
    OFFLINE_REMITTANCE(2, "线下汇款", Collections.singletonList(3)),
    BACK_ACCOUNT(3, "退款至意向金", Collections.singletonList(101)),

    ;

    private final Integer code;

    private final String value;

    private final List<Integer> typeList;

    RefundTypeEnum(Integer code, String value, List<Integer> typeList) {
        this.code = code;
        this.value = value;
        this.typeList = typeList;
    }

    public static RefundTypeEnum getEnumByCode(Integer code) {
        for (RefundTypeEnum enums : RefundTypeEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的收退类型:" + code);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return true;
        }
        for (RefundTypeEnum monthTypeEnum : values()) {
            if (Objects.equals(monthTypeEnum.getCode(), code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (RefundTypeEnum enums : RefundTypeEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的收退类型:" + code);
    }

    public static RefundTypeEnum getType(Integer refundType) {
        for (RefundTypeEnum refundTypeEnum : RefundTypeEnum.values()) {
            if (refundTypeEnum.getTypeList().contains(refundType)) {
                return refundTypeEnum;
            }
        }
        return null;
    }

}

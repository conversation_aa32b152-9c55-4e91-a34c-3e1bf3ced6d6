package com.stbella.order.server.order.cts.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Classname CustomerOrderAdminContractResponse
 * @Description TODO
 * @Date 2022/3/21 16:57
 * @Created by Jacky
 */
@Data
@Accessors(chain = true)
public class CustomerOrderDTO implements Serializable {

    private static final long serialVersionUID = -2551966901767199819L;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "客户手机号")
    private String mobile;

    @ApiModelProperty(value = "签约分站")
    private String ctsSiteName;

    @ApiModelProperty(value = "签约分站id")
    private Long ctsSiteId;
}

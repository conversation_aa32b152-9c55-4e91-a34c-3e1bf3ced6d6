package com.stbella.order.server.order.nutrition.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.stbella.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 广禾堂-费用管理-品牌营销费用类型
 * </p>
 *
 * <AUTHOR> @since 2022-09-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("nutrition_marketing_type")
@ApiModel(value = "NutritionMarketingTypePO对象", description = "广禾堂-费用管理-品牌营销费用类型")
public class NutritionMarketingTypePO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    private String name;

    private Integer enable;


}

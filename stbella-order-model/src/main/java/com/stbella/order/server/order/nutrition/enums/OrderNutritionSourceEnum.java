package com.stbella.order.server.order.nutrition.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import lombok.Getter;

import java.util.Objects;

/**
 * 营养订单来源
 */
public enum OrderNutritionSourceEnum {
    //订单来源(1-淘宝;2-天猫;3-京东)
    TAO_BAO(1, "淘宝"),
    TIAN_MAO(2, "天猫"),
    JING_DONG(3, "京东"),
    STORE(4, "门店"),
    DISTRIBUTION(5, "分销"),
    SMALL_STORE(6, "新零售"),
    DOU_YIN(7, "抖音"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    OrderNutritionSourceEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static OrderNutritionSourceEnum getEnumByCode(Integer code) {
        for (OrderNutritionSourceEnum enums : OrderNutritionSourceEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的营养订单来源:" + code);
    }

    public static Integer getCodeByValue(String value) {
        for (OrderNutritionSourceEnum enums : OrderNutritionSourceEnum.values()) {
            if (enums.value.equals(value)) {
                return enums.getCode();
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的营养订单来源:" + value);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return true;
        }
        for (OrderNutritionSourceEnum monthTypeEnum : values()) {
            if (Objects.equals(monthTypeEnum.getCode(), code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (OrderNutritionSourceEnum enums : OrderNutritionSourceEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的营养订单来源" + code);
    }
}

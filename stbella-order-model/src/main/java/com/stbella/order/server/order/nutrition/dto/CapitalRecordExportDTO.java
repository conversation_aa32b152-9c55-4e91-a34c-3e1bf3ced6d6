package com.stbella.order.server.order.nutrition.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.stbella.core.excel.Column;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class CapitalRecordExportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "收退类型,1-代表支付，2-代表退款")
    @Column(value = "收退类型", order = 1)
    private String collectRefundType;

    @ApiModelProperty(value = "收退方式,1-支付宝，2-微信")
    @Column(value = "收退方式", order = 2)
    private String collectRefundMethod;

    @ApiModelProperty(value = "订单号")
    @Column(value = "订单编号", order = 3)
    private String orderNo;

    @ApiModelProperty(value = "本地流水号")
    @Column(value = "本地流水号", order = 4)
    private String localTransactionNo;

    @ApiModelProperty(value = "第三方流水号")
    @Column(value = "第三方流水号", order = 5)
    private String transactionNo;

    @ApiModelProperty(value = "收退金额")
    @Column(value = "收退金额", order = 6)
    private BigDecimal collectRefundAmount;

    @ApiModelProperty(value = "客户姓名")
    @Column(value = "客户姓名", order = 7)
    private String customerName;

    @ApiModelProperty(value = "客户手机号")
    @Column(value = "客户手机号", order = 8)
    private String customerPhone;

    @ApiModelProperty(value = "收退状态,1-支付成功，2-支付失败，3-处理中")
    @Column(value = "收退状态", order = 9)
    private String collectRefundStatus;

    @ApiModelProperty(value = "到账时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(value = "到账时间", order = 10)
    private Date arriveAccountTime;

    @ApiModelProperty(value = "审批操作人,只有审批通过或者驳回这个字段才有值")
    @Column(value = "审批操作人", order = 11)
    private String approveName;
}

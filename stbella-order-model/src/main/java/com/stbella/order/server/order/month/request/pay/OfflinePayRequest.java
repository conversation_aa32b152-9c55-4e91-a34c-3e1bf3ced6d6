package com.stbella.order.server.order.month.request.pay;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class OfflinePayRequest implements Serializable {

    @ApiModelProperty(value = "支付账号类型,默认0-贝康;1-广禾贝康;2-贝康到家", required = true)
    private Integer accountType;

    @ApiModelProperty(value = "支付类型")
    private Integer payType;

    @ApiModelProperty(value = "订单号", required = true)
    @NotNull(message = "订单号不能为空")
    private String orderNo;

    /**
     * 订单支付标识 到家订单必传
     */
    @ApiModelProperty("支付标识")
    @NotBlank(message = "支付标识不能为空")
    private String paySign;

    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    @ApiModelProperty(value = "创建人姓名")
    private String createByName;

    @ApiModelProperty(value = "凭证地址")
    private String payProof;

    @ApiModelProperty(value = "备注")
    private String sellRemark;
}

package com.stbella.order.server.order.nutrition.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class OrderPayDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "套餐名称")
    private String comboName;

    @ApiModelProperty(value = "套餐数量")
    private Integer comboNum;

    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @ApiModelProperty(value = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "客户手机号")
    private String customerPhone;

    @ApiModelProperty(value = "订单状态 100-待支付;200-部分支付;300-已支付;400-审批中;500-已驳回;600-审批通过;700-退款中;800-已退款（退款完成）")
    private Integer orderStatus;

    @ApiModelProperty(value = "订单总金额。单位：元")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "已支付金额")
    private BigDecimal alreadyPayAmount;

    @ApiModelProperty(value = "付款页面支付文案")
    private String payContent;

    @ApiModelProperty(value = "应付总金额即本次应收金额")
    private BigDecimal payableAmount;

}

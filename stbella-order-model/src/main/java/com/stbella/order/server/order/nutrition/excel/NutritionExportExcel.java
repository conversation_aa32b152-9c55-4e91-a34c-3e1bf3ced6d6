package com.stbella.order.server.order.nutrition.excel;

import com.stbella.core.excel.Column;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: NutritionImportExcel
 * @date 2022/1/12 3:23 下午
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "NutritionImportExcel对象", description = "营养订单导入表格")
public class NutritionExportExcel implements Serializable {

    private static final long serialVersionUID = 1L;

    @Column(value = "序号", order = 1)
    private String no;

    @Column(value = "下单时间", order = 2)
    private String orderTime;

    @Column(value = "三方订单号", order = 3)
    private String outOrderNo;

    @Column(value = "下单用户ID", order = 4)
    private String orderUserId;

    @Column(value = "手机号码", order = 5)
    private String orderUserPhone;

    @Column(value = "商品名称", order = 6)
    private String commodityName;

    @Column(value = "订单金额", order = 7)
    private String orderAmount;

    @Column(value = "结算金额", order = 8)
    private String payAmount;

    @Column(value = "订单来源", order = 9)
    private String orderSource;

    @Column(value = "订单区域", order = 10)
    private String orderArea;

    @Column(value = "导入时间", order = 11)
    private String gmtModified;

    @Column(value = "是否续订", order = 12)
    private String renew;

    @Column(value = "渠道来源", order = 13)
    private String channelSource;


}

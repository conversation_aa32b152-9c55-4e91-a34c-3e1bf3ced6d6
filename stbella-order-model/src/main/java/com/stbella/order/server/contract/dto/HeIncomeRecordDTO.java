package com.stbella.order.server.contract.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 订单支付记录
 * </p>
 *
 * <AUTHOR> @since 2022-11-10
 */
@Data
@ApiModel(value = "HeIncomeRecordDTO对象", description = "订单支付记录HeIncomeRecordDTO")
public class HeIncomeRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @ApiModelProperty(value = "用户id")
    private Integer clientUid;

    @ApiModelProperty(value = "支付编号，相当于支付第三方的订单编号；一个订单ID对应多个支付编号代表一个订单是分多次支付的")
    private String incomeSn;

    @ApiModelProperty(value = "订单id(he_order表主键)")
    private Integer orderId;

    @ApiModelProperty(value = "order_goods的id")
    private Integer orderGoodsId;

    @ApiModelProperty(value = "所在的支付任务ID：0代表不是以任务方式支付")
    private Long taskId;

    @ApiModelProperty(value = "生成的线下审核任务ID")
    private Long offlineTaskId;

    @ApiModelProperty(value = "门店ID(ecp库cfg_store表的主键id)")
    private Integer storeId;

    @ApiModelProperty(value = "收款渠道, 1-原生 2-创识,3-招商")
    private Integer channelType;

    @ApiModelProperty(value = "1=微信, 2=支付宝, 3=线下支付, 4=中信银行支付宝支付, 5=中信银行微信支付  8=在线pos机支付  9=C端小程序支付 10=其他 11=线下支付-转账汇款 12=线下支付-现金")
    private Integer payType;

    @ApiModelProperty(value = "支付回调参数")
    private String params;

    @ApiModelProperty(value = "支付状态：0待支付，1已支付,2支付失败")
    private Integer status;

    @ApiModelProperty(value = "支付金额(单位分)")
    private Integer income;

    @ApiModelProperty(value = "奖励业务处理状态 0=未处理;1=已处理")
    private Integer bonusStatus;

    @ApiModelProperty(value = "本次支付是第几次支付")
    private Integer payNum;

    @ApiModelProperty(value = "支付时间")
    private Integer payTime;

    @ApiModelProperty(value = "对账状态：0未对账，1正常，2异常")
    private Integer checkStatus;

    @ApiModelProperty(value = "第三方交易流水号")
    private String transactionId;

    @ApiModelProperty(value = "当前订单已累计收款金额")
    private Integer subtotalIncome;

    @ApiModelProperty(value = "收款标识 1)意向金；2）预付款；3）尾款；4）押金")
    private Integer receiptType;

    @ApiModelProperty(value = "是否删除：1是，0否")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Long createdAt;

    @ApiModelProperty(value = "最近更新时间")
    private Long updatedAt;

    @ApiModelProperty(value = "变动金额")
    private Integer changeAmount;

    @ApiModelProperty(value = "冻结金额")
    private Integer freezeAmount;

    @ApiModelProperty(value = "已退款金额")
    private Integer alreadyRefundAmount;

    @ApiModelProperty(value = "支付操作人对应ecp.user 表的名称")
    private Integer optId;

    @ApiModelProperty(value = "支付账户主体ID,用于退款")
    private Long accountId;

    @ApiModelProperty(value = "币种")
    private String currency;
}

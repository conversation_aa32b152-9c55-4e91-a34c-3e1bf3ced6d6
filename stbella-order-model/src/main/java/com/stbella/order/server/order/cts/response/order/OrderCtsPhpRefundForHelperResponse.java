package com.stbella.order.server.order.cts.response.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 到家php系统退款金额
 * </p>
 *
 * <AUTHOR> @since 2022-08-25
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "OrderCtsPhpRefundForHelperResponse对象", description = "到家php系统退款金额（为helper服务提供对象）")
public class OrderCtsPhpRefundForHelperResponse implements Serializable {

    private static final long serialVersionUID = -5934446595698004776L;

    @ApiModelProperty(value = "分站id")
    private Long ctsSiteId;

    @ApiModelProperty(value = "分站名称")
    private String ctsSiteName;

    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "退款统计时间")
    private Date refundGmt;


}

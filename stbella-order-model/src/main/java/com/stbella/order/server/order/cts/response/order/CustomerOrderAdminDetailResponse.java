package com.stbella.order.server.order.cts.response.order;

import com.stbella.order.server.order.cts.dto.OrderAttachmentDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Classname OrderAdminListResponse
 * @Description TODO
 * @Date 2022/3/21 16:57
 * @Created by Jacky
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "雇主PC订单详情", description = "雇主PC订单详情")
public class CustomerOrderAdminDetailResponse implements Serializable {

    private static final long serialVersionUID = 6890077210446449358L;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单类型;9-育婴师培训课程订单;10-育婴师平台管理费订单;11-育婴师上户保证金订单;12-雇主母婴订单;13-雇主育婴师订单;")
    private Integer orderType;

    @ApiModelProperty(value = "签约分站")
    private String ctsSiteName;

    @ApiModelProperty(value = "创建人")
    private String createByName;

    @ApiModelProperty(value = "创建时间，时间戳")
    private Long createByTime;

    @ApiModelProperty(value = "客户归属销售")
    private String sellName;

    @ApiModelProperty(value = "合同")
    private String contract;

    @ApiModelProperty(value = "E签宝合同查看长链接")
    private String eSignLongUrl;

    @ApiModelProperty(value = "E签宝合同查看短链接")
    private String eSignShortUrl;

    @ApiModelProperty(value = "续单标志")
    private Integer renewTag;


    @ApiModelProperty("图片附件列表url")
    private List<OrderAttachmentDTO> picUrlList;

    @ApiModelProperty("文件附件列表url")
    private List<OrderAttachmentDTO> fileUrlList;


    @ApiModelProperty(value = "资金信息")
    private CustomerOrderAdminPaymentResponse paymentResponse = new CustomerOrderAdminPaymentResponse();


    @ApiModelProperty(value = "客户信息")
    private CustomerOrderAdminCustomerResponse customerResponse = new CustomerOrderAdminCustomerResponse();


    @ApiModelProperty(value = "合同信息")
    private CustomerOrderAdminContractResponse contractResponse = new CustomerOrderAdminContractResponse();

    @ApiModelProperty(value = "匹配育婴师信息列表")
    private List<OrderCtsAdminSitterResponse> orderCtsAdminSitterResponses = new ArrayList<>();

    @ApiModelProperty(value = "支付信息列表")
    private List<OrderPayAdminRecordResponse> orderPayAdminRecordResponses = new ArrayList<>();


}

package com.stbella.order.server.order.month.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import com.stbella.order.common.enums.core.OmniPayTypeEnum;
import lombok.Getter;

public enum PayMethodEnum {

    //现金支付表示是真实的钱付款
    CASH("0", "现金", "cash"), CJK("1", "产康金", "product"), REDUCTION("2", "减免", "reduction");

    @Getter
    private final String code;

    @Getter
    private final String value;

    @Getter
    private final String model;

    PayMethodEnum(String code, String value, String model) {
        this.code = code;
        this.value = value;
        this.model = model;
    }

    public static PayMethodEnum getEnumByCode(String code) {
        for (PayMethodEnum enums : PayMethodEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的支付方式:" + code);
    }

    public static PayMethodEnum getEnumByoModel(String model) {
        for (PayMethodEnum enums : PayMethodEnum.values()) {
            if (enums.model.equals(model)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的支付方式:" + model);
    }

    public static PayMethodEnum getEnumByCode(Integer code) {
        for (PayMethodEnum enums : PayMethodEnum.values()) {
            if (enums.code.equals(code.toString())) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的支付方式:" + code);
    }


    public static PayMethodEnum PayType2Currency(Integer payType) {
        if (ObjectUtil.isEmpty(payType)) {
            return CASH;
        }
        //目前只处理产康和正常货币，后续有积分再加
        if (OmniPayTypeEnum.PRODUCTION_COIN.getCode().equals(payType)) {
            return CJK;
        } else if (OmniPayTypeEnum.REDUCTION.getCode().equals(payType)) {
            return REDUCTION;
        } else {
            return CASH;
        }
    }
}

package com.stbella.order.server.contract.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2023-10-16
 */
@Data
@ApiModel(value = "新增E签宝电子模板")
public class EsignTemplateAddRequest implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "模版名称。")
    @NotNull(message = "模版名称不能为空")
    private String name;

    @ApiModelProperty(value = "机构主体ID;取he_store_esign ID")
    @NotNull(message = "机构主体ID不能为空")
    private Long storeEsignId;

    @ApiModelProperty(value = "模版类型 1:订单类  2:附件类 3:补充协议")
    private Integer templateType;

    @ApiModelProperty(value = "适用order_type 多个，隔开")
    private String orderTypes;

    @ApiModelProperty(value = "模板关联合同类型合同类型1育婴师-中介-贝康居间服务协议 2雇主-中介-母婴护理-贝康母婴护理委托服务协议 3雇主-中介-育婴师-贝康育婴师服务委托协议 4育婴师-套餐-贝康服务合作协议 5雇主-套餐-母婴护理-贝康母婴护理委托协议 6雇主-套餐-育婴师-贝康育婴师服务委托协议 7育婴师培训课程确认书协议 8月子合同 10.外派合同  11.小月子合同 12.总部与客户和解保密协议 13.门店与客户和解保密协议 14.礼赠协议 15.补充协议 16.门店与个人新媒体置换合同 21.预约协议 22.授权委托书 23.订单折扣保密协议 24.合同解除协议")
    private Integer templateContractType;

    @ApiModelProperty(value = "状态 0:失效 1:生效")
    private Integer state;

    @ApiModelProperty(value = "E签宝模版id。")
    private String esignFileId;

    @ApiModelProperty(value = "门店类型: 0圣贝拉，1小贝拉，2月子总部，3到家总部，4到家门店，5商城网店，6 BELLA VILLA")
    private Integer storeType;

    @ApiModelProperty(value = "模版备注")
    private String remark;

    @ApiModelProperty(value = "合同文件url")
    private String fileUrl;

    @ApiModelProperty(value = "上传合同模版页数")
    private Integer pageNum;

    @ApiModelProperty(value = "门店ID；默认为0，多个门店逗号分隔 特殊处理的门店为对应store表的store_id")
    private String storeId;

    @ApiModelProperty(value = "公司签章位置 x,y坐标")
    private String companySealPos;

    @ApiModelProperty(value = "个人签名x,y坐标")
    private String personSignaturePos;

    @ApiModelProperty(value = "签章所在页面第几页")
    private Integer posPage;

    @ApiModelProperty(value = "模板html代码")
    private String htmlCode;

    @ApiModelProperty(value = "合同签署类型;1-单方签署;2-双方签署;3-三方签署")
    private Integer contractSignType;


}

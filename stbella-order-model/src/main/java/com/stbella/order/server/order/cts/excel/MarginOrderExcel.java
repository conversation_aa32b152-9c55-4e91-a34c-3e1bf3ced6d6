package com.stbella.order.server.order.cts.excel;

import com.stbella.core.excel.Column;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 管理费订单到导出
 */
@Data
public class MarginOrderExcel {

    @Column(value = "订单编号", order = 1)
    private String orderNo;

    @Column(value = "门店名称", order = 2)
    private String siteName;

    @Column(value = "销售姓名", order = 3)
    private String sellName;

    @Column(value = "育婴师姓名", order = 4)
    private String sitterName;

    @Column(value = "育婴师工资(月)", order = 5)
    private BigDecimal sitterWage;

    @Column(value = "签单金额", order = 6)
    private BigDecimal payableAmount;

    @Column(value = "已付金额", order = 7)
    private BigDecimal realityAmount;

    @Column(value = "累计退款金额", order = 8)
    private BigDecimal refundedAmount;

    @Column(value = "创建时间", order = 9)
    private String gmtCreate;

    @Column(value = "首次支付时间", order = 10)
    private String firstPayTime;

    @Column(value = "支付完成时间", order = 11)
    private String finishPayTime;

    @Column(value = "订单状态",order = 12)
    private String statusName;

}

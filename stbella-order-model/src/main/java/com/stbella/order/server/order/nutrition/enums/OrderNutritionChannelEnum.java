package com.stbella.order.server.order.nutrition.enums;

import lombok.Getter;

/**
 * 经营看板-交易分析-模块
 */
public enum OrderNutritionChannelEnum {

    ELECTRICITY(1, "电商"),
    THE_NEW_RETAIL(2, "新零售"),
    DISTRIBUTION(3, "分销"),
    STORES(4, "门店");

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    OrderNutritionChannelEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

}

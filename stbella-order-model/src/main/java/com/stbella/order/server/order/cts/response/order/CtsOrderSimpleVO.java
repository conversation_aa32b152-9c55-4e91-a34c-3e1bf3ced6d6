package com.stbella.order.server.order.cts.response.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "雇主订单查询返回")
public class CtsOrderSimpleVO implements Serializable {

    @ApiModelProperty(value = "客户名字")
    private String customerName;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "下单时间")
    private Date orderTime;

    @ApiModelProperty(value = "订单类型 12雇主母婴订单;13雇主育婴师订单")
    private Integer orderType;

    @ApiModelProperty(value = "预计上户日期")
    private Date expectServeStartDate;

    @ApiModelProperty(value = "预计下户日期")
    private Date expectServeEndDate;

    @ApiModelProperty(value = "服务天数")
    private Integer days;
}

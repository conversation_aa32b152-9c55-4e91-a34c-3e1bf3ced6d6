package com.stbella.order.server.order.cts.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Classname CustomerOrderAdminContractResponse
 * @Description TODO
 * @Date 2022/3/21 16:57
 * @Created by Jacky
 */
@Data
@Accessors(chain = true)
public class CtsPerformanceDTO implements Serializable {

    private static final long serialVersionUID = -2551966901767199819L;

    @ApiModelProperty(value = "所属销售名字")
    private String sellName;

    @ApiModelProperty(value = "套餐名")
    private String comboName;

    @ApiModelProperty(value = "签约金额")
    private String signAmount;

    @ApiModelProperty(value = "销售签单数")
    private String signCount;

    @ApiModelProperty(value = "销售实收金额")
    private String sellActuallyAmount;

    @ApiModelProperty(value = "月销售额")
    private String saleAmount;

    @ApiModelProperty(value = "实收金额")
    private String actuallyAmount;

    @ApiModelProperty(value = "签约分站")
    private String ctsSiteName;

    @ApiModelProperty(value = "目标达成率")
    private String targetPercent;

}

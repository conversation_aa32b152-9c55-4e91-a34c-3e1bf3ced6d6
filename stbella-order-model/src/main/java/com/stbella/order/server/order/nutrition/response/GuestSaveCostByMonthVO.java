package com.stbella.order.server.order.nutrition.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@ApiModel(value = "GuestSaveCostByMonthVO对象", description = "获客成本出参（月）")
public class GuestSaveCostByMonthVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "月份")
    private Integer month;

    @ApiModelProperty(value = "花费（元）")
    private BigDecimal expenses;

    @ApiModelProperty(value = "当月首单购买人数（人）")
    private Integer firstBuyerNum;

    @ApiModelProperty(value = "全部购买人数")
    private Integer allBuyerNum = 0;

    @ApiModelProperty(value = "新客成本（元）")
    private BigDecimal newCustomerCost = BigDecimal.ZERO;

    @ApiModelProperty(value = "全部客户成本（元）")
    private BigDecimal totalCustomerCosts = BigDecimal.ZERO;

}

package com.stbella.order.server.order.nutrition.excel;

import com.stbella.core.excel.Column;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @description: NutritionImportExcel
 * @date 2022/1/12 3:23 下午
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "FailNutritionImportExcel对象", description = "营养订单导入表格失败返回结果")
public class FailNutritionImportExcel extends NutritionImportExcel {

    @Column(value = "错误信息", order = 9)
    private String errMsg;

}

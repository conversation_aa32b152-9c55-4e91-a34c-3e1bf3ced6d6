package com.stbella.order.server.order.cts.response.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Classname PayRefundResult
 * @Description 微信、支付宝发起退款接口的返回结果
 * @Date 2022/4/24 19:23
 * @Created by <PERSON><PERSON>
 */
@Data
@Accessors(chain = true)
public class PayRefundResult implements Serializable {

    private static final long serialVersionUID = 1687029194525593039L;

    //微信、支付宝的原始返回值
    private String result;

    //支付方式（收退方式）:1-支付宝;2-微信;3-POS机;4-汇款;5-现金;6-其他
    private Integer payType;


    //支付状态:1-支付成功;2-支付失败;3-处理中
    private Integer payStatus;

    @ApiModelProperty(value = "失败原因")
    private String failReason;

}

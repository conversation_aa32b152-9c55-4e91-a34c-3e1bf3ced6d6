package com.stbella.order.server.order.month.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import lombok.Getter;

import java.util.Objects;

/**
 * 折扣审批状态
 */
public enum ApprovalDiscountStatusEnum {
    NO_APPROVAL_NEEDED(0, "无需审批"),
    APPROVING(1, "审批中"),
    APPROVED(2, "审批通过"),
    APPROVAL_FAILED(3, "审批失败"),
    INITIATION_FAILED(4, "发起失败"),
    ;



    @Getter
    private final Integer code;

    @Getter
    private final String value;

    ApprovalDiscountStatusEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static ApprovalDiscountStatusEnum getEnumByCode(Integer code) {
        for (ApprovalDiscountStatusEnum enums : ApprovalDiscountStatusEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的支付状态:" + code);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return true;
        }
        for (ApprovalDiscountStatusEnum monthTypeEnum : values()) {
            if (Objects.equals(monthTypeEnum.getCode(), code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (ApprovalDiscountStatusEnum enums : ApprovalDiscountStatusEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的支付状态:" + code);
    }
}

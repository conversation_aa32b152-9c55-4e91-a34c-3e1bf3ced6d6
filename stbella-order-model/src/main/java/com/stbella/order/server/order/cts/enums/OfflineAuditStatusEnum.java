package com.stbella.order.server.order.cts.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import io.swagger.annotations.ApiModel;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */

@ApiModel(value = "线下付款审核状态")
public enum OfflineAuditStatusEnum {


    UNDER_REVIEW(1, "审核中"),
    EXAMINATION_PASSED(2, "审核通过"),
    REVIEW_REJECTED(3, "审核驳回"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    OfflineAuditStatusEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static OfflineAuditStatusEnum getEnumByCode(Integer code) {
        for (OfflineAuditStatusEnum enums : OfflineAuditStatusEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的支付类型:" + code);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return true;
        }
        for (OfflineAuditStatusEnum monthTypeEnum : values()) {
            if (Objects.equals(monthTypeEnum.getCode(), code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (OfflineAuditStatusEnum enums : OfflineAuditStatusEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的支付类型:" + code);
    }

}

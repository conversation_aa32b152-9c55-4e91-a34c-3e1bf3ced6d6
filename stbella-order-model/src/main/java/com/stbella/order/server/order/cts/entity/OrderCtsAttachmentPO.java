package com.stbella.order.server.order.cts.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.stbella.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 到家订单附件表
 * </p>
 *
 * <AUTHOR> @since 2022-09-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("order_cts_attachment")
@ApiModel(value = "OrderCtsAttachmentPO对象", description = "到家订单附件表")
public class OrderCtsAttachmentPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "附件地址")
    private String attachmentUrl;
    /**
     * {@link com.stbella.order.server.order.cts.enums.OrderCtsAttachmentTypeEnum}
     */
    @ApiModelProperty(value = "附件类型,1:图片附件，2:文件附件")
    private Integer attachmentType;

    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    @ApiModelProperty(value = "创建人姓名")
    private String createByName;


    @ApiModelProperty(value = "附件名字")
    private String attachmentName;

    @ApiModelProperty(value = "附件大小")
    private Long attachmentSize;


}

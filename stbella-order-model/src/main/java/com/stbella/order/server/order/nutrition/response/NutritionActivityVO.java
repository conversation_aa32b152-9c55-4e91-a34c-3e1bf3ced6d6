package com.stbella.order.server.order.nutrition.response;

import com.baomidou.mybatisplus.annotation.*;
import com.stbella.core.base.BasicReq;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 广禾堂-活动业绩-类型
 * </p>
 *
 * <AUTHOR> @since 2023-10-26
 */
@Data
@Accessors(chain = true)
@ApiModel(value="NutritionActivityEntity对象", description="广禾堂-活动业绩-类型")
public class NutritionActivityVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "活动备注")
    private String remark;

    @ApiModelProperty(value = "0：未启用；1-已启用")
    private Integer enable;

    /**
     * 逻辑删除标志
     */
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    @ApiModelProperty(value = "创建人姓名")
    private String createByName;

    @ApiModelProperty(value = "修改人ID")
    private Long updateBy;

    @ApiModelProperty(value = "修改人姓名")
    private String updateByName;


}

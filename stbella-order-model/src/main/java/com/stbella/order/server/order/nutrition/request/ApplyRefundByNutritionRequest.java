package com.stbella.order.server.order.nutrition.request;

import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "ApplyRefundByNutritionRequest对象", description = "营养申请退款入参")
public class ApplyRefundByNutritionRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单编号")
    @NotNull(message = "订单编号不能为空")
    private String orderNo;

    @ApiModelProperty(value = "出餐状态;0-未出餐（部分支付传未出餐）;1-已出餐")
    private Integer foodStatus;

    @ApiModelProperty(value = "申请退款的数据")
    private List<SimpleApplyRefundByNutritionRequest> simpleApplyRefundByNutritionRequestList;

    @ApiModelProperty(value = "退款原因")
    @NotNull(message = "退款原因不能为空")
    @Length(max = 50, message = "最大退款原因为50个字")
    private String refundCause;

    public Integer getFoodStatus() {
        return ObjectUtil.isNull(foodStatus) ? 0 : foodStatus;
    }
}

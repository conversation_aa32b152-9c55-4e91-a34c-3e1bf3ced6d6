package com.stbella.order.server.order.month.excel;

import com.stbella.core.excel.Column;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@ApiModel(value = "资金记录导出表格", description = "资金记录导出表格")
public class MoneyRecordExcel implements Serializable {

    private static final long serialVersionUID = 1L;

    @Column(value = "序号", order = 1)
    private Integer no;

    @Column(value = "记录类型", order = 2)
    private String recordType;

    @Column(value = "本地流水号", order = 3)
    private String localTransactionalNo;

    @Column(value = "收退类型", order = 4)
    private String amountType;

    @Column(value = "订单编号", order = 5)
    private String orderNo;

    @Column(value = "第三方流水号", order = 6)
    private String transactionalNo;

    @Column(value = "业务类型", order = 7)
    private String orderType;

    @Column(value = "业务子类型", order = 8)
    private String orderChildType;

    @Column(value = "门店名字", order = 9)
    private String storeName;

    @Column(value = "客户手机号", order = 10)
    private String customPhone;

    @Column(value = "客户名称", order = 11)
    private String customName;

    @Column(value = "收退金额", order = 12)
    private BigDecimal payAmount;

    @Column(value = "订单实收款。单位：元", order = 13)
    private BigDecimal realityAmount;

    @Column(value = "收退方式", order = 14)
    private String payType;

    @Column(value = "到账时间", order = 15)
    private String payTime;

    @Column(value = "创建时间", order = 16)
    private String gmtCreate;

    @Column(value = "收退状态", order = 17)
    private String payStatus;

}

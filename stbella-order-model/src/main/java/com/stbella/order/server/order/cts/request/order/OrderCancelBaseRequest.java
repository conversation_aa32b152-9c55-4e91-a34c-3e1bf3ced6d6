package com.stbella.order.server.order.cts.request.order;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stbella.core.base.UserTokenInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 订单取消Base request
 *
 * <AUTHOR>
 * @date 2022-03-18 14:31
 * @sine 1.0.0
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "订单取消Base", description = "订单取消Base")
public class OrderCancelBaseRequest implements Serializable {

    private static final long serialVersionUID = -6478780861589062742L;

    @NotBlank(message = "订单编号不能为空")
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @JsonIgnore
    private UserTokenInfoDTO userTokenInfoDTO;
}

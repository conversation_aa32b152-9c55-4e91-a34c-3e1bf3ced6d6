package com.stbella.order.server.order.cts.response.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class OrderDetailResponse implements Serializable {

    @ApiModelProperty(value = "日均费用")
    private BigDecimal dayAveFee;

    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderAmount;


}

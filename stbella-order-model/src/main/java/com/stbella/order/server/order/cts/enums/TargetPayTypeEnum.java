package com.stbella.order.server.order.cts.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import lombok.Getter;

import java.util.Objects;

/**
 * 选择支付类型
 */
public enum TargetPayTypeEnum {
    PAY_PERCENT(0, "比例支付"),
    PAY_AMOUNT(1, "指定金额支付");

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    TargetPayTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static TargetPayTypeEnum getEnumByCode(Integer code) {
        for (TargetPayTypeEnum enums : TargetPayTypeEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的枚举值:" + code);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return true;
        }
        for (TargetPayTypeEnum monthTypeEnum : values()) {
            if (Objects.equals(monthTypeEnum.getCode(), code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (TargetPayTypeEnum enums : TargetPayTypeEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的枚举值:" + code);
    }
}

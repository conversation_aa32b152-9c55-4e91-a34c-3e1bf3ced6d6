package com.stbella.order.server.contract.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 机构印章
 */
@Data
public class CompanySealRequestDTO implements Serializable {

    private static final long serialVersionUID = 8709118115964458529L;

    @ApiModelProperty(value = "机构账号的唯一标识")
    private String orgId;

    @ApiModelProperty(value = "印章别名（别名不能重复）")
    private String alias;

    /**
     * 颜色
     */
    private String color = "RED";

    @ApiModelProperty(value = "模板类型")
    private String type = "TEMPLATE_ROUND";

    @ApiModelProperty(value = "中心图案类型")
    private String central = "STAR";

}

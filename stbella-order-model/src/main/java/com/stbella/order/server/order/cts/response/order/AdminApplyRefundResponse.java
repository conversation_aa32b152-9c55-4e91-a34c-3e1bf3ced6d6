package com.stbella.order.server.order.cts.response.order;

import com.stbella.order.server.order.cts.entity.OrderCtsApplyRefundPO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Classname CustomeAdminApplyRefundResponse
 * @Description TODO
 * @Date 2022/3/29 22:49
 * @Created by Jacky
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "AdminApplyRefundResponse", description = "到家订单申请退款列表")
public class AdminApplyRefundResponse extends OrderCtsApplyRefundPO {

    @ApiModelProperty(value = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "客户手机号")
    private String mobile;

    @ApiModelProperty(value = "签约分站")
    private String ctsSiteName;

    @ApiModelProperty(value = "审核状态,0:待审核 1:审核通过 2:审核驳回")
    private Integer auditStatus;


}

package com.stbella.order.server.order.cts.request.order;

import com.stbella.order.server.order.cts.enums.YesOrNoEnum;
import com.stbella.order.server.order.month.annotations.EnumValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 育婴师-培训课程-订单创建request
 *
 * <AUTHOR>
 * @date 2022-03-18 14:31
 * @sine 1.0.0
 */
@Data
@Accessors(chain = true)
@FieldNameConstants
@ApiModel(value = "育婴师-培训课程订单创建Model", description = "育婴师-培训课程订单创建Model")
public class CourseOrderCreateRequest extends SitterOrderCreateBaseRequest implements Serializable {

    private static final long serialVersionUID = -7652485664304976662L;

    @NotNull
    @ApiModelProperty(value = "客户认证id")
    private Long orderCustomCertificationId;

    @ApiModelProperty(value = "套餐id")
    private String comboId;

    @ApiModelProperty(value = "是否需要证书 0:不需要 1:需要")
    @EnumValue(enumClass = YesOrNoEnum.class, enumMethod = "isValidCode")
    private Integer isCer;

    @ApiModelProperty(value = "开课时间 13位时间戳")
    private Long openingTime;

    @ApiModelProperty(value = "培训有效期开始时间")
    private Long trainStart;

    /*目前结束时间自动计算*/
    //@ApiModelProperty(value = "培训有效期结束时间")
    //private Long trainEnd;

    @ApiModelProperty(value = "线下培训地址")
    private String trainingAddress;

}

package com.stbella.order.server.order.nutrition.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 广禾堂-活动业绩-渠道目标值
 * </p>
 *
 * <AUTHOR> @since 2023-10-26
 */
@Data
@Accessors(chain = true)
@TableName("nutrition_activity_target")
@ApiModel(value="NutritionActivityTargetEntity对象", description="广禾堂-活动业绩-渠道目标值")
public class NutritionActivityTargetPO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "活动业绩类型id")
    private Long activityId;

    @ApiModelProperty(value = "分类1-天猫;2-抖音;3-京东;4-私域;")
    private Integer type;

    @ApiModelProperty(value = "t-1日总业绩,金额元,手动输入")
    private BigDecimal baseAmount;

    @ApiModelProperty(value = "t日总业绩,金额元,影刀抓取")
    private BigDecimal amount;

    @ApiModelProperty(value = "0：未启用；1-已启用")
    private Integer enable;

    /**
     * 逻辑删除标志
     */
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    @ApiModelProperty(value = "创建人姓名")
    private String createByName;

    @ApiModelProperty(value = "修改人ID")
    private Long updateBy;

    @ApiModelProperty(value = "修改人姓名")
    private String updateByName;

    @ApiModelProperty(value = "活动名称")
    @TableField(exist = false)
    private String activityName;

    @ApiModelProperty(value = "活动备注")
    @TableField(exist = false)
    private String remark;
}

package com.stbella.order.server.order.cts.request.order;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 育婴师-保证金-订单创建request
 *
 * <AUTHOR>
 * @date 2022-03-18 14:31
 * @sine 1.0.0
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "育婴师-保证金订单创建Model", description = "育婴师-保证金订单创建Model")
public class MarginOrderCreateRequest extends SitterOrderCreateBaseRequest implements Serializable {

    private static final long serialVersionUID = 1722509112613775521L;
}

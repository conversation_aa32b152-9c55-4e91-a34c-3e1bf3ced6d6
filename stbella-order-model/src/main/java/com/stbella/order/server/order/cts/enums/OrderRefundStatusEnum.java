package com.stbella.order.server.order.cts.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import lombok.Getter;

import java.util.Objects;

/**
 * 订单退款审核和退款状态
 */
public enum OrderRefundStatusEnum {
    WAIT_AUDIT(0, "待审核"),
    SUCCESS_AUDIT(1, "审核通过"),
    FAILED_AUDIT(2, "审核驳回"),
    AUDITING_AUDIT(3, "退款处理中"),
    ERR_AUDIT(4, "退款失败，账户余额不足"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    OrderRefundStatusEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static OrderRefundStatusEnum getEnumByCode(Integer code) {
        for (OrderRefundStatusEnum enums : OrderRefundStatusEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的订单退款审核状态:" + code);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return true;
        }
        for (OrderRefundStatusEnum monthTypeEnum : values()) {
            if (Objects.equals(monthTypeEnum.getCode(), code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (OrderRefundStatusEnum enums : OrderRefundStatusEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的订单退款审核状态:" + code);
    }
}

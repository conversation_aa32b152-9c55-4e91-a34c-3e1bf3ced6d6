package com.stbella.order.server.contract.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.stbella.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 雇主合同
 * </p>
 *
 * <AUTHOR> @since 2022-03-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("user_e_sign")
@ApiModel(value = "UserESignPO对象", description = "雇主合同")
public class UserESignPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "证件类型")
    private Integer cerType;

    @ApiModelProperty(value = "证件号")
    private String cerNo;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "e签宝id")
    private String eSignUserId;


}

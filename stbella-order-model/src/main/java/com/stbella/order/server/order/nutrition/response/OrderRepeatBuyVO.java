package com.stbella.order.server.order.nutrition.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "OrderRepeatBuyVO对象", description = "营养订单复购返回对象")
@Accessors(chain = true)
public class OrderRepeatBuyVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "复购次数")
    private String repeatBuyNum;

    @ApiModelProperty(value = "复购客户数")
    private String repeatBuyPeopleNum;

    @ApiModelProperty(value = "全部客户数")
    private String allPeopleNum;

    @ApiModelProperty(value = "复购率")
    private String repeatBuyPer;
}

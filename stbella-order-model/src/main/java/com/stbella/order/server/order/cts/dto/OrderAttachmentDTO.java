/*
 * Copyright (c) 2001-2022 GuaHao.com Corporation Limited. All rights reserved.
 * This software is the confidential and proprietary information of GuaHao Company.
 * ("Confidential Information").
 * You shall not disclose such Confidential Information and shall use it only
 * in accordance with the terms of the license agreement you entered into with GuaHao.com.
 */
package com.stbella.order.server.order.cts.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class OrderAttachmentDTO implements Serializable {

    private static final long serialVersionUID = 8979658600316743818L;

    @ApiModelProperty("附件列表url")
    private String attachmentUrl;

    @ApiModelProperty(value = "附件名字")
    private String attachmentName;

    @ApiModelProperty(value = "附件大小")
    private Long attachmentSize;
}

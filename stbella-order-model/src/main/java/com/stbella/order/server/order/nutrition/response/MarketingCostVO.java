package com.stbella.order.server.order.nutrition.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "MarketingCostVO对象", description = "品牌营销费用")
public class MarketingCostVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "年")
    private Integer year;

    @ApiModelProperty(value = "具体数据（数组下表0代表渠道名称，后面12个数据代表对应月份）")
    private List<SimpleNutritionMarketingVO> allData;
}

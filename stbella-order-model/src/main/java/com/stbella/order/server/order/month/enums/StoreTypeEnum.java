package com.stbella.order.server.order.month.enums;

import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;

import java.util.Objects;

import cn.hutool.core.util.ObjectUtil;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description: OrderTypeEnum 月子套餐类型枚举
 * @date 2021/11/17 3:20 下午
 */
public enum StoreTypeEnum {
    //0圣贝拉，1小贝拉，2月子总部，3到家总部，4到家门店，5商城网店
    SAINT_BELLA(0, "圣贝拉"),
    BABY_BELLA(1, "小贝拉"),
    MONTH_HEADQUARTERS(2, "月子总部"),
    CTS_HEADQUARTERS(3, "到家总部"),
    CTS_STORE(4, "到家门店"),
    SHOP_MALL(5, "商城网店"),
    BELLA_VILLA(6, "BELLA VILLA"),
    ISLA(100, "艾屿"),
    FAMILY(101, "予家"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    StoreTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static StoreTypeEnum getEnumByCode(Integer code) {
        for (StoreTypeEnum enums : StoreTypeEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的业务子类型:" + code);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return true;
        }
        for (StoreTypeEnum monthTypeEnum : values()) {
            if (Objects.equals(monthTypeEnum.getCode(), code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (StoreTypeEnum enums : StoreTypeEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        return "未知";
    }

    public static String getBrandName(Integer type, Integer childType) {

        if (StoreTypeEnum.SAINT_BELLA.getCode().equals(type)) {
            if (StoreChildTypeEnum.BELLA_VILLA.getCode().equals(childType)) {
                return StoreChildTypeEnum.BELLA_VILLA.getValue();
            }
            return StoreTypeEnum.SAINT_BELLA.getValue();
        }
        if (StoreTypeEnum.BABY_BELLA.getCode().equals(type)) {
            if (StoreChildTypeEnum.BABY_BELLA_DELUXE.getCode().equals(childType)) {
                return StoreChildTypeEnum.BABY_BELLA_DELUXE.getValue();
            }
            return StoreTypeEnum.BABY_BELLA.getValue();
        }
        return StoreTypeEnum.getValueByCode(type);
    }
}

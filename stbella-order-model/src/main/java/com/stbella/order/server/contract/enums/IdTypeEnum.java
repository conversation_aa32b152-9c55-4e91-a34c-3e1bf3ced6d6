package com.stbella.order.server.contract.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;

import java.util.Objects;

public enum IdTypeEnum {

    CRED_PSN_CH_IDCARD(0, "大陆身份证"),
    CRED_PSN_PASSPORT(1, "护照"),
    CRED_PSN_CH_HONGKONG(2, "香港来往大陆通行证"),
    CRED_PSN_CH_MACAO(3, "澳门来往大陆通行证"),
    CRED_PSN_CH_TWCARD(4, "台湾来往大陆通行证"),
    CRED_PSN_SG_IDCARD(5, "新加坡身份证");



    private Integer code;
    private String value;

    IdTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }


    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (IdTypeEnum enums : IdTypeEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的枚举:" + code);
    }

    public static void main(String[] args) {
        String valueByCode = getValueByCode(1);
        System.out.println(valueByCode);
    }

    public static String getIdType(Integer code) {
        if (0 == code) {
            return "CRED_PSN_CH_IDCARD";
        }
        if (1 == code) {
            return "CRED_PSN_PASSPORT";
        }
        if (2 == code) {
            return "CRED_PSN_CH_HONGKONG";
        }
        if (3 == code) {
            return "CRED_PSN_CH_MACAO";
        }
        if (4 == code){
            return "CRED_PSN_CH_TWCARD";
        }
        return null;
    }

    public static String getValue(Integer code) {
        for (IdTypeEnum value : IdTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value.getValue();
            }
        }
        return "";
    }
}

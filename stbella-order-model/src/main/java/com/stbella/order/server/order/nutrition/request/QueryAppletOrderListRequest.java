package com.stbella.order.server.order.nutrition.request;

import com.stbella.core.base.BasePageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryAppletOrderListRequest extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "全部还是我的 1代表全部  2代表我的,默认是全部", required = true)
    private Integer allOrMy;

    @ApiModelProperty(value = "订单状态 如果是全部则传null，100-待支付;200-部分支付;300-已支付;400-审批中;500-已驳回;600-审批通过;700-退款中;800-部分退款;900-已退款（退款完成）")
    private Integer orderStatus;

    @ApiModelProperty(value = "手机号和姓名模糊搜索文本")
    private String text;
}

package com.stbella.order.server.order.nutrition.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel(value = "NutritionMaoRiDTO对象", description = "毛利分析DTO")
public class NutritionMaoRiDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "商品收入累加值")
    private BigDecimal commodityIncomeAdd;
    @ApiModelProperty(value = "销售毛利累加值")
    private BigDecimal salesMaoRiAdd;
    @ApiModelProperty(value = "商品成本累加值")
    private BigDecimal costAdd;
}

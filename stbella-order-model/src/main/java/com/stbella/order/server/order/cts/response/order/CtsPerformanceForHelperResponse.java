package com.stbella.order.server.order.cts.response.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Classname CtsPerformanceForHelperResponse
 * @Description 为helper服务提供业绩response
 * @Date 2022/7/13 20:35
 * @Created by Jacky
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "为helper服务提供业绩response", description = "为helper服务提供业绩response")
public class CtsPerformanceForHelperResponse implements Serializable {

    @ApiModelProperty(value = "所有分站已付总金额:元")
    private BigDecimal realityTotalAmount;


    @ApiModelProperty(value = "所有分站总收入（所有分站应付总金额):元")
    private BigDecimal payableTotalAmount = BigDecimal.ZERO;


    @ApiModelProperty(value = "PHP老系统有效的退款业绩列表")
    private List<OrderCtsPhpRefundForHelperResponse> phpRefundList;
}

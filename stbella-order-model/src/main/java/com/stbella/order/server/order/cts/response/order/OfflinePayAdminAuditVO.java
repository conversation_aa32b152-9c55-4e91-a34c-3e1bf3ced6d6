package com.stbella.order.server.order.cts.response.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class OfflinePayAdminAuditVO implements Serializable {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单类型;9-育婴师培训课程订单;10-育婴师平台管理费订单;11-育婴师上户保证金订单;12-雇主母婴订单;13-雇主育婴师订单")
    private Integer orderType;

    @ApiModelProperty(value = "本地流水号")
    private String localTransactionalNo;

    @ApiModelProperty(value = "第三方流水号")
    private String transactionalNo;
    @ApiModelProperty(value = "审核状态 1:审核中 2:审核通过 3:审核驳回")
    private Integer auditStatus;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "到账金额")
    private BigDecimal actuallyPayAmount;

    @ApiModelProperty(value = "到账时间")
    private Date accountTime;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "审核时间")
    private Date auditTime;

    @ApiModelProperty(value = "审核驳回原因")
    private String disagreeReason;

    @ApiModelProperty(value = "创建人姓名")
    private String createByName;


    @ApiModelProperty(value = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "审核人")
    private String auditUserName;

    @ApiModelProperty(value = "分站名")
    private String ctsSiteName;

    @ApiModelProperty(value = "支付主体")
    private String paySubjectName;

}

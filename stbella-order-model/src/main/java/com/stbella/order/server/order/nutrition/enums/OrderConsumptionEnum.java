package com.stbella.order.server.order.nutrition.enums;

import cn.hutool.core.util.ObjectUtil;
import com.stbella.core.exception.BusinessException;
import com.stbella.core.result.ResultEnum;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * 消费金额区间分析枚举
 * @date 2021/11/17 3:20 下午
 */
public enum OrderConsumptionEnum {
    //消费区间:100:0-299;  200:300-399;  300:400-549;  400:550-749;  500:750-1049;  600:1050-1749； 700:1750及以上;
    CONSUMPTION_1(1, "0-299"),
    CONSUMPTION_2(2, "300-399"),
    CONSUMPTION_3(3, "400-549"),
    CONSUMPTION_4(4, "550-749"),
    CONSUMPTION_5(5, "750-1049"),
    CONSUMPTION_6(6, "1050-1749"),
    CONSUMPTION_7(7, "1750及以上"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String value;

    OrderConsumptionEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static OrderConsumptionEnum getEnumByCode(Integer code) {
        for (OrderConsumptionEnum enums : OrderConsumptionEnum.values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的业务类型:" + code);
    }

    public static boolean isValidCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return true;
        }
        for (OrderConsumptionEnum monthTypeEnum : values()) {
            if (Objects.equals(monthTypeEnum.getCode(), code)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return "";
        }
        for (OrderConsumptionEnum enums : OrderConsumptionEnum.values()) {
            if (Objects.equals(code, enums.code)) {
                return enums.value;
            }
        }
        throw new BusinessException(ResultEnum.ENUM_CODE_ERROR, "没有找到对应的业务类型:" + code);
    }
}

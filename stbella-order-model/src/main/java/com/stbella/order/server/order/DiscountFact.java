package com.stbella.order.server.order;

import com.stbella.order.server.order.month.constant.BaseConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: wangchuang
 * @CreateTime: 2024-01-12  16:06
 * @Description: 折扣审批对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DiscountFact implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = BaseConstant.ORDER_TYPR)
    private Integer orderType;

    @ApiModelProperty(value = "'毛利率',没有小数点整数")
    private BigDecimal grossMargin;

    @ApiModelProperty(value = "折扣率")
    private BigDecimal discountMargin;

    @ApiModelProperty(value = "签单折扣率")
    private BigDecimal signMargin;

    @ApiModelProperty(value = "净折扣率")
    private BigDecimal netMargin;

    @ApiModelProperty(value = "品牌")
    private Integer storeBrand;

    @ApiModelProperty(value = "角色ID")
    private Integer roleId;

    @ApiModelProperty(value = "是否新订单")
    private Boolean isNewOrder;

    @ApiModelProperty(value = "门店ID")
    private Integer storeId;

    @ApiModelProperty(value = "订单场景")
    private Integer orderScene;

    @ApiModelProperty(value = "战区")
    private Integer warArea;

}

package com.stbella.order.server.order.nutrition.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: OrderNutritionBoardRequest
 * @date 2022/6/8 11:34 上午
 */
@Data
@ApiModel(value = "OrderNutritionMaoRiRequest对象", description = "毛利分析查询对象")
public class OrderNutritionMaoRiRequest {


    @ApiModelProperty(value = "开始时间", required = true)
    @NotNull(message = "开始时间不能为空")
    private Date beginDate;

    @ApiModelProperty(value = "结束时间", required = true)
    @NotNull(message = "结束时间不能为空")
    private Date endDate;

    @ApiModelProperty(value = "类型;3-京东(默认)")
    private Integer type;
}

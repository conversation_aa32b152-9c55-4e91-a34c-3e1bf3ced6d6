package com.stbella.order.server.contract.dto;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SignPositionResponseDTO implements Serializable {

    private static final long serialVersionUID = 8709118115964458529L;

    @ApiModelProperty(value = "文件id")
    private String fileId;

    @ApiModelProperty(value = "关键字列表")
    private String keyword;

    @ApiModelProperty(value = "位置列表")
    private List<InnerPositionList> positionList;

    @Data
    public static class InnerPositionList {

        @ApiModelProperty(value = "页码")
        private Integer pageIndex;

        @ApiModelProperty(value = "操作日志id")
        private List<InnerCoordinateList> coordinateList;

        @Data
        public static class InnerCoordinateList {

            @ApiModelProperty(value = "x坐标")
            private Integer posx;

            @ApiModelProperty(value = "y坐标")
            private Integer posy;
        }
    }
}

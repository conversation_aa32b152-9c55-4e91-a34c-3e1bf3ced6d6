package com.stbella.order.server.order.nutrition.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "TransactionAnalysisDataVO对象", description = "交易分析各个渠道数据")
public class EachChannelVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "类型：1-电商；2-新零售；3-分销；4-门店")
    private Integer channelType;

    @ApiModelProperty(value = "类型名字")
    private String channelName;

    @ApiModelProperty(value = "订单总金额")
    private String totalOrderAmount;

    @ApiModelProperty(value = "订单数")
    private String cumulativeOrders;

    @ApiModelProperty(value = "业绩完成率")
    private String performanceCompletionRate;

    @ApiModelProperty(value = "具体渠道列表")
    private List<SimpleEachChannelVO> simpleEachChannelVOList;

}

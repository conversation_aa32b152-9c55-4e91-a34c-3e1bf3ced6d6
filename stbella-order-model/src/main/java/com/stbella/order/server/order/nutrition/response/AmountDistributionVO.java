package com.stbella.order.server.order.nutrition.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@ApiModel(value = "AmountDistributionVO对象", description = "金额部分数据")
public class AmountDistributionVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "电商、新零售、分销：1-300以下；2-300至600；3-600至1000；4-1000至1500；5-1500至2500；6-2500至3500；7-3500以上；  门店：1-200以下；2-200至500；3-500至3000；4-3000至4500；5-4500至6000；6-6000以上； ")
    private String section;

    @ApiModelProperty(value = "订单数（笔）")
    private String orderQuantity;

    @ApiModelProperty(value = "订单占比（%）")
    private BigDecimal orderOf;

    @ApiModelProperty(value = "订单总金额（元）")
    private String totalAmountTheOrder;

    @ApiModelProperty(value = "金额占比（%）")
    private BigDecimal amountOf;


}

package com.stbella.order.server.order.month.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <p>
 * 订单用户快照表
 * </p>
 *
 * <AUTHOR> @since 2022-10-27
 */
@Data
@ApiModel(value = "OrderBailorSnapshotVO对象", description = "订单委托人快照OrderBailorSnapshotVO")
public class OrderBailorSnapshotVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "订单ID")
    private Integer orderId;

    @ApiModelProperty(value = "客户ID")
    private Integer clientUid;

    @ApiModelProperty(value = "委托人ID")
    private Integer bailorId;

    @ApiModelProperty(value = "委托人姓名")
    private String name;

    @ApiModelProperty(value = "手机号类型, 0:中国大陆 1:中国香港 2:中国澳门 3:中国台湾")
    private Integer phoneType;

    @ApiModelProperty(value = "委托人手机号码")
    private String phone;

    @ApiModelProperty(value = "0=大陆身份证 1=护照 2=香港来往大陆通行证 3=澳门来往大陆通行证 4=台湾来往大陆通行证")
    private Integer certType;

    @ApiModelProperty(value = "身份证")
    private String idCard;

    @ApiModelProperty(value = "身份证正面 url")
    private String idCardFront;

    @ApiModelProperty(value = "身份证背面图url")
    private String idCardBack;

    @ApiModelProperty(value = "证件认证结果, 0:未认证 1:认证成功 2:认证失败 3:无需认证")
    private Integer isCardVerify;

    @ApiModelProperty(value = "手机号认证状态 0=手机号未验证过;1=已验证")
    private Integer isPhoneVerify;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    @ApiModelProperty("修改时间")
    private Date gmtModified;

    @ApiModelProperty("逻辑删除标识 0-未删除 1-已删除")
    private Integer deleted;

    @ApiModelProperty(value = "认证类型：0=中国大陆(不包含港澳台)-实名认证;1=邮箱")
    private Integer authType;

    @ApiModelProperty(value = "客户证件号/邮箱")
    private String idNumber;
}

package com.stbella.order.server.order;

import com.stbella.order.server.order.month.constant.BaseConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 折扣审批请求
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DiscountFactReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = BaseConstant.ORDER_TYPR)
    private Integer orderType;

    @ApiModelProperty(value = "'毛利率',没有小数点整数")
    private BigDecimal grossMargin;

    @ApiModelProperty(value = "折扣率")
    private BigDecimal discountMargin;

    @ApiModelProperty(value = "签单折扣率")
    private BigDecimal signOrderDiscountMargin;

    @ApiModelProperty(value = "净折扣率")
    private BigDecimal netMargin;

    @ApiModelProperty(value = "品牌")
    private Integer storeBrand;

    @ApiModelProperty(value = "角色ID")
    private List<Integer> roleIdList;

    @ApiModelProperty(value = "是否新订单")
    private Boolean isNewOrder;

    private Integer storeId;

    @ApiModelProperty(value = "订单场景")
    private Integer orderScene;

    @ApiModelProperty(value = "战区")
    private Integer warArea;

}
